"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Download, FileSpreadsheet, FileText, Users } from "lucide-react"

interface BatchExportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedMemberIds: string[]
  memberCount: number
}

export function BatchExportDialog({ open, onOpenChange, selectedMemberIds, memberCount }: BatchExportDialogProps) {
  const { toast } = useToast()
  const [fileFormat, setFileFormat] = useState<"excel" | "csv">("excel")
  const [isExporting, setIsExporting] = useState(false)
  const [selectedFields, setSelectedFields] = useState<string[]>([
    "id", "name", "phone", "level", "source", "birthday", "consultant", "points", "tags"
  ])
  
  // 所有可导出的字段
  const availableFields = [
    { id: "id", label: "会员ID" },
    { id: "name", label: "姓名" },
    { id: "phone", label: "手机号" },
    { id: "email", label: "邮箱" },
    { id: "gender", label: "性别" },
    { id: "birthday", label: "生日" },
    { id: "address", label: "地址" },
    { id: "level", label: "会员等级" },
    { id: "source", label: "渠道来源" },
    { id: "consultant", label: "会籍顾问" },
    { id: "points", label: "积分" },
    { id: "pointsExpiry", label: "积分过期日期" },
    { id: "tags", label: "标签" },
    { id: "lastVisit", label: "最近到访" },
    { id: "totalSpent", label: "累计消费" },
    { id: "cards", label: "持有会员卡" },
  ]
  
  // 处理字段选择
  const toggleField = (fieldId: string) => {
    if (selectedFields.includes(fieldId)) {
      setSelectedFields(selectedFields.filter(id => id !== fieldId))
    } else {
      setSelectedFields([...selectedFields, fieldId])
    }
  }
  
  // 全选/取消全选
  const toggleAllFields = (checked: boolean) => {
    if (checked) {
      setSelectedFields(availableFields.map(field => field.id))
    } else {
      setSelectedFields([])
    }
  }
  
  // 处理导出
  const handleExport = async () => {
    if (selectedFields.length === 0) {
      toast({
        title: "请选择导出字段",
        description: "请至少选择一个字段进行导出",
        variant: "destructive",
      })
      return
    }
    
    setIsExporting(true)
    
    try {
      // 模拟导出过程
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const fileName = `会员数据_${new Date().toISOString().split('T')[0]}.${fileFormat === "excel" ? "xlsx" : "csv"}`
      
      toast({
        title: "导出成功",
        description: `已成功导出 ${memberCount} 名会员数据至 ${fileName}`,
      })
      
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出会员数据时出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>批量导出会员</DialogTitle>
          <DialogDescription>
            导出 {memberCount} 名选中的会员数据
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div>
            <Label>文件格式</Label>
            <RadioGroup
              value={fileFormat}
              onValueChange={(value) => setFileFormat(value as "excel" | "csv")}
              className="flex space-x-4 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="excel" id="excel" />
                <Label htmlFor="excel" className="flex items-center">
                  <FileSpreadsheet className="mr-2 h-4 w-4" />
                  Excel (.xlsx)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="csv" id="csv" />
                <Label htmlFor="csv" className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  CSV (.csv)
                </Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>选择导出字段</Label>
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="select-all"
                  checked={selectedFields.length === availableFields.length}
                  onCheckedChange={toggleAllFields}
                />
                <Label htmlFor="select-all" className="text-sm cursor-pointer">
                  全选
                </Label>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2 mt-2 border rounded-md p-4 max-h-[300px] overflow-y-auto">
              {availableFields.map(field => (
                <div key={field.id} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`field-${field.id}`}
                    checked={selectedFields.includes(field.id)}
                    onCheckedChange={() => toggleField(field.id)}
                  />
                  <Label 
                    htmlFor={`field-${field.id}`}
                    className="text-sm cursor-pointer"
                  >
                    {field.label}
                  </Label>
                </div>
              ))}
            </div>
            
            <p className="text-sm text-muted-foreground">
              已选择 {selectedFields.length} 个字段（共 {availableFields.length} 个）
            </p>
          </div>
        </div>
        
        <DialogFooter className="flex justify-between">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">将导出 {memberCount} 名会员数据</span>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleExport} disabled={isExporting}>
              {isExporting ? (
                "导出中..."
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  导出数据
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
