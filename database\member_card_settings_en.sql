-- Member Card Advanced Settings Tables (English Version)

-- Drop existing tables
DROP TABLE IF EXISTS member_card_course_associations;
DROP TABLE IF EXISTS member_card_sales_settings;
DROP TABLE IF EXISTS member_card_course_settings;
DROP TABLE IF EXISTS member_card_user_settings;
DROP TABLE IF EXISTS member_card_advanced_settings;

-- 1. Member Card Advanced Settings Table
CREATE TABLE member_card_advanced_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT 'Member card type ID',
  tenant_id INT NOT NULL COMMENT 'Tenant ID',
  
  -- Leave option settings
  leave_option ENUM('no_allow', 'no_limit', 'limited') DEFAULT 'no_limit' COMMENT 'Leave option',
  leave_times_limit INT DEFAULT NULL COMMENT 'Leave times limit',
  leave_days_limit INT DEFAULT NULL COMMENT 'Leave days limit',
  
  -- Card activation settings
  auto_activate_days INT DEFAULT 120 COMMENT 'Auto activate days',
  
  -- Booking limit settings
  max_people_per_class INT DEFAULT 1 COMMENT 'Max people per class',
  daily_booking_limit INT DEFAULT 3 COMMENT 'Daily booking limit',
  weekly_booking_limit INT DEFAULT 4 COMMENT 'Weekly booking limit',
  weekly_calculation_type ENUM('natural_week', 'card_cycle') DEFAULT 'natural_week' COMMENT 'Weekly calculation type',
  monthly_booking_limit INT DEFAULT 5 COMMENT 'Monthly booking limit',
  monthly_calculation_type ENUM('natural_month', 'card_cycle') DEFAULT 'natural_month' COMMENT 'Monthly calculation type',
  
  -- Advance booking settings
  advance_booking_days INT DEFAULT NULL COMMENT 'Advance booking days',
  advance_booking_unlimited BOOLEAN DEFAULT TRUE COMMENT 'Advance booking unlimited',
  
  -- Available time settings
  custom_time_enabled BOOLEAN DEFAULT FALSE COMMENT 'Custom time enabled',
  available_days JSON COMMENT 'Available days config',
  available_time_slots JSON COMMENT 'Available time slots config',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Created at',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated at',
  
  UNIQUE KEY unique_card_tenant (card_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Card Advanced Settings';

-- 2. Member Card User Settings Table
CREATE TABLE member_card_user_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT 'Member card type ID',
  tenant_id INT NOT NULL COMMENT 'Tenant ID',
  
  -- Booking interval limit
  booking_interval_enabled BOOLEAN DEFAULT FALSE COMMENT 'Booking interval enabled',
  booking_interval_minutes INT DEFAULT 0 COMMENT 'Booking interval minutes',
  
  -- Pending booking limit
  pending_booking_limit INT DEFAULT 0 COMMENT 'Pending booking limit',
  
  -- Cancel booking limit
  cancel_limit_enabled BOOLEAN DEFAULT FALSE COMMENT 'Cancel limit enabled',
  cancel_limit_count INT DEFAULT 0 COMMENT 'Cancel limit count',
  cancel_limit_period ENUM('day', 'week', 'month') DEFAULT 'week' COMMENT 'Cancel limit period',
  
  -- Same course daily limit
  same_course_daily_limit INT DEFAULT 1 COMMENT 'Same course daily limit',
  
  -- Peak time limit
  peak_time_enabled BOOLEAN DEFAULT FALSE COMMENT 'Peak time enabled',
  peak_start_time TIME DEFAULT '18:00:00' COMMENT 'Peak start time',
  peak_end_time TIME DEFAULT '21:00:00' COMMENT 'Peak end time',
  peak_daily_limit INT DEFAULT 1 COMMENT 'Peak daily limit',
  
  -- Booking priority
  priority_enabled BOOLEAN DEFAULT FALSE COMMENT 'Priority enabled',
  priority_hours INT DEFAULT 24 COMMENT 'Priority hours',
  priority_description VARCHAR(200) DEFAULT 'Priority booking for members' COMMENT 'Priority description',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Created at',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated at',
  
  UNIQUE KEY unique_card_tenant (card_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Card User Settings';

-- 3. Member Card Course Settings Table
CREATE TABLE member_card_course_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT 'Member card type ID',
  tenant_id INT NOT NULL COMMENT 'Tenant ID',
  
  -- Consumption rule settings
  consumption_rule ENUM('AVERAGE', 'FIXED', 'CUSTOM') DEFAULT 'AVERAGE' COMMENT 'Consumption rule',
  consumption_description VARCHAR(200) DEFAULT 'Average consumption rule' COMMENT 'Consumption description',
  
  -- Gift settings
  gift_class_count INT DEFAULT 0 COMMENT 'Gift class count',
  gift_value_coefficient DECIMAL(5,2) DEFAULT 1.0 COMMENT 'Gift value coefficient',
  
  -- Course application settings
  all_courses_enabled BOOLEAN DEFAULT TRUE COMMENT 'All courses enabled',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Created at',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated at',
  
  UNIQUE KEY unique_card_tenant (card_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Card Course Settings';

-- 4. Member Card Course Associations Table
CREATE TABLE member_card_course_associations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT 'Member card type ID',
  course_type_id INT NOT NULL COMMENT 'Course type ID',
  tenant_id INT NOT NULL COMMENT 'Tenant ID',
  
  -- Association settings
  is_enabled BOOLEAN DEFAULT TRUE COMMENT 'Is enabled',
  consumption_times DECIMAL(5,2) DEFAULT 1.0 COMMENT 'Consumption times',
  
  -- Course information
  course_type_name VARCHAR(100) COMMENT 'Course type name',
  course_duration INT COMMENT 'Course duration',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Created at',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated at',
  
  UNIQUE KEY unique_card_course_tenant (card_type_id, course_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE,
  FOREIGN KEY (course_type_id) REFERENCES coursetype(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Card Course Associations';

-- 5. Member Card Sales Settings Table
CREATE TABLE member_card_sales_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT 'Member card type ID',
  tenant_id INT NOT NULL COMMENT 'Tenant ID',
  
  -- Discount settings
  enable_discount BOOLEAN DEFAULT FALSE COMMENT 'Enable discount',
  discount_percentage DECIMAL(5,2) COMMENT 'Discount percentage',
  
  -- Promotion settings
  enable_promotion BOOLEAN DEFAULT FALSE COMMENT 'Enable promotion',
  promotion_type ENUM('new', 'renewal', 'group', 'holiday') COMMENT 'Promotion type',
  promotion_description VARCHAR(200) COMMENT 'Promotion description',
  
  -- Price description
  price_description TEXT COMMENT 'Price description',
  
  -- Sales limits
  max_sales_total INT COMMENT 'Max sales total',
  max_sales_daily INT COMMENT 'Max sales daily',
  max_per_user INT DEFAULT 1 COMMENT 'Max per user',
  
  -- Sales time
  sale_start_date DATETIME COMMENT 'Sale start date',
  sale_end_date DATETIME COMMENT 'Sale end date',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Created at',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated at',
  
  UNIQUE KEY unique_card_tenant (card_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Card Sales Settings';

-- Create indexes
CREATE INDEX idx_card_type_tenant ON member_card_advanced_settings(card_type_id, tenant_id);
CREATE INDEX idx_card_type_tenant_user ON member_card_user_settings(card_type_id, tenant_id);
CREATE INDEX idx_card_type_tenant_course ON member_card_course_settings(card_type_id, tenant_id);
CREATE INDEX idx_card_course_tenant ON member_card_course_associations(card_type_id, course_type_id, tenant_id);
CREATE INDEX idx_card_type_tenant_sales ON member_card_sales_settings(card_type_id, tenant_id);
