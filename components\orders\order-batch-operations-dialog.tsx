"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { 
  AlertCircle, 
  CheckCircle, 
  Download, 
  FileText, 
  Printer, 
  Send, 
  Truck, 
  X 
} from "lucide-react"

interface OrderBatchOperationsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedOrders: string[];
  onApplyOperation: (operation: string) => void;
}

export function OrderBatchOperationsDialog({ 
  open, 
  onOpenChange, 
  selectedOrders, 
  onApplyOperation 
}: OrderBatchOperationsDialogProps) {
  const [activeTab, setActiveTab] = useState("status")
  const [statusOperation, setStatusOperation] = useState("mark_completed")
  const [deliveryStatus, setDeliveryStatus] = useState("shipping")
  const [logisticsCompany, setLogisticsCompany] = useState("")
  const [exportFormat, setExportFormat] = useState("excel")
  const [notifyMethod, setNotifyMethod] = useState("sms")
  const [notifyContent, setNotifyContent] = useState("")
  const [remark, setRemark] = useState("")

  // 处理应用操作
  const handleApplyOperation = () => {
    let operation = "";
    
    switch (activeTab) {
      case "status":
        operation = statusOperation === "mark_completed" ? "标记为已完成" : 
                   statusOperation === "mark_cancelled" ? "标记为已取消" : 
                   "标记为处理中";
        break;
      case "delivery":
        operation = `更新物流状态为 ${deliveryStatus}`;
        break;
      case "export":
        operation = `导出订单为 ${exportFormat}`;
        break;
      case "notify":
        operation = `通过 ${notifyMethod} 通知客户`;
        break;
      case "remark":
        operation = "更新订单备注";
        break;
    }
    
    onApplyOperation(operation);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>批量操作</DialogTitle>
          <DialogDescription>
            已选择 {selectedOrders.length} 个订单进行批量操作
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="status">状态更新</TabsTrigger>
            <TabsTrigger value="delivery">物流更新</TabsTrigger>
            <TabsTrigger value="export">导出订单</TabsTrigger>
            <TabsTrigger value="notify">客户通知</TabsTrigger>
            <TabsTrigger value="remark">批量备注</TabsTrigger>
          </TabsList>

          <TabsContent value="status" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">更新订单状态</CardTitle>
                <CardDescription>
                  为所选订单批量更新状态
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup value={statusOperation} onValueChange={setStatusOperation}>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="mark_completed" id="mark_completed" />
                    <Label htmlFor="mark_completed" className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      标记为已完成
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="mark_processing" id="mark_processing" />
                    <Label htmlFor="mark_processing" className="flex items-center">
                      <Truck className="h-4 w-4 mr-2 text-blue-500" />
                      标记为处理中
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="mark_cancelled" id="mark_cancelled" />
                    <Label htmlFor="mark_cancelled" className="flex items-center">
                      <X className="h-4 w-4 mr-2 text-red-500" />
                      标记为已取消
                    </Label>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="delivery" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">更新物流信息</CardTitle>
                <CardDescription>
                  为所选订单批量更新物流状态
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="delivery-status">物流状态</Label>
                  <Select value={deliveryStatus} onValueChange={setDeliveryStatus}>
                    <SelectTrigger id="delivery-status">
                      <SelectValue placeholder="选择物流状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">待发货</SelectItem>
                      <SelectItem value="shipping">配送中</SelectItem>
                      <SelectItem value="delivered">已送达</SelectItem>
                      <SelectItem value="returned">已退回</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="logistics-company">物流公司</Label>
                  <Select value={logisticsCompany} onValueChange={setLogisticsCompany}>
                    <SelectTrigger id="logistics-company">
                      <SelectValue placeholder="选择物流公司" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sf">顺丰速运</SelectItem>
                      <SelectItem value="yt">圆通速递</SelectItem>
                      <SelectItem value="zt">中通快递</SelectItem>
                      <SelectItem value="yd">韵达快递</SelectItem>
                      <SelectItem value="jd">京东物流</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="pt-2 text-sm text-muted-foreground">
                  <AlertCircle className="h-4 w-4 inline-block mr-1" />
                  注意: 此操作将批量更新所选订单的物流状态，但不会自动生成物流单号。
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">导出订单</CardTitle>
                <CardDescription>
                  将所选订单导出为文件
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup value={exportFormat} onValueChange={setExportFormat}>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="excel" id="excel" />
                    <Label htmlFor="excel" className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-green-600" />
                      Excel 格式 (.xlsx)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="csv" id="csv" />
                    <Label htmlFor="csv" className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-blue-600" />
                      CSV 格式 (.csv)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pdf" id="pdf" />
                    <Label htmlFor="pdf" className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-red-600" />
                      PDF 格式 (.pdf)
                    </Label>
                  </div>
                </RadioGroup>

                <div className="flex items-center mt-4 pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    导出 {selectedOrders.length} 个订单
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notify" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">客户通知</CardTitle>
                <CardDescription>
                  向所选订单的客户发送通知
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="notify-method">通知方式</Label>
                  <Select value={notifyMethod} onValueChange={setNotifyMethod}>
                    <SelectTrigger id="notify-method">
                      <SelectValue placeholder="选择通知方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sms">短信</SelectItem>
                      <SelectItem value="email">邮件</SelectItem>
                      <SelectItem value="wechat">微信</SelectItem>
                      <SelectItem value="app">APP推送</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notify-content">通知内容</Label>
                  <Textarea
                    id="notify-content"
                    value={notifyContent}
                    onChange={(e) => setNotifyContent(e.target.value)}
                    placeholder="输入要发送给客户的通知内容"
                    className="min-h-[120px]"
                  />
                </div>

                <div className="flex items-center mt-2">
                  <Button variant="outline" className="w-full">
                    <Send className="h-4 w-4 mr-2" />
                    发送通知
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="remark" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">批量添加备注</CardTitle>
                <CardDescription>
                  为所选订单批量添加备注信息
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="remark">备注内容</Label>
                  <Textarea
                    id="remark"
                    value={remark}
                    onChange={(e) => setRemark(e.target.value)}
                    placeholder="输入要添加的备注内容"
                    className="min-h-[150px]"
                  />
                </div>

                <div className="pt-2 text-sm text-muted-foreground">
                  <AlertCircle className="h-4 w-4 inline-block mr-1" />
                  注意: 此操作将覆盖所选订单的现有备注信息。
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleApplyOperation}>
            应用操作
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
