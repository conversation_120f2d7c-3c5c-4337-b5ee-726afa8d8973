import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BarChart3, Calendar, Download, LineChart, PieChart, TrendingUp, Users } from "lucide-react"
import { Progress } from "@/components/ui/progress"

export default function MarketingAnalyticsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">营销数据</h1>
          <p className="text-muted-foreground">查看和分析各类营销活动的效果和转化率</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" className="h-8 w-8">
              <Calendar className="h-4 w-4" />
            </Button>
            <Select defaultValue="30days">
              <SelectTrigger className="w-[180px] h-8">
                <SelectValue placeholder="时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7days">最近7天</SelectItem>
                <SelectItem value="30days">最近30天</SelectItem>
                <SelectItem value="90days">最近90天</SelectItem>
                <SelectItem value="year">今年</SelectItem>
                <SelectItem value="custom">自定义范围</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总营销活动数</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">较上月 +4</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">营销总收入</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥128,450</div>
            <p className="text-xs text-muted-foreground">较上月 +15.2%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">新增会员数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">342</div>
            <p className="text-xs text-muted-foreground">较上月 +8.7%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均转化率</CardTitle>
            <PieChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">28.6%</div>
            <p className="text-xs text-muted-foreground">较上月 +2.3%</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="coupons">优惠券分析</TabsTrigger>
          <TabsTrigger value="promotions">促销活动分析</TabsTrigger>
          <TabsTrigger value="points">积分分析</TabsTrigger>
          <TabsTrigger value="channels">渠道分析</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="mt-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>营销活动效果对比</CardTitle>
                <CardDescription>各类营销活动的收入和转化率对比</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="flex h-full items-center justify-center text-muted-foreground">
                  <LineChart className="h-16 w-16" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>营销渠道分布</CardTitle>
                <CardDescription>各营销渠道的效果分析</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="flex h-full items-center justify-center text-muted-foreground">
                  <PieChart className="h-16 w-16" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>营销活动趋势</CardTitle>
                <CardDescription>近90天营销活动效果趋势</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <div className="flex h-full items-center justify-center text-muted-foreground">
                  <LineChart className="h-16 w-16" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>热门营销活动</CardTitle>
                <CardDescription>转化率最高的营销活动</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">春季会员招募活动</span>
                      <span className="text-sm font-medium">32%</span>
                    </div>
                    <Progress value={32} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">三人成团·高级瑜伽课</span>
                      <span className="text-sm font-medium">45%</span>
                    </div>
                    <Progress value={45} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">新人专享8折券</span>
                      <span className="text-sm font-medium">28%</span>
                    </div>
                    <Progress value={28} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">会员推荐计划</span>
                      <span className="text-sm font-medium">28%</span>
                    </div>
                    <Progress value={28} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">周年庆秒杀</span>
                      <span className="text-sm font-medium">68%</span>
                    </div>
                    <Progress value={68} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="coupons" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>优惠券分析</CardTitle>
              <CardDescription>优惠券使用情况和效果分析</CardDescription>
            </CardHeader>
            <CardContent>{/* 优惠券分析内容 */}</CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="promotions" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>促销活动分析</CardTitle>
              <CardDescription>各类促销活动的效果和转化率分析</CardDescription>
            </CardHeader>
            <CardContent>{/* 促销活动分析内容 */}</CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="points" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>积分分析</CardTitle>
              <CardDescription>积分发放、使用和兑换情况分析</CardDescription>
            </CardHeader>
            <CardContent>{/* 积分分析内容 */}</CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="channels" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>渠道分析</CardTitle>
              <CardDescription>各营销渠道的效果和ROI分析</CardDescription>
            </CardHeader>
            <CardContent>{/* 渠道分析内容 */}</CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

