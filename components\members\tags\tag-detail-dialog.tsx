"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Pencil, Users, Calendar, Tag, Search, Filter, Download } from "lucide-react"

type TagDetailDialogProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
  tag?: any
  onEditClick?: () => void
}

// Mock data for members with this tag
const taggedMembers = [
  {
    id: 1,
    name: "张三",
    avatar: "/placeholder.svg?height=40&width=40",
    phone: "138****1234",
    email: "<EMAIL>",
    joinDate: "2023-05-20",
    lastVisit: "2023-11-15",
    courses: 12,
  },
  {
    id: 2,
    name: "李四",
    avatar: "/placeholder.svg?height=40&width=40",
    phone: "139****5678",
    email: "<EMAIL>",
    joinDate: "2023-06-15",
    lastVisit: "2023-11-18",
    courses: 8,
  },
  {
    id: 3,
    name: "王五",
    avatar: "/placeholder.svg?height=40&width=40",
    phone: "137****9012",
    email: "<EMAIL>",
    joinDate: "2023-07-10",
    lastVisit: "2023-11-20",
    courses: 15,
  },
  {
    id: 4,
    name: "赵六",
    avatar: "/placeholder.svg?height=40&width=40",
    phone: "136****3456",
    email: "<EMAIL>",
    joinDate: "2023-08-05",
    lastVisit: "2023-11-19",
    courses: 6,
  },
]

// Mock data for tag history
const tagHistory = [
  {
    id: 1,
    date: "2023-11-20 14:30",
    action: "添加会员",
    operator: "管理员",
    member: "张三",
    details: "手动添加",
  },
  {
    id: 2,
    date: "2023-11-19 10:15",
    action: "添加会员",
    operator: "系统",
    member: "李四",
    details: "自动规则匹配",
  },
  {
    id: 3,
    date: "2023-11-18 16:45",
    action: "移除会员",
    operator: "管理员",
    member: "陈七",
    details: "不再符合条件",
  },
  {
    id: 4,
    date: "2023-11-15 09:30",
    action: "修改标签",
    operator: "管理员",
    member: "-",
    details: "修改标签颜色",
  },
]

export function TagDetailDialog({ open, onOpenChange, tag, onEditClick }: TagDetailDialogProps) {
  const [searchQuery, setSearchQuery] = useState("")

  if (!tag) return null

  const filteredMembers = taggedMembers.filter(
    (member) =>
      member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.phone.includes(searchQuery) ||
      member.email.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="h-6 w-6 rounded-full" style={{ backgroundColor: tag.color }} />
            {tag.name}
            <Badge variant={tag.isAutoTag ? "secondary" : "outline"}>{tag.isAutoTag ? "自动" : "手动"}</Badge>
          </DialogTitle>
          <DialogDescription>{tag.description}</DialogDescription>
        </DialogHeader>

        <div className="flex justify-between items-center">
          <div className="flex gap-4">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">会员数量</p>
              <p className="text-2xl font-bold">{tag.members}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">分类</p>
              <p className="text-lg font-medium">{tag.category}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">创建时间</p>
              <p className="text-lg font-medium">{tag.createdAt}</p>
            </div>
          </div>
          <Button onClick={onEditClick}>
            <Pencil className="mr-2 h-4 w-4" />
            编辑标签
          </Button>
        </div>

        <Tabs defaultValue="members" className="mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="members">
              <Users className="mr-2 h-4 w-4" />
              标签会员
            </TabsTrigger>
            <TabsTrigger value="history">
              <Calendar className="mr-2 h-4 w-4" />
              操作历史
            </TabsTrigger>
            <TabsTrigger value="rules">
              <Tag className="mr-2 h-4 w-4" />
              标签规则
            </TabsTrigger>
          </TabsList>

          <TabsContent value="members" className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="relative w-full max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索会员"
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  筛选
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  导出
                </Button>
              </div>
            </div>

            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>会员</TableHead>
                      <TableHead>联系方式</TableHead>
                      <TableHead>注册日期</TableHead>
                      <TableHead>最近访问</TableHead>
                      <TableHead>课程数</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMembers.map((member) => (
                      <TableRow key={member.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar>
                              <AvatarImage src={member.avatar} alt={member.name} />
                              <AvatarFallback>{member.name.slice(0, 1)}</AvatarFallback>
                            </Avatar>
                            <span>{member.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-sm">{member.phone}</span>
                            <span className="text-xs text-muted-foreground">{member.email}</span>
                          </div>
                        </TableCell>
                        <TableCell>{member.joinDate}</TableCell>
                        <TableCell>{member.lastVisit}</TableCell>
                        <TableCell>{member.courses}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>时间</TableHead>
                      <TableHead>操作</TableHead>
                      <TableHead>操作人</TableHead>
                      <TableHead>会员</TableHead>
                      <TableHead>详情</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tagHistory.map((history) => (
                      <TableRow key={history.id}>
                        <TableCell>{history.date}</TableCell>
                        <TableCell>{history.action}</TableCell>
                        <TableCell>{history.operator}</TableCell>
                        <TableCell>{history.member}</TableCell>
                        <TableCell>{history.details}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rules">
            {tag.isAutoTag ? (
              <Card>
                <CardHeader>
                  <CardTitle>自动标���规则</CardTitle>
                  <CardDescription>符合以下条件的会员将自动添加此标签</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="rounded-md border p-4">
                    <h4 className="font-medium mb-2">条件组 1（满足所有条件）</h4>
                    <ul className="space-y-2 pl-4">
                      <li className="flex items-center gap-2">
                        <Badge variant="outline">注册时间</Badge>
                        <span>小于</span>
                        <Badge>30天</Badge>
                      </li>
                    </ul>
                  </div>

                  <div className="rounded-md border p-4">
                    <h4 className="font-medium mb-2">条件组 2（满足所有条件）</h4>
                    <ul className="space-y-2 pl-4">
                      <li className="flex items-center gap-2">
                        <Badge variant="outline">消费金额</Badge>
                        <span>大于</span>
                        <Badge>¥5000</Badge>
                      </li>
                      <li className="flex items-center gap-2">
                        <Badge variant="outline">课程类型</Badge>
                        <span>包含</span>
                        <Badge>瑜伽</Badge>
                      </li>
                    </ul>
                  </div>

                  <p className="text-sm text-muted-foreground italic">
                    注：会员满足任一条件组中的所有条件即可自动添加此标签
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Tag className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">此标签为手动标签</h3>
                <p className="text-sm text-muted-foreground max-w-md mt-2">
                  手动标签不包含自动规则，需要管理员手动为会员添加或移除此标签。
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

