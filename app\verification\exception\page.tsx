"use client"

import { Checkbox } from "@/components/ui/checkbox"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Search, AlertCircle, Clock, Filter, MoreHorizontal } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function VerificationExceptionPage() {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })
  const [selectedRecord, setSelectedRecord] = useState<any>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isResolveDialogOpen, setIsResolveDialogOpen] = useState(false)

  const [exceptions] = useState([
    {
      id: "EX12345678",
      code: "MT34567890",
      platform: "美团",
      product: "瑜伽垫+瑜伽服套装",
      price: "¥299",
      status: "failed",
      reason: "券码已被使用",
      verifiedAt: "2025-03-28 09:45:18",
      operator: "管理员",
      resolved: false,
      customer: {
        name: "王五",
        phone: "137****9012",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "EX23456789",
      code: "DY34567890",
      platform: "抖音",
      product: "瑜伽会员月卡",
      price: "¥599",
      status: "failed",
      reason: "券码已过期",
      verifiedAt: "2025-03-27 10:45:18",
      operator: "前台",
      resolved: false,
      customer: {
        name: "孙八",
        phone: "134****1234",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "EX34567890",
      code: "MT45678901",
      platform: "美团",
      product: "高级瑜伽单次课（90分钟）",
      price: "¥129",
      status: "failed",
      reason: "商品不匹配",
      verifiedAt: "2025-03-26 14:22:36",
      operator: "教练",
      resolved: true,
      resolvedAt: "2025-03-26 15:10:45",
      resolvedBy: "管理员",
      resolution: "已联系美团客服确认，并手动核销",
      customer: {
        name: "钱七",
        phone: "135****7890",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
  ])

  const handleViewDetail = (record: any) => {
    setSelectedRecord(record)
    setIsDetailDialogOpen(true)
  }

  const handleResolve = (record: any) => {
    setSelectedRecord(record)
    setIsResolveDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">异常核销处理</h1>
        <div className="flex items-center gap-2">
          <Alert className="w-auto py-2">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>有 2 条未处理的异常记录</AlertTitle>
          </Alert>
        </div>
      </div>

      <Tabs defaultValue="unresolved" className="space-y-4">
        <TabsList>
          <TabsTrigger value="unresolved">未处理异常</TabsTrigger>
          <TabsTrigger value="resolved">已处理异常</TabsTrigger>
          <TabsTrigger value="all">全部异常</TabsTrigger>
        </TabsList>

        <TabsContent value="unresolved" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>未处理异常记录</CardTitle>
              <CardDescription>需要处理的异常核销记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <Label htmlFor="search-query" className="mb-2 block">
                    搜索
                  </Label>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input id="search-query" placeholder="券码/商品名称/用户名" className="pl-8" />
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block">日期范围</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                        <Clock className="mr-2 h-4 w-4" />
                        {dateRange.from ? (
                          dateRange.to ? (
                            <>
                              {format(dateRange.from, "yyyy-MM-dd")} - {format(dateRange.to, "yyyy-MM-dd")}
                            </>
                          ) : (
                            format(dateRange.from, "yyyy-MM-dd")
                          )
                        ) : (
                          <span>选择日期范围</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="range"
                        defaultMonth={date}
                        selected={dateRange}
                        onSelect={setDateRange}
                        numberOfMonths={2}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <Label htmlFor="platform-filter" className="mb-2 block">
                    平台
                  </Label>
                  <Select defaultValue="all">
                    <SelectTrigger id="platform-filter" className="w-[180px]">
                      <SelectValue placeholder="选择平台" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部平台</SelectItem>
                      <SelectItem value="meituan">美团</SelectItem>
                      <SelectItem value="douyin">抖音</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button>
                    <Filter className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>异常ID</TableHead>
                    <TableHead>券码</TableHead>
                    <TableHead>平台</TableHead>
                    <TableHead>商品</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>异常原因</TableHead>
                    <TableHead>发生时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {exceptions
                    .filter((ex) => !ex.resolved)
                    .map((exception) => (
                      <TableRow key={exception.id}>
                        <TableCell className="font-medium">{exception.id}</TableCell>
                        <TableCell>{exception.code}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={exception.platform === "美团" ? "bg-yellow-50" : "bg-black text-white"}
                          >
                            {exception.platform}
                          </Badge>
                        </TableCell>
                        <TableCell>{exception.product}</TableCell>
                        <TableCell>{exception.price}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={exception.customer.avatar} />
                              <AvatarFallback>{exception.customer.name[0]}</AvatarFallback>
                            </Avatar>
                            <span>{exception.customer.name}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-red-600">{exception.reason}</TableCell>
                        <TableCell>{exception.verifiedAt}</TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>操作</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewDetail(exception)}>查看详情</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleResolve(exception)}>处理异常</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resolved" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>已处理异常记录</CardTitle>
              <CardDescription>已经处理完成的异常核销记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>异常ID</TableHead>
                    <TableHead>券码</TableHead>
                    <TableHead>平台</TableHead>
                    <TableHead>商品</TableHead>
                    <TableHead>异常原因</TableHead>
                    <TableHead>处理方式</TableHead>
                    <TableHead>处理时间</TableHead>
                    <TableHead>处理人</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {exceptions
                    .filter((ex) => ex.resolved)
                    .map((exception) => (
                      <TableRow key={exception.id}>
                        <TableCell className="font-medium">{exception.id}</TableCell>
                        <TableCell>{exception.code}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={exception.platform === "美团" ? "bg-yellow-50" : "bg-black text-white"}
                          >
                            {exception.platform}
                          </Badge>
                        </TableCell>
                        <TableCell>{exception.product}</TableCell>
                        <TableCell>{exception.reason}</TableCell>
                        <TableCell>{exception.resolution}</TableCell>
                        <TableCell>{exception.resolvedAt}</TableCell>
                        <TableCell>{exception.resolvedBy}</TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm" onClick={() => handleViewDetail(exception)}>
                            详情
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>全部异常记录</CardTitle>
              <CardDescription>所有异常核销记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8">
                <p className="text-muted-foreground">全部异常记录内容与未处理和已处理异常记录合并显示</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 异常详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>异常核销详情</DialogTitle>
            <DialogDescription>查看异常核销记录的详细信息</DialogDescription>
          </DialogHeader>

          {selectedRecord && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Badge variant="destructive">核销失败</Badge>
                  <Badge
                    variant="outline"
                    className={selectedRecord.platform === "美团" ? "bg-yellow-50" : "bg-black text-white"}
                  >
                    {selectedRecord.platform}
                  </Badge>
                </div>
                <Badge
                  variant={selectedRecord.resolved ? "outline" : "secondary"}
                  className={selectedRecord.resolved ? "bg-green-50 text-green-600" : ""}
                >
                  {selectedRecord.resolved ? "已处理" : "未处理"}
                </Badge>
              </div>

              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">异常ID：</span>
                    <span className="font-medium">{selectedRecord.id}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">券码：</span>
                    <span className="font-medium">{selectedRecord.code}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">价格：</span>
                    <span className="font-medium">{selectedRecord.price}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-muted-foreground">商品：</span>
                    <span className="font-medium">{selectedRecord.product}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-muted-foreground">异常原因：</span>
                    <span className="font-medium text-red-600">{selectedRecord.reason}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-muted-foreground">发生时间：</span>
                    <span className="font-medium">{selectedRecord.verifiedAt}</span>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={selectedRecord.customer.avatar} />
                    <AvatarFallback>{selectedRecord.customer.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{selectedRecord.customer.name}</div>
                    <div className="text-sm text-muted-foreground">{selectedRecord.customer.phone}</div>
                  </div>
                </div>

                {selectedRecord.resolved && (
                  <>
                    <Separator />

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">处理信息</h4>
                      <div className="text-sm">
                        <p className="flex justify-between">
                          <span>处理时间</span>
                          <span className="text-muted-foreground">{selectedRecord.resolvedAt}</span>
                        </p>
                        <p>
                          <span className="text-muted-foreground">处理人：</span>
                          <span>{selectedRecord.resolvedBy}</span>
                        </p>
                        <p className="mt-1">
                          <span className="text-muted-foreground">处理方式：</span>
                          <span>{selectedRecord.resolution}</span>
                        </p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}

          <DialogFooter>
            {selectedRecord && !selectedRecord.resolved && (
              <Button
                variant="default"
                onClick={() => {
                  setIsDetailDialogOpen(false)
                  handleResolve(selectedRecord)
                }}
              >
                处理异常
              </Button>
            )}
            <Button variant="outline" onClick={() => setIsDetailDialogOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 处理异常对话框 */}
      <Dialog open={isResolveDialogOpen} onOpenChange={setIsResolveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>处理异常核销</DialogTitle>
            <DialogDescription>请选择处理方式并填写处理说明</DialogDescription>
          </DialogHeader>

          {selectedRecord && (
            <div className="space-y-4">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>核销失败：{selectedRecord.reason}</AlertTitle>
                <AlertDescription>
                  券码：{selectedRecord.code}，商品：{selectedRecord.product}
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label htmlFor="resolution-type">处理方式</Label>
                <Select defaultValue="manual">
                  <SelectTrigger id="resolution-type">
                    <SelectValue placeholder="选择处理方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="manual">手动核销</SelectItem>
                    <SelectItem value="contact">联系平台客服</SelectItem>
                    <SelectItem value="refund">退款处理</SelectItem>
                    <SelectItem value="ignore">忽略异常</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="resolution-note">处理说明</Label>
                <Textarea id="resolution-note" placeholder="请输入处理说明" rows={4} />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox id="notify-customer" />
                <label htmlFor="notify-customer" className="text-sm">
                  通知用户处理结果
                </label>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsResolveDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setIsResolveDialogOpen(false)}>确认处理</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

