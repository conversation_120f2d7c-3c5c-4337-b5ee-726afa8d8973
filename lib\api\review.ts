/**
 * 评价系统API
 */
import axios from 'axios';
import { Review, ReviewFilterParams, ReviewApiResponse } from '../types/review';

// 创建axios实例
const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 评价API
export const reviewApi = {
  /**
   * 获取评价列表
   * @param params 筛选参数
   */
  getReviews: async (params?: ReviewFilterParams): Promise<ReviewApiResponse> => {
    try {
      // 实际项目中应该调用后端API
      // const response = await axiosInstance.get('/api/reviews', { params });
      // return response.data;

      // 模拟API响应
      return {
        data: mockReviews.filter(review => {
          // 关键词搜索
          if (params?.keyword) {
            const keyword = params.keyword.toLowerCase();
            if (
              !review.comment.toLowerCase().includes(keyword) &&
              !review.coach.name.toLowerCase().includes(keyword) &&
              !review.member.name.toLowerCase().includes(keyword) &&
              !review.course.name.toLowerCase().includes(keyword)
            ) {
              return false;
            }
          }

          // 教练筛选
          if (params?.coachId && params.coachId !== 'all') {
            if (review.coachId !== Number(params.coachId)) {
              return false;
            }
          }

          // 课程类型筛选
          if (params?.courseType && params.courseType !== 'all') {
            if (review.course.type !== params.courseType) {
              return false;
            }
          }

          // 评分筛选
          if (params?.rating && params.rating !== 'all') {
            if (params.rating === '5' && review.rating !== 5) return false;
            if (params.rating === '4' && review.rating !== 4) return false;
            if (params.rating === '3' && review.rating <= 3) return false;
          }

          // 状态筛选
          if (params?.status && review.status !== params.status) {
            return false;
          }

          // 回复状态筛选
          if (params?.hasReply !== undefined) {
            if (params.hasReply !== review.hasReply) {
              return false;
            }
          }

          // 情感分析筛选
          if (params?.sentiment && params.sentiment !== 'all') {
            if (review.sentiment !== params.sentiment) {
              return false;
            }
          }

          return true;
        }),
        stats: mockStats,
        pagination: {
          total: mockReviews.length,
          page: 1,
          pageSize: 10,
          totalPages: Math.ceil(mockReviews.length / 10),
        },
      };
    } catch (error) {
      console.error('获取评价列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取评价详情
   * @param id 评价ID
   */
  getReviewById: async (id: number): Promise<Review> => {
    try {
      // 实际项目中应该调用后端API
      // const response = await axiosInstance.get(`/api/reviews/${id}`);
      // return response.data;

      // 模拟API响应
      const review = mockReviews.find(r => r.id === id);
      if (!review) {
        throw new Error('评价不存在');
      }
      return review;
    } catch (error) {
      console.error('获取评价详情失败:', error);
      throw error;
    }
  },

  /**
   * 更新评价状态
   * @param id 评价ID
   * @param status 新状态
   */
  updateReviewStatus: async (id: number, status: string): Promise<Review> => {
    try {
      // 实际项目中应该调用后端API
      // const response = await axiosInstance.patch(`/api/reviews/${id}/status`, { status });
      // return response.data;

      // 模拟API响应
      const reviewIndex = mockReviews.findIndex(r => r.id === id);
      if (reviewIndex === -1) {
        throw new Error('评价不存在');
      }

      mockReviews[reviewIndex] = {
        ...mockReviews[reviewIndex],
        status: status as any,
      };

      return mockReviews[reviewIndex];
    } catch (error) {
      console.error('更新评价状态失败:', error);
      throw error;
    }
  },

  /**
   * 回复评价
   * @param id 评价ID
   * @param reply 回复内容
   */
  replyToReview: async (id: number, reply: string): Promise<Review> => {
    try {
      // 实际项目中应该调用后端API
      // const response = await axiosInstance.post(`/api/reviews/${id}/reply`, { reply });
      // return response.data;

      // 模拟API响应
      const reviewIndex = mockReviews.findIndex(r => r.id === id);
      if (reviewIndex === -1) {
        throw new Error('评价不存在');
      }

      mockReviews[reviewIndex] = {
        ...mockReviews[reviewIndex],
        hasReply: true,
        reply,
        replyDate: new Date().toISOString().split('T')[0],
      };

      return mockReviews[reviewIndex];
    } catch (error) {
      console.error('回复评价失败:', error);
      throw error;
    }
  },

  /**
   * 批量更新评价状态
   * @param ids 评价ID数组
   * @param status 新状态
   */
  batchUpdateStatus: async (ids: number[], status: string): Promise<void> => {
    try {
      // 实际项目中应该调用后端API
      // await axiosInstance.patch('/api/reviews/batch-status', { ids, status });

      // 模拟API响应
      ids.forEach(id => {
        const reviewIndex = mockReviews.findIndex(r => r.id === id);
        if (reviewIndex !== -1) {
          mockReviews[reviewIndex] = {
            ...mockReviews[reviewIndex],
            status: status as any,
          };
        }
      });
    } catch (error) {
      console.error('批量更新评价状态失败:', error);
      throw error;
    }
  },
};

// 模拟数据 - 将在实际项目中移除
const mockReviews: Review[] = [
  {
    id: 101,
    coachId: 1,
    coach: {
      id: 1,
      name: "张教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    courseId: 1001,
    course: {
      id: 1001,
      name: "基础瑜伽入门",
      type: "基础瑜伽",
    },
    memberId: "M001",
    member: {
      id: "M001",
      name: "李四",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "金卡会员",
      joinDate: "2024-01-15",
    },
    rating: 5,
    comment: "张教练非常专业，课程安排合理，动作讲解清晰，很有耐心。每次上课都能学到新东西，感觉自己进步很快。",
    date: "2025-03-25",
    status: "published",
    hasReply: true,
    reply: "感谢您的评价和支持！我们会继续努力提供优质的课程和服务。期待您的下次光临！",
    replyDate: "2025-03-26",
    keywords: ["专业", "耐心", "讲解清晰"],
    sentiment: "positive",
  },
  {
    id: 102,
    coachId: 1,
    coach: {
      id: 1,
      name: "张教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    courseId: 1001,
    course: {
      id: 1001,
      name: "基础瑜伽入门",
      type: "基础瑜伽",
    },
    memberId: "M002",
    member: {
      id: "M002",
      name: "王五",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "银卡会员",
      joinDate: "2024-02-20",
    },
    rating: 4,
    comment: "教练很专业，但课程节奏有点快，希望能更适合初学者。总体来说还是很满意的，会继续参加。",
    date: "2025-03-20",
    status: "published",
    hasReply: false,
    reply: "",
    replyDate: "",
    keywords: ["专业", "节奏快", "初学者"],
    sentiment: "neutral",
  },
  {
    id: 201,
    coachId: 2,
    coach: {
      id: 2,
      name: "李教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    courseId: 2001,
    course: {
      id: 2001,
      name: "高级瑜伽进阶",
      type: "高级瑜伽",
    },
    memberId: "M003",
    member: {
      id: "M003",
      name: "张三",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "白金会员",
      joinDate: "2023-11-05",
    },
    rating: 5,
    comment: "李教练的高级瑜伽课非常棒，动作示范到位，指导细致。课程难度适中，很有挑战性但不会太难。",
    date: "2025-03-26",
    status: "published",
    hasReply: true,
    reply: "谢谢您的肯定！我们会根据学员的反馈不断调整课程内容，让每位学员都能得到适合自己的锻炼。",
    replyDate: "2025-03-27",
    keywords: ["示范到位", "指导细致", "难度适中"],
    sentiment: "positive",
  },
  {
    id: 202,
    coachId: 2,
    coach: {
      id: 2,
      name: "李教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    courseId: 2001,
    course: {
      id: 2001,
      name: "高级瑜伽进阶",
      type: "高级瑜伽",
    },
    memberId: "M004",
    member: {
      id: "M004",
      name: "赵六",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "标准会员",
      joinDate: "2024-03-10",
    },
    rating: 5,
    comment: "课程内容丰富，教练很有耐心，每个动作都会详细讲解。场地环境也很好，整体体验非常棒。",
    date: "2025-03-22",
    status: "pending",
    hasReply: false,
    reply: "",
    replyDate: "",
    keywords: ["内容丰富", "耐心", "环境好"],
    sentiment: "positive",
  },
  {
    id: 301,
    coachId: 3,
    coach: {
      id: 3,
      name: "王教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    courseId: 3001,
    course: {
      id: 3001,
      name: "阴瑜伽放松",
      type: "阴瑜伽",
    },
    memberId: "M005",
    member: {
      id: "M005",
      name: "钱七",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "银卡会员",
      joinDate: "2024-01-30",
    },
    rating: 5,
    comment: "王教练的阴瑜伽课非常放松，氛围很好，音乐选择恰到好处。每次上完课都感觉身心舒畅。",
    date: "2025-03-24",
    status: "published",
    hasReply: true,
    reply: "非常感谢您的评价！阴瑜伽确实是放松身心的好方式，期待在下次课程中再见到您。",
    replyDate: "2025-03-25",
    keywords: ["放松", "氛围好", "音乐好"],
    sentiment: "positive",
  },
  {
    id: 302,
    coachId: 3,
    coach: {
      id: 3,
      name: "王教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    courseId: 3001,
    course: {
      id: 3001,
      name: "阴瑜伽放松",
      type: "阴瑜伽",
    },
    memberId: "M006",
    member: {
      id: "M006",
      name: "孙八",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "标准会员",
      joinDate: "2024-02-15",
    },
    rating: 4,
    comment: "整体不错，但课程时间有点短，希望能延长一些。教练的指导很专业，动作讲解很清晰。",
    date: "2025-03-18",
    status: "hidden",
    hasReply: false,
    reply: "",
    replyDate: "",
    keywords: ["时间短", "专业", "讲解清晰"],
    sentiment: "neutral",
  },
  {
    id: 401,
    coachId: 4,
    coach: {
      id: 4,
      name: "赵教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    courseId: 4001,
    course: {
      id: 4001,
      name: "孕产瑜伽基础",
      type: "孕产瑜伽",
    },
    memberId: "M007",
    member: {
      id: "M007",
      name: "周九",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "金卡会员",
      joinDate: "2023-12-20",
    },
    rating: 3,
    comment: "课程内容一般，感觉和网上视频差不多。教练态度还可以，但专业度有待提高。场地环境不错。",
    date: "2025-03-15",
    status: "published",
    hasReply: true,
    reply: "感谢您的反馈。我们会认真考虑您的建议，提升课程质量和教练专业度。欢迎您再次光临并给我们改进的机会。",
    replyDate: "2025-03-16",
    keywords: ["一般", "态度好", "专业度低"],
    sentiment: "negative",
  },
  {
    id: 501,
    coachId: 5,
    coach: {
      id: 5,
      name: "刘教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    courseId: 5001,
    course: {
      id: 5001,
      name: "空中瑜伽入门",
      type: "空中瑜伽",
    },
    memberId: "M008",
    member: {
      id: "M008",
      name: "吴十",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "白金会员",
      joinDate: "2023-10-10",
    },
    rating: 5,
    comment: "刘教练的空中瑜伽课非常精彩，动作编排流畅，难度适中。教练很注重安全，每个动作都有详细指导。",
    date: "2025-03-10",
    status: "published",
    hasReply: true,
    reply: "谢谢您的评价！安全确实是空中瑜伽最重要的方面，我们会继续保持高标准的教学质量。",
    replyDate: "2025-03-11",
    keywords: ["精彩", "安全", "指导详细"],
    sentiment: "positive",
  },
  {
    id: 502,
    coachId: 5,
    coach: {
      id: 5,
      name: "刘教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    courseId: 5001,
    course: {
      id: 5001,
      name: "空中瑜伽入门",
      type: "空中瑜伽",
    },
    memberId: "M009",
    member: {
      id: "M009",
      name: "郑十一",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "标准会员",
      joinDate: "2024-02-28",
    },
    rating: 2,
    comment: "课程太难了，完全跟不上节奏。教练没有考虑到初学者的情况，感觉很挫败。希望能有更适合初学者的课程。",
    date: "2025-03-05",
    status: "pending",
    hasReply: false,
    reply: "",
    replyDate: "",
    keywords: ["太难", "跟不上", "初学者"],
    sentiment: "negative",
  },
];

// 计算模拟统计数据
const published = mockReviews.filter(r => r.status === 'published').length;
const pending = mockReviews.filter(r => r.status === 'pending').length;
const hidden = mockReviews.filter(r => r.status === 'hidden').length;
const total = mockReviews.length;

const ratings = mockReviews.map(r => r.rating);
const averageRating = ratings.reduce((a, b) => a + b, 0) / ratings.length;

const ratingDistribution = {
  5: mockReviews.filter(r => r.rating === 5).length,
  4: mockReviews.filter(r => r.rating === 4).length,
  3: mockReviews.filter(r => r.rating === 3).length,
  2: mockReviews.filter(r => r.rating === 2).length,
  1: mockReviews.filter(r => r.rating === 1).length,
};

const repliedCount = mockReviews.filter(r => r.hasReply).length;
const replyRate = `${Math.round((repliedCount / total) * 100)}%`;

// 收集所有关键词
const allKeywords = mockReviews.flatMap(r => r.keywords);
const keywordCounts: Record<string, number> = {};
allKeywords.forEach(keyword => {
  keywordCounts[keyword] = (keywordCounts[keyword] || 0) + 1;
});

// 获取出现频率最高的5个关键词
const topKeywords = Object.entries(keywordCounts)
  .sort((a, b) => b[1] - a[1])
  .slice(0, 5)
  .map(([keyword]) => keyword);

const sentimentAnalysis = {
  positive: mockReviews.filter(r => r.sentiment === 'positive').length,
  neutral: mockReviews.filter(r => r.sentiment === 'neutral').length,
  negative: mockReviews.filter(r => r.sentiment === 'negative').length,
};

// 模拟统计数据
const mockStats = {
  total,
  published,
  pending,
  hidden,
  averageRating,
  ratingDistribution,
  replyRate,
  topKeywords,
  sentimentAnalysis,
};

export default reviewApi;
