"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Download, Calculator, Calendar, Filter, Plus, RefreshCw, DollarSign } from "lucide-react"
import { SalaryRecordsList } from "@/components/coach-salary/salary-records-list"
import { SalaryConfigList } from "@/components/coach-salary/salary-config-list"
import { SalaryCalculationPanel } from "@/components/coach-salary/salary-calculation-panel"
import { SalaryReportsPanel } from "@/components/coach-salary/salary-reports-panel"
import { DatePickerWithRange as DateRangePicker } from "@/components/ui/date-range-picker"
import { AdvancedFilterDialog } from "@/components/coach-salary/advanced-filter-dialog"
import { AddSalaryConfigDialog } from "@/components/coach-salary/add-salary-config-dialog"

export default function CoachSalaryPage() {
  const [activeTab, setActiveTab] = useState("records")
  const [showFilterDialog, setShowFilterDialog] = useState(false)
  const [showAddConfigDialog, setShowAddConfigDialog] = useState(false)
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    to: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">教练薪资管理</h1>
          <p className="text-muted-foreground">
            管理教练薪资配置、计算薪资并生成薪资报表
          </p>
        </div>
        <div className="flex flex-wrap gap-2">
          {activeTab === "records" && (
            <>
              <DateRangePicker
                className="w-[300px]"
                selected={dateRange}
                onSelect={setDateRange}
              />
              <Button variant="outline" size="icon" onClick={() => setShowFilterDialog(true)}>
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" className="gap-2">
                <Download className="h-4 w-4" />
                <span>导出</span>
              </Button>
              <Button className="gap-2">
                <Calculator className="h-4 w-4" />
                <span>计算薪资</span>
              </Button>
            </>
          )}
          {activeTab === "configs" && (
            <>
              <Button variant="outline" className="gap-2" onClick={() => window.location.href = "/courses/hourly-rates"}>
                <DollarSign className="h-4 w-4" />
                <span>课时费管理</span>
              </Button>
              <Button onClick={() => setShowAddConfigDialog(true)} className="gap-2">
                <Plus className="h-4 w-4" />
                <span>添加薪资配置</span>
              </Button>
            </>
          )}
          {activeTab === "calculation" && (
            <Button variant="outline" className="gap-2">
              <RefreshCw className="h-4 w-4" />
              <span>刷新数据</span>
            </Button>
          )}
          {activeTab === "reports" && (
            <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              <span>导出报表</span>
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="records" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="records">薪资记录</TabsTrigger>
          <TabsTrigger value="configs">薪资配置</TabsTrigger>
          <TabsTrigger value="calculation">薪资计算</TabsTrigger>
          <TabsTrigger value="reports">薪资报表</TabsTrigger>
        </TabsList>
        <TabsContent value="records" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>薪资记录</CardTitle>
              <CardDescription>
                查看和管理教练的薪资记录，包括审核和发放状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SalaryRecordsList dateRange={dateRange} />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="configs" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>薪资配置</CardTitle>
              <CardDescription>
                管理教练的薪资配置，包括薪资类型、底薪、提成比例等（课时费请在课时费管理中设置）
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SalaryConfigList />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="calculation" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>薪资计算</CardTitle>
              <CardDescription>
                计算教练薪资并生成薪资记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SalaryCalculationPanel />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>薪资报表</CardTitle>
              <CardDescription>
                查看薪资统计报表和趋势分析
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SalaryReportsPanel />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 高级筛选对话框 */}
      <AdvancedFilterDialog
        open={showFilterDialog}
        onOpenChange={setShowFilterDialog}
      />

      {/* 添加薪资配置对话框 */}
      <AddSalaryConfigDialog
        open={showAddConfigDialog}
        onOpenChange={setShowAddConfigDialog}
      />
    </div>
  )
}
