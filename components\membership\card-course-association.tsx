"use client"

import { useState, useEffect } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"

// 模拟课程数据
const courseData = [
  {
    id: "c1",
    name: "空中瑜伽",
    type: "空中瑜伽",
    duration: 60,
    color: "#4F46E5"
  },
  {
    id: "c2",
    name: "空中瑜伽初级",
    type: "空中瑜伽",
    duration: 60,
    color: "#4F46E5"
  },
  {
    id: "c3",
    name: "空中瑜伽中级",
    type: "空中瑜伽",
    duration: 60,
    color: "#4F46E5"
  },
  {
    id: "c4",
    name: "高空吊环",
    type: "空中瑜伽",
    duration: 60,
    color: "#4F46E5"
  },
  {
    id: "c5",
    name: "垫上瑜伽",
    type: "垫上瑜伽",
    duration: 60,
    color: "#06B6D4"
  },
  {
    id: "c6",
    name: "哈他瑜伽",
    type: "垫上瑜伽",
    duration: 60,
    color: "#06B6D4"
  },
  {
    id: "c7",
    name: "流瑜伽",
    type: "垫上瑜伽",
    duration: 75,
    color: "#06B6D4"
  },
  {
    id: "c8",
    name: "阴瑜伽",
    type: "垫上瑜伽",
    duration: 75,
    color: "#06B6D4"
  },
  {
    id: "c9",
    name: "普拉提",
    type: "垫上瑜伽",
    duration: 60,
    color: "#06B6D4"
  }
]

// 课程分类
const courseCategories = [
  { id: "cat1", name: "空中瑜伽", color: "#4F46E5" },
  { id: "cat2", name: "垫上瑜伽", color: "#06B6D4" }
]

interface CardCourseAssociationProps {
  initialSelectedCourses?: string[]
  onChange?: (selectedCourses: string[]) => void
  className?: string
}

export function CardCourseAssociation({
  initialSelectedCourses = [],
  onChange,
  className
}: CardCourseAssociationProps) {
  const [selectedCourses, setSelectedCourses] = useState<string[]>(initialSelectedCourses)
  const [searchTerm, setSearchTerm] = useState("")
  
  // 当选择变化时通知父组件
  useEffect(() => {
    if (onChange) {
      onChange(selectedCourses)
    }
  }, [selectedCourses, onChange])
  
  // 处理课程选择
  const handleCourseSelect = (courseId: string, checked: boolean) => {
    if (checked) {
      setSelectedCourses([...selectedCourses, courseId])
    } else {
      setSelectedCourses(selectedCourses.filter(id => id !== courseId))
    }
  }
  
  // 清除所有选择
  const handleClearAll = () => {
    setSelectedCourses([])
  }
  
  // 选择所有课程
  const handleSelectAll = () => {
    setSelectedCourses(courseData.map(course => course.id))
  }
  
  // 处理分类全选
  const handleSelectCategory = (categoryName: string, checked: boolean) => {
    const coursesInCategory = courseData.filter(course => course.type === categoryName)
    
    if (checked) {
      // 添加该分类下所有未选中的课程
      const newSelectedCourses = [...selectedCourses]
      coursesInCategory.forEach(course => {
        if (!newSelectedCourses.includes(course.id)) {
          newSelectedCourses.push(course.id)
        }
      })
      setSelectedCourses(newSelectedCourses)
    } else {
      // 移除该分类下所有课程
      setSelectedCourses(selectedCourses.filter(
        id => !coursesInCategory.some(course => course.id === id)
      ))
    }
  }
  
  // 检查分类是否全选
  const isCategoryAllSelected = (categoryName: string) => {
    const coursesInCategory = courseData.filter(course => course.type === categoryName)
    return coursesInCategory.every(course => selectedCourses.includes(course.id))
  }
  
  // 过滤课程
  const filteredCourseData = searchTerm
    ? courseData.filter(course => 
        course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        course.type.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : courseData
  
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium">关联课程</div>
        <div className="flex items-center space-x-2">
          <Input
            placeholder="搜索课程"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-8 w-[200px]"
          />
          <div className="flex items-center">
            <Checkbox 
              id="select-all-courses"
              checked={selectedCourses.length === courseData.length}
              onCheckedChange={(checked) => {
                if (checked) {
                  handleSelectAll()
                } else {
                  handleClearAll()
                }
              }}
            />
            <label 
              htmlFor="select-all-courses"
              className="text-xs font-medium cursor-pointer ml-2"
            >
              全选
            </label>
          </div>
        </div>
      </div>
      
      <ScrollArea className="h-[400px] border rounded-md">
        <div className="p-4 space-y-6">
          {courseCategories.map((category) => {
            const coursesInCategory = filteredCourseData.filter(course => course.type === category.name)
            
            // 如果该分类下没有符合搜索条件的课程，则不显示该分类
            if (coursesInCategory.length === 0) {
              return null
            }
            
            return (
              <div key={category.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-2" 
                      style={{ backgroundColor: category.color }}
                    />
                    <span className="font-medium">{category.name}</span>
                  </div>
                  <div className="flex items-center">
                    <Checkbox
                      id={`select-category-${category.id}`}
                      checked={isCategoryAllSelected(category.name)}
                      onCheckedChange={(checked) => handleSelectCategory(category.name, !!checked)}
                    />
                    <label
                      htmlFor={`select-category-${category.id}`}
                      className="text-xs font-medium cursor-pointer ml-2"
                    >
                      全选
                    </label>
                  </div>
                </div>
                
                <div className="space-y-1 pl-5">
                  {coursesInCategory.map(course => (
                    <div key={course.id} className="flex items-center py-2">
                      <Checkbox
                        id={`course-${course.id}`}
                        className="mr-2"
                        checked={selectedCourses.includes(course.id)}
                        onCheckedChange={(checked) => handleCourseSelect(course.id, !!checked)}
                      />
                      <label
                        htmlFor={`course-${course.id}`}
                        className="text-sm cursor-pointer flex-1"
                      >
                        <div className="flex items-center justify-between">
                          <span>{course.name}</span>
                          <span className="text-xs text-muted-foreground">{course.duration}分钟</span>
                        </div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
          
          {filteredCourseData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              没有找到匹配的课程
            </div>
          )}
        </div>
      </ScrollArea>
      
      <div className="flex items-center justify-between">
        <div className="text-sm">
          已选择 <span className="font-medium">{selectedCourses.length}</span> 个课程
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleClearAll}
            disabled={selectedCourses.length === 0}
          >
            清除全部
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleSelectAll}
            disabled={selectedCourses.length === courseData.length}
          >
            选择全部
          </Button>
        </div>
      </div>
    </div>
  )
}
