"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronRight, Clock, Users, Calendar, AlertTriangle } from "lucide-react"

const formSchema = z.object({
  // 预约时间设置
  advanceBookingDays: z.coerce.number().min(0).max(365),
  bookingDeadlineMinutes: z.coerce.number().min(0).max(1440),
  allowLateBooking: z.boolean(),
  lateBookingMinutes: z.coerce.number().min(0).max(120).optional(),

  // 预约数量限制
  maxBookingsPerDay: z.coerce.number().min(0).max(100),
  maxBookingsPerWeek: z.coerce.number().min(0).max(500),
  maxFutureBookings: z.coerce.number().min(0).max(100),

  // 预约方式
  allowSelfBooking: z.boolean(),
  allowStaffBooking: z.boolean(),
  allowBatchBooking: z.boolean(),

  // 签到设置
  checkinStartMinutes: z.coerce.number().min(0).max(120),
  checkinEndMinutes: z.coerce.number().min(0).max(120),
  allowSelfCheckin: z.boolean(),
  allowStaffCheckin: z.boolean(),

  // 取消设置
  cancelDeadlineMinutes: z.coerce.number().min(0).max(1440),
  allowSelfCancel: z.boolean(),
  allowStaffCancel: z.boolean(),

  // 候补设置
  waitlistEnabled: z.boolean(),
  maxWaitlistSize: z.coerce.number().min(0).max(100),
  waitlistStopMinutes: z.coerce.number().min(0).max(120),
})

interface GeneralBookingRulesProps {
  onChange: () => void
}

export function GeneralBookingRules({ onChange }: GeneralBookingRulesProps) {
  const [collapsedSections, setCollapsedSections] = useState({
    booking: false,
    checkin: false,
    cancel: false,
    waitlist: false,
  })

  const toggleSection = (section: keyof typeof collapsedSections) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      // 预约时间设置
      advanceBookingDays: 7,
      bookingDeadlineMinutes: 30,
      allowLateBooking: false,
      lateBookingMinutes: 10,

      // 预约数量限制
      maxBookingsPerDay: 3,
      maxBookingsPerWeek: 15,
      maxFutureBookings: 10,

      // 预约方式
      allowSelfBooking: true,
      allowStaffBooking: true,
      allowBatchBooking: false,

      // 签到设置
      checkinStartMinutes: 30,
      checkinEndMinutes: 15,
      allowSelfCheckin: true,
      allowStaffCheckin: true,

      // 取消设置
      cancelDeadlineMinutes: 120,
      allowSelfCancel: true,
      allowStaffCancel: true,

      // 候补设置
      waitlistEnabled: true,
      maxWaitlistSize: 10,
      waitlistStopMinutes: 30,
    }
  })

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    console.log("通用预约规则:", values)
    onChange()
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>通用预约规则</CardTitle>
          <CardDescription>
            这些规则将作为所有课程类型的默认设置，可以被具体的课程类型规则覆盖
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">

              {/* 预约设置 */}
              <Collapsible open={!collapsedSections.booking} onOpenChange={() => toggleSection('booking')}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="text-base font-medium text-gray-900">预约设置</h3>
                  </div>
                  {collapsedSections.booking ? (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="advanceBookingDays"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>提前预约天数</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            会员最多可以提前多少天预约课程
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="bookingDeadlineMinutes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>预约截止时间（分钟）</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            课程开始前多少分钟停止预约
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="maxBookingsPerDay"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>每日最大预约数</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maxBookingsPerWeek"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>每周最大预约数</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maxFutureBookings"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>最大未来预约数</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="allowSelfBooking"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>会员自主预约</FormLabel>
                            <FormDescription>
                              允许会员自己预约课程
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="allowStaffBooking"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>员工代约</FormLabel>
                            <FormDescription>
                              允许员工为会员预约
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="allowBatchBooking"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>批量预约</FormLabel>
                            <FormDescription>
                              允许批量预约多节课程
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* 签到设置 */}
              <Collapsible open={!collapsedSections.checkin} onOpenChange={() => toggleSection('checkin')}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="text-base font-medium text-gray-900">签到设置</h3>
                  </div>
                  {collapsedSections.checkin ? (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="checkinStartMinutes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>签到开始时间（分钟）</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            课程开始前多少分钟可以签到
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="checkinEndMinutes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>签到结束时间（分钟）</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormDescription>
                            课程结束后多少分钟内可以签到
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="allowSelfCheckin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>会员自主签到</FormLabel>
                            <FormDescription>
                              允许会员自己签到
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="allowStaffCheckin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>员工代签到</FormLabel>
                            <FormDescription>
                              允许员工为会员签到
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* 取消设置 */}
              <Collapsible open={!collapsedSections.cancel} onOpenChange={() => toggleSection('cancel')}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <div className="flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="text-base font-medium text-gray-900">取消设置</h3>
                  </div>
                  {collapsedSections.cancel ? (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="cancelDeadlineMinutes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>取消截止时间（分钟）</FormLabel>
                        <FormControl>
                          <Input type="number" {...field} />
                        </FormControl>
                        <FormDescription>
                          课程开始前多少分钟内不能取消预约
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="allowSelfCancel"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>会员自主取消</FormLabel>
                            <FormDescription>
                              允许会员自己取消预约
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="allowStaffCancel"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                          <div className="space-y-0.5">
                            <FormLabel>员工代取消</FormLabel>
                            <FormDescription>
                              允许员工为会员取消预约
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* 候补设置 */}
              <Collapsible open={!collapsedSections.waitlist} onOpenChange={() => toggleSection('waitlist')}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="text-base font-medium text-gray-900">候补设置</h3>
                  </div>
                  {collapsedSections.waitlist ? (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 pt-4">
                  <FormField
                    control={form.control}
                    name="waitlistEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>启用候补功能</FormLabel>
                          <FormDescription>
                            当课程满员时允许会员加入候补队列
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch("waitlistEnabled") && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="maxWaitlistSize"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>最大候补人数</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormDescription>
                              每个课程最多允许多少人候补
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="waitlistStopMinutes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>候补停止时间（分钟）</FormLabel>
                            <FormControl>
                              <Input type="number" {...field} />
                            </FormControl>
                            <FormDescription>
                              课程开始前多少分钟停止候补
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </CollapsibleContent>
              </Collapsible>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
