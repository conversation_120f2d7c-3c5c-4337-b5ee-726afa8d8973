"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  BarChart3,
  Filter,
  Plus,
  Search,
  Settings,
  Star,
  Download,
  MoreHorizontal,
  Edit,
  Eye,
  Trash,
  AlertTriangle,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { AddPointItemDialog } from "@/components/marketing/points/add-point-item-dialog"
import { PointItemDetailDialog } from "@/components/marketing/points/point-item-detail-dialog"
import { PointRuleDialog } from "@/components/marketing/points/point-rule-dialog"
import { PointRecordsTable } from "@/components/marketing/points/point-records-table"
import { ExchangeManagementTable } from "@/components/marketing/points/exchange-management-table"

export default function PointsPage() {
  const [addItemDialogOpen, setAddItemDialogOpen] = useState(false)
  const [itemDetailDialogOpen, setItemDetailDialogOpen] = useState(false)
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null)
  const [addRuleDialogOpen, setAddRuleDialogOpen] = useState(false)
  const [editRuleDialogOpen, setEditRuleDialogOpen] = useState(false)
  const [selectedRule, setSelectedRule] = useState<any>(null)

  const handleViewItemDetail = (itemId: number) => {
    setSelectedItemId(itemId)
    setItemDetailDialogOpen(true)
  }

  const handleEditRule = (rule: any) => {
    setSelectedRule(rule)
    setEditRuleDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">会员积分</h1>
          <p className="text-muted-foreground">管理会员积分规则、积分商城和积分活动</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setAddRuleDialogOpen(true)}>
            <Settings className="mr-2 h-4 w-4" />
            积分规则设置
          </Button>
          <Button variant="outline">
            <BarChart3 className="mr-2 h-4 w-4" />
            积分数据分析
          </Button>
          <Button onClick={() => setAddItemDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加积分商品
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总积分发放量</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,248,350</div>
            <p className="text-xs text-muted-foreground">较上月 +12.5%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总积分使用量</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">856,720</div>
            <p className="text-xs text-muted-foreground">较上月 +8.2%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">积分兑换率</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">68.6%</div>
            <p className="text-xs text-muted-foreground">较上月 +2.3%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">人均积分</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,245</div>
            <p className="text-xs text-muted-foreground">较上月 +5.8%</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="mall">
        <TabsList>
          <TabsTrigger value="mall">积分商城</TabsTrigger>
          <TabsTrigger value="rules">积分规则</TabsTrigger>
          <TabsTrigger value="records">积分记录</TabsTrigger>
          <TabsTrigger value="exchange">兑换管理</TabsTrigger>
        </TabsList>
        <TabsContent value="mall" className="mt-4">
          <div className="flex items-center gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="搜索积分商品..." className="pl-8" />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
            <Select defaultValue="all">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="商品类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="course">课程</SelectItem>
                <SelectItem value="product">实物商品</SelectItem>
                <SelectItem value="coupon">优惠券</SelectItem>
                <SelectItem value="service">增值服务</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="active">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="商品状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="active">正常</SelectItem>
                <SelectItem value="low_stock">库存不足</SelectItem>
                <SelectItem value="sold_out">已售罄</SelectItem>
                <SelectItem value="inactive">未启用</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              导出商品
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {pointItems.map((item) => (
              <Card key={item.id} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="aspect-video w-full bg-muted relative">
                    <img
                      src="/placeholder.svg?height=200&width=400"
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                    <Badge className="absolute top-2 right-2" variant={getItemTypeBadgeVariant(item.type)}>
                      {getItemTypeName(item.type)}
                    </Badge>
                    <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                      {item.points} 积分
                    </div>
                    {item.status === "low_stock" && (
                      <div className="absolute top-2 left-2 bg-amber-500 text-white px-2 py-1 rounded-full text-xs flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        库存不足
                      </div>
                    )}
                    {item.status === "sold_out" && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-full text-xs flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        已售罄
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium">{item.name}</h3>
                    <p className="text-sm text-muted-foreground mt-1">{item.description}</p>
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-sm">
                        库存: <span className={item.stock < 10 ? "text-destructive" : ""}>{item.stock}</span>
                      </div>
                      <div className="text-sm">已兑换: {item.exchanged}</div>
                    </div>
                    <div className="mt-4 flex items-center justify-between">
                      <Badge variant={getStatusBadgeVariant(item.status)}>{getItemStatusName(item.status)}</Badge>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" onClick={() => handleViewItemDetail(item.id)}>
                          <Eye className="h-4 w-4 mr-1" />
                          查看
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button size="sm" variant="ghost">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleViewItemDetail(item.id)}>
                              <Eye className="h-4 w-4 mr-2" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="h-4 w-4 mr-2" />
                              编辑
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash className="h-4 w-4 mr-2" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        <TabsContent value="rules" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>积分规则设置</CardTitle>
                <CardDescription>设置会员获取和使用积分的规则</CardDescription>
              </div>
              <Button onClick={() => setAddRuleDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                添加规则
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>规则名称</TableHead>
                    <TableHead>积分值</TableHead>
                    <TableHead>规则类型</TableHead>
                    <TableHead>触发动作</TableHead>
                    <TableHead>适用会员</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pointRules.map((rule) => (
                    <TableRow key={rule.id}>
                      <TableCell className="font-medium">{rule.name}</TableCell>
                      <TableCell>{rule.value}</TableCell>
                      <TableCell>
                        <Badge variant={rule.type === "earn" ? "success" : "default"}>
                          {rule.type === "earn" ? "获取" : "使用"}
                        </Badge>
                      </TableCell>
                      <TableCell>{rule.triggerAction}</TableCell>
                      <TableCell>{rule.memberLevel}</TableCell>
                      <TableCell>
                        <Badge variant={rule.status === "active" ? "success" : "outline"}>
                          {rule.status === "active" ? "启用" : "禁用"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button variant="ghost" size="sm" onClick={() => handleEditRule(rule)}>
                            编辑
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button size="sm" variant="ghost">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditRule(rule)}>
                                <Edit className="h-4 w-4 mr-2" />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                {rule.status === "active" ? (
                                  <>
                                    <AlertTriangle className="h-4 w-4 mr-2" />
                                    禁用
                                  </>
                                ) : (
                                  <>
                                    <Star className="h-4 w-4 mr-2" />
                                    启用
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-destructive">
                                <Trash className="h-4 w-4 mr-2" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="records" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>会员积分记录</CardTitle>
              <CardDescription>查看会员积分获取和使用记录</CardDescription>
            </CardHeader>
            <CardContent>
              <PointRecordsTable />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="exchange" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>积分兑换管理</CardTitle>
              <CardDescription>管理会员积分兑换申请和发货</CardDescription>
            </CardHeader>
            <CardContent>
              <ExchangeManagementTable />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 添加积分商品对话框 */}
      <AddPointItemDialog open={addItemDialogOpen} onOpenChange={setAddItemDialogOpen} />

      {/* 积分商品详情对话框 */}
      {selectedItemId && (
        <PointItemDetailDialog
          open={itemDetailDialogOpen}
          onOpenChange={setItemDetailDialogOpen}
          itemId={selectedItemId}
        />
      )}

      {/* 添加积分规则对话框 */}
      <PointRuleDialog open={addRuleDialogOpen} onOpenChange={setAddRuleDialogOpen} />

      {/* 编辑积分规则对话框 */}
      {selectedRule && (
        <PointRuleDialog open={editRuleDialogOpen} onOpenChange={setEditRuleDialogOpen} editRule={selectedRule} />
      )}
    </div>
  )
}

// 模拟数据
const pointItems = [
  {
    id: 1,
    name: "瑜伽单次体验课",
    description: "可兑换任意一节瑜伽课程",
    points: 500,
    type: "course",
    stock: 999,
    exchanged: 128,
    status: "active",
  },
  {
    id: 2,
    name: "高级瑜伽垫",
    description: "环保材质，防滑耐用",
    points: 2000,
    type: "product",
    stock: 42,
    exchanged: 36,
    status: "active",
  },
  {
    id: 3,
    name: "会员月卡8折券",
    description: "可用于购买任意月卡",
    points: 1500,
    type: "coupon",
    stock: 100,
    exchanged: 45,
    status: "active",
  },
  {
    id: 4,
    name: "私教课1次",
    description: "可预约任意教练的私教课",
    points: 3000,
    type: "course",
    stock: 50,
    exchanged: 12,
    status: "active",
  },
  {
    id: 5,
    name: "瑜伽服套装",
    description: "舒适透气，多色可选",
    points: 2500,
    type: "product",
    stock: 8,
    exchanged: 22,
    status: "low_stock",
  },
  {
    id: 6,
    name: "会员生日礼包",
    description: "生日当月专享礼品套装",
    points: 1000,
    type: "product",
    stock: 0,
    exchanged: 30,
    status: "sold_out",
  },
]

// 模拟积分规则数据
const pointRules = [
  {
    id: 1,
    name: "课程签到",
    value: "10积分/次",
    type: "earn",
    triggerAction: "课程签到",
    memberLevel: "所有会员",
    status: "active",
  },
  {
    id: 2,
    name: "课程购买",
    value: "消费金额1%",
    type: "earn",
    triggerAction: "课程购买",
    memberLevel: "所有会员",
    status: "active",
  },
  {
    id: 3,
    name: "会员推荐",
    value: "100积分/人",
    type: "earn",
    triggerAction: "会员推荐",
    memberLevel: "所有会员",
    status: "active",
  },
  {
    id: 4,
    name: "生日特权",
    value: "200积分",
    type: "earn",
    triggerAction: "生日特权",
    memberLevel: "所有会员",
    status: "active",
  },
  {
    id: 5,
    name: "积分抵扣",
    value: "100积分=10元",
    type: "spend",
    triggerAction: "课程抵扣",
    memberLevel: "所有会员",
    status: "active",
  },
  {
    id: 6,
    name: "新会员注册",
    value: "50积分",
    type: "earn",
    triggerAction: "注册奖励",
    memberLevel: "所有会员",
    status: "active",
  },
  {
    id: 7,
    name: "课程评价",
    value: "5积分/次",
    type: "earn",
    triggerAction: "评价奖励",
    memberLevel: "所有会员",
    status: "inactive",
  },
]

// 辅助函数
function getItemTypeBadgeVariant(type: string) {
  switch (type) {
    case "course":
      return "secondary"
    case "product":
      return "default"
    case "coupon":
      return "outline"
    case "service":
      return "success"
    default:
      return "default"
  }
}

function getItemTypeName(type: string) {
  switch (type) {
    case "course":
      return "课程"
    case "product":
      return "实物"
    case "coupon":
      return "优惠券"
    case "service":
      return "服务"
    default:
      return "其他"
  }
}

function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "active":
      return "success"
    case "low_stock":
      return "warning"
    case "sold_out":
      return "destructive"
    case "inactive":
      return "outline"
    default:
      return "default"
  }
}

function getItemStatusName(status: string) {
  switch (status) {
    case "active":
      return "正常"
    case "low_stock":
      return "库存不足"
    case "sold_out":
      return "已售罄"
    case "inactive":
      return "未启用"
    default:
      return "未知"
  }
}

