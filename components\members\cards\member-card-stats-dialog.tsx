"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { SalesTrendChart } from "./sales-trend-chart"
import { CardSalesComparisonChart } from "./card-sales-comparison-chart"
import { CardDistributionChart } from "./card-distribution-chart"
import { GenderDistributionChart } from "./gender-distribution-chart"
import { AgeDistributionChart } from "./age-distribution-chart"
import { MemberActivityChart } from "./member-activity-chart"
import { MemberRetentionChart } from "./member-retention-chart"

interface MemberCardStatsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  cards: any[]
}

export function MemberCardStatsDialog({ open, onOpenChange, cards }: MemberCardStatsDialogProps) {
  // 计算总销售额
  const totalRevenue = cards.reduce((sum, card) => {
    const revenue = Number.parseFloat(card.revenue.replace(/[^0-9.]/g, ""))
    return sum + revenue
  }, 0)

  // 计算总销售数量
  const totalSales = cards.reduce((sum, card) => sum + card.salesCount, 0)

  // 计算总持卡会员数
  const totalMembers = cards.reduce((sum, card) => sum + card.members, 0)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>会员卡统计分析</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">总体概览</TabsTrigger>
            <TabsTrigger value="sales">销售分析</TabsTrigger>
            <TabsTrigger value="members">会员分析</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">总销售额</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">¥{totalRevenue.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">所有会员卡总销售额</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">总销售数量</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalSales}</div>
                  <p className="text-xs text-muted-foreground">所有会员卡总销售数量</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">总持卡会员</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalMembers}</div>
                  <p className="text-xs text-muted-foreground">当前持有会员卡的会员数</p>
                </CardContent>
              </Card>
            </div>

            <CardSalesComparisonChart />

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <SalesTrendChart cardId="all" title="销售趋势" />
              <CardDistributionChart />
            </div>
          </TabsContent>

          <TabsContent value="sales" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>销售详情</CardTitle>
                <CardDescription>各类会员卡销售详细数据</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {cards.map((card) => (
                    <div key={card.id} className="flex items-center justify-between rounded-lg border p-3">
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full" style={{ backgroundColor: card.color }} />
                        <div>
                          <p className="font-medium">{card.name}</p>
                          <p className="text-sm text-muted-foreground">{card.description}</p>
                        </div>
                      </div>
                      <div className="hidden md:block">
                        <p className="text-sm">销售数量: {card.salesCount}</p>
                        <p className="text-sm">持卡会员: {card.members}</p>
                      </div>
                      <div>
                        <p className="font-medium">{card.revenue}</p>
                        <p className="text-right text-sm text-muted-foreground">
                          {Math.round((card.salesCount / totalSales) * 100)}% 的总销量
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <SalesTrendChart cardId="all" />

            <CardSalesComparisonChart />

            <Card>
              <CardHeader>
                <CardTitle>销售渠道分析</CardTitle>
                <CardDescription>各销售渠道的销售情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="rounded-lg border p-4 text-center">
                    <p className="text-lg font-bold text-primary">68%</p>
                    <p className="text-sm">线上小程序</p>
                  </div>
                  <div className="rounded-lg border p-4 text-center">
                    <p className="text-lg font-bold text-primary">22%</p>
                    <p className="text-sm">线下门店</p>
                  </div>
                  <div className="rounded-lg border p-4 text-center">
                    <p className="text-lg font-bold text-primary">8%</p>
                    <p className="text-sm">电话销售</p>
                  </div>
                  <div className="rounded-lg border p-4 text-center">
                    <p className="text-lg font-bold text-primary">2%</p>
                    <p className="text-sm">其他渠道</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="members" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">会员留存率</p>
                    <p className="text-3xl font-bold">78.3%</p>
                    <p className="text-xs text-muted-foreground">会员卡到期后续卡的比例</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">会员活跃度</p>
                    <p className="text-3xl font-bold">65.2%</p>
                    <p className="text-xs text-muted-foreground">每周至少使用一次的会员比例</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">会员推荐率</p>
                    <p className="text-3xl font-bold">28.7%</p>
                    <p className="text-xs text-muted-foreground">通过会员推荐获得的新会员比例</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <CardDistributionChart />

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <GenderDistributionChart cardId="all" />
              <AgeDistributionChart cardId="all" />
            </div>

            <MemberActivityChart />

            <MemberRetentionChart />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

