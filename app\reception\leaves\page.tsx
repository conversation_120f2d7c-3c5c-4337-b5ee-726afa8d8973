"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  ArrowLeft, 
  Plus, 
  Calendar,
  Clock,
  CheckCircle2,
  XCircle,
  Filter,
  Download,
  MoreHorizontal,
  FileText
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"

// 模拟请假记录数据
const leaveRecords = [
  { 
    id: "l1", 
    memberId: "m1",
    memberName: "张三", 
    memberAvatar: "/avatars/01.png", 
    phone: "13800138001",
    cardType: "瑜伽年卡",
    leaveType: "病假",
    startDate: "2023-05-16",
    endDate: "2023-05-18",
    courseName: "空中瑜伽",
    status: "已批准",
    reason: "感冒发烧，需要休息",
    attachments: ["病假条.jpg"],
    approvedBy: "王经理",
    approvedAt: "2023-05-15 14:30:22"
  },
  { 
    id: "l2", 
    memberId: "m2",
    memberName: "李四", 
    memberAvatar: "/avatars/02.png", 
    phone: "13800138002",
    cardType: "瑜伽季卡",
    leaveType: "事假",
    startDate: "2023-05-20",
    endDate: "2023-05-25",
    courseName: "哈他瑜伽",
    status: "待审批",
    reason: "出差，无法参加课程",
    attachments: [],
    approvedBy: null,
    approvedAt: null
  },
  { 
    id: "l3", 
    memberId: "m3",
    memberName: "王五", 
    memberAvatar: "/avatars/03.png", 
    phone: "13800138003",
    cardType: "瑜伽月卡",
    leaveType: "事假",
    startDate: "2023-05-18",
    endDate: "2023-05-19",
    courseName: "普拉提",
    status: "已批准",
    reason: "家中有事，无法参加课程",
    attachments: [],
    approvedBy: "李经理",
    approvedAt: "2023-05-17 09:15:40"
  },
  { 
    id: "l4", 
    memberId: "m4",
    memberName: "赵六", 
    memberAvatar: "/avatars/04.png", 
    phone: "13800138004",
    cardType: "瑜伽年卡",
    leaveType: "病假",
    startDate: "2023-05-22",
    endDate: "2023-05-30",
    courseName: "空中瑜伽",
    status: "已拒绝",
    reason: "手术恢复，需要休息",
    attachments: ["医院证明.pdf"],
    approvedBy: "王经理",
    approvedAt: "2023-05-21 16:45:12"
  },
  { 
    id: "l5", 
    memberId: "m5",
    memberName: "孙七", 
    memberAvatar: "/avatars/05.png", 
    phone: "13800138005",
    cardType: "瑜伽季卡",
    leaveType: "事假",
    startDate: "2023-05-25",
    endDate: "2023-05-26",
    courseName: "哈他瑜伽",
    status: "待审批",
    reason: "临时有事，无法参加课程",
    attachments: [],
    approvedBy: null,
    approvedAt: null
  }
]

// 请假类型选项
const leaveTypes = [
  "病假",
  "事假",
  "休假",
  "其他"
]

export default function LeavesPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [showLeaveDetailDialog, setShowLeaveDetailDialog] = useState(false)
  const [selectedLeave, setSelectedLeave] = useState<any>(null)
  const [showApproveDialog, setShowApproveDialog] = useState(false)
  const [approvalNotes, setApprovalNotes] = useState("")

  // 过滤请假记录
  const filteredLeaves = leaveRecords.filter(record => {
    // 基本搜索过滤
    const searchFilter = 
      searchQuery === "" || 
      record.memberName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.phone.includes(searchQuery) ||
      record.courseName.toLowerCase().includes(searchQuery.toLowerCase());
    
    // 标签过滤
    const tabFilter = 
      activeTab === "all" || 
      (activeTab === "pending" && record.status === "待审批") ||
      (activeTab === "approved" && record.status === "已批准") ||
      (activeTab === "rejected" && record.status === "已拒绝");
    
    return searchFilter && tabFilter;
  });

  // 查看请假详情
  const viewLeaveDetail = (leave: any) => {
    setSelectedLeave(leave)
    setShowLeaveDetailDialog(true)
  }

  // 处理审批
  const handleApproval = (approved: boolean) => {
    // 在实际应用中，这里会调用API进行审批操作
    toast.success(`请假申请已${approved ? '批准' : '拒绝'}！`);
    setShowApproveDialog(false);
    setApprovalNotes("");
    setShowLeaveDetailDialog(false);
  }

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "待审批":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">待审批</Badge>
      case "已批准":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已批准</Badge>
      case "已拒绝":
        return <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200">已拒绝</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/reception">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">会员请假管理</h1>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>请假记录管理</CardTitle>
              <CardDescription>
                管理会员课程请假申请
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col md:flex-row md:items-center gap-4 justify-between">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full max-w-md">
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="pending">待审批</TabsTrigger>
                <TabsTrigger value="approved">已批准</TabsTrigger>
                <TabsTrigger value="rejected">已拒绝</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索会员姓名或课程..."
                  className="pl-8 w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>会员信息</TableHead>
                <TableHead>请假类型</TableHead>
                <TableHead>请假时间</TableHead>
                <TableHead>课程</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredLeaves.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    没有找到符合条件的请假记录
                  </TableCell>
                </TableRow>
              ) : (
                filteredLeaves.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={record.memberAvatar} alt={record.memberName} />
                          <AvatarFallback>{record.memberName[0]}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{record.memberName}</div>
                          <div className="text-xs text-muted-foreground">{record.phone}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {record.leaveType}
                      </Badge>
                      {record.attachments.length > 0 && (
                        <div className="flex items-center text-xs text-blue-600 mt-1">
                          <FileText className="h-3 w-3 mr-1" />
                          附件 ({record.attachments.length})
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{record.startDate}</div>
                      <div className="text-xs text-muted-foreground">至 {record.endDate}</div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-[150px] truncate">
                        {record.courseName}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(record.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm" onClick={() => viewLeaveDetail(record)}>
                        查看详情
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-4">
          <div className="text-sm text-muted-foreground">
            共 {filteredLeaves.length} 条记录
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 请假详情对话框 */}
      {selectedLeave && (
        <Dialog open={showLeaveDetailDialog} onOpenChange={setShowLeaveDetailDialog}>
          <DialogContent className="sm:max-w-[550px]">
            <DialogHeader>
              <DialogTitle>请假详情</DialogTitle>
              <DialogDescription>
                查看会员请假申请详情
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="flex items-center gap-4 pb-4 border-b">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={selectedLeave.memberAvatar} alt={selectedLeave.memberName} />
                  <AvatarFallback>{selectedLeave.memberName[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium text-lg">{selectedLeave.memberName}</div>
                  <div className="text-sm text-muted-foreground">{selectedLeave.phone}</div>
                </div>
                <div className="ml-auto">
                  {getStatusBadge(selectedLeave.status)}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-muted-foreground">会员卡类型</div>
                  <div className="font-medium">{selectedLeave.cardType}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">请假类型</div>
                  <div className="font-medium">{selectedLeave.leaveType}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">开始日期</div>
                  <div className="font-medium">{selectedLeave.startDate}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">结束日期</div>
                  <div className="font-medium">{selectedLeave.endDate}</div>
                </div>
                <div className="col-span-2">
                  <div className="text-sm text-muted-foreground">课程名称</div>
                  <div className="font-medium">{selectedLeave.courseName}</div>
                </div>
                <div className="col-span-2">
                  <div className="text-sm text-muted-foreground">请假原因</div>
                  <div className="p-3 rounded-md bg-muted/50 mt-1">
                    {selectedLeave.reason}
                  </div>
                </div>
                
                {selectedLeave.attachments.length > 0 && (
                  <div className="col-span-2">
                    <div className="text-sm text-muted-foreground">附件</div>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {selectedLeave.attachments.map((attachment: string, index: number) => (
                        <div key={index} className="flex items-center p-2 rounded-md bg-blue-50 text-blue-700 text-sm">
                          <FileText className="h-4 w-4 mr-2" />
                          {attachment}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {selectedLeave.status !== "待审批" && (
                  <>
                    <div>
                      <div className="text-sm text-muted-foreground">审批人</div>
                      <div className="font-medium">{selectedLeave.approvedBy}</div>
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">审批时间</div>
                      <div className="font-medium">{selectedLeave.approvedAt}</div>
                    </div>
                  </>
                )}
              </div>
            </div>
            <DialogFooter>
              {selectedLeave.status === "待审批" ? (
                <>
                  <Button variant="outline" onClick={() => setShowApproveDialog(true)}>拒绝</Button>
                  <Button onClick={() => handleApproval(true)}>批准</Button>
                </>
              ) : (
                <Button onClick={() => setShowLeaveDetailDialog(false)}>关闭</Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 拒绝请假对话框 */}
      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>拒绝请假申请</DialogTitle>
            <DialogDescription>
              请输入拒绝原因
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="approval-notes">拒绝原因</Label>
              <textarea
                id="approval-notes"
                className="flex min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={approvalNotes}
                onChange={(e) => setApprovalNotes(e.target.value)}
                placeholder="请输入拒绝原因..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowApproveDialog(false)}>取消</Button>
            <Button variant="destructive" onClick={() => handleApproval(false)}>确认拒绝</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
