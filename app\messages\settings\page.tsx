"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Send,
  MessageSquare,
  Mail,
  Smartphone,
  Bell,
  Clock,
  Users,
  Shield,
  Sliders,
  Check
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

export default function MessageSettingsPage() {
  const [smsSettings, setSmsSettings] = useState({
    provider: "aliyun",
    accessKeyId: "LTAI4G*************",
    accessKeySecret: "*********************",
    signName: "静心瑜伽馆",
    dailyLimit: 5000
  })

  const [wechatSettings, setWechatSettings] = useState({
    appId: "wx*****************",
    appSecret: "*********************",
    name: "静心瑜伽馆",
    token: "*********************",
    encodingAESKey: "*********************"
  })

  const [appSettings, setAppSettings] = useState({
    provider: "jpush",
    appKey: "*********************",
    masterSecret: "*********************",
    androidPackage: "com.yoga.studio",
    iosBundle: "com.yoga.studio"
  })

  const [sendingStrategy, setSendingStrategy] = useState({
    retryTimes: 3,
    retryInterval: 5,
    batchSize: 100,
    sendingInterval: 1,
    priorityChannel: "wechat"
  })

  const handleSaveSettings = () => {
    toast({
      title: "设置已保存",
      description: "消息系统设置已成功更新",
    })
  }

  const handleTestSms = () => {
    toast({
      title: "测试短信发送中",
      description: "正在发送测试短信，请稍候...",
    })
    
    // 模拟发送完成
    setTimeout(() => {
      toast({
        title: "测试短信已发送",
        description: "测试短信已成功发送，请查收",
      })
    }, 2000)
  }

  const handleTestWechat = () => {
    toast({
      title: "测试微信消息发送中",
      description: "正在发送测试微信消息，请稍候...",
    })
    
    // 模拟发送完成
    setTimeout(() => {
      toast({
        title: "测试微信消息已发送",
        description: "测试微信消息已成功发送，请查收",
      })
    }, 2000)
  }

  const handleTestApp = () => {
    toast({
      title: "测试APP推送发送中",
      description: "正在发送测试APP推送，请稍候...",
    })
    
    // 模拟发送完成
    setTimeout(() => {
      toast({
        title: "测试APP推送已发送",
        description: "测试APP推送已成功发送，请查收",
      })
    }, 2000)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">消息设置</h1>
          <p className="text-sm text-muted-foreground mt-1">配置消息系统的基本设置</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            重置设置
          </Button>
          <Button onClick={handleSaveSettings}>
            <Save className="mr-2 h-4 w-4" />
            保存设置
          </Button>
        </div>
      </div>

      <Tabs defaultValue="channels" className="space-y-4">
        <TabsList>
          <TabsTrigger value="channels" className="flex items-center gap-2">
            <Send className="h-4 w-4" />
            通知渠道
          </TabsTrigger>
          <TabsTrigger value="strategy" className="flex items-center gap-2">
            <Sliders className="h-4 w-4" />
            发送策略
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            权限设置
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="channels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                短信服务配置
              </CardTitle>
              <CardDescription>
                配置短信服务提供商和相关参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sms-provider">服务提供商</Label>
                  <Select 
                    value={smsSettings.provider}
                    onValueChange={(value) => setSmsSettings({...smsSettings, provider: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择服务提供商" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="aliyun">阿里云短信</SelectItem>
                      <SelectItem value="tencent">腾讯云短信</SelectItem>
                      <SelectItem value="netease">网易云信</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sms-sign">短信签名</Label>
                  <Input 
                    id="sms-sign" 
                    value={smsSettings.signName}
                    onChange={(e) => setSmsSettings({...smsSettings, signName: e.target.value})}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sms-key">AccessKey ID</Label>
                  <Input 
                    id="sms-key" 
                    type="password" 
                    value={smsSettings.accessKeyId}
                    onChange={(e) => setSmsSettings({...smsSettings, accessKeyId: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sms-secret">AccessKey Secret</Label>
                  <Input 
                    id="sms-secret" 
                    type="password" 
                    value={smsSettings.accessKeySecret}
                    onChange={(e) => setSmsSettings({...smsSettings, accessKeySecret: e.target.value})}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sms-limit">日发送上限</Label>
                  <div className="flex items-center gap-2">
                    <Input 
                      id="sms-limit" 
                      type="number" 
                      value={smsSettings.dailyLimit}
                      onChange={(e) => setSmsSettings({...smsSettings, dailyLimit: parseInt(e.target.value)})}
                    />
                    <span className="text-sm text-muted-foreground">条</span>
                  </div>
                </div>
                <div className="flex items-center justify-end space-x-2 pt-6">
                  <Button variant="outline" onClick={handleTestSms}>
                    测试短信
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                微信服务配置
              </CardTitle>
              <CardDescription>
                配置微信公众号/小程序的消息推送参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="wechat-appid">AppID</Label>
                  <Input 
                    id="wechat-appid" 
                    value={wechatSettings.appId}
                    onChange={(e) => setWechatSettings({...wechatSettings, appId: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="wechat-name">公众号/小程序名称</Label>
                  <Input 
                    id="wechat-name" 
                    value={wechatSettings.name}
                    onChange={(e) => setWechatSettings({...wechatSettings, name: e.target.value})}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="wechat-secret">AppSecret</Label>
                  <Input 
                    id="wechat-secret" 
                    type="password" 
                    value={wechatSettings.appSecret}
                    onChange={(e) => setWechatSettings({...wechatSettings, appSecret: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="wechat-token">Token</Label>
                  <Input 
                    id="wechat-token" 
                    type="password" 
                    value={wechatSettings.token}
                    onChange={(e) => setWechatSettings({...wechatSettings, token: e.target.value})}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="wechat-aeskey">EncodingAESKey</Label>
                  <Input 
                    id="wechat-aeskey" 
                    type="password" 
                    value={wechatSettings.encodingAESKey}
                    onChange={(e) => setWechatSettings({...wechatSettings, encodingAESKey: e.target.value})}
                  />
                </div>
                <div className="flex items-center justify-end space-x-2 pt-6">
                  <Button variant="outline" onClick={handleTestWechat}>
                    测试微信
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Smartphone className="h-5 w-5" />
                APP推送配置
              </CardTitle>
              <CardDescription>
                配置APP推送服务参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="app-provider">推送服务</Label>
                  <Select 
                    value={appSettings.provider}
                    onValueChange={(value) => setAppSettings({...appSettings, provider: value})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择推送服务" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="jpush">极光推送</SelectItem>
                      <SelectItem value="getui">个推</SelectItem>
                      <SelectItem value="umeng">友盟推送</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="app-package">Android包名</Label>
                  <Input 
                    id="app-package" 
                    value={appSettings.androidPackage}
                    onChange={(e) => setAppSettings({...appSettings, androidPackage: e.target.value})}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="app-key">AppKey</Label>
                  <Input 
                    id="app-key" 
                    type="password" 
                    value={appSettings.appKey}
                    onChange={(e) => setAppSettings({...appSettings, appKey: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="app-secret">Master Secret</Label>
                  <Input 
                    id="app-secret" 
                    type="password" 
                    value={appSettings.masterSecret}
                    onChange={(e) => setAppSettings({...appSettings, masterSecret: e.target.value})}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="app-bundle">iOS Bundle ID</Label>
                  <Input 
                    id="app-bundle" 
                    value={appSettings.iosBundle}
                    onChange={(e) => setAppSettings({...appSettings, iosBundle: e.target.value})}
                  />
                </div>
                <div className="flex items-center justify-end space-x-2 pt-6">
                  <Button variant="outline" onClick={handleTestApp}>
                    测试APP推送
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="strategy" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sliders className="h-5 w-5" />
                发送策略设置
              </CardTitle>
              <CardDescription>
                配置消息发送的策略和规则
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="retry-times">重试次数</Label>
                  <div className="flex items-center gap-2">
                    <Input 
                      id="retry-times" 
                      type="number" 
                      value={sendingStrategy.retryTimes}
                      onChange={(e) => setSendingStrategy({...sendingStrategy, retryTimes: parseInt(e.target.value)})}
                    />
                    <span className="text-sm text-muted-foreground">次</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="retry-interval">重试间隔</Label>
                  <div className="flex items-center gap-2">
                    <Input 
                      id="retry-interval" 
                      type="number" 
                      value={sendingStrategy.retryInterval}
                      onChange={(e) => setSendingStrategy({...sendingStrategy, retryInterval: parseInt(e.target.value)})}
                    />
                    <span className="text-sm text-muted-foreground">分钟</span>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="batch-size">批量发送大小</Label>
                  <div className="flex items-center gap-2">
                    <Input 
                      id="batch-size" 
                      type="number" 
                      value={sendingStrategy.batchSize}
                      onChange={(e) => setSendingStrategy({...sendingStrategy, batchSize: parseInt(e.target.value)})}
                    />
                    <span className="text-sm text-muted-foreground">条/批</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sending-interval">批次间隔</Label>
                  <div className="flex items-center gap-2">
                    <Input 
                      id="sending-interval" 
                      type="number" 
                      value={sendingStrategy.sendingInterval}
                      onChange={(e) => setSendingStrategy({...sendingStrategy, sendingInterval: parseInt(e.target.value)})}
                    />
                    <span className="text-sm text-muted-foreground">秒</span>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority-channel">优先渠道</Label>
                <Select 
                  value={sendingStrategy.priorityChannel}
                  onValueChange={(value) => setSendingStrategy({...sendingStrategy, priorityChannel: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择优先渠道" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wechat">微信</SelectItem>
                    <SelectItem value="app">APP推送</SelectItem>
                    <SelectItem value="sms">短信</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  当消息可通过多个渠道发送时，系统将优先使用此渠道
                </p>
              </div>
              <Separator />
              <div className="space-y-2">
                <h3 className="text-sm font-medium">智能发送策略</h3>
                <div className="flex items-center space-x-2">
                  <Checkbox id="smart-sending" />
                  <Label htmlFor="smart-sending">启用智能发送</Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  根据用户的活跃时间和历史阅读习惯，智能选择最佳发送时间和渠道
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="channel-fallback" defaultChecked />
                  <Label htmlFor="channel-fallback">启用渠道降级</Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  当首选渠道发送失败时，自动尝试其他可用渠道
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="frequency-limit" defaultChecked />
                  <Label htmlFor="frequency-limit">启用频率限制</Label>
                </div>
                <p className="text-xs text-muted-foreground">
                  限制向同一用户在短时间内发送过多消息，避免骚扰
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                定时任务设置
              </CardTitle>
              <CardDescription>
                配置系统定时发送消息的任务
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="birthday-task" defaultChecked />
                    <Label htmlFor="birthday-task">会员生日提醒</Label>
                  </div>
                  <Input type="time" className="w-24" defaultValue="08:00" />
                </div>
                <p className="text-xs text-muted-foreground">
                  每日检查并发送会员生日提醒
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="expiry-task" defaultChecked />
                    <Label htmlFor="expiry-task">会员卡到期提醒</Label>
                  </div>
                  <Input type="time" className="w-24" defaultValue="09:00" />
                </div>
                <p className="text-xs text-muted-foreground">
                  每日检查并发送会员卡到期提醒
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="course-task" defaultChecked />
                    <Label htmlFor="course-task">课程开始提醒</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Input type="number" className="w-16" defaultValue="2" />
                    <span className="text-sm text-muted-foreground">小时前</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  课程开始前自动发送提醒
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="inactive-task" defaultChecked />
                    <Label htmlFor="inactive-task">多次未上课提醒</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Input type="number" className="w-16" defaultValue="7" />
                    <span className="text-sm text-muted-foreground">天</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  检查并提醒连续多天未上课的会员
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                权限设置
              </CardTitle>
              <CardDescription>
                配置消息系统的权限和访问控制
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">角色权限</h3>
                <div className="rounded-md border">
                  <div className="relative w-full overflow-auto">
                    <table className="w-full caption-bottom text-sm">
                      <thead className="[&_tr]:border-b">
                        <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                          <th className="h-12 px-4 text-left align-middle font-medium">角色</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">查看消息</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">发送消息</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">管理模板</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">管理规则</th>
                          <th className="h-12 px-4 text-left align-middle font-medium">系统设置</th>
                        </tr>
                      </thead>
                      <tbody className="[&_tr:last-child]:border-0">
                        <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                          <td className="p-4 align-middle">超级管理员</td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                        </tr>
                        <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                          <td className="p-4 align-middle">管理员</td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox /></td>
                        </tr>
                        <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                          <td className="p-4 align-middle">前台</td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox /></td>
                          <td className="p-4 align-middle"><Checkbox /></td>
                          <td className="p-4 align-middle"><Checkbox /></td>
                        </tr>
                        <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                          <td className="p-4 align-middle">教练</td>
                          <td className="p-4 align-middle"><Checkbox defaultChecked /></td>
                          <td className="p-4 align-middle"><Checkbox /></td>
                          <td className="p-4 align-middle"><Checkbox /></td>
                          <td className="p-4 align-middle"><Checkbox /></td>
                          <td className="p-4 align-middle"><Checkbox /></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <Separator />
              <div className="space-y-2">
                <h3 className="text-sm font-medium">安全设置</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="log-messages" defaultChecked />
                    <Label htmlFor="log-messages">记录所有消息发送日志</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    记录所有消息的发送、送达和阅读状态
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="sensitive-filter" defaultChecked />
                    <Label htmlFor="sensitive-filter">启用敏感内容过滤</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    自动过滤消息中的敏感词汇和违规内容
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="approval-flow" />
                    <Label htmlFor="approval-flow">启用批量消息审批流程</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    批量发送消息前需要管理员审批
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
