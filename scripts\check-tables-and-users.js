// 检查数据库表和用户信息
const mysql = require('mysql2/promise');

async function checkTablesAndUsers() {
  console.log('检查数据库表和用户信息...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    // 1. 检查所有表
    console.log('\n1. 检查所有表:');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('数据库中的表:');
    tables.forEach((table, index) => {
      console.log(`  ${index + 1}. ${Object.values(table)[0]}`);
    });

    // 2. 检查租户数据
    console.log('\n2. 检查租户数据:');
    const [tenants] = await connection.execute('SELECT * FROM tenant');
    console.log('租户列表:');
    tenants.forEach((tenant, index) => {
      console.log(`  ${index + 1}. ID: ${tenant.id}, 名称: ${tenant.tenant_name}, 状态: ${tenant.status}`);
    });

    // 3. 检查员工表（可能是用户表）
    console.log('\n3. 检查员工表:');
    try {
      const [employees] = await connection.execute('SELECT * FROM employee LIMIT 5');
      console.log('员工列表:');
      employees.forEach((emp, index) => {
        console.log(`  ${index + 1}. ID: ${emp.id}, 用户名: ${emp.username || emp.name}, 租户ID: ${emp.tenant_id}, 状态: ${emp.status}`);
      });
    } catch (error) {
      console.log('员工表不存在或查询失败:', error.message);
    }

    // 4. 检查租户1的数据统计
    console.log('\n4. 租户1的数据统计:');
    
    // 课程类型数量
    const [courseTypes] = await connection.execute('SELECT COUNT(*) as count FROM coursetype WHERE tenant_id = 1');
    console.log(`  - 课程类型: ${courseTypes[0].count} 个`);
    
    // 教练数量
    const [coaches] = await connection.execute('SELECT COUNT(*) as count FROM coach WHERE tenant_id = 1');
    console.log(`  - 教练: ${coaches[0].count} 个`);
    
    // 场地数量
    const [venues] = await connection.execute('SELECT COUNT(*) as count FROM venue WHERE tenant_id = 1');
    console.log(`  - 场地: ${venues[0].count} 个`);
    
    // 课程数量
    const [courses] = await connection.execute('SELECT COUNT(*) as count FROM course WHERE tenant_id = 1');
    console.log(`  - 课程: ${courses[0].count} 个`);

    // 5. 创建租户2的数据
    console.log('\n5. 为租户2创建基础数据:');
    
    // 检查是否已有租户2
    const [tenant2] = await connection.execute('SELECT * FROM tenant WHERE id = 2');
    if (tenant2.length === 0) {
      // 创建租户2
      const [result] = await connection.execute(
        `INSERT INTO tenant (tenant_name, contact_person, phone, email, status, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
        ['第二瑜伽馆', '管理员2', '13800000002', '<EMAIL>', 1]
      );
      console.log(`✓ 创建租户2成功，ID: ${result.insertId}`);
    } else {
      console.log('✓ 租户2已存在');
    }

    await connection.end();
    console.log('\n✓ 检查完成');

  } catch (error) {
    console.error('检查失败:', error.message);
  }
}

checkTablesAndUsers();
