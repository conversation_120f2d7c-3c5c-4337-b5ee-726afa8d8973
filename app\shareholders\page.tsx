"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Download, Upload, Filter, LayoutGrid, LayoutList, BarChart2, Search } from "lucide-react"
import { ShareholderTable } from "@/components/shareholders/shareholder-table"
import { ShareholderGrid } from "@/components/shareholders/shareholder-grid"
import { AddShareholderDialog } from "@/components/shareholders/add-shareholder-dialog"
import { ShareholderStatsDialog } from "@/components/shareholders/shareholder-stats-dialog"
import { AdvancedFilterDialog } from "@/components/shareholders/advanced-filter-dialog"
import { ImportShareholdersDialog } from "@/components/shareholders/import-shareholders-dialog"
import { ShareholderBreadcrumb } from "@/components/shareholders/shareholder-breadcrumb"
import { ShareholderNav } from "@/components/shareholders/shareholder-nav"

export default function ShareholdersPage() {
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showStatsDialog, setShowStatsDialog] = useState(false)
  const [showFilterDialog, setShowFilterDialog] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [shareholderType, setShareholderType] = useState("all")

  return (
    <div className="space-y-6">
      <ShareholderBreadcrumb />
      <ShareholderNav />

      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">股东管理</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowStatsDialog(true)}>
            <BarChart2 className="mr-2 h-4 w-4" />
            股东统计
          </Button>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加股东
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center gap-2 max-w-md">
          <Input
            placeholder="搜索股东姓名、电话或标签..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
          <Button variant="outline" size="icon" onClick={() => setShowFilterDialog(true)}>
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Select value={shareholderType} onValueChange={setShareholderType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="股东类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="consumer">消费型股东</SelectItem>
              <SelectItem value="investor">投资型股东</SelectItem>
              <SelectItem value="resource">资源型股东</SelectItem>
              <SelectItem value="employee">员工型股东</SelectItem>
              <SelectItem value="alliance">联盟型股东</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center border rounded-md">
            <Button
              variant="ghost"
              size="icon"
              className={`rounded-none ${viewMode === "list" ? "bg-muted" : ""}`}
              onClick={() => setViewMode("list")}
            >
              <LayoutList className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`rounded-none ${viewMode === "grid" ? "bg-muted" : ""}`}
              onClick={() => setViewMode("grid")}
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
          </div>

          <Button variant="outline" onClick={() => setShowImportDialog(true)}>
            <Upload className="mr-2 h-4 w-4" />
            导入
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      {viewMode === "list" ? (
        <ShareholderTable searchQuery={searchQuery} shareholderType={shareholderType} />
      ) : (
        <ShareholderGrid searchQuery={searchQuery} shareholderType={shareholderType} />
      )}

      <AddShareholderDialog open={showAddDialog} onOpenChange={setShowAddDialog} />
      <ImportShareholdersDialog open={showImportDialog} onOpenChange={setShowImportDialog} />
      <ShareholderStatsDialog open={showStatsDialog} onOpenChange={setShowStatsDialog} />
      <AdvancedFilterDialog open={showFilterDialog} onOpenChange={setShowFilterDialog} />
    </div>
  )
}
