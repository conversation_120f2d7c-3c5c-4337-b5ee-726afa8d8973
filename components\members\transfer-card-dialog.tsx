"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { CreditCard, Search, UserPlus } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"

interface TransferCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
    card: string
    remaining: string
    expiry: string
  }
}

export function TransferCardDialog({ open, onOpenChange, member }: TransferCardDialogProps) {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [transferType, setTransferType] = useState("full")
  const [transferAmount, setTransferAmount] = useState("")
  const [reason, setReason] = useState("")
  const [selectedMember, setSelectedMember] = useState<any>(null)
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [agreeTerms, setAgreeTerms] = useState(false)

  // 模拟搜索会员
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: "请输入搜索内容",
        description: "请输入会员姓名或手机号进行搜索",
        variant: "destructive",
      })
      return
    }

    setIsSearching(true)
    setSearchResults([])

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      // 模拟搜索结果
      setSearchResults([
        { id: "1", name: "李四", phone: "139****5678", avatar: "" },
        { id: "2", name: "王五", phone: "158****9012", avatar: "" },
        { id: "3", name: "赵六", phone: "137****3456", avatar: "" },
      ])
    } catch (error) {
      toast({
        title: "搜索失败",
        description: "无法搜索会员，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSearching(false)
    }
  }

  const handleSelectMember = (member: any) => {
    setSelectedMember(member)
    setSearchResults([])
  }

  const handleSubmit = async () => {
    if (!selectedMember) {
      toast({
        title: "请选择接收会员",
        description: "请先搜索并选择要转让给的会员",
        variant: "destructive",
      })
      return
    }

    if (transferType === "partial" && (!transferAmount || isNaN(Number(transferAmount)) || Number(transferAmount) <= 0)) {
      toast({
        title: "请输入有效的转让数量",
        description: "转让数量必须是大于0的数字",
        variant: "destructive",
      })
      return
    }

    if (!agreeTerms) {
      toast({
        title: "请同意转让条款",
        description: "您需要同意转让条款才能继续",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "转让申请已提交",
        description: `会员卡已成功转让给 ${selectedMember.name}`,
      })
      
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "操作失败",
        description: "转让申请提交失败，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>会员卡转让</DialogTitle>
          <DialogDescription>
            将会员卡剩余次数或时间转让给其他会员
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex items-center gap-4">
              <CreditCard className="h-8 w-8 text-muted-foreground" />
              <div>
                <h4 className="font-medium">{member.name}</h4>
                <p className="text-sm text-muted-foreground">
                  {member.card} (剩余: {member.remaining}, 到期: {member.expiry})
                </p>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>转让类型</Label>
            <RadioGroup value={transferType} onValueChange={setTransferType} className="flex flex-col space-y-1">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="full" id="full" />
                <Label htmlFor="full">全部转让 (转让全部剩余次数/时间)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="partial" id="partial" />
                <Label htmlFor="partial">部分转让 (转让部分剩余次数/时间)</Label>
              </div>
            </RadioGroup>
          </div>
          
          {transferType === "partial" && (
            <div className="space-y-2">
              <Label htmlFor="transfer-amount">转让数量</Label>
              <Input
                id="transfer-amount"
                type="number"
                placeholder="请输入转让次数"
                value={transferAmount}
                onChange={(e) => setTransferAmount(e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                可转让最大数量: {member.remaining}
              </p>
            </div>
          )}
          
          <div className="space-y-2">
            <Label>接收会员</Label>
            <div className="flex gap-2">
              <Input
                placeholder="输入会员姓名或手机号"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Button type="button" onClick={handleSearch} disabled={isSearching}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
            
            {isSearching && <p className="text-sm text-muted-foreground">搜索中...</p>}
            
            {searchResults.length > 0 && (
              <div className="mt-2 max-h-[150px] overflow-y-auto rounded-md border p-2">
                {searchResults.map((result) => (
                  <div
                    key={result.id}
                    className="flex cursor-pointer items-center gap-2 rounded-md p-2 hover:bg-muted"
                    onClick={() => handleSelectMember(result)}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={result.avatar} alt={result.name} />
                      <AvatarFallback>{result.name[0]}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{result.name}</p>
                      <p className="text-xs text-muted-foreground">{result.phone}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            {selectedMember && (
              <div className="mt-2 flex items-center gap-2 rounded-md border p-3">
                <UserPlus className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">接收会员: {selectedMember.name}</p>
                  <p className="text-sm text-muted-foreground">{selectedMember.phone}</p>
                </div>
              </div>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="reason">转让原因</Label>
            <Textarea
              id="reason"
              placeholder="请输入转让原因..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox id="terms" checked={agreeTerms} onCheckedChange={(checked) => setAgreeTerms(checked as boolean)} />
            <label
              htmlFor="terms"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              我已阅读并同意《会员卡转让条款》
            </label>
          </div>
          
          <div className="rounded-md bg-muted p-3 text-sm">
            <p className="font-medium">转让说明：</p>
            <ul className="list-disc pl-5 pt-2 text-muted-foreground">
              <li>转让后无法撤销，请谨慎操作</li>
              <li>转让需经过管理员审核，可能需要1-2个工作日</li>
              <li>转让不会改变会员卡的有效期</li>
              <li>每张会员卡每年最多可转让1次</li>
            </ul>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>取消</Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "提交中..." : "提交转让"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
