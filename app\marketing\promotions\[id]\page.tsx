import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Calendar, Clock, Copy, Download, Edit, Pause, QrCode, Share2, Trash } from "lucide-react"
import Link from "next/link"

export default function PromotionDetailPage({ params }: { params: { id: string } }) {
  // 在实际应用中，这里会根据 ID 从 API 获取促销活动详情
  const promotionId = params.id

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/marketing/promotions">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">春季会员招募活动</h1>
        <Badge variant="success">进行中</Badge>
      </div>

      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-2">
          <Tabs defaultValue="overview">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">概览</TabsTrigger>
              <TabsTrigger value="participants">参与会员</TabsTrigger>
              <TabsTrigger value="statistics">数据统计</TabsTrigger>
              <TabsTrigger value="settings">活动设置</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>活动信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">活动名称</p>
                      <p>春季会员招募活动</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">活动类型</p>
                      <Badge variant="secondary">限时折扣</Badge>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">活动时间</p>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>2025-04-01 至 2025-04-30</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">每日活动时间</p>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>全天</span>
                      </div>
                    </div>
                    <div className="col-span-2">
                      <p className="text-sm text-muted-foreground">活动说明</p>
                      <p>
                        新会员首月半价，赠送瑜伽垫。活动期间，新注册会员可享受首月课程半价优惠，并赠送价值¥100的高品质瑜伽垫一个。
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle>参与人数</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-end justify-between">
                      <div>
                        <p className="text-3xl font-bold">156</p>
                        <p className="text-sm text-muted-foreground">总参与人数</p>
                      </div>
                      <div className="text-right">
                        <p className="text-xl font-semibold text-green-600">+12</p>
                        <p className="text-sm text-muted-foreground">今日新增</p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>进度</span>
                        <span>31%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="bg-green-600 h-2.5 rounded-full" style={{ width: "31%" }}></div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">目标: 500人</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle>转化率</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-end justify-between">
                      <div>
                        <p className="text-3xl font-bold">32%</p>
                        <p className="text-sm text-muted-foreground">总转化率</p>
                      </div>
                      <div className="text-right">
                        <p className="text-xl font-semibold text-green-600">+2.5%</p>
                        <p className="text-sm text-muted-foreground">较昨日</p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>浏览人数</span>
                        <span>487</span>
                      </div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>参与人数</span>
                        <span>156</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>目标转化率</span>
                        <span>40%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>销售数据</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">带动销售额</p>
                      <p className="text-2xl font-bold">¥15,600</p>
                      <p className="text-xs text-green-600">较昨日 +¥1,200</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">平均客单价</p>
                      <p className="text-2xl font-bold">¥100</p>
                      <p className="text-xs text-green-600">较昨日 +¥5</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">新增会员数</p>
                      <p className="text-2xl font-bold">78</p>
                      <p className="text-xs text-green-600">较昨日 +6</p>
                    </div>
                  </div>

                  <div className="mt-6">
                    <p className="text-sm font-medium mb-2">每日销售趋势</p>
                    <div className="h-[200px] bg-gray-50 rounded-md flex items-center justify-center">
                      <p className="text-muted-foreground">图表区域</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="participants" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>参与会员列表</CardTitle>
                  <CardDescription>查看所有参与此活动的会员</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px] bg-gray-50 rounded-md flex items-center justify-center">
                    <p className="text-muted-foreground">会员列表区域</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="statistics" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>数据统计</CardTitle>
                  <CardDescription>查看活动的详细数据分析</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px] bg-gray-50 rounded-md flex items-center justify-center">
                    <p className="text-muted-foreground">数据统计区域</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>活动设置</CardTitle>
                  <CardDescription>修改活动的各项设置</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[400px] bg-gray-50 rounded-md flex items-center justify-center">
                    <p className="text-muted-foreground">活动设置区域</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>活动状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center space-y-2">
                <Badge variant="success" className="mb-2">
                  进行中
                </Badge>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div className="bg-green-600 h-2.5 rounded-full" style={{ width: "45%" }}></div>
                </div>
                <p className="text-sm text-muted-foreground">已进行 45%</p>
                <p className="text-sm">剩余 16 天</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>快捷操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full">
                <Edit className="mr-2 h-4 w-4" />
                编辑活动
              </Button>
              <Button variant="outline" className="w-full">
                <QrCode className="mr-2 h-4 w-4" />
                生成二维码
              </Button>
              <Button variant="outline" className="w-full">
                <Share2 className="mr-2 h-4 w-4" />
                分享活动
              </Button>
              <Button variant="outline" className="w-full">
                <Copy className="mr-2 h-4 w-4" />
                复制活动
              </Button>
              <Button variant="outline" className="w-full">
                <Pause className="mr-2 h-4 w-4" />
                暂停活动
              </Button>
              <Button variant="outline" className="w-full text-destructive hover:text-destructive">
                <Trash className="mr-2 h-4 w-4" />
                删除活动
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>活动资源</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full">
                <Download className="mr-2 h-4 w-4" />
                导出参与数据
              </Button>
              <Button variant="outline" className="w-full">
                <Download className="mr-2 h-4 w-4" />
                导出销售数据
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

