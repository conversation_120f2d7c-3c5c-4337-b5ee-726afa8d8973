-- 瑜伽馆管理系统核心数据表

-- 1. 用户表（系统用户）
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT NOT NULL COMMENT '租户ID',
  username VARCHAR(50) NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
  nickname VARCHAR(100) COMMENT '昵称',
  email VARCHAR(100) COMMENT '邮箱',
  phone VARCHAR(20) COMMENT '手机号',
  avatar VARCHAR(500) COMMENT '头像URL',
  role ENUM('admin', 'manager', 'staff', 'coach', 'reception') DEFAULT 'staff' COMMENT '角色',
  status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
  last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY uk_username_tenant (username, tenant_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_role (role),
  INDEX idx_status (status),
  
  FOREIGN KEY (tenant_id) REFERENCES tenant(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='System Users Table';

-- 2. 会员表
CREATE TABLE IF NOT EXISTS members (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT NOT NULL COMMENT '租户ID',
  member_no VARCHAR(50) NOT NULL COMMENT '会员编号',
  name VARCHAR(100) NOT NULL COMMENT '姓名',
  phone VARCHAR(20) NOT NULL COMMENT '手机号',
  email VARCHAR(100) COMMENT '邮箱',
  gender ENUM('male', 'female', 'other') COMMENT '性别',
  birthday DATE COMMENT '生日',
  avatar VARCHAR(500) COMMENT '头像URL',
  emergency_contact VARCHAR(100) COMMENT '紧急联系人',
  emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
  address TEXT COMMENT '地址',
  notes TEXT COMMENT '备注',
  level ENUM('bronze', 'silver', 'gold', 'platinum', 'diamond') DEFAULT 'bronze' COMMENT '会员等级',
  points INT DEFAULT 0 COMMENT '积分',
  total_spent DECIMAL(10,2) DEFAULT 0 COMMENT '累计消费',
  status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
  source ENUM('online', 'offline', 'referral', 'promotion') DEFAULT 'offline' COMMENT '来源',
  referrer_id INT COMMENT '推荐人ID',
  registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY uk_member_no_tenant (member_no, tenant_id),
  UNIQUE KEY uk_phone_tenant (phone, tenant_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_level (level),
  INDEX idx_status (status),
  INDEX idx_referrer_id (referrer_id),
  
  FOREIGN KEY (tenant_id) REFERENCES tenant(id) ON DELETE CASCADE,
  FOREIGN KEY (referrer_id) REFERENCES members(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Members Table';

-- 3. 会员卡实例表（会员持有的卡）
CREATE TABLE IF NOT EXISTS member_cards (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT NOT NULL COMMENT '租户ID',
  member_id INT NOT NULL COMMENT '会员ID',
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  card_no VARCHAR(50) NOT NULL COMMENT '卡号',
  status ENUM('active', 'expired', 'suspended', 'used_up') DEFAULT 'active' COMMENT '状态',
  remaining_times INT COMMENT '剩余次数（次数卡）',
  remaining_amount DECIMAL(10,2) COMMENT '剩余金额（储值卡）',
  remaining_days INT COMMENT '剩余天数（期限卡）',
  activated_at TIMESTAMP NULL COMMENT '激活时间',
  expires_at TIMESTAMP NULL COMMENT '到期时间',
  purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '购买时间',
  purchase_price DECIMAL(10,2) NOT NULL COMMENT '购买价格',
  notes TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY uk_card_no_tenant (card_no, tenant_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_member_id (member_id),
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_status (status),
  INDEX idx_expires_at (expires_at),
  
  FOREIGN KEY (tenant_id) REFERENCES tenant(id) ON DELETE CASCADE,
  FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Cards Table';

-- 4. 课程预约表
CREATE TABLE IF NOT EXISTS bookings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT NOT NULL COMMENT '租户ID',
  member_id INT NOT NULL COMMENT '会员ID',
  course_id INT NOT NULL COMMENT '课程ID',
  member_card_id INT COMMENT '使用的会员卡ID',
  booking_no VARCHAR(50) NOT NULL COMMENT '预约编号',
  status ENUM('booked', 'checked_in', 'completed', 'cancelled', 'no_show') DEFAULT 'booked' COMMENT '状态',
  booking_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '预约时间',
  check_in_time TIMESTAMP NULL COMMENT '签到时间',
  cancel_time TIMESTAMP NULL COMMENT '取消时间',
  cancel_reason TEXT COMMENT '取消原因',
  consumed_times INT DEFAULT 0 COMMENT '消耗次数',
  consumed_amount DECIMAL(10,2) DEFAULT 0 COMMENT '消耗金额',
  notes TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY uk_booking_no_tenant (booking_no, tenant_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_member_id (member_id),
  INDEX idx_course_id (course_id),
  INDEX idx_member_card_id (member_card_id),
  INDEX idx_status (status),
  INDEX idx_booking_time (booking_time),
  
  FOREIGN KEY (tenant_id) REFERENCES tenant(id) ON DELETE CASCADE,
  FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
  FOREIGN KEY (course_id) REFERENCES course(id) ON DELETE CASCADE,
  FOREIGN KEY (member_card_id) REFERENCES member_cards(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Course Bookings Table';

-- 5. 订单表
CREATE TABLE IF NOT EXISTS orders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT NOT NULL COMMENT '租户ID',
  member_id INT COMMENT '会员ID',
  order_no VARCHAR(50) NOT NULL COMMENT '订单编号',
  type ENUM('member_card', 'course', 'product', 'service') NOT NULL COMMENT '订单类型',
  total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
  paid_amount DECIMAL(10,2) DEFAULT 0 COMMENT '已支付金额',
  discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
  status ENUM('pending', 'paid', 'cancelled', 'refunded', 'partial_refunded') DEFAULT 'pending' COMMENT '订单状态',
  payment_method ENUM('cash', 'alipay', 'wechat', 'card', 'transfer') COMMENT '支付方式',
  payment_time TIMESTAMP NULL COMMENT '支付时间',
  notes TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY uk_order_no_tenant (order_no, tenant_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_member_id (member_id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_payment_time (payment_time),
  
  FOREIGN KEY (tenant_id) REFERENCES tenant(id) ON DELETE CASCADE,
  FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Orders Table';

-- 6. 订单明细表
CREATE TABLE IF NOT EXISTS order_items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  order_id INT NOT NULL COMMENT '订单ID',
  item_type ENUM('member_card', 'course', 'product', 'service') NOT NULL COMMENT '商品类型',
  item_id INT NOT NULL COMMENT '商品ID',
  item_name VARCHAR(200) NOT NULL COMMENT '商品名称',
  quantity INT DEFAULT 1 COMMENT '数量',
  unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
  total_price DECIMAL(10,2) NOT NULL COMMENT '小计',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_order_id (order_id),
  INDEX idx_item_type_id (item_type, item_id),
  
  FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Order Items Table';
