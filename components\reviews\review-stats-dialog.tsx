"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { ReviewStats } from "@/lib/types/review"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Star as StarIcon, ThumbsDown, ThumbsUp } from "lucide-react"

interface ReviewStatsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  stats: ReviewStats
}

export function ReviewStatsDialog({ open, onOpenChange, stats }: ReviewStatsDialogProps) {
  // 计算评分分布百分比
  const calculatePercentage = (count: number) => {
    return Math.round((count / stats.total) * 100) || 0
  }

  // 获取评分分布数据
  const getRatingDistribution = () => {
    return [5, 4, 3, 2, 1].map(rating => ({
      rating,
      count: stats.ratingDistribution[rating] || 0,
      percentage: calculatePercentage(stats.ratingDistribution[rating] || 0)
    }))
  }

  // 获取情感分析数据
  const getSentimentData = () => {
    const { positive, neutral, negative } = stats.sentimentAnalysis
    return [
      { label: "正面评价", value: positive, percentage: calculatePercentage(positive), color: "bg-green-500" },
      { label: "中性评价", value: neutral, percentage: calculatePercentage(neutral), color: "bg-blue-500" },
      { label: "负面评价", value: negative, percentage: calculatePercentage(negative), color: "bg-red-500" }
    ]
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>评价统计分析</DialogTitle>
          <DialogDescription>查看课程与教练评价的统计数据和分析</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">总体概览</TabsTrigger>
            <TabsTrigger value="ratings">评分分析</TabsTrigger>
            <TabsTrigger value="sentiment">情感分析</TabsTrigger>
          </TabsList>
          
          {/* 总体概览标签页 */}
          <TabsContent value="overview" className="space-y-4 mt-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-base">总评价数</CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="text-3xl font-bold">{stats.total}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-base">平均评分</CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="flex items-center">
                    <div className="text-3xl font-bold">{stats.averageRating.toFixed(1)}</div>
                    <div className="ml-2 flex">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <StarIcon
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(stats.averageRating)
                              ? "text-yellow-500 fill-yellow-500"
                              : i < stats.averageRating
                                ? "text-yellow-500 fill-yellow-500 opacity-50"
                                : "text-gray-300"
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-base">回复率</CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="text-3xl font-bold">{stats.replyRate}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-base">待审核</CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="text-3xl font-bold">{stats.pending}</div>
                </CardContent>
              </Card>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>评价状态分布</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Badge variant="default">已发布</Badge>
                        <span className="ml-2">{stats.published} 条</span>
                      </div>
                      <span>{calculatePercentage(stats.published)}%</span>
                    </div>
                    <Progress value={calculatePercentage(stats.published)} className="h-2" />
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">待审核</Badge>
                        <span className="ml-2">{stats.pending} 条</span>
                      </div>
                      <span>{calculatePercentage(stats.pending)}%</span>
                    </div>
                    <Progress value={calculatePercentage(stats.pending)} className="h-2 bg-muted" />
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Badge variant="outline" className="text-gray-500">已隐藏</Badge>
                        <span className="ml-2">{stats.hidden} 条</span>
                      </div>
                      <span>{calculatePercentage(stats.hidden)}%</span>
                    </div>
                    <Progress value={calculatePercentage(stats.hidden)} className="h-2 bg-muted" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>热门关键词</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {stats.topKeywords.map((keyword, index) => (
                      <Badge 
                        key={index} 
                        variant="outline" 
                        className={`text-base py-1 px-3 ${
                          index === 0 ? "bg-blue-50 border-blue-200 text-blue-700" :
                          index === 1 ? "bg-green-50 border-green-200 text-green-700" :
                          index === 2 ? "bg-yellow-50 border-yellow-200 text-yellow-700" :
                          "bg-gray-50 border-gray-200 text-gray-700"
                        }`}
                      >
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          {/* 评分分析标签页 */}
          <TabsContent value="ratings" className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <CardTitle>评分分布</CardTitle>
                <CardDescription>各评分等级的分布情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getRatingDistribution().map(item => (
                    <div key={item.rating} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="w-4 text-sm font-medium">{item.rating}</span>
                          <StarIcon className="h-4 w-4 ml-1 text-yellow-500 fill-yellow-500" />
                          <span className="ml-2">{item.count} 条</span>
                        </div>
                        <span>{item.percentage}%</span>
                      </div>
                      <Progress 
                        value={item.percentage} 
                        className={`h-2 ${
                          item.rating === 5 ? "bg-green-100" :
                          item.rating === 4 ? "bg-blue-100" :
                          item.rating === 3 ? "bg-yellow-100" :
                          item.rating === 2 ? "bg-orange-100" :
                          "bg-red-100"
                        }`}
                      />
                    </div>
                  ))}
                </div>
                
                <div className="mt-6 flex justify-center">
                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold">{stats.averageRating.toFixed(1)}</div>
                      <div className="text-sm text-muted-foreground">平均评分</div>
                    </div>
                    <Separator orientation="vertical" className="h-10" />
                    <div className="text-center">
                      <div className="text-3xl font-bold">{stats.ratingDistribution[5] || 0}</div>
                      <div className="text-sm text-muted-foreground">5星评价</div>
                    </div>
                    <Separator orientation="vertical" className="h-10" />
                    <div className="text-center">
                      <div className="text-3xl font-bold">{(stats.ratingDistribution[1] || 0) + (stats.ratingDistribution[2] || 0)}</div>
                      <div className="text-sm text-muted-foreground">低分评价</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>评分趋势</CardTitle>
                <CardDescription>评分随时间的变化趋势</CardDescription>
              </CardHeader>
              <CardContent className="h-[200px] flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <BarChart className="h-16 w-16 mx-auto mb-2 opacity-30" />
                  <p>评分趋势图表将在此显示</p>
                  <p className="text-sm">（实际项目中接入图表库）</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* 情感分析标签页 */}
          <TabsContent value="sentiment" className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <CardTitle>情感分析</CardTitle>
                <CardDescription>评价的情感倾向分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {getSentimentData().map(item => (
                    <div key={item.label} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          {item.label === "正面评价" && <ThumbsUp className="h-4 w-4 text-green-600 mr-2" />}
                          {item.label === "中性评价" && <Badge className="h-2 w-2 bg-blue-600 mr-2" />}
                          {item.label === "负面评价" && <ThumbsDown className="h-4 w-4 text-red-600 mr-2" />}
                          <span>{item.label}</span>
                          <span className="ml-2">{item.value} 条</span>
                        </div>
                        <span>{item.percentage}%</span>
                      </div>
                      <Progress value={item.percentage} className={`h-2 ${item.color}`} />
                    </div>
                  ))}
                </div>
                
                <div className="mt-6 flex justify-center">
                  <div className="h-[200px] w-[200px] flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <PieChart className="h-16 w-16 mx-auto mb-2 opacity-30" />
                      <p>情感分布饼图将在此显示</p>
                      <p className="text-sm">（实际项目中接入图表库）</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>情感分析建议</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {stats.sentimentAnalysis.negative > 0 && (
                    <div className="p-4 border rounded-md bg-red-50 border-red-200">
                      <div className="font-medium text-red-800 mb-1 flex items-center">
                        <ThumbsDown className="h-4 w-4 mr-2" />
                        负面评价处理建议
                      </div>
                      <p className="text-sm text-red-700">
                        系统检测到 {stats.sentimentAnalysis.negative} 条负面评价，建议及时回复并解决会员提出的问题，
                        必要时可提供补偿或邀请会员再次体验改进后的服务。
                      </p>
                    </div>
                  )}
                  
                  {stats.sentimentAnalysis.neutral > 0 && (
                    <div className="p-4 border rounded-md bg-blue-50 border-blue-200">
                      <div className="font-medium text-blue-800 mb-1">中性评价处理建议</div>
                      <p className="text-sm text-blue-700">
                        系统检测到 {stats.sentimentAnalysis.neutral} 条中性评价，建议深入了解会员需求，
                        针对性地改进服务，将中性评价转化为正面评价。
                      </p>
                    </div>
                  )}
                  
                  {stats.sentimentAnalysis.positive > 0 && (
                    <div className="p-4 border rounded-md bg-green-50 border-green-200">
                      <div className="font-medium text-green-800 mb-1 flex items-center">
                        <ThumbsUp className="h-4 w-4 mr-2" />
                        正面评价处理建议
                      </div>
                      <p className="text-sm text-green-700">
                        系统检测到 {stats.sentimentAnalysis.positive} 条正面评价，建议回复感谢并鼓励会员继续参与，
                        可考虑将优质评价用于营销宣传。
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
