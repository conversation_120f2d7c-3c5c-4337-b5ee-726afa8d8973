"use client"

import type React from "react"

import { useState } from "react"
import {
  Bell,
  ChevronDown,
  Search,
  Settings,
  LogOut,
  User,
  Shield,
  HelpCircle,
  Moon,
  Sun,
  MessageSquare,
  Calendar,
  FileText,
  AlertTriangle,
  CheckCircle,
  X,
  Key,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { useAuth } from "@/contexts/auth-context"
import { authApi } from "@/lib/api/auth"
import { useToast } from "@/hooks/use-toast"

export function TopNav() {
  const { user, logout } = useAuth()
  const { toast } = useToast()
  const [theme, setTheme] = useState<"light" | "dark">("light")
  const [notificationCount, setNotificationCount] = useState(5)
  const [showAllNotifications, setShowAllNotifications] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light"
    setTheme(newTheme)
    // Add class to html element
    if (newTheme === "dark") {
      document.documentElement.classList.add("dark")
    } else {
      document.documentElement.classList.remove("dark")
    }
  }

  const clearNotifications = () => {
    setNotificationCount(0)
  }

  const markAllAsRead = () => {
    // Implementation would mark all as read in a real app
    setNotificationCount(0)
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Implementation would search in a real app
    console.log("Searching for:", searchQuery)
  }

  // Sample notifications data
  const notifications = [
    {
      id: 1,
      type: "message",
      title: "新消息",
      content: "李教练发来了一条关于课程安排的消息",
      time: "10分钟前",
      read: false,
      icon: <MessageSquare className="h-4 w-4 text-blue-500" />,
    },
    {
      id: 2,
      type: "booking",
      title: "课程预约",
      content: "今日瑜伽基础课已达到80%预约率",
      time: "30分钟前",
      read: false,
      icon: <Calendar className="h-4 w-4 text-green-500" />,
    },
    {
      id: 3,
      type: "system",
      title: "系统通知",
      content: "系统将于今晚23:00-23:30进行例行维护",
      time: "1小时前",
      read: true,
      icon: <AlertTriangle className="h-4 w-4 text-yellow-500" />,
    },
    {
      id: 4,
      type: "report",
      title: "日报生成",
      content: "昨日运营数据报告已生成，请查看",
      time: "2小时前",
      read: true,
      icon: <FileText className="h-4 w-4 text-purple-500" />,
    },
    {
      id: 5,
      type: "system",
      title: "备份完成",
      content: "系统数据自动备份已完成",
      time: "昨天",
      read: true,
      icon: <CheckCircle className="h-4 w-4 text-green-500" />,
    },
  ]

  // More notifications for "show all" view
  const allNotifications = [
    ...notifications,
    {
      id: 6,
      type: "message",
      title: "会员留言",
      content: "新会员王女士对瑜伽初级课程有疑问",
      time: "昨天",
      read: true,
      icon: <MessageSquare className="h-4 w-4 text-blue-500" />,
    },
    {
      id: 7,
      type: "booking",
      title: "课程取消",
      content: "明日上午的普拉提课程已被取消",
      time: "2天前",
      read: true,
      icon: <Calendar className="h-4 w-4 text-red-500" />,
    },
    {
      id: 8,
      type: "system",
      title: "系统更新",
      content: "系统已更新至最新版本v2.3.1",
      time: "3天前",
      read: true,
      icon: <CheckCircle className="h-4 w-4 text-green-500" />,
    },
  ]

  const displayNotifications = showAllNotifications ? allNotifications : notifications.slice(0, 3)

  // Quick settings options
  const quickSettings = [
    { id: "notifications", label: "通知提醒", defaultChecked: true },
    { id: "sounds", label: "提示音", defaultChecked: true },
    { id: "auto-logout", label: "自动登出", defaultChecked: false },
    { id: "compact-view", label: "紧凑视图", defaultChecked: false },
  ]

  // 处理退出登录
  const handleLogout = () => {
    try {
      // 直接调用本地登出函数，不再调用可能不存在的API接口
      // 注释掉API调用，避免请求错误
      // await authApi.logout();
      
      // 清除本地存储并跳转到登录页
      logout();
      
      toast({
        title: '登出成功',
        description: '您已安全退出系统',
        variant: 'default'
      });
    } catch (error) {
      console.error('登出失败:', error);
      toast({
        title: '登出失败',
        description: '请稍后再试',
        variant: 'destructive'
      });
    }
  };
  
  // 用户信息显示
  const userDisplayName = user?.nickname || user?.username || "用户";
  const userAvatar = user?.avatar || "";
  const userInitial = userDisplayName.charAt(0).toUpperCase();

  return (
    <div className="flex h-16 items-center border-b px-4">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <span>工作台</span>
      </div>
      <div className="ml-auto flex items-center gap-4">
        <form onSubmit={handleSearch} className="relative hidden md:block">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="搜索..."
            className="w-64 rounded-full bg-muted pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </form>

        {/* Notifications Popover */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon" className="relative">
              <Bell className="h-5 w-5" />
              {notificationCount > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-[10px]"
                >
                  {notificationCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="end">
            <div className="flex items-center justify-between p-3 border-b">
              <h4 className="font-medium">通知中心</h4>
              <div className="flex gap-1">
                <Button variant="ghost" size="icon" className="h-7 w-7" onClick={markAllAsRead} title="全部标为已读">
                  <CheckCircle className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-7 w-7" onClick={clearNotifications} title="清空通知">
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <Tabs defaultValue="all">
              <TabsList className="w-full grid grid-cols-4 rounded-none border-b">
                <TabsTrigger value="all" className="text-xs">
                  全部
                </TabsTrigger>
                <TabsTrigger value="unread" className="text-xs">
                  未读
                </TabsTrigger>
                <TabsTrigger value="system" className="text-xs">
                  系统
                </TabsTrigger>
                <TabsTrigger value="message" className="text-xs">
                  消息
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="p-0 m-0">
                <ScrollArea className="h-[300px]">
                  {displayNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={cn(
                        "flex items-start gap-3 p-3 hover:bg-muted transition-colors cursor-pointer",
                        !notification.read && "bg-muted/50",
                      )}
                    >
                      <div className="mt-1">{notification.icon}</div>
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">{notification.title}</p>
                          <span className="text-xs text-muted-foreground">{notification.time}</span>
                        </div>
                        <p className="text-xs text-muted-foreground">{notification.content}</p>
                      </div>
                      {!notification.read && <div className="h-2 w-2 rounded-full bg-blue-500 mt-2"></div>}
                    </div>
                  ))}
                </ScrollArea>
                <div className="p-3 border-t text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-xs"
                    onClick={() => setShowAllNotifications(!showAllNotifications)}
                  >
                    {showAllNotifications ? "收起" : "查看全部通知"}
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="unread" className="p-0 m-0">
                <ScrollArea className="h-[300px]">
                  {displayNotifications
                    .filter((n) => !n.read)
                    .map((notification) => (
                      <div
                        key={notification.id}
                        className="flex items-start gap-3 p-3 hover:bg-muted transition-colors cursor-pointer bg-muted/50"
                      >
                        <div className="mt-1">{notification.icon}</div>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">{notification.title}</p>
                            <span className="text-xs text-muted-foreground">{notification.time}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">{notification.content}</p>
                        </div>
                        <div className="h-2 w-2 rounded-full bg-blue-500 mt-2"></div>
                      </div>
                    ))}
                </ScrollArea>
                <div className="p-3 border-t text-center">
                  <Button variant="ghost" size="sm" className="w-full text-xs">
                    查看全部未读通知
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="system" className="p-0 m-0">
                <ScrollArea className="h-[300px]">
                  {displayNotifications
                    .filter((n) => n.type === "system")
                    .map((notification) => (
                      <div
                        key={notification.id}
                        className={cn(
                          "flex items-start gap-3 p-3 hover:bg-muted transition-colors cursor-pointer",
                          !notification.read && "bg-muted/50",
                        )}
                      >
                        <div className="mt-1">{notification.icon}</div>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">{notification.title}</p>
                            <span className="text-xs text-muted-foreground">{notification.time}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">{notification.content}</p>
                        </div>
                        {!notification.read && <div className="h-2 w-2 rounded-full bg-blue-500 mt-2"></div>}
                      </div>
                    ))}
                </ScrollArea>
                <div className="p-3 border-t text-center">
                  <Button variant="ghost" size="sm" className="w-full text-xs">
                    查看全部系统通知
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="message" className="p-0 m-0">
                <ScrollArea className="h-[300px]">
                  {displayNotifications
                    .filter((n) => n.type === "message")
                    .map((notification) => (
                      <div
                        key={notification.id}
                        className={cn(
                          "flex items-start gap-3 p-3 hover:bg-muted transition-colors cursor-pointer",
                          !notification.read && "bg-muted/50",
                        )}
                      >
                        <div className="mt-1">{notification.icon}</div>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium">{notification.title}</p>
                            <span className="text-xs text-muted-foreground">{notification.time}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">{notification.content}</p>
                        </div>
                        {!notification.read && <div className="h-2 w-2 rounded-full bg-blue-500 mt-2"></div>}
                      </div>
                    ))}
                </ScrollArea>
                <div className="p-3 border-t text-center">
                  <Button variant="ghost" size="sm" className="w-full text-xs">
                    查看全部消息
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </PopoverContent>
        </Popover>

        {/* Settings Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <Settings className="h-5 w-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>快捷设置</DropdownMenuLabel>
            <DropdownMenuSeparator />

            <div className="p-2 space-y-2">
              {quickSettings.map((setting) => (
                <div key={setting.id} className="flex items-center justify-between">
                  <Label htmlFor={setting.id} className="text-sm cursor-pointer">
                    {setting.label}
                  </Label>
                  <Switch id={setting.id} defaultChecked={setting.defaultChecked} />
                </div>
              ))}
            </div>

            <DropdownMenuSeparator />

            <div className="p-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="theme-toggle" className="text-sm cursor-pointer">
                  {theme === "dark" ? "深色模式" : "浅色模式"}
                </Label>
                <Button variant="ghost" size="icon" className="h-8 w-8" onClick={toggleTheme}>
                  {theme === "dark" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <DropdownMenuSeparator />

            <DropdownMenuGroup>
              <DropdownMenuItem>
                <Shield className="mr-2 h-4 w-4" />
                <span>安全设置</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <HelpCircle className="mr-2 h-4 w-4" />
                <span>帮助中心</span>
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuItem>
              <Button variant="ghost" className="w-full justify-start p-0 h-auto font-normal text-sm">
                <Settings className="mr-2 h-4 w-4" />
                <span>所有设置</span>
              </Button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* User Profile Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Avatar className="h-8 w-8 cursor-pointer">
              <AvatarImage src={userAvatar} alt={userDisplayName} />
              <AvatarFallback>{userInitial}</AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>{userDisplayName}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              <span>个人信息</span>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              <span>设置</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>退出登录</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}

