import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"

// 角色类型
interface Role {
  id: number
  name: string
}

// 员工类型
interface Employee {
  id: number
  name: string
  role: number // 角色ID
}

interface RecipientSelectionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title?: string
  selectedRoles: number[]
  selectedEmployees: number[]
  onSave: (selectedRoles: number[], selectedEmployees: number[]) => void
  roles: Role[]
  employees: Employee[]
  defaultSelectedRoleId?: number // 默认选中的角色ID
}

export function RecipientSelectionDialog({
  open,
  onOpenChange,
  title = "接收人设置",
  selectedRoles,
  selectedEmployees,
  onSave,
  roles,
  employees,
  defaultSelectedRoleId
}: RecipientSelectionDialogProps) {
  // 本地状态
  const [localSelectedRoles, setLocalSelectedRoles] = useState<number[]>([])
  const [localSelectedEmployees, setLocalSelectedEmployees] = useState<number[]>([])

  // 当对话框打开时，初始化设置
  useEffect(() => {
    if (open) {
      // 初始化选中的角色
      let initialRoles: number[] = []
      if (selectedRoles.length > 0) {
        // 如果有已选择的角色，使用已选择的
        initialRoles = selectedRoles
      } else if (defaultSelectedRoleId) {
        // 如果有默认角色ID，则选中该角色
        initialRoles = [defaultSelectedRoleId]
      } else {
        // 否则选中所有角色
        initialRoles = roles.map(role => role.id)
      }
      setLocalSelectedRoles(initialRoles)

      // 初始化选中的员工
      let initialEmployees: number[] = []
      if (selectedEmployees.length > 0) {
        // 如果有已选择的员工，使用已选择的
        initialEmployees = selectedEmployees
      } else if (defaultSelectedRoleId) {
        // 如果有默认角色ID，则选中该角色下的所有员工
        initialEmployees = employees
          .filter(emp => emp.role === defaultSelectedRoleId)
          .map(emp => emp.id)
      } else {
        // 否则选中所有员工
        initialEmployees = employees.map(emp => emp.id)
      }
      setLocalSelectedEmployees(initialEmployees)
    }
  }, [open, selectedRoles, selectedEmployees, roles, employees, defaultSelectedRoleId])

  // 处理角色选择变化
  const handleRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setLocalSelectedRoles(prev => [...prev, roleId])

      // 当选中角色时，自动选中该角色下的所有员工
      const employeesOfRole = employees.filter(emp => emp.role === roleId).map(emp => emp.id)
      setLocalSelectedEmployees(prev => [...prev, ...employeesOfRole.filter(id => !prev.includes(id))])
    } else {
      setLocalSelectedRoles(prev => prev.filter(id => id !== roleId))

      // 当取消选中角色时，自动取消选中该角色下的所有员工
      const employeesOfRole = employees.filter(emp => emp.role === roleId).map(emp => emp.id)
      setLocalSelectedEmployees(prev => prev.filter(id => !employeesOfRole.includes(id)))
    }
  }

  // 处理员工选择变化
  const handleEmployeeChange = (employeeId: number, checked: boolean) => {
    const employee = employees.find(emp => emp.id === employeeId)
    if (!employee) return

    if (checked) {
      setLocalSelectedEmployees(prev => [...prev, employeeId])

      // 检查该角色下的所有员工是否都被选中，如果是，则自动选中该角色
      const roleId = employee.role
      const employeesOfRole = employees.filter(emp => emp.role === roleId)
      const allEmployeesSelected = employeesOfRole.every(emp =>
        localSelectedEmployees.includes(emp.id) || emp.id === employeeId
      )

      if (allEmployeesSelected && !localSelectedRoles.includes(roleId)) {
        setLocalSelectedRoles(prev => [...prev, roleId])
      }
    } else {
      setLocalSelectedEmployees(prev => prev.filter(id => id !== employeeId))

      // 当取消选中员工时，如果该角色被选中，则取消选中该角色
      const roleId = employee.role
      if (localSelectedRoles.includes(roleId)) {
        setLocalSelectedRoles(prev => prev.filter(id => id !== roleId))
      }
    }
  }

  // 处理保存按钮点击
  const handleSave = () => {
    onSave(localSelectedRoles, localSelectedEmployees)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md p-6 rounded-md">
        <h2 className="text-lg font-medium mb-6 text-center border-b pb-4">
          {title}
        </h2>

        <div className="space-y-4">
          <div className="flex items-start">
            <Label className="w-32 text-right mr-4 text-gray-700 pt-1">接收人设置</Label>
            <div className="space-y-3">
              <div className="space-y-1">
                <Label className="text-sm text-gray-500 block">选择接收提醒的角色</Label>
                <div className="grid grid-cols-2 gap-2 pl-1">
                  {roles.map(role => (
                    <div key={role.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`role-${role.id}`}
                        checked={localSelectedRoles.includes(role.id)}
                        onCheckedChange={(checked) =>
                          handleRoleChange(role.id, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`role-${role.id}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {role.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-start mt-4">
            <Label className="w-32 text-right mr-4 text-gray-700 invisible">员工</Label>
            <div className="space-y-2">
              <Label className="text-sm text-gray-500 block">选择接收提醒的员工</Label>

              {/* 按角色分组显示员工 */}
              <div className="space-y-4 max-h-60 overflow-y-auto pr-2">
                {roles.filter(role => localSelectedRoles.includes(role.id)).map(role => {
                  const roleEmployees = employees.filter(emp => emp.role === role.id);
                  if (roleEmployees.length === 0) return null;

                  return (
                    <div key={`emp-group-${role.id}`} className="space-y-2">
                      <div className="text-xs font-medium text-gray-500 border-b pb-1">{role.name}</div>
                      <div className="grid grid-cols-2 gap-2 pl-2">
                        {roleEmployees.map(employee => (
                          <div key={employee.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`employee-${employee.id}`}
                              checked={localSelectedEmployees.includes(employee.id)}
                              onCheckedChange={(checked) =>
                                handleEmployeeChange(employee.id, checked as boolean)
                              }
                            />
                            <Label
                              htmlFor={`employee-${employee.id}`}
                              className="text-sm font-normal cursor-pointer"
                            >
                              {employee.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="px-6"
          >
            取消
          </Button>
          <Button
            onClick={handleSave}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6"
          >
            确定
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
