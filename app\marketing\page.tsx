"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  BarChart,
  Calendar,
  Gift,
  MessageSquare,
  PieChart,
  Share,
  ShoppingBag,
  Tag,
  Ticket,
  TrendingUp,
  Users,
} from "lucide-react"

// 营销工具数据
const marketingTools = [
  {
    id: "1",
    name: "多渠道营销工具",
    description: "一站式管理微信、抖音、美团等多渠道营销活动，提高获客效率",
    icon: <Tag className="h-8 w-8 text-pink-500" />,
    route: "/marketing/multi-channel",
    isPremium: true,
    isActive: true,
  },
  {
    id: "2",
    name: "会员营销",
    description: "针对会员的精准营销活动，提高会员活跃度和复购率",
    icon: <Users className="h-8 w-8 text-indigo-500" />,
    route: "/marketing/member",
    isPremium: false,
    isActive: true,
  },
  {
    id: "3",
    name: "活动管理",
    description: "创建和管理各类营销活动，包括优惠券、折扣和特别活动",
    icon: <Calendar className="h-8 w-8 text-blue-500" />,
    route: "/marketing/events",
    isPremium: false,
    isActive: true,
  },
  {
    id: "4",
    name: "社交媒体管理",
    description: "管理社交媒体账号，安排内容发布，分析社交媒体表现",
    icon: <MessageSquare className="h-8 w-8 text-green-500" />,
    route: "/marketing/social",
    isPremium: true,
    isActive: false,
  },
  {
    id: "5",
    name: "优惠券管理",
    description: "创建、分发和跟踪优惠券使用情况，提高转化率",
    icon: <Ticket className="h-8 w-8 text-yellow-500" />,
    route: "/marketing/coupons",
    isPremium: false,
    isActive: true,
  },
  {
    id: "6",
    name: "营销分析",
    description: "全面分析各营销渠道和活动的效果，优化营销策略",
    icon: <PieChart className="h-8 w-8 text-purple-500" />,
    route: "/marketing/analytics",
    isPremium: true,
    isActive: false,
  },
]

// 增值服务数据
const premiumServices = [
  {
    id: "1",
    name: "多渠道营销工具",
    description: "一站式管理微信、抖音、美团等多渠道营销活动，提高获客效率",
    price: "499/月",
    category: "营销工具",
    status: "active",
    icon: <Tag className="h-8 w-8 text-pink-500" />,
    route: "/premium-services/multi-channel-marketing",
  },
  {
    id: "2",
    name: "社交媒体管理",
    description: "管理社交媒体账号，安排内容发布，分析社交媒体表现",
    price: "399/月",
    category: "营销工具",
    status: "inactive",
    icon: <MessageSquare className="h-8 w-8 text-green-500" />,
    route: "/premium-services/social-media",
  },
  {
    id: "3",
    name: "营销分析",
    description: "全面分析各营销渠道和活动的效果，优化营销策略",
    price: "599/月",
    category: "数据分析",
    status: "inactive",
    icon: <PieChart className="h-8 w-8 text-purple-500" />,
    route: "/premium-services/marketing-analytics",
  },
]

export default function MarketingCenterPage() {
  const [activeTab, setActiveTab] = useState("tools")

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">营销中心</h1>
          <p className="text-muted-foreground">管理所有营销活动和工具</p>
        </div>
        <Button>
          <Calendar className="mr-2 h-4 w-4" />
          创建营销活动
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">本月营销活动</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5</div>
            <p className="text-xs text-muted-foreground">较上月增加2个</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">新获客数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">128</div>
            <p className="text-xs text-muted-foreground">较上月增长15%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">营销ROI</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">320%</div>
            <p className="text-xs text-muted-foreground">较上月增长5%</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 max-w-md">
          <TabsTrigger value="tools">营销工具</TabsTrigger>
          <TabsTrigger value="activities">营销活动</TabsTrigger>
          <TabsTrigger value="premium">增值服务</TabsTrigger>
        </TabsList>

        {/* 营销工具 */}
        <TabsContent value="tools" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {marketingTools.map((tool) => (
              <Card key={tool.id} className={!tool.isActive ? "opacity-70" : ""}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      {tool.icon}
                      <CardTitle className="text-lg">{tool.name}</CardTitle>
                    </div>
                    {tool.isPremium && (
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                        增值服务
                      </Badge>
                    )}
                  </div>
                  <CardDescription className="line-clamp-2 mt-1">{tool.description}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="text-sm">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">状态</span>
                      <Badge variant={tool.isActive ? "default" : "outline"}>
                        {tool.isActive ? "已启用" : "未启用"}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-2">
                  {tool.isActive ? (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full"
                      asChild
                    >
                      <Link href={tool.route}>
                        进入工具
                      </Link>
                    </Button>
                  ) : (
                    <Button 
                      size="sm" 
                      className="w-full"
                      asChild
                    >
                      <Link href="/premium-services">
                        开通服务
                      </Link>
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 营销活动 */}
        <TabsContent value="activities" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>近期营销活动</CardTitle>
              <CardDescription>查看和管理所有营销活动</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">夏季瑜伽课程促销</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge>进行中</Badge>
                        <span className="text-xs text-muted-foreground">
                          2023-06-01 至 2023-06-30
                        </span>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      多渠道营销
                    </Badge>
                  </div>
                  <div className="mt-3 text-sm text-muted-foreground">
                    通过微信公众号、抖音和小红书推广夏季瑜伽课程特惠活动
                  </div>
                  <div className="mt-3 flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className="text-sm">
                        <span className="text-muted-foreground">预算：</span>
                        <span className="font-medium">¥2,000</span>
                      </div>
                      <div className="text-sm">
                        <span className="text-muted-foreground">已消费：</span>
                        <span className="font-medium">¥1,245</span>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">查看详情</Button>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">新店开业特惠活动</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline">已排期</Badge>
                        <span className="text-xs text-muted-foreground">
                          2023-07-15 至 2023-08-15
                        </span>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      会员营销
                    </Badge>
                  </div>
                  <div className="mt-3 text-sm text-muted-foreground">
                    新店开业期间会员专享优惠，包括课程折扣和赠品
                  </div>
                  <div className="mt-3 flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className="text-sm">
                        <span className="text-muted-foreground">预算：</span>
                        <span className="font-medium">¥5,000</span>
                      </div>
                      <div className="text-sm">
                        <span className="text-muted-foreground">已消费：</span>
                        <span className="font-medium">¥0</span>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">查看详情</Button>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full">查看所有活动</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* 增值服务 */}
        <TabsContent value="premium" className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium">营销增值服务</h2>
            <Button variant="outline" asChild>
              <Link href="/premium-services">
                <Gift className="mr-2 h-4 w-4" />
                查看所有增值服务
              </Link>
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {premiumServices.map((service) => (
              <Card key={service.id} className={service.status === "inactive" ? "opacity-70" : ""}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      {service.icon}
                      <CardTitle className="text-lg">{service.name}</CardTitle>
                    </div>
                    <Badge variant={service.status === "active" ? "default" : "outline"}>
                      {service.status === "active" ? "已开通" : "未开通"}
                    </Badge>
                  </div>
                  <CardDescription className="line-clamp-2 mt-1">{service.description}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="text-sm space-y-2">
                    <div>
                      <span className="font-medium">价格：</span>
                      <span className="text-primary">{service.price}</span>
                    </div>
                    <div>
                      <span className="font-medium">类别：</span>
                      {service.category}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-2">
                  {service.status === "active" ? (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full"
                      asChild
                    >
                      <Link href={service.route}>
                        进入服务
                      </Link>
                    </Button>
                  ) : (
                    <Button 
                      size="sm" 
                      className="w-full"
                      asChild
                    >
                      <Link href="/premium-services">
                        开通服务
                      </Link>
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
