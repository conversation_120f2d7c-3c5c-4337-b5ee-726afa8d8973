"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { AlertTriangle, CheckCircle, Edit, UserPlus, BarChart, ShoppingBag, Calendar, Tag } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface PointItemDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  itemId: number
}

export function PointItemDetailDialog({ open, onOpenChange, itemId }: PointItemDetailDialogProps) {
  const [selectedTab, setSelectedTab] = useState("details")

  // Find item in mock data
  const item = pointItems.find((item) => item.id === itemId) || pointItems[0]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>积分商品详情</DialogTitle>
          <DialogDescription>查看积分商品的详细信息和兑换记录</DialogDescription>
        </DialogHeader>

        <div className="flex flex-col space-y-4">
          <div className="flex space-x-4">
            <div className="w-[200px] h-[200px] rounded-md bg-muted relative overflow-hidden flex-shrink-0">
              <img src="/placeholder.svg?height=200&width=200" alt={item.name} className="w-full h-full object-cover" />
              <Badge className="absolute top-2 right-2" variant={getItemTypeBadgeVariant(item.type)}>
                {getItemTypeName(item.type)}
              </Badge>
            </div>

            <div className="flex-1 flex flex-col justify-between">
              <div>
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold">{item.name}</h2>
                  <Badge variant={getStatusBadgeVariant(item.status)}>{getItemStatusName(item.status)}</Badge>
                </div>

                <p className="text-muted-foreground mt-1">{item.description}</p>

                <div className="mt-4 grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      积分: <span className="font-medium">{item.points}</span>
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      库存: <span className={`font-medium ${item.stock < 10 ? "text-red-500" : ""}`}>{item.stock}</span>
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      已兑换: <span className="font-medium">{item.exchanged}</span>
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      上架时间: <span className="font-medium">2023-03-15</span>
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2 mt-4">
                <Button variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-2" />
                  编辑商品
                </Button>
                <Button variant="outline" size="sm">
                  <UserPlus className="h-4 w-4 mr-2" />
                  调整库存
                </Button>
                <Button variant="outline" size="sm">
                  <BarChart className="h-4 w-4 mr-2" />
                  兑换统计
                </Button>
                {item.status === "active" ? (
                  <Button variant="outline" size="sm">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    下架商品
                  </Button>
                ) : (
                  <Button variant="outline" size="sm" className="text-green-500 hover:text-green-600">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    上架商品
                  </Button>
                )}
              </div>
            </div>
          </div>

          <Separator />

          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="details">商品详情</TabsTrigger>
              <TabsTrigger value="exchanges">兑换记录</TabsTrigger>
              <TabsTrigger value="rules">兑换规则</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4 py-4">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">商品描述</h3>
                  <div className="text-sm text-muted-foreground">
                    <p>
                      {item.name}是我们瑜伽馆精选的优质{getItemTypeName(item.type)}商品，专为会员提供高品质的瑜伽体验。
                      {item.type === "product" && "采用环保材质制作，防滑耐用，适合各种瑜伽训练。"}
                      {item.type === "course" && "由专业教练指导，适合各个水平的瑜伽爱好者参加。"}
                      {item.type === "coupon" && "可用于抵扣会员卡费用，帮助您节省开支。"}
                    </p>
                    <p className="mt-2">
                      会员可通过日常签到、课程报名、活动参与等多种方式累积积分，使用{item.points}积分即可兑换此
                      {getItemTypeName(item.type)}。
                    </p>
                  </div>
                </div>

                {item.type === "product" && (
                  <div>
                    <h3 className="text-sm font-medium mb-2">规格参数</h3>
                    <Card>
                      <CardContent className="p-4">
                        <div className="grid grid-cols-2 gap-y-2 text-sm">
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">材质:</span>
                            <span>天然橡胶 + PU表层</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">尺寸:</span>
                            <span>183cm x 68cm</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">厚度:</span>
                            <span>5mm</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">颜色:</span>
                            <span>紫色/蓝色/绿色</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">重量:</span>
                            <span>1.5kg</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">产地:</span>
                            <span>中国</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">包装:</span>
                            <span>便携收纳袋</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">保修:</span>
                            <span>3个月</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {item.type === "course" && (
                  <div>
                    <h3 className="text-sm font-medium mb-2">课程详情</h3>
                    <Card>
                      <CardContent className="p-4">
                        <div className="grid grid-cols-2 gap-y-2 text-sm">
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">课程类型:</span>
                            <span>瑜伽体验课</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">课程时长:</span>
                            <span>60分钟</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">适合水平:</span>
                            <span>初学者</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">预约方式:</span>
                            <span>APP/小程序/前台</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">有效期:</span>
                            <span>兑换后90天</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">取消政策:</span>
                            <span>上课前24小时</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">授课教练:</span>
                            <span>全部教练</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">上课地点:</span>
                            <span>所有门店</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {item.type === "coupon" && (
                  <div>
                    <h3 className="text-sm font-medium mb-2">优惠券详情</h3>
                    <Card>
                      <CardContent className="p-4">
                        <div className="grid grid-cols-2 gap-y-2 text-sm">
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">优惠类型:</span>
                            <span>折扣券</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">优惠力度:</span>
                            <span>8折</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">使用范围:</span>
                            <span>会员月卡</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">最低消费:</span>
                            <span>无限制</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">有效期:</span>
                            <span>兑换后30天</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">使用条件:</span>
                            <span>仅限新会员</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">叠加规则:</span>
                            <span>不可与其他优惠叠加</span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-muted-foreground mr-2">使用方式:</span>
                            <span>线上/线下均可</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                <div>
                  <h3 className="text-sm font-medium mb-2">商品变更记录</h3>
                  <Card>
                    <CardContent className="p-4">
                      <ScrollArea className="h-[200px]">
                        <div className="space-y-3">
                          {changeLogData.map((log, index) => (
                            <div key={index} className="text-sm border-b border-border pb-2 last:border-0 last:pb-0">
                              <div className="flex justify-between">
                                <span className="font-medium">{log.action}</span>
                                <span className="text-muted-foreground text-xs">{log.date}</span>
                              </div>
                              <div className="text-muted-foreground mt-1">
                                <span>
                                  {log.operator}：{log.description}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="exchanges" className="space-y-4 py-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium">兑换记录</h3>
                <Button variant="outline" size="sm">
                  导出记录
                </Button>
              </div>

              <Card>
                <CardContent className="p-4">
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-4">
                      {exchangeRecordsData.map((record, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between border-b border-border pb-4 last:border-0 last:pb-0"
                        >
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src="/placeholder.svg?height=40&width=40" alt={record.memberName} />
                              <AvatarFallback>{record.memberName.charAt(0)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{record.memberName}</div>
                              <div className="text-xs text-muted-foreground">{record.phone}</div>
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-sm">兑换时间</div>
                            <div className="text-xs text-muted-foreground">{record.date}</div>
                          </div>
                          <div className="text-right">
                            <Badge variant={getExchangeStatusBadgeVariant(record.status)}>
                              {getExchangeStatusName(record.status)}
                            </Badge>
                            <div className="text-xs text-muted-foreground mt-1">{record.points} 积分</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              <div className="flex justify-between items-center text-sm text-muted-foreground">
                <div>共 {exchangeRecordsData.length} 条兑换记录</div>
                <div>今日兑换：3次 | 本周兑换：12次 | 本月兑换：35次</div>
              </div>
            </TabsContent>

            <TabsContent value="rules" className="space-y-4 py-4">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">兑换规则</h3>
                  <Card>
                    <CardContent className="p-4">
                      <div className="space-y-3 text-sm">
                        <div>
                          <div className="font-medium">积分兑换条件</div>
                          <div className="text-muted-foreground mt-1">
                            会员需要累计达到 {item.points} 积分才能兑换此商品。每位会员每月最多可兑换2次此商品。
                          </div>
                        </div>
                        <Separator />
                        <div>
                          <div className="font-medium">适用会员等级</div>
                          <div className="text-muted-foreground mt-1">
                            所有等级会员均可兑换，
                            {item.type === "product" ? "金卡会员免运费" : "金卡会员专享9折积分优惠"}。
                          </div>
                        </div>
                        <Separator />
                        <div>
                          <div className="font-medium">兑换有效期</div>
                          <div className="text-muted-foreground mt-1">
                            {item.type === "product"
                              ? "兑换后15天内未收到商品可申请退回积分"
                              : "兑换后90天内有效，逾期未使用将自动失效且不退还积分"}
                            。
                          </div>
                        </div>
                        <Separator />
                        <div>
                          <div className="font-medium">退换政策</div>
                          <div className="text-muted-foreground mt-1">
                            {item.type === "product"
                              ? "商品签收后7天内，如有质量问题可申请退换，积分将退回账户"
                              : "兑换成功后不支持退还积分，请谨慎兑换"}
                            。
                          </div>
                        </div>
                        <Separator />
                        <div>
                          <div className="font-medium">其他说明</div>
                          <div className="text-muted-foreground mt-1">
                            {item.type === "product"
                              ? "实物商品颜色随机发放，以收到实物为准"
                              : item.type === "course"
                                ? "课程需提前24小时预约，如需取消请提前联系前台"
                                : "优惠券不可与其他活动同时使用，不可转赠他人"}
                            。
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">商品限制设置</h3>
                  <Card>
                    <CardContent className="p-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-muted-foreground">每人限兑次数:</span>
                            <span className="font-medium">2次/月</span>
                          </div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-muted-foreground">单日库存限制:</span>
                            <span className="font-medium">10件/日</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-muted-foreground">库存预警值:</span>
                            <span className="font-medium">10件</span>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-muted-foreground">兑换时间设置:</span>
                            <span className="font-medium">不限时间</span>
                          </div>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-muted-foreground">新会员专享:</span>
                            <span className="font-medium">否</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-muted-foreground">会员等级权重:</span>
                            <span className="font-medium">无特殊设置</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Mock data for the point items (same as in the parent component)
const pointItems = [
  {
    id: 1,
    name: "瑜伽单次体验课",
    description: "可兑换任意一节瑜伽课程",
    points: 500,
    type: "course",
    stock: 999,
    exchanged: 128,
    status: "active",
  },
  {
    id: 2,
    name: "高级瑜伽垫",
    description: "环保材质，防滑耐用",
    points: 2000,
    type: "product",
    stock: 42,
    exchanged: 36,
    status: "active",
  },
  {
    id: 3,
    name: "会员月卡8折券",
    description: "可用于购买任意月卡",
    points: 1500,
    type: "coupon",
    stock: 100,
    exchanged: 45,
    status: "active",
  },
  {
    id: 4,
    name: "私教课1次",
    description: "可预约任意教练的私教课",
    points: 3000,
    type: "course",
    stock: 50,
    exchanged: 12,
    status: "active",
  },
  {
    id: 5,
    name: "瑜伽服套装",
    description: "舒适透气，多色可选",
    points: 2500,
    type: "product",
    stock: 8,
    exchanged: 22,
    status: "low_stock",
  },
  {
    id: 6,
    name: "会员生日礼包",
    description: "生日当月专享礼品套装",
    points: 1000,
    type: "product",
    stock: 0,
    exchanged: 30,
    status: "sold_out",
  },
]

// Mock data for exchange records
const exchangeRecordsData = [
  {
    memberName: "张小明",
    phone: "138****1234",
    date: "2023-04-22 14:30",
    status: "completed",
    points: 500,
  },
  {
    memberName: "李华",
    phone: "139****5678",
    date: "2023-04-22 09:15",
    status: "processing",
    points: 2000,
  },
  {
    memberName: "王芳",
    phone: "137****9012",
    date: "2023-04-21 16:45",
    status: "completed",
    points: 1500,
  },
  {
    memberName: "赵敏",
    phone: "136****3456",
    date: "2023-04-21 10:00",
    status: "pending",
    points: 3000,
  },
  {
    memberName: "刘伟",
    phone: "135****7890",
    date: "2023-04-20 15:30",
    status: "processing",
    points: 2500,
  },
  {
    memberName: "陈静",
    phone: "134****2345",
    date: "2023-04-20 11:20",
    status: "completed",
    points: 1000,
  },
  {
    memberName: "张小明",
    phone: "138****1234",
    date: "2023-04-19 16:00",
    status: "cancelled",
    points: 500,
  },
]

// Mock data for change log
const changeLogData = [
  {
    action: "修改库存",
    date: "2023-04-20 14:30",
    operator: "李管理员",
    description: "库存从50调整为42",
  },
  {
    action: "修改积分",
    date: "2023-04-10 11:15",
    operator: "王管理员",
    description: "积分从1800调整为2000",
  },
  {
    action: "商品上架",
    date: "2023-03-15 09:30",
    operator: "系统管理员",
    description: "商品首次上架",
  },
  {
    action: "创建商品",
    date: "2023-03-10 16:45",
    operator: "系统管理员",
    description: "创建积分商品",
  },
]

// Helper functions for badges (assuming they are defined elsewhere in the app)
function getItemTypeBadgeVariant(type: string) {
  switch (type) {
    case "course":
      return "secondary"
    case "product":
      return "default"
    case "coupon":
      return "outline"
    case "service":
      return "success"
    default:
      return "default"
  }
}

function getItemTypeName(type: string) {
  switch (type) {
    case "course":
      return "课程"
    case "product":
      return "实物"
    case "coupon":
      return "优惠券"
    case "service":
      return "服务"
    default:
      return "其他"
  }
}

function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "active":
      return "success"
    case "low_stock":
      return "warning"
    case "sold_out":
      return "destructive"
    case "inactive":
      return "outline"
    default:
      return "default"
  }
}

function getItemStatusName(status: string) {
  switch (status) {
    case "active":
      return "正常"
    case "low_stock":
      return "库存不足"
    case "sold_out":
      return "已售罄"
    case "inactive":
      return "未启用"
    default:
      return "未知"
  }
}

function getExchangeStatusBadgeVariant(status: string) {
  switch (status) {
    case "pending":
      return "outline"
    case "processing":
      return "secondary"
    case "completed":
      return "success"
    case "cancelled":
      return "destructive"
    default:
      return "default"
  }
}

function getExchangeStatusName(status: string) {
  switch (status) {
    case "pending":
      return "待处理"
    case "processing":
      return "处理中"
    case "completed":
      return "已完成"
    case "cancelled":
      return "已取消"
    default:
      return "未知"
  }
}

