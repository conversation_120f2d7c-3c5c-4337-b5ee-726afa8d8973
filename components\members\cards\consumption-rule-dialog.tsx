"use client"

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Info, HelpCircle, Calculator, Clock, CreditCard, Zap, Settings } from "lucide-react"

interface ConsumptionRuleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  ruleType?: "custom" | "average" | "actual" | "weighted"
}

export function ConsumptionRuleDialog({
  open,
  onOpenChange,
  ruleType = "custom"
}: ConsumptionRuleDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-3xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-primary" />
            会员卡消耗规则说明
          </DialogTitle>
          <DialogDescription>
            了解不同消耗规则的计算方式和适用场景
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue={ruleType} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="custom">自定义消耗</TabsTrigger>
            <TabsTrigger value="average">平均消耗</TabsTrigger>
            <TabsTrigger value="actual">实际价格</TabsTrigger>
            <TabsTrigger value="weighted">加权计算</TabsTrigger>
          </TabsList>
          
          {/* 自定义消耗规则 */}
          <TabsContent value="custom" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-primary" />
                  自定义消耗规则
                </CardTitle>
                <CardDescription>
                  为每节课程单独设置消耗的次数或金额
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  自定义消耗规则允许您为每种课程类型单独设置消耗值，最灵活但也最复杂的消耗方式。
                </p>
                
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>适用场景</AlertTitle>
                  <AlertDescription>
                    当不同课程价值差异较大，需要精确控制每种课程消耗的次数或金额时。
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-2">
                  <h4 className="font-medium">计算方式</h4>
                  <p>
                    每种课程类型单独设置消耗值，例如：
                  </p>
                  <ul className="ml-6 list-disc space-y-1">
                    <li>空中瑜伽：消耗2次</li>
                    <li>垫上瑜伽：消耗1次</li>
                    <li>私教课：消耗5次</li>
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">优点</h4>
                  <ul className="ml-6 list-disc space-y-1">
                    <li>最灵活的消耗方式，可以精确控制每种课程的消耗值</li>
                    <li>适合课程价值差异较大的场景</li>
                    <li>可以根据市场需求随时调整不同课程的消耗值</li>
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">注意事项</h4>
                  <ul className="ml-6 list-disc space-y-1">
                    <li>需要为每种课程单独设置消耗值，设置工作量较大</li>
                    <li>会员可能需要了解不同课程的消耗规则，增加沟通成本</li>
                    <li>调整消耗值可能影响会员体验，需谨慎操作</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* 平均消耗规则 */}
          <TabsContent value="average" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5 text-primary" />
                  平均消耗规则
                </CardTitle>
                <CardDescription>
                  会员卡总价值平均分配到每次课程
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  平均消耗规则将会员卡的总价值平均分配到可用次数上，每次消耗相同的价值，简单直观。
                </p>
                
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>适用场景</AlertTitle>
                  <AlertDescription>
                    当所有课程价值相近，或希望简化消耗计算时。
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-2">
                  <h4 className="font-medium">计算方式</h4>
                  <p>
                    单次消耗值 = 会员卡总价值 ÷ 总次数
                  </p>
                  <p className="text-sm text-muted-foreground">
                    例如：1000元的10次卡，每次消耗100元
                  </p>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">优点</h4>
                  <ul className="ml-6 list-disc space-y-1">
                    <li>计算简单，易于理解和实施</li>
                    <li>所有课程消耗相同，会员容易接受</li>
                    <li>无需为每种课程单独设置消耗值</li>
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">注意事项</h4>
                  <ul className="ml-6 list-disc space-y-1">
                    <li>不适合课程价值差异较大的场景</li>
                    <li>可能导致高价值课程被过度预约</li>
                    <li>无法反映不同课程的实际价值差异</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* 实际价格规则 */}
          <TabsContent value="actual" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-primary" />
                  实际价格规则
                </CardTitle>
                <CardDescription>
                  根据课程的实际价格进行消耗
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  实际价格规则根据每种课程的标准价格进行消耗，最能反映课程的实际价值。
                </p>
                
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>适用场景</AlertTitle>
                  <AlertDescription>
                    当课程价格体系完善，且希望消耗与课程实际价值对应时。
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-2">
                  <h4 className="font-medium">计算方式</h4>
                  <p>
                    消耗值 = 课程标准价格
                  </p>
                  <p className="text-sm text-muted-foreground">
                    例如：标准价格为150元的课程，消耗150元
                  </p>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">优点</h4>
                  <ul className="ml-6 list-disc space-y-1">
                    <li>最能反映课程的实际价值</li>
                    <li>消耗与课程价格直接对应，容易理解</li>
                    <li>可以合理控制高价值课程的预约量</li>
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">注意事项</h4>
                  <ul className="ml-6 list-disc space-y-1">
                    <li>需要维护完善的课程价格体系</li>
                    <li>调整课程价格会直接影响消耗值</li>
                    <li>储值卡更适合此种消耗方式</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* 加权计算规则 */}
          <TabsContent value="weighted" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-primary" />
                  加权计算规则
                </CardTitle>
                <CardDescription>
                  根据课程类型的权重进行消耗计算
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p>
                  加权计算规则为不同课程类型设置权重系数，根据权重计算消耗值，平衡了灵活性和复杂度。
                </p>
                
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>适用场景</AlertTitle>
                  <AlertDescription>
                    当课程价值有差异，但又不想为每节课单独设置消耗值时。
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-2">
                  <h4 className="font-medium">计算方式</h4>
                  <p>
                    消耗值 = 基础消耗值 × 课程权重
                  </p>
                  <p className="text-sm text-muted-foreground">
                    例如：基础消耗为1次，空中瑜伽权重为1.5，则消耗1.5次
                  </p>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">优点</h4>
                  <ul className="ml-6 list-disc space-y-1">
                    <li>可以反映不同课程的价值差异</li>
                    <li>设置工作量适中，只需为每种课程类型设置权重</li>
                    <li>兼顾了灵活性和易用性</li>
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">注意事项</h4>
                  <ul className="ml-6 list-disc space-y-1">
                    <li>权重设置需要合理，避免过大或过小</li>
                    <li>非整数消耗可能增加计算复杂度</li>
                    <li>需要向会员解释权重计算方式</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
