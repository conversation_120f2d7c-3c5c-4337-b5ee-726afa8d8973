"use client"

import { Bad<PERSON> } from "@/components/ui/badge"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar, Download, FileText, Printer, Share2 } from "lucide-react"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
} from "recharts"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { format } from "date-fns"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"

// 模拟数据 - 按平台统计
const platformData = [
  { name: "美团", value: 245, color: "#FFC300" },
  { name: "抖音", value: 178, color: "#000000" },
]

// 模拟数据 - 按时间段统计
const timeData = [
  { date: "03-22", meituan: 18, douyin: 12, total: 30 },
  { date: "03-23", meituan: 22, douyin: 15, total: 37 },
  { date: "03-24", meituan: 25, douyin: 18, total: 43 },
  { date: "03-25", meituan: 30, douyin: 20, total: 50 },
  { date: "03-26", meituan: 28, douyin: 22, total: 50 },
  { date: "03-27", meituan: 35, douyin: 25, total: 60 },
  { date: "03-28", meituan: 42, douyin: 30, total: 72 },
  { date: "03-29", meituan: 38, douyin: 28, total: 66 },
  { date: "03-30", meituan: 45, douyin: 32, total: 77 },
]

// 模拟数据 - 核销量与金额统计
const verificationData = [
  { name: "基础瑜伽", count: 85, amount: 8415, platform: "美团" },
  { name: "高级瑜伽", count: 45, amount: 5805, platform: "美团" },
  { name: "阴瑜伽", count: 35, amount: 3465, platform: "美团" },
  { name: "孕产瑜伽", count: 25, amount: 3725, platform: "美团" },
  { name: "空中瑜伽", count: 55, amount: 7095, platform: "美团" },
  { name: "基础瑜伽", count: 40, amount: 3960, platform: "抖音" },
  { name: "高级瑜伽", count: 35, amount: 4515, platform: "抖音" },
  { name: "阴瑜伽", count: 28, amount: 2772, platform: "抖音" },
  { name: "孕产瑜伽", count: 15, amount: 2235, platform: "抖音" },
  { name: "空中瑜伽", count: 60, amount: 7740, platform: "抖音" },
]

// 模拟数据 - 时段分布
const timeSlotData = [
  { time: "9:00-11:00", count: 85, amount: 8415 },
  { time: "11:00-13:00", count: 65, amount: 6435 },
  { time: "13:00-15:00", count: 45, amount: 4455 },
  { time: "15:00-17:00", count: 75, amount: 7425 },
  { time: "17:00-19:00", count: 95, amount: 9405 },
  { time: "19:00-21:00", count: 58, amount: 5742 },
]

// 模拟数据 - 用户分析
const customerData = [
  { type: "新用户", count: 145, percentage: 34.3 },
  { type: "老用户", count: 278, percentage: 65.7 },
]

// 模拟数据 - 转化率
const conversionData = [
  { date: "03-22", rate: 65 },
  { date: "03-23", rate: 68 },
  { date: "03-24", rate: 72 },
  { date: "03-25", rate: 75 },
  { date: "03-26", rate: 70 },
  { date: "03-27", rate: 78 },
  { date: "03-28", rate: 82 },
  { date: "03-29", rate: 80 },
  { date: "03-30", rate: 85 },
]

export default function VerificationStatisticsPage() {
  const [dateRange, setDateRange] = useState("7days")
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isCustomDateDialogOpen, setIsCustomDateDialogOpen] = useState(false)
  const [customDateRange, setCustomDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })

  // 合并相同课程名称的数据
  const mergedVerificationData = verificationData.reduce(
    (acc, curr) => {
      const existingItem = acc.find((item) => item.name === curr.name)
      if (existingItem) {
        existingItem.count += curr.count
        existingItem.amount += curr.amount
      } else {
        acc.push({ ...curr, platform: "合计" })
      }
      return acc
    },
    [] as typeof verificationData,
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">核销数据统计</h1>
        <div className="flex gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">今天</SelectItem>
              <SelectItem value="yesterday">昨天</SelectItem>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="custom">自定义范围</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" className="gap-2" onClick={() => setIsCustomDateDialogOpen(true)}>
            <Calendar className="h-4 w-4" />
            <span>选择日期</span>
          </Button>
          <Button variant="outline" onClick={() => setIsExportDialogOpen(true)}>
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总核销次数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">423</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-green-600">+12.5%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总核销金额</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥45,727</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-green-600">****%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均客单价</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥108.10</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-red-600">-2.1%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">核销转化率</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">78.5%</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-green-600">+5.2%</span>
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="platform" className="space-y-4">
        <TabsList>
          <TabsTrigger value="platform">按平台统计</TabsTrigger>
          <TabsTrigger value="time">按时间段统计</TabsTrigger>
          <TabsTrigger value="amount">核销量与金额统计</TabsTrigger>
          <TabsTrigger value="customer">用户分析</TabsTrigger>
          <TabsTrigger value="conversion">转化率分析</TabsTrigger>
        </TabsList>

        <TabsContent value="platform" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>平台核销占比</CardTitle>
                <CardDescription>各平台核销数量占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={platformData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={120}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {platformData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}次`, "核销次数"]} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button variant="outline" size="sm">
                  <Printer className="mr-2 h-4 w-4" />
                  打印图表
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>平台核销数据</CardTitle>
                <CardDescription>各平台核销数量与金额</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { name: "美团", count: 245, amount: 24505 },
                        { name: "抖音", count: 178, amount: 21222 },
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                      <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                      <Tooltip
                        formatter={(value, name) => [
                          name === "count" ? `${value}次` : `¥${value}`,
                          name === "count" ? "核销次数" : "核销金额",
                        ]}
                      />
                      <Legend />
                      <Bar yAxisId="left" dataKey="count" name="核销次数" fill="#8884d8" />
                      <Bar yAxisId="right" dataKey="amount" name="核销金额" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button variant="outline" size="sm">
                  <Share2 className="mr-2 h-4 w-4" />
                  分享图表
                </Button>
              </CardFooter>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>平台核销详情</CardTitle>
              <CardDescription>各平台核销详细数据</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>平台</TableHead>
                    <TableHead>核销次数</TableHead>
                    <TableHead>核销金额</TableHead>
                    <TableHead>平均客单价</TableHead>
                    <TableHead>占比</TableHead>
                    <TableHead>环比增长</TableHead>
                    <TableHead>同比增长</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">美团</TableCell>
                    <TableCell>245次</TableCell>
                    <TableCell>¥24,505</TableCell>
                    <TableCell>¥100.02</TableCell>
                    <TableCell>57.9%</TableCell>
                    <TableCell className="text-green-600">+12.5%</TableCell>
                    <TableCell className="text-green-600">+18.2%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">抖音</TableCell>
                    <TableCell>178次</TableCell>
                    <TableCell>¥21,222</TableCell>
                    <TableCell>¥119.22</TableCell>
                    <TableCell>42.1%</TableCell>
                    <TableCell className="text-green-600">****%</TableCell>
                    <TableCell className="text-green-600">+15.6%</TableCell>
                  </TableRow>
                  <TableRow className="bg-muted/50">
                    <TableCell className="font-medium">合计</TableCell>
                    <TableCell>423次</TableCell>
                    <TableCell>¥45,727</TableCell>
                    <TableCell>¥108.10</TableCell>
                    <TableCell>100%</TableCell>
                    <TableCell className="text-green-600">+10.8%</TableCell>
                    <TableCell className="text-green-600">+17.2%</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                导出数据
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="time" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>核销趋势</CardTitle>
              <CardDescription>各平台核销数量趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={timeData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`${value}次`, "核销次数"]} />
                    <Legend />
                    <Line type="monotone" dataKey="total" name="总计" stroke="#8884d8" strokeWidth={2} />
                    <Line type="monotone" dataKey="meituan" name="美团" stroke="#FFC300" strokeWidth={2} />
                    <Line type="monotone" dataKey="douyin" name="抖音" stroke="#000000" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>时段分布</CardTitle>
              <CardDescription>不同时段核销数量分布</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={timeSlotData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                    <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                    <Tooltip
                      formatter={(value, name) => [
                        name === "count" ? `${value}次` : `¥${value}`,
                        name === "count" ? "核销次数" : "核销金额",
                      ]}
                    />
                    <Legend />
                    <Bar yAxisId="left" dataKey="count" name="核销次数" fill="#8884d8" />
                    <Bar yAxisId="right" dataKey="amount" name="核销金额" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>每日核销详情</CardTitle>
              <CardDescription>每日核销数据明细</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>日期</TableHead>
                    <TableHead>美团核销</TableHead>
                    <TableHead>抖音核销</TableHead>
                    <TableHead>总核销量</TableHead>
                    <TableHead>总金额</TableHead>
                    <TableHead>平均客单价</TableHead>
                    <TableHead>环比增长</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {timeData
                    .slice()
                    .reverse()
                    .map((item, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">2025-{item.date}</TableCell>
                        <TableCell>{item.meituan}次</TableCell>
                        <TableCell>{item.douyin}次</TableCell>
                        <TableCell>{item.total}次</TableCell>
                        <TableCell>¥{(item.total * 108).toLocaleString()}</TableCell>
                        <TableCell>¥{108}</TableCell>
                        <TableCell
                          className={
                            index < timeData.length - 1
                              ? item.total > timeData[timeData.length - 2 - index].total
                                ? "text-green-600"
                                : "text-red-600"
                              : ""
                          }
                        >
                          {index < timeData.length - 1
                            ? (
                                ((item.total - timeData[timeData.length - 2 - index].total) /
                                  timeData[timeData.length - 2 - index].total) *
                                100
                              ).toFixed(1) + "%"
                            : "-"}
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="amount" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>课程核销数量</CardTitle>
                <CardDescription>各课程核销数量统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={mergedVerificationData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={100} />
                      <Tooltip formatter={(value) => [`${value}次`, "核销次数"]} />
                      <Bar dataKey="count" name="核销次数" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>课程核销金额</CardTitle>
                <CardDescription>各课程核销金额统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={mergedVerificationData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={100} />
                      <Tooltip formatter={(value) => [`¥${value}`, "核销金额"]} />
                      <Bar dataKey="amount" name="核销金额" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>课程核销详情</CardTitle>
              <CardDescription>各课程核销详细数据</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>课程名称</TableHead>
                    <TableHead>平台</TableHead>
                    <TableHead>核销次数</TableHead>
                    <TableHead>核销金额</TableHead>
                    <TableHead>平均价格</TableHead>
                    <TableHead>占比</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {verificationData.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={
                            item.platform === "美团"
                              ? "bg-yellow-50"
                              : item.platform === "抖音"
                                ? "bg-black text-white"
                                : ""
                          }
                        >
                          {item.platform}
                        </Badge>
                      </TableCell>
                      <TableCell>{item.count}次</TableCell>
                      <TableCell>¥{item.amount.toLocaleString()}</TableCell>
                      <TableCell>¥{(item.amount / item.count).toFixed(2)}</TableCell>
                      <TableCell>
                        {((item.count / verificationData.reduce((sum, i) => sum + i.count, 0)) * 100).toFixed(1)}%
                      </TableCell>
                    </TableRow>
                  ))}
                  <TableRow className="bg-muted/50">
                    <TableCell className="font-medium">合计</TableCell>
                    <TableCell>-</TableCell>
                    <TableCell>{verificationData.reduce((sum, item) => sum + item.count, 0)}次</TableCell>
                    <TableCell>
                      ¥{verificationData.reduce((sum, item) => sum + item.amount, 0).toLocaleString()}
                    </TableCell>
                    <TableCell>
                      ¥
                      {(
                        verificationData.reduce((sum, item) => sum + item.amount, 0) /
                        verificationData.reduce((sum, item) => sum + item.count, 0)
                      ).toFixed(2)}
                    </TableCell>
                    <TableCell>100%</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customer" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>用户类型分布</CardTitle>
                <CardDescription>新老用户核销占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={customerData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={120}
                        fill="#8884d8"
                        dataKey="count"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        <Cell key="cell-0" fill="#36A2EB" />
                        <Cell key="cell-1" fill="#FFCE56" />
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}人`, "用户数"]} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>用户消费能力</CardTitle>
                <CardDescription>用户消费金额分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { range: "0-100元", count: 120 },
                        { range: "100-200元", count: 180 },
                        { range: "200-300元", count: 80 },
                        { range: "300-500元", count: 30 },
                        { range: "500元以上", count: 13 },
                      ]}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="range" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value}人`, "用户数"]} />
                      <Bar dataKey="count" name="用户数" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>用户复购情况</CardTitle>
              <CardDescription>用户复购次数分布</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={[
                      { times: "1次", count: 145 },
                      { times: "2次", count: 98 },
                      { times: "3次", count: 65 },
                      { times: "4次", count: 42 },
                      { times: "5次及以上", count: 73 },
                    ]}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="times" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`${value}人`, "用户数"]} />
                    <Bar dataKey="count" name="用户数" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="conversion" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>核销转化率趋势</CardTitle>
              <CardDescription>券码核销转化率变化趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={conversionData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`${value}%`, "转化率"]} />
                    <Area
                      type="monotone"
                      dataKey="rate"
                      name="转化率"
                      stroke="#8884d8"
                      fill="#8884d8"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>平台转化率对比</CardTitle>
                <CardDescription>各平台核销转化率对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { name: "美团", rate: 82.5 },
                        { name: "抖音", rate: 73.8 },
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value}%`, "转化率"]} />
                      <Bar dataKey="rate" name="转化率" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>课程转化率对比</CardTitle>
                <CardDescription>各课程核销转化率对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[350px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { name: "基础瑜伽", rate: 85.2 },
                        { name: "高级瑜伽", rate: 78.6 },
                        { name: "阴瑜伽", rate: 82.3 },
                        { name: "孕产瑜伽", rate: 75.8 },
                        { name: "空中瑜伽", rate: 88.5 },
                      ]}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      layout="vertical"
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" width={100} />
                      <Tooltip formatter={(value) => [`${value}%`, "转化率"]} />
                      <Bar dataKey="rate" name="转化率" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 自定义日期对话框 */}
      <Dialog open={isCustomDateDialogOpen} onOpenChange={setIsCustomDateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>选择自定义日期范围</DialogTitle>
            <DialogDescription>请选择要查看的日期范围</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>日期范围</Label>
              <div className="border rounded-md p-4">
                <Calendar
                  mode="range"
                  selected={customDateRange}
                  onSelect={setCustomDateRange}
                  numberOfMonths={2}
                  className="mx-auto"
                />
              </div>
            </div>

            <div className="flex justify-between">
              <div>
                <Label>开始日期</Label>
                <div className="font-medium">
                  {customDateRange.from ? format(customDateRange.from, "yyyy-MM-dd") : "未选择"}
                </div>
              </div>
              <div>
                <Label>结束日期</Label>
                <div className="font-medium">
                  {customDateRange.to ? format(customDateRange.to, "yyyy-MM-dd") : "未选择"}
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCustomDateDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={() => {
                setDateRange("custom")
                setIsCustomDateDialogOpen(false)
              }}
            >
              应用
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导出报表对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>导出统计报表</DialogTitle>
            <DialogDescription>选择导出格式和内容</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>报表类型</Label>
              <Select defaultValue="summary">
                <SelectTrigger>
                  <SelectValue placeholder="选择报表类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">汇总报表</SelectItem>
                  <SelectItem value="platform">平台分析报表</SelectItem>
                  <SelectItem value="time">时间分析报表</SelectItem>
                  <SelectItem value="product">商品分析报表</SelectItem>
                  <SelectItem value="customer">用户分析报表</SelectItem>
                  <SelectItem value="all">完整报表（包含所有数据）</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>导出格式</Label>
              <Select defaultValue="excel">
                <SelectTrigger>
                  <SelectValue placeholder="选择导出格式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="csv">CSV (.csv)</SelectItem>
                  <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>时间范围</Label>
              <Select defaultValue={dateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">今天</SelectItem>
                  <SelectItem value="yesterday">昨天</SelectItem>
                  <SelectItem value="7days">最近7天</SelectItem>
                  <SelectItem value="30days">最近30天</SelectItem>
                  <SelectItem value="90days">最近90天</SelectItem>
                  <SelectItem value="custom">自定义范围</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label>报表选项</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="include-charts" defaultChecked />
                  <label htmlFor="include-charts" className="text-sm">
                    包含图表
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="include-raw-data" defaultChecked />
                  <label htmlFor="include-raw-data" className="text-sm">
                    包含原始数据
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="include-comparison" defaultChecked />
                  <label htmlFor="include-comparison" className="text-sm">
                    包含环比/同比分析
                  </label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="report-name">报表名称</Label>
              <Input id="report-name" defaultValue="核销数据统计报表" />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setIsExportDialogOpen(false)}>导出</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

