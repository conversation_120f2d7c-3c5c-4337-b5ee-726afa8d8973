// 初始化教练和场地数据
async function seedCoachesAndVenues() {
  console.log('开始初始化教练和场地数据...');
  
  try {
    // 1. 创建教练数据
    console.log('\n1. 创建教练数据:');
    const coaches = [
      {
        tenantId: 1,
        name: '张瑜伽',
        phone: '13800138001',
        email: '<EMAIL>',
        bio: '资深瑜伽教练，拥有10年教学经验，擅长哈他瑜伽和阴瑜伽。',
        specialties: ['哈他瑜伽', '阴瑜伽', '冥想'],
        certifications: ['RYT-200', 'RYT-500', '阴瑜伽认证'],
        experience: 10,
        hourlyRate: 200,
        status: 'active'
      },
      {
        tenantId: 1,
        name: '李流瑜',
        phone: '13800138002',
        email: '<EMAIL>',
        bio: '流瑜伽专家，注重体式的流畅性和呼吸的配合。',
        specialties: ['流瑜伽', '阿斯汤加', '力量瑜伽'],
        certifications: ['RYT-200', '阿斯汤加认证'],
        experience: 8,
        hourlyRate: 180,
        status: 'active'
      },
      {
        tenantId: 1,
        name: '王空中',
        phone: '13800138003',
        email: '<EMAIL>',
        bio: '空中瑜伽专业教练，让学员在空中体验瑜伽的乐趣。',
        specialties: ['空中瑜伽', '普拉提', '舞韵瑜伽'],
        certifications: ['空中瑜伽认证', '普拉提认证'],
        experience: 6,
        hourlyRate: 220,
        status: 'active'
      },
      {
        tenantId: 1,
        name: '陈孕产',
        phone: '13800138004',
        email: '<EMAIL>',
        bio: '孕产瑜伽专家，专注于孕期和产后恢复瑜伽。',
        specialties: ['孕产瑜伽', '产后修复', '亲子瑜伽'],
        certifications: ['孕产瑜伽认证', '产后修复认证'],
        experience: 7,
        hourlyRate: 250,
        status: 'active'
      },
      {
        tenantId: 1,
        name: '赵私教',
        phone: '13800138005',
        email: '<EMAIL>',
        bio: '私教专家，提供一对一个性化瑜伽指导。',
        specialties: ['私教课程', '理疗瑜伽', '体态矫正'],
        certifications: ['RYT-500', '理疗瑜伽认证'],
        experience: 12,
        hourlyRate: 300,
        status: 'active'
      }
    ];

    for (const coach of coaches) {
      const response = await fetch('http://localhost:3001/api/coaches', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(coach)
      });

      const result = await response.json();
      if (result.code === 200) {
        console.log(`✓ 创建教练成功: ${coach.name} (ID: ${result.data.id})`);
      } else {
        console.log(`✗ 创建教练失败: ${coach.name} - ${result.msg}`);
      }
    }

    // 2. 创建场地数据
    console.log('\n2. 创建场地数据:');
    const venues = [
      {
        tenantId: 1,
        name: '1号瑜伽室',
        location: '一楼东侧',
        capacity: 15,
        area: 50.5,
        equipment: ['瑜伽垫', '瑜伽砖', '瑜伽带', '音响设备'],
        description: '宽敞明亮的瑜伽教室，适合团体课程。',
        hourlyRate: 100,
        status: 'active'
      },
      {
        tenantId: 1,
        name: '2号瑜伽室',
        location: '一楼西侧',
        capacity: 10,
        area: 35.0,
        equipment: ['瑜伽垫', '瑜伽球', '音响设备'],
        description: '温馨的小班教室，适合精品课程。',
        hourlyRate: 80,
        status: 'active'
      },
      {
        tenantId: 1,
        name: '3号瑜伽室',
        location: '二楼东侧',
        capacity: 15,
        area: 55.0,
        equipment: ['瑜伽垫', '瑜伽砖', '瑜伽带', '冥想垫', '音响设备'],
        description: '安静的瑜伽教室，适合冥想和阴瑜伽课程。',
        hourlyRate: 120,
        status: 'active'
      },
      {
        tenantId: 1,
        name: '空中瑜伽室',
        location: '二楼西侧',
        capacity: 8,
        area: 40.0,
        equipment: ['空中瑜伽吊床', '瑜伽垫', '安全垫', '音响设备'],
        description: '专业的空中瑜伽教室，配备专业吊床设备。',
        hourlyRate: 150,
        status: 'active'
      },
      {
        tenantId: 1,
        name: '私教室',
        location: '三楼',
        capacity: 2,
        area: 25.0,
        equipment: ['瑜伽垫', '瑜伽砖', '瑜伽带', '瑜伽球', '理疗床'],
        description: '私密的一对一教学空间，配备理疗设备。',
        hourlyRate: 200,
        status: 'active'
      },
      {
        tenantId: 1,
        name: '热瑜伽室',
        location: '地下一层',
        capacity: 12,
        area: 45.0,
        equipment: ['瑜伽垫', '毛巾', '加热设备', '通风设备'],
        description: '专业的热瑜伽教室，温度可调节。',
        hourlyRate: 130,
        status: 'active'
      }
    ];

    for (const venue of venues) {
      const response = await fetch('http://localhost:3001/api/venues', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(venue)
      });

      const result = await response.json();
      if (result.code === 200) {
        console.log(`✓ 创建场地成功: ${venue.name} (ID: ${result.data.id})`);
      } else {
        console.log(`✗ 创建场地失败: ${venue.name} - ${result.msg}`);
      }
    }

    // 3. 验证创建结果
    console.log('\n3. 验证创建结果:');
    
    const coachResponse = await fetch('http://localhost:3001/api/coaches?tenantId=1&pageSize=100');
    const coachResult = await coachResponse.json();
    
    const venueResponse = await fetch('http://localhost:3001/api/venues?tenantId=1&pageSize=100');
    const venueResult = await venueResponse.json();
    
    if (coachResult.code === 200 && venueResult.code === 200) {
      console.log(`✓ 教练总数: ${coachResult.data.list.length} 个`);
      console.log(`✓ 场地总数: ${venueResult.data.list.length} 个`);
      
      console.log('\n教练列表:');
      coachResult.data.list.forEach((coach, index) => {
        console.log(`  ${index + 1}. ${coach.name} - ${coach.specialties.join(', ')} (¥${coach.hourlyRate}/小时)`);
      });
      
      console.log('\n场地列表:');
      venueResult.data.list.forEach((venue, index) => {
        console.log(`  ${index + 1}. ${venue.name} - ${venue.location} (容量: ${venue.capacity}人, ¥${venue.hourlyRate}/小时)`);
      });
    }

    console.log('\n✓ 教练和场地数据初始化完成!');
    console.log('\n现在可以在添加课程页面选择真实的教练和场地了！');
    
  } catch (error) {
    console.error('初始化失败:', error.message);
  }
}

seedCoachesAndVenues();
