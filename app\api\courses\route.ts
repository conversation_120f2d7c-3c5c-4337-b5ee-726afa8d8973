import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tenantId = searchParams.get('tenantId')
    const keyword = searchParams.get('keyword')
    const type = searchParams.get('type')
    const status = searchParams.get('status')
    const coachId = searchParams.get('coachId')
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')
    const sortBy = searchParams.get('sortBy') || 'newest'

    console.log('课程API接收参数:', { tenantId, keyword, type, status, coachId, page, pageSize, sortBy });

    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID参数',
        data: null
      })
    }

    // 构建查询条件
    const where: any = {
      tenant_id: parseInt(tenantId),
    }

    // 关键词搜索
    if (keyword) {
      where.OR = [
        { title: { contains: keyword } },
        { description: { contains: keyword } }
      ]
    }

    // 课程类型筛选
    if (type && type !== 'all') {
      where.type_id = parseInt(type)
    }

    // 状态筛选
    if (status && status !== 'all') {
      where.status = status === 'active' ? 1 : 0
    }

    // 教练筛选
    if (coachId && coachId !== 'all') {
      where.coach_id = coachId
    }

    // 排序设置
    let orderBy: any = {}
    switch (sortBy) {
      case 'newest':
        orderBy = { created_at: 'desc' }
        break
      case 'oldest':
        orderBy = { created_at: 'asc' }
        break
      case 'name':
        orderBy = { title: 'asc' }
        break
      case 'price':
        orderBy = { price: 'asc' }
        break
      default:
        orderBy = { created_at: 'desc' }
    }

    // 计算分页
    const skip = (page - 1) * pageSize

    // 查询数据
    const [courses, total] = await Promise.all([
      prisma.course.findMany({
        where,
        orderBy,
        skip,
        take: pageSize,
        include: {
          course_type: true, // 包含课程类型信息
        }
      }),
      prisma.course.count({ where })
    ])

    console.log('数据库查询结果:', courses.length, '条记录，总计:', total);

    // 格式化数据
    const formattedCourses = courses.map(course => ({
      id: course.id.toString(),
      name: course.title,
      title: course.title,
      description: course.description || '',
      price: course.price ? `¥${course.price}/次` : '免费',
      priceValue: course.price ? parseFloat(course.price.toString()) : 0,
      cover: course.cover || '',
      type: course.type_id?.toString() || '',
      typeName: course.course_type?.name || '',
      typeColor: course.course_type?.color || '#3b82f6',
      content: course.content || '',
      duration: course.duration || 90,
      level: course.level || '1',
      coach: course.coach_id || '',
      coachId: course.coach_id || '',
      time: course.time || '',
      venue: course.venue || '',
      capacity: course.capacity || 0,
      status: course.status === 1 ? 'active' : 'inactive',
      createdAt: course.created_at?.toISOString().split('T')[0] || '',
      updatedAt: course.updated_at?.toISOString().split('T')[0] || '',
    }))

    return NextResponse.json({
      code: 200,
      msg: '获取成功',
      data: {
        list: formattedCourses,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    })

  } catch (error) {
    console.error('获取课程列表失败:', error)
    return NextResponse.json({
      code: 500,
      msg: '服务器内部错误',
      data: null
    })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      tenantId,
      title,
      description,
      price,
      cover,
      type,
      content,
      duration,
      level,
      coachId,
      time,
      venue,
      capacity
    } = body

    console.log('创建课程API接收数据:', body);

    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID',
        data: null
      })
    }

    if (!title) {
      return NextResponse.json({
        code: 400,
        msg: '课程名称不能为空',
        data: null
      })
    }

    // 检查课程名称是否重复
    const existingCourse = await prisma.course.findFirst({
      where: {
        tenant_id: parseInt(tenantId),
        title: title
      }
    })

    if (existingCourse) {
      return NextResponse.json({
        code: 400,
        msg: '课程名称已存在',
        data: null
      })
    }

    // 创建课程
    const course = await prisma.course.create({
      data: {
        tenant_id: parseInt(tenantId),
        title,
        description: description || '',
        price: price ? parseFloat(price) : null,
        cover: cover || '',
        type_id: type ? parseInt(type) : null,
        content: content || '',
        duration: duration ? parseInt(duration) : 90,
        level: level || '1',
        coach_id: coachId || '',
        time: time || '',
        venue: venue || '',
        capacity: capacity ? parseInt(capacity) : 0,
        status: 1 // 默认启用
      },
      include: {
        course_type: true
      }
    })

    console.log('课程创建成功:', course);

    // 格式化返回数据
    const formattedCourse = {
      id: course.id.toString(),
      name: course.title,
      title: course.title,
      description: course.description || '',
      price: course.price ? `¥${course.price}/次` : '免费',
      priceValue: course.price ? parseFloat(course.price.toString()) : 0,
      cover: course.cover || '',
      type: course.type_id?.toString() || '',
      typeName: course.course_type?.name || '',
      typeColor: course.course_type?.color || '#3b82f6',
      content: course.content || '',
      duration: course.duration || 90,
      level: course.level || '1',
      coach: course.coach_id || '',
      coachId: course.coach_id || '',
      time: course.time || '',
      venue: course.venue || '',
      capacity: course.capacity || 0,
      status: course.status === 1 ? 'active' : 'inactive',
      createdAt: course.created_at?.toISOString().split('T')[0] || '',
      updatedAt: course.updated_at?.toISOString().split('T')[0] || '',
    }

    return NextResponse.json({
      code: 200,
      msg: '创建成功',
      data: formattedCourse
    })

  } catch (error) {
    console.error('创建课程失败:', error)
    return NextResponse.json({
      code: 500,
      msg: '服务器内部错误',
      data: null
    })
  }
}