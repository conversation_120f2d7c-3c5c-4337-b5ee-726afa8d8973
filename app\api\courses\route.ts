import { NextRequest, NextResponse } from 'next/server';
import { courseService } from '@/services/course-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const keyword = searchParams.get('keyword') || '';
    const status = searchParams.get('status') || 'all';
    const typeId = searchParams.get('typeId');
    
    console.log('课程API接收参数:', { keyword, status, typeId });
    
    // 使用课程服务获取筛选后的课程列表
    const params: any = { keyword };
    if (status !== 'all') {
      params.status = status;
    }
    if (typeId) {
      params.typeId = parseInt(typeId, 10);
    }
    
    const filteredCourses = courseService.getAll(params);
    console.log('课程API返回数据:', filteredCourses.length, '条记录');
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: {
        list: filteredCourses,
        total: filteredCourses.length,
        pageSize: filteredCourses.length,
        currentPage: 1,
        totalPages: 1
      }
    });
  } catch (error) {
    console.error('获取课程列表错误:', error);
    return NextResponse.json(
      { code: 500, msg: '获取数据失败', error: String(error) },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // 基本验证
    if (!data.name || !data.typeId) {
      return NextResponse.json(
        { code: 400, msg: '课程名称和类型不能为空' },
        { status: 400 }
      );
    }
    
    // 使用课程服务创建新课程
    const newCourse = courseService.create(data);
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: newCourse
    });
  } catch (error) {
    console.error('创建课程错误:', error);
    return NextResponse.json(
      { code: 500, msg: '创建失败', error: String(error) },
      { status: 500 }
    );
  }
} 