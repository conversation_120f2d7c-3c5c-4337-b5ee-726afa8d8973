import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 定义课程接口
interface CourseData {
  tenant_id: number
  title: string
  description?: string
  price?: number
  cover?: string
  type_id?: number
  duration?: number
  level?: string
  coach_id?: string
  time?: string
  venue?: string
  capacity?: number
}

// 定义课程类型接口
interface CourseType {
  id: number
  name: string
  color: string | null
}

// 定义数据库课程接口
interface Course {
  id: number
  title: string
  description: string | null
  price: number | null
  cover: string | null
  type_id: number | null
  duration: number | null
  level: string | null
  coach_id: string | null
  time: string | null
  venue: string | null
  capacity: number | null
  status: number | null
  created_at: Date | null
  course_type: CourseType | null
}

// 获取课程列表
export async function GET(request: NextRequest) {
  try {
    // 从URL参数中获取查询条件
    const { searchParams } = new URL(request.url)
    const tenantId = searchParams.get('tenantId')
    const typeId = searchParams.get('typeId')
    const keyword = searchParams.get('keyword')
    
    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID参数',
        data: null
      }, { status: 400 })
    }
    
    // 模拟课程数据
    const mockCourses: Course[] = [
      {
        id: 1,
        title: '基础瑜伽入门',
        description: '适合初学者的基础瑜伽课程，帮助你建立良好的瑜伽基础',
        price: 99,
        cover: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?q=80&w=1000',
        type_id: 1,
        duration: 60,
        level: '初级',
        coach_id: '1',
        time: '周一至周五 10:00-11:00',
        venue: '1号瑜伽室',
        capacity: 15,
        status: 1,
        created_at: new Date(),
        course_type: {
          id: 1,
          name: '基础瑜伽',
          color: '#4285F4'
        }
      },
      {
        id: 2,
        title: '哈他瑜伽进阶',
        description: '适合有一定基础的学员，提升力量和柔韧性',
        price: 129,
        cover: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?q=80&w=1000',
        type_id: 2,
        duration: 90,
        level: '中级',
        coach_id: '2',
        time: '周二、周四 18:30-20:00',
        venue: '2号瑜伽室',
        capacity: 12,
        status: 1,
        created_at: new Date(),
        course_type: {
          id: 2,
          name: '进阶瑜伽',
          color: '#34A853'
        }
      },
      {
        id: 3,
        title: '阴瑜伽放松',
        description: '深度放松的阴瑜伽课程，缓解压力和疲劳',
        price: 89,
        cover: 'https://images.unsplash.com/photo-1575052814086-f385e2e2ad1b?q=80&w=1000',
        type_id: 3,
        duration: 75,
        level: '全部',
        coach_id: '3',
        time: '周三、周六 20:00-21:15',
        venue: '3号瑜伽室',
        capacity: 20,
        status: 1,
        created_at: new Date(),
        course_type: {
          id: 3,
          name: '阴瑜伽',
          color: '#FBBC05'
        }
      },
      {
        id: 4,
        title: '孕产瑜伽特训',
        description: '专为孕妇设计的安全瑜伽课程',
        price: 159,
        cover: 'https://images.unsplash.com/photo-1552196563-55cd4e45efb3?q=80&w=1000',
        type_id: 4,
        duration: 60,
        level: '初级',
        coach_id: '2',
        time: '周一、周五 15:00-16:00',
        venue: '1号瑜伽室',
        capacity: 8,
        status: 1,
        created_at: new Date(),
        course_type: {
          id: 4,
          name: '孕产瑜伽',
          color: '#EA4335'
        }
      },
      {
        id: 5,
        title: '空中瑜伽体验',
        description: '体验空中瑜伽的乐趣，增强核心力量',
        price: 199,
        cover: 'https://images.unsplash.com/photo-1593164842264-854604db2260?q=80&w=1000',
        type_id: 5,
        duration: 90,
        level: '中级',
        coach_id: '1',
        time: '周六、周日 14:00-15:30',
        venue: '空中瑜伽室',
        capacity: 10,
        status: 1,
        created_at: new Date(),
        course_type: {
          id: 5,
          name: '空中瑜伽',
          color: '#FF6D91'
        }
      },
      {
        id: 6,
        title: '私人瑜伽定制',
        description: '一对一私教课程，根据个人需求定制',
        price: 299,
        cover: 'https://images.unsplash.com/photo-1599447292180-45fd84092ef4?q=80&w=1000',
        type_id: 6,
        duration: 60,
        level: '全部',
        coach_id: '3',
        time: '预约制',
        venue: '私教室',
        capacity: 1,
        status: 1,
        created_at: new Date(),
        course_type: {
          id: 6,
          name: '私教课',
          color: '#9C27B0'
        }
      }
    ];
    
    // 根据关键词筛选
    let filteredCourses = [...mockCourses];
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      filteredCourses = filteredCourses.filter(
        course => 
          course.title.toLowerCase().includes(lowerKeyword) || 
          (course.description && course.description.toLowerCase().includes(lowerKeyword))
      );
    }
    
    // 按课程类型筛选
    if (typeId) {
      filteredCourses = filteredCourses.filter(course => course.type_id === parseInt(typeId));
    }
    
    // 格式化返回数据
    const formattedCourses = filteredCourses.map((course: Course) => ({
      id: course.id,
      title: course.title,
      description: course.description || '',
      price: course.price || 0,
      cover: course.cover || '',
      type: course.course_type ? {
        id: course.course_type.id,
        name: course.course_type.name,
        color: course.course_type.color
      } : null,
      duration: course.duration || 60,
      level: course.level || '初级',
      coach_id: course.coach_id || '',
      time: course.time || '',
      venue: course.venue || '',
      capacity: course.capacity || 10,
      status: course.status === 1 ? 'active' : 'inactive',
      created_at: course.created_at
    }));
    
    return NextResponse.json({
      code: 200,
      msg: '获取课程列表成功',
      data: {
        list: formattedCourses,
        pagination: {
          total: formattedCourses.length,
          page: 1,
          pageSize: 10,
          totalPages: 1
        }
      }
    })
  } catch (error) {
    console.error('获取课程列表失败:', error)
    return NextResponse.json({
      code: 500,
      msg: '获取课程列表失败',
      data: null
    }, { status: 500 })
  }
}

// 添加新课程
export async function POST(request: NextRequest) {
  try {
    const data: CourseData = await request.json()
    
    // 验证必填字段
    if (!data.tenant_id || !data.title) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 })
    }
    
    // 模拟创建新课程
    const newCourse = {
      id: 7, // 模拟自增ID
      title: data.title,
      description: data.description || null,
      price: data.price || null,
      cover: data.cover || null,
      type_id: data.type_id || null,
      duration: data.duration || 60,
      level: data.level || '初级',
      coach_id: data.coach_id || null,
      time: data.time || null,
      venue: data.venue || null,
      capacity: data.capacity || 10,
      status: 1,
      created_at: new Date()
    };
    
    return NextResponse.json({
      code: 200,
      msg: '添加课程成功',
      data: newCourse
    })
  } catch (error) {
    console.error('添加课程失败:', error)
    return NextResponse.json({
      code: 500,
      msg: '添加课程失败',
      data: null
    }, { status: 500 })
  }
}