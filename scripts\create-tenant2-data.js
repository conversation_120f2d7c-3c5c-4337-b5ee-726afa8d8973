// 为租户2创建基础数据
const mysql = require('mysql2/promise');

async function createTenant2Data() {
  console.log('开始为租户2创建基础数据...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    const tenantId = 2;

    // 1. 创建课程类型数据
    console.log('\n1. 创建课程类型数据:');
    const courseTypes = [
      {
        tenant_id: tenantId,
        name: '哈他瑜伽',
        description: '传统的瑜伽练习，注重体式的正确性和呼吸的配合',
        color: '#4285F4',
        display_order: 1,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '流瑜伽',
        description: '动态的瑜伽练习，体式之间流畅连接',
        color: '#34A853',
        display_order: 2,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '阴瑜伽',
        description: '静态的瑜伽练习，长时间保持体式',
        color: '#FBBC05',
        display_order: 3,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '空中瑜伽',
        description: '使用吊床进行的瑜伽练习，增加趣味性',
        color: '#EA4335',
        display_order: 4,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '孕产瑜伽',
        description: '专为孕妇和产后恢复设计的瑜伽课程',
        color: '#9C27B0',
        display_order: 5,
        status: 1
      }
    ];

    for (const type of courseTypes) {
      const [result] = await connection.execute(
        `INSERT INTO coursetype (tenant_id, name, description, color, display_order, status, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [type.tenant_id, type.name, type.description, type.color, type.display_order, type.status]
      );
      console.log(`✓ 创建课程类型成功: ${type.name} (ID: ${result.insertId})`);
    }

    // 2. 创建教练数据
    console.log('\n2. 创建教练数据:');
    const coaches = [
      {
        tenant_id: tenantId,
        name: '刘瑜伽',
        phone: '13800138011',
        email: '<EMAIL>',
        bio: '资深瑜伽教练，拥有8年教学经验，擅长流瑜伽和力量瑜伽。',
        specialties: JSON.stringify(['流瑜伽', '力量瑜伽', '普拉提']),
        certifications: JSON.stringify(['RYT-200', '流瑜伽认证']),
        experience: 8,
        hourly_rate: 180.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '周阴瑜',
        phone: '13800138012',
        email: '<EMAIL>',
        bio: '阴瑜伽专家，注重内在平静和身心放松。',
        specialties: JSON.stringify(['阴瑜伽', '冥想', '理疗瑜伽']),
        certifications: JSON.stringify(['阴瑜伽认证', '理疗瑜伽认证']),
        experience: 6,
        hourly_rate: 200.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '吴空中',
        phone: '13800138013',
        email: '<EMAIL>',
        bio: '空中瑜伽专业教练，让学员体验空中的自由与美妙。',
        specialties: JSON.stringify(['空中瑜伽', '舞韵瑜伽']),
        certifications: JSON.stringify(['空中瑜伽认证']),
        experience: 5,
        hourly_rate: 220.00,
        status: 1
      }
    ];

    for (const coach of coaches) {
      const [result] = await connection.execute(
        `INSERT INTO coach (tenant_id, name, phone, email, bio, specialties, certifications, experience, hourly_rate, status, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [coach.tenant_id, coach.name, coach.phone, coach.email, coach.bio, coach.specialties, coach.certifications, coach.experience, coach.hourly_rate, coach.status]
      );
      console.log(`✓ 创建教练成功: ${coach.name} (ID: ${result.insertId})`);
    }

    // 3. 创建场地数据
    console.log('\n3. 创建场地数据:');
    const venues = [
      {
        tenant_id: tenantId,
        name: 'A号瑜伽室',
        location: '一楼南侧',
        capacity: 12,
        area: 45.0,
        equipment: JSON.stringify(['瑜伽垫', '瑜伽砖', '瑜伽带', '音响设备']),
        description: '明亮舒适的瑜伽教室，适合小班课程。',
        hourly_rate: 90.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: 'B号瑜伽室',
        location: '一楼北侧',
        capacity: 8,
        area: 30.0,
        equipment: JSON.stringify(['瑜伽垫', '瑜伽球', '音响设备']),
        description: '精致的小班教室，适合私教和精品课程。',
        hourly_rate: 70.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '空中瑜伽室',
        location: '二楼',
        capacity: 6,
        area: 35.0,
        equipment: JSON.stringify(['空中瑜伽吊床', '瑜伽垫', '安全垫', '音响设备']),
        description: '专业的空中瑜伽教室，配备高品质吊床设备。',
        hourly_rate: 140.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '冥想室',
        location: '三楼',
        capacity: 10,
        area: 25.0,
        equipment: JSON.stringify(['冥想垫', '香薰设备', '音响设备']),
        description: '安静的冥想空间，适合阴瑜伽和冥想课程。',
        hourly_rate: 80.00,
        status: 1
      }
    ];

    for (const venue of venues) {
      const [result] = await connection.execute(
        `INSERT INTO venue (tenant_id, name, location, capacity, area, equipment, description, hourly_rate, status, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [venue.tenant_id, venue.name, venue.location, venue.capacity, venue.area, venue.equipment, venue.description, venue.hourly_rate, venue.status]
      );
      console.log(`✓ 创建场地成功: ${venue.name} (ID: ${result.insertId})`);
    }

    // 4. 验证创建结果
    console.log('\n4. 验证创建结果:');
    
    const [courseTypesCount] = await connection.execute(`SELECT COUNT(*) as count FROM coursetype WHERE tenant_id = ${tenantId}`);
    const [coachesCount] = await connection.execute(`SELECT COUNT(*) as count FROM coach WHERE tenant_id = ${tenantId}`);
    const [venuesCount] = await connection.execute(`SELECT COUNT(*) as count FROM venue WHERE tenant_id = ${tenantId}`);
    
    console.log(`✓ 租户${tenantId}数据统计:`);
    console.log(`  - 课程类型: ${courseTypesCount[0].count} 个`);
    console.log(`  - 教练: ${coachesCount[0].count} 个`);
    console.log(`  - 场地: ${venuesCount[0].count} 个`);

    await connection.end();
    console.log('\n✓ 租户2基础数据创建完成!');
    
  } catch (error) {
    console.error('创建失败:', error.message);
  }
}

createTenant2Data();
