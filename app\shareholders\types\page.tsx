"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Search, LayoutGrid, LayoutList, MoreHorizontal, Pencil, Trash2, Eye } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { ShareholderTypeDialog } from "@/components/shareholders/types/shareholder-type-dialog"
import { ShareholderTypeDetailDialog } from "@/components/shareholders/types/shareholder-type-detail-dialog"
import { ShareholderBreadcrumb } from "@/components/shareholders/shareholder-breadcrumb"
import { ShareholderNav } from "@/components/shareholders/shareholder-nav"

// 模拟数据
const shareholderTypes = [
  {
    id: "1",
    name: "消费型股东",
    description: "通常是瑜伽馆的老客户，对瑜伽馆的产品和服务认可度高",
    color: "#4CAF50",
    dividendRules: "根据推荐客户消费金额的5%提成",
    dividendRatio: "5%",
    dividendAmount: "",
    productBenefits: "每月赠送1次高级课程体验",
    serviceBenefits: "专属更衣室使用权",
    discountBenefits: "会员卡8折优惠",
    couponBenefits: "每季度赠送100元代金券",
    otherBenefits: "每年2张请客卡",
    memberCount: 28,
    createdAt: "2023-05-15",
  },
  {
    id: "2",
    name: "投资型股东",
    description: "有创业想法但资金或经验不足的人，向瑜伽馆投入一定资金",
    color: "#2196F3",
    dividendRules: "根据投资金额的10%年化收益",
    dividendRatio: "10%",
    dividendAmount: "",
    productBenefits: "免费使用所有课程",
    serviceBenefits: "专属私教1对1指导",
    discountBenefits: "所有产品7折优惠",
    couponBenefits: "每月赠送200元代金券",
    otherBenefits: "优先参与新课程体验",
    memberCount: 12,
    createdAt: "2023-05-20",
  },
  {
    id: "3",
    name: "资源型股东",
    description: "具有广泛人脉和影响力的人，如行业达人、意见领袖等",
    color: "#FF9800",
    dividendRules: "根据引流客户数量，每位新客户提成50元",
    dividendRatio: "",
    dividendAmount: "50元/人",
    productBenefits: "免费使用高级课程",
    serviceBenefits: "专属客户经理服务",
    discountBenefits: "会员卡8.5折优惠",
    couponBenefits: "每月赠送150元代金券",
    otherBenefits: "每季度1张请客卡",
    memberCount: 15,
    createdAt: "2023-06-01",
  },
  {
    id: "4",
    name: "员工型股东",
    description: "瑜伽馆的员工成为股东后，能享受更多福利和权益",
    color: "#9C27B0",
    dividendRules: "基本工资外额外提成2%的门店月收入",
    dividendRatio: "2%",
    dividendAmount: "",
    productBenefits: "免费使用所有课程和设施",
    serviceBenefits: "专业培训和进修机会",
    discountBenefits: "亲友会员卡7折优惠",
    couponBenefits: "每月赠送100元代金券",
    otherBenefits: "年终额外奖金",
    memberCount: 8,
    createdAt: "2023-06-10",
  },
  {
    id: "5",
    name: "联盟型股东",
    description: "与瑜伽馆业务相关的异业商家，如美容院、高档服装店等",
    color: "#F44336",
    dividendRules: "互换客户资源，对方消费金额的3%提成",
    dividendRatio: "3%",
    dividendAmount: "",
    productBenefits: "每月5次免费体验课程",
    serviceBenefits: "联合营销活动支持",
    discountBenefits: "会员卡8折优惠",
    couponBenefits: "每季度赠送200元代金券",
    otherBenefits: "资源共享平台使用权",
    memberCount: 6,
    createdAt: "2023-07-01",
  },
]

export default function ShareholderTypesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [viewMode, setViewMode] = useState<"list" | "grid">("grid")
  const [openAddEditDialog, setOpenAddEditDialog] = useState(false)
  const [openDetailDialog, setOpenDetailDialog] = useState(false)
  const [selectedType, setSelectedType] = useState<any>(null)

  // 过滤股东类型
  const filteredTypes = shareholderTypes.filter(
    (type) =>
      type.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      type.description.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // 打开添加对话框
  const handleAddType = () => {
    setSelectedType(null)
    setOpenAddEditDialog(true)
  }

  // 打开编辑对话框
  const handleEditType = (type: any) => {
    setSelectedType(type)
    setOpenAddEditDialog(true)
  }

  // 打开详情对话框
  const handleViewType = (type: any) => {
    setSelectedType(type)
    setOpenDetailDialog(true)
  }

  return (
    <div className="space-y-6">
      <ShareholderBreadcrumb />
      <ShareholderNav />

      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">股东类型管理</h1>
        <Button onClick={handleAddType}>
          <Plus className="mr-2 h-4 w-4" />
          添加类型
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center gap-2 max-w-md">
          <Input
            placeholder="搜索类型名称或描述..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <div className="flex items-center border rounded-md">
          <Button
            variant="ghost"
            size="icon"
            className={`rounded-none ${viewMode === "list" ? "bg-muted" : ""}`}
            onClick={() => setViewMode("list")}
          >
            <LayoutList className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className={`rounded-none ${viewMode === "grid" ? "bg-muted" : ""}`}
            onClick={() => setViewMode("grid")}
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTypes.map((type) => (
            <Card key={type.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 rounded-full" style={{ backgroundColor: type.color }} />
                    <CardTitle className="text-lg">{type.name}</CardTitle>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>操作</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleViewType(type)}>
                        <Eye className="mr-2 h-4 w-4" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditType(type)}>
                        <Pencil className="mr-2 h-4 w-4" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <CardDescription className="line-clamp-2">{type.description}</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-sm space-y-2">
                  <div>
                    <span className="font-medium">分红规则：</span>
                    {type.dividendRules}
                  </div>
                  <div className="grid grid-cols-2 gap-x-2">
                    <div>
                      <span className="font-medium">股东数量：</span>
                      {type.memberCount}人
                    </div>
                    {type.dividendRatio && (
                      <div>
                        <span className="font-medium">分红比例：</span>
                        {type.dividendRatio}
                      </div>
                    )}
                    {type.dividendAmount && (
                      <div>
                        <span className="font-medium">分红金额：</span>
                        {type.dividendAmount}
                      </div>
                    )}
                  </div>
                  <div className="border-t pt-2">
                    <span className="font-medium">主要权益：</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {type.discountBenefits && (
                        <Badge variant="outline" className="bg-blue-50">
                          {type.discountBenefits.length > 10
                            ? type.discountBenefits.substring(0, 10) + "..."
                            : type.discountBenefits}
                        </Badge>
                      )}
                      {type.couponBenefits && (
                        <Badge variant="outline" className="bg-green-50">
                          {type.couponBenefits.length > 10
                            ? type.couponBenefits.substring(0, 10) + "..."
                            : type.couponBenefits}
                        </Badge>
                      )}
                      {type.productBenefits && (
                        <Badge variant="outline" className="bg-purple-50">
                          {type.productBenefits.length > 10
                            ? type.productBenefits.substring(0, 10) + "..."
                            : type.productBenefits}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-2">
                <Button variant="outline" size="sm" className="w-full" onClick={() => handleViewType(type)}>
                  查看详情
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="border rounded-md">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3 font-medium">类型名称</th>
                <th className="text-left p-3 font-medium">描述</th>
                <th className="text-left p-3 font-medium">分红规则</th>
                <th className="text-left p-3 font-medium">股东数量</th>
                <th className="text-left p-3 font-medium">主要权益</th>
                <th className="text-left p-3 font-medium">创建时间</th>
                <th className="text-right p-3 font-medium">操作</th>
              </tr>
            </thead>
            <tbody>
              {filteredTypes.map((type) => (
                <tr key={type.id} className="border-b">
                  <td className="p-3">
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full" style={{ backgroundColor: type.color }} />
                      {type.name}
                    </div>
                  </td>
                  <td className="p-3 max-w-xs">
                    <div className="truncate">{type.description}</div>
                  </td>
                  <td className="p-3">{type.dividendRules}</td>
                  <td className="p-3">{type.memberCount}人</td>
                  <td className="p-3">
                    <div className="flex flex-wrap gap-1">
                      {type.discountBenefits && (
                        <Badge variant="outline" className="bg-blue-50">
                          {type.discountBenefits.length > 10
                            ? type.discountBenefits.substring(0, 10) + "..."
                            : type.discountBenefits}
                        </Badge>
                      )}
                      {type.couponBenefits && (
                        <Badge variant="outline" className="bg-green-50">
                          {type.couponBenefits.length > 10
                            ? type.couponBenefits.substring(0, 10) + "..."
                            : type.couponBenefits}
                        </Badge>
                      )}
                    </div>
                  </td>
                  <td className="p-3">{type.createdAt}</td>
                  <td className="p-3 text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleViewType(type)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditType(type)}>
                          <Pencil className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <ShareholderTypeDialog
        open={openAddEditDialog}
        onOpenChange={setOpenAddEditDialog}
        type={selectedType}
      />
      <ShareholderTypeDetailDialog
        open={openDetailDialog}
        onOpenChange={setOpenDetailDialog}
        type={selectedType}
      />
    </div>
  )
}
