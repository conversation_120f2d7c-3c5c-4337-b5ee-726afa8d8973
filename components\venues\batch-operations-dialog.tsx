"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Tabs, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { useState } from "react"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface BatchOperationsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  type: "venue" | "equipment" | "booking"
  selectedIds?: string[]
}

export function BatchOperationsDialog({
  open,
  onOpenChange,
  type = "venue",
  selectedIds = [],
}: BatchOperationsDialogProps) {
  const [date, setDate] = useState<Date | undefined>(new Date())

  const renderVenueBatchOperations = () => (
    <Tabs defaultValue="status">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="status">状态更新</TabsTrigger>
        <TabsTrigger value="maintenance">维护安排</TabsTrigger>
        <TabsTrigger value="export">数据导出</TabsTrigger>
      </TabsList>

      <TabsContent value="status" className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="status">更新场地状态</Label>
          <Select>
            <SelectTrigger id="status">
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="available">可用</SelectItem>
              <SelectItem value="maintenance">维护中</SelectItem>
              <SelectItem value="closed">关闭</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="reason">状态变更原因</Label>
          <Textarea id="reason" placeholder="输入状态变更原因" rows={3} />
        </div>

        <div className="space-y-2">
          <Label>状态有效期</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="start-date" className="text-xs">
                开始日期
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="start-date"
                    variant="outline"
                    className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "yyyy-MM-dd") : "选择日期"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <Label htmlFor="end-date" className="text-xs">
                结束日期
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button id="end-date" variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    选择日期
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" initialFocus />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox id="notify" />
          <label
            htmlFor="notify"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            通知相关人员
          </label>
        </div>
      </TabsContent>

      <TabsContent value="maintenance" className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="maintenance-type">维护类型</Label>
          <Select>
            <SelectTrigger id="maintenance-type">
              <SelectValue placeholder="选择维护类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="regular">常规维护</SelectItem>
              <SelectItem value="deep">深度清洁</SelectItem>
              <SelectItem value="repair">设施维修</SelectItem>
              <SelectItem value="renovation">场地翻新</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>维护日期</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, "yyyy-MM-dd") : "选择日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label htmlFor="maintenance-staff">维护人员</Label>
          <Select>
            <SelectTrigger id="maintenance-staff">
              <SelectValue placeholder="选择维护人员" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="staff1">张维修</SelectItem>
              <SelectItem value="staff2">李维修</SelectItem>
              <SelectItem value="staff3">王维修</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="maintenance-notes">维护说明</Label>
          <Textarea id="maintenance-notes" placeholder="输入维护说明" rows={3} />
        </div>
      </TabsContent>

      <TabsContent value="export" className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="export-format">导出格式</Label>
          <Select defaultValue="excel">
            <SelectTrigger id="export-format">
              <SelectValue placeholder="选择导出格式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="excel">Excel</SelectItem>
              <SelectItem value="csv">CSV</SelectItem>
              <SelectItem value="pdf">PDF</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>导出内容</Label>
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center space-x-2">
              <Checkbox id="export-basic" defaultChecked />
              <label htmlFor="export-basic" className="text-sm font-medium leading-none">
                基本信息
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="export-usage" defaultChecked />
              <label htmlFor="export-usage" className="text-sm font-medium leading-none">
                使用情况
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="export-maintenance" defaultChecked />
              <label htmlFor="export-maintenance" className="text-sm font-medium leading-none">
                维护记录
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="export-equipment" defaultChecked />
              <label htmlFor="export-equipment" className="text-sm font-medium leading-none">
                设备信息
              </label>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="export-date-range">日期范围</Label>
          <Select defaultValue="all">
            <SelectTrigger id="export-date-range">
              <SelectValue placeholder="选择日期范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部数据</SelectItem>
              <SelectItem value="last-month">最近一个月</SelectItem>
              <SelectItem value="last-quarter">最近一个季度</SelectItem>
              <SelectItem value="last-year">最近一年</SelectItem>
              <SelectItem value="custom">自定义范围</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </TabsContent>
    </Tabs>
  )

  const renderEquipmentBatchOperations = () => (
    <Tabs defaultValue="status">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="status">状态更新</TabsTrigger>
        <TabsTrigger value="maintenance">维护安排</TabsTrigger>
        <TabsTrigger value="transfer">设备调拨</TabsTrigger>
      </TabsList>

      <TabsContent value="status" className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="equipment-status">更新设备状态</Label>
          <Select>
            <SelectTrigger id="equipment-status">
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="normal">正常</SelectItem>
              <SelectItem value="maintenance">维护中</SelectItem>
              <SelectItem value="damaged">损坏</SelectItem>
              <SelectItem value="scrapped">报废</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="status-reason">状态变更原因</Label>
          <Textarea id="status-reason" placeholder="输入状态变更原因" rows={3} />
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox id="notify-equipment" />
          <label
            htmlFor="notify-equipment"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            通知相关人员
          </label>
        </div>
      </TabsContent>

      <TabsContent value="maintenance" className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="equipment-maintenance-type">维护类型</Label>
          <Select>
            <SelectTrigger id="equipment-maintenance-type">
              <SelectValue placeholder="选择维护类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="regular">常规维护</SelectItem>
              <SelectItem value="repair">维修</SelectItem>
              <SelectItem value="replace">更换部件</SelectItem>
              <SelectItem value="inspection">安全检查</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>维护日期</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, "yyyy-MM-dd") : "选择日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label htmlFor="equipment-maintenance-staff">维护人员</Label>
          <Select>
            <SelectTrigger id="equipment-maintenance-staff">
              <SelectValue placeholder="选择维护人员" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="staff1">张维修</SelectItem>
              <SelectItem value="staff2">李维修</SelectItem>
              <SelectItem value="staff3">王维修</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="equipment-maintenance-notes">维护说明</Label>
          <Textarea id="equipment-maintenance-notes" placeholder="输入维护说明" rows={3} />
        </div>
      </TabsContent>

      <TabsContent value="transfer" className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="target-venue">目标场地</Label>
          <Select>
            <SelectTrigger id="target-venue">
              <SelectValue placeholder="选择目标场地" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1号瑜伽室</SelectItem>
              <SelectItem value="2">2号瑜伽室</SelectItem>
              <SelectItem value="3">3号瑜伽室</SelectItem>
              <SelectItem value="4">4号瑜伽室</SelectItem>
              <SelectItem value="5">私教室</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="transfer-quantity">调拨数量</Label>
          <Input id="transfer-quantity" type="number" min="1" placeholder="输入调拨数量" />
        </div>

        <div className="space-y-2">
          <Label>调拨日期</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, "yyyy-MM-dd") : "选择日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <Label htmlFor="transfer-reason">调拨原因</Label>
          <Textarea id="transfer-reason" placeholder="输入调拨原因" rows={3} />
        </div>
      </TabsContent>
    </Tabs>
  )

  const renderBookingBatchOperations = () => (
    <Tabs defaultValue="status">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="status">状态更新</TabsTrigger>
        <TabsTrigger value="reschedule">重新安排</TabsTrigger>
        <TabsTrigger value="notify">通知管理</TabsTrigger>
      </TabsList>

      <TabsContent value="status" className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="booking-status">更新预订状态</Label>
          <Select>
            <SelectTrigger id="booking-status">
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="confirmed">已确认</SelectItem>
              <SelectItem value="pending">待确认</SelectItem>
              <SelectItem value="cancelled">已取消</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="booking-status-reason">状态变更原因</Label>
          <Textarea id="booking-status-reason" placeholder="输入状态变更原因" rows={3} />
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox id="notify-booking-status" defaultChecked />
          <label
            htmlFor="notify-booking-status"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            通知相关人员
          </label>
        </div>
      </TabsContent>

      <TabsContent value="reschedule" className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="target-venue-booking">目标场地</Label>
          <Select>
            <SelectTrigger id="target-venue-booking">
              <SelectValue placeholder="选择目标场地" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1号瑜伽室</SelectItem>
              <SelectItem value="2">2号瑜伽室</SelectItem>
              <SelectItem value="3">3号瑜伽室</SelectItem>
              <SelectItem value="4">4号瑜伽室</SelectItem>
              <SelectItem value="5">私教室</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>新预订日期</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date ? format(date, "yyyy-MM-dd") : "选择日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
            </PopoverContent>
          </Popover>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="start-time">开始时间</Label>
            <Select>
              <SelectTrigger id="start-time">
                <SelectValue placeholder="选择时间" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 17 }).map((_, i) => {
                  const hour = i + 6
                  return (
                    <SelectItem key={i} value={`${hour}:00`}>
                      {`${hour}:00`}
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="end-time">结束时间</Label>
            <Select>
              <SelectTrigger id="end-time">
                <SelectValue placeholder="选择时间" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 17 }).map((_, i) => {
                  const hour = i + 6
                  return (
                    <SelectItem key={i} value={`${hour}:00`}>
                      {`${hour}:00`}
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="reschedule-reason">变更原因</Label>
          <Textarea id="reschedule-reason" placeholder="输入变更原因" rows={3} />
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox id="notify-reschedule" defaultChecked />
          <label
            htmlFor="notify-reschedule"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            通知相关人员
          </label>
        </div>
      </TabsContent>

      <TabsContent value="notify" className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="notification-type">通知类型</Label>
          <Select defaultValue="reminder">
            <SelectTrigger id="notification-type">
              <SelectValue placeholder="选择通知类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="reminder">课程提醒</SelectItem>
              <SelectItem value="change">变更通知</SelectItem>
              <SelectItem value="cancel">取消通知</SelectItem>
              <SelectItem value="custom">自定义通知</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>通知对象</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox id="notify-members" defaultChecked />
              <label htmlFor="notify-members" className="text-sm font-medium leading-none">
                会员
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="notify-coaches" defaultChecked />
              <label htmlFor="notify-coaches" className="text-sm font-medium leading-none">
                教练
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox id="notify-staff" />
              <label htmlFor="notify-staff" className="text-sm font-medium leading-none">
                工作人员
              </label>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="notification-method">通知方式</Label>
          <Select defaultValue="all">
            <SelectTrigger id="notification-method">
              <SelectValue placeholder="选择通知方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部</SelectItem>
              <SelectItem value="sms">短信</SelectItem>
              <SelectItem value="wechat">微信</SelectItem>
              <SelectItem value="app">APP推送</SelectItem>
              <SelectItem value="email">邮件</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="notification-content">通知内容</Label>
          <Textarea
            id="notification-content"
            placeholder="输入通知内容"
            rows={3}
            defaultValue="尊敬的会员，您预订的课程有新的变更，请查看详情。"
          />
        </div>
      </TabsContent>
    </Tabs>
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>批量操作</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="p-3 border rounded-md">
            <p className="font-medium">已选择 {selectedIds.length} 项</p>
            <p className="text-sm text-muted-foreground">
              {type === "venue" ? "场地ID: " : type === "equipment" ? "设备ID: " : "预订ID: "}
              {selectedIds.join(", ")}
            </p>
          </div>

          {type === "venue" && renderVenueBatchOperations()}
          {type === "equipment" && renderEquipmentBatchOperations()}
          {type === "booking" && renderBookingBatchOperations()}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={() => onOpenChange(false)}>确认操作</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

