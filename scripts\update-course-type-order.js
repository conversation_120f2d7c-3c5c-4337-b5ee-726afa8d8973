// 更新课程类型的排序字段
async function updateCourseTypeOrder() {
  console.log('开始更新课程类型排序...');
  
  try {
    // 获取所有课程类型
    const response = await fetch('http://localhost:3005/api/course-types?tenantId=1');
    const result = await response.json();
    
    if (result.code !== 200) {
      throw new Error(result.msg || '获取课程类型失败');
    }
    
    const courseTypes = result.data.list;
    console.log(`找到 ${courseTypes.length} 个课程类型`);
    
    // 为每个课程类型设置排序值
    for (let i = 0; i < courseTypes.length; i++) {
      const type = courseTypes[i];
      const displayOrder = (i + 1) * 10; // 10, 20, 30, ...
      
      console.log(`更新课程类型 "${type.name}" 的排序为 ${displayOrder}`);
      
      const updateResponse = await fetch(`http://localhost:3005/api/course-types/${type.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          displayOrder: displayOrder
        })
      });
      
      const updateResult = await updateResponse.json();
      
      if (updateResult.code === 200) {
        console.log(`✓ 更新成功: ${type.name} -> 排序 ${displayOrder}`);
      } else {
        console.log(`✗ 更新失败: ${type.name} - ${updateResult.msg}`);
      }
    }
    
    console.log('课程类型排序更新完成!');
  } catch (error) {
    console.error('更新课程类型排序失败:', error.message);
  }
}

updateCourseTypeOrder();
