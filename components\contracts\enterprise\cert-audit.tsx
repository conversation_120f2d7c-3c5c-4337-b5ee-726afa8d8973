"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  ClipboardCheck, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Bell, 
  Calendar, 
  ShieldCheck 
} from "lucide-react"
import { usePremiumServices } from "@/hooks/use-premium-services"

// 审核表单验证模式
const auditFormSchema = z.object({
  result: z.enum(["APPROVED", "REJECTED"], {
    required_error: "请选择审核结果",
  }),
  comment: z.string().min(5, {
    message: "请输入至少5个字符的审核意见",
  }),
})

// 提醒设置表单验证模式
const reminderFormSchema = z.object({
  enabled: z.boolean(),
  frequency: z.enum(["MONTHLY", "QUARTERLY", "YEARLY"], {
    required_error: "请选择提醒频率",
  }),
  reminderDays: z.string().transform((val) => parseInt(val, 10)).refine((val) => !isNaN(val) && val > 0, {
    message: "请输入有效的提前天数",
  }),
  recipients: z.string().min(1, {
    message: "请输入至少一个接收人",
  }),
})

// 模拟待审核数据
const mockPendingAudits = [
  {
    id: "audit-1",
    companyName: "静心瑜伽馆",
    creditCode: "91310000XXXXXXXX3B",
    updateType: "INITIAL",
    updateContent: "初始认证",
    submitTime: "2023-06-15 14:30:25",
    submitter: "张三",
    status: "PENDING",
  },
  {
    id: "audit-2",
    companyName: "阳光健身中心",
    creditCode: "91320000XXXXXXXX2A",
    updateType: "UPDATE",
    updateContent: "变更法定代表人",
    submitTime: "2023-06-14 09:20:15",
    submitter: "李四",
    status: "PENDING",
  },
]

// 模拟审核历史
const mockAuditHistory = [
  {
    id: "history-1",
    companyName: "和平瑜伽馆",
    creditCode: "91330000XXXXXXXX5C",
    updateType: "INITIAL",
    updateContent: "初始认证",
    submitTime: "2023-05-10 14:30:25",
    submitter: "王五",
    status: "APPROVED",
    auditTime: "2023-05-10 15:45:10",
    auditor: "管理员",
    auditResult: "APPROVED",
    auditComment: "资料齐全，认证通过",
  },
  {
    id: "history-2",
    companyName: "健康生活馆",
    creditCode: "91340000XXXXXXXX7D",
    updateType: "UPDATE",
    updateContent: "变更企业名称",
    submitTime: "2023-05-08 10:20:15",
    submitter: "赵六",
    status: "REJECTED",
    auditTime: "2023-05-08 11:30:05",
    auditor: "管理员",
    auditResult: "REJECTED",
    auditComment: "企业名称变更材料不完整，请补充提交工商变更登记证明",
  },
]

// 模拟提醒设置
const mockReminderSettings = {
  enabled: true,
  frequency: "YEARLY",
  reminderDays: 30,
  recipients: "<EMAIL>, <EMAIL>",
  nextReminderDate: "2024-05-10",
}

interface CertAuditProps {
  className?: string
}

export function CertAudit({ className }: CertAuditProps) {
  const { config, loading } = usePremiumServices()
  const [pendingAudits, setPendingAudits] = useState(mockPendingAudits)
  const [auditHistory, setAuditHistory] = useState(mockAuditHistory)
  const [reminderSettings, setReminderSettings] = useState(mockReminderSettings)
  const [selectedAudit, setSelectedAudit] = useState<any>(null)
  const [showAuditDialog, setShowAuditDialog] = useState(false)
  const [showReminderDialog, setShowReminderDialog] = useState(false)
  
  // 检查是否启用了认证信息审核功能
  const isFeatureEnabled = !loading && config?.eContract?.enabled && config?.eContract?.features?.certAudit
  
  // 初始化审核表单
  const auditForm = useForm<z.infer<typeof auditFormSchema>>({
    resolver: zodResolver(auditFormSchema),
    defaultValues: {
      result: "APPROVED",
      comment: "",
    },
  })
  
  // 初始化提醒设置表单
  const reminderForm = useForm<z.infer<typeof reminderFormSchema>>({
    resolver: zodResolver(reminderFormSchema),
    defaultValues: {
      enabled: reminderSettings.enabled,
      frequency: reminderSettings.frequency,
      reminderDays: reminderSettings.reminderDays.toString(),
      recipients: reminderSettings.recipients,
    },
  })
  
  // 处理查看审核详情
  const handleViewAudit = (audit: any) => {
    setSelectedAudit(audit)
    setShowAuditDialog(true)
  }
  
  // 处理提交审核
  const handleSubmitAudit = (values: z.infer<typeof auditFormSchema>) => {
    if (!selectedAudit) return
    
    // 在实际应用中，这里应该调用API提交审核结果
    console.log("提交审核结果:", { auditId: selectedAudit.id, ...values })
    
    // 更新审核状态
    const updatedPendingAudits = pendingAudits.filter(audit => audit.id !== selectedAudit.id)
    setPendingAudits(updatedPendingAudits)
    
    // 添加到审核历史
    const newHistoryItem = {
      ...selectedAudit,
      status: values.result,
      auditTime: new Date().toLocaleString(),
      auditor: "当前用户",
      auditResult: values.result,
      auditComment: values.comment,
    }
    setAuditHistory([newHistoryItem, ...auditHistory])
    
    // 关闭对话框
    setShowAuditDialog(false)
    setSelectedAudit(null)
    
    // 显示成功提示
    toast({
      title: "审核完成",
      description: `审核结果: ${values.result === "APPROVED" ? "通过" : "驳回"}`,
    })
  }
  
  // 处理保存提醒设置
  const handleSaveReminder = (values: z.infer<typeof reminderFormSchema>) => {
    // 在实际应用中，这里应该调用API保存提醒设置
    console.log("保存提醒设置:", values)
    
    // 更新提醒设置
    setReminderSettings({
      enabled: values.enabled,
      frequency: values.frequency,
      reminderDays: values.reminderDays,
      recipients: values.recipients,
      nextReminderDate: reminderSettings.nextReminderDate, // 保持不变
    })
    
    // 关闭对话框
    setShowReminderDialog(false)
    
    // 显示成功提示
    toast({
      title: "设置已保存",
      description: "认证信息审核提醒设置已更新",
    })
  }
  
  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge className="bg-yellow-500">待审核</Badge>
      case "APPROVED":
        return <Badge className="bg-green-500">已通过</Badge>
      case "REJECTED":
        return <Badge className="bg-red-500">已驳回</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }
  
  // 获取频率名称
  const getFrequencyName = (frequency: string) => {
    switch (frequency) {
      case "MONTHLY": return "每月"
      case "QUARTERLY": return "每季度"
      case "YEARLY": return "每年"
      default: return frequency
    }
  }
  
  // 如果功能未启用，显示提示信息
  if (!isFeatureEnabled) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>认证信息审核与提醒</CardTitle>
          <CardDescription>
            审核企业认证信息变更申请并设置定期审核提醒
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10 space-y-4">
          <ShieldCheck className="h-12 w-12 text-muted-foreground/50" />
          <h3 className="text-lg font-semibold">功能未开通</h3>
          <p className="text-sm text-muted-foreground text-center max-w-md">
            认证信息审核与提醒是增值服务的高级功能，需要开通后才能使用。
            开通后，您可以审核企业认证信息变更申请，并设置定期审核提醒。
          </p>
          <Button onClick={() => window.location.href = "/premium-services"}>
            了解详情
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <ClipboardCheck className="mr-2 h-5 w-5" />
            认证信息审核与提醒
          </div>
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => setShowReminderDialog(true)}
          >
            <Bell className="mr-2 h-4 w-4" />
            提醒设置
          </Button>
        </CardTitle>
        <CardDescription>
          审核企业认证信息变更申请并设置定期审核提醒
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">待审核申请</h3>
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>企业名称</TableHead>
                    <TableHead>变更内容</TableHead>
                    <TableHead>提交时间</TableHead>
                    <TableHead>提交人</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingAudits.length > 0 ? (
                    pendingAudits.map((audit) => (
                      <TableRow key={audit.id}>
                        <TableCell className="font-medium">
                          <div>
                            {audit.companyName}
                            <div className="text-xs text-muted-foreground">
                              {audit.creditCode}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{audit.updateContent}</TableCell>
                        <TableCell>{audit.submitTime}</TableCell>
                        <TableCell>{audit.submitter}</TableCell>
                        <TableCell>{getStatusBadge(audit.status)}</TableCell>
                        <TableCell className="text-right">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleViewAudit(audit)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            审核
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                        暂无待审核申请
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-4">审核历史</h3>
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>企业名称</TableHead>
                    <TableHead>变更内容</TableHead>
                    <TableHead>提交时间</TableHead>
                    <TableHead>审核时间</TableHead>
                    <TableHead>审核结果</TableHead>
                    <TableHead>审核人</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditHistory.length > 0 ? (
                    auditHistory.map((history) => (
                      <TableRow key={history.id}>
                        <TableCell className="font-medium">
                          <div>
                            {history.companyName}
                            <div className="text-xs text-muted-foreground">
                              {history.creditCode}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{history.updateContent}</TableCell>
                        <TableCell>{history.submitTime}</TableCell>
                        <TableCell>{history.auditTime}</TableCell>
                        <TableCell>{getStatusBadge(history.status)}</TableCell>
                        <TableCell>{history.auditor}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                        暂无审核历史
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
          
          <div className="bg-muted/30 p-4 rounded-md">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                认证信息审核提醒
              </h3>
              <Badge variant={reminderSettings.enabled ? "default" : "outline"}>
                {reminderSettings.enabled ? "已启用" : "已禁用"}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground mb-2">
              {reminderSettings.enabled 
                ? `系统将${getFrequencyName(reminderSettings.frequency)}在认证到期前 ${reminderSettings.reminderDays} 天发送提醒`
                : "未启用认证信息审核提醒"}
            </p>
            <div className="flex justify-between items-center">
              <div className="text-sm">
                <span className="text-muted-foreground">下次提醒时间: </span>
                {reminderSettings.nextReminderDate}
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowReminderDialog(true)}
              >
                修改设置
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
      
      {/* 审核对话框 */}
      <Dialog open={showAuditDialog} onOpenChange={setShowAuditDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>审核认证信息</DialogTitle>
            <DialogDescription>
              审核企业认证信息变更申请
            </DialogDescription>
          </DialogHeader>
          
          {selectedAudit && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>企业名称</Label>
                  <div className="p-2 border rounded-md bg-muted/50">
                    {selectedAudit.companyName}
                  </div>
                </div>
                <div>
                  <Label>统一社会信用代码</Label>
                  <div className="p-2 border rounded-md bg-muted/50">
                    {selectedAudit.creditCode}
                  </div>
                </div>
              </div>
              
              <div>
                <Label>变更内容</Label>
                <div className="p-2 border rounded-md bg-muted/50">
                  {selectedAudit.updateContent}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>提交时间</Label>
                  <div className="p-2 border rounded-md bg-muted/50">
                    {selectedAudit.submitTime}
                  </div>
                </div>
                <div>
                  <Label>提交人</Label>
                  <div className="p-2 border rounded-md bg-muted/50">
                    {selectedAudit.submitter}
                  </div>
                </div>
              </div>
              
              <Form {...auditForm}>
                <form onSubmit={auditForm.handleSubmit(handleSubmitAudit)} className="space-y-4">
                  <FormField
                    control={auditForm.control}
                    name="result"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>审核结果</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex space-x-4"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="APPROVED" id="approved" />
                              <Label htmlFor="approved" className="flex items-center">
                                <CheckCircle className="mr-1 h-4 w-4 text-green-600" />
                                通过
                              </Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="REJECTED" id="rejected" />
                              <Label htmlFor="rejected" className="flex items-center">
                                <XCircle className="mr-1 h-4 w-4 text-red-600" />
                                驳回
                              </Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={auditForm.control}
                    name="comment"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>审核意见</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="请输入审核意见"
                            className="resize-none"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          请详细说明审核意见，特别是驳回时需要明确指出问题所在
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowAuditDialog(false)}
                    >
                      取消
                    </Button>
                    <Button type="submit">
                      提交审核
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* 提醒设置对话框 */}
      <Dialog open={showReminderDialog} onOpenChange={setShowReminderDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>认证信息审核提醒设置</DialogTitle>
            <DialogDescription>
              设置认证信息审核的定期提醒
            </DialogDescription>
          </DialogHeader>
          
          <Form {...reminderForm}>
            <form onSubmit={reminderForm.handleSubmit(handleSaveReminder)} className="space-y-4">
              <FormField
                control={reminderForm.control}
                name="enabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="h-4 w-4 mt-1"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>启用认证信息审核提醒</FormLabel>
                      <FormDescription>
                        系统将在认证到期前发送提醒邮件
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
              
              <FormField
                control={reminderForm.control}
                name="frequency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>提醒频率</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="MONTHLY" id="monthly" />
                          <Label htmlFor="monthly">每月</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="QUARTERLY" id="quarterly" />
                          <Label htmlFor="quarterly">每季度</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="YEARLY" id="yearly" />
                          <Label htmlFor="yearly">每年</Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={reminderForm.control}
                name="reminderDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>提前提醒天数</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="输入提前提醒天数" 
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      在认证到期前多少天发送提醒
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={reminderForm.control}
                name="recipients"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>接收人</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="输入接收人邮箱，多个邮箱用逗号分隔" 
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      提醒邮件将发送给这些邮箱地址
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowReminderDialog(false)}
                >
                  取消
                </Button>
                <Button type="submit">
                  保存设置
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
