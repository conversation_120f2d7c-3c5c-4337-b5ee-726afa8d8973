// 激活租户账户的脚本
const mysql = require('mysql2/promise');

async function activateTenant() {
  try {
    console.log('🔧 开始激活租户账户...');
    
    // 从环境变量中获取数据库连接信息
    const dbUrl = process.env.DATABASE_URL || 'mysql://root:123456@localhost:3306/yoga';
    
    // 解析数据库URL
    const matches = dbUrl.match(/mysql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
    if (!matches) {
      console.error('无效的数据库URL格式');
      return;
    }
    
    const [, user, password, host, port, database] = matches;
    
    console.log(`连接到数据库: ${host}:${port}/${database}`);
    
    // 创建连接
    const connection = await mysql.createConnection({
      host,
      user,
      password,
      database,
      port: parseInt(port)
    });
    
    console.log('数据库连接成功!');
    
    // 激活所有租户
    const [tenantResult] = await connection.execute(
      'UPDATE Tenant SET status = 1 WHERE status = 0'
    );
    console.log(`✅ 激活了 ${tenantResult.affectedRows} 个租户`);
    
    // 激活所有门店
    const [storeResult] = await connection.execute(
      'UPDATE Store SET status = 1 WHERE status = 0'
    );
    console.log(`✅ 激活了 ${storeResult.affectedRows} 个门店`);
    
    // 查询当前租户状态
    const [tenants] = await connection.execute(
      'SELECT id, tenant_name, contact_person, phone, status FROM Tenant'
    );
    
    console.log('\n📋 当前租户列表:');
    tenants.forEach(tenant => {
      const statusText = tenant.status === 1 ? '✅ 已激活' : '⏳ 待审核';
      console.log(`ID: ${tenant.id}, 名称: ${tenant.tenant_name}, 联系人: ${tenant.contact_person}, 手机: ${tenant.phone}, 状态: ${statusText}`);
    });
    
    // 查询员工信息
    const [employees] = await connection.execute(
      'SELECT id, tenant_id, real_name, phone, is_super_admin, status FROM Employee'
    );
    
    console.log('\n👥 员工列表:');
    employees.forEach(emp => {
      const roleText = emp.is_super_admin ? '超级管理员' : '普通员工';
      const statusText = emp.status === 1 ? '✅ 已激活' : '❌ 未激活';
      console.log(`ID: ${emp.id}, 租户ID: ${emp.tenant_id}, 姓名: ${emp.real_name}, 手机: ${emp.phone}, 角色: ${roleText}, 状态: ${statusText}`);
    });
    
    // 关闭连接
    await connection.end();
    console.log('\n🎉 租户激活完成!');
    
  } catch (error) {
    console.error('❌ 激活租户失败:', error);
  }
}

activateTenant();
