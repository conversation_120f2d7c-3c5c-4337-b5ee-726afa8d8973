import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// 获取路径参数
interface Params {
  params: {
    id: string;
  };
}

// GET /api/coaches/[id] - 获取教练详情
export async function GET(request: NextRequest, { params }: Params) {
  try {
    const { id: paramId } = await params;
    const id = parseInt(paramId);
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的教练ID',
        data: null
      }, { status: 400 });
    }
    
    // 查询教练详情，包含课程数量统计
    const coach = await prisma.coach.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            courses: true
          }
        },
        courses: {
          select: {
            id: true,
            title: true,
            status: true,
            created_at: true
          },
          orderBy: { created_at: 'desc' },
          take: 10 // 最近10个课程
        }
      }
    });

    // 检查是否存在
    if (!coach) {
      return NextResponse.json({
        code: 404,
        msg: '未找到教练',
        data: null
      }, { status: 404 });
    }

    // 格式化返回数据
    const formattedCoach = {
      id: coach.id,
      name: coach.name,
      phone: coach.phone,
      email: coach.email,
      avatar: coach.avatar,
      bio: coach.bio,
      specialties: coach.specialties ? JSON.parse(coach.specialties) : [],
      certifications: coach.certifications ? JSON.parse(coach.certifications) : [],
      experience: coach.experience,
      hourlyRate: coach.hourly_rate,
      status: coach.status === 1 ? 'active' : 'inactive',
      courseCount: coach._count.courses,
      recentCourses: coach.courses.map(course => ({
        id: course.id,
        title: course.title,
        status: course.status === 1 ? 'active' : 'inactive',
        createdAt: course.created_at?.toISOString().split('T')[0]
      })),
      createdAt: coach.created_at?.toISOString().split('T')[0],
      updatedAt: coach.updated_at?.toISOString().split('T')[0]
    };

    return NextResponse.json({
      code: 200,
      msg: '获取教练详情成功',
      data: formattedCoach
    });

  } catch (error) {
    console.error('获取教练详情失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取教练详情失败',
      data: null
    }, { status: 500 });
  }
}

// PUT /api/coaches/[id] - 更新教练
export async function PUT(request: NextRequest, { params }: Params) {
  try {
    const { id: paramId } = await params;
    const id = parseInt(paramId);
    const body = await request.json();
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的教练ID',
        data: null
      }, { status: 400 });
    }

    const {
      name,
      phone,
      email,
      avatar,
      bio,
      specialties,
      certifications,
      experience,
      hourlyRate,
      status
    } = body;

    console.log('更新教练API:', id, body);

    // 检查教练是否存在
    const existingCoach = await prisma.coach.findUnique({
      where: { id }
    });

    if (!existingCoach) {
      return NextResponse.json({
        code: 404,
        msg: '未找到教练',
        data: null
      }, { status: 404 });
    }

    // 如果更新手机号，检查是否重复
    if (phone && phone !== existingCoach.phone) {
      const duplicateCoach = await prisma.coach.findFirst({
        where: {
          tenant_id: existingCoach.tenant_id,
          phone: phone,
          id: { not: id }
        }
      });

      if (duplicateCoach) {
        return NextResponse.json({
          code: 400,
          msg: '手机号已存在',
          data: null
        }, { status: 400 });
      }
    }

    // 更新教练
    const updatedCoach = await prisma.coach.update({
      where: { id },
      data: {
        name,
        phone,
        email,
        avatar,
        bio,
        specialties: specialties ? JSON.stringify(specialties) : null,
        certifications: certifications ? JSON.stringify(certifications) : null,
        experience: experience ? parseInt(experience) : null,
        hourly_rate: hourlyRate ? parseFloat(hourlyRate) : null,
        status: status === 'active' ? 1 : 0
      }
    });

    console.log('教练更新成功:', updatedCoach.id);

    return NextResponse.json({
      code: 200,
      msg: '更新教练成功',
      data: {
        id: updatedCoach.id,
        name: updatedCoach.name,
        phone: updatedCoach.phone,
        email: updatedCoach.email,
        avatar: updatedCoach.avatar,
        bio: updatedCoach.bio,
        specialties: updatedCoach.specialties ? JSON.parse(updatedCoach.specialties) : [],
        certifications: updatedCoach.certifications ? JSON.parse(updatedCoach.certifications) : [],
        experience: updatedCoach.experience,
        hourlyRate: updatedCoach.hourly_rate,
        status: updatedCoach.status === 1 ? 'active' : 'inactive',
        updatedAt: updatedCoach.updated_at?.toISOString().split('T')[0]
      }
    });

  } catch (error) {
    console.error('更新教练失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '更新教练失败',
      data: null
    }, { status: 500 });
  }
}

// DELETE /api/coaches/[id] - 删除教练
export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    const { id: paramId } = await params;
    const id = parseInt(paramId);
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的教练ID',
        data: null
      }, { status: 400 });
    }

    console.log('删除教练API:', id);

    // 检查教练是否存在
    const coach = await prisma.coach.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      }
    });

    if (!coach) {
      return NextResponse.json({
        code: 404,
        msg: '未找到教练',
        data: null
      }, { status: 404 });
    }

    // 检查是否有关联课程
    if (coach._count.courses > 0) {
      return NextResponse.json({
        code: 400,
        msg: `该教练下有 ${coach._count.courses} 个关联课程，无法删除`,
        data: null
      }, { status: 400 });
    }

    // 删除教练
    await prisma.coach.delete({
      where: { id }
    });

    console.log('教练删除成功:', id);

    return NextResponse.json({
      code: 200,
      msg: '删除教练成功',
      data: null
    });

  } catch (error) {
    console.error('删除教练失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '删除教练失败',
      data: null
    }, { status: 500 });
  }
}
