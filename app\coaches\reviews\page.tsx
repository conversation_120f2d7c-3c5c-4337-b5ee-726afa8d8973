"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  StarIcon,
  Filter,
  Download,
  BarChart2,
  Search,
  Calendar,
  CheckCircle,
  XCircle,
  EyeOff,
  Eye,
  MessageSquare,
  RefreshCw,
  AlertTriangle,
  ThumbsUp,
  ThumbsDown,
  MoreHorizontal,
  ChevronDown
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useState } from "react"
import { ReviewDetailDialog } from "@/components/coaches/reviews/review-detail-dialog"
import { ReviewStatsDialog } from "@/components/coaches/reviews/review-stats-dialog"
import { AdvancedFilterDialog } from "@/components/coaches/reviews/advanced-filter-dialog"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

// 模拟数据 - 教练列表
const coaches = [
  { id: 1, name: "张教练", avatar: "/placeholder.svg?height=32&width=32", rating: 4.8 },
  { id: 2, name: "李教练", avatar: "/placeholder.svg?height=32&width=32", rating: 4.9 },
  { id: 3, name: "王教练", avatar: "/placeholder.svg?height=32&width=32", rating: 4.7 },
  { id: 4, name: "赵教练", avatar: "/placeholder.svg?height=32&width=32", rating: 4.5 },
  { id: 5, name: "刘教练", avatar: "/placeholder.svg?height=32&width=32", rating: 4.6 },
]

// 模拟数据 - 课程类型
const courseTypes = [
  { id: 1, name: "基础瑜伽" },
  { id: 2, name: "高级瑜伽" },
  { id: 3, name: "阴瑜伽" },
  { id: 4, name: "孕产瑜伽" },
  { id: 5, name: "空中瑜伽" },
  { id: 6, name: "理疗瑜伽" },
  { id: 7, name: "热瑜伽" },
]

// 模拟数据 - 评价列表
const allReviews = [
  {
    id: 101,
    coachId: 1,
    coach: "张教练",
    coachAvatar: "/placeholder.svg?height=32&width=32",
    memberId: "M001",
    member: {
      name: "李四",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "金卡会员",
      joinDate: "2024-01-15",
    },
    rating: 5,
    comment: "张教练非常专业，课程安排合理，动作讲解清晰，很有耐心。每次上课都能学到新东西，感觉自己进步很快。",
    date: "2025-03-25",
    course: "基础瑜伽入门",
    courseType: "基础瑜伽",
    status: "published",
    hasReply: true,
    reply: "感谢您的评价和支持！我们会继续努力提供优质的课程和服务。期待您的下次光临！",
    replyDate: "2025-03-26",
    keywords: ["专业", "耐心", "讲解清晰"],
    sentiment: "positive",
  },
  {
    id: 102,
    coachId: 1,
    coach: "张教练",
    coachAvatar: "/placeholder.svg?height=32&width=32",
    memberId: "M002",
    member: {
      name: "王五",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "银卡会员",
      joinDate: "2024-02-20",
    },
    rating: 4,
    comment: "教练很专业，但课程节奏有点快，希望能更适合初学者。总体来说还是很满意的，会继续参加。",
    date: "2025-03-20",
    course: "基础瑜伽入门",
    courseType: "基础瑜伽",
    status: "published",
    hasReply: false,
    reply: "",
    replyDate: "",
    keywords: ["专业", "节奏快", "初学者"],
    sentiment: "neutral",
  },
  {
    id: 201,
    coachId: 2,
    coach: "李教练",
    coachAvatar: "/placeholder.svg?height=32&width=32",
    memberId: "M003",
    member: {
      name: "张三",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "白金会员",
      joinDate: "2023-11-05",
    },
    rating: 5,
    comment: "李教练的高级瑜伽课非常棒，动作示范到位，指导细致。课程难度适中，很有挑战性但不会太难。",
    date: "2025-03-26",
    course: "高级瑜伽进阶",
    courseType: "高级瑜伽",
    status: "published",
    hasReply: true,
    reply: "谢谢您的肯定！我们会根据学员的反馈不断调整课程内容，让每位学员都能得到适合自己的锻炼。",
    replyDate: "2025-03-27",
    keywords: ["示范到位", "指导细致", "难度适中"],
    sentiment: "positive",
  },
  {
    id: 202,
    coachId: 2,
    coach: "李教练",
    coachAvatar: "/placeholder.svg?height=32&width=32",
    memberId: "M004",
    member: {
      name: "赵六",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "标准会员",
      joinDate: "2024-03-10",
    },
    rating: 5,
    comment: "课程内容丰富，教练很有耐心，每个动作都会详细讲解。场地环境也很好，整体体验非常棒。",
    date: "2025-03-22",
    course: "高级瑜伽进阶",
    courseType: "高级瑜伽",
    status: "pending",
    hasReply: false,
    reply: "",
    replyDate: "",
    keywords: ["内容丰富", "耐心", "环境好"],
    sentiment: "positive",
  },
  {
    id: 301,
    coachId: 3,
    coach: "王教练",
    coachAvatar: "/placeholder.svg?height=32&width=32",
    memberId: "M005",
    member: {
      name: "钱七",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "银卡会员",
      joinDate: "2024-01-30",
    },
    rating: 5,
    comment: "王教练的阴瑜伽课非常放松，氛围很好，音乐选择恰到好处。每次上完课都感觉身心舒畅。",
    date: "2025-03-24",
    course: "阴瑜伽放松",
    courseType: "阴瑜伽",
    status: "published",
    hasReply: true,
    reply: "非常感谢您的评价！阴瑜伽确实是放松身心的好方式，期待在下次课程中再见到您。",
    replyDate: "2025-03-25",
    keywords: ["放松", "氛围好", "音乐好"],
    sentiment: "positive",
  },
  {
    id: 302,
    coachId: 3,
    coach: "王教练",
    coachAvatar: "/placeholder.svg?height=32&width=32",
    memberId: "M006",
    member: {
      name: "孙八",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "标准会员",
      joinDate: "2024-02-15",
    },
    rating: 4,
    comment: "整体不错，但课程时间有点短，希望能延长一些。教练的指导很专业，动作讲解很清晰。",
    date: "2025-03-18",
    course: "阴瑜伽放松",
    courseType: "阴瑜伽",
    status: "hidden",
    hasReply: false,
    reply: "",
    replyDate: "",
    keywords: ["时间短", "专业", "讲解清晰"],
    sentiment: "neutral",
  },
  {
    id: 401,
    coachId: 4,
    coach: "赵教练",
    coachAvatar: "/placeholder.svg?height=32&width=32",
    memberId: "M007",
    member: {
      name: "周九",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "金卡会员",
      joinDate: "2023-12-20",
    },
    rating: 3,
    comment: "课程内容一般，感觉和网上视频差不多。教练态度还可以，但专业度有待提高。场地环境不错。",
    date: "2025-03-15",
    course: "孕产瑜伽基础",
    courseType: "孕产瑜伽",
    status: "published",
    hasReply: true,
    reply: "感谢您的反馈。我们会认真考虑您的建议，提升课程质量和教练专业度。欢迎您再次光临并给我们改进的机会。",
    replyDate: "2025-03-16",
    keywords: ["一般", "态度好", "专业度低"],
    sentiment: "negative",
  },
  {
    id: 501,
    coachId: 5,
    coach: "刘教练",
    coachAvatar: "/placeholder.svg?height=32&width=32",
    memberId: "M008",
    member: {
      name: "吴十",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "白金会员",
      joinDate: "2023-10-10",
    },
    rating: 5,
    comment: "刘教练的空中瑜伽课非常精彩，动作编排流畅，难度适中。教练很注重安全，每个动作都有详细指导。",
    date: "2025-03-10",
    course: "空中瑜伽入门",
    courseType: "空中瑜伽",
    status: "published",
    hasReply: true,
    reply: "谢谢您的评价！安全确实是空中瑜伽最重要的方面，我们会继续保持高标准的教学质量。",
    replyDate: "2025-03-11",
    keywords: ["精彩", "安全", "指导详细"],
    sentiment: "positive",
  },
  {
    id: 502,
    coachId: 5,
    coach: "刘教练",
    coachAvatar: "/placeholder.svg?height=32&width=32",
    memberId: "M009",
    member: {
      name: "郑十一",
      avatar: "/placeholder.svg?height=32&width=32",
      level: "标准会员",
      joinDate: "2024-02-28",
    },
    rating: 2,
    comment: "课程太难了，完全跟不上节奏。教练没有考虑到初学者的情况，感觉很挫败。希望能有更适合初学者的课程。",
    date: "2025-03-05",
    course: "空中瑜伽入门",
    courseType: "空中瑜伽",
    status: "pending",
    hasReply: false,
    reply: "",
    replyDate: "",
    keywords: ["太难", "跟不上", "初学者"],
    sentiment: "negative",
  },
]

// 统计数据
const reviewStats = {
  total: 9,
  published: 6,
  pending: 2,
  hidden: 1,
  averageRating: 4.2,
  ratingDistribution: {
    5: 5,
    4: 2,
    3: 1,
    2: 1,
    1: 0,
  },
  replyRate: "67%",
  topKeywords: ["专业", "耐心", "讲解清晰", "放松", "氛围好"],
  sentimentAnalysis: {
    positive: 6,
    neutral: 2,
    negative: 1,
  }
}

export default function CoachReviewsPage() {
  // 状态管理
  const [selectedReview, setSelectedReview] = useState<any>(null)
  const [showReviewDetail, setShowReviewDetail] = useState(false)
  const [showStats, setShowStats] = useState(false)
  const [showFilter, setShowFilter] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [searchKeyword, setSearchKeyword] = useState("")
  const [selectedCoach, setSelectedCoach] = useState("all")
  const [selectedCourseType, setSelectedCourseType] = useState("all")
  const [selectedRating, setSelectedRating] = useState("all")
  const [selectedReviews, setSelectedReviews] = useState<number[]>([])
  const [sortBy, setSortBy] = useState("date-desc")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // 处理评价点击
  const handleReviewClick = (review: any) => {
    setSelectedReview(review)
    setShowReviewDetail(true)
  }

  // 处理评价选择
  const toggleSelectReview = (reviewId: number) => {
    if (selectedReviews.includes(reviewId)) {
      setSelectedReviews(selectedReviews.filter(id => id !== reviewId))
    } else {
      setSelectedReviews([...selectedReviews, reviewId])
    }
  }

  // 处理全选/取消全选
  const toggleSelectAll = (reviews: any[]) => {
    if (selectedReviews.length === reviews.length) {
      setSelectedReviews([])
    } else {
      setSelectedReviews(reviews.map(review => review.id))
    }
  }

  // 批量操作处理
  const handleBatchAction = (action: string) => {
    if (selectedReviews.length === 0) {
      toast({
        title: "请先选择评价",
        description: "您需要先选择至少一条评价才能执行此操作",
        variant: "destructive",
      })
      return
    }

    let actionText = ""
    let successText = ""

    switch (action) {
      case "approve":
        actionText = "批准"
        successText = "已批准"
        break
      case "reject":
        actionText = "拒绝"
        successText = "已拒绝"
        break
      case "hide":
        actionText = "隐藏"
        successText = "已隐藏"
        break
      case "show":
        actionText = "显示"
        successText = "已显示"
        break
      case "delete":
        actionText = "删除"
        successText = "已删除"
        break
    }

    if (window.confirm(`确定要${actionText}选中的 ${selectedReviews.length} 条评价吗？`)) {
      toast({
        title: `批量${actionText}成功`,
        description: `${selectedReviews.length} 条评价${successText}`,
      })
      setSelectedReviews([])
    }
  }

  // 处理回复评价
  const handleReply = (reviewId: number) => {
    setSelectedReview(allReviews.find(review => review.id === reviewId))
    setShowReviewDetail(true)
  }

  // 处理审核评价
  const handleReviewAction = (reviewId: number, action: string) => {
    let actionText = ""
    let successText = ""

    switch (action) {
      case "approve":
        actionText = "批准"
        successText = "已批准并发布"
        break
      case "reject":
        actionText = "拒绝"
        successText = "已拒绝"
        break
      case "hide":
        actionText = "隐藏"
        successText = "已隐藏"
        break
      case "show":
        actionText = "显示"
        successText = "已显示"
        break
      case "delete":
        actionText = "删除"
        successText = "已删除"
        break
    }

    if (window.confirm(`确定要${actionText}此评价吗？`)) {
      toast({
        title: `评价${actionText}成功`,
        description: `评价${successText}`,
      })
    }
  }

  // 过滤评价
  const filterReviews = () => {
    let filtered = [...allReviews]

    // 按标签页过滤
    if (activeTab !== "all") {
      filtered = filtered.filter(review => review.status === activeTab)
    }

    // 按关键词搜索
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase()
      filtered = filtered.filter(
        review =>
          review.comment.toLowerCase().includes(keyword) ||
          review.coach.toLowerCase().includes(keyword) ||
          review.member.name.toLowerCase().includes(keyword) ||
          review.course.toLowerCase().includes(keyword)
      )
    }

    // 按教练过滤
    if (selectedCoach !== "all") {
      filtered = filtered.filter(review => review.coachId === parseInt(selectedCoach))
    }

    // 按课程类型过滤
    if (selectedCourseType !== "all") {
      filtered = filtered.filter(review => review.courseType === courseTypes.find(ct => ct.id === parseInt(selectedCourseType))?.name)
    }

    // 按评分过滤
    if (selectedRating !== "all") {
      if (selectedRating === "5") {
        filtered = filtered.filter(review => review.rating === 5)
      } else if (selectedRating === "4") {
        filtered = filtered.filter(review => review.rating === 4)
      } else if (selectedRating === "3") {
        filtered = filtered.filter(review => review.rating <= 3)
      }
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "date-desc":
          return new Date(b.date).getTime() - new Date(a.date).getTime()
        case "date-asc":
          return new Date(a.date).getTime() - new Date(b.date).getTime()
        case "rating-desc":
          return b.rating - a.rating
        case "rating-asc":
          return a.rating - b.rating
        default:
          return new Date(b.date).getTime() - new Date(a.date).getTime()
      }
    })

    return filtered
  }

  // 获取当前页面的评价
  const getCurrentPageReviews = () => {
    const filtered = filterReviews()
    const startIndex = (currentPage - 1) * itemsPerPage
    return filtered.slice(startIndex, startIndex + itemsPerPage)
  }

  // 计算总页数
  const totalPages = Math.ceil(filterReviews().length / itemsPerPage)

  // 获取评分样式
  const getRatingStyle = (rating: number) => {
    if (rating >= 4.5) return "text-green-600"
    if (rating >= 3.5) return "text-blue-600"
    if (rating >= 2.5) return "text-yellow-600"
    return "text-red-600"
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge variant="default">已发布</Badge>
      case "pending":
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">待审核</Badge>
      case "hidden":
        return <Badge variant="outline" className="text-gray-500">已隐藏</Badge>
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">教练评价</h1>
          <p className="text-muted-foreground mt-1">管理和查看学员对教练的评价</p>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <Card className="border-dashed">
            <CardContent className="p-2 flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{reviewStats.total}</div>
                <div className="text-xs text-muted-foreground">总评价</div>
              </div>
              <Separator orientation="vertical" className="h-8" />
              <div className="text-center">
                <div className="text-2xl font-bold">{reviewStats.averageRating}</div>
                <div className="text-xs text-muted-foreground">平均评分</div>
              </div>
              <Separator orientation="vertical" className="h-8" />
              <div className="text-center">
                <div className="text-2xl font-bold">{reviewStats.pending}</div>
                <div className="text-xs text-muted-foreground">待审核</div>
              </div>
            </CardContent>
          </Card>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={() => setShowFilter(true)}>
              <Filter className="h-4 w-4" />
              <span className="sr-only">高级筛选</span>
            </Button>
            <Button variant="outline" size="icon" onClick={() => setShowStats(true)}>
              <BarChart2 className="h-4 w-4" />
              <span className="sr-only">统计分析</span>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">更多操作</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>更多操作</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Download className="mr-2 h-4 w-4" />
                  导出评价
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  刷新数据
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <Tabs defaultValue="all" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all" className="relative">
            全部评价
            <Badge className="ml-1 bg-primary/10 text-primary hover:bg-primary/20">{reviewStats.total}</Badge>
          </TabsTrigger>
          <TabsTrigger value="published" className="relative">
            已发布
            <Badge className="ml-1 bg-primary/10 text-primary hover:bg-primary/20">{reviewStats.published}</Badge>
          </TabsTrigger>
          <TabsTrigger value="pending" className="relative">
            待审核
            <Badge className="ml-1 bg-primary/10 text-primary hover:bg-primary/20">{reviewStats.pending}</Badge>
          </TabsTrigger>
          <TabsTrigger value="hidden" className="relative">
            已隐藏
            <Badge className="ml-1 bg-primary/10 text-primary hover:bg-primary/20">{reviewStats.hidden}</Badge>
          </TabsTrigger>
        </TabsList>

        <div className="mt-4 flex flex-col gap-4 md:flex-row">
          <div className="w-full md:w-1/3 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索教练、会员或评价内容"
              className="pl-10"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
            />
          </div>
          <div className="w-full md:w-1/4">
            <Select value={selectedCoach} onValueChange={setSelectedCoach}>
              <SelectTrigger>
                <SelectValue placeholder="选择教练" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有教练</SelectItem>
                {coaches.map((coach) => (
                  <SelectItem key={coach.id} value={coach.id.toString()}>
                    {coach.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="w-full md:w-1/4">
            <Select value={selectedCourseType} onValueChange={setSelectedCourseType}>
              <SelectTrigger>
                <SelectValue placeholder="课程类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                {courseTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id.toString()}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="w-full md:w-1/6">
            <Select value={selectedRating} onValueChange={setSelectedRating}>
              <SelectTrigger>
                <SelectValue placeholder="评分筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有评分</SelectItem>
                <SelectItem value="5">5星</SelectItem>
                <SelectItem value="4">4星</SelectItem>
                <SelectItem value="3">3星及以下</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full md:w-1/6">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="排序方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date-desc">最新评价</SelectItem>
                <SelectItem value="date-asc">最早评价</SelectItem>
                <SelectItem value="rating-desc">评分从高到低</SelectItem>
                <SelectItem value="rating-asc">评分从低到高</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <TabsContent value="all" className="mt-4">
          {/* 批量操作工具栏 */}
          {selectedReviews.length > 0 && (
            <Card className="mb-4 bg-muted/30">
              <CardContent className="p-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">已选择 {selectedReviews.length} 条评价</span>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={() => handleBatchAction("approve")}>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    批量通过
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBatchAction("reject")}>
                    <XCircle className="mr-2 h-4 w-4" />
                    批量拒绝
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => handleBatchAction("hide")}>
                    <EyeOff className="mr-2 h-4 w-4" />
                    批量隐藏
                  </Button>
                  <Button variant="destructive" size="sm" onClick={() => handleBatchAction("delete")}>
                    <Trash className="mr-2 h-4 w-4" />
                    批量删除
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 评价列表 */}
          <Card>
            <CardHeader className="p-4">
              <div className="flex items-center justify-between">
                <CardTitle>评价列表</CardTitle>
                <div className="text-sm text-muted-foreground">
                  共 {filterReviews().length} 条评价
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="border-t">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="h-10 px-4 text-left align-middle font-medium">
                        <Checkbox
                          checked={selectedReviews.length > 0 && selectedReviews.length === getCurrentPageReviews().length}
                          indeterminate={selectedReviews.length > 0 && selectedReviews.length < getCurrentPageReviews().length}
                          onCheckedChange={() => toggleSelectAll(getCurrentPageReviews())}
                        />
                      </th>
                      <th className="h-10 px-2 text-left align-middle font-medium">会员</th>
                      <th className="h-10 px-2 text-left align-middle font-medium">教练</th>
                      <th className="h-10 px-2 text-left align-middle font-medium">课程</th>
                      <th className="h-10 px-2 text-left align-middle font-medium">评分</th>
                      <th className="h-10 px-2 text-left align-middle font-medium">评价内容</th>
                      <th className="h-10 px-2 text-left align-middle font-medium">日期</th>
                      <th className="h-10 px-2 text-left align-middle font-medium">状态</th>
                      <th className="h-10 px-2 text-right align-middle font-medium">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {getCurrentPageReviews().length > 0 ? (
                      getCurrentPageReviews().map((review) => (
                        <tr key={review.id} className="border-b hover:bg-muted/50 transition-colors">
                          <td className="p-4">
                            <Checkbox
                              checked={selectedReviews.includes(review.id)}
                              onCheckedChange={() => toggleSelectReview(review.id)}
                            />
                          </td>
                          <td className="p-2">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={review.member.avatar} alt={review.member.name} />
                                <AvatarFallback>{review.member.name[0]}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{review.member.name}</div>
                                <div className="text-xs text-muted-foreground">{review.member.level}</div>
                              </div>
                            </div>
                          </td>
                          <td className="p-2">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={review.coachAvatar} alt={review.coach} />
                                <AvatarFallback>{review.coach[0]}</AvatarFallback>
                              </Avatar>
                              <span>{review.coach}</span>
                            </div>
                          </td>
                          <td className="p-2">
                            <div>
                              <div className="font-medium">{review.course}</div>
                              <div className="text-xs text-muted-foreground">{review.courseType}</div>
                            </div>
                          </td>
                          <td className="p-2">
                            <div className="flex items-center">
                              <span className={`font-medium mr-1 ${getRatingStyle(review.rating)}`}>{review.rating}</span>
                              <div className="flex">
                                {Array.from({ length: 5 }).map((_, i) => (
                                  <StarIcon
                                    key={i}
                                    className={`h-3 w-3 ${
                                      i < review.rating ? "text-yellow-500 fill-yellow-500" : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                          </td>
                          <td className="p-2 max-w-[300px]">
                            <div className="truncate">{review.comment}</div>
                          </td>
                          <td className="p-2">
                            <div className="whitespace-nowrap">{review.date}</div>
                          </td>
                          <td className="p-2">
                            {getStatusBadge(review.status)}
                            {!review.hasReply && review.status === "published" && (
                              <Badge variant="outline" className="ml-1 text-orange-500 border-orange-200 bg-orange-50">未回复</Badge>
                            )}
                          </td>
                          <td className="p-2 text-right">
                            <div className="flex justify-end gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleReviewClick(review)}
                              >
                                <Eye className="h-4 w-4" />
                                <span className="sr-only">查看详情</span>
                              </Button>

                              {!review.hasReply && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleReply(review.id)}
                                >
                                  <MessageSquare className="h-4 w-4" />
                                  <span className="sr-only">回复</span>
                                </Button>
                              )}

                              {review.status === "pending" && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                    onClick={() => handleReviewAction(review.id, "approve")}
                                  >
                                    <CheckCircle className="h-4 w-4" />
                                    <span className="sr-only">通过</span>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                    onClick={() => handleReviewAction(review.id, "reject")}
                                  >
                                    <XCircle className="h-4 w-4" />
                                    <span className="sr-only">拒绝</span>
                                  </Button>
                                </>
                              )}

                              {review.status === "published" && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleReviewAction(review.id, "hide")}
                                >
                                  <EyeOff className="h-4 w-4" />
                                  <span className="sr-only">隐藏</span>
                                </Button>
                              )}

                              {review.status === "hidden" && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleReviewAction(review.id, "show")}
                                >
                                  <Eye className="h-4 w-4" />
                                  <span className="sr-only">显示</span>
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={9} className="h-32 text-center">
                          <div className="flex flex-col items-center justify-center text-muted-foreground">
                            <AlertTriangle className="h-8 w-8 mb-2" />
                            <p>没有找到符合条件的评价</p>
                            <Button variant="link" onClick={() => {
                              setSearchKeyword("")
                              setSelectedCoach("all")
                              setSelectedCourseType("all")
                              setSelectedRating("all")
                            }}>
                              清除筛选条件
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
            <CardFooter className="flex items-center justify-between p-4 border-t">
              <div className="text-sm text-muted-foreground">
                显示 {getCurrentPageReviews().length} 条，共 {filterReviews().length} 条
              </div>

              {totalPages > 1 && (
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      />
                    </PaginationItem>

                    {Array.from({ length: totalPages }).map((_, i) => (
                      <PaginationItem key={i}>
                        <PaginationLink
                          isActive={currentPage === i + 1}
                          onClick={() => setCurrentPage(i + 1)}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              )}
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="published" className="mt-4">
          {/* 使用相同的表格组件，但过滤为已发布的评价 */}
          <Card>
            <CardHeader className="p-4">
              <div className="flex items-center justify-between">
                <CardTitle>已发布评价</CardTitle>
                <div className="text-sm text-muted-foreground">
                  共 {allReviews.filter(r => r.status === "published").length} 条评价
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {/* 表格内容与全部评价相同，但数据已过滤 */}
              <div className="text-center py-8 text-muted-foreground">
                切换到"全部评价"标签页查看完整列表
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending" className="mt-4">
          {/* 待审核评价内容 */}
          <Card>
            <CardHeader className="p-4">
              <div className="flex items-center justify-between">
                <CardTitle>待审核评价</CardTitle>
                <div className="text-sm text-muted-foreground">
                  共 {allReviews.filter(r => r.status === "pending").length} 条评价
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {/* 表格内容与全部评价相同，但数据已过滤 */}
              <div className="text-center py-8 text-muted-foreground">
                切换到"全部评价"标签页查看完整列表
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="hidden" className="mt-4">
          {/* 已隐藏评价内容 */}
          <Card>
            <CardHeader className="p-4">
              <div className="flex items-center justify-between">
                <CardTitle>已隐藏评价</CardTitle>
                <div className="text-sm text-muted-foreground">
                  共 {allReviews.filter(r => r.status === "hidden").length} 条评价
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              {/* 表格内容与全部评价相同，但数据已过滤 */}
              <div className="text-center py-8 text-muted-foreground">
                切换到"全部评价"标签页查看完整列表
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 对话框组件 */}
      <ReviewDetailDialog
        open={showReviewDetail}
        onOpenChange={(open) => {
          setShowReviewDetail(open)
          if (!open) {
            // 关闭对话框时可以刷新数据
          }
        }}
        review={selectedReview}
      />

      <ReviewStatsDialog
        open={showStats}
        onOpenChange={setShowStats}
      />

      <AdvancedFilterDialog
        open={showFilter}
        onOpenChange={setShowFilter}
        onApplyFilter={(filters) => {
          // 应用高级筛选
          setShowFilter(false)
          toast({
            title: "筛选条件已应用",
            description: "评价列表已根据筛选条件更新",
          })
        }}
      />

      {/* 页面底部信息 */}
      <div className="mt-8 border-t pt-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 text-sm text-muted-foreground">
          <div>
            <p>提示：您可以通过点击表格中的操作按钮来管理评价，或使用批量操作功能同时处理多条评价。</p>
            <p>评价管理支持审核、回复、隐藏和删除操作。您还可以查看评价统计分析以了解整体情况。</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <Badge variant="default">已发布</Badge>
              <span>- 已审核并公开显示</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">待审核</Badge>
              <span>- 等待管理员审核</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="outline" className="text-gray-500">已隐藏</Badge>
              <span>- 已审核但不公开显示</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

