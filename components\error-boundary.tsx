'use client';

import React, { ErrorInfo, ReactNode } from 'react';
import { useAuth } from '@/contexts/auth-context';

interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryClassProps extends ErrorBoundaryProps {
  showErrorDetails: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

/**
 * 错误边界组件，用于捕获和处理页面渲染过程中的错误
 * 根据用户登录状态决定是否显示错误信息
 */
class ErrorBoundaryClass extends React.Component<ErrorBoundaryClassProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryClassProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 更新 state 使下一次渲染能够显示错误 UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 可以在这里记录错误信息
    console.error('错误边界捕获到错误:', error);
    console.error('错误组件栈:', errorInfo.componentStack);
  }

  render() {
    if (this.state.hasError) {
      // 检查是否应该显示错误信息
      if (this.props.showErrorDetails) {
        return (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <h2 className="text-lg font-semibold text-red-800">页面出现错误</h2>
            <p className="mt-2 text-sm text-red-700">{this.state.error?.message}</p>
            <button
              className="mt-3 px-3 py-1 text-sm bg-red-100 text-red-800 rounded-md hover:bg-red-200"
              onClick={() => this.setState({ hasError: false, error: null })}
            >
              重试
            </button>
          </div>
        );
      } else {
        // 对于未登录用户，显示友好的错误信息，不暴露技术细节
        return (
          <div className="p-4">
            <p className="text-sm text-gray-600">加载内容时遇到问题，请稍后再试。</p>
          </div>
        );
      }
    }

    return this.props.children;
  }
}

/**
 * 错误边界组件的包装器，用于获取身份验证状态
 */
export function ErrorBoundary({ children }: ErrorBoundaryProps) {
  const { isAuthenticated } = useAuth();
  
  // 只有登录用户才能看到详细错误信息
  return (
    <ErrorBoundaryClass showErrorDetails={isAuthenticated}>
      {children}
    </ErrorBoundaryClass>
  );
}

// 添加一个全局错误处理函数，根据登录状态控制控制台错误的显示
// 在应用启动时调用这个函数
// 这个函数将在全局范围内执行，影响所有页面

export function setupErrorHandling() {
  // 保存原始的console方法
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  
  // 重写console.error方法
  console.error = function(...args: any[]) {
    // 检查用户是否登录
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    
    // 如果用户已登录，正常显示错误
    // 如果用户未登录且当前路径是公共页面，不显示错误
    if (token || isPublicPath()) {
      originalConsoleError.apply(console, args);
    }
  };
  
  // 重写console.warn方法
  console.warn = function(...args: any[]) {
    // 检查用户是否登录
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    
    // 如果用户已登录，正常显示警告
    if (token || isPublicPath()) {
      originalConsoleWarn.apply(console, args);
    }
  };
  
  // 判断当前路径是否为公共页面
  function isPublicPath() {
    const publicPaths = ["/login", "/home", "/register", "/forgot-password"];
    const currentPath = window.location.pathname;
    return publicPaths.some(path => currentPath === path || currentPath.startsWith(`${path}/`));
  }
}
