import { NextResponse } from 'next/server';

// 用户数据结构
export interface User {
  id: number;
  username: string;
  name: string;
  avatar: string;
  email?: string;
  phone?: string;
  tenant_id: number;
  tenant_name: string;
  role: string; // admin, manager, staff
  permissions: string[];
}

// 模拟用户数据
const USERS: User[] = [
  {
    id: 1,
    username: 'admin',
    name: '系统管理员',
    avatar: '/avatars/admin.png',
    email: '<EMAIL>',
    phone: '13800000000',
    tenant_id: 0,
    tenant_name: '系统',
    role: 'admin',
    permissions: ['*'] // 所有权限
  },
  {
    id: 2,
    username: 'jingxin',
    name: '静心瑜伽管理员',
    avatar: '/avatars/jingxin.png',
    email: '<EMAIL>',
    phone: '13900000000',
    tenant_id: 1,
    tenant_name: '静心瑜伽馆',
    role: 'admin',
    permissions: ['tenant:manage', 'store:manage', 'staff:manage', 'course:manage', 'member:manage']
  },
  {
    id: 3,
    username: 'manager',
    name: '门店经理',
    avatar: '/avatars/manager.png',
    email: '<EMAIL>',
    phone: '13700000000',
    tenant_id: 1,
    tenant_name: '静心瑜伽馆',
    role: 'manager',
    permissions: ['store:view', 'staff:view', 'course:manage', 'member:manage']
  }
];

// GET 获取当前用户信息
export async function GET(request: Request) {
  try {
    // 从请求头获取用户信息
    const username = request.headers.get('X-Username') || 'jingxin'; // 默认为静心瑜伽管理员
    
    // 查找对应用户
    const user = USERS.find(u => u.username === username);
    
    if (!user) {
      return NextResponse.json(
        { code: 404, message: '用户不存在' },
        { status: 404 }
      );
    }
    
    // 返回用户信息
    return NextResponse.json({
      code: 0,
      message: '获取用户信息成功',
      data: {
        user: {
          ...user,
          token: `mock_token_${user.username}_${Date.now()}` // 生成模拟令牌
        }
      }
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return NextResponse.json(
      { code: 500, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}