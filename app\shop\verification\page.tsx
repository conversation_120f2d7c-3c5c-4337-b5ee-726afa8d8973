"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Search, Plus, MoreHorizontal, CheckCircle, QrCode, FileText, Calendar, Clock, User, CreditCard, BookOpen, UserPlus, Download } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { DatePickerWithRange } from "@/components/date-picker-with-range"

// 模拟核销记录数据
const verificationRecords = [
  {
    id: "VR-20230601-001",
    code: "VC123456789",
    orderId: "ORD-20230510-002",
    customerName: "李四",
    customerPhone: "13800138002",
    productName: "高级会员卡 - 年卡",
    productType: "membership", // 会员卡
    verificationTime: "2023-06-01 10:30:25",
    verifier: "张店长",
    location: "总店",
    status: "verified", // 已核销
    validUntil: "2024-06-01",
  },
  {
    id: "VR-20230605-002",
    code: "VC234567890",
    orderId: "ORD-20230520-004",
    customerName: "赵六",
    customerPhone: "13800138004",
    productName: "瑜伽入门课程 - 在线视频",
    productType: "course", // 课程
    verificationTime: "2023-06-05 14:45:12",
    verifier: "系统自动",
    location: "线上",
    status: "verified", // 已核销
    validUntil: "永久有效",
  },
  {
    id: "VR-20230610-003",
    code: "VC345678901",
    orderId: "ORD-20230601-006",
    customerName: "周八",
    customerPhone: "13800138006",
    productName: "高级私教身份 - 月卡",
    productType: "identity", // 身份卡
    verificationTime: "2023-06-10 09:15:30",
    verifier: "王教练",
    location: "分店一",
    status: "verified", // 已核销
    validUntil: "2023-07-10",
  },
  {
    id: "VR-20230615-004",
    code: "VC456789012",
    orderId: "ORD-20230615-009",
    customerName: "吴九",
    customerPhone: "13800138009",
    productName: "普通会员卡 - 季卡",
    productType: "membership", // 会员卡
    verificationTime: "2023-06-15 16:20:45",
    verifier: "李店长",
    location: "总店",
    status: "verified", // 已核销
    validUntil: "2023-09-15",
  },
]

// 模拟待核销的虚拟商品
const pendingVerifications = [
  {
    id: "PV-20230701-001",
    code: "VC567890123",
    orderId: "ORD-20230630-010",
    customerName: "郑十",
    customerPhone: "13800138010",
    productName: "高级会员卡 - 年卡",
    productType: "membership", // 会员卡
    orderTime: "2023-06-30 11:30:25",
    status: "pending", // 待核销
    expireTime: "2023-07-30", // 核销码过期时间
  },
  {
    id: "PV-20230702-002",
    code: "VC678901234",
    orderId: "ORD-20230701-011",
    customerName: "钱十一",
    customerPhone: "13800138011",
    productName: "瑜伽进阶课程 - 视频系列",
    productType: "course", // 课程
    orderTime: "2023-07-01 14:45:12",
    status: "pending", // 待核销
    expireTime: "2023-08-01", // 核销码过期时间
  },
  {
    id: "PV-20230703-003",
    code: "VC789012345",
    orderId: "ORD-20230702-012",
    customerName: "孙十二",
    customerPhone: "13800138012",
    productName: "高级私教身份 - 月卡",
    productType: "identity", // 身份卡
    orderTime: "2023-07-02 09:15:30",
    status: "pending", // 待核销
    expireTime: "2023-08-02", // 核销码过期时间
  },
]

export default function VerificationPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("records")
  const [typeFilter, setTypeFilter] = useState("all")
  const [dateRange, setDateRange] = useState({ from: undefined, to: undefined })

  // 过滤核销记录
  const filteredRecords = verificationRecords.filter(
    (record) => {
      // 基本搜索过滤
      const basicFilter = (
        record.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        record.customerPhone.includes(searchQuery) ||
        record.productName.toLowerCase().includes(searchQuery.toLowerCase())
      )

      // 类型过滤
      const typeFilterMatch = typeFilter === "all" || record.productType === typeFilter

      // 日期范围过滤
      let dateFilterMatch = true
      if (dateRange.from && dateRange.to) {
        const recordDate = new Date(record.verificationTime)
        const fromDate = dateRange.from
        const toDate = new Date(dateRange.to)
        toDate.setHours(23, 59, 59, 999) // 设置为当天结束时间
        dateFilterMatch = recordDate >= fromDate && recordDate <= toDate
      }

      return basicFilter && typeFilterMatch && dateFilterMatch
    }
  )

  // 过滤待核销商品
  const filteredPending = pendingVerifications.filter(
    (item) => {
      // 基本搜索过滤
      const basicFilter = (
        item.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.customerPhone.includes(searchQuery) ||
        item.productName.toLowerCase().includes(searchQuery.toLowerCase())
      )

      // 类型过滤
      const typeFilterMatch = typeFilter === "all" || item.productType === typeFilter

      return basicFilter && typeFilterMatch
    }
  )

  // 获取商品类型图标
  const getTypeIcon = (type) => {
    switch (type) {
      case "membership":
        return <CreditCard className="h-4 w-4" />
      case "course":
        return <BookOpen className="h-4 w-4" />
      case "identity":
        return <UserPlus className="h-4 w-4" />
      default:
        return null
    }
  }

  // 获取商品类型名称
  const getTypeName = (type) => {
    switch (type) {
      case "membership":
        return "会员卡"
      case "course":
        return "在线课程"
      case "identity":
        return "身份卡"
      default:
        return type
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">核销管理</h1>
        <div className="flex gap-2">
          <Button>
            <QrCode className="mr-2 h-4 w-4" />
            扫码核销
          </Button>
          {activeTab === "records" && (
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              导出记录
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="records">核销记录</TabsTrigger>
          <TabsTrigger value="pending">待核销商品</TabsTrigger>
        </TabsList>

        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mt-6">
          <div className="flex flex-1 items-center gap-2 max-w-md">
            <Input
              placeholder="搜索核销码、订单号、客户信息或商品名称..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>
          <div className="flex items-center gap-2">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="商品类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                <SelectItem value="membership">会员卡</SelectItem>
                <SelectItem value="course">在线课程</SelectItem>
                <SelectItem value="identity">身份卡</SelectItem>
              </SelectContent>
            </Select>
            {activeTab === "records" && (
              <DatePickerWithRange
                className="w-[300px]"
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
              />
            )}
          </div>
        </div>

        {/* 核销记录标签页 */}
        <TabsContent value="records" className="mt-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>核销记录</CardTitle>
              <CardDescription>查看所有虚拟商品的核销记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>核销码</TableHead>
                    <TableHead>订单号</TableHead>
                    <TableHead>客户信息</TableHead>
                    <TableHead>商品信息</TableHead>
                    <TableHead>核销时间</TableHead>
                    <TableHead>核销人员</TableHead>
                    <TableHead>核销地点</TableHead>
                    <TableHead>有效期至</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium">{record.code}</TableCell>
                      <TableCell>{record.orderId}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{record.customerName}</span>
                          <span className="text-muted-foreground text-xs">{record.customerPhone}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {getTypeIcon(record.productType)}
                          <span>{record.productName}</span>
                        </div>
                      </TableCell>
                      <TableCell>{record.verificationTime}</TableCell>
                      <TableCell>{record.verifier}</TableCell>
                      <TableCell>{record.location}</TableCell>
                      <TableCell>{record.validUntil}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <FileText className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <User className="mr-2 h-4 w-4" />
                              查看客户
                            </DropdownMenuItem>
                            <DropdownMenuItem>打印凭证</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 待核销商品标签页 */}
        <TabsContent value="pending" className="mt-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>待核销商品</CardTitle>
              <CardDescription>管理等待核销的虚拟商品</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>核销码</TableHead>
                    <TableHead>订单号</TableHead>
                    <TableHead>客户信息</TableHead>
                    <TableHead>商品信息</TableHead>
                    <TableHead>下单时间</TableHead>
                    <TableHead>核销码过期时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPending.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.code}</TableCell>
                      <TableCell>{item.orderId}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{item.customerName}</span>
                          <span className="text-muted-foreground text-xs">{item.customerPhone}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {getTypeIcon(item.productType)}
                          <span>{item.productName}</span>
                        </div>
                      </TableCell>
                      <TableCell>{item.orderTime}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Clock className="mr-1 h-4 w-4 text-yellow-500" />
                          {item.expireTime}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="default" size="sm">
                          <CheckCircle className="mr-2 h-4 w-4" />
                          立即核销
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
