"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>eader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { MoreHorizontal, Pencil, Wallet, User, Share2, MessageSquare, Trash2, Eye } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ShareholderDetailDialog } from "@/components/shareholders/shareholder-detail-dialog"

// 模拟数据
const shareholders = [
  {
    id: "1",
    name: "张三",
    phone: "13800138001",
    type: "消费型股东",
    status: "active",
    joinDate: "2023-05-15",
    referrals: 5,
    totalDividend: 2500,
    avatar: "/avatars/01.png",
    tags: ["高频消费", "瑜伽爱好者"],
  },
  {
    id: "2",
    name: "李四",
    phone: "13900139001",
    type: "投资型股东",
    status: "active",
    joinDate: "2023-04-20",
    referrals: 0,
    totalDividend: 5000,
    avatar: "/avatars/02.png",
    tags: ["投资人"],
  },
  {
    id: "3",
    name: "王五",
    phone: "13700137001",
    type: "资源型股东",
    status: "active",
    joinDate: "2023-06-01",
    referrals: 15,
    totalDividend: 3750,
    avatar: "/avatars/03.png",
    tags: ["KOL", "社交媒体"],
  },
  {
    id: "4",
    name: "赵六",
    phone: "13600136001",
    type: "员工型股东",
    status: "active",
    joinDate: "2023-03-10",
    referrals: 8,
    totalDividend: 4200,
    avatar: "/avatars/04.png",
    tags: ["瑜伽教练"],
  },
  {
    id: "5",
    name: "美丽美容院",
    phone: "13500135001",
    type: "联盟型股东",
    status: "inactive",
    joinDate: "2023-07-01",
    referrals: 12,
    totalDividend: 1800,
    avatar: "/avatars/05.png",
    tags: ["美容行业", "异业合作"],
  },
]

interface ShareholderGridProps {
  searchQuery: string
  shareholderType: string
}

export function ShareholderGrid({ searchQuery, shareholderType }: ShareholderGridProps) {
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [openDetailDialog, setOpenDetailDialog] = useState(false)
  const [selectedShareholder, setSelectedShareholder] = useState<any>(null)

  // 过滤股东
  const filteredShareholders = shareholders.filter((shareholder) => {
    if (shareholderType !== "all" && !shareholder.type.toLowerCase().includes(shareholderType.toLowerCase())) {
      return false
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        shareholder.name.toLowerCase().includes(query) ||
        shareholder.phone.includes(query) ||
        shareholder.type.toLowerCase().includes(query) ||
        shareholder.tags.some((tag) => tag.toLowerCase().includes(query))
      )
    }
    
    return true
  })

  // 选择单行
  const toggleSelectRow = (id: string) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id))
    } else {
      setSelectedRows([...selectedRows, id])
    }
  }

  // 打开股东详情
  const openShareholderDetail = (shareholder: any) => {
    setSelectedShareholder(shareholder)
    setOpenDetailDialog(true)
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {filteredShareholders.map((shareholder) => (
        <Card key={shareholder.id} className="overflow-hidden">
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Checkbox
                  checked={selectedRows.includes(shareholder.id)}
                  onCheckedChange={() => toggleSelectRow(shareholder.id)}
                  aria-label={`Select ${shareholder.name}`}
                />
                <Avatar className="h-10 w-10">
                  <AvatarImage src={shareholder.avatar} alt={shareholder.name} />
                  <AvatarFallback>{shareholder.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium">{shareholder.name}</h3>
                  <p className="text-sm text-muted-foreground">{shareholder.phone}</p>
                </div>
              </div>
              <Badge variant={shareholder.status === "active" ? "default" : "secondary"}>
                {shareholder.status === "active" ? "活跃" : "不活跃"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <p className="text-muted-foreground">股东类型</p>
                <p className="font-medium">{shareholder.type}</p>
              </div>
              <div>
                <p className="text-muted-foreground">加入日期</p>
                <p className="font-medium">{shareholder.joinDate}</p>
              </div>
              <div>
                <p className="text-muted-foreground">引流客户</p>
                <p className="font-medium">{shareholder.referrals}人</p>
              </div>
              <div>
                <p className="text-muted-foreground">累计分红</p>
                <p className="font-medium">¥{shareholder.totalDividend.toFixed(2)}</p>
              </div>
            </div>
            <div className="mt-3">
              <p className="text-muted-foreground text-sm mb-1">标签</p>
              <div className="flex flex-wrap gap-1">
                {shareholder.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between p-4 pt-0">
            <Button variant="outline" size="sm" onClick={() => openShareholderDetail(shareholder)}>
              <Eye className="mr-2 h-4 w-4" />
              详情
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>操作</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => openShareholderDetail(shareholder)}>
                  <Eye className="mr-2 h-4 w-4" />
                  查看详情
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Pencil className="mr-2 h-4 w-4" />
                  编辑信息
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Wallet className="mr-2 h-4 w-4" />
                  分红记录
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Share2 className="mr-2 h-4 w-4" />
                  引流记录
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <MessageSquare className="mr-2 h-4 w-4" />
                  发送消息
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" />
                  删除
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardFooter>
        </Card>
      ))}

      <ShareholderDetailDialog
        open={openDetailDialog}
        onOpenChange={setOpenDetailDialog}
        shareholder={selectedShareholder}
      />
    </div>
  )
}
