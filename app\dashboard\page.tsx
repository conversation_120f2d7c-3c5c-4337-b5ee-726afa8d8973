"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Settings,
  HelpCircle,
  Bell,
  MapIcon,
  Cloud,
  CloudRain,
  Sun,
  Umbrella,
  Store,
  LinkIcon
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from "@/components/ui/card"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { useOnboarding } from "@/contexts/onboarding-context"

// 导入新组件
import { ActionCenter } from "@/components/dashboard/action-center"
import { BusinessOverview } from "@/components/dashboard/business-overview"
import { CourseSchedulePreview } from "@/components/dashboard/course-schedule-preview"
import { SmartInsights } from "@/components/dashboard/smart-insights"
import { TaskReminder } from "@/components/dashboard/task-reminder"

export default function Dashboard() {
  const { user } = useAuth()
  const { setShowOnboarding, resetOnboarding, isOnboardingComplete } = useOnboarding()
  const [greeting, setGreeting] = useState("你好")
  const [notificationCount, setNotificationCount] = useState(5)
  const [cityInfo, setCityInfo] = useState({ city: "北京", district: "朝阳区" })
  const [weatherInfo, setWeatherInfo] = useState({
    temperature: 24,
    condition: "晴",
    icon: <Sun className="h-5 w-5 text-amber-500" />,
    airQuality: "良好"
  })

  // 根据时间设置问候语
  useEffect(() => {
    const hours = new Date().getHours()
    let greetingText = "你好"

    if (hours >= 5 && hours < 12) {
      greetingText = "早上好"
    } else if (hours >= 12 && hours < 18) {
      greetingText = "下午好"
    } else if (hours >= 18 || hours < 5) {
      greetingText = "晚上好"
    }

    setGreeting(greetingText)
  }, [])

  // 模拟获取天气信息
  useEffect(() => {
    // 实际应用中，这里应该调用天气API获取实时天气
    const getWeatherInfo = () => {
      // 模拟不同天气状态
      const weatherConditions = [
        {
          temperature: 24,
          condition: "晴",
          icon: <Sun className="h-5 w-5 text-amber-500" />,
          airQuality: "良好"
        },
        {
          temperature: 22,
          condition: "多云",
          icon: <Cloud className="h-5 w-5 text-gray-500" />,
          airQuality: "良好"
        },
        {
          temperature: 19,
          condition: "小雨",
          icon: <CloudRain className="h-5 w-5 text-blue-500" />,
          airQuality: "优"
        }
      ];

      // 随机选择一种天气状态
      const randomWeather = weatherConditions[Math.floor(Math.random() * weatherConditions.length)];
      setWeatherInfo(randomWeather);
    };

    getWeatherInfo();
  }, []);

  // 获取当前日期
  const today = new Date()
  const formattedDate = today.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">管理工作台</h1>
      
      {/* 新增连锁管理卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-6 mb-8">
        <Card className="border border-primary/20 hover:border-primary/40 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <div className="bg-primary/10 p-2 rounded-full">
                <Store className="h-5 w-5 text-primary" />
              </div>
              <CardTitle className="text-lg">连锁门店管理</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-sm text-muted-foreground">管理连锁门店、区域和门店调整，提高门店管理效率。</p>
          </CardContent>
          <CardFooter>
            <Link href="/stores/chain">
              <Button size="sm">
                进入连锁管理
              </Button>
            </Link>
          </CardFooter>
        </Card>
        
        <Card className="border border-primary/20 hover:border-primary/40 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <div className="bg-primary/10 p-2 rounded-full">
                <LinkIcon className="h-5 w-5 text-primary" />
              </div>
              <CardTitle className="text-lg">连锁同步设置</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-sm text-muted-foreground">管理课程类型、会员卡和员工信息在所有门店间的同步设置。</p>
          </CardContent>
          <CardFooter>
            <Link href="/stores/chain/sync">
              <Button size="sm">
                配置同步规则
              </Button>
            </Link>
          </CardFooter>
        </Card>
        
        <Card className="border border-primary/20 hover:border-primary/40 transition-colors">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <div className="bg-primary/10 p-2 rounded-full">
                <Settings className="h-5 w-5 text-primary" />
              </div>
              <CardTitle className="text-lg">系统设置</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="pb-2">
            <p className="text-sm text-muted-foreground">管理系统权限、账户设置和通知偏好。</p>
          </CardContent>
          <CardFooter>
            <Link href="/settings">
              <Button size="sm">
                系统设置
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
      
      {/* 顶部欢迎区域 */}
      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center gap-1 bg-muted/50 px-3 py-1 rounded-full">
              <MapIcon className="h-4 w-4 text-primary" />
              <span className="text-sm">{cityInfo.city} {cityInfo.district}</span>
            </div>
            <div className="flex items-center gap-1 bg-muted/50 px-3 py-1 rounded-full">
              {weatherInfo.icon}
              <span className="text-sm">{weatherInfo.condition} {weatherInfo.temperature}°C</span>
              <span className="text-xs text-muted-foreground ml-1">空气{weatherInfo.airQuality}</span>
              {weatherInfo.condition === "小雨" && (
                <span className="text-xs text-blue-500 flex items-center ml-1">
                  <Umbrella className="h-3 w-3 mr-1" />
                  请带伞
                </span>
              )}
            </div>
          </div>
          <h2 className="text-3xl font-bold tracking-tight">{greeting}，{user?.nickname || user?.username || "用户"}</h2>
          <p className="text-muted-foreground">瑜伽生活会馆管理系统工作台 · {formattedDate}</p>
        </div>
        <div className="flex gap-2">
          {!isOnboardingComplete && (
            <Button
              variant="outline"
              onClick={() => setShowOnboarding(true)}
              className="flex items-center gap-2"
            >
              <HelpCircle className="h-4 w-4" />
              继续系统初始化
            </Button>
          )}
          <Button
            variant="outline"
            className="flex items-center gap-2 relative"
          >
            <Bell className="h-4 w-4" />
            消息中心
            {notificationCount > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0">
                {notificationCount}
              </Badge>
            )}
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            自定义工作台
          </Button>
        </div>
      </div>

      {/* 操作中心 */}
      <ActionCenter />

      {/* 业务数据概览 */}
      <BusinessOverview />

      {/* 主要内容区域 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <div className="col-span-1">
          <TaskReminder />
        </div>
        <div className="col-span-1">
          <CourseSchedulePreview />
        </div>
        <div className="col-span-1">
          <SmartInsights />
        </div>
      </div>
    </div>
  )
}