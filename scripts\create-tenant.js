// 通过API创建租户数据
async function createTenant() {
  console.log('开始创建租户数据...');
  
  const tenantData = {
    name: '管理员',
    phone: '13800138000',
    wechat: 'admin_wechat',
    company: '示例瑜伽馆',
    employeeCount: '10-50人',
    storeType: '瑜伽馆',
    channel: '官网',
    message: '测试租户数据'
  };
  
  try {
    const response = await fetch('http://localhost:3005/api/tenants', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tenantData)
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log(`✓ 创建租户成功: ${tenantData.company}`);
      console.log('租户ID:', result.tenantId);
    } else {
      console.log(`✗ 创建租户失败: ${result.error}`);
    }
  } catch (error) {
    console.error(`✗ 创建租户出错:`, error.message);
  }
  
  console.log('租户数据创建完成!');
}

createTenant();
