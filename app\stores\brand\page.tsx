"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge as BadgeIcon, Plus, Upload, Brush, Palette, CircleDollarSign, Eye, ImageIcon } from "lucide-react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "@/components/ui/use-toast"
import Image from "next/image"

// 品牌数据接口
interface Brand {
  id: string | number;
  name: string;
  logo: string;
  primaryColor: string;
  secondaryColor: string;
  description: string;
  guidelines: string;
  createdAt: string;
  status: 'active' | 'inactive' | 'draft';
  storesCount: number;
}

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, { message: "品牌名称至少需要2个字符" }).max(50, { message: "品牌名称不能超过50个字符" }),
  description: z.string().max(500, { message: "描述不能超过500个字符" }).optional(),
  primaryColor: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, { message: "请输入有效的颜色代码，例如 #FF5733" }),
  secondaryColor: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, { message: "请输入有效的颜色代码，例如 #33FF57" }),
  guidelines: z.string().max(1000, { message: "品牌指南不能超过1000个字符" }).optional(),
  type: z.enum(["main", "sub", "partner"], { message: "请选择品牌类型" }),
});

type FormValues = z.infer<typeof formSchema>;

export default function BrandManagementPage() {
  const [brands, setBrands] = useState<Brand[]>([
    {
      id: 1,
      name: "瑜伽星空",
      logo: "/assets/brands/yoga-sky.png",
      primaryColor: "#4A90E2",
      secondaryColor: "#50E3C2",
      description: "专注于高端瑜伽教学和体验的主品牌",
      guidelines: "品牌标志应始终保持清晰可见，主色调使用蓝色系，传达专业、平静的氛围。",
      createdAt: "2023-01-01",
      status: 'active',
      storesCount: 12
    },
    {
      id: 2,
      name: "星空青少年",
      logo: "/assets/brands/yoga-kids.png",
      primaryColor: "#F5A623",
      secondaryColor: "#7ED321",
      description: "针对青少年群体的子品牌，注重趣味性和健康成长",
      guidelines: "使用明亮活泼的色彩，标志设计更加圆润可爱，传达活力与成长。",
      createdAt: "2023-03-15",
      status: 'active',
      storesCount: 5
    },
    {
      id: 3,
      name: "星空高级教练",
      logo: "/assets/brands/yoga-pro.png",
      primaryColor: "#9013FE",
      secondaryColor: "#D0021B",
      description: "面向专业教练培训的高端品牌",
      guidelines: "使用深紫色为主色调，设计风格简约专业，传达高端与专业感。",
      createdAt: "2023-05-20",
      status: 'active',
      storesCount: 3
    },
    {
      id: 4,
      name: "瑜伽用品",
      logo: "/assets/brands/yoga-products.png",
      primaryColor: "#417505",
      secondaryColor: "#7ED321",
      description: "瑜伽相关产品销售的子品牌",
      guidelines: "使用自然色系，设计元素融入自然与环保理念，突出产品品质。",
      createdAt: "2023-07-10",
      status: 'draft',
      storesCount: 0
    }
  ]);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openPreviewDialog, setOpenPreviewDialog] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);

  // 创建表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      primaryColor: "#4A90E2",
      secondaryColor: "#50E3C2",
      guidelines: "",
      type: "main"
    }
  });

  // 处理添加品牌
  const handleAddBrand = (values: FormValues) => {
    try {
      // 创建新品牌对象
      const newBrand: Brand = {
        id: Date.now(),
        name: values.name,
        logo: "/assets/brands/default-logo.png", // 默认logo
        primaryColor: values.primaryColor,
        secondaryColor: values.secondaryColor,
        description: values.description || "",
        guidelines: values.guidelines || "",
        createdAt: new Date().toISOString().split('T')[0],
        status: 'draft',
        storesCount: 0
      };

      // 更新品牌列表
      const updatedBrands = [...brands, newBrand];
      setBrands(updatedBrands);
      
      toast({
        title: "品牌添加成功",
        description: `${values.name} 已成功添加`,
      });

      // 关闭对话框并重置表单
      setOpenAddDialog(false);
      form.reset();
    } catch (error) {
      console.error('添加品牌失败:', error);
      toast({
        title: "添加品牌失败",
        description: "请稍后再试",
        variant: "destructive"
      });
    }
  };

  // 预览品牌
  const handlePreviewBrand = (brand: Brand) => {
    setSelectedBrand(brand);
    setOpenPreviewDialog(true);
  };

  return (
    <div className="container max-w-6xl py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">品牌管理</h1>
        <Button onClick={() => setOpenAddDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          添加品牌
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">总品牌数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{brands.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">已应用门店数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{brands.reduce((acc, brand) => acc + brand.storesCount, 0)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">活跃品牌数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{brands.filter(brand => brand.status === 'active').length}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>品牌列表</CardTitle>
          <CardDescription>管理企业所有品牌形象和视觉标准</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>品牌名称</TableHead>
                <TableHead>Logo</TableHead>
                <TableHead>品牌颜色</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>应用门店</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {brands.map((brand) => (
                <TableRow key={brand.id}>
                  <TableCell className="font-medium">{brand.name}</TableCell>
                  <TableCell>
                    <div className="w-10 h-10 rounded-md bg-gray-100 flex items-center justify-center">
                      <ImageIcon className="h-6 w-6 text-gray-500" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <div 
                        className="w-6 h-6 rounded-full" 
                        style={{backgroundColor: brand.primaryColor}} 
                        title="主色"
                      />
                      <div 
                        className="w-6 h-6 rounded-full" 
                        style={{backgroundColor: brand.secondaryColor}}
                        title="辅色"
                      />
                    </div>
                  </TableCell>
                  <TableCell>{brand.createdAt}</TableCell>
                  <TableCell>{brand.storesCount}</TableCell>
                  <TableCell>
                    <Badge variant={
                      brand.status === 'active' ? "default" : 
                      brand.status === 'draft' ? "secondary" : "outline"
                    }>
                      {brand.status === 'active' ? '使用中' : 
                       brand.status === 'draft' ? '草稿' : '停用'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" onClick={() => handlePreviewBrand(brand)}>
                      <Eye className="h-4 w-4 mr-1" />
                      预览
                    </Button>
                    <Button variant="ghost" size="sm">编辑</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 添加品牌对话框 */}
      <Dialog open={openAddDialog} onOpenChange={setOpenAddDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>添加品牌</DialogTitle>
            <DialogDescription>
              创建新的品牌标识，填写品牌基本信息。
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddBrand)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>品牌名称</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：瑜伽星空" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>品牌类型</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择品牌类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="main">主品牌</SelectItem>
                        <SelectItem value="sub">子品牌</SelectItem>
                        <SelectItem value="partner">合作品牌</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      选择此品牌在企业品牌体系中的定位
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="primaryColor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>主色调</FormLabel>
                      <div className="flex space-x-2">
                        <FormControl>
                          <Input type="text" placeholder="#4A90E2" {...field} />
                        </FormControl>
                        <Input 
                          type="color" 
                          value={field.value} 
                          onChange={(e) => field.onChange(e.target.value)}
                          className="w-10 p-0 h-10"
                        />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="secondaryColor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>辅助色调</FormLabel>
                      <div className="flex space-x-2">
                        <FormControl>
                          <Input type="text" placeholder="#50E3C2" {...field} />
                        </FormControl>
                        <Input 
                          type="color" 
                          value={field.value} 
                          onChange={(e) => field.onChange(e.target.value)}
                          className="w-10 p-0 h-10"
                        />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>品牌描述</FormLabel>
                    <FormControl>
                      <Textarea placeholder="请简要描述品牌定位和理念" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="guidelines"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>品牌使用指南</FormLabel>
                    <FormControl>
                      <Textarea placeholder="描述品牌标识使用规范和注意事项" {...field} />
                    </FormControl>
                    <FormDescription>
                      提供品牌标识、色彩和字体等元素的使用规范
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="border border-dashed border-gray-300 rounded-md p-4">
                <div className="flex flex-col items-center justify-center space-y-2">
                  <Upload className="h-8 w-8 text-gray-400" />
                  <p className="text-sm text-muted-foreground">上传品牌Logo</p>
                  <p className="text-xs text-muted-foreground">支持PNG, SVG, JPG格式，推荐尺寸 512×512px</p>
                  <Button variant="outline" size="sm">
                    选择文件
                  </Button>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setOpenAddDialog(false)}>取消</Button>
                <Button type="submit">创建品牌</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 品牌预览对话框 */}
      {selectedBrand && (
        <Dialog open={openPreviewDialog} onOpenChange={setOpenPreviewDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>品牌预览 - {selectedBrand.name}</DialogTitle>
              <DialogDescription>
                查看品牌标识和视觉元素
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div className="flex flex-col space-y-2">
                <h3 className="text-sm font-medium">品牌描述</h3>
                <p className="text-sm text-muted-foreground">{selectedBrand.description}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium mb-2">品牌颜色</h3>
                  <div className="flex space-x-3">
                    <div>
                      <div 
                        className="w-12 h-12 rounded-md mb-1" 
                        style={{backgroundColor: selectedBrand.primaryColor}} 
                      />
                      <p className="text-xs text-muted-foreground">{selectedBrand.primaryColor}</p>
                    </div>
                    <div>
                      <div 
                        className="w-12 h-12 rounded-md mb-1" 
                        style={{backgroundColor: selectedBrand.secondaryColor}} 
                      />
                      <p className="text-xs text-muted-foreground">{selectedBrand.secondaryColor}</p>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">Logo 预览</h3>
                  <div className="w-20 h-20 rounded-md bg-gray-100 flex items-center justify-center">
                    <ImageIcon className="h-10 w-10 text-gray-500" />
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium mb-2">品牌指南</h3>
                <p className="text-sm text-muted-foreground">{selectedBrand.guidelines}</p>
              </div>
              
              <div className="border rounded-md p-4">
                <h3 className="text-sm font-medium mb-2">应用示例</h3>
                <div className="grid grid-cols-3 gap-2">
                  <div 
                    className="h-16 rounded-md flex items-center justify-center text-white font-medium"
                    style={{backgroundColor: selectedBrand.primaryColor}}
                  >
                    品牌按钮
                  </div>
                  <div 
                    className="h-16 rounded-md flex items-center justify-center"
                    style={{
                      backgroundColor: 'white', 
                      color: selectedBrand.primaryColor,
                      border: `1px solid ${selectedBrand.primaryColor}`
                    }}
                  >
                    辅助按钮
                  </div>
                  <div 
                    className="h-16 rounded-md flex items-center justify-center text-white"
                    style={{
                      background: `linear-gradient(to right, ${selectedBrand.primaryColor}, ${selectedBrand.secondaryColor})`
                    }}
                  >
                    渐变效果
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setOpenPreviewDialog(false)}>关闭</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
} 