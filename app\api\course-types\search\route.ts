import { NextRequest, NextResponse } from 'next/server';
import { courseTypeService } from '@/services/course-type-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const keyword = searchParams.get('q') || '';
    
    if (!keyword) {
      return NextResponse.json({
        code: 400,
        msg: '搜索关键词不能为空',
      }, { status: 400 });
    }
    
    // 使用服务层进行搜索
    const results = courseTypeService.getAll({ keyword });
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: {
        list: results,
        total: results.length,
        keyword
      }
    });
  } catch (error) {
    console.error('搜索课程类型错误:', error);
    return NextResponse.json(
      { code: 500, msg: '搜索失败', error: String(error) },
      { status: 500 }
    );
  }
} 