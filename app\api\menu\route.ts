import { NextResponse } from "next/server";

export async function GET() {
  try {
    // 模拟菜单数据
    const menuData = [
      {
        id: "dashboard",
        name: "首页",
        href: "/dashboard",
        icon: "LayoutDashboard"
      },
      {
        id: "stores",
        name: "门店管理",
        icon: "Store",
        children: [
          { id: "stores-list", name: "门店列表", href: "/stores" },
          { id: "stores-settings", name: "门店信息设置", href: "/stores/settings" },
          { id: "stores-cards", name: "通用卡管理", href: "/stores/cards" },
        ]
      },
      {
        id: "courses",
        name: "课程管理",
        icon: "BookOpen",
        children: [
          { id: "courses-list", name: "课程列表", href: "/courses" },
          { id: "courses-categories", name: "课程分类", href: "/courses/categories" },
          { id: "courses-plans", name: "课程套餐", href: "/courses/plans" },
          { id: "courses-schedule", name: "排课管理", href: "/courses/schedule" },
        ]
      },
      {
        id: "members",
        name: "会员管理",
        icon: "Users",
        children: [
          { id: "members-list", name: "会员列表", href: "/members" },
          { id: "members-cards", name: "会员卡管理", href: "/members/cards" },
          { id: "members-points", name: "积分管理", href: "/members/points" },
          { id: "members-levels", name: "会员等级", href: "/members/levels" },
        ]
      },
      {
        id: "staff",
        name: "员工管理",
        icon: "UserCog",
        children: [
          { id: "staff-list", name: "员工列表", href: "/staff" },
          { id: "staff-roles", name: "角色权限", href: "/staff/roles" },
          { id: "staff-attendance", name: "考勤管理", href: "/staff/attendance" },
          { id: "staff-performance", name: "绩效管理", href: "/staff/performance" },
        ]
      },
      {
        id: "finance",
        name: "财务管理",
        icon: "DollarSign",
        children: [
          { id: "finance-overview", name: "财务概览", href: "/finance" },
          { id: "finance-orders", name: "订单管理", href: "/finance/orders" },
          { id: "finance-payments", name: "支付管理", href: "/finance/payments" },
          { id: "finance-refunds", name: "退款管理", href: "/finance/refunds" },
          { id: "finance-reports", name: "财务报表", href: "/finance/reports" },
        ]
      },
      {
        id: "marketing",
        name: "营销管理",
        icon: "Megaphone",
        children: [
          { id: "marketing-campaigns", name: "营销活动", href: "/marketing/campaigns" },
          { id: "marketing-coupons", name: "优惠券管理", href: "/marketing/coupons" },
          { id: "marketing-promotions", name: "促销活动", href: "/marketing/promotions" },
          { id: "marketing-sms", name: "短信营销", href: "/marketing/sms" },
        ]
      },
      {
        id: "statistics",
        name: "数据统计",
        icon: "BarChart",
        children: [
          { id: "statistics-overview", name: "数据概览", href: "/statistics" },
          { id: "statistics-members", name: "会员分析", href: "/statistics/members" },
          { id: "statistics-courses", name: "课程分析", href: "/statistics/courses" },
          { id: "statistics-operations", name: "运营分析", href: "/statistics/operations" },
        ]
      },
      {
        id: "settings",
        name: "系统设置",
        icon: "Settings",
        children: [
          { id: "settings-general", name: "基础设置", href: "/settings" },
          { id: "settings-appearance", name: "外观设置", href: "/settings/appearance" },
          { id: "settings-security", name: "安全设置", href: "/settings/security" },
          { id: "settings-notifications", name: "通知设置", href: "/settings/notifications" },
          { id: "settings-integrations", name: "第三方集成", href: "/settings/integrations" },
        ]
      }
    ];

    return NextResponse.json(menuData);
  } catch (error) {
    console.error("Failed to fetch menu data:", error);
    return NextResponse.json(
      { error: "Failed to fetch menu data" },
      { status: 500 }
    );
  }
} 