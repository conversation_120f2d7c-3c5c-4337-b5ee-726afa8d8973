"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CoachTable } from "@/components/coach-table"
import { CoachGrid } from "@/components/coaches/coach-grid"
import { Plus, Download, Upload, Filter, LayoutGrid, LayoutList, BarChart2 } from "lucide-react"
import { useState } from "react"
import { AddCoachDialog } from "@/components/coaches/add-coach-dialog"
import { CoachStatsDialog } from "@/components/coaches/coach-stats-dialog"
import { AdvancedFilterDialog } from "@/components/coaches/advanced-filter-dialog"

export default function CoachesPage() {
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")
  const [showAddCoach, setShowAddCoach] = useState(false)
  const [showStats, setShowStats] = useState(false)
  const [showFilter, setShowFilter] = useState(false)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">教练管理</h1>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setViewMode("list")}
            className={viewMode === "list" ? "bg-muted" : ""}
          >
            <LayoutList className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setViewMode("grid")}
            className={viewMode === "grid" ? "bg-muted" : ""}
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => setShowStats(true)}>
            <BarChart2 className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={() => setShowFilter(true)}>
            <Filter className="h-4 w-4" />
          </Button>
          <Button variant="outline" className="hidden md:flex">
            <Upload className="mr-2 h-4 w-4" />
            导入
          </Button>
          <Button variant="outline" className="hidden md:flex">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
          <Button onClick={() => setShowAddCoach(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加教练
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">全部教练</TabsTrigger>
          <TabsTrigger value="active">在职</TabsTrigger>
          <TabsTrigger value="leave">请假中</TabsTrigger>
          <TabsTrigger value="resigned">已离职</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="mt-4">
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="w-full md:w-1/3">
              <Input placeholder="搜索教练姓名、专长" />
            </div>
            <div className="flex flex-1 gap-4">
              <Select defaultValue="all">
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="教练状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">在职</SelectItem>
                  <SelectItem value="leave">请假中</SelectItem>
                  <SelectItem value="resigned">已离职</SelectItem>
                </SelectContent>
              </Select>

              <Select defaultValue="all">
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="专长" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部专长</SelectItem>
                  <SelectItem value="basic">基础瑜伽</SelectItem>
                  <SelectItem value="advanced">高级瑜伽</SelectItem>
                  <SelectItem value="yin">阴瑜伽</SelectItem>
                  <SelectItem value="prenatal">孕产瑜伽</SelectItem>
                  <SelectItem value="aerial">空中瑜伽</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {viewMode === "list" ? <CoachTable /> : <CoachGrid />}
        </TabsContent>
        <TabsContent value="active" className="mt-4">
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="w-full md:w-1/3">
              <Input placeholder="搜索教练姓名、专长" />
            </div>
            <div className="flex flex-1 gap-4">
              <Select defaultValue="all">
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="专长" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部专长</SelectItem>
                  <SelectItem value="basic">基础瑜伽</SelectItem>
                  <SelectItem value="advanced">高级瑜伽</SelectItem>
                  <SelectItem value="yin">阴瑜伽</SelectItem>
                  <SelectItem value="prenatal">孕产瑜伽</SelectItem>
                  <SelectItem value="aerial">空中瑜伽</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {viewMode === "list" ? <CoachTable statusFilter="active" /> : <CoachGrid statusFilter="active" />}
        </TabsContent>
        <TabsContent value="leave" className="mt-4">
          {viewMode === "list" ? <CoachTable statusFilter="leave" /> : <CoachGrid statusFilter="leave" />}
        </TabsContent>
        <TabsContent value="resigned" className="mt-4">
          {viewMode === "list" ? <CoachTable statusFilter="resigned" /> : <CoachGrid statusFilter="resigned" />}
        </TabsContent>
      </Tabs>

      <AddCoachDialog open={showAddCoach} onOpenChange={setShowAddCoach} />
      <CoachStatsDialog open={showStats} onOpenChange={setShowStats} />
      <AdvancedFilterDialog open={showFilter} onOpenChange={setShowFilter} />
    </div>
  )
}

