-- 基础模块：租户管理
CREATE TABLE IF NOT EXISTS `tenants` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_name` VARCHAR(100) NOT NULL COMMENT '租户名称（瑜伽馆品牌）',
  `contact_person` VARCHAR(50) COMMENT '联系人',
  `phone` VARCHAR(20) COMMENT '联系电话',
  `email` VARCHAR(100) COMMENT '邮箱',
  `address` VARCHAR(255) COMMENT '地址',
  `logo_url` VARCHAR(255) COMMENT 'Logo链接',
  `city` VARCHAR(50) DEFAULT NULL COMMENT '城市',
  `province` VARCHAR(50) DEFAULT NULL COMMENT '省份',
  `country` VARCHAR(50) DEFAULT NULL COMMENT '国家',
  `business_license` VARCHAR(50) COMMENT '营业执照',
  `valid_start` DATE NOT NULL COMMENT '有效期开始日期',
  `valid_end` DATE NOT NULL COMMENT '有效期结束日期',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-暂停，-1-注销，-2-过期',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_city (`city`),
  INDEX idx_status (`status`),
  INDEX idx_valid_end (`valid_end`)
);

-- 租户有效期变更记录表
CREATE TABLE IF NOT EXISTS `tenant_validity_logs` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `old_start_date` DATE NOT NULL COMMENT '旧的开始日期',
  `old_end_date` DATE NOT NULL COMMENT '旧的结束日期',
  `new_start_date` DATE NOT NULL COMMENT '新的开始日期',
  `new_end_date` DATE NOT NULL COMMENT '新的结束日期',
  `change_reason` VARCHAR(255) COMMENT '变更原因',
  `operator_id` INT UNSIGNED NOT NULL COMMENT '操作人ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`operator_id`) REFERENCES `employees`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_created_at (`created_at`)
);

-- 自动更新租户状态的触发器
DELIMITER $$
CREATE TRIGGER `update_tenant_status` 
AFTER UPDATE ON `tenants`
FOR EACH ROW
BEGIN
    IF NEW.valid_end < CURDATE() AND NEW.status = 1 THEN
        UPDATE `tenants` SET `status` = -2 WHERE `id` = NEW.id;
    END IF;
END$$
DELIMITER ;

-- 租户续费存储过程
DELIMITER $$
CREATE PROCEDURE `renew_tenant`(
    IN p_tenant_id INT UNSIGNED,
    IN p_new_end_date DATE,
    IN p_reason VARCHAR(255),
    IN p_operator_id INT UNSIGNED
)
BEGIN
    DECLARE old_start DATE;
    DECLARE old_end DATE;
    
    -- 获取当前有效期
    SELECT `valid_start`, `valid_end` 
    INTO old_start, old_end 
    FROM `tenants` 
    WHERE `id` = p_tenant_id;
    
    -- 开启事务
    START TRANSACTION;
    
    -- 更新租户有效期
    UPDATE `tenants` 
    SET `valid_end` = p_new_end_date,
        `status` = 1  -- 续费后状态恢复正常
    WHERE `id` = p_tenant_id;
    
    -- 记录变更日志
    INSERT INTO `tenant_validity_logs` 
        (`tenant_id`, `old_start_date`, `old_end_date`, `new_start_date`, `new_end_date`, `change_reason`, `operator_id`)
    VALUES 
        (p_tenant_id, old_start, old_end, old_start, p_new_end_date, p_reason, p_operator_id);
    
    -- 提交事务
    COMMIT;
END$$
DELIMITER ;

-- 基础模块：门店管理
CREATE TABLE IF NOT EXISTS `stores` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '所属租户ID',
  `store_name` VARCHAR(100) NOT NULL COMMENT '门店名称',
  `contact_person` VARCHAR(50) COMMENT '联系人',
  `phone` VARCHAR(20) COMMENT '联系电话',
  `address` VARCHAR(255) COMMENT '门店地址',
  `business_hours` VARCHAR(100) COMMENT '营业时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-营业中，0-暂停营业，-1-已关闭',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  INDEX idx_tenant_id (`tenant_id`)
);

-- 会员主表（跨租户）
CREATE TABLE IF NOT EXISTS `members` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `member_no` VARCHAR(50) UNIQUE NOT NULL COMMENT '会员编号',
  `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
  `nickname` VARCHAR(50) COMMENT '昵称',
  `gender` TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号码',
  `email` VARCHAR(100) COMMENT '邮箱',
  `birthdate` DATE COMMENT '出生日期',
  `id_card` VARCHAR(20) COMMENT '身份证号',
  `avatar_url` VARCHAR(255) COMMENT '头像链接',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 租户会员关联表
CREATE TABLE IF NOT EXISTS `tenant_members` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `join_date` DATE NOT NULL COMMENT '加入日期',
  `source` VARCHAR(50) COMMENT '会员来源',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-活跃，0-暂停，-1-注销',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_tenant_member` (`tenant_id`, `member_id`),
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_member_id (`member_id`)
);

-- 会员卡类型表（租户级）
CREATE TABLE IF NOT EXISTS `membership_types` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED DEFAULT NULL COMMENT '所属门店ID（NULL表示全局类型）',
  `type_name` VARCHAR(50) NOT NULL COMMENT '卡类型名称',
  `duration` INT NOT NULL COMMENT '有效期（月）',
  `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
  `visit_times` INT DEFAULT NULL COMMENT '可预约次数（NULL表示不限次）',
  `description` TEXT COMMENT '卡类型描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`)
);

-- -- 会员卡表（租户级）
-- CREATE TABLE IF NOT EXISTS `membership_cards` (
--   `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
--   `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
--   `store_id` INT UNSIGNED NOT NULL COMMENT '发卡门店ID',
--   `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
--   `card_no` VARCHAR(50) UNIQUE NOT NULL COMMENT '卡号',
--   `type_id` INT UNSIGNED NOT NULL COMMENT '卡类型ID',
--   `start_date` DATE NOT NULL COMMENT '开始日期',
--   `end_date` DATE NOT NULL COMMENT '结束日期',
--   `remaining_times` INT DEFAULT NULL COMMENT '剩余次数（NULL表示不限次）',
--   `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-暂停，-1-过期，-2-作废',
--   `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
--   FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
--   FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
--   FOREIGN KEY (`type_id`) REFERENCES `membership_types`(`id`),
--   INDEX idx_tenant_id (`tenant_id`),
--   INDEX idx_store_id (`store_id`),
--   INDEX idx_member_id (`member_id`)
-- );
-- 会员卡模块：会员卡实例
CREATE TABLE IF NOT EXISTS `membership_cards` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '发卡门店ID',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `card_no` VARCHAR(50) UNIQUE NOT NULL COMMENT '卡号',
  `type_id` INT UNSIGNED NOT NULL COMMENT '卡类型ID',
  `start_date` DATE NOT NULL COMMENT '开始日期',
  `end_date` DATE NOT NULL COMMENT '结束日期',
  `initial_value` DECIMAL(10,2) DEFAULT 0 COMMENT '初始价值',
  `remaining_value` DECIMAL(10,2) DEFAULT 0 COMMENT '剩余价值',
  `gift_value` DECIMAL(10,2) DEFAULT 0 COMMENT '赠送价值',
  `remaining_times` INT DEFAULT NULL COMMENT '剩余次数（NULL表示不限次）',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-暂停，-1-过期，-2-作废',
  `suspension_start` DATE DEFAULT NULL COMMENT '暂停开始日期',
  `suspension_end` DATE DEFAULT NULL COMMENT '暂停结束日期',
  `suspension_days` INT DEFAULT 0 COMMENT '累计暂停天数',
  `first_used_date` DATE DEFAULT NULL COMMENT '首次使用日期',
  `is_gift_used_first` TINYINT DEFAULT 0 COMMENT '是否优先使用赠送金额：1-是，0-否',
  `last_consumption_time` TIMESTAMP DEFAULT NULL COMMENT '上次消费时间',
  `expiry_type` TINYINT DEFAULT 1 COMMENT '过期类型：1-按截止日期，2-按激活天数，3-按首次使用天数',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`type_id`) REFERENCES `membership_types`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_status (`status`),
  INDEX idx_end_date (`end_date`)
);
-- 员工表（租户级）
CREATE TABLE IF NOT EXISTS `employees` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `employee_no` VARCHAR(50) UNIQUE NOT NULL COMMENT '员工编号',
  `real_name` VARCHAR(50) NOT NULL COMMENT '真实姓名',
  `phone` VARCHAR(20) NOT NULL COMMENT '手机号码',
  `email` VARCHAR(100) COMMENT '邮箱',
  `password_hash` VARCHAR(100) NOT NULL COMMENT '密码哈希',
  `role_id` INT UNSIGNED DEFAULT NULL COMMENT '角色ID',
  `avatar_url` VARCHAR(255) COMMENT '头像链接', -- 新增字段
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-在职，0-休假，-1-离职',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`),
  INDEX idx_tenant_id (`tenant_id`)
);

-- 员工门店关联表
CREATE TABLE IF NOT EXISTS `employee_stores` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `employee_id` INT UNSIGNED NOT NULL COMMENT '员工ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '门店ID',
  `is_primary` TINYINT DEFAULT 0 COMMENT '是否主门店',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_employee_store` (`employee_id`, `store_id`),
  FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  INDEX idx_employee_id (`employee_id`),
  INDEX idx_store_id (`store_id`)
);

-- 角色表（租户级）
CREATE TABLE IF NOT EXISTS `roles` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `role_name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `description` VARCHAR(255) COMMENT '角色描述',
  `is_system` TINYINT DEFAULT 0 COMMENT '是否系统内置角色',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  INDEX idx_tenant_id (`tenant_id`)
);

-- 权限表（系统级）
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `permission_code` VARCHAR(50) UNIQUE NOT NULL COMMENT '权限代码',
  `permission_name` VARCHAR(100) NOT NULL COMMENT '权限名称',
  `module` VARCHAR(50) COMMENT '所属模块',
  `description` VARCHAR(255) COMMENT '权限描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `role_id` INT UNSIGNED NOT NULL COMMENT '角色ID',
  `permission_id` INT UNSIGNED NOT NULL COMMENT '权限ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_role_permission` (`role_id`, `permission_id`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`),
  FOREIGN KEY (`permission_id`) REFERENCES `permissions`(`id`),
  INDEX idx_role_id (`role_id`),
  INDEX idx_permission_id (`permission_id`)
);

-- 门店数据权限表
CREATE TABLE IF NOT EXISTS `employee_store_permissions` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `employee_id` INT UNSIGNED NOT NULL COMMENT '员工ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '门店ID',
  `permission_type` TINYINT NOT NULL COMMENT '权限类型：1-查看，2-管理',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_employee_store` (`employee_id`, `store_id`),
  FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  INDEX idx_employee_id (`employee_id`),
  INDEX idx_store_id (`store_id`)
);

-- 课程类型表（租户级）
CREATE TABLE IF NOT EXISTS `course_types` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED DEFAULT NULL COMMENT '所属门店ID（NULL表示全局类型）',
  `type_name` VARCHAR(50) NOT NULL COMMENT '课程类型名称',
  `description` TEXT COMMENT '课程描述',
  `duration` INT NOT NULL COMMENT '课程时长（分钟）',
  `difficulty` TINYINT DEFAULT 2 COMMENT '难度：1-初级，2-中级，3-高级',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`)
);

-- 教练表（租户级）
CREATE TABLE IF NOT EXISTS `coaches` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `employee_id` INT UNSIGNED NOT NULL COMMENT '关联员工ID',
  `specialty` VARCHAR(100) COMMENT '专长',
  `introduction` TEXT COMMENT '教练简介',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-在职，0-休假，-1-离职',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_employee_id (`employee_id`)
);

-- 教室管理
CREATE TABLE IF NOT EXISTS `classrooms` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '所属门店ID',
  `room_name` VARCHAR(50) NOT NULL COMMENT '教室名称',
  `location` VARCHAR(100) COMMENT '教室位置',
  `capacity` INT NOT NULL COMMENT '最大容量',
  `equipment` VARCHAR(255) COMMENT '教室设备',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-可用，0-维护中，-1-已停用',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_status (`status`)
);

-- 课程时间表（租户级）
CREATE TABLE IF NOT EXISTS `course_schedules` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '所属门店ID',
  `course_type_id` INT UNSIGNED NOT NULL COMMENT '课程类型ID',
  `coach_id` INT UNSIGNED NOT NULL COMMENT '教练ID',
  `classroom_id` INT UNSIGNED NOT NULL COMMENT '教室ID',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME NOT NULL COMMENT '结束时间',
  `max_capacity` INT NOT NULL COMMENT '最大容量',
  `current_capacity` INT DEFAULT 0 COMMENT '当前已预约人数',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-可预约，0-已满，-1-取消',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  FOREIGN KEY (`course_type_id`) REFERENCES `course_types`(`id`),
  FOREIGN KEY (`coach_id`) REFERENCES `coaches`(`id`),
  FOREIGN KEY (`classroom_id`) REFERENCES `classrooms`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_course_type_id (`course_type_id`),
  INDEX idx_coach_id (`coach_id`),
  INDEX idx_start_time (`start_time`)
);

-- 会员预约表（租户级）
CREATE TABLE IF NOT EXISTS `course_bookings` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '所属门店ID',
  `schedule_id` INT UNSIGNED NOT NULL COMMENT '课程时间表ID',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `card_id` INT UNSIGNED DEFAULT NULL COMMENT '使用的会员卡ID',
  `booking_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '预约时间',
  `operator_id` INT UNSIGNED DEFAULT NULL COMMENT '操作人ID', -- 新增字段
  `booking_channel` TINYINT DEFAULT 1 COMMENT '预约渠道：1-前台，2-线上APP，3-小程序，4-电话', -- 新增字段
 `status` TINYINT DEFAULT 1 COMMENT '状态：1-已预约，0-已取消，2-已完成，3-已缺席，4-已签到',
  `cancel_reason` VARCHAR(255) DEFAULT NULL COMMENT '取消原因',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_booking` (`tenant_id`, `schedule_id`, `member_id`),
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  FOREIGN KEY (`schedule_id`) REFERENCES `course_schedules`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`card_id`) REFERENCES `membership_cards`(`id`),
  FOREIGN KEY (`operator_id`) REFERENCES `employees`(`id`), -- 新增外键
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_schedule_id (`schedule_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_operator_id (`operator_id`) -- 新增索引
);
-- -- 课程管理模块：会员预约
-- CREATE TABLE IF NOT EXISTS `course_bookings` (
--   `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
--   `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
--   `store_id` INT UNSIGNED NOT NULL COMMENT '所属门店ID',
--   `schedule_id` INT UNSIGNED NOT NULL COMMENT '课程时间表ID',
--   `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
--   `card_id` INT UNSIGNED DEFAULT NULL COMMENT '使用的会员卡ID',
--   `booking_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '预约时间',
--   `status` TINYINT DEFAULT 1 COMMENT '状态：1-已预约，0-已取消，2-已完成，3-已缺席，4-已签到',
--   `cancel_reason` VARCHAR(255) DEFAULT NULL COMMENT '取消原因',
--   `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   UNIQUE KEY `uniq_booking` (`tenant_id`, `schedule_id`, `member_id`),
--   FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
--   FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
--   FOREIGN KEY (`schedule_id`) REFERENCES `course_schedules`(`id`),
--   FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
--   FOREIGN KEY (`card_id`) REFERENCES `membership_cards`(`id`),
--   INDEX idx_tenant_id (`tenant_id`),
--   INDEX idx_store_id (`store_id`),
--   INDEX idx_schedule_id (`schedule_id`),
--   INDEX idx_member_id (`member_id`),
--   INDEX idx_status (`status`)
-- );

-- -- 消费记录表（租户级）
-- CREATE TABLE IF NOT EXISTS `consumptions` (
--   `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
--   `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
--   `store_id` INT UNSIGNED NOT NULL COMMENT '消费门店ID',
--   `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
--   `card_id` INT UNSIGNED DEFAULT NULL COMMENT '关联会员卡ID',
--   `transaction_type` TINYINT NOT NULL COMMENT '交易类型：1-购卡，2-充值，3-消费',
--   `amount` DECIMAL(10,2) NOT NULL COMMENT '金额',
--   `description` VARCHAR(255) COMMENT '交易描述',
--   `transaction_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '交易时间',
--   `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--   `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
--   FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
--   FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
--   FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
--   FOREIGN KEY (`card_id`) REFERENCES `membership_cards`(`id`),
--   INDEX idx_tenant_id (`tenant_id`),
--   INDEX idx_store_id (`store_id`),
--   INDEX idx_member_id (`member_id`),
--   INDEX idx_transaction_time (`transaction_time`)
-- );
-- 财务管理模块：消费记录
CREATE TABLE IF NOT EXISTS `consumptions` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '消费门店ID',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `card_id` INT UNSIGNED DEFAULT NULL COMMENT '关联会员卡ID',
  `transaction_type` TINYINT NOT NULL COMMENT '交易类型：1-购卡，2-充值，3-消费，4-退款',
  `amount` DECIMAL(10,2) NOT NULL COMMENT '金额',
  `value_consumed` DECIMAL(10,2) DEFAULT 0 COMMENT '本次消费的卡价值',
  `actual_amount` DECIMAL(10,2) DEFAULT 0 COMMENT '实际支付金额',
  `gift_amount` DECIMAL(10,2) DEFAULT 0 COMMENT '使用赠送金额',
  `description` VARCHAR(255) COMMENT '交易描述',
  `transaction_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '交易时间',
  `operator_id` INT UNSIGNED DEFAULT NULL COMMENT '操作员工ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`card_id`) REFERENCES `membership_cards`(`id`),
  FOREIGN KEY (`operator_id`) REFERENCES `employees`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_card_id (`card_id`),
  INDEX idx_transaction_time (`transaction_time`),
  INDEX idx_transaction_type (`transaction_type`)
);

-- 订单主表
CREATE TABLE IF NOT EXISTS `orders` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '门店ID',
  `order_no` VARCHAR(50) UNIQUE NOT NULL COMMENT '订单编号',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `employee_id` INT UNSIGNED DEFAULT NULL COMMENT '员工ID（销售员）',
  `order_type` TINYINT NOT NULL COMMENT '订单类型：1-购卡，2-充值，3-课程包，4-商品，5-组合',
  `total_amount` DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
  `discount_amount` DECIMAL(10,2) DEFAULT 0 COMMENT '折扣金额',
  `actual_amount` DECIMAL(10,2) NOT NULL COMMENT '实际支付金额',
  `payment_method` TINYINT DEFAULT 1 COMMENT '支付方式：1-现金，2-微信，3-支付宝，4-银行卡，5-储值卡',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-待支付，2-已支付，3-已取消，4-部分退款，5-已退款',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `paid_at` TIMESTAMP DEFAULT NULL COMMENT '支付时间',
  `cancelled_at` TIMESTAMP DEFAULT NULL COMMENT '取消时间',
  `refunded_at` TIMESTAMP DEFAULT NULL COMMENT '退款时间',
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_order_no (`order_no`),
  INDEX idx_status (`status`),
  INDEX idx_created_at (`created_at`)
);

-- 订单明细表（支持混合订单）
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `order_id` INT UNSIGNED NOT NULL COMMENT '订单ID',
  `item_type` TINYINT NOT NULL COMMENT '项目类型：1-会员卡，2-课程包，3-商品，4-服务',
  `item_id` INT UNSIGNED NOT NULL COMMENT '项目ID（会员卡类型ID/课程包ID/商品ID等）',
  `quantity` INT DEFAULT 1 COMMENT '数量',
  `price` DECIMAL(10,2) NOT NULL COMMENT '单价',
  `discount` DECIMAL(5,2) DEFAULT 1 COMMENT '折扣率（如0.9表示9折）',
  `amount` DECIMAL(10,2) NOT NULL COMMENT '金额',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '项目描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`),
  INDEX idx_order_id (`order_id`),
  INDEX idx_item_type (`item_type`)
);

-- 课程包定义表
CREATE TABLE IF NOT EXISTS `course_packages` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `package_name` VARCHAR(100) NOT NULL COMMENT '课程包名称',
  `description` TEXT COMMENT '课程包描述',
  `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
  `course_count` INT NOT NULL COMMENT '包含课程数量',
  `valid_days` INT DEFAULT 30 COMMENT '有效期天数',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-停用',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_status (`status`)
);

-- 课程包包含的课程类型
CREATE TABLE IF NOT EXISTS `package_course_types` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `package_id` INT UNSIGNED NOT NULL COMMENT '课程包ID',
  `course_type_id` INT UNSIGNED NOT NULL COMMENT '课程类型ID',
  `course_count` INT DEFAULT 1 COMMENT '该类型课程数量',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_package_course` (`package_id`, `course_type_id`),
  FOREIGN KEY (`package_id`) REFERENCES `course_packages`(`id`),
  FOREIGN KEY (`course_type_id`) REFERENCES `course_types`(`id`),
  INDEX idx_package_id (`package_id`),
  INDEX idx_course_type_id (`course_type_id`)
);

-- 会员课程包记录表
CREATE TABLE IF NOT EXISTS `member_course_packages` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `package_id` INT UNSIGNED NOT NULL COMMENT '课程包ID',
  `order_id` INT UNSIGNED NOT NULL COMMENT '关联订单ID',
  `start_date` DATE NOT NULL COMMENT '开始日期',
  `end_date` DATE NOT NULL COMMENT '结束日期',
  `remaining_courses` INT NOT NULL COMMENT '剩余课程数',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-有效，0-暂停，-1-过期，-2-已用完',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`package_id`) REFERENCES `course_packages`(`id`),
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_status (`status`),
  INDEX idx_end_date (`end_date`)
);

-- 课时费设置表
CREATE TABLE IF NOT EXISTS `course_fee_settings` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `course_type_id` INT UNSIGNED NOT NULL COMMENT '课程类型ID',
  `coach_id` INT UNSIGNED DEFAULT NULL COMMENT '教练ID（NULL表示通用设置）',
  `classroom_id` INT UNSIGNED DEFAULT NULL COMMENT '教室ID（NULL表示通用设置）',
  `fee_type` TINYINT DEFAULT 1 COMMENT '费用类型：1-固定金额，2-百分比',
  `amount` DECIMAL(10,2) DEFAULT 0 COMMENT '金额或百分比',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-停用',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`course_type_id`) REFERENCES `course_types`(`id`),
  FOREIGN KEY (`coach_id`) REFERENCES `coaches`(`id`),
  FOREIGN KEY (`classroom_id`) REFERENCES `classrooms`(`id`),
  UNIQUE KEY `uniq_course_coach_classroom` (`tenant_id`, `course_type_id`, `coach_id`, `classroom_id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_course_type_id (`course_type_id`),
  INDEX idx_coach_id (`coach_id`)
);

-- 会员卡高级设置表
CREATE TABLE IF NOT EXISTS `membership_card_settings` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `type_id` INT UNSIGNED NOT NULL COMMENT '卡类型ID',
  `allowed_course_types` VARCHAR(255) DEFAULT NULL COMMENT '允许预约的课程类型ID列表，逗号分隔',
  `allowed_classrooms` VARCHAR(255) DEFAULT NULL COMMENT '允许使用的教室ID列表，逗号分隔',
  `max_booking_days` INT DEFAULT 7 COMMENT '最多可预约未来天数',
  `min_booking_hours` INT DEFAULT 2 COMMENT '最小预约提前小时数',
  `max_concurrent_bookings` INT DEFAULT 1 COMMENT '最大同时预约数',
  `is_transferable` TINYINT DEFAULT 0 COMMENT '是否可转让：1-是，0-否',
  `is_refundable` TINYINT DEFAULT 0 COMMENT '是否可退款：1-是，0-否',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`type_id`) REFERENCES `membership_types`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_type_id (`type_id`)
);

-- 课程高级设置表
CREATE TABLE IF NOT EXISTS `course_advanced_settings` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `course_type_id` INT UNSIGNED NOT NULL COMMENT '课程类型ID',
  `is_auto_schedule` TINYINT DEFAULT 0 COMMENT '是否自动排课：1-是，0-否',
  `schedule_days` VARCHAR(50) DEFAULT NULL COMMENT '排课星期（如1,3,5表示周一、周三、周五）',
  `schedule_times` VARCHAR(255) DEFAULT NULL COMMENT '排课时间段（如09:00-10:30,15:00-16:30）',
  `booking_cutoff_hours` INT DEFAULT 2 COMMENT '预约截止小时数',
  `late_cancellation_penalty` DECIMAL(5,2) DEFAULT 0 COMMENT '迟到取消违约金比例',
  `no_show_penalty` DECIMAL(5,2) DEFAULT 0 COMMENT '缺席违约金比例',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`course_type_id`) REFERENCES `course_types`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_course_type_id (`course_type_id`)
);

-- 会员积分表
CREATE TABLE IF NOT EXISTS `member_points` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `order_id` INT UNSIGNED DEFAULT NULL COMMENT '关联订单ID',
  `points` INT NOT NULL COMMENT '积分数量（正数为增加，负数为减少）',
  `reason` VARCHAR(255) NOT NULL COMMENT '积分原因',
  `expiry_date` DATE DEFAULT NULL COMMENT '过期日期',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-有效，0-已使用，-1-已过期',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_expiry_date (`expiry_date`),
  INDEX idx_status (`status`)
);

-- 积分规则表
CREATE TABLE IF NOT EXISTS `points_rules` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `rule_type` TINYINT NOT NULL COMMENT '规则类型：1-消费积分，2-注册积分，3-推荐积分，4-课程完成积分',
  `amount_per_point` DECIMAL(10,2) DEFAULT 1 COMMENT '每积分对应金额（消费积分用）',
  `points` INT DEFAULT 0 COMMENT '积分数量',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-停用',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_rule_type (`rule_type`),
  INDEX idx_status (`status`)
);

-- 会员等级表
CREATE TABLE IF NOT EXISTS `member_levels` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `level_name` VARCHAR(50) NOT NULL COMMENT '等级名称',
  `level_value` INT NOT NULL COMMENT '等级值，数值越大等级越高',
  `min_points` INT DEFAULT 0 COMMENT '最低积分要求',
  `max_discount_rate` DECIMAL(5,2) DEFAULT 1 COMMENT '最大折扣率（如0.9表示9折）',
  `description` VARCHAR(255) COMMENT '等级描述',
  `is_default` TINYINT DEFAULT 0 COMMENT '是否默认等级：1-是，0-否',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  UNIQUE KEY `uniq_tenant_level_value` (`tenant_id`, `level_value`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_level_value (`level_value`)
);

-- 会员等级记录表
CREATE TABLE IF NOT EXISTS `member_level_logs` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `old_level_id` INT UNSIGNED DEFAULT NULL COMMENT '旧等级ID',
  `new_level_id` INT UNSIGNED NOT NULL COMMENT '新等级ID',
  `change_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  `change_reason` VARCHAR(255) COMMENT '变更原因',
  `operator_id` INT UNSIGNED DEFAULT NULL COMMENT '操作人ID',
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`old_level_id`) REFERENCES `member_levels`(`id`),
  FOREIGN KEY (`new_level_id`) REFERENCES `member_levels`(`id`),
  FOREIGN KEY (`operator_id`) REFERENCES `employees`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_change_time (`change_time`)
);

-- 会员标签组表
CREATE TABLE IF NOT EXISTS `member_tag_groups` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `group_name` VARCHAR(50) NOT NULL COMMENT '标签组名称',
  `description` VARCHAR(255) COMMENT '标签组描述',
  `is_system` TINYINT DEFAULT 0 COMMENT '是否系统内置：1-是，0-否',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  INDEX idx_tenant_id (`tenant_id`)
);

-- 会员标签表
CREATE TABLE IF NOT EXISTS `member_tags` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `group_id` INT UNSIGNED NOT NULL COMMENT '所属标签组ID',
  `tag_name` VARCHAR(50) NOT NULL COMMENT '标签名称',
  `description` VARCHAR(255) COMMENT '标签描述',
  `color` VARCHAR(20) DEFAULT NULL COMMENT '标签颜色',
  `is_system` TINYINT DEFAULT 0 COMMENT '是否系统内置：1-是，0-否',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`group_id`) REFERENCES `member_tag_groups`(`id`),
  UNIQUE KEY `uniq_tenant_group_tag` (`tenant_id`, `group_id`, `tag_name`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_group_id (`group_id`)
);

-- 会员标签关联表
CREATE TABLE IF NOT EXISTS `member_tag_relations` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `tag_id` INT UNSIGNED NOT NULL COMMENT '标签ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `created_by` INT UNSIGNED DEFAULT NULL COMMENT '创建人ID',
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`tag_id`) REFERENCES `member_tags`(`id`),
  FOREIGN KEY (`created_by`) REFERENCES `employees`(`id`),
  UNIQUE KEY `uniq_member_tag` (`tenant_id`, `member_id`, `tag_id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_tag_id (`tag_id`)
);

-- 商品表（用于零售商品管理）
CREATE TABLE IF NOT EXISTS `products` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED DEFAULT NULL COMMENT '所属门店ID（NULL表示全局商品）',
  `product_code` VARCHAR(50) UNIQUE NOT NULL COMMENT '商品编码',
  `product_name` VARCHAR(100) NOT NULL COMMENT '商品名称',
  `category_id` INT UNSIGNED DEFAULT NULL COMMENT '分类ID',
  `brand` VARCHAR(50) DEFAULT NULL COMMENT '品牌',
  `purchase_price` DECIMAL(10,2) DEFAULT 0 COMMENT '采购价',
  `retail_price` DECIMAL(10,2) NOT NULL COMMENT '零售价',
  `stock` INT DEFAULT 0 COMMENT '库存',
  `min_stock` INT DEFAULT 0 COMMENT '最低库存',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-在售，0-下架',
  `description` TEXT COMMENT '商品描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  FOREIGN KEY (`category_id`) REFERENCES `product_categories`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_category_id (`category_id`),
  INDEX idx_status (`status`)
);

-- 商品分类表
CREATE TABLE IF NOT EXISTS `product_categories` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `parent_id` INT UNSIGNED DEFAULT NULL COMMENT '父分类ID',
  `category_name` VARCHAR(50) NOT NULL COMMENT '分类名称',
  `description` VARCHAR(255) COMMENT '分类描述',
  `sort_order` INT DEFAULT 0 COMMENT '排序顺序',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`parent_id`) REFERENCES `product_categories`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_parent_id (`parent_id`)
);

-- 库存记录表
CREATE TABLE IF NOT EXISTS `inventory_logs` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '门店ID',
  `product_id` INT UNSIGNED NOT NULL COMMENT '商品ID',
  `before_quantity` INT NOT NULL COMMENT '变更前数量',
  `quantity` INT NOT NULL COMMENT '变更数量（正数为增加，负数为减少）',
  `after_quantity` INT NOT NULL COMMENT '变更后数量',
  `reason` TINYINT NOT NULL COMMENT '变更原因：1-采购入库，2-销售出库，3-调拨，4-盘点',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '备注',
  `operator_id` INT UNSIGNED NOT NULL COMMENT '操作人ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  FOREIGN KEY (`product_id`) REFERENCES `products`(`id`),
  FOREIGN KEY (`operator_id`) REFERENCES `employees`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_product_id (`product_id`),
  INDEX idx_created_at (`created_at`)
);

-- 统计报表配置表
CREATE TABLE IF NOT EXISTS `report_configs` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `report_code` VARCHAR(50) NOT NULL COMMENT '报表代码',
  `report_name` VARCHAR(100) NOT NULL COMMENT '报表名称',
  `report_type` TINYINT NOT NULL COMMENT '报表类型：1-销售，2-会员，3-课程，4-库存',
  `is_system` TINYINT DEFAULT 0 COMMENT '是否系统内置：1-是，0-否',
  `config_data` TEXT COMMENT '报表配置数据',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  UNIQUE KEY `uniq_tenant_report_code` (`tenant_id`, `report_code`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_report_type (`report_type`)
);

-- 系统设置表
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `setting_key` VARCHAR(50) NOT NULL COMMENT '设置键',
  `setting_value` TEXT COMMENT '设置值',
  `setting_type` TINYINT DEFAULT 1 COMMENT '设置类型：1-文本，2-数字，3-布尔，4-数组，5-对象',
  `description` VARCHAR(255) COMMENT '设置描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  UNIQUE KEY `uniq_tenant_setting_key` (`tenant_id`, `setting_key`),
  INDEX idx_tenant_id (`tenant_id`)
);

-- 基础模块：教室管理
CREATE TABLE IF NOT EXISTS `classrooms` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '所属门店ID',
  `room_name` VARCHAR(50) NOT NULL COMMENT '教室名称',
  `location` VARCHAR(100) COMMENT '教室位置',
  `capacity` INT NOT NULL COMMENT '最大容量',
  `equipment` VARCHAR(255) COMMENT '教室设备',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-可用，0-维护中，-1-已停用',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_status (`status`)
);

-- 会员卡高级设置表
CREATE TABLE IF NOT EXISTS `membership_card_settings` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `type_id` INT UNSIGNED NOT NULL COMMENT '卡类型ID',
  `allowed_course_types` VARCHAR(255) DEFAULT NULL COMMENT '允许预约的课程类型ID列表，逗号分隔',
  `allowed_classrooms` VARCHAR(255) DEFAULT NULL COMMENT '允许使用的教室ID列表，逗号分隔',
  `max_booking_days` INT DEFAULT 7 COMMENT '最多可预约未来天数',
  `min_booking_hours` INT DEFAULT 2 COMMENT '最小预约提前小时数',
  `max_concurrent_bookings` INT DEFAULT 1 COMMENT '最大同时预约数',
  `is_transferable` TINYINT DEFAULT 0 COMMENT '是否可转让：1-是，0-否',
  `is_refundable` TINYINT DEFAULT 0 COMMENT '是否可退款：1-是，0-否',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`type_id`) REFERENCES `membership_types`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_type_id (`type_id`)
);

-- 会员卡与课程关联表（用于限制特定卡类型可预约的课程）
CREATE TABLE IF NOT EXISTS `card_course_relations` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `card_type_id` INT UNSIGNED NOT NULL COMMENT '卡类型ID',
  `course_type_id` INT UNSIGNED NOT NULL COMMENT '课程类型ID',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_card_course` (`tenant_id`, `card_type_id`, `course_type_id`),
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`card_type_id`) REFERENCES `membership_types`(`id`),
  FOREIGN KEY (`course_type_id`) REFERENCES `course_types`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_card_type_id (`card_type_id`),
  INDEX idx_course_type_id (`course_type_id`)
);

-- 订单主表
CREATE TABLE IF NOT EXISTS `orders` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `store_id` INT UNSIGNED NOT NULL COMMENT '门店ID',
  `order_no` VARCHAR(50) UNIQUE NOT NULL COMMENT '订单编号',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `employee_id` INT UNSIGNED DEFAULT NULL COMMENT '员工ID（销售员）',
  `order_type` TINYINT NOT NULL COMMENT '订单类型：1-购卡，2-充值，3-课程包，4-商品，5-组合',
  `total_amount` DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
  `discount_amount` DECIMAL(10,2) DEFAULT 0 COMMENT '折扣金额',
  `actual_amount` DECIMAL(10,2) NOT NULL COMMENT '实际支付金额',
  `payment_method` TINYINT DEFAULT 1 COMMENT '支付方式：1-现金，2-微信，3-支付宝，4-银行卡，5-储值卡',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-待支付，2-已支付，3-已取消，4-部分退款，5-已退款',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `paid_at` TIMESTAMP DEFAULT NULL COMMENT '支付时间',
  `cancelled_at` TIMESTAMP DEFAULT NULL COMMENT '取消时间',
  `refunded_at` TIMESTAMP DEFAULT NULL COMMENT '退款时间',
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`store_id`) REFERENCES `stores`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`employee_id`) REFERENCES `employees`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_store_id (`store_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_order_no (`order_no`),
  INDEX idx_status (`status`),
  INDEX idx_created_at (`created_at`)
);

-- 订单明细表（支持混合订单）
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `order_id` INT UNSIGNED NOT NULL COMMENT '订单ID',
  `item_type` TINYINT NOT NULL COMMENT '项目类型：1-会员卡，2-课程包，3-商品，4-服务',
  `item_id` INT UNSIGNED NOT NULL COMMENT '项目ID（会员卡类型ID/课程包ID/商品ID等）',
  `quantity` INT DEFAULT 1 COMMENT '数量',
  `price` DECIMAL(10,2) NOT NULL COMMENT '单价',
  `discount` DECIMAL(5,2) DEFAULT 1 COMMENT '折扣率（如0.9表示9折）',
  `amount` DECIMAL(10,2) NOT NULL COMMENT '金额',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '项目描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`),
  INDEX idx_order_id (`order_id`),
  INDEX idx_item_type (`item_type`)
);

-- 课程包定义表
CREATE TABLE IF NOT EXISTS `course_packages` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `package_name` VARCHAR(100) NOT NULL COMMENT '课程包名称',
  `description` TEXT COMMENT '课程包描述',
  `price` DECIMAL(10,2) NOT NULL COMMENT '价格',
  `course_count` INT NOT NULL COMMENT '包含课程数量',
  `valid_days` INT DEFAULT 30 COMMENT '有效期天数',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-停用',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_status (`status`)
);

-- 课程包包含的课程类型
CREATE TABLE IF NOT EXISTS `package_course_types` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `package_id` INT UNSIGNED NOT NULL COMMENT '课程包ID',
  `course_type_id` INT UNSIGNED NOT NULL COMMENT '课程类型ID',
  `course_count` INT DEFAULT 1 COMMENT '该类型课程数量',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_package_course` (`package_id`, `course_type_id`),
  FOREIGN KEY (`package_id`) REFERENCES `course_packages`(`id`),
  FOREIGN KEY (`course_type_id`) REFERENCES `course_types`(`id`),
  INDEX idx_package_id (`package_id`),
  INDEX idx_course_type_id (`course_type_id`)
);

-- 会员课程包记录表
CREATE TABLE IF NOT EXISTS `member_course_packages` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `member_id` INT UNSIGNED NOT NULL COMMENT '会员ID',
  `package_id` INT UNSIGNED NOT NULL COMMENT '课程包ID',
  `order_id` INT UNSIGNED NOT NULL COMMENT '关联订单ID',
  `start_date` DATE NOT NULL COMMENT '开始日期',
  `end_date` DATE NOT NULL COMMENT '结束日期',
  `remaining_courses` INT NOT NULL COMMENT '剩余课程数',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-有效，0-暂停，-1-过期，-2-已用完',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`member_id`) REFERENCES `members`(`id`),
  FOREIGN KEY (`package_id`) REFERENCES `course_packages`(`id`),
  FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_member_id (`member_id`),
  INDEX idx_status (`status`),
  INDEX idx_end_date (`end_date`)
);

-- 课时费设置表
CREATE TABLE IF NOT EXISTS `course_fee_settings` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `course_type_id` INT UNSIGNED NOT NULL COMMENT '课程类型ID',
  `coach_id` INT UNSIGNED DEFAULT NULL COMMENT '教练ID（NULL表示通用设置）',
  `classroom_id` INT UNSIGNED DEFAULT NULL COMMENT '教室ID（NULL表示通用设置）',
  `fee_type` TINYINT DEFAULT 1 COMMENT '费用类型：1-固定金额，2-百分比',
  `amount` DECIMAL(10,2) DEFAULT 0 COMMENT '金额或百分比',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-停用',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`course_type_id`) REFERENCES `course_types`(`id`),
  FOREIGN KEY (`coach_id`) REFERENCES `coaches`(`id`),
  FOREIGN KEY (`classroom_id`) REFERENCES `classrooms`(`id`),
  UNIQUE KEY `uniq_course_coach_classroom` (`tenant_id`, `course_type_id`, `coach_id`, `classroom_id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_course_type_id (`course_type_id`),
  INDEX idx_coach_id (`coach_id`)
);

-- 课程高级设置表
CREATE TABLE IF NOT EXISTS `course_advanced_settings` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `course_type_id` INT UNSIGNED NOT NULL COMMENT '课程类型ID',
  `is_auto_schedule` TINYINT DEFAULT 0 COMMENT '是否自动排课：1-是，0-否',
  `schedule_days` VARCHAR(50) DEFAULT NULL COMMENT '排课星期（如1,3,5表示周一、周三、周五）',
  `schedule_times` VARCHAR(255) DEFAULT NULL COMMENT '排课时间段（如09:00-10:30,15:00-16:30）',
  `booking_cutoff_hours` INT DEFAULT 2 COMMENT '预约截止小时数',
  `late_cancellation_penalty` DECIMAL(5,2) DEFAULT 0 COMMENT '迟到取消违约金比例',
  `no_show_penalty` DECIMAL(5,2) DEFAULT 0 COMMENT '缺席违约金比例',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`tenant_id`) REFERENCES `tenants`(`id`),
  FOREIGN KEY (`course_type_id`) REFERENCES `course_types`(`id`),
  INDEX idx_tenant_id (`tenant_id`),
  INDEX idx_course_type_id (`course_type_id`)
);
-- 操作日志表
CREATE TABLE IF NOT EXISTS `operation_logs` (
  `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `tenant_id` INT UNSIGNED NOT NULL COMMENT '租户ID',
  `operator_id` INT UNSIGNED NOT NULL COMMENT '操作人ID',
  `operation_type` VARCHAR(50) NOT NULL COMMENT '操作类型',
  `operation_desc` TEXT COMMENT '操作描述',
  `operation_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `ip_address` VARCHAR(50) COMMENT 'IP地址',
  `user_agent` VARCHAR(255) COMMENT '用户代理',
  INDEX idx_tenant_time (`tenant_id`, `operation_time`),
  INDEX idx_operator_time (`operator_id`, `operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
PARTITION BY RANGE (YEAR(operation_time) * 100 + MONTH(operation_time)) (
  PARTITION p_202301 VALUES LESS THAN (202302),
  PARTITION p_202302 VALUES LESS THAN (202303),
  PARTITION p_max VALUES LESS THAN MAXVALUE
);

-- 操作日志归档表
CREATE TABLE IF NOT EXISTS `operation_logs_archive` LIKE `operation_logs`;
ALTER TABLE `operation_logs_archive` DROP PARTITIONING;

-- 归档存储过程
DELIMITER $$
CREATE PROCEDURE archive_old_logs()
BEGIN
  DECLARE v_old_month INT;
  DECLARE v_partition_name VARCHAR(20);
  
  -- 计算需要归档的月份（当前月-3）
  SET v_old_month = YEAR(DATE_SUB(NOW(), INTERVAL 3 MONTH)) * 100 + MONTH(DATE_SUB(NOW(), INTERVAL 3 MONTH));
  SET v_partition_name = CONCAT('p_', v_old_month);
  
  -- 归档数据
  IF EXISTS (
    SELECT * FROM information_schema.partitions 
    WHERE table_name = 'operation_logs' AND partition_name = v_partition_name
  ) THEN
    -- 插入到归档表
    SET @sql = CONCAT('INSERT INTO operation_logs_archive SELECT * FROM operation_logs PARTITION (', v_partition_name, ')');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 删除原表分区
    SET @sql = CONCAT('ALTER TABLE operation_logs DROP PARTITION ', v_partition_name);
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
  END IF;
END$$
DELIMITER ;

-- 每月执行归档任务
CREATE EVENT IF NOT EXISTS monthly_archive
ON SCHEDULE EVERY 1 MONTH
STARTS '2023-01-01 01:00:00'
DO
CALL archive_old_logs();        