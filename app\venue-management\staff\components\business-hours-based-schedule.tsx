import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Trash2, Calendar, Clock } from "lucide-react";
import { toast } from "@/components/ui/use-toast";

// 定义日期类型
type DayKey = "周一" | "周二" | "周三" | "周四" | "周五" | "周六" | "周日";

// 定义休息时间类型
type BreakTime = {
  type: string;
  name: string;
  start: string;
  end: string;
};

// 定义特殊日期类型
type SpecialDate = {
  date: string; // YYYY-MM-DD
  enabled: boolean;
  workTime: {
    start: string;
    end: string;
  };
  breakTimes: BreakTime[];
};

// 定义基于营业时间的私教时间设置
type BusinessHoursBasedSchedule = {
  businessHours: {
    [key in DayKey]: {
      enabled: boolean;
      workTime: {
        start: string;
        end: string;
      };
    };
  };
  defaultBreakTimes: BreakTime[];
  specialDates: SpecialDate[];
};

interface BusinessHoursBasedScheduleProps {
  initialData?: BusinessHoursBasedSchedule;
  onSave: (data: BusinessHoursBasedSchedule) => void;
  onCancel: () => void;
  staffName?: string;
}

// 默认数据
const defaultSchedule: BusinessHoursBasedSchedule = {
  businessHours: {
    "周一": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
    "周二": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
    "周三": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
    "周四": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
    "周五": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
    "周六": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
    "周日": { enabled: true, workTime: { start: "09:00", end: "21:00" } }
  },
  defaultBreakTimes: [
    { type: "lunch", name: "午餐时间", start: "12:00", end: "13:00" },
    { type: "dinner", name: "晚餐时间", start: "18:00", end: "19:00" }
  ],
  specialDates: []
};

export default function BusinessHoursBasedSchedule({
  initialData,
  onSave,
  onCancel,
  staffName
}: BusinessHoursBasedScheduleProps) {
  // 状态
  const [schedule, setSchedule] = useState<BusinessHoursBasedSchedule>(
    initialData || defaultSchedule
  );
  const [allDaysSame, setAllDaysSame] = useState(true);
  const [activeTab, setActiveTab] = useState("business-hours");

  // 处理"每个工作日作息时间都一样"选项变化
  const handleAllDaysSameChange = (checked: boolean) => {
    setAllDaysSame(checked);

    if (checked) {
      // 如果选中"所有天相同"，则将所有天的时间设置为与周一相同
      const mondaySettings = schedule.businessHours["周一"];
      const updatedBusinessHours = { ...schedule.businessHours };

      (Object.keys(updatedBusinessHours) as DayKey[]).forEach(day => {
        if (day !== "周一") {
          updatedBusinessHours[day] = { ...mondaySettings };
        }
      });

      setSchedule({
        ...schedule,
        businessHours: updatedBusinessHours
      });
    }
  };

  // 处理某天是否上班的开关变化
  const handleDayEnabledChange = (day: DayKey, enabled: boolean) => {
    const updatedBusinessHours = { ...schedule.businessHours };
    updatedBusinessHours[day] = {
      ...updatedBusinessHours[day],
      enabled
    };

    // 如果选中了"所有天相同"，则更新所有天
    if (allDaysSame) {
      (Object.keys(updatedBusinessHours) as DayKey[]).forEach(d => {
        updatedBusinessHours[d] = { ...updatedBusinessHours[d], enabled };
      });
    }

    setSchedule({
      ...schedule,
      businessHours: updatedBusinessHours
    });
  };

  // 处理工作时间变化
  const handleWorkTimeChange = (day: DayKey, field: 'start' | 'end', value: string) => {
    const updatedBusinessHours = { ...schedule.businessHours };
    updatedBusinessHours[day] = {
      ...updatedBusinessHours[day],
      workTime: {
        ...updatedBusinessHours[day].workTime,
        [field]: value
      }
    };

    // 如果选中了"所有天相同"，则更新所有天
    if (allDaysSame) {
      (Object.keys(updatedBusinessHours) as DayKey[]).forEach(d => {
        if (d !== day) {
          updatedBusinessHours[d] = {
            ...updatedBusinessHours[d],
            workTime: {
              ...updatedBusinessHours[d].workTime,
              [field]: value
            }
          };
        }
      });
    }

    setSchedule({
      ...schedule,
      businessHours: updatedBusinessHours
    });
  };

  // 处理休息时间变化
  const handleBreakTimeChange = (index: number, field: 'name' | 'start' | 'end', value: string) => {
    const breakTimes = [...schedule.defaultBreakTimes];
    breakTimes[index] = { ...breakTimes[index], [field]: value };

    setSchedule({
      ...schedule,
      defaultBreakTimes: breakTimes
    });
  };

  // 添加新的休息时间
  const addBreakTime = () => {
    setSchedule({
      ...schedule,
      defaultBreakTimes: [
        ...schedule.defaultBreakTimes,
        { type: "custom", name: "休息时间", start: "15:00", end: "16:00" }
      ]
    });
  };

  // 删除休息时间
  const removeBreakTime = (index: number) => {
    const breakTimes = [...schedule.defaultBreakTimes];
    breakTimes.splice(index, 1);

    setSchedule({
      ...schedule,
      defaultBreakTimes: breakTimes
    });
  };

  // 添加特殊日期
  const addSpecialDate = () => {
    // 获取明天的日期
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dateString = tomorrow.toISOString().split('T')[0];

    setSchedule({
      ...schedule,
      specialDates: [
        ...schedule.specialDates,
        {
          date: dateString,
          enabled: false,
          workTime: { start: "09:00", end: "21:00" },
          breakTimes: []
        }
      ]
    });
  };

  // 删除特殊日期
  const removeSpecialDate = (index: number) => {
    const specialDates = [...schedule.specialDates];
    specialDates.splice(index, 1);

    setSchedule({
      ...schedule,
      specialDates
    });
  };

  // 处理特殊日期变化
  const handleSpecialDateChange = (index: number, field: 'date' | 'enabled', value: any) => {
    const specialDates = [...schedule.specialDates];
    specialDates[index] = { 
      ...specialDates[index], 
      [field]: value 
    };

    setSchedule({
      ...schedule,
      specialDates
    });
  };

  // 处理特殊日期工作时间变化
  const handleSpecialDateWorkTimeChange = (index: number, field: 'start' | 'end', value: string) => {
    const specialDates = [...schedule.specialDates];
    specialDates[index] = { 
      ...specialDates[index], 
      workTime: {
        ...specialDates[index].workTime,
        [field]: value
      }
    };

    setSchedule({
      ...schedule,
      specialDates
    });
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="business-hours">营业时间设置</TabsTrigger>
          <TabsTrigger value="special-dates">特殊日期/请假</TabsTrigger>
        </TabsList>

        <TabsContent value="business-hours" className="space-y-4 mt-4">
          {/* 每个工作日作息时间都一样 */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="all-days-same"
              checked={allDaysSame}
              onCheckedChange={(checked) => handleAllDaysSameChange(!!checked)}
              className="h-5 w-5 rounded-sm border-primary data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
            />
            <Label
              htmlFor="all-days-same"
              className="text-sm font-normal cursor-pointer"
            >
              每个工作日作息时间都一样（私教课时间将基于营业时间减去休息时间自动计算）
            </Label>
          </div>

          {/* 工作时间表格 */}
          <div className="border rounded-md overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="bg-muted/30">
                  <th className="p-2 text-left w-[100px]"></th>
                  {(Object.keys(schedule.businessHours) as DayKey[]).map(day => (
                    <th key={day} className="p-2 text-center">
                      {day}
                    </th>
                  ))}
                </tr>
                <tr>
                  <th className="p-2 text-left">上班</th>
                  {(Object.keys(schedule.businessHours) as DayKey[]).map(day => (
                    <th key={day} className="p-2 text-center">
                      <Switch
                        checked={schedule.businessHours[day].enabled}
                        onCheckedChange={(checked) => handleDayEnabledChange(day, checked)}
                        className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                      />
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="p-1 text-left border-t">上班<br/>时间</td>
                  {(Object.keys(schedule.businessHours) as DayKey[]).map(day => (
                    <td key={day} className="p-1 text-center border-t">
                      <Input
                        type="time"
                        value={schedule.businessHours[day].workTime.start}
                        onChange={(e) => handleWorkTimeChange(day, 'start', e.target.value)}
                        className="h-8 text-center border-0 border-b focus-visible:ring-0 focus-visible:border-primary px-1"
                        disabled={!schedule.businessHours[day].enabled}
                      />
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="p-1 text-left border-t">下班<br/>时间</td>
                  {(Object.keys(schedule.businessHours) as DayKey[]).map(day => (
                    <td key={day} className="p-1 text-center border-t">
                      <Input
                        type="time"
                        value={schedule.businessHours[day].workTime.end}
                        onChange={(e) => handleWorkTimeChange(day, 'end', e.target.value)}
                        className="h-8 text-center border-0 border-b focus-visible:ring-0 focus-visible:border-primary px-1"
                        disabled={!schedule.businessHours[day].enabled}
                      />
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>

          {/* 固定休息时间 */}
          <div className="mt-6">
            <h3 className="text-base font-medium mb-2">固定休息时间</h3>
            <div className="border rounded-md overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="bg-muted/30">
                    <th className="p-2 text-left">休息名称</th>
                    <th className="p-2 text-center">开始时间</th>
                    <th className="p-2 text-center">结束时间</th>
                    <th className="p-2 text-center w-[60px]">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {schedule.defaultBreakTimes.map((breakTime, index) => (
                    <tr key={`${breakTime.type}-${index}`}>
                      <td className="p-2 text-left border-t">
                        <Input
                          type="text"
                          value={breakTime.name}
                          onChange={(e) => handleBreakTimeChange(index, 'name', e.target.value)}
                          className="h-8 border-0 focus-visible:ring-0 focus-visible:border-primary"
                        />
                      </td>
                      <td className="p-2 text-center border-t">
                        <Input
                          type="time"
                          value={breakTime.start}
                          onChange={(e) => handleBreakTimeChange(index, 'start', e.target.value)}
                          className="h-8 text-center border-0 focus-visible:ring-0 focus-visible:border-primary"
                        />
                      </td>
                      <td className="p-2 text-center border-t">
                        <Input
                          type="time"
                          value={breakTime.end}
                          onChange={(e) => handleBreakTimeChange(index, 'end', e.target.value)}
                          className="h-8 text-center border-0 focus-visible:ring-0 focus-visible:border-primary"
                        />
                      </td>
                      <td className="p-2 text-center border-t">
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeBreakTime(index)}
                          className="text-muted-foreground hover:text-destructive h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addBreakTime}
              className="text-primary border-primary hover:bg-primary/10 mt-2"
            >
              <Plus className="h-4 w-4 mr-1" />
              添加休息时间
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="special-dates" className="space-y-4 mt-4">
          <div className="flex items-center justify-between">
            <h3 className="text-base font-medium">特殊日期/请假设置</h3>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addSpecialDate}
              className="text-primary border-primary hover:bg-primary/10"
            >
              <Calendar className="h-4 w-4 mr-1" />
              添加特殊日期
            </Button>
          </div>

          {schedule.specialDates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              暂无特殊日期设置，点击"添加特殊日期"按钮添加
            </div>
          ) : (
            <div className="space-y-4">
              {schedule.specialDates.map((specialDate, index) => (
                <div key={`special-date-${index}`} className="border rounded-md p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Input
                        type="date"
                        value={specialDate.date}
                        onChange={(e) => handleSpecialDateChange(index, 'date', e.target.value)}
                        className="w-40"
                      />
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={specialDate.enabled}
                          onCheckedChange={(checked) => handleSpecialDateChange(index, 'enabled', checked)}
                          className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                        />
                        <Label className="text-sm font-normal">
                          {specialDate.enabled ? "可上课" : "请假/不可上课"}
                        </Label>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeSpecialDate(index)}
                      className="text-muted-foreground hover:text-destructive h-8 w-8 p-0"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  {specialDate.enabled && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm">上班时间</Label>
                        <Input
                          type="time"
                          value={specialDate.workTime.start}
                          onChange={(e) => handleSpecialDateWorkTimeChange(index, 'start', e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-sm">下班时间</Label>
                        <Input
                          type="time"
                          value={specialDate.workTime.end}
                          onChange={(e) => handleSpecialDateWorkTimeChange(index, 'end', e.target.value)}
                        />
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button onClick={() => onSave(schedule)}>
          保存
        </Button>
      </div>
    </div>
  );
}
