import { downloadHelper } from "../download-helper"
import axiosInstance from "../axios-config"

// 定义课程API的参数类型
interface CourseParams {
  page?: number
  pageSize?: number
  keyword?: string
  type?: string
  status?: string
  coach?: string
  venue?: string
  priceMin?: number
  priceMax?: number
  capacityMin?: number
  capacityMax?: number
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  dayOfWeek?: string[]
  timeOfDay?: string[]
}

// 定义课程数据类型
interface CourseData {
  title: string
  description?: string
  type: string
  price: number
  capacity: number
  coachId: string
  venueId?: string
  status?: string
  level?: string
  startDate?: string
  [key: string]: any
}

// 定义导出选项类型
interface ExportOptions {
  format: 'xlsx' | 'csv'
  type: 'all' | 'selected' | 'filtered'
  fields: string[]
  courseIds?: string[]
  filters?: CourseParams
  typeNameMap?: Record<string, string>
  statusNameMap?: Record<string, string>
}

// 课程API接口
export const courseApi = {
  // 获取所有课程列表
  getAllCourses: async (params?: CourseParams) => {
    try {
      const response = await axiosInstance.get("/api/course/list", { params })
      return response.data
    } catch (error) {
      console.error("获取课程列表失败:", error)
      throw error
    }
  },

  // 获取课程详情
  getCourseById: async (id: string) => {
    try {
      const response = await axiosInstance.get(`/api/course/detail/${id}`)
      return response.data
    } catch (error) {
      console.error("获取课程详情失败:", error)
      throw error
    }
  },

  // 创建课程
  createCourse: async (courseData: CourseData) => {
    try {
      const response = await axiosInstance.post("/api/course/create", courseData)
      return response.data
    } catch (error) {
      console.error("创建课程失败:", error)
      throw error
    }
  },

  // 更新课程
  updateCourse: async (id: string, courseData: CourseData) => {
    try {
      const response = await axiosInstance.put(`/api/course/update/${id}`, courseData)
      return response.data
    } catch (error) {
      console.error("更新课程失败:", error)
      throw error
    }
  },

  // 删除课程
  deleteCourse: async (id: string) => {
    try {
      const response = await axiosInstance.delete(`/api/course/delete/${id}`)
      return response.data
    } catch (error) {
      console.error("删除课程失败:", error)
      throw error
    }
  },

  // 批量删除课程
  deleteCourses: async (ids: string[]) => {
    try {
      const response = await axiosInstance.delete("/api/course/batch-delete", { data: { ids } })
      return response.data
    } catch (error) {
      console.error("批量删除课程失败:", error)
      throw error
    }
  },

  // 课程统计
  getCourseStats: async () => {
    try {
      const response = await axiosInstance.get("/api/course/stats")
      return response.data
    } catch (error) {
      console.error("获取课程统计失败:", error)
      throw error
    }
  },

  // 导出课程数据
  exportCourses: async (options: ExportOptions) => {
    try {
      // 如果存在本地数据，并且是选中导出模式，可以尝试在前端处理数据转换
      if (options.type === "selected" && options.courseIds?.length) {
        // 如果后端API支持导出功能，优先使用后端API
        try {
          // 创建一个包含所有参数的通用对象
          const requestParams: Record<string, any> = {
            format: options.format,
            fields: options.fields.join(","),
            ids: options.courseIds.join(",")
          }
          
          // 添加类型和状态映射参数
          if (options.typeNameMap) {
            requestParams.typeNameMap = JSON.stringify(options.typeNameMap);
          }
          
          if (options.statusNameMap) {
            requestParams.statusNameMap = JSON.stringify(options.statusNameMap);
          }
          
          // 调用API导出
          const response = await axiosInstance.get("/api/course/export", { 
            params: requestParams,
            responseType: "blob" 
          })
          
          // 从响应头获取文件名
          const contentDisposition = response.headers["content-disposition"]
          let filename = "selected-courses-export"
          
          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename="(.+)"/)
            if (filenameMatch && filenameMatch[1]) {
              filename = filenameMatch[1]
            }
          }
          
          // 文件扩展名
          const extension = options.format === "xlsx" ? ".xlsx" : ".csv"
          
          // 下载文件
          downloadHelper.downloadFile(
            response.data,
            `${filename}${extension}`,
            options.format === "xlsx"
              ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              : "text/csv"
          )
        } catch (apiError) {
          console.warn("无法通过API导出，尝试前端导出:", apiError);
          
          // 如果API导出失败，尝试从本地数据导出
          // 这里可以实现一个本地导出的逻辑，先获取课程数据，然后转换成CSV
          const coursesResponse = await axiosInstance.get("/api/course/list", { 
            params: { ids: options.courseIds.join(",") } 
          });
          
          if (coursesResponse.data && coursesResponse.data.code === 200) {
            const coursesData = coursesResponse.data.data?.list || [];
            
            // 处理数据，应用中文映射
            const processedData = coursesData.map((course: any) => {
              const result: Record<string, any> = {};
              
              // 添加选择的字段，全部使用中文字段名
              if (options.fields.includes('title')) result['课程名称'] = course.title || course.name || '';
              if (options.fields.includes('description')) result['课程描述'] = course.description || '';
              
              // 使用类型的中文名称
              if (options.fields.includes('type')) {
                const typeName = course.type && options.typeNameMap?.[course.type]
                  ? options.typeNameMap[course.type]
                  : (course.typeName || course.type || '');
                result['课程类型'] = typeName;
              }
              
              if (options.fields.includes('coach')) result['教练'] = course.coachName || '';
              if (options.fields.includes('venue')) result['场地'] = course.venueName || '';
              if (options.fields.includes('price')) result['价格'] = course.price || 0;
              if (options.fields.includes('capacity')) result['容量'] = course.capacity || 0;
              
              // 使用状态的中文名称
              if (options.fields.includes('status')) {
                const statusName = course.status && options.statusNameMap?.[course.status]
                  ? options.statusNameMap[course.status]
                  : (course.statusName || course.status || '');
                result['状态'] = statusName;
              }
              
              if (options.fields.includes('time')) result['上课时间'] = course.time || '';
              if (options.fields.includes('enrollments')) result['报名人数'] = course.bookings || 0;
              if (options.fields.includes('createdAt')) result['创建时间'] = course.createdAt || course.createTime || '';
              
              return result;
            });
            
            // 下载为CSV或Excel
            if (options.format === 'csv') {
              downloadHelper.downloadCSV(processedData, 'courses-export.csv');
            } else {
              // Excel需要特殊处理，这里简化为CSV下载
              downloadHelper.downloadCSV(processedData, 'courses-export.csv');
            }
          } else {
            throw new Error('获取课程数据失败');
          }
        }
      } else {
        // 如果是全部导出或者筛选导出，调用后端API
        const requestParams: Record<string, any> = {
          format: options.format,
          fields: options.fields.join(","),
          type: options.type
        }
        
        // 添加类型和状态映射参数
        if (options.typeNameMap) {
          requestParams.typeNameMap = JSON.stringify(options.typeNameMap);
        }
        
        if (options.statusNameMap) {
          requestParams.statusNameMap = JSON.stringify(options.statusNameMap);
        }
        
        // 如果是当前筛选结果，需要添加筛选条件
        if (options.type === "filtered" && options.filters) {
          Object.assign(requestParams, options.filters)
        }
        
        // 调用后端API导出
        const response = await axiosInstance.get("/api/course/export", { 
          params: requestParams,
          responseType: "blob" 
        })
        
        // 从响应头获取文件名
        const contentDisposition = response.headers["content-disposition"]
        let filename = "courses-export"
        
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/)
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1]
          }
        }
        
        // 文件扩展名
        const extension = options.format === "xlsx" ? ".xlsx" : ".csv"
        
        // 下载文件
        downloadHelper.downloadFile(
          response.data,
          `${filename}${extension}`,
          options.format === "xlsx"
            ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            : "text/csv"
        )
      }
    } catch (error) {
      console.error("导出课程失败:", error)
      throw error
    }
  },
  
  // 下载课程导入模板
  downloadImportTemplate: async () => {
    try {
      // 尝试从API获取模板
      try {
        const response = await axiosInstance.get("/api/course/import-template", { 
          responseType: "blob" 
        })
        
        // 从响应头获取文件名
        const contentDisposition = response.headers["content-disposition"]
        let filename = "course-import-template.xlsx"
        
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/)
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1]
          }
        }
        
        // 下载文件
        downloadHelper.downloadFile(
          response.data,
          filename,
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
      } catch (apiError) {
        console.warn("无法从API获取模板，将创建本地模板:", apiError)
        
        // 如果API不存在，则创建简单模板
        downloadHelper.createCourseImportTemplate()
      }
    } catch (error) {
      console.error("下载课程导入模板失败:", error)
      throw error
    }
  },
  
  // 导入课程数据
  importCourses: async (file: File) => {
    try {
      const formData = new FormData()
      formData.append("file", file)
      
      const response = await axiosInstance.post("/api/course/import", formData, {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      })
      
      return response.data
    } catch (error) {
      console.error("导入课程失败:", error)
      throw error
    }
  }
} 