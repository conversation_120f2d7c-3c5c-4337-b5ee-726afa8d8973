"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, <PERSON><PERSON>eader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, MoreHorizontal, Pencil, Eye } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface CourseTypeCardProps {
  type: any
  onEdit: () => void
  onViewDetails: () => void
}

export function CourseTypeCard({ type, onEdit, onViewDetails }: CourseTypeCardProps) {
  return (
    <Card className="overflow-hidden">
      <div className="h-2" style={{ backgroundColor: type.color }} />
      <CardHeader className="pb-2 flex flex-row items-start justify-between">
        <div>
          <div className="flex items-center gap-2">
            <div className="font-semibold text-lg">{type.name}</div>
            <Badge variant={type.status === "active" ? "default" : "secondary"}>
              {type.status === "active" ? "启用" : "停用"}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">{type.description}</p>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>操作</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={onViewDetails}>
              <Eye className="mr-2 h-4 w-4" />
              查看详情
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onEdit}>
              <Pencil className="mr-2 h-4 w-4" />
              编辑
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            {type.status === "active" ? (
              <DropdownMenuItem>停用</DropdownMenuItem>
            ) : (
              <DropdownMenuItem>启用</DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span>创建于 {type.createdAt}</span>
          </div>
          {type.name === "团课" && (
            <p className="text-xs text-muted-foreground">适合多人参与的常规团体课程，人数通常在15-30人</p>
          )}
          {type.name === "小班课" && (
            <p className="text-xs text-muted-foreground">小规模教学，人数通常在5-10人，教练能给予更多个性化指导</p>
          )}
          {type.name === "精品课" && (
            <p className="text-xs text-muted-foreground">高端定制课程，由资深教练授课，提供优质教学体验</p>
          )}
          {type.name === "私教课" && (
            <p className="text-xs text-muted-foreground">一对一个性化教学，根据会员需求定制课程内容</p>
          )}
          {type.name === "教培课" && (
            <p className="text-xs text-muted-foreground">针对瑜伽教练的专业培训课程，提升教学技能和专业知识</p>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        <div className="text-sm">
          <span className="font-medium">{type.courses}</span> 个关联课程
        </div>
        <Button variant="outline" size="sm" onClick={onViewDetails}>
          查看详情
        </Button>
      </CardFooter>
    </Card>
  )
}

