"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { MoreHorizontal, Pencil, Wallet, User, Tag, Calendar, MessageSquare, Trash2, Share2, Eye } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ShareholderDetailDialog } from "@/components/shareholders/shareholder-detail-dialog"

// 模拟数据
const shareholders = [
  {
    id: "1",
    name: "张三",
    phone: "13800138001",
    type: "消费型股东",
    status: "active",
    joinDate: "2023-05-15",
    referrals: 5,
    totalDividend: 2500,
    avatar: "/avatars/01.png",
    tags: ["高频消费", "瑜伽爱好者"],
  },
  {
    id: "2",
    name: "李四",
    phone: "13900139001",
    type: "投资型股东",
    status: "active",
    joinDate: "2023-04-20",
    referrals: 0,
    totalDividend: 5000,
    avatar: "/avatars/02.png",
    tags: ["投资人"],
  },
  {
    id: "3",
    name: "王五",
    phone: "13700137001",
    type: "资源型股东",
    status: "active",
    joinDate: "2023-06-01",
    referrals: 15,
    totalDividend: 3750,
    avatar: "/avatars/03.png",
    tags: ["KOL", "社交媒体"],
  },
  {
    id: "4",
    name: "赵六",
    phone: "13600136001",
    type: "员工型股东",
    status: "active",
    joinDate: "2023-03-10",
    referrals: 8,
    totalDividend: 4200,
    avatar: "/avatars/04.png",
    tags: ["瑜伽教练"],
  },
  {
    id: "5",
    name: "美丽美容院",
    phone: "13500135001",
    type: "联盟型股东",
    status: "inactive",
    joinDate: "2023-07-01",
    referrals: 12,
    totalDividend: 1800,
    avatar: "/avatars/05.png",
    tags: ["美容行业", "异业合作"],
  },
]

interface ShareholderTableProps {
  searchQuery: string
  shareholderType: string
}

export function ShareholderTable({ searchQuery, shareholderType }: ShareholderTableProps) {
  const [selectedRows, setSelectedRows] = useState<string[]>([])
  const [openDetailDialog, setOpenDetailDialog] = useState(false)
  const [selectedShareholder, setSelectedShareholder] = useState<any>(null)

  // 过滤股东
  const filteredShareholders = shareholders.filter((shareholder) => {
    if (shareholderType !== "all" && !shareholder.type.toLowerCase().includes(shareholderType.toLowerCase())) {
      return false
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        shareholder.name.toLowerCase().includes(query) ||
        shareholder.phone.includes(query) ||
        shareholder.type.toLowerCase().includes(query) ||
        shareholder.tags.some((tag) => tag.toLowerCase().includes(query))
      )
    }
    
    return true
  })

  // 选择所有行
  const toggleSelectAll = () => {
    if (selectedRows.length === filteredShareholders.length) {
      setSelectedRows([])
    } else {
      setSelectedRows(filteredShareholders.map((s) => s.id))
    }
  }

  // 选择单行
  const toggleSelectRow = (id: string) => {
    if (selectedRows.includes(id)) {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id))
    } else {
      setSelectedRows([...selectedRows, id])
    }
  }

  // 打开股东详情
  const openShareholderDetail = (shareholder: any) => {
    setSelectedShareholder(shareholder)
    setOpenDetailDialog(true)
  }

  return (
    <div className="border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={selectedRows.length === filteredShareholders.length && filteredShareholders.length > 0}
                onCheckedChange={toggleSelectAll}
                aria-label="Select all"
              />
            </TableHead>
            <TableHead>股东信息</TableHead>
            <TableHead>股东类型</TableHead>
            <TableHead>标签</TableHead>
            <TableHead>加入日期</TableHead>
            <TableHead>引流客户</TableHead>
            <TableHead>累计分红</TableHead>
            <TableHead>状态</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredShareholders.map((shareholder) => (
            <TableRow key={shareholder.id}>
              <TableCell>
                <Checkbox
                  checked={selectedRows.includes(shareholder.id)}
                  onCheckedChange={() => toggleSelectRow(shareholder.id)}
                  aria-label={`Select ${shareholder.name}`}
                />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={shareholder.avatar} alt={shareholder.name} />
                    <AvatarFallback>{shareholder.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{shareholder.name}</div>
                    <div className="text-sm text-muted-foreground">{shareholder.phone}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>{shareholder.type}</TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {shareholder.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </TableCell>
              <TableCell>{shareholder.joinDate}</TableCell>
              <TableCell>{shareholder.referrals}人</TableCell>
              <TableCell>¥{shareholder.totalDividend.toFixed(2)}</TableCell>
              <TableCell>
                <Badge variant={shareholder.status === "active" ? "default" : "secondary"}>
                  {shareholder.status === "active" ? "活跃" : "不活跃"}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>操作</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => openShareholderDetail(shareholder)}>
                      <Eye className="mr-2 h-4 w-4" />
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Pencil className="mr-2 h-4 w-4" />
                      编辑信息
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Wallet className="mr-2 h-4 w-4" />
                      分红记录
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Share2 className="mr-2 h-4 w-4" />
                      引流记录
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <MessageSquare className="mr-2 h-4 w-4" />
                      发送消息
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <ShareholderDetailDialog
        open={openDetailDialog}
        onOpenChange={setOpenDetailDialog}
        shareholder={selectedShareholder}
      />
    </div>
  )
}
