"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format, subDays, subMonths } from "date-fns"
import { CalendarIcon, Download, BarChart2, TrendingUp, Users, CreditCard, DollarSign } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface SalesAnalysisDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  card: {
    id: number | string
    name: string
    color: string
    price: string
    salesCount: number
    revenue: string
  } | null
}

// 模拟销售数据
const mockDailySales = [
  { date: "05-10", count: 3, revenue: 1350 },
  { date: "05-11", count: 2, revenue: 900 },
  { date: "05-12", count: 5, revenue: 2250 },
  { date: "05-13", count: 4, revenue: 1800 },
  { date: "05-14", count: 6, revenue: 2700 },
  { date: "05-15", count: 3, revenue: 1350 },
  { date: "05-16", count: 7, revenue: 3150 },
]

const mockMonthlySales = [
  { date: "2023-01", count: 15, revenue: 6750 },
  { date: "2023-02", count: 18, revenue: 8100 },
  { date: "2023-03", count: 22, revenue: 9900 },
  { date: "2023-04", count: 25, revenue: 11250 },
  { date: "2023-05", count: 30, revenue: 13500 },
]

const mockChannelSales = [
  { channel: "线下门店", count: 45, revenue: 20250, percentage: 45 },
  { channel: "小程序", count: 35, revenue: 15750, percentage: 35 },
  { channel: "推荐", count: 15, revenue: 6750, percentage: 15 },
  { channel: "活动", count: 5, revenue: 2250, percentage: 5 },
]

export function SalesAnalysisDialog({
  open,
  onOpenChange,
  card
}: SalesAnalysisDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("daily")
  const [dateRange, setDateRange] = useState<{from: Date | undefined, to: Date | undefined}>({
    from: subDays(new Date(), 7),
    to: new Date()
  })
  const [timeFrame, setTimeFrame] = useState("week")
  
  const handleExport = () => {
    toast({
      title: "导出成功",
      description: "销售数据已导出为Excel文件",
    })
  }

  const handleTimeFrameChange = (value: string) => {
    setTimeFrame(value)
    
    switch (value) {
      case "week":
        setDateRange({
          from: subDays(new Date(), 7),
          to: new Date()
        })
        break
      case "month":
        setDateRange({
          from: subDays(new Date(), 30),
          to: new Date()
        })
        break
      case "quarter":
        setDateRange({
          from: subMonths(new Date(), 3),
          to: new Date()
        })
        break
      case "year":
        setDateRange({
          from: subMonths(new Date(), 12),
          to: new Date()
        })
        break
      default:
        break
    }
  }

  if (!card) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-5xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart2 className="h-5 w-5 text-primary" />
            销售数据分析
          </DialogTitle>
          <DialogDescription>
            <span className="font-medium" style={{ color: card.color }}>{card.name}</span> 会员卡的销售数据分析，累计销售 {card.salesCount} 张，总收入 {card.revenue}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex items-center gap-2">
              <Select value={timeFrame} onValueChange={handleTimeFrameChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">最近一周</SelectItem>
                  <SelectItem value="month">最近一个月</SelectItem>
                  <SelectItem value="quarter">最近一季度</SelectItem>
                  <SelectItem value="year">最近一年</SelectItem>
                  <SelectItem value="custom">自定义</SelectItem>
                </SelectContent>
              </Select>
              
              {timeFrame === "custom" && (
                <div className="flex items-center gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[130px] justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange.from ? format(dateRange.from, "yyyy-MM-dd") : "开始日期"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateRange.from}
                        onSelect={(date) => setDateRange({ ...dateRange, from: date })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <span>至</span>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[130px] justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : "结束日期"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateRange.to}
                        onSelect={(date) => setDateRange({ ...dateRange, to: date })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              )}
            </div>
            <Button variant="outline" onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              导出Excel
            </Button>
          </div>

          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总销售额</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥13,500</div>
                <p className="text-xs text-muted-foreground">
                  较上期 <span className="text-green-500">+12.5%</span>
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">销售数量</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">30</div>
                <p className="text-xs text-muted-foreground">
                  较上期 <span className="text-green-500">+20%</span>
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">新增会员</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">25</div>
                <p className="text-xs text-muted-foreground">
                  较上期 <span className="text-green-500">+8.3%</span>
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">转化率</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">15.2%</div>
                <p className="text-xs text-muted-foreground">
                  较上期 <span className="text-red-500">-2.1%</span>
                </p>
              </CardContent>
            </Card>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="daily">日销售趋势</TabsTrigger>
              <TabsTrigger value="monthly">月销售趋势</TabsTrigger>
              <TabsTrigger value="channel">销售渠道分析</TabsTrigger>
            </TabsList>

            <TabsContent value="daily" className="mt-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>日销售趋势</CardTitle>
                  <CardDescription>最近7天的销售数据</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full">
                    <div className="flex h-full items-center justify-center">
                      <div className="flex h-[250px] w-full items-end justify-between gap-2">
                        {mockDailySales.map((item, index) => (
                          <div key={index} className="flex flex-col items-center">
                            <div 
                              className="w-12 bg-primary" 
                              style={{ 
                                height: `${(item.count / 7) * 200}px`,
                                backgroundColor: card.color
                              }}
                            ></div>
                            <div className="mt-2 text-xs">{item.date}</div>
                            <div className="text-xs font-medium">{item.count}张</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="monthly" className="mt-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>月销售趋势</CardTitle>
                  <CardDescription>最近5个月的销售数据</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full">
                    <div className="flex h-full items-center justify-center">
                      <div className="flex h-[250px] w-full items-end justify-between gap-2">
                        {mockMonthlySales.map((item, index) => (
                          <div key={index} className="flex flex-col items-center">
                            <div 
                              className="w-16 bg-primary" 
                              style={{ 
                                height: `${(item.count / 30) * 200}px`,
                                backgroundColor: card.color
                              }}
                            ></div>
                            <div className="mt-2 text-xs">{item.date}</div>
                            <div className="text-xs font-medium">{item.count}张</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="channel" className="mt-4 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>销售渠道分析</CardTitle>
                  <CardDescription>各渠道销售占比</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px] w-full">
                    <div className="flex h-full items-center justify-center">
                      <div className="grid w-full grid-cols-4 gap-4">
                        {mockChannelSales.map((item, index) => (
                          <div key={index} className="flex flex-col items-center">
                            <div className="relative h-[200px] w-full">
                              <div 
                                className="absolute bottom-0 w-full bg-primary" 
                                style={{ 
                                  height: `${item.percentage * 2}px`,
                                  backgroundColor: index === 0 ? card.color : `${card.color}${90 - index * 20}`
                                }}
                              ></div>
                            </div>
                            <div className="mt-2 text-sm font-medium">{item.channel}</div>
                            <div className="text-xs">{item.count}张 ({item.percentage}%)</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
