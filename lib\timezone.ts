// 时区工具函数 - 统一使用东八区上海时间

/**
 * 获取上海时间的Date对象
 */
export function getShanghaiDate(): Date {
  const now = new Date();
  // 获取UTC时间
  const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
  // 东八区时间 (UTC+8)
  const shanghaiTime = new Date(utc + (8 * 3600000));
  return shanghaiTime;
}

/**
 * 格式化为上海时间字符串
 * @param date 可选的日期对象，默认为当前时间
 * @param format 格式类型
 */
export function formatShanghaiTime(
  date?: Date, 
  format: 'datetime' | 'date' | 'time' | 'timestamp' = 'datetime'
): string {
  const targetDate = date || getShanghaiDate();
  
  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'Asia/Shanghai',
    hour12: false
  };

  switch (format) {
    case 'datetime':
      return targetDate.toLocaleString('zh-CN', {
        ...options,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    
    case 'date':
      return targetDate.toLocaleDateString('zh-CN', {
        ...options,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    
    case 'time':
      return targetDate.toLocaleTimeString('zh-CN', {
        ...options,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    
    case 'timestamp':
      return targetDate.toISOString();
    
    default:
      return targetDate.toLocaleString('zh-CN', options);
  }
}

/**
 * 格式化为数据库兼容的时间格式
 * @param date 可选的日期对象，默认为当前时间
 */
export function formatForDatabase(date?: Date): string {
  const targetDate = date || getShanghaiDate();
  return targetDate.toISOString().slice(0, 19).replace('T', ' ');
}

/**
 * 解析数据库时间为上海时间显示
 * @param dbTime 数据库时间字符串
 * @param format 显示格式
 */
export function parseFromDatabase(
  dbTime: string | Date, 
  format: 'datetime' | 'date' | 'time' = 'datetime'
): string {
  const date = typeof dbTime === 'string' ? new Date(dbTime) : dbTime;
  return formatShanghaiTime(date, format);
}

/**
 * 获取相对时间描述（如：刚刚、5分钟前、1小时前等）
 * @param date 目标日期
 */
export function getRelativeTime(date: Date | string): string {
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  const now = getShanghaiDate();
  const diffMs = now.getTime() - targetDate.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSeconds < 60) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return formatShanghaiTime(targetDate, 'date');
  }
}

/**
 * 验证时间字符串是否有效
 * @param timeString 时间字符串
 */
export function isValidTimeString(timeString: string): boolean {
  const date = new Date(timeString);
  return !isNaN(date.getTime());
}

/**
 * 获取今天的开始时间（00:00:00）
 */
export function getTodayStart(): Date {
  const today = getShanghaiDate();
  today.setHours(0, 0, 0, 0);
  return today;
}

/**
 * 获取今天的结束时间（23:59:59）
 */
export function getTodayEnd(): Date {
  const today = getShanghaiDate();
  today.setHours(23, 59, 59, 999);
  return today;
}

/**
 * 获取本周的开始时间（周一00:00:00）
 */
export function getWeekStart(): Date {
  const today = getShanghaiDate();
  const dayOfWeek = today.getDay();
  const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 周日为0，需要特殊处理
  const monday = new Date(today);
  monday.setDate(today.getDate() - daysToMonday);
  monday.setHours(0, 0, 0, 0);
  return monday;
}

/**
 * 获取本月的开始时间（1号00:00:00）
 */
export function getMonthStart(): Date {
  const today = getShanghaiDate();
  const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
  firstDay.setHours(0, 0, 0, 0);
  return firstDay;
}
