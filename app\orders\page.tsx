"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { OrderTable } from "@/components/order-table"
import { Download, Filter, Search, SlidersHorizontal } from "lucide-react"
import { OrderDetailDialog } from "@/components/order-detail-dialog"
import { OrderAdvancedFilterDialog } from "@/components/order-advanced-filter-dialog"
import { OrderBatchOperationsDialog } from "@/components/order-batch-operations-dialog"
import { DatePickerZh } from "@/components/date-picker-zh"

export default function OrdersPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [paymentStatus, setPaymentStatus] = useState("all")
  const [orderType, setOrderType] = useState("all")
  const [dateRange, setDateRange] = useState("30days")
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)

  const [showOrderDetail, setShowOrderDetail] = useState(false)
  const [selectedOrderId, setSelectedOrderId] = useState("")
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false)
  const [showBatchOperations, setShowBatchOperations] = useState(false)
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])

  const handleViewOrderDetail = (orderId: string) => {
    setSelectedOrderId(orderId)
    setShowOrderDetail(true)
  }

  const handleApplyFilters = (filters: any) => {
    console.log("Applied filters:", filters)
    // 实际应用中，这里会根据筛选条件更新订单列表
  }

  const handleApplyBatchOperation = (operation: string, data: any) => {
    console.log(`Applied operation ${operation} to orders:`, selectedOrders, data)
    // 实际应用中，这里会执行批量操作
    setSelectedOrders([])
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">订单管理</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowBatchOperations(true)} disabled={selectedOrders.length === 0}>
            <SlidersHorizontal className="mr-2 h-4 w-4" />
            批量操作 {selectedOrders.length > 0 && `(${selectedOrders.length})`}
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出数据
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative w-full md:w-1/3">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索订单号、会员姓名"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-1 gap-4">
          <Select value={paymentStatus} onValueChange={setPaymentStatus}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="支付状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="paid">已支付</SelectItem>
              <SelectItem value="pending">待支付</SelectItem>
              <SelectItem value="refunded">已退款</SelectItem>
              <SelectItem value="failed">支付失败</SelectItem>
            </SelectContent>
          </Select>

          <Select value={orderType} onValueChange={setOrderType}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="订单类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="membership">会员卡</SelectItem>
              <SelectItem value="course">单次课程</SelectItem>
              <SelectItem value="package">课程套餐</SelectItem>
              <SelectItem value="product">商品</SelectItem>
            </SelectContent>
          </Select>

          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button variant="outline" onClick={() => setShowAdvancedFilter(true)}>
          <Filter className="mr-2 h-4 w-4" />
          高级筛选
        </Button>
      </div>

      {dateRange === "custom" && (
        <div className="flex items-center gap-2">
          <DatePickerZh placeholder="开始日期" selected={startDate} onSelect={setStartDate} />
          <span>至</span>
          <DatePickerZh placeholder="结束日期" selected={endDate} onSelect={setEndDate} />
        </div>
      )}

      <OrderTable
        onViewDetail={handleViewOrderDetail}
        selectedOrders={selectedOrders}
        onSelectOrders={setSelectedOrders}
      />

      <OrderDetailDialog open={showOrderDetail} onOpenChange={setShowOrderDetail} orderId={selectedOrderId} />

      <OrderAdvancedFilterDialog
        open={showAdvancedFilter}
        onOpenChange={setShowAdvancedFilter}
        onApplyFilters={handleApplyFilters}
      />

      <OrderBatchOperationsDialog
        open={showBatchOperations}
        onOpenChange={setShowBatchOperations}
        selectedOrders={selectedOrders}
        onApplyOperation={handleApplyBatchOperation}
      />
    </div>
  )
}

