import { NextResponse } from 'next/server';

// 员工数据结构
export interface Staff {
  id: number;
  tenant_id: number;
  store_id: number | null; // null表示总部员工
  name: string;
  gender: string;
  position: string;
  department: string;
  phone: string;
  email?: string;
  avatar?: string;
  hire_date: string;
  status: number; // 1-在职，0-离职
  is_store_admin?: boolean; // 是否是门店管理员
  is_tenant_admin?: boolean; // 是否是租户管理员
  created_at: string;
  updated_at: string;
}

// 模拟员工数据
const STAFF_LIST: Staff[] = [
  // 总部员工
  {
    id: 1,
    tenant_id: 1,
    store_id: null, // 总部
    name: '张静',
    gender: '女',
    position: 'CEO',
    department: '管理层',
    phone: '13800000001',
    email: '<EMAIL>',
    avatar: '/avatars/staff1.png',
    hire_date: '2022-01-01',
    status: 1,
    is_tenant_admin: true,
    created_at: '2022-01-01T08:00:00Z',
    updated_at: '2022-01-01T08:00:00Z'
  },
  {
    id: 2,
    tenant_id: 1,
    store_id: null, // 总部
    name: '王明',
    gender: '男',
    position: '财务总监',
    department: '财务部',
    phone: '13800000002',
    email: '<EMAIL>',
    avatar: '/avatars/staff2.png',
    hire_date: '2022-01-15',
    status: 1,
    created_at: '2022-01-15T08:00:00Z',
    updated_at: '2022-01-15T08:00:00Z'
  },
  {
    id: 3,
    tenant_id: 1,
    store_id: null, // 总部
    name: '李红',
    gender: '女',
    position: '人事总监',
    department: '人事部',
    phone: '13800000003',
    email: '<EMAIL>',
    avatar: '/avatars/staff3.png',
    hire_date: '2022-02-01',
    status: 1,
    created_at: '2022-02-01T08:00:00Z',
    updated_at: '2022-02-01T08:00:00Z'
  },
  // 三里屯店员工
  {
    id: 4,
    tenant_id: 1,
    store_id: 1, // 三里屯店
    name: '赵平',
    gender: '男',
    position: '店长',
    department: '管理',
    phone: '13800000004',
    email: '<EMAIL>',
    avatar: '/avatars/staff4.png',
    hire_date: '2022-03-01',
    status: 1,
    is_store_admin: true,
    created_at: '2022-03-01T08:00:00Z',
    updated_at: '2022-03-01T08:00:00Z'
  },
  {
    id: 5,
    tenant_id: 1,
    store_id: 1, // 三里屯店
    name: '刘芳',
    gender: '女',
    position: '高级瑜伽教练',
    department: '教学',
    phone: '13800000005',
    email: '<EMAIL>',
    avatar: '/avatars/staff5.png',
    hire_date: '2022-03-15',
    status: 1,
    created_at: '2022-03-15T08:00:00Z',
    updated_at: '2022-03-15T08:00:00Z'
  },
  // 国贸店员工
  {
    id: 6,
    tenant_id: 1,
    store_id: 2, // 国贸店
    name: '陈伟',
    gender: '男',
    position: '店长',
    department: '管理',
    phone: '13800000006',
    email: '<EMAIL>',
    avatar: '/avatars/staff6.png',
    hire_date: '2022-04-01',
    status: 1,
    is_store_admin: true,
    created_at: '2022-04-01T08:00:00Z',
    updated_at: '2022-04-01T08:00:00Z'
  },
  {
    id: 7,
    tenant_id: 1,
    store_id: 2, // 国贸店
    name: '杨丽',
    gender: '女',
    position: '前台',
    department: '客服',
    phone: '13800000007',
    email: '<EMAIL>',
    avatar: '/avatars/staff7.png',
    hire_date: '2022-04-15',
    status: 1,
    created_at: '2022-04-15T08:00:00Z',
    updated_at: '2022-04-15T08:00:00Z'
  },
  // 望京店员工
  {
    id: 8,
    tenant_id: 1,
    store_id: 3, // 望京店
    name: '王芳',
    gender: '女',
    position: '店长',
    department: '管理',
    phone: '13800000008',
    email: '<EMAIL>',
    avatar: '/avatars/staff8.png',
    hire_date: '2022-06-15',
    status: 1,
    is_store_admin: true,
    created_at: '2022-06-15T08:00:00Z',
    updated_at: '2022-06-15T08:00:00Z'
  }
];

// GET 获取员工列表
export async function GET(request: Request) {
  try {
    // 获取查询参数
    const url = new URL(request.url);
    const storeId = url.searchParams.get('store_id');
    const position = url.searchParams.get('position');
    const department = url.searchParams.get('department');
    
    // 获取租户ID - 默认为1 (静心瑜伽馆)
    let tenantId = '1';
    const tenantIdHeader = request.headers.get('X-Tenant-ID');
    if (tenantIdHeader) {
      tenantId = tenantIdHeader;
    }
    
    // 根据查询条件过滤员工
    let filteredStaff = STAFF_LIST.filter(staff => 
      staff.tenant_id.toString() === tenantId
    );
    
    // 如果指定了门店ID
    if (storeId) {
      if (storeId === 'hq') {
        // 总部员工
        filteredStaff = filteredStaff.filter(staff => staff.store_id === null);
      } else {
        // 特定门店员工
        filteredStaff = filteredStaff.filter(staff => 
          staff.store_id === parseInt(storeId)
        );
      }
    }
    
    // 根据职位筛选
    if (position) {
      filteredStaff = filteredStaff.filter(staff => 
        staff.position.includes(position)
      );
    }
    
    // 根据部门筛选
    if (department) {
      filteredStaff = filteredStaff.filter(staff => 
        staff.department.includes(department)
      );
    }
    
    // 返回员工列表
    return NextResponse.json({
      code: 0,
      message: '获取员工列表成功',
      data: {
        staff: filteredStaff,
        total: filteredStaff.length
      }
    });
  } catch (error) {
    console.error('获取员工列表失败:', error);
    return NextResponse.json(
      { code: 500, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST 创建新员工
export async function POST(request: Request) {
  try {
    // 解析请求体
    const body = await request.json();
    
    // 获取租户ID - 默认为1 (静心瑜伽馆)
    let tenantId = 1;
    const tenantIdHeader = request.headers.get('X-Tenant-ID');
    if (tenantIdHeader) {
      tenantId = parseInt(tenantIdHeader);
    }
    
    // 创建新员工记录
    const newStaff: Staff = {
      ...body,
      id: STAFF_LIST.length + 1,
      tenant_id: tenantId,
      status: body.status || 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // 将新员工添加到模拟数据中
    STAFF_LIST.push(newStaff);
    
    // 返回创建成功的响应
    return NextResponse.json({
      code: 0,
      message: '创建员工成功',
      data: newStaff
    });
  } catch (error) {
    console.error('创建员工失败:', error);
    return NextResponse.json(
      { code: 500, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}