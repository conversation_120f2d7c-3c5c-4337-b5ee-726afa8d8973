"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { useState } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface MemberActivityChartProps {
  className?: string
}

export function MemberActivityChart({ className }: MemberActivityChartProps) {
  const [timeRange, setTimeRange] = useState("month")
  
  // 模拟会员活跃度数据 - 每周访问次数分布
  const weeklyVisitsData = [
    { range: "0次", percentage: 15, color: "#EF4444" },
    { range: "1-2次", percentage: 35, color: "#F59E0B" },
    { range: "3-4次", percentage: 30, color: "#10B981" },
    { range: "5-6次", percentage: 15, color: "#06B6D4" },
    { range: "7次以上", percentage: 5, color: "#4F46E5" },
  ]
  
  // 模拟会员活跃度数据 - 每月访问次数分布
  const monthlyVisitsData = [
    { range: "0-3次", percentage: 10, color: "#EF4444" },
    { range: "4-7次", percentage: 25, color: "#F59E0B" },
    { range: "8-12次", percentage: 35, color: "#10B981" },
    { range: "13-20次", percentage: 20, color: "#06B6D4" },
    { range: "20次以上", percentage: 10, color: "#4F46E5" },
  ]
  
  // 模拟会员活跃度数据 - 每季度访问次数分布
  const quarterlyVisitsData = [
    { range: "0-10次", percentage: 15, color: "#EF4444" },
    { range: "11-20次", percentage: 30, color: "#F59E0B" },
    { range: "21-30次", percentage: 25, color: "#10B981" },
    { range: "31-40次", percentage: 20, color: "#06B6D4" },
    { range: "40次以上", percentage: 10, color: "#4F46E5" },
  ]
  
  // 根据选择的时间范围获取数据
  const getVisitsData = () => {
    switch (timeRange) {
      case "week":
        return weeklyVisitsData
      case "quarter":
        return quarterlyVisitsData
      case "month":
      default:
        return monthlyVisitsData
    }
  }
  
  const visitsData = getVisitsData()
  
  // 计算活跃会员比例（访问次数大于等于中间值的会员）
  const calculateActivePercentage = () => {
    const middleIndex = Math.floor(visitsData.length / 2)
    return visitsData.slice(middleIndex).reduce((sum, item) => sum + item.percentage, 0)
  }
  
  const activePercentage = calculateActivePercentage()
  
  // 渲染水平条形图
  const renderBarChart = () => {
    return (
      <div className="space-y-4">
        {visitsData.map((item, index) => (
          <div key={index} className="space-y-1">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <div 
                  className="h-3 w-3 rounded-sm" 
                  style={{ backgroundColor: item.color }}
                />
                <span>{item.range}</span>
              </div>
              <span className="font-medium">{item.percentage}%</span>
            </div>
            <div className="h-2 w-full overflow-hidden rounded-full bg-secondary">
              <div 
                className="h-full rounded-full transition-all duration-500" 
                style={{ 
                  width: `${item.percentage}%`, 
                  backgroundColor: item.color 
                }}
              />
            </div>
          </div>
        ))}
      </div>
    )
  }
  
  // 渲染活跃度指标
  const renderActivityMetrics = () => {
    // 计算平均每周访问次数
    let avgVisits = 0
    
    if (timeRange === "week") {
      avgVisits = 3.2
    } else if (timeRange === "month") {
      avgVisits = 10.5
    } else {
      avgVisits = 28.3
    }
    
    return (
      <div className="mt-6 grid grid-cols-3 gap-4">
        <div className="rounded-lg border p-3 text-center">
          <div className="text-2xl font-bold text-primary">{activePercentage}%</div>
          <div className="text-xs text-muted-foreground">活跃会员比例</div>
        </div>
        <div className="rounded-lg border p-3 text-center">
          <div className="text-2xl font-bold text-primary">{avgVisits}</div>
          <div className="text-xs text-muted-foreground">
            平均{timeRange === "week" ? "周" : timeRange === "month" ? "月" : "季度"}访问次数
          </div>
        </div>
        <div className="rounded-lg border p-3 text-center">
          <div className="text-2xl font-bold text-primary">78%</div>
          <div className="text-xs text-muted-foreground">续卡率</div>
        </div>
      </div>
    )
  }
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-base">会员活跃度分析</CardTitle>
          <CardDescription>会员卡使用频率分析</CardDescription>
        </div>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="h-8 w-[120px]">
            <SelectValue placeholder="选择时间范围" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="week">每周</SelectItem>
            <SelectItem value="month">每月</SelectItem>
            <SelectItem value="quarter">每季度</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        {renderBarChart()}
        {renderActivityMetrics()}
      </CardContent>
    </Card>
  )
}
