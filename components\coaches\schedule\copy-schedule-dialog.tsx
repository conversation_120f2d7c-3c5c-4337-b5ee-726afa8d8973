"use client"

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { useState } from "react"
import { DatePicker } from "@/components/coaches/schedule/date-picker"

interface CopyScheduleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CopyScheduleDialog({ open, onOpenChange }: CopyScheduleDialogProps) {
  const [sourceDate, setSourceDate] = useState<Date | undefined>(new Date())
  const [targetDate, setTargetDate] = useState<Date | undefined>(new Date())

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>复制排班</DialogTitle>
          <DialogDescription>将现有排班复制到其他日期或周</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>复制类型</Label>
            <Select defaultValue="day">
              <SelectTrigger>
                <SelectValue placeholder="请选择复制类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">单日复制</SelectItem>
                <SelectItem value="week">整周复制</SelectItem>
                <SelectItem value="custom">自定义范围</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>源日期</Label>
            <DatePicker date={sourceDate} onSelect={setSourceDate} />
          </div>

          <div className="space-y-2">
            <Label>目标日期</Label>
            <DatePicker date={targetDate} onSelect={setTargetDate} />
          </div>

          <div className="space-y-2">
            <Label>复制选项</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="copy-all-coaches" defaultChecked />
                <Label htmlFor="copy-all-coaches" className="font-normal">
                  复制所有教练的排班
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="copy-all-venues" defaultChecked />
                <Label htmlFor="copy-all-venues" className="font-normal">
                  复制所有场地的排班
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="overwrite" />
                <Label htmlFor="overwrite" className="font-normal">
                  覆盖目标日期已有的排班
                </Label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>特定教练（可选）</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="选择特定教练" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有教练</SelectItem>
                <SelectItem value="1">张教练</SelectItem>
                <SelectItem value="2">李教练</SelectItem>
                <SelectItem value="3">王教练</SelectItem>
                <SelectItem value="4">赵教练</SelectItem>
                <SelectItem value="5">刘教练</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>特定场地（可选）</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="选择特定场地" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有场地</SelectItem>
                <SelectItem value="1">1号瑜伽室</SelectItem>
                <SelectItem value="2">2号瑜伽室</SelectItem>
                <SelectItem value="3">3号瑜伽室</SelectItem>
                <SelectItem value="4">空中瑜伽室</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>重复次数（可选）</Label>
            <Input type="number" min="1" defaultValue="1" />
            <p className="text-xs text-muted-foreground">设置要连续复制的次数，例如连续复制4周</p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button>复制排班</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

