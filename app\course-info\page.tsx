"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Search, 
  Calendar, 
  Clock, 
  Filter, 
  Download, 
  ChevronLeft, 
  ChevronRight,
  CreditCard,
  User,
  Users,
  CalendarDays
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// 模拟课程数据
const courseData = [
  {
    id: "YG001",
    name: "基础瑜伽入门",
    time: "10:00-11:30",
    date: "2023-05-16",
    weekday: "星期五",
    coach: "李教练",
    venue: "1号瑜伽室",
    booked: 12,
    capacity: 15,
    status: "可预约"
  },
  {
    id: "YG002",
    name: "高级瑜伽进阶",
    time: "14:00-15:30",
    date: "2023-05-16",
    weekday: "星期五",
    coach: "王教练",
    venue: "2号瑜伽室",
    booked: 8,
    capacity: 10,
    status: "可预约"
  },
  {
    id: "YG003",
    name: "阴瑜伽放松",
    time: "16:00-17:00",
    date: "2023-05-16",
    weekday: "星期五",
    coach: "张教练",
    venue: "3号瑜伽室",
    booked: 15,
    capacity: 15,
    status: "已满"
  },
  {
    id: "YG004",
    name: "孕产瑜伽",
    time: "18:30-19:30",
    date: "2023-05-16",
    weekday: "星期五",
    coach: "刘教练",
    venue: "1号瑜伽室",
    booked: 6,
    capacity: 10,
    status: "可预约"
  }
];

export default function CourseInfoPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [dateFilter, setDateFilter] = useState("today")
  const [statusFilter, setStatusFilter] = useState("all")

  // 获取状态样式
  const getStatusStyle = (status: string) => {
    switch(status) {
      case "可预约":
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case "已满":
        return "bg-amber-100 text-amber-800 hover:bg-amber-100";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">课程信息</h1>
          <p className="text-muted-foreground">
            查看和管理课程信息
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            课程日历
          </Button>
          <Button>
            <Users className="mr-2 h-4 w-4" />
            会员预约
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
        <div className="flex-1 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索课程名称/教练..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="选择日期" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">今日</SelectItem>
                <SelectItem value="tomorrow">明日</SelectItem>
                <SelectItem value="week">本周</SelectItem>
                <SelectItem value="month">本月</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="课程状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="available">可预约</SelectItem>
                <SelectItem value="full">已满</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            筛选
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="all">全部课程</TabsTrigger>
          <TabsTrigger value="today">今日课程</TabsTrigger>
          <TabsTrigger value="tomorrow">明日课程</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle>课程信息</CardTitle>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm">2023年5月16日 星期五</span>
                  <Button variant="outline" size="sm">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                显示90天内的系统自动取消的课程信息
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>课程名称</TableHead>
                    <TableHead>课程时间</TableHead>
                    <TableHead>教练</TableHead>
                    <TableHead className="text-right">预约情况</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {courseData.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell className="font-medium">{course.name}</TableCell>
                      <TableCell>{course.time}</TableCell>
                      <TableCell>{course.coach}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <span>{course.booked}/{course.capacity}</span>
                          <Badge className={getStatusStyle(course.status)}>
                            {course.status}
                          </Badge>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="today" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>今日课程</CardTitle>
              <CardDescription>
                2023年5月16日 星期五的课程安排
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>课程名称</TableHead>
                    <TableHead>课程时间</TableHead>
                    <TableHead>教练</TableHead>
                    <TableHead className="text-right">预约情况</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {courseData.map((course) => (
                    <TableRow key={course.id}>
                      <TableCell className="font-medium">{course.name}</TableCell>
                      <TableCell>{course.time}</TableCell>
                      <TableCell>{course.coach}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <span>{course.booked}/{course.capacity}</span>
                          <Badge className={getStatusStyle(course.status)}>
                            {course.status}
                          </Badge>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="tomorrow" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>明日课程</CardTitle>
              <CardDescription>
                2023年5月17日 星期六的课程安排
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <CalendarDays className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
                <p>明日暂无课程安排</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
