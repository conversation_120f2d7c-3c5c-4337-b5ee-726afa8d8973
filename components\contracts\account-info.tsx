"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Wallet, UserCheck, FileSignature, RefreshCw, AlertCircle, Clock, CheckCircle, XCircle } from "lucide-react"
import { RechargeDialog } from "@/components/contracts/recharge-dialog"
import { RealNameAuthDialog } from "@/components/contracts/real-name-auth-dialog"
import { SignatureUploadDialog } from "@/components/contracts/signature-upload-dialog"

interface AccountInfoProps {
  className?: string
}

export function AccountInfo({ className }: AccountInfoProps) {
  // 账户状态
  const [balance, setBalance] = useState(0)
  const [isRealNameVerified, setIsRealNameVerified] = useState(false)
  const [hasSignature, setHasSignature] = useState(false)
  const [contractsUsed, setContractsUsed] = useState(0)
  const [contractsLimit, setContractsLimit] = useState(100)
  
  // 对话框状态
  const [showRechargeDialog, setShowRechargeDialog] = useState(false)
  const [showAuthDialog, setShowAuthDialog] = useState(false)
  const [showSignatureDialog, setShowSignatureDialog] = useState(false)
  
  // 交易记录
  const transactions = [
    {
      id: "T001",
      type: "RECHARGE",
      amount: 500,
      date: "2023-05-15 10:30",
      status: "COMPLETED",
      description: "账户充值"
    },
    {
      id: "T002",
      type: "USAGE",
      amount: -10,
      date: "2023-05-16 14:20",
      status: "COMPLETED",
      description: "合同签署费用"
    },
    {
      id: "T003",
      type: "USAGE",
      amount: -10,
      date: "2023-05-18 09:45",
      status: "COMPLETED",
      description: "合同签署费用"
    },
    {
      id: "T004",
      type: "RECHARGE",
      amount: 200,
      date: "2023-06-01 16:30",
      status: "COMPLETED",
      description: "账户充值"
    },
    {
      id: "T005",
      type: "USAGE",
      amount: -10,
      date: "2023-06-05 11:15",
      status: "COMPLETED",
      description: "合同签署费用"
    }
  ]
  
  // 签章列表
  const signatures = [
    {
      id: "S001",
      name: "个人签名",
      type: "HANDWRITTEN",
      createdAt: "2023-05-10 14:30",
      previewUrl: "/signatures/signature1.png"
    },
    {
      id: "S002",
      name: "公司印章",
      type: "UPLOADED",
      createdAt: "2023-05-12 10:15",
      previewUrl: "/signatures/signature2.png"
    }
  ]
  
  // 处理充值成功
  const handleRechargeSuccess = (amount: number) => {
    setBalance(prevBalance => prevBalance + amount)
  }
  
  // 处理实名认证成功
  const handleAuthSuccess = () => {
    setIsRealNameVerified(true)
  }
  
  // 处理签章上传成功
  const handleSignatureSuccess = () => {
    setHasSignature(true)
  }
  
  // 获取交易类型标签
  const getTransactionBadge = (type: string) => {
    switch (type) {
      case "RECHARGE":
        return <Badge className="bg-green-500">充值</Badge>
      case "USAGE":
        return <Badge className="bg-blue-500">消费</Badge>
      case "REFUND":
        return <Badge className="bg-orange-500">退款</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }
  
  // 获取交易状态标签
  const getTransactionStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <Badge variant="outline" className="border-green-500 text-green-500">已完成</Badge>
      case "PENDING":
        return <Badge variant="outline" className="border-yellow-500 text-yellow-500">处理中</Badge>
      case "FAILED":
        return <Badge variant="outline" className="border-red-500 text-red-500">失败</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className={className}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        {/* 账户余额卡片 */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">账户余额</CardTitle>
            <CardDescription>法大大电子合同账户余额</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">¥{balance.toFixed(2)}</div>
            <p className="text-sm text-muted-foreground mt-1">
              每次签署合同消费 ¥10.00
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => setShowRechargeDialog(true)} className="w-full">
              <Wallet className="mr-2 h-4 w-4" />
              充值
            </Button>
          </CardFooter>
        </Card>
        
        {/* 实名认证卡片 */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">实名认证</CardTitle>
            <CardDescription>法大大电子合同实名认证状态</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {isRealNameVerified ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="font-medium">已完成实名认证</span>
                </>
              ) : (
                <>
                  <AlertCircle className="h-5 w-5 text-yellow-500" />
                  <span className="font-medium">未完成实名认证</span>
                </>
              )}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {isRealNameVerified 
                ? "您已完成实名认证，可以正常使用电子签章功能" 
                : "完成实名认证后才能使用电子签章功能"}
            </p>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={() => setShowAuthDialog(true)} 
              variant={isRealNameVerified ? "outline" : "default"}
              className="w-full"
            >
              <UserCheck className="mr-2 h-4 w-4" />
              {isRealNameVerified ? "查看认证信息" : "立即认证"}
            </Button>
          </CardFooter>
        </Card>
        
        {/* 电子签章卡片 */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">电子签章</CardTitle>
            <CardDescription>管理您的电子签章</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {hasSignature ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="font-medium">已上传 {signatures.length} 个签章</span>
                </>
              ) : (
                <>
                  <AlertCircle className="h-5 w-5 text-yellow-500" />
                  <span className="font-medium">未上传电子签章</span>
                </>
              )}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              {hasSignature 
                ? "您可以使用已上传的签章签署合同" 
                : "上传电子签章后可以更便捷地签署合同"}
            </p>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={() => setShowSignatureDialog(true)} 
              disabled={!isRealNameVerified}
              className="w-full"
            >
              <FileSignature className="mr-2 h-4 w-4" />
              {hasSignature ? "管理签章" : "上传签章"}
            </Button>
          </CardFooter>
        </Card>
      </div>
      
      {/* 使用情况卡片 */}
      <Card className="mb-4">
        <CardHeader>
          <CardTitle>合同使用情况</CardTitle>
          <CardDescription>本月已使用 {contractsUsed} 份合同，剩余 {contractsLimit - contractsUsed} 份</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{contractsUsed}/{contractsLimit}</span>
              <span className="text-sm text-muted-foreground">{Math.round((contractsUsed / contractsLimit) * 100)}%</span>
            </div>
            <Progress value={(contractsUsed / contractsLimit) * 100} />
          </div>
        </CardContent>
      </Card>
      
      {/* 详细信息标签页 */}
      <Tabs defaultValue="transactions" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="transactions">交易记录</TabsTrigger>
          <TabsTrigger value="signatures">我的签章</TabsTrigger>
        </TabsList>
        
        {/* 交易记录标签内容 */}
        <TabsContent value="transactions" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>交易记录</CardTitle>
              <CardDescription>查看您的账户交易记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {transactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-md">
                    <div className="flex flex-col">
                      <div className="flex items-center space-x-2">
                        {getTransactionBadge(transaction.type)}
                        <span className="font-medium">{transaction.description}</span>
                      </div>
                      <div className="text-sm text-muted-foreground flex items-center mt-1">
                        <Clock className="h-3 w-3 mr-1" />
                        {transaction.date}
                      </div>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className={`font-semibold ${transaction.amount > 0 ? 'text-green-600' : 'text-blue-600'}`}>
                        {transaction.amount > 0 ? '+' : ''}{transaction.amount.toFixed(2)}
                      </span>
                      <div className="mt-1">
                        {getTransactionStatusBadge(transaction.status)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
              <Button variant="outline" size="sm">
                查看更多
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* 我的签章标签内容 */}
        <TabsContent value="signatures" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>我的签章</CardTitle>
              <CardDescription>管理您的电子签章</CardDescription>
            </CardHeader>
            <CardContent>
              {signatures.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {signatures.map((signature) => (
                    <div key={signature.id} className="border rounded-md p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-medium">{signature.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {signature.type === "HANDWRITTEN" ? "手写签名" : "上传签章"}
                          </p>
                        </div>
                        <Badge variant="outline">默认</Badge>
                      </div>
                      <div className="bg-gray-50 rounded-md p-4 flex items-center justify-center h-20 mb-2">
                        {signature.previewUrl ? (
                          <img 
                            src={signature.previewUrl} 
                            alt={signature.name} 
                            className="max-h-full max-w-full object-contain"
                          />
                        ) : (
                          <span className="text-sm text-muted-foreground">无预览</span>
                        )}
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground">
                          创建于 {signature.createdAt}
                        </span>
                        <Button variant="ghost" size="sm">
                          设为默认
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8">
                  <FileSignature className="h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-4 text-lg font-semibold">没有签章</h3>
                  <p className="mt-2 text-sm text-muted-foreground text-center">
                    您还没有上传任何电子签章，点击下方按钮添加
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button 
                onClick={() => setShowSignatureDialog(true)} 
                disabled={!isRealNameVerified}
                className="w-full"
              >
                <FileSignature className="mr-2 h-4 w-4" />
                添加签章
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* 充值对话框 */}
      <RechargeDialog
        open={showRechargeDialog}
        onOpenChange={setShowRechargeDialog}
        onRechargeSuccess={handleRechargeSuccess}
        currentBalance={balance}
      />
      
      {/* 实名认证对话框 */}
      <RealNameAuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        onAuthSuccess={handleAuthSuccess}
      />
      
      {/* 签章上传对话框 */}
      <SignatureUploadDialog
        open={showSignatureDialog}
        onOpenChange={setShowSignatureDialog}
        onUploadSuccess={handleSignatureSuccess}
      />
    </div>
  )
}
