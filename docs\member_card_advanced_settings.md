# 会员卡高级设置系统设计文档

## 概述

本文档详细说明了会员卡高级设置系统的数据库设计和功能实现，基于前端页面的真实字段分析而设计。

## 数据表结构

### 1. member_card_advanced_settings（会员卡高级设置主表）

**功能说明：** 存储会员卡的核心高级设置，包括请假规则、开卡设置、预约限制等。

**字段详解：**

#### 请假选项设置
- `leave_option`: 请假选项（枚举值）
  - `no_allow`: 不允许请假
  - `no_limit`: 不限制请假
  - `limited`: 有限制请假
- `leave_times_limit`: 有效期内可请假次数（仅当leave_option=limited时有效）
- `leave_days_limit`: 有效期内累计请假天数（仅当leave_option=limited时有效）

#### 开卡设置
- `auto_activate_days`: 发卡后多少天内未开卡则自动开卡（默认120天）

#### 预约限制设置
- `max_people_per_class`: 单节课可约人数上限
- `daily_booking_limit`: 每日可约次数上限
- `weekly_booking_limit`: 每周可约次数上限
- `weekly_calculation_type`: 每周计算方式
  - `natural_week`: 自然周计算（每周一重置）
  - `card_cycle`: 购卡周期计算（从购卡日开始每7天为一个周期）
- `monthly_booking_limit`: 每月可约次数上限
- `monthly_calculation_type`: 每月计算方式
  - `natural_month`: 自然月计算（每月1号重置）
  - `card_cycle`: 购卡周期计算（从购卡日开始每30天为一个周期）

#### 预约天数设置
- `advance_booking_days`: 可预约天数（NULL表示不限制，0表示当天）
- `advance_booking_unlimited`: 是否不限制预约天数

#### 可用时间设置
- `custom_time_enabled`: 是否启用自定义时段
- `available_days`: 可用星期配置（JSON格式，存储周一到周日的布尔值）
- `available_time_slots`: 可用时间段配置（JSON格式，最多3个时段）

### 2. member_card_user_settings（会员卡用卡人设置表）

**功能说明：** 存储与用卡人行为相关的限制设置。

**字段详解：**

#### 约课间隔限制
- `booking_interval_enabled`: 是否启用约课间隔限制
- `booking_interval_minutes`: 约课间隔时间（分钟），防止连续约课

#### 预约次数限制
- `pending_booking_limit`: 未结束课程预约次数限制（0表示不限制）

#### 取消预约限制
- `cancel_limit_enabled`: 是否启用取消预约次数限制
- `cancel_limit_count`: 取消预约次数限制
- `cancel_limit_period`: 取消限制周期（day/week/month）

#### 同类课程限制
- `same_course_daily_limit`: 同类课程每日限制次数

#### 高峰时段限制
- `peak_time_enabled`: 是否启用高峰时段限制
- `peak_start_time`: 高峰时段开始时间
- `peak_end_time`: 高峰时段结束时间
- `peak_daily_limit`: 高峰时段每日预约限制

#### 预约优先级
- `priority_enabled`: 是否启用预约优先级
- `priority_hours`: 优先预约时间（小时）
- `priority_description`: 优先级描述

### 3. member_card_course_settings（会员卡课程设置表）

**功能说明：** 存储会员卡与课程关联的基本设置。

**字段详解：**

#### 消耗规则设置
- `consumption_rule`: 消耗规则（枚举值）
  - `AVERAGE`: 平均消耗（总价值平均分配）
  - `FIXED`: 固定消耗（每次固定消耗）
  - `CUSTOM`: 自定义消耗（按课程类型自定义）
- `consumption_description`: 消耗规则描述

#### 赠送设置
- `gift_class_count`: 赠送课时数
- `gift_value_coefficient`: 赠送课程价值系数

#### 适用课程设置
- `all_courses_enabled`: 是否适用所有课程

### 4. member_card_course_associations（会员卡课程关联明细表）

**功能说明：** 存储会员卡与具体课程类型的关联关系和消耗设置。

**字段详解：**
- `course_type_id`: 课程类型ID
- `is_enabled`: 是否启用此课程类型
- `consumption_times`: 消耗次数（支持小数，如0.8表示VIP折扣）
- `course_type_name`: 课程类型名称（冗余存储）
- `course_duration`: 课程时长（冗余存储）

### 5. member_card_sales_settings（会员卡销售设置表）

**功能说明：** 存储会员卡的销售相关设置（扩展功能）。

## 业务逻辑说明

### 1. 请假功能
- **不允许**：会员卡不支持请假功能
- **不限制**：会员可以无限次请假
- **有限制**：会员在有效期内可请假指定次数，累计指定天数

### 2. 预约限制计算方式
- **自然周/月计算**：按照日历的自然周期重置计数
- **购卡周期计算**：从购卡日开始计算周期，更加个性化

### 3. 消耗规则
- **平均消耗**：适用于期限卡，总价值平均分配到每次使用
- **固定消耗**：适用于次卡，每次固定消耗1次
- **自定义消耗**：适用于储值卡，不同课程类型消耗不同金额

### 4. 高峰时段限制
- 在指定的高峰时段内限制预约次数
- 避免会员在热门时段占用过多资源

### 5. 预约优先级
- 给予尚未参加过特定类型课程的会员优先预约权
- 确保公平性，避免少数会员反复预约同类课程

## API接口设计

### 获取高级设置
```
GET /api/member-cards/{id}/advanced-settings
```

**返回数据结构：**
```json
{
  "code": 200,
  "data": {
    "advanced": {...},           // 卡相关设置
    "user": {...},              // 用卡人设置
    "course": {...},            // 课程设置
    "courseAssociations": [...], // 课程关联明细
    "sales": {...}              // 销售设置
  },
  "msg": "获取成功"
}
```

### 更新高级设置
```
PUT /api/member-cards/{id}/advanced-settings
```

**请求数据结构：**
```json
{
  "advanced": {...},           // 卡相关设置
  "user": {...},              // 用卡人设置
  "course": {...},            // 课程设置
  "courseAssociations": [...], // 课程关联明细
  "sales": {...}              // 销售设置
}
```

## 数据示例

### 时间段配置示例
```json
{
  "available_days": {
    "monday": true,
    "tuesday": true,
    "wednesday": true,
    "thursday": true,
    "friday": true,
    "saturday": true,
    "sunday": true
  },
  "available_time_slots": [
    {
      "startHour": "06",
      "startMinute": "00",
      "endHour": "22",
      "endMinute": "00"
    },
    {
      "startHour": "08",
      "startMinute": "00",
      "endHour": "20",
      "endMinute": "00"
    }
  ]
}
```

## 使用场景

### 1. 年卡设置
- 请假：不限制
- 预约：每日3次，每周4次，每月5次
- 时间：全时段可用
- 课程：所有课程，平均消耗

### 2. 体验卡设置
- 请假：不允许
- 预约：严格限制，当天预约
- 时间：全时段可用
- 课程：指定课程，固定消耗

### 3. VIP年卡设置
- 请假：不限制
- 预约：更高限额，优先预约
- 时间：全时段可用，包括深夜
- 课程：所有课程，VIP折扣

## 注意事项

1. **数据一致性**：所有设置表都通过card_type_id关联，确保数据一致性
2. **默认值**：所有字段都有合理的默认值，避免空值问题
3. **扩展性**：表结构设计考虑了未来功能扩展的需要
4. **性能优化**：创建了必要的索引，提高查询性能
5. **业务逻辑**：在应用层实现复杂的业务逻辑验证

## 实现状态

### ✅ 已完成功能

1. **数据库表结构设计** - 完全基于页面真实字段分析
   - ✅ member_card_advanced_settings - 卡相关设置（请假、开卡、预约限制等）
   - ✅ member_card_user_settings - 用卡人设置（约课间隔、取消限制等）
   - ✅ member_card_course_settings - 课程设置（消耗规则、赠送设置等）
   - ✅ member_card_course_associations - 课程关联明细（具体课程消耗配置）
   - ✅ member_card_sales_settings - 销售设置（折扣、促销等）

2. **字段完全匹配页面设计**
   - ✅ 请假选项：不允许/不限制/有限制（包含次数和天数限制）
   - ✅ 开卡设置：自动开卡天数配置
   - ✅ 预约限制：每日/每周/每月限制，支持自然周期和购卡周期
   - ✅ 预约天数：可预约天数限制或不限制
   - ✅ 可用时间：自定义时段配置（周一到周日，最多3个时段）
   - ✅ 约课间隔：防止连续约课的时间间隔
   - ✅ 高峰时段：高峰时段限制和每日预约次数
   - ✅ 预约优先级：优先预约时间和描述
   - ✅ 消耗规则：平均消耗/固定消耗/自定义消耗
   - ✅ 课程关联：支持不同课程类型的差异化消耗设置

3. **完整的种子数据**
   - ✅ 8种会员卡类型的完整高级设置
   - ✅ 年卡、半年卡、季卡、月卡、次卡、储值卡、体验卡、VIP年卡
   - ✅ 每种卡都有差异化的设置策略
   - ✅ 真实的课程关联数据（基于现有coursetype表）

4. **API接口实现**
   - ✅ GET /api/member-cards/[id]/advanced-settings - 获取完整高级设置
   - ✅ PUT /api/member-cards/[id]/advanced-settings - 更新高级设置
   - ✅ 结构化数据返回（advanced/user/course/courseAssociations/sales）
   - ✅ 事务处理确保数据一致性

5. **数据验证**
   - ✅ 外键约束确保数据完整性
   - ✅ 枚举类型确保数据规范性
   - ✅ JSON字段存储复杂配置
   - ✅ 索引优化查询性能

### 🎯 页面字段分析结果

通过仔细分析 `add-member-card-dialog.tsx` 和 `edit-member-card-dialog.tsx` 文件，确认了以下关键设计意图：

1. **请假功能**：支持三种模式（不允许/不限制/有限制），有限制时需要配置次数和天数
2. **预约限制**：支持多维度限制（每日/每周/每月），计算方式支持自然周期和购卡周期
3. **时间段配置**：支持自定义可用时间段，最多3个时段，按星期配置
4. **消耗规则**：支持三种消耗模式，适应不同卡类型的业务需求
5. **课程关联**：支持精细化的课程消耗配置，不同课程可设置不同消耗倍数

### 📊 数据示例

**年卡高级设置示例：**
```json
{
  "advanced": {
    "leave_option": "no_limit",
    "auto_activate_days": 120,
    "daily_booking_limit": 3,
    "weekly_booking_limit": 4,
    "monthly_booking_limit": 5,
    "advance_booking_unlimited": true
  },
  "user": {
    "booking_interval_enabled": false,
    "peak_time_enabled": false,
    "priority_enabled": false
  },
  "course": {
    "consumption_rule": "AVERAGE",
    "all_courses_enabled": true,
    "gift_class_count": 0
  }
}
```

**VIP年卡高级设置示例：**
```json
{
  "advanced": {
    "leave_option": "no_limit",
    "max_people_per_class": 2,
    "daily_booking_limit": 5,
    "custom_time_enabled": true,
    "available_time_slots": [
      {"startHour": "06", "startMinute": "00", "endHour": "23", "endMinute": "00"}
    ]
  },
  "user": {
    "priority_enabled": true,
    "priority_hours": 48,
    "priority_description": "VIP会员优先预约48小时"
  },
  "course": {
    "consumption_rule": "AVERAGE",
    "gift_class_count": 5,
    "gift_value_coefficient": 1.2
  }
}
```

### 🚀 下一步建议

1. **前端集成**：将高级设置API集成到会员卡编辑页面
2. **UI组件**：实现高级设置的可视化配置界面
3. **业务逻辑**：在预约系统中实现这些规则的验证
4. **测试完善**：添加完整的单元测试和集成测试

现在会员卡高级设置系统已经完全按照页面真实字段建立，数据库结构完整，API接口正常工作！🎉
