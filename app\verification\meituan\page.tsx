"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  QrCode,
  Search,
  Check,
  X,
  AlertCircle,
  FileText,
  Clock,
  Download,
  Upload,
  Printer,
  Bell,
  BellOff,
} from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function MeituanVerificationPage() {
  const [verificationCode, setVerificationCode] = useState("")
  const [verificationResult, setVerificationResult] = useState<null | {
    success: boolean
    message: string
    data?: {
      code: string
      product: string
      price: string
      validUntil: string
      customer?: {
        name: string
        phone: string
        avatar: string
      }
    }
  }>(null)

  const [recentVerifications] = useState([
    {
      id: "**********",
      code: "**********",
      product: "基础瑜伽体验课（60分钟）",
      price: "¥99",
      status: "success",
      verifiedAt: "2025-03-28 14:30:25",
      customer: {
        name: "张三",
        phone: "138****1234",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "MT23456789",
      code: "MT23456789",
      product: "高级瑜伽单次课（90分钟）",
      price: "¥129",
      status: "success",
      verifiedAt: "2025-03-28 11:15:42",
      customer: {
        name: "李四",
        phone: "139****5678",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "MT34567890",
      code: "MT34567890",
      product: "瑜伽垫+瑜伽服套装",
      price: "¥299",
      status: "failed",
      verifiedAt: "2025-03-28 09:45:18",
      customer: {
        name: "王五",
        phone: "137****9012",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
  ])

  const [date, setDate] = useState<Date | undefined>(new Date())
  const [isNotificationEnabled, setIsNotificationEnabled] = useState(true)
  const [isPrintEnabled, setIsPrintEnabled] = useState(true)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [selectedVerification, setSelectedVerification] = useState<any>(null)
  const [batchCodes, setBatchCodes] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })

  const handleVerification = () => {
    // 模拟核销验证
    if (verificationCode.trim() === "") {
      setVerificationResult({
        success: false,
        message: "请输入有效的券码",
      })
      return
    }

    // 模拟成功核销
    if (verificationCode === "**********") {
      setVerificationResult({
        success: true,
        message: "核销成功",
        data: {
          code: "**********",
          product: "基础瑜伽体验课（60分钟）",
          price: "¥99",
          validUntil: "2025-04-30",
          customer: {
            name: "张三",
            phone: "138****1234",
            avatar: "/placeholder.svg?height=32&width=32",
          },
        },
      })
    } else {
      // 模拟失败核销
      setVerificationResult({
        success: false,
        message: "券码无效或已被使用",
      })
    }
  }

  const handleScanQRCode = () => {
    // 模拟扫码核销
    alert("调用摄像头扫描二维码")
    // 实际项目中，这里会调用摄像头API进行扫码
  }

  const handleBatchVerification = () => {
    if (!batchCodes.trim()) {
      alert("请输入券码")
      return
    }

    const codes = batchCodes.split("\n").filter((code) => code.trim() !== "")
    alert(`将批量核销 ${codes.length} 个券码`)
    // 实际项目中，这里会调用批量核销API
  }

  const handleViewDetail = (verification: any) => {
    setSelectedVerification(verification)
    setIsDetailDialogOpen(true)
  }

  const handlePrint = () => {
    alert("打印核销凭证")
    // 实际项目中，这里会调用打印API
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">美团券码核销</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsPrintEnabled(!isPrintEnabled)}>
            {isPrintEnabled ? (
              <Printer className="h-4 w-4 mr-2" />
            ) : (
              <Printer className="h-4 w-4 mr-2 text-muted-foreground" />
            )}
            {isPrintEnabled ? "打印已启用" : "打印已禁用"}
          </Button>
          <Button variant="outline" size="sm" onClick={() => setIsNotificationEnabled(!isNotificationEnabled)}>
            {isNotificationEnabled ? <Bell className="h-4 w-4 mr-2" /> : <BellOff className="h-4 w-4 mr-2" />}
            {isNotificationEnabled ? "通知已启用" : "通知已禁用"}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="manual" className="space-y-4">
        <TabsList>
          <TabsTrigger value="manual">手动输入核销</TabsTrigger>
          <TabsTrigger value="scan">扫码核销</TabsTrigger>
          <TabsTrigger value="batch">批量核销</TabsTrigger>
          <TabsTrigger value="history">核销历史</TabsTrigger>
          <TabsTrigger value="orders">美团订单查询</TabsTrigger>
        </TabsList>

        <TabsContent value="manual" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>手动输入券码</CardTitle>
              <CardDescription>输入美团券码进行核销</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input
                    placeholder="请输入美团券码"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                  />
                </div>
                <Button onClick={handleVerification}>核销</Button>
              </div>

              {verificationResult && (
                <Card className={verificationResult.success ? "border-green-500" : "border-red-500"}>
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      <div className={`rounded-full p-2 ${verificationResult.success ? "bg-green-100" : "bg-red-100"}`}>
                        {verificationResult.success ? (
                          <Check className="h-6 w-6 text-green-600" />
                        ) : (
                          <X className="h-6 w-6 text-red-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className={`font-medium ${verificationResult.success ? "text-green-600" : "text-red-600"}`}>
                          {verificationResult.success ? "核销成功" : "核销失败"}
                        </h3>
                        <p className="text-sm text-muted-foreground">{verificationResult.message}</p>

                        {verificationResult.data && (
                          <div className="mt-4 space-y-3">
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div>
                                <span className="text-muted-foreground">券码：</span>
                                <span className="font-medium">{verificationResult.data.code}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">价格：</span>
                                <span className="font-medium">{verificationResult.data.price}</span>
                              </div>
                              <div className="col-span-2">
                                <span className="text-muted-foreground">商品：</span>
                                <span className="font-medium">{verificationResult.data.product}</span>
                              </div>
                              <div className="col-span-2">
                                <span className="text-muted-foreground">有效期至：</span>
                                <span className="font-medium">{verificationResult.data.validUntil}</span>
                              </div>
                            </div>

                            {verificationResult.data.customer && (
                              <div className="flex items-center gap-3 pt-2 border-t">
                                <Avatar>
                                  <AvatarImage src={verificationResult.data.customer.avatar} />
                                  <AvatarFallback>{verificationResult.data.customer.name[0]}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{verificationResult.data.customer.name}</div>
                                  <div className="text-sm text-muted-foreground">
                                    {verificationResult.data.customer.phone}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                  {verificationResult.success && (
                    <CardFooter className="flex justify-end gap-2 pt-0">
                      <Button variant="outline" size="sm" onClick={handlePrint}>
                        <Printer className="h-4 w-4 mr-2" />
                        打印凭证
                      </Button>
                      <Button size="sm">
                        <Check className="h-4 w-4 mr-2" />
                        完成
                      </Button>
                    </CardFooter>
                  )}
                </Card>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>最近核销记录</CardTitle>
              <CardDescription>显示最近的美团券码核销记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>券码</TableHead>
                    <TableHead>商品</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>核销时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentVerifications.map((verification) => (
                    <TableRow key={verification.id}>
                      <TableCell className="font-medium">{verification.code}</TableCell>
                      <TableCell>{verification.product}</TableCell>
                      <TableCell>{verification.price}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={verification.customer.avatar} />
                            <AvatarFallback>{verification.customer.name[0]}</AvatarFallback>
                          </Avatar>
                          <span>{verification.customer.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{verification.verifiedAt}</TableCell>
                      <TableCell>
                        <Badge variant={verification.status === "success" ? "default" : "destructive"}>
                          {verification.status === "success" ? "成功" : "失败"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" onClick={() => handleViewDetail(verification)}>
                          详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scan" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>扫码核销</CardTitle>
              <CardDescription>使用摄像头扫描美团券码进行核销</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-12 text-center">
                <QrCode className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="font-medium text-lg mb-2">扫描美团券码</h3>
                <p className="text-sm text-muted-foreground mb-4">点击下方按钮启动摄像头扫描美团券码</p>
                <Button onClick={handleScanQRCode}>
                  <QrCode className="mr-2 h-4 w-4" />
                  开始扫码
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-2">
                  <Switch id="auto-verify" checked={true} />
                  <Label htmlFor="auto-verify">自动核销</Label>
                </div>
                <div className="flex items-center gap-2">
                  <Switch id="continuous-scan" checked={true} />
                  <Label htmlFor="continuous-scan">连续扫码</Label>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    id="sound-notification"
                    checked={isNotificationEnabled}
                    onCheckedChange={setIsNotificationEnabled}
                  />
                  <Label htmlFor="sound-notification">声音提示</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>扫码历史</CardTitle>
              <CardDescription>本次扫码核销的历史记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">
                      今日已扫码：<span className="font-bold">12</span> 次
                    </p>
                    <p className="text-sm text-muted-foreground">
                      成功：<span className="text-green-600">10</span> 次，失败：<span className="text-red-600">2</span>{" "}
                      次
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    <FileText className="h-4 w-4 mr-2" />
                    导出记录
                  </Button>
                </div>

                <div className="border rounded-md">
                  <div className="p-3 border-b bg-muted/50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-green-50">
                          成功
                        </Badge>
                        <span className="font-medium">MT98765432</span>
                      </div>
                      <span className="text-sm text-muted-foreground">2025-03-30 10:45:12</span>
                    </div>
                    <p className="mt-1 text-sm">阴瑜伽单次课（90分钟）- ¥119</p>
                  </div>

                  <div className="p-3 border-b">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-green-50">
                          成功
                        </Badge>
                        <span className="font-medium">MT87654321</span>
                      </div>
                      <span className="text-sm text-muted-foreground">2025-03-30 10:42:35</span>
                    </div>
                    <p className="mt-1 text-sm">基础瑜伽体验课（60分钟）- ¥99</p>
                  </div>

                  <div className="p-3 border-b">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="bg-red-50 text-red-600">
                          失败
                        </Badge>
                        <span className="font-medium">MT76543210</span>
                      </div>
                      <span className="text-sm text-muted-foreground">2025-03-30 10:38:21</span>
                    </div>
                    <p className="mt-1 text-sm text-red-600">券码已被使用</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="batch" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>批量核销</CardTitle>
              <CardDescription>一次性核销多个美团券码</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="batch-codes">输入券码（每行一个）</Label>
                <Textarea
                  id="batch-codes"
                  placeholder="请输入美团券码，每行一个"
                  rows={6}
                  value={batchCodes}
                  onChange={(e) => setBatchCodes(e.target.value)}
                />
              </div>

              <div className="flex items-center gap-4">
                <Button onClick={handleBatchVerification}>
                  <Check className="mr-2 h-4 w-4" />
                  批量核销
                </Button>
                <Button variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  导入券码
                </Button>
              </div>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>批量核销说明</AlertTitle>
                <AlertDescription>
                  批量核销将一次性处理多个券码，请确保券码格式正确。每个券码独立验证，部分券码验证失败不会影响其他券码的核销。
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>批量核销历史</CardTitle>
              <CardDescription>显示最近的批量核销记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>批次ID</TableHead>
                    <TableHead>核销时间</TableHead>
                    <TableHead>券码数量</TableHead>
                    <TableHead>成功数量</TableHead>
                    <TableHead>失败数量</TableHead>
                    <TableHead>操作人</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">BV20250330001</TableCell>
                    <TableCell>2025-03-30 09:15:42</TableCell>
                    <TableCell>15</TableCell>
                    <TableCell className="text-green-600">13</TableCell>
                    <TableCell className="text-red-600">2</TableCell>
                    <TableCell>管理员</TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        详情
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">BV20250329002</TableCell>
                    <TableCell>2025-03-29 16:45:18</TableCell>
                    <TableCell>8</TableCell>
                    <TableCell className="text-green-600">8</TableCell>
                    <TableCell className="text-red-600">0</TableCell>
                    <TableCell>前台</TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        详情
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>核销历史查询</CardTitle>
              <CardDescription>查询历史核销记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <Label htmlFor="search-query" className="mb-2 block">
                    搜索
                  </Label>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search-query"
                      placeholder="券码/商品名称/用户名"
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block">日期范围</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                        <Clock className="mr-2 h-4 w-4" />
                        {dateRange.from ? (
                          dateRange.to ? (
                            <>
                              {format(dateRange.from, "yyyy-MM-dd")} - {format(dateRange.to, "yyyy-MM-dd")}
                            </>
                          ) : (
                            format(dateRange.from, "yyyy-MM-dd")
                          )
                        ) : (
                          <span>选择日期范围</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="range"
                        defaultMonth={date}
                        selected={dateRange}
                        onSelect={setDateRange}
                        numberOfMonths={2}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <Label htmlFor="status-filter" className="mb-2 block">
                    状态
                  </Label>
                  <Select defaultValue="all">
                    <SelectTrigger id="status-filter" className="w-[180px]">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="success">成功</SelectItem>
                      <SelectItem value="failed">失败</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button>
                    <Search className="mr-2 h-4 w-4" />
                    查询
                  </Button>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  导出结果
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <Checkbox id="select-all" />
                    </TableHead>
                    <TableHead>券码</TableHead>
                    <TableHead>商品</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>核销时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作人</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[
                    ...recentVerifications,
                    {
                      id: "MT45678901",
                      code: "MT45678901",
                      product: "空中瑜伽单次课（90分钟）",
                      price: "¥149",
                      status: "success",
                      verifiedAt: "2025-03-27 16:20:15",
                      customer: {
                        name: "赵六",
                        phone: "136****5678",
                        avatar: "/placeholder.svg?height=32&width=32",
                      },
                    },
                    {
                      id: "MT56789012",
                      code: "MT56789012",
                      product: "瑜伽会员月卡",
                      price: "¥599",
                      status: "success",
                      verifiedAt: "2025-03-27 14:05:33",
                      customer: {
                        name: "钱七",
                        phone: "135****9012",
                        avatar: "/placeholder.svg?height=32&width=32",
                      },
                    },
                  ].map((verification) => (
                    <TableRow key={verification.id}>
                      <TableCell>
                        <Checkbox id={`select-${verification.id}`} />
                      </TableCell>
                      <TableCell className="font-medium">{verification.code}</TableCell>
                      <TableCell>{verification.product}</TableCell>
                      <TableCell>{verification.price}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={verification.customer.avatar} />
                            <AvatarFallback>{verification.customer.name[0]}</AvatarFallback>
                          </Avatar>
                          <span>{verification.customer.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{verification.verifiedAt}</TableCell>
                      <TableCell>
                        <Badge variant={verification.status === "success" ? "default" : "destructive"}>
                          {verification.status === "success" ? "成功" : "失败"}
                        </Badge>
                      </TableCell>
                      <TableCell>管理员</TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" onClick={() => handleViewDetail(verification)}>
                          详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">共 5 条记录，已选择 0 条</div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" disabled>
                    上一页
                  </Button>
                  <Button variant="outline" size="sm">
                    下一页
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>美团订单查询</CardTitle>
              <CardDescription>查询美团平台订单信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input placeholder="输入订单号、手机号或用户名查询" />
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Select defaultValue="7days">
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="选择时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">今天</SelectItem>
                      <SelectItem value="yesterday">昨天</SelectItem>
                      <SelectItem value="7days">最近7天</SelectItem>
                      <SelectItem value="30days">最近30天</SelectItem>
                      <SelectItem value="custom">自定义范围</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select defaultValue="all">
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="订单状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="paid">已支付</SelectItem>
                      <SelectItem value="used">已使用</SelectItem>
                      <SelectItem value="refunded">已退款</SelectItem>
                      <SelectItem value="expired">已过期</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  导出订单
                </Button>
              </div>

              <div className="flex items-center justify-center p-8 border rounded-lg">
                <div className="text-center">
                  <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="font-medium text-lg mb-2">暂无查询结果</h3>
                  <p className="text-sm text-muted-foreground">请输入查询条件并点击查询按钮</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 核销详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>核销详情</DialogTitle>
            <DialogDescription>查看核销记录的详细信息</DialogDescription>
          </DialogHeader>

          {selectedVerification && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Badge variant={selectedVerification.status === "success" ? "default" : "destructive"}>
                  {selectedVerification.status === "success" ? "核销成功" : "核销失败"}
                </Badge>
                <span className="text-sm text-muted-foreground">{selectedVerification.verifiedAt}</span>
              </div>

              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">券码：</span>
                    <span className="font-medium">{selectedVerification.code}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">价格：</span>
                    <span className="font-medium">{selectedVerification.price}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-muted-foreground">商品：</span>
                    <span className="font-medium">{selectedVerification.product}</span>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={selectedVerification.customer.avatar} />
                    <AvatarFallback>{selectedVerification.customer.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{selectedVerification.customer.name}</div>
                    <div className="text-sm text-muted-foreground">{selectedVerification.customer.phone}</div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">操作记录</h4>
                  <div className="text-sm">
                    <p className="flex justify-between">
                      <span>核销操作</span>
                      <span className="text-muted-foreground">{selectedVerification.verifiedAt}</span>
                    </p>
                    <p className="text-muted-foreground">操作人：管理员</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => handlePrint()}>
              <Printer className="mr-2 h-4 w-4" />
              打印凭证
            </Button>
            <Button onClick={() => setIsDetailDialogOpen(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

