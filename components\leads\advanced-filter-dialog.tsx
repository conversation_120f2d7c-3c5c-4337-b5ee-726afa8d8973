"use client"

import { useState } from "react"
import { 
  <PERSON><PERSON>, 
  <PERSON>alogContent, 
  <PERSON>alogDescription, 
  <PERSON><PERSON><PERSON>ooter, 
  <PERSON><PERSON>Header, 
  DialogTitle 
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"

interface AdvancedFilterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AdvancedFilterDialog({ open, onOpenChange }: AdvancedFilterDialogProps) {
  const { toast } = useToast()
  const [filters, setFilters] = useState({
    status: [] as string[],
    source: [] as string[],
    interest: [] as string[],
    assignedTo: [] as string[],
    createdDateStart: "",
    createdDateEnd: "",
    lastFollowUpStart: "",
    lastFollowUpEnd: "",
    hasEmail: false,
    hasFollowUp: false,
  })
  
  // 已选筛选条件计数
  const activeFilterCount = 
    filters.status.length + 
    filters.source.length + 
    filters.interest.length + 
    filters.assignedTo.length + 
    (filters.createdDateStart ? 1 : 0) + 
    (filters.createdDateEnd ? 1 : 0) + 
    (filters.lastFollowUpStart ? 1 : 0) + 
    (filters.lastFollowUpEnd ? 1 : 0) + 
    (filters.hasEmail ? 1 : 0) + 
    (filters.hasFollowUp ? 1 : 0)

  // 处理多选状态
  const handleStatusChange = (value: string) => {
    setFilters(prev => {
      if (prev.status.includes(value)) {
        return { ...prev, status: prev.status.filter(item => item !== value) }
      } else {
        return { ...prev, status: [...prev.status, value] }
      }
    })
  }

  // 处理多选来源
  const handleSourceChange = (value: string) => {
    setFilters(prev => {
      if (prev.source.includes(value)) {
        return { ...prev, source: prev.source.filter(item => item !== value) }
      } else {
        return { ...prev, source: [...prev.source, value] }
      }
    })
  }

  // 处理多选意向度
  const handleInterestChange = (value: string) => {
    setFilters(prev => {
      if (prev.interest.includes(value)) {
        return { ...prev, interest: prev.interest.filter(item => item !== value) }
      } else {
        return { ...prev, interest: [...prev.interest, value] }
      }
    })
  }

  // 处理多选负责人
  const handleAssignedToChange = (value: string) => {
    setFilters(prev => {
      if (prev.assignedTo.includes(value)) {
        return { ...prev, assignedTo: prev.assignedTo.filter(item => item !== value) }
      } else {
        return { ...prev, assignedTo: [...prev.assignedTo, value] }
      }
    })
  }

  // 应用筛选
  const handleApplyFilter = () => {
    // 这里应该调用API或更新父组件的筛选状态
    console.log("应用筛选条件:", filters)
    
    toast({
      title: "筛选已应用",
      description: `已应用 ${activeFilterCount} 个筛选条件`,
    })
    
    onOpenChange(false)
  }

  // 重置筛选
  const handleResetFilter = () => {
    setFilters({
      status: [],
      source: [],
      interest: [],
      assignedTo: [],
      createdDateStart: "",
      createdDateEnd: "",
      lastFollowUpStart: "",
      lastFollowUpEnd: "",
      hasEmail: false,
      hasFollowUp: false,
    })
  }

  // 移除单个筛选条件
  const removeFilter = (type: string, value?: string) => {
    setFilters(prev => {
      switch (type) {
        case "status":
          return { ...prev, status: prev.status.filter(item => item !== value) }
        case "source":
          return { ...prev, source: prev.source.filter(item => item !== value) }
        case "interest":
          return { ...prev, interest: prev.interest.filter(item => item !== value) }
        case "assignedTo":
          return { ...prev, assignedTo: prev.assignedTo.filter(item => item !== value) }
        case "createdDateStart":
          return { ...prev, createdDateStart: "" }
        case "createdDateEnd":
          return { ...prev, createdDateEnd: "" }
        case "lastFollowUpStart":
          return { ...prev, lastFollowUpStart: "" }
        case "lastFollowUpEnd":
          return { ...prev, lastFollowUpEnd: "" }
        case "hasEmail":
          return { ...prev, hasEmail: false }
        case "hasFollowUp":
          return { ...prev, hasFollowUp: false }
        default:
          return prev
      }
    })
  }

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case "new": return "新获取"
      case "contacted": return "已联系"
      case "qualified": return "已确认"
      case "negotiating": return "洽谈中"
      case "converted": return "已转化"
      case "lost": return "已流失"
      default: return status
    }
  }

  // 获取意向度显示文本
  const getInterestText = (interest: string) => {
    switch (interest) {
      case "1": return "★ 非常低"
      case "2": return "★★ 较低"
      case "3": return "★★★ 一般"
      case "4": return "★★★★ 较高"
      case "5": return "★★★★★ 非常高"
      default: return interest
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
          <DialogDescription>
            设置多个条件筛选潜客数据
          </DialogDescription>
        </DialogHeader>
        
        {activeFilterCount > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>已选条件</Label>
              <Button variant="ghost" size="sm" onClick={handleResetFilter}>
                清除全部
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {filters.status.map(status => (
                <Badge key={status} variant="secondary" className="flex items-center gap-1">
                  状态: {getStatusText(status)}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("status", status)} 
                  />
                </Badge>
              ))}
              {filters.source.map(source => (
                <Badge key={source} variant="secondary" className="flex items-center gap-1">
                  来源: {source}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("source", source)} 
                  />
                </Badge>
              ))}
              {filters.interest.map(interest => (
                <Badge key={interest} variant="secondary" className="flex items-center gap-1">
                  意向度: {getInterestText(interest)}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("interest", interest)} 
                  />
                </Badge>
              ))}
              {filters.assignedTo.map(assignedTo => (
                <Badge key={assignedTo} variant="secondary" className="flex items-center gap-1">
                  负责人: {assignedTo}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("assignedTo", assignedTo)} 
                  />
                </Badge>
              ))}
              {filters.createdDateStart && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  获取时间起: {filters.createdDateStart}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("createdDateStart")} 
                  />
                </Badge>
              )}
              {filters.createdDateEnd && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  获取时间止: {filters.createdDateEnd}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("createdDateEnd")} 
                  />
                </Badge>
              )}
              {filters.lastFollowUpStart && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  跟进时间起: {filters.lastFollowUpStart}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("lastFollowUpStart")} 
                  />
                </Badge>
              )}
              {filters.lastFollowUpEnd && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  跟进时间止: {filters.lastFollowUpEnd}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("lastFollowUpEnd")} 
                  />
                </Badge>
              )}
              {filters.hasEmail && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  有邮箱
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("hasEmail")} 
                  />
                </Badge>
              )}
              {filters.hasFollowUp && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  有跟进记录
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeFilter("hasFollowUp")} 
                  />
                </Badge>
              )}
            </div>
          </div>
        )}
        
        <Separator className="my-2" />
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>潜客状态</Label>
            <div className="space-y-2">
              {["new", "contacted", "qualified", "negotiating", "converted", "lost"].map((status) => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`status-${status}`} 
                    checked={filters.status.includes(status)}
                    onCheckedChange={() => handleStatusChange(status)}
                  />
                  <Label htmlFor={`status-${status}`} className="font-normal cursor-pointer">
                    {getStatusText(status)}
                  </Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>来源渠道</Label>
            <div className="space-y-2">
              {["website", "wechat", "referral", "walk-in", "ad", "event", "other"].map((source) => (
                <div key={source} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`source-${source}`} 
                    checked={filters.source.includes(source)}
                    onCheckedChange={() => handleSourceChange(source)}
                  />
                  <Label htmlFor={`source-${source}`} className="font-normal cursor-pointer">
                    {source === "website" && "官网"}
                    {source === "wechat" && "微信"}
                    {source === "referral" && "会员推荐"}
                    {source === "walk-in" && "门店到访"}
                    {source === "ad" && "广告投放"}
                    {source === "event" && "活动获取"}
                    {source === "other" && "其他渠道"}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div className="space-y-2">
            <Label>意向度</Label>
            <div className="space-y-2">
              {["1", "2", "3", "4", "5"].map((interest) => (
                <div key={interest} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`interest-${interest}`} 
                    checked={filters.interest.includes(interest)}
                    onCheckedChange={() => handleInterestChange(interest)}
                  />
                  <Label htmlFor={`interest-${interest}`} className="font-normal cursor-pointer">
                    {getInterestText(interest)}
                  </Label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>负责人</Label>
            <div className="space-y-2">
              {["张三", "李四", "王五", "赵六"].map((staff) => (
                <div key={staff} className="flex items-center space-x-2">
                  <Checkbox 
                    id={`staff-${staff}`} 
                    checked={filters.assignedTo.includes(staff)}
                    onCheckedChange={() => handleAssignedToChange(staff)}
                  />
                  <Label htmlFor={`staff-${staff}`} className="font-normal cursor-pointer">
                    {staff}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div className="space-y-2">
            <Label>获取时间范围</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Input 
                  type="date" 
                  value={filters.createdDateStart}
                  onChange={(e) => setFilters(prev => ({ ...prev, createdDateStart: e.target.value }))}
                />
              </div>
              <div>
                <Input 
                  type="date" 
                  value={filters.createdDateEnd}
                  onChange={(e) => setFilters(prev => ({ ...prev, createdDateEnd: e.target.value }))}
                />
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>最近跟进时间</Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Input 
                  type="date" 
                  value={filters.lastFollowUpStart}
                  onChange={(e) => setFilters(prev => ({ ...prev, lastFollowUpStart: e.target.value }))}
                />
              </div>
              <div>
                <Input 
                  type="date" 
                  value={filters.lastFollowUpEnd}
                  onChange={(e) => setFilters(prev => ({ ...prev, lastFollowUpEnd: e.target.value }))}
                />
              </div>
            </div>
          </div>
        </div>
        
        <div className="space-y-2 mt-4">
          <Label>其他条件</Label>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="has-email" 
                checked={filters.hasEmail}
                onCheckedChange={(checked) => setFilters(prev => ({ ...prev, hasEmail: checked as boolean }))}
              />
              <Label htmlFor="has-email" className="font-normal cursor-pointer">
                有电子邮箱
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id="has-follow-up" 
                checked={filters.hasFollowUp}
                onCheckedChange={(checked) => setFilters(prev => ({ ...prev, hasFollowUp: checked as boolean }))}
              />
              <Label htmlFor="has-follow-up" className="font-normal cursor-pointer">
                有跟进记录
              </Label>
            </div>
          </div>
        </div>
        
        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={handleResetFilter}>
            重置
          </Button>
          <Button onClick={handleApplyFilter}>
            应用筛选 {activeFilterCount > 0 && `(${activeFilterCount})`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
