"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { BarChart, Download, LineChart, PieChart, TrendingDown, TrendingUp, Users } from "lucide-react"
import {
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart as RechartsLineChart,
  Line,
  Pie<PERSON>hart as RechartsPie<PERSON>hart,
  Pie,
  Cell,
} from "recharts"

// 模拟数据
const usageTrendData = [
  { date: "04-01", 使用数量: 45, 消费金额: 2800 },
  { date: "04-02", 使用数量: 52, 消费金额: 3200 },
  { date: "04-03", 使用数量: 48, 消费金额: 2950 },
  { date: "04-04", 使用数量: 61, 消费金额: 3800 },
  { date: "04-05", 使用数量: 55, 消费金额: 3400 },
  { date: "04-06", 使用数量: 67, 消费金额: 4100 },
  { date: "04-07", 使用数量: 72, 消费金额: 4500 },
  { date: "04-08", 使用数量: 58, 消费金额: 3600 },
  { date: "04-09", 使用数量: 63, 消费金额: 3900 },
  { date: "04-10", 使用数量: 59, 消费金额: 3650 },
  { date: "04-11", 使用数量: 51, 消费金额: 3150 },
  { date: "04-12", 使用数量: 49, 消费金额: 3050 },
  { date: "04-13", 使用数量: 53, 消费金额: 3300 },
  { date: "04-14", 使用数量: 64, 消费金额: 4000 },
  { date: "04-15", 使用数量: 60, 消费金额: 3750 },
]

const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#a4de6c"]

const couponTypeData = [
  { name: "折扣券", value: 35 },
  { name: "满减券", value: 40 },
  { name: "赠品券", value: 10 },
  { name: "免费券", value: 15 },
]

const topCoupons = [
  { id: 1, name: "会员生日专享券", type: "fixed", usageCount: 876, revenue: 87600 },
  { id: 2, name: "瑜伽课程体验券", type: "free", usageCount: 245, revenue: 73500 },
  { id: 3, name: "新人专享8折券", type: "discount", usageCount: 156, revenue: 62400 },
  { id: 4, name: "健身体验课赠送", type: "free", usageCount: 201, revenue: 60300 },
  { id: 5, name: "购课送瑜伽垫", type: "gift", usageCount: 80, revenue: 48000 },
]

const couponTypeComparisonData = [
  { name: "折扣券", 使用次数: 576, 带动消费: 230400, 平均优惠金额: 40 },
  { name: "满减券", 使用次数: 658, 带动消费: 263200, 平均优惠金额: 50 },
  { name: "赠品券", 使用次数: 165, 带动消费: 99000, 平均优惠金额: 60 },
  { name: "免费券", 使用次数: 246, 带动消费: 147600, 平均优惠金额: 70 },
]

const discountAnalysisData = [
  { discount: "9.5折", 使用率: 85, 客单价: 450 },
  { discount: "9折", 使用率: 78, 客单价: 420 },
  { discount: "8.5折", 使用率: 65, 客单价: 400 },
  { discount: "8折", 使用率: 52, 客单价: 380 },
  { discount: "7.5折", 使用率: 45, 客单价: 350 },
  { discount: "7折", 使用率: 38, 客单价: 320 },
]

const fixedAnalysisData = [
  { threshold: "满100减20", 使用率: 75, 客单价: 150 },
  { threshold: "满200减50", 使用率: 65, 客单价: 250 },
  { threshold: "满300减100", 使用率: 55, 客单价: 350 },
  { threshold: "满500减150", 使用率: 40, 客单价: 550 },
  { threshold: "满1000减300", 使用率: 25, 客单价: 1100 },
]

const memberLevelData = [
  { level: "普通会员", 使用次数: 450, 人均使用: 1.5, 客单价: 200 },
  { level: "银卡会员", 使用次数: 520, 人均使用: 2.3, 客单价: 350 },
  { level: "金卡会员", 使用次数: 480, 人均使用: 3.2, 客单价: 500 },
  { level: "白金会员", 使用次数: 195, 人均使用: 3.9, 客单价: 800 },
]

const memberActivityData = [
  { activity: "高活跃", count: 245, usageCount: 735, perCapita: 3.0, averageSpend: 650 },
  { activity: "中活跃", count: 520, usageCount: 780, perCapita: 1.5, averageSpend: 450 },
  { activity: "低活跃", count: 780, usageCount: 130, perCapita: 0.17, averageSpend: 350 },
]

const newVsOldMemberData = [
  { name: "新会员", value: 35 },
  { name: "老会员", value: 65 },
]

const conversionFunnelData = [
  { name: "优惠券发放数", value: 3842 },
  { name: "会员领取数", value: 2568 },
  { name: "会员查看数", value: 2105 },
  { name: "会员使用数", value: 1645 },
  { name: "复购使用数", value: 587 },
]

const roiData = [
  { type: "discount", cost: 23040, revenue: 230400, roi: 10 },
  { type: "fixed", cost: 32900, revenue: 263200, roi: 8 },
  { type: "gift", cost: 9900, revenue: 99000, roi: 10 },
  { type: "free", cost: 17220, revenue: 147600, roi: 8.57 },
]

const channelConversionData = [
  { channel: "指定会员", 领取率: 100, 使用率: 65 },
  { channel: "会员标签", 领取率: 85, 使用率: 55 },
  { channel: "二维码", 领取率: 45, 使用率: 35 },
  { channel: "短信通知", 领取率: 75, 使用率: 50 },
]

// 辅助函数
function getBadgeVariant(type: string): "default" | "secondary" | "destructive" | "outline" {
  switch (type) {
    case "discount":
      return "secondary"
    case "fixed":
      return "default"
    case "gift":
      return "outline"
    case "free":
      return "default"
    default:
      return "default"
  }
}

function getCouponTypeName(type: string) {
  switch (type) {
    case "discount":
      return "折扣券"
    case "fixed":
      return "满减券"
    case "gift":
      return "赠品券"
    case "free":
      return "免费券"
    default:
      return "其他"
  }
}

export default function CouponAnalyticsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">优惠券使用分析</h1>
          <p className="text-muted-foreground">分析优惠券使用情况和效果，优化营销策略</p>
        </div>
        <div className="flex items-center gap-2">
          <Select defaultValue="30days">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="year">今年</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">优惠券使用次数</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,645</div>
            <div className="flex items-center pt-1 text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3.5 w-3.5 text-green-500" />
              <span className="text-green-500">+12.5%</span>
              <span className="ml-1">较上期</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">带动消费金额</CardTitle>
            <LineChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥89,432</div>
            <div className="flex items-center pt-1 text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3.5 w-3.5 text-green-500" />
              <span className="text-green-500">+23.1%</span>
              <span className="ml-1">较上期</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均优惠金额</CardTitle>
            <PieChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥54.37</div>
            <div className="flex items-center pt-1 text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3.5 w-3.5 text-green-500" />
              <span className="text-green-500">+5.2%</span>
              <span className="ml-1">较上期</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">新增会员数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">246</div>
            <div className="flex items-center pt-1 text-xs text-muted-foreground">
              <TrendingDown className="mr-1 h-3.5 w-3.5 text-red-500" />
              <span className="text-red-500">-3.8%</span>
              <span className="ml-1">较上期</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">总体概览</TabsTrigger>
          <TabsTrigger value="coupon-types">优惠券类型分析</TabsTrigger>
          <TabsTrigger value="member-analysis">会员使用分析</TabsTrigger>
          <TabsTrigger value="conversion">转化率分析</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>优惠券使用趋势</CardTitle>
              <CardDescription>最近30天优惠券使用数量和带动消费金额</CardDescription>
            </CardHeader>
            <CardContent className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsLineChart
                  data={usageTrendData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Line yAxisId="left" type="monotone" dataKey="使用数量" stroke="#8884d8" activeDot={{ r: 8 }} />
                  <Line yAxisId="right" type="monotone" dataKey="消费金额" stroke="#82ca9d" />
                </RechartsLineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>优惠券类型分布</CardTitle>
                <CardDescription>各类型优惠券的使用占比</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={couponTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {couponTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>热门优惠券排行</CardTitle>
                <CardDescription>使用次数最多的优惠券</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>排名</TableHead>
                      <TableHead>优惠券名称</TableHead>
                      <TableHead>类型</TableHead>
                      <TableHead>使用次数</TableHead>
                      <TableHead>带动消费</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {topCoupons.map((coupon, index) => (
                      <TableRow key={coupon.id}>
                        <TableCell className="font-medium">{index + 1}</TableCell>
                        <TableCell>{coupon.name}</TableCell>
                        <TableCell>
                          <Badge variant={getBadgeVariant(coupon.type)}>{getCouponTypeName(coupon.type)}</Badge>
                        </TableCell>
                        <TableCell>{coupon.usageCount}</TableCell>
                        <TableCell>¥{coupon.revenue.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="coupon-types" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>各类型优惠券效果对比</CardTitle>
              <CardDescription>不同类型优惠券的使用情况和带动消费对比</CardDescription>
            </CardHeader>
            <CardContent className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsBarChart
                  data={couponTypeComparisonData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="使用次数" fill="#8884d8" />
                  <Bar dataKey="带动消费" fill="#82ca9d" />
                  <Bar dataKey="平均优惠金额" fill="#ffc658" />
                </RechartsBarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>折扣券效果分析</CardTitle>
                <CardDescription>折扣力度与使用率关系</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={discountAnalysisData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="discount" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="使用率" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="客单价" stroke="#82ca9d" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>满减券效果分析</CardTitle>
                <CardDescription>满减门槛与使用率关系</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={fixedAnalysisData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="threshold" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="使用率" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="客单价" stroke="#82ca9d" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="member-analysis" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>会员等级使用分布</CardTitle>
              <CardDescription>不同会员等级的优惠券使用情况</CardDescription>
            </CardHeader>
            <CardContent className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsBarChart
                  data={memberLevelData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="level" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="使用次数" fill="#8884d8" />
                  <Bar dataKey="人均使用" fill="#82ca9d" />
                  <Bar dataKey="客单价" fill="#ffc658" />
                </RechartsBarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>会员活跃度分析</CardTitle>
                <CardDescription>优惠券使用与会员活跃度关系</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>会员活跃度</TableHead>
                      <TableHead>会员数量</TableHead>
                      <TableHead>优惠券使用次数</TableHead>
                      <TableHead>人均使用</TableHead>
                      <TableHead>客单价</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {memberActivityData.map((data) => (
                      <TableRow key={data.activity}>
                        <TableCell className="font-medium">{data.activity}</TableCell>
                        <TableCell>{data.count}</TableCell>
                        <TableCell>{data.usageCount}</TableCell>
                        <TableCell>{data.perCapita}</TableCell>
                        <TableCell>¥{data.averageSpend}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>新老会员对比</CardTitle>
                <CardDescription>新会员与老会员优惠券使用对比</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={newVsOldMemberData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {newVsOldMemberData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="conversion" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>优惠券转化漏斗</CardTitle>
              <CardDescription>从发放到使用的转化过程分析</CardDescription>
            </CardHeader>
            <CardContent className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsBarChart
                  data={conversionFunnelData}
                  layout="vertical"
                  margin={{
                    top: 20,
                    right: 30,
                    left: 100,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" fill="#8884d8" />
                </RechartsBarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>优惠券ROI分析</CardTitle>
                <CardDescription>各类型优惠券的投入产出比</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>优惠券类型</TableHead>
                      <TableHead>优惠总额</TableHead>
                      <TableHead>带动消费</TableHead>
                      <TableHead>ROI</TableHead>
                      <TableHead>评估</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {roiData.map((data) => (
                      <TableRow key={data.type}>
                        <TableCell className="font-medium">{getCouponTypeName(data.type)}</TableCell>
                        <TableCell>¥{data.cost.toLocaleString()}</TableCell>
                        <TableCell>¥{data.revenue.toLocaleString()}</TableCell>
                        <TableCell>{data.roi.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant={data.roi >= 3 ? "default" : data.roi >= 1 ? "secondary" : "destructive"}>
                            {data.roi >= 3 ? "优秀" : data.roi >= 1 ? "良好" : "不佳"}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>发放渠道转化率</CardTitle>
                <CardDescription>不同发放渠道的优惠券转化率对比</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart
                    data={channelConversionData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="channel" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="领取率" fill="#8884d8" />
                    <Bar dataKey="使用率" fill="#82ca9d" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

