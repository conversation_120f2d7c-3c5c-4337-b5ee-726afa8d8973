"use client"

import { useState } from "react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

interface CardType {
  id: number
  name: string
  description: string
  color: string
  category: "time" | "count" | "value"
}

// 模拟数据
const CARD_DATA: CardType[] = [
  // 时间卡
  { id: 1, name: "瑜伽年卡", description: "365天", color: "#4F46E5", category: "time" },
  { id: 2, name: "瑜伽季卡", description: "90天", color: "#06B6D4", category: "time" },
  { id: 3, name: "瑜伽月卡", description: "30天", color: "#10B981", category: "time" },
  
  // 次数卡
  { id: 4, name: "20次卡", description: "20次", color: "#F59E0B", category: "count" },
  { id: 5, name: "10次卡", description: "10次", color: "#EC4899", category: "count" },
  
  // 储值卡
  { id: 6, name: "1000元储值卡", description: "¥1,000", color: "#EF4444", category: "value" },
  { id: 7, name: "2000元储值卡", description: "¥2,000", color: "#F97316", category: "value" },
]

interface CardAssociationScrollableProps {
  initialSelectedCards?: number[]
  onChange?: (selectedCards: number[]) => void
}

export function CardAssociationScrollable({
  initialSelectedCards = [],
  onChange
}: CardAssociationScrollableProps) {
  const [selectedCards, setSelectedCards] = useState<number[]>(initialSelectedCards)
  
  const handleCardToggle = (cardId: number) => {
    const newSelection = selectedCards.includes(cardId)
      ? selectedCards.filter(id => id !== cardId)
      : [...selectedCards, cardId]
    
    setSelectedCards(newSelection)
    onChange?.(newSelection)
  }
  
  // 按类别分组卡片
  const timeCards = CARD_DATA.filter(card => card.category === "time")
  const countCards = CARD_DATA.filter(card => card.category === "count")
  const valueCards = CARD_DATA.filter(card => card.category === "value")
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-2">
        <Badge variant="outline" className="bg-[#4F46E5]/10 text-[#4F46E5] hover:bg-[#4F46E5]/20 border-[#4F46E5]/20">
          时间卡
        </Badge>
        <Badge variant="outline" className="bg-[#F59E0B]/10 text-[#F59E0B] hover:bg-[#F59E0B]/20 border-[#F59E0B]/20">
          次数卡
        </Badge>
        <Badge variant="outline" className="bg-[#EF4444]/10 text-[#EF4444] hover:bg-[#EF4444]/20 border-[#EF4444]/20">
          储值卡
        </Badge>
      </div>
      
      <ScrollArea className="h-[300px] rounded-md border p-4">
        <div className="space-y-6">
          {/* 时间卡 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">时间卡</h3>
            <div className="space-y-2">
              {timeCards.map(card => (
                <div 
                  key={card.id}
                  className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5 cursor-pointer"
                  onClick={() => handleCardToggle(card.id)}
                >
                  <div className="flex items-center gap-3">
                    <div className="h-4 w-4 rounded-full" style={{ backgroundColor: card.color }} />
                    <div>
                      <div className="font-medium">{card.name}</div>
                      <div className="text-xs text-muted-foreground">{card.description}</div>
                    </div>
                  </div>
                  <Checkbox 
                    checked={selectedCards.includes(card.id)}
                    onCheckedChange={() => handleCardToggle(card.id)}
                  />
                </div>
              ))}
            </div>
          </div>
          
          {/* 次数卡 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">次数卡</h3>
            <div className="space-y-2">
              {countCards.map(card => (
                <div 
                  key={card.id}
                  className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5 cursor-pointer"
                  onClick={() => handleCardToggle(card.id)}
                >
                  <div className="flex items-center gap-3">
                    <div className="h-4 w-4 rounded-full" style={{ backgroundColor: card.color }} />
                    <div>
                      <div className="font-medium">{card.name}</div>
                      <div className="text-xs text-muted-foreground">{card.description}</div>
                    </div>
                  </div>
                  <Checkbox 
                    checked={selectedCards.includes(card.id)}
                    onCheckedChange={() => handleCardToggle(card.id)}
                  />
                </div>
              ))}
            </div>
          </div>
          
          {/* 储值卡 */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">储值卡</h3>
            <div className="space-y-2">
              {valueCards.map(card => (
                <div 
                  key={card.id}
                  className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5 cursor-pointer"
                  onClick={() => handleCardToggle(card.id)}
                >
                  <div className="flex items-center gap-3">
                    <div className="h-4 w-4 rounded-full" style={{ backgroundColor: card.color }} />
                    <div>
                      <div className="font-medium">{card.name}</div>
                      <div className="text-xs text-muted-foreground">{card.description}</div>
                    </div>
                  </div>
                  <Checkbox 
                    checked={selectedCards.includes(card.id)}
                    onCheckedChange={() => handleCardToggle(card.id)}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  )
}
