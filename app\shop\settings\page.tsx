"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Save, Settings, CreditCard, Truck, CheckCircle, ShoppingBag } from "lucide-react"
import { toast } from "@/hooks/use-toast"

export default function ShopSettingsPage() {
  const [activeTab, setActiveTab] = useState("general")
  
  // 模拟设置状态
  const [settings, setSettings] = useState({
    general: {
      shopName: "瑜伽工作室商城",
      shopLogo: "/logo.svg",
      shopBanner: "/shop-banner.jpg",
      shopDescription: "专业瑜伽用品和课程商城，提供高品质瑜伽装备和专业课程",
      shopStatus: "open", // open, closed, maintenance
      enableCart: true,
      enableWishlist: true,
      enableReviews: true,
      enableComparison: false,
      itemsPerPage: "12",
    },
    payment: {
      enableWechatPay: true,
      enableAlipay: true,
      enableBankTransfer: false,
      enableCashOnDelivery: false,
      autoConfirmPayment: true,
      confirmationDelay: "30", // 分钟
      paymentExpiration: "120", // 分钟
      refundPolicy: "购买后7天内，未使用可申请全额退款。虚拟商品激活后不支持退款。",
    },
    delivery: {
      enableLogistics: true,
      enableSelfPickup: true,
      enableVerification: true,
      freeShippingThreshold: "199",
      defaultShippingFee: "12",
      selfPickupDiscount: "5", // 百分比
      deliveryTimePromise: "3-5个工作日",
      outOfStockBehavior: "allow_backorder", // allow_backorder, hide, disable
    },
    notification: {
      orderConfirmation: true,
      paymentConfirmation: true,
      shipmentNotification: true,
      deliveryConfirmation: true,
      reviewReminder: true,
      stockAlert: true,
      abandonedCartReminder: true,
      reminderDelay: "24", // 小时
    }
  })

  // 处理设置变更
  const handleSettingChange = (category, setting, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value
      }
    }))
  }

  // 保存设置
  const saveSettings = () => {
    // 这里应该有API调用来保存设置
    toast({
      title: "设置已保存",
      description: "您的商城设置已成功更新。",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">商城设置</h1>
        <Button onClick={saveSettings}>
          <Save className="mr-2 h-4 w-4" />
          保存设置
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 max-w-2xl">
          <TabsTrigger value="general">基本设置</TabsTrigger>
          <TabsTrigger value="payment">支付设置</TabsTrigger>
          <TabsTrigger value="delivery">配送设置</TabsTrigger>
          <TabsTrigger value="notification">通知设置</TabsTrigger>
        </TabsList>

        {/* 基本设置 */}
        <TabsContent value="general" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>基本设置</CardTitle>
              <CardDescription>配置商城的基本信息和功能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="shop-name">商城名称</Label>
                  <Input
                    id="shop-name"
                    value={settings.general.shopName}
                    onChange={(e) => handleSettingChange("general", "shopName", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="items-per-page">每页显示商品数</Label>
                  <Select
                    value={settings.general.itemsPerPage}
                    onValueChange={(value) => handleSettingChange("general", "itemsPerPage", value)}
                  >
                    <SelectTrigger id="items-per-page">
                      <SelectValue placeholder="选择每页显示数量" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="8">8</SelectItem>
                      <SelectItem value="12">12</SelectItem>
                      <SelectItem value="16">16</SelectItem>
                      <SelectItem value="24">24</SelectItem>
                      <SelectItem value="36">36</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="shop-description">商城描述</Label>
                  <Textarea
                    id="shop-description"
                    value={settings.general.shopDescription}
                    onChange={(e) => handleSettingChange("general", "shopDescription", e.target.value)}
                    rows={3}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label>商城状态</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="status-open"
                      checked={settings.general.shopStatus === "open"}
                      onChange={() => handleSettingChange("general", "shopStatus", "open")}
                    />
                    <Label htmlFor="status-open" className="font-normal">正常营业</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="status-closed"
                      checked={settings.general.shopStatus === "closed"}
                      onChange={() => handleSettingChange("general", "shopStatus", "closed")}
                    />
                    <Label htmlFor="status-closed" className="font-normal">暂停营业</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="status-maintenance"
                      checked={settings.general.shopStatus === "maintenance"}
                      onChange={() => handleSettingChange("general", "shopStatus", "maintenance")}
                    />
                    <Label htmlFor="status-maintenance" className="font-normal">系统维护</Label>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">功能开关</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-cart" className="flex flex-col space-y-1">
                      <span>购物车功能</span>
                      <span className="font-normal text-sm text-muted-foreground">
                        允许用户将商品添加到购物车
                      </span>
                    </Label>
                    <Switch
                      id="enable-cart"
                      checked={settings.general.enableCart}
                      onCheckedChange={(value) => handleSettingChange("general", "enableCart", value)}
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-wishlist" className="flex flex-col space-y-1">
                      <span>收藏夹功能</span>
                      <span className="font-normal text-sm text-muted-foreground">
                        允许用户收藏喜欢的商品
                      </span>
                    </Label>
                    <Switch
                      id="enable-wishlist"
                      checked={settings.general.enableWishlist}
                      onCheckedChange={(value) => handleSettingChange("general", "enableWishlist", value)}
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-reviews" className="flex flex-col space-y-1">
                      <span>评价功能</span>
                      <span className="font-normal text-sm text-muted-foreground">
                        允许用户对购买的商品进行评价
                      </span>
                    </Label>
                    <Switch
                      id="enable-reviews"
                      checked={settings.general.enableReviews}
                      onCheckedChange={(value) => handleSettingChange("general", "enableReviews", value)}
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-comparison" className="flex flex-col space-y-1">
                      <span>商品对比功能</span>
                      <span className="font-normal text-sm text-muted-foreground">
                        允许用户对比不同商品的特性
                      </span>
                    </Label>
                    <Switch
                      id="enable-comparison"
                      checked={settings.general.enableComparison}
                      onCheckedChange={(value) => handleSettingChange("general", "enableComparison", value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSettings} className="ml-auto">
                <Save className="mr-2 h-4 w-4" />
                保存基本设置
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* 其他标签页内容将在后续步骤中添加 */}
        <TabsContent value="payment" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>支付设置</CardTitle>
              <CardDescription>配置商城的支付方式和规则</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <h3 className="text-lg font-medium">支付方式</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-wechat-pay" className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      <span>微信支付</span>
                    </Label>
                    <Switch
                      id="enable-wechat-pay"
                      checked={settings.payment.enableWechatPay}
                      onCheckedChange={(value) => handleSettingChange("payment", "enableWechatPay", value)}
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-alipay" className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      <span>支付宝</span>
                    </Label>
                    <Switch
                      id="enable-alipay"
                      checked={settings.payment.enableAlipay}
                      onCheckedChange={(value) => handleSettingChange("payment", "enableAlipay", value)}
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-bank-transfer" className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      <span>银行转账</span>
                    </Label>
                    <Switch
                      id="enable-bank-transfer"
                      checked={settings.payment.enableBankTransfer}
                      onCheckedChange={(value) => handleSettingChange("payment", "enableBankTransfer", value)}
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-cash-on-delivery" className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      <span>货到付款</span>
                    </Label>
                    <Switch
                      id="enable-cash-on-delivery"
                      checked={settings.payment.enableCashOnDelivery}
                      onCheckedChange={(value) => handleSettingChange("payment", "enableCashOnDelivery", value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="delivery" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>配送设置</CardTitle>
              <CardDescription>配置商城的配送方式和规则</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <h3 className="text-lg font-medium">配送方式</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-logistics" className="flex items-center gap-2">
                      <Truck className="h-4 w-4" />
                      <span>物流配送</span>
                    </Label>
                    <Switch
                      id="enable-logistics"
                      checked={settings.delivery.enableLogistics}
                      onCheckedChange={(value) => handleSettingChange("delivery", "enableLogistics", value)}
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-self-pickup" className="flex items-center gap-2">
                      <ShoppingBag className="h-4 w-4" />
                      <span>门店自提</span>
                    </Label>
                    <Switch
                      id="enable-self-pickup"
                      checked={settings.delivery.enableSelfPickup}
                      onCheckedChange={(value) => handleSettingChange("delivery", "enableSelfPickup", value)}
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="enable-verification" className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      <span>核销</span>
                    </Label>
                    <Switch
                      id="enable-verification"
                      checked={settings.delivery.enableVerification}
                      onCheckedChange={(value) => handleSettingChange("delivery", "enableVerification", value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notification" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>通知设置</CardTitle>
              <CardDescription>配置商城的通知和提醒规则</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <h3 className="text-lg font-medium">通知类型</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="order-confirmation" className="flex flex-col space-y-1">
                      <span>订单确认通知</span>
                      <span className="font-normal text-sm text-muted-foreground">
                        用户下单后发送订单确认通知
                      </span>
                    </Label>
                    <Switch
                      id="order-confirmation"
                      checked={settings.notification.orderConfirmation}
                      onCheckedChange={(value) => handleSettingChange("notification", "orderConfirmation", value)}
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="payment-confirmation" className="flex flex-col space-y-1">
                      <span>支付确认通知</span>
                      <span className="font-normal text-sm text-muted-foreground">
                        用户支付成功后发送确认通知
                      </span>
                    </Label>
                    <Switch
                      id="payment-confirmation"
                      checked={settings.notification.paymentConfirmation}
                      onCheckedChange={(value) => handleSettingChange("notification", "paymentConfirmation", value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
