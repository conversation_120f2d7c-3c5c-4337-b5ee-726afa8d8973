import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { formatShanghaiTime, parseFromDatabase } from '@/lib/timezone';

// GET - 获取门店列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');

    if (!tenantId) {
      return NextResponse.json({
        success: false,
        error: '缺少租户ID'
      }, { status: 400 });
    }

    console.log('获取门店列表，租户ID:', tenantId);

    const stores = await prisma.store.findMany({
      where: {
        tenant_id: parseInt(tenantId)
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    console.log('查询到门店数量:', stores.length);

    // 转换数据格式
    const formattedStores = stores.map(store => ({
      id: store.id,
      name: store.store_name,
      address: store.address || '地址待完善',
      phone: store.phone,
      managerName: store.contact_person,
      employeesCount: 1, // 暂时固定为1，后续可以关联员工表统计
      status: store.status === 1 ? 'active' : store.status === 0 ? 'pending' : 'inactive',
      createdAt: parseFromDatabase(store.created_at, 'date'),
      description: '门店描述待完善',
      area: '200',
      type: 'standard' as const,
      courseCount: 0,
      memberCount: 0,
      revenue: 0,
      rating: 5.0
    }));

    return NextResponse.json({
      success: true,
      data: formattedStores
    });

  } catch (error: any) {
    console.error('获取门店列表失败:', error);
    return NextResponse.json({
      success: false,
      error: `获取门店列表失败: ${error.message}`
    }, { status: 500 });
  }
}

// POST - 创建新门店
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('创建门店请求数据:', body);

    const {
      name,
      address,
      phone,
      managerName,
      description,
      area,
      type,
      tenantId
    } = body;

    // 验证必填字段
    if (!name || !address || !phone || !managerName || !tenantId) {
      return NextResponse.json({
        success: false,
        error: '请填写所有必填字段'
      }, { status: 400 });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return NextResponse.json({
        success: false,
        error: '请输入有效的手机号码'
      }, { status: 400 });
    }

    // 创建门店
    const newStore = await prisma.store.create({
      data: {
        tenant_id: parseInt(tenantId),
        store_name: name,
        contact_person: managerName,
        phone,
        address,
        business_hours: '09:00-21:00', // 默认营业时间
        status: 1, // 1-正常营业
      }
    });

    console.log('门店创建成功:', newStore.id);

    // 返回格式化的门店数据
    const formattedStore = {
      id: newStore.id,
      name: newStore.store_name,
      address: newStore.address,
      phone: newStore.phone,
      managerName: newStore.contact_person,
      employeesCount: 1,
      status: 'active' as const,
      createdAt: parseFromDatabase(newStore.created_at, 'date'),
      description: description || '门店描述待完善',
      area: area || '200',
      type: type || 'standard',
      courseCount: 0,
      memberCount: 0,
      revenue: 0,
      rating: 5.0
    };

    return NextResponse.json({
      success: true,
      data: formattedStore,
      message: '门店创建成功'
    });

  } catch (error: any) {
    console.error('创建门店失败:', error);

    // 检查是否是重复键错误
    if (error.code === 'P2002') {
      return NextResponse.json({
        success: false,
        error: '门店名称或电话号码已存在'
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: `创建门店失败: ${error.message}`
    }, { status: 500 });
  }
}