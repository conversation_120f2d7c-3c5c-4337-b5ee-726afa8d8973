"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { useState } from "react"
import { TimePicker } from "@/components/venues/time-picker"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface AddBookingDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddBooking<PERSON><PERSON><PERSON>({ open, onOpenChange }: AddBookingDialogProps) {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [startTime, setStartTime] = useState("10:00")
  const [endTime, setEndTime] = useState("11:30")
  const [isRecurring, setIsRecurring] = useState(false)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>添加场地预订</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="venue">选择场地</Label>
                <Select>
                  <SelectTrigger id="venue">
                    <SelectValue placeholder="选择场地" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1号瑜伽室</SelectItem>
                    <SelectItem value="2">2号瑜伽室</SelectItem>
                    <SelectItem value="3">3号瑜伽室</SelectItem>
                    <SelectItem value="4">4号瑜伽室</SelectItem>
                    <SelectItem value="5">私教室</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="course">选择课程</Label>
                <Select>
                  <SelectTrigger id="course">
                    <SelectValue placeholder="选择课程" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">基础瑜伽入门</SelectItem>
                    <SelectItem value="2">高级瑜伽进阶</SelectItem>
                    <SelectItem value="3">阴瑜伽放松</SelectItem>
                    <SelectItem value="4">孕产瑜伽特训</SelectItem>
                    <SelectItem value="5">空中瑜伽体验</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="coach">选择教练</Label>
                <Select>
                  <SelectTrigger id="coach">
                    <SelectValue placeholder="选择教练" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">张教练</SelectItem>
                    <SelectItem value="2">李教练</SelectItem>
                    <SelectItem value="3">王教练</SelectItem>
                    <SelectItem value="4">赵教练</SelectItem>
                    <SelectItem value="5">刘教练</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>日期</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "yyyy-MM-dd") : "选择日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label>开始时间</Label>
                <TimePicker value={startTime} onChange={setStartTime} />
              </div>
              <div className="space-y-2">
                <Label>结束时间</Label>
                <TimePicker value={endTime} onChange={setEndTime} />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="capacity">容纳人数</Label>
              <Input id="capacity" type="number" min="1" defaultValue="15" />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="recurring"
                checked={isRecurring}
                onCheckedChange={(checked) => setIsRecurring(checked === true)}
              />
              <label
                htmlFor="recurring"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                设置为重复预订
              </label>
            </div>

            {isRecurring && (
              <div className="space-y-4 p-4 border rounded-md">
                <div className="space-y-2">
                  <Label>重复类型</Label>
                  <Select defaultValue="weekly">
                    <SelectTrigger>
                      <SelectValue placeholder="选择重复类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">每天</SelectItem>
                      <SelectItem value="weekly">每周</SelectItem>
                      <SelectItem value="monthly">每月</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>重复周期</Label>
                  <div className="flex flex-wrap gap-2">
                    {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Checkbox id={`day-${index}`} />
                        <label htmlFor={`day-${index}`} className="text-sm font-medium leading-none">
                          {day}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>结束日期</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        选择结束日期
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar mode="single" initialFocus />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">预订状态</Label>
                <Select defaultValue="confirmed">
                  <SelectTrigger id="status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="confirmed">已确认</SelectItem>
                    <SelectItem value="pending">待确认</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">价格设置</Label>
                <Input id="price" type="number" min="0" placeholder="输入价格" />
              </div>
            </div>

            <div className="space-y-2">
              <Label>设备需求</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="equipment-1" />
                  <label htmlFor="equipment-1" className="text-sm">
                    瑜伽垫
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="equipment-2" />
                  <label htmlFor="equipment-2" className="text-sm">
                    瑜伽砖
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="equipment-3" />
                  <label htmlFor="equipment-3" className="text-sm">
                    瑜伽带
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="equipment-4" />
                  <label htmlFor="equipment-4" className="text-sm">
                    瑜伽球
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="equipment-5" />
                  <label htmlFor="equipment-5" className="text-sm">
                    瑜伽轮
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="equipment-6" />
                  <label htmlFor="equipment-6" className="text-sm">
                    空中瑜伽吊床
                  </label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">备注</Label>
              <Input id="notes" placeholder="输入备注信息" />
            </div>

            <div className="space-y-2">
              <Label>通知设置</Label>
              <div className="flex items-center space-x-2">
                <Checkbox id="notify-coach" defaultChecked />
                <label htmlFor="notify-coach" className="text-sm">
                  通知教练
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="notify-members" defaultChecked />
                <label htmlFor="notify-members" className="text-sm">
                  通知会员
                </label>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={() => onOpenChange(false)}>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

