"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { CalendarIcon, Download, Filter, MoreH<PERSON>zontal, Search, Users } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface CardMembersDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  card: {
    id: number | string
    name: string
    color: string
    members: number
  } | null
}

// 模拟会员数据
const mockMembers = [
  {
    id: 1,
    name: "张三",
    phone: "138****1234",
    gender: "男",
    age: 28,
    joinDate: "2023-01-15",
    expireDate: "2024-01-15",
    remainingCount: "不限次数",
    status: "active",
    lastVisit: "2023-05-10",
    totalVisits: 45,
  },
  {
    id: 2,
    name: "李四",
    phone: "139****5678",
    gender: "女",
    age: 32,
    joinDate: "2023-02-20",
    expireDate: "2024-02-20",
    remainingCount: "不限次数",
    status: "active",
    lastVisit: "2023-05-15",
    totalVisits: 38,
  },
  {
    id: 3,
    name: "王五",
    phone: "137****9012",
    gender: "男",
    age: 25,
    joinDate: "2023-03-05",
    expireDate: "2024-03-05",
    remainingCount: "不限次数",
    status: "inactive",
    lastVisit: "2023-04-20",
    totalVisits: 12,
  },
  {
    id: 4,
    name: "赵六",
    phone: "136****3456",
    gender: "女",
    age: 30,
    joinDate: "2023-03-15",
    expireDate: "2024-03-15",
    remainingCount: "不限次数",
    status: "active",
    lastVisit: "2023-05-16",
    totalVisits: 25,
  },
  {
    id: 5,
    name: "钱七",
    phone: "135****7890",
    gender: "男",
    age: 35,
    joinDate: "2023-04-01",
    expireDate: "2024-04-01",
    remainingCount: "不限次数",
    status: "active",
    lastVisit: "2023-05-12",
    totalVisits: 18,
  },
]

export function CardMembersDialog({
  open,
  onOpenChange,
  card
}: CardMembersDialogProps) {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [members, setMembers] = useState(mockMembers)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined)
  
  // 过滤会员
  const filteredMembers = members.filter(member => {
    // 状态过滤
    if (statusFilter !== "all" && member.status !== statusFilter) {
      return false
    }
    
    // 搜索过滤
    if (searchQuery && !member.name.includes(searchQuery) && !member.phone.includes(searchQuery)) {
      return false
    }
    
    return true
  })

  const handleExport = () => {
    toast({
      title: "导出成功",
      description: "会员列表已导出为Excel文件",
    })
  }

  const handleMemberAction = (action: string, memberId: number) => {
    toast({
      title: "操作成功",
      description: `已对会员 ID:${memberId} 执行 ${action} 操作`,
    })
  }

  if (!card) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-5xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-primary" />
            持卡会员列表
          </DialogTitle>
          <DialogDescription>
            <span className="font-medium" style={{ color: card.color }}>{card.name}</span> 会员卡的持卡会员列表，共 {card.members} 名会员
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex flex-1 items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索会员姓名或手机号"
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="状态筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">有效</SelectItem>
                  <SelectItem value="inactive">已过期</SelectItem>
                </SelectContent>
              </Select>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, "yyyy-MM-dd") : "按加入日期筛选"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            <Button variant="outline" onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              导出Excel
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>会员姓名</TableHead>
                    <TableHead>手机号</TableHead>
                    <TableHead>性别</TableHead>
                    <TableHead>年龄</TableHead>
                    <TableHead>加入日期</TableHead>
                    <TableHead>到期日期</TableHead>
                    <TableHead>剩余次数</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>最近到访</TableHead>
                    <TableHead>总到访次数</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMembers.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell className="font-medium">{member.name}</TableCell>
                      <TableCell>{member.phone}</TableCell>
                      <TableCell>{member.gender}</TableCell>
                      <TableCell>{member.age}</TableCell>
                      <TableCell>{member.joinDate}</TableCell>
                      <TableCell>{member.expireDate}</TableCell>
                      <TableCell>{member.remainingCount}</TableCell>
                      <TableCell>
                        <Badge variant={member.status === "active" ? "default" : "secondary"}>
                          {member.status === "active" ? "有效" : "已过期"}
                        </Badge>
                      </TableCell>
                      <TableCell>{member.lastVisit}</TableCell>
                      <TableCell>{member.totalVisits}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>会员操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleMemberAction("查看详情", member.id)}>
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleMemberAction("编辑信息", member.id)}>
                              编辑信息
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleMemberAction("续费", member.id)}>
                              续费
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleMemberAction("暂停", member.id)}>
                              暂停会员卡
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleMemberAction("转卡", member.id)}>
                              转卡
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              显示 {filteredMembers.length} 条记录，共 {members.length} 条
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" disabled>
                上一页
              </Button>
              <Button variant="outline" size="sm" disabled>
                下一页
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
