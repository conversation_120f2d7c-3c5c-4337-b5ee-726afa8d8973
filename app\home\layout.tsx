import { Inter as FontSans } from "next/font/google"
import "../globals.css"
import { cn } from "@/lib/utils"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { Toaster as SonnerToaster } from "sonner"
import Head from "next/head"

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
})

export default function HomeLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className={cn(
      "min-h-screen bg-background font-sans antialiased",
      fontSans.variable
    )}>
      <ThemeProvider attribute="class" defaultTheme="light" enableSystem={false}>
        {children}
        <Toaster />
        <SonnerToaster position="top-right" richColors />
      </ThemeProvider>
    </div>
  )
}
