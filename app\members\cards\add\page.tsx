"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { ArrowLeft, CreditCard, Save, User, Calendar, Tag, DollarSign, Clock, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// 模拟会员卡类型数据
const cardTypes = [
  { id: "1", name: "年卡", color: "#4f46e5", price: 3600, validity: "365天", limit: "不限次数" },
  { id: "2", name: "季卡", color: "#0ea5e9", price: 1200, validity: "90天", limit: "不限次数" },
  { id: "3", name: "月卡", color: "#10b981", price: 450, validity: "30天", limit: "不限次数" },
  { id: "4", name: "次卡20次", color: "#f59e0b", price: 1600, validity: "180天", limit: "20次" },
  { id: "5", name: "次卡10次", color: "#ec4899", price: 900, validity: "90天", limit: "10次" },
  { id: "6", name: "体验卡", color: "#8b5cf6", price: 199, validity: "15天", limit: "3次" },
  { id: "7", name: "年卡限次", color: "#6366f1", price: 2800, validity: "365天", limit: "100次" },
  { id: "8", name: "储值卡", color: "#8b5cf6", price: 1000, validity: "365天", limit: "1000元" },
]

// 模拟会员数据
const members = [
  { id: "M001", name: "张三", phone: "13800138001" },
  { id: "M002", name: "李四", phone: "13800138002" },
  { id: "M003", name: "王五", phone: "13800138003" },
  { id: "M004", name: "赵六", phone: "13800138004" },
  { id: "M005", name: "钱七", phone: "13800138005" },
]

export default function AddMemberCardPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")
  const [cardType, setCardType] = useState("")
  const [member, setMember] = useState("")
  const [startDate, setStartDate] = useState("")
  const [activationMethod, setActivationMethod] = useState("immediate")
  const [customEndDate, setCustomEndDate] = useState(false)
  const [endDate, setEndDate] = useState("")
  const [price, setPrice] = useState("")
  const [discount, setDiscount] = useState("")
  const [actualPrice, setActualPrice] = useState("")
  const [paymentMethod, setPaymentMethod] = useState("cash")
  const [notes, setNotes] = useState("")

  // 获取选中的会员卡类型信息
  const selectedCardType = cardTypes.find(ct => ct.id === cardType)

  // 获取选中的会员信息
  const selectedMember = members.find(m => m.id === member)

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // 在实际应用中，这里会调用API保存会员卡数据
    toast({
      title: "会员卡已创建",
      description: `已为${selectedMember?.name || ''}创建${selectedCardType?.name || ''}`,
    })

    // 跳转到会员卡列表页面
    router.push("/members/cards/list")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.push("/members/cards/list")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">添加会员卡名称</h1>
            <p className="text-muted-foreground">
              为会员创建新的会员卡
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardContent className="pt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="validity">有效期设置</TabsTrigger>
              <TabsTrigger value="payment">支付信息</TabsTrigger>
            </TabsList>

            <form onSubmit={handleSubmit}>
              <TabsContent value="basic" className="space-y-4 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="card-type">会员卡名称</Label>
                    <Select value={cardType} onValueChange={setCardType}>
                      <SelectTrigger id="card-type">
                        <SelectValue placeholder="选择会员卡名称" />
                      </SelectTrigger>
                      <SelectContent>
                        {cardTypes.map(type => (
                          <SelectItem key={type.id} value={type.id}>
                            <div className="flex items-center">
                              <div
                                className="w-3 h-3 rounded-full mr-2"
                                style={{ backgroundColor: type.color }}
                              ></div>
                              {type.name} - {type.price}元
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="member">会员</Label>
                    <Select value={member} onValueChange={setMember}>
                      <SelectTrigger id="member">
                        <SelectValue placeholder="选择会员" />
                      </SelectTrigger>
                      <SelectContent>
                        {members.map(m => (
                          <SelectItem key={m.id} value={m.id}>
                            {m.name} - {m.phone}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="validity" className="space-y-4 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="start-date">开始日期</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="activation-method">激活方式</Label>
                    </div>
                    <RadioGroup
                      value={activationMethod}
                      onValueChange={setActivationMethod}
                      className="flex flex-col space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="immediate" id="immediate" />
                        <Label htmlFor="immediate">立即激活</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="first-use" id="first-use" />
                        <Label htmlFor="first-use">首次使用时激活</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="manual" id="manual" />
                        <Label htmlFor="manual">手动激活</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="custom-end-date">自定义结束日期</Label>
                      <Switch
                        id="custom-end-date"
                        checked={customEndDate}
                        onCheckedChange={setCustomEndDate}
                      />
                    </div>
                    {customEndDate && (
                      <Input
                        id="end-date"
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                      />
                    )}
                    {!customEndDate && selectedCardType && (
                      <div className="text-sm text-muted-foreground flex items-center mt-2">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        将根据会员卡类型自动计算结束日期（{selectedCardType.validity}）
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="payment" className="space-y-4 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="price">原价（元）</Label>
                    <Input
                      id="price"
                      type="number"
                      value={price || (selectedCardType ? selectedCardType.price : '')}
                      onChange={(e) => setPrice(e.target.value)}
                      placeholder="输入原价"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="discount">折扣</Label>
                    <Input
                      id="discount"
                      type="number"
                      value={discount}
                      onChange={(e) => setDiscount(e.target.value)}
                      placeholder="例如：0.8表示8折"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="actual-price">实付金额（元）</Label>
                    <Input
                      id="actual-price"
                      type="number"
                      value={actualPrice}
                      onChange={(e) => setActualPrice(e.target.value)}
                      placeholder="输入实际支付金额"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="payment-method">支付方式</Label>
                    <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                      <SelectTrigger id="payment-method">
                        <SelectValue placeholder="选择支付方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">现金</SelectItem>
                        <SelectItem value="wechat">微信支付</SelectItem>
                        <SelectItem value="alipay">支付宝</SelectItem>
                        <SelectItem value="card">刷卡</SelectItem>
                        <SelectItem value="other">其他</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="notes">备注</Label>
                    <Textarea
                      id="notes"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="添加备注信息"
                      rows={3}
                    />
                  </div>
                </div>
              </TabsContent>

              <div className="flex justify-end mt-6">
                <Button type="button" variant="outline" className="mr-2" onClick={() => router.push("/members/cards/list")}>
                  取消
                </Button>
                <Button type="submit">
                  <Save className="mr-2 h-4 w-4" />
                  保存
                </Button>
              </div>
            </form>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
