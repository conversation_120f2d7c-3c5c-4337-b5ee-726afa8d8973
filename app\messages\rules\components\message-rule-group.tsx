import React from 'react'
import { Switch } from "@/components/ui/switch"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface MessageRule {
  id: number
  name: string
  template: string
  smsEnabled: boolean
  appEnabled: boolean
  emailEnabled?: boolean
  sendRule?: string
  showRuleButton?: boolean // 是否显示设置规则按钮
}

interface MessageRuleGroup {
  group: string
  rules: MessageRule[]
}

interface MessageRuleGroupProps {
  group: MessageRuleGroup
  onToggleSms: (ruleId: number, currentStatus: boolean) => void
  onToggleApp: (ruleId: number, currentStatus: boolean) => void
  onToggleEmail?: (ruleId: number, currentStatus: boolean) => void
  onSetRule?: (rule: MessageRule) => void
}

export function MessageRuleGroup({ group, onToggleSms, onToggleApp, onToggleEmail, onSetRule }: MessageRuleGroupProps) {
  return (
    <Card>
      <CardHeader className="py-4">
        <CardTitle className="text-base font-medium">{group.group}</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[20%]">消息类型</TableHead>
              <TableHead className="w-[30%]">消息模板</TableHead>
              <TableHead className="w-[10%] text-center">短信</TableHead>
              <TableHead className="w-[10%] text-center">APP推送</TableHead>
              <TableHead className="w-[10%] text-center">邮件</TableHead>
              <TableHead className="w-[20%] text-center">发送规则</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {group.rules.map((rule) => (
              <TableRow key={rule.id}>
                <TableCell>{rule.name}</TableCell>
                <TableCell>
                  <div className="max-w-md truncate text-muted-foreground" title={rule.template}>
                    {rule.template}
                  </div>
                </TableCell>
                <TableCell className="text-center">
                  <div className="flex justify-center">
                    <Switch
                      checked={rule.smsEnabled}
                      onCheckedChange={() => onToggleSms(rule.id, rule.smsEnabled)}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>
                </TableCell>
                <TableCell className="text-center">
                  <div className="flex justify-center">
                    <Switch
                      checked={rule.appEnabled}
                      onCheckedChange={() => onToggleApp(rule.id, rule.appEnabled)}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>
                </TableCell>
                <TableCell className="text-center">
                  <div className="flex justify-center">
                    <Switch
                      checked={rule.emailEnabled}
                      onCheckedChange={() => onToggleEmail && onToggleEmail(rule.id, rule.emailEnabled || false)}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>
                </TableCell>
                <TableCell className="text-center">
                  {rule.showRuleButton !== false ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onSetRule && onSetRule(rule)}
                    >
                      设置规则
                    </Button>
                  ) : (
                    <span className="text-muted-foreground text-sm">-</span>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
