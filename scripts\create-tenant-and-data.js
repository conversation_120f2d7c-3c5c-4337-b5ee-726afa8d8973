// 创建租户并插入教练和场地数据
const mysql = require('mysql2/promise');

async function createTenantAndData() {
  console.log('开始创建租户并插入教练和场地数据...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    // 1. 创建租户
    console.log('\n1. 创建租户:');
    const [tenantResult] = await connection.execute(
      `INSERT INTO tenant (tenant_name, contact_person, phone, email, status, created_at, updated_at) 
       VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
      ['默认瑜伽馆', '管理员', '13800000000', '<EMAIL>', 1]
    );
    const tenantId = tenantResult.insertId;
    console.log(`✓ 创建租户成功，ID: ${tenantId}`);

    // 2. 插入教练数据
    console.log('\n2. 插入教练数据:');
    const coaches = [
      {
        tenant_id: tenantId,
        name: '张瑜伽',
        phone: '13800138001',
        email: '<EMAIL>',
        bio: '资深瑜伽教练，拥有10年教学经验，擅长哈他瑜伽和阴瑜伽。',
        specialties: JSON.stringify(['哈他瑜伽', '阴瑜伽', '冥想']),
        certifications: JSON.stringify(['RYT-200', 'RYT-500', '阴瑜伽认证']),
        experience: 10,
        hourly_rate: 200.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '李流瑜',
        phone: '13800138002',
        email: '<EMAIL>',
        bio: '流瑜伽专家，注重体式的流畅性和呼吸的配合。',
        specialties: JSON.stringify(['流瑜伽', '阿斯汤加', '力量瑜伽']),
        certifications: JSON.stringify(['RYT-200', '阿斯汤加认证']),
        experience: 8,
        hourly_rate: 180.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '王空中',
        phone: '13800138003',
        email: '<EMAIL>',
        bio: '空中瑜伽专业教练，让学员在空中体验瑜伽的乐趣。',
        specialties: JSON.stringify(['空中瑜伽', '普拉提', '舞韵瑜伽']),
        certifications: JSON.stringify(['空中瑜伽认证', '普拉提认证']),
        experience: 6,
        hourly_rate: 220.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '陈孕产',
        phone: '13800138004',
        email: '<EMAIL>',
        bio: '孕产瑜伽专家，专注于孕期和产后恢复瑜伽。',
        specialties: JSON.stringify(['孕产瑜伽', '产后修复', '亲子瑜伽']),
        certifications: JSON.stringify(['孕产瑜伽认证', '产后修复认证']),
        experience: 7,
        hourly_rate: 250.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '赵私教',
        phone: '13800138005',
        email: '<EMAIL>',
        bio: '私教专家，提供一对一个性化瑜伽指导。',
        specialties: JSON.stringify(['私教课程', '理疗瑜伽', '体态矫正']),
        certifications: JSON.stringify(['RYT-500', '理疗瑜伽认证']),
        experience: 12,
        hourly_rate: 300.00,
        status: 1
      }
    ];

    for (const coach of coaches) {
      const [result] = await connection.execute(
        `INSERT INTO coach (tenant_id, name, phone, email, bio, specialties, certifications, experience, hourly_rate, status, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [coach.tenant_id, coach.name, coach.phone, coach.email, coach.bio, coach.specialties, coach.certifications, coach.experience, coach.hourly_rate, coach.status]
      );
      console.log(`✓ 创建教练成功: ${coach.name} (ID: ${result.insertId})`);
    }

    // 3. 插入场地数据
    console.log('\n3. 插入场地数据:');
    const venues = [
      {
        tenant_id: tenantId,
        name: '1号瑜伽室',
        location: '一楼东侧',
        capacity: 15,
        area: 50.5,
        equipment: JSON.stringify(['瑜伽垫', '瑜伽砖', '瑜伽带', '音响设备']),
        description: '宽敞明亮的瑜伽教室，适合团体课程。',
        hourly_rate: 100.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '2号瑜伽室',
        location: '一楼西侧',
        capacity: 10,
        area: 35.0,
        equipment: JSON.stringify(['瑜伽垫', '瑜伽球', '音响设备']),
        description: '温馨的小班教室，适合精品课程。',
        hourly_rate: 80.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '3号瑜伽室',
        location: '二楼东侧',
        capacity: 15,
        area: 55.0,
        equipment: JSON.stringify(['瑜伽垫', '瑜伽砖', '瑜伽带', '冥想垫', '音响设备']),
        description: '安静的瑜伽教室，适合冥想和阴瑜伽课程。',
        hourly_rate: 120.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '空中瑜伽室',
        location: '二楼西侧',
        capacity: 8,
        area: 40.0,
        equipment: JSON.stringify(['空中瑜伽吊床', '瑜伽垫', '安全垫', '音响设备']),
        description: '专业的空中瑜伽教室，配备专业吊床设备。',
        hourly_rate: 150.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '私教室',
        location: '三楼',
        capacity: 2,
        area: 25.0,
        equipment: JSON.stringify(['瑜伽垫', '瑜伽砖', '瑜伽带', '瑜伽球', '理疗床']),
        description: '私密的一对一教学空间，配备理疗设备。',
        hourly_rate: 200.00,
        status: 1
      },
      {
        tenant_id: tenantId,
        name: '热瑜伽室',
        location: '地下一层',
        capacity: 12,
        area: 45.0,
        equipment: JSON.stringify(['瑜伽垫', '毛巾', '加热设备', '通风设备']),
        description: '专业的热瑜伽教室，温度可调节。',
        hourly_rate: 130.00,
        status: 1
      }
    ];

    for (const venue of venues) {
      const [result] = await connection.execute(
        `INSERT INTO venue (tenant_id, name, location, capacity, area, equipment, description, hourly_rate, status, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [venue.tenant_id, venue.name, venue.location, venue.capacity, venue.area, venue.equipment, venue.description, venue.hourly_rate, venue.status]
      );
      console.log(`✓ 创建场地成功: ${venue.name} (ID: ${result.insertId})`);
    }

    // 4. 验证插入结果
    console.log('\n4. 验证插入结果:');
    
    const [coaches_result] = await connection.execute(`SELECT COUNT(*) as count FROM coach WHERE tenant_id = ${tenantId}`);
    const [venues_result] = await connection.execute(`SELECT COUNT(*) as count FROM venue WHERE tenant_id = ${tenantId}`);
    
    console.log(`✓ 租户ID: ${tenantId}`);
    console.log(`✓ 教练总数: ${coaches_result[0].count} 个`);
    console.log(`✓ 场地总数: ${venues_result[0].count} 个`);
    
    // 显示教练列表
    const [coaches_list] = await connection.execute(`SELECT id, name, phone, hourly_rate FROM coach WHERE tenant_id = ${tenantId}`);
    console.log('\n教练列表:');
    coaches_list.forEach((coach, index) => {
      console.log(`  ${index + 1}. ${coach.name} - ${coach.phone} (¥${coach.hourly_rate}/小时)`);
    });
    
    // 显示场地列表
    const [venues_list] = await connection.execute(`SELECT id, name, location, capacity, hourly_rate FROM venue WHERE tenant_id = ${tenantId}`);
    console.log('\n场地列表:');
    venues_list.forEach((venue, index) => {
      console.log(`  ${index + 1}. ${venue.name} - ${venue.location} (容量: ${venue.capacity}人, ¥${venue.hourly_rate}/小时)`);
    });

    await connection.end();
    console.log('\n✓ 租户、教练和场地数据创建完成!');
    console.log('\n现在可以在添加课程页面选择真实的教练和场地了！');
    console.log(`请使用租户ID: ${tenantId} 来测试API`);
    
  } catch (error) {
    console.error('创建失败:', error.message);
  }
}

createTenantAndData();
