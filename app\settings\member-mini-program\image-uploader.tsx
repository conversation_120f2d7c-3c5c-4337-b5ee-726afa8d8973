"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { Upload, Link, Image as ImageIcon } from "lucide-react"

interface ImageUploaderProps {
  imageUrl: string
  onImageChange: (url: string) => void
  className?: string
}

export function ImageUploader({ imageUrl, onImageChange, className }: ImageUploaderProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("url")
  const [urlInput, setUrlInput] = useState(imageUrl)
  
  // 预设图片
  const presetImages = [
    "https://images.unsplash.com/photo-1545205597-3d9d02c29597?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    "https://images.unsplash.com/photo-1599447292180-45fd84092ef4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    "https://images.unsplash.com/photo-1518611012118-696072aa579a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    "https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    "https://images.unsplash.com/photo-1510894347713-fc3ed6fdf539?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
  ]
  
  // 应用图片
  const applyImage = (url: string) => {
    onImageChange(url)
    setIsOpen(false)
  }
  
  // 处理URL输入
  const handleUrlSubmit = () => {
    if (urlInput) {
      applyImage(urlInput)
    }
  }
  
  // 模拟文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // 在实际应用中，这里应该上传文件到服务器
      // 这里我们只是模拟一个本地URL
      const localUrl = URL.createObjectURL(file)
      applyImage(localUrl)
    }
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div 
          className={cn(
            "relative cursor-pointer overflow-hidden rounded-md border border-dashed border-gray-300 hover:border-gray-400 transition-colors",
            className
          )}
        >
          {imageUrl ? (
            <img 
              src={imageUrl} 
              alt="Uploaded" 
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="flex flex-col items-center justify-center p-4 h-full">
              <ImageIcon className="h-8 w-8 text-gray-400 mb-2" />
              <span className="text-sm text-gray-500">点击上传图片</span>
            </div>
          )}
          <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
            <Button variant="secondary" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              更换图片
            </Button>
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>上传图片</DialogTitle>
          <DialogDescription>
            选择一张图片上传或输入图片URL
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="url">图片URL</TabsTrigger>
            <TabsTrigger value="upload">本地上传</TabsTrigger>
          </TabsList>
          
          <TabsContent value="url" className="space-y-4">
            <div className="space-y-2 mt-4">
              <Label htmlFor="url">图片URL</Label>
              <div className="flex gap-2">
                <Input 
                  id="url" 
                  value={urlInput} 
                  onChange={(e) => setUrlInput(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                />
                <Button onClick={handleUrlSubmit}>应用</Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>预设图片</Label>
              <div className="grid grid-cols-3 gap-2">
                {presetImages.map((url, index) => (
                  <div 
                    key={index}
                    className="aspect-video rounded-md overflow-hidden cursor-pointer border hover:border-primary transition-colors"
                    onClick={() => applyImage(url)}
                  >
                    <img 
                      src={url} 
                      alt={`Preset ${index + 1}`} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="upload" className="space-y-4">
            <div className="mt-4">
              <Label htmlFor="file">选择文件</Label>
              <div className="mt-2 flex justify-center rounded-lg border border-dashed border-gray-300 px-6 py-10">
                <div className="text-center">
                  <ImageIcon className="mx-auto h-12 w-12 text-gray-300" />
                  <div className="mt-4 flex text-sm leading-6 text-gray-500">
                    <label
                      htmlFor="file-upload"
                      className="relative cursor-pointer rounded-md bg-white font-semibold text-primary focus-within:outline-none focus-within:ring-2 focus-within:ring-primary"
                    >
                      <span>上传文件</span>
                      <Input
                        id="file-upload"
                        type="file"
                        accept="image/*"
                        className="sr-only"
                        onChange={handleFileUpload}
                      />
                    </label>
                    <p className="pl-1">或拖放到此处</p>
                  </div>
                  <p className="text-xs leading-5 text-gray-500">PNG, JPG, GIF 最大 10MB</p>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="sm:justify-end">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            取消
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
