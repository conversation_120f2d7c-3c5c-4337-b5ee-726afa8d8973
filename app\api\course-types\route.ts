import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// 定义课程类型接口
interface CourseType {
  id: number;
  name: string;
  description: string | null;
  color: string | null;
  status: number | null;
  display_order: number | null;
  course_count: number | null;
}

// GET /api/course-types
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const keyword = searchParams.get('keyword');
    const status = searchParams.get('status');
    
    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID参数',
        data: null
      }, { status: 400 });
    }
    
    // 模拟课程类型数据
    const mockCourseTypes: CourseType[] = [
      {
        id: 1,
        name: '基础瑜伽',
        description: '适合初学者的基础瑜伽课程',
        color: '#4285F4',
        status: 1,
        display_order: 1,
        course_count: 5
      },
      {
        id: 2,
        name: '进阶瑜伽',
        description: '适合有基础的学员',
        color: '#34A853',
        status: 1,
        display_order: 2,
        course_count: 3
      },
      {
        id: 3,
        name: '阴瑜伽',
        description: '深度放松的阴瑜伽',
        color: '#FBBC05',
        status: 1,
        display_order: 3,
        course_count: 4
      },
      {
        id: 4,
        name: '孕产瑜伽',
        description: '专为孕妇设计的瑜伽',
        color: '#EA4335',
        status: 1,
        display_order: 4,
        course_count: 2
      },
      {
        id: 5,
        name: '空中瑜伽',
        description: '空中瑜伽练习',
        color: '#FF6D91',
        status: 1,
        display_order: 5,
        course_count: 3
      },
      {
        id: 6,
        name: '私教课',
        description: '一对一私教课程',
        color: '#9C27B0',
        status: 1,
        display_order: 6,
        course_count: 6
      }
    ];
    
    // 根据关键词筛选
    let filteredTypes = [...mockCourseTypes];
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      filteredTypes = filteredTypes.filter(
        type => 
          type.name.toLowerCase().includes(lowerKeyword) || 
          (type.description && type.description.toLowerCase().includes(lowerKeyword))
      );
    }
    
    // 根据状态筛选
    if (status && status !== 'all') {
      const statusValue = status === 'active' ? 1 : 0;
      filteredTypes = filteredTypes.filter(type => type.status === statusValue);
    }
    
    // 按显示顺序排序
    filteredTypes.sort((a, b) => {
      const orderA = a.display_order || 0;
      const orderB = b.display_order || 0;
      return orderA - orderB;
    });
    
    // 格式化返回数据
    const formattedCourseTypes = filteredTypes.map((type: CourseType) => ({
      id: type.id.toString(),
      name: type.name,
      description: type.description || '',
      color: type.color || '#3b82f6',
      status: type.status === 1 ? 'active' : 'inactive',
      display_order: type.display_order || 0,
      course_count: type.course_count || 0
    }));
    
    return NextResponse.json({
      code: 200,
      msg: '获取课程类型列表成功',
      data: {
        list: formattedCourseTypes,
        total: formattedCourseTypes.length
      }
    });
  } catch (error) {
    console.error('获取课程类型列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取课程类型列表失败',
      data: null
    }, { status: 500 });
  }
}

// POST /api/course-types
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const { tenant_id, name, description, color, display_order } = data;
    
    // 验证必填字段
    if (!tenant_id || !name) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }
    
    // 模拟创建新课程类型
    const newCourseType = {
      id: 7, // 模拟自增ID
      name,
      description: description || null,
      color: color || '#3b82f6',
      status: 1,
      display_order: display_order || 0,
      course_count: 0
    };
    
    return NextResponse.json({
      code: 200,
      msg: '添加课程类型成功',
      data: {
        id: newCourseType.id.toString(),
        name: newCourseType.name,
        description: newCourseType.description || '',
        color: newCourseType.color || '#3b82f6',
        status: 'active',
        display_order: newCourseType.display_order || 0,
        course_count: 0
      }
    });
  } catch (error) {
    console.error('添加课程类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '添加课程类型失败',
      data: null
    }, { status: 500 });
  }
}