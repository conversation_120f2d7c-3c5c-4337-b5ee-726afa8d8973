import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/course-types
export async function GET(request: NextRequest) {
  try {
    // 从URL参数中获取查询条件
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const keyword = searchParams.get('keyword');
    const status = searchParams.get('status');
    const showWithCourses = searchParams.get('showWithCourses') === 'true';
    const showWithoutCourses = searchParams.get('showWithoutCourses') === 'true';
    const createdAfter = searchParams.get('createdAfter');
    const createdBefore = searchParams.get('createdBefore');

    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID参数',
        data: null
      }, { status: 400 });
    }

    // 构建查询条件
    const where: any = {
      tenant_id: parseInt(tenantId),
    };

    // 关键词搜索
    if (keyword) {
      where.name = {
        contains: keyword
      };
    }

    // 状态筛选
    if (status && status !== 'all') {
      where.status = status === 'active' ? 1 : 0;
    }

    // 创建日期筛选
    if (createdAfter || createdBefore) {
      where.created_at = {};
      
      if (createdAfter) {
        where.created_at.gte = new Date(createdAfter);
      }
      
      if (createdBefore) {
        where.created_at.lte = new Date(createdBefore);
      }
    }

    // 查询课程类型
    const courseTypes = await prisma.courseType.findMany({
      where,
      orderBy: [
        { display_order: 'asc' },
        { created_at: 'desc' }
      ]
    });

    // 格式化返回数据
    const formattedTypes = courseTypes.map(type => ({
      id: type.id,
      name: type.name,
      description: type.description || '',
      color: type.color || '#3b82f6',
      status: type.status === 1 ? 'active' : 'inactive',
      displayOrder: type.display_order || 0,
      courseCount: type.course_count || 0,
      createdAt: type.created_at ? type.created_at.toISOString() : '',
      updatedAt: type.updated_at ? type.updated_at.toISOString() : ''
    }));

    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '获取课程类型成功',
      data: {
        list: formattedTypes,
        total: formattedTypes.length,
        page: 1,
        pageSize: formattedTypes.length
      }
    });
  } catch (error: any) {
    console.error('获取课程类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `获取课程类型失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}

// POST /api/course-types
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      tenantId,
      name,
      description,
      color,
      displayOrder
    } = body;

    // 验证必填字段
    if (!tenantId || !name) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }

    // 创建课程类型
    const courseType = await prisma.courseType.create({
      data: {
        tenant_id: parseInt(tenantId),
        name,
        description,
        color,
        display_order: displayOrder || 0,
        status: 1, // 默认启用
        course_count: 0
      }
    });

    // 格式化返回数据
    const formattedType = {
      id: courseType.id,
      name: courseType.name,
      description: courseType.description || '',
      color: courseType.color || '#3b82f6',
      status: courseType.status === 1 ? 'active' : 'inactive',
      displayOrder: courseType.display_order || 0,
      courseCount: courseType.course_count || 0,
      createdAt: courseType.created_at ? courseType.created_at.toISOString() : '',
      updatedAt: courseType.updated_at ? courseType.updated_at.toISOString() : ''
    };

    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '创建课程类型成功',
      data: formattedType
    });
  } catch (error: any) {
    console.error('创建课程类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `创建课程类型失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}