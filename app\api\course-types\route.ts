import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// GET /api/course-types
export async function GET(request: NextRequest) {
  try {
    // 从URL参数中获取查询条件
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const keyword = searchParams.get('keyword');
    const status = searchParams.get('status');
    const showWithCourses = searchParams.get('showWithCourses') === 'true';
    const showWithoutCourses = searchParams.get('showWithoutCourses') === 'true';
    const createdAfter = searchParams.get('createdAfter');
    const createdBefore = searchParams.get('createdBefore');
    const sortBy = searchParams.get('sortBy') || 'display_order'; // 默认按排序字段排序

    console.log('课程类型API接收参数:', { tenantId, keyword, status, sortBy });

    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID参数',
        data: null
      }, { status: 400 });
    }

    // 构建查询条件
    const where: any = {
      tenant_id: parseInt(tenantId),
    };

    // 关键词搜索
    if (keyword) {
      where.OR = [
        { name: { contains: keyword } },
        { description: { contains: keyword } }
      ];
    }

    // 状态筛选
    if (status && status !== 'all') {
      where.status = status === 'active' ? 1 : 0;
    }

    // 创建日期筛选
    if (createdAfter || createdBefore) {
      where.created_at = {};

      if (createdAfter) {
        where.created_at.gte = new Date(createdAfter);
      }

      if (createdBefore) {
        where.created_at.lte = new Date(createdBefore);
      }
    }

    // 构建排序条件
    let orderBy: any = {};
    switch (sortBy) {
      case 'display_order':
        orderBy = [
          { display_order: 'asc' },
          { created_at: 'desc' }
        ];
        break;
      case 'name':
        orderBy = { name: 'asc' };
        break;
      case 'created_at':
        orderBy = { created_at: 'desc' };
        break;
      case 'course_count':
        orderBy = { course_count: 'desc' };
        break;
      default:
        orderBy = [
          { display_order: 'asc' },
          { created_at: 'desc' }
        ];
    }

    // 查询课程类型，包含课程数量统计
    const courseTypes = await prisma.courseType.findMany({
      where,
      orderBy,
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      }
    });

    // 更新课程数量统计
    for (const type of courseTypes) {
      const actualCourseCount = type._count.courses;
      if (type.course_count !== actualCourseCount) {
        await prisma.courseType.update({
          where: { id: type.id },
          data: { course_count: actualCourseCount }
        });
      }
    }

    // 根据课程数量筛选
    let filteredTypes = courseTypes;
    if (showWithCourses && !showWithoutCourses) {
      filteredTypes = courseTypes.filter(type => type._count.courses > 0);
    } else if (showWithoutCourses && !showWithCourses) {
      filteredTypes = courseTypes.filter(type => type._count.courses === 0);
    }

    // 格式化返回数据
    const formattedTypes = filteredTypes.map(type => ({
      id: type.id.toString(),
      name: type.name,
      description: type.description || '',
      color: type.color || '#3b82f6',
      status: type.status === 1 ? 'active' : 'inactive',
      displayOrder: type.display_order || 0,
      courseCount: type._count.courses,
      courses: type._count.courses, // 兼容前端
      createdAt: type.created_at ? type.created_at.toISOString().split('T')[0] : '',
      updatedAt: type.updated_at ? type.updated_at.toISOString().split('T')[0] : ''
    }));

    console.log('课程类型查询结果:', formattedTypes.length, '条记录');

    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '获取课程类型成功',
      data: {
        list: formattedTypes,
        total: formattedTypes.length,
        page: 1,
        pageSize: formattedTypes.length
      }
    });
  } catch (error: any) {
    console.error('获取课程类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `获取课程类型失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}

// POST /api/course-types
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      tenantId,
      name,
      description,
      color,
      displayOrder,
      status
    } = body;

    console.log('创建课程类型API接收数据:', body);

    // 验证必填字段
    if (!tenantId || !name) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }

    // 检查名称是否重复
    const existingType = await prisma.courseType.findFirst({
      where: {
        tenant_id: parseInt(tenantId),
        name: name
      }
    });

    if (existingType) {
      return NextResponse.json({
        code: 400,
        msg: '课程类型名称已存在',
        data: null
      }, { status: 400 });
    }

    // 如果没有指定排序，获取最大排序值+1
    let finalDisplayOrder = displayOrder;
    if (finalDisplayOrder === undefined || finalDisplayOrder === null) {
      const maxOrderType = await prisma.courseType.findFirst({
        where: { tenant_id: parseInt(tenantId) },
        orderBy: { display_order: 'desc' }
      });
      finalDisplayOrder = (maxOrderType?.display_order || 0) + 1;
    }

    // 创建课程类型
    const courseType = await prisma.courseType.create({
      data: {
        tenant_id: parseInt(tenantId),
        name,
        description: description || '',
        color: color || '#3b82f6',
        display_order: finalDisplayOrder,
        status: status === 'inactive' ? 0 : 1, // 默认启用
        course_count: 0
      }
    });

    console.log('课程类型创建成功:', courseType);

    // 格式化返回数据
    const formattedType = {
      id: courseType.id.toString(),
      name: courseType.name,
      description: courseType.description || '',
      color: courseType.color || '#3b82f6',
      status: courseType.status === 1 ? 'active' : 'inactive',
      displayOrder: courseType.display_order || 0,
      courseCount: courseType.course_count || 0,
      courses: courseType.course_count || 0, // 兼容前端
      createdAt: courseType.created_at ? courseType.created_at.toISOString().split('T')[0] : '',
      updatedAt: courseType.updated_at ? courseType.updated_at.toISOString().split('T')[0] : ''
    };

    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '创建课程类型成功',
      data: formattedType
    });
  } catch (error: any) {
    console.error('创建课程类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `创建课程类型失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}