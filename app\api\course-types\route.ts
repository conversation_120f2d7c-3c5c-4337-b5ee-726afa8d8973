import { NextRequest, NextResponse } from 'next/server';
import { courseTypeService } from '@/services/course-type-service';
import { CourseTypeFilter } from '@/types/course-type';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    
    // 构建过滤参数
    const filter: CourseTypeFilter = {
      keyword: searchParams.get('keyword') || undefined,
      status: searchParams.get('status') || undefined,
    };
    
    // 处理布尔值筛选条件
    if (searchParams.has('showWithCourses')) {
      filter.showWithCourses = searchParams.get('showWithCourses') === 'true';
    }
    
    if (searchParams.has('showWithoutCourses')) {
      filter.showWithoutCourses = searchParams.get('showWithoutCourses') === 'true';
    }
    
    // 处理日期范围
    if (searchParams.has('createdAfter')) {
      filter.createdAfter = searchParams.get('createdAfter') || undefined;
    }
    
    if (searchParams.has('createdBefore')) {
      filter.createdBefore = searchParams.get('createdBefore') || undefined;
    }
    
    const types = courseTypeService.getAll(filter);
    console.log('API返回类型数据:', types);
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: {
        list: types,
        total: types.length
      }
    });
  } catch (error) {
    console.error('获取课程类型列表错误:', error);
    return NextResponse.json(
      { code: 500, msg: '获取数据失败', error: String(error) },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // 基本验证
    if (!data.name) {
      return NextResponse.json(
        { code: 400, msg: '课程类型名称不能为空' },
        { status: 400 }
      );
    }
    
    const newType = courseTypeService.create(data);
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: newType
    });
  } catch (error) {
    console.error('创建课程类型错误:', error);
    return NextResponse.json(
      { code: 500, msg: '创建失败', error: String(error) },
      { status: 500 }
    );
  }
} 