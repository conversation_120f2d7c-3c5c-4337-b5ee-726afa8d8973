"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { 
  UserPlus, 
  Users, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  FileSignature, 
  ShieldCheck, 
  Mail 
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { usePremiumServices } from "@/hooks/use-premium-services"

// 用户表单验证模式
const userFormSchema = z.object({
  name: z.string().min(2, {
    message: "姓名至少需要2个字符",
  }),
  email: z.string().email({
    message: "请输入有效的电子邮箱",
  }),
  mobile: z.string().min(11, {
    message: "请输入有效的手机号码",
  }),
  role: z.enum(["ADMIN", "MANAGER", "STAFF"], {
    required_error: "请选择用户角色",
  }),
  department: z.string().optional(),
  position: z.string().optional(),
  permissions: z.array(z.string()).optional(),
})

// 权限选项
const permissionOptions = [
  { id: "CONTRACT_CREATE", label: "创建合同" },
  { id: "CONTRACT_SIGN", label: "签署合同" },
  { id: "CONTRACT_VIEW", label: "查看合同" },
  { id: "TEMPLATE_MANAGE", label: "管理模板" },
  { id: "USER_MANAGE", label: "管理用户" },
  { id: "CERT_MANAGE", label: "管理认证信息" },
]

// 角色预设权限
const rolePresetPermissions = {
  ADMIN: ["CONTRACT_CREATE", "CONTRACT_SIGN", "CONTRACT_VIEW", "TEMPLATE_MANAGE", "USER_MANAGE", "CERT_MANAGE"],
  MANAGER: ["CONTRACT_CREATE", "CONTRACT_SIGN", "CONTRACT_VIEW", "TEMPLATE_MANAGE"],
  STAFF: ["CONTRACT_SIGN", "CONTRACT_VIEW"],
}

// 模拟用户数据
const mockUsers = [
  {
    id: "user-1",
    name: "张三",
    email: "<EMAIL>",
    mobile: "13800138001",
    role: "ADMIN",
    department: "管理部",
    position: "总经理",
    status: "ACTIVE",
    lastLogin: "2023-06-15 14:30",
    permissions: ["CONTRACT_CREATE", "CONTRACT_SIGN", "CONTRACT_VIEW", "TEMPLATE_MANAGE", "USER_MANAGE", "CERT_MANAGE"],
  },
  {
    id: "user-2",
    name: "李四",
    email: "<EMAIL>",
    mobile: "13800138002",
    role: "MANAGER",
    department: "销售部",
    position: "销售经理",
    status: "ACTIVE",
    lastLogin: "2023-06-14 09:15",
    permissions: ["CONTRACT_CREATE", "CONTRACT_SIGN", "CONTRACT_VIEW", "TEMPLATE_MANAGE"],
  },
  {
    id: "user-3",
    name: "王五",
    email: "<EMAIL>",
    mobile: "13800138003",
    role: "STAFF",
    department: "市场部",
    position: "市场专员",
    status: "INACTIVE",
    lastLogin: "2023-05-20 16:45",
    permissions: ["CONTRACT_SIGN", "CONTRACT_VIEW"],
  },
]

interface UserManagementProps {
  className?: string
}

export function UserManagement({ className }: UserManagementProps) {
  const { config, loading } = usePremiumServices()
  const [users, setUsers] = useState(mockUsers)
  const [showAddUserDialog, setShowAddUserDialog] = useState(false)
  const [showEditUserDialog, setShowEditUserDialog] = useState(false)
  const [selectedUser, setSelectedUser] = useState<any>(null)
  const [searchQuery, setSearchQuery] = useState("")
  
  // 检查是否启用了企业多用户功能
  const isFeatureEnabled = !loading && config?.eContract?.enabled && config?.eContract?.features?.enterpriseUsers
  
  // 初始化表单
  const form = useForm<z.infer<typeof userFormSchema>>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      name: "",
      email: "",
      mobile: "",
      role: "STAFF",
      department: "",
      position: "",
      permissions: [],
    },
  })
  
  // 处理角色变更，自动设置对应的权限
  const handleRoleChange = (role: string) => {
    if (role in rolePresetPermissions) {
      form.setValue("permissions", rolePresetPermissions[role as keyof typeof rolePresetPermissions])
    }
  }
  
  // 处理添加用户
  const handleAddUser = (values: z.infer<typeof userFormSchema>) => {
    const newUser = {
      id: `user-${Date.now()}`,
      ...values,
      status: "ACTIVE",
      lastLogin: "从未登录",
    }
    
    setUsers([...users, newUser])
    setShowAddUserDialog(false)
    form.reset()
    
    toast({
      title: "添加成功",
      description: `用户 ${values.name} 已成功添加`,
    })
  }
  
  // 处理编辑用户
  const handleEditUser = (values: z.infer<typeof userFormSchema>) => {
    if (!selectedUser) return
    
    const updatedUsers = users.map(user => 
      user.id === selectedUser.id ? { ...user, ...values } : user
    )
    
    setUsers(updatedUsers)
    setShowEditUserDialog(false)
    setSelectedUser(null)
    
    toast({
      title: "更新成功",
      description: `用户 ${values.name} 信息已更新`,
    })
  }
  
  // 处理删除用户
  const handleDeleteUser = (userId: string) => {
    const userToDelete = users.find(user => user.id === userId)
    if (!userToDelete) return
    
    const updatedUsers = users.filter(user => user.id !== userId)
    setUsers(updatedUsers)
    
    toast({
      title: "删除成功",
      description: `用户 ${userToDelete.name} 已删除`,
    })
  }
  
  // 处理编辑用户对话框打开
  const handleEditUserDialogOpen = (user: any) => {
    setSelectedUser(user)
    form.reset({
      name: user.name,
      email: user.email,
      mobile: user.mobile,
      role: user.role,
      department: user.department || "",
      position: user.position || "",
      permissions: user.permissions || [],
    })
    setShowEditUserDialog(true)
  }
  
  // 处理发送邀请
  const handleSendInvitation = (userId: string) => {
    const userToInvite = users.find(user => user.id === userId)
    if (!userToInvite) return
    
    toast({
      title: "邀请已发送",
      description: `邀请已发送至 ${userToInvite.email}`,
    })
  }
  
  // 过滤用户列表
  const filteredUsers = users.filter(user => 
    user.name.includes(searchQuery) || 
    user.email.includes(searchQuery) || 
    user.mobile.includes(searchQuery) ||
    user.department?.includes(searchQuery) ||
    user.position?.includes(searchQuery)
  )
  
  // 获取角色名称
  const getRoleName = (role: string) => {
    switch (role) {
      case "ADMIN": return "管理员"
      case "MANAGER": return "经理"
      case "STAFF": return "员工"
      default: return role
    }
  }
  
  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge className="bg-green-500">已激活</Badge>
      case "INACTIVE":
        return <Badge variant="outline" className="text-yellow-500 border-yellow-500">未激活</Badge>
      case "SUSPENDED":
        return <Badge variant="outline" className="text-red-500 border-red-500">已停用</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }
  
  // 如果功能未启用，显示提示信息
  if (!isFeatureEnabled) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>企业多用户管理</CardTitle>
          <CardDescription>
            管理企业内部不同角色的用户及其权限
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10 space-y-4">
          <ShieldCheck className="h-12 w-12 text-muted-foreground/50" />
          <h3 className="text-lg font-semibold">功能未开通</h3>
          <p className="text-sm text-muted-foreground text-center max-w-md">
            企业多用户管理是增值服务的高级功能，需要开通后才能使用。
            开通后，您可以添加多个企业用户，并为他们分配不同的角色和权限。
          </p>
          <Button onClick={() => window.location.href = "/premium-services"}>
            了解详情
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            企业多用户管理
          </div>
          <Button size="sm" onClick={() => {
            form.reset()
            setShowAddUserDialog(true)
          }}>
            <UserPlus className="mr-2 h-4 w-4" />
            添加用户
          </Button>
        </CardTitle>
        <CardDescription>
          管理企业内部不同角色的用户及其权限
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <Input
              placeholder="搜索用户..."
              className="max-w-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>姓名</TableHead>
                  <TableHead>联系方式</TableHead>
                  <TableHead>角色</TableHead>
                  <TableHead>部门/职位</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>最后登录</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.length > 0 ? (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{user.email}</div>
                          <div className="text-muted-foreground">{user.mobile}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getRoleName(user.role)}</TableCell>
                      <TableCell>
                        {user.department && (
                          <div className="text-sm">
                            <div>{user.department}</div>
                            {user.position && (
                              <div className="text-muted-foreground">{user.position}</div>
                            )}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>{getStatusBadge(user.status)}</TableCell>
                      <TableCell>{user.lastLogin}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleEditUserDialogOpen(user)}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleSendInvitation(user.id)}>
                              <Mail className="mr-2 h-4 w-4" />
                              发送邀请
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={() => handleDeleteUser(user.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                      没有找到匹配的用户
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
      
      {/* 添加用户对话框 */}
      <Dialog open={showAddUserDialog} onOpenChange={setShowAddUserDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加用户</DialogTitle>
            <DialogDescription>
              添加新的企业用户并设置其角色和权限
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddUser)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>姓名</FormLabel>
                      <FormControl>
                        <Input placeholder="输入用户姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>角色</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value)
                          handleRoleChange(value)
                        }}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择用户角色" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ADMIN">管理员</SelectItem>
                          <SelectItem value="MANAGER">经理</SelectItem>
                          <SelectItem value="STAFF">员工</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        不同角色拥有不同的默认权限
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>电子邮箱</FormLabel>
                      <FormControl>
                        <Input placeholder="输入电子邮箱" {...field} />
                      </FormControl>
                      <FormDescription>
                        用于登录和接收通知
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="mobile"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>手机号码</FormLabel>
                      <FormControl>
                        <Input placeholder="输入手机号码" {...field} />
                      </FormControl>
                      <FormDescription>
                        用于接收验证码和签署通知
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>部门</FormLabel>
                      <FormControl>
                        <Input placeholder="输入部门名称（可选）" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>职位</FormLabel>
                      <FormControl>
                        <Input placeholder="输入职位名称（可选）" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="permissions"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel>权限设置</FormLabel>
                      <FormDescription>
                        选择该用户可以执行的操作
                      </FormDescription>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {permissionOptions.map((permission) => (
                        <FormField
                          key={permission.id}
                          control={form.control}
                          name="permissions"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={permission.id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(permission.id)}
                                    onCheckedChange={(checked) => {
                                      const currentPermissions = field.value || []
                                      if (checked) {
                                        field.onChange([...currentPermissions, permission.id])
                                      } else {
                                        field.onChange(
                                          currentPermissions.filter(
                                            (value) => value !== permission.id
                                          )
                                        )
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {permission.label}
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddUserDialog(false)}
                >
                  取消
                </Button>
                <Button type="submit">
                  <UserPlus className="mr-2 h-4 w-4" />
                  添加用户
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* 编辑用户对话框 */}
      <Dialog open={showEditUserDialog} onOpenChange={setShowEditUserDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑用户</DialogTitle>
            <DialogDescription>
              修改用户信息、角色和权限
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleEditUser)} className="space-y-6">
              {/* 表单内容与添加用户对话框相同 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>姓名</FormLabel>
                      <FormControl>
                        <Input placeholder="输入用户姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>角色</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value)
                          handleRoleChange(value)
                        }}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择用户角色" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ADMIN">管理员</SelectItem>
                          <SelectItem value="MANAGER">经理</SelectItem>
                          <SelectItem value="STAFF">员工</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        不同角色拥有不同的默认权限
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>电子邮箱</FormLabel>
                      <FormControl>
                        <Input placeholder="输入电子邮箱" {...field} />
                      </FormControl>
                      <FormDescription>
                        用于登录和接收通知
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="mobile"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>手机号码</FormLabel>
                      <FormControl>
                        <Input placeholder="输入手机号码" {...field} />
                      </FormControl>
                      <FormDescription>
                        用于接收验证码和签署通知
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>部门</FormLabel>
                      <FormControl>
                        <Input placeholder="输入部门名称（可选）" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>职位</FormLabel>
                      <FormControl>
                        <Input placeholder="输入职位名称（可选）" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="permissions"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel>权限设置</FormLabel>
                      <FormDescription>
                        选择该用户可以执行的操作
                      </FormDescription>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {permissionOptions.map((permission) => (
                        <FormField
                          key={permission.id}
                          control={form.control}
                          name="permissions"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={permission.id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(permission.id)}
                                    onCheckedChange={(checked) => {
                                      const currentPermissions = field.value || []
                                      if (checked) {
                                        field.onChange([...currentPermissions, permission.id])
                                      } else {
                                        field.onChange(
                                          currentPermissions.filter(
                                            (value) => value !== permission.id
                                          )
                                        )
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {permission.label}
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowEditUserDialog(false)}
                >
                  取消
                </Button>
                <Button type="submit">
                  <Edit className="mr-2 h-4 w-4" />
                  保存修改
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
