import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface ReviewStatsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ReviewStatsDialog({ open, onOpenChange }: ReviewStatsDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>评价统计分析</DialogTitle>
          <DialogDescription>查看教练评价的统计数据和分析报告</DialogDescription>
        </DialogHeader>

        <div className="flex justify-end">
          <Select defaultValue="last30days">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last7days">最近7天</SelectItem>
              <SelectItem value="last30days">最近30天</SelectItem>
              <SelectItem value="last90days">最近90天</SelectItem>
              <SelectItem value="lastYear">最近一年</SelectItem>
              <SelectItem value="custom">自定义范围</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">总体概览</TabsTrigger>
            <TabsTrigger value="coaches">教练分析</TabsTrigger>
            <TabsTrigger value="courses">课程分析</TabsTrigger>
            <TabsTrigger value="sentiment">情感分析</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>总评价数</CardDescription>
                  <CardTitle className="text-2xl">256</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+28</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>平均评分</CardDescription>
                  <CardTitle className="text-2xl">4.7</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+0.2</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>回复率</CardDescription>
                  <CardTitle className="text-2xl">85%</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+5%</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>5星评价</CardDescription>
                  <CardTitle className="text-2xl">178</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    占比 <span className="text-green-500">69.5%</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>4星评价</CardDescription>
                  <CardTitle className="text-2xl">62</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    占比 <span className="text-blue-500">24.2%</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>3星及以下</CardDescription>
                  <CardTitle className="text-2xl">16</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    占比 <span className="text-yellow-500">6.3%</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>评分分布</CardTitle>
                <CardDescription>各评分等级分布情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">评分分布图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>评价趋势</CardTitle>
                <CardDescription>评价数量和评分随时间变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">评价趋势图表</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="coaches" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>教练评分排名</CardTitle>
                <CardDescription>各教练评分排名</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">教练评分排名图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>教练评价数量</CardTitle>
                <CardDescription>各教练收到的评价数量</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">教练评价数量图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>教练评分趋势</CardTitle>
                <CardDescription>各教练评分随时间变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">教练评分趋势图表</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="courses" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>课程类型评分</CardTitle>
                <CardDescription>各类型课程的平均评分</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">课程类型评分图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>课程评价数量</CardTitle>
                <CardDescription>各课程收到的评价数量</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">课程评价数量图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>热门课程排名</CardTitle>
                <CardDescription>按评价数量和评分排名的热门课程</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">热门课程排名图表</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sentiment" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>评价关键词</CardTitle>
                <CardDescription>评价中出现的高频关键词</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">评价关键词云图</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>情感分析</CardTitle>
                <CardDescription>评价内容的情感分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">情感分析图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>改进建议</CardTitle>
                <CardDescription>基于评价内容的改进建议</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">改进建议分析</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

