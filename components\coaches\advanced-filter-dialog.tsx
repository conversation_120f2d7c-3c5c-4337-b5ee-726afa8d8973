"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { useState } from "react"

interface AdvancedFilterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AdvancedFilterDialog({ open, onOpenChange }: AdvancedFilterDialogProps) {
  const [ratingRange, setRatingRange] = useState([3, 5])
  const [experienceRange, setExperienceRange] = useState([0, 10])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
          <DialogDescription>设置更多筛选条件以查找特定教练</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>教练状态</Label>
            <div className="grid grid-cols-3 gap-2">
              {["在职", "请假中", "已离职"].map((status) => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox id={`status-${status}`} />
                  <Label htmlFor={`status-${status}`} className="font-normal">
                    {status}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>教学专长</Label>
            <div className="grid grid-cols-2 gap-2">
              {["基础瑜伽", "高级瑜伽", "阴瑜伽", "孕产瑜伽", "空中瑜伽", "热瑜伽"].map((specialty) => (
                <div key={specialty} className="flex items-center space-x-2">
                  <Checkbox id={`specialty-${specialty}`} />
                  <Label htmlFor={`specialty-${specialty}`} className="font-normal">
                    {specialty}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label>评分范围</Label>
            <div className="px-2">
              <Slider defaultValue={ratingRange} max={5} min={1} step={0.1} onValueChange={setRatingRange} />
              <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                <span>{ratingRange[0]}</span>
                <span>{ratingRange[1]}</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>工作经验（年）</Label>
            <div className="px-2">
              <Slider defaultValue={experienceRange} max={20} min={0} step={1} onValueChange={setExperienceRange} />
              <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                <span>{experienceRange[0]}</span>
                <span>{experienceRange[1]}</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="join-date-from">入职日期</Label>
            <div className="flex items-center gap-2">
              <Input id="join-date-from" type="date" className="w-1/2" />
              <span>至</span>
              <Input id="join-date-to" type="date" className="w-1/2" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="salary-type">薪资类型</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="请选择薪资类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="fixed">固定薪资</SelectItem>
                <SelectItem value="hourly">课时费</SelectItem>
                <SelectItem value="mixed">底薪+课时费</SelectItem>
                <SelectItem value="commission">底薪+提成</SelectItem>
                <SelectItem value="full">底薪+课时费+提成</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>其他条件</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="has-certificate" />
                <Label htmlFor="has-certificate" className="font-normal">
                  持有专业证书
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="full-time" />
                <Label htmlFor="full-time" className="font-normal">
                  全职教练
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="can-private" />
                <Label htmlFor="can-private" className="font-normal">
                  可提供私教课
                </Label>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button variant="outline">重置</Button>
          <Button>应用筛选</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

