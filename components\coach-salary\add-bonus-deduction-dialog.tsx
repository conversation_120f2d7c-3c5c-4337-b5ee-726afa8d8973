"use client"

import { useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface AddBonusDeductionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  type: "bonus" | "deduction"
  coachId: string
  coachName: string
  month: string
}

// 奖金类型选项
const bonusTypes = [
  { value: "performance", label: "绩效奖金" },
  { value: "attendance", label: "满勤奖" },
  { value: "holiday", label: "节日奖金" },
  { value: "year-end", label: "年终奖金" },
  { value: "other", label: "其他奖金" },
]

// 扣款类型选项
const deductionTypes = [
  { value: "late", label: "迟到" },
  { value: "absence", label: "缺勤" },
  { value: "leave", label: "请假" },
  { value: "mistake", label: "工作失误" },
  { value: "other", label: "其他扣款" },
]

export function AddBonusDeductionDialog({
  open,
  onOpenChange,
  type,
  coachId,
  coachName,
  month,
}: AddBonusDeductionDialogProps) {
  const [amount, setAmount] = useState<string>("")
  const [reason, setReason] = useState<string>("")
  const [reasonType, setReasonType] = useState<string>("")
  const [date, setDate] = useState<string>(new Date().toISOString().split("T")[0])
  const [description, setDescription] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)

  // 获取标题
  const getTitle = () => {
    return type === "bonus" ? "添加奖金" : "添加扣款"
  }

  // 获取描述
  const getDescription = () => {
    return type === "bonus"
      ? `为 ${coachName} 添加 ${month} 的奖金`
      : `为 ${coachName} 添加 ${month} 的扣款`
  }

  // 获取类型选项
  const getTypeOptions = () => {
    return type === "bonus" ? bonusTypes : deductionTypes
  }

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    setIsSubmitting(true)
    
    // 构建数据
    const data = {
      coachId,
      month,
      type,
      amount: parseFloat(amount),
      reasonType,
      reason: reasonType === "other" ? reason : getTypeOptions().find(t => t.value === reasonType)?.label,
      date,
      description,
    }
    
    // 这里应该调用API提交数据
    console.log(`提交${type === "bonus" ? "奖金" : "扣款"}数据:`, data)
    
    // 模拟API调用延迟
    setTimeout(() => {
      setIsSubmitting(false)
      onOpenChange(false)
      
      // 重置表单
      setAmount("")
      setReason("")
      setReasonType("")
      setDate(new Date().toISOString().split("T")[0])
      setDescription("")
    }, 1000)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>{getTitle()}</DialogTitle>
            <DialogDescription>{getDescription()}</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">教练姓名</p>
                <p className="font-medium">{coachName}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">薪资月份</p>
                <p className="font-medium">{month}</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="amount">{type === "bonus" ? "奖金金额" : "扣款金额"}(元)</Label>
              <Input
                id="amount"
                type="number"
                min="0"
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder={`请输入${type === "bonus" ? "奖金" : "扣款"}金额`}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="reason-type">{type === "bonus" ? "奖金类型" : "扣款类型"}</Label>
              <Select
                value={reasonType}
                onValueChange={setReasonType}
                required
              >
                <SelectTrigger id="reason-type">
                  <SelectValue placeholder={`请选择${type === "bonus" ? "奖金" : "扣款"}类型`} />
                </SelectTrigger>
                <SelectContent>
                  {getTypeOptions().map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {reasonType === "other" && (
              <div className="space-y-2">
                <Label htmlFor="reason">{type === "bonus" ? "奖金原因" : "扣款原因"}</Label>
                <Input
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder={`请输入${type === "bonus" ? "奖金" : "扣款"}原因`}
                  required
                />
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="date">日期</Label>
              <Input
                id="date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">详细说明</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder={`请输入${type === "bonus" ? "奖金" : "扣款"}的详细说明`}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "提交中..." : "确认添加"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
