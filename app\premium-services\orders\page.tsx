"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Download, Eye, MoreHorizontal } from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// 模拟订购记录数据
const orderRecords = [
  {
    id: "ORD-001",
    serviceName: "智能排课系统",
    orderDate: "2023-05-15",
    startDate: "2023-05-16",
    endDate: "2024-05-15",
    price: "299/月",
    totalAmount: "3,588.00",
    status: "active",
    paymentMethod: "微信支付",
    paymentStatus: "已支付",
  },
  {
    id: "ORD-002",
    serviceName: "多渠道营销工具",
    orderDate: "2023-06-01",
    startDate: "2023-06-02",
    endDate: "2023-12-01",
    price: "499/月",
    totalAmount: "2,994.00",
    status: "active",
    paymentMethod: "支付宝",
    paymentStatus: "已支付",
  },
  {
    id: "ORD-003",
    serviceName: "私教绩效系统",
    orderDate: "2023-07-10",
    startDate: "2023-07-11",
    endDate: "2024-01-10",
    price: "349/月",
    totalAmount: "2,094.00",
    status: "active",
    paymentMethod: "银行转账",
    paymentStatus: "已支付",
  },
  {
    id: "ORD-004",
    serviceName: "高级财务报表",
    orderDate: "2023-04-20",
    startDate: "2023-04-21",
    endDate: "2023-10-20",
    price: "599/月",
    totalAmount: "3,594.00",
    status: "expired",
    paymentMethod: "微信支付",
    paymentStatus: "已支付",
  },
  {
    id: "ORD-005",
    serviceName: "会员数据分析",
    orderDate: "2023-03-15",
    startDate: "2023-03-16",
    endDate: "2023-09-15",
    price: "399/月",
    totalAmount: "2,394.00",
    status: "expired",
    paymentMethod: "支付宝",
    paymentStatus: "已支付",
  },
]

export default function PremiumServicesOrdersPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // 过滤订单记录
  const filteredOrders = orderRecords.filter(
    (order) =>
      (statusFilter === "all" ||
        (statusFilter === "active" && order.status === "active") ||
        (statusFilter === "expired" && order.status === "expired")) &&
      (order.serviceName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.id.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">订购记录</h1>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          导出记录
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center gap-2 max-w-md">
          <Input
            placeholder="搜索订单号或服务名称..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <div className="flex items-center gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="筛选状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="active">使用中</SelectItem>
              <SelectItem value="expired">已过期</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>订购记录列表</CardTitle>
          <CardDescription>查看所有增值服务的订购记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>订单号</TableHead>
                <TableHead>服务名称</TableHead>
                <TableHead>订购日期</TableHead>
                <TableHead>服务期限</TableHead>
                <TableHead>价格</TableHead>
                <TableHead>总金额</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>支付方式</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.id}</TableCell>
                  <TableCell>{order.serviceName}</TableCell>
                  <TableCell>{order.orderDate}</TableCell>
                  <TableCell>{`${order.startDate} 至 ${order.endDate}`}</TableCell>
                  <TableCell>{order.price}</TableCell>
                  <TableCell>¥{order.totalAmount}</TableCell>
                  <TableCell>
                    <Badge variant={order.status === "active" ? "default" : "secondary"}>
                      {order.status === "active" ? "使用中" : "已过期"}
                    </Badge>
                  </TableCell>
                  <TableCell>{order.paymentMethod}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        {order.status === "active" && (
                          <DropdownMenuItem>续费服务</DropdownMenuItem>
                        )}
                        {order.status === "expired" && (
                          <DropdownMenuItem>重新开通</DropdownMenuItem>
                        )}
                        <DropdownMenuItem>下载发票</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
