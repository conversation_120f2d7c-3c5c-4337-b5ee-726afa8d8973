"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { PlusCircle, Trash2 } from "lucide-react"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon } from "lucide-react"

const formSchema = z.object({
  // 迟到惩罚
  latePenalty: z.object({
    enabled: z.boolean(),
    thresholdMinutes: z.coerce.number().min(0).max(120).optional(),
  }),
  
  // 缺席惩罚
  absencePenalty: z.object({
    enabled: z.boolean(),
  }),
  
  // 临时取消惩罚
  lateCancellationPenalty: z.object({
    enabled: z.boolean(),
    thresholdHours: z.coerce.number().min(0).max(24).optional(),
  }),
  
  // 频繁取消惩罚
  frequentCancellationPenalty: z.object({
    enabled: z.boolean(),
    thresholdCount: z.coerce.number().min(0).max(20).optional(),
    timeWindowDays: z.coerce.number().min(0).max(90).optional(),
  }),
  
  // 豁免设置
  exemptions: z.object({
    firstViolation: z.boolean(),
    maxExemptionsPerMonth: z.coerce.number().min(0).max(10),
  }),
  
  // 覆盖全局设置
  overrideGlobal: z.boolean(),
})

interface PenaltyAction {
  id: string;
  type: string;
  value: number;
  days?: number;
}

interface CoursePenaltyRulesProps {
  courseTypeId: string;
  onChange: () => void;
}

export function CoursePenaltyRules({ courseTypeId, onChange }: CoursePenaltyRulesProps) {
  // 根据课程类型获取不同的默认值
  const getDefaultValues = () => {
    // 这里可以根据courseTypeId返回不同的默认值
    if (courseTypeId === "private") {
      return {
        latePenalty: {
          enabled: true,
          thresholdMinutes: 10,
        },
        absencePenalty: {
          enabled: true,
        },
        lateCancellationPenalty: {
          enabled: true,
          thresholdHours: 4,
        },
        frequentCancellationPenalty: {
          enabled: true,
          thresholdCount: 2,
          timeWindowDays: 7,
        },
        exemptions: {
          firstViolation: false,
          maxExemptionsPerMonth: 0,
        },
        overrideGlobal: true,
      }
    }
    
    return {
      latePenalty: {
        enabled: true,
        thresholdMinutes: 15,
      },
      absencePenalty: {
        enabled: true,
      },
      lateCancellationPenalty: {
        enabled: true,
        thresholdHours: 6,
      },
      frequentCancellationPenalty: {
        enabled: true,
        thresholdCount: 3,
        timeWindowDays: 7,
      },
      exemptions: {
        firstViolation: true,
        maxExemptionsPerMonth: 1,
      },
      overrideGlobal: false,
    }
  }
  
  const [latePenaltyActions, setLatePenaltyActions] = useState<PenaltyAction[]>([
    { id: "1", type: "deductPoints", value: 5 }
  ])
  
  const [absencePenaltyActions, setAbsencePenaltyActions] = useState<PenaltyAction[]>([
    { id: "1", type: "deductExtraCount", value: 1 }
  ])
  
  const [lateCancellationPenaltyActions, setLateCancellationPenaltyActions] = useState<PenaltyAction[]>([
    { id: "1", type: "deductPoints", value: 3 }
  ])
  
  const [frequentCancellationPenaltyActions, setFrequentCancellationPenaltyActions] = useState<PenaltyAction[]>([
    { id: "1", type: "suspendBooking", value: 0, days: 3 }
  ])
  
  const [exemptedLevels, setExemptedLevels] = useState<string[]>(["gold"])
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
  })
  
  const watchLatePenalty = form.watch("latePenalty.enabled")
  const watchAbsencePenalty = form.watch("absencePenalty.enabled")
  const watchLateCancellationPenalty = form.watch("lateCancellationPenalty.enabled")
  const watchFrequentCancellationPenalty = form.watch("frequentCancellationPenalty.enabled")
  const watchOverrideGlobal = form.watch("overrideGlobal")
  
  const addPenaltyAction = (
    type: "late" | "absence" | "lateCancellation" | "frequentCancellation",
    defaultAction: PenaltyAction
  ) => {
    switch (type) {
      case "late":
        const newLateId = (latePenaltyActions.length + 1).toString()
        setLatePenaltyActions([...latePenaltyActions, { ...defaultAction, id: newLateId }])
        break
      case "absence":
        const newAbsenceId = (absencePenaltyActions.length + 1).toString()
        setAbsencePenaltyActions([...absencePenaltyActions, { ...defaultAction, id: newAbsenceId }])
        break
      case "lateCancellation":
        const newLateCancellationId = (lateCancellationPenaltyActions.length + 1).toString()
        setLateCancellationPenaltyActions([...lateCancellationPenaltyActions, { ...defaultAction, id: newLateCancellationId }])
        break
      case "frequentCancellation":
        const newFrequentCancellationId = (frequentCancellationPenaltyActions.length + 1).toString()
        setFrequentCancellationPenaltyActions([...frequentCancellationPenaltyActions, { ...defaultAction, id: newFrequentCancellationId }])
        break
    }
  }
  
  const removePenaltyAction = (
    type: "late" | "absence" | "lateCancellation" | "frequentCancellation",
    id: string
  ) => {
    switch (type) {
      case "late":
        setLatePenaltyActions(latePenaltyActions.filter(action => action.id !== id))
        break
      case "absence":
        setAbsencePenaltyActions(absencePenaltyActions.filter(action => action.id !== id))
        break
      case "lateCancellation":
        setLateCancellationPenaltyActions(lateCancellationPenaltyActions.filter(action => action.id !== id))
        break
      case "frequentCancellation":
        setFrequentCancellationPenaltyActions(frequentCancellationPenaltyActions.filter(action => action.id !== id))
        break
    }
  }
  
  const updatePenaltyAction = (
    type: "late" | "absence" | "lateCancellation" | "frequentCancellation",
    id: string,
    field: keyof PenaltyAction,
    value: string | number
  ) => {
    switch (type) {
      case "late":
        setLatePenaltyActions(latePenaltyActions.map(action => 
          action.id === id ? { ...action, [field]: value } : action
        ))
        break
      case "absence":
        setAbsencePenaltyActions(absencePenaltyActions.map(action => 
          action.id === id ? { ...action, [field]: value } : action
        ))
        break
      case "lateCancellation":
        setLateCancellationPenaltyActions(lateCancellationPenaltyActions.map(action => 
          action.id === id ? { ...action, [field]: value } : action
        ))
        break
      case "frequentCancellation":
        setFrequentCancellationPenaltyActions(frequentCancellationPenaltyActions.map(action => 
          action.id === id ? { ...action, [field]: value } : action
        ))
        break
    }
  }
  
  const toggleExemptedLevel = (level: string) => {
    if (exemptedLevels.includes(level)) {
      setExemptedLevels(exemptedLevels.filter(l => l !== level))
    } else {
      setExemptedLevels([...exemptedLevels, level])
    }
  }
  
  const renderPenaltyActions = (
    type: "late" | "absence" | "lateCancellation" | "frequentCancellation",
    actions: PenaltyAction[]
  ) => {
    return (
      <div className="space-y-4 mt-4">
        {actions.map((action) => (
          <div key={action.id} className="flex items-center space-x-2 border rounded-md p-3">
            <Select
              value={action.type}
              onValueChange={(value) => updatePenaltyAction(type, action.id, "type", value)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择惩罚类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="deductPoints">扣除积分</SelectItem>
                <SelectItem value="deductExtraCount">扣除额外次数</SelectItem>
                <SelectItem value="suspendBooking">暂停预约权限</SelectItem>
                <SelectItem value="lowerPriority">降低预约优先级</SelectItem>
                <SelectItem value="limitBookings">限制预约数量</SelectItem>
              </SelectContent>
            </Select>
            
            {action.type === "deductPoints" && (
              <div className="flex items-center">
                <Input
                  type="number"
                  value={action.value}
                  onChange={(e) => updatePenaltyAction(type, action.id, "value", parseInt(e.target.value))}
                  className="w-20"
                />
                <span className="ml-2">积分</span>
              </div>
            )}
            
            {action.type === "deductExtraCount" && (
              <div className="flex items-center">
                <Input
                  type="number"
                  value={action.value}
                  onChange={(e) => updatePenaltyAction(type, action.id, "value", parseInt(e.target.value))}
                  className="w-20"
                />
                <span className="ml-2">次</span>
              </div>
            )}
            
            {action.type === "suspendBooking" && (
              <div className="flex items-center">
                <Input
                  type="number"
                  value={action.days}
                  onChange={(e) => updatePenaltyAction(type, action.id, "days", parseInt(e.target.value))}
                  className="w-20"
                />
                <span className="ml-2">天</span>
              </div>
            )}
            
            {action.type === "lowerPriority" && (
              <div className="flex items-center">
                <Input
                  type="number"
                  value={action.days}
                  onChange={(e) => updatePenaltyAction(type, action.id, "days", parseInt(e.target.value))}
                  className="w-20"
                />
                <span className="ml-2">天</span>
              </div>
            )}
            
            {action.type === "limitBookings" && (
              <div className="flex items-center space-x-2">
                <div className="flex items-center">
                  <Input
                    type="number"
                    value={action.value}
                    onChange={(e) => updatePenaltyAction(type, action.id, "value", parseInt(e.target.value))}
                    className="w-20"
                  />
                  <span className="ml-2">次</span>
                </div>
                <span>，</span>
                <div className="flex items-center">
                  <Input
                    type="number"
                    value={action.days}
                    onChange={(e) => updatePenaltyAction(type, action.id, "days", parseInt(e.target.value))}
                    className="w-20"
                  />
                  <span className="ml-2">天</span>
                </div>
              </div>
            )}
            
            {actions.length > 1 && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => removePenaltyAction(type, action.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        ))}
        
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => addPenaltyAction(type, { id: "0", type: "deductPoints", value: 5 })}
        >
          <PlusCircle className="h-4 w-4 mr-2" />
          添加惩罚措施
        </Button>
      </div>
    )
  }
  
  return (
    <Form {...form}>
      <form onChange={onChange} className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>惩罚规则设置</CardTitle>
                <CardDescription>
                  设置课程相关的惩罚规则，包括迟到、缺席、取消等惩罚
                </CardDescription>
              </div>
              <FormField
                control={form.control}
                name="overrideGlobal"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      覆盖全局设置
                    </FormLabel>
                  </FormItem>
                )}
              />
            </div>
          </CardHeader>
          <CardContent>
            {!watchOverrideGlobal && (
              <Alert className="mb-6">
                <InfoIcon className="h-4 w-4" />
                <AlertTitle>使用全局设置</AlertTitle>
                <AlertDescription>
                  当前使用全局惩罚规则设置。启用"覆盖全局设置"开关可自定义此课程类型的惩罚规则。
                </AlertDescription>
              </Alert>
            )}
            
            {watchOverrideGlobal && (
              <div className="space-y-6">
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="item-1">
                    <AccordionTrigger>
                      <div className="flex items-center">
                        <span>迟到惩罚</span>
                        {watchLatePenalty && <Badge className="ml-2">已启用</Badge>}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4 pt-4">
                        <FormField
                          control={form.control}
                          name="latePenalty.enabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">启用迟到惩罚</FormLabel>
                                <FormDescription>
                                  对迟到签到的会员进行惩罚
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        {watchLatePenalty && (
                          <>
                            <FormField
                              control={form.control}
                              name="latePenalty.thresholdMinutes"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>迟到判定阈值</FormLabel>
                                  <FormControl>
                                    <div className="flex items-center">
                                      <Input type="number" {...field} className="w-24" />
                                      <span className="ml-2">分钟</span>
                                    </div>
                                  </FormControl>
                                  <FormDescription>
                                    课程开始后多少分钟签到判定为迟到
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            
                            <div>
                              <h4 className="text-sm font-medium mb-2">惩罚措施</h4>
                              {renderPenaltyActions("late", latePenaltyActions)}
                            </div>
                          </>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="item-2">
                    <AccordionTrigger>
                      <div className="flex items-center">
                        <span>缺席惩罚</span>
                        {watchAbsencePenalty && <Badge className="ml-2">已启用</Badge>}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4 pt-4">
                        <FormField
                          control={form.control}
                          name="absencePenalty.enabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">启用缺席惩罚</FormLabel>
                                <FormDescription>
                                  对预约未到且未取消的会员进行惩罚
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        {watchAbsencePenalty && (
                          <div>
                            <h4 className="text-sm font-medium mb-2">惩罚措施</h4>
                            {renderPenaltyActions("absence", absencePenaltyActions)}
                          </div>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="item-3">
                    <AccordionTrigger>
                      <div className="flex items-center">
                        <span>临时取消惩罚</span>
                        {watchLateCancellationPenalty && <Badge className="ml-2">已启用</Badge>}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4 pt-4">
                        <FormField
                          control={form.control}
                          name="lateCancellationPenalty.enabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">启用临时取消惩罚</FormLabel>
                                <FormDescription>
                                  对课程开始前短时间内取消的会员进行惩罚
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        {watchLateCancellationPenalty && (
                          <>
                            <FormField
                              control={form.control}
                              name="lateCancellationPenalty.thresholdHours"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>临时取消判定阈值</FormLabel>
                                  <FormControl>
                                    <div className="flex items-center">
                                      <Input type="number" {...field} className="w-24" />
                                      <span className="ml-2">小时</span>
                                    </div>
                                  </FormControl>
                                  <FormDescription>
                                    课程开始前多少小时内取消判定为临时取消
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            
                            <div>
                              <h4 className="text-sm font-medium mb-2">惩罚措施</h4>
                              {renderPenaltyActions("lateCancellation", lateCancellationPenaltyActions)}
                            </div>
                          </>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="item-4">
                    <AccordionTrigger>
                      <div className="flex items-center">
                        <span>频繁取消惩罚</span>
                        {watchFrequentCancellationPenalty && <Badge className="ml-2">已启用</Badge>}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4 pt-4">
                        <FormField
                          control={form.control}
                          name="frequentCancellationPenalty.enabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">启用频繁取消惩罚</FormLabel>
                                <FormDescription>
                                  对一定时间内取消次数过多的会员进行惩罚
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        
                        {watchFrequentCancellationPenalty && (
                          <>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="frequentCancellationPenalty.thresholdCount"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>取消次数阈值</FormLabel>
                                    <FormControl>
                                      <div className="flex items-center">
                                        <Input type="number" {...field} className="w-24" />
                                        <span className="ml-2">次</span>
                                      </div>
                                    </FormControl>
                                    <FormDescription>
                                      超过多少次取消判定为频繁取消
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={form.control}
                                name="frequentCancellationPenalty.timeWindowDays"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>统计时间窗口</FormLabel>
                                    <FormControl>
                                      <div className="flex items-center">
                                        <Input type="number" {...field} className="w-24" />
                                        <span className="ml-2">天</span>
                                      </div>
                                    </FormControl>
                                    <FormDescription>
                                      在多少天内的取消次数进行统计
                                    </FormDescription>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                            
                            <div>
                              <h4 className="text-sm font-medium mb-2">惩罚措施</h4>
                              {renderPenaltyActions("frequentCancellation", frequentCancellationPenaltyActions)}
                            </div>
                          </>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
                
                <div className="space-y-4 pt-4">
                  <h3 className="text-lg font-medium">惩罚豁免设置</h3>
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="exemptions.firstViolation"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">首次违规豁免</FormLabel>
                          <FormDescription>
                            会员首次违规不受惩罚
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="exemptions.maxExemptionsPerMonth"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>每月最大豁免次数</FormLabel>
                        <FormControl>
                          <div className="flex items-center">
                            <Input type="number" {...field} className="w-24" />
                            <span className="ml-2">次</span>
                          </div>
                        </FormControl>
                        <FormDescription>
                          会员每月最多可以被豁免的次数，设置为0表示不允许豁免
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">豁免会员等级</h4>
                    <FormDescription>
                      选择可以豁免惩罚的会员等级
                    </FormDescription>
                    <div className="flex flex-wrap gap-2 mt-2">
                      <Badge 
                        variant={exemptedLevels.includes("normal") ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => toggleExemptedLevel("normal")}
                      >
                        普通会员
                      </Badge>
                      <Badge 
                        variant={exemptedLevels.includes("silver") ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => toggleExemptedLevel("silver")}
                      >
                        银卡会员
                      </Badge>
                      <Badge 
                        variant={exemptedLevels.includes("gold") ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => toggleExemptedLevel("gold")}
                      >
                        金卡会员
                      </Badge>
                      <Badge 
                        variant={exemptedLevels.includes("platinum") ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => toggleExemptedLevel("platinum")}
                      >
                        白金会员
                      </Badge>
                      <Badge 
                        variant={exemptedLevels.includes("diamond") ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => toggleExemptedLevel("diamond")}
                      >
                        钻石会员
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </form>
    </Form>
  )
}
