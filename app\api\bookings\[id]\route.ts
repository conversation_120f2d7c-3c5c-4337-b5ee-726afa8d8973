import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 获取预约详情
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    console.log('获取预约详情，ID:', id);

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询预约详情
      const [bookingResult] = await connection.execute(
        `SELECT 
          b.*,
          m.name as member_name,
          m.phone as member_phone,
          m.member_no,
          m.level as member_level,
          c.title as course_name,
          c.description as course_description,
          c.duration as course_duration,
          c.price as course_price,
          c.capacity as course_capacity,
          mct.name as card_type_name,
          mct.card_category,
          mc.card_no,
          mc.remaining_times,
          mc.remaining_amount,
          mc.remaining_days
        FROM bookings b
        LEFT JOIN members m ON b.member_id = m.id
        LEFT JOIN course c ON b.course_id = c.id
        LEFT JOIN member_cards mc ON b.member_card_id = mc.id
        LEFT JOIN member_card_types mct ON mc.card_type_id = mct.id
        WHERE b.id = ?`,
        [parseInt(id)]
      );

      if ((bookingResult as any[]).length === 0) {
        return NextResponse.json({
          code: 404,
          msg: '预约记录不存在',
          data: null
        }, { status: 404 });
      }

      const booking = (bookingResult as any[])[0];

      console.log('获取预约详情成功');

      return NextResponse.json({
        code: 200,
        data: booking,
        msg: '获取预约详情成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取预约详情失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取预约详情失败',
      data: null
    }, { status: 500 });
  }
}

// 更新预约状态
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    const data = await req.json();
    console.log('更新预约状态，ID:', id, '数据:', data);

    const { status, cancel_reason, consumed_times, consumed_amount, notes } = data;

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.beginTransaction();

      // 检查预约是否存在
      const [existingBooking] = await connection.execute(
        'SELECT * FROM bookings WHERE id = ?',
        [parseInt(id)]
      );

      if ((existingBooking as any[]).length === 0) {
        throw new Error('预约记录不存在');
      }

      const booking = (existingBooking as any[])[0];

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      if (status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(status);

        // 根据状态更新相应的时间字段
        if (status === 'checked_in') {
          updateFields.push('check_in_time = NOW()');
        } else if (status === 'cancelled') {
          updateFields.push('cancel_time = NOW()');
          if (cancel_reason) {
            updateFields.push('cancel_reason = ?');
            updateValues.push(cancel_reason);
          }
        }
      }

      if (consumed_times !== undefined) {
        updateFields.push('consumed_times = ?');
        updateValues.push(consumed_times);
      }

      if (consumed_amount !== undefined) {
        updateFields.push('consumed_amount = ?');
        updateValues.push(consumed_amount);
      }

      if (notes !== undefined) {
        updateFields.push('notes = ?');
        updateValues.push(notes);
      }

      if (updateFields.length === 0) {
        throw new Error('没有需要更新的字段');
      }

      // 执行更新
      updateValues.push(parseInt(id));
      const updateQuery = `UPDATE bookings SET ${updateFields.join(', ')} WHERE id = ?`;
      
      await connection.execute(updateQuery, updateValues);

      // 如果是签到或完成，更新会员卡余额
      if (status === 'checked_in' || status === 'completed') {
        if (booking.member_card_id && (consumed_times > 0 || consumed_amount > 0)) {
          const updateCardFields = [];
          const updateCardValues = [];

          if (consumed_times > 0) {
            updateCardFields.push('remaining_times = remaining_times - ?');
            updateCardValues.push(consumed_times);
          }

          if (consumed_amount > 0) {
            updateCardFields.push('remaining_amount = remaining_amount - ?');
            updateCardValues.push(consumed_amount);
          }

          if (updateCardFields.length > 0) {
            updateCardValues.push(booking.member_card_id);
            const updateCardQuery = `UPDATE member_cards SET ${updateCardFields.join(', ')} WHERE id = ?`;
            await connection.execute(updateCardQuery, updateCardValues);
          }
        }
      }

      await connection.commit();

      console.log('更新预约状态成功');

      return NextResponse.json({
        code: 200,
        data: { id: parseInt(id), status },
        msg: '更新预约状态成功'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  } catch (error: any) {
    console.error('更新预约状态失败:', error);
    return NextResponse.json({
      code: 500,
      msg: error.message || '更新预约状态失败',
      data: null
    }, { status: 500 });
  }
}

// 删除预约
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    console.log('删除预约，ID:', id);

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 检查预约是否存在
      const [existingBooking] = await connection.execute(
        'SELECT id, status FROM bookings WHERE id = ?',
        [parseInt(id)]
      );

      if ((existingBooking as any[]).length === 0) {
        return NextResponse.json({
          code: 404,
          msg: '预约记录不存在',
          data: null
        }, { status: 404 });
      }

      const booking = (existingBooking as any[])[0];

      // 检查是否可以删除
      if (booking.status === 'completed' || booking.status === 'checked_in') {
        return NextResponse.json({
          code: 400,
          msg: '已签到或已完成的预约无法删除',
          data: null
        }, { status: 400 });
      }

      // 删除预约
      await connection.execute('DELETE FROM bookings WHERE id = ?', [parseInt(id)]);

      console.log('删除预约成功');

      return NextResponse.json({
        code: 200,
        data: null,
        msg: '删除预约成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('删除预约失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '删除预约失败',
      data: null
    }, { status: 500 });
  }
}
