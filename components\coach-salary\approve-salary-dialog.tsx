"use client"

import { useState } from "react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { CheckCircle, XCircle } from "lucide-react"

interface ApproveSalaryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  record: any
}

export function ApproveSalaryDialog({ open, onOpenChange, record }: ApproveSalaryDialogProps) {
  const [approvalStatus, setApprovalStatus] = useState<"approved" | "rejected">("approved")
  const [remark, setRemark] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    setIsSubmitting(true)
    
    // 构建审核数据
    const approvalData = {
      recordId: record?.id,
      status: approvalStatus,
      remark,
    }
    
    // 这里应该调用API提交审核结果
    console.log("提交审核结果:", approvalData)
    
    // 模拟API调用延迟
    setTimeout(() => {
      setIsSubmitting(false)
      onOpenChange(false)
      
      // 重置表单
      setApprovalStatus("approved")
      setRemark("")
    }, 1000)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        {record && (
          <form onSubmit={handleSubmit}>
            <DialogHeader>
              <DialogTitle>审核薪资记录</DialogTitle>
              <DialogDescription>
                审核 {record.coachName} 的 {record.salaryMonth} 薪资记录
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">教练姓名</p>
                  <p className="font-medium">{record.coachName}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">薪资月份</p>
                  <p className="font-medium">{record.salaryMonth}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">总薪资</p>
                  <p className="font-medium">¥{record.totalSalary.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">实发薪资</p>
                  <p className="font-medium">¥{record.netSalary.toLocaleString()}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>审核结果</Label>
                <RadioGroup
                  value={approvalStatus}
                  onValueChange={(value) => setApprovalStatus(value as "approved" | "rejected")}
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="approved" id="approved" />
                    <Label htmlFor="approved" className="flex items-center">
                      <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                      审核通过
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="rejected" id="rejected" />
                    <Label htmlFor="rejected" className="flex items-center">
                      <XCircle className="mr-2 h-4 w-4 text-red-500" />
                      审核拒绝
                    </Label>
                  </div>
                </RadioGroup>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="remark">审核备注</Label>
                <Textarea
                  id="remark"
                  value={remark}
                  onChange={(e) => setRemark(e.target.value)}
                  placeholder="请输入审核备注"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "提交中..." : "提交审核"}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
