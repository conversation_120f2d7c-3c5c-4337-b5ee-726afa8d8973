"use client"

import { useState, useRef, ChangeEvent } from "react"
import { Upload, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface FileUploaderProps {
  onFilesUploaded: (files: File[]) => void
  maxFiles?: number
  maxSize?: number // in bytes
  accept?: string
  className?: string
}

export function FileUploader({
  onFilesUploaded,
  maxFiles = 1,
  maxSize = 5 * 1024 * 1024, // 5MB default
  accept,
  className,
}: FileUploaderProps) {
  const [error, setError] = useState<string | null>(null)
  const [isDragActive, setIsDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFiles = (files: FileList | null): File[] => {
    if (!files || files.length === 0) {
      return []
    }

    // 检查文件数量
    if (files.length > maxFiles) {
      setError(`最多只能上传 ${maxFiles} 个文件`)
      return []
    }

    const validFiles: File[] = []

    // 验证每个文件
    for (let i = 0; i < files.length; i++) {
      const file = files[i]

      // 检查文件大小
      if (file.size > maxSize) {
        setError(`文件 "${file.name}" 超过最大大小限制 (${(maxSize / 1024 / 1024).toFixed(0)}MB)`)
        return []
      }

      // 检查文件类型（如果指定了accept）
      if (accept && !isFileTypeAccepted(file, accept)) {
        setError(`文件 "${file.name}" 类型不被接受，支持的格式: ${accept}`)
        return []
      }

      validFiles.push(file)
    }

    return validFiles
  }

  // 检查文件类型是否被接受
  const isFileTypeAccepted = (file: File, acceptString: string): boolean => {
    const acceptedTypes = acceptString.split(',').map(type => type.trim())

    // 检查MIME类型
    if (acceptedTypes.includes(file.type)) {
      return true
    }

    // 检查文件扩展名
    const fileName = file.name || ''
    const fileExtension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase()

    return acceptedTypes.some(type => {
      // 处理.jpg这样的扩展名格式
      if (type.startsWith('.')) {
        return fileExtension === type.toLowerCase()
      }
      // 处理image/*这样的MIME类型格式
      if (type.endsWith('/*')) {
        const mainType = type.split('/')[0]
        return file.type.startsWith(`${mainType}/`)
      }
      return false
    })
  }

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    const validFiles = validateFiles(files)

    if (validFiles.length > 0) {
      setError(null)
      onFilesUploaded(validFiles)
    }

    // 重置input，以便可以再次选择相同的文件
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(false)
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragActive(false)

    const files = e.dataTransfer.files
    const validFiles = validateFiles(files)

    if (validFiles.length > 0) {
      setError(null)
      onFilesUploaded(validFiles)
    }
  }

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  return (
    <div className="space-y-4">
      <div
        className={cn(
          "border-2 border-dashed rounded-md p-6 cursor-pointer transition-colors",
          isDragActive
            ? "border-primary bg-primary/5"
            : "border-muted-foreground/20 hover:border-primary/50 hover:bg-muted/50",
          className
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          onChange={handleFileChange}
          accept={accept}
          multiple={maxFiles > 1}
        />
        <div className="flex flex-col items-center justify-center space-y-2 text-center">
          <Upload className="h-10 w-10 text-muted-foreground" />
          <div className="space-y-1">
            <p className="text-sm font-medium">
              {isDragActive ? "放开以上传文件" : "拖放文件到此处或点击上传"}
            </p>
            <p className="text-xs text-muted-foreground">
              {accept ? `支持的文件格式: ${accept}` : "支持所有文件格式"}
              {maxSize && ` · 最大文件大小: ${(maxSize / 1024 / 1024).toFixed(0)}MB`}
              {maxFiles > 1 && ` · 最多上传 ${maxFiles} 个文件`}
            </p>
          </div>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
