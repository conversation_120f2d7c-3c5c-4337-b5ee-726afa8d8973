"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Building2,
  Users,
  Phone,
  MapPin,
  User,
  Calendar,
  Clock,
  MoreHorizontal,
  Edit,
  Trash2,
  BarChart4,
  ArrowUpDown
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useState } from "react"
import Link from "next/link"
import { EditStoreDialog } from "./edit-store-dialog"
import { DeleteStoreDialog } from "./delete-store-dialog"

interface StoreCardProps {
  store: {
    id: string
    name: string
    address: string
    phone: string
    manager: string
    status: string
    memberCount: number
    coachCount: number
    monthlyRevenue: number
    openDate: string
    lastSync: string
  }
}

export function StoreCard({ store }: StoreCardProps) {
  const [showEditDial<PERSON>, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN')
  }

  // 格式化时间
  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString)
    return date.toLocaleString('zh-CN')
  }

  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <div
        className={`h-2 ${
          store.status === "active" ? "bg-green-500" : "bg-gray-400"
        }`}
      />
      <CardHeader className="pb-2 flex flex-row items-start justify-between">
        <div>
          <div className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-muted-foreground" />
            <div className="font-semibold text-lg">{store.name}</div>
            <Badge variant={store.status === "active" ? "default" : "secondary"}>
              {store.status === "active" ? "营业中" : "已关闭"}
            </Badge>
          </div>
          <div className="flex items-center gap-1 mt-2 text-sm text-muted-foreground">
            <MapPin className="h-4 w-4" />
            <span>{store.address}</span>
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>操作</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setShowEditDialog(true)}>
              <Edit className="mr-2 h-4 w-4" />
              编辑门店
            </DropdownMenuItem>
            <DropdownMenuItem>
              <ArrowUpDown className="mr-2 h-4 w-4" />
              同步数据
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Users className="mr-2 h-4 w-4" />
              会员管理
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Calendar className="mr-2 h-4 w-4" />
              课程安排
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setShowDeleteDialog(true)} className="text-destructive">
              <Trash2 className="mr-2 h-4 w-4" />
              删除门店
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-1 text-sm">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span>{store.phone}</span>
            </div>
            <div className="flex items-center gap-1 text-sm">
              <User className="h-4 w-4 text-muted-foreground" />
              <span>店长: {store.manager}</span>
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>开业日期: {formatDate(store.openDate)}</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center gap-1 text-sm">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span>会员数: {store.memberCount}</span>
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span>教练数: {store.coachCount}</span>
            </div>
            <div className="flex items-center gap-1 text-sm">
              <BarChart4 className="h-4 w-4 text-muted-foreground" />
              <span>月营收: ¥{store.monthlyRevenue.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* 进度条 */}
        <div className="mt-4 space-y-2">
          <div>
            <div className="flex justify-between mb-1 text-xs">
              <span>会员容量</span>
              <span>{store.memberCount}/600</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-blue-600 h-1.5 rounded-full"
                style={{ width: `${(store.memberCount / 600) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="mt-3 text-xs text-muted-foreground flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <span>最后同步: {formatDateTime(store.lastSync)}</span>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        <Button variant="outline" size="sm">
          <ArrowUpDown className="mr-2 h-4 w-4" />
          同步数据
        </Button>
        <Button size="sm" asChild>
          <Link href={`/premium-services/chain-management/store/${store.id}`}>查看详情</Link>
        </Button>
      </CardFooter>

      {/* 编辑门店对话框 */}
      <EditStoreDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        store={store}
      />

      {/* 删除门店对话框 */}
      <DeleteStoreDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        store={store}
      />
    </Card>
  )
}
