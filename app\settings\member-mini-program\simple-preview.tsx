"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { EnhancedHomePage } from "@/app/member-mini-program/enhanced-home-page"

// 预览组件接收设置作为props
interface SimplePreviewProps {
  settings: any
}

export function SimplePreview({ settings }: SimplePreviewProps) {
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>预览</CardTitle>
        <CardDescription>
          会员端小程序效果预览
        </CardDescription>
      </CardHeader>
      <CardContent className="flex justify-center">
        <div
          className="relative w-[320px] h-[640px] bg-white rounded-[36px] shadow-lg overflow-hidden border-8 border-black"
        >
          <div className="absolute top-0 left-0 right-0 h-6 bg-black flex justify-center items-end pb-1">
            <div className="w-20 h-4 bg-black rounded-b-xl"></div>
          </div>

          <div className="pt-6 h-full">
            {/* 手机状态栏 */}
            <div className="h-6 px-4 flex items-center justify-between text-xs">
              <span>9:41</span>
              <div className="flex items-center gap-1">
                <div className="w-4 h-2 bg-black rounded-sm"></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
              </div>
            </div>

            {/* 小程序内容 */}
            <div className="h-[calc(100%-6rem)] overflow-y-auto">
              <EnhancedHomePage settings={settings} />
            </div>
          </div>

          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-1/3 h-1 bg-black rounded-full"></div>
        </div>
      </CardContent>
    </Card>
  )
}
