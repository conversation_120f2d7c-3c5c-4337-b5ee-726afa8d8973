import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Pencil, Calendar, Users, Clock, MapPin, X, Check } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface BookingDetailDialogProps {
  booking: {
    id: string
    venue: string
    course: string
    coach: string
    date: string
    time: string
    status: string
    bookings: number
    capacity: number
  }
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function BookingDetailDialog({ booking, open, onOpenChange }: BookingDetailDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>预订详情 #{booking.id}</span>
            <Badge
              variant={
                booking.status === "confirmed" ? "default" : booking.status === "pending" ? "outline" : "secondary"
              }
            >
              {booking.status === "confirmed" ? "已确认" : booking.status === "pending" ? "待确认" : "已取消"}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="info">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="info">基本信息</TabsTrigger>
            <TabsTrigger value="members">预订会员</TabsTrigger>
            <TabsTrigger value="history">操作记录</TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">{booking.course}</CardTitle>
                  <CardDescription>{booking.venue}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>{booking.date}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>{booking.time}</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>
                      预订人数: {booking.bookings}/{booking.capacity}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span>位置: 一楼东侧</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">教练信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center">
                    <Avatar className="h-10 w-10 mr-3">
                      <AvatarImage src="/placeholder.svg?height=40&width=40" alt={booking.coach} />
                      <AvatarFallback>{booking.coach[0]}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{booking.coach}</p>
                      <p className="text-sm text-muted-foreground">高级瑜伽教练</p>
                    </div>
                  </div>
                  <div className="text-sm">
                    <p>联系电话: 138****1234</p>
                    <p>教龄: 5年</p>
                    <p>专长: 哈他瑜伽、阴瑜伽</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">设备需求</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  <div className="flex items-center p-2 border rounded-md">
                    <span className="text-sm">瑜伽垫 x 15</span>
                  </div>
                  <div className="flex items-center p-2 border rounded-md">
                    <span className="text-sm">瑜伽砖 x 15</span>
                  </div>
                  <div className="flex items-center p-2 border rounded-md">
                    <span className="text-sm">瑜伽带 x 15</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end gap-2">
              {booking.status === "pending" && (
                <>
                  <Button variant="outline" size="sm" className="gap-1">
                    <X className="h-4 w-4" />
                    拒绝
                  </Button>
                  <Button variant="outline" size="sm" className="gap-1">
                    <Check className="h-4 w-4" />
                    确认
                  </Button>
                </>
              )}
              <Button variant="outline" size="sm" className="gap-1">
                <Calendar className="h-4 w-4" />
                复制预订
              </Button>
              <Button variant="outline" size="sm" className="gap-1">
                <Pencil className="h-4 w-4" />
                编辑预订
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="members">
            <Card>
              <CardHeader>
                <CardTitle>预订会员列表</CardTitle>
                <CardDescription>
                  已预订会员: {booking.bookings}/{booking.capacity}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Array.from({ length: booking.bookings }).map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-3 border rounded-md">
                      <div className="flex items-center">
                        <Avatar className="h-8 w-8 mr-3">
                          <AvatarImage src={`/placeholder.svg?height=32&width=32&text=${i + 1}`} />
                          <AvatarFallback>会员</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">会员{i + 1}</p>
                          <p className="text-xs text-muted-foreground">预订时间: 2025-03-25 14:30</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">铂金会员</Badge>
                        <Button variant="ghost" size="sm">
                          查看详情
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle>操作历史记录</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-1 h-full bg-primary rounded-full"></div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="font-medium">创建预订</p>
                        <p className="text-sm text-muted-foreground">2025-03-25 10:00</p>
                      </div>
                      <p className="text-sm text-muted-foreground">管理员创建了预订</p>
                    </div>
                  </div>

                  {booking.status === "confirmed" && (
                    <div className="flex items-start gap-3">
                      <div className="w-1 h-full bg-primary rounded-full"></div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <p className="font-medium">确认预订</p>
                          <p className="text-sm text-muted-foreground">2025-03-25 10:15</p>
                        </div>
                        <p className="text-sm text-muted-foreground">管理员确认了预订</p>
                      </div>
                    </div>
                  )}

                  <div className="flex items-start gap-3">
                    <div className="w-1 h-full bg-primary rounded-full"></div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="font-medium">通知教练</p>
                        <p className="text-sm text-muted-foreground">2025-03-25 10:20</p>
                      </div>
                      <p className="text-sm text-muted-foreground">系统已通知教练</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-1 h-full bg-primary rounded-full"></div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="font-medium">会员预订</p>
                        <p className="text-sm text-muted-foreground">2025-03-25 14:30</p>
                      </div>
                      <p className="text-sm text-muted-foreground">会员1预订了课程</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

