'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Carousel, CarouselContent, CarouselItem, CarouselPrevious, CarouselNext } from '@/components/ui/carousel';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';

// 示例数据
const chartData = [
  { name: '1月', value: 120 },
  { name: '2月', value: 180 },
  { name: '3月', value: 250 },
  { name: '4月', value: 320 },
  { name: '5月', value: 400 },
  { name: '6月', value: 500 }
];

const HomePage = () => {
  // 使用项目自带的Toast组件
  const { toast } = useToast();

  // 试用表单状态
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    wechat: '',
    company: '',
    employeeCount: '',
    storeType: '',
    customStoreType: '',
    channel: '',
    customChannel: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 表单输入处理
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // 下拉选择处理
  const handleSelectChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
      // 当选择"其他"时，不清空自定义字段，否则清空
      ...((field === 'storeType' && value !== '其他') ? { customStoreType: '' } : {}),
      ...((field === 'channel' && value !== '其他') ? { customChannel: '' } : {})
    }));
  };

  // 表单提交处理
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // 表单验证逻辑
    if (!formData.name || !formData.phone) {
      toast({
        variant: "destructive",
        title: "验证错误",
        description: "请填写必要的信息"
      });
      setIsSubmitting(false);
      return;
    }

    try {
      // 准备提交数据
      const submitData = {
        name: formData.name,
        phone: formData.phone,
        wechat: formData.wechat || '',
        company: formData.company || formData.name + '的工作室', // 如果没有填公司名，使用姓名+工作室
        employeeCount: formData.employeeCount || '',
        storeType: formData.storeType === '其他' ? formData.customStoreType : formData.storeType,
        channel: formData.channel === '其他' ? formData.customChannel : formData.channel,
        message: formData.message || ''
      };

      console.log('提交数据:', submitData);

      // 真实API调用
      const res = await fetch("/api/tenants", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(submitData)
      });
      
      if (!res.ok) {
        throw new Error(`HTTP error! Status: ${res.status}`);
      }
      
      const result = await res.json();
      if (result.success) {
        toast({
          title: "申请成功",
          description: result.message || "我们的客服人员将尽快与您联系"
        });
        setIsDialogOpen(false);
        setFormData({
          name: '',
          phone: '',
          wechat: '',
          company: '',
          employeeCount: '',
          storeType: '',
          customStoreType: '',
          channel: '',
          customChannel: '',
          message: ''
        });
      } else {
        toast({
          variant: "destructive",
          title: "提交失败",
          description: result.error || "请稍后再试"
        });
      }
    } catch (error) {
      console.error('提交表单错误:', error);
      toast({
        variant: "destructive",
        title: "提交失败",
        description: "请稍后再试"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* 导航栏 */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="flex items-center justify-between h-16 4xl:h-20 5xl:h-24">
            <div className="flex items-center">
              <img src="https://ai-public.mastergo.com/ai/img_res/8440626a989270c41c4abf3cd0025b9d.jpg"
                   alt="静心瑜伽生活馆"
                   className="h-8 4xl:h-12 5xl:h-16" />
            </div>
            <div className="hidden md:flex items-center space-x-8 4xl:space-x-12 5xl:space-x-16">
              <a href="#" className="text-gray-600 hover:text-indigo-600 4xl:text-lg 5xl:text-xl">产品功能</a>
              <a href="#" className="text-gray-600 hover:text-indigo-600 4xl:text-lg 5xl:text-xl">解决方案</a>
              <a href="#" className="text-gray-600 hover:text-indigo-600 4xl:text-lg 5xl:text-xl">客户案例</a>
              <a href="#" className="text-gray-600 hover:text-indigo-600 4xl:text-lg 5xl:text-xl">定价方案</a>
              <a href="#" className="text-gray-600 hover:text-indigo-600 4xl:text-lg 5xl:text-xl">帮助中心</a>
              <Link href="/login" className="text-gray-600 hover:text-indigo-600 font-medium ml-4 4xl:text-lg 5xl:text-xl">
                登录
              </Link>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="default" className="bg-indigo-600 hover:bg-indigo-700 4xl:px-6 4xl:py-3 4xl:text-lg 5xl:px-8 5xl:py-4 5xl:text-xl">
                    免费试用
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle className="text-xl font-bold">申请免费试用</DialogTitle>
                    <DialogDescription>
                      请填写以下信息，我们将在 1 个工作日内与您联系。
                    </DialogDescription>
                  </DialogHeader>
                  <form onSubmit={handleSubmit} className="space-y-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-right">
                          姓名 <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          placeholder="请输入您的姓名"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-right">
                          手机号 <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="请输入您的手机号"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="wechat" className="text-right">
                          微信号
                        </Label>
                        <Input
                          id="wechat"
                          name="wechat"
                          value={formData.wechat}
                          onChange={handleInputChange}
                          placeholder="请输入您的微信号"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="company" className="text-right">
                          公司/工作室名称
                        </Label>
                        <Input
                          id="company"
                          name="company"
                          value={formData.company}
                          onChange={handleInputChange}
                          placeholder="请输入公司或工作室名称"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="employeeCount" className="text-right">
                        员工规模
                      </Label>
                      <Select
                        value={formData.employeeCount}
                        onValueChange={(value) => handleSelectChange('employeeCount', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="请选择员工规模" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1-5">1-5 人</SelectItem>
                          <SelectItem value="6-20">6-20 人</SelectItem>
                          <SelectItem value="21-50">21-50 人</SelectItem>
                          <SelectItem value="50+">50 人以上</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="storeType" className="text-right">
                        门店类型
                      </Label>
                      <Select
                        value={formData.storeType}
                        onValueChange={(value) => handleSelectChange('storeType', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="请选择门店类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="纯瑜伽馆">纯瑜伽馆</SelectItem>
                          <SelectItem value="普拉提">普拉提</SelectItem>
                          <SelectItem value="舞蹈工作室">舞蹈工作室</SelectItem>
                          <SelectItem value="综合健身会所">综合健身会所</SelectItem>
                          <SelectItem value="其他">其他</SelectItem>
                        </SelectContent>
                      </Select>

                      {formData.storeType === '其他' && (
                        <Input
                          id="customStoreType"
                          name="customStoreType"
                          value={formData.customStoreType}
                          onChange={handleInputChange}
                          placeholder="请输入门店类型"
                          className="mt-2"
                        />
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="channel" className="text-right">
                        渠道来源
                      </Label>
                      <Select
                        value={formData.channel}
                        onValueChange={(value) => handleSelectChange('channel', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="请选择渠道来源" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="官网">官网</SelectItem>
                          <SelectItem value="微信公众号">微信公众号</SelectItem>
                          <SelectItem value="微博">微博</SelectItem>
                          <SelectItem value="朋友推荐">朋友推荐</SelectItem>
                          <SelectItem value="线下广告">线下广告</SelectItem>
                          <SelectItem value="其他">其他</SelectItem>
                        </SelectContent>
                      </Select>

                      {formData.channel === '其他' && (
                        <Input
                          id="customChannel"
                          name="customChannel"
                          value={formData.customChannel}
                          onChange={handleInputChange}
                          placeholder="请输入渠道来源"
                          className="mt-2"
                        />
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message" className="text-right">
                        需求描述
                      </Label>
                      <Textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        placeholder="请简要描述您的需求或问题"
                        className="min-h-[100px]"
                      />
                    </div>

                    <DialogFooter>
                      <Button type="submit" disabled={isSubmitting} className="bg-indigo-600 hover:bg-indigo-700">
                        {isSubmitting ? '提交中...' : '提交申请'}
                      </Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero区域 */}
      <div className="relative pt-16 4xl:pt-20 5xl:pt-24 overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="https://ai-public.mastergo.com/ai/img_res/fce5b69bc0a12264d383c0465620346b.jpg"
            className="w-full h-full object-cover object-center"
            alt="背景"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-white via-white/90 to-transparent"></div>
        </div>

        <div className="relative max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12 pt-20 4xl:pt-32 5xl:pt-40 pb-24 4xl:pb-32 5xl:pb-40">
          <div className="max-w-lg 4xl:max-w-2xl 5xl:max-w-3xl">
            <h1 className="text-5xl 4xl:text-6xl 5xl:text-7xl font-bold text-gray-900 mb-6 4xl:mb-8 5xl:mb-12 leading-tight">
              一站式瑜伽/普拉提门店经营解决方案
            </h1>
            <p className="text-xl 4xl:text-2xl 5xl:text-3xl text-gray-600 mb-8 4xl:mb-12 5xl:mb-16 leading-relaxed">
              智能化管理系统，让门店运营更轻松，让会员服务更专业，让经营决策更精准
            </p>
            <div className="flex space-x-4 4xl:space-x-6 5xl:space-x-8">
              <Button size="lg" className="bg-indigo-600 hover:bg-indigo-700 4xl:px-8 4xl:py-4 4xl:text-xl 5xl:px-12 5xl:py-6 5xl:text-2xl" onClick={() => setIsDialogOpen(true)}>
                立即体验
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-gray-300 text-gray-700 hover:border-indigo-600 hover:text-indigo-600 4xl:px-8 4xl:py-4 4xl:text-xl 5xl:px-12 5xl:py-6 5xl:text-2xl">
                预约演示
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 核心功能模块 */}
      <div className="py-24 4xl:py-32 5xl:py-40 bg-gray-50">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="text-center mb-16 4xl:mb-20 5xl:mb-24">
            <h2 className="text-3xl 4xl:text-4xl 5xl:text-5xl font-bold text-gray-900">核心功能模块</h2>
            <p className="mt-4 4xl:mt-6 5xl:mt-8 text-gray-600 4xl:text-xl 5xl:text-2xl">专业的瑜伽/普拉提门店管理系统，助力提升运营效率</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 4xl:gap-12 5xl:gap-16">
            {[
              {
                title: '会员管理系统',
                desc: '智能会员画像，精准营销推送，提升续约率',
                icon: 'fa-users'
              },
              {
                title: '排课预约系统',
                desc: '灵活排课管理，在线预约签到，轻松掌握课程动态',
                icon: 'fa-calendar-check'
              },
              {
                title: '教练管理系统',
                desc: '教练排班管理，绩效考核，提升团队效率',
                icon: 'fa-user-tie'
              },
              {
                title: '营收数据分析',
                desc: '多维度数据分析，智能经营决策，提升盈利能力',
                icon: 'fa-chart-line'
              }
            ].map((item, index) => (
              <div key={index} className="bg-white p-8 4xl:p-12 5xl:p-16 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <i className={`fas ${item.icon} text-4xl 4xl:text-5xl 5xl:text-6xl text-indigo-600 mb-6 4xl:mb-8 5xl:mb-10`}></i>
                <h3 className="text-xl 4xl:text-2xl 5xl:text-3xl font-bold text-gray-900 mb-4 4xl:mb-6 5xl:mb-8">{item.title}</h3>
                <p className="text-gray-600 4xl:text-lg 5xl:text-xl mb-4 4xl:mb-6 5xl:mb-8 leading-relaxed">{item.desc}</p>
                <a href="#" className="text-indigo-600 hover:text-indigo-700 flex items-center 4xl:text-lg 5xl:text-xl">
                  了解更多
                  <i className="fas fa-arrow-right ml-2 4xl:ml-3 5xl:ml-4"></i>
                </a>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 数据价值展示 */}
      <div className="py-24 4xl:py-32 5xl:py-40 bg-white">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 4xl:gap-12 5xl:gap-16">
            {[
              { number: '2,000+', label: '服务门店数' },
              { number: '100万+', label: '课程预约量' },
              { number: '50万+', label: '会员管理数' },
              { number: '85%', label: '平均营收提升' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl 4xl:text-5xl 5xl:text-6xl font-bold text-indigo-600 mb-2 4xl:mb-4 5xl:mb-6">{item.number}</div>
                <div className="text-gray-600 4xl:text-xl 5xl:text-2xl">{item.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 经营数据分析 */}
      <div className="py-24 4xl:py-32 5xl:py-40 bg-gray-50">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="text-center mb-16 4xl:mb-20 5xl:mb-24">
            <h2 className="text-3xl 4xl:text-4xl 5xl:text-5xl font-bold text-gray-900">经营数据分析</h2>
            <p className="mt-4 4xl:mt-6 5xl:mt-8 text-gray-600 4xl:text-xl 5xl:text-2xl">直观的数据展示，助力门店经营决策</p>
          </div>
          <div className="w-full h-[400px] 4xl:h-[500px] 5xl:h-[600px]">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="value" stroke="#6366f1" fill="#6366f1" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* 客户案例 */}
      <div className="py-24 4xl:py-32 5xl:py-40 bg-white">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="text-center mb-16 4xl:mb-20 5xl:mb-24">
            <h2 className="text-3xl 4xl:text-4xl 5xl:text-5xl font-bold text-gray-900">成功案例</h2>
            <p className="mt-4 4xl:mt-6 5xl:mt-8 text-gray-600 4xl:text-xl 5xl:text-2xl">值得信赖的瑜伽/普拉提门店管理系统</p>
          </div>

          <Carousel className="mb-16 4xl:mb-20 5xl:mb-24">
            <CarouselContent>
              {[
                {
                  name: '瑜心禅悦瑜伽',
                  content: '使用系统后，会员续约率提升了40%，运营效率显著提升。',
                  author: '张晓芳',
                  title: '创始人'
                },
                {
                  name: '和光普拉提',
                  content: '数据分析功能帮助我们精准把握经营状况，实现了30%的营收增长。',
                  author: '李雯雯',
                  title: '运营总监'
                },
                {
                  name: '静心瑜伽',
                  content: '排课系统极大提升了课程预约效率，会员满意度显著提升。',
                  author: '王梓萱',
                  title: '品牌总监'
                }
              ].map((item, index) => (
                <CarouselItem key={index}>
                  <Card className="bg-gray-50 p-8 4xl:p-12 5xl:p-16">
                    <div className="text-xl 4xl:text-2xl 5xl:text-3xl text-gray-600 mb-6 4xl:mb-8 5xl:mb-10 leading-relaxed">{item.content}</div>
                    <div className="flex items-center">
                      <div>
                        <div className="font-bold text-gray-900 4xl:text-xl 5xl:text-2xl">{item.name}</div>
                        <div className="text-gray-600 4xl:text-lg 5xl:text-xl">{item.author} - {item.title}</div>
                      </div>
                    </div>
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious />
            <CarouselNext />
          </Carousel>
        </div>
      </div>

      {/* 价格方案 */}
      <div className="py-24 4xl:py-32 5xl:py-40 bg-gray-50">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="text-center mb-16 4xl:mb-20 5xl:mb-24">
            <h2 className="text-3xl 4xl:text-4xl 5xl:text-5xl font-bold text-gray-900">价格方案</h2>
            <p className="mt-4 4xl:mt-6 5xl:mt-8 text-gray-600 4xl:text-xl 5xl:text-2xl">灵活的价格方案，满足不同规模门店需求</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 4xl:gap-12 5xl:gap-16">
            {[
              {
                name: '基础版',
                price: '999',
                features: ['会员基础管理', '课程预约管理', '签到管理', '基础数据统计']
              },
              {
                name: '专业版',
                price: '2999',
                features: ['会员精准画像', '智能排课系统', '教练绩效管理', '营收分析报表']
              },
              {
                name: '企业版',
                price: '6999',
                features: ['多店管理', 'AI营销推送', '自定义报表', '专属顾问服务']
              }
            ].map((plan, index) => (
              <Card key={index} className={`p-8 4xl:p-12 5xl:p-16 ${index === 1 ? 'border-2 border-indigo-600' : ''}`}>
                <h3 className="text-xl 4xl:text-2xl 5xl:text-3xl font-bold text-gray-900 mb-4 4xl:mb-6 5xl:mb-8">{plan.name}</h3>
                <div className="text-3xl 4xl:text-4xl 5xl:text-5xl font-bold text-indigo-600 mb-6 4xl:mb-8 5xl:mb-10">
                  ¥{plan.price}<span className="text-base 4xl:text-lg 5xl:text-xl font-normal text-gray-600">/年</span>
                </div>
                <ul className="space-y-4 4xl:space-y-6 5xl:space-y-8 mb-8 4xl:mb-12 5xl:mb-16">
                  {plan.features.map((feature, fIndex) => (
                    <li key={fIndex} className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 4xl:h-6 4xl:w-6 5xl:h-8 5xl:w-8 text-green-500 mr-2 4xl:mr-3 5xl:mr-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-600 4xl:text-lg 5xl:text-xl">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button className={`w-full 4xl:py-4 4xl:text-lg 5xl:py-6 5xl:text-xl ${
                  index === 1
                    ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                    : 'border-2 border-gray-300 text-gray-700 hover:border-indigo-600 hover:text-indigo-600'
                }`} variant={index === 1 ? 'default' : 'outline'}>
                  立即购买
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* 页脚 */}
      <footer className="bg-gray-900 text-gray-300 py-16 4xl:py-20 5xl:py-24">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 4xl:gap-12 5xl:gap-16 mb-8 4xl:mb-12 5xl:mb-16">
            <div>
              <h4 className="text-white font-bold mb-4 4xl:mb-6 5xl:mb-8 4xl:text-xl 5xl:text-2xl">产品</h4>
              <ul className="space-y-2 4xl:space-y-3 5xl:space-y-4">
                <li><a href="#" className="hover:text-white 4xl:text-lg 5xl:text-xl">功能介绍</a></li>
                <li><a href="#" className="hover:text-white 4xl:text-lg 5xl:text-xl">最新更新</a></li>
                <li><a href="#" className="hover:text-white 4xl:text-lg 5xl:text-xl">价格方案</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-bold mb-4 4xl:mb-6 5xl:mb-8 4xl:text-xl 5xl:text-2xl">解决方案</h4>
              <ul className="space-y-2 4xl:space-y-3 5xl:space-y-4">
                <li><a href="#" className="hover:text-white 4xl:text-lg 5xl:text-xl">瑜伽工作室</a></li>
                <li><a href="#" className="hover:text-white 4xl:text-lg 5xl:text-xl">普拉提会所</a></li>
                <li><a href="#" className="hover:text-white 4xl:text-lg 5xl:text-xl">连锁门店</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-bold mb-4 4xl:mb-6 5xl:mb-8 4xl:text-xl 5xl:text-2xl">支持</h4>
              <ul className="space-y-2 4xl:space-y-3 5xl:space-y-4">
                <li><a href="#" className="hover:text-white 4xl:text-lg 5xl:text-xl">帮助中心</a></li>
                <li><a href="#" className="hover:text-white 4xl:text-lg 5xl:text-xl">使用教程</a></li>
                <li><a href="#" className="hover:text-white 4xl:text-lg 5xl:text-xl">联系我们</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-bold mb-4 4xl:mb-6 5xl:mb-8 4xl:text-xl 5xl:text-2xl">联系我们</h4>
              <div className="space-y-2 4xl:space-y-3 5xl:space-y-4">
                <p className="4xl:text-lg 5xl:text-xl">电话：400-888-8888</p>
                <p className="4xl:text-lg 5xl:text-xl">邮箱：<EMAIL></p>
                <div className="flex space-x-4 4xl:space-x-6 5xl:space-x-8 mt-4 4xl:mt-6 5xl:mt-8">
                  <a href="#" className="text-gray-400 hover:text-white">
                    <i className="fab fa-weixin text-2xl 4xl:text-3xl 5xl:text-4xl"></i>
                  </a>
                  <a href="#" className="text-gray-400 hover:text-white">
                    <i className="fab fa-weibo text-2xl 4xl:text-3xl 5xl:text-4xl"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-800 pt-8 4xl:pt-12 5xl:pt-16 text-center">
            <p className="4xl:text-lg 5xl:text-xl">© 2024 YogaFlow. 保留所有权利</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;

