"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import {
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Users,
  CreditCard,
  Percent,
  Clock,
  Award,
  ChevronRight,
  BarChart2,
  Target
} from "lucide-react"
import { SetTargetDialog, TargetData, TargetPeriod } from "./set-target-dialog"

// 模拟数据
const statsData = {
  today: {
    bookings: { value: 128, change: 15.8, isPositive: true, target: 150, progress: 85, color: "bg-blue-500" },
    activeMembers: { value: 573, change: 8.2, isPositive: true, target: 600, progress: 95, color: "bg-green-500" },
    revenue: { value: "¥12,450", change: 20.1, isPositive: true, target: "¥15,000", progress: 83, color: "bg-amber-500" },
    renewalRate: { value: "89%", change: -2.3, isPositive: false, target: "95%", progress: 89, color: "bg-purple-500" },
  },
  week: {
    bookings: { value: 856, change: 12.3, isPositive: true, target: 1000, progress: 85, color: "bg-blue-500" },
    activeMembers: { value: 612, change: 5.7, isPositive: true, target: 650, progress: 94, color: "bg-green-500" },
    revenue: { value: "¥78,350", change: 15.2, isPositive: true, target: "¥85,000", progress: 92, color: "bg-amber-500" },
    renewalRate: { value: "91%", change: 1.5, isPositive: true, target: "95%", progress: 91, color: "bg-purple-500" },
  },
  month: {
    bookings: { value: 3245, change: 8.5, isPositive: true, target: 3500, progress: 92, color: "bg-blue-500" },
    activeMembers: { value: 685, change: 10.2, isPositive: true, target: 700, progress: 97, color: "bg-green-500" },
    revenue: { value: "¥325,780", change: 12.8, isPositive: true, target: "¥350,000", progress: 93, color: "bg-amber-500" },
    renewalRate: { value: "93%", change: 2.1, isPositive: true, target: "95%", progress: 93, color: "bg-purple-500" },
  },
}

export function BusinessOverview() {
  const [timeRange, setTimeRange] = useState("today")
  const router = useRouter()
  const [stats, setStats] = useState(statsData)

  // 获取当前时间范围的数据
  const currentStats = stats[timeRange as keyof typeof stats]

  // 处理目标设置
  const handleSaveTargets = (targets: TargetData, period: TargetPeriod) => {
    // 在实际应用中，这里会调用API保存目标数据
    // 这里我们只是更新本地状态用于演示

    // 根据周期选择更新哪个时间范围的目标
    let timeKey: "today" | "week" | "month";
    switch(period) {
      case "daily":
        timeKey = "today";
        break;
      case "weekly":
        timeKey = "week";
        break;
      case "monthly":
      case "quarterly":
      case "yearly":
        timeKey = "month";
        break;
      default:
        timeKey = "month";
    }

    // 创建新的状态对象
    const newStats = {
      ...stats,
      [timeKey]: {
        ...stats[timeKey],
        bookings: {
          ...stats[timeKey].bookings,
          target: targets.bookings
        },
        activeMembers: {
          ...stats[timeKey].activeMembers,
          target: targets.activeMembers
        },
        revenue: {
          ...stats[timeKey].revenue,
          target: `¥${targets.revenue.toLocaleString()}`
        },
        renewalRate: {
          ...stats[timeKey].renewalRate,
          target: `${targets.renewalRate}%`
        }
      }
    }

    // 更新状态
    setStats(newStats)

    // 显示成功提示
    toast.success(`${period === "daily" ? "每日" : period === "weekly" ? "每周" : "每月"}目标设置成功！`)
  }

  // 定义统计卡片数据
  const statCards = [
    {
      title: "预约数量",
      description: `总计${currentStats.bookings.value}人次`,
      value: currentStats.bookings.value,
      change: currentStats.bookings.change,
      isPositive: currentStats.bookings.isPositive,
      target: currentStats.bookings.target,
      progress: currentStats.bookings.progress,
      color: currentStats.bookings.color,
      icon: <Calendar className="h-5 w-5 text-white" />,
      href: "/booking-records"
    },
    {
      title: "活跃会员",
      description: `${currentStats.activeMembers.value}位会员`,
      value: currentStats.activeMembers.value,
      change: currentStats.activeMembers.change,
      isPositive: currentStats.activeMembers.isPositive,
      target: currentStats.activeMembers.target,
      progress: currentStats.activeMembers.progress,
      color: currentStats.activeMembers.color,
      icon: <Users className="h-5 w-5 text-white" />,
      href: "/members"
    },
    {
      title: "营业收入",
      description: `总收入${currentStats.revenue.value}`,
      value: currentStats.revenue.value,
      change: currentStats.revenue.change,
      isPositive: currentStats.revenue.isPositive,
      target: currentStats.revenue.target,
      progress: currentStats.revenue.progress,
      color: currentStats.revenue.color,
      icon: <CreditCard className="h-5 w-5 text-white" />,
      href: "/statistics/revenue"
    },
    {
      title: "续费率",
      description: `会员续费率${currentStats.renewalRate.value}`,
      value: currentStats.renewalRate.value,
      change: currentStats.renewalRate.change,
      isPositive: currentStats.renewalRate.isPositive,
      target: currentStats.renewalRate.target,
      progress: currentStats.renewalRate.progress,
      color: currentStats.renewalRate.color,
      icon: <Percent className="h-5 w-5 text-white" />,
      href: "/members/renewal"
    },
  ]

  return (
    <Card className="border shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium flex items-center">
            <BarChart className="mr-2 h-5 w-5 text-primary" />
            业务数据概览
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className="flex items-center bg-muted rounded-md p-1 h-8">
              <button
                className={`text-xs px-3 rounded-sm h-6 flex items-center justify-center ${timeRange === "today" ? "bg-background shadow-sm" : ""}`}
                onClick={() => setTimeRange("today")}
              >
                今日
              </button>
              <button
                className={`text-xs px-3 rounded-sm h-6 flex items-center justify-center ${timeRange === "week" ? "bg-background shadow-sm" : ""}`}
                onClick={() => setTimeRange("week")}
              >
                本周
              </button>
              <button
                className={`text-xs px-3 rounded-sm h-6 flex items-center justify-center ${timeRange === "month" ? "bg-background shadow-sm" : ""}`}
                onClick={() => setTimeRange("month")}
              >
                本月
              </button>
            </div>
            <div className="flex items-center gap-1">
              <SetTargetDialog
                currentTargets={{
                  bookings: currentStats.bookings,
                  activeMembers: currentStats.activeMembers,
                  revenue: currentStats.revenue,
                  renewalRate: currentStats.renewalRate
                }}
                onSaveTargets={handleSaveTargets}
              />
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => router.push('/statistics/dashboard')}
              >
                <BarChart2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {statCards.map((stat, index) => (
            <div
              key={index}
              className="flex flex-col p-4 rounded-lg border hover:shadow-sm transition-all cursor-pointer"
              onClick={() => router.push(stat.href)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className={`p-2 rounded-full ${stat.color}`}>
                  {stat.icon}
                </div>
                <Badge variant="outline" className="text-[10px] h-5 px-2">
                  目标: {stat.target}
                </Badge>
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm">{stat.title}</div>
                <div className="text-2xl font-bold mt-1">{stat.value}</div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-muted-foreground">
                    <span className={stat.isPositive ? "text-green-500 inline-flex items-center" : "text-red-500 inline-flex items-center"}>
                      {stat.isPositive ?
                        <ArrowUpRight className="h-3 w-3 mr-1" /> :
                        <ArrowDownRight className="h-3 w-3 mr-1" />
                      }
                      {stat.change}%
                    </span>
                    &nbsp;较{timeRange === "today" ? "昨日" : timeRange === "week" ? "上周" : "上月"}
                  </p>
                  <Button variant="ghost" size="icon" className="h-6 w-6 -mr-2">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
                <Progress value={stat.progress} className="h-1 mt-2" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
