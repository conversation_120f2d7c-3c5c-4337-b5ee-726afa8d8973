import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

interface CourseType {
  id: number
  name: string
  selected: boolean
  reminderMinutes?: number // 提前提醒分钟数
}

interface MessageRule {
  id: number
  name: string
  template: string
  smsEnabled: boolean
  appEnabled: boolean
  sendRule?: string
  courseTypes?: number[] // 支持的课程类型ID列表
  showRuleButton?: boolean // 是否显示设置规则按钮
  courseReminderSettings?: {[key: number]: number} // 课程类型ID -> 提前提醒分钟数
}

interface CourseSelectionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  rule: MessageRule | null
  onSave: (rule: MessageRule, selectedCourseTypes: number[], reminderSettings: {[key: number]: number}) => void
}

// 示例课程类型数据
const courseTypes: CourseType[] = [
  { id: 1, name: "团体课", selected: false },
  { id: 2, name: "私教课", selected: false },
  { id: 3, name: "小班课", selected: false },
  { id: 4, name: "精品课", selected: false },
  { id: 5, name: "教练培训课", selected: false },
  { id: 6, name: "体验课", selected: false },
  { id: 7, name: "特色课", selected: false },
  { id: 8, name: "季卡课", selected: false },
]

export function CourseSelectionDialog({
  open,
  onOpenChange,
  rule,
  onSave
}: CourseSelectionDialogProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCourseTypes, setSelectedCourseTypes] = useState<CourseType[]>([])
  const [showReminderSettings, setShowReminderSettings] = useState(false)

  // 当对话框打开时，初始化选中的课程类型和提醒设置
  React.useEffect(() => {
    if (open && rule) {
      // 检查是否是开课前提醒规则
      const isReminderRule = rule.name.includes("开课前") || rule.name.includes("开始前")
      setShowReminderSettings(isReminderRule)

      const initialSelected = courseTypes.map(course => {
        const isSelected = rule.courseTypes ? rule.courseTypes.includes(course.id) : false
        const reminderMinutes = rule.courseReminderSettings && rule.courseReminderSettings[course.id]
          ? rule.courseReminderSettings[course.id]
          : 120 // 默认120分钟

        return {
          ...course,
          selected: isSelected,
          reminderMinutes
        }
      })

      setSelectedCourseTypes(initialSelected)
    }
  }, [open, rule])

  // 处理课程类型选择状态变化
  const handleCourseTypeChange = (id: number, checked: boolean) => {
    setSelectedCourseTypes(prev =>
      prev.map(course =>
        course.id === id ? { ...course, selected: checked } : course
      )
    )
  }

  // 处理提醒时间变化
  const handleReminderMinutesChange = (id: number, minutes: number) => {
    setSelectedCourseTypes(prev =>
      prev.map(course =>
        course.id === id ? { ...course, reminderMinutes: minutes } : course
      )
    )
  }

  // 处理保存按钮点击
  const handleSave = () => {
    if (rule) {
      const selectedIds = selectedCourseTypes
        .filter(course => course.selected)
        .map(course => course.id)

      // 构建提醒设置对象
      const reminderSettings: {[key: number]: number} = {}
      selectedCourseTypes.forEach(course => {
        if (course.selected && course.reminderMinutes) {
          reminderSettings[course.id] = course.reminderMinutes
        }
      })

      onSave(rule, selectedIds, reminderSettings)
      onOpenChange(false)
    }
  }

  // 过滤课程类型
  const filteredCourseTypes = selectedCourseTypes.filter(course =>
    course.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>设置发送规则</DialogTitle>
          <DialogDescription>
            {rule ? `选择支持"${rule.name}"的课程类型` : "选择支持该消息类型的课程类型"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 my-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索课程类型..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="border rounded-md p-3 max-h-60 overflow-y-auto">
            {filteredCourseTypes.length > 0 ? (
              <div className="space-y-3">
                {filteredCourseTypes.map((course) => (
                  <div key={course.id} className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`course-${course.id}`}
                        checked={course.selected}
                        onCheckedChange={(checked) =>
                          handleCourseTypeChange(course.id, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`course-${course.id}`}
                        className="text-sm font-medium cursor-pointer"
                      >
                        {course.name}
                      </Label>
                    </div>

                    {showReminderSettings && course.selected && (
                      <div className="flex items-center pl-6 space-x-2">
                        <Label className="text-xs text-gray-500">提前</Label>
                        <Input
                          type="number"
                          value={course.reminderMinutes}
                          onChange={(e) => handleReminderMinutesChange(
                            course.id,
                            parseInt(e.target.value) || 120
                          )}
                          className="w-16 h-7 text-xs text-center"
                          min={1}
                        />
                        <Label className="text-xs text-gray-500">分钟提醒</Label>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500">
                未找到匹配的课程类型
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>取消</Button>
          <Button onClick={handleSave}>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
