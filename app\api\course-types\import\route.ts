import { NextRequest, NextResponse } from 'next/server';
import { courseTypeService } from '@/services/course-type-service';

export async function POST(request: NextRequest) {
  try {
    // 在实际项目中，这里应该解析FormData中的文件
    // 为了模拟演示，我们直接从请求体获取数据
    const data = await request.json();
    
    if (!Array.isArray(data) && !data.types) {
      return NextResponse.json(
        { error: '无效的数据格式' },
        { status: 400 }
      );
    }
    
    // 使用data.types如果存在，否则使用data本身
    const typesToImport = Array.isArray(data) ? data : data.types;
    
    // 导入每个类型
    const importedTypes = typesToImport.map((typeData: any) => {
      return courseTypeService.create(typeData);
    });
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: {
        imported: importedTypes.length,
        types: importedTypes
      }
    });
  } catch (error) {
    console.error('导入课程类型错误:', error);
    return NextResponse.json(
      { error: '导入失败', details: String(error) },
      { status: 500 }
    );
  }
} 