"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Eye, FileText } from "lucide-react"

interface ContractTemplatePreviewProps {
  previewUrl: string | null
  templateName: string
  onPreview: () => void
}

export function ContractTemplatePreview({
  previewUrl,
  templateName,
  onPreview
}: ContractTemplatePreviewProps) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)

  useEffect(() => {
    if (previewUrl) {
      setLoading(true)
      setError(false)

      // 检查URL是否有效
      fetch(previewUrl, { method: 'HEAD' })
        .then(response => {
          if (!response.ok) {
            setError(true)
          }
          // 即使URL有效，也设置一个短暂的加载时间，以便用户看到加载状态
          setTimeout(() => {
            setLoading(false)
          }, 800)
        })
        .catch(() => {
          setError(true)
          setLoading(false)
        })
    }
  }, [previewUrl])

  if (!previewUrl) {
    return (
      <div className="flex items-center justify-center h-[400px] bg-muted rounded-lg">
        <div className="text-center">
          <FileText className="h-16 w-16 mx-auto text-muted-foreground/50" />
          <p className="mt-4 text-muted-foreground">模板预览暂不可用</p>
          <Button variant="outline" className="mt-2" onClick={onPreview}>
            <Eye className="mr-2 h-4 w-4" />
            查看模板
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col space-y-4">
      {loading ? (
        <div className="flex items-center justify-center h-[400px] bg-white rounded-lg border">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">正在加载模板预览...</p>
          </div>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center h-[400px] bg-muted rounded-lg">
          <div className="text-center">
            <FileText className="h-16 w-16 mx-auto text-destructive/50" />
            <p className="mt-4 text-muted-foreground">模板预览加载失败</p>
            <Button variant="outline" className="mt-2" onClick={onPreview}>
              <Eye className="mr-2 h-4 w-4" />
              在新窗口打开
            </Button>
          </div>
        </div>
      ) : (
        <>
          <div className="h-[400px] bg-white rounded-lg border overflow-hidden">
            <iframe
              src={previewUrl}
              className="w-full h-full"
              title={`${templateName} 预览`}
              onError={() => setError(true)}
              sandbox="allow-same-origin allow-scripts"
              loading="lazy"
            />
          </div>
          <div className="flex justify-end">
            <Button variant="outline" onClick={onPreview}>
              <Eye className="mr-2 h-4 w-4" />
              在新窗口打开
            </Button>
          </div>
        </>
      )}
    </div>
  )
}
