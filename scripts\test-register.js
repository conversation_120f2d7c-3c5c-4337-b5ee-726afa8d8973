// 测试注册API的脚本
// 使用Node.js 18+的内置fetch API

async function testRegister() {
  try {
    console.log('🧪 开始测试注册API...');
    
    const testData = {
      companyName: "测试瑜伽馆",
      businessLicense: "123456789012345678",
      username: "测试管理员",
      email: "<EMAIL>",
      phone: "***********",
      password: "Test123456",
      confirmPassword: "Test123456",
      agreeTerms: true
    };
    
    console.log('📤 发送注册请求...');
    console.log('请求数据:', JSON.stringify(testData, null, 2));
    
    const response = await fetch('http://localhost:3001/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });
    
    console.log('📥 响应状态:', response.status);
    
    const result = await response.json();
    console.log('📥 响应数据:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('✅ 注册测试成功!');
      console.log(`租户ID: ${result.tenantId}`);
      console.log(`门店ID: ${result.storeId}`);
      console.log(`员工ID: ${result.employeeId}`);
    } else {
      console.log('❌ 注册测试失败:', result.error);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testRegister();
