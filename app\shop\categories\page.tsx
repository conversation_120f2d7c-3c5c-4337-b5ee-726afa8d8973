"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Plus, MoreHorizontal, Edit, Trash2, Eye, ArrowUp, ArrowDown, Folder, FolderPlus } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"

// 模拟分类数据
const categories = [
  {
    id: "1",
    name: "瑜伽装备",
    description: "各类瑜伽练习所需的装备和工具",
    productCount: 28,
    status: "active",
    parentId: null,
    level: 1,
    sort: 1,
    showInMenu: true,
    showInHome: true,
    icon: "🧘",
    createdAt: "2023-05-10",
  },
  {
    id: "2",
    name: "瑜伽垫",
    description: "各种材质和厚度的瑜伽垫",
    productCount: 12,
    status: "active",
    parentId: "1",
    level: 2,
    sort: 1,
    showInMenu: true,
    showInHome: false,
    icon: "🧠",
    createdAt: "2023-05-11",
  },
  {
    id: "3",
    name: "瑜伽砖",
    description: "辅助瑜伽练习的瑜伽砖",
    productCount: 8,
    status: "active",
    parentId: "1",
    level: 2,
    sort: 2,
    showInMenu: true,
    showInHome: false,
    icon: "🧱",
    createdAt: "2023-05-12",
  },
  {
    id: "4",
    name: "瑜伽服饰",
    description: "舒适透气的瑜伽服装",
    productCount: 35,
    status: "active",
    parentId: null,
    level: 1,
    sort: 2,
    showInMenu: true,
    showInHome: true,
    icon: "👚",
    createdAt: "2023-05-15",
  },
  {
    id: "5",
    name: "女款瑜伽服",
    description: "女性专用瑜伽服装",
    productCount: 25,
    status: "active",
    parentId: "4",
    level: 2,
    sort: 1,
    showInMenu: true,
    showInHome: true,
    icon: "👗",
    createdAt: "2023-05-16",
  },
  {
    id: "6",
    name: "男款瑜伽服",
    description: "男性专用瑜伽服装",
    productCount: 10,
    status: "active",
    parentId: "4",
    level: 2,
    sort: 2,
    showInMenu: true,
    showInHome: false,
    icon: "👕",
    createdAt: "2023-05-17",
  },
  {
    id: "7",
    name: "会员卡",
    description: "各类会员卡和身份卡",
    productCount: 5,
    status: "active",
    parentId: null,
    level: 1,
    sort: 3,
    showInMenu: true,
    showInHome: true,
    icon: "💳",
    createdAt: "2023-06-01",
  },
  {
    id: "8",
    name: "在线课程",
    description: "各类瑜伽在线视频课程",
    productCount: 15,
    status: "active",
    parentId: null,
    level: 1,
    sort: 4,
    showInMenu: true,
    showInHome: true,
    icon: "🎬",
    createdAt: "2023-06-10",
  },
  {
    id: "9",
    name: "入门课程",
    description: "适合初学者的瑜伽课程",
    productCount: 8,
    status: "active",
    parentId: "8",
    level: 2,
    sort: 1,
    showInMenu: true,
    showInHome: true,
    icon: "🔰",
    createdAt: "2023-06-11",
  },
  {
    id: "10",
    name: "进阶课程",
    description: "适合有一定基础的瑜伽练习者",
    productCount: 7,
    status: "active",
    parentId: "8",
    level: 2,
    sort: 2,
    showInMenu: true,
    showInHome: false,
    icon: "🏆",
    createdAt: "2023-06-12",
  },
]

export default function ShopCategoriesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [showAll, setShowAll] = useState(false)

  // 过滤分类
  const filteredCategories = categories.filter(
    (category) =>
      category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // 获取顶级分类
  const topLevelCategories = filteredCategories.filter(category => category.parentId === null)

  // 获取子分类
  const getChildCategories = (parentId) => {
    return filteredCategories.filter(category => category.parentId === parentId)
  }

  // 渲染分类行
  const renderCategoryRow = (category, isChild = false) => (
    <TableRow key={category.id}>
      <TableCell className="font-medium">
        <div className="flex items-center">
          {isChild && <div className="w-6 border-l-2 border-b-2 h-6 mr-2"></div>}
          <div className="mr-2">{category.icon}</div>
          <span>{category.name}</span>
        </div>
      </TableCell>
      <TableCell>{category.description}</TableCell>
      <TableCell>{category.productCount}</TableCell>
      <TableCell>
        <Badge variant={category.status === "active" ? "default" : "secondary"}>
          {category.status === "active" ? "启用" : "禁用"}
        </Badge>
      </TableCell>
      <TableCell className="text-center">{category.sort}</TableCell>
      <TableCell className="text-center">
        <Switch checked={category.showInMenu} disabled />
      </TableCell>
      <TableCell className="text-center">
        <Switch checked={category.showInHome} disabled />
      </TableCell>
      <TableCell>{category.createdAt}</TableCell>
      <TableCell className="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>操作</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Edit className="mr-2 h-4 w-4" />
              编辑分类
            </DropdownMenuItem>
            {category.level === 1 && (
              <DropdownMenuItem>
                <FolderPlus className="mr-2 h-4 w-4" />
                添加子分类
              </DropdownMenuItem>
            )}
            <DropdownMenuItem>
              <ArrowUp className="mr-2 h-4 w-4" />
              上移
            </DropdownMenuItem>
            <DropdownMenuItem>
              <ArrowDown className="mr-2 h-4 w-4" />
              下移
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-red-600">
              <Trash2 className="mr-2 h-4 w-4" />
              删除分类
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">商品分类</h1>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          添加分类
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center gap-2 max-w-md">
          <Input
            placeholder="搜索分类名称或描述..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center space-x-2">
            <Switch
              id="show-all"
              checked={showAll}
              onCheckedChange={setShowAll}
            />
            <label htmlFor="show-all" className="text-sm">显示为平铺列表</label>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>分类列表</CardTitle>
          <CardDescription>管理商城商品分类</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>分类名称</TableHead>
                <TableHead>描述</TableHead>
                <TableHead>商品数量</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-center">排序</TableHead>
                <TableHead className="text-center">菜单显示</TableHead>
                <TableHead className="text-center">首页显示</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {showAll ? (
                // 平铺显示所有分类
                filteredCategories.map(category => renderCategoryRow(category))
              ) : (
                // 层级显示分类
                <>
                  {topLevelCategories.map(category => (
                    <React.Fragment key={category.id}>
                      {renderCategoryRow(category)}
                      {getChildCategories(category.id).map(childCategory => (
                        renderCategoryRow(childCategory, true)
                      ))}
                    </React.Fragment>
                  ))}
                </>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
