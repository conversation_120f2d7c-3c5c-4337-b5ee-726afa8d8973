"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import {
  Book<PERSON><PERSON>,
  CreditCard,
  FileText,
  Package,
  Tag,
  Clock,
  Calendar,
  User,
  UserPlus,
  CheckCircle,
  AlertCircle,
  Upload,
  Plus,
  Trash2
} from "lucide-react"

interface VirtualProductFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  product?: any; // 编辑时传入的商品数据
  onSave: (data: any) => void;
}

export function VirtualProductFormDialog({ 
  open, 
  onOpenChange, 
  product, 
  onSave 
}: VirtualProductFormDialogProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  
  // 表单数据
  const [formData, setFormData] = useState({
    id: "",
    name: "",
    description: "",
    price: "",
    originalPrice: "",
    category: "",
    type: "membership", // 默认为会员卡
    status: "active",
    validityPeriod: "365", // 默认365天
    validityType: "days", // days 或 forever
    autoActivate: true,
    verificationMethod: "二维码",
    usageInstructions: "",
    refundPolicy: "",
    thumbnail: "",
    // 会员卡特有字段
    benefits: [""],
    // 课程特有字段
    courseDetails: {
      lessons: "",
      duration: "",
      level: "beginner",
      format: "video",
      instructor: ""
    },
    // 身份卡特有字段
    identityPermissions: [""]
  })

  // 初始化表单数据
  useEffect(() => {
    if (product) {
      setFormData({
        ...formData,
        ...product,
        validityType: product.validityPeriod === "forever" ? "forever" : "days",
        validityPeriod: product.validityPeriod === "forever" ? "" : product.validityPeriod
      })
    }
  }, [product])

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // 处理嵌套对象的输入变化
  const handleNestedInputChange = (objectName: string, fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [objectName]: {
        ...prev[objectName],
        [fieldName]: value
      }
    }))
  }

  // 处理数组项的变化
  const handleArrayItemChange = (arrayName: string, index: number, value: string) => {
    setFormData(prev => {
      const newArray = [...prev[arrayName]]
      newArray[index] = value
      return {
        ...prev,
        [arrayName]: newArray
      }
    })
  }

  // 添加数组项
  const handleAddArrayItem = (arrayName: string) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: [...prev[arrayName], ""]
    }))
  }

  // 删除数组项
  const handleRemoveArrayItem = (arrayName: string, index: number) => {
    setFormData(prev => {
      const newArray = [...prev[arrayName]]
      newArray.splice(index, 1)
      return {
        ...prev,
        [arrayName]: newArray
      }
    })
  }

  // 处理开关变化
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }))
  }

  // 处理选择变化
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // 处理有效期类型变化
  const handleValidityTypeChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      validityType: value,
      validityPeriod: value === "forever" ? "forever" : prev.validityPeriod
    }))
  }

  // 提交表单
  const handleSubmit = async () => {
    try {
      setLoading(true)
      
      // 验证必填字段
      if (!formData.name || !formData.price || !formData.type) {
        toast({
          title: "表单验证失败",
          description: "请填写所有必填字段",
          variant: "destructive"
        })
        setLoading(false)
        return
      }
      
      // 处理有效期
      const finalFormData = {
        ...formData,
        validityPeriod: formData.validityType === "forever" ? "forever" : formData.validityPeriod
      }
      
      // 删除中间状态字段
      delete finalFormData.validityType
      
      // 调用保存回调
      await onSave(finalFormData)
      
      toast({
        title: product ? "更新成功" : "创建成功",
        description: product ? "虚拟商品已更新" : "新虚拟商品已创建",
      })
      
      onOpenChange(false)
    } catch (error) {
      console.error("保存失败:", error)
      toast({
        title: "保存失败",
        description: "请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{product ? "编辑虚拟商品" : "创建虚拟商品"}</DialogTitle>
          <DialogDescription>
            {product ? "修改虚拟商品信息" : "创建新的虚拟商品"}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="details">详细设置</TabsTrigger>
            <TabsTrigger value="content">内容管理</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">商品名称 <span className="text-red-500">*</span></Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="输入商品名称"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">商品类型 <span className="text-red-500">*</span></Label>
                  <Select 
                    value={formData.type} 
                    onValueChange={(value) => handleSelectChange("type", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择商品类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="membership">会员卡</SelectItem>
                      <SelectItem value="course">在线课程</SelectItem>
                      <SelectItem value="identity">身份卡</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">商品分类</Label>
                  <Input
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    placeholder="输入商品分类"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">销售价格 <span className="text-red-500">*</span></Label>
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      value={formData.price}
                      onChange={handleInputChange}
                      placeholder="0.00"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="originalPrice">原价</Label>
                    <Input
                      id="originalPrice"
                      name="originalPrice"
                      type="number"
                      value={formData.originalPrice}
                      onChange={handleInputChange}
                      placeholder="0.00"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="description">商品描述</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="输入商品描述"
                    className="min-h-[120px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="thumbnail">商品图片</Label>
                  <div className="border border-dashed rounded-md p-4 text-center">
                    {formData.thumbnail ? (
                      <div className="relative">
                        <img 
                          src={formData.thumbnail} 
                          alt="商品图片" 
                          className="max-h-[150px] mx-auto rounded-md"
                        />
                        <Button 
                          variant="destructive" 
                          size="icon" 
                          className="absolute top-2 right-2"
                          onClick={() => handleSelectChange("thumbnail", "")}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="py-4">
                        <Upload className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground mb-2">点击或拖拽上传图片</p>
                        <Button variant="outline" size="sm">
                          选择图片
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>商品状态</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={formData.status === "active"}
                      onCheckedChange={(checked) => 
                        handleSelectChange("status", checked ? "active" : "inactive")
                      }
                    />
                    <Label>
                      {formData.status === "active" ? "上架" : "下架"}
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="details" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">有效期设置</CardTitle>
                <CardDescription>设置虚拟商品的有效期和激活方式</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>有效期类型</Label>
                  <RadioGroup 
                    value={formData.validityType} 
                    onValueChange={handleValidityTypeChange}
                    className="flex flex-col space-y-1"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="days" id="validity-days" />
                      <Label htmlFor="validity-days">固定天数</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="forever" id="validity-forever" />
                      <Label htmlFor="validity-forever">永久有效</Label>
                    </div>
                  </RadioGroup>
                </div>

                {formData.validityType === "days" && (
                  <div className="space-y-2">
                    <Label htmlFor="validityPeriod">有效天数</Label>
                    <Input
                      id="validityPeriod"
                      name="validityPeriod"
                      type="number"
                      value={formData.validityPeriod}
                      onChange={handleInputChange}
                      placeholder="例如：365"
                    />
                    <p className="text-xs text-muted-foreground">从激活日开始计算的有效天数</p>
                  </div>
                )}

                <div className="space-y-2 pt-2">
                  <Label>激活方式</Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={formData.autoActivate}
                      onCheckedChange={(checked) => 
                        handleSwitchChange("autoActivate", checked)
                      }
                    />
                    <Label>
                      {formData.autoActivate ? "购买后自动激活" : "需要手动激活"}
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {formData.autoActivate 
                      ? "用户购买后商品将自动激活，有效期立即开始计算" 
                      : "用户购买后需要手动激活商品，有效期从激活时开始计算"}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">使用设置</CardTitle>
                <CardDescription>设置虚拟商品的核销方式和使用说明</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="verificationMethod">核销方式</Label>
                  <Select 
                    value={formData.verificationMethod} 
                    onValueChange={(value) => handleSelectChange("verificationMethod", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择核销方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="二维码">二维码核销</SelectItem>
                      <SelectItem value="验证码">验证码核销</SelectItem>
                      <SelectItem value="账号授权">账号授权</SelectItem>
                      <SelectItem value="人工核销">人工核销</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="usageInstructions">使用说明</Label>
                  <Textarea
                    id="usageInstructions"
                    name="usageInstructions"
                    value={formData.usageInstructions}
                    onChange={handleInputChange}
                    placeholder="输入商品使用说明"
                    className="min-h-[80px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="refundPolicy">退款政策</Label>
                  <Textarea
                    id="refundPolicy"
                    name="refundPolicy"
                    value={formData.refundPolicy}
                    onChange={handleInputChange}
                    placeholder="输入退款政策"
                    className="min-h-[80px]"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="content" className="space-y-4 mt-4">
            {/* 根据商品类型显示不同的内容管理界面 */}
            {formData.type === "membership" && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">会员卡权益</CardTitle>
                  <CardDescription>设置会员卡包含的权益</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {formData.benefits.map((benefit, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Input
                          value={benefit}
                          onChange={(e) => handleArrayItemChange("benefits", index, e.target.value)}
                          placeholder={`权益 ${index + 1}`}
                        />
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleRemoveArrayItem("benefits", index)}
                          disabled={formData.benefits.length <= 1}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    
                    <Button 
                      variant="outline" 
                      onClick={() => handleAddArrayItem("benefits")}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      添加权益
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {formData.type === "course" && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">课程详情</CardTitle>
                  <CardDescription>设置课程的基本信息</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="lessons">课程数量</Label>
                        <Input
                          id="lessons"
                          type="number"
                          value={formData.courseDetails.lessons}
                          onChange={(e) => handleNestedInputChange("courseDetails", "lessons", e.target.value)}
                          placeholder="例如：10"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="duration">总时长</Label>
                        <Input
                          id="duration"
                          value={formData.courseDetails.duration}
                          onChange={(e) => handleNestedInputChange("courseDetails", "duration", e.target.value)}
                          placeholder="例如：5小时30分钟"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="level">难度等级</Label>
                        <Select 
                          value={formData.courseDetails.level} 
                          onValueChange={(value) => handleNestedInputChange("courseDetails", "level", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择难度等级" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="beginner">入门</SelectItem>
                            <SelectItem value="intermediate">中级</SelectItem>
                            <SelectItem value="advanced">高级</SelectItem>
                            <SelectItem value="all">全部级别</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="format">课程形式</Label>
                        <Select 
                          value={formData.courseDetails.format} 
                          onValueChange={(value) => handleNestedInputChange("courseDetails", "format", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择课程形式" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="video">视频</SelectItem>
                            <SelectItem value="audio">音频</SelectItem>
                            <SelectItem value="text">图文</SelectItem>
                            <SelectItem value="mixed">混合形式</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="instructor">讲师</Label>
                      <Input
                        id="instructor"
                        value={formData.courseDetails.instructor}
                        onChange={(e) => handleNestedInputChange("courseDetails", "instructor", e.target.value)}
                        placeholder="输入讲师姓名"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {formData.type === "identity" && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">身份权限</CardTitle>
                  <CardDescription>设置身份卡包含的权限</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {formData.identityPermissions.map((permission, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Input
                          value={permission}
                          onChange={(e) => handleArrayItemChange("identityPermissions", index, e.target.value)}
                          placeholder={`权限 ${index + 1}`}
                        />
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleRemoveArrayItem("identityPermissions", index)}
                          disabled={formData.identityPermissions.length <= 1}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    
                    <Button 
                      variant="outline" 
                      onClick={() => handleAddArrayItem("identityPermissions")}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      添加权限
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? "保存中..." : (product ? "更新商品" : "创建商品")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
