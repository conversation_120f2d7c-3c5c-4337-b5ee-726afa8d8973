"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Search, Plus, MoreHorizontal, Edit, Trash2, Eye, Truck, Package, MapPin, Settings, FileText, AlertCircle, CheckCircle2, RefreshCcw, Download } from "lucide-react"
import { LogisticsDialog } from "./logistics-dialog"
import { CompanyDialog } from "./company-dialog"
import { PickupDialog } from "./pickup-dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// 模拟物流公司数据
const logisticsCompanies = [
  {
    id: "1",
    name: "顺丰速运",
    code: "SF",
    trackingUrl: "https://www.sf-express.com/cn/sc/dynamic_function/waybill/#search/bill-number/",
    status: "active",
    defaultCost: "20.00",
    freeThreshold: "199.00",
    priority: 1,
  },
  {
    id: "2",
    name: "圆通速递",
    code: "YTO",
    trackingUrl: "https://www.yto.net.cn/gw/index/index.html",
    status: "active",
    defaultCost: "12.00",
    freeThreshold: "199.00",
    priority: 2,
  },
  {
    id: "3",
    name: "中通快递",
    code: "ZTO",
    trackingUrl: "https://www.zto.com/express/expressCheck.html",
    status: "active",
    defaultCost: "12.00",
    freeThreshold: "199.00",
    priority: 3,
  },
  {
    id: "4",
    name: "韵达速递",
    code: "YD",
    trackingUrl: "https://www.yundaex.com/cn/index.php",
    status: "inactive",
    defaultCost: "12.00",
    freeThreshold: "199.00",
    priority: 4,
  },
  {
    id: "5",
    name: "申通快递",
    code: "STO",
    trackingUrl: "https://www.sto.cn/query/",
    status: "active",
    defaultCost: "12.00",
    freeThreshold: "199.00",
    priority: 5,
  },
]

// 模拟发货记录数据
const shipmentRecords = [
  {
    id: "SH-20230601-001",
    orderId: "ORD-20230501-001",
    customerName: "张三",
    trackingNumber: "SF1234567890",
    logisticsCompany: "顺丰速运",
    shipDate: "2023-06-01 10:30:25",
    status: "delivered", // 已送达
    address: "上海市浦东新区XX路XX号",
    products: ["瑜伽垫 - 专业防滑"],
    remark: "",
  },
  {
    id: "SH-20230615-002",
    orderId: "ORD-20230515-003",
    customerName: "王五",
    trackingNumber: "YT9876543210",
    logisticsCompany: "圆通速递",
    shipDate: "2023-06-15 14:45:12",
    status: "shipping", // 配送中
    address: "北京市朝阳区XX街XX号",
    products: ["瑜伽服套装 - 女款"],
    remark: "尺码: M",
  },
  {
    id: "SH-20230620-003",
    orderId: "ORD-20230620-007",
    customerName: "赵七",
    trackingNumber: "ZT5678901234",
    logisticsCompany: "中通快递",
    shipDate: "2023-06-20 09:15:30",
    status: "shipping", // 配送中
    address: "广州市天河区XX路XX号",
    products: ["瑜伽垫 - 专业防滑", "瑜伽砖 - 高密度泡沫"],
    remark: "",
  },
  {
    id: "SH-20230625-004",
    orderId: "ORD-20230625-008",
    customerName: "钱八",
    trackingNumber: "ST6789012345",
    logisticsCompany: "申通快递",
    shipDate: "2023-06-25 16:20:45",
    status: "delivered", // 已送达
    address: "深圳市南山区XX街XX号",
    products: ["瑜伽服套装 - 男款"],
    remark: "尺码: L",
  },
]

// 模拟自提点数据
const pickupLocations = [
  {
    id: "1",
    name: "总店自提点",
    address: "上海市静安区XX路XX号瑜伽馆",
    contactPerson: "李店长",
    contactPhone: "***********",
    businessHours: "09:00-21:00",
    status: "active",
  },
  {
    id: "2",
    name: "分店一自提点",
    address: "上海市浦东新区XX路XX号瑜伽馆",
    contactPerson: "王店长",
    contactPhone: "***********",
    businessHours: "10:00-22:00",
    status: "active",
  },
  {
    id: "3",
    name: "分店二自提点",
    address: "上海市徐汇区XX路XX号瑜伽馆",
    contactPerson: "张店长",
    contactPhone: "***********",
    businessHours: "09:30-21:30",
    status: "active",
  },
]

export default function LogisticsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("shipments")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedShipment, setSelectedShipment] = useState<any>(null)
  const [selectedCompany, setSelectedCompany] = useState<any>(null)
  const [selectedPickupLocation, setSelectedPickupLocation] = useState<any>(null)
  const [showLogisticsDialog, setShowLogisticsDialog] = useState(false)
  const [showCompanyDialog, setShowCompanyDialog] = useState(false)
  const [showPickupDialog, setShowPickupDialog] = useState(false)
  const [dialogMode, setDialogMode] = useState<"add" | "edit">("add")
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  // 过滤发货记录
  const filteredShipments = shipmentRecords.filter(
    (shipment) => {
      // 基本搜索过滤
      const basicFilter = (
        shipment.orderId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shipment.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shipment.trackingNumber.toLowerCase().includes(searchQuery.toLowerCase())
      )

      // 状态过滤
      const statusFilterMatch = statusFilter === "all" || shipment.status === statusFilter

      return basicFilter && statusFilterMatch
    }
  )

  // 过滤物流公司
  const filteredCompanies = logisticsCompanies.filter(
    (company) =>
      company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.code.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // 过滤自提点
  const filteredPickupLocations = pickupLocations.filter(
    (location) =>
      location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.contactPerson.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // 获取物流状态标签
  const getStatusBadge = (status) => {
    switch (status) {
      case "delivered":
        return <Badge variant="default" className="bg-green-500">已送达</Badge>
      case "shipping":
        return <Badge variant="default" className="bg-blue-500">配送中</Badge>
      case "pending":
        return <Badge variant="default" className="bg-yellow-500">待发货</Badge>
      case "returned":
        return <Badge variant="default" className="bg-red-500">已退回</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 处理查看物流详情
  const handleViewLogistics = (shipment: any) => {
    setSelectedShipment(shipment)
    setShowLogisticsDialog(true)
  }

  // 处理添加/编辑物流公司
  const handleCompanyAction = (company: any = null, mode: "add" | "edit" = "add") => {
    setSelectedCompany(company)
    setDialogMode(mode)
    setShowCompanyDialog(true)
  }

  // 处理添加/编辑自提点
  const handlePickupAction = (location: any = null, mode: "add" | "edit" = "add") => {
    setSelectedPickupLocation(location)
    setDialogMode(mode)
    setShowPickupDialog(true)
  }

  // 处理选择项目
  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id])
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id))
    }
  }

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      if (activeTab === "shipments") {
        setSelectedItems(filteredShipments.map(item => item.id))
      } else if (activeTab === "companies") {
        setSelectedItems(filteredCompanies.map(item => item.id))
      } else if (activeTab === "pickup") {
        setSelectedItems(filteredPickupLocations.map(item => item.id))
      }
    } else {
      setSelectedItems([])
    }
  }

  // 批量导出选中记录
  const handleExportSelected = () => {
    console.log("导出选中记录:", selectedItems)
    alert(`已选择 ${selectedItems.length} 条记录进行导出`)
  }

  // 批量更新状态
  const handleBatchUpdateStatus = (status: string) => {
    console.log(`批量更新状态为 ${status}:`, selectedItems)
    alert(`已将 ${selectedItems.length} 条记录状态更新为 ${status}`)
    setSelectedItems([])
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">物流管理</h1>
        <div className="flex gap-2">
          {activeTab === "companies" && (
            <Button onClick={() => handleCompanyAction()}>
              <Plus className="mr-2 h-4 w-4" />
              添加物流公司
            </Button>
          )}
          {activeTab === "pickup" && (
            <Button onClick={() => handlePickupAction()}>
              <Plus className="mr-2 h-4 w-4" />
              添加自提点
            </Button>
          )}
          {activeTab === "shipments" && selectedItems.length > 0 && (
            <>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Settings className="mr-2 h-4 w-4" />
                    批量操作
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleBatchUpdateStatus("delivered")}>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    标记为已送达
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBatchUpdateStatus("shipping")}>
                    <Truck className="mr-2 h-4 w-4" />
                    标记为配送中
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBatchUpdateStatus("exception")}>
                    <AlertCircle className="mr-2 h-4 w-4" />
                    标记为异常
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setSelectedItems([])}>
                    取消选择
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button variant="outline" onClick={handleExportSelected}>
                <Download className="mr-2 h-4 w-4" />
                导出选中 ({selectedItems.length})
              </Button>
            </>
          )}
          {activeTab === "shipments" && selectedItems.length === 0 && (
            <Button variant="outline">
              <FileText className="mr-2 h-4 w-4" />
              导出记录
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 max-w-md">
          <TabsTrigger value="shipments">发货记录</TabsTrigger>
          <TabsTrigger value="companies">物流公司</TabsTrigger>
          <TabsTrigger value="pickup">自提点管理</TabsTrigger>
        </TabsList>

        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mt-6">
          <div className="flex flex-1 items-center gap-2 max-w-md">
            <Input
              placeholder={
                activeTab === "shipments" ? "搜索订单号、客户名称或物流单号..." :
                activeTab === "companies" ? "搜索物流公司名称或代码..." :
                "搜索自提点名称或地址..."
              }
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>
          {activeTab === "shipments" && (
            <div className="flex items-center gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="物流状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有状态</SelectItem>
                  <SelectItem value="pending">待发货</SelectItem>
                  <SelectItem value="shipping">配送中</SelectItem>
                  <SelectItem value="delivered">已送达</SelectItem>
                  <SelectItem value="returned">已退回</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* 发货记录标签页 */}
        <TabsContent value="shipments" className="mt-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>发货记录</CardTitle>
              <CardDescription>管理所有订单的物流发货记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        checked={selectedItems.length > 0 && selectedItems.length === filteredShipments.length}
                        onCheckedChange={handleSelectAll}
                        aria-label="全选"
                      />
                    </TableHead>
                    <TableHead>发货单号</TableHead>
                    <TableHead>订单号</TableHead>
                    <TableHead>客户信息</TableHead>
                    <TableHead>物流信息</TableHead>
                    <TableHead>发货时间</TableHead>
                    <TableHead>物流状态</TableHead>
                    <TableHead>商品信息</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredShipments.map((shipment) => (
                    <TableRow key={shipment.id} className={selectedItems.includes(shipment.id) ? "bg-muted/50" : ""}>
                      <TableCell>
                        <Checkbox
                          checked={selectedItems.includes(shipment.id)}
                          onCheckedChange={(checked) => handleSelectItem(shipment.id, !!checked)}
                          aria-label={`选择 ${shipment.id}`}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{shipment.id}</TableCell>
                      <TableCell>{shipment.orderId}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{shipment.customerName}</span>
                          <span className="text-muted-foreground text-xs line-clamp-1">{shipment.address}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{shipment.logisticsCompany}</span>
                          <span className="text-muted-foreground text-xs">{shipment.trackingNumber}</span>
                        </div>
                      </TableCell>
                      <TableCell>{shipment.shipDate}</TableCell>
                      <TableCell>{getStatusBadge(shipment.status)}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          {shipment.products.map((product, index) => (
                            <span key={index} className={index > 0 ? "text-muted-foreground text-xs" : ""}>
                              {product}
                            </span>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleViewLogistics(shipment)}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleViewLogistics(shipment)}>
                              <Truck className="mr-2 h-4 w-4" />
                              查询物流
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {shipment.status === "shipping" && (
                              <DropdownMenuItem onClick={() => handleBatchUpdateStatus("delivered")}>
                                <CheckCircle2 className="mr-2 h-4 w-4" />
                                标记为已送达
                              </DropdownMenuItem>
                            )}
                            {shipment.status === "pending" && (
                              <DropdownMenuItem onClick={() => handleBatchUpdateStatus("shipping")}>
                                <Truck className="mr-2 h-4 w-4" />
                                标记为配送中
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              打印面单
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 物流公司标签页 */}
        <TabsContent value="companies" className="mt-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>物流公司</CardTitle>
              <CardDescription>管理支持的物流公司信息</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        checked={selectedItems.length > 0 && selectedItems.length === filteredCompanies.length}
                        onCheckedChange={handleSelectAll}
                        aria-label="全选"
                      />
                    </TableHead>
                    <TableHead>公司名称</TableHead>
                    <TableHead>代码</TableHead>
                    <TableHead>默认运费</TableHead>
                    <TableHead>免邮门槛</TableHead>
                    <TableHead>优先级</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCompanies.map((company) => (
                    <TableRow key={company.id} className={selectedItems.includes(company.id) ? "bg-muted/50" : ""}>
                      <TableCell>
                        <Checkbox
                          checked={selectedItems.includes(company.id)}
                          onCheckedChange={(checked) => handleSelectItem(company.id, !!checked)}
                          aria-label={`选择 ${company.name}`}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{company.name}</TableCell>
                      <TableCell>{company.code}</TableCell>
                      <TableCell>¥{company.defaultCost}</TableCell>
                      <TableCell>¥{company.freeThreshold}</TableCell>
                      <TableCell>{company.priority}</TableCell>
                      <TableCell>
                        <Badge variant={company.status === "active" ? "default" : "secondary"}>
                          {company.status === "active" ? "启用" : "禁用"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleCompanyAction(company, "edit")}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑信息
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleCompanyAction(company, "edit")}>
                              <Settings className="mr-2 h-4 w-4" />
                              配置规则
                            </DropdownMenuItem>
                            {company.status === "active" ? (
                              <DropdownMenuItem>
                                <AlertCircle className="mr-2 h-4 w-4" />
                                禁用公司
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem>
                                <CheckCircle2 className="mr-2 h-4 w-4" />
                                启用公司
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除公司
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 自提点管理标签页 */}
        <TabsContent value="pickup" className="mt-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>自提点管理</CardTitle>
              <CardDescription>管理门店自提点信息</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        checked={selectedItems.length > 0 && selectedItems.length === filteredPickupLocations.length}
                        onCheckedChange={handleSelectAll}
                        aria-label="全选"
                      />
                    </TableHead>
                    <TableHead>自提点名称</TableHead>
                    <TableHead>地址</TableHead>
                    <TableHead>联系人</TableHead>
                    <TableHead>联系电话</TableHead>
                    <TableHead>营业时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPickupLocations.map((location) => (
                    <TableRow key={location.id} className={selectedItems.includes(location.id) ? "bg-muted/50" : ""}>
                      <TableCell>
                        <Checkbox
                          checked={selectedItems.includes(location.id)}
                          onCheckedChange={(checked) => handleSelectItem(location.id, !!checked)}
                          aria-label={`选择 ${location.name}`}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{location.name}</TableCell>
                      <TableCell>{location.address}</TableCell>
                      <TableCell>{location.contactPerson}</TableCell>
                      <TableCell>{location.contactPhone}</TableCell>
                      <TableCell>{location.businessHours}</TableCell>
                      <TableCell>
                        <Badge variant={location.status === "active" ? "default" : "secondary"}>
                          {location.status === "active" ? "启用" : "禁用"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handlePickupAction(location, "edit")}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑信息
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <MapPin className="mr-2 h-4 w-4" />
                              查看地图
                            </DropdownMenuItem>
                            {location.status === "active" ? (
                              <DropdownMenuItem>
                                <AlertCircle className="mr-2 h-4 w-4" />
                                禁用自提点
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem>
                                <CheckCircle2 className="mr-2 h-4 w-4" />
                                启用自提点
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除自提点
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 物流详情对话框 */}
      <LogisticsDialog
        open={showLogisticsDialog}
        onOpenChange={setShowLogisticsDialog}
        shipment={selectedShipment}
      />

      {/* 物流公司对话框 */}
      <CompanyDialog
        open={showCompanyDialog}
        onOpenChange={setShowCompanyDialog}
        company={selectedCompany}
        mode={dialogMode}
      />

      {/* 自提点对话框 */}
      <PickupDialog
        open={showPickupDialog}
        onOpenChange={setShowPickupDialog}
        location={selectedPickupLocation}
        mode={dialogMode}
      />
    </div>
  )
}
