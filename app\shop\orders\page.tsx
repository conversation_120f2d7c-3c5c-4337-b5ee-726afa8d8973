"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Search, Download, Eye, MoreHorizontal, Truck, Package, CheckCircle, AlertTriangle, Clock,
  RefreshCw, Filter, SlidersHorizontal, ShoppingBag, CreditCard, FileText, Calendar,
  Send, Printer, X, BarChart3
} from "lucide-react"
// 注意：Yoga 图标在 lucide-react 中不存在，需要使用其他图标或自定义组件
import { ShopOrderDetailDialog } from "@/components/shop/order-detail-dialog"
import { ShopOrderAdvancedFilterDialog } from "@/components/shop/order-advanced-filter-dialog"
import { ShopOrderBatchOperationsDialog } from "@/components/shop/order-batch-operations-dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"

// 模拟订单数据
const orders = [
  {
    id: "ORD-20230501-001",
    customerName: "张三",
    customerPhone: "13800138001",
    orderDate: "2023-05-01 14:30:25",
    totalAmount: "199.00",
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "logistics", // 物流配送
    deliveryStatus: "delivered", // 已送达
    products: [
      { name: "瑜伽垫 - 专业防滑", quantity: 1, price: "199.00", type: "physical" }
    ],
    address: "上海市浦东新区XX路XX号",
    trackingNumber: "SF1234567890",
    remark: "",
  },
  {
    id: "ORD-20230510-002",
    customerName: "李四",
    customerPhone: "13800138002",
    orderDate: "2023-05-10 10:15:36",
    totalAmount: "3688.00",
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "alipay", // 支付宝
    deliveryMethod: "verification", // 核销
    deliveryStatus: "verified", // 已核销
    products: [
      { name: "高级会员卡 - 年卡", quantity: 1, price: "3688.00", type: "virtual" }
    ],
    address: "",
    trackingNumber: "",
    remark: "会员编号: VIP20230510002",
  },
  {
    id: "ORD-20230515-003",
    customerName: "王五",
    customerPhone: "13800138003",
    orderDate: "2023-05-15 16:45:12",
    totalAmount: "268.00",
    status: "processing", // 处理中
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "logistics", // 物流配送
    deliveryStatus: "shipping", // 配送中
    products: [
      { name: "瑜伽服套装 - 女款", quantity: 1, price: "268.00", type: "physical" }
    ],
    address: "北京市朝阳区XX街XX号",
    trackingNumber: "YT9876543210",
    remark: "尺码: M",
  },
  {
    id: "ORD-20230520-004",
    customerName: "赵六",
    customerPhone: "13800138004",
    orderDate: "2023-05-20 09:30:45",
    totalAmount: "99.00",
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "verification", // 核销
    deliveryStatus: "verified", // 已核销
    products: [
      { name: "瑜伽入门课程 - 在线视频", quantity: 1, price: "99.00", type: "virtual" }
    ],
    address: "",
    trackingNumber: "",
    remark: "课程已发送至用户邮箱",
  },
  {
    id: "ORD-20230525-005",
    customerName: "孙七",
    customerPhone: "13800138005",
    orderDate: "2023-05-25 13:20:18",
    totalAmount: "49.00",
    status: "processing", // 处理中
    paymentStatus: "paid", // 已支付
    paymentMethod: "alipay", // 支付宝
    deliveryMethod: "self_pickup", // 自提
    deliveryStatus: "ready", // 待取货
    products: [
      { name: "瑜伽砖 - 高密度泡沫", quantity: 1, price: "49.00", type: "physical" }
    ],
    address: "自提点: 上海市静安区XX路XX号瑜伽馆",
    trackingNumber: "",
    remark: "自提码: 8765",
  },
  {
    id: "ORD-20230601-006",
    customerName: "周八",
    customerPhone: "13800138006",
    orderDate: "2023-06-01 11:05:33",
    totalAmount: "1288.00",
    status: "pending", // 待处理
    paymentStatus: "unpaid", // 未支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "verification", // 核销
    deliveryStatus: "pending", // 待处理
    products: [
      { name: "高级私教身份 - 月卡", quantity: 1, price: "1288.00", type: "virtual" }
    ],
    address: "",
    trackingNumber: "",
    remark: "等待支付",
  },
]

// 订单统计数据
const orderStatistics = {
  total: 256,
  completed: 198,
  processing: 42,
  pending: 16,
  totalAmount: 128560.00,
  todayOrders: 24,
  todayAmount: 12480.00,
  productOrders: 145,
  courseOrders: 68,
  membershipOrders: 43
}

export default function ShopOrdersPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [deliveryFilter, setDeliveryFilter] = useState("all")
  const [orderTypeFilter, setOrderTypeFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("all")
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [showOrderDetail, setShowOrderDetail] = useState(false)
  const [selectedOrderId, setSelectedOrderId] = useState("")
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false)
  const [showBatchOperations, setShowBatchOperations] = useState(false)

  // 处理查看订单详情
  const handleViewOrderDetail = (orderId: string) => {
    setSelectedOrderId(orderId);
    setShowOrderDetail(true);
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedOrders(filteredOrders.map(order => order.id));
    } else {
      setSelectedOrders([]);
    }
  };

  // 处理批量操作
  const handleBatchOperation = (operation: string) => {
    console.log(`对订单 ${selectedOrders.join(", ")} 执行操作: ${operation}`);
    // 实际应用中，这里会执行相应的批量操作
    alert(`已对 ${selectedOrders.length} 个订单执行 ${operation} 操作`);
    setSelectedOrders([]);
  };

  // 处理应用高级筛选
  const handleApplyFilters = (filters: any) => {
    console.log("应用高级筛选:", filters);
    // 实际应用中，这里会更新筛选状态
    alert("已应用高级筛选条件");
  };

  // 过滤订单
  const filteredOrders = orders.filter((order) => {
    // 基本搜索过滤
    const searchFilter =
      searchQuery === "" ||
      order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customerPhone.includes(searchQuery) ||
      order.products.some(p => p.name.toLowerCase().includes(searchQuery.toLowerCase()));

    // 状态过滤
    const statusFilterMatch = statusFilter === "all" || order.status === statusFilter;

    // 配送方式过滤
    const deliveryFilterMatch = deliveryFilter === "all" || order.deliveryMethod === deliveryFilter;

    // 订单类型过滤
    const typeFilterMatch = orderTypeFilter === "all" ||
      (orderTypeFilter === "physical" && order.products.some(p => p.type === "physical")) ||
      (orderTypeFilter === "virtual" && order.products.some(p => p.type === "virtual"));

    // 标签页过滤
    let tabFilterMatch = true;
    if (activeTab === "physical") {
      tabFilterMatch = order.products.some(product => product.type === "physical");
    } else if (activeTab === "virtual") {
      tabFilterMatch = order.products.some(product => product.type === "virtual");
    } else if (activeTab === "unpaid") {
      tabFilterMatch = order.paymentStatus === "unpaid";
    } else if (activeTab === "processing") {
      tabFilterMatch = order.status === "processing";
    }

    return searchFilter && statusFilterMatch && deliveryFilterMatch && typeFilterMatch && tabFilterMatch;
  })

  // 获取订单状态标签
  const getStatusBadge = (status) => {
    switch (status) {
      case "completed":
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="mr-1 h-3 w-3" /> 已完成</Badge>
      case "processing":
        return <Badge variant="default" className="bg-blue-500"><Truck className="mr-1 h-3 w-3" /> 处理中</Badge>
      case "pending":
        return <Badge variant="default" className="bg-yellow-500"><Clock className="mr-1 h-3 w-3" /> 待处理</Badge>
      case "cancelled":
        return <Badge variant="default" className="bg-red-500"><AlertTriangle className="mr-1 h-3 w-3" /> 已取消</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取支付状态标签
  const getPaymentBadge = (status) => {
    switch (status) {
      case "paid":
        return <Badge variant="outline" className="bg-green-50 text-green-700">已支付</Badge>
      case "unpaid":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700">未支付</Badge>
      case "refunded":
        return <Badge variant="outline" className="bg-red-50 text-red-700">已退款</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取配送方式标签
  const getDeliveryMethodBadge = (method) => {
    switch (method) {
      case "logistics":
        return <Badge variant="outline" className="bg-blue-50"><Truck className="mr-1 h-3 w-3" /> 物流配送</Badge>
      case "self_pickup":
        return <Badge variant="outline" className="bg-purple-50"><Package className="mr-1 h-3 w-3" /> 门店自提</Badge>
      case "verification":
        return <Badge variant="outline" className="bg-green-50"><CheckCircle className="mr-1 h-3 w-3" /> 核销</Badge>
      default:
        return <Badge variant="outline">{method}</Badge>
    }
  }

  // 获取订单类型图标和文本
  const getOrderTypeInfo = (order) => {
    // 检查订单中的产品类型
    const hasPhysical = order.products.some(p => p.type === "physical");
    const hasVirtual = order.products.some(p => p.type === "virtual");

    if (hasPhysical && !hasVirtual) {
      return { icon: <ShoppingBag className="h-4 w-4 text-blue-500" />, text: "实物商品" };
    } else if (!hasPhysical && hasVirtual) {
      // 进一步区分虚拟商品类型
      const productName = order.products[0].name.toLowerCase();
      if (productName.includes("会员") || productName.includes("卡")) {
        return { icon: <CreditCard className="h-4 w-4 text-green-500" />, text: "会员服务" };
      } else if (productName.includes("课程") || productName.includes("教程") || productName.includes("瑜伽")) {
        return { icon: <span className="flex items-center justify-center"><FileText className="h-4 w-4 text-purple-500" /></span>, text: "课程服务" };
      } else {
        return { icon: <FileText className="h-4 w-4 text-orange-500" />, text: "数字商品" };
      }
    } else {
      // 混合类型
      return { icon: <Package className="h-4 w-4 text-gray-500" />, text: "混合订单" };
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">瑜伽商城订单</h1>
        <div className="flex items-center gap-2">
          {selectedOrders.length > 0 ? (
            <>
              <Button variant="outline" onClick={() => setShowBatchOperations(true)}>
                批量操作 ({selectedOrders.length})
              </Button>
              <Button variant="outline" onClick={() => setSelectedOrders([])}>
                <X className="mr-2 h-4 w-4" />
                取消选择
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={() => setShowAdvancedFilter(true)}>
                <SlidersHorizontal className="mr-2 h-4 w-4" />
                高级筛选
              </Button>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                导出订单
              </Button>
              <Button variant="outline" onClick={() => window.location.reload()}>
                <RefreshCw className="mr-2 h-4 w-4" />
                刷新
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 订单统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">订单总数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderStatistics.total}</div>
            <p className="text-xs text-muted-foreground">
              今日新增: {orderStatistics.todayOrders}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">商品订单</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderStatistics.productOrders}</div>
            <p className="text-xs text-muted-foreground">
              占比: {Math.round(orderStatistics.productOrders / orderStatistics.total * 100)}%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">课程订单</CardTitle>
            <span className="flex items-center justify-center h-4 w-4 text-muted-foreground">
              <FileText className="h-4 w-4" />
            </span>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderStatistics.courseOrders}</div>
            <p className="text-xs text-muted-foreground">
              占比: {Math.round(orderStatistics.courseOrders / orderStatistics.total * 100)}%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">会员订单</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderStatistics.membershipOrders}</div>
            <p className="text-xs text-muted-foreground">
              占比: {Math.round(orderStatistics.membershipOrders / orderStatistics.total * 100)}%
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6 max-w-3xl">
          <TabsTrigger value="all">全部订单</TabsTrigger>
          <TabsTrigger value="physical">实物订单</TabsTrigger>
          <TabsTrigger value="virtual">虚拟订单</TabsTrigger>
          <TabsTrigger value="unpaid">待付款</TabsTrigger>
          <TabsTrigger value="processing">处理中</TabsTrigger>
          <TabsTrigger value="completed">已完成</TabsTrigger>
        </TabsList>

        <div className="flex flex-col gap-4 md:flex-row mt-6">
          <div className="relative w-full md:w-1/3">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索订单号、客户姓名或手机号"
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex flex-1 gap-2 flex-wrap md:flex-nowrap">
            <Select value={orderTypeFilter} onValueChange={setOrderTypeFilter}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="订单类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="physical">实物商品</SelectItem>
                <SelectItem value="virtual">虚拟商品</SelectItem>
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="订单状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="pending">待处理</SelectItem>
                <SelectItem value="processing">处理中</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>

            <Select value={deliveryFilter} onValueChange={setDeliveryFilter}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="配送方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部配送方式</SelectItem>
                <SelectItem value="logistics">物流配送</SelectItem>
                <SelectItem value="self_pickup">门店自提</SelectItem>
                <SelectItem value="verification">核销</SelectItem>
              </SelectContent>
            </Select>

            {/* 暂时注释掉日期范围选择器，等组件创建好后再启用 */}
            {/* <DatePickerWithRange
              className="w-full"
              selected={dateRange}
              onSelect={setDateRange}
            /> */}
          </div>
        </div>

        <TabsContent value={activeTab} className="mt-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>订单列表</CardTitle>
              <CardDescription>管理所有商城订单</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        checked={selectedOrders.length > 0 && selectedOrders.length === filteredOrders.length}
                        onCheckedChange={handleSelectAll}
                        aria-label="全选"
                      />
                    </TableHead>
                    <TableHead>订单号</TableHead>
                    <TableHead>订单类型</TableHead>
                    <TableHead>客户信息</TableHead>
                    <TableHead>订单日期</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>订单状态</TableHead>
                    <TableHead>支付状态</TableHead>
                    <TableHead>配送方式</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="h-24 text-center">
                        没有找到符合条件的订单
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredOrders.map((order) => {
                      const orderTypeInfo = getOrderTypeInfo(order);
                      return (
                        <TableRow key={order.id} className={selectedOrders.includes(order.id) ? "bg-muted/50" : ""}>
                          <TableCell>
                            <Checkbox
                              checked={selectedOrders.includes(order.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedOrders([...selectedOrders, order.id]);
                                } else {
                                  setSelectedOrders(selectedOrders.filter(id => id !== order.id));
                                }
                              }}
                              aria-label={`选择订单 ${order.id}`}
                            />
                          </TableCell>
                          <TableCell className="font-medium">{order.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {orderTypeInfo.icon}
                              <span className="text-xs">{orderTypeInfo.text}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col">
                              <span>{order.customerName}</span>
                              <span className="text-muted-foreground text-xs">{order.customerPhone}</span>
                            </div>
                          </TableCell>
                          <TableCell>{order.orderDate}</TableCell>
                          <TableCell>¥{order.totalAmount}</TableCell>
                          <TableCell>{getStatusBadge(order.status)}</TableCell>
                          <TableCell>{getPaymentBadge(order.paymentStatus)}</TableCell>
                          <TableCell>{getDeliveryMethodBadge(order.deliveryMethod)}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>订单操作</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleViewOrderDetail(order.id)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  查看详情
                                </DropdownMenuItem>

                                {order.paymentStatus === "unpaid" && (
                                  <DropdownMenuItem>
                                    <Clock className="mr-2 h-4 w-4" />
                                    催付提醒
                                  </DropdownMenuItem>
                                )}

                                {order.deliveryMethod === "logistics" && order.deliveryStatus === "pending" && (
                                  <DropdownMenuItem>
                                    <Truck className="mr-2 h-4 w-4" />
                                    发货
                                  </DropdownMenuItem>
                                )}

                                {order.deliveryMethod === "logistics" && order.deliveryStatus === "shipping" && (
                                  <DropdownMenuItem>
                                    <Truck className="mr-2 h-4 w-4" />
                                    查看物流
                                  </DropdownMenuItem>
                                )}

                                {order.deliveryMethod === "self_pickup" && order.deliveryStatus === "ready" && (
                                  <DropdownMenuItem>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    确认取货
                                  </DropdownMenuItem>
                                )}

                                {order.deliveryMethod === "verification" && order.deliveryStatus === "pending" && (
                                  <DropdownMenuItem>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    核销
                                  </DropdownMenuItem>
                                )}

                                <DropdownMenuItem>
                                  <Printer className="mr-2 h-4 w-4" />
                                  打印订单
                                </DropdownMenuItem>

                                <DropdownMenuItem>
                                  <Send className="mr-2 h-4 w-4" />
                                  发送订单
                                </DropdownMenuItem>

                                <DropdownMenuSeparator />

                                {order.status !== "completed" && order.status !== "cancelled" && (
                                  <DropdownMenuItem>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    标记为已完成
                                  </DropdownMenuItem>
                                )}

                                {order.status !== "cancelled" && (
                                  <DropdownMenuItem className="text-red-600">
                                    <X className="mr-2 h-4 w-4" />
                                    取消订单
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 订单详情对话框 */}
      <ShopOrderDetailDialog
        open={showOrderDetail}
        onOpenChange={setShowOrderDetail}
        orderId={selectedOrderId}
      />

      {/* 高级筛选对话框 */}
      <ShopOrderAdvancedFilterDialog
        open={showAdvancedFilter}
        onOpenChange={setShowAdvancedFilter}
        onApplyFilters={handleApplyFilters}
      />

      {/* 批量操作对话框 */}
      <ShopOrderBatchOperationsDialog
        open={showBatchOperations}
        onOpenChange={setShowBatchOperations}
        selectedOrders={selectedOrders}
        onApplyOperation={handleBatchOperation}
      />
    </div>
  )
}
