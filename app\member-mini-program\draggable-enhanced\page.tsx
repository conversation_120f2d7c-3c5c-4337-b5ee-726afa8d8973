"use client"

import { useState } from "react"
import { Calendar, CreditCard, Layers, User, Home, ChevronRight, MapPin, Clock, Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"

// 定义功能图标类型
interface FeatureIcon {
  id: number
  name: string
  icon: string
  bgColor: string
  iconColor: string
}

// 定义轮播图类型
interface CarouselItem {
  id: number
  image: string
  title: string
}

// 定义课程类型
interface Course {
  id: number
  title: string
  time: string
  location: string
  trainer: string
  image: string
}

export default function DraggableEnhancedPage() {
  // 标题和副标题状态
  const [title, setTitle] = useState("静心瑜伽")
  const [subtitle, setSubtitle] = useState("欢迎来到静心瑜伽，开启您的瑜伽之旅")

  // 颜色状态
  const [primaryColor, setPrimaryColor] = useState("#FF9800")
  const [secondaryColor, setSecondaryColor] = useState("#F44336")

  // 圆角状态
  const [borderRadius, setBorderRadius] = useState(8)

  // 功能图标状态
  const [features, setFeatures] = useState<FeatureIcon[]>([
    { id: 1, name: "预约", icon: "calendar", bgColor: "bg-purple-100", iconColor: "text-purple-600" },
    { id: 2, name: "会员卡", icon: "card", bgColor: "bg-red-100", iconColor: "text-red-500" },
    { id: 3, name: "课程", icon: "course", bgColor: "bg-indigo-100", iconColor: "text-indigo-600" },
    { id: 4, name: "我的", icon: "user", bgColor: "bg-pink-100", iconColor: "text-pink-500" },
  ])

  // 轮播图状态
  const [carouselItems, setCarouselItems] = useState<CarouselItem[]>([
    {
      id: 1,
      image: "https://images.unsplash.com/photo-1545205597-3d9d02c29597?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      title: "瑜伽训练营开始啦"
    },
    {
      id: 2,
      image: "https://images.unsplash.com/photo-1599447292180-45fd84092ef4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      title: "新店开业优惠"
    }
  ])

  // 公告状态
  const [announcement, setAnnouncement] = useState("场馆公告：2025年国庆放假通知请点击！！！")

  // 课程推荐状态
  const [recommendedCourses, setRecommendedCourses] = useState<Course[]>([
    {
      id: 1,
      title: "初级瑜伽训练",
      time: "周一 10:00-11:30",
      location: "一号教室",
      trainer: "Sarah",
      image: "https://images.unsplash.com/photo-1599447292180-45fd84092ef4?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
    },
    {
      id: 2,
      title: "高级瑜伽训练",
      time: "周三 14:00-15:30",
      location: "二号教室",
      trainer: "Mike",
      image: "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
    }
  ])

  // 场馆信息状态
  const [venueName, setVenueName] = useState("静心瑜伽美学生活馆")
  const [venueDescription, setVenueDescription] = useState("静心瑜伽美学生活馆是一家专注于提供高品质瑜伽课程的场馆，我们拥有专业的教练团队和舒适的环境，致力于帮助每一位会员找到身心平衡。")
  const [venueAddress, setVenueAddress] = useState("北京市朝阳区三里屯SOHO 5号楼3层301")
  const [venueHours, setVenueHours] = useState("周一至周日 09:00-22:00")

  // 显示控制
  const [showCarousel, setShowCarousel] = useState(true)
  const [showAnnouncement, setShowAnnouncement] = useState(true)
  const [showCourses, setShowCourses] = useState(true)
  const [showVenueInfo, setShowVenueInfo] = useState(true)

  // 移动功能图标
  const moveFeature = (dragIndex: number, hoverIndex: number) => {
    const newFeatures = [...features]
    const draggedFeature = newFeatures[dragIndex]
    newFeatures.splice(dragIndex, 1)
    newFeatures.splice(hoverIndex, 0, draggedFeature)
    setFeatures(newFeatures)
  }

  // 渲染图标
  const renderIcon = (iconName: string, color: string = "currentColor") => {
    switch (iconName) {
      case "calendar":
        return <Calendar className="h-6 w-6" style={{ color }} />
      case "card":
        return <CreditCard className="h-6 w-6" style={{ color }} />
      case "course":
        return <Layers className="h-6 w-6" style={{ color }} />
      case "user":
        return <User className="h-6 w-6" style={{ color }} />
      default:
        return null
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row gap-6">
        {/* 左侧预览 */}
        <div className="md:w-1/2">
          <div className="relative w-[375px] h-[700px] mx-auto bg-white rounded-[40px] shadow-xl overflow-hidden border-8 border-black">
            <div className="absolute top-0 left-0 right-0 h-6 bg-black flex justify-center items-end pb-1">
              <div className="w-20 h-4 bg-black rounded-b-xl"></div>
            </div>

            <div className="h-full pt-6 overflow-hidden">
              <div className="h-[calc(100%-6px)] overflow-y-auto pb-16">
                {/* 状态栏 - 模拟手机状态栏 */}
                <div className="bg-white px-4 py-2 flex justify-between items-center text-xs text-gray-700">
                  <span>9:41</span>
                  <div className="flex items-center gap-1">
                    <div className="w-4 h-2 bg-black rounded-sm"></div>
                    <div className="w-2 h-2 bg-black rounded-full"></div>
                    <div className="w-2 h-2 bg-black rounded-full"></div>
                  </div>
                </div>

                {/* 轮播图 */}
                {showCarousel && (
                  <div className="relative w-full h-40 bg-gray-200 overflow-hidden">
                    <div className="absolute inset-0">
                      <img
                        src={carouselItems[0].image}
                        alt={carouselItems[0].title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                        <h2 className="text-white text-xl font-bold">{carouselItems[0].title}</h2>
                      </div>
                    </div>
                    <div className="absolute bottom-2 left-0 right-0 flex justify-center">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 rounded-full bg-white"></div>
                        <div className="w-2 h-2 rounded-full bg-white/50"></div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 头部 */}
                <div className="bg-gray-100 p-4 text-center">
                  <h1 className="text-xl font-bold">{title}</h1>
                  <p className="text-sm text-gray-600 mt-1">
                    {subtitle}
                  </p>
                </div>

                {/* 功能图标 - 可拖拽 */}
                <div className="grid grid-cols-4 gap-4 p-4 relative">
                  {features.map((feature, index) => (
                    <div
                      key={feature.id}
                      className="flex flex-col items-center cursor-move"
                      draggable
                      onDragStart={(e) => {
                        e.dataTransfer.setData("text/plain", index.toString())
                      }}
                      onDragOver={(e) => e.preventDefault()}
                      onDrop={(e) => {
                        e.preventDefault()
                        const dragIndex = parseInt(e.dataTransfer.getData("text/plain"))
                        moveFeature(dragIndex, index)
                      }}
                    >
                      <div
                        className="w-14 h-14 rounded-lg flex items-center justify-center mb-1"
                        style={{
                          borderRadius: `${borderRadius}px`,
                          backgroundColor: `${feature.icon === 'calendar' || feature.icon === 'course' ? primaryColor : secondaryColor}20`
                        }}
                      >
                        {renderIcon(
                          feature.icon,
                          feature.icon === 'calendar' || feature.icon === 'course' ? primaryColor : secondaryColor
                        )}
                      </div>
                      <span className="text-xs">{feature.name}</span>
                    </div>
                  ))}
                  <div className="absolute -top-3 right-2 bg-gray-100 text-xs px-2 py-1 rounded text-gray-500">
                    拖动调整顺序
                  </div>
                </div>

                {/* 公告栏 */}
                {showAnnouncement && (
                  <div className="mx-4 my-3 flex items-center bg-white border-l-4 border-orange-500 rounded-md px-3 py-2 text-sm shadow-sm">
                    <span className="text-orange-500 mr-2">📢</span>
                    <div className="flex-1 truncate">{announcement}</div>
                    <span className="text-blue-500">查看 &gt;</span>
                  </div>
                )}

                {/* 课程推荐 */}
                {showCourses && (
                  <div className="mx-4 my-3">
                    <div className="flex justify-between items-center mb-2">
                      <h2 className="text-base font-bold">课程推荐</h2>
                      <span className="text-sm text-gray-500 flex items-center">
                        更多 <ChevronRight size={16} />
                      </span>
                    </div>

                    <div className="space-y-3">
                      {recommendedCourses.map(course => (
                        <div key={course.id} className="bg-white rounded-lg shadow-sm border border-gray-100 flex">
                          <div className="w-20 h-20 overflow-hidden">
                            <img
                              src={course.image}
                              alt={course.title}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex-1 p-2">
                            <div className="font-medium">{course.title}</div>
                            <div className="text-xs text-gray-500 flex items-center mt-1">
                              <Clock size={12} className="mr-1" /> {course.time}
                            </div>
                            <div className="text-xs text-gray-500 flex items-center mt-1">
                              <MapPin size={12} className="mr-1" /> {course.location}
                            </div>
                          </div>
                          <div className="p-2 flex flex-col justify-between items-end">
                            <div className="text-xs text-gray-500">教练: {course.trainer}</div>
                            <button
                              className="px-3 py-1 text-xs rounded-full text-white"
                              style={{ backgroundColor: primaryColor }}
                            >
                              预约
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 场馆简介 */}
                {showVenueInfo && (
                  <div className="mx-4 my-3">
                    <div className="flex justify-between items-center mb-2">
                      <h2 className="text-base font-bold">场馆简介</h2>
                      <span className="text-sm text-gray-500 flex items-center">
                        详情 <ChevronRight size={16} />
                      </span>
                    </div>

                    <div className="bg-white rounded-lg shadow p-3">
                      <h3 className="font-medium">{venueName}</h3>
                      <div className="text-sm text-gray-500 mt-2 line-clamp-2">
                        {venueDescription}
                      </div>
                      <div className="mt-2 text-xs text-gray-500 flex items-center">
                        <MapPin size={12} className="mr-1" /> {venueAddress}
                      </div>
                      <div className="mt-1 text-xs text-gray-500 flex items-center">
                        <Clock size={12} className="mr-1" /> {venueHours}
                      </div>
                      <div className="mt-2 flex items-center">
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map(star => (
                            <Star key={star} size={12} className="text-yellow-400" fill="currentColor" />
                          ))}
                        </div>
                        <span className="text-xs text-gray-500 ml-1">5.0 (128条评价)</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* 底部导航 */}
                <div className="mt-auto sticky bottom-0 left-0 right-0 bg-white border-t flex justify-around py-2">
                  <div className="flex flex-col items-center" style={{ color: primaryColor }}>
                    <Home className="h-6 w-6" />
                    <span className="text-xs mt-1">首页</span>
                  </div>

                  <div className="flex flex-col items-center text-gray-400">
                    <Calendar className="h-6 w-6" />
                    <span className="text-xs mt-1">预约</span>
                  </div>

                  <div className="flex flex-col items-center text-gray-400">
                    <Layers className="h-6 w-6" />
                    <span className="text-xs mt-1">课程</span>
                  </div>

                  <div className="flex flex-col items-center text-gray-400">
                    <User className="h-6 w-6" />
                    <span className="text-xs mt-1">我的</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-1/3 h-1 bg-black rounded-full"></div>
          </div>
        </div>

        {/* 右侧设置 */}
        <div className="md:w-1/2">
          <Card className="p-6">
            <h2 className="text-xl font-bold mb-4">小程序设置</h2>

            <Tabs defaultValue="content">
              <TabsList className="mb-4">
                <TabsTrigger value="content">内容设置</TabsTrigger>
                <TabsTrigger value="features">功能设置</TabsTrigger>
                <TabsTrigger value="style">样式设置</TabsTrigger>
                <TabsTrigger value="display">显示设置</TabsTrigger>
              </TabsList>

              <TabsContent value="content">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="title">标题</Label>
                    <Input
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="subtitle">副标题</Label>
                    <Input
                      id="subtitle"
                      value={subtitle}
                      onChange={(e) => setSubtitle(e.target.value)}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="announcement">公告内容</Label>
                    <Input
                      id="announcement"
                      value={announcement}
                      onChange={(e) => setAnnouncement(e.target.value)}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="venueName">场馆名称</Label>
                    <Input
                      id="venueName"
                      value={venueName}
                      onChange={(e) => setVenueName(e.target.value)}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="venueDescription">场馆简介</Label>
                    <Textarea
                      id="venueDescription"
                      value={venueDescription}
                      onChange={(e) => setVenueDescription(e.target.value)}
                      className="mt-1"
                      rows={3}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="features">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">功能排序</h3>
                  <p className="text-sm text-gray-500">拖动左侧预览中的功能图标可以调整顺序</p>

                  <div className="mt-4">
                    <Label>当前功能顺序</Label>
                    <div className="mt-2 space-y-2">
                      {features.map((feature, index) => (
                        <div key={feature.id} className="flex items-center p-2 bg-gray-50 rounded-md">
                          <span className="w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full mr-2">
                            {index + 1}
                          </span>
                          <div
                            className="w-6 h-6 rounded-md mr-2 flex items-center justify-center"
                            style={{
                              backgroundColor: `${feature.icon === 'calendar' || feature.icon === 'course' ? primaryColor : secondaryColor}20`,
                              color: feature.icon === 'calendar' || feature.icon === 'course' ? primaryColor : secondaryColor
                            }}
                          >
                            {renderIcon(feature.icon)}
                          </div>
                          <span>{feature.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="style">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">颜色设置</h3>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>主色调</Label>
                      <div className="flex mt-1">
                        <Input
                          type="color"
                          value={primaryColor}
                          onChange={(e) => setPrimaryColor(e.target.value)}
                          className="w-10 h-10 p-1 mr-2"
                        />
                        <Input
                          type="text"
                          value={primaryColor}
                          onChange={(e) => setPrimaryColor(e.target.value)}
                          className="flex-1"
                        />
                      </div>
                    </div>

                    <div>
                      <Label>辅助色调</Label>
                      <div className="flex mt-1">
                        <Input
                          type="color"
                          value={secondaryColor}
                          onChange={(e) => setSecondaryColor(e.target.value)}
                          className="w-10 h-10 p-1 mr-2"
                        />
                        <Input
                          type="text"
                          value={secondaryColor}
                          onChange={(e) => setSecondaryColor(e.target.value)}
                          className="flex-1"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between">
                      <Label>圆角大小</Label>
                      <span>{borderRadius}px</span>
                    </div>
                    <Input
                      type="range"
                      min="0"
                      max="20"
                      value={borderRadius}
                      onChange={(e) => setBorderRadius(parseInt(e.target.value))}
                      className="mt-1"
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="display">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">显示设置</h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="showCarousel">显示轮播图</Label>
                      <Switch
                        id="showCarousel"
                        checked={showCarousel}
                        onCheckedChange={setShowCarousel}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="showAnnouncement">显示公告栏</Label>
                      <Switch
                        id="showAnnouncement"
                        checked={showAnnouncement}
                        onCheckedChange={setShowAnnouncement}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="showCourses">显示课程推荐</Label>
                      <Switch
                        id="showCourses"
                        checked={showCourses}
                        onCheckedChange={setShowCourses}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="showVenueInfo">显示场馆简介</Label>
                      <Switch
                        id="showVenueInfo"
                        checked={showVenueInfo}
                        onCheckedChange={setShowVenueInfo}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <div className="mt-6 flex justify-end">
              <Button>保存设置</Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
