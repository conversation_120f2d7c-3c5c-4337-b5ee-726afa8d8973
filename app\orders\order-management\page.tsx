"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { 
  AlertCircle, 
  ArrowDownToLine, 
  ArrowUpDown,
  BarChart3,
  Calendar, 
  CheckCircle, 
  ChevronDown, 
  Clock, 
  Download, 
  Eye, 
  FileSpreadsheet,
  Filter, 
  FileText, 
  MoreHorizontal, 
  Printer, 
  RefreshCw, 
  Search, 
  Send, 
  Settings,
  SlidersHorizontal, 
  Truck, 
  X 
} from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { OrderDetailDialog } from "@/components/orders/order-detail-dialog"
import { OrderAdvancedFilterDialog } from "@/components/orders/order-advanced-filter-dialog"
import { OrderBatchOperationsDialog } from "@/components/orders/order-batch-operations-dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { cn } from "@/lib/utils"
import { addDays, format } from "date-fns"
import { DateRange } from "react-day-picker"

// 模拟订单数据
const orders = [
  {
    id: "ORD-20230501-001",
    customer: {
      name: "张三",
      phone: "13800138001",
      avatar: "/avatars/01.png"
    },
    orderDate: "2023-05-01 14:30:25",
    totalAmount: 199.00,
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "logistics", // 物流配送
    deliveryStatus: "delivered", // 已送达
    products: [
      { name: "瑜伽垫 - 专业防滑", quantity: 1, price: 199.00, type: "physical" }
    ],
    address: "上海市浦东新区XX路XX号",
    trackingNumber: "SF1234567890",
    remark: "",
  },
  {
    id: "ORD-20230515-002",
    customer: {
      name: "李四",
      phone: "13800138002",
      avatar: "/avatars/02.png"
    },
    orderDate: "2023-05-15 09:20:15",
    totalAmount: 2580.00,
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "alipay", // 支付宝
    deliveryMethod: "self_pickup", // 自提
    deliveryStatus: "delivered", // 已送达
    products: [
      { name: "瑜伽年卡", quantity: 1, price: 2580.00, type: "membership" }
    ],
    address: "",
    trackingNumber: "",
    remark: "会员卡已激活",
  },
  {
    id: "ORD-20230515-003",
    customer: {
      name: "王五",
      phone: "13800138003",
      avatar: "/avatars/03.png"
    },
    orderDate: "2023-05-15 16:45:30",
    totalAmount: 1588.00,
    status: "processing", // 处理中
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "logistics", // 物流配送
    deliveryStatus: "shipping", // 配送中
    products: [
      { name: "瑜伽服套装 - 女款", quantity: 1, price: 1588.00, type: "physical" }
    ],
    address: "北京市朝阳区XX街XX号",
    trackingNumber: "YT9876543210",
    remark: "尺码: M",
  },
  {
    id: "ORD-20230520-004",
    customer: {
      name: "赵六",
      phone: "13800138004",
      avatar: "/avatars/04.png"
    },
    orderDate: "2023-05-20 09:30:45",
    totalAmount: 99.00,
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "balance", // 余额支付
    deliveryMethod: "verification", // 核销
    deliveryStatus: "delivered", // 已送达
    products: [
      { name: "瑜伽单次体验课", quantity: 1, price: 99.00, type: "course" }
    ],
    address: "",
    trackingNumber: "",
    remark: "已核销",
  },
  {
    id: "ORD-20230525-005",
    customer: {
      name: "孙七",
      phone: "13800138005",
      avatar: "/avatars/05.png"
    },
    orderDate: "2023-05-25 13:20:18",
    totalAmount: 49.00,
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "verification", // 核销
    deliveryStatus: "delivered", // 已送达
    products: [
      { name: "瑜伽视频教程 - 初级", quantity: 1, price: 49.00, type: "virtual" }
    ],
    address: "",
    trackingNumber: "",
    remark: "",
  },
  {
    id: "ORD-20230601-006",
    customer: {
      name: "周八",
      phone: "13800138006",
      avatar: "/avatars/06.png"
    },
    orderDate: "2023-06-01 11:05:33",
    totalAmount: 1288.00,
    status: "pending", // 待处理
    paymentStatus: "unpaid", // 未支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "verification", // 核销
    deliveryStatus: "pending", // 待处理
    products: [
      { name: "高级私教身份 - 月卡", quantity: 1, price: 1288.00, type: "virtual" }
    ],
    address: "",
    trackingNumber: "",
    remark: "等待支付",
  },
]

export default function OrderManagementPage() {
  // 基础状态管理
  const [activeTab, setActiveTab] = useState("all-orders")
  const [searchQuery, setSearchQuery] = useState("")
  const [paymentStatus, setPaymentStatus] = useState("all")
  const [orderStatus, setOrderStatus] = useState("all")
  const [deliveryMethod, setDeliveryMethod] = useState("all")
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: new Date(),
  })
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [showOrderDetail, setShowOrderDetail] = useState(false)
  const [selectedOrderId, setSelectedOrderId] = useState("")
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false)
  const [showBatchOperations, setShowBatchOperations] = useState(false)
  
  // 新增状态
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [showExportOptions, setShowExportOptions] = useState(false)
  const [orderStatistics, setOrderStatistics] = useState({
    total: 0,
    completed: 0,
    processing: 0,
    pending: 0,
    unpaid: 0,
    totalAmount: 0
  })

  // 过滤订单
  const filteredOrders = orders.filter((order) => {
    // 基本搜索过滤
    const searchFilter = 
      searchQuery === "" || 
      order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customer.phone.includes(searchQuery);
    
    // 标签页过滤
    const tabFilter = 
      activeTab === "all-orders" || 
      (activeTab === "physical-orders" && order.products.some(p => p.type === "physical")) ||
      (activeTab === "virtual-orders" && order.products.some(p => p.type === "virtual" || p.type === "course" || p.type === "membership")) ||
      (activeTab === "pending-payment" && order.paymentStatus === "unpaid") ||
      (activeTab === "processing" && order.status === "processing");
    
    // 支付状态过滤
    const paymentFilter = 
      paymentStatus === "all" || 
      order.paymentStatus === paymentStatus;
    
    // 订单状态过滤
    const statusFilter = 
      orderStatus === "all" || 
      order.status === orderStatus;
    
    // 配送方式过滤
    const deliveryFilter = 
      deliveryMethod === "all" || 
      order.deliveryMethod === deliveryMethod;
    
    // 日期范围过滤
    const orderDate = new Date(order.orderDate);
    const dateFilter = 
      !dateRange || 
      !dateRange.from || 
      (orderDate >= dateRange.from && 
       (!dateRange.to || orderDate <= dateRange.to));
    
    return searchFilter && tabFilter && paymentFilter && statusFilter && deliveryFilter && dateFilter;
  });

  // 处理查看订单详情
  const handleViewOrderDetail = (orderId: string) => {
    setSelectedOrderId(orderId);
    setShowOrderDetail(true);
  };

  // 处理应用高级筛选
  const handleApplyFilters = (filters: any) => {
    console.log("应用高级筛选:", filters);
    // 实际应用中，这里会更新筛选状态
  };

  // 处理批量操作
  const handleApplyBatchOperation = (operation: string) => {
    console.log(`对订单 ${selectedOrders.join(", ")} 执行操作: ${operation}`);
    // 实际应用中，这里会执行相应的批量操作
    setSelectedOrders([]);
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedOrders(filteredOrders.map(order => order.id));
    } else {
      setSelectedOrders([]);
    }
  };

  // 获取订单状态标签
  const getStatusBadge = (status: string, paymentStatus: string) => {
    if (paymentStatus === "unpaid") {
      return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-50 border-yellow-200">待付款</Badge>;
    }
    
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">待处理</Badge>;
      case "processing":
        return <Badge variant="outline" className="bg-indigo-50 text-indigo-700 hover:bg-indigo-50 border-indigo-200">处理中</Badge>;
      case "completed":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已完成</Badge>;
      case "cancelled":
        return <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200">已取消</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // 计算订单统计数据
  useEffect(() => {
    // 实际项目中应该从API获取这些数据
    const stats = {
      total: orders.length,
      completed: orders.filter(o => o.status === "completed").length,
      processing: orders.filter(o => o.status === "processing").length,
      pending: orders.filter(o => o.status === "pending").length,
      unpaid: orders.filter(o => o.paymentStatus === "unpaid").length,
      totalAmount: orders.reduce((sum, order) => sum + order.totalAmount, 0)
    };
    setOrderStatistics(stats);
  }, []);

  // 刷新订单数据
  const handleRefresh = () => {
    setIsRefreshing(true);
    // 模拟API请求
    setTimeout(() => {
      setIsRefreshing(false);
      // 实际项目中这里应该重新获取订单数据
    }, 800);
  };

  return (
    <div className="space-y-6">
      {/* 订单统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">订单总数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderStatistics.total}</div>
            <p className="text-xs text-muted-foreground">
              已完成: {orderStatistics.completed} | 处理中: {orderStatistics.processing}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待处理订单</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderStatistics.pending}</div>
            <p className="text-xs text-muted-foreground">
              待付款: {orderStatistics.unpaid}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">订单总金额</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{orderStatistics.totalAmount.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              {dateRange?.from ? format(dateRange.from, 'yyyy-MM-dd') : ''} 至 {dateRange?.to ? format(dateRange.to, 'yyyy-MM-dd') : '今天'}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均订单金额</CardTitle>
            <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ¥{orderStatistics.total > 0 ? (orderStatistics.totalAmount / orderStatistics.total).toFixed(2) : '0.00'}
            </div>
            <p className="text-xs text-muted-foreground">
              基于当前筛选条件计算
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 标题和操作按钮 */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">订单管理</h1>
        <div className="flex items-center gap-2">
          {selectedOrders.length > 0 ? (
            <>
              <Button variant="outline" onClick={() => setShowBatchOperations(true)}>
                批量操作 ({selectedOrders.length})
              </Button>
              <Button variant="outline" onClick={() => setSelectedOrders([])}>
                <X className="h-4 w-4 mr-2" />
                取消选择
              </Button>
            </>
          ) : (
            <>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" onClick={() => setShowAdvancedFilter(true)}>
                      <SlidersHorizontal className="h-4 w-4 mr-2" />
                      高级筛选
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>设置更多筛选条件</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <DropdownMenu open={showExportOptions} onOpenChange={setShowExportOptions}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    导出订单
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                    导出为Excel
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <FileText className="h-4 w-4 mr-2" />
                    导出为CSV
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Settings className="h-4 w-4 mr-2" />
                    导出设置
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <Button 
                variant="outline" 
                onClick={handleRefresh} 
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                {isRefreshing ? '刷新中...' : '刷新'}
              </Button>
            </>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 max-w-2xl">
          <TabsTrigger value="all-orders">全部订单</TabsTrigger>
          <TabsTrigger value="physical-orders">实物订单</TabsTrigger>
          <TabsTrigger value="virtual-orders">虚拟订单</TabsTrigger>
          <TabsTrigger value="pending-payment">待付款</TabsTrigger>
          <TabsTrigger value="processing">处理中</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative w-full md:w-1/3">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索订单号、客户姓名或手机号"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-1 gap-2 flex-wrap md:flex-nowrap">
          <Select value={paymentStatus} onValueChange={setPaymentStatus}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="支付状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部支付状态</SelectItem>
              <SelectItem value="paid">已支付</SelectItem>
              <SelectItem value="unpaid">待支付</SelectItem>
              <SelectItem value="refunded">已退款</SelectItem>
              <SelectItem value="failed">支付失败</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={orderStatus} onValueChange={setOrderStatus}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="订单状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部订单状态</SelectItem>
              <SelectItem value="pending">待处理</SelectItem>
              <SelectItem value="processing">处理中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
              <SelectItem value="cancelled">已取消</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={deliveryMethod} onValueChange={setDeliveryMethod}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="配送方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部配送方式</SelectItem>
              <SelectItem value="logistics">物流配送</SelectItem>
              <SelectItem value="self_pickup">门店自提</SelectItem>
              <SelectItem value="verification">核销</SelectItem>
            </SelectContent>
          </Select>
          
          <DatePickerWithRange 
            className="w-full"
            selected={dateRange} 
            onSelect={setDateRange} 
          />
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <Checkbox 
                    checked={selectedOrders.length > 0 && selectedOrders.length === filteredOrders.length}
                    onCheckedChange={handleSelectAll}
                    aria-label="全选"
                  />
                </TableHead>
                <TableHead>订单号</TableHead>
                <TableHead>客户信息</TableHead>
                <TableHead>订单日期</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>订单状态</TableHead>
                <TableHead>支付方式</TableHead>
                <TableHead>配送方式</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="h-24 text-center">
                    没有找到符合条件的订单
                  </TableCell>
                </TableRow>
              ) : (
                filteredOrders.map((order) => (
                  <TableRow key={order.id} className={cn(selectedOrders.includes(order.id) && "bg-muted/50")}>
                    <TableCell>
                      <Checkbox 
                        checked={selectedOrders.includes(order.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedOrders([...selectedOrders, order.id]);
                          } else {
                            setSelectedOrders(selectedOrders.filter(id => id !== order.id));
                          }
                        }}
                        aria-label={`选择订单 ${order.id}`}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{order.id}</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span>{order.customer.name}</span>
                        <span className="text-muted-foreground text-xs">{order.customer.phone}</span>
                      </div>
                    </TableCell>
                    <TableCell>{order.orderDate}</TableCell>
                    <TableCell>¥{order.totalAmount.toFixed(2)}</TableCell>
                    <TableCell>{getStatusBadge(order.status, order.paymentStatus)}</TableCell>
                    <TableCell>
                      {order.paymentMethod === "wechat" ? "微信支付" : 
                       order.paymentMethod === "alipay" ? "支付宝" : 
                       order.paymentMethod === "balance" ? "余额支付" : 
                       order.paymentMethod}
                    </TableCell>
                    <TableCell>
                      {order.deliveryMethod === "logistics" ? "物流配送" : 
                       order.deliveryMethod === "self_pickup" ? "门店自提" : 
                       order.deliveryMethod === "verification" ? "核销" : 
                       order.deliveryMethod}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>订单操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleViewOrderDetail(order.id)}>
                            <Eye className="h-4 w-4 mr-2" />
                            查看详情
                          </DropdownMenuItem>
                          
                          {order.paymentStatus === "unpaid" && (
                            <DropdownMenuItem>
                              <Clock className="h-4 w-4 mr-2" />
                              催付提醒
                            </DropdownMenuItem>
                          )}
                          
                          {order.deliveryMethod === "logistics" && order.deliveryStatus === "shipping" && (
                            <DropdownMenuItem>
                              <Truck className="h-4 w-4 mr-2" />
                              查看物流
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuItem>
                            <Printer className="h-4 w-4 mr-2" />
                            打印订单
                          </DropdownMenuItem>
                          
                          <DropdownMenuItem>
                            <Send className="h-4 w-4 mr-2" />
                            发送订单
                          </DropdownMenuItem>
                          
                          <DropdownMenuSeparator />
                          
                          {order.status !== "completed" && order.status !== "cancelled" && (
                            <DropdownMenuItem>
                              <CheckCircle className="h-4 w-4 mr-2" />
                              标记为已完成
                            </DropdownMenuItem>
                          )}
                          
                          {order.status !== "cancelled" && (
                            <DropdownMenuItem className="text-red-600">
                              <X className="h-4 w-4 mr-2" />
                              取消订单
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 订单详情对话框 */}
      <OrderDetailDialog 
        open={showOrderDetail} 
        onOpenChange={setShowOrderDetail} 
        orderId={selectedOrderId} 
      />

      {/* 高级筛选对话框 */}
      <OrderAdvancedFilterDialog
        open={showAdvancedFilter}
        onOpenChange={setShowAdvancedFilter}
        onApplyFilters={handleApplyFilters}
      />

      {/* 批量操作对话框 */}
      <OrderBatchOperationsDialog
        open={showBatchOperations}
        onOpenChange={setShowBatchOperations}
        selectedOrders={selectedOrders}
        onApplyOperation={handleApplyBatchOperation}
      />
    </div>
  )
}
