import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Calculator, FileText, Download, Search, Filter, Plus, Calendar } from "lucide-react"

export default function SalaryPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">薪资管理</h1>
      </div>

      <Tabs defaultValue="settings" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
          <TabsTrigger value="settings">薪资设置</TabsTrigger>
          <TabsTrigger value="structure">薪资结构</TabsTrigger>
          <TabsTrigger value="calculation">薪资计算</TabsTrigger>
          <TabsTrigger value="records">薪资记录</TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>薪资基本设置</CardTitle>
              <CardDescription>配置薪资发放和计算规则</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">发放设置</h3>

                <div className="space-y-2">
                  <Label htmlFor="salary-day">薪资发放日</Label>
                  <Select defaultValue="15">
                    <SelectTrigger>
                      <SelectValue placeholder="选择发放日" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">每月1日</SelectItem>
                      <SelectItem value="5">每月5日</SelectItem>
                      <SelectItem value="10">每月10日</SelectItem>
                      <SelectItem value="15">每月15日</SelectItem>
                      <SelectItem value="20">每月20日</SelectItem>
                      <SelectItem value="25">每月25日</SelectItem>
                      <SelectItem value="last">每月最后一天</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="salary-period">薪资计算周期</Label>
                  <Select defaultValue="prev-month">
                    <SelectTrigger>
                      <SelectValue placeholder="选择计算周期" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="prev-month">上月1日至月底</SelectItem>
                      <SelectItem value="current-month">当月1日至月底</SelectItem>
                      <SelectItem value="custom">自定义周期</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-calculate">自动计算薪资</Label>
                    <p className="text-sm text-muted-foreground">系统自动计算每月薪资</p>
                  </div>
                  <Switch id="auto-calculate" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="calculate-day">自动计算日期</Label>
                  <Select defaultValue="1">
                    <SelectTrigger>
                      <SelectValue placeholder="选择计算日期" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">每月1日</SelectItem>
                      <SelectItem value="2">每月2日</SelectItem>
                      <SelectItem value="3">每月3日</SelectItem>
                      <SelectItem value="4">每月4日</SelectItem>
                      <SelectItem value="5">每月5日</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="salary-approval">薪资需审批</Label>
                    <p className="text-sm text-muted-foreground">薪资发放前需要审批</p>
                  </div>
                  <Switch id="salary-approval" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="approval-roles">审批角色</Label>
                  <Select defaultValue="finance-manager">
                    <SelectTrigger>
                      <SelectValue placeholder="选择审批角色" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="finance-manager">财务经理</SelectItem>
                      <SelectItem value="hr-manager">人事经理</SelectItem>
                      <SelectItem value="general-manager">总经理</SelectItem>
                      <SelectItem value="multiple">多级审批</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">薪资单设置</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-send">自动发送薪资单</Label>
                    <p className="text-sm text-muted-foreground">自动向员工发送薪资单</p>
                  </div>
                  <Switch id="auto-send" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="send-method">发送方式</Label>
                  <Select defaultValue="email">
                    <SelectTrigger>
                      <SelectValue placeholder="选择发送方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">邮件</SelectItem>
                      <SelectItem value="sms">短信</SelectItem>
                      <SelectItem value="both">邮件和短信</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="salary-encrypt">薪资单加密</Label>
                    <p className="text-sm text-muted-foreground">使用密码保护薪资单</p>
                  </div>
                  <Switch id="salary-encrypt" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="encrypt-method">加密方式</Label>
                  <Select defaultValue="id-last4">
                    <SelectTrigger>
                      <SelectValue placeholder="选择加密方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="id-last4">身份证后4位</SelectItem>
                      <SelectItem value="phone-last4">手机号后4位</SelectItem>
                      <SelectItem value="birth-date">出生日期</SelectItem>
                      <SelectItem value="custom">自定义密码</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="salary-template">薪资单模板</Label>
                  <Select defaultValue="standard">
                    <SelectTrigger>
                      <SelectValue placeholder="选择薪资单模板" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="standard">标准模板</SelectItem>
                      <SelectItem value="detailed">详细模板</SelectItem>
                      <SelectItem value="simple">简洁模板</SelectItem>
                      <SelectItem value="custom">自定义模板</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">税务设置</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-tax">自动计算个税</Label>
                    <p className="text-sm text-muted-foreground">系统自动计算个人所得税</p>
                  </div>
                  <Switch id="auto-tax" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tax-method">计税方法</Label>
                  <Select defaultValue="standard">
                    <SelectTrigger>
                      <SelectValue placeholder="选择计税方法" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="standard">标准累进税率</SelectItem>
                      <SelectItem value="flat">固定税率</SelectItem>
                      <SelectItem value="custom">自定义税率</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tax-threshold">起征点（元/月）</Label>
                  <Input id="tax-threshold" type="number" defaultValue="5000" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="social-insurance">社保公积金</Label>
                    <p className="text-sm text-muted-foreground">自动计算社保和公积金</p>
                  </div>
                  <Switch id="social-insurance" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="insurance-base">社保基数（元）</Label>
                  <Input id="insurance-base" type="number" defaultValue="10000" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fund-base">公积金基数（元）</Label>
                  <Input id="fund-base" type="number" defaultValue="10000" />
                </div>
              </div>

              <div className="flex justify-end">
                <Button>保存设置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="structure" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>薪资结构设置</CardTitle>
              <CardDescription>配置薪资组成部分和计算规则</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">薪资组成</h3>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>项目名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>计算方式</TableHead>
                        <TableHead>是否计税</TableHead>
                        <TableHead>适用对象</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>基本工资</TableCell>
                        <TableCell>固定项</TableCell>
                        <TableCell>固定金额</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>绩效工资</TableCell>
                        <TableCell>浮动项</TableCell>
                        <TableCell>基本工资百分比</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>课时费</TableCell>
                        <TableCell>浮动项</TableCell>
                        <TableCell>单价×课时数</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>教练</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>销售提成</TableCell>
                        <TableCell>浮动项</TableCell>
                        <TableCell>销售额百分比</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>销售人员</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>交通补贴</TableCell>
                        <TableCell>固定项</TableCell>
                        <TableCell>固定金额</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>餐饮补贴</TableCell>
                        <TableCell>固定项</TableCell>
                        <TableCell>固定金额</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>社保（个人部分）</TableCell>
                        <TableCell>扣减项</TableCell>
                        <TableCell>基数×比例</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>公积金（个人部分）</TableCell>
                        <TableCell>扣减项</TableCell>
                        <TableCell>基数×比例</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>个人所得税</TableCell>
                        <TableCell>扣减项</TableCell>
                        <TableCell>累进税率</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    添加薪资项目
                  </Button>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">薪资等级</h3>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>等级名称</TableHead>
                        <TableHead>基本工资范围</TableHead>
                        <TableHead>适用职位</TableHead>
                        <TableHead>绩效比例</TableHead>
                        <TableHead>晋升条件</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>P1</TableCell>
                        <TableCell>4000-6000</TableCell>
                        <TableCell>初级职位</TableCell>
                        <TableCell>10%</TableCell>
                        <TableCell>入职满1年，绩效良好</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>P2</TableCell>
                        <TableCell>6000-8000</TableCell>
                        <TableCell>中级职位</TableCell>
                        <TableCell>15%</TableCell>
                        <TableCell>P1满2年，绩效优秀</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>P3</TableCell>
                        <TableCell>8000-10000</TableCell>
                        <TableCell>高级职位</TableCell>
                        <TableCell>20%</TableCell>
                        <TableCell>P2满2年，绩效优秀</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>M1</TableCell>
                        <TableCell>10000-15000</TableCell>
                        <TableCell>管理职位</TableCell>
                        <TableCell>25%</TableCell>
                        <TableCell>P3满2年，管理能力突出</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>M2</TableCell>
                        <TableCell>15000-20000</TableCell>
                        <TableCell>高级管理职位</TableCell>
                        <TableCell>30%</TableCell>
                        <TableCell>M1满3年，业绩突出</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    添加薪资等级
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calculation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>薪资计算规则</CardTitle>
              <CardDescription>配置薪资计算的具体规则</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">绩效计算</h3>

                <div className="space-y-2">
                  <Label htmlFor="performance-method">绩效计算方式</Label>
                  <Select defaultValue="percentage">
                    <SelectTrigger>
                      <SelectValue placeholder="选择计算方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="percentage">基本工资百分比</SelectItem>
                      <SelectItem value="fixed">固定金额</SelectItem>
                      <SelectItem value="formula">自定义公式</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="performance-base">绩效基数</Label>
                  <Select defaultValue="basic">
                    <SelectTrigger>
                      <SelectValue placeholder="选择绩效基数" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="basic">基本工资</SelectItem>
                      <SelectItem value="total">总工资</SelectItem>
                      <SelectItem value="custom">自定义基数</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>绩效等级</TableHead>
                        <TableHead>评分范围</TableHead>
                        <TableHead>绩效系数</TableHead>
                        <TableHead>适用对象</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>A</TableCell>
                        <TableCell>90-100</TableCell>
                        <TableCell>1.2</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>B+</TableCell>
                        <TableCell>80-89</TableCell>
                        <TableCell>1.1</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>B</TableCell>
                        <TableCell>70-79</TableCell>
                        <TableCell>1.0</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>C</TableCell>
                        <TableCell>60-69</TableCell>
                        <TableCell>0.8</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>D</TableCell>
                        <TableCell>0-59</TableCell>
                        <TableCell>0.5</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    添加绩效等级
                  </Button>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">考勤计算</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="attendance-deduction">考勤扣款</Label>
                    <p className="text-sm text-muted-foreground">根据考勤情况扣减工资</p>
                  </div>
                  <Switch id="attendance-deduction" defaultChecked />
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>考勤类型</TableHead>
                        <TableHead>计算规则</TableHead>
                        <TableHead>扣款基数</TableHead>
                        <TableHead>适用对象</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>迟到</TableCell>
                        <TableCell>每次扣款50元</TableCell>
                        <TableCell>固定金额</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>早退</TableCell>
                        <TableCell>每次扣款50元</TableCell>
                        <TableCell>固定金额</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>旷工</TableCell>
                        <TableCell>每天扣款日工资的3倍</TableCell>
                        <TableCell>日工资</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>事假</TableCell>
                        <TableCell>按天扣除日工资</TableCell>
                        <TableCell>日工资</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>病假</TableCell>
                        <TableCell>按天扣除日工资的80%</TableCell>
                        <TableCell>日工资</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">加班计算</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="overtime-pay">加班费计算</Label>
                    <p className="text-sm text-muted-foreground">根据加班情况计算加班费</p>
                  </div>
                  <Switch id="overtime-pay" defaultChecked />
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>加班类型</TableHead>
                        <TableHead>计算规则</TableHead>
                        <TableHead>加班费基数</TableHead>
                        <TableHead>适用对象</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>工作日加班</TableCell>
                        <TableCell>1.5倍时薪</TableCell>
                        <TableCell>时薪</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>休息日加班</TableCell>
                        <TableCell>2倍时薪</TableCell>
                        <TableCell>时薪</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>法定节假日加班</TableCell>
                        <TableCell>3倍时薪</TableCell>
                        <TableCell>时薪</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>保存设置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="records" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>薪资记录</CardTitle>
              <CardDescription>查看和管理员工薪资发放记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="relative flex-1 mr-4">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="搜索员工姓名、部门..." className="pl-8 max-w-sm" />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Calendar className="mr-2 h-4 w-4" />
                    选择月份
                  </Button>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    导出
                  </Button>
                  <Button>
                    <Calculator className="mr-2 h-4 w-4" />
                    计算薪资
                  </Button>
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>员工</TableHead>
                      <TableHead>部门</TableHead>
                      <TableHead>职位</TableHead>
                      <TableHead>薪资月份</TableHead>
                      <TableHead>基本工资</TableHead>
                      <TableHead>绩效工资</TableHead>
                      <TableHead>其他补贴</TableHead>
                      <TableHead>扣款</TableHead>
                      <TableHead>实发金额</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>张静</TableCell>
                      <TableCell>教学部</TableCell>
                      <TableCell>高级教练</TableCell>
                      <TableCell>2024-03</TableCell>
                      <TableCell>8000</TableCell>
                      <TableCell>2400</TableCell>
                      <TableCell>1500</TableCell>
                      <TableCell>1200</TableCell>
                      <TableCell>10700</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          已发放
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>李明</TableCell>
                      <TableCell>运营部</TableCell>
                      <TableCell>前台主管</TableCell>
                      <TableCell>2024-03</TableCell>
                      <TableCell>6000</TableCell>
                      <TableCell>1200</TableCell>
                      <TableCell>800</TableCell>
                      <TableCell>900</TableCell>
                      <TableCell>7100</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          已发放
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>王芳</TableCell>
                      <TableCell>教学部</TableCell>
                      <TableCell>资深教练</TableCell>
                      <TableCell>2024-03</TableCell>
                      <TableCell>7000</TableCell>
                      <TableCell>2100</TableCell>
                      <TableCell>1200</TableCell>
                      <TableCell>1000</TableCell>
                      <TableCell>9300</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          已发放
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>赵健</TableCell>
                      <TableCell>销售部</TableCell>
                      <TableCell>销售顾问</TableCell>
                      <TableCell>2024-03</TableCell>
                      <TableCell>5000</TableCell>
                      <TableCell>3500</TableCell>
                      <TableCell>500</TableCell>
                      <TableCell>800</TableCell>
                      <TableCell>8200</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          已发放
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>钱蓉</TableCell>
                      <TableCell>财务部</TableCell>
                      <TableCell>财务主管</TableCell>
                      <TableCell>2024-03</TableCell>
                      <TableCell>8500</TableCell>
                      <TableCell>1700</TableCell>
                      <TableCell>1000</TableCell>
                      <TableCell>1300</TableCell>
                      <TableCell>9900</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          已发放
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>张静</TableCell>
                      <TableCell>教学部</TableCell>
                      <TableCell>高级教练</TableCell>
                      <TableCell>2024-04</TableCell>
                      <TableCell>8000</TableCell>
                      <TableCell>2400</TableCell>
                      <TableCell>1500</TableCell>
                      <TableCell>1200</TableCell>
                      <TableCell>10700</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                          待审批
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          审批
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>李明</TableCell>
                      <TableCell>运营部</TableCell>
                      <TableCell>前台主管</TableCell>
                      <TableCell>2024-04</TableCell>
                      <TableCell>6000</TableCell>
                      <TableCell>1200</TableCell>
                      <TableCell>800</TableCell>
                      <TableCell>900</TableCell>
                      <TableCell>7100</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                          待审批
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          审批
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">显示 1-7 条，共 42 条记录</div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" disabled>
                    上一页
                  </Button>
                  <Button variant="outline" size="sm">
                    下一页
                  </Button>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">薪资统计</h3>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">本月薪资总额</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center">
                        <div className="text-3xl font-bold">¥ 45,200</div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">平均薪资</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center">
                        <div className="text-3xl font-bold">¥ 9,040</div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">最高薪资</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center">
                        <div className="text-3xl font-bold">¥ 10,700</div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">最低薪资</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center">
                        <div className="text-3xl font-bold">¥ 7,100</div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="flex justify-end">
                  <Button variant="outline">
                    <FileText className="mr-2 h-4 w-4" />
                    生成薪资报表
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

