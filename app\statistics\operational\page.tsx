"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Calendar, Download, Clock, Dumbbell, Building2, Star } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"

export default function OperationalStatisticsPage() {
  const [dateRange, setDateRange] = useState("30days")
  const [venueFilter, setVenueFilter] = useState("all")

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">运营效率分析</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Calendar className="h-4 w-4" />
            <span>选择时间范围</span>
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex flex-1 gap-4">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="thismonth">本月</SelectItem>
              <SelectItem value="lastmonth">上月</SelectItem>
              <SelectItem value="thisyear">今年</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>

          <Select value={venueFilter} onValueChange={setVenueFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="场馆筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部场馆</SelectItem>
              <SelectItem value="venue1">朝阳店</SelectItem>
              <SelectItem value="venue2">海淀店</SelectItem>
              <SelectItem value="venue3">西城店</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">场地利用率</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">78.5%</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-green-500">+3.2%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">教练工时利用率</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">82.3%</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-green-500">+1.8%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">设备使用率</CardTitle>
            <Dumbbell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">65.7%</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-red-500">-2.1%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">客户满意度</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.8/5.0</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-green-500">+0.2</span>
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="venue" className="space-y-4">
        <TabsList>
          <TabsTrigger value="venue">场地利用分析</TabsTrigger>
          <TabsTrigger value="coach">人力资源效率</TabsTrigger>
          <TabsTrigger value="equipment">设备使用效率</TabsTrigger>
          <TabsTrigger value="satisfaction">客户满意度</TabsTrigger>
        </TabsList>

        <TabsContent value="venue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>场地利用率分析</CardTitle>
              <CardDescription>各场地使用情况分析</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>场地名称</TableHead>
                    <TableHead>可用时间(小时)</TableHead>
                    <TableHead>已用时间(小时)</TableHead>
                    <TableHead>利用率</TableHead>
                    <TableHead>平均课程人数</TableHead>
                    <TableHead>满座率</TableHead>
                    <TableHead>收入贡献</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">大型瑜伽教室A</TableCell>
                    <TableCell>360</TableCell>
                    <TableCell>320</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={89} className="w-[60px]" />
                        <span>89%</span>
                      </div>
                    </TableCell>
                    <TableCell>18人</TableCell>
                    <TableCell>90%</TableCell>
                    <TableCell>¥64,000</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">大型瑜伽教室B</TableCell>
                    <TableCell>360</TableCell>
                    <TableCell>305</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={85} className="w-[60px]" />
                        <span>85%</span>
                      </div>
                    </TableCell>
                    <TableCell>16人</TableCell>
                    <TableCell>80%</TableCell>
                    <TableCell>¥54,900</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">中型瑜伽教室</TableCell>
                    <TableCell>360</TableCell>
                    <TableCell>280</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={78} className="w-[60px]" />
                        <span>78%</span>
                      </div>
                    </TableCell>
                    <TableCell>12人</TableCell>
                    <TableCell>85%</TableCell>
                    <TableCell>¥42,000</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">小型私教室A</TableCell>
                    <TableCell>360</TableCell>
                    <TableCell>252</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={70} className="w-[60px]" />
                        <span>70%</span>
                      </div>
                    </TableCell>
                    <TableCell>3人</TableCell>
                    <TableCell>100%</TableCell>
                    <TableCell>¥37,800</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">小型私教室B</TableCell>
                    <TableCell>360</TableCell>
                    <TableCell>245</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={68} className="w-[60px]" />
                        <span>68%</span>
                      </div>
                    </TableCell>
                    <TableCell>3人</TableCell>
                    <TableCell>100%</TableCell>
                    <TableCell>¥36,750</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看详细数据
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>场地使用高峰期</CardTitle>
                <CardDescription>不同时段场地使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是场地使用高峰期图表 */}
                  <div className="flex h-[300px] items-end gap-2">
                    {Array.from({ length: 24 }).map((_, i) => {
                      const height = i >= 8 && i <= 22 ? 20 + Math.random() * 80 : Math.random() * 20
                      return (
                        <div key={i} className="relative flex-1">
                          <div className="bg-primary rounded-t-md w-full" style={{ height: `${height}%` }}></div>
                          {i % 3 === 0 && (
                            <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                              {i}:00
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>场地周使用率</CardTitle>
                <CardDescription>一周中各天的场地使用率</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是场地周使用率图表 */}
                  <div className="flex h-[300px] items-end gap-6">
                    {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, i) => {
                      const height = 40 + Math.random() * 60
                      const isWeekend = i >= 5
                      return (
                        <div key={i} className="relative flex-1">
                          <div
                            className={`rounded-t-md w-full ${isWeekend ? "bg-blue-500" : "bg-primary"}`}
                            style={{ height: `${height}%` }}
                          ></div>
                          <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                            {day}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="coach" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>教练工时分析</CardTitle>
              <CardDescription>各教练工时利用情况</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>教练姓名</TableHead>
                    <TableHead>计划工时</TableHead>
                    <TableHead>实际工时</TableHead>
                    <TableHead>工时利用率</TableHead>
                    <TableHead>平均课程人数</TableHead>
                    <TableHead>满座率</TableHead>
                    <TableHead>收入贡献</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">张教练</TableCell>
                    <TableCell>160</TableCell>
                    <TableCell>152</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={95} className="w-[60px]" />
                        <span>95%</span>
                      </div>
                    </TableCell>
                    <TableCell>16人</TableCell>
                    <TableCell>89%</TableCell>
                    <TableCell>¥45,600</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">李教练</TableCell>
                    <TableCell>160</TableCell>
                    <TableCell>145</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={91} className="w-[60px]" />
                        <span>91%</span>
                      </div>
                    </TableCell>
                    <TableCell>15人</TableCell>
                    <TableCell>83%</TableCell>
                    <TableCell>¥40,600</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">王教练</TableCell>
                    <TableCell>160</TableCell>
                    <TableCell>136</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={85} className="w-[60px]" />
                        <span>85%</span>
                      </div>
                    </TableCell>
                    <TableCell>14人</TableCell>
                    <TableCell>78%</TableCell>
                    <TableCell>¥35,360</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">赵教练</TableCell>
                    <TableCell>120</TableCell>
                    <TableCell>96</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={80} className="w-[60px]" />
                        <span>80%</span>
                      </div>
                    </TableCell>
                    <TableCell>12人</TableCell>
                    <TableCell>75%</TableCell>
                    <TableCell>¥23,040</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">刘教练</TableCell>
                    <TableCell>120</TableCell>
                    <TableCell>84</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={70} className="w-[60px]" />
                        <span>70%</span>
                      </div>
                    </TableCell>
                    <TableCell>10人</TableCell>
                    <TableCell>67%</TableCell>
                    <TableCell>¥16,800</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看详细数据
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>教练工作负荷</CardTitle>
                <CardDescription>教练工作负荷分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是教练工作负荷图表 */}
                  <div className="space-y-8">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-green-500"></div>
                          <span>高负荷 ({">"}90%)</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>2人</span>
                          <span className="text-muted-foreground">20%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "20%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                          <span>中负荷 (70%-90%)</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>5人</span>
                          <span className="text-muted-foreground">50%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-blue-500" style={{ width: "50%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                          <span>低负荷 (50%-70%)</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>2人</span>
                          <span className="text-muted-foreground">20%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-yellow-500" style={{ width: "20%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-red-500"></div>
                          <span>极低负荷 ({"<"}50%)</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>1人</span>
                          <span className="text-muted-foreground">10%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-red-500" style={{ width: "10%" }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>教练效率对比</CardTitle>
                <CardDescription>教练效率和收入贡献对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是教练效率对比图表 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>张教练</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">效率指数: 95</span>
                        <Badge variant="outline" className="bg-green-50">
                          高
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>李教练</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">效率指数: 88</span>
                        <Badge variant="outline" className="bg-green-50">
                          高
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>王教练</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">效率指数: 82</span>
                        <Badge variant="outline" className="bg-blue-50">
                          中
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>赵教练</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">效率指数: 75</span>
                        <Badge variant="outline" className="bg-blue-50">
                          中
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>刘教练</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">效率指数: 65</span>
                        <Badge variant="outline" className="bg-yellow-50">
                          低
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="equipment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>设备使用效率分析</CardTitle>
              <CardDescription>各类设备使用情况分析</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>设备类型</TableHead>
                    <TableHead>设备数量</TableHead>
                    <TableHead>平均使用率</TableHead>
                    <TableHead>高峰期使用率</TableHead>
                    <TableHead>低谷期使用率</TableHead>
                    <TableHead>维护频率</TableHead>
                    <TableHead>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">瑜伽垫</TableCell>
                    <TableCell>100</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={85} className="w-[60px]" />
                        <span>85%</span>
                      </div>
                    </TableCell>
                    <TableCell>95%</TableCell>
                    <TableCell>45%</TableCell>
                    <TableCell>每周</TableCell>
                    <TableCell>
                      <Badge className="bg-green-50 text-green-700">良好</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">瑜伽砖</TableCell>
                    <TableCell>80</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={75} className="w-[60px]" />
                        <span>75%</span>
                      </div>
                    </TableCell>
                    <TableCell>85%</TableCell>
                    <TableCell>40%</TableCell>
                    <TableCell>每月</TableCell>
                    <TableCell>
                      <Badge className="bg-green-50 text-green-700">良好</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">瑜伽绳</TableCell>
                    <TableCell>60</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={65} className="w-[60px]" />
                        <span>65%</span>
                      </div>
                    </TableCell>
                    <TableCell>75%</TableCell>
                    <TableCell>35%</TableCell>
                    <TableCell>每月</TableCell>
                    <TableCell>
                      <Badge className="bg-green-50 text-green-700">良好</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">瑜伽球</TableCell>
                    <TableCell>40</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={55} className="w-[60px]" />
                        <span>55%</span>
                      </div>
                    </TableCell>
                    <TableCell>70%</TableCell>
                    <TableCell>30%</TableCell>
                    <TableCell>每月</TableCell>
                    <TableCell>
                      <Badge className="bg-yellow-50 text-yellow-700">一般</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">空中瑜伽吊床</TableCell>
                    <TableCell>20</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Progress value={80} className="w-[60px]" />
                        <span>80%</span>
                      </div>
                    </TableCell>
                    <TableCell>90%</TableCell>
                    <TableCell>50%</TableCell>
                    <TableCell>每周</TableCell>
                    <TableCell>
                      <Badge className="bg-green-50 text-green-700">良好</Badge>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看详细数据
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>设备使用高峰期</CardTitle>
                <CardDescription>不同时段设备使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是设备使用高峰期图表 */}
                  <div className="flex h-[300px] items-end gap-2">
                    {Array.from({ length: 24 }).map((_, i) => {
                      const height = i >= 8 && i <= 22 ? 20 + Math.random() * 80 : Math.random() * 20
                      return (
                        <div key={i} className="relative flex-1">
                          <div className="bg-primary rounded-t-md w-full" style={{ height: `${height}%` }}></div>
                          {i % 3 === 0 && (
                            <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                              {i}:00
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>设备维护情况</CardTitle>
                <CardDescription>设备维护和故障情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是设备维护情况图表 */}
                  <div className="space-y-8">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-green-500"></div>
                          <span>正常使用</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>285件</span>
                          <span className="text-muted-foreground">95%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "95%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                          <span>需要维护</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>12件</span>
                          <span className="text-muted-foreground">4%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-yellow-500" style={{ width: "4%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-red-500"></div>
                          <span>故障</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>3件</span>
                          <span className="text-muted-foreground">1%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-red-500" style={{ width: "1%" }}></div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-8">
                    <h3 className="text-sm font-medium mb-4">最近维护记录</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>瑜伽垫清洁</span>
                        <span className="text-muted-foreground">2天前</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>空中瑜伽吊床检查</span>
                        <span className="text-muted-foreground">3天前</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>瑜伽球气压检查</span>
                        <span className="text-muted-foreground">1周前</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="satisfaction" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>客户满意度分析</CardTitle>
              <CardDescription>客户满意度评价分布</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-5">
                <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg">
                  <div className="text-3xl font-bold">4.8</div>
                  <div className="flex items-center mt-2">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${i < 4 ? "text-yellow-500 fill-yellow-500" : i === 4 ? "text-yellow-500 fill-yellow-500" : "text-muted-foreground"}`}
                      />
                    ))}
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">总体评分</p>
                </div>
                <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg">
                  <div className="text-3xl font-bold">4.9</div>
                  <p className="text-sm text-muted-foreground mt-2">教练评分</p>
                </div>
                <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg">
                  <div className="text-3xl font-bold">4.7</div>
                  <p className="text-sm text-muted-foreground mt-2">课程评分</p>
                </div>
                <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg">
                  <div className="text-3xl font-bold">4.6</div>
                  <p className="text-sm text-muted-foreground mt-2">环境评分</p>
                </div>
                <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg">
                  <div className="text-3xl font-bold">4.8</div>
                  <p className="text-sm text-muted-foreground mt-2">服务评分</p>
                </div>
              </div>

              <div className="mt-8 space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                        ))}
                      </div>
                      <span>5星</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span>420条</span>
                      <span className="text-muted-foreground">84%</span>
                    </div>
                  </div>
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div className="h-2 rounded-full bg-green-500" style={{ width: "84%" }}></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {Array.from({ length: 4 }).map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                        ))}
                        <Star className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <span>4星</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span>65条</span>
                      <span className="text-muted-foreground">13%</span>
                    </div>
                  </div>
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div className="h-2 rounded-full bg-blue-500" style={{ width: "13%" }}></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {Array.from({ length: 3 }).map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                        ))}
                        {Array.from({ length: 2 }).map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-muted-foreground" />
                        ))}
                      </div>
                      <span>3星</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span>10条</span>
                      <span className="text-muted-foreground">2%</span>
                    </div>
                  </div>
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div className="h-2 rounded-full bg-yellow-500" style={{ width: "2%" }}></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {Array.from({ length: 2 }).map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                        ))}
                        {Array.from({ length: 3 }).map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-muted-foreground" />
                        ))}
                      </div>
                      <span>2星</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span>3条</span>
                      <span className="text-muted-foreground">0.6%</span>
                    </div>
                  </div>
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div className="h-2 rounded-full bg-orange-500" style={{ width: "0.6%" }}></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                        {Array.from({ length: 4 }).map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-muted-foreground" />
                        ))}
                      </div>
                      <span>1星</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <span>2条</span>
                      <span className="text-muted-foreground">0.4%</span>
                    </div>
                  </div>
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div className="h-2 rounded-full bg-red-500" style={{ width: "0.4%" }}></div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看详细评价
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>满意度趋势</CardTitle>
                <CardDescription>客户满意度随时间变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是满意度趋势图表 */}
                  <div className="flex h-[300px] items-end gap-2">
                    {Array.from({ length: 12 }).map((_, i) => {
                      const height = 80 + Math.random() * 20
                      return (
                        <div key={i} className="relative flex-1">
                          <div className="bg-primary rounded-t-md w-full" style={{ height: `${height}%` }}></div>
                          <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                            {i + 1}月
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>客户反馈热点</CardTitle>
                <CardDescription>客户反馈中的热点问题</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是客户反馈热点图表 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>教练专业度</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">提及率: 42%</span>
                        <Badge variant="outline" className="bg-green-50">
                          正面
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>课程内容</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">提及率: 38%</span>
                        <Badge variant="outline" className="bg-green-50">
                          正面
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>环境舒适度</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">提及率: 35%</span>
                        <Badge variant="outline" className="bg-green-50">
                          正面
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>设备质量</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">提及率: 28%</span>
                        <Badge variant="outline" className="bg-green-50">
                          正面
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>预约便捷性</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">提及率: 25%</span>
                        <Badge variant="outline" className="bg-blue-50">
                          中性
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>价格合理性</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">提及率: 22%</span>
                        <Badge variant="outline" className="bg-yellow-50">
                          负面
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>停车便利性</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">提及率: 18%</span>
                        <Badge variant="outline" className="bg-yellow-50">
                          负面
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

