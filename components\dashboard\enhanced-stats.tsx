"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useState } from "react"
import {
  Calendar,
  Users,
  DollarSign,
  Percent,
  ArrowUpRight,
  ArrowDownRight,
  TrendingUp,
  Clock,
  Award,
  Star
} from "lucide-react"

// 模拟数据
const statsData = {
  today: {
    bookings: { value: 128, change: 15.8, isPositive: true, target: 150, progress: 85, color: "bg-blue-500" },
    activeMembers: { value: 573, change: 8.2, isPositive: true, target: 600, progress: 95, color: "bg-green-500" },
    revenue: { value: "¥12,450", change: 20.1, isPositive: true, target: "¥15,000", progress: 83, color: "bg-amber-500" },
    renewalRate: { value: "89%", change: -2.3, isPositive: false, target: "95%", progress: 89, color: "bg-purple-500" },
    attendance: { value: "92%", change: 3.5, isPositive: true, target: "95%", progress: 92, color: "bg-pink-500" },
    satisfaction: { value: "4.8", change: 0.2, isPositive: true, target: "5.0", progress: 96, color: "bg-indigo-500" },
  },
  week: {
    bookings: { value: 856, change: 12.3, isPositive: true, target: 1000, progress: 85, color: "bg-blue-500" },
    activeMembers: { value: 612, change: 5.7, isPositive: true, target: 650, progress: 94, color: "bg-green-500" },
    revenue: { value: "¥78,350", change: 15.2, isPositive: true, target: "¥85,000", progress: 92, color: "bg-amber-500" },
    renewalRate: { value: "91%", change: 1.5, isPositive: true, target: "95%", progress: 91, color: "bg-purple-500" },
    attendance: { value: "94%", change: 2.1, isPositive: true, target: "95%", progress: 94, color: "bg-pink-500" },
    satisfaction: { value: "4.7", change: 0.1, isPositive: true, target: "5.0", progress: 94, color: "bg-indigo-500" },
  },
  month: {
    bookings: { value: 3245, change: 8.5, isPositive: true, target: 3500, progress: 92, color: "bg-blue-500" },
    activeMembers: { value: 685, change: 10.2, isPositive: true, target: 700, progress: 97, color: "bg-green-500" },
    revenue: { value: "¥325,780", change: 12.8, isPositive: true, target: "¥350,000", progress: 93, color: "bg-amber-500" },
    renewalRate: { value: "93%", change: 2.1, isPositive: true, target: "95%", progress: 93, color: "bg-purple-500" },
    attendance: { value: "95%", change: 1.8, isPositive: true, target: "95%", progress: 100, color: "bg-pink-500" },
    satisfaction: { value: "4.9", change: 0.3, isPositive: true, target: "5.0", progress: 98, color: "bg-indigo-500" },
  },
}

export function EnhancedStats() {
  const [timeRange, setTimeRange] = useState("today")

  // 获取当前时间范围的数据
  const currentStats = statsData[timeRange as keyof typeof statsData]

  // 定义统计卡片数据
  const statCards = [
    {
      title: "预约数量",
      description: `总计${currentStats.bookings.value}人次`,
      value: currentStats.bookings.value,
      change: currentStats.bookings.change,
      isPositive: currentStats.bookings.isPositive,
      target: currentStats.bookings.target,
      progress: currentStats.bookings.progress,
      color: currentStats.bookings.color,
      icon: <Calendar className="h-5 w-5 text-white" />
    },
    {
      title: "活跃会员",
      description: `总计${currentStats.activeMembers.value}人`,
      value: currentStats.activeMembers.value,
      change: currentStats.activeMembers.change,
      isPositive: currentStats.activeMembers.isPositive,
      target: currentStats.activeMembers.target,
      progress: currentStats.activeMembers.progress,
      color: currentStats.activeMembers.color,
      icon: <Users className="h-5 w-5 text-white" />
    },
    {
      title: "收入",
      description: `总计${currentStats.revenue.value}`,
      value: currentStats.revenue.value,
      change: currentStats.revenue.change,
      isPositive: currentStats.revenue.isPositive,
      target: currentStats.revenue.target,
      progress: currentStats.revenue.progress,
      color: currentStats.revenue.color,
      icon: <DollarSign className="h-5 w-5 text-white" />
    },
    {
      title: "续费率",
      description: `目标${currentStats.renewalRate.target}`,
      value: currentStats.renewalRate.value,
      change: currentStats.renewalRate.change,
      isPositive: currentStats.renewalRate.isPositive,
      target: currentStats.renewalRate.target,
      progress: currentStats.renewalRate.progress,
      color: currentStats.renewalRate.color,
      icon: <Percent className="h-5 w-5 text-white" />
    },
    {
      title: "出勤率",
      description: `目标${currentStats.attendance.target}`,
      value: currentStats.attendance.value,
      change: currentStats.attendance.change,
      isPositive: currentStats.attendance.isPositive,
      target: currentStats.attendance.target,
      progress: currentStats.attendance.progress,
      color: currentStats.attendance.color,
      icon: <Clock className="h-5 w-5 text-white" />
    },
    {
      title: "满意度",
      description: `目标${currentStats.satisfaction.target}`,
      value: currentStats.satisfaction.value,
      change: currentStats.satisfaction.change,
      isPositive: currentStats.satisfaction.isPositive,
      target: currentStats.satisfaction.target,
      progress: currentStats.satisfaction.progress,
      color: currentStats.satisfaction.color,
      icon: <Star className="h-5 w-5 text-white" />
    }
  ]

  return (
    <Card className="border-none shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">业务数据概览</CardTitle>
          <Tabs defaultValue="today" value={timeRange} onValueChange={setTimeRange} className="w-auto">
            <TabsList className="h-8">
              <TabsTrigger value="today" className="text-xs px-3">今日</TabsTrigger>
              <TabsTrigger value="week" className="text-xs px-3">本周</TabsTrigger>
              <TabsTrigger value="month" className="text-xs px-3">本月</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {statCards.map((stat, index) => (
            <div key={index} className="flex items-start space-x-4 p-4 rounded-lg border hover:shadow-sm transition-all">
              <div className={`p-2 rounded-full ${stat.color}`}>
                {stat.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="font-medium text-sm">{stat.title}</div>
                  <Badge variant="outline" className="text-[10px] h-5 px-2">
                    目标: {stat.target}
                  </Badge>
                </div>
                <div className="text-2xl font-bold mt-1">{stat.value}</div>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-muted-foreground">
                    <span className={stat.isPositive ? "text-green-500 inline-flex items-center" : "text-red-500 inline-flex items-center"}>
                      {stat.isPositive ?
                        <ArrowUpRight className="h-3 w-3 mr-1" /> :
                        <ArrowDownRight className="h-3 w-3 mr-1" />
                      }
                      {stat.isPositive ? "+" : ""}{stat.change}%
                    </span>{" "}
                    相比{timeRange === "today" ? "昨日" : timeRange === "week" ? "上周" : "上月"}
                  </p>
                </div>
                <Progress value={stat.progress} className="mt-2 h-1" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
