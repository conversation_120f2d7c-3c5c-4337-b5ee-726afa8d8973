"use client"

import { useState, useRef, useEffect } from "react"
import { DndProvider, useDrag, useDrop } from "react-dnd"
import { HTML5Backend } from "react-dnd-html5-backend"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import {
  Home,
  Calendar,
  CreditCard,
  User,
  Layers,
  Menu,
  Plus,
  Grip,
  X,
  Edit,
  ChevronRight,
  Star
} from "lucide-react"

// 定义拖拽类型
const ItemTypes = {
  FEATURE: 'feature',
  BANNER: 'banner',
  COURSE: 'course',
}

// 可拖拽的功能图标组件
const DraggableFeature = ({ id, index, feature, moveFeature }) => {
  const ref = useRef(null)
  
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.FEATURE,
    item: { id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })
  
  const [, drop] = useDrop({
    accept: ItemTypes.FEATURE,
    hover: (item, monitor) => {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index
      
      if (dragIndex === hoverIndex) {
        return
      }
      
      moveFeature(dragIndex, hoverIndex)
      item.index = hoverIndex
    },
  })
  
  drag(drop(ref))
  
  return (
    <div 
      ref={ref}
      className={cn(
        "flex flex-col items-center gap-1 cursor-move",
        isDragging ? "opacity-50" : ""
      )}
    >
      <div 
        className="w-12 h-12 rounded-lg flex items-center justify-center"
        style={{ backgroundColor: `${feature.color}20` }}
      >
        <div 
          className="w-8 h-8 rounded-md flex items-center justify-center text-white"
          style={{ backgroundColor: feature.color }}
        >
          {feature.icon === "venue-intro" && <Home size={18} />}
          {feature.icon === "trainer" && <User size={18} />}
          {feature.icon === "checkin" && <CreditCard size={18} />}
          {feature.icon === "membership" && <CreditCard size={18} />}
          {feature.icon === "course" && <Layers size={18} />}
        </div>
      </div>
      <span className="text-xs">{feature.name}</span>
    </div>
  )
}

// 可拖拽的课程卡片组件
const DraggableCourse = ({ id, index, course, moveCourse }) => {
  const ref = useRef(null)
  
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.COURSE,
    item: { id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })
  
  const [, drop] = useDrop({
    accept: ItemTypes.COURSE,
    hover: (item, monitor) => {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index
      
      if (dragIndex === hoverIndex) {
        return
      }
      
      moveCourse(dragIndex, hoverIndex)
      item.index = hoverIndex
    },
  })
  
  drag(drop(ref))
  
  return (
    <div 
      ref={ref}
      className={cn(
        "bg-white rounded-lg p-3 flex items-center cursor-move",
        isDragging ? "opacity-50" : ""
      )}
    >
      <div className="absolute top-2 right-2 text-gray-400">
        <Grip size={12} />
      </div>
      <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-200 mr-3">
        {course.trainer.avatar ? (
          <img 
            src={course.trainer.avatar} 
            alt={course.trainer.name} 
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            {course.trainer.name[0]}
          </div>
        )}
      </div>
      <div className="flex-1">
        <div className="text-xs text-gray-500">
          可预约数 {course.availableSpots}/{course.availableSpots + course.bookedSpots} | 还可预约{course.availableSpots}人
        </div>
        <div className="font-medium">{course.title} ({course.location})</div>
        <div className="flex items-center text-xs text-yellow-500">
          难度系数：{Array(course.rating).fill(0).map((_, i) => (
            <Star key={i} size={12} fill="currentColor" />
          ))}
        </div>
      </div>
      <div className="text-right">
        <div className="text-lg font-bold">{course.startTime}</div>
        <div className="text-xs text-gray-500">{course.endTime}结束</div>
        <button className="mt-1 bg-orange-500 text-white text-xs px-3 py-1 rounded-full">
          立即预约
        </button>
      </div>
    </div>
  )
}

// 交互式预览组件
export function InteractivePreview({ settings, updateSettings }) {
  // 功能图标状态
  const [features, setFeatures] = useState([
    { id: 1, name: "场馆介绍", icon: "venue-intro", color: "#4EADFF" },
    { id: 2, name: "场馆教练", icon: "trainer", color: "#FF9966" },
    { id: 3, name: "每日打卡", icon: "checkin", color: "#5B8DEF" },
    { id: 4, name: "在线售卡", icon: "membership", color: "#FF9966" },
    { id: 5, name: "在线课程", icon: "course", color: "#4EADFF" },
  ])
  
  // 课程状态
  const [courses, setCourses] = useState([
    {
      id: 1,
      title: "瑜伽课训练营",
      location: "文澜大教室",
      trainer: {
        name: "Alex",
        avatar: "https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
      },
      date: "10/15",
      availableSpots: 10,
      bookedSpots: 10,
      rating: 5,
      startTime: "14:30",
      endTime: "15:30"
    },
    {
      id: 2,
      title: "瑜伽课训练营",
      location: "文澜大教室",
      trainer: {
        name: "Sarah",
        avatar: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
      },
      date: "10/15",
      availableSpots: 10,
      bookedSpots: 10,
      rating: 5,
      startTime: "14:30",
      endTime: "15:30"
    },
    {
      id: 3,
      title: "瑜伽课训练营",
      location: "文澜大教室",
      trainer: {
        name: "Mike",
        avatar: "https://images.unsplash.com/photo-1566492031773-4f4e44671857?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
      },
      date: "10/15",
      availableSpots: 10,
      bookedSpots: 10,
      rating: 5,
      startTime: "14:30",
      endTime: "15:30"
    }
  ])
  
  // 公告状态
  const [notice, setNotice] = useState("场馆公告：2025年国庆放假通知请点击！！！")
  
  // 移动功能图标
  const moveFeature = (dragIndex, hoverIndex) => {
    const draggedFeature = features[dragIndex]
    const newFeatures = [...features]
    newFeatures.splice(dragIndex, 1)
    newFeatures.splice(hoverIndex, 0, draggedFeature)
    setFeatures(newFeatures)
  }
  
  // 移动课程
  const moveCourse = (dragIndex, hoverIndex) => {
    const draggedCourse = courses[dragIndex]
    const newCourses = [...courses]
    newCourses.splice(dragIndex, 1)
    newCourses.splice(hoverIndex, 0, draggedCourse)
    setCourses(newCourses)
  }
  
  // 编辑公告
  const [isEditingNotice, setIsEditingNotice] = useState(false)
  const [editedNotice, setEditedNotice] = useState(notice)
  
  const saveNotice = () => {
    setNotice(editedNotice)
    setIsEditingNotice(false)
  }
  
  return (
    <DndProvider backend={HTML5Backend}>
      <Card className="h-full">
        <CardContent className="p-0 h-full">
          <div className="relative w-full h-full bg-white overflow-hidden">
            <div className="h-full overflow-y-auto">
              {/* 状态栏 - 模拟手机状态栏 */}
              <div className="bg-white px-4 py-2 flex justify-between items-center text-xs text-gray-700">
                <span>12:00</span>
                <div className="flex items-center gap-1">
                  <span>●●●</span>
                  <span>📶</span>
                  <span>🔋</span>
                </div>
              </div>
              
              {/* 头部横幅 */}
              <div className="relative w-full h-48">
                <div className="absolute inset-0 bg-black/20 z-10 flex items-center justify-center">
                  <div className="text-white text-center">
                    <h1 className="text-xl font-bold">静心瑜伽美学生活馆 ✨</h1>
                  </div>
                </div>
                <div className="absolute top-4 right-4 z-20 flex gap-2">
                  <div className="w-8 h-8 bg-white/80 rounded-full flex items-center justify-center">
                    <Menu size={16} />
                  </div>
                  <div className="w-8 h-8 bg-white/80 rounded-full flex items-center justify-center">
                    <Plus size={16} />
                  </div>
                </div>
                <div className="w-full h-full overflow-hidden">
                  <div 
                    className="w-full h-full bg-cover bg-center"
                    style={{ 
                      backgroundImage: `url('https://images.unsplash.com/photo-1545205597-3d9d02c29597?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80')` 
                    }}
                  ></div>
                </div>
              </div>
              
              {/* 功能导航 - 可拖拽 */}
              <div className="bg-white px-4 py-5 flex justify-between relative">
                {features.map((feature, index) => (
                  <DraggableFeature
                    key={feature.id}
                    id={feature.id}
                    index={index}
                    feature={feature}
                    moveFeature={moveFeature}
                  />
                ))}
                <div className="absolute -top-3 right-2 bg-gray-100 text-xs px-2 py-1 rounded text-gray-500">
                  拖动调整顺序
                </div>
              </div>
              
              {/* 活动横幅 */}
              <div className="mx-4 my-3 rounded-xl overflow-hidden bg-indigo-600 text-white relative h-24">
                <div className="absolute inset-0 flex items-center px-6">
                  <div className="flex-1">
                    <div className="text-lg font-bold">
                      🎵 喵出你的2022
                    </div>
                    <div className="text-lg font-bold">
                      收藏的第一首歌 ~
                    </div>
                    <div className="mt-1 text-xs bg-white/20 inline-block px-2 py-0.5 rounded-full">
                      参与赢取全年免费音乐会员
                    </div>
                  </div>
                  <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                      <div className="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center">
                        <span className="text-xl">▶</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 公告栏 - 可编辑 */}
              <div className="mx-4 flex items-center bg-white rounded-md px-3 py-2 text-sm relative">
                <span className="text-orange-500 mr-2">📢</span>
                {isEditingNotice ? (
                  <div className="flex-1 flex items-center">
                    <Input 
                      value={editedNotice}
                      onChange={(e) => setEditedNotice(e.target.value)}
                      className="flex-1 h-7 text-sm"
                    />
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      className="h-7 px-2 ml-1"
                      onClick={saveNotice}
                    >
                      保存
                    </Button>
                  </div>
                ) : (
                  <>
                    <div className="flex-1 truncate">{notice}</div>
                    <div className="flex items-center">
                      <span className="text-gray-400 mr-2">查看 &gt;</span>
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        className="h-6 w-6 p-0"
                        onClick={() => setIsEditingNotice(true)}
                      >
                        <Edit size={14} />
                      </Button>
                    </div>
                  </>
                )}
              </div>
              
              {/* 团课推荐 - 可拖拽 */}
              <div className="mt-4 px-4">
                <div className="flex justify-between items-center mb-3">
                  <h2 className="text-lg font-bold">团课推荐</h2>
                  <span className="text-sm text-gray-500 flex items-center">
                    更多 <ChevronRight size={16} />
                  </span>
                </div>
                
                <div className="space-y-3 relative">
                  {courses.map((course, index) => (
                    <DraggableCourse
                      key={course.id}
                      id={course.id}
                      index={index}
                      course={course}
                      moveCourse={moveCourse}
                    />
                  ))}
                  <div className="absolute -top-8 right-2 bg-gray-100 text-xs px-2 py-1 rounded text-gray-500">
                    拖动调整顺序
                  </div>
                </div>
              </div>
              
              {/* 私教推荐 */}
              <div className="mt-4 px-4 pb-20">
                <div className="flex justify-between items-center mb-3">
                  <h2 className="text-lg font-bold">私教推荐</h2>
                  <span className="text-sm text-gray-500 flex items-center">
                    更多 <ChevronRight size={16} />
                  </span>
                </div>
                
                <div className="grid grid-cols-4 gap-2">
                  {[1, 2, 3, 4].map((id) => (
                    <div key={id} className="bg-white rounded-lg overflow-hidden">
                      <div className="h-20 bg-gray-200 flex items-center justify-center text-gray-400">
                        图片
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* 底部导航 */}
              <div className="fixed bottom-0 left-0 right-0 bg-white border-t flex justify-around py-2">
                <div className="flex flex-col items-center text-orange-500">
                  <Home size={20} />
                  <span className="text-xs">首页</span>
                </div>
                <div className="flex flex-col items-center text-gray-400">
                  <Calendar size={20} />
                  <span className="text-xs">约课</span>
                </div>
                <div className="flex flex-col items-center text-gray-400">
                  <User size={20} />
                  <span className="text-xs">我的</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </DndProvider>
  )
}
