// 测试注册和登录完整流程的脚本
// 使用Node.js 18+的内置fetch API

async function testRegisterAndLogin() {
  try {
    console.log('🧪 开始测试注册和登录完整流程...');
    
    // 生成随机测试数据
    const timestamp = Date.now();
    const testData = {
      companyName: `测试公司${timestamp}`,
      businessLicense: `${timestamp}*********`,
      username: `测试用户${timestamp}`,
      email: `test${timestamp}@example.com`,
      phone: `138${timestamp.toString().slice(-8)}`, // 生成11位手机号
      password: "TestPassword123",
      confirmPassword: "TestPassword123",
      agreeTerms: true
    };
    
    console.log('\n📤 第一步：测试注册...');
    console.log('注册数据:', JSON.stringify(testData, null, 2));
    
    const registerResponse = await fetch('http://localhost:3001/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });
    
    console.log('📥 注册响应状态:', registerResponse.status);
    
    const registerResult = await registerResponse.json();
    console.log('📥 注册响应数据:', JSON.stringify(registerResult, null, 2));
    
    if (!registerResult.success) {
      console.log('❌ 注册失败:', registerResult.error);
      return;
    }
    
    console.log('✅ 注册成功!');
    console.log(`租户ID: ${registerResult.tenantId}`);
    console.log(`员工ID: ${registerResult.employeeId}`);
    
    // 等待一秒确保数据写入完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('\n📤 第二步：测试使用默认密码123456登录...');
    const loginData = {
      username: testData.phone, // 使用手机号登录
      password: "123456" // 使用默认密码
    };
    
    console.log('登录数据:', JSON.stringify(loginData, null, 2));
    
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData)
    });
    
    console.log('📥 登录响应状态:', loginResponse.status);
    
    const loginResult = await loginResponse.json();
    console.log('📥 登录响应数据:', JSON.stringify(loginResult, null, 2));
    
    if (loginResult.code === 200) {
      console.log('✅ 使用默认密码登录成功!');
      console.log(`用户: ${loginResult.data.user.nickname}`);
      console.log(`公司: ${loginResult.data.user.companyName}`);
      console.log(`角色: ${loginResult.data.user.role}`);
    } else if (loginResult.code === 403) {
      console.log('⚠️ 账户需要审核:', loginResult.message);
      
      // 激活账户后再次尝试登录
      console.log('\n📤 第三步：激活账户后重新登录...');
      
      // 这里可以调用激活脚本或直接更新数据库
      // 为了简化，我们假设账户已经激活
      console.log('账户已激活，重新尝试登录...');
      
      const retryLoginResponse = await fetch('http://localhost:3001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData)
      });
      
      const retryLoginResult = await retryLoginResponse.json();
      console.log('📥 重试登录响应:', JSON.stringify(retryLoginResult, null, 2));
      
      if (retryLoginResult.code === 200) {
        console.log('✅ 激活后登录成功!');
      } else {
        console.log('❌ 激活后登录仍然失败:', retryLoginResult.message);
      }
    } else {
      console.log('❌ 登录失败:', loginResult.message);
    }
    
    console.log('\n🎉 测试完成!');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testRegisterAndLogin();
