-- 会员卡正确的高级设置表（基于页面真实字段）

-- 1. 会员卡高级设置表
CREATE TABLE IF NOT EXISTS member_card_advanced_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 卡相关设置
  vacation_option ENUM('unlimited', 'limited', 'none') DEFAULT 'unlimited' COMMENT '清假选项：不限制/有限制/不允许',
  
  -- 开卡设置
  auto_activate_days INT DEFAULT 120 COMMENT '发卡后多少天内未开卡则自动开卡',
  auto_activate_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用自动开卡',
  
  -- 预约限制设置
  max_people_per_class INT DEFAULT 1 COMMENT '单节课可约人数上限',
  daily_booking_limit INT DEFAULT 3 COMMENT '每日可约次数上限',
  weekly_booking_limit INT DEFAULT 4 COMMENT '每周可约次数上限',
  weekly_calculation_type ENUM('natural', 'rolling') DEFAULT 'natural' COMMENT '每周计算方式：自然周/滚动周',
  monthly_booking_limit INT DEFAULT 5 COMMENT '每月可约次数上限', 
  monthly_calculation_type ENUM('natural', 'rolling') DEFAULT 'natural' COMMENT '每月计算方式：自然月/滚动月',
  
  -- 预约天数设置
  advance_booking_days INT COMMENT '可预约天数，NULL表示不限制，0表示当天',
  advance_booking_unlimited BOOLEAN DEFAULT TRUE COMMENT '是否不限制预约天数',
  
  -- 可用时间设置
  custom_time_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用自定义时段',
  available_time_slots JSON COMMENT '可用时间段配置',
  
  -- 用卡人相关设置
  cardholder_settings_enabled BOOLEAN DEFAULT FALSE COMMENT '是否展开用卡人设置',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_tenant_id (tenant_id),
  
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Card Advanced Settings Table';

-- 2. 会员卡价格设置表（价格设置标签页）
CREATE TABLE IF NOT EXISTS member_card_pricing_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 折扣设置
  enable_discount BOOLEAN DEFAULT FALSE COMMENT '启用折扣',
  discount_percentage DECIMAL(5,2) COMMENT '折扣百分比',
  
  -- 促销设置
  enable_promotion BOOLEAN DEFAULT FALSE COMMENT '启用促销',
  promotion_type ENUM('new', 'renewal', 'group', 'holiday') COMMENT '促销类型',
  
  -- 价格说明
  price_description TEXT COMMENT '价格说明',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_tenant_id (tenant_id),
  
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Card Pricing Settings Table';

-- 3. 会员卡课程关联设置表（关联课程标签页）
CREATE TABLE IF NOT EXISTS member_card_course_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 消耗规则设置
  consumption_rule ENUM('average', 'fixed', 'custom') DEFAULT 'average' COMMENT '消耗规则',
  
  -- 赠送设置
  gift_class_count INT DEFAULT 0 COMMENT '赠送课时数',
  gift_value_coefficient DECIMAL(5,2) DEFAULT 1.0 COMMENT '赠送课程价值系数',
  
  -- 适用课程（JSON存储选中的课程ID列表）
  applicable_courses JSON COMMENT '适用课程列表',
  
  -- 课程消耗设置（JSON存储每个课程的消耗配置）
  course_consumption_settings JSON COMMENT '课程消耗设置',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_tenant_id (tenant_id),
  
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Card Course Settings Table';

-- 插入示例数据
INSERT INTO member_card_advanced_settings (card_type_id, tenant_id, vacation_option, auto_activate_days, max_people_per_class, daily_booking_limit, weekly_booking_limit, monthly_booking_limit) VALUES
(1, 2, 'unlimited', 120, 1, 3, 4, 5),
(2, 2, 'unlimited', 120, 1, 3, 4, 5);

INSERT INTO member_card_pricing_settings (card_type_id, tenant_id, enable_discount, discount_percentage, enable_promotion, promotion_type, price_description) VALUES
(1, 2, FALSE, NULL, FALSE, NULL, '年卡享受最优惠价格，包含所有课程'),
(2, 2, TRUE, 15.00, TRUE, 'new', '新会员专享85折优惠');

INSERT INTO member_card_course_settings (card_type_id, tenant_id, consumption_rule, gift_class_count, gift_value_coefficient, applicable_courses) VALUES
(1, 2, 'average', 2, 1.0, '["c1","c2","c3","c4","c5","c6"]'),
(2, 2, 'fixed', 1, 1.0, '["c1","c4","c5","c6"]');
