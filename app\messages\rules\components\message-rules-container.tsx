import React from 'react'
import { MessageRuleGroup } from './message-rule-group'

interface MessageRule {
  id: number
  name: string
  template: string
  smsEnabled: boolean
  appEnabled: boolean
  emailEnabled?: boolean
  sendRule?: string
}

interface MessageRuleGroup {
  group: string
  rules: MessageRule[]
  serviceType?: string // 用于标识该组规则属于哪种增值服务
}

interface MessageRulesContainerProps {
  messageGroups: MessageRuleGroup[]
  enabledServices?: string[] // 已启用的增值服务列表
  onToggleSms: (ruleId: number, currentStatus: boolean) => void
  onToggleApp: (ruleId: number, currentStatus: boolean) => void
  onToggleEmail?: (ruleId: number, currentStatus: boolean) => void
  onSetRule?: (rule: MessageRule) => void
}

export function MessageRulesContainer({
  messageGroups,
  enabledServices = [],
  onToggleSms,
  onToggleApp,
  onToggleEmail,
  onSetRule
}: MessageRulesContainerProps) {
  // 过滤出应该显示的消息组
  // 1. 没有serviceType的组（基础功能）总是显示
  // 2. serviceType在enabledServices中的组才显示
  const visibleGroups = messageGroups.filter(group =>
    !group.serviceType || enabledServices.includes(group.serviceType)
  )

  return (
    <div className="space-y-8 py-4">
      {visibleGroups.map((group) => (
        <MessageRuleGroup
          key={group.group}
          group={group}
          onToggleSms={onToggleSms}
          onToggleApp={onToggleApp}
          onToggleEmail={onToggleEmail}
          onSetRule={onSetRule}
        />
      ))}
    </div>
  )
}
