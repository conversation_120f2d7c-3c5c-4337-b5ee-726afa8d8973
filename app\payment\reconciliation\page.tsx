"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { AlertCircle, CalendarIcon, Download, Eye, FileText, Search } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function PaymentReconciliationPage() {
  const [date, setDate] = useState<Date>()
  const [paymentType, setPaymentType] = useState("all")
  const [selectedRecord, setSelectedRecord] = useState(null)
  const [showRecordDetails, setShowRecordDetails] = useState(false)

  const [reconciliationRecords] = useState([
    {
      id: "REC20250328",
      date: "2025-03-28",
      channel: "微信支付",
      platformCount: 125,
      platformAmount: "¥12,580.00",
      localCount: 125,
      localAmount: "¥12,580.00",
      diffCount: 0,
      diffAmount: "¥0.00",
      status: "matched",
      details: {
        matchedTransactions: 125,
        unmatchedPlatform: 0,
        unmatchedLocal: 0,
        platformFee: "¥62.90",
        settlementAmount: "¥12,517.10",
        settlementTime: "2025-03-29 10:00:00",
      },
    },
    {
      id: "REC20250327",
      date: "2025-03-27",
      channel: "支付宝",
      platformCount: 98,
      platformAmount: "¥9,860.00",
      localCount: 97,
      localAmount: "¥9,760.00",
      diffCount: 1,
      diffAmount: "¥100.00",
      status: "unmatched",
      details: {
        matchedTransactions: 97,
        unmatchedPlatform: 1,
        unmatchedLocal: 0,
        platformFee: "¥49.30",
        settlementAmount: "¥9,810.70",
        settlementTime: "2025-03-28 10:00:00",
        unmatchedDetails: [
          {
            id: "ZFB20250327123456",
            amount: "¥100.00",
            time: "2025-03-27 23:58:45",
            reason: "本地系统未记录",
          },
        ],
      },
    },
    {
      id: "REC20250326",
      date: "2025-03-26",
      channel: "微信支付",
      platformCount: 115,
      platformAmount: "¥11,500.00",
      localCount: 116,
      localAmount: "¥11,600.00",
      diffCount: 1,
      diffAmount: "-¥100.00",
      status: "unmatched",
      details: {
        matchedTransactions: 115,
        unmatchedPlatform: 0,
        unmatchedLocal: 1,
        platformFee: "¥57.50",
        settlementAmount: "¥11,442.50",
        settlementTime: "2025-03-27 10:00:00",
        unmatchedDetails: [
          {
            id: "WX20250326123456",
            amount: "¥100.00",
            time: "2025-03-26 22:30:15",
            reason: "平台未记录",
          },
        ],
      },
    },
    {
      id: "REC20250325",
      date: "2025-03-25",
      channel: "支付宝",
      platformCount: 105,
      platformAmount: "¥10,500.00",
      localCount: 105,
      localAmount: "¥10,500.00",
      diffCount: 0,
      diffAmount: "¥0.00",
      status: "matched",
      details: {
        matchedTransactions: 105,
        unmatchedPlatform: 0,
        unmatchedLocal: 0,
        platformFee: "¥52.50",
        settlementAmount: "¥10,447.50",
        settlementTime: "2025-03-26 10:00:00",
      },
    },
    {
      id: "REC20250324",
      date: "2025-03-24",
      channel: "微信支付",
      platformCount: 120,
      platformAmount: "¥12,000.00",
      localCount: 120,
      localAmount: "¥12,000.00",
      diffCount: 0,
      diffAmount: "¥0.00",
      status: "matched",
      details: {
        matchedTransactions: 120,
        unmatchedPlatform: 0,
        unmatchedLocal: 0,
        platformFee: "¥60.00",
        settlementAmount: "¥11,940.00",
        settlementTime: "2025-03-25 10:00:00",
      },
    },
  ])

  const handleViewDetails = (record) => {
    setSelectedRecord(record)
    setShowRecordDetails(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">支付对账管理</h1>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          导出对账报表
        </Button>
      </div>

      <Tabs defaultValue="daily" className="space-y-4">
        <TabsList>
          <TabsTrigger value="daily">日对账</TabsTrigger>
          <TabsTrigger value="monthly">月对账</TabsTrigger>
          <TabsTrigger value="settlement">结算记录</TabsTrigger>
        </TabsList>

        <TabsContent value="daily" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>日对账查询</CardTitle>
              <CardDescription>查询每日支付对账记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="flex flex-1 gap-4">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-[240px] justify-start text-left font-normal",
                          !date && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {date ? format(date, "yyyy-MM-dd") : "选择日期"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar mode="single" selected={date} onSelect={setDate} />
                    </PopoverContent>
                  </Popover>

                  <Select value={paymentType} onValueChange={setPaymentType}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="支付渠道" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部渠道</SelectItem>
                      <SelectItem value="wechat">微信支付</SelectItem>
                      <SelectItem value="alipay">支付宝</SelectItem>
                      <SelectItem value="unionpay">银联支付</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  生成对账单
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>对账日期</TableHead>
                    <TableHead>支付渠道</TableHead>
                    <TableHead>平台交易笔数</TableHead>
                    <TableHead>平台交易金额</TableHead>
                    <TableHead>本地交易笔数</TableHead>
                    <TableHead>本地交易金额</TableHead>
                    <TableHead>差异笔数</TableHead>
                    <TableHead>差异金额</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reconciliationRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium">{record.date}</TableCell>
                      <TableCell>{record.channel}</TableCell>
                      <TableCell>{record.platformCount}</TableCell>
                      <TableCell>{record.platformAmount}</TableCell>
                      <TableCell>{record.localCount}</TableCell>
                      <TableCell>{record.localAmount}</TableCell>
                      <TableCell>{record.diffCount}</TableCell>
                      <TableCell
                        className={cn(
                          record.diffAmount !== "¥0.00" &&
                            (record.diffAmount.startsWith("-") ? "text-red-500" : "text-orange-500"),
                        )}
                      >
                        {record.diffAmount}
                      </TableCell>
                      <TableCell>
                        <Badge variant={record.status === "matched" ? "outline" : "destructive"}>
                          {record.status === "matched" ? "已匹配" : "有差异"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" onClick={() => handleViewDetails(record)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">今日对账状态</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">已完成</div>
                <p className="text-xs text-muted-foreground">最近更新: 2025-03-28 10:15:30</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">本月对账差异</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2笔 / ¥200.00</div>
                <p className="text-xs text-muted-foreground">较上月减少 3笔</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">对账匹配率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">99.6%</div>
                <p className="text-xs text-muted-foreground">较上月提升 0.2%</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="monthly" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>月对账查询</CardTitle>
              <CardDescription>查询每月支付对账记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="flex flex-1 gap-4">
                  <Select defaultValue="202503">
                    <SelectTrigger className="w-[240px]">
                      <SelectValue placeholder="选择月份" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="202503">2025年3月</SelectItem>
                      <SelectItem value="202502">2025年2月</SelectItem>
                      <SelectItem value="202501">2025年1月</SelectItem>
                      <SelectItem value="202412">2024年12月</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select defaultValue="all">
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="支付渠道" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部渠道</SelectItem>
                      <SelectItem value="wechat">微信支付</SelectItem>
                      <SelectItem value="alipay">支付宝</SelectItem>
                      <SelectItem value="unionpay">银联支付</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  生成月度报表
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>对账月份</TableHead>
                    <TableHead>支付渠道</TableHead>
                    <TableHead>平台交易笔数</TableHead>
                    <TableHead>平台交易金额</TableHead>
                    <TableHead>本地交易笔数</TableHead>
                    <TableHead>本地交易金额</TableHead>
                    <TableHead>差异笔数</TableHead>
                    <TableHead>差异金额</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">2025年3月</TableCell>
                    <TableCell>微信支付</TableCell>
                    <TableCell>3,560</TableCell>
                    <TableCell>¥356,000.00</TableCell>
                    <TableCell>3,562</TableCell>
                    <TableCell>¥356,200.00</TableCell>
                    <TableCell>2</TableCell>
                    <TableCell className="text-red-500">-¥200.00</TableCell>
                    <TableCell>
                      <Badge variant="destructive">有差异</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">2025年3月</TableCell>
                    <TableCell>支付宝</TableCell>
                    <TableCell>2,980</TableCell>
                    <TableCell>¥298,000.00</TableCell>
                    <TableCell>2,980</TableCell>
                    <TableCell>¥298,000.00</TableCell>
                    <TableCell>0</TableCell>
                    <TableCell>¥0.00</TableCell>
                    <TableCell>
                      <Badge variant="outline">已匹配</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">2025年2月</TableCell>
                    <TableCell>微信支付</TableCell>
                    <TableCell>3,200</TableCell>
                    <TableCell>¥320,000.00</TableCell>
                    <TableCell>3,205</TableCell>
                    <TableCell>¥320,500.00</TableCell>
                    <TableCell>5</TableCell>
                    <TableCell className="text-red-500">-¥500.00</TableCell>
                    <TableCell>
                      <Badge variant="destructive">有差异</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settlement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>结算记录查询</CardTitle>
              <CardDescription>查询支付渠道结算记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="flex flex-1 gap-4">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="支付渠道" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部渠道</SelectItem>
                      <SelectItem value="wechat">微信支付</SelectItem>
                      <SelectItem value="alipay">支付宝</SelectItem>
                      <SelectItem value="unionpay">银联支付</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select defaultValue="30days">
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7days">最近7天</SelectItem>
                      <SelectItem value="30days">最近30天</SelectItem>
                      <SelectItem value="90days">最近90天</SelectItem>
                      <SelectItem value="thismonth">本月</SelectItem>
                      <SelectItem value="lastmonth">上月</SelectItem>
                      <SelectItem value="custom">自定义</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>结算批次号</TableHead>
                    <TableHead>支付渠道</TableHead>
                    <TableHead>结算日期</TableHead>
                    <TableHead>交易笔数</TableHead>
                    <TableHead>交易金额</TableHead>
                    <TableHead>手续费</TableHead>
                    <TableHead>结算金额</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">STL20250329001</TableCell>
                    <TableCell>微信支付</TableCell>
                    <TableCell>2025-03-29</TableCell>
                    <TableCell>125</TableCell>
                    <TableCell>¥12,580.00</TableCell>
                    <TableCell>¥62.90</TableCell>
                    <TableCell>¥12,517.10</TableCell>
                    <TableCell>
                      <Badge>已结算</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">STL20250328001</TableCell>
                    <TableCell>支付宝</TableCell>
                    <TableCell>2025-03-28</TableCell>
                    <TableCell>98</TableCell>
                    <TableCell>¥9,860.00</TableCell>
                    <TableCell>¥49.30</TableCell>
                    <TableCell>¥9,810.70</TableCell>
                    <TableCell>
                      <Badge>已结算</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">STL20250327001</TableCell>
                    <TableCell>微信支付</TableCell>
                    <TableCell>2025-03-27</TableCell>
                    <TableCell>115</TableCell>
                    <TableCell>¥11,500.00</TableCell>
                    <TableCell>¥57.50</TableCell>
                    <TableCell>¥11,442.50</TableCell>
                    <TableCell>
                      <Badge>已结算</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 对账详情对话框 */}
      {selectedRecord && (
        <Dialog open={showRecordDetails} onOpenChange={setShowRecordDetails}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>对账详情</DialogTitle>
              <DialogDescription>对账单号: {selectedRecord.id}</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {selectedRecord.status === "unmatched" && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>对账差异</AlertTitle>
                  <AlertDescription>该对账单存在差异，请核对交易记录</AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">对账日期</h3>
                  <p>{selectedRecord.date}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">支付渠道</h3>
                  <p>{selectedRecord.channel}</p>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium mb-2">对账汇总</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">平台交易笔数</span>
                    <span>{selectedRecord.platformCount}笔</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">平台交易金额</span>
                    <span>{selectedRecord.platformAmount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">本地交易笔数</span>
                    <span>{selectedRecord.localCount}笔</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">本地交易金额</span>
                    <span>{selectedRecord.localAmount}</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>差异笔数</span>
                    <span>{selectedRecord.diffCount}笔</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>差异金额</span>
                    <span
                      className={cn(
                        selectedRecord.diffAmount !== "¥0.00" &&
                          (selectedRecord.diffAmount.startsWith("-") ? "text-red-500" : "text-orange-500"),
                      )}
                    >
                      {selectedRecord.diffAmount}
                    </span>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium mb-2">结算信息</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">平台手续费</span>
                    <span>{selectedRecord.details.platformFee}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">结算金额</span>
                    <span>{selectedRecord.details.settlementAmount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">结算时间</span>
                    <span>{selectedRecord.details.settlementTime}</span>
                  </div>
                </div>
              </div>

              {selectedRecord.status === "unmatched" && selectedRecord.details.unmatchedDetails && (
                <div className="border-t pt-4">
                  <h3 className="font-medium mb-2">差异明细</h3>
                  <div className="space-y-4">
                    {selectedRecord.details.unmatchedDetails.map((item, index) => (
                      <div key={index} className="p-3 border rounded-md">
                        <div className="flex justify-between mb-1">
                          <span className="font-medium">交易单号</span>
                          <span>{item.id}</span>
                        </div>
                        <div className="flex justify-between mb-1">
                          <span className="text-muted-foreground">交易金额</span>
                          <span>{item.amount}</span>
                        </div>
                        <div className="flex justify-between mb-1">
                          <span className="text-muted-foreground">交易时间</span>
                          <span>{item.time}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">差异原因</span>
                          <span className="text-red-500">{item.reason}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowRecordDetails(false)}>
                  关闭
                </Button>
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  导出详情
                </Button>
                {selectedRecord.status === "unmatched" && <Button>处理差异</Button>}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

