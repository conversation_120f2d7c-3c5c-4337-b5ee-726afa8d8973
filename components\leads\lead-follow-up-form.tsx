"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"

// 表单验证模式
const formSchema = z.object({
  followUpType: z.string({ required_error: "请选择跟进方式" }),
  followUpDate: z.string({ required_error: "请选择跟进日期" }),
  followUpTime: z.string({ required_error: "请选择跟进时间" }),
  content: z.string().min(5, { message: "内容至少需要5个字符" }),
  nextFollowUpDate: z.string().optional(),
  nextFollowUpPlan: z.string().optional(),
  updateStatus: z.string().optional(),
  updateInterest: z.string().optional(),
})

interface LeadFollowUpFormProps {
  leadId: string
  onSuccess: () => void
  onCancel: () => void
}

export function LeadFollowUpForm({ leadId, onSuccess, onCancel }: LeadFollowUpFormProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 初始化表单
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      followUpType: "",
      followUpDate: new Date().toISOString().split("T")[0],
      followUpTime: new Date().toTimeString().split(" ")[0].substring(0, 5),
      content: "",
      nextFollowUpDate: "",
      nextFollowUpPlan: "",
      updateStatus: "unchanged",
      updateInterest: "unchanged",
    },
  })

  // 提交表单
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)

    try {
      // 这里应该调用API保存跟进记录
      console.log("提交的跟进记录:", { leadId, ...values })

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: "添加成功",
        description: "跟进记录已成功添加",
      })

      // 重置表单并通知父组件
      form.reset()
      onSuccess()
    } catch (error) {
      toast({
        title: "添加失败",
        description: "保存跟进记录时出错，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="followUpType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>跟进方式</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择跟进方式" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="phone">电话沟通</SelectItem>
                    <SelectItem value="wechat">微信沟通</SelectItem>
                    <SelectItem value="visit">到店参观</SelectItem>
                    <SelectItem value="email">邮件沟通</SelectItem>
                    <SelectItem value="other">其他方式</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="followUpDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>跟进日期</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="followUpTime"
            render={({ field }) => (
              <FormItem>
                <FormLabel>跟进时间</FormLabel>
                <FormControl>
                  <Input type="time" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormLabel>跟进内容</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="请详细描述跟进内容、客户反馈等信息"
                  className="min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="updateStatus"
            render={({ field }) => (
              <FormItem>
                <FormLabel>更新状态 (选填)</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择更新状态" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="unchanged">保持不变</SelectItem>
                    <SelectItem value="contacted">已联系</SelectItem>
                    <SelectItem value="qualified">已确认</SelectItem>
                    <SelectItem value="negotiating">洽谈中</SelectItem>
                    <SelectItem value="converted">已转化</SelectItem>
                    <SelectItem value="lost">已流失</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="updateInterest"
            render={({ field }) => (
              <FormItem>
                <FormLabel>更新意向度 (选填)</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="请选择更新意向度" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="unchanged">保持不变</SelectItem>
                    <SelectItem value="1">★ 非常低</SelectItem>
                    <SelectItem value="2">★★ 较低</SelectItem>
                    <SelectItem value="3">★★★ 一般</SelectItem>
                    <SelectItem value="4">★★★★ 较高</SelectItem>
                    <SelectItem value="5">★★★★★ 非常高</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="nextFollowUpDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>下次跟进日期 (选填)</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="nextFollowUpPlan"
            render={({ field }) => (
              <FormItem>
                <FormLabel>下次跟进计划 (选填)</FormLabel>
                <FormControl>
                  <Input placeholder="简述下次跟进计划" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-2 pt-2">
          <Button type="button" variant="outline" onClick={onCancel}>
            取消
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "保存中..." : "保存"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
