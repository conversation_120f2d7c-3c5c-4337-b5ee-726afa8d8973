"use client"

import { useState } from "react"
import Link from "next/link"
import {
  <PERSON><PERSON><PERSON> as <PERSON>charts<PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  <PERSON><PERSON>hart as <PERSON>chartsBarChart,
  Bar,
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as <PERSON>chartsTooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  ReferenceLine
} from "recharts"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import {
  BarChart,
  Calendar,
  ChevronRight,
  Clock,
  Download,
  FileText,
  Filter,
  LineChart,
  MoreHorizontal,
  PieChart,
  Plus,
  Printer,
  RefreshCw,
  Search,
  Settings,
  Share,
  TrendingDown,
  TrendingUp,
  DollarSign,
  ArrowUpDown,
  CreditCard,
  Wallet,
  Landmark,
  CircleDollarSign,
  BarChart3,
  ArrowDownRight,
  ArrowUpRight,
  AlertCircle,
  CheckCircle2,
  X,
  Info,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// 模拟财务数据 - 收入
const incomeData = [
  {
    id: "1",
    category: "会员费",
    amount: 125000,
    previousAmount: 110000,
    change: 13.64,
    trend: "up",
  },
  {
    id: "2",
    category: "私教课程",
    amount: 85000,
    previousAmount: 78000,
    change: 8.97,
    trend: "up",
  },
  {
    id: "3",
    category: "团体课程",
    amount: 45000,
    previousAmount: 42000,
    change: 7.14,
    trend: "up",
  },
  {
    id: "4",
    category: "商品销售",
    amount: 28000,
    previousAmount: 32000,
    change: -12.5,
    trend: "down",
  },
  {
    id: "5",
    category: "场地租赁",
    amount: 12000,
    previousAmount: 10000,
    change: 20,
    trend: "up",
  },
]

// 模拟财务数据 - 支出
const expenseData = [
  {
    id: "1",
    category: "员工薪资",
    amount: 95000,
    previousAmount: 90000,
    change: 5.56,
    trend: "up",
  },
  {
    id: "2",
    category: "场地租金",
    amount: 45000,
    previousAmount: 45000,
    change: 0,
    trend: "stable",
  },
  {
    id: "3",
    category: "设备维护",
    amount: 15000,
    previousAmount: 12000,
    change: 25,
    trend: "up",
  },
  {
    id: "4",
    category: "水电费",
    amount: 18000,
    previousAmount: 16500,
    change: 9.09,
    trend: "up",
  },
  {
    id: "5",
    category: "营销推广",
    amount: 22000,
    previousAmount: 25000,
    change: -12,
    trend: "down",
  },
]

// 模拟现金流数据
const cashFlowData = [
  { month: "1月", income: 280000, expense: 195000, profit: 85000 },
  { month: "2月", income: 295000, expense: 200000, profit: 95000 },
  { month: "3月", income: 310000, expense: 205000, profit: 105000 },
  { month: "4月", income: 305000, expense: 210000, profit: 95000 },
  { month: "5月", income: 320000, expense: 215000, profit: 105000 },
  { month: "6月", income: 335000, expense: 220000, profit: 115000 },
]

// 模拟成本控制建议
const costControlSuggestions = [
  {
    id: "1",
    category: "能源成本",
    currentCost: 18000,
    potentialSaving: 3600,
    savingPercentage: 20,
    priority: "high",
    suggestion: "安装智能照明和温控系统，减少非营业时间的能源消耗",
    status: "pending",
  },
  {
    id: "2",
    category: "营销支出",
    currentCost: 22000,
    potentialSaving: 4400,
    savingPercentage: 20,
    priority: "medium",
    suggestion: "优化数字营销渠道，减少低效渠道的支出",
    status: "implemented",
  },
  {
    id: "3",
    category: "设备维护",
    currentCost: 15000,
    potentialSaving: 2250,
    savingPercentage: 15,
    priority: "medium",
    suggestion: "建立预防性维护计划，减少紧急维修成本",
    status: "pending",
  },
  {
    id: "4",
    category: "库存管理",
    currentCost: 28000,
    potentialSaving: 5600,
    savingPercentage: 20,
    priority: "high",
    suggestion: "优化库存水平，减少滞销商品和过量库存",
    status: "pending",
  },
]

// 模拟税务优化建议
const taxOptimizationSuggestions = [
  {
    id: "1",
    category: "设备折旧",
    potentialSaving: 12000,
    complexity: "low",
    suggestion: "采用加速折旧方法，提前获得税收减免",
    status: "pending",
  },
  {
    id: "2",
    category: "员工福利",
    potentialSaving: 8500,
    complexity: "medium",
    suggestion: "将部分现金薪酬转为免税福利，如健康保险和退休计划",
    status: "implemented",
  },
  {
    id: "3",
    category: "研发支出",
    potentialSaving: 15000,
    complexity: "high",
    suggestion: "申请研发费用加计扣除，减少应纳税所得额",
    status: "pending",
  },
]

export default function FinancialReportsPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const [dateRange, setDateRange] = useState("month")
  const [dateCompare, setDateCompare] = useState("none") // none, yoy (同比), mom (环比)
  const [showReportSettings, setShowReportSettings] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [customDateRange, setCustomDateRange] = useState({
    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  })
  const [dataFilters, setDataFilters] = useState({
    minAmount: "",
    maxAmount: ""
  })

  // 预付费数据
  const prepaidData = {
    // 预付费收入数据
    income: [
      { month: "1月", membershipCards: 120000, coursePackages: 85000, shareholderDeposits: 50000, total: 255000 },
      { month: "2月", membershipCards: 135000, coursePackages: 92000, shareholderDeposits: 45000, total: 272000 },
      { month: "3月", membershipCards: 142000, coursePackages: 88000, shareholderDeposits: 60000, total: 290000 },
      { month: "4月", membershipCards: 138000, coursePackages: 95000, shareholderDeposits: 55000, total: 288000 },
      { month: "5月", membershipCards: 150000, coursePackages: 105000, shareholderDeposits: 65000, total: 320000 },
      { month: "6月", membershipCards: 165000, coursePackages: 110000, shareholderDeposits: 70000, total: 345000 },
    ],
    // 预付费负债数据
    liabilities: [
      { month: "1月", membershipCards: 580000, coursePackages: 320000, shareholderDeposits: 420000, total: 1320000 },
      { month: "2月", membershipCards: 595000, coursePackages: 335000, shareholderDeposits: 410000, total: 1340000 },
      { month: "3月", membershipCards: 610000, coursePackages: 345000, shareholderDeposits: 430000, total: 1385000 },
      { month: "4月", membershipCards: 625000, coursePackages: 360000, shareholderDeposits: 425000, total: 1410000 },
      { month: "5月", membershipCards: 645000, coursePackages: 380000, shareholderDeposits: 440000, total: 1465000 },
      { month: "6月", membershipCards: 670000, coursePackages: 395000, shareholderDeposits: 460000, total: 1525000 },
    ],
    // 预付费消耗率数据
    consumptionRates: [
      { type: "会员卡", rate: 15.2, previousRate: 14.5, change: 0.7 },
      { type: "课程包", rate: 22.5, previousRate: 21.8, change: 0.7 },
      { type: "股东储值", rate: 12.8, previousRate: 13.5, change: -0.7 },
    ],
    // 预付费类型分布
    distribution: [
      { type: "会员卡", value: 670000, percentage: 44 },
      { type: "课程包", value: 395000, percentage: 26 },
      { type: "股东储值", value: 460000, percentage: 30 },
    ],
    // 预付费产品详情
    products: [
      { id: 1, name: "年卡会员", totalSold: 85, activeCount: 78, avgPrice: 5800, totalValue: 452400 },
      { id: 2, name: "季卡会员", totalSold: 120, activeCount: 105, avgPrice: 1800, totalValue: 189000 },
      { id: 3, name: "私教课10次卡", totalSold: 95, activeCount: 82, avgPrice: 2500, totalValue: 205000 },
      { id: 4, name: "团体课20次卡", totalSold: 150, activeCount: 135, avgPrice: 1200, totalValue: 162000 },
      { id: 5, name: "消费型股东储值", totalSold: 45, activeCount: 42, avgPrice: 10000, totalValue: 420000 },
    ]
  }

  // 刷新数据
  const refreshData = () => {
    setIsLoading(true)
    // 模拟API请求
    setTimeout(() => {
      setIsLoading(false)
      // 这里可以添加实际的数据刷新逻辑
    }, 1000)
  }

  // 应用自定义日期范围
  const applyCustomDateRange = () => {
    // 验证日期范围
    if (new Date(customDateRange.start) > new Date(customDateRange.end)) {
      // 可以添加错误提示
      return
    }

    refreshData()
  }

  // 导出报表
  const exportReport = (format: "excel" | "pdf" | "print") => {
    setIsLoading(true)
    // 模拟导出过程
    setTimeout(() => {
      setIsLoading(false)
      // 这里可以添加实际的导出逻辑
      if (format === "print") {
        window.print()
      } else {
        // 模拟下载
        alert(`报表已导出为${format === "excel" ? "Excel" : "PDF"}格式`)
      }
    }, 1000)
  }

  // 计算总收入
  const totalIncome = incomeData.reduce((sum, item) => sum + item.amount, 0)
  const previousTotalIncome = incomeData.reduce((sum, item) => sum + item.previousAmount, 0)
  const incomeChange = ((totalIncome - previousTotalIncome) / previousTotalIncome) * 100

  // 计算总支出
  const totalExpense = expenseData.reduce((sum, item) => sum + item.amount, 0)
  const previousTotalExpense = expenseData.reduce((sum, item) => sum + item.previousAmount, 0)
  const expenseChange = ((totalExpense - previousTotalExpense) / previousTotalExpense) * 100

  // 计算净利润
  const netProfit = totalIncome - totalExpense
  const previousNetProfit = previousTotalIncome - previousTotalExpense
  const profitChange = ((netProfit - previousNetProfit) / previousNetProfit) * 100

  // 计算利润率
  const profitMargin = (netProfit / totalIncome) * 100
  const previousProfitMargin = (previousNetProfit / previousTotalIncome) * 100
  const marginChange = profitMargin - previousProfitMargin

  return (
    <div className="space-y-6 relative">
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-50">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            <span className="mt-2 text-sm text-muted-foreground">加载中...</span>
          </div>
        </div>
      )}

      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">高级财务报表</h1>
          <p className="text-muted-foreground">全面的财务分析和报表，帮助优化业务决策</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button variant="outline" onClick={() => setShowReportSettings(true)}>
            <Settings className="mr-2 h-4 w-4" />
            报表设置
          </Button>
          <Button variant="outline" onClick={() => window.print()}>
            <Printer className="mr-2 h-4 w-4" />
            打印
          </Button>
          <Button>
            <Share className="mr-2 h-4 w-4" />
            分享报表
          </Button>
        </div>
      </div>

      <div className="text-sm breadcrumbs">
        <ul className="flex items-center space-x-1">
          <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li>高级财务报表</li>
        </ul>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex flex-wrap items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">今日</SelectItem>
              <SelectItem value="week">本周</SelectItem>
              <SelectItem value="month">本月</SelectItem>
              <SelectItem value="quarter">本季度</SelectItem>
              <SelectItem value="year">本年度</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center gap-2 border rounded-md p-1">
            <Button variant="ghost" size="sm" className={dateCompare === "none" ? "bg-muted" : ""} onClick={() => setDateCompare("none")}>
              无对比
            </Button>
            <Button variant="ghost" size="sm" className={dateCompare === "yoy" ? "bg-muted" : ""} onClick={() => setDateCompare("yoy")}>
              同比
            </Button>
            <Button variant="ghost" size="sm" className={dateCompare === "mom" ? "bg-muted" : ""} onClick={() => setDateCompare("mom")}>
              环比
            </Button>
          </div>

          {dateRange === "custom" && (
            <div className="flex items-center gap-2">
              <div className="flex items-center border rounded-md">
                <Input
                  type="date"
                  className="w-[140px] border-0"
                  value={customDateRange.start}
                  onChange={(e) => setCustomDateRange({...customDateRange, start: e.target.value})}
                />
                <span className="px-2 text-muted-foreground">至</span>
                <Input
                  type="date"
                  className="w-[140px] border-0"
                  value={customDateRange.end}
                  onChange={(e) => setCustomDateRange({...customDateRange, end: e.target.value})}
                />
              </div>
              <Button variant="outline" size="sm" onClick={applyCustomDateRange}>
                应用
              </Button>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2 w-full md:w-auto">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                筛选
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuLabel>数据筛选</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div className="p-2">
                <div className="mb-2">
                  <Label htmlFor="min-amount" className="text-xs">最小金额</Label>
                  <Input id="min-amount" placeholder="0" className="h-8" />
                </div>
                <div className="mb-2">
                  <Label htmlFor="max-amount" className="text-xs">最大金额</Label>
                  <Input id="max-amount" placeholder="不限" className="h-8" />
                </div>
                <div className="flex justify-end mt-4">
                  <Button size="sm">应用筛选</Button>
                </div>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="outline" size="sm" onClick={refreshData}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新数据
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                导出
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => exportReport("excel")}>
                <FileText className="mr-2 h-4 w-4" />
                导出为Excel
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportReport("pdf")}>
                <FileText className="mr-2 h-4 w-4" />
                导出为PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportReport("print")}>
                <Printer className="mr-2 h-4 w-4" />
                打印报表
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6 max-w-4xl">
          <TabsTrigger value="overview">财务概览</TabsTrigger>
          <TabsTrigger value="cashflow">现金流分析</TabsTrigger>
          <TabsTrigger value="prepaid">预付费分析</TabsTrigger>
          <TabsTrigger value="profitloss">盈亏分析</TabsTrigger>
          <TabsTrigger value="costcontrol">成本控制</TabsTrigger>
          <TabsTrigger value="tax">税务优化</TabsTrigger>
        </TabsList>

        {/* 财务概览 */}
        <TabsContent value="overview" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-sm font-medium">总收入</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-[200px] text-xs">总收入是指企业在特定时期内从所有业务活动中获得的总金额，包括会员费、课程收入、商品销售等。</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥{(totalIncome / 10000).toFixed(2)}万</div>
                <div className="flex items-center mt-1">
                  {incomeChange > 0 ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <TrendingUp className="mr-1 h-3 w-3" />
                      +{incomeChange.toFixed(2)}%
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      <TrendingDown className="mr-1 h-3 w-3" />
                      {incomeChange.toFixed(2)}%
                    </Badge>
                  )}
                  <span className="text-xs text-muted-foreground ml-2">
                    {dateCompare === "yoy" ? "同比" : dateCompare === "mom" ? "环比" : "较上期"}
                  </span>
                </div>
                <div className="mt-3 pt-3 border-t">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>日均收入</span>
                    <span>¥{((totalIncome / 30) / 1000).toFixed(2)}千</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-sm font-medium">总支出</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-[200px] text-xs">总支出是指企业在特定时期内为维持业务运营而产生的所有成本和费用，包括人力成本、租金、设备维护等。</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥{(totalExpense / 10000).toFixed(2)}万</div>
                <div className="flex items-center mt-1">
                  {expenseChange < 0 ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <TrendingDown className="mr-1 h-3 w-3" />
                      {expenseChange.toFixed(2)}%
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      <TrendingUp className="mr-1 h-3 w-3" />
                      +{expenseChange.toFixed(2)}%
                    </Badge>
                  )}
                  <span className="text-xs text-muted-foreground ml-2">
                    {dateCompare === "yoy" ? "同比" : dateCompare === "mom" ? "环比" : "较上期"}
                  </span>
                </div>
                <div className="mt-3 pt-3 border-t">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>日均支出</span>
                    <span>¥{((totalExpense / 30) / 1000).toFixed(2)}千</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-sm font-medium">净利润</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-[200px] text-xs">净利润是总收入减去总支出后的剩余金额，反映企业在特定时期内的盈利能力。</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥{(netProfit / 10000).toFixed(2)}万</div>
                <div className="flex items-center mt-1">
                  {profitChange > 0 ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <TrendingUp className="mr-1 h-3 w-3" />
                      +{profitChange.toFixed(2)}%
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      <TrendingDown className="mr-1 h-3 w-3" />
                      {profitChange.toFixed(2)}%
                    </Badge>
                  )}
                  <span className="text-xs text-muted-foreground ml-2">
                    {dateCompare === "yoy" ? "同比" : dateCompare === "mom" ? "环比" : "较上期"}
                  </span>
                </div>
                <div className="mt-3 pt-3 border-t">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>人均贡献</span>
                    <span>¥{(netProfit / 15 / 1000).toFixed(2)}千</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-sm font-medium">利润率</CardTitle>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="w-[200px] text-xs">利润率是净利润与总收入的比率，以百分比表示，反映企业将收入转化为利润的能力。</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{profitMargin.toFixed(2)}%</div>
                <div className="flex items-center mt-1">
                  {marginChange > 0 ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <TrendingUp className="mr-1 h-3 w-3" />
                      +{marginChange.toFixed(2)}%
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      <TrendingDown className="mr-1 h-3 w-3" />
                      {marginChange.toFixed(2)}%
                    </Badge>
                  )}
                  <span className="text-xs text-muted-foreground ml-2">
                    {dateCompare === "yoy" ? "同比" : dateCompare === "mom" ? "环比" : "较上期"}
                  </span>
                </div>
                <div className="mt-3 pt-3 border-t">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>行业平均</span>
                    <span>22.5%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">会员续约率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">85.3%</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +2.1%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">客单价</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥1,250</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +5.2%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">新增会员</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">78</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +12.5%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">现金流比率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.35</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +0.08
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>收入构成</CardTitle>
                <CardDescription>各收入类别占比分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={incomeData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="amount"
                        nameKey="category"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {incomeData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={[
                              "#3b82f6", // 蓝色 - 会员费
                              "#22c55e", // 绿色 - 私教课程
                              "#eab308", // 黄色 - 团体课程
                              "#a855f7", // 紫色 - 商品销售
                              "#ef4444"  // 红色 - 场地租赁
                            ][index % 5]}
                          />
                        ))}
                      </Pie>
                      <Legend layout="horizontal" verticalAlign="bottom" align="center" />
                      <RechartsTooltip
                        formatter={(value, name) => [
                          `¥${value.toLocaleString()}`,
                          name
                        ]}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>支出构成</CardTitle>
                <CardDescription>各支出类别占比分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={expenseData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="amount"
                        nameKey="category"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {expenseData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={[
                              "#ef4444", // 红色 - 员工薪资
                              "#f97316", // 橙色 - 场地租金
                              "#3b82f6", // 蓝色 - 设备维护
                              "#06b6d4", // 青色 - 水电费
                              "#a855f7"  // 紫色 - 营销推广
                            ][index % 5]}
                          />
                        ))}
                      </Pie>
                      <Legend layout="horizontal" verticalAlign="bottom" align="center" />
                      <RechartsTooltip
                        formatter={(value, name) => [
                          `¥${value.toLocaleString()}`,
                          name
                        ]}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>收入明细</CardTitle>
                  <CardDescription>各收入类别的详细数据</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  筛选
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>收入类别</TableHead>
                    <TableHead className="text-right">金额 (元)</TableHead>
                    <TableHead className="text-right">占比</TableHead>
                    <TableHead className="text-right">较上期</TableHead>
                    <TableHead className="text-right">趋势</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {incomeData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.category}</TableCell>
                      <TableCell className="text-right">{item.amount.toLocaleString()}</TableCell>
                      <TableCell className="text-right">{((item.amount / totalIncome) * 100).toFixed(2)}%</TableCell>
                      <TableCell className="text-right">
                        {item.change > 0 ? `+${item.change.toFixed(2)}%` : `${item.change.toFixed(2)}%`}
                      </TableCell>
                      <TableCell className="text-right">
                        {item.trend === "up" ? (
                          <ArrowUpRight className="h-4 w-4 text-green-500 ml-auto" />
                        ) : item.trend === "down" ? (
                          <ArrowDownRight className="h-4 w-4 text-red-500 ml-auto" />
                        ) : (
                          <ArrowUpDown className="h-4 w-4 text-yellow-500 ml-auto" />
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>支出明细</CardTitle>
                  <CardDescription>各支出类别的详细数据</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  筛选
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>支出类别</TableHead>
                    <TableHead className="text-right">金额 (元)</TableHead>
                    <TableHead className="text-right">占比</TableHead>
                    <TableHead className="text-right">较上期</TableHead>
                    <TableHead className="text-right">趋势</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {expenseData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.category}</TableCell>
                      <TableCell className="text-right">{item.amount.toLocaleString()}</TableCell>
                      <TableCell className="text-right">{((item.amount / totalExpense) * 100).toFixed(2)}%</TableCell>
                      <TableCell className="text-right">
                        {item.change > 0 ? `+${item.change.toFixed(2)}%` : `${item.change.toFixed(2)}%`}
                      </TableCell>
                      <TableCell className="text-right">
                        {item.trend === "up" ? (
                          <ArrowUpRight className="h-4 w-4 text-red-500 ml-auto" />
                        ) : item.trend === "down" ? (
                          <ArrowDownRight className="h-4 w-4 text-green-500 ml-auto" />
                        ) : (
                          <ArrowUpDown className="h-4 w-4 text-yellow-500 ml-auto" />
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 现金流分析 */}
        <TabsContent value="cashflow" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">经营现金流</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥85.3万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +12.4%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">投资现金流</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">-¥25.8万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                    <TrendingDown className="mr-1 h-3 w-3" />
                    -35.2%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">筹资现金流</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">-¥12.5万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +8.3%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>现金流趋势</CardTitle>
              <CardDescription>近6个月现金流入、流出和净现金流</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={cashFlowData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 10,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis
                      tickFormatter={(value) => `${value / 10000}万`}
                      width={80}
                    />
                    <RechartsTooltip
                      formatter={(value, name) => {
                        const formattedValue = `¥${Number(value).toLocaleString()}`;
                        const formattedName = name === "income" ? "现金流入" :
                                             name === "expense" ? "现金流出" : "净现金流";
                        return [formattedValue, formattedName];
                      }}
                      labelFormatter={(label) => `${label}`}
                    />
                    <Legend
                      formatter={(value) => {
                        return value === "income" ? "现金流入" :
                               value === "expense" ? "现金流出" : "净现金流";
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="income"
                      stroke="#22c55e"
                      strokeWidth={2}
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="expense"
                      stroke="#ef4444"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="profit"
                      stroke="#3b82f6"
                      strokeWidth={2}
                      strokeDasharray="5 5"
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>现金流明细</CardTitle>
                  <CardDescription>月度现金流入、流出和净现金流</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  导出
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>月份</TableHead>
                    <TableHead className="text-right">现金流入 (元)</TableHead>
                    <TableHead className="text-right">现金流出 (元)</TableHead>
                    <TableHead className="text-right">净现金流 (元)</TableHead>
                    <TableHead className="text-right">趋势</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cashFlowData.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{item.month}</TableCell>
                      <TableCell className="text-right">{item.income.toLocaleString()}</TableCell>
                      <TableCell className="text-right">{item.expense.toLocaleString()}</TableCell>
                      <TableCell className="text-right">{item.profit.toLocaleString()}</TableCell>
                      <TableCell className="text-right">
                        {index > 0 && cashFlowData[index].profit > cashFlowData[index - 1].profit ? (
                          <ArrowUpRight className="h-4 w-4 text-green-500 ml-auto" />
                        ) : index > 0 && cashFlowData[index].profit < cashFlowData[index - 1].profit ? (
                          <ArrowDownRight className="h-4 w-4 text-red-500 ml-auto" />
                        ) : (
                          <ArrowUpDown className="h-4 w-4 text-yellow-500 ml-auto" />
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>现金流预测</CardTitle>
                <CardDescription>未来3个月现金流预测</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={[
                        ...cashFlowData.slice(-3),
                        { month: "7月", income: 350000, expense: 225000, profit: 125000, isPrediction: true },
                        { month: "8月", income: 365000, expense: 230000, profit: 135000, isPrediction: true },
                        { month: "9月", income: 380000, expense: 235000, profit: 145000, isPrediction: true },
                      ]}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 10,
                      }}
                    >
                      <defs>
                        <linearGradient id="colorIncome" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#22c55e" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#22c55e" stopOpacity={0.1}/>
                        </linearGradient>
                        <linearGradient id="colorProfit" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                        </linearGradient>
                        <pattern id="diagonalHatch" patternUnits="userSpaceOnUse" width="4" height="4">
                          <path d="M-1,1 l2,-2 M0,4 l4,-4 M3,5 l2,-2" stroke="#000000" strokeWidth="0.5" opacity="0.2"/>
                        </pattern>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="month"
                        tickFormatter={(value, index, ticks) => {
                          if (!ticks || !Array.isArray(ticks)) return value;
                          const item = ticks.find(t => t === value);
                          if (!item) return value;
                          const idx = ticks.indexOf(item);
                          return idx >= 3 ? `${value} (预测)` : value;
                        }}
                      />
                      <YAxis
                        tickFormatter={(value) => `${value / 10000}万`}
                        width={80}
                      />
                      <RechartsTooltip
                        formatter={(value, name, props) => {
                          if (!props || !props.payload) return [value, name];
                          const { payload } = props;
                          const isPrediction = payload.isPrediction;
                          const formattedValue = `¥${Number(value).toLocaleString()}`;
                          const formattedName = name === "income" ? "现金流入" :
                                              name === "expense" ? "现金流出" : "净现金流";
                          return [
                            `${formattedValue} ${isPrediction ? '(预测)' : ''}`,
                            formattedName
                          ];
                        }}
                      />
                      <Legend
                        formatter={(value) => {
                          return value === "income" ? "现金流入" :
                                value === "expense" ? "现金流出" : "净现金流";
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="income"
                        stroke="#22c55e"
                        strokeWidth={2}
                        fillOpacity={1}
                        fill="url(#colorIncome)"
                        strokeDasharray={(_, index) => index >= 3 ? "5 5" : "0"}
                      />
                      <Area
                        type="monotone"
                        dataKey="profit"
                        stroke="#3b82f6"
                        strokeWidth={2}
                        fillOpacity={1}
                        fill="url(#colorProfit)"
                        strokeDasharray={(_, index) => index >= 3 ? "5 5" : "0"}
                      />
                      <Line
                        type="monotone"
                        dataKey="expense"
                        stroke="#ef4444"
                        strokeWidth={2}
                        strokeDasharray={(_, index) => index >= 3 ? "5 5" : "0"}
                      />
                      {/* 预测区域分隔线 */}
                      <ReferenceLine x="6月" stroke="#666" strokeDasharray="3 3" label={{ value: '预测开始', position: 'insideTopRight' }} />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>现金流健康度</CardTitle>
                <CardDescription>现金流关键指标分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">经营现金流比率</span>
                      <span className="text-sm font-medium">1.35</span>
                    </div>
                    <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-green-500" style={{ width: "85%" }}></div>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">目标: > 1.2</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        良好
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">现金转换周期</span>
                      <span className="text-sm font-medium">28天</span>
                    </div>
                    <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-yellow-500" style={{ width: "65%" }}></div>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">目标: &lt; 25天</span>
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                        一般
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">现金储备率</span>
                      <span className="text-sm font-medium">3.2个月</span>
                    </div>
                    <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-green-500" style={{ width: "90%" }}></div>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">目标: > 3个月</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        良好
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 预付费分析 */}
        <TabsContent value="prepaid" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">预付费总额</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥1,525,000</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +4.1%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上月</span>
                </div>
                <div className="mt-3 pt-3 border-t">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>平均消耗率</span>
                    <span>16.8%/月</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">本月预付费收入</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥345,000</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +7.8%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上月</span>
                </div>
                <div className="mt-3 pt-3 border-t">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>占总收入比例</span>
                    <span>42.5%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">预付费负债比率</CardTitle>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-6 w-6 absolute right-4 top-2">
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-[200px] text-xs">预付费负债比率 = 预付费负债总额 / 月均收入，反映企业偿还预付费负债的能力。比率越低越好，一般建议不超过5。</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.42</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +0.08
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上月</span>
                </div>
                <div className="mt-3 pt-3 border-t">
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>行业平均</span>
                    <span>4.2</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>预付费收入趋势</CardTitle>
                <CardDescription>近6个月预付费收入趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart
                      data={prepaidData.income}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 10,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis
                        tickFormatter={(value) => `${value / 10000}万`}
                        width={80}
                      />
                      <RechartsTooltip
                        formatter={(value, name) => {
                          const formattedValue = `¥${Number(value).toLocaleString()}`;
                          const formattedName = name === "membershipCards" ? "会员卡" :
                                              name === "coursePackages" ? "课程包" :
                                              name === "shareholderDeposits" ? "股东储值" : "总计";
                          return [formattedValue, formattedName];
                        }}
                      />
                      <Legend
                        formatter={(value) => {
                          return value === "membershipCards" ? "会员卡" :
                                value === "coursePackages" ? "课程包" :
                                value === "shareholderDeposits" ? "股东储值" : "总计";
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="membershipCards"
                        stroke="#3b82f6"
                        strokeWidth={2}
                      />
                      <Line
                        type="monotone"
                        dataKey="coursePackages"
                        stroke="#22c55e"
                        strokeWidth={2}
                      />
                      <Line
                        type="monotone"
                        dataKey="shareholderDeposits"
                        stroke="#f59e0b"
                        strokeWidth={2}
                      />
                      <Line
                        type="monotone"
                        dataKey="total"
                        stroke="#000000"
                        strokeWidth={2}
                        strokeDasharray="5 5"
                      />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>预付费负债趋势</CardTitle>
                <CardDescription>近6个月预付费负债趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart
                      data={prepaidData.liabilities}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 10,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis
                        tickFormatter={(value) => `${value / 10000}万`}
                        width={80}
                      />
                      <RechartsTooltip
                        formatter={(value, name) => {
                          const formattedValue = `¥${Number(value).toLocaleString()}`;
                          const formattedName = name === "membershipCards" ? "会员卡" :
                                              name === "coursePackages" ? "课程包" :
                                              name === "shareholderDeposits" ? "股东储值" : "总计";
                          return [formattedValue, formattedName];
                        }}
                      />
                      <Legend
                        formatter={(value) => {
                          return value === "membershipCards" ? "会员卡" :
                                value === "coursePackages" ? "课程包" :
                                value === "shareholderDeposits" ? "股东储值" : "总计";
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="membershipCards"
                        stroke="#3b82f6"
                        strokeWidth={2}
                      />
                      <Line
                        type="monotone"
                        dataKey="coursePackages"
                        stroke="#22c55e"
                        strokeWidth={2}
                      />
                      <Line
                        type="monotone"
                        dataKey="shareholderDeposits"
                        stroke="#f59e0b"
                        strokeWidth={2}
                      />
                      <Line
                        type="monotone"
                        dataKey="total"
                        stroke="#000000"
                        strokeWidth={2}
                        strokeDasharray="5 5"
                      />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>预付费类型分布</CardTitle>
                <CardDescription>各类预付费产品占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={prepaidData.distribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="type"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {prepaidData.distribution.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={[
                              "#3b82f6", // 蓝色 - 会员卡
                              "#22c55e", // 绿色 - 课程包
                              "#f59e0b"  // 黄色 - 股东储值
                            ][index % 3]}
                          />
                        ))}
                      </Pie>
                      <Legend layout="horizontal" verticalAlign="bottom" align="center" />
                      <RechartsTooltip
                        formatter={(value, name) => [
                          `¥${value.toLocaleString()}`,
                          name
                        ]}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>预付费消耗率</CardTitle>
                <CardDescription>各类预付费产品的月均消耗率</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {prepaidData.consumptionRates.map((item, index) => (
                    <div key={index}>
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-sm font-medium">{item.type}</span>
                        <span className="text-sm font-medium">{item.rate}%/月</span>
                      </div>
                      <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className={`h-full ${item.rate > 20 ? "bg-green-500" : item.rate > 15 ? "bg-blue-500" : "bg-yellow-500"}`}
                          style={{ width: `${item.rate * 3}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-xs text-muted-foreground">上期: {item.previousRate}%</span>
                        <Badge
                          variant="outline"
                          className={item.change > 0
                            ? "bg-green-50 text-green-700 border-green-200"
                            : "bg-red-50 text-red-700 border-red-200"}
                        >
                          {item.change > 0 ? "+" : ""}{item.change}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>预付费产品详情</CardTitle>
                  <CardDescription>各类预付费产品的详细数据</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  筛选
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>产品名称</TableHead>
                    <TableHead className="text-right">已售数量</TableHead>
                    <TableHead className="text-right">活跃数量</TableHead>
                    <TableHead className="text-right">平均价格</TableHead>
                    <TableHead className="text-right">总价值</TableHead>
                    <TableHead className="text-right">使用率</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {prepaidData.products.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell className="text-right">{item.totalSold}</TableCell>
                      <TableCell className="text-right">{item.activeCount}</TableCell>
                      <TableCell className="text-right">¥{item.avgPrice.toLocaleString()}</TableCell>
                      <TableCell className="text-right">¥{item.totalValue.toLocaleString()}</TableCell>
                      <TableCell className="text-right">
                        {((item.activeCount / item.totalSold) * 100).toFixed(1)}%
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>预付费管理建议</CardTitle>
              <CardDescription>基于数据分析的预付费管理优化建议</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <CreditCard className="h-4 w-4 text-blue-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">优化会员卡消耗率</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        会员卡的月均消耗率为15.2%，低于行业平均水平(18%)。建议增加会员活动和专属课程，
                        提高会员到店频率，增加会员卡消耗率，降低预付费负债风险。
                      </p>
                      <div className="flex items-center gap-2 mt-3">
                        <span className="text-sm">建议优先级:</span>
                        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          高
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-green-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <Wallet className="h-4 w-4 text-green-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">调整预付费产品结构</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        课程包的消耗率(22.5%)高于会员卡(15.2%)和股东储值(12.8%)。建议增加课程包产品的比例，
                        减少长期会员卡和股东储值的比例，优化预付费产品结构，提高整体消耗率。
                      </p>
                      <div className="flex items-center gap-2 mt-3">
                        <span className="text-sm">建议优先级:</span>
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          中
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-amber-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <CircleDollarSign className="h-4 w-4 text-amber-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">预付费负债风险管理</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        当前预付费负债比率为4.42，接近警戒线(5.0)。建议设立预付费风险准备金，按预付费收入的
                        10%计提，用于应对可能的退款需求和预付费负债风险。
                      </p>
                      <div className="flex items-center gap-2 mt-3">
                        <span className="text-sm">建议优先级:</span>
                        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          中
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 盈亏分析 */}
        <TabsContent value="profitloss" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">毛利润</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥168.5万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +8.2%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">营业利润</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥95.3万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +12.5%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">税后净利润</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥76.2万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +15.3%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>利润趋势</CardTitle>
              <CardDescription>近12个月利润趋势分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart
                    data={[
                      { month: "1月", grossProfit: 1450000, netProfit: 720000, profitMargin: 24.5 },
                      { month: "2月", grossProfit: 1480000, netProfit: 735000, profitMargin: 24.8 },
                      { month: "3月", grossProfit: 1520000, netProfit: 750000, profitMargin: 25.1 },
                      { month: "4月", grossProfit: 1550000, netProfit: 765000, profitMargin: 25.3 },
                      { month: "5月", grossProfit: 1600000, netProfit: 780000, profitMargin: 25.6 },
                      { month: "6月", grossProfit: 1650000, netProfit: 795000, profitMargin: 25.8 },
                      { month: "7月", grossProfit: 1685000, netProfit: 810000, profitMargin: 26.0 },
                      { month: "8月", grossProfit: 1700000, netProfit: 825000, profitMargin: 26.2 },
                      { month: "9月", grossProfit: 1720000, netProfit: 840000, profitMargin: 26.5 },
                      { month: "10月", grossProfit: 1750000, netProfit: 855000, profitMargin: 26.8 },
                      { month: "11月", grossProfit: 1780000, netProfit: 870000, profitMargin: 27.0 },
                      { month: "12月", grossProfit: 1800000, netProfit: 885000, profitMargin: 27.2 },
                    ]}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 10,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis
                      yAxisId="left"
                      orientation="left"
                      tickFormatter={(value) => `${value / 10000}万`}
                      width={80}
                    />
                    <YAxis
                      yAxisId="right"
                      orientation="right"
                      domain={[0, 40]}
                      tickFormatter={(value) => `${value}%`}
                      width={60}
                    />
                    <RechartsTooltip
                      formatter={(value, name) => {
                        if (name === "grossProfit") {
                          return [`¥${(value / 10000).toFixed(2)}万`, "毛利润"];
                        } else if (name === "netProfit") {
                          return [`¥${(value / 10000).toFixed(2)}万`, "净利润"];
                        } else {
                          return [`${value}%`, "利润率"];
                        }
                      }}
                    />
                    <Legend
                      formatter={(value) => {
                        if (value === "grossProfit") return "毛利润";
                        if (value === "netProfit") return "净利润";
                        return "利润率";
                      }}
                    />
                    <Bar
                      yAxisId="left"
                      dataKey="grossProfit"
                      fill="#3b82f6"
                      name="grossProfit"
                      barSize={20}
                    />
                    <Bar
                      yAxisId="left"
                      dataKey="netProfit"
                      fill="#22c55e"
                      name="netProfit"
                      barSize={20}
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="profitMargin"
                      stroke="#f59e0b"
                      strokeWidth={2}
                      name="profitMargin"
                      dot={{ r: 4 }}
                    />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>利润率分析</CardTitle>
                <CardDescription>各类利润率指标分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">毛利率</span>
                      <span className="text-sm font-medium">56.2%</span>
                    </div>
                    <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-green-500" style={{ width: "56.2%" }}></div>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">行业平均: 52%</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        高于平均
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">营业利润率</span>
                      <span className="text-sm font-medium">31.8%</span>
                    </div>
                    <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-green-500" style={{ width: "31.8%" }}></div>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">行业平均: 28%</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        高于平均
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">净利润率</span>
                      <span className="text-sm font-medium">25.4%</span>
                    </div>
                    <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-green-500" style={{ width: "25.4%" }}></div>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">行业平均: 22%</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        高于平均
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">ROI</span>
                      <span className="text-sm font-medium">18.5%</span>
                    </div>
                    <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                      <div className="h-full bg-yellow-500" style={{ width: "18.5%" }}></div>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-muted-foreground">行业平均: 20%</span>
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                        略低于平均
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>业务线盈利能力</CardTitle>
                <CardDescription>各业务线的盈利能力分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      layout="vertical"
                      data={[
                        { name: "会员业务", profitMargin: 45, industryAvg: 42 },
                        { name: "私教课程", profitMargin: 35, industryAvg: 42 },
                        { name: "团体课程", profitMargin: 42, industryAvg: 40 },
                        { name: "商品销售", profitMargin: 48, industryAvg: 38 },
                        { name: "场地租赁", profitMargin: 55, industryAvg: 45 },
                        { name: "线上课程", profitMargin: 52, industryAvg: 48 },
                      ]}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 100,
                        bottom: 10,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                      <XAxis
                        type="number"
                        domain={[0, 60]}
                        tickFormatter={(value) => `${value}%`}
                      />
                      <YAxis
                        dataKey="name"
                        type="category"
                        width={90}
                      />
                      <RechartsTooltip
                        formatter={(value, name) => {
                          return [`${value}%`, name === "profitMargin" ? "利润率" : "行业平均"];
                        }}
                      />
                      <Legend
                        formatter={(value) => {
                          return value === "profitMargin" ? "利润率" : "行业平均";
                        }}
                      />
                      <Bar
                        dataKey="profitMargin"
                        fill="#3b82f6"
                        name="profitMargin"
                        barSize={20}
                        radius={[0, 4, 4, 0]}
                      >
                        {/* 根据与行业平均的比较设置不同颜色 */}
                        {[
                          { name: "会员业务", profitMargin: 45, industryAvg: 42 },
                          { name: "私教课程", profitMargin: 35, industryAvg: 42 },
                          { name: "团体课程", profitMargin: 42, industryAvg: 40 },
                          { name: "商品销售", profitMargin: 48, industryAvg: 38 },
                          { name: "场地租赁", profitMargin: 55, industryAvg: 45 },
                          { name: "线上课程", profitMargin: 52, industryAvg: 48 },
                        ].map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={entry.profitMargin >= entry.industryAvg ? "#22c55e" : "#ef4444"}
                          />
                        ))}
                      </Bar>
                      <ReferenceLine
                        x={40}
                        stroke="#f59e0b"
                        strokeDasharray="3 3"
                        label={{
                          value: '行业平均: 40%',
                          position: 'top',
                          fill: '#f59e0b',
                          fontSize: 12
                        }}
                      />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>盈利能力优化建议</CardTitle>
                  <CardDescription>基于数据分析的盈利能力优化建议</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  导出报告
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-green-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">提高私教课程利润率</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        私教课程的利润率为35%，低于行业平均水平(42%)。建议优化教练排班，提高单位时间产出，
                        或考虑调整定价策略，提高高端私教课程的比例。
                      </p>
                      <div className="flex items-center gap-2 mt-3">
                        <span className="text-sm">预计提升空间:</span>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          +15%
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-green-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">优化商品销售结构</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        商品销售的毛利率为45%，分析显示高端瑜伽装备的毛利率可达65%。建议增加高端瑜伽装备的比例，
                        减少低毛利商品的库存，优化商品销售结构。
                      </p>
                      <div className="flex items-center gap-2 mt-3">
                        <span className="text-sm">预计提升空间:</span>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          +20%
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-green-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">提高场地利用率</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        场地利用率为68%，低于行业平均水平(75%)。建议优化课程安排，增加高峰时段的课程密度，
                        并在非高峰时段推出特价课程，提高整体场地利用率。
                      </p>
                      <div className="flex items-center gap-2 mt-3">
                        <span className="text-sm">预计提升空间:</span>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          +10%
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 成本控制 */}
        <TabsContent value="costcontrol" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">总成本</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥195.0万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +5.4%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">成本收入比</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">65.2%</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingDown className="mr-1 h-3 w-3" />
                    -2.1%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">潜在节约空间</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥15.8万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    8.1%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">占总成本</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>成本构成分析</CardTitle>
              <CardDescription>各类成本占比及趋势分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <div className="flex h-full">
                    {/* 左侧饼图 */}
                    <div className="w-1/2 h-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <Pie
                            data={[
                              { name: "员工薪资", value: 95000, percentage: 49 },
                              { name: "场地租金", value: 45000, percentage: 23 },
                              { name: "设备维护", value: 15000, percentage: 8 },
                              { name: "水电费", value: 18000, percentage: 9 },
                              { name: "营销推广", value: 22000, percentage: 11 },
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={120}
                            fill="#8884d8"
                            dataKey="value"
                            nameKey="name"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {[
                              { name: "员工薪资", value: 95000, percentage: 49 },
                              { name: "场地租金", value: 45000, percentage: 23 },
                              { name: "设备维护", value: 15000, percentage: 8 },
                              { name: "水电费", value: 18000, percentage: 9 },
                              { name: "营销推广", value: 22000, percentage: 11 },
                            ].map((entry, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={[
                                  "#ef4444", // 红色 - 员工薪资
                                  "#f97316", // 橙色 - 场地租金
                                  "#3b82f6", // 蓝色 - 设备维护
                                  "#06b6d4", // 青色 - 水电费
                                  "#a855f7"  // 紫色 - 营销推广
                                ][index % 5]}
                              />
                            ))}
                          </Pie>
                          <RechartsTooltip
                            formatter={(value, name) => [
                              `¥${Number(value).toLocaleString()}`,
                              name
                            ]}
                          />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </div>

                    {/* 右侧堆叠面积图 */}
                    <div className="w-1/2 h-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart
                          data={[
                            { month: "1月", salary: 92000, rent: 45000, maintenance: 14000, utilities: 17000, marketing: 20000 },
                            { month: "2月", salary: 93000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 21000 },
                            { month: "3月", salary: 94000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 21000 },
                            { month: "4月", salary: 94000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 22000 },
                            { month: "5月", salary: 95000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 22000 },
                            { month: "6月", salary: 95000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 22000 },
                          ]}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 10,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis
                            tickFormatter={(value) => `${value / 10000}万`}
                          />
                          <RechartsTooltip
                            formatter={(value, name) => {
                              const labels = {
                                salary: "员工薪资",
                                rent: "场地租金",
                                maintenance: "设备维护",
                                utilities: "水电费",
                                marketing: "营销推广"
                              };
                              return [`¥${Number(value).toLocaleString()}`, labels[name]];
                            }}
                          />
                          <Legend
                            formatter={(value) => {
                              const labels = {
                                salary: "员工薪资",
                                rent: "场地租金",
                                maintenance: "设备维护",
                                utilities: "水电费",
                                marketing: "营销推广"
                              };
                              return labels[value];
                            }}
                          />
                          <Area
                            type="monotone"
                            dataKey="marketing"
                            stackId="1"
                            stroke="#a855f7"
                            fill="#a855f7"
                          />
                          <Area
                            type="monotone"
                            dataKey="utilities"
                            stackId="1"
                            stroke="#06b6d4"
                            fill="#06b6d4"
                          />
                          <Area
                            type="monotone"
                            dataKey="maintenance"
                            stackId="1"
                            stroke="#3b82f6"
                            fill="#3b82f6"
                          />
                          <Area
                            type="monotone"
                            dataKey="rent"
                            stackId="1"
                            stroke="#f97316"
                            fill="#f97316"
                          />
                          <Area
                            type="monotone"
                            dataKey="salary"
                            stackId="1"
                            stroke="#ef4444"
                            fill="#ef4444"
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>成本控制建议</CardTitle>
                  <CardDescription>基于数据分析的成本控制建议</CardDescription>
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="筛选优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有优先级</SelectItem>
                    <SelectItem value="high">高优先级</SelectItem>
                    <SelectItem value="medium">中优先级</SelectItem>
                    <SelectItem value="low">低优先级</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {costControlSuggestions.map((suggestion) => (
                  <div key={suggestion.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex items-start gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                          suggestion.priority === "high" ? "bg-red-50" :
                          suggestion.priority === "medium" ? "bg-yellow-50" : "bg-blue-50"
                        }`}>
                          <CircleDollarSign className={`h-4 w-4 ${
                            suggestion.priority === "high" ? "text-red-500" :
                            suggestion.priority === "medium" ? "text-yellow-500" : "text-blue-500"
                          }`} />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{suggestion.category}</h3>
                            <Badge variant={
                              suggestion.priority === "high" ? "destructive" :
                              suggestion.priority === "medium" ? "secondary" : "outline"
                            }>
                              {suggestion.priority === "high" ? "高优先级" :
                               suggestion.priority === "medium" ? "中优先级" : "低优先级"}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {suggestion.suggestion}
                          </p>
                        </div>
                      </div>
                      <Badge variant={suggestion.status === "implemented" ? "outline" : "default"} className={
                        suggestion.status === "implemented" ? "bg-green-50 text-green-700 border-green-200" : ""
                      }>
                        {suggestion.status === "implemented" ? "已实施" : "待实施"}
                      </Badge>
                    </div>

                    <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">当前成本</p>
                        <p className="font-medium">¥{suggestion.currentCost.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">潜在节约</p>
                        <p className="font-medium">¥{suggestion.potentialSaving.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">节约比例</p>
                        <p className="font-medium">{suggestion.savingPercentage}%</p>
                      </div>
                    </div>

                    <div className="mt-3 flex justify-end">
                      {suggestion.status === "pending" ? (
                        <Button variant="outline" size="sm">
                          标记为已实施
                        </Button>
                      ) : (
                        <Button variant="outline" size="sm">
                          查看详情
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>成本趋势</CardTitle>
              <CardDescription>各类成本的历史趋势分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={[
                      { month: "1月", salary: 92000, rent: 45000, maintenance: 14000, utilities: 17000, marketing: 20000, total: 188000 },
                      { month: "2月", salary: 93000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 21000, total: 192000 },
                      { month: "3月", salary: 94000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 21000, total: 193000 },
                      { month: "4月", salary: 94000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 22000, total: 194000 },
                      { month: "5月", salary: 95000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 22000, total: 195000 },
                      { month: "6月", salary: 95000, rent: 45000, maintenance: 15000, utilities: 18000, marketing: 22000, total: 195000 },
                      { month: "7月", salary: 96000, rent: 46000, maintenance: 15000, utilities: 19000, marketing: 23000, total: 199000 },
                      { month: "8月", salary: 97000, rent: 46000, maintenance: 16000, utilities: 19000, marketing: 23000, total: 201000 },
                      { month: "9月", salary: 97000, rent: 46000, maintenance: 16000, utilities: 19000, marketing: 24000, total: 202000 },
                      { month: "10月", salary: 98000, rent: 46000, maintenance: 16000, utilities: 20000, marketing: 24000, total: 204000 },
                      { month: "11月", salary: 99000, rent: 46000, maintenance: 16000, utilities: 20000, marketing: 25000, total: 206000 },
                      { month: "12月", salary: 100000, rent: 46000, maintenance: 17000, utilities: 21000, marketing: 25000, total: 209000 },
                    ]}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 10,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis
                      tickFormatter={(value) => `${value / 10000}万`}
                    />
                    <RechartsTooltip
                      formatter={(value, name) => {
                        const labels = {
                          salary: "员工薪资",
                          rent: "场地租金",
                          maintenance: "设备维护",
                          utilities: "水电费",
                          marketing: "营销推广",
                          total: "总成本"
                        };
                        return [`¥${Number(value).toLocaleString()}`, labels[name]];
                      }}
                    />
                    <Legend
                      formatter={(value) => {
                        const labels = {
                          salary: "员工薪资",
                          rent: "场地租金",
                          maintenance: "设备维护",
                          utilities: "水电费",
                          marketing: "营销推广",
                          total: "总成本"
                        };
                        return labels[value];
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="total"
                      stroke="#000000"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="salary"
                      stroke="#ef4444"
                      strokeWidth={1.5}
                    />
                    <Line
                      type="monotone"
                      dataKey="rent"
                      stroke="#f97316"
                      strokeWidth={1.5}
                    />
                    <Line
                      type="monotone"
                      dataKey="maintenance"
                      stroke="#3b82f6"
                      strokeWidth={1.5}
                    />
                    <Line
                      type="monotone"
                      dataKey="utilities"
                      stroke="#06b6d4"
                      strokeWidth={1.5}
                    />
                    <Line
                      type="monotone"
                      dataKey="marketing"
                      stroke="#a855f7"
                      strokeWidth={1.5}
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 税务优化 */}
        <TabsContent value="tax" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">应纳税所得额</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥89.5万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingDown className="mr-1 h-3 w-3" />
                    -3.2%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">实际税负</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥19.1万</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingDown className="mr-1 h-3 w-3" />
                    -5.4%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">有效税率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">21.3%</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingDown className="mr-1 h-3 w-3" />
                    -2.1%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上期</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>税务优化建议</CardTitle>
                  <CardDescription>基于税法分析的税务优化建议</CardDescription>
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="筛选复杂度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有复杂度</SelectItem>
                    <SelectItem value="low">低复杂度</SelectItem>
                    <SelectItem value="medium">中复杂度</SelectItem>
                    <SelectItem value="high">高复杂度</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {taxOptimizationSuggestions.map((suggestion) => (
                  <div key={suggestion.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex items-start gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                          suggestion.complexity === "low" ? "bg-green-50" :
                          suggestion.complexity === "medium" ? "bg-yellow-50" : "bg-red-50"
                        }`}>
                          <Landmark className={`h-4 w-4 ${
                            suggestion.complexity === "low" ? "text-green-500" :
                            suggestion.complexity === "medium" ? "text-yellow-500" : "text-red-500"
                          }`} />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{suggestion.category}</h3>
                            <Badge variant="outline" className={
                              suggestion.complexity === "low" ? "bg-green-50 text-green-700 border-green-200" :
                              suggestion.complexity === "medium" ? "bg-yellow-50 text-yellow-700 border-yellow-200" :
                              "bg-red-50 text-red-700 border-red-200"
                            }>
                              {suggestion.complexity === "low" ? "低复杂度" :
                               suggestion.complexity === "medium" ? "中复杂度" : "高复杂度"}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">
                            {suggestion.suggestion}
                          </p>
                        </div>
                      </div>
                      <Badge variant={suggestion.status === "implemented" ? "outline" : "default"} className={
                        suggestion.status === "implemented" ? "bg-green-50 text-green-700 border-green-200" : ""
                      }>
                        {suggestion.status === "implemented" ? "已实施" : "待实施"}
                      </Badge>
                    </div>

                    <div className="mt-3 grid grid-cols-1 gap-2 text-sm">
                      <div>
                        <p className="text-muted-foreground">潜在节税</p>
                        <p className="font-medium">¥{suggestion.potentialSaving.toLocaleString()}</p>
                      </div>
                    </div>

                    <div className="mt-3 flex justify-end">
                      {suggestion.status === "pending" ? (
                        <Button variant="outline" size="sm">
                          咨询专业顾问
                        </Button>
                      ) : (
                        <Button variant="outline" size="sm">
                          查看详情
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>税务日历</CardTitle>
              <CardDescription>重要税务日期和提醒</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4 bg-yellow-50 border-yellow-200">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">增值税申报截止日</h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        7月15日前需完成6月增值税申报，请确保所有发票已开具并核对无误。
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <Calendar className="h-4 w-4 text-blue-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">企业所得税季度预缴</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        7月20日前需完成第二季度企业所得税预缴申报。
                      </p>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-green-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">个人所得税代扣代缴</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        已完成6月个人所得税代扣代缴申报。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 报表设置对话框 */}
      {showReportSettings && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-3xl max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">报表设置</h2>
                <Button variant="ghost" size="icon" onClick={() => setShowReportSettings(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-6">
                <Tabs defaultValue="general" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="general">基本设置</TabsTrigger>
                    <TabsTrigger value="content">内容设置</TabsTrigger>
                    <TabsTrigger value="export">导出设置</TabsTrigger>
                    <TabsTrigger value="advanced">高级设置</TabsTrigger>
                  </TabsList>

                  {/* 基本设置 */}
                  <TabsContent value="general" className="space-y-4 mt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="fiscal-year" className="block mb-2">财年设置</Label>
                        <Select defaultValue="calendar">
                          <SelectTrigger id="fiscal-year">
                            <SelectValue placeholder="选择财年类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="calendar">日历年度 (1月-12月)</SelectItem>
                            <SelectItem value="fiscal-1">财政年度 (4月-3月)</SelectItem>
                            <SelectItem value="fiscal-2">财政年度 (7月-6月)</SelectItem>
                            <SelectItem value="custom">自定义</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="currency" className="block mb-2">货币单位</Label>
                        <Select defaultValue="cny">
                          <SelectTrigger id="currency">
                            <SelectValue placeholder="选择货币单位" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="cny">人民币 (CNY)</SelectItem>
                            <SelectItem value="usd">美元 (USD)</SelectItem>
                            <SelectItem value="eur">欧元 (EUR)</SelectItem>
                            <SelectItem value="hkd">港币 (HKD)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="decimal-places" className="block mb-2">小数位数</Label>
                        <Select defaultValue="2">
                          <SelectTrigger id="decimal-places">
                            <SelectValue placeholder="选择小数位数" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="0">0位 (整数)</SelectItem>
                            <SelectItem value="1">1位</SelectItem>
                            <SelectItem value="2">2位</SelectItem>
                            <SelectItem value="3">3位</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="default-view" className="block mb-2">默认视图</Label>
                        <Select defaultValue="overview">
                          <SelectTrigger id="default-view">
                            <SelectValue placeholder="选择默认视图" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="overview">财务概览</SelectItem>
                            <SelectItem value="cashflow">现金流分析</SelectItem>
                            <SelectItem value="profitloss">盈亏分析</SelectItem>
                            <SelectItem value="costcontrol">成本控制</SelectItem>
                            <SelectItem value="tax">税务优化</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-4 mt-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="auto-refresh">自动刷新数据</Label>
                          <span className="text-xs text-muted-foreground">(每天自动更新财务数据)</span>
                        </div>
                        <Switch id="auto-refresh" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="data-cache">缓存数据</Label>
                          <span className="text-xs text-muted-foreground">(缓存报表数据以提高加载速度)</span>
                        </div>
                        <Switch id="data-cache" defaultChecked />
                      </div>
                    </div>
                  </TabsContent>

                  {/* 内容设置 */}
                  <TabsContent value="content" className="space-y-4 mt-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="show-comparison">显示同比/环比</Label>
                          <span className="text-xs text-muted-foreground">(显示与上期的对比数据)</span>
                        </div>
                        <Switch id="show-comparison" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="show-charts">显示图表</Label>
                          <span className="text-xs text-muted-foreground">(在报表中显示可视化图表)</span>
                        </div>
                        <Switch id="show-charts" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="show-suggestions">显示优化建议</Label>
                          <span className="text-xs text-muted-foreground">(显示基于AI的财务优化建议)</span>
                        </div>
                        <Switch id="show-suggestions" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="show-details">显示详细数据</Label>
                          <span className="text-xs text-muted-foreground">(显示更详细的财务数据和分析)</span>
                        </div>
                        <Switch id="show-details" defaultChecked />
                      </div>
                    </div>

                    <Separator className="my-4" />

                    <div>
                      <Label className="block mb-2">自定义指标</Label>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Checkbox id="kpi-1" defaultChecked />
                          <Label htmlFor="kpi-1" className="text-sm">会员续约率</Label>
                        </div>
                        <div className="flex items-center gap-2">
                          <Checkbox id="kpi-2" defaultChecked />
                          <Label htmlFor="kpi-2" className="text-sm">客单价</Label>
                        </div>
                        <div className="flex items-center gap-2">
                          <Checkbox id="kpi-3" defaultChecked />
                          <Label htmlFor="kpi-3" className="text-sm">新增会员数</Label>
                        </div>
                        <div className="flex items-center gap-2">
                          <Checkbox id="kpi-4" defaultChecked />
                          <Label htmlFor="kpi-4" className="text-sm">现金流比率</Label>
                        </div>
                        <div className="flex items-center gap-2">
                          <Checkbox id="kpi-5" defaultChecked />
                          <Label htmlFor="kpi-5" className="text-sm">资产周转率</Label>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" className="mt-2">
                        <Plus className="h-4 w-4 mr-1" />
                        添加自定义指标
                      </Button>
                    </div>
                  </TabsContent>

                  {/* 导出设置 */}
                  <TabsContent value="export" className="space-y-4 mt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="export-format" className="block mb-2">默认导出格式</Label>
                        <Select defaultValue="excel">
                          <SelectTrigger id="export-format">
                            <SelectValue placeholder="选择导出格式" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                            <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                            <SelectItem value="csv">CSV (.csv)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="export-period" className="block mb-2">导出时间范围</Label>
                        <Select defaultValue="current">
                          <SelectTrigger id="export-period">
                            <SelectValue placeholder="选择导出时间范围" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="current">当前选择的时间范围</SelectItem>
                            <SelectItem value="month">本月</SelectItem>
                            <SelectItem value="quarter">本季度</SelectItem>
                            <SelectItem value="year">本年度</SelectItem>
                            <SelectItem value="all">全部数据</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-4 mt-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="include-logo">包含公司标志</Label>
                          <span className="text-xs text-muted-foreground">(在导出的报表中显示公司标志)</span>
                        </div>
                        <Switch id="include-logo" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="include-charts">包含图表</Label>
                          <span className="text-xs text-muted-foreground">(在导出的报表中包含图表)</span>
                        </div>
                        <Switch id="include-charts" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="include-details">包含详细数据</Label>
                          <span className="text-xs text-muted-foreground">(在导出的报表中包含详细数据)</span>
                        </div>
                        <Switch id="include-details" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="auto-email">自动发送报表</Label>
                          <span className="text-xs text-muted-foreground">(每月自动发送报表到指定邮箱)</span>
                        </div>
                        <Switch id="auto-email" />
                      </div>
                    </div>

                    <div className="mt-4">
                      <Label htmlFor="email-recipients" className="block mb-2">报表接收邮箱</Label>
                      <Input id="email-recipients" placeholder="输入邮箱地址，多个邮箱用逗号分隔" />
                    </div>
                  </TabsContent>

                  {/* 高级设置 */}
                  <TabsContent value="advanced" className="space-y-4 mt-4">
                    <div>
                      <Label htmlFor="data-source" className="block mb-2">数据源设置</Label>
                      <Select defaultValue="auto">
                        <SelectTrigger id="data-source">
                          <SelectValue placeholder="选择数据源" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="auto">自动选择最佳数据源</SelectItem>
                          <SelectItem value="primary">主数据库</SelectItem>
                          <SelectItem value="backup">备份数据库</SelectItem>
                          <SelectItem value="cache">本地缓存</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="calculation-method" className="block mb-2">计算方法</Label>
                      <Select defaultValue="standard">
                        <SelectTrigger id="calculation-method">
                          <SelectValue placeholder="选择计算方法" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="standard">标准会计方法</SelectItem>
                          <SelectItem value="cash">现金流量法</SelectItem>
                          <SelectItem value="accrual">权责发生制</SelectItem>
                          <SelectItem value="custom">自定义方法</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-4 mt-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="enable-ai">启用AI分析</Label>
                          <span className="text-xs text-muted-foreground">(使用AI技术分析财务数据并提供建议)</span>
                        </div>
                        <Switch id="enable-ai" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="enable-forecast">启用预测功能</Label>
                          <span className="text-xs text-muted-foreground">(基于历史数据预测未来财务趋势)</span>
                        </div>
                        <Switch id="enable-forecast" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Label htmlFor="debug-mode">调试模式</Label>
                          <span className="text-xs text-muted-foreground">(显示详细的计算过程和数据来源)</span>
                        </div>
                        <Switch id="debug-mode" />
                      </div>
                    </div>

                    <div className="mt-4">
                      <Label htmlFor="custom-script" className="block mb-2">自定义计算脚本</Label>
                      <Textarea
                        id="custom-script"
                        placeholder="// 在此输入自定义JavaScript计算脚本"
                        className="font-mono text-xs h-[100px]"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        高级用户可以编写自定义脚本来处理财务数据。请确保脚本安全且符合业务逻辑。
                      </p>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              <div className="flex justify-between gap-2 mt-6">
                <Button variant="outline" onClick={() => setShowReportSettings(false)}>
                  取消
                </Button>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => alert("设置已重置为默认值")}>
                    重置为默认值
                  </Button>
                  <Button onClick={() => {
                    setShowReportSettings(false)
                    // 这里可以添加保存设置的逻辑
                    alert("设置已保存")
                  }}>
                    保存设置
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}