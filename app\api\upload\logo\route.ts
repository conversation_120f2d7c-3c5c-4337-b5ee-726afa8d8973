import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { prisma } from '@/lib/db';

// 处理Logo上传的API
export async function POST(request: NextRequest) {
  try {
    // 解析multipart/form-data
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const tenantId = formData.get('tenantId') as string | null;

    // 验证请求
    if (!file) {
      return NextResponse.json({ success: false, error: '未提供文件' }, { status: 400 });
    }

    if (!tenantId) {
      return NextResponse.json({ success: false, error: '未提供租户ID' }, { status: 400 });
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ success: false, error: '只允许上传图片文件' }, { status: 400 });
    }

    // 验证文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
      return NextResponse.json({ success: false, error: '文件大小不能超过2MB' }, { status: 400 });
    }

    // 创建文件名（使用时间戳和原始文件名）
    const timestamp = Date.now();
    const originalName = file.name.replace(/\s+/g, '_').toLowerCase();
    const fileExtension = originalName.split('.').pop() || 'png';
    const fileName = `logo_${tenantId}_${timestamp}.${fileExtension}`;

    // 确保上传目录存在
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'logos');
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      console.error('创建目录失败:', error);
    }

    // 保存文件路径
    const filePath = join(uploadDir, fileName);
    
    // 将文件内容转换为Buffer
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    
    // 写入文件
    await writeFile(filePath, fileBuffer);
    
    // 生成可访问的URL
    const fileUrl = `/uploads/logos/${fileName}`;
    
    // 更新租户的logoUrl
    await prisma.tenant.update({
      where: { id: parseInt(tenantId) },
      data: { logo_url: fileUrl }
    });

    // 返回成功响应
    return NextResponse.json({
      success: true,
      url: fileUrl,
      message: 'Logo上传成功'
    });
  } catch (error: any) {
    console.error('Logo上传失败:', error);
    return NextResponse.json({ 
      success: false, 
      error: `上传失败: ${error.message || '服务器错误'}` 
    }, { status: 500 });
  }
} 