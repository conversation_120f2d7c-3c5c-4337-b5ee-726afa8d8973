import { NextRequest, NextResponse } from 'next/server';

// 获取会员卡状态列表
export async function GET(request: NextRequest) {
  try {
    // 模拟会员卡状态数据
    const cardStatus = [
      { id: 1, name: '销售中', color: '#34A853' },
      { id: 2, name: '已下架', color: '#9E9E9E' }
    ];
    
    return NextResponse.json({
      code: 0,
      data: cardStatus,
      msg: '获取会员卡状态列表成功'
    });
  } catch (error) {
    console.error('获取会员卡状态列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取会员卡状态列表失败',
      data: null
    }, { status: 500 });
  }
} 