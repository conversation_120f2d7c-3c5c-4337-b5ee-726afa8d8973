"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { FileText, Settings, UserCog, CheckCircle, AlertCircle, Clock } from "lucide-react"

export function StandardsManagementCard() {
  // 标准文档数据
  const standardDocs = [
    {
      id: "1",
      name: "会员服务标准手册",
      category: "服务标准",
      status: "published",
      updatedAt: "2023-05-15",
      compliance: 95,
    },
    {
      id: "2",
      name: "教练培训与授课规范",
      category: "教练标准",
      status: "published",
      updatedAt: "2023-04-20",
      compliance: 88,
    },
    {
      id: "3",
      name: "门店运营管理手册",
      category: "运营标准",
      status: "draft",
      updatedAt: "2023-06-01",
      compliance: 78,
    },
  ]

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "published":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "draft":
        return <Clock className="h-4 w-4 text-orange-500" />
      case "review":
        return <AlertCircle className="h-4 w-4 text-blue-500" />
      default:
        return null
    }
  }

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case "published":
        return "已发布"
      case "draft":
        return "草稿"
      case "review":
        return "审核中"
      default:
        return ""
    }
  }

  // 获取类别图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "服务标准":
        return <FileText className="h-4 w-4 text-blue-500" />
      case "教练标准":
        return <UserCog className="h-4 w-4 text-green-500" />
      case "运营标准":
        return <Settings className="h-4 w-4 text-orange-500" />
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-500" />
          标准化管理
        </CardTitle>
        <CardDescription>连锁门店标准化文档和执行情况</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {standardDocs.map((doc) => (
            <div key={doc.id} className="flex items-start justify-between border-b pb-3">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  {getCategoryIcon(doc.category)}
                  <span className="font-medium">{doc.name}</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Badge variant="outline" className="text-xs">
                    {doc.category}
                  </Badge>
                  <span>更新于 {doc.updatedAt}</span>
                </div>
              </div>
              <div className="flex flex-col items-end gap-1">
                <Badge
                  variant={doc.status === "published" ? "default" : "outline"}
                  className="flex items-center gap-1"
                >
                  {getStatusIcon(doc.status)}
                  <span>{getStatusText(doc.status)}</span>
                </Badge>
                <div className="flex items-center gap-1 text-sm">
                  <span>执行率:</span>
                  <span className={`font-medium ${
                    doc.compliance >= 90 
                      ? "text-green-600" 
                      : doc.compliance >= 80 
                        ? "text-amber-600" 
                        : "text-red-600"
                  }`}>
                    {doc.compliance}%
                  </span>
                </div>
              </div>
            </div>
          ))}

          <div className="pt-2">
            <h4 className="text-sm font-medium mb-3">标准执行情况</h4>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">朝阳店</span>
                  <span className="text-sm font-medium">92%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: "92%" }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">西城店</span>
                  <span className="text-sm font-medium">88%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: "88%" }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">海淀店</span>
                  <span className="text-sm font-medium">95%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: "95%" }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm">通州店</span>
                  <span className="text-sm font-medium">78%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-amber-600 h-2 rounded-full" style={{ width: "78%" }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full">管理标准文档</Button>
      </CardFooter>
    </Card>
  )
}
