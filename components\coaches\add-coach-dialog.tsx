"use client"

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

interface AddCoachDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddCoachDialog({ open, onOpenChange }: AddCoachDialogProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isRedirecting, setIsRedirecting] = useState(false)

  // 当对话框打开时，重定向到员工管理页面并自动打开添加员工对话框
  useEffect(() => {
    if (open && !isRedirecting) {
      setIsRedirecting(true)

      // 显示提示
      toast({
        title: "正在跳转到员工管理页面",
        description: "将自动为您打开添加教练表单",
      })

      // 使用查询参数标记需要打开添加教练对话框
      router.push("/venue-management/staff?openAddCoach=true")

      // 关闭当前对话框
      onOpenChange(false)
    }
  }, [open, isRedirecting, router, onOpenChange, toast])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>添加教练</DialogTitle>
          <DialogDescription>正在跳转到员工管理页面...</DialogDescription>
        </DialogHeader>
        <div className="py-6 flex justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

