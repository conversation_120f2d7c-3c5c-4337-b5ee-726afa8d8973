"use client"

import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { StarIcon, MessageSquare, Flag, Eye, EyeOff, CheckCircle, AlertCircle } from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useState } from "react"

interface ReviewDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  review: any
}

export function ReviewDetailDialog({ open, onOpenChange, review }: ReviewDetailDialogProps) {
  const [replyText, setReplyText] = useState("")

  if (!review) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>评价详情</DialogTitle>
          <DialogDescription>查看和管理学员评价</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="detail">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="detail">评价详情</TabsTrigger>
            <TabsTrigger value="reply">回复管理</TabsTrigger>
            <TabsTrigger value="history">操作历史</TabsTrigger>
          </TabsList>

          <TabsContent value="detail" className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={review.member.avatar} alt={review.member.name} />
                  <AvatarFallback>{review.member.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold">{review.member.name}</h3>
                  <div className="text-sm text-muted-foreground">{review.date}</div>
                </div>
              </div>
              <Badge
                variant={
                  review.status === "published" ? "default" : review.status === "pending" ? "secondary" : "outline"
                }
              >
                {review.status === "published" ? "已发布" : review.status === "pending" ? "待审核" : "已隐藏"}
              </Badge>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>评价内容</CardTitle>
                <CardDescription>学员对课程的评价和反馈</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-muted-foreground">课程信息</Label>
                  <p className="font-medium">{review.course}</p>
                </div>
                <div>
                  <Label className="text-muted-foreground">评分</Label>
                  <div className="flex items-center mt-1">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <StarIcon
                        key={i}
                        className={`h-5 w-5 ${i < review.rating ? "text-yellow-500 fill-yellow-500" : "text-gray-300"}`}
                      />
                    ))}
                    <span className="ml-2 font-medium">{review.rating}/5</span>
                  </div>
                </div>
                <div>
                  <Label className="text-muted-foreground">评价内容</Label>
                  <p className="mt-1">{review.comment}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>会员信息</CardTitle>
                <CardDescription>提交评价的会员信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <Label className="text-muted-foreground">会员姓名</Label>
                    <p className="font-medium">{review.member.name}</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">会员ID</Label>
                    <p className="font-medium">M10086</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">会员等级</Label>
                    <p className="font-medium">黄金会员</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">注册时间</Label>
                    <p className="font-medium">2024-10-15</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">课程参与次数</Label>
                    <p className="font-medium">12次</p>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">历史评价数</Label>
                    <p className="font-medium">5条</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reply" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>回复评价</CardTitle>
                <CardDescription>对学员的评价进行回复</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-muted-foreground">评价内容</Label>
                  <p className="mt-1 p-3 bg-muted rounded-md">{review.comment}</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reply">回复内容</Label>
                  <Textarea
                    id="reply"
                    placeholder="请输入回复内容"
                    value={replyText}
                    onChange={(e) => setReplyText(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>
                <div className="flex justify-end">
                  <Button>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    发布回复
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>历史回复</CardTitle>
                <CardDescription>查看历史回复记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {review.id === 101 && (
                    <div className="border rounded-md p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src="/placeholder.svg?height=32&width=32" alt="管理员" />
                            <AvatarFallback>管</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">管理员</div>
                            <div className="text-xs text-muted-foreground">2025-03-26 14:30</div>
                          </div>
                        </div>
                      </div>
                      <p className="text-sm">
                        感谢您的评价和支持！我们会继续努力提供优质的课程和服务。期待您的下次光临！
                      </p>
                    </div>
                  )}
                  {review.id !== 101 && <div className="text-center text-muted-foreground py-8">暂无历史回复记录</div>}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>操作历史</CardTitle>
                <CardDescription>评价的审核和操作记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border-l-2 border-muted pl-4 space-y-4">
                    <div className="relative">
                      <div className="absolute -left-6 top-1 h-3 w-3 rounded-full bg-primary"></div>
                      <div>
                        <div className="font-medium">评价提交</div>
                        <div className="text-sm text-muted-foreground">2025-03-25 10:15</div>
                        <div className="text-sm mt-1">会员 {review.member.name} 提交了评价</div>
                      </div>
                    </div>
                    {review.id === 101 && (
                      <>
                        <div className="relative">
                          <div className="absolute -left-6 top-1 h-3 w-3 rounded-full bg-green-500"></div>
                          <div>
                            <div className="font-medium">评价审核通过</div>
                            <div className="text-sm text-muted-foreground">2025-03-25 14:30</div>
                            <div className="text-sm mt-1">管理员审核通过并发布评价</div>
                          </div>
                        </div>
                        <div className="relative">
                          <div className="absolute -left-6 top-1 h-3 w-3 rounded-full bg-blue-500"></div>
                          <div>
                            <div className="font-medium">回复评价</div>
                            <div className="text-sm text-muted-foreground">2025-03-26 14:30</div>
                            <div className="text-sm mt-1">管理员回复了评价</div>
                          </div>
                        </div>
                      </>
                    )}
                    {review.id === 102 && (
                      <div className="relative">
                        <div className="absolute -left-6 top-1 h-3 w-3 rounded-full bg-green-500"></div>
                        <div>
                          <div className="font-medium">评价审核通过</div>
                          <div className="text-sm text-muted-foreground">2025-03-20 16:45</div>
                          <div className="text-sm mt-1">管理员审核通过并发布评价</div>
                        </div>
                      </div>
                    )}
                    {review.id === 202 && (
                      <div className="relative">
                        <div className="absolute -left-6 top-1 h-3 w-3 rounded-full bg-yellow-500"></div>
                        <div>
                          <div className="font-medium">等待审核</div>
                          <div className="text-sm text-muted-foreground">2025-03-22 11:20</div>
                          <div className="text-sm mt-1">评价正在等待审核</div>
                        </div>
                      </div>
                    )}
                    {review.id === 302 && (
                      <>
                        <div className="relative">
                          <div className="absolute -left-6 top-1 h-3 w-3 rounded-full bg-green-500"></div>
                          <div>
                            <div className="font-medium">评价审核通过</div>
                            <div className="text-sm text-muted-foreground">2025-03-18 15:10</div>
                            <div className="text-sm mt-1">管理员审核通过并发布评价</div>
                          </div>
                        </div>
                        <div className="relative">
                          <div className="absolute -left-6 top-1 h-3 w-3 rounded-full bg-gray-500"></div>
                          <div>
                            <div className="font-medium">隐藏评价</div>
                            <div className="text-sm text-muted-foreground">2025-03-19 09:25</div>
                            <div className="text-sm mt-1">管理员将评价设为隐藏</div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex flex-col sm:flex-row sm:justify-between sm:space-x-0">
          <div className="flex space-x-2 mb-4 sm:mb-0">
            <Button variant="outline" className="gap-2">
              <Flag className="h-4 w-4" />
              标记
            </Button>
            {review.status === "published" ? (
              <Button variant="outline" className="gap-2">
                <EyeOff className="h-4 w-4" />
                隐藏
              </Button>
            ) : review.status === "hidden" ? (
              <Button variant="outline" className="gap-2">
                <Eye className="h-4 w-4" />
                显示
              </Button>
            ) : null}
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
            {review.status === "pending" && (
              <>
                <Button variant="destructive" className="gap-2">
                  <AlertCircle className="h-4 w-4" />
                  拒绝
                </Button>
                <Button className="gap-2">
                  <CheckCircle className="h-4 w-4" />
                  通过
                </Button>
              </>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

