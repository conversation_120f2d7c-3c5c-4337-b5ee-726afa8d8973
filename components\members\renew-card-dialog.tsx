"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON>alog<PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { CreditCard, CalendarIcon, Plus } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format, addMonths } from "date-fns"
import { zh } from "date-fns/locale"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface RenewCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
    card: string
    remaining: string
    expiry: string
  }
}

export function RenewCardDialog({ open, onOpenChange, member }: RenewCardDialogProps) {
  const { toast } = useToast()
  const [renewType, setRenewType] = useState("same")
  const [selectedPlan, setSelectedPlan] = useState("3")
  const [customDate, setCustomDate] = useState<Date>()
  const [paymentMethod, setPaymentMethod] = useState("wechat")
  const [amount, setAmount] = useState("1200")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 计算续费后的到期日期
  const getNewExpiryDate = () => {
    const currentExpiry = new Date(member.expiry)
    if (renewType === "custom" && customDate) {
      return format(customDate, 'yyyy-MM-dd')
    } else {
      const months = parseInt(selectedPlan)
      return format(addMonths(currentExpiry, months), 'yyyy-MM-dd')
    }
  }

  const handleSubmit = async () => {
    if (renewType === "custom" && !customDate) {
      toast({
        title: "请选择日期",
        description: "自定义续费需要选择到期日期",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "会员卡续费成功",
        description: `${member.name}的会员卡已成功续费，新到期日期：${getNewExpiryDate()}`,
      })
      
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "操作失败",
        description: "会员卡续费失败，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>续费会员卡</DialogTitle>
          <DialogDescription>
            为会员延长会员卡有效期
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex items-center gap-4">
              <CreditCard className="h-8 w-8 text-muted-foreground" />
              <div>
                <h4 className="font-medium">{member.name}</h4>
                <p className="text-sm text-muted-foreground">
                  {member.card} (到期: {member.expiry})
                </p>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>续费方式</Label>
            <RadioGroup value={renewType} onValueChange={setRenewType} className="flex flex-col space-y-1">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="same" id="same" />
                <Label htmlFor="same">续费相同类型会员卡</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="custom" id="custom" />
                <Label htmlFor="custom">自定义到期日期</Label>
              </div>
            </RadioGroup>
          </div>
          
          {renewType === "same" ? (
            <div className="space-y-2">
              <Label>选择续费时长</Label>
              <div className="grid grid-cols-3 gap-4">
                {["3", "6", "12"].map((months) => (
                  <Card 
                    key={months}
                    className={cn(
                      "cursor-pointer hover:border-primary",
                      selectedPlan === months ? "border-2 border-primary" : ""
                    )}
                    onClick={() => setSelectedPlan(months)}
                  >
                    <CardContent className="p-4 text-center">
                      <p className="text-lg font-bold">{months}个月</p>
                      <p className="text-sm text-muted-foreground">
                        {months === "3" ? "¥1,200" : months === "6" ? "¥2,200" : "¥3,800"}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
              <p className="text-sm text-muted-foreground">
                续费后到期日期: {getNewExpiryDate()}
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              <Label htmlFor="custom-date">自定义到期日期</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="custom-date"
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {customDate ? format(customDate, 'yyyy-MM-dd') : <span>选择日期</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={customDate}
                    onSelect={setCustomDate}
                    initialFocus
                    locale={zh}
                    disabled={(date) => date < new Date(member.expiry)}
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}
          
          <div className="space-y-2">
            <Label>支付方式</Label>
            <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod} className="flex space-x-4">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="wechat" id="wechat" />
                <Label htmlFor="wechat">微信支付</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="alipay" id="alipay" />
                <Label htmlFor="alipay">支付宝</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="cash" id="cash" />
                <Label htmlFor="cash">现金</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="card" id="card" />
                <Label htmlFor="card">刷卡</Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="amount">支付金额</Label>
            <div className="flex items-center">
              <span className="mr-2">¥</span>
              <Input
                id="amount"
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
              />
            </div>
          </div>
          
          <div className="rounded-md bg-muted p-3 text-sm">
            <p className="font-medium">续费说明：</p>
            <ul className="list-disc pl-5 pt-2 text-muted-foreground">
              <li>续费后，会员卡有效期将延长</li>
              <li>如果是次卡，可以选择增加次数或延长有效期</li>
              <li>续费完成后，系统将自动生成收款记录</li>
            </ul>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>取消</Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "处理中..." : "确认续费"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
