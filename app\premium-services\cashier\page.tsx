"use client"

import React, { useState, useMemo } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  Search,
  ChevronRight,
  CreditCard,
  ShoppingBag,
  Package,
  User,
  Calendar,
  BarChart,
  Clock,
  CheckCircle,
  XCircle,
  Plus,
  Trash,
  Edit,
  Save,
  RefreshCw,
  FileText,
  Printer,
  Download,
  QrCode,
  Smartphone,
  Tag,
  X,
  DollarSign
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// 模拟商品数据
const products = [
  // 实体商品
  { id: 1, name: "瑜伽垫", type: "实体商品", category: "瑜伽装备", price: 199, memberPrice: 179, inventory: 25, hot: true },
  { id: 2, name: "瑜伽服", type: "实体商品", category: "瑜伽装备", price: 299, memberPrice: 269, inventory: 18, hot: true },
  { id: 3, name: "瑜伽砖", type: "实体商品", category: "瑜伽装备", price: 59, memberPrice: 49, inventory: 32, hot: false },
  { id: 4, name: "瑜伽带", type: "实体商品", category: "瑜伽装备", price: 39, memberPrice: 35, inventory: 45, hot: false },
  { id: 5, name: "瑜伽球", type: "实体商品", category: "瑜伽装备", price: 89, memberPrice: 79, inventory: 15, hot: false },
  { id: 6, name: "有机蛋白粉", type: "实体商品", category: "营养补充", price: 259, memberPrice: 229, inventory: 12, hot: true },
  { id: 7, name: "能量棒", type: "实体商品", category: "营养补充", price: 15, memberPrice: 12, inventory: 50, hot: false },
  { id: 8, name: "运动水壶", type: "实体商品", category: "瑜伽装备", price: 69, memberPrice: 59, inventory: 28, hot: false },

  // 虚拟商品
  { id: 101, name: "会员卡-月卡", type: "虚拟商品", category: "会员卡", price: 399, memberPrice: 399, inventory: null, hot: true },
  { id: 102, name: "会员卡-季卡", type: "虚拟商品", category: "会员卡", price: 999, memberPrice: 999, inventory: null, hot: true },
  { id: 103, name: "会员卡-年卡", type: "虚拟商品", category: "会员卡", price: 3299, memberPrice: 3299, inventory: null, hot: true },
  { id: 104, name: "私教课-单次", type: "虚拟商品", category: "课程", price: 299, memberPrice: 269, inventory: null, hot: false },
  { id: 105, name: "私教课-10次", type: "虚拟商品", category: "课程", price: 1999, memberPrice: 1799, inventory: null, hot: true },
  { id: 106, name: "私教课-20次", type: "虚拟商品", category: "课程", price: 3599, memberPrice: 3199, inventory: null, hot: false },
  { id: 107, name: "团体课-单次", type: "虚拟商品", category: "课程", price: 99, memberPrice: 89, inventory: null, hot: false },
  { id: 108, name: "团体课-10次", type: "虚拟商品", category: "课程", price: 799, memberPrice: 699, inventory: null, hot: false },
  { id: 109, name: "团体课-20次", type: "虚拟商品", category: "课程", price: 1299, memberPrice: 1099, inventory: null, hot: true },
]

// 商品分类
const productCategories = [
  { id: "all", name: "全部商品" },
  { id: "hot", name: "热门商品" },
  { id: "瑜伽装备", name: "瑜伽装备" },
  { id: "营养补充", name: "营养补充" },
  { id: "会员卡", name: "会员卡" },
  { id: "课程", name: "课程" },
]

// 模拟会员数据
const members = [
  { id: 1, name: "张三", phone: "13800138001", level: "黄金会员", balance: 2500, points: 1250, discount: 0.9, courses: 12 },
  { id: 2, name: "李四", phone: "13800138002", level: "白银会员", balance: 1200, points: 850, discount: 0.95, courses: 5 },
  { id: 3, name: "王五", phone: "13800138003", level: "铂金会员", balance: 5000, points: 3200, discount: 0.85, courses: 25 },
  { id: 4, name: "赵六", phone: "13800138004", level: "普通会员", balance: 800, points: 450, discount: 0.98, courses: 2 },
  { id: 5, name: "钱七", phone: "13800138005", level: "钻石会员", balance: 8500, points: 5600, discount: 0.8, courses: 48 },
]

// 模拟优惠券数据
const coupons = [
  { id: 1, code: "YOGA10", name: "满200减10", type: "满减", minAmount: 200, value: 10, expires: "2023-12-31" },
  { id: 2, code: "YOGA50", name: "满500减50", type: "满减", minAmount: 500, value: 50, expires: "2023-12-31" },
  { id: 3, code: "YOGA100", name: "满1000减100", type: "满减", minAmount: 1000, value: 100, expires: "2023-12-31" },
  { id: 4, code: "NEWUSER", name: "新用户95折", type: "折扣", value: 0.95, expires: "2023-12-31" },
  { id: 5, code: "VIP90", name: "VIP会员9折", type: "折扣", value: 0.9, expires: "2023-12-31" },
]

// 模拟订单数据
const recentOrders = [
  { id: "ORD20230001", time: "2023-06-01 14:30", amount: 598, items: 2, status: "已完成", type: "会员购买", paymentMethod: "微信支付", member: "张三" },
  { id: "ORD20230002", time: "2023-06-01 16:45", amount: 1299, items: 1, status: "已完成", type: "非会员购买", paymentMethod: "支付宝", member: null },
  { id: "ORD20230003", time: "2023-06-02 10:15", amount: 399, items: 1, status: "已完成", type: "会员购买", paymentMethod: "余额支付", member: "李四" },
  { id: "ORD20230004", time: "2023-06-02 11:30", amount: 199, items: 1, status: "已完成", type: "美团核销", paymentMethod: "已付款", member: null },
  { id: "ORD20230005", time: "2023-06-02 15:20", amount: 1999, items: 1, status: "已完成", type: "抖音核销", paymentMethod: "已付款", member: null },
  { id: "ORD20230006", time: "2023-06-03 09:45", amount: 269, items: 1, status: "已完成", type: "会员购买", paymentMethod: "微信支付", member: "王五" },
  { id: "ORD20230007", time: "2023-06-03 13:20", amount: 1099, items: 1, status: "已完成", type: "会员购买", paymentMethod: "余额支付", member: "张三" },
  { id: "ORD20230008", time: "2023-06-03 16:10", amount: 89, items: 1, status: "已完成", type: "会员购买", paymentMethod: "积分兑换", member: "李四" },
  { id: "ORD20230009", time: "2023-06-04 10:30", amount: 3199, items: 1, status: "已完成", type: "会员购买", paymentMethod: "微信支付", member: "王五" },
  { id: "ORD20230010", time: "2023-06-04 14:15", amount: 299, items: 1, status: "已完成", type: "非会员购买", paymentMethod: "支付宝", member: null },
]

// 模拟渠道订单数据
const channelOrders = [
  { id: "MT123456", platform: "美团", product: "私教体验课", price: 99, purchaseTime: "2023-06-01", expireTime: "2023-07-01", status: "未使用", verificationCode: "987654" },
  { id: "MT234567", platform: "美团", product: "瑜伽团课体验", price: 49, purchaseTime: "2023-06-02", expireTime: "2023-07-02", status: "未使用", verificationCode: "876543" },
  { id: "DY123456", platform: "抖音", product: "瑜伽月卡体验", price: 299, purchaseTime: "2023-06-03", expireTime: "2023-07-03", status: "未使用", verificationCode: "765432" },
  { id: "MT345678", platform: "美团", product: "高温瑜伽体验", price: 69, purchaseTime: "2023-06-04", expireTime: "2023-07-04", status: "已使用", verificationCode: "654321" },
  { id: "DY234567", platform: "抖音", product: "私教体验课", price: 99, purchaseTime: "2023-06-05", expireTime: "2023-07-05", status: "已使用", verificationCode: "543210" },
]

// 模拟销售统计数据
const salesStatistics = {
  today: {
    totalSales: 4586,
    orderCount: 15,
    avgOrderValue: 305.7,
    memberSales: 3287,
    nonMemberSales: 1299,
    productSales: 1587,
    courseSales: 2999
  },
  week: {
    totalSales: 28750,
    orderCount: 87,
    avgOrderValue: 330.5,
    memberSales: 21560,
    nonMemberSales: 7190,
    productSales: 9850,
    courseSales: 18900
  },
  month: {
    totalSales: 125800,
    orderCount: 356,
    avgOrderValue: 353.4,
    memberSales: 98600,
    nonMemberSales: 27200,
    productSales: 42300,
    courseSales: 83500
  }
}

// 使用React.memo优化组件渲染
function CashierPage() {
  const [activeTab, setActiveTab] = useState("cashier")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMember, setSelectedMember] = useState<any>(null)
  const [cart, setCart] = useState<any[]>([])
  const [showMemberSearch, setShowMemberSearch] = useState(false)
  const [memberSearchQuery, setMemberSearchQuery] = useState("")
  const [showProductSearch, setShowProductSearch] = useState(false)
  const [productSearchQuery, setProductSearchQuery] = useState("")
  const [productCategory, setProductCategory] = useState("all")
  const [paymentMethod, setPaymentMethod] = useState("cash")
  const [orderNote, setOrderNote] = useState("")
  const [selectedCoupon, setSelectedCoupon] = useState<any>(null)
  const [showCouponModal, setShowCouponModal] = useState(false)
  const [couponCode, setCouponCode] = useState("")
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [paymentLoading, setPaymentLoading] = useState(false)
  const [paymentSuccess, setPaymentSuccess] = useState(false)
  const [verificationCode, setVerificationCode] = useState("")
  const [showVerificationModal, setShowVerificationModal] = useState(false)
  const [verificationResult, setVerificationResult] = useState<any>(null)
  const [statisticsPeriod, setStatisticsPeriod] = useState("today")
  const [orderSearchQuery, setOrderSearchQuery] = useState("")
  const [orderDateFilter, setOrderDateFilter] = useState("all")
  const [showOrderDetails, setShowOrderDetails] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<any>(null)

  // 使用useMemo优化过滤商品
  const filteredProducts = useMemo(() => {
    return products.filter(
      (product) => {
        // 先按搜索词过滤
        const matchesSearch =
          product.name.toLowerCase().includes(productSearchQuery.toLowerCase()) ||
          product.type.toLowerCase().includes(productSearchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(productSearchQuery.toLowerCase());

        // 再按分类过滤
        const matchesCategory =
          productCategory === "all" ||
          (productCategory === "hot" && product.hot) ||
          product.category === productCategory;

        return matchesSearch && matchesCategory;
      }
    );
  }, [productSearchQuery, productCategory]);

  // 使用useMemo优化过滤会员
  const filteredMembers = useMemo(() => {
    return members.filter(
      (member) =>
        member.name.toLowerCase().includes(memberSearchQuery.toLowerCase()) ||
        member.phone.includes(memberSearchQuery)
    );
  }, [memberSearchQuery]);

  // 使用useMemo优化过滤订单
  const filteredOrders = useMemo(() => {
    return recentOrders.filter(
      (order) => {
        const matchesSearch =
          order.id.toLowerCase().includes(orderSearchQuery.toLowerCase()) ||
          (order.member && order.member.toLowerCase().includes(orderSearchQuery.toLowerCase())) ||
          order.type.toLowerCase().includes(orderSearchQuery.toLowerCase());

        // 日期过滤逻辑可以根据实际需求扩展
        const matchesDate = orderDateFilter === "all" || true;

        return matchesSearch && matchesDate;
      }
    );
  }, [orderSearchQuery, orderDateFilter]);

  // 使用useMemo优化过滤渠道订单
  const filteredChannelOrders = useMemo(() => {
    return channelOrders.filter(
      (order) => order.status === "未使用" || verificationCode === order.verificationCode
    );
  }, [verificationCode]);

  // 添加商品到购物车
  const addToCart = (product: any) => {
    const existingItem = cart.find(item => item.id === product.id)

    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ))
    } else {
      // 如果是会员，使用会员价
      const price = selectedMember ? product.memberPrice : product.price;
      setCart([...cart, { ...product, quantity: 1, appliedPrice: price }])
    }

    setShowProductSearch(false)
    setProductSearchQuery("")
  }

  // 从购物车移除商品
  const removeFromCart = (productId: number) => {
    setCart(cart.filter(item => item.id !== productId))
  }

  // 更新购物车商品数量
  const updateQuantity = (productId: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId)
      return
    }

    setCart(cart.map(item =>
      item.id === productId
        ? { ...item, quantity }
        : item
    ))
  }

  // 计算购物车小计
  const calculateSubtotal = useMemo(() => {
    return cart.reduce((total, item) => total + (item.appliedPrice * item.quantity), 0);
  }, [cart]);

  // 计算折扣金额
  const calculateDiscount = useMemo(() => {
    let discount = 0;

    // 优惠券折扣
    if (selectedCoupon) {
      if (selectedCoupon.type === "满减" && calculateSubtotal >= selectedCoupon.minAmount) {
        discount += selectedCoupon.value;
      } else if (selectedCoupon.type === "折扣") {
        discount += calculateSubtotal * (1 - selectedCoupon.value);
      }
    }

    return discount;
  }, [calculateSubtotal, selectedCoupon]);

  // 计算购物车总金额
  const calculateTotal = useMemo(() => {
    return calculateSubtotal - calculateDiscount;
  }, [calculateSubtotal, calculateDiscount]);

  // 清空购物车
  const clearCart = () => {
    setCart([])
    setSelectedCoupon(null)
    setOrderNote("")
  }

  // 应用优惠券
  const applyCoupon = () => {
    const coupon = coupons.find(c => c.code === couponCode);
    if (coupon) {
      setSelectedCoupon(coupon);
      setShowCouponModal(false);
      setCouponCode("");
    } else {
      alert("无效的优惠券代码");
    }
  }

  // 移除优惠券
  const removeCoupon = () => {
    setSelectedCoupon(null);
  }

  // 处理结账
  const handleCheckout = () => {
    setShowPaymentModal(true);
  }

  // 完成支付
  const completePayment = () => {
    setPaymentLoading(true);

    // 模拟支付过程
    setTimeout(() => {
      setPaymentLoading(false);
      setPaymentSuccess(true);

      // 3秒后关闭支付成功提示
      setTimeout(() => {
        setPaymentSuccess(false);
        setShowPaymentModal(false);

        // 清空购物车
        clearCart();

        // 生成新订单并添加到订单列表（实际应用中应该通过API）
        const newOrder = {
          id: `ORD${Date.now().toString().slice(-8)}`,
          time: new Date().toLocaleString(),
          amount: calculateTotal,
          items: cart.length,
          status: "已完成",
          type: selectedMember ? "会员购买" : "非会员购买",
          paymentMethod: paymentMethod,
          member: selectedMember ? selectedMember.name : null
        };

        // 这里应该调用API保存订单
        console.log("新订单:", newOrder);

      }, 3000);
    }, 2000);
  }

  // 处理订单核销
  const verifyOrder = () => {
    const order = channelOrders.find(o => o.verificationCode === verificationCode && o.status === "未使用");

    if (order) {
      setVerificationResult({
        success: true,
        order: order
      });

      // 实际应用中应该调用API更新订单状态
      console.log(`核销订单: ${order.id}`);
    } else {
      setVerificationResult({
        success: false,
        message: "无效的核销码或订单已使用"
      });
    }
  }

  // 完成核销
  const completeVerification = () => {
    setVerificationCode("");
    setVerificationResult(null);
    setShowVerificationModal(false);

    // 显示成功提示
    alert("订单核销成功!");
  }

  // 查看订单详情
  const viewOrderDetails = (order: any) => {
    setSelectedOrder(order);
    setShowOrderDetails(true);
  }

  // 选择会员
  const selectMember = (member: any) => {
    setSelectedMember(member)
    setShowMemberSearch(false)
    setMemberSearchQuery("")

    // 如果已有商品在购物车中，更新为会员价
    if (cart.length > 0) {
      setCart(cart.map(item => {
        const product = products.find(p => p.id === item.id);
        return {
          ...item,
          appliedPrice: product ? product.memberPrice : item.price
        };
      }));
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">智能收银系统</h1>
          <p className="text-muted-foreground">一站式收银解决方案，整合商城商品、渠道订单和会员服务</p>
        </div>
      </div>

      <div className="text-sm breadcrumbs">
        <ul className="flex items-center space-x-1">
          <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li>智能收银系统</li>
        </ul>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 max-w-2xl">
          <TabsTrigger value="cashier">收银台</TabsTrigger>
          <TabsTrigger value="verification">订单核销</TabsTrigger>
          <TabsTrigger value="history">交易记录</TabsTrigger>
          <TabsTrigger value="statistics">收银统计</TabsTrigger>
        </TabsList>

        {/* 收银台 */}
        <TabsContent value="cashier" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 左侧 - 会员信息和购物车 */}
            <div className="md:col-span-2 space-y-4">
              {/* 会员信息卡片 */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">会员信息</CardTitle>
                    {selectedMember ? (
                      <Button variant="ghost" size="sm" onClick={() => setSelectedMember(null)}>
                        <XCircle className="h-4 w-4 mr-1" />
                        清除会员
                      </Button>
                    ) : (
                      <Button variant="outline" size="sm" onClick={() => setShowMemberSearch(true)}>
                        <User className="h-4 w-4 mr-1" />
                        选择会员
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {selectedMember ? (
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="font-medium">会员姓名:</span>
                        <span>{selectedMember.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">手机号码:</span>
                        <span>{selectedMember.phone}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">会员等级:</span>
                        <Badge>{selectedMember.level}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">账户余额:</span>
                        <span className="text-primary">¥{selectedMember.balance}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">积分:</span>
                        <span>{selectedMember.points}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">剩余课程:</span>
                        <span>{selectedMember.courses}节</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium">会员折扣:</span>
                        <span>{selectedMember.discount * 10}折</span>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <User className="h-12 w-12 mx-auto mb-2 opacity-20" />
                      <p>未选择会员</p>
                      <p className="text-sm">选择会员可享受会员价和积分</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 购物车 */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">购物车</CardTitle>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={() => setShowProductSearch(true)}>
                        <Plus className="h-4 w-4 mr-1" />
                        添加商品
                      </Button>
                      {cart.length > 0 && (
                        <Button variant="ghost" size="sm" onClick={clearCart}>
                          <Trash className="h-4 w-4 mr-1" />
                          清空
                        </Button>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {cart.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>商品</TableHead>
                          <TableHead>类型</TableHead>
                          <TableHead className="text-right">单价</TableHead>
                          <TableHead className="text-right">数量</TableHead>
                          <TableHead className="text-right">小计</TableHead>
                          <TableHead></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {cart.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell className="font-medium">{item.name}</TableCell>
                            <TableCell>{item.type}</TableCell>
                            <TableCell className="text-right">
                              {selectedMember && item.appliedPrice !== item.price ? (
                                <div>
                                  <span className="line-through text-muted-foreground text-xs">¥{item.price}</span>
                                  <span className="text-primary ml-1">¥{item.appliedPrice}</span>
                                </div>
                              ) : (
                                <span>¥{item.appliedPrice || item.price}</span>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end">
                                <Button
                                  variant="outline"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                >
                                  -
                                </Button>
                                <span className="w-8 text-center">{item.quantity}</span>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                >
                                  +
                                </Button>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">¥{(item.appliedPrice || item.price) * item.quantity}</TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => removeFromCart(item.id)}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-12 text-muted-foreground">
                      <ShoppingBag className="h-12 w-12 mx-auto mb-2 opacity-20" />
                      <p>购物车为空</p>
                      <p className="text-sm">添加商品到购物车</p>
                    </div>
                  )}
                </CardContent>
                {cart.length > 0 && (
                  <CardFooter className="flex flex-col border-t pt-4">
                    <div className="flex justify-between w-full">
                      <span className="font-medium">小计:</span>
                      <span>¥{calculateSubtotal}</span>
                    </div>

                    {calculateDiscount > 0 && (
                      <div className="flex justify-between w-full text-green-600">
                        <span className="font-medium">优惠:</span>
                        <span>-¥{calculateDiscount}</span>
                      </div>
                    )}

                    {selectedCoupon && (
                      <div className="flex justify-between w-full text-xs text-muted-foreground mt-1">
                        <span>已使用优惠券: {selectedCoupon.name}</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 ml-2"
                          onClick={removeCoupon}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}

                    <div className="flex justify-between w-full mt-2 pt-2 border-t">
                      <span className="font-medium">总计:</span>
                      <span className="text-xl font-bold text-primary">¥{calculateTotal}</span>
                    </div>

                    {!selectedCoupon && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="mt-2 w-full"
                        onClick={() => setShowCouponModal(true)}
                      >
                        <Tag className="mr-2 h-4 w-4" />
                        使用优惠券
                      </Button>
                    )}
                  </CardFooter>
                )}
              </Card>
            </div>

            {/* 右侧 - 结账区域 */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">结账</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="payment-method">支付方式</Label>
                    <select
                      id="payment-method"
                      value={paymentMethod}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                      className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 mt-1"
                    >
                      <option value="cash">现金支付</option>
                      <option value="wechat">微信支付</option>
                      <option value="alipay">支付宝</option>
                      <option value="card">刷卡支付</option>
                      {selectedMember && selectedMember.balance >= calculateTotal && (
                        <option value="balance">余额支付</option>
                      )}
                      {selectedMember && selectedMember.points >= calculateTotal * 10 && (
                        <option value="points">积分兑换</option>
                      )}
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="order-note">订单备注</Label>
                    <Input
                      id="order-note"
                      placeholder="添加订单备注..."
                      value={orderNote}
                      onChange={(e) => setOrderNote(e.target.value)}
                      className="mt-1"
                    />
                  </div>

                  <div className="pt-4">
                    <Button
                      className="w-full"
                      size="lg"
                      disabled={cart.length === 0}
                      onClick={handleCheckout}
                    >
                      <CreditCard className="mr-2 h-5 w-5" />
                      结账支付
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-2 pt-2">
                    <Button variant="outline">
                      <Printer className="mr-2 h-4 w-4" />
                      打印小票
                    </Button>
                    <Button variant="outline">
                      <FileText className="mr-2 h-4 w-4" />
                      保存草稿
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="mt-4">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">快捷功能</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant="outline"
                      className="h-auto py-4 flex flex-col items-center justify-center"
                      onClick={() => setShowVerificationModal(true)}
                    >
                      <QrCode className="h-6 w-6 mb-1" />
                      <span>扫码核销</span>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-auto py-4 flex flex-col items-center justify-center"
                      onClick={() => setShowMemberSearch(true)}
                    >
                      <Smartphone className="h-6 w-6 mb-1" />
                      <span>会员查询</span>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-auto py-4 flex flex-col items-center justify-center"
                    >
                      <Calendar className="h-6 w-6 mb-1" />
                      <span>课程预约</span>
                    </Button>
                    <Button
                      variant="outline"
                      className="h-auto py-4 flex flex-col items-center justify-center"
                      onClick={() => setShowCouponModal(true)}
                    >
                      <Tag className="h-6 w-6 mb-1" />
                      <span>优惠券</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="mt-4">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">热门商品</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2">
                    {products.filter(p => p.hot).slice(0, 4).map(product => (
                      <Button
                        key={product.id}
                        variant="outline"
                        className="h-auto py-3 px-2 flex flex-col items-center justify-center text-xs"
                        onClick={() => addToCart(product)}
                      >
                        <span className="font-medium mb-1 text-center line-clamp-1">{product.name}</span>
                        <span className="text-primary">¥{product.price}</span>
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* 会员搜索对话框 */}
          {showMemberSearch && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg w-full max-w-md">
                <div className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">选择会员</h2>
                    <Button variant="ghost" size="icon" onClick={() => setShowMemberSearch(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="mb-4">
                    <Input
                      placeholder="搜索会员姓名或手机号..."
                      value={memberSearchQuery}
                      onChange={(e) => setMemberSearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="max-h-[300px] overflow-y-auto">
                    {filteredMembers.length > 0 ? (
                      <div className="space-y-2">
                        {filteredMembers.map((member) => (
                          <div
                            key={member.id}
                            className="p-3 border rounded-md hover:bg-muted cursor-pointer"
                            onClick={() => selectMember(member)}
                          >
                            <div className="flex justify-between">
                              <span className="font-medium">{member.name}</span>
                              <Badge>{member.level}</Badge>
                            </div>
                            <div className="text-sm text-muted-foreground">{member.phone}</div>
                            <div className="flex justify-between text-sm mt-1">
                              <span>余额: <span className="text-primary">¥{member.balance}</span></span>
                              <span>折扣: {member.discount * 10}折</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>未找到匹配的会员</p>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" onClick={() => setShowMemberSearch(false)}>
                      取消
                    </Button>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      新增会员
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 优惠券对话框 */}
          {showCouponModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg w-full max-w-md">
                <div className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">使用优惠券</h2>
                    <Button variant="ghost" size="icon" onClick={() => setShowCouponModal(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="mb-4 flex gap-2">
                    <Input
                      placeholder="输入优惠券代码..."
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value)}
                    />
                    <Button onClick={applyCoupon}>应用</Button>
                  </div>

                  <div className="mb-2">
                    <h3 className="font-medium mb-2">可用优惠券</h3>
                  </div>

                  <div className="max-h-[300px] overflow-y-auto">
                    {coupons.length > 0 ? (
                      <div className="space-y-2">
                        {coupons.map((coupon) => (
                          <div
                            key={coupon.id}
                            className="p-3 border rounded-md hover:bg-muted cursor-pointer"
                            onClick={() => {
                              setCouponCode(coupon.code);
                              applyCoupon();
                            }}
                          >
                            <div className="flex justify-between">
                              <span className="font-medium">{coupon.name}</span>
                              <Badge variant="outline">{coupon.type}</Badge>
                            </div>
                            <div className="flex justify-between text-sm mt-1">
                              <span className="text-muted-foreground">代码: {coupon.code}</span>
                              <span className="text-muted-foreground">有效期至: {coupon.expires}</span>
                            </div>
                            <div className="text-sm text-primary mt-1">
                              {coupon.type === "满减" ?
                                `满${coupon.minAmount}减${coupon.value}` :
                                `${coupon.value * 10}折优惠`}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>暂无可用优惠券</p>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" onClick={() => setShowCouponModal(false)}>
                      取消
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 支付对话框 */}
          {showPaymentModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg w-full max-w-md">
                <div className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">确认支付</h2>
                    {!paymentLoading && !paymentSuccess && (
                      <Button variant="ghost" size="icon" onClick={() => setShowPaymentModal(false)}>
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  {paymentSuccess ? (
                    <div className="text-center py-8">
                      <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                      <h3 className="text-xl font-medium mb-2">支付成功</h3>
                      <p className="text-muted-foreground mb-6">订单已完成，感谢您的购买！</p>
                      <Button
                        className="w-full"
                        onClick={() => {
                          setShowPaymentModal(false);
                          setSelectedMember(null);
                        }}
                      >
                        完成
                      </Button>
                    </div>
                  ) : paymentLoading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin h-16 w-16 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                      <h3 className="text-xl font-medium mb-2">处理中</h3>
                      <p className="text-muted-foreground">正在处理您的支付请求...</p>
                    </div>
                  ) : (
                    <>
                      <div className="mb-6 space-y-4">
                        <div className="p-4 bg-muted rounded-lg">
                          <div className="flex justify-between mb-2">
                            <span className="font-medium">商品总数:</span>
                            <span>{cart.reduce((total, item) => total + item.quantity, 0)}件</span>
                          </div>
                          <div className="flex justify-between mb-2">
                            <span className="font-medium">小计金额:</span>
                            <span>¥{calculateSubtotal}</span>
                          </div>
                          {calculateDiscount > 0 && (
                            <div className="flex justify-between mb-2 text-green-600">
                              <span className="font-medium">优惠金额:</span>
                              <span>-¥{calculateDiscount}</span>
                            </div>
                          )}
                          <div className="flex justify-between pt-2 border-t border-border mt-2">
                            <span className="font-medium">应付金额:</span>
                            <span className="text-xl font-bold text-primary">¥{calculateTotal}</span>
                          </div>
                        </div>

                        <div>
                          <h3 className="font-medium mb-2">支付方式</h3>
                          <div className="grid grid-cols-2 gap-2">
                            <Button
                              variant={paymentMethod === "cash" ? "default" : "outline"}
                              onClick={() => setPaymentMethod("cash")}
                              className="justify-start"
                            >
                              <DollarSign className="mr-2 h-4 w-4" />
                              现金支付
                            </Button>
                            <Button
                              variant={paymentMethod === "wechat" ? "default" : "outline"}
                              onClick={() => setPaymentMethod("wechat")}
                              className="justify-start"
                            >
                              <Smartphone className="mr-2 h-4 w-4" />
                              微信支付
                            </Button>
                            <Button
                              variant={paymentMethod === "alipay" ? "default" : "outline"}
                              onClick={() => setPaymentMethod("alipay")}
                              className="justify-start"
                            >
                              <Smartphone className="mr-2 h-4 w-4" />
                              支付宝
                            </Button>
                            <Button
                              variant={paymentMethod === "card" ? "default" : "outline"}
                              onClick={() => setPaymentMethod("card")}
                              className="justify-start"
                            >
                              <CreditCard className="mr-2 h-4 w-4" />
                              刷卡支付
                            </Button>
                            {selectedMember && selectedMember.balance >= calculateTotal && (
                              <Button
                                variant={paymentMethod === "balance" ? "default" : "outline"}
                                onClick={() => setPaymentMethod("balance")}
                                className="justify-start"
                              >
                                <User className="mr-2 h-4 w-4" />
                                余额支付
                              </Button>
                            )}
                            {selectedMember && selectedMember.points >= calculateTotal * 10 && (
                              <Button
                                variant={paymentMethod === "points" ? "default" : "outline"}
                                onClick={() => setPaymentMethod("points")}
                                className="justify-start"
                              >
                                <Tag className="mr-2 h-4 w-4" />
                                积分兑换
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          className="flex-1"
                          onClick={() => setShowPaymentModal(false)}
                        >
                          取消
                        </Button>
                        <Button
                          className="flex-1"
                          onClick={completePayment}
                        >
                          确认支付
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 商品搜索对话框 */}
          {showProductSearch && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg w-full max-w-2xl">
                <div className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">添加商品</h2>
                    <Button variant="ghost" size="icon" onClick={() => setShowProductSearch(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="flex flex-col md:flex-row gap-4 mb-4">
                    <div className="flex-1">
                      <Input
                        placeholder="搜索商品名称或类型..."
                        value={productSearchQuery}
                        onChange={(e) => setProductSearchQuery(e.target.value)}
                      />
                    </div>
                    <div>
                      <select
                        value={productCategory}
                        onChange={(e) => setProductCategory(e.target.value)}
                        className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                      >
                        {productCategories.map(category => (
                          <option key={category.id} value={category.id}>{category.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-2">
                    {productCategories.slice(0, 6).map(category => (
                      <Button
                        key={category.id}
                        variant={productCategory === category.id ? "default" : "outline"}
                        size="sm"
                        onClick={() => setProductCategory(category.id)}
                        className="text-xs"
                      >
                        {category.name}
                      </Button>
                    ))}
                  </div>

                  <div className="max-h-[400px] overflow-y-auto">
                    {filteredProducts.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {filteredProducts.map((product) => (
                          <div
                            key={product.id}
                            className="p-3 border rounded-md hover:bg-muted cursor-pointer"
                            onClick={() => addToCart(product)}
                          >
                            <div className="flex justify-between">
                              <span className="font-medium">{product.name}</span>
                              <div>
                                {selectedMember ? (
                                  <div className="text-right">
                                    <span className="line-through text-muted-foreground text-xs">¥{product.price}</span>
                                    <span className="text-primary ml-1">¥{product.memberPrice}</span>
                                  </div>
                                ) : (
                                  <span className="text-primary">¥{product.price}</span>
                                )}
                              </div>
                            </div>
                            <div className="flex justify-between text-sm text-muted-foreground">
                              <div>
                                <span>{product.type}</span>
                                <span className="mx-1">·</span>
                                <span>{product.category}</span>
                              </div>
                              {product.inventory !== null && (
                                <span>库存: {product.inventory}</span>
                              )}
                            </div>
                            {product.hot && (
                              <Badge variant="outline" className="mt-1 bg-red-50 text-red-600 text-xs">热门</Badge>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>未找到匹配的商品</p>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-between gap-2 mt-4">
                    <Button variant="outline" onClick={() => setProductCategory("all")}>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      重置筛选
                    </Button>
                    <Button variant="outline" onClick={() => setShowProductSearch(false)}>
                      关闭
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </TabsContent>

        {/* 订单核销 */}
        <TabsContent value="verification" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>订单核销</CardTitle>
                  <CardDescription>核销来自美团、抖音等渠道的订单</CardDescription>
                </div>
                <Button variant="outline" size="sm" onClick={() => setShowVerificationModal(true)}>
                  <QrCode className="mr-2 h-4 w-4" />
                  扫码核销
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex gap-2">
                <Input
                  placeholder="输入核销码..."
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                />
                <Button onClick={verifyOrder}>核销</Button>
              </div>

              <div className="mb-4">
                <h3 className="font-medium mb-2">待核销订单</h3>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>订单号</TableHead>
                    <TableHead>平台</TableHead>
                    <TableHead>商品</TableHead>
                    <TableHead className="text-right">价格</TableHead>
                    <TableHead>购买日期</TableHead>
                    <TableHead>到期日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {channelOrders.filter(order => order.status === "未使用").map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">{order.id}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={order.platform === "美团" ? "bg-green-50 text-green-600" : "bg-blue-50 text-blue-600"}>
                          {order.platform}
                        </Badge>
                      </TableCell>
                      <TableCell>{order.product}</TableCell>
                      <TableCell className="text-right">¥{order.price}</TableCell>
                      <TableCell>{order.purchaseTime}</TableCell>
                      <TableCell>{order.expireTime}</TableCell>
                      <TableCell>
                        <Badge variant={order.status === "已使用" ? "outline" : "default"}>
                          {order.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setVerificationCode(order.verificationCode);
                            verifyOrder();
                          }}
                        >
                          核销
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {channelOrders.filter(order => order.status === "未使用").length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <p>暂无待核销订单</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 核销结果对话框 */}
          {verificationResult && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg w-full max-w-md">
                <div className="p-4">
                  {verificationResult.success ? (
                    <>
                      <div className="text-center mb-6">
                        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                        <h3 className="text-xl font-medium mb-2">核销成功</h3>
                        <p className="text-muted-foreground">订单已成功核销</p>
                      </div>

                      <div className="p-4 bg-muted rounded-lg mb-6">
                        <div className="flex justify-between mb-2">
                          <span className="font-medium">订单号:</span>
                          <span>{verificationResult.order.id}</span>
                        </div>
                        <div className="flex justify-between mb-2">
                          <span className="font-medium">平台:</span>
                          <span>{verificationResult.order.platform}</span>
                        </div>
                        <div className="flex justify-between mb-2">
                          <span className="font-medium">商品:</span>
                          <span>{verificationResult.order.product}</span>
                        </div>
                        <div className="flex justify-between mb-2">
                          <span className="font-medium">价格:</span>
                          <span>¥{verificationResult.order.price}</span>
                        </div>
                        <div className="flex justify-between mb-2">
                          <span className="font-medium">购买日期:</span>
                          <span>{verificationResult.order.purchaseTime}</span>
                        </div>
                      </div>

                      <Button
                        className="w-full"
                        onClick={completeVerification}
                      >
                        完成
                      </Button>
                    </>
                  ) : (
                    <>
                      <div className="text-center mb-6">
                        <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
                        <h3 className="text-xl font-medium mb-2">核销失败</h3>
                        <p className="text-muted-foreground">{verificationResult.message}</p>
                      </div>

                      <Button
                        className="w-full"
                        onClick={() => {
                          setVerificationResult(null);
                          setVerificationCode("");
                        }}
                      >
                        返回
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 扫码核销对话框 */}
          {showVerificationModal && !verificationResult && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg w-full max-w-md">
                <div className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">扫码核销</h2>
                    <Button variant="ghost" size="icon" onClick={() => setShowVerificationModal(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="text-center py-6 mb-4 border rounded-lg">
                    <QrCode className="h-16 w-16 mx-auto mb-4 opacity-20" />
                    <p className="text-muted-foreground">请对准核销码进行扫描</p>
                    <p className="text-xs text-muted-foreground mt-1">或手动输入核销码</p>
                  </div>

                  <div className="mb-4 flex gap-2">
                    <Input
                      placeholder="输入核销码..."
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                    />
                    <Button onClick={() => {
                      verifyOrder();
                      setShowVerificationModal(false);
                    }}>
                      核销
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </TabsContent>

        {/* 交易记录 */}
        <TabsContent value="history" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>交易记录</CardTitle>
                  <CardDescription>查看最近的交易记录</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Input
                    placeholder="搜索订单号或会员..."
                    value={orderSearchQuery}
                    onChange={(e) => setOrderSearchQuery(e.target.value)}
                    className="max-w-[200px]"
                  />
                  <Button variant="outline" size="sm">
                    <RefreshCw className="mr-2 h-4 w-4" />
                    刷新
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>订单号</TableHead>
                    <TableHead>时间</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>支付方式</TableHead>
                    <TableHead>会员</TableHead>
                    <TableHead className="text-right">金额</TableHead>
                    <TableHead className="text-right">状态</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">{order.id}</TableCell>
                      <TableCell>{order.time}</TableCell>
                      <TableCell>{order.type}</TableCell>
                      <TableCell>{order.paymentMethod}</TableCell>
                      <TableCell>{order.member || "-"}</TableCell>
                      <TableCell className="text-right">¥{order.amount}</TableCell>
                      <TableCell className="text-right">
                        <Badge variant={order.status === "已完成" ? "default" : "outline"}>
                          {order.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => viewOrderDetails(order)}
                        >
                          详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredOrders.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <p>未找到匹配的订单</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 订单详情对话框 */}
          {showOrderDetails && selectedOrder && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg w-full max-w-md">
                <div className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold">订单详情</h2>
                    <Button variant="ghost" size="icon" onClick={() => setShowOrderDetails(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="p-4 bg-muted rounded-lg mb-6">
                    <div className="flex justify-between mb-2">
                      <span className="font-medium">订单号:</span>
                      <span>{selectedOrder.id}</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="font-medium">时间:</span>
                      <span>{selectedOrder.time}</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="font-medium">类型:</span>
                      <span>{selectedOrder.type}</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="font-medium">支付方式:</span>
                      <span>{selectedOrder.paymentMethod}</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="font-medium">会员:</span>
                      <span>{selectedOrder.member || "-"}</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="font-medium">商品数量:</span>
                      <span>{selectedOrder.items}件</span>
                    </div>
                    <div className="flex justify-between pt-2 border-t border-border mt-2">
                      <span className="font-medium">金额:</span>
                      <span className="text-xl font-bold text-primary">¥{selectedOrder.amount}</span>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => setShowOrderDetails(false)}
                    >
                      关闭
                    </Button>
                    <Button
                      className="flex-1"
                    >
                      <Printer className="mr-2 h-4 w-4" />
                      打印小票
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </TabsContent>

        {/* 收银统计 */}
        <TabsContent value="statistics" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">销售总额</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">¥{salesStatistics[statisticsPeriod].totalSales}</div>
                <p className="text-muted-foreground text-sm">共{salesStatistics[statisticsPeriod].orderCount}笔订单</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">会员销售</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">¥{salesStatistics[statisticsPeriod].memberSales}</div>
                <p className="text-muted-foreground text-sm">
                  占比{Math.round(salesStatistics[statisticsPeriod].memberSales / salesStatistics[statisticsPeriod].totalSales * 100)}%
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">平均订单金额</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">¥{salesStatistics[statisticsPeriod].avgOrderValue}</div>
                <p className="text-muted-foreground text-sm">每笔订单平均金额</p>
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-end mb-4">
            <div className="flex border rounded-md overflow-hidden">
              <Button
                variant={statisticsPeriod === "today" ? "default" : "ghost"}
                className="rounded-none"
                onClick={() => setStatisticsPeriod("today")}
              >
                今日
              </Button>
              <Button
                variant={statisticsPeriod === "week" ? "default" : "ghost"}
                className="rounded-none"
                onClick={() => setStatisticsPeriod("week")}
              >
                本周
              </Button>
              <Button
                variant={statisticsPeriod === "month" ? "default" : "ghost"}
                className="rounded-none"
                onClick={() => setStatisticsPeriod("month")}
              >
                本月
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>销售类型分布</CardTitle>
              </CardHeader>
              <CardContent className="h-[300px] flex items-center justify-center">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-8">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {Math.round(salesStatistics[statisticsPeriod].productSales / salesStatistics[statisticsPeriod].totalSales * 100)}%
                      </div>
                      <div className="text-sm text-muted-foreground">实体商品</div>
                      <div className="text-sm font-medium mt-1">¥{salesStatistics[statisticsPeriod].productSales}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-indigo-500">
                        {Math.round(salesStatistics[statisticsPeriod].courseSales / salesStatistics[statisticsPeriod].totalSales * 100)}%
                      </div>
                      <div className="text-sm text-muted-foreground">课程服务</div>
                      <div className="text-sm font-medium mt-1">¥{salesStatistics[statisticsPeriod].courseSales}</div>
                    </div>
                  </div>
                  <div className="w-full h-4 bg-muted rounded-full mt-4 overflow-hidden">
                    <div
                      className="h-full bg-primary"
                      style={{
                        width: `${Math.round(salesStatistics[statisticsPeriod].productSales / salesStatistics[statisticsPeriod].totalSales * 100)}%`
                      }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>会员与非会员销售</CardTitle>
              </CardHeader>
              <CardContent className="h-[300px] flex items-center justify-center">
                <div className="text-center">
                  <div className="flex items-center justify-center gap-8">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {Math.round(salesStatistics[statisticsPeriod].memberSales / salesStatistics[statisticsPeriod].totalSales * 100)}%
                      </div>
                      <div className="text-sm text-muted-foreground">会员销售</div>
                      <div className="text-sm font-medium mt-1">¥{salesStatistics[statisticsPeriod].memberSales}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-500">
                        {Math.round(salesStatistics[statisticsPeriod].nonMemberSales / salesStatistics[statisticsPeriod].totalSales * 100)}%
                      </div>
                      <div className="text-sm text-muted-foreground">非会员销售</div>
                      <div className="text-sm font-medium mt-1">¥{salesStatistics[statisticsPeriod].nonMemberSales}</div>
                    </div>
                  </div>
                  <div className="w-full h-4 bg-muted rounded-full mt-4 overflow-hidden">
                    <div
                      className="h-full bg-primary"
                      style={{
                        width: `${Math.round(salesStatistics[statisticsPeriod].memberSales / salesStatistics[statisticsPeriod].totalSales * 100)}%`
                      }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default React.memo(CashierPage);