"use client"

import { useState, useEffect } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { FileUploader } from "@/components/file-uploader"
import { FileText, Upload, X } from "lucide-react"

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, {
    message: "模板名称至少需要2个字符",
  }),
  category: z.string({
    required_error: "请选择模板类别",
  }),
  description: z.string().optional(),
  file: z.any().refine((file) => file && file.length > 0, {
    message: "请上传模板文件",
  }),
})

// 模板类别选项
const templateCategories = [
  { label: "会员合同", value: "会员合同" },
  { label: "教练合同", value: "教练合同" },
  { label: "场地合同", value: "场地合同" },
  { label: "采购合同", value: "采购合同" },
  { label: "股东合同", value: "股东合同" },
]

interface UploadTemplateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onUploadSuccess?: (template: any) => void
}

export function UploadTemplateDialog({
  open,
  onOpenChange,
  onUploadSuccess,
}: UploadTemplateDialogProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)

  // 初始化表单
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  })

  // 处理文件上传
  const handleFileUpload = (files: File[]) => {
    if (files.length > 0) {
      const file = files[0]
      setUploadedFile(file)
      form.setValue("file", files)

      // 创建文件预览URL
      if (file.type === "text/html" || file.type === "application/pdf" || file.type.startsWith("image/")) {
        const url = URL.createObjectURL(file)
        setPreviewUrl(url)
      } else {
        setPreviewUrl(null)
      }
    }
  }

  // 清理预览URL
  useEffect(() => {
    return () => {
      // 组件卸载时清理URL对象
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
      }
    }
  }, [previewUrl])

  // 清除上传的文件
  const clearUploadedFile = () => {
    setUploadedFile(null)
    setPreviewUrl(null)
    form.setValue("file", null)
  }

  // 提交表单
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsUploading(true)
    try {
      // 模拟上传过程
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 创建FormData对象
      const formData = new FormData()
      formData.append("name", values.name)
      formData.append("category", values.category)
      if (values.description) {
        formData.append("description", values.description)
      }
      if (uploadedFile) {
        formData.append("file", uploadedFile)
      }

      // 模拟API调用
      console.log("上传模板:", formData)

      // 创建模拟的模板对象
      const newTemplate = {
        id: `template-${Date.now()}`,
        name: values.name,
        category: values.category,
        description: values.description || "",
        fileName: uploadedFile?.name,
        fileSize: uploadedFile?.size,
        uploadDate: new Date().toISOString(),
        previewUrl: previewUrl || "",
      }

      // 显示成功提示
      toast({
        title: "上传成功",
        description: `模板 ${values.name} 已成功上传`,
      })

      // 调用成功回调
      if (onUploadSuccess) {
        onUploadSuccess(newTemplate)
      }

      // 重置表单
      form.reset()
      setUploadedFile(null)
      setPreviewUrl(null)

      // 关闭对话框
      onOpenChange(false)
    } catch (error) {
      console.error("上传失败:", error)
      toast({
        title: "上传失败",
        description: "模板上传过程中发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>上传合同模板</DialogTitle>
          <DialogDescription>
            上传新的合同模板，支持HTML、PDF格式
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>模板名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入模板名称" {...field} />
                  </FormControl>
                  <FormDescription>
                    请输入一个清晰描述模板用途的名称
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>模板类别</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择模板类别" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {templateCategories.map((category) => (
                        <SelectItem key={category.value} value={category.value}>
                          {category.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    选择此模板适用的合同类别
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>模板描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="输入模板描述（可选）"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    简要描述此模板的用途和适用场景
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="file"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>模板文件</FormLabel>
                  <FormControl>
                    <div className="space-y-4">
                      {!uploadedFile ? (
                        <FileUploader
                          onFilesUploaded={handleFileUpload}
                          maxFiles={1}
                          maxSize={5 * 1024 * 1024} // 5MB
                          accept=".html,.pdf"
                        />
                      ) : (
                        <div className="flex items-center justify-between p-4 border rounded-md">
                          <div className="flex items-center space-x-2">
                            <FileText className="h-6 w-6 text-blue-500" />
                            <div>
                              <p className="font-medium">{uploadedFile.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {(uploadedFile.size / 1024).toFixed(2)} KB
                              </p>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={clearUploadedFile}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormDescription>
                    上传HTML或PDF格式的合同模板文件，最大5MB
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                取消
              </Button>
              <Button type="submit" disabled={isUploading}>
                {isUploading ? (
                  <>
                    <span className="animate-spin mr-2">⏳</span>
                    上传中...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    上传模板
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
