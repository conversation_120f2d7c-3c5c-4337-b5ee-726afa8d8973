"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Slider } from "@/components/ui/slider"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { useState } from "react"

interface AdvancedFilterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AdvancedFilterDialog({ open, onOpenChange }: AdvancedFilterDialogProps) {
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-3xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div className="space-y-4">
            <div>
              <h3 className="mb-2 font-medium">会员状态</h3>
              <RadioGroup defaultValue="all">
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="all" id="status-all" />
                    <Label htmlFor="status-all">全部</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="active" id="status-active" />
                    <Label htmlFor="status-active">活跃</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="inactive" id="status-inactive" />
                    <Label htmlFor="status-inactive">不活跃</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="expired" id="status-expired" />
                    <Label htmlFor="status-expired">已过期</Label>
                  </div>
                </div>
              </RadioGroup>
            </div>

            <div>
              <h3 className="mb-2 font-medium">会员等级</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="level-standard" />
                  <Label htmlFor="level-standard">标准会员</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="level-silver" />
                  <Label htmlFor="level-silver">银卡会员</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="level-gold" />
                  <Label htmlFor="level-gold">金卡会员</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="level-platinum" />
                  <Label htmlFor="level-platinum">白金会员</Label>
                </div>
              </div>
            </div>

            <div>
              <h3 className="mb-2 font-medium">会员卡类型</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="card-year" />
                  <Label htmlFor="card-year">年卡</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="card-quarter" />
                  <Label htmlFor="card-quarter">季卡</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="card-month" />
                  <Label htmlFor="card-month">月卡</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="card-times" />
                  <Label htmlFor="card-times">次卡</Label>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="mb-2 font-medium">注册日期</h3>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dateRange.from && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "yyyy-MM-dd")} - {format(dateRange.to, "yyyy-MM-dd")}
                        </>
                      ) : (
                        format(dateRange.from, "yyyy-MM-dd")
                      )
                    ) : (
                      "选择日期范围"
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar mode="range" selected={dateRange} onSelect={setDateRange as any} initialFocus />
                </PopoverContent>
              </Popover>
            </div>

            <div>
              <h3 className="mb-2 font-medium">到期日期</h3>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择到期时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="7days">7天内</SelectItem>
                  <SelectItem value="30days">30天内</SelectItem>
                  <SelectItem value="90days">90天内</SelectItem>
                  <SelectItem value="expired">已过期</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <h3 className="mb-2 font-medium">消费金额</h3>
              <div className="space-y-4">
                <Slider defaultValue={[0, 10000]} max={10000} step={100} />
                <div className="flex items-center justify-between">
                  <Input type="number" placeholder="最小值" className="w-[45%]" defaultValue={0} />
                  <span>-</span>
                  <Input type="number" placeholder="最大值" className="w-[45%]" defaultValue={10000} />
                </div>
              </div>
            </div>

            <div>
              <h3 className="mb-2 font-medium">标签</h3>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="tag-new" />
                  <Label htmlFor="tag-new">新会员</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="tag-vip" />
                  <Label htmlFor="tag-vip">VIP</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="tag-potential" />
                  <Label htmlFor="tag-potential">潜在流失</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="tag-yoga" />
                  <Label htmlFor="tag-yoga">瑜伽爱好者</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="tag-coach" />
                  <Label htmlFor="tag-coach">教练推荐</Label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button variant="outline">重置</Button>
          <Button>应用筛选</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

