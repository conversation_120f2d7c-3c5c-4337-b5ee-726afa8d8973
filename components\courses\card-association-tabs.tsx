"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"

// 会员卡类型映射
const getCardTypeFromApiData = (cardType: string) => {
  // 根据API返回的card_type字段映射到我们的类型
  if (cardType?.includes('年') || cardType?.includes('月') || cardType?.includes('季') || cardType?.includes('天')) {
    return 'time';
  } else if (cardType?.includes('次')) {
    return 'count';
  } else if (cardType?.includes('储值') || cardType?.includes('金额')) {
    return 'value';
  }
  return 'time'; // 默认为时间卡
};

// 卡片类别
const cardCategories = [
  { id: "time", name: "时间卡" },
  { id: "count", name: "次数卡" },
  { id: "value", name: "储值卡" }
]

interface CardAssociationTabsProps {
  selectedCards: string[]
  onSelectedCardsChange: (selectedCards: string[]) => void
  cardConsumption: any
  onCardConsumptionChange: (cardConsumption: any) => void
  membershipCards?: any[]
  loading?: boolean
}

export function CardAssociationTabs({
  selectedCards,
  onSelectedCardsChange,
  cardConsumption,
  onCardConsumptionChange,
  membershipCards: propMembershipCards = [],
  loading = false
}: CardAssociationTabsProps) {

  // 转换API数据为组件需要的格式
  const membershipCards = propMembershipCards.map(card => ({
    id: card.id.toString(),
    name: card.card_name || card.name,
    color: "#4CAF50", // 可以根据卡片类型设置不同颜色
    type: getCardTypeFromApiData(card.card_type),
    validity: card.valid_period ? `${card.valid_period}天` : '不限期',
    limit: card.card_type?.includes('次') ? `${card.valid_period || 0}次` : '不限次数'
  }));
  // 处理卡片选择
  const handleCardSelect = (cardId: string, checked: boolean) => {
    if (checked) {
      onSelectedCardsChange([...selectedCards, cardId])
    } else {
      onSelectedCardsChange(selectedCards.filter(id => id !== cardId))
    }
  }

  // 清除所有选择
  const handleClearAll = () => {
    onSelectedCardsChange([])
  }

  // 选择所有卡片
  const handleSelectAll = () => {
    onSelectedCardsChange(membershipCards.map(card => card.id))
  }

  // 处理分类全选
  const handleSelectCategory = (categoryType: string, checked: boolean) => {
    const cardsInCategory = membershipCards.filter(card => card.type === categoryType)

    if (checked) {
      // 添加该分类下所有未选中的卡片
      const newSelectedCards = [...selectedCards]
      cardsInCategory.forEach(card => {
        if (!newSelectedCards.includes(card.id)) {
          newSelectedCards.push(card.id)
        }
      })
      onSelectedCardsChange(newSelectedCards)
    } else {
      // 移除该分类下所有卡片
      onSelectedCardsChange(selectedCards.filter(
        id => !cardsInCategory.some(card => card.id === id)
      ))
    }
  }

  // 检查分类是否全选
  const isCategoryAllSelected = (categoryType: string) => {
    const cardsInCategory = membershipCards.filter(card => card.type === categoryType)
    return cardsInCategory.every(card => selectedCards.includes(card.id))
  }

  // 处理消耗值变更
  const handleConsumptionChange = (cardId: string, type: string, value: number) => {
    const newCustomConsumption = {
      ...(cardConsumption?.customConsumption || {}),
      [cardId]: {
        ...(cardConsumption?.customConsumption?.[cardId] || {}),
        [type === "count" ? "count" : "value"]: value
      }
    }

    onCardConsumptionChange({
      ...cardConsumption,
      customConsumption: newCustomConsumption
    })
  }

  // 获取卡片消耗值
  const getConsumptionValue = (cardId: string, type: string) => {
    if (type === "count") {
      return cardConsumption?.customConsumption?.[cardId]?.count ||
             cardConsumption?.countCardConsumption || 1
    } else {
      return cardConsumption?.customConsumption?.[cardId]?.value ||
             cardConsumption?.valueCardConsumption || 100
    }
  }

  // 计算已选择的卡片数量
  const getSelectedCount = () => {
    return selectedCards.length;
  }

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>会员卡类型</Label>
        </div>
        <div className="border rounded-md p-4">
          <div className="text-center text-muted-foreground">
            正在加载会员卡数据...
          </div>
        </div>
      </div>
    );
  }

  // 如果没有会员卡数据，显示空状态
  if (membershipCards.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Label>会员卡类型</Label>
        </div>
        <div className="border rounded-md p-4">
          <div className="text-center text-muted-foreground">
            暂无可用的会员卡类型
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>会员卡类型</Label>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="select-all-cards"
            checked={selectedCards.length === membershipCards.length}
            onCheckedChange={(checked) => {
              if (checked) {
                handleSelectAll()
              } else {
                handleClearAll()
              }
            }}
          />
          <label
            htmlFor="select-all-cards"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            全选
          </label>
        </div>
      </div>

      <div className="border rounded-md">
        {cardCategories.map((category) => {
          const cardsInCategory = membershipCards.filter(card => card.type === category.id)
          
          if (cardsInCategory.length === 0) return null
          
          return (
            <div key={category.id} className="p-4 border-b last:border-b-0">
              <div className="flex items-center">
                <div 
                  className="h-4 w-4 rounded-full mr-2" 
                  style={{ 
                    backgroundColor: category.id === "time" ? "#4CAF50" : 
                                    category.id === "count" ? "#9C27B0" : "#8B5CF6" 
                  }}
                />
                <h3 className="font-medium">{category.name}</h3>
                <Badge variant="outline" className="ml-2 text-xs">
                  {cardsInCategory.length}张
                </Badge>
                <div className="ml-auto flex items-center space-x-2">
                  <Checkbox
                    id={`select-category-${category.id}`}
                    checked={isCategoryAllSelected(category.id)}
                    onCheckedChange={(checked) => handleSelectCategory(category.id, !!checked)}
                  />
                  <label
                    htmlFor={`select-category-${category.id}`}
                    className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    全选
                  </label>
                </div>
              </div>
              
              <div className="mt-2 space-y-1">
                {cardsInCategory.map(card => (
                  <div 
                    key={card.id} 
                    className="flex items-center py-2 px-2"
                  >
                    <Checkbox
                      id={`card-${card.id}`}
                      checked={selectedCards.includes(card.id)}
                      onCheckedChange={(checked) => handleCardSelect(card.id, !!checked)}
                      className="mr-2"
                    />
                    <label
                      htmlFor={`card-${card.id}`}
                      className="font-medium text-sm cursor-pointer"
                    >
                      {card.name}
                    </label>
                    <div className="text-xs text-muted-foreground ml-2">
                      {card.type === "time" ? card.validity : card.limit}
                    </div>
                    
                    {card.type !== "time" && selectedCards.includes(card.id) && (
                      <div className="ml-auto flex items-center">
                        <Input
                          type="number"
                          min={card.type === "count" ? "1" : "0"}
                          value={getConsumptionValue(card.id, card.type)}
                          onChange={(e) => {
                            const value = parseInt(e.target.value) || (card.type === "count" ? 1 : 0)
                            handleConsumptionChange(card.id, card.type, value)
                          }}
                          className="w-16 h-8 text-xs"
                        />
                        <span className="text-xs ml-1">{card.type === "count" ? "次" : "元"}</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )
        })}
      </div>

      <div className="text-sm">
        已选择 <span className="font-medium">{getSelectedCount()}</span> 张会员卡
      </div>
    </div>
  )
}
