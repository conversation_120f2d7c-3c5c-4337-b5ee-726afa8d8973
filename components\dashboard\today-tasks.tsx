"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import {
  CheckCircle,
  AlertCircle,
  MessageSquare,
  Calendar,
  CreditCard,
  Bell
} from "lucide-react"

// 模拟数据
const tasks = [
  {
    id: 1,
    icon: <CheckCircle className="h-5 w-5 text-primary" />,
    title: "会员续费提醒",
    count: 5,
    href: "/members/renewal",
    priority: "high",
  },
  {
    id: 2,
    icon: <AlertCircle className="h-5 w-5 text-amber-500" />,
    title: "会员生日提醒",
    count: 2,
    href: "/members/birthday",
    priority: "medium",
  },
  {
    id: 3,
    icon: <MessageSquare className="h-5 w-5 text-blue-500" />,
    title: "未读消息",
    count: 3,
    href: "/messages",
    priority: "medium",
  },
  {
    id: 4,
    icon: <Calendar className="h-5 w-5 text-green-500" />,
    title: "今日预约确认",
    count: 8,
    href: "/booking-records",
    priority: "high",
  },
  {
    id: 5,
    icon: <CreditCard className="h-5 w-5 text-purple-500" />,
    title: "待处理订单",
    count: 2,
    href: "/orders",
    priority: "medium",
  },
]

export function TodayTasks() {
  const router = useRouter()

  // 获取优先级样式
  const getPriorityStyle = (priority: string) => {
    switch(priority) {
      case "high":
        return "border-l-4 border-red-500"
      case "medium":
        return "border-l-4 border-amber-500"
      case "low":
        return "border-l-4 border-green-500"
      default:
        return ""
    }
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">今日待办</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {tasks.map((task) => (
            <div
              key={task.id}
              className={`flex items-center justify-between p-2 rounded-md ${getPriorityStyle(task.priority)} pl-3 bg-background hover:bg-muted/50 transition-colors`}
            >
              <div className="flex items-center gap-2">
                {task.icon}
                <span className="font-medium">{task.title} <span className="text-muted-foreground">({task.count})</span></span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(task.href)}
                className="text-primary hover:text-primary/80"
              >
                查看
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
