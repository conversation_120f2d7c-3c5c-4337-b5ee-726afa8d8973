// 检查租户数据
const mysql = require('mysql2/promise');

async function checkTenants() {
  console.log('检查租户数据...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    // 1. 检查所有租户
    console.log('\n1. 检查所有租户:');
    const [tenants] = await connection.execute('SELECT * FROM tenant');
    console.log('租户列表:');
    tenants.forEach((tenant, index) => {
      console.log(`  ${index + 1}. ID: ${tenant.id}, 名称: ${tenant.name}, 状态: ${tenant.status}`);
    });

    // 2. 如果没有租户，创建一个
    if (tenants.length === 0) {
      console.log('\n2. 创建默认租户:');
      const [result] = await connection.execute(
        `INSERT INTO tenant (name, status, created_at, updated_at) VALUES (?, ?, NOW(), NOW())`,
        ['默认瑜伽馆', 1]
      );
      console.log(`✓ 创建租户成功，ID: ${result.insertId}`);
    } else {
      console.log('\n2. 租户已存在，无需创建');
    }

    await connection.end();
    console.log('\n✓ 租户检查完成');

  } catch (error) {
    console.error('检查失败:', error.message);
  }
}

checkTenants();
