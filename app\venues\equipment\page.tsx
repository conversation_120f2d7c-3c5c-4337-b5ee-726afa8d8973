"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { MoreHorizontal, Pencil, Trash2, Plus, Filter, Download, Upload, Search, Eye } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useState } from "react"
import { AddEquipmentDialog } from "@/components/venues/equipment/add-equipment-dialog"
import { EquipmentDetailDialog } from "@/components/venues/equipment/equipment-detail-dialog"
import { EquipmentGrid } from "@/components/venues/equipment/equipment-grid"

const equipments = [
  {
    id: "E001",
    name: "瑜伽垫",
    venue: "所有场地",
    quantity: 50,
    status: "normal",
    lastMaintenance: "2025-02-15",
    nextMaintenance: "2025-05-15",
    brand: "Lululemon",
    purchaseDate: "2024-10-15",
    price: 150,
    lifespan: "24个月",
  },
  {
    id: "E002",
    name: "瑜伽砖",
    venue: "所有场地",
    quantity: 60,
    status: "normal",
    lastMaintenance: "2025-02-20",
    nextMaintenance: "2025-05-20",
    brand: "Manduka",
    purchaseDate: "2024-11-05",
    price: 80,
    lifespan: "36个月",
  },
  {
    id: "E003",
    name: "瑜伽带",
    venue: "所有场地",
    quantity: 40,
    status: "normal",
    lastMaintenance: "2025-03-01",
    nextMaintenance: "2025-06-01",
    brand: "Gaiam",
    purchaseDate: "2024-12-10",
    price: 60,
    lifespan: "24个月",
  },
  {
    id: "E004",
    name: "瑜伽球",
    venue: "2号瑜伽室",
    quantity: 15,
    status: "maintenance",
    lastMaintenance: "2025-03-10",
    nextMaintenance: "2025-03-30",
    brand: "TheraBand",
    purchaseDate: "2024-09-20",
    price: 120,
    lifespan: "18个月",
  },
  {
    id: "E005",
    name: "瑜伽轮",
    venue: "3号瑜伽室",
    quantity: 20,
    status: "normal",
    lastMaintenance: "2025-02-25",
    nextMaintenance: "2025-05-25",
    brand: "Yoga Design Lab",
    purchaseDate: "2025-01-15",
    price: 200,
    lifespan: "36个月",
  },
  {
    id: "E006",
    name: "空中瑜伽吊床",
    venue: "4号瑜伽室",
    quantity: 8,
    status: "normal",
    lastMaintenance: "2025-03-05",
    nextMaintenance: "2025-06-05",
    brand: "Aerial Yoga",
    purchaseDate: "2024-08-10",
    price: 350,
    lifespan: "24个月",
  },
]

export default function EquipmentPage() {
  const [isAddEquipmentOpen, setIsAddEquipmentOpen] = useState(false)
  const [isDetailOpen, setIsDetailOpen] = useState(false)
  const [selectedEquipment, setSelectedEquipment] = useState<(typeof equipments)[0] | null>(null)
  const [viewMode, setViewMode] = useState("list")
  const [searchQuery, setSearchQuery] = useState("")

  const handleViewDetail = (equipment: (typeof equipments)[0]) => {
    setSelectedEquipment(equipment)
    setIsDetailOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">设备管理</h1>
        <Button onClick={() => setIsAddEquipmentOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          添加设备
        </Button>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/3">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索设备"
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="设备状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="normal">正常</SelectItem>
              <SelectItem value="maintenance">维护中</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="所属场地" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有场地</SelectItem>
              <SelectItem value="1">1号瑜伽室</SelectItem>
              <SelectItem value="2">2号瑜伽室</SelectItem>
              <SelectItem value="3">3号瑜伽室</SelectItem>
              <SelectItem value="4">4号瑜伽室</SelectItem>
              <SelectItem value="5">私教室</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex gap-2 ml-auto">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="mr-2 h-4 w-4" />
            导入
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" value={viewMode} onValueChange={setViewMode}>
        <TabsList>
          <TabsTrigger value="list">列表视图</TabsTrigger>
          <TabsTrigger value="grid">网格视图</TabsTrigger>
        </TabsList>
        <TabsContent value="list">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>设备编号</TableHead>
                    <TableHead>设备名称</TableHead>
                    <TableHead>所属场地</TableHead>
                    <TableHead>数量</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>上次维护</TableHead>
                    <TableHead>下次维护</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {equipments.map((equipment) => (
                    <TableRow key={equipment.id}>
                      <TableCell className="font-medium">{equipment.id}</TableCell>
                      <TableCell>{equipment.name}</TableCell>
                      <TableCell>{equipment.venue}</TableCell>
                      <TableCell>{equipment.quantity}</TableCell>
                      <TableCell>
                        <Badge variant={equipment.status === "normal" ? "default" : "destructive"}>
                          {equipment.status === "normal" ? "正常" : "维护中"}
                        </Badge>
                      </TableCell>
                      <TableCell>{equipment.lastMaintenance}</TableCell>
                      <TableCell>{equipment.nextMaintenance}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleViewDetail(equipment)}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Pencil className="mr-2 h-4 w-4" />
                              编辑
                            </DropdownMenuItem>
                            {equipment.status === "normal" ? (
                              <DropdownMenuItem>标记为维护中</DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem>标记为正常</DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="grid">
          <EquipmentGrid equipments={equipments} onViewDetail={handleViewDetail} />
        </TabsContent>
      </Tabs>

      <AddEquipmentDialog open={isAddEquipmentOpen} onOpenChange={setIsAddEquipmentOpen} />
      {selectedEquipment && (
        <EquipmentDetailDialog equipment={selectedEquipment} open={isDetailOpen} onOpenChange={setIsDetailOpen} />
      )}
    </div>
  )
}

