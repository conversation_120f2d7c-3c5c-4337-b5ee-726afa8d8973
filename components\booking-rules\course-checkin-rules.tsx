"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon } from "lucide-react"

const formSchema = z.object({
  // 签到时间设置
  checkInStartMinutes: z.coerce.number().min(0).max(120),
  checkInEndMinutes: z.coerce.number().min(0).max(120),
  
  // 签到方式
  allowSelfCheckin: z.boolean(),
  allowStaffCheckin: z.boolean(),
  allowBatchCheckin: z.boolean(),
  allowAutoCheckin: z.boolean(),
  
  // 签到验证方式
  checkInMethods: z.array(z.string()).min(1),
  
  // 未签到处理
  absentHandling: z.enum(["auto", "delay", "manual"]),
  absentDelayMinutes: z.coerce.number().min(0).max(120).optional(),
  
  // 覆盖全局设置
  overrideGlobal: z.boolean(),
})

const checkInMethods = [
  {
    id: "qrcode",
    label: "二维码扫描签到",
  },
  {
    id: "location",
    label: "位置验证签到",
  },
  {
    id: "face",
    label: "人脸识别签到",
  },
  {
    id: "staff",
    label: "前台确认签到",
  },
]

interface CourseCheckinRulesProps {
  courseTypeId: string;
  onChange: () => void;
}

export function CourseCheckinRules({ courseTypeId, onChange }: CourseCheckinRulesProps) {
  // 根据课程类型获取不同的默认值
  const getDefaultValues = () => {
    // 这里可以根据courseTypeId返回不同的默认值
    if (courseTypeId === "private") {
      return {
        checkInStartMinutes: 15,
        checkInEndMinutes: 10,
        allowSelfCheckin: true,
        allowStaffCheckin: true,
        allowBatchCheckin: false,
        allowAutoCheckin: false,
        checkInMethods: ["qrcode", "staff"],
        absentHandling: "manual" as const,
        absentDelayMinutes: 0,
        overrideGlobal: true,
      }
    }
    
    return {
      checkInStartMinutes: 30,
      checkInEndMinutes: 15,
      allowSelfCheckin: true,
      allowStaffCheckin: true,
      allowBatchCheckin: true,
      allowAutoCheckin: false,
      checkInMethods: ["qrcode", "location", "staff"],
      absentHandling: "delay" as const,
      absentDelayMinutes: 30,
      overrideGlobal: false,
    }
  }
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
  })
  
  const watchAbsentHandling = form.watch("absentHandling")
  const watchOverrideGlobal = form.watch("overrideGlobal")
  
  return (
    <Form {...form}>
      <form onChange={onChange} className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>签到规则设置</CardTitle>
                <CardDescription>
                  设置课程签到的时间限制、签到方式和未签到处理等
                </CardDescription>
              </div>
              <FormField
                control={form.control}
                name="overrideGlobal"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      覆盖全局设置
                    </FormLabel>
                  </FormItem>
                )}
              />
            </div>
          </CardHeader>
          <CardContent>
            {!watchOverrideGlobal && (
              <Alert className="mb-6">
                <InfoIcon className="h-4 w-4" />
                <AlertTitle>使用全局设置</AlertTitle>
                <AlertDescription>
                  当前使用全局签到规则设置。启用"覆盖全局设置"开关可自定义此课程类型的签到规则。
                </AlertDescription>
              </Alert>
            )}
            
            {watchOverrideGlobal && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">签到时间设置</h3>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="checkInStartMinutes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>签到开始时间</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">分钟</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            课程开始前多少分钟可以开始签到
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="checkInEndMinutes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>签到截止时间</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">分钟</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            课程开始后多少分钟内仍可签到（迟到签到）
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">签到方式</h3>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="allowSelfCheckin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许自主签到
                            </FormLabel>
                            <FormDescription>
                              会员可通过小程序/APP自行签到
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="allowStaffCheckin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许代操作签到
                            </FormLabel>
                            <FormDescription>
                              前台/教练可代会员签到
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="allowBatchCheckin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许批量签到
                            </FormLabel>
                            <FormDescription>
                              教练/管理员可批量为多名会员签到
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="allowAutoCheckin"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许自动签到
                            </FormLabel>
                            <FormDescription>
                              系统可根据规则自动为特定会员签到
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">签到验证方式</h3>
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="checkInMethods"
                    render={() => (
                      <FormItem>
                        <div className="mb-4">
                          <FormLabel className="text-base">选择签到验证方式</FormLabel>
                          <FormDescription>
                            可选择多种签到验证方式
                          </FormDescription>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {checkInMethods.map((method) => (
                            <FormField
                              key={method.id}
                              control={form.control}
                              name="checkInMethods"
                              render={({ field }) => {
                                return (
                                  <FormItem
                                    key={method.id}
                                    className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                                  >
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(method.id)}
                                        onCheckedChange={(checked) => {
                                          return checked
                                            ? field.onChange([...field.value, method.id])
                                            : field.onChange(
                                                field.value?.filter(
                                                  (value) => value !== method.id
                                                )
                                              )
                                        }}
                                      />
                                    </FormControl>
                                    <div className="space-y-1 leading-none">
                                      <FormLabel>
                                        {method.label}
                                      </FormLabel>
                                    </div>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">未签到处理</h3>
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="absentHandling"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>未签到处理方式</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择处理方式" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="auto">自动标记为缺席</SelectItem>
                            <SelectItem value="delay">延迟一定时间后标记为缺席</SelectItem>
                            <SelectItem value="manual">教练/管理员手动确认缺席</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {watchAbsentHandling === "delay" && (
                    <FormField
                      control={form.control}
                      name="absentDelayMinutes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>延迟标记时间</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">分钟</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            课程开始后多少分钟未签到则标记为缺席
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </form>
    </Form>
  )
}
