"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronUp, History, Wallet, ArrowDownRight, ArrowUpRight } from "lucide-react"

interface ConsumptionRecord {
  id: string
  date: string
  type: "class" | "service" | "product" | "refund" | "adjustment" // 课程、服务、产品、退款、调整
  itemName: string
  originalPrice: number     // 项目原价
  consumedValue: number     // 本次消耗的卡价值
  remainingValue: number    // 消费后剩余价值
  instructor?: string       // 教练（如果是课程）
  location?: string         // 地点
  notes?: string            // 备注
}

interface CardConsumptionRecordsProps {
  records: ConsumptionRecord[]
  cardType: "time" | "count" | "value" // 期限卡、次数卡、储值卡
  originalValue: number // 原始价值
  compact?: boolean // 是否使用紧凑模式
  limit?: number // 限制显示记录数量
}

export function CardConsumptionRecords({
  records,
  cardType,
  originalValue,
  compact = false,
  limit
}: CardConsumptionRecordsProps) {
  const [showAll, setShowAll] = useState(false)
  
  // 格式化金额显示
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(value)
  }
  
  // 获取消费类型标签
  const getConsumptionTypeBadge = (type: string) => {
    switch (type) {
      case "class":
        return <Badge variant="outline" className="text-blue-500 border-blue-500">课程</Badge>
      case "service":
        return <Badge variant="outline" className="text-green-500 border-green-500">服务</Badge>
      case "product":
        return <Badge variant="outline" className="text-purple-500 border-purple-500">产品</Badge>
      case "refund":
        return <Badge variant="outline" className="text-red-500 border-red-500">退款</Badge>
      case "adjustment":
        return <Badge variant="outline" className="text-yellow-500 border-yellow-500">调整</Badge>
      default:
        return <Badge variant="outline">其他</Badge>
    }
  }
  
  // 获取价值变化图标
  const getValueChangeIcon = (consumedValue: number) => {
    if (consumedValue > 0) {
      return <ArrowDownRight className="h-4 w-4 text-red-500" />
    } else if (consumedValue < 0) {
      return <ArrowUpRight className="h-4 w-4 text-green-500" />
    }
    return null
  }
  
  // 处理记录显示
  const displayRecords = showAll || !limit ? records : records.slice(0, limit)
  
  if (compact) {
    // 紧凑模式，适用于卡片内嵌显示
    return (
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h4 className="text-sm font-medium flex items-center gap-1">
            <History className="h-4 w-4" />
            消费记录
          </h4>
          {records.length > 0 && limit && records.length > limit && (
            <Button variant="ghost" size="sm" onClick={() => setShowAll(!showAll)}>
              {showAll ? "收起" : `查看全部(${records.length})`}
            </Button>
          )}
        </div>
        
        {records.length === 0 ? (
          <p className="text-sm text-muted-foreground">暂无消费记录</p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>日期</TableHead>
                <TableHead>项目</TableHead>
                <TableHead className="text-right">消费值</TableHead>
                <TableHead className="text-right">剩余价值</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell className="text-xs">{record.date}</TableCell>
                  <TableCell className="text-xs">{record.itemName}</TableCell>
                  <TableCell className="text-right text-xs flex items-center justify-end">
                    {getValueChangeIcon(record.consumedValue)}
                    {formatCurrency(Math.abs(record.consumedValue))}
                  </TableCell>
                  <TableCell className="text-right text-xs font-medium">
                    {formatCurrency(record.remainingValue)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>
    )
  }
  
  // 标准模式，显示详细信息
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center gap-2">
            <History className="h-5 w-5" />
            消费记录与价值变化
          </CardTitle>
          <Badge className="bg-blue-500">{records.length}条记录</Badge>
        </div>
      </CardHeader>
      <CardContent>
        {records.length === 0 ? (
          <p className="text-muted-foreground">暂无消费记录</p>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>日期</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>项目</TableHead>
                <TableHead className="text-right">原价</TableHead>
                <TableHead className="text-right">消费值</TableHead>
                <TableHead className="text-right">剩余价值</TableHead>
                <TableHead className="text-right">剩余比例</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayRecords.map((record) => (
                <Collapsible key={record.id} asChild>
                  <>
                    <TableRow className="cursor-pointer hover:bg-muted/50">
                      <TableCell>{record.date}</TableCell>
                      <TableCell>{getConsumptionTypeBadge(record.type)}</TableCell>
                      <TableCell>{record.itemName}</TableCell>
                      <TableCell className="text-right">{formatCurrency(record.originalPrice)}</TableCell>
                      <TableCell className="text-right flex items-center justify-end">
                        {getValueChangeIcon(record.consumedValue)}
                        {formatCurrency(Math.abs(record.consumedValue))}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(record.remainingValue)}
                      </TableCell>
                      <TableCell className="text-right">
                        {Math.round((record.remainingValue / originalValue) * 100)}%
                      </TableCell>
                      <CollapsibleTrigger asChild>
                        <TableCell className="w-10">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </CollapsibleTrigger>
                    </TableRow>
                    <CollapsibleContent asChild>
                      <TableRow className="bg-muted/30">
                        <TableCell colSpan={8} className="p-4">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {record.instructor && (
                              <div>
                                <p className="text-xs text-muted-foreground">教练</p>
                                <p className="text-sm">{record.instructor}</p>
                              </div>
                            )}
                            {record.location && (
                              <div>
                                <p className="text-xs text-muted-foreground">地点</p>
                                <p className="text-sm">{record.location}</p>
                              </div>
                            )}
                            {record.notes && (
                              <div className="md:col-span-2">
                                <p className="text-xs text-muted-foreground">备注</p>
                                <p className="text-sm">{record.notes}</p>
                              </div>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    </CollapsibleContent>
                  </>
                </Collapsible>
              ))}
            </TableBody>
          </Table>
        )}
        
        {records.length > 0 && limit && records.length > limit && !showAll && (
          <div className="mt-4 text-center">
            <Button variant="outline" onClick={() => setShowAll(true)}>
              查看全部{records.length}条记录
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
