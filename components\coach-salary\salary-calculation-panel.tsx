"use client"

import { useState } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calculator, AlertCircle, CheckCircle, Save } from "lucide-react"
import { AddBonusDeductionDialog } from "./add-bonus-deduction-dialog"

// 模拟数据 - 教练列表
const mockCoaches = [
  { id: "C001", name: "张教练" },
  { id: "C002", name: "李教练" },
  { id: "C003", name: "王教练" },
  { id: "C004", name: "赵教练" },
]

// 模拟数据 - 月份列表
const mockMonths = [
  { value: "2023-09", label: "2023年9月" },
  { value: "2023-10", label: "2023年10月" },
  { value: "2023-11", label: "2023年11月" },
]

// 薪资类型映射
const salaryTypeMap = {
  fixed: "固定薪资",
  hourly: "课时费",
  mixed: "底薪+课时费",
  commission: "底薪+提成",
  full: "底薪+课时费+提成",
}

export function SalaryCalculationPanel() {
  const [selectedCoach, setSelectedCoach] = useState<string>("")
  const [selectedMonth, setSelectedMonth] = useState<string>("2023-10")
  const [includeBonus, setIncludeBonus] = useState(true)
  const [includeDeduction, setIncludeDeduction] = useState(true)
  const [calculationResult, setCalculationResult] = useState<any>(null)
  const [calculationStatus, setCalculationStatus] = useState<"idle" | "calculating" | "success" | "error">("idle")
  const [showAddBonusDialog, setShowAddBonusDialog] = useState(false)
  const [showAddDeductionDialog, setShowAddDeductionDialog] = useState(false)
  const [activeTab, setActiveTab] = useState("calculation")

  // 计算薪资
  const calculateSalary = () => {
    if (!selectedCoach || !selectedMonth) {
      return
    }

    setCalculationStatus("calculating")

    // 模拟API调用延迟
    setTimeout(() => {
      // 模拟计算结果
      const result = {
        coachId: selectedCoach,
        coachName: mockCoaches.find(coach => coach.id === selectedCoach)?.name,
        salaryMonth: selectedMonth,
        salaryType: "full",
        baseSalary: 5000,
        classHours: 45,
        hourlySalary: 9000,
        commissionAmount: 2500,
        bonusAmount: includeBonus ? 1000 : 0,
        deductionAmount: includeDeduction ? 500 : 0,
        totalSalary: 17000 - (includeDeduction ? 0 : 500) + (includeBonus ? 0 : -1000),
        taxAmount: 1200,
        socialInsurance: 500,
        housingFund: 0,
        netSalary: 15300 - (includeDeduction ? 0 : 500) + (includeBonus ? 0 : -1000),
        classRecords: [
          { date: "2023-10-02", courseName: "基础瑜伽", duration: 1.5, hourlyRate: 200, amount: 300 },
          { date: "2023-10-05", courseName: "高级瑜伽", duration: 2, hourlyRate: 250, amount: 500 },
          { date: "2023-10-08", courseName: "阴瑜伽", duration: 1.5, hourlyRate: 200, amount: 300 },
          // 更多课程记录...
        ],
        commissionRecords: [
          { date: "2023-10-02", courseName: "私教课", income: 5000, rate: 10, amount: 500 },
          { date: "2023-10-10", courseName: "私教课", income: 8000, rate: 10, amount: 800 },
          { date: "2023-10-18", courseName: "私教课", income: 12000, rate: 10, amount: 1200 },
          // 更多提成记录...
        ],
        bonusRecords: includeBonus ? [
          { date: "2023-10-15", reason: "绩效奖金", amount: 500 },
          { date: "2023-10-25", reason: "满勤奖", amount: 500 },
        ] : [],
        deductionRecords: includeDeduction ? [
          { date: "2023-10-12", reason: "迟到", amount: 200 },
          { date: "2023-10-20", reason: "请假", amount: 300 },
        ] : [],
      }

      setCalculationResult(result)
      setCalculationStatus("success")
    }, 1500)
  }

  // 保存薪资记录
  const saveSalaryRecord = () => {
    // 模拟保存操作
    alert("薪资记录已保存")
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="calculation" value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="calculation">薪资计算</TabsTrigger>
          <TabsTrigger value="result" disabled={!calculationResult}>计算结果</TabsTrigger>
        </TabsList>
        
        <TabsContent value="calculation" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="coach">选择教练</Label>
                <Select value={selectedCoach} onValueChange={setSelectedCoach}>
                  <SelectTrigger id="coach">
                    <SelectValue placeholder="请选择教练" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockCoaches.map((coach) => (
                      <SelectItem key={coach.id} value={coach.id}>
                        {coach.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="month">薪资月份</Label>
                <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                  <SelectTrigger id="month">
                    <SelectValue placeholder="请选择月份" />
                  </SelectTrigger>
                  <SelectContent>
                    {mockMonths.map((month) => (
                      <SelectItem key={month.value} value={month.value}>
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>计算选项</Label>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-bonus" 
                    checked={includeBonus} 
                    onCheckedChange={(checked) => setIncludeBonus(!!checked)} 
                  />
                  <label
                    htmlFor="include-bonus"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    包含奖金
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-deduction" 
                    checked={includeDeduction} 
                    onCheckedChange={(checked) => setIncludeDeduction(!!checked)} 
                  />
                  <label
                    htmlFor="include-deduction"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    包含扣款
                  </label>
                </div>
              </div>

              <div className="pt-4">
                <Button 
                  onClick={calculateSalary} 
                  disabled={!selectedCoach || !selectedMonth || calculationStatus === "calculating"}
                  className="w-full gap-2"
                >
                  <Calculator className="h-4 w-4" />
                  {calculationStatus === "calculating" ? "计算中..." : "计算薪资"}
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label>奖金与扣款管理</Label>
                <div className="grid grid-cols-2 gap-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowAddBonusDialog(true)}
                    disabled={!selectedCoach || !selectedMonth}
                  >
                    添加奖金
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setShowAddDeductionDialog(true)}
                    disabled={!selectedCoach || !selectedMonth}
                  >
                    添加扣款
                  </Button>
                </div>
              </div>

              {calculationStatus === "success" && (
                <Alert className="bg-green-50 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertTitle className="text-green-800">计算成功</AlertTitle>
                  <AlertDescription className="text-green-700">
                    薪资计算已完成，请查看计算结果。
                  </AlertDescription>
                </Alert>
              )}

              {calculationStatus === "error" && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>计算失败</AlertTitle>
                  <AlertDescription>
                    薪资计算过程中发生错误，请重试。
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          {calculationResult && (
            <div className="pt-4">
              <Button 
                variant="default" 
                onClick={() => setActiveTab("result")}
                className="w-full"
              >
                查看计算结果
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="result" className="space-y-6">
          {calculationResult && (
            <>
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-medium">
                    {calculationResult.coachName} - {mockMonths.find(m => m.value === calculationResult.salaryMonth)?.label} 薪资
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    薪资类型: {salaryTypeMap[calculationResult.salaryType as keyof typeof salaryTypeMap]}
                  </p>
                </div>
                <Button onClick={saveSalaryRecord} className="gap-2">
                  <Save className="h-4 w-4" />
                  保存薪资记录
                </Button>
              </div>

              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">底薪:</span>
                        <span>¥{calculationResult.baseSalary.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">课时费 ({calculationResult.classHours}课时):</span>
                        <span>¥{calculationResult.hourlySalary.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">提成:</span>
                        <span>¥{calculationResult.commissionAmount.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">奖金:</span>
                        <span>¥{calculationResult.bonusAmount.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">扣款:</span>
                        <span className="text-red-500">-¥{calculationResult.deductionAmount.toLocaleString()}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between font-medium">
                        <span>总薪资:</span>
                        <span>¥{calculationResult.totalSalary.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">个人所得税:</span>
                        <span className="text-red-500">-¥{calculationResult.taxAmount.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">社保:</span>
                        <span className="text-red-500">-¥{calculationResult.socialInsurance.toLocaleString()}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between text-lg font-bold">
                        <span>实发薪资:</span>
                        <span>¥{calculationResult.netSalary.toLocaleString()}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">课时费明细</h4>
                    <div className="max-h-40 overflow-y-auto rounded-md border p-2">
                      {calculationResult.classRecords.map((record: any, index: number) => (
                        <div key={index} className="flex justify-between py-1 text-sm">
                          <span>{record.date} {record.courseName} ({record.duration}课时)</span>
                          <span>¥{record.amount}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {calculationResult.commissionRecords.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">提成明细</h4>
                      <div className="max-h-40 overflow-y-auto rounded-md border p-2">
                        {calculationResult.commissionRecords.map((record: any, index: number) => (
                          <div key={index} className="flex justify-between py-1 text-sm">
                            <span>{record.date} {record.courseName} (¥{record.income} × {record.rate}%)</span>
                            <span>¥{record.amount}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {calculationResult.bonusRecords.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">奖金明细</h4>
                      <div className="max-h-40 overflow-y-auto rounded-md border p-2">
                        {calculationResult.bonusRecords.map((record: any, index: number) => (
                          <div key={index} className="flex justify-between py-1 text-sm">
                            <span>{record.date} {record.reason}</span>
                            <span>¥{record.amount}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {calculationResult.deductionRecords.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">扣款明细</h4>
                      <div className="max-h-40 overflow-y-auto rounded-md border p-2">
                        {calculationResult.deductionRecords.map((record: any, index: number) => (
                          <div key={index} className="flex justify-between py-1 text-sm">
                            <span>{record.date} {record.reason}</span>
                            <span className="text-red-500">-¥{record.amount}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
        </TabsContent>
      </Tabs>

      {/* 添加奖金对话框 */}
      <AddBonusDeductionDialog
        open={showAddBonusDialog}
        onOpenChange={setShowAddBonusDialog}
        type="bonus"
        coachId={selectedCoach}
        coachName={mockCoaches.find(coach => coach.id === selectedCoach)?.name || ""}
        month={selectedMonth}
      />

      {/* 添加扣款对话框 */}
      <AddBonusDeductionDialog
        open={showAddDeductionDialog}
        onOpenChange={setShowAddDeductionDialog}
        type="deduction"
        coachId={selectedCoach}
        coachName={mockCoaches.find(coach => coach.id === selectedCoach)?.name || ""}
        month={selectedMonth}
      />
    </div>
  )
}
