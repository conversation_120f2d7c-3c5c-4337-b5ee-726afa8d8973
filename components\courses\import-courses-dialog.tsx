"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { toast } from "@/hooks/use-toast"
import { courseApi } from "@/lib/api"
import { FileUp, AlertCircle, Upload } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"

export interface ImportCoursesDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImportSuccess?: () => void
}

export function ImportCoursesDialog({ 
  open, 
  onOpenChange,
  onImportSuccess
}: ImportCoursesDialogProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      // 验证文件类型
      const fileType = selectedFile.type
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
        'text/csv', // .csv
        'application/csv', // .csv - 一些浏览器使用这个MIME类型
      ]
      
      if (!validTypes.includes(fileType)) {
        setError('请上传Excel (.xlsx, .xls) 或 CSV (.csv) 格式的文件')
        setFile(null)
        return
      }
      
      // 验证文件大小 (最大5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        setError('文件大小不能超过5MB')
        setFile(null)
        return
      }
      
      setFile(selectedFile)
      setError(null)
    }
  }

  // 处理文件上传
  const handleUpload = async () => {
    if (!file) {
      setError('请先选择要导入的文件')
      return
    }

    try {
      setIsUploading(true)
      setUploadProgress(0)
      setError(null)
      
      // 模拟上传进度
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          // 到90%停止，剩下的10%留给处理
          if (prev >= 90) {
            clearInterval(interval)
            return 90
          }
          return prev + 10
        })
      }, 500)
      
      // 调用API导入课程
      await courseApi.importCourses(file)
      
      clearInterval(interval)
      setUploadProgress(100)
      
      toast({
        title: "导入成功",
        description: "课程数据已成功导入",
      })
      
      // 导入成功后关闭对话框
      setTimeout(() => {
        onOpenChange(false)
        // 调用成功回调刷新课程列表
        if (typeof onImportSuccess === 'function') {
          onImportSuccess()
        }
      }, 1000)
    } catch (error) {
      console.error('导入失败:', error)
      setError(error instanceof Error ? error.message : "导入失败，请检查文件格式并重试")
      
      toast({
        title: "导入失败",
        description: error instanceof Error ? error.message : "导入过程中发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  // 重置状态
  const resetState = () => {
    setFile(null)
    setIsUploading(false)
    setUploadProgress(0)
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }
  
  // 在对话框关闭时重置状态
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetState()
    }
    onOpenChange(open)
  }
  
  // 触发文件选择
  const handleBrowseClick = () => {
    fileInputRef.current?.click()
  }

  // 修改模板下载功能
  const handleDownloadTemplate = () => {
    // 使用我们已经创建的下载助手直接创建和下载模板
    try {
      // 尝试导入downloadHelper
      import("@/lib/download-helper").then(({ downloadHelper }) => {
        // 调用辅助函数创建模板
        downloadHelper.createCourseImportTemplate();
        
        toast({
          title: "下载成功",
          description: "课程导入模板已下载",
        });
      });
    } catch (error) {
      console.error('模板下载失败:', error);
      toast({
        title: "下载失败",
        description: "模板下载失败，请重试",
        variant: "destructive",
      });
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>导入课程</DialogTitle>
          <DialogDescription>
            上传Excel或CSV文件导入课程数据。您可以先下载模板，按照模板格式填写课程信息。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex justify-between items-center">
            <Button 
              variant="outline" 
              onClick={handleDownloadTemplate}
              disabled={isUploading}
            >
              下载导入模板
            </Button>
            <Input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>

          <div 
            className={`
              border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
              ${isUploading ? 'bg-muted' : 'hover:bg-muted/50'}
              ${error ? 'border-destructive' : 'border-muted'}
            `}
            onClick={handleBrowseClick}
          >
            <div className="flex flex-col items-center justify-center space-y-2">
              <FileUp className="h-8 w-8 text-muted-foreground" />
              <div className="text-sm text-muted-foreground">
                {file ? (
                  <span className="font-medium text-foreground">{file.name}</span>
                ) : (
                  <>
                    <span className="font-medium">点击选择文件</span> 或拖放文件到此处
                    <p>支持 .xlsx, .xls, .csv 格式</p>
                  </>
                )}
              </div>
            </div>
          </div>

          {isUploading && (
            <div className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <span>上传进度</span>
                <span>{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>导入失败</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter className="flex justify-between items-center pt-2">
          <div className="text-sm text-muted-foreground">
            {file && !isUploading && `已选择: ${file.name}`}
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => handleOpenChange(false)}
              disabled={isUploading}
            >
              取消
            </Button>
            <Button 
              onClick={handleUpload} 
              disabled={!file || isUploading}
            >
              {isUploading ? (
                <>处理中...</>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  导入
                </>
              )}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

