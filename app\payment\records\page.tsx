"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Search, Download, Eye, RefreshCw, FileText } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"

export default function PaymentRecordsPage() {
  const [dateRange, setDateRange] = useState("7days")
  const [paymentType, setPaymentType] = useState("all")
  const [paymentStatus, setPaymentStatus] = useState("all")
  const [selectedPayment, setSelectedPayment] = useState(null)
  const [showPaymentDetails, setShowPaymentDetails] = useState(false)

  const [paymentRecords] = useState([
    {
      id: "WX20250328123456",
      channel: "wechat",
      amount: "¥199.00",
      description: "基础瑜伽月卡",
      time: "2025-03-28 14:30:25",
      status: "success",
      type: "扫码支付",
      customer: "张三",
      details: {
        transactionId: "4200001234202503281234567890",
        outTradeNo: "YG20250328001",
        bankType: "CMB_DEBIT",
        feeType: "CNY",
        totalFee: "19900",
        cashFee: "19900",
        timeEnd: "**************",
        tradeState: "SUCCESS",
        tradeStateDesc: "支付成功",
      },
    },
    {
      id: "ZFB20250328234567",
      channel: "alipay",
      amount: "¥299.00",
      description: "高级瑜伽季卡",
      time: "2025-03-28 11:15:42",
      status: "success",
      type: "手机网站支付",
      customer: "李四",
      details: {
        tradeNo: "2025032822001001234567890123",
        outTradeNo: "YG20250328002",
        buyerLogonId: "159****8888",
        totalAmount: "299.00",
        receiptAmount: "299.00",
        gmtPayment: "2025-03-28 11:15:42",
        fundBillList: [
          {
            amount: "299.00",
            fundChannel: "ALIPAYACCOUNT",
          },
        ],
        tradeStatus: "TRADE_SUCCESS",
      },
    },
    {
      id: "****************",
      channel: "wechat",
      amount: "¥99.00",
      description: "单次体验课",
      time: "2025-03-28 09:45:18",
      status: "pending",
      type: "小程序支付",
      customer: "王五",
      details: {
        outTradeNo: "YG20250328003",
        tradeState: "NOTPAY",
        tradeStateDesc: "订单未支付",
      },
    },
    {
      id: "ZFB20250327123456",
      channel: "alipay",
      amount: "¥129.00",
      description: "阴瑜伽单次课",
      time: "2025-03-27 16:30:25",
      status: "success",
      type: "扫码支付",
      customer: "赵六",
      details: {
        tradeNo: "2025032722001001234567890124",
        outTradeNo: "YG20250327001",
        buyerLogonId: "186****9999",
        totalAmount: "129.00",
        receiptAmount: "129.00",
        gmtPayment: "2025-03-27 16:30:25",
        fundBillList: [
          {
            amount: "129.00",
            fundChannel: "ALIPAYACCOUNT",
          },
        ],
        tradeStatus: "TRADE_SUCCESS",
      },
    },
    {
      id: "****************",
      channel: "wechat",
      amount: "¥399.00",
      description: "空中瑜伽10次卡",
      time: "2025-03-27 10:15:42",
      status: "refunded",
      type: "扫码支付",
      customer: "钱七",
      details: {
        transactionId: "4200001234202503271234567891",
        outTradeNo: "YG20250327002",
        bankType: "ICBC_DEBIT",
        feeType: "CNY",
        totalFee: "39900",
        cashFee: "39900",
        timeEnd: "**************",
        tradeState: "REFUND",
        tradeStateDesc: "转入退款",
        refundId: "50000506732025032701234567890",
        refundFee: "39900",
        refundTime: "2025-03-27 15:30:25",
      },
    },
  ])

  const handleViewDetails = (payment) => {
    setSelectedPayment(payment)
    setShowPaymentDetails(true)
  }

  const handleRefreshStatus = (paymentId) => {
    alert(`刷新支付状态: ${paymentId}`)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">支付记录管理</h1>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          导出数据
        </Button>
      </div>

      <Tabs defaultValue="payment" className="space-y-4">
        <TabsList>
          <TabsTrigger value="payment">支付记录</TabsTrigger>
          <TabsTrigger value="refund">退款记录</TabsTrigger>
        </TabsList>

        <TabsContent value="payment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付记录查询</CardTitle>
              <CardDescription>查询和管理支付记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="w-full md:w-1/3">
                  <Input placeholder="输入订单号、交易号或客户名称" />
                </div>
                <div className="flex flex-1 gap-4">
                  <Select value={paymentType} onValueChange={setPaymentType}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="支付渠道" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部渠道</SelectItem>
                      <SelectItem value="wechat">微信支付</SelectItem>
                      <SelectItem value="alipay">支付宝</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={paymentStatus} onValueChange={setPaymentStatus}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="支付状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="success">支付成功</SelectItem>
                      <SelectItem value="pending">待支付</SelectItem>
                      <SelectItem value="refunded">已退款</SelectItem>
                      <SelectItem value="failed">支付失败</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7days">最近7天</SelectItem>
                      <SelectItem value="30days">最近30天</SelectItem>
                      <SelectItem value="90days">最近90天</SelectItem>
                      <SelectItem value="custom">自定义</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>交易单号</TableHead>
                    <TableHead>支付渠道</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>商品描述</TableHead>
                    <TableHead>客户</TableHead>
                    <TableHead>支付时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paymentRecords.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">{payment.id}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{payment.channel === "wechat" ? "微信支付" : "支付宝"}</Badge>
                      </TableCell>
                      <TableCell>{payment.amount}</TableCell>
                      <TableCell>{payment.description}</TableCell>
                      <TableCell>{payment.customer}</TableCell>
                      <TableCell>{payment.time}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            payment.status === "success"
                              ? "default"
                              : payment.status === "pending"
                                ? "outline"
                                : payment.status === "refunded"
                                  ? "secondary"
                                  : "destructive"
                          }
                        >
                          {payment.status === "success"
                            ? "支付成功"
                            : payment.status === "pending"
                              ? "待支付"
                              : payment.status === "refunded"
                                ? "已退款"
                                : "支付失败"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleViewDetails(payment)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          {payment.status === "pending" && (
                            <Button variant="ghost" size="icon" onClick={() => handleRefreshStatus(payment.id)}>
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="refund" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>退款记录管理</CardTitle>
              <CardDescription>查询和管理退款记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="w-full md:w-1/3">
                  <Input placeholder="输入退款单号、订单号或客户名称" />
                </div>
                <div className="flex flex-1 gap-4">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="支付渠道" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部渠道</SelectItem>
                      <SelectItem value="wechat">微信支付</SelectItem>
                      <SelectItem value="alipay">支付宝</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select defaultValue="all">
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="退款状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="success">退款成功</SelectItem>
                      <SelectItem value="processing">处理中</SelectItem>
                      <SelectItem value="failed">退款失败</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select defaultValue="30days">
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7days">最近7天</SelectItem>
                      <SelectItem value="30days">最近30天</SelectItem>
                      <SelectItem value="90days">最近90天</SelectItem>
                      <SelectItem value="custom">自定义</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>退款单号</TableHead>
                    <TableHead>原订单号</TableHead>
                    <TableHead>支付渠道</TableHead>
                    <TableHead>退款金额</TableHead>
                    <TableHead>客户</TableHead>
                    <TableHead>申请时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">RF20250327001</TableCell>
                    <TableCell>****************</TableCell>
                    <TableCell>
                      <Badge variant="outline">微信支付</Badge>
                    </TableCell>
                    <TableCell>¥399.00</TableCell>
                    <TableCell>钱七</TableCell>
                    <TableCell>2025-03-27 15:30:25</TableCell>
                    <TableCell>
                      <Badge>退款成功</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">RF20250326001</TableCell>
                    <TableCell>ZFB20250326123456</TableCell>
                    <TableCell>
                      <Badge variant="outline">支付宝</Badge>
                    </TableCell>
                    <TableCell>¥199.00</TableCell>
                    <TableCell>孙八</TableCell>
                    <TableCell>2025-03-26 14:20:15</TableCell>
                    <TableCell>
                      <Badge>退款成功</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">RF20250325001</TableCell>
                    <TableCell>WX20250325123456</TableCell>
                    <TableCell>
                      <Badge variant="outline">微信支付</Badge>
                    </TableCell>
                    <TableCell>¥99.00</TableCell>
                    <TableCell>周九</TableCell>
                    <TableCell>2025-03-25 10:15:30</TableCell>
                    <TableCell>
                      <Badge variant="outline">处理中</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="ghost" size="icon">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 支付详情对话框 */}
      {selectedPayment && (
        <Dialog open={showPaymentDetails} onOpenChange={setShowPaymentDetails}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>支付详情</DialogTitle>
              <DialogDescription>交易单号: {selectedPayment.id}</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">支付渠道</h3>
                  <p>{selectedPayment.channel === "wechat" ? "微信支付" : "支付宝"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">支付方式</h3>
                  <p>{selectedPayment.type}</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">商品描述</h3>
                <p>{selectedPayment.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">支付金额</h3>
                  <p className="font-medium">{selectedPayment.amount}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">支付状态</h3>
                  <Badge
                    variant={
                      selectedPayment.status === "success"
                        ? "default"
                        : selectedPayment.status === "pending"
                          ? "outline"
                          : selectedPayment.status === "refunded"
                            ? "secondary"
                            : "destructive"
                    }
                  >
                    {selectedPayment.status === "success"
                      ? "支付成功"
                      : selectedPayment.status === "pending"
                        ? "待支付"
                        : selectedPayment.status === "refunded"
                          ? "已退款"
                          : "支付失败"}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">客户</h3>
                  <p>{selectedPayment.customer}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">支付时间</h3>
                  <p>{selectedPayment.time}</p>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium mb-2">详细信息</h3>
                <div className="bg-muted rounded-md p-3">
                  <pre className="text-xs overflow-auto whitespace-pre-wrap">
                    {JSON.stringify(selectedPayment.details, null, 2)}
                  </pre>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowPaymentDetails(false)}>
                  关闭
                </Button>
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  导出详情
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

