// 测试教练和场地API
async function testAPI() {
  console.log('开始测试教练和场地API...');
  
  try {
    // 1. 测试教练API
    console.log('\n1. 测试教练API:');
    const coachResponse = await fetch('http://localhost:3001/api/coaches?tenantId=1&pageSize=10');
    const coachResult = await coachResponse.json();
    
    if (coachResult.code === 200) {
      console.log(`✓ 教练API正常，返回 ${coachResult.data.list.length} 个教练`);
      coachResult.data.list.forEach((coach, index) => {
        console.log(`  ${index + 1}. ${coach.name} - ${coach.specialties.join(', ')} (¥${coach.hourlyRate}/小时)`);
      });
    } else {
      console.log(`✗ 教练API失败: ${coachResult.msg}`);
    }

    // 2. 测试场地API
    console.log('\n2. 测试场地API:');
    const venueResponse = await fetch('http://localhost:3001/api/venues?tenantId=1&pageSize=10');
    const venueResult = await venueResponse.json();
    
    if (venueResult.code === 200) {
      console.log(`✓ 场地API正常，返回 ${venueResult.data.list.length} 个场地`);
      venueResult.data.list.forEach((venue, index) => {
        console.log(`  ${index + 1}. ${venue.name} - ${venue.location} (容量: ${venue.capacity}人, ¥${venue.hourlyRate}/小时)`);
      });
    } else {
      console.log(`✗ 场地API失败: ${venueResult.msg}`);
    }

    // 3. 测试课程类型API
    console.log('\n3. 测试课程类型API:');
    const typeResponse = await fetch('http://localhost:3001/api/course-types?tenantId=1');
    const typeResult = await typeResponse.json();
    
    if (typeResult.code === 200) {
      console.log(`✓ 课程类型API正常，返回 ${typeResult.data.list.length} 个课程类型`);
      typeResult.data.list.forEach((type, index) => {
        console.log(`  ${index + 1}. ${type.name} - ${type.color}`);
      });
    } else {
      console.log(`✗ 课程类型API失败: ${typeResult.msg}`);
    }

    console.log('\n✓ API测试完成!');
    console.log('\n现在可以在添加课程页面使用真实的数据了：');
    console.log('- 课程类型选择：从真实的课程类型API获取');
    console.log('- 教练选择：从真实的教练API获取');
    console.log('- 场地选择：从真实的场地API获取');
    
  } catch (error) {
    console.error('API测试失败:', error.message);
  }
}

testAPI();
