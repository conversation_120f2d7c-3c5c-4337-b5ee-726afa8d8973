<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瑜伽后台管理系统 - 开发甘特图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            overflow: auto;
        }
        .note {
            margin-top: 20px;
            padding: 10px;
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>基础版开发详细周计划 (2025年6月1日-8月24日)</h1>
        
        <div class="mermaid">
            gantt
                title 基础版开发详细周计划 (2025年6月1日-8月24日)
                dateFormat  YYYY-MM-DD
                axisFormat  第%U周
                
                section 第1周 (6.1-6.7)
                业务需求分析       :a1, 2025-06-01, 7d
                核心功能定义       :a2, after a1, 7d
                
                section 第2周 (6.8-6.14)
                用户流程设计       :a3, 2025-06-08, 4d
                交互原型设计       :a4, after a3, 3d
                初版原型评审       :a5, after a4, 1d
                
                section 第3周 (6.15-6.21)
                技术架构设计       :a6, 2025-06-15, 3d
                数据库建模        :a7, after a6, 2d
                接口规范定义       :a8, after a7, 2d
                
                section 第4-5周 (6.22-7.5)
                课程列表API     :b1, 2025-06-22, 5d
                分类筛选功能     :b2, after b1, 4d
                课程详情页      :b3, after b2, 5d
                课程创建功能     :c1, 2025-06-22, 7d
                排期管理       :c2, after c1, 7d
                
                section 第6周 (7.6-7.12)
                日历组件开发     :b4, 2025-07-06, 5d
                预约规则引擎     :b5, after b4, 2d
                后台基础框架     :d1, 2025-07-06, 7d
                
                section 第7周 (7.13-7.19)
                微信支付对接     :b6, 2025-07-13, 4d
                支付宝对接      :b7, after b6, 3d
                订单处理流程     :c3, 2025-07-13, 5d
                状态变更通知     :c4, after c3, 2d
                
                section 第8周 (7.20-7.26)
                店铺创建功能     :d2, 2025-07-20, 4d
                套餐定价策略     :d3, after d2, 3d
                股东关系架构     :e1, 2025-07-20, 4d
                分润规则设置     :e2, after e1, 3d
                
                section 第9周 (7.27-8.2)
                接口联调测试     :f1, 2025-07-27, 5d
                数据一致性验证    :f2, after f1, 2d
                我的订单模块     :b8, 2025-07-27, 4d
                会员卡展示      :b9, after b8, 3d
                
                section 第10周 (8.3-8.9)
                功能测试       :g1, 2025-08-03, 4d
                性能测试       :g2, after g1, 3d
                安全测试       :g3, after g2, 3d
                用户手册编写     :h1, 2025-08-03, 5d
                
                section 第11周 (8.10-8.16)
                问题修复         :i1, 2025-08-10, 7d
                UAT用户测试       :i2, after i1, 3d
                
                section 第12周 (8.17-8.23)
                上线准备         :j1, 2025-08-17, 4d
                生产环境部署       :j2, after j1, 3d
                
                section 交付日 (8.24)
                里程碑：基础版发布  :milestone, m1, 2025-08-24, 0d
        </div>
        
        <div class="note">
            <p><strong>注意：</strong>此甘特图展示了瑜伽后台管理系统基础版的开发计划，从需求分析到最终发布的完整时间线。</p>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            gantt: {
                titleTopMargin: 25,
                barHeight: 20,
                barGap: 4,
                topPadding: 50,
                sidePadding: 75,
                gridLineStartPadding: 35,
                fontSize: 12,
                fontFamily: '"Microsoft YaHei", Arial, sans-serif',
                numberSectionStyles: 12,
                axisFormat: '第%U周',
                tickInterval: '1week'
            }
        });
    </script>
</body>
</html>
