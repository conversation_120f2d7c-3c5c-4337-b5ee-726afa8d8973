"use client"

import { useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, FileText, Printer, Send } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface OrderBatchOperationsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedOrders: string[]
  onApplyOperation: (operation: string, data: any) => void
}

export function OrderBatchOperationsDialog({
  open,
  onOpenChange,
  selectedOrders,
  onApplyOperation,
}: OrderBatchOperationsDialogProps) {
  const [operation, setOperation] = useState("")
  const [notes, setNotes] = useState("")

  const handleApply = () => {
    onApplyOperation(operation, { notes })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>批量操作</DialogTitle>
          <DialogDescription>已选择 {selectedOrders.length} 个订单</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="operation">选择操作</Label>
            <Select value={operation} onValueChange={setOperation}>
              <SelectTrigger id="operation">
                <SelectValue placeholder="选择要执行的操作" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="print">批量打印</SelectItem>
                <SelectItem value="export">批量导出</SelectItem>
                <SelectItem value="send-receipt">发送电子收据</SelectItem>
                <SelectItem value="mark-paid">标记为已支付</SelectItem>
                <SelectItem value="mark-cancelled">标记为已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {operation && (
            <div className="space-y-2">
              <Label htmlFor="notes">操作备注</Label>
              <Textarea
                id="notes"
                placeholder="请输入操作备注"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          )}

          {operation === "mark-cancelled" && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>注意</AlertTitle>
              <AlertDescription>批量取消操作不可撤销，请确认后再操作。</AlertDescription>
            </Alert>
          )}

          {operation === "print" && (
            <Alert>
              <Printer className="h-4 w-4" />
              <AlertTitle>打印提示</AlertTitle>
              <AlertDescription>将为选中的 {selectedOrders.length} 个订单生成打印任务。</AlertDescription>
            </Alert>
          )}

          {operation === "export" && (
            <Alert>
              <FileText className="h-4 w-4" />
              <AlertTitle>导出提示</AlertTitle>
              <AlertDescription>将导出选中的 {selectedOrders.length} 个订单数据。</AlertDescription>
            </Alert>
          )}

          {operation === "send-receipt" && (
            <Alert>
              <Send className="h-4 w-4" />
              <AlertTitle>发送提示</AlertTitle>
              <AlertDescription>将向对应会员发送 {selectedOrders.length} 个订单的电子收据。</AlertDescription>
            </Alert>
          )}
        </div>

        <Separator />

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleApply} disabled={!operation}>
            应用
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

