#!/bin/bash

# 瑜伽后台管理系统 - 内部调用部署脚本

echo "🚀 开始部署瑜伽后台管理系统（内部调用模式）"

# 1. 检查后端服务状态
echo "📡 检查后端服务状态..."
if curl -f http://localhost:8000/health 2>/dev/null; then
    echo "✅ 后端服务运行正常"
else
    echo "❌ 后端服务未运行，请先启动后端服务"
    echo "提示：确保后端API服务在端口8000上运行"
    exit 1
fi

# 2. 获取服务器公网IP
echo "🌐 获取服务器公网IP..."
PUBLIC_IP=$(curl -s ifconfig.me 2>/dev/null || echo "无法获取")
echo "服务器公网IP: $PUBLIC_IP"

# 3. 创建生产环境配置
echo "⚙️ 配置生产环境变量..."
cat > .env.production << EOF
# 生产环境配置 - 内部调用优化

# API配置
# 客户端API调用（浏览器访问）
NEXT_PUBLIC_API_BASE_URL=http://$PUBLIC_IP:8000

# 服务端API调用（内部网络，用于SSR和服务端请求）
API_BASE_URL=http://localhost:8000

# 端口配置
PORT=3001

# 应用配置
NODE_ENV=production

# 图片服务配置
NEXT_PUBLIC_IMAGE_DOMAIN=ai-public.mastergo.com
EOF

# 4. 安装依赖
echo "📦 安装项目依赖..."
npm install

# 5. 构建项目
echo "🔨 构建生产版本..."
npm run build

# 6. 配置防火墙
echo "🔥 配置防火墙..."
firewall-cmd --permanent --add-port=3001/tcp
firewall-cmd --permanent --add-port=8000/tcp
firewall-cmd --reload

# 7. 启动应用
echo "🚀 启动应用..."
npm run start &

# 8. 等待启动
echo "⏳ 等待应用启动..."
sleep 10

# 9. 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:3001 2>/dev/null; then
    echo "✅ 前端服务启动成功"
    echo "🌐 外网访问地址: http://$PUBLIC_IP:3001"
else
    echo "❌ 前端服务启动失败"
    exit 1
fi

echo ""
echo "🎉 部署完成！"
echo "📋 服务信息："
echo "   - 前端服务: http://$PUBLIC_IP:3001"
echo "   - 后端服务: http://$PUBLIC_IP:8000"
echo "   - 内部通信: 前端 -> localhost:8000"
echo ""
echo "💡 优化说明："
echo "   - 服务端渲染使用内部网络调用 (localhost:8000)"
echo "   - 客户端请求使用公网地址 ($PUBLIC_IP:8000)"
echo "   - 减少了网络延迟和外部依赖"
