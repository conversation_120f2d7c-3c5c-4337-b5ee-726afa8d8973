"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, Filter, Download, Calendar } from "lucide-react"
import { cn } from "@/lib/utils"

interface SalesRecord {
  id: string
  member: string
  date: string
  amount: string
  paymentMethod: string
  status: string
  discount?: string
}

interface SalesRecordsProps {
  cardId: number | string
  className?: string
}

export function SalesRecords({ cardId, className }: SalesRecordsProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [filterPayment, setFilterPayment] = useState("all")
  const [currentView, setCurrentView] = useState("list")
  
  // 模拟销售记录数据
  const salesRecords: SalesRecord[] = [
    {
      id: "S001",
      member: "张三",
      date: "2023-01-15",
      amount: "¥3,600",
      paymentMethod: "微信支付",
      status: "已完成",
    },
    {
      id: "S002",
      member: "李四",
      date: "2023-02-20",
      amount: "¥3,600",
      paymentMethod: "支付宝",
      status: "已完成",
    },
    {
      id: "S003",
      member: "王五",
      date: "2023-03-10",
      amount: "¥3,240",
      paymentMethod: "微信支付",
      status: "已完成",
      discount: "9折优惠",
    },
    {
      id: "S004",
      member: "赵六",
      date: "2023-04-05",
      amount: "¥3,600",
      paymentMethod: "银行卡",
      status: "已完成",
    },
    {
      id: "S005",
      member: "钱七",
      date: "2023-05-12",
      amount: "¥3,240",
      paymentMethod: "微信支付",
      status: "已完成",
      discount: "9折优惠",
    },
    {
      id: "S006",
      member: "孙八",
      date: "2023-06-18",
      amount: "¥3,600",
      paymentMethod: "支付宝",
      status: "已完成",
    },
    {
      id: "S007",
      member: "周九",
      date: "2023-07-22",
      amount: "¥3,240",
      paymentMethod: "微信支付",
      status: "已完成",
      discount: "9折优惠",
    },
  ]
  
  // 过滤和搜索记录
  const filteredRecords = salesRecords.filter(record => {
    // 搜索条件
    const matchesSearch = 
      searchTerm === "" || 
      record.member.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.id.toLowerCase().includes(searchTerm.toLowerCase())
    
    // 状态过滤
    const matchesStatus = filterStatus === "all" || record.status === filterStatus
    
    // 支付方式过滤
    const matchesPayment = filterPayment === "all" || record.paymentMethod === filterPayment
    
    return matchesSearch && matchesStatus && matchesPayment
  })
  
  // 渲染列表视图
  const renderListView = () => {
    return (
      <div className="space-y-4">
        {filteredRecords.map((record) => (
          <div key={record.id} className="flex items-center justify-between rounded-lg border p-3">
            <div>
              <p className="font-medium">{record.member}</p>
              <p className="text-sm text-muted-foreground">订单号: {record.id}</p>
            </div>
            <div className="hidden md:block">
              <p className="text-sm">购买日期: {record.date}</p>
              <p className="text-sm">支付方式: {record.paymentMethod}</p>
            </div>
            <div>
              <p className="font-medium text-right">{record.amount}</p>
              {record.discount && (
                <p className="text-right text-sm text-muted-foreground">{record.discount}</p>
              )}
            </div>
            <Badge variant="outline">{record.status}</Badge>
          </div>
        ))}
        
        {filteredRecords.length === 0 && (
          <div className="flex h-32 items-center justify-center rounded-lg border border-dashed">
            <p className="text-muted-foreground">没有找到匹配的销售记录</p>
          </div>
        )}
      </div>
    )
  }
  
  // 渲染卡片视图
  const renderCardView = () => {
    return (
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {filteredRecords.map((record) => (
          <Card key={record.id} className="overflow-hidden">
            <CardHeader className="p-4 pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">{record.member}</CardTitle>
                <Badge variant="outline">{record.status}</Badge>
              </div>
              <CardDescription>订单号: {record.id}</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="mt-2 space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">购买日期:</span>
                  <span className="text-sm">{record.date}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">支付方式:</span>
                  <span className="text-sm">{record.paymentMethod}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">金额:</span>
                  <div className="text-right">
                    <span className="text-sm font-medium">{record.amount}</span>
                    {record.discount && (
                      <div className="text-xs text-muted-foreground">{record.discount}</div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        
        {filteredRecords.length === 0 && (
          <div className="col-span-full flex h-32 items-center justify-center rounded-lg border border-dashed">
            <p className="text-muted-foreground">没有找到匹配的销售记录</p>
          </div>
        )}
      </div>
    )
  }
  
  return (
    <div className={className}>
      <div className="mb-4 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索会员或订单号"
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-[130px]">
              <Filter className="mr-2 h-4 w-4" />
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="已完成">已完成</SelectItem>
              <SelectItem value="处理中">处理中</SelectItem>
              <SelectItem value="已取消">已取消</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={filterPayment} onValueChange={setFilterPayment}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="支付方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部方式</SelectItem>
              <SelectItem value="微信支付">微信支付</SelectItem>
              <SelectItem value="支付宝">支付宝</SelectItem>
              <SelectItem value="银行卡">银行卡</SelectItem>
              <SelectItem value="现金">现金</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center gap-2">
          <Tabs value={currentView} onValueChange={setCurrentView} className="w-[180px]">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="list">列表</TabsTrigger>
              <TabsTrigger value="card">卡片</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <Button variant="outline" size="icon">
            <Calendar className="h-4 w-4" />
          </Button>
          
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="mt-4">
        {currentView === "list" ? renderListView() : renderCardView()}
      </div>
      
      <div className="mt-4 flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          显示 {filteredRecords.length} 条记录，共 {salesRecords.length} 条
        </div>
        
        <div className="flex items-center gap-1">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button variant="outline" size="sm" className="bg-primary text-primary-foreground">
            1
          </Button>
          <Button variant="outline" size="sm" disabled>
            下一页
          </Button>
        </div>
      </div>
    </div>
  )
}
