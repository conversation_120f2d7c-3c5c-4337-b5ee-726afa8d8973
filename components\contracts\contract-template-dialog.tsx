"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import {
  FileText,
  Download,
  Calendar,
  Clock,
  Copy,
  FileSignature,
  Eye
} from "lucide-react"
import { ContractTemplatePreview } from "./contract-template-preview"

interface ContractTemplateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  template: any
}

export function ContractTemplateDialog({ open, onOpenChange, template }: ContractTemplateDialogProps) {
  // 预览模板
  const handlePreview = () => {
    // 根据模板名称构建URL
    let templateFileName = '';

    switch (template.name) {
      case "标准会员协议":
        templateFileName = "standard-member-agreement.html";
        break;
      case "会员卡标准协议":
        templateFileName = "membership-card-agreement.html";
        break;
      case "标准教练合同":
        templateFileName = "coach-employment-contract.html";
        break;
      case "场地租赁标准协议":
        templateFileName = "venue-rental-agreement.html";
        break;
      case "设备采购标准协议":
        templateFileName = "equipment-purchase-agreement.html";
        break;
      case "消费型股东协议":
        templateFileName = "consumer-shareholder-agreement.html";
        break;
      case "投资型股东协议":
        templateFileName = "investor-shareholder-agreement.html";
        break;
      case "资源型股东协议":
        templateFileName = "resource-shareholder-agreement.html";
        break;
      case "员工型股东协议":
        templateFileName = "employee-shareholder-agreement.html";
        break;
      case "联盟型股东协议":
        templateFileName = "alliance-shareholder-agreement.html";
        break;
      default:
        // 如果没有匹配的模板，尝试使用原来的方法
        templateFileName = `${template.name.toLowerCase().replace(/\s+/g, '-')}.html`;
    }

    const templateUrl = `/contract-templates/${templateFileName}`;
    window.open(templateUrl, '_blank');
  }

  // 下载模板
  const handleDownload = () => {
    // 根据模板名称构建URL
    let templateFileName = '';

    switch (template.name) {
      case "标准会员协议":
        templateFileName = "standard-member-agreement.html";
        break;
      case "会员卡标准协议":
        templateFileName = "membership-card-agreement.html";
        break;
      case "标准教练合同":
        templateFileName = "coach-employment-contract.html";
        break;
      case "场地租赁标准协议":
        templateFileName = "venue-rental-agreement.html";
        break;
      case "设备采购标准协议":
        templateFileName = "equipment-purchase-agreement.html";
        break;
      case "消费型股东协议":
        templateFileName = "consumer-shareholder-agreement.html";
        break;
      case "投资型股东协议":
        templateFileName = "investor-shareholder-agreement.html";
        break;
      case "资源型股东协议":
        templateFileName = "resource-shareholder-agreement.html";
        break;
      case "员工型股东协议":
        templateFileName = "employee-shareholder-agreement.html";
        break;
      case "联盟型股东协议":
        templateFileName = "alliance-shareholder-agreement.html";
        break;
      default:
        // 如果没有匹配的模板，尝试使用原来的方法
        templateFileName = `${template.name.toLowerCase().replace(/\s+/g, '-')}.html`;
    }

    const templateUrl = `/contract-templates/${templateFileName}`;

    // 创建一个临时链接元素
    const link = document.createElement('a')
    link.href = templateUrl
    link.download = `${template.name}.html`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "开始下载",
      description: "模板文件下载已开始",
    })
  }

  // 复制模板
  const handleCopy = () => {
    toast({
      title: "复制成功",
      description: "模板已复制，可以在模板列表中查看",
    })
    onOpenChange(false)
  }

  // 使用模板创建合同
  const handleCreateContract = () => {
    toast({
      title: "创建合同",
      description: "正在使用此模板创建新合同",
    })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            {template.name}
          </DialogTitle>
          <DialogDescription>
            模板ID: {template.id}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">模板信息</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">模板名称:</span>
                  <span className="font-medium">{template.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">模板类型:</span>
                  <span className="font-medium">{template.category}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">创建时间:</span>
                  <span className="font-medium">{template.createdAt}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">更新时间:</span>
                  <span className="font-medium">{template.updatedAt}</span>
                </div>
              </div>
            </div>

            <div className="flex-1 border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">模板描述</h3>
              <p className="text-muted-foreground">{template.description}</p>
              <div className="mt-4 pt-4 border-t">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">创建于 {template.createdAt}</span>
                </div>
                <div className="flex items-center space-x-2 mt-1">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">更新于 {template.updatedAt}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-medium mb-4">模板预览</h3>
            <ContractTemplatePreview
              previewUrl={(() => {
                // 根据模板名称构建URL
                let templateFileName = '';

                switch (template.name) {
                  case "标准会员协议":
                    templateFileName = "standard-member-agreement.html";
                    break;
                  case "会员卡标准协议":
                    templateFileName = "membership-card-agreement.html";
                    break;
                  case "标准教练合同":
                    templateFileName = "coach-employment-contract.html";
                    break;
                  case "场地租赁标准协议":
                    templateFileName = "venue-rental-agreement.html";
                    break;
                  case "设备采购标准协议":
                    templateFileName = "equipment-purchase-agreement.html";
                    break;
                  case "消费型股东协议":
                    templateFileName = "consumer-shareholder-agreement.html";
                    break;
                  case "投资型股东协议":
                    templateFileName = "investor-shareholder-agreement.html";
                    break;
                  case "资源型股东协议":
                    templateFileName = "resource-shareholder-agreement.html";
                    break;
                  case "员工型股东协议":
                    templateFileName = "employee-shareholder-agreement.html";
                    break;
                  case "联盟型股东协议":
                    templateFileName = "alliance-shareholder-agreement.html";
                    break;
                  default:
                    // 如果没有匹配的模板，尝试使用原来的方法
                    templateFileName = `${template.name.toLowerCase().replace(/\s+/g, '-')}.html`;
                }

                return `/contract-templates/${templateFileName}`;
              })()}
              templateName={template.name}
              onPreview={handlePreview}
            />
          </div>

          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-medium mb-4">变量字段</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {/* 通用变量字段 */}
              <div className="border rounded p-2">
                <Badge variant="outline" className="mb-1">合同编号</Badge>
                <p className="text-sm text-muted-foreground">&#123;&#123;contractNumber&#125;&#125;</p>
              </div>
              <div className="border rounded p-2">
                <Badge variant="outline" className="mb-1">签署日期</Badge>
                <p className="text-sm text-muted-foreground">合同的签署日期</p>
              </div>

              {/* 会员合同特有变量 */}
              {template.category === "会员合同" && (
                <>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">会员姓名</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;memberName&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">会员身份证号</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;memberIdNumber&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">会员电话</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;memberPhone&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">会员类型</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;membershipType&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">有效期</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;startDate&#125;&#125; 至 &#123;&#123;endDate&#125;&#125;</p>
                  </div>
                </>
              )}

              {/* 教练合同特有变量 */}
              {template.category === "教练合同" && (
                <>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">教练姓名</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;coachName&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">教练身份证号</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;coachIdNumber&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">教练电话</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;coachPhone&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">岗位</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;coachPosition&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">基本工资</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;baseSalary&#125;&#125;</p>
                  </div>
                </>
              )}

              {/* 股东合同特有变量 */}
              {template.category === "股东合同" && (
                <>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">股东姓名</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;shareholderName&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">股东身份证号</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;shareholderIdNumber&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">股东电话</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;shareholderPhone&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">分红比例</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;dividendRatio&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">股东期限</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;shareholderPeriod&#125;&#125;</p>
                  </div>
                </>
              )}

              {/* 场地合同特有变量 */}
              {template.category === "场地合同" && (
                <>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">出租方名称</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;lessorName&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">场地地址</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;propertyAddress&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">场地面积</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;propertyArea&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">月租金</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;monthlyRent&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">租赁期限</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;rentalPeriod&#125;&#125;</p>
                  </div>
                </>
              )}

              {/* 采购合同特有变量 */}
              {template.category === "采购合同" && (
                <>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">卖方名称</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;sellerName&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">设备名称</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;item1Name&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">设备数量</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;item1Quantity&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">设备单价</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;item1UnitPrice&#125;&#125;</p>
                  </div>
                  <div className="border rounded p-2">
                    <Badge variant="outline" className="mb-1">合计金额</Badge>
                    <p className="text-sm text-muted-foreground">&#123;&#123;totalAmount&#125;&#125;</p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleDownload}>
              <Download className="mr-2 h-4 w-4" />
              下载模板
            </Button>
            <Button variant="outline" onClick={handleCopy}>
              <Copy className="mr-2 h-4 w-4" />
              复制模板
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              关闭
            </Button>
            <Button onClick={handleCreateContract}>
              <FileSignature className="mr-2 h-4 w-4" />
              使用此模板
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
