"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, Filter, Download, Calendar, Plus } from "lucide-react"
import { GenderDistributionChart } from "./gender-distribution-chart"
import { AgeDistributionChart } from "./age-distribution-chart"

interface CardMembersProps {
  cardId: number | string
  className?: string
}

export function CardMembers({ cardId, className }: CardMembersProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [currentView, setCurrentView] = useState("list")
  
  // 模拟会员卡持有者数据
  const cardHolders = [
    {
      id: "M001",
      name: "张三",
      avatar: "/placeholder.svg?height=32&width=32",
      purchaseDate: "2023-01-15",
      expiryDate: "2024-01-14",
      remaining: "无限次",
      status: "active",
      gender: "男",
      age: 32,
      phone: "138****1234",
      visits: 45,
    },
    {
      id: "M002",
      name: "李四",
      avatar: "/placeholder.svg?height=32&width=32",
      purchaseDate: "2023-02-20",
      expiryDate: "2024-02-19",
      remaining: "无限次",
      status: "active",
      gender: "男",
      age: 28,
      phone: "139****5678",
      visits: 32,
    },
    {
      id: "M003",
      name: "王五",
      avatar: "/placeholder.svg?height=32&width=32",
      purchaseDate: "2023-03-10",
      expiryDate: "2024-03-09",
      remaining: "无限次",
      status: "active",
      gender: "男",
      age: 35,
      phone: "137****9012",
      visits: 28,
    },
    {
      id: "M004",
      name: "赵六",
      avatar: "/placeholder.svg?height=32&width=32",
      purchaseDate: "2023-04-05",
      expiryDate: "2024-04-04",
      remaining: "无限次",
      status: "active",
      gender: "女",
      age: 26,
      phone: "136****3456",
      visits: 52,
    },
    {
      id: "M005",
      name: "钱七",
      avatar: "/placeholder.svg?height=32&width=32",
      purchaseDate: "2023-05-12",
      expiryDate: "2024-05-11",
      remaining: "无限次",
      status: "active",
      gender: "女",
      age: 30,
      phone: "135****7890",
      visits: 38,
    },
  ]
  
  // 过滤和搜索会员
  const filteredMembers = cardHolders.filter(member => {
    // 搜索条件
    const matchesSearch = 
      searchTerm === "" || 
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.id.toLowerCase().includes(searchTerm.toLowerCase())
    
    // 状态过滤
    const matchesStatus = filterStatus === "all" || member.status === filterStatus
    
    return matchesSearch && matchesStatus
  })
  
  // 渲染列表视图
  const renderListView = () => {
    return (
      <div className="space-y-4">
        {filteredMembers.map((member) => (
          <div key={member.id} className="flex items-center justify-between rounded-lg border p-3">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={member.avatar} alt={member.name} />
                <AvatarFallback>{member.name[0]}</AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{member.name}</p>
                <p className="text-sm text-muted-foreground">ID: {member.id}</p>
              </div>
            </div>
            <div className="hidden md:block">
              <p className="text-sm">购买日期: {member.purchaseDate}</p>
              <p className="text-sm">到期日期: {member.expiryDate}</p>
            </div>
            <div className="hidden md:block">
              <p className="text-sm">剩余: {member.remaining}</p>
              <Badge variant={member.status === "active" ? "default" : "secondary"} className="mt-1">
                {member.status === "active" ? "有效" : "已过期"}
              </Badge>
            </div>
            <Button variant="ghost" size="sm">
              查看详情
            </Button>
          </div>
        ))}
        
        {filteredMembers.length === 0 && (
          <div className="flex h-32 items-center justify-center rounded-lg border border-dashed">
            <p className="text-muted-foreground">没有找到匹配的会员</p>
          </div>
        )}
      </div>
    )
  }
  
  // 渲染卡片视图
  const renderCardView = () => {
    return (
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {filteredMembers.map((member) => (
          <Card key={member.id} className="overflow-hidden">
            <CardHeader className="p-4 pb-2">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={member.avatar} alt={member.name} />
                  <AvatarFallback>{member.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-base">{member.name}</CardTitle>
                  <CardDescription>ID: {member.id}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="mt-2 space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">购买日期:</span>
                  <span className="text-sm">{member.purchaseDate}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">到期日期:</span>
                  <span className="text-sm">{member.expiryDate}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">剩余次数:</span>
                  <span className="text-sm">{member.remaining}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">状态:</span>
                  <Badge variant={member.status === "active" ? "default" : "secondary"}>
                    {member.status === "active" ? "有效" : "已过期"}
                  </Badge>
                </div>
              </div>
              <Button variant="ghost" size="sm" className="mt-3 w-full">
                查看详情
              </Button>
            </CardContent>
          </Card>
        ))}
        
        {filteredMembers.length === 0 && (
          <div className="col-span-full flex h-32 items-center justify-center rounded-lg border border-dashed">
            <p className="text-muted-foreground">没有找到匹配的会员</p>
          </div>
        )}
      </div>
    )
  }
  
  return (
    <div className={className}>
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-xl font-semibold">持卡会员</h2>
        <p className="text-sm text-muted-foreground">当前持有此会员卡的会员列表</p>
      </div>
      
      <div className="mb-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索会员或ID"
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-[130px]">
              <Filter className="mr-2 h-4 w-4" />
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="active">有效</SelectItem>
              <SelectItem value="expired">已过期</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center gap-2">
          <Tabs value={currentView} onValueChange={setCurrentView} className="w-[180px]">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="list">列表</TabsTrigger>
              <TabsTrigger value="card">卡片</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
          
          <Button size="sm">
            <Plus className="mr-1 h-4 w-4" />
            添加会员
          </Button>
        </div>
      </div>
      
      <div className="mb-6">
        {currentView === "list" ? renderListView() : renderCardView()}
      </div>
      
      <div className="mb-6 flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          显示 {filteredMembers.length} 位会员，共 {cardHolders.length} 位
        </div>
        
        <div className="flex items-center gap-1">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button variant="outline" size="sm" className="bg-primary text-primary-foreground">
            1
          </Button>
          <Button variant="outline" size="sm" disabled>
            下一页
          </Button>
        </div>
      </div>
      
      <h2 className="mb-4 text-xl font-semibold">会员统计</h2>
      
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <GenderDistributionChart cardId={cardId} />
        <AgeDistributionChart cardId={cardId} />
      </div>
    </div>
  )
}
