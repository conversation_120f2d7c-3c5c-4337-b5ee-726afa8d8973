"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  ArrowLeft, 
  Plus, 
  Calendar,
  Clock,
  CheckCircle2,
  Filter,
  Download,
  MoreHorizontal,
  UserPlus
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"

// 模拟会员到访数据
const visitRecords = [
  { 
    id: "v1", 
    memberId: "m1",
    memberName: "张三", 
    memberAvatar: "/avatars/01.png", 
    phone: "13800138001",
    visitTime: "2023-05-16 09:15:22",
    purpose: "上课",
    course: "空中瑜伽",
    status: "已签到",
    notes: "准时到达"
  },
  { 
    id: "v2", 
    memberId: "m2",
    memberName: "李四", 
    memberAvatar: "/avatars/02.png", 
    phone: "13800138002",
    visitTime: "2023-05-16 10:30:45",
    purpose: "咨询",
    course: null,
    status: "已接待",
    notes: "咨询会员卡续费事宜"
  },
  { 
    id: "v3", 
    memberId: "m3",
    memberName: "王五", 
    memberAvatar: "/avatars/03.png", 
    phone: "13800138003",
    visitTime: "2023-05-16 14:05:12",
    purpose: "上课",
    course: "哈他瑜伽",
    status: "已签到",
    notes: ""
  },
  { 
    id: "v4", 
    memberId: "m4",
    memberName: "赵六", 
    memberAvatar: "/avatars/04.png", 
    phone: "13800138004",
    visitTime: "2023-05-16 15:45:30",
    purpose: "购物",
    course: null,
    status: "已接待",
    notes: "购买瑜伽垫"
  },
  { 
    id: "v5", 
    memberId: "m5",
    memberName: "孙七", 
    memberAvatar: "/avatars/05.png", 
    phone: "13800138005",
    visitTime: "2023-05-16 16:20:18",
    purpose: "上课",
    course: "普拉提",
    status: "已签到",
    notes: "迟到10分钟"
  }
]

// 到访目的选项
const visitPurposes = [
  "上课",
  "咨询",
  "购物",
  "办卡",
  "续卡",
  "其他"
]

export default function VisitsPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [showAddVisitDialog, setShowAddVisitDialog] = useState(false)
  const [newVisit, setNewVisit] = useState({
    memberName: "",
    phone: "",
    purpose: "上课",
    course: "",
    notes: ""
  })

  // 过滤到访记录
  const filteredVisits = visitRecords.filter(record => {
    // 基本搜索过滤
    const searchFilter = 
      searchQuery === "" || 
      record.memberName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.phone.includes(searchQuery);
    
    // 标签过滤
    const tabFilter = 
      activeTab === "all" || 
      (activeTab === "class" && record.purpose === "上课") ||
      (activeTab === "consultation" && record.purpose === "咨询") ||
      (activeTab === "shopping" && record.purpose === "购物") ||
      (activeTab === "other" && !["上课", "咨询", "购物"].includes(record.purpose));
    
    return searchFilter && tabFilter;
  });

  // 处理添加到访记录
  const handleAddVisit = () => {
    // 在实际应用中，这里会调用API添加到访记录
    toast.success("到访记录添加成功！");
    setShowAddVisitDialog(false);
    
    // 重置表单
    setNewVisit({
      memberName: "",
      phone: "",
      purpose: "上课",
      course: "",
      notes: ""
    });
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/reception">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">会员到访记录</h1>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>到访记录管理</CardTitle>
              <CardDescription>
                记录和管理会员到访情况
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={() => setShowAddVisitDialog(true)}>
                <UserPlus className="h-4 w-4 mr-2" />
                记录到访
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col md:flex-row md:items-center gap-4 justify-between">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full max-w-md">
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="class">上课</TabsTrigger>
                <TabsTrigger value="consultation">咨询</TabsTrigger>
                <TabsTrigger value="other">其他</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索会员姓名或手机号..."
                  className="pl-8 w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>会员信息</TableHead>
                <TableHead>到访时间</TableHead>
                <TableHead>到访目的</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>备注</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVisits.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    没有找到符合条件的到访记录
                  </TableCell>
                </TableRow>
              ) : (
                filteredVisits.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={record.memberAvatar} alt={record.memberName} />
                          <AvatarFallback>{record.memberName[0]}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{record.memberName}</div>
                          <div className="text-xs text-muted-foreground">{record.phone}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{record.visitTime.split(' ')[0]}</div>
                      <div className="text-xs text-muted-foreground">{record.visitTime.split(' ')[1]}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {record.purpose}
                      </Badge>
                      {record.course && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {record.course}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant={record.status === "已签到" ? "success" : "default"}>
                        {record.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-[200px] truncate text-sm">
                        {record.notes || "-"}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>查看详情</DropdownMenuItem>
                          <DropdownMenuItem>编辑记录</DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">删除记录</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-4">
          <div className="text-sm text-muted-foreground">
            共 {filteredVisits.length} 条记录
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 添加到访记录对话框 */}
      <Dialog open={showAddVisitDialog} onOpenChange={setShowAddVisitDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>记录会员到访</DialogTitle>
            <DialogDescription>
              添加新的会员到访记录
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="member-name">会员姓名</Label>
                <Input
                  id="member-name"
                  value={newVisit.memberName}
                  onChange={(e) => setNewVisit({...newVisit, memberName: e.target.value})}
                  placeholder="输入会员姓名"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="member-phone">手机号码</Label>
                <Input
                  id="member-phone"
                  value={newVisit.phone}
                  onChange={(e) => setNewVisit({...newVisit, phone: e.target.value})}
                  placeholder="输入手机号码"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="visit-purpose">到访目的</Label>
              <select
                id="visit-purpose"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newVisit.purpose}
                onChange={(e) => setNewVisit({...newVisit, purpose: e.target.value})}
              >
                {visitPurposes.map((purpose) => (
                  <option key={purpose} value={purpose}>{purpose}</option>
                ))}
              </select>
            </div>
            
            {newVisit.purpose === "上课" && (
              <div className="space-y-2">
                <Label htmlFor="course-name">课程名称</Label>
                <Input
                  id="course-name"
                  value={newVisit.course}
                  onChange={(e) => setNewVisit({...newVisit, course: e.target.value})}
                  placeholder="输入课程名称"
                />
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="visit-notes">备注</Label>
              <textarea
                id="visit-notes"
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newVisit.notes}
                onChange={(e) => setNewVisit({...newVisit, notes: e.target.value})}
                placeholder="添加备注信息"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddVisitDialog(false)}>取消</Button>
            <Button onClick={handleAddVisit}>保存记录</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
