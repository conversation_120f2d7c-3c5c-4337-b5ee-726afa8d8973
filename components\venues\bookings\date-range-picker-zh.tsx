"use client"

import * as React from "react"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { CalendarIcon } from "lucide-react"
import type { DateRange } from "react-day-picker"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CalendarZh } from "@/components/ui/calendar-zh"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

export function DatePickerWithRangeZh({ className }: React.HTMLAttributes<HTMLDivElement>) {
  const [date, setDate] = React.useState<DateRange | undefined>({
    from: new Date(),
    to: new Date(new Date().setDate(new Date().getDate() + 7)),
  })

  // 使用状态来存储窗口宽度
  const [numberOfMonths, setNumberOfMonths] = React.useState(2)

  // 在客户端渲染时检测窗口宽度
  React.useEffect(() => {
    const handleResize = () => {
      setNumberOfMonths(window.innerWidth > 768 ? 2 : 1)
    }
    
    // 初始化
    handleResize()
    
    // 添加事件监听器
    window.addEventListener('resize', handleResize)
    
    // 清理
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn("w-[300px] justify-start text-left font-normal", !date && "text-muted-foreground")}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "yyyy年MM月dd日", { locale: zhCN })} 至 {format(date.to, "yyyy年MM月dd日", { locale: zhCN })}
                </>
              ) : (
                format(date.from, "yyyy年MM月dd日", { locale: zhCN })
              )
            ) : (
              <span>选择日期范围</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 shadow-lg border-gray-200" align="start">
          <CalendarZh
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={setDate}
            numberOfMonths={numberOfMonths}
            className="rounded-md border shadow-md"
          />
          <div className="p-3 border-t border-border">
            <div className="flex items-center justify-between mb-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDate(undefined)}
              >
                清除
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  const today = new Date()
                  setDate({
                    from: today,
                    to: today
                  })
                }}
              >
                今天
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  const today = new Date()
                  const nextWeek = new Date(today)
                  nextWeek.setDate(today.getDate() + 7)
                  setDate({
                    from: today,
                    to: nextWeek
                  })
                }}
              >
                未来7天
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  const today = new Date()
                  const nextMonth = new Date(today)
                  nextMonth.setDate(today.getDate() + 30)
                  setDate({
                    from: today,
                    to: nextMonth
                  })
                }}
              >
                未来30天
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
