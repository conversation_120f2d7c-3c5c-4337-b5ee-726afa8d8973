import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { parseFromDatabase } from '@/lib/timezone';

// GET - 获取租户信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    
    if (!tenantId) {
      return NextResponse.json({
        success: false,
        error: '未提供租户ID'
      }, { status: 400 });
    }

    console.log('获取租户信息，租户ID:', tenantId);

    const tenant = await prisma.tenant.findUnique({
      where: {
        id: parseInt(tenantId)
      }
    });

    if (!tenant) {
      return NextResponse.json({
        success: false,
        error: '未找到租户信息'
      }, { status: 404 });
    }

    console.log('查询到租户信息:', tenant.tenant_name);

    // 转换数据格式
    const formattedTenant = {
      id: tenant.id,
      name: tenant.tenant_name,
      contactPerson: tenant.contact_person || '',
      phone: tenant.phone || '',
      email: tenant.email || '',
      address: tenant.address || '',
      city: tenant.city || '',
      province: tenant.province || '',
      district: tenant.district || '',
      businessLicense: tenant.business_license || '',
      logoUrl: tenant.logo_url || '',
      validStart: tenant.valid_start ? tenant.valid_start.toISOString() : '',
      validEnd: tenant.valid_end ? tenant.valid_end.toISOString() : '',
      status: tenant.status === 1 ? 'active' : tenant.status === 0 ? 'pending' : 'inactive',
      createdAt: tenant.created_at ? tenant.created_at.toISOString() : '',
      updatedAt: tenant.updated_at ? tenant.updated_at.toISOString() : ''
    };

    return NextResponse.json({
      success: true,
      data: formattedTenant
    });

  } catch (error: any) {
    console.error('获取租户信息失败:', error);
    return NextResponse.json({
      success: false,
      error: `获取租户信息失败: ${error.message || '服务器错误'}`
    }, { status: 500 });
  }
}

// PUT - 更新租户信息
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('更新租户信息请求数据:', body);

    const {
      tenantId,
      name,
      contactPerson,
      phone,
      email,
      address,
      city,
      province,
      district,
      businessLicense
    } = body;

    if (!tenantId) {
      return NextResponse.json({
        success: false,
        error: '未提供租户ID'
      }, { status: 400 });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return NextResponse.json({
        success: false,
        error: '请输入有效的手机号码'
      }, { status: 400 });
    }

    // 验证邮箱格式（如果提供）
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return NextResponse.json({
          success: false,
          error: '请输入有效的邮箱地址'
        }, { status: 400 });
      }
    }

    // 检查租户是否存在
    const existingTenant = await prisma.tenant.findUnique({
      where: { id: parseInt(tenantId) }
    });

    if (!existingTenant) {
      return NextResponse.json({
        success: false,
        error: '租户不存在'
      }, { status: 404 });
    }

    // 更新租户信息
    const updatedTenant = await prisma.tenant.update({
      where: { id: parseInt(tenantId) },
      data: {
        tenant_name: name,
        contact_person: contactPerson,
        phone,
        email,
        address,
        city,
        province,
        district,
        business_license: businessLicense,
      }
    });

    console.log('租户信息更新成功:', updatedTenant.id);

    // 返回格式化的租户数据
    const formattedTenant = {
      id: updatedTenant.id,
      name: updatedTenant.tenant_name,
      contactPerson: updatedTenant.contact_person || '',
      phone: updatedTenant.phone || '',
      email: updatedTenant.email || '',
      address: updatedTenant.address || '',
      city: updatedTenant.city || '',
      province: updatedTenant.province || '',
      district: updatedTenant.district || '',
      businessLicense: updatedTenant.business_license || '',
      logoUrl: updatedTenant.logo_url || '',
      validStart: updatedTenant.valid_start ? updatedTenant.valid_start.toISOString() : '',
      validEnd: updatedTenant.valid_end ? updatedTenant.valid_end.toISOString() : '',
      status: updatedTenant.status === 1 ? 'active' : updatedTenant.status === 0 ? 'pending' : 'inactive',
      createdAt: updatedTenant.created_at ? updatedTenant.created_at.toISOString() : '',
      updatedAt: updatedTenant.updated_at ? updatedTenant.updated_at.toISOString() : ''
    };

    return NextResponse.json({
      success: true,
      data: formattedTenant,
      message: '租户信息已成功更新'
    });

  } catch (error: any) {
    console.error('更新租户信息失败:', error);
    
    // 检查是否是重复键错误
    if (error.code === 'P2002') {
      return NextResponse.json({
        success: false,
        error: '手机号或邮箱已被其他租户使用'
      }, { status: 400 });
    }
    
    return NextResponse.json({
      success: false,
      error: `更新租户信息失败: ${error.message || '服务器错误'}`
    }, { status: 500 });
  }
}
