"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Save,
  Refresh<PERSON><PERSON>,
  <PERSON>,
  Setting<PERSON>,
  Check
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// 同步项目接口
interface SyncItem {
  id: string | number;
  name: string;
  type: string; // 'course', 'card', 'staff'
  status: 'active' | 'inactive';
  isAutoSync: boolean; // 是否自动同步
  lastSyncTime: string | null;
  storeCount: number; // 已同步门店数量
}

// 门店接口
interface Store {
  id: string | number;
  name: string;
  area: string;
  type: string;
  status: 'active' | 'inactive';
}

export default function ChainSyncPage() {
  const [activeTab, setActiveTab] = useState("courses")
  const [syncItems, setSyncItems] = useState<SyncItem[]>([
    {
      id: 1,
      name: "瑜伽基础课程",
      type: "course",
      status: "active",
      isAutoSync: true,
      lastSyncTime: "2023-10-15 14:30:25",
      storeCount: 12
    },
    {
      id: 2,
      name: "高级瑜伽课程",
      type: "course",
      status: "active",
      isAutoSync: false,
      lastSyncTime: "2023-10-10 09:15:40",
      storeCount: 8
    },
    {
      id: 3,
      name: "流瑜伽课程",
      type: "course",
      status: "active",
      isAutoSync: true,
      lastSyncTime: "2023-10-12 16:45:10",
      storeCount: 15
    },
    {
      id: 4,
      name: "月卡会员",
      type: "card",
      status: "active",
      isAutoSync: true,
      lastSyncTime: "2023-10-14 11:20:35",
      storeCount: 18
    },
    {
      id: 5,
      name: "季卡会员",
      type: "card",
      status: "active",
      isAutoSync: true,
      lastSyncTime: "2023-10-14 11:22:15",
      storeCount: 18
    },
    {
      id: 6,
      name: "年卡会员",
      type: "card",
      status: "active",
      isAutoSync: true,
      lastSyncTime: "2023-10-14 11:25:40",
      storeCount: 18
    },
    {
      id: 7,
      name: "李教练",
      type: "staff",
      status: "active",
      isAutoSync: false,
      lastSyncTime: "2023-10-08 14:30:25",
      storeCount: 5
    },
    {
      id: 8,
      name: "王教练",
      type: "staff",
      status: "active",
      isAutoSync: true,
      lastSyncTime: "2023-10-13 16:40:15",
      storeCount: 10
    },
    {
      id: 9,
      name: "张经理",
      type: "staff",
      status: "active",
      isAutoSync: true,
      lastSyncTime: "2023-10-15 09:10:45",
      storeCount: 14
    }
  ])
  
  // 门店列表
  const [stores, setStores] = useState<Store[]>([
    { id: 1, name: "静心瑜伽旗舰店", area: "华北区", type: "flagship", status: "active" },
    { id: 2, name: "静心瑜伽国贸店", area: "华北区", type: "standard", status: "active" },
    { id: 3, name: "静心瑜伽望京店", area: "华北区", type: "standard", status: "active" },
    { id: 4, name: "静心瑜伽上海旗舰店", area: "华东区", type: "flagship", status: "active" },
    { id: 5, name: "静心瑜伽南京东路店", area: "华东区", type: "standard", status: "active" },
    { id: 6, name: "静心瑜伽杭州店", area: "华东区", type: "standard", status: "active" },
    { id: 7, name: "静心瑜伽广州店", area: "华南区", type: "standard", status: "active" },
    { id: 8, name: "静心瑜伽深圳店", area: "华南区", type: "standard", status: "active" }
  ])

  const [selectedItem, setSelectedItem] = useState<SyncItem | null>(null)
  const [isConfigOpen, setIsConfigOpen] = useState(false)
  const [selectedStores, setSelectedStores] = useState<(string | number)[]>([])
  const [globalSyncSettings, setGlobalSyncSettings] = useState({
    autoSyncNewItems: true,
    syncFrequency: "daily",
    notifyChanges: true,
    syncOnStatusChange: true
  })

  // 过滤当前标签相关的同步项目
  const filteredSyncItems = syncItems.filter(item => {
    if (activeTab === "courses") return item.type === "course"
    if (activeTab === "cards") return item.type === "card"
    if (activeTab === "staff") return item.type === "staff"
    return true
  })

  // 处理自动同步切换
  const handleAutoSyncToggle = (id: string | number) => {
    setSyncItems(syncItems.map(item => 
      item.id === id ? { ...item, isAutoSync: !item.isAutoSync } : item
    ))
    
    // 显示提示
    const item = syncItems.find(item => item.id === id)
    if (item) {
      const newStatus = !item.isAutoSync
      toast({
        title: `${item.name} 自动同步已${newStatus ? '开启' : '关闭'}`,
        description: newStatus 
          ? "该项目将自动同步到所有关联门店" 
          : "该项目将不再自动同步，需手动同步",
      })
    }
  }

  // 处理立即同步
  const handleSyncNow = (id: string | number) => {
    // 模拟同步过程
    const now = new Date().toISOString().replace('T', ' ').substring(0, 19)
    
    setSyncItems(syncItems.map(item => 
      item.id === id ? { ...item, lastSyncTime: now } : item
    ))
    
    const item = syncItems.find(item => item.id === id)
    if (item) {
      toast({
        title: `${item.name} 同步完成`,
        description: `已成功同步到 ${item.storeCount} 家门店`,
      })
    }
  }

  // 打开配置对话框
  const openConfigDialog = (item: SyncItem) => {
    setSelectedItem(item)
    // 假设这些是已选择的门店
    setSelectedStores([1, 2, 4, 5])
    setIsConfigOpen(true)
  }

  // 保存同步配置
  const saveConfig = () => {
    if (selectedItem) {
      toast({
        title: "同步配置已保存",
        description: `${selectedItem.name} 的同步设置已更新`,
      })
    }
    setIsConfigOpen(false)
  }

  // 保存全局设置
  const saveGlobalSettings = () => {
    toast({
      title: "全局同步设置已保存",
      description: "所有全局同步配置已更新",
    })
  }

  return (
    <div className="mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">连锁同步设置</h1>
          <p className="text-muted-foreground">管理连锁门店间数据同步设置</p>
        </div>
        <Button onClick={saveGlobalSettings}>
          <Save className="mr-2 h-4 w-4" />
          保存设置
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>全局同步设置</CardTitle>
          <CardDescription>设置连锁同步的全局规则</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="flex items-center justify-between border p-4 rounded-lg">
              <div className="space-y-0.5">
                <Label htmlFor="auto-sync">新项目自动同步</Label>
                <p className="text-sm text-muted-foreground">新创建的项目是否自动应用到所有门店</p>
              </div>
              <Switch 
                id="auto-sync" 
                checked={globalSyncSettings.autoSyncNewItems}
                onCheckedChange={(checked) => setGlobalSyncSettings({...globalSyncSettings, autoSyncNewItems: checked})}
              />
            </div>
            
            <div className="flex items-center justify-between border p-4 rounded-lg">
              <div className="space-y-0.5">
                <Label htmlFor="sync-frequency">同步频率</Label>
                <p className="text-sm text-muted-foreground">定期同步数据的频率</p>
              </div>
              <Select 
                value={globalSyncSettings.syncFrequency}
                onValueChange={(value) => setGlobalSyncSettings({...globalSyncSettings, syncFrequency: value})}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择同步频率" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="realtime">实时同步</SelectItem>
                  <SelectItem value="hourly">每小时</SelectItem>
                  <SelectItem value="daily">每天</SelectItem>
                  <SelectItem value="weekly">每周</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center justify-between border p-4 rounded-lg">
              <div className="space-y-0.5">
                <Label htmlFor="notify-changes">变更通知</Label>
                <p className="text-sm text-muted-foreground">连锁数据变更时是否通知管理员</p>
              </div>
              <Switch 
                id="notify-changes" 
                checked={globalSyncSettings.notifyChanges}
                onCheckedChange={(checked) => setGlobalSyncSettings({...globalSyncSettings, notifyChanges: checked})}
              />
            </div>
            
            <div className="flex items-center justify-between border p-4 rounded-lg">
              <div className="space-y-0.5">
                <Label htmlFor="sync-status-change">状态变更时同步</Label>
                <p className="text-sm text-muted-foreground">项目状态变更时自动同步到所有门店</p>
              </div>
              <Switch 
                id="sync-status-change" 
                checked={globalSyncSettings.syncOnStatusChange}
                onCheckedChange={(checked) => setGlobalSyncSettings({...globalSyncSettings, syncOnStatusChange: checked})}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="courses" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="courses">课程类型同步</TabsTrigger>
          <TabsTrigger value="cards">会员卡同步</TabsTrigger>
          <TabsTrigger value="staff">员工同步</TabsTrigger>
        </TabsList>
        
        <div className="mt-6">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>
                {activeTab === "courses" && "课程类型同步设置"}
                {activeTab === "cards" && "会员卡同步设置"}
                {activeTab === "staff" && "员工同步设置"}
              </CardTitle>
              <CardDescription>
                {activeTab === "courses" && "管理课程类型在连锁门店间的同步"}
                {activeTab === "cards" && "管理会员卡在连锁门店间的同步"}
                {activeTab === "staff" && "管理员工信息在连锁门店间的同步"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>名称</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>自动同步</TableHead>
                    <TableHead>最后同步时间</TableHead>
                    <TableHead>同步门店数</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSyncItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>
                        <Badge variant={item.status === "active" ? "default" : "secondary"}>
                          {item.status === "active" ? "启用" : "停用"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Switch 
                          checked={item.isAutoSync} 
                          onCheckedChange={() => handleAutoSyncToggle(item.id)}
                        />
                      </TableCell>
                      <TableCell>{item.lastSyncTime || "从未同步"}</TableCell>
                      <TableCell>{item.storeCount} 家门店</TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm" className="mr-2" onClick={() => openConfigDialog(item)}>
                          <Settings className="h-4 w-4 mr-1" />
                          配置
                        </Button>
                        <Button variant="default" size="sm" onClick={() => handleSyncNow(item.id)}>
                          <RefreshCw className="h-4 w-4 mr-1" />
                          立即同步
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </Tabs>

      {/* 同步配置对话框 */}
      <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>同步配置 - {selectedItem?.name}</DialogTitle>
            <DialogDescription>
              选择需要同步的门店和同步规则
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4 space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="sync-all">同步到所有门店</Label>
              <Switch id="sync-all" />
            </div>
            
            <div>
              <Label>选择同步门店</Label>
              <div className="border rounded-md mt-2">
                <div className="p-2 border-b">
                  <Input placeholder="搜索门店..." className="h-8" />
                </div>
                <ScrollArea className="h-[200px]">
                  <div className="p-2 space-y-2">
                    {stores.map((store) => (
                      <div key={store.id} className="flex items-center space-x-2">
                        <Checkbox 
                          id={`store-${store.id}`} 
                          checked={selectedStores.includes(store.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedStores([...selectedStores, store.id])
                            } else {
                              setSelectedStores(selectedStores.filter(id => id !== store.id))
                            }
                          }}
                        />
                        <Label htmlFor={`store-${store.id}`} className="flex-1 flex items-center justify-between">
                          <span>{store.name}</span>
                          <Badge variant="outline">{store.area}</Badge>
                        </Label>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
                <div className="p-2 border-t bg-muted/50 flex justify-between items-center">
                  <span className="text-sm">已选择 {selectedStores.length} 家门店</span>
                  <Button variant="ghost" size="sm" onClick={() => setSelectedStores([])}>清除</Button>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsConfigOpen(false)}>取消</Button>
            <Button onClick={saveConfig}>保存配置</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 