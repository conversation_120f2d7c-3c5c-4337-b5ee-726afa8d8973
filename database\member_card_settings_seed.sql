-- 会员卡高级设置种子数据

-- 1. 会员卡高级设置数据（卡相关设置）
INSERT INTO member_card_advanced_settings (
  card_type_id, tenant_id, vacation_option, auto_activate_days, auto_activate_enabled,
  max_people_per_class, daily_booking_limit, weekly_booking_limit, weekly_calculation_type,
  monthly_booking_limit, monthly_calculation_type, advance_booking_days, advance_booking_unlimited,
  custom_time_enabled, available_time_slots, time_restriction_enabled
) VALUES
-- 年卡设置
(1, 2, 'unlimited', 120, TRUE, 1, 3, 4, 'natural', 5, 'natural', NULL, TRUE, FALSE, NULL, FALSE),
-- 半年卡设置
(2, 2, 'unlimited', 90, TRUE, 1, 2, 3, 'natural', 4, 'natural', 7, FALSE, TRUE, 
 JSON_OBJECT(
   'monday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '22:00')),
   'tuesday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '22:00')),
   'wednesday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '22:00')),
   'thursday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '22:00')),
   'friday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '22:00')),
   'saturday', JSON_ARRAY(JSON_OBJECT('start', '08:00', 'end', '20:00')),
   'sunday', JSON_ARRAY(JSON_OBJECT('start', '08:00', 'end', '20:00'))
 ), TRUE),
-- 季卡设置
(3, 2, 'limited', 60, TRUE, 1, 2, 2, 'natural', 3, 'natural', 3, FALSE, FALSE, NULL, FALSE),
-- 月卡设置
(4, 2, 'limited', 30, TRUE, 1, 1, 2, 'natural', 2, 'natural', 1, FALSE, FALSE, NULL, FALSE),
-- 次卡设置
(5, 2, 'none', 180, TRUE, 1, 1, 1, 'natural', 1, 'natural', 0, FALSE, FALSE, NULL, FALSE),
-- 储值卡设置
(6, 2, 'unlimited', 365, TRUE, 1, 5, 10, 'natural', 20, 'natural', NULL, TRUE, FALSE, NULL, FALSE),
-- 体验卡设置
(7, 2, 'none', 7, TRUE, 1, 1, 1, 'natural', 1, 'natural', 0, FALSE, FALSE, NULL, FALSE),
-- VIP年卡设置
(8, 2, 'unlimited', 180, TRUE, 2, 5, 8, 'natural', 15, 'natural', NULL, TRUE, TRUE,
 JSON_OBJECT(
   'monday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '23:00')),
   'tuesday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '23:00')),
   'wednesday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '23:00')),
   'thursday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '23:00')),
   'friday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '23:00')),
   'saturday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '23:00')),
   'sunday', JSON_ARRAY(JSON_OBJECT('start', '06:00', 'end', '23:00'))
 ), TRUE);

-- 2. 会员卡用卡人设置数据
INSERT INTO member_card_user_settings (
  card_type_id, tenant_id, booking_interval_enabled, booking_interval_minutes,
  pending_booking_limit, cancel_limit_enabled, cancel_limit_count, cancel_limit_period,
  same_course_daily_limit, peak_time_enabled, peak_start_time, peak_end_time, peak_daily_limit,
  priority_enabled, priority_hours, priority_description
) VALUES
-- 年卡用户设置
(1, 2, FALSE, 0, 0, FALSE, 0, 'week', 1, FALSE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),
-- 半年卡用户设置
(2, 2, TRUE, 60, 2, TRUE, 3, 'week', 1, TRUE, '18:00:00', '21:00:00', 1, TRUE, 24, '本周尚未参加此类课程的会员优先预约'),
-- 季卡用户设置
(3, 2, TRUE, 120, 1, TRUE, 2, 'week', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),
-- 月卡用户设置
(4, 2, TRUE, 180, 1, TRUE, 1, 'week', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),
-- 次卡用户设置
(5, 2, TRUE, 240, 1, TRUE, 1, 'day', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),
-- 储值卡用户设置
(6, 2, FALSE, 0, 0, FALSE, 0, 'week', 2, FALSE, '18:00:00', '21:00:00', 2, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),
-- 体验卡用户设置
(7, 2, TRUE, 1440, 1, TRUE, 0, 'day', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),
-- VIP年卡用户设置
(8, 2, FALSE, 0, 0, FALSE, 0, 'week', 2, FALSE, '18:00:00', '21:00:00', 3, TRUE, 48, 'VIP会员享有48小时优先预约权');

-- 3. 会员卡课程设置数据
INSERT INTO member_card_course_settings (
  card_type_id, tenant_id, consumption_rule, consumption_description,
  gift_class_count, gift_value_coefficient, all_courses_enabled
) VALUES
-- 年卡课程设置
(1, 2, 'average', '会员卡总价值将平均分配到每次课程消耗中', 0, 1.0, TRUE),
-- 半年卡课程设置
(2, 2, 'average', '会员卡总价值将平均分配到每次课程消耗中', 0, 1.0, TRUE),
-- 季卡课程设置
(3, 2, 'average', '会员卡总价值将平均分配到每次课程消耗中', 0, 1.0, TRUE),
-- 月卡课程设置
(4, 2, 'average', '会员卡总价值将平均分配到每次课程消耗中', 0, 1.0, TRUE),
-- 次卡课程设置
(5, 2, 'fixed', '每次课程固定消耗1次', 0, 1.0, FALSE),
-- 储值卡课程设置
(6, 2, 'custom', '根据课程类型自定义消耗金额', 0, 1.0, TRUE),
-- 体验卡课程设置
(7, 2, 'fixed', '体验卡仅限指定课程', 1, 1.0, FALSE),
-- VIP年卡课程设置
(8, 2, 'average', 'VIP会员卡总价值将平均分配到每次课程消耗中', 5, 1.2, TRUE);

-- 4. 会员卡课程关联明细数据
INSERT INTO member_card_course_associations (
  card_type_id, course_type_id, tenant_id, is_enabled, consumption_times,
  course_type_name, course_duration
) VALUES
-- 年卡关联所有课程（1次消耗）
(1, 1, 2, TRUE, 1.0, '空中瑜伽初级', 60),
(1, 2, 2, TRUE, 1.0, '空中瑜伽中级', 60),
(1, 3, 2, TRUE, 1.0, '空中瑜伽高级', 60),
(1, 4, 2, TRUE, 1.0, '高温吊环', 60),
(1, 5, 2, TRUE, 1.0, '哈他瑜伽', 60),

-- 半年卡关联所有课程（1次消耗）
(2, 1, 2, TRUE, 1.0, '空中瑜伽初级', 60),
(2, 2, 2, TRUE, 1.0, '空中瑜伽中级', 60),
(2, 3, 2, TRUE, 1.0, '空中瑜伽高级', 60),
(2, 4, 2, TRUE, 1.0, '高温吊环', 60),
(2, 5, 2, TRUE, 1.0, '哈他瑜伽', 60),

-- 季卡关联基础课程
(3, 1, 2, TRUE, 1.0, '空中瑜伽初级', 60),
(3, 2, 2, TRUE, 1.0, '空中瑜伽中级', 60),
(3, 5, 2, TRUE, 1.0, '哈他瑜伽', 60),

-- 月卡关联基础课程
(4, 1, 2, TRUE, 1.0, '空中瑜伽初级', 60),
(4, 5, 2, TRUE, 1.0, '哈他瑜伽', 60),

-- 次卡关联指定课程（1次消耗）
(5, 1, 2, TRUE, 1.0, '空中瑜伽初级', 60),
(5, 5, 2, TRUE, 1.0, '哈他瑜伽', 60),

-- 储值卡关联所有课程（不同消耗金额）
(6, 1, 2, TRUE, 1.0, '空中瑜伽初级', 60),
(6, 2, 2, TRUE, 1.5, '空中瑜伽中级', 60),
(6, 3, 2, TRUE, 2.0, '空中瑜伽高级', 60),
(6, 4, 2, TRUE, 2.0, '高温吊环', 60),
(6, 5, 2, TRUE, 1.0, '哈他瑜伽', 60),

-- 体验卡仅限初级课程
(7, 1, 2, TRUE, 1.0, '空中瑜伽初级', 60),

-- VIP年卡关联所有课程（优惠消耗）
(8, 1, 2, TRUE, 0.8, '空中瑜伽初级', 60),
(8, 2, 2, TRUE, 0.8, '空中瑜伽中级', 60),
(8, 3, 2, TRUE, 0.8, '空中瑜伽高级', 60),
(8, 4, 2, TRUE, 0.8, '高温吊环', 60),
(8, 5, 2, TRUE, 0.8, '哈他瑜伽', 60);

-- 5. 会员卡销售设置数据
INSERT INTO member_card_sales_settings (
  card_type_id, tenant_id, enable_discount, discount_percentage,
  enable_promotion, promotion_type, promotion_description, price_description,
  max_sales_total, max_sales_daily, max_per_user, sale_start_date, sale_end_date
) VALUES
-- 年卡销售设置
(1, 2, TRUE, 15.00, TRUE, 'new', '新会员首次购买年卡享受85折优惠', '年卡365天不限次数，适合长期练习', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- 半年卡销售设置
(2, 2, TRUE, 10.00, TRUE, 'renewal', '老会员续费半年卡享受9折优惠', '半年卡180天不限次数，性价比之选', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- 季卡销售设置
(3, 2, FALSE, NULL, TRUE, 'group', '3人团购季卡每人立减200元', '季卡90天不限次数，短期体验首选', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- 月卡销售设置
(4, 2, FALSE, NULL, FALSE, NULL, NULL, '月卡30天不限次数，灵活选择', NULL, NULL, 2, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- 次卡销售设置
(5, 2, FALSE, NULL, FALSE, NULL, NULL, '次卡10次课程，随时使用', NULL, NULL, 3, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- 储值卡销售设置
(6, 2, TRUE, 5.00, TRUE, 'new', '储值满1000元赠送100元', '储值卡按次扣费，灵活便捷', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- 体验卡销售设置
(7, 2, FALSE, NULL, TRUE, 'new', '新会员专享体验价', '体验卡1次课程，新会员专享', 100, 10, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- VIP年卡销售设置
(8, 2, TRUE, 20.00, TRUE, 'new', 'VIP年卡享受8折优惠及专属服务', 'VIP年卡365天不限次数，享受专属服务', 50, 2, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59');
