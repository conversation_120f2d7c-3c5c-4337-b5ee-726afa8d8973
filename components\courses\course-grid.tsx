"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>oot<PERSON>, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoreHorizontal, Pencil, Eye, Calendar, Users, BarChart3, Clock, TrendingUp, Copy, Trash2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

// 使用与 CourseTable 相同的模拟数据生成函数
const generateCourses = () => {
  const courseTypes = [
    { id: "group", name: "团课", color: "#4285F4" },
    { id: "small", name: "小班课", color: "#34A853" },
    { id: "premium", name: "精品课", color: "#FBBC05" },
    { id: "private", name: "私教课", color: "#EA4335" },
    { id: "training", name: "教培课", color: "#9C27B0" },
  ]

  const coaches = [
    { id: "1", name: "张教练", avatar: "/placeholder.svg?height=32&width=32" },
    { id: "2", name: "李教练", avatar: "/placeholder.svg?height=32&width=32" },
    { id: "3", name: "王教练", avatar: "/placeholder.svg?height=32&width=32" },
    { id: "4", name: "赵教练", avatar: "/placeholder.svg?height=32&width=32" },
    { id: "5", name: "刘教练", avatar: "/placeholder.svg?height=32&width=32" },
  ]

  const venues = [
    { id: "1", name: "1号瑜伽室", capacity: 15 },
    { id: "2", name: "2号瑜伽室", capacity: 10 },
    { id: "3", name: "3号瑜伽室", capacity: 15 },
    { id: "4", name: "4号瑜伽室", capacity: 8 },
    { id: "5", name: "私教室", capacity: 2 },
  ]

  const statuses = ["active", "inactive", "upcoming", "ended", "cancelled"]
  const statusNames = {
    active: "进行中",
    inactive: "未开始",
    upcoming: "即将开始",
    ended: "已结束",
    cancelled: "已取消",
  }

  const days = ["周一、周三、周五", "周二、周四、周六", "周一、周五", "周三、周六", "周二、周日"]
  const times = ["10:00-11:30", "14:00-15:30", "16:00-17:00", "18:30-20:00", "09:00-10:30"]
  const prices = ["¥80/次", "¥120/次", "¥100/次", "¥150/次", "¥200/次"]

  // 生成25个课程
  return Array.from({ length: 25 }).map((_, index) => {
    const courseType = courseTypes[index % courseTypes.length]
    const coach = coaches[index % coaches.length]
    const venue = venues[index % venues.length]
    const status = statuses[index % statuses.length]
    const day = days[index % days.length]
    const time = times[index % times.length]
    const price = prices[index % prices.length]

    return {
      id: `YG${String(index + 1).padStart(3, "0")}`,
      name: `${courseType.name}${index < 5 ? "入门" : index < 10 ? "进阶" : index < 15 ? "提高" : index < 20 ? "专业" : "大师"}课程`,
      type: courseType.id,
      typeName: courseType.name,
      typeColor: courseType.color,
      coach: coach.id,
      coachName: coach.name,
      coachAvatar: coach.avatar,
      venue: venue.id,
      venueName: venue.name,
      time: `${day} ${time}`,
      price: price,
      capacity: venue.capacity,
      status: status,
      statusName: statusNames[status as keyof typeof statusNames],
      bookings: Math.floor(Math.random() * (venue.capacity + 1)),
      description: `这是一个${courseType.name}课程，适合${index < 10 ? "初学者" : "有一定基础的学员"}。`,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
    }
  })
}

const courses = generateCourses()

interface CourseGridProps {
  courses: any[] // 课程数据数组
  loading?: boolean // 加载状态
  onEditCourse?: (course: any) => void
  onCopyCourse?: (course: any) => void // 复制课程回调
  onDeleteCourse?: (courseId: string) => void // 删除课程回调
}

export function CourseGrid({ courses = [], loading = false, onEditCourse, onCopyCourse, onDeleteCourse }: CourseGridProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(12)
  const [viewCourse, setViewCourse] = useState<any>(null)
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [courseToDelete, setCourseToDelete] = useState<any>(null)

  // 计算总页数
  const totalPages = Math.ceil(courses.length / itemsPerPage)

  // 获取当前页的数据
  const currentCourses = courses.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // 处理查看课程详情
  const handleViewCourse = (course: any) => {
    setViewCourse(course)
    setShowViewDialog(true)
  }

  // 处理复制课程
  const handleCopyCourse = (course: any) => {
    try {
      // 生成建议的课程名称（避免重名）
      let suggestedName = `${course.name} - 副本`
      let counter = 1

      // 检查是否存在同名课程，如果存在则添加数字后缀
      while (courses.some(c => c.name === suggestedName)) {
        counter++
        suggestedName = `${course.name} - 副本${counter}`
      }

      // 创建课程副本数据
      const copiedCourse = {
        ...course,
        id: `${course.id}_copy_${Date.now()}`,
        name: suggestedName,
        status: 'inactive',
        statusName: '未开始',
        createdAt: new Date().toISOString(),
        totalBookings: 0,
        activeSchedules: 0,
        totalSchedules: 0
      }

      // 调用父组件的复制回调，传入复制的课程数据以便在编辑对话框中使用
      if (onCopyCourse) {
        onCopyCourse(copiedCourse)
      }

    } catch (error) {
      console.error('复制课程失败:', error)
      toast({
        title: "复制失败",
        description: "复制课程时发生错误，请重试",
        variant: "destructive"
      })
    }
  }

  // 处理删除课程
  const handleDeleteCourse = (course: any) => {
    setCourseToDelete(course)
    setShowDeleteDialog(true)
  }

  // 确认删除课程
  const confirmDeleteCourse = () => {
    if (courseToDelete) {
      try {
        // 调用父组件的删除回调
        if (onDeleteCourse) {
          onDeleteCourse(courseToDelete.id)
        }

        // 显示成功提示
        toast({
          title: "删除成功",
          description: `课程 "${courseToDelete.name}" 已删除`,
          variant: "default"
        })

        // 关闭对话框并清理状态
        setShowDeleteDialog(false)
        setCourseToDelete(null)
      } catch (error) {
        console.error('删除课程失败:', error)
        toast({
          title: "删除失败",
          description: "删除课程时发生错误，请重试",
          variant: "destructive"
        })
      }
    }
  }

  // 处理页码变更
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // 生成页码链接
  const renderPaginationItems = () => {
    const items = []

    // 始终显示第一页
    items.push(
      <PaginationItem key="first">
        <PaginationLink onClick={() => handlePageChange(1)} isActive={currentPage === 1}>
          1
        </PaginationLink>
      </PaginationItem>,
    )

    // 如果当前页大于3，显示省略号
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis1">
          <PaginationEllipsis />
        </PaginationItem>,
      )
    }

    // 显示当前页附近的页码
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i === 1 || i === totalPages) continue // 跳过第一页和最后一页，因为它们已经单独处理

      items.push(
        <PaginationItem key={i}>
          <PaginationLink onClick={() => handlePageChange(i)} isActive={currentPage === i}>
            {i}
          </PaginationLink>
        </PaginationItem>,
      )
    }

    // 如果当前页小于总页数-2，显示省略号
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis2">
          <PaginationEllipsis />
        </PaginationItem>,
      )
    }

    // 如果总页数大于1，始终显示最后一页
    if (totalPages > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink onClick={() => handlePageChange(totalPages)} isActive={currentPage === totalPages}>
            {totalPages}
          </PaginationLink>
        </PaginationItem>,
      )
    }

    return items
  }

  return (
    <div className="space-y-4">
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary" />
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {currentCourses.length === 0 ? (
              <div className="col-span-full flex items-center justify-center h-64">
                <p className="text-muted-foreground">暂无课程数据</p>
              </div>
            ) : (
              currentCourses.map((course) => (
                <Card key={course.id} className="overflow-hidden">
                  <div className="h-3" style={{ backgroundColor: course.typeColor }} />
                  <CardHeader className="p-4 pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-lg line-clamp-1">{course.name}</h3>
                        <Badge
                          className="mt-1"
                          variant={
                            course.status === "active"
                              ? "default"
                              : course.status === "upcoming"
                                ? "outline"
                                : course.status === "ended"
                                  ? "secondary"
                                  : "destructive"
                          }
                        >
                          {course.statusName}
                        </Badge>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="-mt-1">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewCourse(course)}>
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => onEditCourse?.(course)}>
                            <Pencil className="mr-2 h-4 w-4" />
                            编辑
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleCopyCourse(course)}>
                            <Copy className="mr-2 h-4 w-4" />
                            复制
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteCourse(course)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4 pt-0 pb-2">
                    <div className="flex items-center gap-2 mb-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={course.coachAvatar} alt={course.defaultCoachName || course.coachName} />
                        <AvatarFallback>{(course.defaultCoachName || course.coachName)?.[0] || "?"}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{course.defaultCoachName || course.coachName || "未分配"}</span>
                    </div>
                    <div className="text-sm text-muted-foreground mb-1">
                      <span className="block truncate">时长: {course.duration}分钟</span>
                      <span className="block truncate">{course.defaultVenueName || course.venueName || "未设置场地"}</span>
                    </div>
                    <p className="text-sm line-clamp-2 h-10 text-muted-foreground">{course.description}</p>
                  </CardContent>
                  <CardFooter className="p-4 pt-0 flex justify-between items-center">
                    <div className="font-medium">
                      {typeof course.price === 'number' ? `¥${course.price}` : course.price}
                    </div>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Users className="h-4 w-4" />
                      <span>
                        {course.totalBookings || course.bookings || 0}总预约
                      </span>
                    </div>
                  </CardFooter>
                </Card>
              ))
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              显示 {(currentPage - 1) * itemsPerPage + 1} 到 {Math.min(currentPage * itemsPerPage, courses.length)} 条，共 {courses.length} 条
            </div>

            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => currentPage > 1 ? handlePageChange(currentPage - 1) : undefined}
                    aria-disabled={currentPage === 1}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>

                {renderPaginationItems()}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => currentPage < totalPages ? handlePageChange(currentPage + 1) : undefined}
                    aria-disabled={currentPage === totalPages}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </>
      )}

      {/* 课程详情对话框 */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {viewCourse?.name}
              <Badge
                variant={
                  viewCourse?.status === "active"
                    ? "default"
                    : viewCourse?.status === "upcoming"
                      ? "outline"
                      : viewCourse?.status === "ended"
                        ? "secondary"
                        : "destructive"
                }
              >
                {viewCourse?.statusName}
              </Badge>
            </DialogTitle>
            <DialogDescription>查看课程的详细信息和统计数据</DialogDescription>
          </DialogHeader>

          {viewCourse && (
            <Tabs defaultValue="info" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="info">基本信息</TabsTrigger>
                <TabsTrigger value="schedules">排期管理</TabsTrigger>
                <TabsTrigger value="stats">数据统计</TabsTrigger>
              </TabsList>

              <TabsContent value="info" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程编号</p>
                    <p className="font-medium">{viewCourse.id}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程类型</p>
                    <Badge style={{ backgroundColor: viewCourse.typeColor, color: "white" }}>{viewCourse.typeName}</Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">默认教练</p>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={viewCourse.coachAvatar} alt={viewCourse.defaultCoachName || viewCourse.coachName} />
                        <AvatarFallback>{(viewCourse.defaultCoachName || viewCourse.coachName)?.[0] || "?"}</AvatarFallback>
                      </Avatar>
                      <span>{viewCourse.defaultCoachName || viewCourse.coachName || "未分配"}</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">默认场地</p>
                    <p>{viewCourse.defaultVenueName || viewCourse.venueName || viewCourse.venue || '未设置场地'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程时长</p>
                    <p>{viewCourse.duration || 90}分钟</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">标准价格</p>
                    <p>{typeof viewCourse.price === 'number' ? `¥${viewCourse.price}` : viewCourse.price}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">标准容量</p>
                    <p>{viewCourse.capacity}人</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程难度</p>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <span key={i} className={`text-sm ${i < (viewCourse.level || 1) ? 'text-yellow-500' : 'text-gray-300'}`}>
                          ★
                        </span>
                      ))}
                      <span className="text-sm text-muted-foreground ml-1">({viewCourse.level || 1}星)</span>
                    </div>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">课程描述</p>
                  <p className="mt-1">{viewCourse.description}</p>
                </div>

                {viewCourse.tags && viewCourse.tags.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程标签</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {viewCourse.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {viewCourse.requiresEquipment && viewCourse.equipmentList && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">所需器材</p>
                    <p className="mt-1">{viewCourse.equipmentList}</p>
                  </div>
                )}

                {viewCourse.prerequisites && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">参与条件</p>
                    <p className="mt-1">{viewCourse.prerequisites}</p>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-muted-foreground">创建时间</p>
                  <p className="mt-1">{new Date(viewCourse.createdAt).toLocaleString("zh-CN")}</p>
                </div>
              </TabsContent>

              <TabsContent value="schedules" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>排期管理</CardTitle>
                    <CardDescription>查看和管理课程的排期信息</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">总排期数</p>
                          <p className="text-xl font-bold">{viewCourse.totalSchedules || 0}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-green-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">活跃排期</p>
                          <p className="text-xl font-bold">{viewCourse.activeSchedules || 0}</p>
                        </div>
                      </div>
                    </div>

                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>日期</TableHead>
                          <TableHead>时间</TableHead>
                          <TableHead>教练</TableHead>
                          <TableHead>场地</TableHead>
                          <TableHead>预约/容量</TableHead>
                          <TableHead>状态</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell>2024-01-15</TableCell>
                          <TableCell>10:00-11:30</TableCell>
                          <TableCell>张教练</TableCell>
                          <TableCell>1号瑜伽室</TableCell>
                          <TableCell>12/15</TableCell>
                          <TableCell><Badge variant="default">进行中</Badge></TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>2024-01-17</TableCell>
                          <TableCell>14:00-15:30</TableCell>
                          <TableCell>张教练</TableCell>
                          <TableCell>1号瑜伽室</TableCell>
                          <TableCell>8/15</TableCell>
                          <TableCell><Badge variant="outline">即将开始</Badge></TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>2024-01-19</TableCell>
                          <TableCell>16:00-17:30</TableCell>
                          <TableCell>张教练</TableCell>
                          <TableCell>1号瑜伽室</TableCell>
                          <TableCell>15/15</TableCell>
                          <TableCell><Badge variant="default">已满员</Badge></TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="stats" className="space-y-4 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <Users className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">总预约数</p>
                          <p className="text-2xl font-bold">{viewCourse.totalBookings || viewCourse.bookings || 0}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <BarChart3 className="h-5 w-5 text-green-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">平均出席率</p>
                          <p className="text-2xl font-bold">87%</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-5 w-5 text-orange-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">平均评分</p>
                          <p className="text-2xl font-bold">{viewCourse.averageRating?.toFixed(1) || "4.5"}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-5 w-5 text-purple-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">本月课次</p>
                          <p className="text-2xl font-bold">12</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>预约趋势</CardTitle>
                      <CardDescription>近6个月预约数据</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[200px] flex items-end gap-2">
                        {[28, 35, 42, 38, 45, 52].map((count, index) => (
                          <div key={index} className="flex flex-col items-center flex-1">
                            <div
                              className="w-full bg-primary rounded-t-sm"
                              style={{
                                height: `${(count / 52) * 150}px`,
                              }}
                            />
                            <div className="text-xs mt-2">{index + 1}月</div>
                            <div className="text-xs text-muted-foreground">{count}</div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>会员反馈</CardTitle>
                      <CardDescription>最近的评价和建议</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="border-l-4 border-green-500 pl-3">
                          <p className="text-sm">"课程内容很丰富，教练很专业！"</p>
                          <p className="text-xs text-muted-foreground">★★★★★ - 张会员</p>
                        </div>
                        <div className="border-l-4 border-blue-500 pl-3">
                          <p className="text-sm">"动作讲解很详细，适合初学者。"</p>
                          <p className="text-xs text-muted-foreground">★★★★☆ - 李会员</p>
                        </div>
                        <div className="border-l-4 border-yellow-500 pl-3">
                          <p className="text-sm">"希望能增加一些进阶动作。"</p>
                          <p className="text-xs text-muted-foreground">★★★★☆ - 王会员</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowViewDialog(false)}>
              关闭
            </Button>
            <Button
              onClick={() => {
                setShowViewDialog(false)
                onEditCourse?.(viewCourse)
              }}
            >
              <Pencil className="mr-2 h-4 w-4" />
              编辑课程
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除课程 "{courseToDelete?.name}" 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDeleteCourse}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

