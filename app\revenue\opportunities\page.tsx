"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  TrendingUp,
  BarChart,
  LineChart,
  PieChart,
  DollarSign,
  Calendar,
  ArrowUpRight,
  Lightbulb,
  MoreHorizontal,
  CheckCircle,
  Clock,
  Filter,
  ChevronRight
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Dialog, DialogContent, <PERSON>alogDes<PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/components/ui/use-toast"

export default function RevenueOpportunitiesPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [selectedOpportunity, setSelectedOpportunity] = useState<any>(null)
  const [showImplementDialog, setShowImplementDialog] = useState(false)
  const [implementationNotes, setImplementationNotes] = useState("")

  // 模拟机会数据
  const opportunities = [
    {
      id: "OPP001",
      title: "高级瑜伽课程定价优化",
      description: "高级瑜伽课程预约率高于平均水平30%，建议适当增加课程价格或数量。",
      category: "pricing",
      status: "in_progress",
      discoveryDate: "2023-06-01",
      potentialRevenue: 8500,
      actualRevenue: 0,
      implementationCost: 0,
      difficulty: "低",
      priority: "高",
      icon: <DollarSign className="h-5 w-5 text-green-500" />,
      iconBg: "bg-green-50"
    },
    {
      id: "OPP002",
      title: "周末早晨课程增加",
      description: "周末早晨8:00-10:00时段预约率达95%，存在供不应求情况，建议增加同类型课程。",
      category: "courses",
      status: "new",
      discoveryDate: "2023-06-05",
      potentialRevenue: 6200,
      actualRevenue: 0,
      implementationCost: 2000,
      difficulty: "中",
      priority: "高",
      icon: <Calendar className="h-5 w-5 text-purple-500" />,
      iconBg: "bg-purple-50"
    },
    {
      id: "OPP003",
      title: "会员卡价格层级优化",
      description: "分析显示季卡和年卡价格差异不足以激励长期购买，建议调整价格层级，增加年卡性价比。",
      category: "members",
      status: "implemented",
      discoveryDate: "2023-05-15",
      potentialRevenue: 7800,
      actualRevenue: 7800,
      implementationCost: 0,
      difficulty: "低",
      priority: "高",
      icon: <LineChart className="h-5 w-5 text-blue-500" />,
      iconBg: "bg-blue-50"
    },
    {
      id: "OPP004",
      title: "会员推荐奖励优化",
      description: "数据显示当前推荐奖励转化率低于行业平均水平，建议提高推荐奖励金额并简化流程。",
      category: "marketing",
      status: "in_progress",
      discoveryDate: "2023-05-28",
      potentialRevenue: 5300,
      actualRevenue: 0,
      implementationCost: 1500,
      difficulty: "中",
      priority: "中",
      icon: <PieChart className="h-5 w-5 text-amber-500" />,
      iconBg: "bg-amber-50"
    }
  ];

  // 处理查看详情
  const handleViewDetail = (opportunity: any) => {
    setSelectedOpportunity(opportunity);
    setShowDetailDialog(true);
  };

  // 处理实施机会
  const handleImplement = (opportunity: any) => {
    setSelectedOpportunity(opportunity);
    setImplementationNotes("");
    setShowImplementDialog(true);
  };

  // 确认实施
  const confirmImplementation = () => {
    if (!implementationNotes.trim()) {
      toast({
        title: "请填写实施计划",
        description: "实施计划不能为空",
        variant: "destructive",
      });
      return;
    }

    // 实际应用中，这里应该调用API更新机会状态
    toast({
      title: "实施计划已提交",
      description: `已开始实施 ${selectedOpportunity.title}`,
    });
    setShowImplementDialog(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">收入增长机会</h1>
          <p className="text-muted-foreground">
            基于数据分析的收入增长机会和建议
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <BarChart className="h-4 w-4 mr-2" />
            查看完整报告
          </Button>
          <Button>
            <Lightbulb className="h-4 w-4 mr-2" />
            创建自定义分析
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>收入增长机会概览</CardTitle>
          <CardDescription>
            基于数据分析发现的潜在收入增长点
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">潜在增长</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥25,800</div>
                <p className="text-xs text-muted-foreground">
                  预计可增加的月收入
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">机会数量</CardTitle>
                <Lightbulb className="h-4 w-4 text-amber-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">
                  发现的收入增长机会
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">实施中</CardTitle>
                <Clock className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">5</div>
                <p className="text-xs text-muted-foreground">
                  正在实施的增长策略
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已实现</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥12,500</div>
                <p className="text-xs text-muted-foreground">
                  已实现的月收入增长
                </p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 max-w-md">
          <TabsTrigger value="all">全部机会</TabsTrigger>
          <TabsTrigger value="pricing">定价优化</TabsTrigger>
          <TabsTrigger value="courses">课程优化</TabsTrigger>
          <TabsTrigger value="members">会员优化</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative w-full md:w-1/3">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索增长机会..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-1 gap-2">
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="机会类别" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类别</SelectItem>
              <SelectItem value="pricing">定价优化</SelectItem>
              <SelectItem value="courses">课程优化</SelectItem>
              <SelectItem value="members">会员优化</SelectItem>
              <SelectItem value="marketing">营销优化</SelectItem>
              <SelectItem value="operations">运营优化</SelectItem>
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="new">新发现</SelectItem>
              <SelectItem value="in_progress">实施中</SelectItem>
              <SelectItem value="implemented">已实施</SelectItem>
              <SelectItem value="rejected">已拒绝</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="space-y-6">
            {/* 定价优化机会 */}
            <div className="rounded-lg border p-4 hover:border-primary transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-full bg-green-50">
                    <DollarSign className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <h3 className="font-medium">高级瑜伽课程定价优化</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      高级瑜伽课程预约率高于平均水平30%，建议适当增加课程价格或数量。
                    </p>
                    <div className="flex items-center gap-4 mt-3">
                      <Badge className="bg-green-100 text-green-800 hover:bg-green-100">定价优化</Badge>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">实施中</Badge>
                      <span className="text-sm text-muted-foreground flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        发现于 2023-06-01
                      </span>
                    </div>
                    <div className="flex items-center gap-2 mt-3">
                      <div className="flex items-center text-green-600">
                        <ArrowUpRight className="h-4 w-4 mr-1" />
                        <span>预计增加月收入 ¥8,500</span>
                      </div>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 课程优化机会 */}
            <div className="rounded-lg border p-4 hover:border-primary transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-full bg-purple-50">
                    <Calendar className="h-5 w-5 text-purple-500" />
                  </div>
                  <div>
                    <h3 className="font-medium">周末早晨课程增加</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      周末早晨8:00-10:00时段预约率达95%，存在供不应求情况，建议增加同类型课程。
                    </p>
                    <div className="flex items-center gap-4 mt-3">
                      <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">课程优化</Badge>
                      <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">新发现</Badge>
                      <span className="text-sm text-muted-foreground flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        发现于 2023-06-05
                      </span>
                    </div>
                    <div className="flex items-center gap-2 mt-3">
                      <div className="flex items-center text-green-600">
                        <ArrowUpRight className="h-4 w-4 mr-1" />
                        <span>预计增加月收入 ¥6,200</span>
                      </div>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 会员优化机会 */}
            <div className="rounded-lg border p-4 hover:border-primary transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-full bg-blue-50">
                    <LineChart className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <h3 className="font-medium">会员卡价格层级优化</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      分析显示季卡和年卡价格差异不足以激励长期购买，建议调整价格层级，增加年卡性价比。
                    </p>
                    <div className="flex items-center gap-4 mt-3">
                      <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">会员优化</Badge>
                      <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已实施</Badge>
                      <span className="text-sm text-muted-foreground flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        发现于 2023-05-15
                      </span>
                    </div>
                    <div className="flex items-center gap-2 mt-3">
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        <span>已增加月收入 ¥7,800</span>
                      </div>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 营销优化机会 */}
            <div className="rounded-lg border p-4 hover:border-primary transition-colors">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-full bg-amber-50">
                    <PieChart className="h-5 w-5 text-amber-500" />
                  </div>
                  <div>
                    <h3 className="font-medium">会员推荐奖励优化</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      数据显示当前推荐奖励转化率低于行业平均水平，建议提高推荐奖励金额并简化流程。
                    </p>
                    <div className="flex items-center gap-4 mt-3">
                      <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">营销优化</Badge>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">实施中</Badge>
                      <span className="text-sm text-muted-foreground flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        发现于 2023-05-28
                      </span>
                    </div>
                    <div className="flex items-center gap-2 mt-3">
                      <div className="flex items-center text-green-600">
                        <ArrowUpRight className="h-4 w-4 mr-1" />
                        <span>预计增加月收入 ¥5,300</span>
                      </div>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 机会详情对话框 */}
      <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>增长机会详情</DialogTitle>
            <DialogDescription>
              查看收入增长机会的详细信息和分析
            </DialogDescription>
          </DialogHeader>

          {selectedOpportunity && (
            <div className="space-y-6 py-4">
              <div className="flex items-start gap-4">
                <div className={`p-3 rounded-full ${selectedOpportunity.iconBg}`}>
                  {selectedOpportunity.icon}
                </div>
                <div className="flex-1">
                  <h2 className="text-xl font-semibold">{selectedOpportunity.title}</h2>
                  <p className="text-muted-foreground mt-1">{selectedOpportunity.description}</p>
                  <div className="flex flex-wrap gap-2 mt-3">
                    <Badge className={
                      selectedOpportunity.category === "pricing" ? "bg-green-100 text-green-800" :
                      selectedOpportunity.category === "courses" ? "bg-purple-100 text-purple-800" :
                      selectedOpportunity.category === "members" ? "bg-blue-100 text-blue-800" :
                      "bg-amber-100 text-amber-800"
                    }>
                      {
                        selectedOpportunity.category === "pricing" ? "定价优化" :
                        selectedOpportunity.category === "courses" ? "课程优化" :
                        selectedOpportunity.category === "members" ? "会员优化" :
                        "营销优化"
                      }
                    </Badge>
                    <Badge variant="outline" className={
                      selectedOpportunity.status === "new" ? "bg-amber-50 text-amber-700 border-amber-200" :
                      selectedOpportunity.status === "in_progress" ? "bg-blue-50 text-blue-700 border-blue-200" :
                      selectedOpportunity.status === "implemented" ? "bg-green-50 text-green-700 border-green-200" :
                      "bg-red-50 text-red-700 border-red-200"
                    }>
                      {
                        selectedOpportunity.status === "new" ? "新发现" :
                        selectedOpportunity.status === "in_progress" ? "实施中" :
                        selectedOpportunity.status === "implemented" ? "已实施" :
                        "已拒绝"
                      }
                    </Badge>
                    <Badge variant="outline">难度: {selectedOpportunity.difficulty}</Badge>
                    <Badge variant="outline">优先级: {selectedOpportunity.priority}</Badge>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">财务影响</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">潜在月收入增长</TableCell>
                          <TableCell className="text-right">¥{selectedOpportunity.potentialRevenue.toLocaleString()}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">实际月收入增长</TableCell>
                          <TableCell className="text-right">¥{selectedOpportunity.actualRevenue.toLocaleString()}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">实施成本</TableCell>
                          <TableCell className="text-right">¥{selectedOpportunity.implementationCost.toLocaleString()}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">投资回报率</TableCell>
                          <TableCell className="text-right">
                            {selectedOpportunity.implementationCost > 0
                              ? `${((selectedOpportunity.potentialRevenue * 12 / selectedOpportunity.implementationCost) * 100).toFixed(0)}%`
                              : "∞"}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">实施信息</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">发现日期</TableCell>
                          <TableCell className="text-right">{selectedOpportunity.discoveryDate}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">状态</TableCell>
                          <TableCell className="text-right">
                            {
                              selectedOpportunity.status === "new" ? "新发现" :
                              selectedOpportunity.status === "in_progress" ? "实施中" :
                              selectedOpportunity.status === "implemented" ? "已实施" :
                              "已拒绝"
                            }
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">预计实施时间</TableCell>
                          <TableCell className="text-right">
                            {selectedOpportunity.difficulty === "低" ? "1-2周" :
                             selectedOpportunity.difficulty === "中" ? "2-4周" :
                             "4-8周"}
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">负责人</TableCell>
                          <TableCell className="text-right">
                            {selectedOpportunity.status === "new" ? "未分配" : "张经理"}
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">数据分析</CardTitle>
                </CardHeader>
                <CardContent className="h-[200px] flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <BarChart className="h-16 w-16 mx-auto mb-4 text-primary/40" />
                    <p>相关数据分析图表将在此显示</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={() => setShowDetailDialog(false)}>
              关闭
            </Button>
            {selectedOpportunity && selectedOpportunity.status === "new" && (
              <Button onClick={() => {
                setShowDetailDialog(false);
                handleImplement(selectedOpportunity);
              }}>
                开始实施
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 实施计划对话框 */}
      <Dialog open={showImplementDialog} onOpenChange={setShowImplementDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>实施计划</DialogTitle>
            <DialogDescription>
              为 {selectedOpportunity?.title} 创建实施计划
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="implementation-notes" className="text-sm font-medium">
                实施计划详情
              </label>
              <textarea
                id="implementation-notes"
                rows={5}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="请描述实施计划的具体步骤、时间安排和资源需求..."
                value={implementationNotes}
                onChange={(e) => setImplementationNotes(e.target.value)}
              />
            </div>
            <div className="grid gap-2">
              <label className="text-sm font-medium">
                预计完成时间
              </label>
              <Select defaultValue="2_weeks">
                <SelectTrigger>
                  <SelectValue placeholder="选择预计完成时间" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1_week">1周内</SelectItem>
                  <SelectItem value="2_weeks">2周内</SelectItem>
                  <SelectItem value="1_month">1个月内</SelectItem>
                  <SelectItem value="3_months">3个月内</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <label className="text-sm font-medium">
                负责人
              </label>
              <Select defaultValue="manager_zhang">
                <SelectTrigger>
                  <SelectValue placeholder="选择负责人" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manager_zhang">张经理</SelectItem>
                  <SelectItem value="manager_li">李经理</SelectItem>
                  <SelectItem value="manager_wang">王经理</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowImplementDialog(false)}>
              取消
            </Button>
            <Button onClick={confirmImplementation}>
              提交计划
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
