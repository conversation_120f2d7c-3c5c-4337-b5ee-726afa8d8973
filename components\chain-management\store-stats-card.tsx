"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts"
import { FileText, BarChart4, Download } from "lucide-react"

interface StoreStatsCardProps {
  store: any
}

export function StoreStatsCard({ store }: StoreStatsCardProps) {
  // 模拟月度营收数据
  const revenueData = [
    { month: "1月", revenue: 95000 },
    { month: "2月", revenue: 88000 },
    { month: "3月", revenue: 102000 },
    { month: "4月", revenue: 99000 },
    { month: "5月", revenue: 115000 },
    { month: "6月", revenue: 120000 },
  ]

  // 模拟会员增长数据
  const memberGrowthData = [
    { month: "1月", members: 380 },
    { month: "2月", members: 400 },
    { month: "3月", members: 410 },
    { month: "4月", members: 425 },
    { month: "5月", revenue: 440 },
    { month: "6月", members: 450 },
  ]

  // 模拟课程类型分布数据
  const classTypeData = [
    { name: "团课", value: 45, color: "#4285F4" },
    { name: "小班课", value: 25, color: "#34A853" },
    { name: "精品课", value: 15, color: "#FBBC05" },
    { name: "私教课", value: 10, color: "#EA4335" },
    { name: "教培课", value: 5, color: "#9C27B0" },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart4 className="h-5 w-5 text-blue-500" />
          门店统计数据
        </CardTitle>
        <CardDescription>门店运营数据统计与分析</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h4 className="text-sm font-medium mb-2">月度营收趋势</h4>
            <ResponsiveContainer width="100%" height={150}>
              <LineChart data={revenueData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="month" fontSize={12} tickMargin={5} />
                <YAxis fontSize={12} tickMargin={5} />
                <Tooltip 
                  formatter={(value) => [`¥${value}`, "营收"]}
                  labelFormatter={(label) => `${label}`}
                />
                <Line type="monotone" dataKey="revenue" stroke="#4285F4" strokeWidth={2} dot={{ r: 3 }} />
              </LineChart>
            </ResponsiveContainer>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">会员增长趋势</h4>
            <ResponsiveContainer width="100%" height={150}>
              <BarChart data={memberGrowthData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="month" fontSize={12} tickMargin={5} />
                <YAxis fontSize={12} tickMargin={5} />
                <Tooltip 
                  formatter={(value) => [`${value} 人`, "会员数"]}
                  labelFormatter={(label) => `${label}`}
                />
                <Bar dataKey="members" fill="#34A853" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">课程类型分布</h4>
            <div className="flex items-center">
              <ResponsiveContainer width="60%" height={150}>
                <PieChart>
                  <Pie
                    data={classTypeData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={60}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {classTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value) => [`${value}%`, "占比"]}
                    labelFormatter={(label) => `${label}`}
                  />
                </PieChart>
              </ResponsiveContainer>
              <div className="space-y-2">
                {classTypeData.map((entry, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }}></div>
                    <span className="text-xs">{entry.name}: {entry.value}%</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full">
          <Download className="mr-2 h-4 w-4" />
          导出统计报表
        </Button>
      </CardFooter>
    </Card>
  )
}
