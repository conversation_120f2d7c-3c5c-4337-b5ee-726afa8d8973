"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { FileUploader } from "@/components/file-uploader"
import { 
  Building, 
  FileEdit, 
  Save, 
  History, 
  AlertCircle, 
  CheckCircle, 
  ShieldCheck 
} from "lucide-react"
import { usePremiumServices } from "@/hooks/use-premium-services"

// 企业认证信息变更表单验证模式
const certUpdateFormSchema = z.object({
  companyName: z.string().min(2, {
    message: "企业名称至少需要2个字符",
  }),
  creditCode: z.string().min(18, {
    message: "请输入正确的统一社会信用代码",
  }),
  legalRepName: z.string().min(2, {
    message: "法定代表人姓名至少需要2个字符",
  }),
  contactName: z.string().min(2, {
    message: "联系人姓名至少需要2个字符",
  }),
  contactMobile: z.string().min(11, {
    message: "请输入有效的手机号码",
  }),
  businessLicense: z.any().optional(),
  changeReason: z.string().min(5, {
    message: "请简要说明变更原因",
  }),
})

// 模拟企业认证信息
const mockCertInfo = {
  companyName: "静心瑜伽馆",
  creditCode: "91310000XXXXXXXX3B",
  legalRepName: "张三",
  contactName: "李四",
  contactMobile: "***********",
  businessLicenseUrl: "/images/business-license-sample.jpg",
  certStatus: "VERIFIED", // VERIFIED, PENDING, REJECTED
  lastUpdateTime: "2023-05-10 14:30:25",
}

// 模拟变更历史
const mockUpdateHistory = [
  {
    id: "update-1",
    updateTime: "2023-05-10 14:30:25",
    updateType: "INITIAL",
    updateContent: "初始认证",
    status: "VERIFIED",
    verifyTime: "2023-05-10 15:45:10",
    verifyResult: "通过",
    verifyComment: "资料齐全，认证通过",
  },
  {
    id: "update-2",
    updateTime: "2023-04-15 09:20:15",
    updateType: "UPDATE",
    updateContent: "变更法定代表人",
    status: "VERIFIED",
    verifyTime: "2023-04-15 11:30:05",
    verifyResult: "通过",
    verifyComment: "法定代表人变更材料齐全，认证通过",
  },
]

interface CertUpdateProps {
  className?: string
}

export function CertUpdate({ className }: CertUpdateProps) {
  const { config, loading } = usePremiumServices()
  const [certInfo, setCertInfo] = useState(mockCertInfo)
  const [updateHistory, setUpdateHistory] = useState(mockUpdateHistory)
  const [isEditing, setIsEditing] = useState(false)
  const [businessLicenseFile, setBusinessLicenseFile] = useState<File | null>(null)
  
  // 检查是否启用了认证信息变更功能
  const isFeatureEnabled = !loading && config?.eContract?.enabled && config?.eContract?.features?.certUpdate
  
  // 初始化表单
  const form = useForm<z.infer<typeof certUpdateFormSchema>>({
    resolver: zodResolver(certUpdateFormSchema),
    defaultValues: {
      companyName: certInfo.companyName,
      creditCode: certInfo.creditCode,
      legalRepName: certInfo.legalRepName,
      contactName: certInfo.contactName,
      contactMobile: certInfo.contactMobile,
      changeReason: "",
    },
  })
  
  // 处理营业执照上传
  const handleBusinessLicenseUpload = (files: File[]) => {
    if (files.length > 0) {
      setBusinessLicenseFile(files[0])
      form.setValue("businessLicense", files)
    }
  }
  
  // 处理提交变更
  const handleSubmitUpdate = (values: z.infer<typeof certUpdateFormSchema>) => {
    // 在实际应用中，这里应该调用API提交变更申请
    console.log("提交变更申请:", values)
    
    // 模拟提交成功
    const newUpdate = {
      id: `update-${Date.now()}`,
      updateTime: new Date().toLocaleString(),
      updateType: "UPDATE",
      updateContent: values.changeReason,
      status: "PENDING",
      verifyTime: "",
      verifyResult: "",
      verifyComment: "",
    }
    
    setUpdateHistory([newUpdate, ...updateHistory])
    
    // 更新认证信息状态为待审核
    setCertInfo({
      ...certInfo,
      certStatus: "PENDING",
    })
    
    // 退出编辑模式
    setIsEditing(false)
    
    // 显示成功提示
    toast({
      title: "提交成功",
      description: "认证信息变更申请已提交，请等待审核",
    })
  }
  
  // 获取认证状态标签
  const getCertStatusBadge = (status: string) => {
    switch (status) {
      case "VERIFIED":
        return (
          <div className="flex items-center text-green-600">
            <CheckCircle className="mr-1 h-4 w-4" />
            已认证
          </div>
        )
      case "PENDING":
        return (
          <div className="flex items-center text-yellow-600">
            <History className="mr-1 h-4 w-4" />
            审核中
          </div>
        )
      case "REJECTED":
        return (
          <div className="flex items-center text-red-600">
            <AlertCircle className="mr-1 h-4 w-4" />
            已驳回
          </div>
        )
      default:
        return (
          <div className="flex items-center text-gray-600">
            {status}
          </div>
        )
    }
  }
  
  // 如果功能未启用，显示提示信息
  if (!isFeatureEnabled) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>企业认证信息变更</CardTitle>
          <CardDescription>
            变更企业认证信息并提交审核
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10 space-y-4">
          <ShieldCheck className="h-12 w-12 text-muted-foreground/50" />
          <h3 className="text-lg font-semibold">功能未开通</h3>
          <p className="text-sm text-muted-foreground text-center max-w-md">
            企业认证信息变更是增值服务的高级功能，需要开通后才能使用。
            开通后，您可以随时变更企业认证信息，并提交审核。
          </p>
          <Button onClick={() => window.location.href = "/premium-services"}>
            了解详情
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Building className="mr-2 h-5 w-5" />
            企业认证信息
          </div>
          {!isEditing && (
            <Button 
              size="sm" 
              onClick={() => setIsEditing(true)}
              disabled={certInfo.certStatus === "PENDING"}
            >
              <FileEdit className="mr-2 h-4 w-4" />
              申请变更
            </Button>
          )}
        </CardTitle>
        <CardDescription>
          查看和变更企业认证信息
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmitUpdate)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="companyName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>企业名称</FormLabel>
                      <FormControl>
                        <Input placeholder="输入企业名称" {...field} />
                      </FormControl>
                      <FormDescription>
                        请输入与营业执照上一致的企业名称
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="creditCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>统一社会信用代码</FormLabel>
                      <FormControl>
                        <Input placeholder="输入统一社会信用代码" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="businessLicense"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>营业执照照片</FormLabel>
                    <FormControl>
                      <FileUploader
                        onFilesUploaded={handleBusinessLicenseUpload}
                        maxFiles={1}
                        maxSize={5 * 1024 * 1024} // 5MB
                        accept=".jpg,.jpeg,.png"
                      />
                    </FormControl>
                    <FormDescription>
                      上传最新的营业执照照片，支持JPG、PNG格式，最大5MB
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="legalRepName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>法定代表人姓名</FormLabel>
                      <FormControl>
                        <Input placeholder="输入法定代表人姓名" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="contactName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>联系人姓名</FormLabel>
                        <FormControl>
                          <Input placeholder="输入联系人姓名" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="contactMobile"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>联系人手机号码</FormLabel>
                        <FormControl>
                          <Input placeholder="输入联系人手机号码" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              <FormField
                control={form.control}
                name="changeReason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>变更原因</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请简要说明变更原因"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      请说明本次变更的原因，以便审核人员了解
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                >
                  取消
                </Button>
                <Button type="submit">
                  <Save className="mr-2 h-4 w-4" />
                  提交变更
                </Button>
              </div>
            </form>
          </Form>
        ) : (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">当前认证信息</h3>
              <div className="flex items-center">
                <span className="text-sm text-muted-foreground mr-2">认证状态:</span>
                {getCertStatusBadge(certInfo.certStatus)}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>企业名称</Label>
                <div className="p-2 border rounded-md bg-muted/50">
                  {certInfo.companyName}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>统一社会信用代码</Label>
                <div className="p-2 border rounded-md bg-muted/50">
                  {certInfo.creditCode}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>法定代表人</Label>
                <div className="p-2 border rounded-md bg-muted/50">
                  {certInfo.legalRepName}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>联系人</Label>
                <div className="p-2 border rounded-md bg-muted/50">
                  {certInfo.contactName} ({certInfo.contactMobile})
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>营业执照</Label>
              <div className="border rounded-md p-4 flex items-center justify-center h-40">
                {certInfo.businessLicenseUrl ? (
                  <img 
                    src={certInfo.businessLicenseUrl} 
                    alt="营业执照" 
                    className="max-h-full max-w-full object-contain"
                  />
                ) : (
                  <span className="text-sm text-muted-foreground">暂无营业执照照片</span>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>最近更新时间</Label>
              <div className="p-2 border rounded-md bg-muted/50">
                {certInfo.lastUpdateTime}
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-medium">变更历史</h3>
              <div className="border rounded-md divide-y">
                {updateHistory.map((update) => (
                  <div key={update.id} className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium">{update.updateContent}</h4>
                        <p className="text-sm text-muted-foreground">
                          提交时间: {update.updateTime}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="mb-1">
                          {getCertStatusBadge(update.status)}
                        </div>
                        {update.verifyTime && (
                          <p className="text-sm text-muted-foreground">
                            审核时间: {update.verifyTime}
                          </p>
                        )}
                      </div>
                    </div>
                    {update.verifyComment && (
                      <div className="text-sm bg-muted/30 p-2 rounded-md">
                        <span className="font-medium">审核意见: </span>
                        {update.verifyComment}
                      </div>
                    )}
                  </div>
                ))}
                
                {updateHistory.length === 0 && (
                  <div className="p-4 text-center text-muted-foreground">
                    暂无变更历史
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
