"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend,
  <PERSON><PERSON>hart,
  Line,
} from "recharts"

const courseBookingData = [
  { month: "1月", value: 320 },
  { month: "2月", value: 350 },
  { month: "3月", value: 380 },
  { month: "4月", value: 420 },
  { month: "5月", value: 480 },
  { month: "6月", value: 520 },
  { month: "7月", value: 550 },
  { month: "8月", value: 590 },
  { month: "9月", value: 620 },
  { month: "10月", value: 650 },
  { month: "11月", value: 680 },
  { month: "12月", value: 720 },
]

const courseTypeData = [
  { name: "基础瑜伽", value: 40, color: "#4285F4" },
  { name: "高级瑜伽", value: 25, color: "#34A853" },
  { name: "阴瑜伽", value: 15, color: "#FBBC05" },
  { name: "孕产瑜伽", value: 10, color: "#EA4335" },
  { name: "空中瑜伽", value: 10, color: "#FF6D91" },
]

const attendanceRateData = [
  { month: "1月", rate: 85 },
  { month: "2月", rate: 83 },
  { month: "3月", rate: 86 },
  { month: "4月", rate: 88 },
  { month: "5月", rate: 87 },
  { month: "6月", rate: 85 },
  { month: "7月", rate: 84 },
  { month: "8月", rate: 86 },
  { month: "9月", rate: 88 },
  { month: "10月", rate: 89 },
  { month: "11月", rate: 87 },
  { month: "12月", rate: 86 },
]

const popularTimeData = [
  { time: "6:00-8:00", value: 15 },
  { time: "8:00-10:00", value: 25 },
  { time: "10:00-12:00", value: 20 },
  { time: "12:00-14:00", value: 10 },
  { time: "14:00-16:00", value: 15 },
  { time: "16:00-18:00", value: 30 },
  { time: "18:00-20:00", value: 40 },
  { time: "20:00-22:00", value: 20 },
]

export function CourseStatistics() {
  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总课程数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">128</div>
            <p className="text-xs text-muted-foreground">较上月增长 5.8%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">月预约量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">720</div>
            <p className="text-xs text-muted-foreground">较上月增长 6.2%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均出勤率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">86%</div>
            <p className="text-xs text-muted-foreground">较上月下降 1.0%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">满员率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">65%</div>
            <p className="text-xs text-muted-foreground">较上月增长 4.5%</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>课程预约趋势</CardTitle>
            <CardDescription>过去12个月课程预约量变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={courseBookingData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="value" stroke="#4285F4" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>课程出勤率</CardTitle>
            <CardDescription>过去12个月课程出勤率变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={attendanceRateData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="month" />
                  <YAxis domain={[80, 95]} />
                  <Tooltip />
                  <Line type="monotone" dataKey="rate" stroke="#34A853" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>课程类型分布</CardTitle>
            <CardDescription>不同类型课程占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={courseTypeData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {courseTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend layout="vertical" verticalAlign="middle" align="right" />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>热门上课时段</CardTitle>
            <CardDescription>不同时段课程预约人数分布</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={popularTimeData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#4285F4" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

