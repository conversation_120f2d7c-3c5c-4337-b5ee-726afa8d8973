// 测试登录API的脚本
// 使用Node.js 18+的内置fetch API

async function testLogin() {
  try {
    console.log('🧪 开始测试登录API...');
    
    // 测试1: 平台管理员登录
    console.log('\n📤 测试平台管理员登录...');
    const adminData = {
      username: "admin",
      password: "admin123",
      userType: "admin"
    };
    
    console.log('请求数据:', JSON.stringify(adminData, null, 2));
    
    const adminResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(adminData)
    });
    
    console.log('📥 管理员登录响应状态:', adminResponse.status);
    const adminResult = await adminResponse.json();
    console.log('📥 管理员登录响应数据:', JSON.stringify(adminResult, null, 2));
    
    if (adminResult.code === 200) {
      console.log('✅ 平台管理员登录测试成功!');
    } else {
      console.log('❌ 平台管理员登录测试失败:', adminResult.message);
    }
    
    // 测试2: 租户用户登录（使用之前注册的用户）
    console.log('\n📤 测试租户用户登录...');
    const tenantData = {
      username: "13800138000", // 使用手机号登录
      password: "123456", // 默认密码
      userType: "tenant"
    };
    
    console.log('请求数据:', JSON.stringify(tenantData, null, 2));
    
    const tenantResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tenantData)
    });
    
    console.log('📥 租户登录响应状态:', tenantResponse.status);
    const tenantResult = await tenantResponse.json();
    console.log('📥 租户登录响应数据:', JSON.stringify(tenantResult, null, 2));
    
    if (tenantResult.code === 200) {
      console.log('✅ 租户用户登录测试成功!');
    } else if (tenantResult.code === 403) {
      console.log('⚠️ 租户账户需要审核:', tenantResult.message);
    } else {
      console.log('❌ 租户用户登录测试失败:', tenantResult.message);
    }
    
    // 测试3: 错误的用户名密码
    console.log('\n📤 测试错误的用户名密码...');
    const wrongData = {
      username: "wronguser",
      password: "wrongpass",
      userType: "tenant"
    };
    
    const wrongResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(wrongData)
    });
    
    const wrongResult = await wrongResponse.json();
    console.log('📥 错误登录响应:', JSON.stringify(wrongResult, null, 2));
    
    if (wrongResult.code === 401) {
      console.log('✅ 错误登录测试成功（正确返回401）!');
    } else {
      console.log('❌ 错误登录测试失败，应该返回401');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testLogin();
