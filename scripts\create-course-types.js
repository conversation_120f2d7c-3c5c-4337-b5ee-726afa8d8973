// 通过API创建课程类型数据
const courseTypes = [
  {
    tenantId: 1,
    name: '基础瑜伽',
    description: '适合初学者的基础瑜伽课程',
    color: '#4285F4'
  },
  {
    tenantId: 1,
    name: '进阶瑜伽',
    description: '适合有基础的学员',
    color: '#34A853'
  },
  {
    tenantId: 1,
    name: '阴瑜伽',
    description: '深度放松的阴瑜伽',
    color: '#FBBC05'
  },
  {
    tenantId: 1,
    name: '孕产瑜伽',
    description: '专为孕妇设计的瑜伽',
    color: '#EA4335'
  },
  {
    tenantId: 1,
    name: '空中瑜伽',
    description: '空中瑜伽练习',
    color: '#FF6D91'
  },
  {
    tenantId: 1,
    name: '私教课',
    description: '一对一私教课程',
    color: '#9C27B0'
  }
];

async function createCourseTypes() {
  console.log('开始创建课程类型数据...');
  
  for (const typeData of courseTypes) {
    try {
      const response = await fetch('http://localhost:3005/api/course-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(typeData)
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        console.log(`✓ 创建课程类型成功: ${typeData.name}`);
      } else {
        console.log(`✗ 创建课程类型失败: ${typeData.name} - ${result.msg}`);
      }
    } catch (error) {
      console.error(`✗ 创建课程类型出错: ${typeData.name}`, error.message);
    }
  }
  
  console.log('课程类型数据创建完成!');
}

createCourseTypes();
