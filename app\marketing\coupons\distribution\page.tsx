import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import {
  ArrowUpDown,
  Calendar,
  Check,
  Download,
  Filter,
  MessageSquare,
  Plus,
  QrCode,
  Search,
  Send,
  Share2,
  Users,
} from "lucide-react"

export default function CouponDistributionPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">优惠券发放</h1>
          <p className="text-muted-foreground">管理优惠券的发放记录和发放方式</p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Send className="mr-2 h-4 w-4" />
                发放优惠券
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>发放优惠券</DialogTitle>
                <DialogDescription>选择优惠券和发放对象，可批量发放给多个会员</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="coupon-select">选择优惠券</Label>
                  <Select>
                    <SelectTrigger id="coupon-select">
                      <SelectValue placeholder="选择要发放的优惠券" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">新人专享8折券</SelectItem>
                      <SelectItem value="2">会员日满200减50</SelectItem>
                      <SelectItem value="5">季度卡95折优惠</SelectItem>
                      <SelectItem value="7">会员生日专享券</SelectItem>
                      <SelectItem value="8">健身体验课赠送</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>发放方式</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2 rounded-md border p-3">
                      <Checkbox id="method-individual" />
                      <Label htmlFor="method-individual" className="flex items-center gap-2 font-normal">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        指定会员
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 rounded-md border p-3">
                      <Checkbox id="method-tag" />
                      <Label htmlFor="method-tag" className="flex items-center gap-2 font-normal">
                        <Filter className="h-4 w-4 text-muted-foreground" />
                        会员标签
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 rounded-md border p-3">
                      <Checkbox id="method-qrcode" />
                      <Label htmlFor="method-qrcode" className="flex items-center gap-2 font-normal">
                        <QrCode className="h-4 w-4 text-muted-foreground" />
                        生成二维码
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2 rounded-md border p-3">
                      <Checkbox id="method-message" />
                      <Label htmlFor="method-message" className="flex items-center gap-2 font-normal">
                        <MessageSquare className="h-4 w-4 text-muted-foreground" />
                        短信通知
                      </Label>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>选择会员</Label>
                  <div className="max-h-[200px] overflow-y-auto rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50px]">
                            <Checkbox />
                          </TableHead>
                          <TableHead>会员姓名</TableHead>
                          <TableHead>手机号码</TableHead>
                          <TableHead>会员等级</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {members.map((member) => (
                          <TableRow key={member.id}>
                            <TableCell>
                              <Checkbox />
                            </TableCell>
                            <TableCell>{member.name}</TableCell>
                            <TableCell>{member.phone}</TableCell>
                            <TableCell>{member.level}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>已选择: 0 位会员</span>
                    <Button variant="link" size="sm" className="h-auto p-0">
                      导入会员名单
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">发送消息</Label>
                  <Textarea id="message" placeholder="可选：发送优惠券时附带的消息内容" className="min-h-[80px]" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline">取消</Button>
                <Button>确认发放</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline">
            <QrCode className="mr-2 h-4 w-4" />
            批量生成二维码
          </Button>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出记录
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="搜索会员姓名、手机号或优惠券名称..." className="pl-8" />
        </div>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="状态筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="unused">未使用</SelectItem>
            <SelectItem value="used">已使用</SelectItem>
            <SelectItem value="expired">已过期</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="records">
        <TabsList>
          <TabsTrigger value="records">发放记录</TabsTrigger>
          <TabsTrigger value="statistics">发放统计</TabsTrigger>
          <TabsTrigger value="qrcodes">二维码管理</TabsTrigger>
        </TabsList>
        <TabsContent value="records" className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <Checkbox />
                    </TableHead>
                    <TableHead>优惠券名称</TableHead>
                    <TableHead>发放对象</TableHead>
                    <TableHead>发放时间</TableHead>
                    <TableHead>
                      <div className="flex items-center">
                        状态
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>发放方式</TableHead>
                    <TableHead>操作人</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {distributionRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <Checkbox />
                      </TableCell>
                      <TableCell className="font-medium">{record.couponName}</TableCell>
                      <TableCell>
                        {record.recipient}
                        {record.recipientCount > 1 && (
                          <Badge variant="outline" className="ml-2">
                            +{record.recipientCount - 1}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{record.date}</TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadge(record.status)}>{getStatusName(record.status)}</Badge>
                      </TableCell>
                      <TableCell>{record.method}</TableCell>
                      <TableCell>{record.operator}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="statistics" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总发放数量</CardTitle>
                <Send className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3,842</div>
                <p className="text-xs text-muted-foreground">
                  较上月 <span className="text-green-500">+15.3%</span>
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">使用数量</CardTitle>
                <Check className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,645</div>
                <p className="text-xs text-muted-foreground">
                  较上月 <span className="text-green-500">+10.2%</span>
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">使用率</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">42.8%</div>
                <p className="text-xs text-muted-foreground">
                  较上月 <span className="text-red-500">-2.5%</span>
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">带动消费</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥89,432</div>
                <p className="text-xs text-muted-foreground">
                  较上月 <span className="text-green-500">+23.1%</span>
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-4 grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>优惠券使用率排行</CardTitle>
                <CardDescription>按使用率排序的前10张优惠券</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>优惠券名称</TableHead>
                      <TableHead>发放数量</TableHead>
                      <TableHead>使用数量</TableHead>
                      <TableHead>使用率</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {couponStats.map((stat) => (
                      <TableRow key={stat.id}>
                        <TableCell className="font-medium">{stat.name}</TableCell>
                        <TableCell>{stat.issued}</TableCell>
                        <TableCell>{stat.used}</TableCell>
                        <TableCell>{stat.usageRate}%</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>发放方式分布</CardTitle>
                <CardDescription>不同发放方式的数量统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>指定会员</span>
                      </div>
                      <span className="font-medium">1,245 (32.4%)</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 w-[32.4%] rounded-full bg-primary"></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-muted-foreground" />
                        <span>会员标签</span>
                      </div>
                      <span className="font-medium">986 (25.7%)</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 w-[25.7%] rounded-full bg-primary"></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <QrCode className="h-4 w-4 text-muted-foreground" />
                        <span>二维码领取</span>
                      </div>
                      <span className="font-medium">1,124 (29.3%)</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 w-[29.3%] rounded-full bg-primary"></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <MessageSquare className="h-4 w-4 text-muted-foreground" />
                        <span>短信通知</span>
                      </div>
                      <span className="font-medium">487 (12.6%)</span>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 w-[12.6%] rounded-full bg-primary"></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="qrcodes" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>优惠券二维码</CardTitle>
              <CardDescription>生成和管理优惠券领取二维码</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {qrcodes.map((qrcode) => (
                  <div key={qrcode.id} className="overflow-hidden rounded-lg border">
                    <div className="flex items-center justify-center bg-muted p-6">
                      <div className="h-32 w-32 rounded bg-white p-2">
                        <QrCode className="h-full w-full text-primary" />
                      </div>
                    </div>
                    <div className="p-4">
                      <h3 className="font-medium">{qrcode.name}</h3>
                      <p className="text-sm text-muted-foreground">{qrcode.description}</p>
                      <div className="mt-2 flex items-center justify-between text-sm">
                        <span>
                          已领取: {qrcode.claimed}/{qrcode.total}
                        </span>
                        <Badge variant={qrcode.active ? "success" : "outline"}>
                          {qrcode.active ? "生效中" : "已停用"}
                        </Badge>
                      </div>
                      <div className="mt-4 flex justify-end gap-2">
                        <Button size="sm" variant="outline">
                          <Share2 className="mr-2 h-3 w-3" />
                          分享
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="mr-2 h-3 w-3" />
                          下载
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="flex h-full min-h-[240px] items-center justify-center rounded-lg border border-dashed">
                  <Button variant="outline">
                    <Plus className="mr-2 h-4 w-4" />
                    生成新二维码
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

// 模拟数据
const members = [
  { id: 1, name: "张三", phone: "138****1234", level: "金卡会员" },
  { id: 2, name: "李四", phone: "139****5678", level: "银卡会员" },
  { id: 3, name: "王五", phone: "137****9012", level: "白金会员" },
  { id: 4, name: "赵六", phone: "136****3456", level: "普通会员" },
  { id: 5, name: "钱七", phone: "135****7890", level: "金卡会员" },
]

const distributionRecords = [
  {
    id: 1,
    couponName: "新人专享8折券",
    recipient: "张三",
    recipientCount: 1,
    date: "2025-04-01 14:30",
    status: "unused",
    method: "指定会员",
    operator: "管理员",
  },
  {
    id: 2,
    couponName: "会员日满200减50",
    recipient: "金卡会员",
    recipientCount: 124,
    date: "2025-04-02 09:15",
    status: "used",
    method: "会员标签",
    operator: "系统",
  },
  {
    id: 3,
    couponName: "季度卡95折优惠",
    recipient: "李四",
    recipientCount: 1,
    date: "2025-04-03 16:45",
    status: "expired",
    method: "短信通知",
    operator: "管理员",
  },
  {
    id: 4,
    couponName: "健身体验课赠送",
    recipient: "二维码领取",
    recipientCount: 56,
    date: "2025-04-04 10:20",
    status: "unused",
    method: "二维码",
    operator: "系统",
  },
  {
    id: 5,
    couponName: "会员生日专享券",
    recipient: "王五",
    recipientCount: 1,
    date: "2025-04-05 11:30",
    status: "used",
    method: "指定会员",
    operator: "管理员",
  },
]

const couponStats = [
  { id: 1, name: "瑜伽课程体验券", issued: 300, used: 245, usageRate: 81.7 },
  { id: 2, name: "购课送瑜伽垫", issued: 87, used: 80, usageRate: 92.0 },
  { id: 3, name: "会员生日专享券", issued: 1245, used: 876, usageRate: 70.4 },
  { id: 4, name: "会员日满200减50", issued: 213, used: 87, usageRate: 40.8 },
  { id: 5, name: "新人专享8折券", issued: 328, used: 156, usageRate: 47.6 },
]

const qrcodes = [
  {
    id: 1,
    name: "新人专享8折券",
    description: "新会员注册专享优惠",
    claimed: 156,
    total: 500,
    active: true,
  },
  {
    id: 2,
    name: "健身体验课赠送",
    description: "线下活动推广使用",
    claimed: 87,
    total: 200,
    active: true,
  },
  {
    id: 3,
    name: "会员日满200减50",
    description: "会员日专属活动",
    claimed: 213,
    total: 300,
    active: false,
  },
]

// 辅助函数
function getStatusBadge(status: string) {
  switch (status) {
    case "unused":
      return "secondary"
    case "used":
      return "success"
    case "expired":
      return "outline"
    default:
      return "default"
  }
}

function getStatusName(status: string) {
  switch (status) {
    case "unused":
      return "未使用"
    case "used":
      return "已使用"
    case "expired":
      return "已过期"
    default:
      return "未知"
  }
}

