"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Info, Save, Plus, Trash } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"

interface CardBookingRulesProps {
  cardTypeId?: string;
  level?: "global" | "cardType";
  onChange?: (settings: any) => void;
  onSave?: (settings: any) => void;
}

// 表单验证模式
const formSchema = z.object({
  // 卡相关设置
  bookingOption: z.enum(["not_allowed", "unlimited", "limited"]),
  bookingLimitCount: z.coerce.number().min(0).max(100).optional(),
  bookingLimitDays: z.coerce.number().min(0).max(365).optional(),

  // 开卡设置
  activationDays: z.coerce.number().min(0).max(365),

  // 单节课预约人数上限
  maxBookingsPerCourse: z.coerce.number().min(0).max(100),

  // 预约频率限制
  dailyBookingLimit: z.string(),
  weeklyBookingLimit: z.string(),
  monthlyBookingLimit: z.string(),

  // 可预约天数
  bookingDaysLimit: z.string(),

  // 可用时间设置
  timeRangeOption: z.enum(["all_time", "custom"]),
  availableDays: z.array(z.string()),

  // 用卡人相关设置
  bookingIntervalMinutes: z.string(),
  maxPendingBookings: z.string(),
  cancellationLimitCount: z.coerce.number().min(0).max(100),
  cancellationLimitPeriod: z.enum(["day", "week", "month"]),
})

export function CardBookingRules({
  cardTypeId,
  level = "global",
  onChange,
  onSave
}: CardBookingRulesProps) {
  const [activeTab, setActiveTab] = useState("card")
  const [timeRanges, setTimeRanges] = useState([
    { start: { hour: "08", minute: "00" }, end: { hour: "22", minute: "00" } }
  ])

  // 获取默认值
  const getDefaultValues = () => {
    return {
      bookingOption: "unlimited" as const,
      bookingLimitCount: 5,
      bookingLimitDays: 30,
      activationDays: 120,
      maxBookingsPerCourse: 3,
      dailyBookingLimit: "unlimited",
      weeklyBookingLimit: "unlimited",
      monthlyBookingLimit: "unlimited",
      bookingDaysLimit: "unlimited",
      timeRangeOption: "all_time" as const,
      availableDays: ["1", "2", "3", "4", "5", "6", "0"],
      bookingIntervalMinutes: "unlimited",
      maxPendingBookings: "unlimited",
      cancellationLimitCount: 3,
      cancellationLimitPeriod: "week" as const,
    }
  }

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
  })

  // 监听表单值变化
  const watchBookingOption = form.watch("bookingOption")
  const watchTimeRangeOption = form.watch("timeRangeOption")

  // 添加时间段
  const addTimeRange = () => {
    setTimeRanges([
      ...timeRanges,
      { start: { hour: "08", minute: "00" }, end: { hour: "22", minute: "00" } }
    ])
  }

  // 删除时间段
  const removeTimeRange = (index: number) => {
    setTimeRanges(timeRanges.filter((_, i) => i !== index))
  }

  // 更新时间段
  const updateTimeRange = (index: number, field: "start" | "end", subfield: "hour" | "minute", value: string) => {
    const newTimeRanges = [...timeRanges]
    newTimeRanges[index][field][subfield] = value
    setTimeRanges(newTimeRanges)
  }

  // 保存设置
  const handleSave = (values: z.infer<typeof formSchema>) => {
    const settings = {
      ...values,
      timeRanges: watchTimeRangeOption === "custom" ? timeRanges : [],
    }

    console.log("保存会员卡预约规则设置:", settings)
    if (onSave) {
      onSave(settings)
    }
  }

  return (
    <Card className="shadow-sm border-slate-200">
      <CardHeader className="pb-3">
        <CardTitle className="text-xl font-semibold text-slate-800">
          {level === "global" ? "全局会员卡预约规则设置" : "会员卡类型预约规则设置"}
        </CardTitle>
        <CardDescription className="text-slate-500">
          {level === "global"
            ? "设置适用于所有会员卡的全局预约规则"
            : "设置适用于特定会员卡类型的预约规则，将覆盖全局设置"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 pt-2">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 w-full bg-slate-100">
            <TabsTrigger value="card" className="text-sm font-medium">卡相关设置</TabsTrigger>
            <TabsTrigger value="user" className="text-sm font-medium">用卡人相关设置</TabsTrigger>
          </TabsList>

          <TabsContent value="card" className="space-y-6 pt-4">
            <form onSubmit={form.handleSubmit(handleSave)}>
              <div className="space-y-6">
                {/* 预约选项 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">预约选项:</Label>
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </div>
                  </div>

                  <div className="pl-1 space-y-4 border-l-4 border-l-slate-100 pl-4">
                    <RadioGroup
                      value={watchBookingOption}
                      onValueChange={(value: "not_allowed" | "unlimited" | "limited") =>
                        form.setValue("bookingOption", value)
                      }
                      className="space-y-4"
                    >
                      <div className="flex items-center space-x-3">
                        <RadioGroupItem value="not_allowed" id="not-allowed" />
                        <Label htmlFor="not-allowed" className="font-normal">不允许</Label>
                      </div>

                      <div className="flex items-center space-x-3">
                        <RadioGroupItem value="unlimited" id="unlimited" />
                        <Label htmlFor="unlimited" className="font-normal">不限制</Label>
                      </div>

                      <div className="flex items-center flex-wrap space-x-3">
                        <RadioGroupItem value="limited" id="limited" />
                        <Label htmlFor="limited" className="font-normal">允许，有效期内可以预约</Label>
                        <div className="flex items-center mt-2 ml-6 space-x-2">
                          <Input
                            type="number"
                            className="w-16 h-8 text-center"
                            disabled={watchBookingOption !== "limited"}
                            value={form.getValues("bookingLimitCount")}
                            onChange={(e) => form.setValue("bookingLimitCount", parseInt(e.target.value) || 0)}
                          />
                          <span className="text-sm">次，累计</span>
                          <Input
                            type="number"
                            className="w-16 h-8 text-center"
                            disabled={watchBookingOption !== "limited"}
                            value={form.getValues("bookingLimitDays")}
                            onChange={(e) => form.setValue("bookingLimitDays", parseInt(e.target.value) || 0)}
                          />
                          <span className="text-sm">天</span>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                </div>

                <Separator />

                {/* 开卡设置 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">开卡设置:</Label>
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </div>
                  </div>

                  <div className="pl-1 border-l-4 border-l-slate-100 pl-4">
                    <div className="flex items-center space-x-2">
                      <Label className="font-normal text-sm">发卡后</Label>
                      <Input
                        type="number"
                        className="w-16 h-8 text-center"
                        value={form.getValues("activationDays")}
                        onChange={(e) => form.setValue("activationDays", parseInt(e.target.value) || 0)}
                      />
                      <span className="text-sm">天内未开卡则自动开卡</span>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 单节课预约人数上限 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">单节课预约人数上限:</Label>
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </div>
                  </div>

                  <div className="pl-1 border-l-4 border-l-slate-100 pl-4">
                    <div className="flex items-center space-x-2">
                      <Input
                        type="number"
                        className="w-16 h-8 text-center"
                        value={form.getValues("maxBookingsPerCourse")}
                        onChange={(e) => form.setValue("maxBookingsPerCourse", parseInt(e.target.value) || 0)}
                      />
                      <span className="text-sm">人</span>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 预约频率限制 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">预约频率限制:</Label>
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </div>
                  </div>

                  <div className="pl-1 border-l-4 border-l-slate-100 pl-4 space-y-4">
                    <div className="flex items-center space-x-2">
                      <Label className="w-32 text-sm font-normal">每日可约次数上限:</Label>
                      <Select
                        value={form.getValues("dailyBookingLimit")}
                        onValueChange={(value) => form.setValue("dailyBookingLimit", value)}
                      >
                        <SelectTrigger className="w-48 h-8">
                          <SelectValue placeholder="选择限制" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="unlimited">不限制预约次数</SelectItem>
                          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                            <SelectItem key={num} value={num.toString()}>{num}次</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Label className="w-32 text-sm font-normal">每周可约次数上限:</Label>
                      <Select
                        value={form.getValues("weeklyBookingLimit")}
                        onValueChange={(value) => form.setValue("weeklyBookingLimit", value)}
                      >
                        <SelectTrigger className="w-48 h-8">
                          <SelectValue placeholder="选择限制" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="unlimited">不限制预约次数</SelectItem>
                          {[5, 10, 15, 20, 25, 30].map((num) => (
                            <SelectItem key={num} value={num.toString()}>{num}次</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Label className="w-32 text-sm font-normal">每月可约次数上限:</Label>
                      <Select
                        value={form.getValues("monthlyBookingLimit")}
                        onValueChange={(value) => form.setValue("monthlyBookingLimit", value)}
                      >
                        <SelectTrigger className="w-48 h-8">
                          <SelectValue placeholder="选择限制" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="unlimited">不限制预约次数</SelectItem>
                          {[10, 20, 30, 40, 50, 60].map((num) => (
                            <SelectItem key={num} value={num.toString()}>{num}次</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 可预约天数 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">可预约天数:</Label>
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </div>
                  </div>

                  <div className="pl-1 border-l-4 border-l-slate-100 pl-4">
                    <div className="flex items-center space-x-2">
                      <Label className="text-sm font-normal">会员最多可预约</Label>
                      <Select
                        value={form.getValues("bookingDaysLimit")}
                        onValueChange={(value) => form.setValue("bookingDaysLimit", value)}
                      >
                        <SelectTrigger className="w-48 h-8">
                          <SelectValue placeholder="选择天数" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="unlimited">不限制</SelectItem>
                          <SelectItem value="0">天的课程，0表示当天</SelectItem>
                          {[1, 3, 5, 7, 14, 30, 60, 90].map((num) => (
                            <SelectItem key={num} value={num.toString()}>未来{num}天的课程</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 可用时间设置 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">可用时间:</Label>
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </div>
                  </div>

                  <div className="pl-1 border-l-4 border-l-slate-100 pl-4">
                    <RadioGroup
                      value={watchTimeRangeOption}
                      onValueChange={(value: "all_time" | "custom") =>
                        form.setValue("timeRangeOption", value)
                      }
                      className="space-y-4"
                    >
                      <div className="flex items-center space-x-3">
                        <RadioGroupItem value="all_time" id="all-time" />
                        <Label htmlFor="all-time" className="font-normal">全时段</Label>
                      </div>

                      <div className="flex items-center space-x-3">
                        <RadioGroupItem value="custom" id="custom-time" />
                        <Label htmlFor="custom-time" className="font-normal">自定义时段</Label>
                      </div>
                    </RadioGroup>

                    {watchTimeRangeOption === "custom" && (
                      <div className="space-y-4 pl-6 pt-4">
                        <div className="space-y-3">
                          <Label className="text-sm font-medium">可用时间内可预约</Label>
                          <div className="grid grid-cols-7 gap-3">
                            {[
                              { value: "1", label: "周一" },
                              { value: "2", label: "周二" },
                              { value: "3", label: "周三" },
                              { value: "4", label: "周四" },
                              { value: "5", label: "周五" },
                              { value: "6", label: "周六" },
                              { value: "0", label: "周日" },
                            ].map((day) => (
                              <div key={day.value} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`day-${day.value}`}
                                  className="h-4 w-4 rounded-sm"
                                  checked={form.getValues("availableDays").includes(day.value)}
                                  onCheckedChange={(checked) => {
                                    const currentDays = form.getValues("availableDays");
                                    if (checked) {
                                      form.setValue("availableDays", [...currentDays, day.value]);
                                    } else {
                                      form.setValue("availableDays", currentDays.filter(d => d !== day.value));
                                    }
                                  }}
                                />
                                <Label htmlFor={`day-${day.value}`} className="text-sm font-normal">{day.label}</Label>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="space-y-3">
                          <Label className="text-sm font-medium">可用时间段(最多3个)</Label>
                          {timeRanges.map((timeRange, index) => (
                            <div key={index} className="flex items-center space-x-2">
                              <Select
                                value={timeRange.start.hour}
                                onValueChange={(value) => updateTimeRange(index, "start", "hour", value)}
                              >
                                <SelectTrigger className="w-16 h-8">
                                  <SelectValue placeholder="时" />
                                </SelectTrigger>
                                <SelectContent>
                                  {Array.from({ length: 24 }, (_, i) => i).map((hour) => (
                                    <SelectItem key={hour} value={hour.toString().padStart(2, '0')}>
                                      {hour.toString().padStart(2, '0')}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <span className="text-sm">:</span>
                              <Select
                                value={timeRange.start.minute}
                                onValueChange={(value) => updateTimeRange(index, "start", "minute", value)}
                              >
                                <SelectTrigger className="w-16 h-8">
                                  <SelectValue placeholder="分" />
                                </SelectTrigger>
                                <SelectContent>
                                  {["00", "15", "30", "45"].map((minute) => (
                                    <SelectItem key={minute} value={minute}>{minute}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <span className="text-sm">至</span>
                              <Select
                                value={timeRange.end.hour}
                                onValueChange={(value) => updateTimeRange(index, "end", "hour", value)}
                              >
                                <SelectTrigger className="w-16 h-8">
                                  <SelectValue placeholder="时" />
                                </SelectTrigger>
                                <SelectContent>
                                  {Array.from({ length: 24 }, (_, i) => i).map((hour) => (
                                    <SelectItem key={hour} value={hour.toString().padStart(2, '0')}>
                                      {hour.toString().padStart(2, '0')}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <span className="text-sm">:</span>
                              <Select
                                value={timeRange.end.minute}
                                onValueChange={(value) => updateTimeRange(index, "end", "minute", value)}
                              >
                                <SelectTrigger className="w-16 h-8">
                                  <SelectValue placeholder="分" />
                                </SelectTrigger>
                                <SelectContent>
                                  {["00", "15", "30", "45"].map((minute) => (
                                    <SelectItem key={minute} value={minute}>{minute}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              {index > 0 && (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => removeTimeRange(index)}
                                  className="h-8 w-8"
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          ))}

                          {timeRanges.length < 3 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={addTimeRange}
                              className="mt-2 h-8 text-xs"
                            >
                              <Plus className="h-3.5 w-3.5 mr-1" />
                              新增可用时段
                            </Button>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <Button type="submit" className="bg-primary text-white hover:bg-primary/90">
                  <Save className="h-4 w-4 mr-2" />
                  保存设置
                </Button>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="user" className="space-y-6 pt-4">
            <form onSubmit={form.handleSubmit(handleSave)}>
              <div className="space-y-6">
                {/* 预约间隔时间限制 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">每日约课间隔时间限制:</Label>
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </div>
                  </div>

                  <div className="pl-1 border-l-4 border-l-slate-100 pl-4">
                    <div className="flex items-center space-x-2">
                      <Select
                        value={form.getValues("bookingIntervalMinutes")}
                        onValueChange={(value) => form.setValue("bookingIntervalMinutes", value)}
                      >
                        <SelectTrigger className="w-48 h-8">
                          <SelectValue placeholder="选择间隔时间" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="unlimited">不限制</SelectItem>
                          {[5, 10, 15, 30, 60, 120, 240, 360, 720].map((num) => (
                            <SelectItem key={num} value={num.toString()}>{num}分钟内不可连续约课</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 预约次数限制 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">预约次数限制:</Label>
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </div>
                  </div>

                  <div className="pl-1 border-l-4 border-l-slate-100 pl-4">
                    <div className="flex items-center space-x-2">
                      <Label className="text-sm font-normal">未结束课程的预约次数限制为</Label>
                      <Select
                        value={form.getValues("maxPendingBookings")}
                        onValueChange={(value) => form.setValue("maxPendingBookings", value)}
                      >
                        <SelectTrigger className="w-48 h-8">
                          <SelectValue placeholder="选择次数" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="unlimited">不限制</SelectItem>
                          {[0, 1, 2, 3, 4, 5, 10].map((num) => (
                            <SelectItem key={num} value={num.toString()}>{num}次，0表示不限制</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* 取消预约次数限制 */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-base font-medium">取消预约次数限制:</Label>
                    <div className="flex items-center space-x-2">
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </div>
                  </div>

                  <div className="pl-1 border-l-4 border-l-slate-100 pl-4">
                    <div className="flex items-center space-x-2">
                      <Label className="text-sm font-normal">会员取消预约次数限制为</Label>
                      <Input
                        type="number"
                        className="w-16 h-8 text-center"
                        value={form.getValues("cancellationLimitCount")}
                        onChange={(e) => form.setValue("cancellationLimitCount", parseInt(e.target.value) || 0)}
                      />
                      <span className="text-sm">次，每</span>
                      <Select
                        value={form.getValues("cancellationLimitPeriod")}
                        onValueChange={(value: "day" | "week" | "month") =>
                          form.setValue("cancellationLimitPeriod", value)
                        }
                      >
                        <SelectTrigger className="w-24 h-8">
                          <SelectValue placeholder="选择周期" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="day">天</SelectItem>
                          <SelectItem value="week">周</SelectItem>
                          <SelectItem value="month">月</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <Button type="submit" className="bg-primary text-white hover:bg-primary/90">
                  <Save className="h-4 w-4 mr-2" />
                  保存设置
                </Button>
              </div>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
