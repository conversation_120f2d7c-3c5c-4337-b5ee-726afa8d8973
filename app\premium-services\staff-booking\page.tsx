"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Calendar, ChevronRight, Search, Users, ArrowRight, X, QrCode, Filter, RefreshCw, CheckCircle2 } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePicker } from "@/components/date-picker"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "@/components/ui/use-toast"

// 模拟会员数据
const members = [
  {
    id: "M001",
    name: "张三",
    phone: "13812345678",
    avatar: "/placeholder.svg?height=40&width=40",
    membershipType: "瑜伽年卡",
    expiryDate: "2024-12-31",
    remainingClasses: 42,
    points: 320,
    status: "active",
  },
  {
    id: "M002",
    name: "李四",
    phone: "13987654321",
    avatar: "/placeholder.svg?height=40&width=40",
    membershipType: "瑜伽季卡",
    expiryDate: "2024-06-30",
    remainingClasses: 18,
    points: 150,
    status: "active",
  },
  {
    id: "M003",
    name: "王五",
    phone: "13765432198",
    avatar: "/placeholder.svg?height=40&width=40",
    membershipType: "瑜伽月卡",
    expiryDate: "2024-04-15",
    remainingClasses: 6,
    points: 80,
    status: "active",
  },
  {
    id: "M004",
    name: "赵六",
    phone: "13654321987",
    avatar: "/placeholder.svg?height=40&width=40",
    membershipType: "私教次卡",
    expiryDate: "2024-08-20",
    remainingClasses: 8,
    points: 210,
    status: "active",
  },
  {
    id: "M005",
    name: "钱七",
    phone: "13543219876",
    avatar: "/placeholder.svg?height=40&width=40",
    membershipType: "瑜伽年卡",
    expiryDate: "2024-11-10",
    remainingClasses: 35,
    points: 280,
    status: "active",
  },
]

// 模拟课程数据
const classes = [
  {
    id: "C001",
    name: "哈他瑜伽初级",
    time: "2024-04-10 10:00-11:30",
    teacher: "王教练",
    venue: "1号瑜伽室",
    capacity: 20,
    remainingSpots: 8,
    type: "group",
    status: "upcoming",
  },
  {
    id: "C002",
    name: "阴瑜伽",
    time: "2024-04-10 14:00-15:30",
    teacher: "李教练",
    venue: "2号瑜伽室",
    capacity: 15,
    remainingSpots: 5,
    type: "group",
    status: "upcoming",
  },
  {
    id: "C003",
    name: "流瑜伽中级",
    time: "2024-04-11 09:00-10:30",
    teacher: "张教练",
    venue: "1号瑜伽室",
    capacity: 18,
    remainingSpots: 3,
    type: "group",
    status: "upcoming",
  },
  {
    id: "C004",
    name: "私教课",
    time: "2024-04-11 16:00-17:00",
    teacher: "赵教练",
    venue: "私教室",
    capacity: 1,
    remainingSpots: 1,
    type: "private",
    status: "upcoming",
  },
  {
    id: "C005",
    name: "普拉提",
    time: "2024-04-12 11:00-12:00",
    teacher: "刘教练",
    venue: "3号瑜伽室",
    capacity: 12,
    remainingSpots: 6,
    type: "group",
    status: "upcoming",
  },
]

// 模拟预约记录
const bookingRecords = [
  {
    id: "B001",
    memberId: "M001",
    memberName: "张三",
    courseId: "C001",
    courseName: "哈他瑜伽初级",
    bookingTime: "2024-04-05 14:23",
    courseTime: "2024-04-10 10:00-11:30",
    status: "confirmed",
    operatorId: "S001",
    operatorName: "客服小王",
  },
  {
    id: "B002",
    memberId: "M002",
    memberName: "李四",
    courseId: "C003",
    courseName: "流瑜伽中级",
    bookingTime: "2024-04-06 09:15",
    courseTime: "2024-04-11 09:00-10:30",
    status: "confirmed",
    operatorId: "S002",
    operatorName: "客服小李",
  },
  {
    id: "B003",
    memberId: "M003",
    memberName: "王五",
    courseId: "C002",
    courseName: "阴瑜伽",
    bookingTime: "2024-04-06 16:42",
    courseTime: "2024-04-10 14:00-15:30",
    status: "cancelled",
    operatorId: "S001",
    operatorName: "客服小王",
    cancelReason: "会员临时有事",
  },
]

export default function StaffBookingPage() {
  // 状态管理
  const [activeTab, setActiveTab] = useState("booking")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMember, setSelectedMember] = useState<any>(null)
  const [showMemberSearchResults, setShowMemberSearchResults] = useState(false)
  const [memberSearchResults, setMemberSearchResults] = useState<any[]>([])
  const [showBookingDialog, setShowBookingDialog] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [filteredClasses, setFilteredClasses] = useState(classes)
  const [bookingHistory, setBookingHistory] = useState(bookingRecords)
  const [selectedCourseType, setSelectedCourseType] = useState("all")
  const [selectedClasses, setSelectedClasses] = useState<string[]>([])
  const [bookingNote, setBookingNote] = useState("")
  const [selectedMembershipCard, setSelectedMembershipCard] = useState("")
  const [showConsumptionPreview, setShowConsumptionPreview] = useState(false)
  const [consumptionPreview, setConsumptionPreview] = useState({
    totalClasses: 0,
    totalValue: 0
  })

  // 搜索会员
  const searchMembers = (query: string) => {
    if (!query.trim()) {
      setMemberSearchResults([])
      setShowMemberSearchResults(false)
      return
    }

    const results = members.filter(
      member =>
        member.name.toLowerCase().includes(query.toLowerCase()) ||
        member.phone.includes(query)
    )

    setMemberSearchResults(results)
    setShowMemberSearchResults(true)
  }

  // 处理会员搜索输入变化
  const handleMemberSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
    searchMembers(query)
  }

  // 选择会员
  const selectMember = (member: any) => {
    setSelectedMember(member)
    setShowMemberSearchResults(false)
    setSearchQuery("")
  }

  // 处理代约课
  const handleBooking = (member: any) => {
    setSelectedMember(member)
    setShowBookingDialog(true)
  }

  // 根据日期筛选课程
  useEffect(() => {
    // 实际应用中，这里应该调用API获取指定日期的课程
    // 这里仅做模拟
    if (selectedDate) {
      const dateStr = selectedDate.toISOString().split('T')[0]
      let filtered = classes.filter(c => c.time.includes(dateStr))

      if (selectedCourseType !== "all") {
        filtered = filtered.filter(c => c.type === selectedCourseType)
      }

      setFilteredClasses(filtered)
    } else {
      setFilteredClasses(classes)
    }
  }, [selectedDate, selectedCourseType])

  // 确认单个预约
  const confirmBooking = (classItem: any) => {
    if (!selectedMember) return

    // 实际应用中，这里应该调用API进行预约
    // 这里仅做模拟
    const newBooking = {
      id: `B${Math.floor(Math.random() * 10000).toString().padStart(3, '0')}`,
      memberId: selectedMember.id,
      memberName: selectedMember.name,
      courseId: classItem.id,
      courseName: classItem.name,
      bookingTime: new Date().toLocaleString(),
      courseTime: classItem.time,
      status: "confirmed",
      operatorId: "S001",
      operatorName: "当前操作员",
      note: "",
      membershipCard: selectedMember.membershipType,
    }

    setBookingHistory([newBooking, ...bookingHistory])

    // 更新剩余名额
    const updatedClasses = filteredClasses.map(c =>
      c.id === classItem.id
        ? {...c, remainingSpots: c.remainingSpots - 1}
        : c
    )
    setFilteredClasses(updatedClasses)

    toast({
      title: "预约成功",
      description: `已成功为 ${selectedMember.name} 预约 ${classItem.name}`,
    })
    setShowBookingDialog(false)
  }

  // 确认批量预约
  const confirmBatchBooking = () => {
    if (!selectedMember || selectedClasses.length === 0) return

    // 获取选中的课程
    const selectedCourseItems = filteredClasses.filter(c => selectedClasses.includes(c.id))

    // 创建预约记录
    const newBookings = selectedCourseItems.map(classItem => ({
      id: `B${Math.floor(Math.random() * 10000).toString().padStart(3, '0')}`,
      memberId: selectedMember.id,
      memberName: selectedMember.name,
      courseId: classItem.id,
      courseName: classItem.name,
      bookingTime: new Date().toLocaleString(),
      courseTime: classItem.time,
      status: "confirmed",
      operatorId: "S001",
      operatorName: "当前操作员",
      note: bookingNote,
      membershipCard: selectedMembershipCard || selectedMember.membershipType,
    }))

    // 更新预约历史
    setBookingHistory([...newBookings, ...bookingHistory])

    // 更新剩余名额
    const updatedClasses = filteredClasses.map(c => {
      if (selectedClasses.includes(c.id)) {
        return {...c, remainingSpots: c.remainingSpots - 1}
      }
      return c
    })
    setFilteredClasses(updatedClasses)

    // 重置状态
    setSelectedClasses([])
    setBookingNote("")
    setSelectedMembershipCard("")
    setShowConsumptionPreview(false)

    toast({
      title: "批量预约成功",
      description: `已成功为 ${selectedMember.name} 预约 ${selectedCourseItems.length} 节课程`,
    })
  }

  // 取消预约
  const cancelBooking = (bookingId: string) => {
    // 实际应用中，这里应该调用API取消预约
    // 这里仅做模拟
    const updatedBookings = bookingHistory.map(booking =>
      booking.id === bookingId
        ? {...booking, status: "cancelled", cancelReason: "工作人员取消"}
        : booking
    )
    setBookingHistory(updatedBookings)

    alert("预约已取消")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">工作人员代约课</h1>
          <p className="text-muted-foreground">
            帮助会员预约课程，管理预约记录
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setSelectedMember(null)}>
            <RefreshCw className="mr-2 h-4 w-4" />
            重置
          </Button>
          <Button onClick={() => setShowBookingDialog(true)} disabled={!selectedMember}>
            <Calendar className="mr-2 h-4 w-4" />
            立即约课
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center gap-2 max-w-md relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索会员姓名或手机号..."
            value={searchQuery}
            onChange={handleMemberSearchChange}
            className="pl-8"
          />
          {showMemberSearchResults && memberSearchResults.length > 0 && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-md border shadow-lg z-10 max-h-60 overflow-y-auto">
              {memberSearchResults.map(member => (
                <div
                  key={member.id}
                  className="flex items-center gap-3 p-3 hover:bg-muted cursor-pointer"
                  onClick={() => selectMember(member)}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={member.avatar} alt={member.name} />
                    <AvatarFallback>{member.name.slice(0, 1)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{member.name}</div>
                    <div className="text-sm text-muted-foreground">{member.phone}</div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {selectedMember && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>已选会员</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={selectedMember.avatar} alt={selectedMember.name} />
                  <AvatarFallback>{selectedMember.name.slice(0, 1)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium text-lg">{selectedMember.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {selectedMember.phone} | {selectedMember.membershipType}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1 flex items-center gap-2">
                    <span className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {selectedMember.expiryDate} 到期
                    </span>
                    <span>•</span>
                    <span>剩余课时: {selectedMember.remainingClasses}</span>
                  </div>
                </div>
              </div>
              <Button onClick={() => handleBooking(selectedMember)}>
                约课
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="booking">约课记录</TabsTrigger>
          <TabsTrigger value="history">历史记录</TabsTrigger>
        </TabsList>

        <TabsContent value="booking" className="mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>近期约课记录</CardTitle>
              <CardDescription>查看最近的约课记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {bookingHistory.filter(b => b.status === "confirmed").length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    暂无约课记录
                  </div>
                ) : (
                  bookingHistory
                    .filter(b => b.status === "confirmed")
                    .map(booking => (
                      <div key={booking.id} className="flex items-center justify-between border-b pb-4">
                        <div>
                          <div className="font-medium">{booking.courseName}</div>
                          <div className="text-sm text-muted-foreground">
                            {booking.courseTime} | 会员: {booking.memberName}
                          </div>
                          <div className="text-xs text-muted-foreground mt-1">
                            预约时间: {booking.bookingTime} | 操作员: {booking.operatorName}
                          </div>
                        </div>
                        <Button variant="outline" size="sm" onClick={() => cancelBooking(booking.id)}>
                          取消预约
                        </Button>
                      </div>
                    ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>历史记录</CardTitle>
              <CardDescription>查看所有约课历史记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {bookingHistory.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    暂无历史记录
                  </div>
                ) : (
                  bookingHistory.map(booking => (
                    <div key={booking.id} className="flex items-center justify-between border-b pb-4">
                      <div>
                        <div className="font-medium">{booking.courseName}</div>
                        <div className="text-sm text-muted-foreground">
                          {booking.courseTime} | 会员: {booking.memberName}
                        </div>
                        <div className="text-xs text-muted-foreground mt-1 flex items-center gap-2">
                          <span>预约时间: {booking.bookingTime}</span>
                          <span>•</span>
                          <span>操作员: {booking.operatorName}</span>
                          <Badge variant={booking.status === "confirmed" ? "default" : "destructive"}>
                            {booking.status === "confirmed" ? "已确认" : "已取消"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 代约课对话框 */}
      <Dialog open={showBookingDialog} onOpenChange={setShowBookingDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>代约课</DialogTitle>
            <DialogDescription>
              {selectedMember ? `为 ${selectedMember.name} 预约课程` : "选择会员和课程进行预约"}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {!selectedMember ? (
              <div className="text-center py-4 text-muted-foreground">
                请先选择会员
              </div>
            ) : (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <DatePicker
                      placeholder="选择日期"
                      selected={selectedDate}
                      onSelect={setSelectedDate}
                    />
                    <Select value={selectedCourseType} onValueChange={setSelectedCourseType}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="课程类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部类型</SelectItem>
                        <SelectItem value="group">团体课</SelectItem>
                        <SelectItem value="private">私教课</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedClasses.length > 0 && (
                    <Button
                      variant="default"
                      onClick={() => setShowConsumptionPreview(true)}
                    >
                      批量预约 ({selectedClasses.length})
                      <CheckCircle2 className="ml-2 h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="space-y-3">
                  {filteredClasses.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      当前日期没有可预约的课程
                    </div>
                  ) : (
                    filteredClasses.map((classItem) => (
                      <div key={classItem.id} className="border rounded-md p-4 flex items-center justify-between hover:bg-muted/30">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                            <Users className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <div className="font-medium">{classItem.name}</div>
                            <div className="text-sm text-muted-foreground">{classItem.time} | {classItem.teacher}</div>
                            <div className="text-xs text-muted-foreground">{classItem.venue}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Badge variant="outline">剩余 {classItem.remainingSpots} 位</Badge>
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id={`select-${classItem.id}`}
                              checked={selectedClasses.includes(classItem.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedClasses([...selectedClasses, classItem.id])
                                } else {
                                  setSelectedClasses(selectedClasses.filter(id => id !== classItem.id))
                                }
                              }}
                              disabled={classItem.remainingSpots <= 0}
                            />
                            <Button
                              size="sm"
                              onClick={() => confirmBooking(classItem)}
                              disabled={classItem.remainingSpots <= 0}
                            >
                              单独预约
                              <ArrowRight className="ml-2 h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBookingDialog(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 批量预约确认对话框 */}
      <Dialog open={showConsumptionPreview} onOpenChange={setShowConsumptionPreview}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>批量预约确认</DialogTitle>
            <DialogDescription>
              确认为 {selectedMember?.name} 预约 {selectedClasses.length} 节课程
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* 会员卡选择 */}
            <div className="space-y-2">
              <Label>选择会员卡</Label>
              <RadioGroup
                value={selectedMembershipCard || selectedMember?.membershipType}
                onValueChange={setSelectedMembershipCard}
                className="flex flex-col space-y-1"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value={selectedMember?.membershipType} id="default-card" />
                  <Label htmlFor="default-card" className="font-normal">
                    {selectedMember?.membershipType} (默认)
                  </Label>
                </div>
                {selectedMember?.membershipType !== "瑜伽年卡" && (
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="瑜伽年卡" id="year-card" />
                    <Label htmlFor="year-card" className="font-normal">瑜伽年卡</Label>
                  </div>
                )}
                {selectedMember?.membershipType !== "瑜伽季卡" && (
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="瑜伽季卡" id="season-card" />
                    <Label htmlFor="season-card" className="font-normal">瑜伽季卡</Label>
                  </div>
                )}
              </RadioGroup>
            </div>

            {/* 消费预览 */}
            <Card>
              <CardHeader className="py-2">
                <CardTitle className="text-sm">消费预览</CardTitle>
              </CardHeader>
              <CardContent className="py-2">
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>总课时数:</span>
                    <span className="font-medium">{selectedClasses.length} 节</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>消耗课时:</span>
                    <span className="font-medium">{selectedClasses.length} 节</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>剩余课时:</span>
                    <span className="font-medium">
                      {selectedMember ? selectedMember.remainingClasses - selectedClasses.length : 0} 节
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 预约备注 */}
            <div className="space-y-2">
              <Label htmlFor="booking-note">预约备注</Label>
              <Textarea
                id="booking-note"
                placeholder="添加预约备注..."
                value={bookingNote}
                onChange={(e) => setBookingNote(e.target.value)}
              />
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setShowConsumptionPreview(false)}>
              取消
            </Button>
            <Button onClick={confirmBatchBooking}>
              确认预约
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
