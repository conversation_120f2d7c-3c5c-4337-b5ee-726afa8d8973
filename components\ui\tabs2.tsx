"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON>, <PERSON>bsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"

const Tabs2 = React.forwardRef<React.ElementRef<typeof Tabs>, React.ComponentPropsWithoutKeyof<typeof Tabs, "dir">>(
  ({ className, children, ...props }, ref) => (
    <Tabs {...props} className={cn("relative", className)} ref={ref}>
      {children}
    </Tabs>
  ),
)
Tabs2.displayName = "Tabs2"

const Tabs2List = React.forwardRef<
  React.ElementRef<typeof TabsList>,
  React.ComponentPropsWithoutKeyof<typeof TabsList, "dir">
>(({ className, children, ...props }, ref) => (
  <TabsList
    {...props}
    className={cn(
      "inline-flex items-center justify-center rounded-md p-1 text-blue-500 data-[orientation=horizontal]:h-10 data-[orientation=vertical]:w-10",
      className,
    )}
    ref={ref}
  >
    {children}
  </TabsList>
))
Tabs2List.displayName = "Tabs2List"

const Tabs2Trigger = React.forwardRef<
  React.ElementRef<typeof TabsTrigger>,
  React.ComponentPropsWithoutKeyof<typeof TabsTrigger, "dir">
>(({ className, children, ...props }, ref) => (
  <TabsTrigger
    {...props}
    className={cn(
      "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
      className,
    )}
    ref={ref}
  >
    {children}
  </TabsTrigger>
))
Tabs2Trigger.displayName = "Tabs2Trigger"

const Tabs2Content = React.forwardRef<
  React.ElementRef<typeof TabsContent>,
  React.ComponentPropsWithoutKeyof<typeof TabsContent, "dir">
>(({ className, children, ...props }, ref) => (
  <TabsContent
    {...props}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
      className,
    )}
    ref={ref}
  >
    {children}
  </TabsContent>
))
Tabs2Content.displayName = "Tabs2Content"

export { Tabs2, Tabs2List, Tabs2Trigger, Tabs2Content }

