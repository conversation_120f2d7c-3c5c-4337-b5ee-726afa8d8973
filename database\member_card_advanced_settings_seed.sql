-- 会员卡高级设置种子数据（基于页面真实字段）

-- 1. 会员卡高级设置数据
INSERT INTO member_card_advanced_settings (
  card_type_id, tenant_id, leave_option, leave_times_limit, leave_days_limit, auto_activate_days,
  max_people_per_class, daily_booking_limit, weekly_booking_limit, weekly_calculation_type,
  monthly_booking_limit, monthly_calculation_type, advance_booking_days, advance_booking_unlimited,
  custom_time_enabled, available_days, available_time_slots
) VALUES
-- 年卡设置（ID=1）
(1, 2, 'no_limit', NULL, NULL, 120, 1, 3, 4, 'natural_week', 5, 'natural_month', NULL, TRUE, FALSE, 
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true), 
 NULL),

-- 半年卡设置（ID=2）
(2, 2, 'no_limit', NULL, NULL, 90, 1, 2, 3, 'natural_week', 4, 'natural_month', 7, FALSE, TRUE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 JSON_ARRAY(
   JSON_OBJECT('startHour', '06', 'startMinute', '00', 'endHour', '22', 'endMinute', '00'),
   JSON_OBJECT('startHour', '08', 'startMinute', '00', 'endHour', '20', 'endMinute', '00')
 )),

-- 季卡设置（ID=3）
(3, 2, 'limited', 2, 10, 60, 1, 2, 2, 'natural_week', 3, 'natural_month', 3, FALSE, FALSE, 
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true), 
 NULL),

-- 月卡设置（ID=4）
(4, 2, 'limited', 1, 5, 30, 1, 1, 2, 'natural_week', 2, 'natural_month', 1, FALSE, FALSE, 
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true), 
 NULL),

-- 次卡设置（ID=5）
(5, 2, 'no_allow', NULL, NULL, 180, 1, 1, 1, 'natural_week', 1, 'natural_month', 0, FALSE, FALSE, 
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true), 
 NULL),

-- 储值卡设置（ID=6）
(6, 2, 'no_limit', NULL, NULL, 365, 1, 5, 10, 'natural_week', 20, 'natural_month', NULL, TRUE, FALSE, 
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true), 
 NULL),

-- 体验卡设置（ID=7）
(7, 2, 'no_allow', NULL, NULL, 7, 1, 1, 1, 'natural_week', 1, 'natural_month', 0, FALSE, FALSE, 
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true), 
 NULL),

-- VIP年卡设置（ID=8）
(8, 2, 'no_limit', NULL, NULL, 180, 2, 5, 8, 'natural_week', 15, 'natural_month', NULL, TRUE, TRUE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 JSON_ARRAY(
   JSON_OBJECT('startHour', '06', 'startMinute', '00', 'endHour', '23', 'endMinute', '00')
 ));

-- 2. 会员卡用卡人设置数据
INSERT INTO member_card_user_settings (
  card_type_id, tenant_id, booking_interval_enabled, booking_interval_minutes,
  pending_booking_limit, cancel_limit_enabled, cancel_limit_count, cancel_limit_period,
  same_course_daily_limit, peak_time_enabled, peak_start_time, peak_end_time, peak_daily_limit,
  priority_enabled, priority_hours, priority_description
) VALUES
-- 年卡用户设置
(1, 2, FALSE, 0, 0, FALSE, 0, 'week', 1, FALSE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),

-- 半年卡用户设置
(2, 2, TRUE, 60, 2, TRUE, 3, 'week', 1, TRUE, '18:00:00', '21:00:00', 1, TRUE, 24, '本周尚未参加此类课程的会员优先预约'),

-- 季卡用户设置
(3, 2, TRUE, 120, 1, TRUE, 2, 'week', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),

-- 月卡用户设置
(4, 2, TRUE, 180, 1, TRUE, 1, 'week', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),

-- 次卡用户设置
(5, 2, TRUE, 240, 1, TRUE, 1, 'day', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),

-- 储值卡用户设置
(6, 2, FALSE, 0, 0, FALSE, 0, 'week', 2, FALSE, '18:00:00', '21:00:00', 2, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),

-- 体验卡用户设置
(7, 2, TRUE, 1440, 1, TRUE, 0, 'day', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, '本周尚未参加此类课程的会员优先预约'),

-- VIP年卡用户设置
(8, 2, FALSE, 0, 0, FALSE, 0, 'week', 2, FALSE, '18:00:00', '21:00:00', 3, TRUE, 48, 'VIP会员优先预约48小时');

-- 3. 会员卡课程设置数据
INSERT INTO member_card_course_settings (
  card_type_id, tenant_id, consumption_rule, consumption_description,
  gift_class_count, gift_value_coefficient, all_courses_enabled
) VALUES
-- 年卡课程设置
(1, 2, 'AVERAGE', '会员卡总价值将平均分配到每次课程消耗中', 0, 1.0, TRUE),

-- 半年卡课程设置
(2, 2, 'AVERAGE', '会员卡总价值将平均分配到每次课程消耗中', 0, 1.0, TRUE),

-- 季卡课程设置
(3, 2, 'AVERAGE', '会员卡总价值将平均分配到每次课程消耗中', 0, 1.0, TRUE),

-- 月卡课程设置
(4, 2, 'AVERAGE', '会员卡总价值将平均分配到每次课程消耗中', 0, 1.0, TRUE),

-- 次卡课程设置
(5, 2, 'FIXED', '每节课固定消耗1次', 0, 1.0, FALSE),

-- 储值卡课程设置
(6, 2, 'CUSTOM', '根据课程类型自定义消耗金额', 0, 1.0, TRUE),

-- 体验卡课程设置
(7, 2, 'FIXED', '体验卡专用课程', 1, 1.0, FALSE),

-- VIP年卡课程设置
(8, 2, 'AVERAGE', 'VIP会员卡总价值将平均分配到每次课程消耗中', 5, 1.2, TRUE);

-- 4. 会员卡课程关联数据
INSERT INTO member_card_course_associations (
  card_type_id, course_type_id, tenant_id, is_enabled, consumption_times,
  course_type_name, course_duration
) VALUES
-- 年卡关联
(1, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(1, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),
(1, 17, 2, TRUE, 1.0, 'Yin Yoga', 60),
(1, 18, 2, TRUE, 1.0, 'Aerial Yoga', 60),
(1, 19, 2, TRUE, 1.0, 'Prenatal Yoga', 60),

-- 半年卡关联
(2, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(2, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),
(2, 17, 2, TRUE, 1.0, 'Yin Yoga', 60),
(2, 18, 2, TRUE, 1.0, 'Aerial Yoga', 60),
(2, 19, 2, TRUE, 1.0, 'Prenatal Yoga', 60),

-- 季卡关联
(3, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(3, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),
(3, 18, 2, TRUE, 1.0, 'Aerial Yoga', 60),

-- 月卡关联
(4, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(4, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),

-- 次卡关联
(5, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(5, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),

-- 储值卡关联（不同课程不同消耗）
(6, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(6, 16, 2, TRUE, 1.5, 'Flow Yoga', 60),
(6, 17, 2, TRUE, 2.0, 'Yin Yoga', 60),
(6, 18, 2, TRUE, 2.0, 'Aerial Yoga', 60),
(6, 19, 2, TRUE, 1.0, 'Prenatal Yoga', 60),

-- 体验卡关联
(7, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),

-- VIP年卡关联（VIP折扣）
(8, 15, 2, TRUE, 0.8, 'Hatha Yoga', 60),
(8, 16, 2, TRUE, 0.8, 'Flow Yoga', 60),
(8, 17, 2, TRUE, 0.8, 'Yin Yoga', 60),
(8, 18, 2, TRUE, 0.8, 'Aerial Yoga', 60),
(8, 19, 2, TRUE, 0.8, 'Prenatal Yoga', 60);

-- 5. 会员卡销售设置数据
INSERT INTO member_card_sales_settings (
  card_type_id, tenant_id, enable_discount, discount_percentage,
  enable_promotion, promotion_type, promotion_description, price_description,
  max_sales_total, max_sales_daily, max_per_user, sale_start_date, sale_end_date
) VALUES
-- 年卡销售设置
(1, 2, TRUE, 15.00, TRUE, 'new', '新会员15%折扣', '年卡365天无限次使用', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),

-- 半年卡销售设置
(2, 2, TRUE, 10.00, TRUE, 'renewal', '续费会员10%折扣', '半年卡180天无限次使用', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),

-- 季卡销售设置
(3, 2, FALSE, NULL, TRUE, 'group', '团购优惠', '季卡90天无限次使用', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),

-- 月卡销售设置
(4, 2, FALSE, NULL, FALSE, NULL, NULL, '月卡30天无限次使用', NULL, NULL, 2, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),

-- 次卡销售设置
(5, 2, FALSE, NULL, FALSE, NULL, NULL, '次卡10次课程', NULL, NULL, 3, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),

-- 储值卡销售设置
(6, 2, TRUE, 5.00, TRUE, 'new', '储值赠送', '储值卡灵活消费', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),

-- 体验卡销售设置
(7, 2, FALSE, NULL, TRUE, 'new', '新会员体验价', '体验卡1次课程，仅限新会员', 100, 10, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),

-- VIP年卡销售设置
(8, 2, TRUE, 20.00, TRUE, 'new', 'VIP卡20%折扣，专享服务', 'VIP年卡365天无限次使用，专享服务', 50, 2, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59');
