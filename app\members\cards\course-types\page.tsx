"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Search, Plus, Save, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Trash, <PERSON> } from "lucide-react"

// 模拟会员卡类型数据
const membershipTypes = [
  { id: "MT001", name: "瑜伽年卡", color: "#4CAF50", price: 3600, validity: "365天", limit: "不限次数", type: "period" },
  { id: "MT002", name: "瑜伽季卡", color: "#2196F3", price: 1200, validity: "90天", limit: "不限次数", type: "period" },
  { id: "MT003", name: "瑜伽月卡", color: "#FF9800", price: 600, validity: "30天", limit: "不限次数", type: "period" },
  { id: "MT004", name: "私教次卡", color: "#9C27B0", price: 2000, validity: "180天", limit: "20次", type: "count" },
  { id: "MT005", name: "体验卡", color: "#F44336", price: 100, validity: "7天", limit: "3次", type: "count" },
  { id: "MT006", name: "储值卡", color: "#8B5CF6", price: 1000, validity: "365天", limit: "1000元", type: "value" },
]

// 模拟课程类型数据
const courseTypes = [
  { id: "CT001", name: "团课", color: "#4285F4", description: "适合多人参与的常规团体课程" },
  { id: "CT002", name: "小班课", color: "#34A853", description: "小规模教学，教练能给予更多个性化指导" },
  { id: "CT003", name: "精品课", color: "#FBBC05", description: "高端定制课程，由资深教练授课" },
  { id: "CT004", name: "私教课", color: "#EA4335", description: "一对一个性化教学" },
  { id: "CT005", name: "教培课", color: "#9C27B0", description: "针对瑜伽教练的专业培训课程" },
]

// 模拟会员卡与课程类型关联数据
const membershipCourseTypeRelations = [
  {
    membershipTypeId: "MT001",
    courseTypes: [
      { courseTypeId: "CT001", weight: 1.0, maxUsageTimes: null, consumptionValue: null },
      { courseTypeId: "CT002", weight: 1.0, maxUsageTimes: null, consumptionValue: null },
      { courseTypeId: "CT003", weight: 1.0, maxUsageTimes: null, consumptionValue: null },
    ]
  },
  {
    membershipTypeId: "MT002",
    courseTypes: [
      { courseTypeId: "CT001", weight: 1.0, maxUsageTimes: null, consumptionValue: null },
      { courseTypeId: "CT002", weight: 1.0, maxUsageTimes: null, consumptionValue: null },
    ]
  },
  {
    membershipTypeId: "MT004",
    courseTypes: [
      { courseTypeId: "CT004", weight: 1.0, maxUsageTimes: 20, consumptionValue: 1 },
    ]
  },
  {
    membershipTypeId: "MT005",
    courseTypes: [
      { courseTypeId: "CT001", weight: 1.0, maxUsageTimes: 3, consumptionValue: 1 },
      { courseTypeId: "CT002", weight: 1.0, maxUsageTimes: 3, consumptionValue: 1 },
      { courseTypeId: "CT003", weight: 1.0, maxUsageTimes: 3, consumptionValue: 1 },
    ]
  },
  {
    membershipTypeId: "MT006",
    courseTypes: [
      { courseTypeId: "CT001", weight: 1.0, maxUsageTimes: null, consumptionValue: 50 },
      { courseTypeId: "CT002", weight: 1.0, maxUsageTimes: null, consumptionValue: 60 },
      { courseTypeId: "CT003", weight: 1.0, maxUsageTimes: null, consumptionValue: 70 },
      { courseTypeId: "CT004", weight: 1.0, maxUsageTimes: null, consumptionValue: 100 },
    ]
  },
]

export default function MembershipCourseTypesPage() {
  // 状态管理
  const [selectedMembershipType, setSelectedMembershipType] = useState<string | null>(null)
  const [selectedCourseTypes, setSelectedCourseTypes] = useState<Map<string, { weight: number, maxUsageTimes: number | null, consumptionValue: number | null }>>(new Map())
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingCourseType, setEditingCourseType] = useState<{ id: string, weight: number, maxUsageTimes: number | null, consumptionValue: number | null } | null>(null)
  const [searchTerm, setSearchTerm] = useState("")

  // 当选择会员卡类型时，加载关联的课程类型
  useEffect(() => {
    if (selectedMembershipType) {
      const relation = membershipCourseTypeRelations.find(r => r.membershipTypeId === selectedMembershipType)

      if (relation) {
        const newSelectedCourseTypes = new Map<string, { weight: number, maxUsageTimes: number | null, consumptionValue: number | null }>()

        relation.courseTypes.forEach(ct => {
          newSelectedCourseTypes.set(ct.courseTypeId, {
            weight: ct.weight,
            maxUsageTimes: ct.maxUsageTimes,
            consumptionValue: ct.consumptionValue
          })
        })

        setSelectedCourseTypes(newSelectedCourseTypes)
      } else {
        setSelectedCourseTypes(new Map())
      }
    } else {
      setSelectedCourseTypes(new Map())
    }
  }, [selectedMembershipType])

  // 获取当前选择的会员卡类型信息
  const currentMembershipType = membershipTypes.find(mt => mt.id === selectedMembershipType)

  // 过滤课程类型
  const filteredCourseTypes = courseTypes.filter(ct =>
    ct.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ct.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 处理课程类型选择
  const handleCourseTypeSelect = (courseTypeId: string, checked: boolean) => {
    const newSelectedCourseTypes = new Map(selectedCourseTypes)

    if (checked) {
      // 根据会员卡类型设置默认值
      const cardType = currentMembershipType?.type || "time"
      let defaultConsumptionValue = null

      if (cardType === "count") {
        defaultConsumptionValue = 1 // 次卡默认消耗1次
      } else if (cardType === "value") {
        // 储值卡默认消耗金额，可以根据课程类型设置不同的默认值
        const courseType = courseTypes.find(ct => ct.id === courseTypeId)
        if (courseType) {
          if (courseType.id === "CT004") { // 私教课
            defaultConsumptionValue = 100
          } else {
            defaultConsumptionValue = 50
          }
        }
      }

      newSelectedCourseTypes.set(courseTypeId, {
        weight: 1.0,
        maxUsageTimes: null,
        consumptionValue: defaultConsumptionValue
      })
    } else {
      newSelectedCourseTypes.delete(courseTypeId)
    }

    setSelectedCourseTypes(newSelectedCourseTypes)
  }

  // 打开编辑对话框
  const openEditDialog = (courseTypeId: string) => {
    const courseTypeSettings = selectedCourseTypes.get(courseTypeId)

    if (courseTypeSettings) {
      setEditingCourseType({
        id: courseTypeId,
        weight: courseTypeSettings.weight,
        maxUsageTimes: courseTypeSettings.maxUsageTimes,
        consumptionValue: courseTypeSettings.consumptionValue
      })
      setShowEditDialog(true)
    }
  }

  // 保存课程类型设置
  const saveCourseTypeSettings = () => {
    if (editingCourseType) {
      const newSelectedCourseTypes = new Map(selectedCourseTypes)

      newSelectedCourseTypes.set(editingCourseType.id, {
        weight: editingCourseType.weight,
        maxUsageTimes: editingCourseType.maxUsageTimes,
        consumptionValue: editingCourseType.consumptionValue
      })

      setSelectedCourseTypes(newSelectedCourseTypes)
      setShowEditDialog(false)
      setEditingCourseType(null)

      toast({
        title: "设置已保存",
        description: "课程类型设置已更新",
      })
    }
  }

  // 保存会员卡与课程类型关联
  const saveRelations = () => {
    // 实际应用中，这里应该调用API保存关联
    // 这里仅做模拟
    toast({
      title: "关联已保存",
      description: `已成功保存 ${currentMembershipType?.name} 的课程类型关联`,
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">会员卡课程类型关联</h1>
          <p className="text-muted-foreground">
            设置不同会员卡可以使用的课程类型
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.location.href = "/members/cards"}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回会员卡名称管理
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 会员卡列表 */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>会员卡名称</CardTitle>
            <CardDescription>选择要设置的会员卡</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {membershipTypes.map((membershipType) => (
                <div
                  key={membershipType.id}
                  className={`p-3 rounded-md border cursor-pointer transition-colors ${
                    selectedMembershipType === membershipType.id
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50"
                  }`}
                  onClick={() => setSelectedMembershipType(membershipType.id)}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: membershipType.color }}
                    ></div>
                    <span className="font-medium">{membershipType.name}</span>
                  </div>
                  <div className="mt-2 text-sm text-muted-foreground">
                    <div>价格: {membershipType.price}元</div>
                    <div>有效期: {membershipType.validity}</div>
                    <div>次数限制: {membershipType.limit}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 课程类型选择 */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>
              {currentMembershipType
                ? `${currentMembershipType.name} - 可用课程类型`
                : "课程类型设置"}
            </CardTitle>
            <CardDescription>
              {currentMembershipType
                ? "选择该会员卡可以使用的课程类型"
                : "请先选择一个会员卡"}
            </CardDescription>
            <div className="flex w-full max-w-sm items-center space-x-2 mt-2">
              <Input
                placeholder="搜索课程类型..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <Button type="submit" size="icon" variant="ghost">
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {!selectedMembershipType ? (
              <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
                <p>请从左侧选择一个会员卡</p>
              </div>
            ) : (
              <div className="space-y-4">
                <ScrollArea className="h-[400px] pr-4">
                  {filteredCourseTypes.map((courseType) => {
                    const isSelected = selectedCourseTypes.has(courseType.id)
                    const settings = selectedCourseTypes.get(courseType.id)

                    return (
                      <div key={courseType.id} className="flex items-start space-x-3 py-2">
                        <Checkbox
                          id={`course-type-${courseType.id}`}
                          checked={isSelected}
                          onCheckedChange={(checked) =>
                            handleCourseTypeSelect(courseType.id, checked === true)
                          }
                        />
                        <div className="grid gap-1.5 leading-none w-full">
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: courseType.color }}
                              ></div>
                              <Label
                                htmlFor={`course-type-${courseType.id}`}
                                className="font-medium cursor-pointer"
                              >
                                {courseType.name}
                              </Label>
                            </div>
                            {isSelected && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => openEditDialog(courseType.id)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {courseType.description}
                          </p>
                          {isSelected && settings && (
                            <div className="flex flex-wrap gap-2 mt-1">
                              {settings.weight !== 1.0 && (
                                <Badge variant="outline" className="text-xs">
                                  权重: {settings.weight}
                                </Badge>
                              )}
                              {settings.maxUsageTimes && (
                                <Badge variant="outline" className="text-xs">
                                  最多使用: {settings.maxUsageTimes}次
                                </Badge>
                              )}
                              {currentMembershipType?.type === "count" && settings.consumptionValue && (
                                <Badge variant="outline" className="text-xs">
                                  消耗次数: {settings.consumptionValue}次
                                </Badge>
                              )}
                              {currentMembershipType?.type === "value" && settings.consumptionValue && (
                                <Badge variant="outline" className="text-xs">
                                  消耗金额: {settings.consumptionValue}元
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </ScrollArea>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-end">
            <Button
              onClick={saveRelations}
              disabled={!selectedMembershipType}
            >
              <Save className="mr-2 h-4 w-4" />
              保存关联设置
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* 编辑课程类型设置对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>编辑课程类型设置</DialogTitle>
            <DialogDescription>
              设置课程类型的权重和使用次数限制
            </DialogDescription>
          </DialogHeader>

          {editingCourseType && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="weight">消耗权重</Label>
                <Input
                  id="weight"
                  type="number"
                  step="0.1"
                  min="0.1"
                  max="10"
                  value={editingCourseType.weight}
                  onChange={(e) => setEditingCourseType({
                    ...editingCourseType,
                    weight: parseFloat(e.target.value)
                  })}
                />
                <p className="text-xs text-muted-foreground">
                  设置该课程类型的消耗权重，默认为1.0
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-usage">最大使用次数</Label>
                <Input
                  id="max-usage"
                  type="number"
                  min="0"
                  value={editingCourseType.maxUsageTimes === null ? "" : editingCourseType.maxUsageTimes}
                  onChange={(e) => setEditingCourseType({
                    ...editingCourseType,
                    maxUsageTimes: e.target.value === "" ? null : parseInt(e.target.value)
                  })}
                />
                <p className="text-xs text-muted-foreground">
                  限制该课程类型的最大使用次数，留空表示不限制
                </p>
              </div>

              {currentMembershipType?.type === "count" && (
                <div className="space-y-2">
                  <Label htmlFor="consumption-count">消耗次数</Label>
                  <Input
                    id="consumption-count"
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={editingCourseType.consumptionValue === null ? "" : editingCourseType.consumptionValue}
                    onChange={(e) => setEditingCourseType({
                      ...editingCourseType,
                      consumptionValue: e.target.value === "" ? null : parseFloat(e.target.value)
                    })}
                  />
                  <p className="text-xs text-muted-foreground">
                    设置该课程类型消耗的次数，默认为1次
                  </p>
                </div>
              )}

              {currentMembershipType?.type === "value" && (
                <div className="space-y-2">
                  <Label htmlFor="consumption-value">消耗金额（元）</Label>
                  <Input
                    id="consumption-value"
                    type="number"
                    min="0"
                    step="1"
                    value={editingCourseType.consumptionValue === null ? "" : editingCourseType.consumptionValue}
                    onChange={(e) => setEditingCourseType({
                      ...editingCourseType,
                      consumptionValue: e.target.value === "" ? null : parseFloat(e.target.value)
                    })}
                  />
                  <p className="text-xs text-muted-foreground">
                    设置该课程类型消耗的金额
                  </p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              取消
            </Button>
            <Button onClick={saveCourseTypeSettings}>
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
