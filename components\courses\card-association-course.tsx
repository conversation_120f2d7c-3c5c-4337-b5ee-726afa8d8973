"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"

// 模拟会员卡数据 - 实际应用中应该从API获取
const membershipCards = [
  { id: "MC001", name: "瑜伽年卡", color: "#4CAF50", type: "time", validity: "365天", limit: "不限次数" },
  { id: "MC002", name: "瑜伽季卡", color: "#2196F3", type: "time", validity: "90天", limit: "不限次数" },
  { id: "MC003", name: "瑜伽月卡", color: "#FF9800", type: "time", validity: "30天", limit: "不限次数" },
  { id: "MC004", name: "私教次卡", color: "#9C27B0", type: "count", validity: "180天", limit: "20次" },
  { id: "MC005", name: "体验卡", color: "#F44336", type: "count", validity: "7天", limit: "3次" },
  { id: "MC006", name: "储值卡", color: "#8B5CF6", type: "value", validity: "365天", limit: "1000元" },
]

// 卡片类别
const cardCategories = [
  { id: "time", name: "时间卡", count: 3 },
  { id: "count", name: "次数卡", count: 2 },
  { id: "value", name: "储值卡", count: 1 }
]

interface CardAssociationCourseProps {
  selectedCards: string[]
  onSelectedCardsChange: (selectedCards: string[]) => void
  cardConsumption: any
  onCardConsumptionChange: (cardConsumption: any) => void
}

export function CardAssociationCourse({
  selectedCards,
  onSelectedCardsChange,
  cardConsumption,
  onCardConsumptionChange
}: CardAssociationCourseProps) {
  // 处理卡片选择
  const handleCardSelect = (cardId: string, checked: boolean) => {
    if (checked) {
      onSelectedCardsChange([...selectedCards, cardId])
    } else {
      onSelectedCardsChange(selectedCards.filter(id => id !== cardId))
    }
  }

  // 清除所有选择
  const handleClearAll = () => {
    onSelectedCardsChange([])
  }

  // 选择所有卡片
  const handleSelectAll = () => {
    onSelectedCardsChange(membershipCards.map(card => card.id))
  }

  // 处理分类全选
  const handleSelectCategory = (categoryType: string, checked: boolean) => {
    const cardsInCategory = membershipCards.filter(card => card.type === categoryType)

    if (checked) {
      // 添加该分类下所有未选中的卡片
      const newSelectedCards = [...selectedCards]
      cardsInCategory.forEach(card => {
        if (!newSelectedCards.includes(card.id)) {
          newSelectedCards.push(card.id)
        }
      })
      onSelectedCardsChange(newSelectedCards)
    } else {
      // 移除该分类下所有卡片
      onSelectedCardsChange(selectedCards.filter(
        id => !cardsInCategory.some(card => card.id === id)
      ))
    }
  }

  // 检查分类是否全选
  const isCategoryAllSelected = (categoryType: string) => {
    const cardsInCategory = membershipCards.filter(card => card.type === categoryType)
    return cardsInCategory.every(card => selectedCards.includes(card.id))
  }

  // 处理消耗值变更
  const handleConsumptionChange = (cardId: string, type: string, value: number) => {
    const newCustomConsumption = {
      ...(cardConsumption?.customConsumption || {}),
      [cardId]: {
        ...(cardConsumption?.customConsumption?.[cardId] || {}),
        [type === "count" ? "count" : "value"]: value
      }
    }

    onCardConsumptionChange({
      ...cardConsumption,
      customConsumption: newCustomConsumption
    })
  }

  // 获取卡片消耗值
  const getConsumptionValue = (cardId: string, type: string) => {
    if (type === "count") {
      return cardConsumption?.customConsumption?.[cardId]?.count ||
             cardConsumption?.countCardConsumption || 1
    } else {
      return cardConsumption?.customConsumption?.[cardId]?.value ||
             cardConsumption?.valueCardConsumption || 100
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>会员卡类型</Label>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="select-all-cards"
            checked={selectedCards.length === membershipCards.length}
            onCheckedChange={(checked) => {
              if (checked) {
                handleSelectAll()
              } else {
                handleClearAll()
              }
            }}
          />
          <label
            htmlFor="select-all-cards"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            全选
          </label>
        </div>
      </div>

      <div className="border rounded-md">
        {cardCategories.map((category) => {
          const cardsInCategory = membershipCards.filter(card => card.type === category.id)
          
          if (cardsInCategory.length === 0) return null
          
          return (
            <div key={category.id} className="border-b last:border-b-0">
              <div className="flex items-center justify-between p-3 bg-muted/50">
                <div className="flex items-center">
                  <div 
                    className="h-4 w-4 rounded-full mr-2" 
                    style={{ 
                      backgroundColor: category.id === "time" ? "#4CAF50" : 
                                      category.id === "count" ? "#9C27B0" : "#8B5CF6" 
                    }}
                  />
                  <h3 className="font-medium">{category.name}</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`select-category-${category.id}`}
                    checked={isCategoryAllSelected(category.id)}
                    onCheckedChange={(checked) => handleSelectCategory(category.id, !!checked)}
                  />
                  <label
                    htmlFor={`select-category-${category.id}`}
                    className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    全选
                  </label>
                  <Badge variant="outline" className="text-xs">
                    {category.count}张
                  </Badge>
                </div>
              </div>
              
              <div className="p-3 bg-muted/20">
                <div className="grid grid-cols-2 gap-2">
                  {cardsInCategory.map(card => (
                    <div 
                      key={card.id} 
                      className="flex items-center"
                    >
                      <Checkbox
                        id={`card-${card.id}`}
                        checked={selectedCards.includes(card.id)}
                        onCheckedChange={(checked) => handleCardSelect(card.id, !!checked)}
                        className="mr-2"
                      />
                      <label
                        htmlFor={`card-${card.id}`}
                        className="text-sm cursor-pointer"
                      >
                        {card.name}
                      </label>
                      <div className="text-xs text-muted-foreground ml-2">
                        {card.type === "time" ? card.validity : card.limit}
                      </div>
                      
                      {card.type !== "time" && selectedCards.includes(card.id) && (
                        <div className="ml-auto flex items-center">
                          <Input
                            type="number"
                            min={card.type === "count" ? "1" : "0"}
                            value={getConsumptionValue(card.id, card.type)}
                            onChange={(e) => {
                              const value = parseInt(e.target.value) || (card.type === "count" ? 1 : 0)
                              handleConsumptionChange(card.id, card.type, value)
                            }}
                            className="w-16 h-8 text-xs"
                          />
                          <span className="text-xs ml-1">{card.type === "count" ? "次" : "元"}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm">
          已选择 <span className="font-medium">{selectedCards.length}</span> 张会员卡
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearAll}
            disabled={selectedCards.length === 0}
          >
            清除全部
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
            disabled={selectedCards.length === membershipCards.length}
          >
            选择全部
          </Button>
        </div>
      </div>
    </div>
  )
}
