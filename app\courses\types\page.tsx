"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useDebounce } from "@/hooks/use-debounce"
import {
  MoreHorizontal,
  Pencil,
  Trash2,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Eye,
  BarChart3,
  Grid,
  List,
  FileText,
  CheckCircle,
  AlertCircle,
  X,
  Calendar,
  Tag,
  ChevronDown,
  Users,
  FileX,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
// import { CourseTypeDialog } from "@/components/courses/course-type-dialog"
import { CourseTypeDetailDialog } from "@/components/courses/course-type-detail-dialog"
import { CourseTypeCard } from "@/components/courses/course-type-card"
import { AddEditCourseTypeDialog } from "@/components/courses/add-edit-course-type-dialog"
import { courseTypeService } from "@/services/course-type-service"
import { useAuth } from "@/contexts/auth-context"



export default function CourseTypesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")
  const [openAddEditDialog, setOpenAddEditDialog] = useState(false)
  const [openDetailDialog, setOpenDetailDialog] = useState(false)
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false)
  const [selectedType, setSelectedType] = useState<any>(null)
  const [types, setTypes] = useState<any[]>([])
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  // 使用防抖处理搜索查询
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  // 筛选状态
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("display_order") // 排序方式
  const [filterOptions, setFilterOptions] = useState({
    showWithCourses: false,    // 显示有关联课程的类型
    showWithoutCourses: false, // 显示无关联课程的类型
    createdAfter: "",        // 创建日期筛选
    createdBefore: "",       // 创建日期筛选
  })

  // 导入导出状态
  const [importDialogOpen, setImportDialogOpen] = useState(false)
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importPreview, setImportPreview] = useState<any[]>([])
  const [importErrors, setImportErrors] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 使用useCallback包装fetchTypes函数
  const fetchTypes = useCallback(async () => {
    setLoading(true);
    console.log('Fetching course types...');

    try {
      // 使用组件顶层获取的用户信息
      const tenantId = user?.tenant_id;

      if (!tenantId) {
        throw new Error('未找到租户信息，请重新登录');
      }

      // 构建查询参数
      const params = new URLSearchParams({
        tenantId: tenantId.toString(),
      });

      if (debouncedSearchQuery) {
        params.append('keyword', debouncedSearchQuery);
      }

      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      if (sortBy) {
        params.append('sortBy', sortBy);
      }

      if (filterOptions.showWithCourses) {
        params.append('showWithCourses', 'true');
      }

      if (filterOptions.showWithoutCourses) {
        params.append('showWithoutCourses', 'true');
      }

      if (filterOptions.createdAfter) {
        params.append('createdAfter', filterOptions.createdAfter);
      }

      if (filterOptions.createdBefore) {
        params.append('createdBefore', filterOptions.createdBefore);
      }

      // 调用API
      const response = await fetch(`/api/course-types?${params.toString()}`);
      const result = await response.json();

      console.log('API response:', result);

      if (result.code === 200) {
        setTypes(result.data.list || []);
      } else {
        throw new Error(result.msg || '获取数据失败');
      }
    } catch (error) {
      console.error("获取课程类型失败:", error);
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "无法获取数据",
        variant: "destructive"
      });
      setTypes([]); // 出错时设置为空数组
    } finally {
      setLoading(false);
    }
  }, [debouncedSearchQuery, statusFilter, sortBy, filterOptions, user]);
  
  // 当搜索或状态筛选变化时，获取数据
  useEffect(() => {
    fetchTypes();
  }, [debouncedSearchQuery, statusFilter, sortBy, fetchTypes]);
  
  // 组件首次加载时获取数据
  useEffect(() => {
    fetchTypes();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  // 由于过滤现在在API中处理，这里直接使用types
  const filteredTypes = types || [];

  // 打开添加对话框
  const handleAddType = () => {
    console.log('添加课程类型按钮被点击');
    console.log('当前对话框状态:', openAddEditDialog);
    console.log('当前用户信息:', user);

    setSelectedType(null);
    setOpenAddEditDialog(true);

    console.log('已设置对话框状态为打开');

    // 强制检查状态更新
    setTimeout(() => {
      console.log('延迟检查对话框状态:', openAddEditDialog);
    }, 100);
  }

  // 打开编辑对话框
  const handleEditType = (type: any) => {
    setSelectedType(type)
    setOpenAddEditDialog(true)
  }

  // 打开详情对话框
  const handleViewDetails = (type: any) => {
    setSelectedType(type)
    setOpenDetailDialog(true)
  }

  // 切换课程类型状态
  const handleToggleStatus = async (type: any) => {
    const newStatus = type.status === "active" ? "inactive" : "active"
    try {
      const response = await fetch(`/api/course-types/${type.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      const result = await response.json();

      if (result.code === 200) {
        toast({
          title: "状态更新成功",
          description: `课程类型"${type.name}"已${newStatus === "active" ? "启用" : "停用"}`,
          variant: "default",
        });
        fetchTypes(); // 重新获取数据
      } else {
        toast({
          title: "状态更新失败",
          description: result.msg || "操作失败",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("更新状态失败:", error);
      toast({
        title: "状态更新失败",
        description: "操作过程中出现错误",
        variant: "destructive"
      });
    }
  }

  // 打开删除确认对话框
  const handleDeleteType = (type: any) => {
    setSelectedType(type)
    setOpenDeleteDialog(true)
  }

  // 确认删除课程类型
  const confirmDeleteType = async () => {
    if (selectedType) {
      try {
        const response = await fetch(`/api/course-types/${selectedType.id}`, {
          method: 'DELETE',
        });

        const result = await response.json();

        if (result.code === 200) {
          toast({
            title: "删除成功",
            description: `课程类型"${selectedType.name}"已删除`,
            variant: "default",
          });
          setOpenDeleteDialog(false);
          setSelectedType(null);
          fetchTypes(); // 重新获取数据
        } else {
          toast({
            title: "删除失败",
            description: result.msg || "删除失败",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("删除课程类型失败:", error);
        toast({
          title: "删除失败",
          description: "操作过程中出现错误",
          variant: "destructive"
        });
      }
    }
  }

  // 保存（新增/编辑）
  const handleSaveType = async (data: any) => {
    try {
      // 使用useAuth钩子获取的用户信息
      const tenantId = user?.tenant_id;

      if (!tenantId) {
        toast({ title: "保存失败", description: "未找到租户信息，请重新登录", variant: "destructive" });
        return;
      }

      if (data.id) { // 编辑
        const response = await fetch(`/api/course-types/${data.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        const result = await response.json();

        if (result.code === 200) {
          toast({ title: "更新成功" });
          setOpenAddEditDialog(false);
          fetchTypes();
        } else {
          toast({ title: "更新失败", description: result.msg || "更新失败", variant: "destructive" });
        }
      } else { // 新增
        const response = await fetch('/api/course-types', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ...data, tenantId }),
        });

        const result = await response.json();

        if (result.code === 200) {
          toast({ title: "创建成功" });
          setOpenAddEditDialog(false);
          fetchTypes();
        } else {
          toast({ title: "创建失败", description: result.msg || "创建失败", variant: "destructive" });
        }
      }
    } catch (error) {
      console.error("保存课程类型失败:", error);
      toast({ title: "保存失败", description: "操作过程中出现错误", variant: "destructive" });
    }
  }

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      processFile(file)
    }
  }

  // 处理文件拖拽
  const handleFileDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0]
      processFile(file)
    }
  }

  // 处理拖拽悬停状态
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
  }

  // 处理文件解析
  const processFile = (file: File) => {
    // 检查文件类型和大小
    const validTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]

    if (!validTypes.includes(file.type) && !file.name.endsWith('.csv') && !file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      toast({
        title: '文件类型错误',
        description: '请选择 CSV 或 Excel 格式的文件',
        variant: 'destructive',
      })
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB
      toast({
        title: '文件过大',
        description: '文件大小不能超过 5MB',
        variant: 'destructive',
      })
      return
    }

    setImportFile(file)

    // 模拟预览数据，实际应该解析文件
    // 在实际应用中，这里应该调用文件解析函数，如 Papa Parse 等库
    const mockPreview = [
      {
        id: 101,
        name: '导入课程1',
        description: '从文件导入的课程类型，包含详细说明和注意事项',
        status: 'active',
        color: '#FF5722',
        courses: 0,
        createdAt: new Date().toISOString().split('T')[0]
      },
      {
        id: 102,
        name: '导入课程2',
        description: '从文件导入的课程类型，适合初学者',
        status: 'active',
        color: '#9C27B0',
        courses: 5,
        createdAt: new Date().toISOString().split('T')[0]
      },
      {
        id: 103,
        name: '导入课程3',
        description: '从文件导入的课程类型，适合进阶者',
        status: 'inactive',
        color: '#2196F3',
        courses: 0,
        createdAt: new Date().toISOString().split('T')[0]
      },
    ]

    setImportPreview(mockPreview)
    setImportErrors([])
  }

  // 处理导入数据
  const handleImportData = () => {
    // 显示加载状态
    toast({
      title: '正在导入数据',
      description: '请稍候...',
      variant: 'default',
    })

    // 模拟导入处理延迟
    setTimeout(() => {
      // 模拟导入成功
      toast({
        title: '导入成功',
        description: `已成功导入 ${importPreview.length} 条课程类型数据`,
        variant: 'default',
      })

      // 关闭对话框并重置状态
      setImportDialogOpen(false)
      setImportFile(null)
      setImportPreview([])
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }, 800)
  }

  // 处理导出数据
  const handleExportData = () => {
    // 获取当前筛选后的数据
    const dataToExport = filteredTypes

    // 显示导出中状态
    toast({
      title: '正在准备导出文件',
      description: '请稍候...',
      variant: 'default',
    })

    // 模拟导出处理延迟
    setTimeout(() => {
      try {
        // 准备导出数据
        const exportData = dataToExport.map(item => ({
          ID: item.id,
          名称: item.name,
          描述: item.description,
          状态: item.status === 'active' ? '启用' : '停用',
          课程数量: item.courses,
          颜色: item.color,
          创建日期: item.createdAt,
          更新日期: item.updatedAt
        }))

        // 在实际应用中，这里应该使用库如 xlsx 或 csv 来生成文件
        // 以下是模拟下载过程
        const fileName = `课程类型数据_${new Date().toISOString().split('T')[0]}.xlsx`

        // 创建一个简单的 CSV 内容
        let csvContent = '\ufeff'; // 添加 BOM 以支持中文

        // 添加表头
        const headers = ['ID', '名称', '描述', '状态', '课程数量', '颜色', '创建日期', '更新日期'];
        csvContent += headers.join(',') + '\r\n';

        // 添加数据行
        exportData.forEach(item => {
          const row = [
            item.ID,
            '"' + item.名称 + '"', // 使用引号包裹文本字段，避免逗号问题
            '"' + item.描述 + '"',
            item.状态,
            item.课程数量,
            item.颜色,
            item.创建日期,
            item.更新日期
          ];
          csvContent += row.join(',') + '\r\n';
        });

        // 创建 Blob 对象
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        // 创建下载链接
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = fileName;

        // 触发下载
        document.body.appendChild(a);
        a.click();

        // 清理
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 100);

        toast({
          title: '导出成功',
          description: `已成功导出 ${dataToExport.length} 条课程类型数据到 ${fileName}`,
          variant: 'default',
        })
      } catch (error) {
        console.error('导出失败:', error)
        toast({
          title: '导出失败',
          description: '导出过程中发生错误，请稍后重试',
          variant: 'destructive',
        })
      }
    }, 800)
  }

  // 处理下载导入模板
  const handleDownloadTemplate = () => {
    // 显示下载中状态
    toast({
      title: '准备下载模板',
      description: '请稍候...',
      variant: 'default',
    })

    // 模拟下载延迟
    setTimeout(() => {
      try {
        // 模板文件名
        const fileName = '课程类型导入模板.xlsx'

        // 在实际应用中，这里应该是一个真实的模板文件 URL
        // 例如：从服务器下载预设的模板文件
        // 或者动态生成一个包含表头和示例数据的模板

        // 创建模板 CSV 内容
        let templateContent = '\ufeff'; // 添加 BOM 以支持中文

        // 添加表头
        const headers = ['ID', '名称', '描述', '状态', '颜色', '课程数量'];
        templateContent += headers.join(',') + '\r\n';

        // 添加示例数据行
        const exampleData = [
          [1001, '"示例课程类型1"', '"这是一个示例课程类型描述"', 'active', '#4285F4', 0],
          [1002, '"示例课程类型2"', '"这是另一个示例课程类型描述"', 'inactive', '#34A853', 0]
        ];

        exampleData.forEach(row => {
          templateContent += row.join(',') + '\r\n';
        });

        // 添加空行供用户填写
        for (let i = 0; i < 5; i++) {
          const emptyRow = ['', '""', '""', '', '', ''];
          templateContent += emptyRow.join(',') + '\r\n';
        }

        // 创建 Blob 对象
        const blob = new Blob([templateContent], { type: 'text/csv;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        // 创建下载链接
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = fileName;

        // 触发下载
        document.body.appendChild(a);
        a.click();

        // 清理
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 100);

        toast({
          title: '下载成功',
          description: `已成功下载导入模板 ${fileName}`,
          variant: 'default',
        })
      } catch (error) {
        console.error('下载模板失败:', error)
        toast({
          title: '下载失败',
          description: '下载模板过程中发生错误，请稍后重试',
          variant: 'destructive',
        })
      }
    }, 800)
  }

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  // 处理状态筛选变化
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">加载中...</h2>
          <p className="text-gray-500">正在获取课程类型数据</p>
        </div>
      </div>
    );
  }
  
  // 如果不是加载状态但没有数据
  if (!loading && (!types || types.length === 0)) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h1 className="text-2xl font-semibold tracking-tight">课程类型</h1>
          <div className="flex flex-wrap gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  高级筛选
                  {(filterOptions.showWithCourses ||
                    filterOptions.showWithoutCourses ||
                    filterOptions.createdAfter ||
                    filterOptions.createdBefore ||
                    statusFilter !== "all") &&
                    <Badge variant="secondary" className="ml-2 px-1">
                      <CheckCircle className="h-3 w-3" />
                    </Badge>
                  }
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-72">
                <DropdownMenuLabel>筛选选项</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div className="p-2">
                  <h4 className="mb-2 text-sm font-medium">课程类型状态</h4>
                  <div className="space-y-2">
                    <Label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        checked={statusFilter === "all"}
                        onChange={() => setStatusFilter("all")}
                        className="h-4 w-4"
                      />
                      <span>全部</span>
                    </Label>
                    <Label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        checked={statusFilter === "active"}
                        onChange={() => setStatusFilter("active")}
                        className="h-4 w-4"
                      />
                      <span>启用</span>
                    </Label>
                    <Label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        checked={statusFilter === "inactive"}
                        onChange={() => setStatusFilter("inactive")}
                        className="h-4 w-4"
                      />
                      <span>停用</span>
                    </Label>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <div className="p-2">
                  <h4 className="mb-2 text-sm font-medium">课程关联</h4>
                  <div className="space-y-2">
                    <Label className="flex items-center space-x-2">
                      <Checkbox
                        checked={filterOptions.showWithCourses}
                        onCheckedChange={(checked) =>
                          setFilterOptions(prev => ({...prev, showWithCourses: !!checked}))
                        }
                      />
                      <span>有关联课程</span>
                    </Label>
                    <Label className="flex items-center space-x-2">
                      <Checkbox
                        checked={filterOptions.showWithoutCourses}
                        onCheckedChange={(checked) =>
                          setFilterOptions(prev => ({...prev, showWithoutCourses: !!checked}))
                        }
                      />
                      <span>无关联课程</span>
                    </Label>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <div className="p-2">
                  <h4 className="mb-2 text-sm font-medium">创建日期</h4>
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Label htmlFor="createdAfter" className="text-xs">开始日期</Label>
                        <div className="relative">
                          <Calendar className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                          <Input
                            id="createdAfter"
                            type="date"
                            value={filterOptions.createdAfter}
                            onChange={(e) => setFilterOptions(prev => ({...prev, createdAfter: e.target.value}))}
                            className="h-8 pl-8"
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="createdBefore" className="text-xs">结束日期</Label>
                        <div className="relative">
                          <Calendar className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                          <Input
                            id="createdBefore"
                            type="date"
                            value={filterOptions.createdBefore}
                            onChange={(e) => setFilterOptions(prev => ({...prev, createdBefore: e.target.value}))}
                            className="h-8 pl-8"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <div className="p-2 flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setStatusFilter("all");
                      setFilterOptions({
                        showWithCourses: false,
                        showWithoutCourses: false,
                        createdAfter: "",
                        createdBefore: "",
                      });
                    }}
                  >
                    重置筛选
                  </Button>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  导入/导出
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setImportDialogOpen(true)}>
                  <Upload className="mr-2 h-4 w-4" />
                  导入课程类型
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleExportData}>
                  <Download className="mr-2 h-4 w-4" />
                  导出课程类型
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleDownloadTemplate()}>
                  <FileText className="mr-2 h-4 w-4" />
                  下载导入模板
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex rounded-md border">
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                className="rounded-r-none"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
                <span className="sr-only">列表视图</span>
              </Button>
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                className="rounded-l-none"
                onClick={() => setViewMode("grid")}
              >
                <Grid className="h-4 w-4" />
                <span className="sr-only">网格视图</span>
              </Button>
            </div>

            <Button onClick={handleAddType}>
              <Plus className="mr-2 h-4 w-4" />
              添加课程类型
            </Button>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <Card>
            <CardContent className="p-3">
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="w-full md:w-1/3">
                  <Input
                    placeholder="搜索课程类型名称或描述"
                    value={searchQuery}
                    onChange={handleSearchChange}
                  />
                </div>
                <div className="flex flex-1 flex-wrap gap-4">
                  <Select 
                    value={statusFilter} 
                    onValueChange={handleStatusFilterChange}
                  >
                    <SelectTrigger className="w-full md:w-[180px]">
                      <SelectValue placeholder="课程类型状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="active">启用</SelectItem>
                      <SelectItem value="inactive">停用</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    value={filterOptions.showWithCourses ? "with" : filterOptions.showWithoutCourses ? "without" : "all"}
                    onValueChange={(value) => {
                      if (value === "with") {
                        setFilterOptions(prev => ({...prev, showWithCourses: true, showWithoutCourses: false}));
                      } else if (value === "without") {
                        setFilterOptions(prev => ({...prev, showWithCourses: false, showWithoutCourses: true}));
                      } else {
                        setFilterOptions(prev => ({...prev, showWithCourses: false, showWithoutCourses: false}));
                      }
                    }}
                  >
                    <SelectTrigger className="w-full md:w-[180px]">
                      <SelectValue placeholder="关联课程" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="with">有关联课程</SelectItem>
                      <SelectItem value="without">无关联课程</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    size="sm"
                    className="ml-auto"
                    onClick={() => {
                      setSearchQuery("");
                      setStatusFilter("all");
                      setFilterOptions({
                        showWithCourses: false,
                        showWithoutCourses: false,
                        createdAfter: "",
                        createdBefore: "",
                      });
                    }}
                  >
                    <X className="mr-2 h-4 w-4" />
                    清除筛选
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* 表格展示无数据状态 */}
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">颜色</TableHead>
                  <TableHead>类型名称</TableHead>
                  <TableHead className="hidden md:table-cell">描述</TableHead>
                  <TableHead className="hidden md:table-cell">课程数量</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="hidden md:table-cell">创建日期</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={7} className="h-60 text-center">
                    <div className="flex flex-col items-center justify-center py-10">
                      <FileX className="h-12 w-12 text-muted-foreground" />
                      <h3 className="mt-4 text-lg font-medium">
                        {debouncedSearchQuery ? "未找到匹配的课程类型" : "暂无课程类型数据"}
                      </h3>
                      <p className="mt-2 text-sm text-muted-foreground">
                        {debouncedSearchQuery 
                          ? `没有找到与"${debouncedSearchQuery}"匹配的课程类型，请尝试其他搜索条件。` 
                          : "您还没有创建任何课程类型，点击添加按钮创建第一个课程类型。"
                        }
                      </p>
                      <div className="mt-6">
                        {debouncedSearchQuery ? (
                          <Button variant="outline" onClick={() => setSearchQuery("")}>
                            清除搜索条件
                          </Button>
                        ) : (
                          <Button
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              console.log('空状态添加按钮被点击');
                              handleAddType();
                            }}
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            添加课程类型
                          </Button>
                        )}
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 调试区域 */}
      <div className="p-4 bg-gray-100 rounded-md">
        <h3 className="font-bold mb-2">调试信息</h3>
        <p>对话框状态: <span className={openAddEditDialog ? 'text-green-600 font-bold' : 'text-red-600'}>{openAddEditDialog ? '打开' : '关闭'}</span></p>
        <p>选中类型: {selectedType ? JSON.stringify(selectedType) : '无'}</p>
        <p>用户信息: {user ? `ID: ${user.id}, 租户: ${user.tenant_id}` : '无'}</p>
        <div className="mt-2 flex gap-2">
          <Button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('直接设置对话框状态为true');
              setOpenAddEditDialog(true);
              console.log('设置后的状态:', openAddEditDialog);
            }}
            variant="outline"
          >
            直接打开对话框
          </Button>
          <Button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('当前状态:', { openAddEditDialog, selectedType, user });
            }}
            variant="outline"
          >
            打印状态
          </Button>
          <Button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('测试添加按钮被点击');
              handleAddType();
            }}
            variant="outline"
          >
            测试添加按钮
          </Button>
        </div>

        {/* 简单的测试对话框 */}
        <div className="mt-4">
          <Button
            onClick={() => {
              console.log('显示简单测试对话框');
              alert('这是一个简单的测试弹窗，如果你能看到这个，说明JavaScript正常工作');
            }}
            variant="destructive"
          >
            测试简单弹窗
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">课程类型</h1>
        <div className="flex flex-wrap gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                高级筛选
                {(filterOptions.showWithCourses ||
                  filterOptions.showWithoutCourses ||
                  filterOptions.createdAfter ||
                  filterOptions.createdBefore ||
                  statusFilter !== "all") &&
                  <Badge variant="secondary" className="ml-2 px-1">
                    <CheckCircle className="h-3 w-3" />
                  </Badge>
                }
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-72">
              <DropdownMenuLabel>筛选选项</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <div className="p-2">
                <h4 className="mb-2 text-sm font-medium">课程类型状态</h4>
                <div className="space-y-2">
                  <Label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      checked={statusFilter === "all"}
                      onChange={() => setStatusFilter("all")}
                      className="h-4 w-4"
                    />
                    <span>全部</span>
                  </Label>
                  <Label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      checked={statusFilter === "active"}
                      onChange={() => setStatusFilter("active")}
                      className="h-4 w-4"
                    />
                    <span>启用</span>
                  </Label>
                  <Label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      checked={statusFilter === "inactive"}
                      onChange={() => setStatusFilter("inactive")}
                      className="h-4 w-4"
                    />
                    <span>停用</span>
                  </Label>
                </div>
              </div>
              <DropdownMenuSeparator />
              <div className="p-2">
                <h4 className="mb-2 text-sm font-medium">课程关联</h4>
                <div className="space-y-2">
                  <Label className="flex items-center space-x-2">
                    <Checkbox
                      checked={filterOptions.showWithCourses}
                      onCheckedChange={(checked) =>
                        setFilterOptions(prev => ({...prev, showWithCourses: !!checked}))
                      }
                    />
                    <span>有关联课程</span>
                  </Label>
                  <Label className="flex items-center space-x-2">
                    <Checkbox
                      checked={filterOptions.showWithoutCourses}
                      onCheckedChange={(checked) =>
                        setFilterOptions(prev => ({...prev, showWithoutCourses: !!checked}))
                      }
                    />
                    <span>无关联课程</span>
                  </Label>
                </div>
              </div>
              <DropdownMenuSeparator />
              <div className="p-2">
                <h4 className="mb-2 text-sm font-medium">创建日期</h4>
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label htmlFor="createdAfter" className="text-xs">开始日期</Label>
                      <div className="relative">
                        <Calendar className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                          id="createdAfter"
                          type="date"
                          value={filterOptions.createdAfter}
                          onChange={(e) => setFilterOptions(prev => ({...prev, createdAfter: e.target.value}))}
                          className="h-8 pl-8"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="createdBefore" className="text-xs">结束日期</Label>
                      <div className="relative">
                        <Calendar className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                        <Input
                          id="createdBefore"
                          type="date"
                          value={filterOptions.createdBefore}
                          onChange={(e) => setFilterOptions(prev => ({...prev, createdBefore: e.target.value}))}
                          className="h-8 pl-8"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <DropdownMenuSeparator />
              <div className="p-2 flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setStatusFilter("all");
                    setFilterOptions({
                      showWithCourses: false,
                      showWithoutCourses: false,
                      createdAfter: "",
                      createdBefore: "",
                    });
                  }}
                >
                  重置筛选
                </Button>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Download className="mr-2 h-4 w-4" />
                导入/导出
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setImportDialogOpen(true)}>
                <Upload className="mr-2 h-4 w-4" />
                导入课程类型
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExportData}>
                <Download className="mr-2 h-4 w-4" />
                导出课程类型
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleDownloadTemplate()}>
                <FileText className="mr-2 h-4 w-4" />
                下载导入模板
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <div className="flex rounded-md border">
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              className="rounded-r-none"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
              <span className="sr-only">列表视图</span>
            </Button>
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              className="rounded-l-none"
              onClick={() => setViewMode("grid")}
            >
              <Grid className="h-4 w-4" />
              <span className="sr-only">网格视图</span>
            </Button>
          </div>

          <Button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('主添加按钮被点击');
              handleAddType();
            }}
          >
            <Plus className="mr-2 h-4 w-4" />
            添加课程类型
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <Card>
          <CardContent className="p-3">
            <div className="flex flex-col gap-4 md:flex-row">
              <div className="w-full md:w-1/3">
                <Input
                  placeholder="搜索课程类型名称或描述"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>
              <div className="flex flex-1 flex-wrap gap-4">
                <Select 
                  value={statusFilter} 
                  onValueChange={handleStatusFilterChange}
                >
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="课程类型状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="active">启用</SelectItem>
                    <SelectItem value="inactive">停用</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={filterOptions.showWithCourses ? "with" : filterOptions.showWithoutCourses ? "without" : "all"}
                  onValueChange={(value) => {
                    if (value === "with") {
                      setFilterOptions(prev => ({...prev, showWithCourses: true, showWithoutCourses: false}));
                    } else if (value === "without") {
                      setFilterOptions(prev => ({...prev, showWithCourses: false, showWithoutCourses: true}));
                    } else {
                      setFilterOptions(prev => ({...prev, showWithCourses: false, showWithoutCourses: false}));
                    }
                  }}
                >
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="关联课程" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部</SelectItem>
                    <SelectItem value="with">有关联课程</SelectItem>
                    <SelectItem value="without">无关联课程</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="排序方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="display_order">按排序</SelectItem>
                    <SelectItem value="name">按名称</SelectItem>
                    <SelectItem value="created_at">按创建时间</SelectItem>
                    <SelectItem value="course_count">按课程数量</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="sm"
                  className="ml-auto"
                  onClick={() => {
                    setSearchQuery("");
                    setStatusFilter("all");
                    setSortBy("display_order");
                    setFilterOptions({
                      showWithCourses: false,
                      showWithoutCourses: false,
                      createdAfter: "",
                      createdBefore: "",
                    });
                  }}
                >
                  <X className="mr-2 h-4 w-4" />
                  清除筛选
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {viewMode === "list" ? (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">颜色</TableHead>
                  <TableHead>类型名称</TableHead>
                  <TableHead className="hidden md:table-cell">描述</TableHead>
                  <TableHead className="hidden md:table-cell">课程数量</TableHead>
                  <TableHead className="hidden lg:table-cell">排序</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="hidden md:table-cell">创建日期</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTypes.map((type) => (
                  <TableRow key={type.id}>
                    <TableCell>
                      <div className="h-6 w-6 rounded-full" style={{ backgroundColor: type.color }} />
                    </TableCell>
                    <TableCell className="font-medium">{type.name}</TableCell>
                    <TableCell className="hidden md:table-cell max-w-xs truncate">{type.description}</TableCell>
                    <TableCell className="hidden md:table-cell">
                      <Button variant="link" className="p-0 h-auto" onClick={() => handleViewDetails(type)}>
                        {type.courses} 个课程
                      </Button>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <Badge variant="outline">{type.displayOrder || 0}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={type.status === "active" ? "default" : "secondary"}>
                        {type.status === "active" ? "启用" : "停用"}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">{type.createdAt}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleViewDetails(type)}>
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditType(type)}>
                            <Pencil className="mr-2 h-4 w-4" />
                            编辑
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {type.status === "active" ? (
                            <DropdownMenuItem onClick={() => handleToggleStatus(type)} className="text-orange-600">
                              停用
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem onClick={() => handleToggleStatus(type)} className="text-green-600">
                              启用
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600"
                            disabled={type.courses > 0}
                            onClick={() => handleDeleteType(type)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTypes.map((type) => (
            <CourseTypeCard 
              key={type.id} 
              type={type} 
              onEdit={() => handleEditType(type)}
              onViewDetails={() => handleViewDetails(type)}
            />
          ))}
        </div>
      )}

      {/* 课程类型对话框 */}
      <AddEditCourseTypeDialog open={openAddEditDialog} onOpenChange={setOpenAddEditDialog} type={selectedType} onSave={handleSaveType} />

      <CourseTypeDetailDialog 
        open={openDetailDialog}
        onOpenChange={setOpenDetailDialog}
        type={selectedType}
      />

      {/* 删除确认对话框 */}
      <Dialog open={openDeleteDialog} onOpenChange={setOpenDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除课程类型 "{selectedType?.name}" 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {selectedType?.courses > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                  <div className="text-sm text-red-800">
                    <p className="font-medium">无法删除</p>
                    <p>该课程类型下还有 {selectedType.courses} 个关联课程，请先删除或转移这些课程。</p>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <h4 className="font-medium mb-2">课程类型信息：</h4>
              <div className="space-y-1 text-sm text-gray-600">
                <p><span className="font-medium">名称：</span>{selectedType?.name}</p>
                <p><span className="font-medium">描述：</span>{selectedType?.description}</p>
                <p><span className="font-medium">状态：</span>{selectedType?.status === "active" ? "启用" : "停用"}</p>
                <p><span className="font-medium">创建日期：</span>{selectedType?.createdAt}</p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenDeleteDialog(false)}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteType}
              disabled={selectedType?.courses > 0}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导入数据对话框 */}
      <Dialog open={importDialogOpen} onOpenChange={setImportDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>导入课程类型数据</DialogTitle>
            <DialogDescription>
              导入数据将与现有课程类型合并，相同ID的数据将被更新
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {!importFile ? (
              <div
                className="border-2 border-dashed rounded-lg p-8 text-center space-y-6 bg-muted/5 hover:bg-muted/10 transition-colors cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
                onDrop={handleFileDrop}
                onDragOver={handleDragOver}
                onDragEnter={handleDragOver}>
                <div className="flex justify-center">
                  <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                    <FileText className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium">选择文件导入</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    支持 CSV, Excel 格式文件，文件大小不超过 5MB
                  </p>
                  <p className="text-xs text-muted-foreground mt-2">
                    点击或拖拽文件到此处上传
                  </p>
                </div>
                <Button
                  variant="outline"
                  className="mt-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    fileInputRef.current?.click();
                  }}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  选择文件
                </Button>
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-blue-500" />
                    <span className="font-medium">{importFile.name}</span>
                    <Badge variant="outline">{(importFile.size / 1024).toFixed(2)} KB</Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setImportFile(null)
                      setImportPreview([])
                      if (fileInputRef.current) {
                        fileInputRef.current.value = ''
                      }
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="border rounded-lg">
                  <h3 className="p-3 border-b font-medium flex items-center">
                    <Tag className="h-4 w-4 mr-2" />
                    <span>数据预览</span>
                    <Badge className="ml-2">{importPreview.length} 条记录</Badge>
                  </h3>
                  <div className="p-3 overflow-auto max-h-[400px]">
                    <Table>
                      <TableHeader className="sticky top-0 bg-background">
                        <TableRow>
                          <TableHead className="w-16">ID</TableHead>
                          <TableHead>类型名称</TableHead>
                          <TableHead className="w-1/3">描述</TableHead>
                          <TableHead>状态</TableHead>
                          <TableHead>课程数</TableHead>
                          <TableHead>颜色</TableHead>
                          <TableHead>创建日期</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {importPreview.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell>{item.id}</TableCell>
                            <TableCell className="font-medium">{item.name}</TableCell>
                            <TableCell className="max-w-xs truncate" title={item.description}>{item.description}</TableCell>
                            <TableCell>
                              <Badge variant={item.status === "active" ? "default" : "secondary"}>
                                {item.status === "active" ? "启用" : "停用"}
                              </Badge>
                            </TableCell>
                            <TableCell>{item.courses}</TableCell>
                            <TableCell>
                              <div className="h-5 w-5 rounded-full" style={{ backgroundColor: item.color }} title={item.color} />
                            </TableCell>
                            <TableCell>{item.createdAt}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>

                {importErrors.length > 0 && (
                  <div className="border border-red-200 bg-red-50 rounded-lg p-3">
                    <h3 className="font-medium text-red-800 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-2" />
                      导入错误
                    </h3>
                    <ul className="mt-2 text-sm text-red-700 space-y-1">
                      {importErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setImportDialogOpen(false)}>
              取消
            </Button>
            <Button
              onClick={handleImportData}
              disabled={!importFile || importPreview.length === 0}
              className="gap-2"
            >
              <CheckCircle className="h-4 w-4" />
              确认导入 ({importPreview.length} 条)
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
