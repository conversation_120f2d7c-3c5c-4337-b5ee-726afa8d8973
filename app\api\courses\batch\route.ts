import { NextRequest, NextResponse } from 'next/server';
import { courseService } from '@/services/course-service';

// 模拟课程数据 
const mockCourses = [
  {
    id: 1,
    name: "瑜伽基础入门",
    description: "适合初学者的瑜伽基础课程，学习基本体式和呼吸方法",
    typeId: 1,
    typeName: "团课",
    typeColor: "#4285F4", 
    instructor: "张教练",
    capacity: 25,
    enrolled: 18,
    duration: 60,
    price: 50,
    status: "active",
    createdAt: "2023-05-15",
    updatedAt: "2023-06-10"
  },
  {
    id: 2,
    name: "高级流瑜伽",
    description: "连贯流畅的瑜伽序列，提升力量和灵活性",
    typeId: 2,
    typeName: "小班课",
    typeColor: "#34A853",
    instructor: "李教练",
    capacity: 10,
    enrolled: 8,
    duration: 75,
    price: 80,
    status: "active",
    createdAt: "2023-05-20",
    updatedAt: "2023-06-15"
  },
  {
    id: 3,
    name: "瑜伽私教定制",
    description: "根据个人需求定制的一对一瑜伽私教课程",
    typeId: 4,
    typeName: "私教课",
    typeColor: "#EA4335",
    instructor: "王教练",
    capacity: 1,
    enrolled: 1,
    duration: 90,
    price: 300,
    status: "active",
    createdAt: "2023-06-01",
    updatedAt: "2023-06-20"
  },
  {
    id: 4,
    name: "高温瑜伽体验",
    description: "在38℃环境下进行的热瑜伽课程，促进排汗和新陈代谢",
    typeId: 6,
    typeName: "热瑜伽",
    typeColor: "#FF9800",
    instructor: "赵教练",
    capacity: 15,
    enrolled: 0,
    duration: 60,
    price: 120,
    status: "inactive",
    createdAt: "2023-06-05",
    updatedAt: "2023-06-25"
  },
  {
    id: 5,
    name: "RYT200瑜伽导师培训",
    description: "国际认证的瑜伽教练培训课程",
    typeId: 5,
    typeName: "教培课",
    typeColor: "#9C27B0",
    instructor: "陈教练",
    capacity: 12,
    enrolled: 10,
    duration: 120,
    price: 12000,
    status: "active",
    createdAt: "2023-06-10",
    updatedAt: "2023-06-30"
  },
  {
    id: 6,
    name: "普拉提器械课程",
    description: "使用专业器械辅助的普拉提课程",
    typeId: 9,
    typeName: "器械普拉提",
    typeColor: "#607D8B",
    instructor: "钱教练",
    capacity: 8,
    enrolled: 5,
    duration: 60,
    price: 150,
    status: "active",
    createdAt: "2023-06-15",
    updatedAt: "2023-07-05"
  }
];

export async function POST(request: NextRequest) {
  try {
    const { action, ids, data } = await request.json();
    
    // 基本验证
    if (!action || !ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { code: 400, msg: '请提供有效的操作和课程ID列表' },
        { status: 400 }
      );
    }
    
    let result;
    
    switch (action) {
      case 'updateStatus':
        if (!data || !data.status) {
          return NextResponse.json(
            { code: 400, msg: '请提供要更新的状态' },
            { status: 400 }
          );
        }
        result = courseService.batchUpdateStatus(ids, data.status);
        break;
        
      case 'updateType':
        if (!data || !data.typeId || !data.typeName) {
          return NextResponse.json(
            { code: 400, msg: '请提供要更新的课程类型信息' },
            { status: 400 }
          );
        }
        result = courseService.batchUpdateType(
          ids, 
          data.typeId, 
          data.typeName, 
          data.typeColor || '#cccccc'
        );
        break;
        
      case 'delete':
        result = courseService.batchDelete(ids);
        break;
        
      default:
        return NextResponse.json(
          { code: 400, msg: '不支持的操作类型' },
          { status: 400 }
        );
    }
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: result
    });
  } catch (error) {
    console.error('批量操作课程错误:', error);
    return NextResponse.json(
      { code: 500, msg: '操作失败', error: String(error) },
      { status: 500 }
    );
  }
} 