"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { useToast } from "@/hooks/use-toast"
import {
  CreditCard,
  Calendar as CalendarIcon,
  User,
  Search,
  Tag,
  Plus,
  Minus,
  Clock,
  CalendarRange,
  Wallet,
  BarChart4,
  CheckCircle2,
  X,
  Sparkles
} from "lucide-react"

// 模拟会员卡类型数据
const cardTypes = [
  { id: "CT001", name: "瑜伽年卡", type: "期限卡", color: "#4CAF50", price: 3600, validDays: 365, isTrialCard: false },
  { id: "CT002", name: "瑜伽季卡", type: "期限卡", color: "#2196F3", price: 1200, validDays: 90, isTrialCard: false },
  { id: "CT003", name: "瑜伽月卡", type: "期限卡", color: "#FF9800", price: 450, validDays: 30, isTrialCard: false },
  { id: "CT004", name: "私教次卡", type: "次卡", color: "#9C27B0", price: 2000, validDays: 180, count: 20, isTrialCard: false },
  { id: "CT005", name: "团课次卡", type: "次卡", color: "#E91E63", price: 1500, validDays: 180, count: 30, isTrialCard: false },
  { id: "CT006", name: "瑜伽体验卡", type: "期限卡", color: "#E91E63", price: 199, validDays: 15, isTrialCard: true },
  { id: "CT007", name: "储值卡", type: "储值卡", color: "#607D8B", price: 1000, validDays: 365, value: 1200, isTrialCard: false },
]

// 模拟会员数据
const members = [
  { id: "M001", name: "张三", phone: "13800138001", avatar: "/avatars/01.png", level: "金卡会员" },
  { id: "M002", name: "李四", phone: "13800138002", avatar: "/avatars/02.png", level: "普通会员" },
  { id: "M003", name: "王五", phone: "13800138003", avatar: "/avatars/03.png", level: "银卡会员" },
  { id: "M004", name: "赵六", phone: "13800138004", avatar: "/avatars/04.png", level: "普通会员" },
  { id: "M005", name: "钱七", phone: "13800138005", avatar: "/avatars/05.png", level: "铜卡会员" },
]

interface AddMemberCardInstanceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function AddMemberCardInstanceDialog({
  open,
  onOpenChange,
  onSuccess
}: AddMemberCardInstanceDialogProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedMember, setSelectedMember] = useState<any>(null)
  const [selectedCardType, setSelectedCardType] = useState<any>(null)
  const [formData, setFormData] = useState({
    price: "",
    actualPrice: "",
    startDate: new Date(),
    endDate: null as Date | null,
    paymentMethod: "wechat",
    isAutoRenew: false,
    isActivateNow: true,
    activationType: "immediate", // 立即开卡、特定时间开卡、首次预约开卡、首次上课日期开卡
    cardNumber: "", // 卡号
    isReplacement: false, // 补办老卡
    referrer: "", // 推荐人
    bonusPoints: "", // 赠送积分
    bonusDays: "", // 优惠活动天数
    coupon: "none", // 优惠券
    notes: "",
    tags: [] as string[],
  })

  // 当选择卡类型时，自动设置价格、计算结束日期和生成卡号
  useEffect(() => {
    if (selectedCardType) {
      // 生成卡号：当前日期 + 卡类型ID + 随机数
      const today = new Date();
      const dateStr = today.getFullYear().toString().slice(-2) +
                     (today.getMonth() + 1).toString().padStart(2, '0') +
                     today.getDate().toString().padStart(2, '0');
      const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      const cardNumber = dateStr + selectedCardType.id.slice(-3) + randomNum;

      setFormData(prev => ({
        ...prev,
        price: selectedCardType.price.toString(),
        actualPrice: selectedCardType.price.toString(),
        cardNumber: cardNumber,
        endDate: selectedCardType.validDays ? new Date(prev.startDate.getTime() + selectedCardType.validDays * 24 * 60 * 60 * 1000) : null
      }))
    }
  }, [selectedCardType, formData.startDate])

  // 当开始日期变化时，重新计算结束日期
  useEffect(() => {
    if (selectedCardType && selectedCardType.validDays) {
      setFormData(prev => ({
        ...prev,
        endDate: new Date(prev.startDate.getTime() + selectedCardType.validDays * 24 * 60 * 60 * 1000)
      }))
    }
  }, [formData.startDate, selectedCardType])

  // 过滤会员搜索结果
  const filteredMembers = searchTerm
    ? members.filter(member =>
        member.name.includes(searchTerm) ||
        member.phone.includes(searchTerm) ||
        member.id.includes(searchTerm)
      )
    : []

  // 处理表单字段变化
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 处理标签切换
  const handleTagToggle = (tag: string) => {
    setFormData(prev => {
      const tags = [...prev.tags]
      const index = tags.indexOf(tag)
      if (index > -1) {
        tags.splice(index, 1)
      } else {
        tags.push(tag)
      }
      return { ...prev, tags }
    })
  }

  // 提交表单
  const handleSubmit = () => {
    if (!selectedMember) {
      toast({
        title: "请选择会员",
        description: "必须选择一个会员才能创建会员卡",
        variant: "destructive",
      })
      return
    }

    if (!selectedCardType) {
      toast({
        title: "请选择会员卡类型",
        description: "必须选择一个会员卡类型",
        variant: "destructive",
      })
      return
    }

    // 验证必填字段
    if (!formData.cardNumber) {
      toast({
        title: "请输入卡号",
        description: "卡号是必填项",
        variant: "destructive",
      })
      return
    }

    if (!formData.price || !formData.actualPrice) {
      toast({
        title: "请输入价格信息",
        description: "卡售价和实收金额是必填项",
        variant: "destructive",
      })
      return
    }

    // 这里应该是API调用来创建会员卡
    // 模拟API调用
    setTimeout(() => {
      toast({
        title: "会员卡创建成功",
        description: `已为 ${selectedMember.name} 创建 ${selectedCardType.name}`,
      })

      // 重置表单
      setSelectedMember(null)
      setSelectedCardType(null)
      setFormData({
        price: "",
        actualPrice: "",
        startDate: new Date(),
        endDate: null,
        paymentMethod: "wechat",
        isAutoRenew: false,
        isActivateNow: true,
        activationType: "immediate",
        cardNumber: "",
        isReplacement: false,
        referrer: "",
        bonusPoints: "",
        bonusDays: "",
        coupon: "none",
        notes: "",
        tags: [],
      })
      setActiveTab("basic")

      // 关闭对话框
      onOpenChange(false)

      // 调用成功回调
      if (onSuccess) {
        onSuccess()
      }
    }, 1000)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>添加会员卡名称</DialogTitle>
          <DialogDescription>
            为会员添加新的会员卡，填写必要信息并设置会员卡参数。
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">
              <User className="mr-2 h-4 w-4" />
              选择会员
            </TabsTrigger>
            <TabsTrigger value="cardType">
              <CreditCard className="mr-2 h-4 w-4" />
              选择卡类型
            </TabsTrigger>
            <TabsTrigger value="settings" disabled={!selectedMember || !selectedCardType}>
              <CalendarRange className="mr-2 h-4 w-4" />
              设置参数
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索会员（姓名、手机号或ID）"
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {selectedMember ? (
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-base">已选择会员</CardTitle>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setSelectedMember(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                        {selectedMember.avatar ? (
                          <img
                            src={selectedMember.avatar}
                            alt={selectedMember.name}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <User className="h-6 w-6 text-primary" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-medium">{selectedMember.name}</h3>
                        <p className="text-sm text-muted-foreground">{selectedMember.phone}</p>
                        <Badge variant="outline" className="mt-1">{selectedMember.level}</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : searchTerm ? (
                <div className="border rounded-md divide-y">
                  {filteredMembers.length > 0 ? (
                    filteredMembers.map(member => (
                      <div
                        key={member.id}
                        className="p-3 flex items-center gap-3 hover:bg-accent cursor-pointer"
                        onClick={() => {
                          setSelectedMember(member)
                          setSearchTerm("")
                        }}
                      >
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                          {member.avatar ? (
                            <img
                              src={member.avatar}
                              alt={member.name}
                              className="w-full h-full rounded-full object-cover"
                            />
                          ) : (
                            <User className="h-5 w-5 text-primary" />
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium">{member.name}</h3>
                          <p className="text-sm text-muted-foreground">{member.phone}</p>
                        </div>
                        <Badge variant="outline" className="ml-auto">{member.level}</Badge>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-muted-foreground">
                      未找到匹配的会员
                    </div>
                  )}
                </div>
              ) : (
                <div className="border rounded-md p-8 text-center text-muted-foreground">
                  请输入会员姓名、手机号或ID进行搜索
                </div>
              )}

              <div className="flex justify-between items-center">
                <Button
                  variant="outline"
                  onClick={() => router.push("/members/add")}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  新建会员
                </Button>

                <Button
                  onClick={() => setActiveTab("cardType")}
                  disabled={!selectedMember}
                >
                  下一步
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="cardType" className="space-y-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {cardTypes.map(cardType => (
                <Card
                  key={cardType.id}
                  className={cn(
                    "cursor-pointer transition-all hover:border-primary",
                    selectedCardType?.id === cardType.id ? "border-2 border-primary" : ""
                  )}
                  onClick={() => setSelectedCardType(cardType)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: cardType.color }}
                      ></div>
                      <CardTitle className="text-base">{cardType.name}</CardTitle>
                      {cardType.isTrialCard && (
                        <Badge variant="outline" className="bg-pink-50 text-pink-700 border-pink-200">
                          <Sparkles className="mr-1 h-3 w-3" />
                          体验卡
                        </Badge>
                      )}
                    </div>
                    <CardDescription>{cardType.type}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">价格</span>
                        <span className="font-medium">¥{cardType.price}</span>
                      </div>

                      {cardType.type === "期限卡" && (
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">有效期</span>
                          <span>{cardType.validDays}天</span>
                        </div>
                      )}

                      {cardType.type === "次卡" && (
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">次数</span>
                          <span>{cardType.count}次</span>
                        </div>
                      )}

                      {cardType.type === "储值卡" && (
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">储值金额</span>
                          <span>¥{cardType.value}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="flex justify-between items-center mt-4">
              <Button
                variant="outline"
                onClick={() => setActiveTab("basic")}
              >
                上一步
              </Button>

              <Button
                onClick={() => setActiveTab("settings")}
                disabled={!selectedCardType}
              >
                下一步
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4 py-4">
            {selectedMember && selectedCardType && (
              <div className="space-y-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <Card className="flex-1">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">会员信息</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                          {selectedMember.avatar ? (
                            <img
                              src={selectedMember.avatar}
                              alt={selectedMember.name}
                              className="w-full h-full rounded-full object-cover"
                            />
                          ) : (
                            <User className="h-5 w-5 text-primary" />
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium">{selectedMember.name}</h3>
                          <p className="text-sm text-muted-foreground">{selectedMember.phone}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="flex-1">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">卡类型信息</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: selectedCardType.color }}
                        ></div>
                        <h3 className="font-medium">{selectedCardType.name}</h3>
                        <Badge variant="outline">{selectedCardType.type}</Badge>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="space-y-6">
                  {/* 卡信息部分 */}
                  <div className="space-y-4 border p-4 rounded-md bg-gray-50">
                    <h3 className="font-medium">卡信息</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="cardNumber">
                          卡号 <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="cardNumber"
                          value={formData.cardNumber}
                          onChange={(e) => handleInputChange("cardNumber", e.target.value)}
                          placeholder="请输入卡号"
                        />
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label htmlFor="isReplacement" className="flex-1">补办老卡</Label>
                          <Switch
                            id="isReplacement"
                            checked={formData.isReplacement}
                            onCheckedChange={(checked) => handleInputChange("isReplacement", checked)}
                          />
                        </div>
                        {formData.isReplacement && (
                          <p className="text-xs text-muted-foreground">补办老卡将继承原卡的使用记录和余额</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>开卡方式</Label>
                      <RadioGroup
                        value={formData.activationType}
                        onValueChange={(value) => handleInputChange("activationType", value)}
                        className="flex flex-wrap gap-4"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="immediate" id="immediate" />
                          <Label htmlFor="immediate">立即开卡</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="specific_date" id="specific_date" />
                          <Label htmlFor="specific_date">特定时间开卡</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="first_reservation" id="first_reservation" />
                          <Label htmlFor="first_reservation">首次预约开卡</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="first_class" id="first_class" />
                          <Label htmlFor="first_class">首次上课日期开卡</Label>
                        </div>
                      </RadioGroup>
                    </div>

                    {formData.activationType === "specific_date" && (
                      <div className="space-y-2">
                        <Label>开卡日期</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {formData.startDate ? (
                                format(formData.startDate, "yyyy年MM月dd日", { locale: zhCN })
                              ) : (
                                <span>选择日期</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={formData.startDate}
                              onSelect={(date) => handleInputChange("startDate", date || new Date())}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    )}

                    {selectedCardType.type === "期限卡" && (
                      <div className="space-y-2">
                        <Label>结束日期</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {formData.endDate ? (
                                format(formData.endDate, "yyyy年MM月dd日", { locale: zhCN })
                              ) : (
                                <span>选择日期</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={formData.endDate || undefined}
                              onSelect={(date) => handleInputChange("endDate", date)}
                              initialFocus
                              disabled={(date) => date < formData.startDate}
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="referrer">推荐人</Label>
                      <Input
                        id="referrer"
                        value={formData.referrer}
                        onChange={(e) => handleInputChange("referrer", e.target.value)}
                        placeholder="请输入推荐人姓名/手机号"
                      />
                    </div>
                  </div>

                  {/* 收款信息部分 */}
                  <div className="space-y-4 border p-4 rounded-md bg-gray-50">
                    <h3 className="font-medium">收款信息</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="price">
                          卡售价 <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                          <span className="absolute left-3 top-2.5">¥</span>
                          <Input
                            id="price"
                            type="number"
                            className="pl-7"
                            value={formData.price}
                            onChange={(e) => handleInputChange("price", e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bonusDays">优惠活动 (天)</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            id="bonusDays"
                            type="number"
                            value={formData.bonusDays}
                            onChange={(e) => handleInputChange("bonusDays", e.target.value)}
                            placeholder="赠送天数"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            className="whitespace-nowrap"
                            onClick={() => {
                              if (formData.bonusDays && formData.endDate) {
                                const newEndDate = new Date(formData.endDate);
                                newEndDate.setDate(newEndDate.getDate() + parseInt(formData.bonusDays));
                                handleInputChange("endDate", newEndDate);
                              }
                            }}
                          >
                            加赠
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bonusPoints">赠送积分 (分)</Label>
                        <Input
                          id="bonusPoints"
                          type="number"
                          value={formData.bonusPoints}
                          onChange={(e) => handleInputChange("bonusPoints", e.target.value)}
                          placeholder="请输入积分"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="coupon">优惠券</Label>
                        <Select
                          value={formData.coupon}
                          onValueChange={(value) => handleInputChange("coupon", value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="请选择" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">无</SelectItem>
                            <SelectItem value="new_member">新会员优惠券</SelectItem>
                            <SelectItem value="holiday">节日优惠券</SelectItem>
                            <SelectItem value="renewal">续卡优惠券</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="actualPrice">
                          实收金额 <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                          <span className="absolute left-3 top-2.5">¥</span>
                          <Input
                            id="actualPrice"
                            type="number"
                            className="pl-7"
                            value={formData.actualPrice}
                            onChange={(e) => handleInputChange("actualPrice", e.target.value)}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>支付方式</Label>
                        <RadioGroup
                          value={formData.paymentMethod}
                          onValueChange={(value) => handleInputChange("paymentMethod", value)}
                          className="flex flex-wrap gap-4"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="wechat" id="wechat" />
                            <Label htmlFor="wechat">微信支付</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="alipay" id="alipay" />
                            <Label htmlFor="alipay">支付宝</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="cash" id="cash" />
                            <Label htmlFor="cash">现金</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="card" id="card" />
                            <Label htmlFor="card">刷卡</Label>
                          </div>
                        </RadioGroup>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>标签</Label>
                  <div className="flex flex-wrap gap-2">
                    {["VIP", "老客户", "新客户", "潜在客户", "活跃会员", "回流会员"].map(tag => (
                      <Badge
                        key={tag}
                        variant={formData.tags.includes(tag) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => handleTagToggle(tag)}
                      >
                        {formData.tags.includes(tag) && (
                          <CheckCircle2 className="mr-1 h-3 w-3" />
                        )}
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">备注</Label>
                  <Textarea
                    id="notes"
                    placeholder="添加备注信息"
                    value={formData.notes}
                    onChange={(e) => handleInputChange("notes", e.target.value)}
                  />
                </div>

                <div className="flex justify-between items-center">
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab("cardType")}
                  >
                    上一步
                  </Button>

                  <Button onClick={handleSubmit}>
                    创建会员卡
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
