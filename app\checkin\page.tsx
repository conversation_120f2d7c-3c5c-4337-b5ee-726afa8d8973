"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Calendar, Search, QrCode, CheckCircle, X, Clock, AlertCircle, Camera, UserCheck, FileText, MoreHorizontal } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePickerZh } from "@/components/date-picker-zh"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { format } from "date-fns"

// 模拟课程数据
const todayCourses = [
  {
    id: "C001",
    name: "哈他瑜伽初级",
    time: "10:00-11:30",
    teacher: "王教练",
    venue: "1号瑜伽室",
    capacity: 15,
    booked: 12,
    checkedIn: 8,
  },
  {
    id: "C002",
    name: "阴瑜伽",
    time: "14:00-15:30",
    teacher: "李教练",
    venue: "2号瑜伽室",
    capacity: 12,
    booked: 10,
    checkedIn: 7,
  },
  {
    id: "C003",
    name: "流瑜伽中级",
    time: "16:00-17:30",
    teacher: "张教练",
    venue: "1号瑜伽室",
    capacity: 18,
    booked: 15,
    checkedIn: 0,
    status: "upcoming",
  },
  {
    id: "C004",
    name: "私教课",
    time: "18:00-19:00",
    teacher: "赵教练",
    venue: "私教室",
    capacity: 1,
    booked: 1,
    checkedIn: 0,
    status: "upcoming",
  },
]

// 模拟预约记录数据
const bookingRecords = [
  {
    id: "B001",
    courseId: "C001",
    courseName: "哈他瑜伽初级",
    courseTime: "10:00-11:30",
    memberId: "M001",
    memberName: "张三",
    memberPhone: "13812345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    membershipCard: "瑜伽年卡",
    bookingTime: "2024-04-05 14:23",
    checkinStatus: "checked_in",
    checkinTime: "09:50",
    checkinMethod: "扫码",
    operatorId: "S001",
    operatorName: "客服小王",
  },
  {
    id: "B002",
    courseId: "C001",
    courseName: "哈他瑜伽初级",
    courseTime: "10:00-11:30",
    memberId: "M002",
    memberName: "李四",
    memberPhone: "13987654321",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    membershipCard: "瑜伽季卡",
    bookingTime: "2024-04-06 09:15",
    checkinStatus: "checked_in",
    checkinTime: "09:55",
    checkinMethod: "人工",
    operatorId: "S002",
    operatorName: "客服小李",
  },
  {
    id: "B003",
    courseId: "C001",
    courseName: "哈他瑜伽初级",
    courseTime: "10:00-11:30",
    memberId: "M003",
    memberName: "王五",
    memberPhone: "13765432198",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    membershipCard: "瑜伽月卡",
    bookingTime: "2024-04-06 16:42",
    checkinStatus: "pending",
    checkinTime: null,
    checkinMethod: null,
    operatorId: null,
    operatorName: null,
  },
  {
    id: "B004",
    courseId: "C002",
    courseName: "阴瑜伽",
    courseTime: "14:00-15:30",
    memberId: "M001",
    memberName: "张三",
    memberPhone: "13812345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    membershipCard: "瑜伽年卡",
    bookingTime: "2024-04-04 10:30",
    checkinStatus: "checked_in",
    checkinTime: "13:50",
    checkinMethod: "扫码",
    operatorId: null,
    operatorName: null,
  },
  {
    id: "B005",
    courseId: "C002",
    courseName: "阴瑜伽",
    courseTime: "14:00-15:30",
    memberId: "M004",
    memberName: "赵六",
    memberPhone: "13654321987",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    membershipCard: "私教次卡",
    bookingTime: "2024-04-03 15:20",
    checkinStatus: "late",
    checkinTime: "14:15",
    checkinMethod: "人工",
    operatorId: "S002",
    operatorName: "客服小李",
  },
]

export default function CheckinPage() {
  // 状态管理
  const [activeTab, setActiveTab] = useState("courses")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCourse, setSelectedCourse] = useState<any>(null)
  const [showCheckinDialog, setShowCheckinDialog] = useState(false)
  const [showQrCodeDialog, setShowQrCodeDialog] = useState(false)
  const [showCameraDialog, setShowCameraDialog] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [manualCheckinData, setManualCheckinData] = useState({
    memberPhone: "",
    memberName: "",
    membershipCardId: "",
    courseId: "",
    note: "",
  })
  const [selectedMember, setSelectedMember] = useState<any>(null)

  // 获取课程的预约记录
  const getBookingsForCourse = (courseId: string) => {
    return bookingRecords.filter(booking => booking.courseId === courseId);
  };

  // 获取课程的签到率
  const getCheckinRate = (course: any) => {
    if (course.booked === 0) return "0%";
    return `${Math.round((course.checkedIn / course.booked) * 100)}%`;
  };

  // 处理查看课程
  const handleViewCourse = (course: any) => {
    setSelectedCourse(course);
    setShowCheckinDialog(true);
  };

  // 处理扫码签到
  const handleScanQrCode = () => {
    setShowQrCodeDialog(true);
  };

  // 处理人脸识别签到
  const handleFaceRecognition = () => {
    setShowCameraDialog(true);
  };

  // 处理会员搜索
  const handleMemberSearch = () => {
    if (!manualCheckinData.memberPhone) {
      toast({
        title: "请输入手机号",
        description: "请输入会员手机号进行搜索",
        variant: "destructive",
      });
      return;
    }

    // 实际应用中，这里应该调用API搜索会员
    // 这里仅做模拟
    // 模拟找到会员
    setSelectedMember({
      id: "M001",
      name: "张三",
      phone: manualCheckinData.memberPhone,
      avatar: "/placeholder.svg?height=40&width=40",
      membershipCards: [
        { id: "MC001", name: "瑜伽年卡", expiry: "2025-04-30", status: "active" },
        { id: "MC002", name: "私教次卡", expiry: "2024-10-15", status: "active", remainingTimes: 8 }
      ]
    });

    setManualCheckinData({
      ...manualCheckinData,
      memberName: "张三",
    });
  };

  // 处理手动签到
  const handleManualCheckin = () => {
    if (!selectedMember) {
      toast({
        title: "请先搜索会员",
        description: "请先搜索并选择会员进行签到",
        variant: "destructive",
      });
      return;
    }

    if (!manualCheckinData.courseId) {
      toast({
        title: "请选择课程",
        description: "请选择要签到的课程",
        variant: "destructive",
      });
      return;
    }

    // 实际应用中，这里应该调用API进行签到
    // 这里仅做模拟
    toast({
      title: "签到成功",
      description: `会员 ${selectedMember.name} 已成功签到`,
    });

    // 重置表单
    setManualCheckinData({
      memberPhone: "",
      memberName: "",
      membershipCardId: "",
      courseId: "",
      note: "",
    });
    setSelectedMember(null);
  };

  // 处理会员签到
  const handleMemberCheckin = (booking: any) => {
    // 实际应用中，这里应该调用API进行签到
    // 这里仅做模拟
    toast({
      title: "签到成功",
      description: `${booking.memberName} 已成功签到`,
    });
  };

  // 获取签到状态文本
  const getCheckinStatusText = (status: string) => {
    switch (status) {
      case "checked_in":
        return "已签到";
      case "pending":
        return "未签到";
      case "late":
        return "迟到";
      case "absent":
        return "缺席";
      default:
        return "未知";
    }
  };

  // 获取签到状态标签颜色
  const getCheckinStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "checked_in":
        return "success";
      case "pending":
        return "secondary";
      case "late":
        return "warning";
      case "absent":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">签到与核销管理</h1>
          <p className="text-muted-foreground">
            管理课程签到和会员核销
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.location.href = "/booking-records"}>
            <Calendar className="mr-2 h-4 w-4" />
            预约记录
          </Button>
          <Button onClick={() => window.location.href = "/booking-rules"}>
            <FileText className="mr-2 h-4 w-4" />
            预约规则
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="courses">今日课程</TabsTrigger>
          <TabsTrigger value="manual">手动签到</TabsTrigger>
        </TabsList>

        {/* 今日课程 */}
        <TabsContent value="courses" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>今日课程</CardTitle>
              <CardDescription>
                {format(selectedDate, 'yyyy年MM月dd日')} 的课程安排和签到情况
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <DatePickerZh
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    placeholder="选择日期"
                  />
                  <div className="relative flex-1 max-w-sm">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索课程名称/教练..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {todayCourses.map((course) => (
                    <Card key={course.id} className="overflow-hidden">
                      <CardHeader className="bg-muted/50 pb-3">
                        <div className="flex justify-between items-center">
                          <CardTitle className="text-base">{course.name}</CardTitle>
                          <Badge variant={course.status === "upcoming" ? "secondary" : "default"}>
                            {course.status === "upcoming" ? "即将开始" : "进行中"}
                          </Badge>
                        </div>
                        <CardDescription>
                          {course.time} | {course.teacher} | {course.venue}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-3">
                        <div className="flex justify-between items-center">
                          <div className="space-y-1">
                            <div className="text-sm flex items-center gap-2">
                              <span className="text-muted-foreground">预约人数:</span>
                              <span>{course.booked}/{course.capacity}</span>
                            </div>
                            <div className="text-sm flex items-center gap-2">
                              <span className="text-muted-foreground">签到人数:</span>
                              <span>{course.checkedIn}/{course.booked}</span>
                            </div>
                            <div className="text-sm flex items-center gap-2">
                              <span className="text-muted-foreground">签到率:</span>
                              <span>{getCheckinRate(course)}</span>
                            </div>
                          </div>
                          <Button onClick={() => handleViewCourse(course)}>
                            查看签到
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 手动签到 */}
        <TabsContent value="manual" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>手动签到</CardTitle>
              <CardDescription>
                为会员进行手动签到或核销
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="col-span-1 md:col-span-2">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">会员信息</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="member-phone">会员搜索</Label>
                      <div className="flex space-x-2">
                        <Input
                          id="member-phone"
                          placeholder="输入会员手机号"
                          value={manualCheckinData.memberPhone}
                          onChange={(e) => setManualCheckinData({
                            ...manualCheckinData,
                            memberPhone: e.target.value
                          })}
                        />
                        <Button variant="outline" onClick={handleMemberSearch}>
                          <Search className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" onClick={handleScanQrCode}>
                          <QrCode className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" onClick={handleFaceRecognition}>
                          <Camera className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {selectedMember && (
                      <div className="space-y-2">
                        <Label>已选会员</Label>
                        <div className="flex items-center space-x-2 p-2 border rounded-md">
                          <Avatar>
                            <AvatarImage src={selectedMember.avatar} alt={selectedMember.name} />
                            <AvatarFallback>{selectedMember.name.slice(0, 1)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{selectedMember.name}</div>
                            <div className="text-xs text-muted-foreground">{selectedMember.phone}</div>
                          </div>
                        </div>
                      </div>
                    )}

                    {selectedMember && (
                      <div className="space-y-2">
                        <Label htmlFor="membership-card">会员卡选择</Label>
                        <Select
                          value={manualCheckinData.membershipCardId}
                          onValueChange={(value) => setManualCheckinData({
                            ...manualCheckinData,
                            membershipCardId: value
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择会员卡" />
                          </SelectTrigger>
                          <SelectContent>
                            {selectedMember.membershipCards.map((card: any) => (
                              <SelectItem key={card.id} value={card.id}>
                                {card.name} {card.remainingTimes ? `(剩余${card.remainingTimes}次)` : ''}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="course-select">选择课程</Label>
                      <Select
                        value={manualCheckinData.courseId}
                        onValueChange={(value) => setManualCheckinData({
                          ...manualCheckinData,
                          courseId: value
                        })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择要签到的课程" />
                        </SelectTrigger>
                        <SelectContent>
                          {todayCourses.map((course) => (
                            <SelectItem key={course.id} value={course.id}>
                              {course.name} ({course.time})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="checkin-note">签到备注</Label>
                      <Textarea
                        id="checkin-note"
                        placeholder="添加签到备注..."
                        value={manualCheckinData.note}
                        onChange={(e) => setManualCheckinData({
                          ...manualCheckinData,
                          note: e.target.value
                        })}
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      onClick={handleManualCheckin}
                      disabled={!selectedMember || !manualCheckinData.courseId || !manualCheckinData.membershipCardId}
                    >
                      <UserCheck className="mr-2 h-4 w-4" />
                      确认签到
                    </Button>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">签到方式</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Button variant="outline" className="w-full justify-start" onClick={handleScanQrCode}>
                      <QrCode className="mr-2 h-4 w-4" />
                      扫码签到
                    </Button>
                    <Button variant="outline" className="w-full justify-start" onClick={handleFaceRecognition}>
                      <Camera className="mr-2 h-4 w-4" />
                      人脸识别
                    </Button>
                    <Button variant="outline" className="w-full justify-start" onClick={() => setActiveTab("courses")}>
                      <Calendar className="mr-2 h-4 w-4" />
                      课程签到
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 课程签到对话框 */}
      <Dialog open={showCheckinDialog} onOpenChange={setShowCheckinDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{selectedCourse?.name} - 签到管理</DialogTitle>
            <DialogDescription>
              {selectedCourse?.time} | {selectedCourse?.teacher} | {selectedCourse?.venue} | 签到率: {selectedCourse && getCheckinRate(selectedCourse)}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Input
                  placeholder="搜索会员姓名/手机号..."
                  className="w-64"
                />
                <Button variant="outline" size="sm">
                  <Search className="mr-2 h-4 w-4" />
                  搜索
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={handleScanQrCode}>
                  <QrCode className="mr-2 h-4 w-4" />
                  扫码签到
                </Button>
                <Button variant="outline" size="sm" onClick={handleFaceRecognition}>
                  <Camera className="mr-2 h-4 w-4" />
                  人脸识别
                </Button>
              </div>
            </div>

            {selectedCourse && getBookingsForCourse(selectedCourse.id).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                当前课程没有预约记录
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>会员信息</TableHead>
                    <TableHead>会员卡</TableHead>
                    <TableHead>预约时间</TableHead>
                    <TableHead>签到状态</TableHead>
                    <TableHead>签到时间</TableHead>
                    <TableHead>签到方式</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {selectedCourse && getBookingsForCourse(selectedCourse.id).map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={booking.memberAvatar} alt={booking.memberName} />
                            <AvatarFallback>{booking.memberName.slice(0, 1)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{booking.memberName}</div>
                            <div className="text-xs text-muted-foreground">{booking.memberPhone}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{booking.membershipCard}</TableCell>
                      <TableCell>{booking.bookingTime}</TableCell>
                      <TableCell>
                        <Badge variant={getCheckinStatusBadgeVariant(booking.checkinStatus)}>
                          {getCheckinStatusText(booking.checkinStatus)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {booking.checkinTime || "-"}
                      </TableCell>
                      <TableCell>
                        {booking.checkinMethod || "-"}
                      </TableCell>
                      <TableCell className="text-right">
                        {booking.checkinStatus === "pending" ? (
                          <Button
                            size="sm"
                            onClick={() => handleMemberCheckin(booking)}
                          >
                            签到
                          </Button>
                        ) : (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>操作</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => {
                                toast({
                                  title: "签到已取消",
                                  description: `${booking.memberName} 的签到已取消`,
                                });
                              }}>
                                <X className="mr-2 h-4 w-4" />
                                取消签到
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                toast({
                                  title: "已标记为迟到",
                                  description: `${booking.memberName} 已被标记为迟到`,
                                });
                              }}>
                                <Clock className="mr-2 h-4 w-4" />
                                标记为迟到
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                toast({
                                  title: "已标记为缺席",
                                  description: `${booking.memberName} 已被标记为缺席`,
                                });
                              }}>
                                <AlertCircle className="mr-2 h-4 w-4" />
                                标记为缺席
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCheckinDialog(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 扫码签到对话框 */}
      <Dialog open={showQrCodeDialog} onOpenChange={setShowQrCodeDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>扫码签到</DialogTitle>
            <DialogDescription>
              请会员出示二维码进行扫描
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-md">
              <QrCode className="h-16 w-16 text-muted-foreground mb-4" />
              <p className="text-center text-muted-foreground">
                请将摄像头对准会员的二维码<br />
                系统将自动识别并签到
              </p>
            </div>

            <div className="flex justify-center">
              <Button variant="outline" onClick={() => {
                toast({
                  title: "签到成功",
                  description: "会员已成功签到",
                });
                setShowQrCodeDialog(false);
              }}>
                模拟扫码成功
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowQrCodeDialog(false)}>
              取消
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 人脸识别对话框 */}
      <Dialog open={showCameraDialog} onOpenChange={setShowCameraDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>人脸识别签到</DialogTitle>
            <DialogDescription>
              请会员面对摄像头进行人脸识别
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="flex flex-col items-center justify-center p-6 border-2 border-dashed rounded-md bg-muted/50 h-64">
              <Camera className="h-16 w-16 text-muted-foreground mb-4" />
              <p className="text-center text-muted-foreground">
                请会员面对摄像头<br />
                保持面部在框内并保持不动
              </p>
            </div>

            <div className="flex justify-center">
              <Button variant="outline" onClick={() => {
                toast({
                  title: "识别成功",
                  description: "会员张三已成功签到",
                });
                setShowCameraDialog(false);
              }}>
                模拟识别成功
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCameraDialog(false)}>
              取消
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
