import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Eye, RefreshCw, Search, Download, Filter } from "lucide-react"
import { Textarea } from "@/components/ui/textarea"

export default function SecurityPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">安全设置</h1>
      </div>

      <Tabs defaultValue="password" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
          <TabsTrigger value="password">密码策略</TabsTrigger>
          <TabsTrigger value="login">登录安全</TabsTrigger>
          <TabsTrigger value="access">访问控制</TabsTrigger>
          <TabsTrigger value="logs">安全日志</TabsTrigger>
        </TabsList>

        <TabsContent value="password" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>密码策略</CardTitle>
              <CardDescription>设置系统密码安全策略</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="password-complexity">密码复杂度要求</Label>
                    <p className="text-sm text-muted-foreground">要求密码包含大小写字母、数字和特殊字符</p>
                  </div>
                  <Switch id="password-complexity" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password-length">最小密码长度</Label>
                  <Input id="password-length" type="number" defaultValue="8" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="password-expiry">密码过期策略</Label>
                    <p className="text-sm text-muted-foreground">要求用户定期更改密码</p>
                  </div>
                  <Switch id="password-expiry" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password-expiry-days">密码有效期（天）</Label>
                  <Input id="password-expiry-days" type="number" defaultValue="90" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="password-history">密码历史记录</Label>
                    <p className="text-sm text-muted-foreground">防止用户重复使用最近使用过的密码</p>
                  </div>
                  <Switch id="password-history" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password-history-count">记住最近密码数量</Label>
                  <Input id="password-history-count" type="number" defaultValue="5" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="first-login-change">首次登录强制修改密码</Label>
                    <p className="text-sm text-muted-foreground">新用户首次登录时必须修改初始密码</p>
                  </div>
                  <Switch id="first-login-change" defaultChecked />
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">密码重置</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="self-reset">允许自助重置密码</Label>
                    <p className="text-sm text-muted-foreground">允许用户通过邮箱或手机自助重置密码</p>
                  </div>
                  <Switch id="self-reset" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reset-method">重置方式</Label>
                  <Select defaultValue="both">
                    <SelectTrigger>
                      <SelectValue placeholder="选择重置方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">仅邮箱验证</SelectItem>
                      <SelectItem value="phone">仅手机验证</SelectItem>
                      <SelectItem value="both">邮箱和手机验证</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="reset-link-expiry">重置链接有效期</Label>
                    <p className="text-sm text-muted-foreground">密码重置链接的有效时间</p>
                  </div>
                  <Select defaultValue="24">
                    <SelectTrigger className="w-[100px]">
                      <SelectValue placeholder="选择时长" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1小时</SelectItem>
                      <SelectItem value="6">6小时</SelectItem>
                      <SelectItem value="12">12小时</SelectItem>
                      <SelectItem value="24">24小时</SelectItem>
                      <SelectItem value="48">48小时</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>保存设置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="login" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>登录安全</CardTitle>
              <CardDescription>配置系统登录安全策略</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="login-attempts">登录失败限制</Label>
                    <p className="text-sm text-muted-foreground">限制连续登录失败次数</p>
                  </div>
                  <Switch id="login-attempts" defaultChecked />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="max-attempts">最大尝试次数</Label>
                    <Input id="max-attempts" type="number" defaultValue="5" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lockout-duration">锁定时长（分钟）</Label>
                    <Input id="lockout-duration" type="number" defaultValue="30" />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="two-factor-auth">双因素认证</Label>
                    <p className="text-sm text-muted-foreground">要求管理员使用双因素认证</p>
                  </div>
                  <Switch id="two-factor-auth" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="two-factor-method">双因素认证方式</Label>
                  <Select defaultValue="app">
                    <SelectTrigger>
                      <SelectValue placeholder="选择认证方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="app">认证应用（推荐）</SelectItem>
                      <SelectItem value="sms">短信验证码</SelectItem>
                      <SelectItem value="email">邮箱验证码</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="session-timeout">会话超时</Label>
                    <p className="text-sm text-muted-foreground">长时间不活动后自动登出</p>
                  </div>
                  <Switch id="session-timeout" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timeout-minutes">超时时间（分钟）</Label>
                  <Input id="timeout-minutes" type="number" defaultValue="30" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="concurrent-sessions">并发会话限制</Label>
                    <p className="text-sm text-muted-foreground">限制同一账号的并发登录数量</p>
                  </div>
                  <Switch id="concurrent-sessions" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-sessions">最大并发会话数</Label>
                  <Input id="max-sessions" type="number" defaultValue="1" />
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">登录通知</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="unusual-login">异常登录通知</Label>
                    <p className="text-sm text-muted-foreground">检测到异常登录行为时发送通知</p>
                  </div>
                  <Switch id="unusual-login" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="admin-login">管理员登录通知</Label>
                    <p className="text-sm text-muted-foreground">管理员账号登录时发送通知</p>
                  </div>
                  <Switch id="admin-login" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notification-email">通知邮箱</Label>
                  <Input id="notification-email" defaultValue="<EMAIL>" />
                  <p className="text-xs text-muted-foreground">多个邮箱请用逗号分隔</p>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>保存设置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="access" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>访问控制</CardTitle>
              <CardDescription>配置系统访问控制策略</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">IP访问控制</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="ip-restriction">IP访问限制</Label>
                    <p className="text-sm text-muted-foreground">限制可访问管理后台的IP地址</p>
                  </div>
                  <Switch id="ip-restriction" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="allowed-ips">允许的IP地址</Label>
                  <Textarea
                    id="allowed-ips"
                    placeholder="每行输入一个IP地址或IP段，例如：*********** 或 ***********/24"
                    className="min-h-[100px]"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="block-countries">地区访问限制</Label>
                    <p className="text-sm text-muted-foreground">限制特定地区的访问</p>
                  </div>
                  <Switch id="block-countries" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="blocked-countries">禁止访问的地区</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择地区" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">无限制</SelectItem>
                      <SelectItem value="us">美国</SelectItem>
                      <SelectItem value="eu">欧盟</SelectItem>
                      <SelectItem value="asia">亚洲</SelectItem>
                      <SelectItem value="custom">自定义</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">时间访问控制</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="time-restriction">时间访问限制</Label>
                    <p className="text-sm text-muted-foreground">限制系统的访问时间</p>
                  </div>
                  <Switch id="time-restriction" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="start-time">开始时间</Label>
                    <Input id="start-time" type="time" defaultValue="09:00" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="end-time">结束时间</Label>
                    <Input id="end-time" type="time" defaultValue="18:00" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="allowed-days">允许访问的日期</Label>
                  <div className="flex flex-wrap gap-2">
                    {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Switch id={`day-${index}`} defaultChecked={index < 5} />
                        <Label htmlFor={`day-${index}`}>{day}</Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">设备访问控制</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="device-restriction">设备访问限制</Label>
                    <p className="text-sm text-muted-foreground">限制可访问系统的设备类型</p>
                  </div>
                  <Switch id="device-restriction" />
                </div>

                <div className="space-y-2">
                  <Label>允许的设备类型</Label>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch id="desktop" defaultChecked />
                      <Label htmlFor="desktop">桌面电脑</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="laptop" defaultChecked />
                      <Label htmlFor="laptop">笔记本电脑</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="tablet" defaultChecked />
                      <Label htmlFor="tablet">平板电脑</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="mobile" />
                      <Label htmlFor="mobile">移动设备</Label>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="browser-restriction">浏览器访问限制</Label>
                    <p className="text-sm text-muted-foreground">限制可访问系统的浏览器类型</p>
                  </div>
                  <Switch id="browser-restriction" />
                </div>

                <div className="space-y-2">
                  <Label>允许的浏览器类型</Label>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch id="chrome" defaultChecked />
                      <Label htmlFor="chrome">Chrome</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="firefox" defaultChecked />
                      <Label htmlFor="firefox">Firefox</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="edge" defaultChecked />
                      <Label htmlFor="edge">Edge</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="safari" defaultChecked />
                      <Label htmlFor="safari">Safari</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="other-browser" />
                      <Label htmlFor="other-browser">其他浏览器</Label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>保存设置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>安全日志</CardTitle>
              <CardDescription>查看系统安全相关的操作日志</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="relative flex-1 mr-4">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="搜索日志内容、IP地址、用户名..." className="pl-8" />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    导出
                  </Button>
                  <Button variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>时间</TableHead>
                      <TableHead>用户</TableHead>
                      <TableHead>操作</TableHead>
                      <TableHead>IP地址</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>详情</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>2025-03-28 10:15:22</TableCell>
                      <TableCell>admin</TableCell>
                      <TableCell>登录系统</TableCell>
                      <TableCell>***********00</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          成功
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-28 10:20:15</TableCell>
                      <TableCell>admin</TableCell>
                      <TableCell>修改安全设置</TableCell>
                      <TableCell>***********00</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          成功
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-28 09:15:10</TableCell>
                      <TableCell>user1</TableCell>
                      <TableCell>登录失败</TableCell>
                      <TableCell>***********01</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-red-600 border-red-600">
                          失败
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-28 09:16:22</TableCell>
                      <TableCell>user1</TableCell>
                      <TableCell>登录系统</TableCell>
                      <TableCell>***********01</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          成功
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-28 09:30:45</TableCell>
                      <TableCell>user1</TableCell>
                      <TableCell>重置密码</TableCell>
                      <TableCell>***********01</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          成功
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-27 16:45:33</TableCell>
                      <TableCell>unknown</TableCell>
                      <TableCell>异常访问尝试</TableCell>
                      <TableCell>************</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-red-600 border-red-600">
                          阻止
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-27 14:20:18</TableCell>
                      <TableCell>admin</TableCell>
                      <TableCell>添加用户</TableCell>
                      <TableCell>***********00</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          成功
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-27 11:05:42</TableCell>
                      <TableCell>admin</TableCell>
                      <TableCell>修改权限</TableCell>
                      <TableCell>***********00</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          成功
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">显示 1-8 条，共 125 条记录</div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" disabled>
                    上一页
                  </Button>
                  <Button variant="outline" size="sm">
                    下一页
                  </Button>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">日志设置</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="log-login">记录登录活动</Label>
                    <p className="text-sm text-muted-foreground">记录所有用户的登录和登出活动</p>
                  </div>
                  <Switch id="log-login" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="log-security">记录安全设置变更</Label>
                    <p className="text-sm text-muted-foreground">记录对系统安全设置的所有修改</p>
                  </div>
                  <Switch id="log-security" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="log-user">记录用户管理操作</Label>
                    <p className="text-sm text-muted-foreground">记录用户添加、修改、删除等操作</p>
                  </div>
                  <Switch id="log-user" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="log-failed">记录失败的操作</Label>
                    <p className="text-sm text-muted-foreground">记录所有失败的登录和操作尝试</p>
                  </div>
                  <Switch id="log-failed" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="log-retention">日志保留期限（天）</Label>
                  <Input id="log-retention" type="number" defaultValue="90" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="log-export">自动导出日志</Label>
                    <p className="text-sm text-muted-foreground">定期自动导出安全日志</p>
                  </div>
                  <Switch id="log-export" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="export-frequency">导出频率</Label>
                  <Select defaultValue="weekly">
                    <SelectTrigger>
                      <SelectValue placeholder="选择导出频率" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">每日</SelectItem>
                      <SelectItem value="weekly">每周</SelectItem>
                      <SelectItem value="monthly">每月</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="export-path">导出路径</Label>
                  <Input id="export-path" defaultValue="/logs/security" />
                </div>
              </div>

              <div className="flex justify-end">
                <Button>保存设置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

