"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  AlertCircle,
  CheckCircle,
  Download,
  FileText,
  Printer,
  Send,
  Truck,
  X,
  CreditCard
} from "lucide-react"
// 注意：Yoga 图标在 lucide-react 中不存在，需要使用其他图标或自定义组件

interface OrderBatchOperationsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedOrders: string[];
  onApplyOperation: (operation: string) => void;
}

export function ShopOrderBatchOperationsDialog({
  open,
  onOpenChange,
  selectedOrders,
  onApplyOperation
}: OrderBatchOperationsDialogProps) {
  const [activeTab, setActiveTab] = useState("status")
  const [statusOperation, setStatusOperation] = useState("mark_completed")
  const [deliveryStatus, setDeliveryStatus] = useState("shipping")
  const [logisticsCompany, setLogisticsCompany] = useState("")
  const [exportFormat, setExportFormat] = useState("excel")
  const [notifyMethod, setNotifyMethod] = useState("sms")
  const [notifyContent, setNotifyContent] = useState("")
  const [remark, setRemark] = useState("")

  // 瑜伽商城特有的批量操作状态
  const [verificationOperation, setVerificationOperation] = useState("verify")
  const [courseOperation, setCourseOperation] = useState("activate")

  // 处理应用操作
  const handleApplyOperation = () => {
    let operation = "";

    switch (activeTab) {
      case "status":
        operation = statusOperation === "mark_completed" ? "标记为已完成" :
                   statusOperation === "mark_cancelled" ? "标记为已取消" :
                   "标记为处理中";
        break;
      case "delivery":
        operation = `更新物流状态为 ${deliveryStatus}`;
        break;
      case "export":
        operation = `导出订单为 ${exportFormat}`;
        break;
      case "notify":
        operation = `通过 ${notifyMethod} 通知客户`;
        break;
      case "remark":
        operation = "更新订单备注";
        break;
      case "verification":
        operation = verificationOperation === "verify" ? "批量核销" :
                   verificationOperation === "generate" ? "生成核销码" :
                   "重置核销状态";
        break;
      case "course":
        operation = courseOperation === "activate" ? "激活课程" :
                   courseOperation === "extend" ? "延长有效期" :
                   "重置课程状态";
        break;
    }

    onApplyOperation(operation);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>瑜伽商城批量操作</DialogTitle>
          <DialogDescription>
            已选择 {selectedOrders.length} 个订单进行批量操作
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="status">状态更新</TabsTrigger>
            <TabsTrigger value="delivery">物流更新</TabsTrigger>
            <TabsTrigger value="verification">核销管理</TabsTrigger>
            <TabsTrigger value="course">课程管理</TabsTrigger>
            <TabsTrigger value="export">导出订单</TabsTrigger>
            <TabsTrigger value="notify">客户通知</TabsTrigger>
            <TabsTrigger value="remark">批量备注</TabsTrigger>
          </TabsList>

          <TabsContent value="status" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">更新订单状态</CardTitle>
                <CardDescription>
                  为所选订单批量更新状态
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup value={statusOperation} onValueChange={setStatusOperation}>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="mark_completed" id="mark_completed" />
                    <Label htmlFor="mark_completed" className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      标记为已完成
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="mark_processing" id="mark_processing" />
                    <Label htmlFor="mark_processing" className="flex items-center">
                      <Truck className="h-4 w-4 mr-2 text-blue-500" />
                      标记为处理中
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="mark_cancelled" id="mark_cancelled" />
                    <Label htmlFor="mark_cancelled" className="flex items-center">
                      <X className="h-4 w-4 mr-2 text-red-500" />
                      标记为已取消
                    </Label>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="delivery" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">更新物流信息</CardTitle>
                <CardDescription>
                  为所选订单批量更新物流状态
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="delivery-status">物流状态</Label>
                  <Select value={deliveryStatus} onValueChange={setDeliveryStatus}>
                    <SelectTrigger id="delivery-status">
                      <SelectValue placeholder="选择物流状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">待发货</SelectItem>
                      <SelectItem value="shipping">配送中</SelectItem>
                      <SelectItem value="delivered">已送达</SelectItem>
                      <SelectItem value="returned">已退回</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="logistics-company">物流公司</Label>
                  <Select value={logisticsCompany} onValueChange={setLogisticsCompany}>
                    <SelectTrigger id="logistics-company">
                      <SelectValue placeholder="选择物流公司" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sf">顺丰速运</SelectItem>
                      <SelectItem value="yt">圆通速递</SelectItem>
                      <SelectItem value="zt">中通快递</SelectItem>
                      <SelectItem value="yd">韵达快递</SelectItem>
                      <SelectItem value="jd">京东物流</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="pt-2 text-sm text-muted-foreground">
                  <AlertCircle className="h-4 w-4 inline-block mr-1" />
                  注意: 此操作将批量更新所选订单的物流状态，但不会自动生成物流单号。
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 瑜伽商城特有：核销管理标签页 */}
          <TabsContent value="verification" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">核销管理</CardTitle>
                <CardDescription>
                  为所选虚拟商品订单进行批量核销操作
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup value={verificationOperation} onValueChange={setVerificationOperation}>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="verify" id="verify" />
                    <Label htmlFor="verify" className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      批量核销
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="generate" id="generate" />
                    <Label htmlFor="generate" className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-blue-500" />
                      生成核销码
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="reset" id="reset" />
                    <Label htmlFor="reset" className="flex items-center">
                      <X className="h-4 w-4 mr-2 text-red-500" />
                      重置核销状态
                    </Label>
                  </div>
                </RadioGroup>

                <div className="mt-4 pt-4 border-t">
                  <div className="space-y-2">
                    <Label htmlFor="verification-remark">核销备注</Label>
                    <Textarea
                      id="verification-remark"
                      placeholder="添加核销相关备注信息"
                      className="min-h-[100px]"
                      value={remark}
                      onChange={(e) => setRemark(e.target.value)}
                    />
                  </div>
                </div>

                <div className="pt-2 text-sm text-muted-foreground mt-4">
                  <AlertCircle className="h-4 w-4 inline-block mr-1" />
                  注意: 此操作仅适用于虚拟商品、课程和会员卡等需要核销的订单。
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 瑜伽商城特有：课程管理标签页 */}
          <TabsContent value="course" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center">
                  <FileText className="h-4 w-4 mr-2" />
                  课程管理
                </CardTitle>
                <CardDescription>
                  为所选课程订单进行批量操作
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup value={courseOperation} onValueChange={setCourseOperation}>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="activate" id="activate" />
                    <Label htmlFor="activate" className="flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      激活课程
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="extend" id="extend" />
                    <Label htmlFor="extend" className="flex items-center">
                      <CreditCard className="h-4 w-4 mr-2 text-blue-500" />
                      延长有效期
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="reset" id="reset_course" />
                    <Label htmlFor="reset_course" className="flex items-center">
                      <X className="h-4 w-4 mr-2 text-red-500" />
                      重置课程状态
                    </Label>
                  </div>
                </RadioGroup>

                <div className="mt-4 pt-4 border-t">
                  <div className="space-y-2">
                    <Label htmlFor="course-days">延长天数</Label>
                    <Select disabled={courseOperation !== "extend"}>
                      <SelectTrigger id="course-days">
                        <SelectValue placeholder="选择延长天数" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="7">7天</SelectItem>
                        <SelectItem value="15">15天</SelectItem>
                        <SelectItem value="30">30天</SelectItem>
                        <SelectItem value="90">90天</SelectItem>
                        <SelectItem value="180">180天</SelectItem>
                        <SelectItem value="365">365天</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="pt-2 text-sm text-muted-foreground mt-4">
                  <AlertCircle className="h-4 w-4 inline-block mr-1" />
                  注意: 此操作仅适用于瑜伽课程相关订单。
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">导出订单</CardTitle>
                <CardDescription>
                  将所选订单导出为文件
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup value={exportFormat} onValueChange={setExportFormat}>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="excel" id="excel" />
                    <Label htmlFor="excel" className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-green-600" />
                      Excel 格式 (.xlsx)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 mb-3">
                    <RadioGroupItem value="csv" id="csv" />
                    <Label htmlFor="csv" className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-blue-600" />
                      CSV 格式 (.csv)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pdf" id="pdf" />
                    <Label htmlFor="pdf" className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-red-600" />
                      PDF 格式 (.pdf)
                    </Label>
                  </div>
                </RadioGroup>

                <div className="flex items-center mt-4 pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    导出 {selectedOrders.length} 个订单
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notify" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">客户通知</CardTitle>
                <CardDescription>
                  向所选订单的客户发送通知
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="notify-method">通知方式</Label>
                  <Select value={notifyMethod} onValueChange={setNotifyMethod}>
                    <SelectTrigger id="notify-method">
                      <SelectValue placeholder="选择通知方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sms">短信</SelectItem>
                      <SelectItem value="email">邮件</SelectItem>
                      <SelectItem value="wechat">微信</SelectItem>
                      <SelectItem value="app">APP推送</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notify-content">通知内容</Label>
                  <Textarea
                    id="notify-content"
                    value={notifyContent}
                    onChange={(e) => setNotifyContent(e.target.value)}
                    placeholder="输入要发送给客户的通知内容"
                    className="min-h-[120px]"
                  />
                </div>

                <div className="flex items-center mt-2">
                  <Button variant="outline" className="w-full">
                    <Send className="h-4 w-4 mr-2" />
                    发送通知
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="remark" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">批量添加备注</CardTitle>
                <CardDescription>
                  为所选订单批量添加备注信息
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="remark">备注内容</Label>
                  <Textarea
                    id="remark"
                    value={remark}
                    onChange={(e) => setRemark(e.target.value)}
                    placeholder="输入要添加的备注内容"
                    className="min-h-[150px]"
                  />
                </div>

                <div className="pt-2 text-sm text-muted-foreground">
                  <AlertCircle className="h-4 w-4 inline-block mr-1" />
                  注意: 此操作将覆盖所选订单的现有备注信息。
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleApplyOperation}>
            应用操作
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
