"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import {
  Calendar,
  Clock,
  MapPin,
  User,
  Users,
  Star,
  MessageSquare,
  ChevronLeft,
  ChevronRight,
  Info,
  FileText,
  Heart,
  Share2,
  Bookmark,
  AlertCircle,
  CheckCircle,
  ArrowRight,
  MapIcon,
  Cloud,
  CloudRain,
  Sun,
  Umbrella
} from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/components/ui/use-toast"

// 模拟课程数据
const courseData = {
  id: "YG001",
  name: "基础瑜伽入门",
  type: "基础课程",
  description: "适合初学者的基础瑜伽课程，帮助学员掌握基本的瑜伽姿势和呼吸技巧，提高身体柔韧性和平衡能力。",
  longDescription: "这是一门专为瑜伽初学者设计的入门课程，无需任何瑜伽基础。课程将从最基本的瑜伽姿势开始，循序渐进地引导学员进入瑜伽的世界。通过系统的教学，学员将学习正确的呼吸方法、基础体式和简单的冥想技巧，为进一步学习瑜伽打下坚实基础。\n\n课程特点：\n- 循序渐进，适合零基础学员\n- 强调正确姿势，预防受伤\n- 结合呼吸与动作，提高身体觉知\n- 放松身心，缓解压力\n\n每节课后，教练会根据学员情况给予个性化建议，帮助学员在家也能安全练习。",
  duration: 90,
  capacity: 15,
  price: 128,
  memberPrice: 98,
  rating: 4.8,
  reviewCount: 156,
  image: "/courses/yoga-basic.jpg",
  coach: {
    id: "C001",
    name: "李教练",
    avatar: "/avatars/coach-01.png",
    title: "高级瑜伽教练",
    experience: "8年教学经验",
    certification: "国际瑜伽联盟认证教练",
    introduction: "李教练专注于初学者瑜伽教学，教学风格温和细致，特别注重体式的正确性和安全性，深受初学者喜爱。",
    rating: 4.9,
    reviewCount: 320
  },
  venue: {
    id: "V001",
    name: "1号瑜伽室",
    capacity: 20,
    equipment: "瑜伽垫、瑜伽砖、瑜伽带、瑜伽球",
    location: "一楼东侧",
    image: "/venues/yoga-room-1.jpg"
  },
  schedule: [
    {
      date: "2023-05-16",
      weekday: "星期五",
      timeSlots: [
        {
          id: "TS001",
          startTime: "10:00",
          endTime: "11:30",
          duration: 90,
          booked: 12,
          capacity: 15,
          status: "可预约",
          coach: "李教练",
          venue: "1号瑜伽室",
          type: "基础课程"
        },
        {
          id: "TS002",
          startTime: "14:00",
          endTime: "15:30",
          duration: 90,
          booked: 8,
          capacity: 10,
          status: "可预约",
          coach: "王教练",
          venue: "2号瑜伽室",
          type: "进阶课程"
        },
        {
          id: "TS003",
          startTime: "16:00",
          endTime: "17:00",
          duration: 60,
          booked: 15,
          capacity: 15,
          status: "已满",
          coach: "张教练",
          venue: "3号瑜伽室",
          type: "放松课程"
        },
        {
          id: "TS004",
          startTime: "18:30",
          endTime: "19:30",
          duration: 60,
          booked: 6,
          capacity: 10,
          status: "可预约",
          coach: "刘教练",
          venue: "1号瑜伽室",
          type: "专项课程"
        }
      ]
    },
    {
      date: "2023-05-17",
      weekday: "星期六",
      timeSlots: [
        {
          id: "TS005",
          startTime: "09:00",
          endTime: "10:30",
          duration: 90,
          booked: 14,
          capacity: 15,
          status: "可预约",
          coach: "李教练",
          venue: "1号瑜伽室",
          type: "基础课程"
        },
        {
          id: "TS006",
          startTime: "11:00",
          endTime: "12:30",
          duration: 90,
          booked: 10,
          capacity: 10,
          status: "已满",
          coach: "王教练",
          venue: "2号瑜伽室",
          type: "进阶课程"
        }
      ]
    }
  ],
  suitableFor: ["瑜伽初学者", "想要改善身体柔韧性的人", "希望缓解压力的人", "想要学习正确瑜伽姿势的人"],
  benefits: ["提高身体柔韧性", "改善姿势和平衡", "缓解压力和焦虑", "增强核心力量", "提高身体觉知"],
  notes: ["请穿着舒适的运动服装", "课前1小时避免大量进食", "自备水和毛巾", "初学者请提前告知教练身体状况"],
  relatedCourses: ["阴瑜伽放松", "流瑜伽入门", "哈他瑜伽进阶"]
};

export default function CourseDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("info")
  const [selectedDate, setSelectedDate] = useState(0)
  const [showBookingDialog, setShowBookingDialog] = useState(false)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<any>(null)

  // 处理预约
  const handleBooking = (timeSlot: any) => {
    setSelectedTimeSlot(timeSlot);
    setShowBookingDialog(true);
  };

  // 确认预约
  const confirmBooking = () => {
    // 实际应用中，这里应该调用API进行预约
    toast({
      title: "预约成功",
      description: `已成功预约 ${courseData.name} ${selectedTimeSlot.startTime}-${selectedTimeSlot.endTime} 的课程`,
    });
    setShowBookingDialog(false);
  };

  // 获取时间段状态样式
  const getTimeSlotStatusStyle = (status: string, booked: number, capacity: number) => {
    if (status === "已满") {
      return {
        badge: <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">已满</Badge>,
        button: <Button variant="outline" disabled>已满</Button>
      };
    } else if (booked / capacity >= 0.8) {
      return {
        badge: <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">剩余: {capacity - booked}人</Badge>,
        button: <Button>预约</Button>
      };
    } else {
      return {
        badge: <Badge className="bg-green-100 text-green-800 hover:bg-green-100">剩余: {capacity - booked}人</Badge>,
        button: <Button>预约</Button>
      };
    }
  };

  // 获取课程类型样式
  const getCourseTypeStyle = (type: string) => {
    switch(type) {
      case "基础课程":
        return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      case "进阶课程":
        return "bg-purple-100 text-purple-800 hover:bg-purple-100";
      case "放松课程":
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case "专项课程":
        return "bg-amber-100 text-amber-800 hover:bg-amber-100";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" onClick={() => router.back()} className="mr-2">
          <ChevronLeft className="h-4 w-4 mr-1" />
          返回
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">{courseData.name}</h1>
        <Badge className={`ml-3 ${getCourseTypeStyle(courseData.type)}`}>{courseData.type}</Badge>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardContent className="p-0">
              <img
                src={courseData.image || "/placeholder.jpg"}
                alt={courseData.name}
                className="w-full h-[300px] object-cover rounded-t-lg"
              />
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      <Star className="h-5 w-5 fill-amber-400 text-amber-400" />
                      <span className="ml-1 font-medium">{courseData.rating}</span>
                      <span className="text-muted-foreground ml-1">({courseData.reviewCount}条评价)</span>
                    </div>
                    <div className="flex items-center ml-4">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="ml-1 text-muted-foreground">{courseData.duration}分钟</span>
                    </div>
                    <div className="flex items-center ml-4">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="ml-1 text-muted-foreground">限{courseData.capacity}人</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Bookmark className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <p className="text-muted-foreground">{courseData.description}</p>
                <div className="mt-4 flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">¥{courseData.memberPrice}</div>
                    <div className="text-sm text-muted-foreground">会员价 (原价: ¥{courseData.price})</div>
                  </div>
                  <Button size="lg">
                    立即预约
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="info">课程详情</TabsTrigger>
              <TabsTrigger value="coach">教练介绍</TabsTrigger>
              <TabsTrigger value="venue">场地信息</TabsTrigger>
              <TabsTrigger value="reviews">学员评价</TabsTrigger>
            </TabsList>

            <TabsContent value="info" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>课程详情</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium mb-2">课程介绍</h3>
                    <p className="text-muted-foreground whitespace-pre-line">{courseData.longDescription}</p>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">适合人群</h3>
                    <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                      {courseData.suitableFor.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">课程收益</h3>
                    <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                      {courseData.benefits.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">注意事项</h3>
                    <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                      {courseData.notes.map((item, index) => (
                        <li key={index}>{item}</li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="coach" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>教练介绍</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start gap-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={courseData.coach.avatar} alt={courseData.coach.name} />
                      <AvatarFallback>{courseData.coach.name.slice(0, 1)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium text-lg">{courseData.coach.name}</h3>
                      <p className="text-muted-foreground">{courseData.coach.title}</p>
                      <div className="flex items-center mt-1">
                        <Star className="h-4 w-4 fill-amber-400 text-amber-400" />
                        <span className="ml-1">{courseData.coach.rating}</span>
                        <span className="text-muted-foreground ml-1">({courseData.coach.reviewCount}条评价)</span>
                      </div>
                      <div className="mt-2 space-y-1 text-sm">
                        <div className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                          <span>{courseData.coach.experience}</span>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                          <span>{courseData.coach.certification}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">教练简介</h4>
                    <p className="text-muted-foreground">{courseData.coach.introduction}</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="venue" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>场地信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <img
                      src={courseData.venue.image || "/placeholder.jpg"}
                      alt={courseData.venue.name}
                      className="w-full h-[200px] object-cover rounded-lg"
                    />
                  </div>
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium">{courseData.venue.name}</h3>
                      <p className="text-muted-foreground">{courseData.venue.location}</p>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium">容量</h4>
                        <p className="text-muted-foreground">{courseData.venue.capacity}人</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium">设备</h4>
                        <p className="text-muted-foreground">{courseData.venue.equipment}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>学员评价</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-4">
                      <div className="text-4xl font-bold">{courseData.rating}</div>
                      <div>
                        <div className="flex">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-5 w-5 ${star <= Math.round(courseData.rating) ? "fill-amber-400 text-amber-400" : "text-gray-300"}`}
                            />
                          ))}
                        </div>
                        <div className="text-muted-foreground mt-1">{courseData.reviewCount}条评价</div>
                      </div>
                    </div>
                    <Button>写评价</Button>
                  </div>

                  <div className="space-y-4">
                    <div className="text-center text-muted-foreground">
                      <MessageSquare className="h-16 w-16 mx-auto mb-4 text-primary/40" />
                      <p>评价内容将在此显示</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-primary" />
                  课程安排
                </CardTitle>
                <div className="flex">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setSelectedDate(Math.max(0, selectedDate - 1))}
                    disabled={selectedDate === 0}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setSelectedDate(Math.min(courseData.schedule.length - 1, selectedDate + 1))}
                    disabled={selectedDate === courseData.schedule.length - 1}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                {courseData.schedule[selectedDate].date} {courseData.schedule[selectedDate].weekday}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {courseData.schedule[selectedDate].timeSlots.map((timeSlot) => {
                const statusStyle = getTimeSlotStatusStyle(timeSlot.status, timeSlot.booked, timeSlot.capacity);
                return (
                  <div key={timeSlot.id} className="border rounded-lg p-4 hover:border-primary transition-colors">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center">
                          <div className="text-lg font-medium">{timeSlot.startTime}</div>
                          <Badge className={`ml-2 ${getCourseTypeStyle(timeSlot.type)}`}>{timeSlot.type}</Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">{timeSlot.duration}分钟</div>
                      </div>
                      {statusStyle.badge}
                    </div>
                    <div className="mt-2 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        <span>共 {timeSlot.booked}/{timeSlot.capacity} 人</span>
                      </div>
                      <div className="flex items-center mt-1">
                        <MapPin className="h-3 w-3 mr-1" />
                        <span>{timeSlot.venue}</span>
                      </div>
                      <div className="flex items-center mt-1">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{timeSlot.startTime}-{timeSlot.endTime}</span>
                      </div>
                    </div>
                    <div className="mt-3 flex justify-end">
                      <Button
                        variant={timeSlot.status === "已满" ? "outline" : "default"}
                        disabled={timeSlot.status === "已满"}
                        onClick={() => handleBooking(timeSlot)}
                      >
                        {timeSlot.status === "已满" ? "已满" : "预约"}
                      </Button>
                    </div>
                  </div>
                );
              })}
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => router.push('/courses/schedule')}>
                查看更多排期
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>相关课程</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {courseData.relatedCourses.map((course, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="font-medium">{course}</div>
                  <Button variant="ghost" size="sm">
                    查看
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 预约对话框 */}
      <Dialog open={showBookingDialog} onOpenChange={setShowBookingDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>确认预约</DialogTitle>
            <DialogDescription>
              请确认以下课程预约信息
            </DialogDescription>
          </DialogHeader>

          {selectedTimeSlot && (
            <div className="py-4">
              <div className="rounded-lg border p-4 bg-muted/50">
                <h3 className="font-medium">{courseData.name}</h3>
                <div className="mt-2 space-y-1 text-sm">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{courseData.schedule[selectedDate].date} {courseData.schedule[selectedDate].weekday}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{selectedTimeSlot.startTime}-{selectedTimeSlot.endTime} ({selectedTimeSlot.duration}分钟)</span>
                  </div>
                  <div className="flex items-center">
                    <User className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{selectedTimeSlot.coach}</span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{selectedTimeSlot.venue}</span>
                  </div>
                </div>
              </div>

              <div className="mt-4 flex items-center">
                <AlertCircle className="h-4 w-4 text-amber-500 mr-2" />
                <span className="text-sm text-muted-foreground">预约成功后，如需取消请提前2小时操作</span>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBookingDialog(false)}>
              取消
            </Button>
            <Button onClick={confirmBooking}>
              确认预约
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
