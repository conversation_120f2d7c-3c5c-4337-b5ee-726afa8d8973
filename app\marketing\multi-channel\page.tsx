"use client"

import MultiChannelMarketing from "@/components/marketing/MultiChannelMarketing"

// 模拟用户订阅状态检查
// 实际应用中，这应该从API或状态管理中获取
const checkSubscriptionStatus = () => {
  // 假设用户已经订阅了多渠道营销工具
  // 在实际应用中，这应该是一个API调用或从状态管理中获取
  return true
}

export default function MarketingMultiChannelPage() {
  // 检查用户是否有权限访问完整功能
  const hasFullAccess = checkSubscriptionStatus()
  
  return (
    <MultiChannelMarketing 
      entrySource="marketing-center"
      hasFullAccess={hasFullAccess}
    />
  )
}
