"use client"

import { useState, useEffect, useRef } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Info } from "lucide-react"
import { RuleInfoButton } from "./rule-info-button"

// 模拟课程数据
const courseData = [
  {
    id: "c1",
    name: "空中瑜伽初级",
    type: "空中瑜伽",
    duration: 60,
    defaultCount: 1,
    defaultAmount: 100
  },
  {
    id: "c2",
    name: "空中瑜伽中级",
    type: "空中瑜伽",
    duration: 60,
    defaultCount: 1.5,
    defaultAmount: 150
  },
  {
    id: "c3",
    name: "空中瑜伽高级",
    type: "空中瑜伽",
    duration: 60,
    defaultCount: 2,
    defaultAmount: 200
  },
  {
    id: "c4",
    name: "哈他瑜伽",
    type: "垫上瑜伽",
    duration: 60,
    defaultCount: 1,
    defaultAmount: 80
  },
  {
    id: "c5",
    name: "流瑜伽",
    type: "垫上瑜伽",
    duration: 75,
    defaultCount: 1,
    defaultAmount: 100
  },
  {
    id: "c6",
    name: "阴瑜伽",
    type: "垫上瑜伽",
    duration: 75,
    defaultCount: 1,
    defaultAmount: 100
  },
  {
    id: "c7",
    name: "高空吊环",
    type: "空中瑜伽",
    duration: 60,
    defaultCount: 2,
    defaultAmount: 180
  },
  {
    id: "c8",
    name: "普拉提",
    type: "垫上瑜伽",
    duration: 60,
    defaultCount: 1,
    defaultAmount: 100
  },
  {
    id: "c9",
    name: "产后修复瑜伽",
    type: "特殊瑜伽",
    duration: 60,
    defaultCount: 2,
    defaultAmount: 150
  },
  {
    id: "c10",
    name: "孕妇瑜伽",
    type: "特殊瑜伽",
    duration: 60,
    defaultCount: 2,
    defaultAmount: 150
  },
  {
    id: "c11",
    name: "理疗瑜伽",
    type: "特殊瑜伽",
    duration: 60,
    defaultCount: 2,
    defaultAmount: 180
  }
]

// 课程分类
const courseCategories = [
  { id: "cat1", name: "空中瑜伽", color: "green" },
  { id: "cat2", name: "垫上瑜伽", color: "blue" },
  { id: "cat3", name: "特殊瑜伽", color: "red" }
]

interface CourseAssociationProps {
  cardType: "count" | "time" | "value"; // 会员卡类型：次数卡、时间卡、储值卡
  initialData?: {
    consumptionRule?: string;
    selectedCourses?: string[];
    courseSettings?: {[key: string]: {count: number, amount: number}};
    giftCount?: number;
    giftValue?: number;
  };
  onChange?: (data: {
    consumptionRule: string;
    selectedCourses: string[];
    courseSettings: {[key: string]: {count: number, amount: number}};
    giftCount: number;
    giftValue: number;
  }) => void;
}

export function CourseAssociation({ cardType, initialData, onChange }: CourseAssociationProps) {
  // 使用useRef记住初始值
  const initialDataRef = useRef(initialData);
  // 添加一个标志，标记是否是外部更新
  const isExternalUpdateRef = useRef(false);
  
  const [consumptionRule, setConsumptionRule] = useState(initialDataRef.current?.consumptionRule || "average")
  const [selectedCourses, setSelectedCourses] = useState<string[]>(initialDataRef.current?.selectedCourses || [])
  const [allSelected, setAllSelected] = useState(false)
  const [courseSettings, setCourseSettings] = useState<{[key: string]: {count: number, amount: number}}>(
    initialDataRef.current?.courseSettings || {}
  )
  const [giftCount, setGiftCount] = useState(initialDataRef.current?.giftCount || 0)
  const [giftValue, setGiftValue] = useState(initialDataRef.current?.giftValue || 1)

  // 初始化时检查是否全选
  useEffect(() => {
    if (selectedCourses.length === courseData.length) {
      setAllSelected(true)
    }
  }, [])
  
  // 处理initialData的变化，避免无限循环
  useEffect(() => {
    // 只有当initialData与当前状态明显不同时才更新
    const shouldUpdate = 
      initialData && 
      (JSON.stringify(initialData.selectedCourses) !== JSON.stringify(selectedCourses) ||
       JSON.stringify(initialData.courseSettings) !== JSON.stringify(courseSettings) ||
       initialData.consumptionRule !== consumptionRule ||
       initialData.giftCount !== giftCount ||
       initialData.giftValue !== giftValue);
    
    if (shouldUpdate) {
      // 使用一个标志避免在下一个渲染周期中触发onChange
      isExternalUpdateRef.current = true;
      
      if (initialData.consumptionRule) setConsumptionRule(initialData.consumptionRule);
      if (initialData.selectedCourses) setSelectedCourses(initialData.selectedCourses);
      if (initialData.courseSettings) setCourseSettings(initialData.courseSettings);
      if (initialData.giftCount !== undefined) setGiftCount(initialData.giftCount);
      if (initialData.giftValue !== undefined) setGiftValue(initialData.giftValue);
      
      // 更新全选状态
      if (initialData.selectedCourses && initialData.selectedCourses.length === courseData.length) {
        setAllSelected(true);
      } else {
        setAllSelected(false);
      }
      
      // 重置标志
      setTimeout(() => {
        isExternalUpdateRef.current = false;
      }, 0);
    }
  }, [initialData]);

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    setAllSelected(checked)
    if (checked) {
      setSelectedCourses(courseData.map(course => course.id))

      // 初始化所有课程的设置
      const newSettings: {[key: string]: {count: number, amount: number}} = {}
      courseData.forEach(course => {
        newSettings[course.id] = {
          count: course.defaultCount,
          amount: course.defaultAmount
        }
      })
      setCourseSettings({...courseSettings, ...newSettings})
    } else {
      setSelectedCourses([])
    }
  }

  // 处理单个课程选择
  const handleSelectCourse = (courseId: string, checked: boolean) => {
    if (checked) {
      // 添加课程
      setSelectedCourses([...selectedCourses, courseId])

      // 初始化课程设置
      const course = courseData.find(c => c.id === courseId)
      if (course) {
        setCourseSettings({
          ...courseSettings,
          [courseId]: {
            count: course.defaultCount,
            amount: course.defaultAmount
          }
        })
      }
    } else {
      // 移除课程
      setSelectedCourses(selectedCourses.filter(id => id !== courseId))
    }
  }

  // 更新课程设置
  const updateCourseSetting = (courseId: string, field: 'count' | 'amount', value: number) => {
    setCourseSettings({
      ...courseSettings,
      [courseId]: {
        ...courseSettings[courseId],
        [field]: value
      }
    })
  }

  // 获取消耗字段标签
  const getConsumptionLabel = () => {
    switch (cardType) {
      case "count":
        return "消耗次数";
      case "time":
        return "消耗天数";
      case "value":
        return "消耗金额";
      default:
        return "消耗值";
    }
  }

  // 当数据变化时通知父组件
  useEffect(() => {
    if (onChange && !isExternalUpdateRef.current) {
      // 使用ref防止无限循环
      const timer = setTimeout(() => {
        onChange({
          consumptionRule,
          selectedCourses,
          courseSettings,
          giftCount,
          giftValue
        });
      }, 0);
      return () => clearTimeout(timer);
    }
  }, [consumptionRule, selectedCourses, courseSettings, giftCount, giftValue]);

  return (
    <div className="space-y-4">
      {/* 隐藏消耗规则设置 */}
      {false && cardType !== "time" && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>消耗规则</Label>
            <RuleInfoButton
              ruleType={consumptionRule === "average" ? "average" :
                        consumptionRule === "fixed" ? "actual" :
                        consumptionRule === "custom" ? "custom" : "weighted"}
              variant="ghost"
            />
          </div>
          <Select value={consumptionRule} onValueChange={setConsumptionRule}>
            <SelectTrigger>
              <SelectValue placeholder="选择消耗规则" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="average">平均消耗（总价值平均分配）</SelectItem>
              <SelectItem value="fixed">固定消耗（每节课固定消耗）</SelectItem>
              <SelectItem value="custom">自定义消耗（每节课单独设置）</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-sm text-muted-foreground mt-1">
            {consumptionRule === "average" && "会员卡总价值将平均分配到每次课程消耗中"}
            {consumptionRule === "fixed" && "每节课程消耗固定的次数或金额"}
            {consumptionRule === "custom" && "可以为每节课程单独设置消耗的次数或金额"}
          </p>
        </div>
      )}

      {/* 隐藏赠送设置 */}
      {false && (
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="gift-count">赠送课时数</Label>
            <Input
              id="gift-count"
              type="number"
              placeholder="输入赠送课时数"
              value={giftCount}
              onChange={(e) => setGiftCount(Number(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">会员卡包含的额外赠送课时数</p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="gift-value">赠送课程价值系数</Label>
            <Input
              id="gift-value"
              type="number"
              placeholder="输入赠送课程价值系数"
              value={giftValue}
              onChange={(e) => setGiftValue(Number(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">赠送课程相对价值的计算系数，默认为1.0</p>
          </div>
        </div>
      )}

      <Separator className="my-4" />

      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Label>适用课程</Label>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="select-all"
              checked={allSelected}
              onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
            />
            <label
              htmlFor="select-all"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              全选
            </label>
          </div>
        </div>

        <div className="border rounded-md">
          {courseCategories.map((category) => (
            <div key={category.id} className="p-4 border-b last:border-b-0">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full bg-${category.color}-500 mr-2`}></div>
                  <h3 className="font-medium">{category.name}</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`select-category-${category.id}`}
                    checked={courseData
                      .filter(course => course.type === category.name)
                      .every(course => selectedCourses.includes(course.id))
                    }
                    onCheckedChange={(checked) => {
                      const coursesInCategory = courseData.filter(course => course.type === category.name);
                      if (checked) {
                        // 选中该分类下所有课程
                        const newSelectedCourses = [...selectedCourses];
                        const newSettings = {...courseSettings};

                        coursesInCategory.forEach(course => {
                          if (!newSelectedCourses.includes(course.id)) {
                            newSelectedCourses.push(course.id);
                            newSettings[course.id] = {
                              count: course.defaultCount,
                              amount: course.defaultAmount
                            };
                          }
                        });

                        setSelectedCourses(newSelectedCourses);
                        setCourseSettings(newSettings);
                      } else {
                        // 取消选中该分类下所有课程
                        setSelectedCourses(selectedCourses.filter(
                          id => !coursesInCategory.some(course => course.id === id)
                        ));
                      }
                    }}
                  />
                  <label
                    htmlFor={`select-category-${category.id}`}
                    className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    全选
                  </label>
                </div>
              </div>

              {/* 课程列表表头 */}
              <div className="grid grid-cols-12 bg-muted px-2 py-2 rounded-md mb-2 text-xs font-medium">
                <div className="col-span-5">课程名称</div>
                <div className="col-span-2 text-center">时长</div>
                {cardType !== "time" && (
                  <>
                    <div className="col-span-5 text-center">{getConsumptionLabel()}</div>
                  </>
                )}
              </div>

              <div className="space-y-1">
                {courseData
                  .filter(course => course.type === category.name)
                  .map(course => (
                    <div key={course.id} className="grid grid-cols-12 items-center py-2 px-2 hover:bg-accent/50 rounded-md">
                      <div className="col-span-5 flex items-center">
                        <Checkbox
                          id={`course-${course.id}`}
                          className="mr-2"
                          checked={selectedCourses.includes(course.id)}
                          onCheckedChange={(checked) => handleSelectCourse(course.id, checked as boolean)}
                        />
                        <label
                          htmlFor={`course-${course.id}`}
                          className="font-medium text-sm cursor-pointer"
                        >
                          {course.name}
                        </label>
                      </div>

                      <div className="col-span-2 text-center text-xs text-muted-foreground">
                        {course.duration}分钟
                      </div>

                      {selectedCourses.includes(course.id) && cardType !== "time" && (
                        <div className="col-span-5">
                          {cardType === "count" && (
                            <div className="flex items-center justify-center">
                              <Input
                                id={`count-${course.id}`}
                                type="number"
                                className="h-8 w-24"
                                value={courseSettings[course.id]?.count || course.defaultCount}
                                onChange={(e) => updateCourseSetting(course.id, 'count', parseFloat(e.target.value))}
                              />
                              <span className="text-xs ml-2">次</span>
                            </div>
                          )}
                          {cardType === "value" && (
                            <div className="flex items-center justify-center">
                              <Input
                                id={`amount-${course.id}`}
                                type="number"
                                className="h-8 w-24"
                                value={courseSettings[course.id]?.amount || course.defaultAmount}
                                onChange={(e) => updateCourseSetting(course.id, 'amount', parseInt(e.target.value))}
                              />
                              <span className="text-xs ml-2">元</span>
                            </div>
                          )}
                        </div>
                      )}

                      {(!selectedCourses.includes(course.id) || cardType === "time") && (
                        <div className="col-span-5"></div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
