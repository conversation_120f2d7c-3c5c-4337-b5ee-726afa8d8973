"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Search, 
  Filter, 
  Calendar, 
  CreditCard, 
  Clock, 
  ChevronLeft, 
  ChevronRight,
  Phone,
  Mail,
  MessageSquare,
  MoreHorizontal,
  CheckCircle,
  AlertCircle,
  Bell,
  UserPlus,
  RefreshCw
} from "lucide-react"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { DateRange } from "react-day-picker"
import { addDays, format, differenceInDays } from "date-fns"

// 模拟会员续费提醒数据
const renewalMembers = [
  {
    id: "M001",
    name: "张三",
    avatar: "/avatars/01.png",
    phone: "13800138001",
    email: "<EMAIL>",
    cardType: "瑜伽年卡",
    cardNumber: "YG-2023-0001",
    expiryDate: "2023-06-15",
    daysLeft: 5,
    lastVisit: "2023-05-30",
    visitFrequency: "高",
    spendAmount: 3680,
    renewalProbability: 85,
    tags: ["高频用户", "VIP会员"],
    status: "未联系"
  },
  {
    id: "M002",
    name: "李四",
    avatar: "/avatars/02.png",
    phone: "13800138002",
    email: "<EMAIL>",
    cardType: "瑜伽季卡",
    cardNumber: "YG-2023-0056",
    expiryDate: "2023-06-10",
    daysLeft: 0,
    lastVisit: "2023-05-15",
    visitFrequency: "中",
    spendAmount: 1580,
    renewalProbability: 60,
    tags: ["中频用户"],
    status: "已联系"
  },
  {
    id: "M003",
    name: "王五",
    avatar: "/avatars/03.png",
    phone: "13800138003",
    email: "<EMAIL>",
    cardType: "瑜伽月卡",
    cardNumber: "YG-2023-0128",
    expiryDate: "2023-06-08",
    daysLeft: -2,
    lastVisit: "2023-06-01",
    visitFrequency: "高",
    spendAmount: 680,
    renewalProbability: 75,
    tags: ["高频用户", "新会员"],
    status: "已续费"
  },
  {
    id: "M004",
    name: "赵六",
    avatar: "/avatars/04.png",
    phone: "13800138004",
    email: "<EMAIL>",
    cardType: "瑜伽年卡",
    cardNumber: "YG-2023-0078",
    expiryDate: "2023-06-20",
    daysLeft: 10,
    lastVisit: "2023-05-28",
    visitFrequency: "低",
    spendAmount: 3680,
    renewalProbability: 40,
    tags: ["低频用户", "潜在流失"],
    status: "未联系"
  },
  {
    id: "M005",
    name: "孙七",
    avatar: "/avatars/05.png",
    phone: "13800138005",
    email: "<EMAIL>",
    cardType: "瑜伽季卡",
    cardNumber: "YG-2023-0145",
    expiryDate: "2023-06-12",
    daysLeft: 2,
    lastVisit: "2023-06-05",
    visitFrequency: "中",
    spendAmount: 1580,
    renewalProbability: 65,
    tags: ["中频用户"],
    status: "已联系"
  }
];

export default function MemberRenewalPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMembers, setSelectedMembers] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: addDays(new Date(), 30),
  })

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "未联系":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">未联系</Badge>
      case "已联系":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">已联系</Badge>
      case "已续费":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已续费</Badge>
      case "已流失":
        return <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200">已流失</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取续费概率样式
  const getRenewalProbabilityStyle = (probability: number) => {
    if (probability >= 75) {
      return "text-green-600"
    } else if (probability >= 50) {
      return "text-amber-600"
    } else {
      return "text-red-600"
    }
  }

  // 过滤会员
  const filteredMembers = renewalMembers.filter(member => {
    // 基本搜索过滤
    const searchFilter = 
      searchQuery === "" || 
      member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.phone.includes(searchQuery) ||
      member.cardNumber.toLowerCase().includes(searchQuery.toLowerCase());
    
    // 标签页过滤
    const tabFilter = 
      activeTab === "all" || 
      (activeTab === "expiring" && member.daysLeft >= 0 && member.daysLeft <= 7) ||
      (activeTab === "expired" && member.daysLeft < 0) ||
      (activeTab === "contacted" && member.status === "已联系") ||
      (activeTab === "renewed" && member.status === "已续费");
    
    return searchFilter && tabFilter;
  });

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedMembers(filteredMembers.map(member => member.id));
    } else {
      setSelectedMembers([]);
    }
  };

  // 处理批量操作
  const handleBatchOperation = (operation: string) => {
    console.log(`对会员 ${selectedMembers.join(", ")} 执行操作: ${operation}`);
    // 实际应用中，这里会执行相应的批量操作
    alert(`已对 ${selectedMembers.length} 个会员执行 ${operation} 操作`);
    setSelectedMembers([]);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">会员续费提醒</h1>
          <p className="text-muted-foreground">
            管理即将到期和已过期的会员卡，提高续费率
          </p>
        </div>
        <div className="flex items-center gap-2">
          {selectedMembers.length > 0 ? (
            <>
              <Button variant="outline" onClick={() => handleBatchOperation("发送续费提醒")}>
                <Bell className="h-4 w-4 mr-2" />
                发送续费提醒 ({selectedMembers.length})
              </Button>
              <Button variant="outline" onClick={() => handleBatchOperation("标记为已联系")}>
                <CheckCircle className="h-4 w-4 mr-2" />
                标记为已联系
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新数据
              </Button>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                添加会员
              </Button>
            </>
          )}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>会员续费概览</CardTitle>
          <CardDescription>
            当前共有 {renewalMembers.filter(m => m.daysLeft >= 0 && m.daysLeft <= 7).length} 个会员卡即将到期，{renewalMembers.filter(m => m.daysLeft < 0).length} 个会员卡已过期
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">即将到期</CardTitle>
                <AlertCircle className="h-4 w-4 text-amber-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{renewalMembers.filter(m => m.daysLeft >= 0 && m.daysLeft <= 7).length}</div>
                <p className="text-xs text-muted-foreground">
                  7天内到期的会员卡数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已过期</CardTitle>
                <Clock className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{renewalMembers.filter(m => m.daysLeft < 0).length}</div>
                <p className="text-xs text-muted-foreground">
                  已过期但未续费的会员卡数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已联系</CardTitle>
                <MessageSquare className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{renewalMembers.filter(m => m.status === "已联系").length}</div>
                <p className="text-xs text-muted-foreground">
                  已联系但未续费的会员数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已续费</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{renewalMembers.filter(m => m.status === "已续费").length}</div>
                <p className="text-xs text-muted-foreground">
                  成功续费的会员数量
                </p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 max-w-2xl">
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="expiring">即将到期</TabsTrigger>
          <TabsTrigger value="expired">已过期</TabsTrigger>
          <TabsTrigger value="contacted">已联系</TabsTrigger>
          <TabsTrigger value="renewed">已续费</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative w-full md:w-1/3">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索会员姓名、手机号或卡号"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-1 gap-2">
          <DatePickerWithRange 
            className="w-full"
            selected={dateRange} 
            onSelect={setDateRange} 
          />
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <Checkbox 
                    checked={selectedMembers.length > 0 && selectedMembers.length === filteredMembers.length}
                    onCheckedChange={handleSelectAll}
                    aria-label="全选"
                  />
                </TableHead>
                <TableHead>会员信息</TableHead>
                <TableHead>会员卡信息</TableHead>
                <TableHead>到期日期</TableHead>
                <TableHead>最近到访</TableHead>
                <TableHead>续费概率</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMembers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    没有找到符合条件的会员
                  </TableCell>
                </TableRow>
              ) : (
                filteredMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedMembers.includes(member.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedMembers([...selectedMembers, member.id]);
                          } else {
                            setSelectedMembers(selectedMembers.filter(id => id !== member.id));
                          }
                        }}
                        aria-label={`选择会员 ${member.name}`}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-9 w-9">
                          <AvatarImage src={member.avatar} alt={member.name} />
                          <AvatarFallback>{member.name.slice(0, 1)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-xs text-muted-foreground flex items-center gap-2">
                            <span className="flex items-center"><Phone className="h-3 w-3 mr-1" />{member.phone}</span>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{member.cardType}</div>
                      <div className="text-xs text-muted-foreground">{member.cardNumber}</div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{member.expiryDate}</div>
                      <div className="text-xs text-muted-foreground">
                        {member.daysLeft > 0 ? `还剩 ${member.daysLeft} 天` : 
                         member.daysLeft === 0 ? "今日到期" : 
                         `已过期 ${Math.abs(member.daysLeft)} 天`}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>{member.lastVisit}</div>
                      <div className="text-xs text-muted-foreground">访问频率: {member.visitFrequency}</div>
                    </TableCell>
                    <TableCell>
                      <div className={`font-medium ${getRenewalProbabilityStyle(member.renewalProbability)}`}>
                        {member.renewalProbability}%
                      </div>
                      <Progress 
                        value={member.renewalProbability} 
                        className="h-2 w-24" 
                        indicatorClassName={
                          member.renewalProbability >= 75 ? "bg-green-500" : 
                          member.renewalProbability >= 50 ? "bg-amber-500" : 
                          "bg-red-500"
                        }
                      />
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(member.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>会员操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => router.push(`/members/${member.id}`)}>
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            发送续费提醒
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            标记为已联系
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            办理续费
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
