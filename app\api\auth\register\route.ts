import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('注册API接收到的数据:', body);
    
    const {
      companyName,
      businessLicense,
      username,
      email,
      phone,
      password,
      confirmPassword
    } = body;

    // 基本验证
    if (!companyName || !username || !email || !phone || !password) {
      return NextResponse.json({
        success: false,
        error: '请填写所有必填字段'
      }, { status: 400 });
    }

    // 验证密码确认
    if (password !== confirmPassword) {
      return NextResponse.json({
        success: false,
        error: '两次输入的密码不匹配'
      }, { status: 400 });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return NextResponse.json({
        success: false,
        error: '请输入有效的手机号码'
      }, { status: 400 });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        success: false,
        error: '请输入有效的邮箱地址'
      }, { status: 400 });
    }

    try {
      // 检查手机号是否已存在
      const existingEmployee = await prisma.employee.findFirst({
        where: { phone }
      });

      if (existingEmployee) {
        return NextResponse.json({
          success: false,
          error: '该手机号已被注册'
        }, { status: 400 });
      }

      // 使用事务创建租户、门店和管理员
      const result = await prisma.$transaction(async (tx) => {
        console.log('开始创建租户...');
        // 1. 创建租户
        const tenant = await tx.tenant.create({
          data: {
            tenant_name: companyName,
            contact_person: username,
            phone,
            email,
            business_license: businessLicense,
            status: 0, // 0-待审核
          }
        });
        console.log('租户创建成功:', tenant.id);

        console.log('开始创建门店...');
        // 2. 创建默认门店
        const store = await tx.store.create({
          data: {
            tenant_id: tenant.id,
            store_name: companyName,
            contact_person: username,
            phone,
            status: 0, // 0-待审核/暂停
          }
        });
        console.log('门店创建成功:', store.id);

        console.log('开始创建超级管理员...');
        // 3. 创建超级管理员
        const employee = await tx.employee.create({
          data: {
            tenant_id: tenant.id,
            real_name: username,
            phone,
            email,
            is_super_admin: 1,
            status: 1,
          }
        });
        console.log('超级管理员创建成功:', employee.id);

        return { tenant, store, employee };
      });

      console.log('注册事务完成，返回成功响应');
      return NextResponse.json({
        success: true,
        tenantId: result.tenant.id,
        storeId: result.store.id,
        employeeId: result.employee.id,
        message: '注册成功，您的账户正在等待审核。审核通过后，您将收到通知。'
      });

    } catch (dbError: any) {
      console.error('数据库操作失败:', dbError);
      
      // 检查是否是重复键错误
      if (dbError.code === 'P2002') {
        return NextResponse.json({
          success: false,
          error: '该手机号或邮箱已被注册'
        }, { status: 400 });
      }
      
      return NextResponse.json({
        success: false,
        error: `数据库操作失败: ${dbError.message || '未知错误'}`
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('注册处理失败:', error);
    return NextResponse.json({
      success: false,
      error: `注册失败: ${error.message || '请稍后再试'}`
    }, { status: 500 });
  }
}
