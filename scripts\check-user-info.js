// 检查当前用户信息和租户数据
const mysql = require('mysql2/promise');

async function checkUserInfo() {
  console.log('检查用户信息和租户数据...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    // 1. 检查所有租户
    console.log('\n1. 检查所有租户:');
    const [tenants] = await connection.execute('SELECT * FROM tenant');
    console.log('租户列表:');
    tenants.forEach((tenant, index) => {
      console.log(`  ${index + 1}. ID: ${tenant.id}, 名称: ${tenant.tenant_name}, 状态: ${tenant.status}`);
    });

    // 2. 检查所有用户
    console.log('\n2. 检查所有用户:');
    const [users] = await connection.execute('SELECT * FROM user');
    console.log('用户列表:');
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ID: ${user.id}, 用户名: ${user.username}, 租户ID: ${user.tenant_id}, 状态: ${user.status}`);
    });

    // 3. 检查各租户的数据统计
    console.log('\n3. 各租户数据统计:');
    for (const tenant of tenants) {
      console.log(`\n租户 ${tenant.id} (${tenant.tenant_name}) 的数据:`);
      
      // 课程类型数量
      const [courseTypes] = await connection.execute('SELECT COUNT(*) as count FROM coursetype WHERE tenant_id = ?', [tenant.id]);
      console.log(`  - 课程类型: ${courseTypes[0].count} 个`);
      
      // 教练数量
      const [coaches] = await connection.execute('SELECT COUNT(*) as count FROM coach WHERE tenant_id = ?', [tenant.id]);
      console.log(`  - 教练: ${coaches[0].count} 个`);
      
      // 场地数量
      const [venues] = await connection.execute('SELECT COUNT(*) as count FROM venue WHERE tenant_id = ?', [tenant.id]);
      console.log(`  - 场地: ${venues[0].count} 个`);
      
      // 课程数量
      const [courses] = await connection.execute('SELECT COUNT(*) as count FROM course WHERE tenant_id = ?', [tenant.id]);
      console.log(`  - 课程: ${courses[0].count} 个`);
    }

    await connection.end();
    console.log('\n✓ 检查完成');

  } catch (error) {
    console.error('检查失败:', error.message);
  }
}

checkUserInfo();
