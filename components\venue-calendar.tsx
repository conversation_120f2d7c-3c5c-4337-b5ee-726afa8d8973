"use client"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { useState } from "react"

// 模拟数据
const venueEvents = [
  {
    id: "E001",
    venue: "1号瑜伽室",
    title: "基础瑜伽入门",
    coach: "张教练",
    date: "2025-03-28",
    startTime: "10:00",
    endTime: "11:30",
    color: "blue",
  },
  {
    id: "E002",
    venue: "2号瑜伽室",
    title: "高级瑜伽进阶",
    coach: "李教练",
    date: "2025-03-28",
    startTime: "14:00",
    endTime: "15:30",
    color: "green",
  },
  {
    id: "E003",
    venue: "3号瑜伽室",
    title: "阴瑜伽放松",
    coach: "王教练",
    date: "2025-03-28",
    startTime: "16:00",
    endTime: "17:00",
    color: "yellow",
  },
  {
    id: "E004",
    venue: "1号瑜伽室",
    title: "孕产瑜伽特训",
    coach: "赵教练",
    date: "2025-03-28",
    startTime: "18:30",
    endTime: "20:00",
    color: "purple",
  },
  {
    id: "E005",
    venue: "4号瑜伽室",
    title: "空中瑜伽体验",
    coach: "刘教练",
    date: "2025-03-29",
    startTime: "09:00",
    endTime: "10:30",
    color: "pink",
  },
]

const venues = ["1号瑜伽室", "2号瑜伽室", "3号瑜伽室", "4号瑜伽室", "私教室"]

const timeSlots = [
  "06:00",
  "07:00",
  "08:00",
  "09:00",
  "10:00",
  "11:00",
  "12:00",
  "13:00",
  "14:00",
  "15:00",
  "16:00",
  "17:00",
  "18:00",
  "19:00",
  "20:00",
  "21:00",
  "22:00",
]

export function VenueCalendar() {
  const [viewType, setViewType] = useState("day")
  const [currentDate, setCurrentDate] = useState(new Date())

  const monthNames = [
    "一月",
    "二月",
    "三月",
    "四月",
    "五月",
    "六月",
    "七月",
    "八月",
    "九月",
    "十月",
    "十一月",
    "十二月",
  ]
  const currentMonthName = monthNames[currentDate.getMonth()]
  const currentYear = currentDate.getFullYear()

  const handlePrevious = () => {
    const newDate = new Date(currentDate)
    if (viewType === "day") {
      newDate.setDate(newDate.getDate() - 1)
    } else if (viewType === "week") {
      newDate.setDate(newDate.getDate() - 7)
    } else {
      newDate.setMonth(newDate.getMonth() - 1)
    }
    setCurrentDate(newDate)
  }

  const handleNext = () => {
    const newDate = new Date(currentDate)
    if (viewType === "day") {
      newDate.setDate(newDate.getDate() + 1)
    } else if (viewType === "week") {
      newDate.setDate(newDate.getDate() + 7)
    } else {
      newDate.setMonth(newDate.getMonth() + 1)
    }
    setCurrentDate(newDate)
  }

  const renderDayView = () => {
    const formattedDate = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${String(currentDate.getDate()).padStart(2, "0")}`
    const dayEvents = venueEvents.filter((event) => event.date === formattedDate)

    return (
      <div className="grid grid-cols-1 gap-4">
        <div className="grid grid-cols-[100px_1fr] border rounded-md overflow-hidden">
          <div className="bg-muted p-2 border-r">
            <div className="h-12 flex items-center justify-center font-medium">场地</div>
            {venues.map((venue, index) => (
              <div key={index} className="h-16 flex items-center justify-center border-t">
                {venue}
              </div>
            ))}
          </div>
          <div className="relative">
            <div className="grid grid-cols-[repeat(16,1fr)] border-b">
              <div className="col-span-1 h-12 flex items-center justify-center border-r">6:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">7:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">8:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">9:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">10:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">11:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">12:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">13:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">14:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">15:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">16:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">17:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">18:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">19:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center border-r">20:00</div>
              <div className="col-span-1 h-12 flex items-center justify-center">21:00</div>
            </div>

            {venues.map((venue, venueIndex) => (
              <div key={venueIndex} className="h-16 border-b relative">
                {dayEvents
                  .filter((event) => event.venue === venue)
                  .map((event, eventIndex) => {
                    const startHour = Number.parseInt(event.startTime.split(":")[0])
                    const startMinute = Number.parseInt(event.startTime.split(":")[1])
                    const endHour = Number.parseInt(event.endTime.split(":")[0])
                    const endMinute = Number.parseInt(event.endTime.split(":")[1])

                    const startPosition = startHour - 6 + startMinute / 60
                    const duration = endHour - startHour + (endMinute - startMinute) / 60
                    const width = `${(duration * 100) / 16}%`
                    const left = `${(startPosition * 100) / 16}%`

                    return (
                      <div
                        key={eventIndex}
                        className={`absolute top-1 h-14 rounded-md p-1 text-xs overflow-hidden cursor-pointer bg-${event.color}-100 border-l-2 border-${event.color}-500`}
                        style={{ left, width }}
                      >
                        <div className="font-medium truncate">{event.title}</div>
                        <div className="truncate">{event.coach}</div>
                        <div className="truncate">
                          {event.startTime}-{event.endTime}
                        </div>
                      </div>
                    )
                  })}
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const renderWeekView = () => {
    // 获取当前周的起始日期（周一）和结束日期（周日）
    const currentDay = currentDate.getDay() || 7 // 将周日的0转换为7
    const mondayOffset = currentDay - 1
    const startDate = new Date(currentDate)
    startDate.setDate(currentDate.getDate() - mondayOffset)

    const weekDays = []
    for (let i = 0; i < 7; i++) {
      const day = new Date(startDate)
      day.setDate(startDate.getDate() + i)
      weekDays.push(day)
    }

    return (
      <div className="grid grid-cols-1 gap-4">
        <div className="grid grid-cols-[100px_1fr] border rounded-md overflow-hidden">
          <div className="bg-muted p-2 border-r">
            <div className="h-12 flex items-center justify-center font-medium">场地/日期</div>
            {venues.map((venue, index) => (
              <div key={index} className="h-16 flex items-center justify-center border-t">
                {venue}
              </div>
            ))}
          </div>
          <div>
            <div className="grid grid-cols-7 border-b">
              {weekDays.map((day, index) => (
                <div
                  key={index}
                  className={cn(
                    "h-12 flex flex-col items-center justify-center border-r",
                    day.toDateString() === new Date().toDateString() ? "bg-primary/10" : "",
                  )}
                >
                  <div className="text-xs">{["周一", "周二", "周三", "周四", "周五", "周六", "周日"][index]}</div>
                  <div className="text-sm">{`${day.getMonth() + 1}/${day.getDate()}`}</div>
                </div>
              ))}
            </div>

            {venues.map((venue, venueIndex) => (
              <div key={venueIndex} className="grid grid-cols-7 h-16 border-b">
                {weekDays.map((day, dayIndex) => {
                  const formattedDate = `${day.getFullYear()}-${String(day.getMonth() + 1).padStart(2, "0")}-${String(day.getDate()).padStart(2, "0")}`
                  const dayVenueEvents = venueEvents.filter(
                    (event) => event.date === formattedDate && event.venue === venue,
                  )

                  return (
                    <div key={dayIndex} className="border-r relative p-1 overflow-hidden">
                      {dayVenueEvents.map((event, eventIndex) => (
                        <div
                          key={eventIndex}
                          className={`mb-1 rounded-sm p-1 text-xs truncate cursor-pointer bg-${event.color}-100 border-l-2 border-${event.color}-500`}
                        >
                          {event.startTime} {event.title}
                        </div>
                      ))}
                    </div>
                  )
                })}
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const renderMonthView = () => {
    // 获取当月第一天是星期几
    const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).getDay() || 7 // 将周日的0转换为7
    const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()

    // 计算日历表格中的天数（包括上个月和下个月的部分天数）
    const totalDays = 42 // 6行7列
    const days = []

    // 上个月的天数
    const prevMonthDays = firstDay - 1
    const prevMonthLastDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0).getDate()
    for (let i = prevMonthDays - 1; i >= 0; i--) {
      days.push({
        date: prevMonthLastDate - i,
        currentMonth: false,
        formattedDate: `${currentDate.getFullYear()}-${String(currentDate.getMonth()).padStart(2, "0")}-${String(prevMonthLastDate - i).padStart(2, "0")}`,
      })
    }

    // 当月的天数
    for (let i = 1; i <= daysInMonth; i++) {
      days.push({
        date: i,
        currentMonth: true,
        formattedDate: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${String(i).padStart(2, "0")}`,
      })
    }

    // 下个月的天数
    const nextMonthDays = totalDays - prevMonthDays - daysInMonth
    for (let i = 1; i <= nextMonthDays; i++) {
      days.push({
        date: i,
        currentMonth: false,
        formattedDate: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 2).padStart(2, "0")}-${String(i).padStart(2, "0")}`,
      })
    }

    return (
      <div className="grid grid-cols-7 gap-1">
        {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, index) => (
          <div key={index} className="text-center font-medium p-2">
            {day}
          </div>
        ))}

        {days.map((day, index) => {
          const dayEvents = venueEvents.filter((event) => event.date === day.formattedDate)
          const isToday =
            day.currentMonth &&
            day.date === new Date().getDate() &&
            currentDate.getMonth() === new Date().getMonth() &&
            currentDate.getFullYear() === new Date().getFullYear()

          return (
            <div
              key={index}
              className={cn(
                "h-24 border p-1 relative",
                day.currentMonth ? "bg-white" : "bg-gray-50 text-gray-400",
                isToday ? "border-primary" : "",
              )}
            >
              <div
                className={cn(
                  "text-xs font-medium",
                  isToday
                    ? "bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center"
                    : "",
                )}
              >
                {day.date}
              </div>

              <div className="mt-1 space-y-1 overflow-hidden max-h-[calc(100%-20px)]">
                {dayEvents.slice(0, 3).map((event, eventIndex) => (
                  <div
                    key={eventIndex}
                    className={`text-xs bg-${event.color}-100 rounded p-0.5 truncate border-l-2 border-${event.color}-500 cursor-pointer`}
                  >
                    {event.startTime} {event.title}
                  </div>
                ))}
                {dayEvents.length > 3 && (
                  <div className="text-xs text-center text-muted-foreground">+{dayEvents.length - 3} 更多</div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={handlePrevious}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <CardTitle>
              {viewType === "month"
                ? `${currentYear}年 ${currentMonthName}`
                : viewType === "week"
                  ? `${currentYear}年 第${Math.ceil((currentDate.getDate() + new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).getDay()) / 7)}周`
                  : `${currentYear}年 ${currentMonthName} ${currentDate.getDate()}日`}
            </CardTitle>
            <Button variant="outline" size="icon" onClick={handleNext}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button variant={viewType === "day" ? "default" : "outline"} size="sm" onClick={() => setViewType("day")}>
              日
            </Button>
            <Button variant={viewType === "week" ? "default" : "outline"} size="sm" onClick={() => setViewType("week")}>
              周
            </Button>
            <Button
              variant={viewType === "month" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewType("month")}
            >
              月
            </Button>
          </div>

          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择场地" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有场地</SelectItem>
              <SelectItem value="1">1号瑜伽室</SelectItem>
              <SelectItem value="2">2号瑜伽室</SelectItem>
              <SelectItem value="3">3号瑜伽室</SelectItem>
              <SelectItem value="4">4号瑜伽室</SelectItem>
              <SelectItem value="5">私教室</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <CardDescription>场地使用情况日历视图</CardDescription>
      </CardHeader>
      <CardContent>
        {viewType === "day" && renderDayView()}
        {viewType === "week" && renderWeekView()}
        {viewType === "month" && renderMonthView()}
      </CardContent>
    </Card>
  )
}

