"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { UserPlus, Repeat, FileCheck, CreditCard, Calendar, Users } from "lucide-react"

// 工作流程数据
const workflows = [
  {
    id: 1,
    icon: <UserPlus className="h-6 w-6" />,
    title: "会员入会",
    description: "新会员信息录入与办卡",
    href: "/workflows/member-registration",
  },
  {
    id: 2,
    icon: <Repeat className="h-6 w-6" />,
    title: "会员续费",
    description: "会员卡续费与升级",
    href: "/workflows/member-renewal",
  },
  {
    id: 3,
    icon: <FileCheck className="h-6 w-6" />,
    title: "日结",
    description: "日常收支核对与结算",
    href: "/workflows/daily-settlement",
  },
]

export function WorkflowShortcuts() {
  const router = useRouter()
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>常用工作流程</CardTitle>
        <CardDescription>快速启动标准化工作流程</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {workflows.map((workflow) => (
            <Button 
              key={workflow.id}
              variant="outline" 
              className="h-auto flex flex-col items-center p-4 gap-2 justify-start"
              onClick={() => router.push(workflow.href)}
            >
              {workflow.icon}
              <span className="font-medium">{workflow.title}</span>
              <span className="text-xs text-muted-foreground">{workflow.description}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
