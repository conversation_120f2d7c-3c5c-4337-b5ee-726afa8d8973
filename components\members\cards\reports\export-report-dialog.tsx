"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON>alog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { cn } from "@/lib/utils"
import { format, subDays, subMonths } from "date-fns"
import { CalendarIcon, Download, FileText, FileSp<PERSON>sheet, <PERSON><PERSON><PERSON>, Check } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface ExportReportDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  card: {
    id: number | string
    name: string
    color: string
  } | null
}

export function ExportReportDialog({
  open,
  onOpenChange,
  card
}: ExportReportDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("members")
  const [dateRange, setDateRange] = useState<{from: Date | undefined, to: Date | undefined}>({
    from: subDays(new Date(), 30),
    to: new Date()
  })
  const [fileFormat, setFileFormat] = useState("excel")
  const [isExporting, setIsExporting] = useState(false)

  // 会员报表字段选择
  const [memberFields, setMemberFields] = useState({
    basic: true,
    contact: true,
    attendance: true,
    consumption: true,
    feedback: false
  })

  // 销售报表字段选择
  const [salesFields, setFieldsFields] = useState({
    daily: true,
    monthly: true,
    channel: true,
    payment: true,
    promotion: false
  })

  // 课程报表字段选择
  const [courseFields, setCourseFields] = useState({
    attendance: true,
    booking: true,
    cancellation: true,
    feedback: false,
    instructor: true
  })

  const handleExport = async () => {
    setIsExporting(true)

    try {
      // 模拟导出过程
      await new Promise(resolve => setTimeout(resolve, 1500))

      toast({
        title: "导出成功",
        description: `${card?.name} 的${getReportTypeName()}已导出为${getFileFormatName()}文件`,
      })

      onOpenChange(false)
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出报表时出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  const getReportTypeName = () => {
    switch (activeTab) {
      case "members":
        return "会员报表"
      case "sales":
        return "销售报表"
      case "courses":
        return "课程报表"
      default:
        return "报表"
    }
  }

  const getFileFormatName = () => {
    switch (fileFormat) {
      case "excel":
        return "Excel"
      case "pdf":
        return "PDF"
      case "csv":
        return "CSV"
      case "json":
        return "JSON"
      default:
        return "Excel"
    }
  }

  const getFileFormatIcon = () => {
    switch (fileFormat) {
      case "excel":
        return <FileSpreadsheet className="h-5 w-5" />
      case "pdf":
        return <FileText className="h-5 w-5" />
      case "csv":
        return <FileText className="h-5 w-5" />
      case "json":
        return <FileJson className="h-5 w-5" />
      default:
        return <FileSpreadsheet className="h-5 w-5" />
    }
  }

  if (!card) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-3xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            导出会员报表
          </DialogTitle>
          <DialogDescription>
            为 <span className="font-medium" style={{ color: card.color }}>{card.name}</span> 会员卡导出各类报表
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-[130px] justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.from ? format(dateRange.from, "yyyy-MM-dd") : "开始日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.from}
                      onSelect={(date) => setDateRange({ ...dateRange, from: date })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <span>至</span>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-[130px] justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : "结束日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.to}
                      onSelect={(date) => setDateRange({ ...dateRange, to: date })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="members">会员报表</TabsTrigger>
              <TabsTrigger value="sales">销售报表</TabsTrigger>
              <TabsTrigger value="courses">课程报表</TabsTrigger>
            </TabsList>

            <TabsContent value="members" className="mt-4 space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="text-sm font-medium">选择要包含的数据字段</div>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="members-basic"
                          checked={memberFields.basic}
                          onCheckedChange={(checked) =>
                            setMemberFields({...memberFields, basic: checked === true})
                          }
                        />
                        <Label htmlFor="members-basic">基本信息（姓名、性别、年龄）</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="members-contact"
                          checked={memberFields.contact}
                          onCheckedChange={(checked) =>
                            setMemberFields({...memberFields, contact: checked === true})
                          }
                        />
                        <Label htmlFor="members-contact">联系方式（手机、邮箱、地址）</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="members-attendance"
                          checked={memberFields.attendance}
                          onCheckedChange={(checked) =>
                            setMemberFields({...memberFields, attendance: checked === true})
                          }
                        />
                        <Label htmlFor="members-attendance">出勤记录（到访次数、最近到访）</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="members-consumption"
                          checked={memberFields.consumption}
                          onCheckedChange={(checked) =>
                            setMemberFields({...memberFields, consumption: checked === true})
                          }
                        />
                        <Label htmlFor="members-consumption">消费记录（消费金额、消费次数）</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="members-feedback"
                          checked={memberFields.feedback}
                          onCheckedChange={(checked) =>
                            setMemberFields({...memberFields, feedback: checked === true})
                          }
                        />
                        <Label htmlFor="members-feedback">反馈评价（评分、评价内容）</Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="sales" className="mt-4 space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="text-sm font-medium">选择要包含的数据字段</div>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sales-daily"
                          checked={salesFields.daily}
                          onCheckedChange={(checked) =>
                            setFieldsFields({...salesFields, daily: checked === true})
                          }
                        />
                        <Label htmlFor="sales-daily">日销售数据</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sales-monthly"
                          checked={salesFields.monthly}
                          onCheckedChange={(checked) =>
                            setFieldsFields({...salesFields, monthly: checked === true})
                          }
                        />
                        <Label htmlFor="sales-monthly">月销售数据</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sales-channel"
                          checked={salesFields.channel}
                          onCheckedChange={(checked) =>
                            setFieldsFields({...salesFields, channel: checked === true})
                          }
                        />
                        <Label htmlFor="sales-channel">销售渠道分析</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sales-payment"
                          checked={salesFields.payment}
                          onCheckedChange={(checked) =>
                            setFieldsFields({...salesFields, payment: checked === true})
                          }
                        />
                        <Label htmlFor="sales-payment">支付方式分析</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sales-promotion"
                          checked={salesFields.promotion}
                          onCheckedChange={(checked) =>
                            setFieldsFields({...salesFields, promotion: checked === true})
                          }
                        />
                        <Label htmlFor="sales-promotion">促销活动效果分析</Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="courses" className="mt-4 space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <div className="text-sm font-medium">选择要包含的数据字段</div>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="courses-attendance"
                          checked={courseFields.attendance}
                          onCheckedChange={(checked) =>
                            setCourseFields({...courseFields, attendance: checked === true})
                          }
                        />
                        <Label htmlFor="courses-attendance">会员出勤记录</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="courses-booking"
                          checked={courseFields.booking}
                          onCheckedChange={(checked) =>
                            setCourseFields({...courseFields, booking: checked === true})
                          }
                        />
                        <Label htmlFor="courses-booking">课程预约记录</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="courses-cancellation"
                          checked={courseFields.cancellation}
                          onCheckedChange={(checked) =>
                            setCourseFields({...courseFields, cancellation: checked === true})
                          }
                        />
                        <Label htmlFor="courses-cancellation">取消预约记录</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="courses-feedback"
                          checked={courseFields.feedback}
                          onCheckedChange={(checked) =>
                            setCourseFields({...courseFields, feedback: checked === true})
                          }
                        />
                        <Label htmlFor="courses-feedback">课程评价反馈</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="courses-instructor"
                          checked={courseFields.instructor}
                          onCheckedChange={(checked) =>
                            setCourseFields({...courseFields, instructor: checked === true})
                          }
                        />
                        <Label htmlFor="courses-instructor">教练授课记录</Label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="text-sm font-medium">选择导出文件格式</div>
                <RadioGroup value={fileFormat} onValueChange={setFileFormat} className="grid grid-cols-2 gap-4 md:grid-cols-4">
                  <div className="flex flex-col items-center space-y-2">
                    <div className={cn(
                      "flex h-16 w-16 items-center justify-center rounded-md border-2",
                      fileFormat === "excel" ? "border-primary bg-primary/10" : "border-muted"
                    )}>
                      <FileSpreadsheet className={cn(
                        "h-8 w-8",
                        fileFormat === "excel" ? "text-primary" : "text-muted-foreground"
                      )} />
                      {fileFormat === "excel" && (
                        <Check className="absolute -right-1 -top-1 h-5 w-5 text-primary" />
                      )}
                    </div>
                    <RadioGroupItem value="excel" id="excel" className="sr-only" />
                    <Label htmlFor="excel" className="cursor-pointer">Excel</Label>
                  </div>
                  <div className="flex flex-col items-center space-y-2">
                    <div className={cn(
                      "flex h-16 w-16 items-center justify-center rounded-md border-2",
                      fileFormat === "pdf" ? "border-primary bg-primary/10" : "border-muted"
                    )}>
                      <FileText className={cn(
                        "h-8 w-8",
                        fileFormat === "pdf" ? "text-primary" : "text-muted-foreground"
                      )} />
                      {fileFormat === "pdf" && (
                        <Check className="absolute -right-1 -top-1 h-5 w-5 text-primary" />
                      )}
                    </div>
                    <RadioGroupItem value="pdf" id="pdf" className="sr-only" />
                    <Label htmlFor="pdf" className="cursor-pointer">PDF</Label>
                  </div>
                  <div className="flex flex-col items-center space-y-2">
                    <div className={cn(
                      "flex h-16 w-16 items-center justify-center rounded-md border-2",
                      fileFormat === "csv" ? "border-primary bg-primary/10" : "border-muted"
                    )}>
                      <FileText className={cn(
                        "h-8 w-8",
                        fileFormat === "csv" ? "text-primary" : "text-muted-foreground"
                      )} />
                      {fileFormat === "csv" && (
                        <Check className="absolute -right-1 -top-1 h-5 w-5 text-primary" />
                      )}
                    </div>
                    <RadioGroupItem value="csv" id="csv" className="sr-only" />
                    <Label htmlFor="csv" className="cursor-pointer">CSV</Label>
                  </div>
                  <div className="flex flex-col items-center space-y-2">
                    <div className={cn(
                      "flex h-16 w-16 items-center justify-center rounded-md border-2",
                      fileFormat === "json" ? "border-primary bg-primary/10" : "border-muted"
                    )}>
                      <FileJson className={cn(
                        "h-8 w-8",
                        fileFormat === "json" ? "text-primary" : "text-muted-foreground"
                      )} />
                      {fileFormat === "json" && (
                        <Check className="absolute -right-1 -top-1 h-5 w-5 text-primary" />
                      )}
                    </div>
                    <RadioGroupItem value="json" id="json" className="sr-only" />
                    <Label htmlFor="json" className="cursor-pointer">JSON</Label>
                  </div>
                </RadioGroup>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleExport} disabled={isExporting} className="gap-2">
            {isExporting ? (
              <>导出中...</>
            ) : (
              <>
                {getFileFormatIcon()}
                导出{getReportTypeName()}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
