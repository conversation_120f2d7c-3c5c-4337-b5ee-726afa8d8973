/**
 * 评价系统类型定义
 */

// 评价状态
export type ReviewStatus = 'published' | 'pending' | 'hidden';

// 评价情感分析结果
export type SentimentType = 'positive' | 'neutral' | 'negative';

// 会员信息
export interface ReviewMember {
  id: string;
  name: string;
  avatar: string;
  level: string;
  joinDate: string;
}

// 教练信息
export interface ReviewCoach {
  id: number;
  name: string;
  avatar: string;
}

// 课程信息
export interface ReviewCourse {
  id: number;
  name: string;
  type: string;
}

// 评价模型
export interface Review {
  id: number;
  // 关联信息
  coachId: number;
  coach: ReviewCoach;
  courseId: number;
  course: ReviewCourse;
  memberId: string;
  member: ReviewMember;
  
  // 评价内容
  rating: number;
  comment: string;
  date: string;
  
  // 状态管理
  status: ReviewStatus;
  
  // 回复信息
  hasReply: boolean;
  reply: string;
  replyDate: string;
  
  // 分析数据
  keywords: string[];
  sentiment: SentimentType;
}

// 评价统计数据
export interface ReviewStats {
  total: number;
  published: number;
  pending: number;
  hidden: number;
  averageRating: number;
  ratingDistribution: {
    [key: number]: number;
  };
  replyRate: string;
  topKeywords: string[];
  sentimentAnalysis: {
    positive: number;
    neutral: number;
    negative: number;
  };
}

// 评价筛选参数
export interface ReviewFilterParams {
  keyword?: string;
  coachId?: number | string;
  courseId?: number | string;
  courseType?: string;
  rating?: string;
  status?: ReviewStatus;
  dateFrom?: string;
  dateTo?: string;
  hasReply?: boolean;
  sentiment?: SentimentType;
  sortBy?: string;
}

// 评价API响应
export interface ReviewApiResponse {
  data: Review[];
  stats: ReviewStats;
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}
