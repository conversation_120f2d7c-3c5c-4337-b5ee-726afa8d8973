"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CourseAssociation } from "./course-association"
import { useToast } from "@/hooks/use-toast"
import { CircleDollarSign, Clock, Hash, CreditCard, Percent, Info } from "lucide-react"

interface MemberCardData {
  id: number | string
  name: string
  description: string
  price: string
  originalPrice: string
  validity: string
  limit: string
  status: "active" | "inactive"
  color: string
  isTrialCard?: boolean
  members?: number
  salesCount?: number
  revenue?: string
  createdAt?: string
  updatedAt?: string
}

interface EditMemberCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  card: MemberCardData | null
  onSave?: (card: MemberCardData) => void
}

export function EditMemberCardDialog({
  open,
  onOpenChange,
  card,
  onSave
}: EditMemberCardDialogProps) {
  const { toast } = useToast()
  const [cardData, setCardData] = useState<MemberCardData | null>(null)
  const [activeTab, setActiveTab] = useState("basic")
  const [cardType, setCardType] = useState<"time" | "count" | "value">("time")
  const [isTrialCard, setIsTrialCard] = useState(false)
  const [hasCountLimit, setHasCountLimit] = useState(false)
  const [consumptionRule, setConsumptionRule] = useState("AVERAGE")
  const [selectedCourseTypes, setSelectedCourseTypes] = useState<string[]>([])
  const [bonusClassTimes, setBonusClassTimes] = useState(0)
  const [bonusValueCoefficient, setBonusValueCoefficient] = useState(1.0)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 初始化表单数据
  useEffect(() => {
    if (card) {
      setCardData({ ...card })

      // 根据限制类型设置卡类型
      if (card.limit.includes("元")) {
        setCardType("value")
      } else if (card.limit.includes("次")) {
        setCardType("count")
      } else {
        setCardType("time")
        setHasCountLimit(card.limit !== "不限次数")
      }

      // 设置是否为体验卡
      setIsTrialCard(card.isTrialCard || false)

      // 设置课程关联数据（这里可以根据实际情况从卡数据中提取）
      // 如果卡数据中没有这些信息，可以设置默认值
      setConsumptionRule("AVERAGE") // 默认平均消耗
      setSelectedCourseTypes([]) // 默认没有选中的课程类型
      setBonusClassTimes(0) // 默认没有赠送课时
      setBonusValueCoefficient(1.0) // 默认系数为1

      // 重置标签页
      setActiveTab("basic")
    }
  }, [card])

  const handleInputChange = (field: keyof MemberCardData, value: string) => {
    if (cardData) {
      setCardData({
        ...cardData,
        [field]: value
      })
    }
  }

  const handleCardTypeChange = (value: "time" | "count" | "value") => {
    setCardType(value)

    if (cardData) {
      // 根据卡类型更新限制字段
      let newLimit = ""
      if (value === "time") {
        newLimit = "不限次数"
      } else if (value === "count") {
        newLimit = "10次"
      } else if (value === "value") {
        newLimit = "1000元"
      }

      setCardData({
        ...cardData,
        limit: newLimit
      })
    }
  }

  const handleSubmit = async () => {
    if (!cardData) return

    setIsSubmitting(true)

    try {
      // 准备更新的数据，确保包含所有编辑的字段
      const updatedCardData = {
        ...cardData,
        isTrialCard: isTrialCard,
        // 可以添加其他在界面上编辑但没有直接映射到cardData的字段
        // 例如，可以添加课程关联数据
        _courseAssociationData: {
          consumptionRule,
          selectedCourseTypes,
          bonusClassTimes,
          bonusValueCoefficient
        }
      };

      // 这里应该是调用API保存数据
      // await api.updateMemberCard(updatedCardData)

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      if (onSave) {
        onSave(updatedCardData)
      }

      toast({
        title: "保存成功",
        description: `会员卡 ${updatedCardData.name} 已更新`,
      })

      onOpenChange(false)
    } catch (error) {
      toast({
        title: "保存失败",
        description: "更新会员卡时出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!cardData) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>编辑会员卡名称</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="pricing">价格设置</TabsTrigger>
            <TabsTrigger value="course_types">关联课程</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">
                  会员卡名称 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  placeholder="请输入会员卡名称"
                  value={cardData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label>会员卡颜色</Label>
                <div className="flex flex-wrap gap-2">
                  {["#4f46e5", "#0ea5e9", "#10b981", "#f59e0b", "#ec4899", "#8b5cf6", "#ef4444"].map((c) => (
                    <div
                      key={c}
                      className={`h-8 w-8 cursor-pointer rounded-full ${cardData.color === c ? "ring-2 ring-primary ring-offset-2" : ""}`}
                      style={{ backgroundColor: c }}
                      onClick={() => handleInputChange("color", c)}
                    />
                  ))}
                </div>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">
                  会员卡描述 <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="description"
                  placeholder="请输入会员卡描述"
                  value={cardData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label>
                  会员卡类别 <span className="text-red-500">*</span>
                </Label>
                <RadioGroup value={cardType} onValueChange={(value: "time" | "count" | "value") => handleCardTypeChange(value)} className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="time" id="time" />
                    <Label htmlFor="time">期限卡（按有效期计费）</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="count" id="count" />
                    <Label htmlFor="count">次数卡（按次数计费）</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="value" id="value" />
                    <Label htmlFor="value">储值卡（按金额计费）</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="trial-card-switch">体验卡</Label>
                  <Switch
                    id="trial-card-switch"
                    checked={isTrialCard}
                    onCheckedChange={setIsTrialCard}
                  />
                  <div className="w-[60%]">
                    <p className="text-xs text-muted-foreground">
                      体验卡通常用于新会员体验，有特殊的使用限制
                    </p>
                  </div>
                </div>

                {cardType === "time" && (
                  <div className="space-y-2">
                    <Label htmlFor="validity">
                      有效期（天） <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="validity"
                      type="number"
                      placeholder="请输入有效期天数"
                      value={cardData.validity.replace(/[^\d]/g, '')}
                      onChange={(e) => handleInputChange("validity", `${e.target.value}天`)}
                    />

                    <div className="mt-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="limit-count-switch">限制使用次数</Label>
                        <Switch
                          id="limit-count-switch"
                          checked={hasCountLimit}
                          onCheckedChange={setHasCountLimit}
                        />
                      </div>

                      {hasCountLimit && (
                        <div className="mt-2">
                          <Label htmlFor="time-card-count">
                            最大使用次数 <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="time-card-count"
                            type="number"
                            min="1"
                            placeholder="请输入最大使用次数"
                            value={hasCountLimit ? cardData.limit.replace(/[^\d]/g, '') : ""}
                            onChange={(e) => handleInputChange("limit", `${e.target.value}次`)}
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            设置时间卡的最大使用次数限制
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {cardType === "count" && (
                  <div className="space-y-2">
                    <Label htmlFor="count">
                      使用次数 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="count"
                      type="number"
                      placeholder="请输入使用次数"
                      value={cardData.limit.replace(/[^\d]/g, '')}
                      onChange={(e) => handleInputChange("limit", `${e.target.value}次`)}
                    />

                    <div className="mt-2">
                      <Label htmlFor="count-validity">
                        有效期（天） <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="count-validity"
                        type="number"
                        placeholder="请输入有效期天数"
                        value={cardData.validity.replace(/[^\d]/g, '')}
                        onChange={(e) => handleInputChange("validity", `${e.target.value}天`)}
                      />
                    </div>
                  </div>
                )}

                {cardType === "value" && (
                  <div className="space-y-2">
                    <Label htmlFor="value-amount">
                      储值金额（元） <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="value-amount"
                      type="number"
                      placeholder="请输入储值金额"
                      value={cardData.limit.replace(/[^\d]/g, '')}
                      onChange={(e) => handleInputChange("limit", `${e.target.value}元`)}
                    />

                    <div className="mt-2">
                      <Label htmlFor="value-validity">
                        有效期（天） <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="value-validity"
                        type="number"
                        placeholder="请输入有效期天数"
                        value={cardData.validity.replace(/[^\d]/g, '')}
                        onChange={(e) => handleInputChange("validity", `${e.target.value}天`)}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="pricing" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="price">
                  售价（元） <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="price"
                  type="number"
                  placeholder="请输入会员卡售价"
                  value={cardData.price.replace(/[^\d.,]/g, '')}
                  onChange={(e) => handleInputChange("price", `¥${e.target.value}`)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="original-price">原价（元）</Label>
                <Input
                  id="original-price"
                  type="number"
                  placeholder="请输入会员卡原价"
                  value={cardData.originalPrice.replace(/[^\d.,]/g, '')}
                  onChange={(e) => handleInputChange("originalPrice", `¥${e.target.value}`)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="discount-switch">启用折扣</Label>
                  <Switch id="discount-switch" />
                </div>
                <Input id="discount" type="number" placeholder="折扣百分比，例如：85 表示 85折" />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="promotion-switch">启用促销</Label>
                  <Switch id="promotion-switch" />
                </div>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择促销类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">新会员专享</SelectItem>
                    <SelectItem value="renewal">续费优惠</SelectItem>
                    <SelectItem value="group">团购优惠</SelectItem>
                    <SelectItem value="holiday">节日促销</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label>价格说明</Label>
                <Textarea placeholder="请输入价格相关说明，如退款政策、优惠条件等" />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="course_types" className="mt-4 space-y-4">
            <CourseAssociation
              cardType={cardType as "count" | "time" | "value"}
              initialData={{
                consumptionRule: consumptionRule,
                selectedCourses: selectedCourseTypes,
                giftCount: bonusClassTimes,
                giftValue: bonusValueCoefficient,
                // 如果有课程设置数据，也可以传递
                courseSettings: {}
              }}
              onChange={(data) => {
                setConsumptionRule(data.consumptionRule);
                setSelectedCourseTypes(data.selectedCourses);
                setBonusClassTimes(data.giftCount);
                setBonusValueCoefficient(data.giftValue);
              }}
            />
          </TabsContent>

          <TabsContent value="advanced" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="status-switch">立即上架</Label>
                  <Switch
                    id="status-switch"
                    checked={cardData.status === "active"}
                    onCheckedChange={(checked) => handleInputChange("status", checked ? "active" : "inactive")}
                  />
                </div>
                <p className="text-sm text-muted-foreground">开启后，会员卡将立即在前台显示并可购买</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="featured-switch">设为推荐</Label>
                  <Switch id="featured-switch" />
                </div>
                <p className="text-sm text-muted-foreground">开启后，会员卡将在前台推荐位置显示</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sort-order">排序优先级</Label>
                <Input id="sort-order" type="number" placeholder="数字越小排序越靠前" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-sales">销售上限</Label>
                <Input id="max-sales" type="number" placeholder="最大销售数量，留空表示不限制" />
                <p className="text-sm text-muted-foreground">设置后，会员卡销售达到上限将自动下架</p>
              </div>

              <div className="space-y-2">
                <Label>购买限制</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择购买限制" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">不限制</SelectItem>
                    <SelectItem value="new">仅限新会员</SelectItem>
                    <SelectItem value="old">仅限老会员</SelectItem>
                    <SelectItem value="once">每人限购一次</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {isTrialCard && (
                <div className="space-y-2">
                  <Label>体验卡限制</Label>
                  <Select defaultValue="once">
                    <SelectTrigger>
                      <SelectValue placeholder="选择体验卡限制" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="once">每人限购一次</SelectItem>
                      <SelectItem value="new_only">仅限新会员购买</SelectItem>
                      <SelectItem value="time_limit">限时体验（自动到期）</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">体验卡的特殊使用限制</p>
                </div>
              )}

              <div className="space-y-2 md:col-span-2">
                <Label>使用规则</Label>
                <Textarea placeholder="请输入会员卡使用规则，如使用限制、注意事项等" />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          {activeTab !== "basic" && (
            <Button
              variant="outline"
              onClick={() =>
                setActiveTab((prev) => {
                  const tabs = ["basic", "pricing", "course_types", "advanced"]
                  const currentIndex = tabs.indexOf(prev)
                  return tabs[currentIndex - 1]
                })
              }
            >
              上一步
            </Button>
          )}
          {activeTab !== "advanced" ? (
            <Button
              onClick={() =>
                setActiveTab((prev) => {
                  const tabs = ["basic", "pricing", "course_types", "advanced"]
                  const currentIndex = tabs.indexOf(prev)
                  return tabs[currentIndex + 1]
                })
              }
            >
              下一步
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? "保存中..." : "保存"}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
