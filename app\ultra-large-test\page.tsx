'use client'

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

const UltraLargeTestPage = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* 测试导航栏 */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="flex items-center justify-between h-16 4xl:h-20 5xl:h-24">
            <div className="flex items-center">
              <h1 className="text-xl 4xl:text-2xl 5xl:text-3xl font-bold text-gray-900">超大屏幕适配测试</h1>
            </div>
            <div className="flex items-center space-x-4 4xl:space-x-6 5xl:space-x-8">
              <Button className="4xl:px-6 4xl:py-3 4xl:text-lg 5xl:px-8 5xl:py-4 5xl:text-xl">
                测试按钮
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* 测试Hero区域 */}
      <div className="pt-16 4xl:pt-20 5xl:pt-24 py-24 4xl:py-32 5xl:py-40 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="text-center">
            <h1 className="text-5xl 4xl:text-6xl 5xl:text-7xl font-bold text-gray-900 mb-6 4xl:mb-8 5xl:mb-12">
              超大屏幕适配测试
            </h1>
            <p className="text-xl 4xl:text-2xl 5xl:text-3xl text-gray-600 mb-8 4xl:mb-12 5xl:mb-16 max-w-3xl 4xl:max-w-4xl 5xl:max-w-5xl mx-auto">
              这是一个测试页面，用于验证在超大屏幕（4K、8K等）上的显示效果。所有元素都应该根据屏幕尺寸进行适当的缩放。
            </p>
            <div className="flex justify-center space-x-4 4xl:space-x-6 5xl:space-x-8">
              <Button size="lg" className="4xl:px-8 4xl:py-4 4xl:text-xl 5xl:px-12 5xl:py-6 5xl:text-2xl">
                主要按钮
              </Button>
              <Button size="lg" variant="outline" className="4xl:px-8 4xl:py-4 4xl:text-xl 5xl:px-12 5xl:py-6 5xl:text-2xl">
                次要按钮
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 测试卡片网格 */}
      <div className="py-24 4xl:py-32 5xl:py-40 bg-gray-50">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="text-center mb-16 4xl:mb-20 5xl:mb-24">
            <h2 className="text-3xl 4xl:text-4xl 5xl:text-5xl font-bold text-gray-900">响应式卡片测试</h2>
            <p className="mt-4 4xl:mt-6 5xl:mt-8 text-gray-600 4xl:text-xl 5xl:text-2xl">
              测试不同屏幕尺寸下的卡片布局和字体大小
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 4xl:gap-12 5xl:gap-16">
            {[
              { title: '小屏幕', desc: '手机和平板设备' },
              { title: '中等屏幕', desc: '笔记本电脑和桌面显示器' },
              { title: '大屏幕', desc: '大型桌面显示器' },
              { title: '超大屏幕', desc: '4K显示器和电视' },
              { title: '巨型屏幕', desc: '8K显示器和投影仪' },
              { title: '未来屏幕', desc: '更高分辨率的显示设备' }
            ].map((item, index) => (
              <Card key={index} className="p-8 4xl:p-12 5xl:p-16 hover:shadow-lg transition-shadow">
                <h3 className="text-xl 4xl:text-2xl 5xl:text-3xl font-bold text-gray-900 mb-4 4xl:mb-6 5xl:mb-8">
                  {item.title}
                </h3>
                <p className="text-gray-600 4xl:text-lg 5xl:text-xl leading-relaxed">
                  {item.desc}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* 测试数据展示 */}
      <div className="py-24 4xl:py-32 5xl:py-40 bg-white">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 4xl:gap-12 5xl:gap-16">
            {[
              { number: '1920px', label: '标准大屏' },
              { number: '2560px', label: '2K显示器' },
              { number: '3840px', label: '4K显示器' },
              { number: '5120px', label: '5K显示器' }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl 4xl:text-5xl 5xl:text-6xl font-bold text-indigo-600 mb-2 4xl:mb-4 5xl:mb-6">
                  {item.number}
                </div>
                <div className="text-gray-600 4xl:text-xl 5xl:text-2xl">
                  {item.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 屏幕信息显示 */}
      <div className="py-16 4xl:py-20 5xl:py-24 bg-gray-900 text-white">
        <div className="max-w-7xl 3xl:max-w-[1800px] 4xl:max-w-[2400px] 5xl:max-w-[3600px] mx-auto px-4 4xl:px-8 5xl:px-12 text-center">
          <h3 className="text-2xl 4xl:text-3xl 5xl:text-4xl font-bold mb-4 4xl:mb-6 5xl:mb-8">
            当前屏幕信息
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 4xl:gap-6 5xl:gap-8 text-sm 4xl:text-base 5xl:text-lg">
            <div>
              <span className="block font-semibold">屏幕宽度:</span>
              <span id="screen-width">检测中...</span>
            </div>
            <div>
              <span className="block font-semibold">屏幕高度:</span>
              <span id="screen-height">检测中...</span>
            </div>
            <div>
              <span className="block font-semibold">设备像素比:</span>
              <span id="device-ratio">检测中...</span>
            </div>
          </div>
        </div>
      </div>

      <script dangerouslySetInnerHTML={{
        __html: `
          function updateScreenInfo() {
            document.getElementById('screen-width').textContent = window.screen.width + 'px';
            document.getElementById('screen-height').textContent = window.screen.height + 'px';
            document.getElementById('device-ratio').textContent = window.devicePixelRatio;
          }
          if (typeof window !== 'undefined') {
            updateScreenInfo();
            window.addEventListener('resize', updateScreenInfo);
          }
        `
      }} />
    </div>
  );
};

export default UltraLargeTestPage;
