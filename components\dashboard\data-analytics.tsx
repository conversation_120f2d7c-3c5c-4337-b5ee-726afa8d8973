"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { ArrowUpRight, ArrowDownRight } from "lucide-react"

export function DataAnalytics() {
  return (
    <Tabs defaultValue="member" className="mt-6">
      <TabsList>
        <TabsTrigger value="member">会员分析</TabsTrigger>
        <TabsTrigger value="course">课程分析</TabsTrigger>
        <TabsTrigger value="finance">财务分析</TabsTrigger>
      </TabsList>
      
      <TabsContent value="member" className="mt-4">
        <Card>
          <CardContent className="pt-6">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <div className="text-sm font-medium">新增会员</div>
                <div className="text-2xl font-bold">24</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">本月累计: 128</div>
                  <div className="text-xs text-green-500 inline-flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" /> +12.5%
                  </div>
                </div>
                <Progress value={75} className="h-1" />
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">活跃会员</div>
                <div className="text-2xl font-bold">573</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">占总会员: 78.5%</div>
                  <div className="text-xs text-green-500 inline-flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" /> +5.2%
                  </div>
                </div>
                <Progress value={78} className="h-1" />
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">续费率</div>
                <div className="text-2xl font-bold">85.2%</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">目标: 90%</div>
                  <div className="text-xs text-green-500 inline-flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" /> +2.3%
                  </div>
                </div>
                <Progress value={85} className="h-1" />
              </div>
            </div>
            
            <div className="mt-6 space-y-2">
              <div className="text-sm font-medium">会员来源分布</div>
              <div className="grid grid-cols-4 gap-2">
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>线上推广</span>
                    <span className="font-medium">45%</span>
                  </div>
                  <Progress value={45} className="h-1" />
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>会员推荐</span>
                    <span className="font-medium">30%</span>
                  </div>
                  <Progress value={30} className="h-1" />
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>线下活动</span>
                    <span className="font-medium">15%</span>
                  </div>
                  <Progress value={15} className="h-1" />
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>其他渠道</span>
                    <span className="font-medium">10%</span>
                  </div>
                  <Progress value={10} className="h-1" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="course" className="mt-4">
        <Card>
          <CardContent className="pt-6">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <div className="text-sm font-medium">课程总数</div>
                <div className="text-2xl font-bold">156</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">本月新增: 12</div>
                  <div className="text-xs text-green-500 inline-flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" /> +8.3%
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">平均预约率</div>
                <div className="text-2xl font-bold">78.5%</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">目标: 85%</div>
                  <div className="text-xs text-green-500 inline-flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" /> +3.2%
                  </div>
                </div>
                <Progress value={78} className="h-1" />
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">满员率</div>
                <div className="text-2xl font-bold">42.3%</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">目标: 50%</div>
                  <div className="text-xs text-green-500 inline-flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" /> +5.7%
                  </div>
                </div>
                <Progress value={42} className="h-1" />
              </div>
            </div>
            
            <div className="mt-6 space-y-2">
              <div className="text-sm font-medium">热门课程排名</div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="bg-primary/20 text-primary w-6 h-6 rounded-full flex items-center justify-center text-xs">1</div>
                    <span>高级瑜伽进阶</span>
                  </div>
                  <div className="text-sm">预约率: 95%</div>
                </div>
                <Progress value={95} className="h-1" />
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="bg-primary/20 text-primary w-6 h-6 rounded-full flex items-center justify-center text-xs">2</div>
                    <span>阴瑜伽放松</span>
                  </div>
                  <div className="text-sm">预约率: 92%</div>
                </div>
                <Progress value={92} className="h-1" />
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="bg-primary/20 text-primary w-6 h-6 rounded-full flex items-center justify-center text-xs">3</div>
                    <span>基础瑜伽入门</span>
                  </div>
                  <div className="text-sm">预约率: 88%</div>
                </div>
                <Progress value={88} className="h-1" />
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="finance" className="mt-4">
        <Card>
          <CardContent className="pt-6">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <div className="text-sm font-medium">本月收入</div>
                <div className="text-2xl font-bold">¥325,780</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">目标: ¥350,000</div>
                  <div className="text-xs text-green-500 inline-flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" /> +12.8%
                  </div>
                </div>
                <Progress value={93} className="h-1" />
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">本月支出</div>
                <div className="text-2xl font-bold">¥156,420</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">预算: ¥180,000</div>
                  <div className="text-xs text-green-500 inline-flex items-center">
                    <ArrowDownRight className="h-3 w-3 mr-1" /> -5.2%
                  </div>
                </div>
                <Progress value={87} className="h-1" />
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">利润率</div>
                <div className="text-2xl font-bold">52.0%</div>
                <div className="flex items-center justify-between">
                  <div className="text-xs text-muted-foreground">目标: 55%</div>
                  <div className="text-xs text-green-500 inline-flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" /> +3.5%
                  </div>
                </div>
                <Progress value={94} className="h-1" />
              </div>
            </div>
            
            <div className="mt-6 space-y-2">
              <div className="text-sm font-medium">收入构成</div>
              <div className="grid grid-cols-4 gap-2">
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>会员卡</span>
                    <span className="font-medium">65%</span>
                  </div>
                  <Progress value={65} className="h-1" />
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>单次课程</span>
                    <span className="font-medium">15%</span>
                  </div>
                  <Progress value={15} className="h-1" />
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>私教课</span>
                    <span className="font-medium">12%</span>
                  </div>
                  <Progress value={12} className="h-1" />
                </div>
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span>商品销售</span>
                    <span className="font-medium">8%</span>
                  </div>
                  <Progress value={8} className="h-1" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
