"use client"

import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertCircle, ArrowRight, Clock, Download, Printer } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface RefundDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  refundId: string
}

export function RefundDetailDialog({ open, onOpenChange, refundId }: RefundDetailDialogProps) {
  // 模拟退款数据
  const refund = {
    id: refundId,
    orderId: "ORD005",
    status: "pending", // pending, approved, rejected
    amount: "¥1,200.00",
    date: "2025-03-28 09:10:45",
    reason: "个人原因",
    description: "因个人时间安排变动，无法继续使用会员卡服务，申请退款。",
    member: {
      id: "MEM007",
      name: "钱七",
      avatar: "/placeholder.svg?height=40&width=40",
      level: "银卡会员",
    },
    order: {
      id: "ORD005",
      type: "会员卡",
      product: "季卡",
      amount: "¥1,200.00",
      date: "2025-03-28 09:10",
      paymentMethod: "支付宝",
    },
    timeline: [
      { time: "2025-03-28 09:10:45", action: "提交退款申请", operator: "钱七" },
      { time: "2025-03-28 09:15:22", action: "系统自动审核", operator: "系统" },
      { time: "2025-03-28 09:15:22", action: "等待人工审核", operator: "系统" },
    ],
  }

  const handleApproveRefund = () => {
    alert("批准退款: " + refundId)
  }

  const handleRejectRefund = () => {
    alert("拒绝退款: " + refundId)
  }

  const handlePrintRefund = () => {
    alert("打印退款单: " + refundId)
  }

  const handleDownloadRefund = () => {
    alert("下载退款凭证: " + refundId)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl">退款详情</DialogTitle>
              <DialogDescription>退款编号: {refund.id}</DialogDescription>
            </div>
            <Badge
              variant={
                refund.status === "approved" ? "default" : refund.status === "pending" ? "outline" : "destructive"
              }
              className="ml-2"
            >
              {refund.status === "approved" ? "已批准" : refund.status === "pending" ? "待审核" : "已拒绝"}
            </Badge>
          </div>
        </DialogHeader>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="details">退款详情</TabsTrigger>
            <TabsTrigger value="order">原订单信息</TabsTrigger>
            <TabsTrigger value="history">操作记录</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">退款信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">退款状态</span>
                    <Badge
                      variant={
                        refund.status === "approved"
                          ? "default"
                          : refund.status === "pending"
                            ? "outline"
                            : "destructive"
                      }
                    >
                      {refund.status === "approved" ? "已批准" : refund.status === "pending" ? "待审核" : "已拒绝"}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">退款金额</span>
                    <span className="font-medium">{refund.amount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">申请时间</span>
                    <span>{refund.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">退款原因</span>
                    <span>{refund.reason}</span>
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="space-y-2">
                  <h4 className="font-medium">详细说明</h4>
                  <p className="text-sm">{refund.description}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">申请人信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={refund.member.avatar} alt={refund.member.name} />
                    <AvatarFallback>{refund.member.name[0]}</AvatarFallback>
                  </Avatar>
                  <div className="space-y-1">
                    <h3 className="font-medium text-lg">{refund.member.name}</h3>
                    <div className="flex items-center text-muted-foreground">
                      <span>会员ID: {refund.member.id}</span>
                    </div>
                    <div className="flex items-center">
                      <Badge variant="secondary">{refund.member.level}</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {refund.status === "pending" && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">审核操作</CardTitle>
                  <CardDescription>请审核此退款申请</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="review-notes">审核备注</Label>
                      <Textarea id="review-notes" placeholder="请输入审核备注" />
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>退款提醒</AlertTitle>
                      <AlertDescription>
                        根据退款政策，会员卡退款将收取5%的手续费。退款将在3-5个工作日内退回原支付账户。
                      </AlertDescription>
                    </Alert>

                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={handleRejectRefund} className="text-red-500">
                        拒绝退款
                      </Button>
                      <Button onClick={handleApproveRefund}>批准退款</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {refund.status !== "pending" && (
              <div className="flex flex-wrap gap-2 justify-end">
                <Button variant="outline" onClick={handlePrintRefund}>
                  <Printer className="mr-2 h-4 w-4" />
                  打印退款单
                </Button>
                <Button variant="outline" onClick={handleDownloadRefund}>
                  <Download className="mr-2 h-4 w-4" />
                  下载退款凭证
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="order" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">原订单信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单编号</span>
                    <span>{refund.order.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单类型</span>
                    <span>{refund.order.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">商品/服务</span>
                    <span>{refund.order.product}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单金额</span>
                    <span>{refund.order.amount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">下单时间</span>
                    <span>{refund.order.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">支付方式</span>
                    <span>{refund.order.paymentMethod}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button variant="outline">
                <ArrowRight className="mr-2 h-4 w-4" />
                查看原订单详情
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">操作记录</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative pl-6 border-l">
                  {refund.timeline.map((event, index) => (
                    <div key={index} className="mb-4 relative">
                      <div className="absolute -left-[25px] w-4 h-4 rounded-full bg-primary"></div>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="mr-1 h-3 w-3" />
                          <span>{event.time}</span>
                        </div>
                        <p className="font-medium">{event.action}</p>
                        <p className="text-sm text-muted-foreground">操作人: {event.operator}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

