"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { CreditCard, Plus, Clock, Calendar, BarChart2, History, Tag, Pencil, Trash2, AlertCircle, Search } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CardValueDisplay } from "./cards/card-value-display"
import { MemberCardInstanceDetailDialog } from "./cards/member-card-instance-detail-dialog"

interface MembershipCardsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
  }
}

export function MembershipCardsDialog({ open, onOpenChange, member }: MembershipCardsDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("active")
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null)
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)

  // 格式化金额显示，与详情页保持一致
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(value)
  }

  // 模拟会员卡数据
  const membershipCards = [
    {
      id: "MC001",
      name: "瑜伽年卡",
      type: "time",
      cardCategory: "time", // 与详情页保持一致
      purchaseDate: "2023-01-15",
      activationDate: "2023-01-15",
      expiryDate: "2024-01-14",
      status: "有效",
      price: 3600,
      remainingDays: 120,
      totalDays: 365,
      usedDays: 245,
      // 价值相关字段，与详情页保持一致
      originalValue: 3600, // 原始价值（购买价格）
      consumedValue: 1200, // 已消耗价值
      remainingValue: 2400, // 剩余价值
      color: "#4CAF50",
      usageRecords: [
        { date: "2023-10-15", course: "瑜伽基础课", instructor: "李教练", value: 100, valueDisplay: "¥100" },
        { date: "2023-10-10", course: "普拉提进阶", instructor: "王教练", value: 120, valueDisplay: "¥120" },
        { date: "2023-10-05", course: "瑜伽基础课", instructor: "李教练", value: 100, valueDisplay: "¥100" },
      ]
    },
    {
      id: "MC002",
      name: "私教次卡",
      type: "count",
      cardCategory: "count",
      purchaseDate: "2023-05-20",
      activationDate: "2023-05-20",
      expiryDate: "2023-11-20",
      status: "有效",
      price: 2000,
      remainingTimes: 5,
      totalTimes: 10,
      usedTimes: 5,
      // 价值相关字段
      originalValue: 2000,
      consumedValue: 1000,
      remainingValue: 1000,
      color: "#2196F3",
      usageRecords: [
        { date: "2023-10-12", course: "私教一对一", instructor: "张教练", value: 200, valueDisplay: "¥200" },
        { date: "2023-09-28", course: "私教一对一", instructor: "张教练", value: 200, valueDisplay: "¥200" },
        { date: "2023-09-15", course: "私教一对一", instructor: "张教练", value: 200, valueDisplay: "¥200" },
      ]
    },
    {
      id: "MC003",
      name: "瑜伽季卡",
      type: "time",
      cardCategory: "time",
      purchaseDate: "2023-04-10",
      activationDate: "2023-04-10",
      expiryDate: "2023-07-09",
      status: "已过期",
      price: 1200,
      remainingDays: 0,
      totalDays: 90,
      usedDays: 90,
      // 价值相关字段
      originalValue: 1200,
      consumedValue: 1200,
      remainingValue: 0,
      color: "#FF9800",
      usageRecords: [
        { date: "2023-07-05", course: "瑜伽基础课", instructor: "李教练", value: 100, valueDisplay: "¥100" },
        { date: "2023-06-20", course: "瑜伽基础课", instructor: "李教练", value: 100, valueDisplay: "¥100" },
        { date: "2023-06-05", course: "瑜伽基础课", instructor: "李教练", value: 100, valueDisplay: "¥100" },
      ]
    }
  ]

  const activeCards = membershipCards.filter(card => card.status === "有效")
  const expiredCards = membershipCards.filter(card => card.status === "已过期")

  const handleExtendCard = (cardId: string) => {
    toast({
      title: "续费成功",
      description: "会员卡已成功续费",
    })
  }

  const handleFreezeCard = (cardId: string) => {
    toast({
      title: "冻结成功",
      description: "会员卡已成功冻结",
    })
  }

  const handleDeleteCard = (cardId: string) => {
    toast({
      title: "删除成功",
      description: "会员卡已成功删除",
      variant: "destructive",
    })
  }

  const handleAddCard = () => {
    toast({
      title: "添加会员卡",
      description: "即将为会员添加新的会员卡",
    })
  }

  // 处理查看详情
  const handleViewCardDetail = (cardId: string) => {
    setSelectedCardId(cardId)
    setDetailDialogOpen(true)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>会员卡管理</DialogTitle>
          <DialogDescription>
            管理 {member.name} 的会员卡
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-between items-center">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList>
              <TabsTrigger value="active">
                <CreditCard className="mr-2 h-4 w-4" />
                有效会员卡 ({activeCards.length})
              </TabsTrigger>
              <TabsTrigger value="expired">
                <Clock className="mr-2 h-4 w-4" />
                已过期 ({expiredCards.length})
              </TabsTrigger>
              <TabsTrigger value="history">
                <History className="mr-2 h-4 w-4" />
                购买记录
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <Button onClick={handleAddCard}>
            <Plus className="mr-2 h-4 w-4" />
            添加会员卡名称
          </Button>
        </div>

        <TabsContent value="active" className="space-y-4 mt-4">
          {activeCards.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>无有效会员卡</AlertTitle>
              <AlertDescription>
                该会员当前没有有效的会员卡，请点击"添加会员卡"按钮添加。
              </AlertDescription>
            </Alert>
          ) : (
            activeCards.map(card => (
              <Card key={card.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full" style={{ backgroundColor: card.color }} />
                      <CardTitle>{card.name}</CardTitle>
                    </div>
                    <Badge>{card.type === "time" ? "期限卡" : "次数卡"}</Badge>
                  </div>
                  <CardDescription>
                    购买日期: {card.purchaseDate} | 到期日期: {card.expiryDate}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  {/* 使用情况进度条 */}
                  {card.type === "time" ? (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>剩余天数: {card.remainingDays}天</span>
                        <span>{Math.round((card.remainingDays / card.totalDays) * 100)}%</span>
                      </div>
                      <Progress value={(card.remainingDays / card.totalDays) * 100} />
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>剩余次数: {card.remainingTimes}次</span>
                        <span>{Math.round((card.remainingTimes / card.totalTimes) * 100)}%</span>
                      </div>
                      <Progress value={(card.remainingTimes / card.totalTimes) * 100} />
                    </div>
                  )}

                  {/* 价值显示 - 使用统一的价值显示组件 */}
                  <div className="mt-4">
                    <CardValueDisplay
                      cardType={card.cardCategory as "time" | "count" | "value"}
                      originalValue={card.originalValue}
                      consumedValue={card.consumedValue}
                      remainingValue={card.remainingValue}
                      compact={true}
                      showDetails={false}
                      showChart={false}
                    />
                  </div>

                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">最近使用记录</h4>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>日期</TableHead>
                          <TableHead>课程</TableHead>
                          <TableHead>教练</TableHead>
                          <TableHead className="text-right">消费值</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {card.usageRecords.slice(0, 2).map((record, index) => (
                          <TableRow key={index}>
                            <TableCell>{record.date}</TableCell>
                            <TableCell>{record.course}</TableCell>
                            <TableCell>{record.instructor}</TableCell>
                            <TableCell className="text-right">{formatCurrency(record.value)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between pt-2">
                  <div>
                    <span className="text-sm text-muted-foreground">剩余价值: </span>
                    <span className="font-medium">{formatCurrency(card.remainingValue)}</span>
                    <span className="text-sm text-muted-foreground"> / {formatCurrency(card.originalValue)}</span>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => handleViewCardDetail(card.id)}>
                      <Search className="mr-1 h-4 w-4" />
                      详情
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleExtendCard(card.id)}>
                      续费
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleFreezeCard(card.id)}>
                      冻结
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-500" onClick={() => handleDeleteCard(card.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="expired" className="space-y-4 mt-4">
          {expiredCards.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>无过期会员卡</AlertTitle>
              <AlertDescription>
                该会员没有过期的会员卡记录。
              </AlertDescription>
            </Alert>
          ) : (
            expiredCards.map(card => (
              <Card key={card.id} className="opacity-70">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full" style={{ backgroundColor: card.color }} />
                      <CardTitle>{card.name}</CardTitle>
                    </div>
                    <Badge variant="outline">已过期</Badge>
                  </div>
                  <CardDescription>
                    购买日期: {card.purchaseDate} | 到期日期: {card.expiryDate}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  {/* 价值显示 - 使用统一的价值显示组件 */}
                  <div className="mb-4">
                    <CardValueDisplay
                      cardType={card.cardCategory as "time" | "count" | "value"}
                      originalValue={card.originalValue}
                      consumedValue={card.consumedValue}
                      remainingValue={card.remainingValue}
                      compact={true}
                      showDetails={false}
                      showChart={false}
                    />
                  </div>

                  <div className="mt-2">
                    <h4 className="text-sm font-medium mb-2">使用记录</h4>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>日期</TableHead>
                          <TableHead>课程</TableHead>
                          <TableHead>教练</TableHead>
                          <TableHead className="text-right">消费值</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {card.usageRecords.slice(0, 2).map((record, index) => (
                          <TableRow key={index}>
                            <TableCell>{record.date}</TableCell>
                            <TableCell>{record.course}</TableCell>
                            <TableCell>{record.instructor}</TableCell>
                            <TableCell className="text-right">{formatCurrency(record.value)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between pt-2">
                  <div>
                    <span className="text-sm text-muted-foreground">总价值: </span>
                    <span className="font-medium">{formatCurrency(card.originalValue)}</span>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => handleViewCardDetail(card.id)}>
                      <Search className="mr-1 h-4 w-4" />
                      详情
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleExtendCard(card.id)}>
                      重新购买
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="history" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>购买历史</CardTitle>
              <CardDescription>会员卡购买和续费记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>日期</TableHead>
                    <TableHead>会员卡</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>支付方式</TableHead>
                    <TableHead>操作人</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>2023-01-15</TableCell>
                    <TableCell>瑜伽年卡</TableCell>
                    <TableCell>新购</TableCell>
                    <TableCell>{formatCurrency(3600)}</TableCell>
                    <TableCell>微信支付</TableCell>
                    <TableCell>张经理</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>2023-05-20</TableCell>
                    <TableCell>私教次卡</TableCell>
                    <TableCell>新购</TableCell>
                    <TableCell>{formatCurrency(2000)}</TableCell>
                    <TableCell>支付宝</TableCell>
                    <TableCell>李前台</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>2023-04-10</TableCell>
                    <TableCell>瑜伽季卡</TableCell>
                    <TableCell>新购</TableCell>
                    <TableCell>{formatCurrency(1200)}</TableCell>
                    <TableCell>现金</TableCell>
                    <TableCell>王教练</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* 会员卡详情对话框 */}
    {selectedCardId && (
      <MemberCardInstanceDetailDialog
        open={detailDialogOpen}
        onOpenChange={setDetailDialogOpen}
        cardId={selectedCardId}
      />
    )}
  )
}
