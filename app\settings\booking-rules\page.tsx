"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { HomeIcon, SaveIcon, UndoIcon, Settings, BookOpen } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { GeneralBookingRules } from "@/components/settings/booking-rules/general-booking-rules"
import { CourseTypeBookingRules } from "@/components/settings/booking-rules/course-type-booking-rules"

export default function BookingRulesPage() {
  const [activeTab, setActiveTab] = useState("general")
  const [hasChanges, setHasChanges] = useState(false)
  const { toast } = useToast()

  const handleChange = () => {
    setHasChanges(true)
  }

  const handleSave = () => {
    console.log("保存预约规则设置")
    toast({
      title: "设置已保存",
      description: "预约规则设置已成功保存",
    })
    setHasChanges(false)
  }

  const handleReset = () => {
    console.log("重置预约规则设置")
    toast({
      title: "设置已重置",
      description: "预约规则设置已重置为默认值",
    })
    setHasChanges(false)
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard" className="flex items-center">
              <HomeIcon className="h-4 w-4" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/settings">系统设置</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>预约规则</BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">预约规则设置</h1>
          <p className="text-muted-foreground">
            设置课程预约的通用规则和各课程类型的特定规则
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={!hasChanges}
          >
            <UndoIcon className="mr-2 h-4 w-4" />
            重置
          </Button>
          <Button
            onClick={handleSave}
            disabled={!hasChanges}
          >
            <SaveIcon className="mr-2 h-4 w-4" />
            保存设置
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2 text-primary" />
            预约规则层级说明
          </CardTitle>
          <CardDescription>
            了解预约规则的优先级和应用范围
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium">规则优先级（从高到低）</h4>
              <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
                <li>课程类型规则（本页面设置）</li>
                <li>通用规则（本页面设置）</li>
              </ol>
            </div>
            <div className="space-y-3">
              <h4 className="font-medium">规则应用范围</h4>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li>• <strong>通用规则</strong>：适用于所有课程类型的默认设置</li>
                <li>• <strong>课程类型规则</strong>：覆盖通用规则，适用于特定课程类型</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 w-full max-w-md">
          <TabsTrigger value="general" className="flex items-center">
            <Settings className="h-4 w-4 mr-2" />
            通用规则
          </TabsTrigger>
          <TabsTrigger value="course-types" className="flex items-center">
            <BookOpen className="h-4 w-4 mr-2" />
            课程类型规则
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="mt-6">
          <GeneralBookingRules onChange={handleChange} />
        </TabsContent>

        <TabsContent value="course-types" className="mt-6">
          <CourseTypeBookingRules onChange={handleChange} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
