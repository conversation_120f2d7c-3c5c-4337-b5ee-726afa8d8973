"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  BellRing,
  UserCheck,
  BarChart,
  Clock,
  Activity,
  Smartphone,
  Globe,
  ChevronRight,
  Calendar,
  Target,
  TrendingUp,
  Users,
  MessageSquare,
  Tag,
  Zap,
  ArrowLeft,
  Sparkles,
  CheckCircle2,
  Timer,
  MousePointer,
  Layers,
  PieChart,
  LineChart,
  BarChart2,
  Map,
  Share2,
  Search
} from "lucide-react"
import Link from "next/link"

export default function PremiumLeadsPage() {
  const [isPremium, setIsPremium] = useState(false)
  const [activeTab, setActiveTab] = useState("dashboard")
  const [activeSubTab, setActiveSubTab] = useState("overview")

  // 检查用户是否已开通高级版
  useEffect(() => {
    // 这里应该是实际的API调用，检查用户是否已开通高级版
    // 为了演示，我们使用localStorage来模拟状态
    const premiumStatus = localStorage.getItem("premiumLeadsEnabled")
    setIsPremium(premiumStatus === "true")
  }, [])

  // 仅用于演示，实际应用中不需要此功能
  const handleTogglePremium = () => {
    const newStatus = !isPremium
    localStorage.setItem("premiumLeadsEnabled", newStatus.toString())
    setIsPremium(newStatus)
  }

  // 模拟数据
  const behaviorData = {
    stayTime: "3分42秒",
    pageViews: "5.2页/访客",
    mobileRate: "78%",
    mainSource: "微信小程序",
    bounceRate: "32%",
    returnRate: "45%"
  }

  const pathData = [
    { path: "首页 → 课程介绍 → 预约体验", percentage: 42 },
    { path: "首页 → 教练团队 → 课程介绍", percentage: 28 },
    { path: "活动页 → 会员卡介绍 → 联系我们", percentage: 17 },
    { path: "场馆环境 → 课程介绍 → 预约体验", percentage: 13 }
  ]

  const questionData = [
    { question: "首页 → 课程介绍 → 场地体验", percentage: 42 },
    { question: "首页 → 教练团队 → 课程介绍", percentage: 28 },
    { question: "活动页 → 会员卡介绍 → 联系我们", percentage: 17 }
  ]

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <div className="flex items-center gap-2">
            <Link href="/leads" className="text-muted-foreground hover:text-foreground">
              <Button variant="ghost" size="sm" className="gap-1">
                <ArrowLeft className="h-4 w-4" />
                返回潜客管理
              </Button>
            </Link>
          </div>
          <h1 className="text-2xl font-semibold tracking-tight mt-1">高级潜客管理</h1>
          <div className="flex items-center mt-1">
            {/* 仅用于演示，实际应用中不需要此按钮 */}
            <Button variant="ghost" size="sm" className="h-6 px-2 text-xs" onClick={handleTogglePremium}>
              (演示用：{isPremium ? "取消" : "开通"}高级版)
            </Button>
            {isPremium ? (
              <Badge className="ml-2 bg-gradient-to-r from-amber-500 to-amber-300 hover:from-amber-500 hover:to-amber-300 text-white">
                <Sparkles className="h-3 w-3 mr-1" />
                高级版已开通
              </Badge>
            ) : (
              <Badge variant="outline" className="ml-2">未开通</Badge>
            )}
          </div>
        </div>
        {!isPremium && (
          <Link href="/premium-services/advanced-leads">
            <Button className="whitespace-nowrap">
              <Sparkles className="mr-2 h-4 w-4" />
              升级高级版
            </Button>
          </Link>
        )}
      </div>

      {!isPremium ? (
        <Card className="border-primary/20">
          <CardHeader>
            <CardTitle>高级潜客管理功能</CardTitle>
            <CardDescription>
              升级到高级版，解锁全部高级功能，提升获客效率和转化率
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium">智能跟进提醒</p>
                    <p className="text-sm text-muted-foreground">自动提醒跟进时间，不错过任何潜在客户</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium">潜客画像分析</p>
                    <p className="text-sm text-muted-foreground">深入了解潜客特征，精准营销</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium">转化漏斗分析</p>
                    <p className="text-sm text-muted-foreground">分析潜客转化各环节数据，优化流程</p>
                  </div>
                </div>
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="font-medium">访客行为分析</p>
                    <p className="text-sm text-muted-foreground">分析潜客访问路径、停留时间和交互行为</p>
                  </div>
                </div>
              </div>

              <div className="flex justify-center mt-6">
                <Link href="/premium-services/advanced-leads">
                  <Button size="lg" className="gap-2">
                    <Sparkles className="h-4 w-4" />
                    了解更多并升级
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid grid-cols-4 w-full">
              <TabsTrigger value="dashboard">数据概览</TabsTrigger>
              <TabsTrigger value="followup">智能跟进</TabsTrigger>
              <TabsTrigger value="analysis">潜客分析</TabsTrigger>
              <TabsTrigger value="behavior">行为分析</TabsTrigger>
            </TabsList>

            <div className="flex justify-between items-center mt-4">
              <h2 className="text-xl font-semibold">高级潜客管理</h2>
              <Button variant="outline" size="sm" className="gap-1">
                <Search className="h-4 w-4" />
                搜索
              </Button>
            </div>

            <TabsContent value="dashboard" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* 智能跟进提醒 */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <BellRing className="mr-2 h-5 w-5 text-primary" />
                      智能跟进提醒
                    </CardTitle>
                    <CardDescription>自动提醒跟进时间，不错过任何潜在客户</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span>今日待跟进</span>
                        <Badge>5</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>明日待跟进</span>
                        <Badge variant="outline">3</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>已逾期未跟进</span>
                        <Badge variant="destructive">2</Badge>
                      </div>
                      <Button size="sm" className="w-full" onClick={() => setActiveTab("followup")}>
                        <BellRing className="mr-2 h-4 w-4" />
                        查看全部提醒
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* 潜客画像分析 */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <UserCheck className="mr-2 h-5 w-5 text-primary" />
                      潜客画像分析
                    </CardTitle>
                    <CardDescription>深入了解潜客特征，精准营销</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span>潜客兴趣分布</span>
                        <Button variant="ghost" size="sm">查看</Button>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>年龄段分布</span>
                        <Button variant="ghost" size="sm">查看</Button>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>消费能力分析</span>
                        <Button variant="ghost" size="sm">查看</Button>
                      </div>
                      <Button size="sm" className="w-full" onClick={() => setActiveTab("analysis")}>
                        <UserCheck className="mr-2 h-4 w-4" />
                        查看完整画像
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* 转化漏斗分析 */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <BarChart className="mr-2 h-5 w-5 text-primary" />
                      转化漏斗分析
                    </CardTitle>
                    <CardDescription>分析潜客转化各环节数据，优化流程</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span>本月转化率</span>
                        <Badge className="bg-green-500">32%</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>环比上月</span>
                        <span className="text-green-500">↑ 5%</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>流失率</span>
                        <Badge variant="outline" className="text-red-500">15%</Badge>
                      </div>
                      <Button size="sm" className="w-full" onClick={() => setActiveTab("analysis")}>
                        <BarChart className="mr-2 h-4 w-4" />
                        查看详细报告
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 访客行为分析 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Activity className="mr-2 h-5 w-5 text-primary" />
                      访客行为分析
                    </CardTitle>
                    <CardDescription>分析潜客访问路径、停留时间和交互行为</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>平均停留时长</span>
                        </div>
                        <span className="font-medium">3分42秒</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Activity className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>页面浏览量</span>
                        </div>
                        <span className="font-medium">5.2页/访客</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Smartphone className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>移动端占比</span>
                        </div>
                        <span className="font-medium">78%</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Globe className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>主要来源渠道</span>
                        </div>
                        <span className="font-medium">微信小程序</span>
                      </div>
                      <Button size="sm" className="w-full mt-2" onClick={() => setActiveTab("behavior")}>查看详细数据</Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Target className="mr-2 h-5 w-5 text-primary" />
                      热门访问路径
                    </CardTitle>
                    <CardDescription>了解潜客最常访问的页面和路径</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <Badge variant="outline" className="mr-2">1</Badge>
                            <span>首页 → 课程介绍 → 预约体验</span>
                          </div>
                          <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                            <div className="bg-primary h-2 rounded-full" style={{ width: "42%" }}></div>
                          </div>
                        </div>
                        <span className="ml-4 text-sm font-medium">42%</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <Badge variant="outline" className="mr-2">2</Badge>
                            <span>首页 → 教练团队 → 课程介绍</span>
                          </div>
                          <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                            <div className="bg-primary h-2 rounded-full" style={{ width: "28%" }}></div>
                          </div>
                        </div>
                        <span className="ml-4 text-sm font-medium">28%</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <Badge variant="outline" className="mr-2">3</Badge>
                            <span>活动页 → 会员卡介绍 → 联系我们</span>
                          </div>
                          <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                            <div className="bg-primary h-2 rounded-full" style={{ width: "17%" }}></div>
                          </div>
                        </div>
                        <span className="ml-4 text-sm font-medium">17%</span>
                      </div>

                      <Button size="sm" className="w-full mt-2" onClick={() => setActiveTab("behavior")}>查看更多路径</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="followup">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <BellRing className="mr-2 h-5 w-5 text-primary" />
                      智能跟进提醒
                    </CardTitle>
                    <CardDescription>自动提醒跟进时间，不错过任何潜在客户</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span>今日待跟进</span>
                        <Badge>5</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>明日待跟进</span>
                        <Badge variant="outline">3</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>已逾期未跟进</span>
                        <Badge variant="destructive">2</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>本周待跟进</span>
                        <Badge variant="outline">12</Badge>
                      </div>
                      <Button size="sm" className="w-full">
                        <BellRing className="mr-2 h-4 w-4" />
                        查看全部提醒
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <Calendar className="mr-2 h-5 w-5 text-primary" />
                      跟进计划
                    </CardTitle>
                    <CardDescription>查看和安排潜客跟进计划</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span>今日已完成</span>
                        <Badge className="bg-green-500">3/8</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>本周计划</span>
                        <Badge variant="outline">24</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>下周计划</span>
                        <Badge variant="outline">18</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>待安排</span>
                        <Badge variant="secondary">7</Badge>
                      </div>
                      <Button size="sm" className="w-full">
                        <Calendar className="mr-2 h-4 w-4" />
                        查看跟进日历
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <Zap className="mr-2 h-5 w-5 text-primary" />
                      智能建议
                    </CardTitle>
                    <CardDescription>基于AI的跟进建议和话术</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span>最佳跟进时间</span>
                        <Badge className="bg-blue-500">上午9-11点</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>推荐跟进方式</span>
                        <Badge variant="outline">电话 + 微信</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>话术模板</span>
                        <Badge variant="outline">8个</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>个性化建议</span>
                        <Badge variant="secondary">5个</Badge>
                      </div>
                      <Button size="sm" className="w-full">
                        <Zap className="mr-2 h-4 w-4" />
                        查看智能建议
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="mr-2 h-5 w-5 text-primary" />
                      待跟进潜客列表
                    </CardTitle>
                    <CardDescription>今日需要跟进的潜客列表</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                        <div>
                          <div className="font-medium">张小姐</div>
                          <div className="text-sm text-muted-foreground">兴趣：瑜伽入门课程</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm">今日 10:30</div>
                          <div className="text-xs text-muted-foreground">第3次跟进</div>
                        </div>
                        <Button size="sm" variant="outline">跟进</Button>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                        <div>
                          <div className="font-medium">王先生</div>
                          <div className="text-sm text-muted-foreground">兴趣：私教课程</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm">今日 14:00</div>
                          <div className="text-xs text-muted-foreground">第1次跟进</div>
                        </div>
                        <Button size="sm" variant="outline">跟进</Button>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-muted rounded-md">
                        <div>
                          <div className="font-medium">李女士</div>
                          <div className="text-sm text-muted-foreground">兴趣：年卡会员</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm">今日 16:30</div>
                          <div className="text-xs text-muted-foreground">第2次跟进</div>
                        </div>
                        <Button size="sm" variant="outline">跟进</Button>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-destructive/10 rounded-md">
                        <div>
                          <div className="font-medium">赵先生</div>
                          <div className="text-sm text-muted-foreground">兴趣：团体课</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-destructive">已逾期 1天</div>
                          <div className="text-xs text-muted-foreground">第2次跟进</div>
                        </div>
                        <Button size="sm" variant="outline">跟进</Button>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-destructive/10 rounded-md">
                        <div>
                          <div className="font-medium">陈女士</div>
                          <div className="text-sm text-muted-foreground">兴趣：瑜伽垫</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-destructive">已逾期 2天</div>
                          <div className="text-xs text-muted-foreground">第1次跟进</div>
                        </div>
                        <Button size="sm" variant="outline">跟进</Button>
                      </div>

                      <Button size="sm" className="w-full">查看全部待跟进潜客</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="analysis">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <UserCheck className="mr-2 h-5 w-5 text-primary" />
                      潜客画像分析
                    </CardTitle>
                    <CardDescription>深入了解潜客特征，精准营销</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span>潜客兴趣分布</span>
                        <Button variant="ghost" size="sm">查看</Button>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>年龄段分布</span>
                        <Button variant="ghost" size="sm">查看</Button>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>消费能力分析</span>
                        <Button variant="ghost" size="sm">查看</Button>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>地域分布</span>
                        <Button variant="ghost" size="sm">查看</Button>
                      </div>
                      <Button size="sm" className="w-full">
                        <UserCheck className="mr-2 h-4 w-4" />
                        查看完整画像
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <BarChart className="mr-2 h-5 w-5 text-primary" />
                      转化漏斗分析
                    </CardTitle>
                    <CardDescription>分析潜客转化各环节数据，优化流程</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span>本月转化率</span>
                        <Badge className="bg-green-500">32%</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>环比上月</span>
                        <span className="text-green-500">↑ 5%</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>流失率</span>
                        <Badge variant="outline" className="text-red-500">15%</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>平均转化周期</span>
                        <span>7.2天</span>
                      </div>
                      <Button size="sm" className="w-full">
                        <BarChart className="mr-2 h-4 w-4" />
                        查看详细报告
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <PieChart className="mr-2 h-5 w-5 text-primary" />
                      来源渠道分析
                    </CardTitle>
                    <CardDescription>分析潜客获取渠道效果</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span>微信小程序</span>
                        <Badge className="bg-blue-500">42%</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>线下活动</span>
                        <Badge className="bg-purple-500">28%</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>会员推荐</span>
                        <Badge className="bg-amber-500">18%</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>其他渠道</span>
                        <Badge className="bg-gray-500">12%</Badge>
                      </div>
                      <Button size="sm" className="w-full">
                        <PieChart className="mr-2 h-4 w-4" />
                        查看渠道分析
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <LineChart className="mr-2 h-5 w-5 text-primary" />
                      潜客趋势分析
                    </CardTitle>
                    <CardDescription>分析潜客数量和质量变化趋势</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[200px] flex items-center justify-center bg-muted/50 rounded-md mb-4">
                      <span className="text-muted-foreground">趋势图表将在这里显示</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <div className="text-sm text-muted-foreground">本月新增潜客</div>
                        <div className="text-2xl font-bold">128</div>
                        <div className="text-xs text-green-500">↑ 12% 环比上月</div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-sm text-muted-foreground">本月转化潜客</div>
                        <div className="text-2xl font-bold">42</div>
                        <div className="text-xs text-green-500">↑ 8% 环比上月</div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-sm text-muted-foreground">平均获客成本</div>
                        <div className="text-2xl font-bold">¥68</div>
                        <div className="text-xs text-green-500">↓ 5% 环比上月</div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-sm text-muted-foreground">潜客质量评分</div>
                        <div className="text-2xl font-bold">7.8</div>
                        <div className="text-xs text-green-500">↑ 0.3 环比上月</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Map className="mr-2 h-5 w-5 text-primary" />
                      地域分布分析
                    </CardTitle>
                    <CardDescription>了解潜客的地理分布情况</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[200px] flex items-center justify-center bg-muted/50 rounded-md mb-4">
                      <span className="text-muted-foreground">地域分布图将在这里显示</span>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <Badge variant="outline" className="mr-2">1</Badge>
                            <span>朝阳区</span>
                          </div>
                          <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                            <div className="bg-primary h-2 rounded-full" style={{ width: "38%" }}></div>
                          </div>
                        </div>
                        <span className="ml-4 text-sm font-medium">38%</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <Badge variant="outline" className="mr-2">2</Badge>
                            <span>海淀区</span>
                          </div>
                          <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                            <div className="bg-primary h-2 rounded-full" style={{ width: "24%" }}></div>
                          </div>
                        </div>
                        <span className="ml-4 text-sm font-medium">24%</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <Badge variant="outline" className="mr-2">3</Badge>
                            <span>东城区</span>
                          </div>
                          <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                            <div className="bg-primary h-2 rounded-full" style={{ width: "16%" }}></div>
                          </div>
                        </div>
                        <span className="ml-4 text-sm font-medium">16%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="behavior">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Activity className="mr-2 h-5 w-5 text-primary" />
                      访客行为分析
                    </CardTitle>
                    <CardDescription>分析潜客访问路径、停留时间和交互行为</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>平均停留时长</span>
                        </div>
                        <span className="font-medium">{behaviorData.stayTime}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Activity className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>页面浏览量</span>
                        </div>
                        <span className="font-medium">{behaviorData.pageViews}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Smartphone className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>移动端占比</span>
                        </div>
                        <span className="font-medium">{behaviorData.mobileRate}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Globe className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>主要来源渠道</span>
                        </div>
                        <span className="font-medium">{behaviorData.mainSource}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Share2 className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>跳出率</span>
                        </div>
                        <span className="font-medium">{behaviorData.bounceRate}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Timer className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>回访率</span>
                        </div>
                        <span className="font-medium">{behaviorData.returnRate}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MousePointer className="mr-2 h-5 w-5 text-primary" />
                      页面互动热点
                    </CardTitle>
                    <CardDescription>了解用户在页面上的点击和互动热点</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span>预约体验按钮</span>
                        <Badge className="bg-green-500">68%</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>课程详情链接</span>
                        <Badge className="bg-blue-500">42%</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>教练介绍卡片</span>
                        <Badge className="bg-purple-500">37%</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>价格查询按钮</span>
                        <Badge className="bg-amber-500">29%</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>联系我们表单</span>
                        <Badge className="bg-red-500">18%</Badge>
                      </div>
                      <Button size="sm" className="w-full mt-2">查看热力图</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Target className="mr-2 h-5 w-5 text-primary" />
                      热门访问路径
                    </CardTitle>
                    <CardDescription>了解潜客最常访问的页面和路径</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {pathData.map((item, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center">
                              <Badge variant="outline" className="mr-2">{index + 1}</Badge>
                              <span>{item.path}</span>
                            </div>
                            <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                              <div className="bg-primary h-2 rounded-full" style={{ width: `${item.percentage}%` }}></div>
                            </div>
                          </div>
                          <span className="ml-4 text-sm font-medium">{item.percentage}%</span>
                        </div>
                      ))}
                      <Button size="sm" className="w-full mt-2">查看更多路径</Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MessageSquare className="mr-2 h-5 w-5 text-primary" />
                      热门问题路径
                    </CardTitle>
                    <CardDescription>了解潜客最关心的问题和转化路径</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {questionData.map((item, index) => (
                        <div key={index} className="flex items-center">
                          <Badge className="mr-2">{index + 1}</Badge>
                          <div className="flex-1">
                            <div>{item.question}</div>
                            <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                              <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${item.percentage}%` }}></div>
                            </div>
                          </div>
                          <span className="ml-4 text-sm font-medium">{item.percentage}%</span>
                        </div>
                      ))}
                      <Button size="sm" className="w-full mt-4">查看更多问题</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}
