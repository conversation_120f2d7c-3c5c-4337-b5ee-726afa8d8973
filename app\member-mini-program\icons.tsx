"use client"

import { 
  Home, 
  Users, 
  Calendar, 
  CreditCard, 
  BookOpen,
  CheckSquare,
  User,
  Bell,
  Menu,
  Plus
} from "lucide-react"

interface IconProps {
  name: string;
  className?: string;
  size?: number;
  color?: string;
}

export function Icon({ name, className, size = 24, color }: IconProps) {
  const iconProps = {
    size,
    className,
    color,
    strokeWidth: 1.5
  }

  switch (name) {
    case "venue-intro":
      return <Home {...iconProps} />
    case "trainer":
      return <Users {...iconProps} />
    case "checkin":
      return <CheckSquare {...iconProps} />
    case "membership":
      return <CreditCard {...iconProps} />
    case "course":
      return <BookOpen {...iconProps} />
    case "calendar":
      return <Calendar {...iconProps} />
    case "user":
      return <User {...iconProps} />
    case "notification":
      return <Bell {...iconProps} />
    case "menu":
      return <Menu {...iconProps} />
    case "plus":
      return <Plus {...iconProps} />
    default:
      return <div className="w-6 h-6 bg-gray-200 rounded-full" />
  }
}
