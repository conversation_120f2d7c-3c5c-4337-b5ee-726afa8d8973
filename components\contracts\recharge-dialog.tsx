"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { Wallet, CreditCard, Banknote, CheckCircle } from "lucide-react"

// 表单验证模式
const formSchema = z.object({
  amount: z.string().min(1, {
    message: "请输入充值金额",
  }).refine((val) => {
    const num = parseFloat(val)
    return !isNaN(num) && num > 0
  }, {
    message: "请输入有效的充值金额",
  }),
  paymentMethod: z.enum(["WECHAT", "ALIPAY", "BANK_TRANSFER"], {
    required_error: "请选择支付方式",
  }),
})

interface RechargeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onRechargeSuccess?: (amount: number) => void
  currentBalance?: number
}

export function RechargeDialog({
  open,
  onOpenChange,
  onRechargeSuccess,
  currentBalance = 0,
}: RechargeDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [step, setStep] = useState<"form" | "success">("form")
  const [rechargeAmount, setRechargeAmount] = useState(0)

  // 预设金额选项
  const presetAmounts = [100, 200, 500, 1000, 2000]

  // 初始化表单
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: "",
      paymentMethod: "ALIPAY",
    },
  })

  // 选择预设金额
  const selectPresetAmount = (amount: number) => {
    form.setValue("amount", amount.toString())
  }

  // 提交表单
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const amount = parseFloat(values.amount)
      setRechargeAmount(amount)
      
      console.log("充值请求:", {
        amount,
        paymentMethod: values.paymentMethod
      })
      
      // 模拟支付成功
      setStep("success")
      
      // 显示成功提示
      toast({
        title: "充值成功",
        description: `成功充值 ¥${amount.toFixed(2)}`,
      })
      
      // 延迟关闭对话框
      setTimeout(() => {
        // 调用成功回调
        if (onRechargeSuccess) {
          onRechargeSuccess(amount)
        }
        
        // 重置表单
        form.reset()
        setStep("form")
        
        // 关闭对话框
        onOpenChange(false)
      }, 2000)
    } catch (error) {
      console.error("充值失败:", error)
      toast({
        title: "充值失败",
        description: "充值过程中发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 获取支付方式图标
  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case "WECHAT":
        return <svg className="h-4 w-4 text-green-600" viewBox="0 0 24 24" fill="currentColor"><path d="M8.686 13.991c-1.173 0-2.12-.957-2.12-2.13 0-1.175.947-2.132 2.12-2.132 1.175 0 2.12.957 2.12 2.132 0 1.173-.945 2.13-2.12 2.13zm6.696-4.262c1.174 0 2.12.957 2.12 2.132 0 1.173-.946 2.13-2.12 2.13-1.175 0-2.12-.957-2.12-2.13 0-1.175.945-2.132 2.12-2.132zm4.007-2.417C17.29 3.09 12.73 1 8.274 1 3.275 1 0 4.724 0 9.133c0 2.56 1.332 4.763 3.222 6.348l-.803 2.416 2.805-1.41c1.01.273 1.865.547 2.87.547.258 0 .512-.015.764-.028-.16-.547-.252-1.124-.252-1.718 0-3.954 3.37-7.177 7.483-7.177.346 0 .69.028 1.03.08z"/></svg>
      case "ALIPAY":
        return <svg className="h-4 w-4 text-blue-600" viewBox="0 0 24 24" fill="currentColor"><path d="M21.422 15.358c-4.597-1.363-7.215-2.334-7.85-2.915 1.895-1.65 3.144-3.301 3.745-4.952.6-1.65.872-3.219.872-4.702H13.6v2.87h-4.35v-2.87H5.677v2.87H1.326v2.333h4.351v2.87h-4.35v2.334h4.35v4.35c0 .387.31.697.698.697h3.875v3.491h3.49v-3.49h3.49c.387 0 .698-.31.698-.698v-4.35h4.35c-.001-.775-.388-1.55-1.164-2.333-1.938.775-3.875 1.55-5.813 2.334.775.774 2.325 1.55 4.65 2.333-1.551 1.938-3.876 3.487-6.977 4.65-3.101 1.162-6.202 1.549-9.303 1.162 1.55-1.55 2.714-3.1 3.49-4.65.774-1.55 1.161-3.1 1.161-4.65l-1.55-.387c-.387 1.55-.774 2.714-1.548 3.876-.775 1.163-1.55 2.325-2.325 3.488-1.55-.387-2.714-1.163-3.49-2.325-.774-1.163-1.161-2.325-1.161-3.876 0-2.325.775-4.262 2.325-5.813 1.55-1.55 3.488-2.325 5.813-2.325h13.653c0-1.163-.387-1.938-1.163-2.325-.774-.387-1.937-.387-3.487 0 0-.775-.387-1.55-1.163-2.325-.774-.775-1.55-1.163-2.324-1.163.387.387.387 1.163 0 2.325-.387 1.163-.775 1.938-1.163 2.325.775.387 1.55.387 2.325 0 .775-.387 1.55-.387 2.325 0-1.162 3.101-3.1 5.426-5.812 6.977 1.162.775 3.1 1.55 5.812 2.325 2.713.775 4.65 1.55 5.813 2.325v.001z"/></svg>
      case "BANK_TRANSFER":
        return <CreditCard className="h-4 w-4 text-gray-600" />
      default:
        return <Wallet className="h-4 w-4" />
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>充值法大大电子合同</DialogTitle>
          <DialogDescription>
            为您的账户充值，用于支付电子合同服务费用
          </DialogDescription>
        </DialogHeader>
        
        {step === "form" && (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="flex items-center justify-between">
                <Label>当前余额</Label>
                <div className="text-lg font-semibold">¥{currentBalance.toFixed(2)}</div>
              </div>
              
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>充值金额</FormLabel>
                    <FormControl>
                      <div className="space-y-2">
                        <Input
                          placeholder="输入充值金额"
                          {...field}
                          type="number"
                          min="0"
                          step="0.01"
                        />
                        <div className="flex flex-wrap gap-2">
                          {presetAmounts.map((amount) => (
                            <Button
                              key={amount}
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => selectPresetAmount(amount)}
                            >
                              ¥{amount}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </FormControl>
                    <FormDescription>
                      请输入您要充值的金额，或选择预设金额
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="paymentMethod"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>支付方式</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <div className="flex items-center space-x-2 rounded-md border p-3">
                          <RadioGroupItem value="ALIPAY" id="alipay" />
                          <Label htmlFor="alipay" className="flex items-center">
                            <svg className="h-4 w-4 text-blue-600 mr-2" viewBox="0 0 24 24" fill="currentColor"><path d="M21.422 15.358c-4.597-1.363-7.215-2.334-7.85-2.915 1.895-1.65 3.144-3.301 3.745-4.952.6-1.65.872-3.219.872-4.702H13.6v2.87h-4.35v-2.87H5.677v2.87H1.326v2.333h4.351v2.87h-4.35v2.334h4.35v4.35c0 .387.31.697.698.697h3.875v3.491h3.49v-3.49h3.49c.387 0 .698-.31.698-.698v-4.35h4.35c-.001-.775-.388-1.55-1.164-2.333-1.938.775-3.875 1.55-5.813 2.334.775.774 2.325 1.55 4.65 2.333-1.551 1.938-3.876 3.487-6.977 4.65-3.101 1.162-6.202 1.549-9.303 1.162 1.55-1.55 2.714-3.1 3.49-4.65.774-1.55 1.161-3.1 1.161-4.65l-1.55-.387c-.387 1.55-.774 2.714-1.548 3.876-.775 1.163-1.55 2.325-2.325 3.488-1.55-.387-2.714-1.163-3.49-2.325-.774-1.163-1.161-2.325-1.161-3.876 0-2.325.775-4.262 2.325-5.813 1.55-1.55 3.488-2.325 5.813-2.325h13.653c0-1.163-.387-1.938-1.163-2.325-.774-.387-1.937-.387-3.487 0 0-.775-.387-1.55-1.163-2.325-.774-.775-1.55-1.163-2.324-1.163.387.387.387 1.163 0 2.325-.387 1.163-.775 1.938-1.163 2.325.775.387 1.55.387 2.325 0 .775-.387 1.55-.387 2.325 0-1.162 3.101-3.1 5.426-5.812 6.977 1.162.775 3.1 1.55 5.812 2.325 2.713.775 4.65 1.55 5.813 2.325v.001z"/></svg>
                            支付宝
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 rounded-md border p-3">
                          <RadioGroupItem value="WECHAT" id="wechat" />
                          <Label htmlFor="wechat" className="flex items-center">
                            <svg className="h-4 w-4 text-green-600 mr-2" viewBox="0 0 24 24" fill="currentColor"><path d="M8.686 13.991c-1.173 0-2.12-.957-2.12-2.13 0-1.175.947-2.132 2.12-2.132 1.175 0 2.12.957 2.12 2.132 0 1.173-.945 2.13-2.12 2.13zm6.696-4.262c1.174 0 2.12.957 2.12 2.132 0 1.173-.946 2.13-2.12 2.13-1.175 0-2.12-.957-2.12-2.13 0-1.175.945-2.132 2.12-2.132zm4.007-2.417C17.29 3.09 12.73 1 8.274 1 3.275 1 0 4.724 0 9.133c0 2.56 1.332 4.763 3.222 6.348l-.803 2.416 2.805-1.41c1.01.273 1.865.547 2.87.547.258 0 .512-.015.764-.028-.16-.547-.252-1.124-.252-1.718 0-3.954 3.37-7.177 7.483-7.177.346 0 .69.028 1.03.08z"/></svg>
                            微信支付
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 rounded-md border p-3">
                          <RadioGroupItem value="BANK_TRANSFER" id="bank" />
                          <Label htmlFor="bank" className="flex items-center">
                            <CreditCard className="h-4 w-4 text-gray-600 mr-2" />
                            银行转账
                          </Label>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  取消
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <span className="animate-spin mr-2">⏳</span>
                      处理中...
                    </>
                  ) : (
                    <>
                      <Banknote className="mr-2 h-4 w-4" />
                      确认充值
                    </>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
        
        {step === "success" && (
          <div className="flex flex-col items-center justify-center py-10 space-y-4">
            <div className="rounded-full bg-green-100 p-3">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <h3 className="text-lg font-medium">充值成功</h3>
            <p className="text-sm text-muted-foreground text-center">
              您已成功充值 ¥{rechargeAmount.toFixed(2)}
            </p>
            <div className="flex items-center justify-between w-full px-4 py-2 bg-muted rounded-md">
              <span>当前余额</span>
              <span className="font-semibold">¥{(currentBalance + rechargeAmount).toFixed(2)}</span>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
