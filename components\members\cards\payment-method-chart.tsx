"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface PaymentMethodChartProps {
  cardId: number | string
  className?: string
  title?: string
}

export function PaymentMethodChart({ 
  cardId, 
  className,
  title = "支付方式分布"
}: PaymentMethodChartProps) {
  // 模拟支付方式数据
  const paymentData = [
    { method: "微信支付", count: 65, color: "#07C160" },
    { method: "支付宝", count: 25, color: "#1677FF" },
    { method: "银行卡", count: 8, color: "#FF6A00" },
    { method: "现金", count: 2, color: "#FFD700" },
  ]
  
  // 计算总数
  const total = paymentData.reduce((sum, item) => sum + item.count, 0)
  
  // 计算每种支付方式的百分比
  const paymentDataWithPercentage = paymentData.map(item => ({
    ...item,
    percentage: Math.round((item.count / total) * 100)
  }))
  
  // 排序，从大到小
  paymentDataWithPercentage.sort((a, b) => b.count - a.count)
  
  // 渲染饼图
  const renderPieChart = () => {
    let cumulativePercentage = 0
    
    return (
      <div className="relative mx-auto h-[180px] w-[180px]">
        <div className="absolute inset-0 flex items-center justify-center rounded-full border-8 border-background">
          <div className="text-center">
            <div className="text-2xl font-bold">{total}</div>
            <div className="text-xs text-muted-foreground">总交易数</div>
          </div>
        </div>
        
        <svg className="h-full w-full -rotate-90" viewBox="0 0 100 100">
          {paymentDataWithPercentage.map((item, index) => {
            const startPercentage = cumulativePercentage
            cumulativePercentage += item.percentage
            
            // 计算圆弧路径
            const startAngle = startPercentage * 3.6 // 3.6 = 360 / 100
            const endAngle = cumulativePercentage * 3.6
            
            // 计算圆弧的起点和终点坐标
            const startX = 50 + 40 * Math.cos((startAngle * Math.PI) / 180)
            const startY = 50 + 40 * Math.sin((startAngle * Math.PI) / 180)
            const endX = 50 + 40 * Math.cos((endAngle * Math.PI) / 180)
            const endY = 50 + 40 * Math.sin((endAngle * Math.PI) / 180)
            
            // 确定是否需要使用大弧（large-arc-flag）
            const largeArcFlag = item.percentage > 50 ? 1 : 0
            
            // 构建SVG路径
            const path = `
              M 50 50
              L ${startX} ${startY}
              A 40 40 0 ${largeArcFlag} 1 ${endX} ${endY}
              Z
            `
            
            return (
              <path
                key={index}
                d={path}
                fill={item.color}
                stroke="white"
                strokeWidth="1"
                className="transition-opacity duration-300 hover:opacity-80"
              />
            )
          })}
        </svg>
      </div>
    )
  }
  
  // 渲染图例
  const renderLegend = () => {
    return (
      <div className="mt-4 grid grid-cols-2 gap-2">
        {paymentDataWithPercentage.map((item, index) => (
          <div key={index} className="flex items-center gap-2">
            <div 
              className="h-3 w-3 rounded-sm" 
              style={{ backgroundColor: item.color }}
            />
            <div className="flex items-center justify-between gap-2 text-sm">
              <span>{item.method}</span>
              <span className="font-medium">{item.percentage}%</span>
            </div>
          </div>
        ))}
      </div>
    )
  }
  
  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-base">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {renderPieChart()}
        {renderLegend()}
      </CardContent>
    </Card>
  )
}
