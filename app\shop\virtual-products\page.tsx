"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Search, Plus, MoreHorizontal, Edit, Trash2, Eye, Copy, Tag, CreditCard, BookOpen, UserPlus } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

// 导入自定义组件
import { VirtualProductDetailDialog } from "@/components/shop/virtual-product-detail-dialog"
import { VirtualProductFormDialog } from "@/components/shop/virtual-product-form-dialog"

// 模拟虚拟商品数据
const virtualProducts = [
  {
    id: "1",
    name: "高级会员卡 - 年卡",
    description: "瑜伽馆高级会员年卡，享受全年不限次数的课程和场地使用权",
    price: "3688.00",
    originalPrice: "4988.00",
    sales: 45,
    category: "会员卡",
    type: "membership", // 会员卡
    status: "active",
    validityPeriod: "365", // 有效期（天）
    autoActivate: true, // 自动激活
    benefits: [
      "不限次数上课",
      "专属更衣柜",
      "免费停车",
      "优先预约",
      "会员专属活动"
    ],
    createdAt: "2023-06-01",
  },
  {
    id: "2",
    name: "瑜伽入门课程 - 在线视频",
    description: "专业教练指导的瑜伽入门课程，包含10节视频课程",
    price: "99.00",
    originalPrice: "199.00",
    sales: 256,
    category: "在线课程",
    type: "course", // 课程
    status: "active",
    validityPeriod: "forever", // 永久有效
    autoActivate: true, // 自动激活
    courseDetails: {
      lessons: 10,
      duration: "5小时30分钟",
      level: "入门",
      format: "视频",
      instructor: "王教练"
    },
    createdAt: "2023-07-01",
  },
  {
    id: "3",
    name: "高级私教身份 - 月卡",
    description: "每月享受4次私教课程，专业教练一对一指导",
    price: "1288.00",
    originalPrice: "1688.00",
    sales: 32,
    category: "身份卡",
    type: "identity", // 身份卡
    status: "active",
    validityPeriod: "30", // 有效期（天）
    autoActivate: false, // 手动激活
    benefits: [
      "每月4次私教课",
      "专业一对一指导",
      "个性化训练计划",
      "体能评估"
    ],
    createdAt: "2023-08-01",
  },
  {
    id: "4",
    name: "瑜伽进阶课程 - 视频系列",
    description: "适合有一定基础的学员，提升瑜伽技巧和体式",
    price: "199.00",
    originalPrice: "299.00",
    sales: 128,
    category: "在线课程",
    type: "course", // 课程
    status: "active",
    validityPeriod: "forever", // 永久有效
    autoActivate: true, // 自动激活
    courseDetails: {
      lessons: 15,
      duration: "8小时45分钟",
      level: "进阶",
      format: "视频",
      instructor: "李教练"
    },
    createdAt: "2023-08-15",
  },
  {
    id: "5",
    name: "普通会员卡 - 季卡",
    description: "瑜伽馆普通会员季卡，每周可上3次常规课程",
    price: "888.00",
    originalPrice: "1088.00",
    sales: 67,
    category: "会员卡",
    type: "membership", // 会员卡
    status: "active",
    validityPeriod: "90", // 有效期（天）
    autoActivate: true, // 自动激活
    benefits: [
      "每周3次常规课程",
      "会员价购买私教课",
      "会员专属活动"
    ],
    createdAt: "2023-09-01",
  },
  {
    id: "6",
    name: "瑜伽教练培训课程",
    description: "专业瑜伽教练培训课程，包含理论和实践内容",
    price: "4999.00",
    originalPrice: "5999.00",
    sales: 15,
    category: "在线课程",
    type: "course", // 课程
    status: "active",
    validityPeriod: "180", // 有效期（天）
    autoActivate: false, // 手动激活
    courseDetails: {
      lessons: 30,
      duration: "40小时",
      level: "专业",
      format: "视频+直播",
      instructor: "张教练"
    },
    createdAt: "2023-09-15",
  },
]

export default function VirtualProductsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("all")
  const { toast } = useToast()

  // 对话框状态
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [showFormDialog, setShowFormDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedProductId, setSelectedProductId] = useState("")
  const [selectedProduct, setSelectedProduct] = useState<any>(null)

  // 过滤虚拟商品
  const filteredProducts = virtualProducts.filter(
    (product) => {
      // 基本搜索过滤
      const basicFilter = (
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase())
      )

      // 类型过滤
      const typeFilterMatch = typeFilter === "all" || product.type === typeFilter

      // 标签页过滤
      let tabFilterMatch = true
      if (activeTab === "membership") {
        tabFilterMatch = product.type === "membership"
      } else if (activeTab === "course") {
        tabFilterMatch = product.type === "course"
      } else if (activeTab === "identity") {
        tabFilterMatch = product.type === "identity"
      }

      return basicFilter && typeFilterMatch && tabFilterMatch
    }
  )

  // 获取商品类型图标
  const getTypeIcon = (type) => {
    switch (type) {
      case "membership":
        return <CreditCard className="h-4 w-4" />
      case "course":
        return <BookOpen className="h-4 w-4" />
      case "identity":
        return <UserPlus className="h-4 w-4" />
      default:
        return null
    }
  }

  // 获取商品类型名称
  const getTypeName = (type) => {
    switch (type) {
      case "membership":
        return "会员卡"
      case "course":
        return "在线课程"
      case "identity":
        return "身份卡"
      default:
        return type
    }
  }

  // 处理查看详情
  const handleViewDetail = (productId: string) => {
    setSelectedProductId(productId)
    setShowDetailDialog(true)
  }

  // 处理添加商品
  const handleAddProduct = () => {
    setSelectedProduct(null)
    setShowFormDialog(true)
  }

  // 处理编辑商品
  const handleEditProduct = (product: any) => {
    setSelectedProduct(product)
    setShowFormDialog(true)
  }

  // 处理删除商品
  const handleDeleteClick = (productId: string) => {
    setSelectedProductId(productId)
    setShowDeleteDialog(true)
  }

  // 确认删除商品
  const confirmDelete = () => {
    // 这里应该调用API删除商品
    toast({
      title: "删除成功",
      description: "虚拟商品已成功删除",
    })
    setShowDeleteDialog(false)
  }

  // 保存商品（创建或更新）
  const handleSaveProduct = async (data: any) => {
    try {
      // 这里应该调用API保存商品
      console.log("保存商品数据:", data)

      toast({
        title: selectedProduct ? "更新成功" : "创建成功",
        description: selectedProduct ? "虚拟商品已更新" : "新虚拟商品已创建",
      })

      return true
    } catch (error) {
      console.error("保存失败:", error)
      toast({
        title: "保存失败",
        description: "请稍后重试",
        variant: "destructive"
      })
      return false
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">虚拟商品管理</h1>
        <Button onClick={handleAddProduct}>
          <Plus className="mr-2 h-4 w-4" />
          添加虚拟商品
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 max-w-md">
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="membership">会员卡</TabsTrigger>
          <TabsTrigger value="course">在线课程</TabsTrigger>
          <TabsTrigger value="identity">身份卡</TabsTrigger>
        </TabsList>

        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mt-6">
          <div className="flex flex-1 items-center gap-2 max-w-md">
            <Input
              placeholder="搜索商品名称或描述..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>
          <div className="flex items-center gap-2">
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="商品类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                <SelectItem value="membership">会员卡</SelectItem>
                <SelectItem value="course">在线课程</SelectItem>
                <SelectItem value="identity">身份卡</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <TabsContent value={activeTab} className="mt-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>虚拟商品列表</CardTitle>
              <CardDescription>管理会员卡、在线课程和身份卡等虚拟商品</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>商品名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>销量</TableHead>
                    <TableHead>有效期</TableHead>
                    <TableHead>自动激活</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">
                        <div className="flex flex-col">
                          <span>{product.name}</span>
                          <span className="text-muted-foreground text-xs line-clamp-1">{product.description}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {getTypeIcon(product.type)}
                          <span>{getTypeName(product.type)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-primary">¥{product.price}</span>
                          {product.originalPrice && (
                            <span className="text-muted-foreground line-through text-xs">¥{product.originalPrice}</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{product.sales}</TableCell>
                      <TableCell>
                        {product.validityPeriod === "forever" ? "永久有效" : `${product.validityPeriod}天`}
                      </TableCell>
                      <TableCell>
                        <Badge variant={product.autoActivate ? "default" : "outline"}>
                          {product.autoActivate ? "自动激活" : "手动激活"}
                        </Badge>
                      </TableCell>
                      <TableCell>{product.createdAt}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleViewDetail(product.id)}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditProduct(product)}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑商品
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Copy className="mr-2 h-4 w-4" />
                              复制商品
                            </DropdownMenuItem>
                            {product.type === "membership" && (
                              <DropdownMenuItem onClick={() => handleViewDetail(product.id)}>
                                <Tag className="mr-2 h-4 w-4" />
                                管理权益
                              </DropdownMenuItem>
                            )}
                            {product.type === "course" && (
                              <DropdownMenuItem onClick={() => handleViewDetail(product.id)}>
                                <BookOpen className="mr-2 h-4 w-4" />
                                管理课程内容
                              </DropdownMenuItem>
                            )}
                            {product.type === "identity" && (
                              <DropdownMenuItem onClick={() => handleViewDetail(product.id)}>
                                <UserPlus className="mr-2 h-4 w-4" />
                                管理身份权限
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDeleteClick(product.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除商品
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 虚拟商品详情对话框 */}
      <VirtualProductDetailDialog
        open={showDetailDialog}
        onOpenChange={setShowDetailDialog}
        productId={selectedProductId}
      />

      {/* 虚拟商品表单对话框 */}
      <VirtualProductFormDialog
        open={showFormDialog}
        onOpenChange={setShowFormDialog}
        product={selectedProduct}
        onSave={handleSaveProduct}
      />

      {/* 删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这个虚拟商品吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
