version: '3.8'

services:
  # 后端API服务
  yoga-backend:
    image: yoga-backend:latest  # 您的后端镜像
    container_name: yoga-backend
    ports:
      - "8000:8000"
    networks:
      - yoga-network
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  # 前端服务
  yoga-frontend:
    image: yoga-admin:latest  # 您的前端镜像
    container_name: yoga-frontend
    ports:
      - "3001:3001"
    networks:
      - yoga-network
    environment:
      # 客户端API调用（浏览器访问）
      - NEXT_PUBLIC_API_BASE_URL=http://120.79.162.122:8000
      # 服务端API调用（容器内部网络）
      - API_BASE_URL=http://yoga-backend:8000
      - PORT=3001
      - NODE_ENV=production
    depends_on:
      - yoga-backend
    restart: unless-stopped
    volumes:
      - /var/www/wonderPy-www:/var/www/wonderPy-www

networks:
  yoga-network:
    driver: bridge
