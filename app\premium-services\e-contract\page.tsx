"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  Search,
  ChevronRight,
  FileText,
  Plus,
  Download,
  Filter,
  RefreshCw,
  MoreHorizontal,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileSignature,
  Eye,
  Trash2,
  Copy,
  Send,
  UserPlus,
  Settings,
  ExternalLink,
  Building,
  Users,
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "@/components/ui/use-toast"
import { CreateContractDialog } from "@/components/contracts/create-contract-dialog"
import { ContractDetailDialog } from "@/components/contracts/contract-detail-dialog"
import { ContractTemplateDialog } from "@/components/contracts/contract-template-dialog"
import { ContractSettingsDialog } from "@/components/contracts/contract-settings-dialog"
import { UploadTemplateDialog } from "@/components/contracts/upload-template-dialog"
import { RealNameAuthDialog } from "@/components/contracts/real-name-auth-dialog"
import { SignatureUploadDialog } from "@/components/contracts/signature-upload-dialog"
import { RechargeDialog } from "@/components/contracts/recharge-dialog"
import { AccountInfo } from "@/components/contracts/account-info"

// 模拟合同数据
const contracts = [
  // 会员合同
  {
    id: "C001",
    title: "会员服务协议",
    templateName: "标准会员协议",
    serialNumber: "M20230001",
    status: "COMPLETED",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "张三", type: "PERSON", mobile: "***********" }
    ],
    createdAt: "2023-06-01 10:30",
    completedAt: "2023-06-01 14:45",
    category: "会员合同"
  },
  {
    id: "C005",
    title: "会员卡购买协议",
    templateName: "会员卡标准协议",
    serialNumber: "M20230005",
    status: "COMPLETED",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "王五", type: "PERSON", mobile: "***********" }
    ],
    createdAt: "2023-06-20 16:45",
    completedAt: "2023-06-20 18:30",
    category: "会员合同"
  },

  // 教练合同
  {
    id: "C002",
    title: "教练聘用合同",
    templateName: "标准教练合同",
    serialNumber: "T20230002",
    status: "SIGNING",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "李教练", type: "PERSON", mobile: "13800138002" }
    ],
    createdAt: "2023-06-05 09:15",
    completedAt: null,
    category: "教练合同"
  },

  // 场地和采购合同
  {
    id: "C003",
    title: "场地租赁协议",
    templateName: "场地租赁标准协议",
    serialNumber: "V20230003",
    status: "DRAFT",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "某商业广场", type: "COMPANY" }
    ],
    createdAt: "2023-06-10 14:20",
    completedAt: null,
    category: "场地合同"
  },
  {
    id: "C004",
    title: "设备采购合同",
    templateName: "设备采购标准协议",
    serialNumber: "E20230004",
    status: "REJECTED",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "某瑜伽器材公司", type: "COMPANY" }
    ],
    createdAt: "2023-06-15 11:30",
    completedAt: null,
    category: "采购合同"
  },

  // 股东合同
  {
    id: "C006",
    title: "消费型股东协议",
    templateName: "消费型股东协议",
    serialNumber: "S20230006",
    status: "COMPLETED",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "刘芳", type: "PERSON", mobile: "13800138006" }
    ],
    createdAt: "2023-08-10 09:30",
    completedAt: "2023-08-10 15:20",
    category: "股东合同",
    subCategory: "消费型股东"
  },
  {
    id: "C007",
    title: "投资型股东协议",
    templateName: "投资型股东协议",
    serialNumber: "S20230007",
    status: "SIGNING",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "赵明", type: "PERSON", mobile: "13800138007" }
    ],
    createdAt: "2023-08-15 10:45",
    completedAt: null,
    category: "股东合同",
    subCategory: "投资型股东"
  },
  {
    id: "C008",
    title: "资源型股东协议",
    templateName: "资源型股东协议",
    serialNumber: "S20230008",
    status: "DRAFT",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "孙媛", type: "PERSON", mobile: "13800138008" }
    ],
    createdAt: "2023-08-20 14:30",
    completedAt: null,
    category: "股东合同",
    subCategory: "资源型股东"
  },
  {
    id: "C009",
    title: "员工型股东协议",
    templateName: "员工型股东协议",
    serialNumber: "S20230009",
    status: "COMPLETED",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "钱教练", type: "PERSON", mobile: "13800138009" }
    ],
    createdAt: "2023-08-25 11:20",
    completedAt: "2023-08-25 16:45",
    category: "股东合同",
    subCategory: "员工型股东"
  },
  {
    id: "C010",
    title: "联盟型股东协议",
    templateName: "联盟型股东协议",
    serialNumber: "S20230010",
    status: "SIGNING",
    parties: [
      { name: "静心瑜伽馆", type: "COMPANY" },
      { name: "健康食品有限公司", type: "COMPANY" }
    ],
    createdAt: "2023-08-30 09:15",
    completedAt: null,
    category: "股东合同",
    subCategory: "联盟型股东"
  }
]

// 模拟合同模板数据
const contractTemplates = [
  // 会员合同模板
  {
    id: "T001",
    name: "标准会员协议",
    description: "适用于普通会员的标准服务协议",
    category: "会员合同",
    previewUrl: "/contract-templates/standard-member-agreement.html",
    createdAt: "2023-05-01",
    updatedAt: "2023-05-15"
  },
  {
    id: "T005",
    name: "会员卡标准协议",
    description: "适用于会员卡购买的标准协议",
    category: "会员合同",
    previewUrl: "/contract-templates/membership-card-agreement.html",
    createdAt: "2023-05-05",
    updatedAt: "2023-05-19"
  },

  // 教练合同模板
  {
    id: "T002",
    name: "标准教练合同",
    description: "适用于瑜伽教练的聘用合同",
    category: "教练合同",
    previewUrl: "/contract-templates/coach-employment-contract.html",
    createdAt: "2023-05-02",
    updatedAt: "2023-05-16"
  },

  // 场地和采购合同模板
  {
    id: "T003",
    name: "场地租赁标准协议",
    description: "适用于场地租赁的标准协议",
    category: "场地合同",
    previewUrl: "/contract-templates/venue-rental-agreement.html",
    createdAt: "2023-05-03",
    updatedAt: "2023-05-17"
  },
  {
    id: "T004",
    name: "设备采购标准协议",
    description: "适用于瑜伽设备采购的标准协议",
    category: "采购合同",
    previewUrl: "/contract-templates/equipment-purchase-agreement.html",
    createdAt: "2023-05-04",
    updatedAt: "2023-05-18"
  },

  // 股东合同模板
  {
    id: "T006",
    name: "消费型股东协议",
    description: "适用于消费型股东的权益和分红协议",
    category: "股东合同",
    previewUrl: "/contract-templates/consumer-shareholder-agreement.html",
    createdAt: "2023-08-01",
    updatedAt: "2023-08-15"
  },
  {
    id: "T007",
    name: "投资型股东协议",
    description: "适用于投资型股东的权益和分红协议",
    category: "股东合同",
    previewUrl: "/contract-templates/investor-shareholder-agreement.html",
    createdAt: "2023-08-02",
    updatedAt: "2023-08-16"
  },
  {
    id: "T008",
    name: "资源型股东协议",
    description: "适用于资源型股东的权益和分红协议",
    category: "股东合同",
    previewUrl: "/contract-templates/resource-shareholder-agreement.html",
    createdAt: "2023-08-03",
    updatedAt: "2023-08-17"
  },
  {
    id: "T009",
    name: "员工型股东协议",
    description: "适用于员工型股东的权益和分红协议",
    category: "股东合同",
    previewUrl: "/contract-templates/employee-shareholder-agreement.html",
    createdAt: "2023-08-04",
    updatedAt: "2023-08-18"
  },
  {
    id: "T010",
    name: "联盟型股东协议",
    description: "适用于联盟型股东的权益和分红协议",
    category: "股东合同",
    previewUrl: "/contract-templates/alliance-shareholder-agreement.html",
    createdAt: "2023-08-05",
    updatedAt: "2023-08-19"
  }
]

export default function EContractPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState("contracts")
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [showTemplateDialog, setShowTemplateDialog] = useState(false)
  const [showSettingsDialog, setShowSettingsDialog] = useState(false)
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [selectedContract, setSelectedContract] = useState<any>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null)

  // 过滤合同
  const filteredContracts = contracts.filter(
    (contract) => {
      const matchesSearch =
        contract.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        contract.serialNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        contract.parties.some(party => party.name.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesStatus =
        statusFilter === "all" ||
        (statusFilter === "completed" && contract.status === "COMPLETED") ||
        (statusFilter === "signing" && contract.status === "SIGNING") ||
        (statusFilter === "draft" && contract.status === "DRAFT") ||
        (statusFilter === "rejected" && contract.status === "REJECTED");

      // 支持股东合同子类型筛选
      const matchesCategory =
        categoryFilter === "all" ||
        contract.category === categoryFilter ||
        (categoryFilter === "股东合同" && contract.category === "股东合同") ||
        (contract.subCategory && contract.subCategory === categoryFilter);

      return matchesSearch && matchesStatus && matchesCategory;
    }
  )

  // 过滤模板
  const filteredTemplates = contractTemplates.filter(
    (template) => {
      const matchesSearch =
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesCategory =
        categoryFilter === "all" ||
        template.category === categoryFilter;

      return matchesSearch && matchesCategory;
    }
  )

  // 查看合同详情
  const handleViewContract = (contract: any) => {
    setSelectedContract(contract)
    setShowDetailDialog(true)
  }

  // 查看模板详情
  const handleViewTemplate = (template: any) => {
    setSelectedTemplate(template)
    setShowTemplateDialog(true)
  }

  // 处理预览模板
  const handlePreviewTemplate = (template: any, e?: React.MouseEvent) => {
    if (e) e.stopPropagation()
    if (template.previewUrl) {
      window.open(template.previewUrl, '_blank')
    } else {
      toast({
        title: "预览不可用",
        description: "该模板暂无预览",
        variant: "destructive"
      })
    }
  }

  // 处理上传模板成功
  const handleUploadSuccess = (newTemplate: any) => {
    // 在实际应用中，这里应该调用API刷新模板列表
    // 这里我们模拟添加新模板到列表中
    console.log("新上传的模板:", newTemplate)
    toast({
      title: "上传成功",
      description: `模板 ${newTemplate.name} 已成功上传`,
    })
  }

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <Badge className="bg-green-500">已完成</Badge>
      case "SIGNING":
        return <Badge className="bg-blue-500">签署中</Badge>
      case "DRAFT":
        return <Badge variant="outline">草稿</Badge>
      case "REJECTED":
        return <Badge variant="destructive">已拒绝</Badge>
      case "EXPIRED":
        return <Badge variant="secondary">已过期</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">法大大电子合同</h1>
          <p className="text-muted-foreground">安全、合规的电子合同签署和管理解决方案</p>
        </div>
      </div>

      <div className="text-sm breadcrumbs">
        <ul className="flex items-center space-x-1">
          <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li>法大大电子合同</li>
        </ul>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 max-w-xl">
          <TabsTrigger value="contracts">合同管理</TabsTrigger>
          <TabsTrigger value="templates">合同模板</TabsTrigger>
          <TabsTrigger value="enterprise">企业功能</TabsTrigger>
          <TabsTrigger value="settings">系统设置</TabsTrigger>
        </TabsList>

        {/* 合同管理 */}
        <TabsContent value="contracts" className="space-y-4 mt-6">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="flex flex-1 items-center space-x-2">
              <Input
                placeholder="搜索合同标题、编号或签署方..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-md"
              />
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>筛选条件</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>状态</DropdownMenuLabel>
                  <DropdownMenuItem onClick={() => setStatusFilter("all")}>
                    全部状态
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("completed")}>
                    已完成
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("signing")}>
                    签署中
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("draft")}>
                    草稿
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter("rejected")}>
                    已拒绝
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>合同类型</DropdownMenuLabel>
                  <DropdownMenuItem onClick={() => setCategoryFilter("all")}>
                    全部类型
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("会员合同")}>
                    会员合同
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("教练合同")}>
                    教练合同
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("场地合同")}>
                    场地合同
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("采购合同")}>
                    采购合同
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setCategoryFilter("股东合同")}>
                    股东合同
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("消费型股东")}>
                    消费型股东
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("投资型股东")}>
                    投资型股东
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("资源型股东")}>
                    资源型股东
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("员工型股东")}>
                    员工型股东
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("联盟型股东")}>
                    联盟型股东
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              创建合同
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>合同标题</TableHead>
                    <TableHead>合同编号</TableHead>
                    <TableHead>签署方</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredContracts.map((contract) => (
                    <TableRow key={contract.id} className="cursor-pointer" onClick={() => handleViewContract(contract)}>
                      <TableCell className="font-medium">{contract.title}</TableCell>
                      <TableCell>{contract.serialNumber}</TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {contract.parties.map((party, index) => (
                            <div key={index} className="text-sm">
                              {party.name}
                              <span className="text-xs text-muted-foreground ml-1">
                                ({party.type === "COMPANY" ? "公司" : "个人"})
                              </span>
                            </div>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>{contract.createdAt}</TableCell>
                      <TableCell>{getStatusBadge(contract.status)}</TableCell>
                      <TableCell>{contract.category}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              handleViewContract(contract);
                            }}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            {contract.status === "DRAFT" && (
                              <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                                <Send className="mr-2 h-4 w-4" />
                                发起签署
                              </DropdownMenuItem>
                            )}
                            {contract.status === "COMPLETED" && (
                              <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                                <Download className="mr-2 h-4 w-4" />
                                下载合同
                              </DropdownMenuItem>
                            )}
                            {contract.status === "DRAFT" && (
                              <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除合同
                              </DropdownMenuItem>
                            )}
                            {contract.status !== "DRAFT" && contract.status !== "COMPLETED" && (
                              <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                                <AlertCircle className="mr-2 h-4 w-4" />
                                催签
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {filteredContracts.length === 0 && (
                <div className="flex flex-col items-center justify-center p-8 text-center">
                  <FileText className="h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-4 text-lg font-semibold">没有找到合同</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    尝试调整搜索或筛选条件，或者创建一个新合同。
                  </p>
                  <Button className="mt-4" onClick={() => setShowCreateDialog(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    创建合同
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 合同模板 */}
        <TabsContent value="templates" className="space-y-4 mt-6">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <div className="flex flex-1 items-center space-x-2">
              <Input
                placeholder="搜索模板名称或描述..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-md"
              />
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>筛选条件</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>合同类型</DropdownMenuLabel>
                  <DropdownMenuItem onClick={() => setCategoryFilter("all")}>
                    全部类型
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("会员合同")}>
                    会员合同
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("教练合同")}>
                    教练合同
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("场地合同")}>
                    场地合同
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setCategoryFilter("采购合同")}>
                    采购合同
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setCategoryFilter("股东合同")}>
                    股东合同
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <Button variant="outline" onClick={() => setShowUploadDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              上传模板
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.map((template) => (
              <Card key={template.id} className="cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => handleViewTemplate(template)}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    <Badge>{template.category}</Badge>
                  </div>
                  <CardDescription>{template.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="mr-1 h-4 w-4" />
                    <span>创建于 {template.createdAt}</span>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground mt-1">
                    <Clock className="mr-1 h-4 w-4" />
                    <span>更新于 {template.updatedAt}</span>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between pt-0">
                  <Button variant="outline" size="sm" onClick={(e) => {
                    e.stopPropagation();
                    setShowCreateDialog(true);
                  }}>
                    <FileSignature className="mr-2 h-4 w-4" />
                    使用此模板
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>操作</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        handleViewTemplate(template);
                      }}>
                        <Eye className="mr-2 h-4 w-4" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => handlePreviewTemplate(template, e)}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        预览模板
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                        <Copy className="mr-2 h-4 w-4" />
                        复制模板
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </CardFooter>
              </Card>
            ))}
          </div>
          {filteredTemplates.length === 0 && (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <FileText className="h-12 w-12 text-muted-foreground/50" />
              <h3 className="mt-4 text-lg font-semibold">没有找到合同模板</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                尝试调整搜索或筛选条件，或者上传一个新模板。
              </p>
              <Button variant="outline" className="mt-4" onClick={() => setShowUploadDialog(true)}>
                <Plus className="mr-2 h-4 w-4" />
                上传模板
              </Button>
            </div>
          )}
        </TabsContent>

        {/* 企业功能 */}
        <TabsContent value="enterprise" className="space-y-4 mt-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium">企业电子合同功能</h3>
              <p className="text-sm text-muted-foreground">管理企业用户、认证信息和员工签署功能</p>
            </div>
            <Button onClick={() => window.location.href = "/premium-services/e-contract/enterprise"}>
              <Building className="mr-2 h-4 w-4" />
              进入企业管理
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  企业多用户管理
                </CardTitle>
                <CardDescription>管理企业内部不同角色的用户及其权限</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">
                  支持企业内部不同角色的用户使用电子签章，包括管理员、经理和普通员工，可以为不同角色分配不同的权限。
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" onClick={() => window.location.href = "/premium-services/e-contract/enterprise?tab=users"}>
                  查看详情
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <FileSignature className="mr-2 h-5 w-5" />
                  企业认证信息变更
                </CardTitle>
                <CardDescription>变更企业认证信息并提交审核</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">
                  支持企业认证信息的变更和更新，包括企业名称、统一社会信用代码、法定代表人等信息，变更后需要提交审核。
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" onClick={() => window.location.href = "/premium-services/e-contract/enterprise?tab=cert-update"}>
                  查看详情
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <CheckCircle className="mr-2 h-5 w-5" />
                  认证信息审核与提醒
                </CardTitle>
                <CardDescription>审核企业认证信息变更申请并设置定期审核提醒</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">
                  支持企业认证信息的审核和定期提醒功能，可以设置审核提醒的频率和提前天数，确保认证信息的有效性。
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" onClick={() => window.location.href = "/premium-services/e-contract/enterprise?tab=cert-audit"}>
                  查看详情
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <UserPlus className="mr-2 h-5 w-5" />
                  企业员工个人签署
                </CardTitle>
                <CardDescription>企业员工使用个人身份进行合同签署</CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground">
                  支持企业员工使用个人身份签署企业合同，包括劳动合同、保密协议等，提高签署效率和合规性。
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" onClick={() => window.location.href = "/premium-services/e-contract/enterprise?tab=employee-sign"}>
                  查看详情
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        {/* 系统设置 */}
        <TabsContent value="settings" className="space-y-4 mt-6">
          <AccountInfo />

          <Card>
            <CardHeader>
              <CardTitle>法大大电子合同配置</CardTitle>
              <CardDescription>配置法大大电子合同的基本参数和签署设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>应用ID (AppId)</Label>
                  <Input type="text" placeholder="请输入法大大应用ID" />
                  <p className="text-sm text-muted-foreground">在法大大开发者平台获取的应用ID</p>
                </div>
                <div className="space-y-2">
                  <Label>应用密钥 (AppSecret)</Label>
                  <Input type="password" placeholder="请输入法大大应用密钥" />
                  <p className="text-sm text-muted-foreground">在法大大开发者平台获取的应用密钥</p>
                </div>
                <div className="space-y-2">
                  <Label>服务器地址</Label>
                  <Input type="text" placeholder="请输入法大大服务器地址" defaultValue="https://api.fadada.com/api/v2" />
                  <p className="text-sm text-muted-foreground">法大大API服务器地址</p>
                </div>
                <div className="space-y-2">
                  <Label>回调地址</Label>
                  <Input type="text" placeholder="请输入回调地址" />
                  <p className="text-sm text-muted-foreground">接收法大大回调通知的地址</p>
                </div>
              </div>
              <Separator />
              <div className="space-y-2">
                <Label>公司信息</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>公司名称</Label>
                    <Input type="text" placeholder="请输入公司名称" defaultValue="静心瑜伽馆" />
                  </div>
                  <div className="space-y-2">
                    <Label>统一社会信用代码</Label>
                    <Input type="text" placeholder="请输入统一社会信用代码" />
                  </div>
                  <div className="space-y-2">
                    <Label>法定代表人</Label>
                    <Input type="text" placeholder="请输入法定代表人姓名" />
                  </div>
                  <div className="space-y-2">
                    <Label>联系电话</Label>
                    <Input type="text" placeholder="请输入联系电话" />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end space-x-2">
              <Button variant="outline">重置</Button>
              <Button>保存配置</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 创建合同对话框 */}
      <CreateContractDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        templates={contractTemplates}
      />

      {/* 合同详情对话框 */}
      {selectedContract && (
        <ContractDetailDialog
          open={showDetailDialog}
          onOpenChange={setShowDetailDialog}
          contract={selectedContract}
        />
      )}

      {/* 模板详情对话框 */}
      {selectedTemplate && (
        <ContractTemplateDialog
          open={showTemplateDialog}
          onOpenChange={setShowTemplateDialog}
          template={selectedTemplate}
        />
      )}

      {/* 上传模板对话框 */}
      <UploadTemplateDialog
        open={showUploadDialog}
        onOpenChange={setShowUploadDialog}
        onUploadSuccess={handleUploadSuccess}
      />
    </div>
  )
}
