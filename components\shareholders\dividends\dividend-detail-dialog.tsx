"use client"

import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Wallet, 
  Calendar, 
  CreditCard, 
  FileText, 
  User,
  Clock,
  CheckCircle,
  Users,
  Receipt,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface DividendDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  dividend: any
}

export function DividendDetailDialog({ 
  open, 
  onOpenChange,
  dividend,
}: DividendDetailDialogProps) {
  if (!dividend) return null

  // 模拟引流客户数据
  const referralCustomers = [
    {
      id: "1",
      name: "客户A",
      joinDate: "2023-05-10",
      consumptionAmount: 2500,
    },
    {
      id: "2",
      name: "客户B",
      joinDate: "2023-05-15",
      consumptionAmount: 1800,
    },
    {
      id: "3",
      name: "客户C",
      joinDate: "2023-05-20",
      consumptionAmount: 2200,
    },
    {
      id: "4",
      name: "客户D",
      joinDate: "2023-05-25",
      consumptionAmount: 1900,
    },
    {
      id: "5",
      name: "客户E",
      joinDate: "2023-05-30",
      consumptionAmount: 2000,
    },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>分红详情</DialogTitle>
          <DialogDescription>
            查看分红记录的详细信息
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center justify-between py-4">
          <div className="flex items-center gap-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={dividend.shareholder.avatar} alt={dividend.shareholder.name} />
              <AvatarFallback>{dividend.shareholder.name[0]}</AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-semibold">{dividend.shareholder.name}</h2>
              <Badge>{dividend.shareholder.type}</Badge>
            </div>
          </div>
          <Badge variant={dividend.status === "已发放" ? "default" : "secondary"} className="text-base py-1 px-3">
            {dividend.status}
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">分红信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center">
                <Wallet className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground mr-2">分红金额:</span>
                <span className="font-medium">¥{dividend.amount.toFixed(2)}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground mr-2">分红周期:</span>
                <span>{dividend.period}</span>
              </div>
              <div className="flex items-center">
                <Receipt className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground mr-2">分红比例:</span>
                <span>{dividend.dividendRate}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground mr-2">创建时间:</span>
                <span>{dividend.createdAt}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">业绩信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground mr-2">引流客户:</span>
                <span>{dividend.referrals}人</span>
              </div>
              <div className="flex items-center">
                <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground mr-2">客户消费:</span>
                <span>¥{dividend.referralAmount?.toFixed(2) || "0.00"}</span>
              </div>
              {dividend.status === "已发放" && (
                <>
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-muted-foreground mr-2">发放时间:</span>
                    <span>{dividend.paidAt}</span>
                  </div>
                  <div className="flex items-center">
                    <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-muted-foreground mr-2">支付方式:</span>
                    <span>{dividend.paymentMethod}</span>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <Card className="mt-4">
          <CardHeader>
            <CardTitle>引流客户明细</CardTitle>
            <CardDescription>本周期内该股东引流的客户消费明细</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>客户姓名</TableHead>
                  <TableHead>加入日期</TableHead>
                  <TableHead>消费金额</TableHead>
                  <TableHead>分红贡献</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {referralCustomers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell>{customer.name}</TableCell>
                    <TableCell>{customer.joinDate}</TableCell>
                    <TableCell>¥{customer.consumptionAmount.toFixed(2)}</TableCell>
                    <TableCell>¥{(customer.consumptionAmount * 0.05).toFixed(2)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card className="mt-4">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">备注</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              {dividend.status === "已发放" 
                ? "该分红已于" + dividend.paidAt + "通过" + dividend.paymentMethod + "发放完成。"
                : "该分红尚未发放，请尽快处理。"}
            </p>
          </CardContent>
        </Card>

        <DialogFooter className="flex justify-between mt-4">
          <Button variant="outline" size="sm">
            <FileText className="mr-2 h-4 w-4" />
            导出明细
          </Button>
          <div className="flex gap-2">
            {dividend.status === "待发放" && (
              <Button variant="default" size="sm">
                <Wallet className="mr-2 h-4 w-4" />
                发放分红
              </Button>
            )}
            <Button variant="outline" onClick={() => onOpenChange(false)}>关闭</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
