"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Plus,
  Save,
  Trash2,
  Copy,
  RefreshCw,
  Search,
  ChevronDown,
  ChevronRight,
  Shield,
  AlertCircle,
  FileText,
  Settings,
  Users,
  Calendar,
  CreditCard,
  BarChart2,
  Home,
  ShoppingCart,
  MoreHorizontal,
  Pencil,
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { useToast } from "@/hooks/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"

const modules = [
  {
    id: "dashboard",
    name: "工作台",
    icon: <Home className="h-4 w-4" />,
    permissions: [
      { id: "dashboard-view", name: "查看", description: "查看工作台数据" },
      { id: "dashboard-export", name: "导出", description: "导出工作台数据" },
    ],
  },
  {
    id: "course",
    name: "课程管理",
    icon: <Calendar className="h-4 w-4" />,
    permissions: [
      { id: "course-view", name: "查看", description: "查看课程信息" },
      { id: "course-create", name: "创建", description: "创建新课程" },
      { id: "course-edit", name: "编辑", description: "编辑课程信息" },
      { id: "course-delete", name: "删除", description: "删除课程" },
      { id: "course-schedule", name: "排课", description: "安排课程时间" },
      { id: "course-type", name: "课程类型", description: "管理课程类型" },
    ],
  },
  {
    id: "member",
    name: "会员管理",
    icon: <Users className="h-4 w-4" />,
    permissions: [
      { id: "member-view", name: "查看", description: "查看会员信息" },
      { id: "member-create", name: "创建", description: "创建新会员" },
      { id: "member-edit", name: "编辑会员信息" },
      { id: "member-delete", name: "删除", description: "删除会员" },
      { id: "member-card", name: "会员卡", description: "管理会员卡" },
      { id: "member-tag", name: "会员标签", description: "管理会员标签" },
      { id: "member-import", name: "导入", description: "批量导入会员" },
      { id: "member-export", name: "导出", description: "导出会员数据" },
    ],
  },
  {
    id: "coach",
    name: "教练管理",
    icon: <Users className="h-4 w-4" />,
    permissions: [
      { id: "coach-view", name: "查看", description: "查看教练信息" },
      { id: "coach-create", name: "创建", description: "创建新教练" },
      { id: "coach-edit", name: "编辑教练信息" },
      { id: "coach-delete", name: "删除教练" },
      { id: "coach-schedule", name: "排班", description: "安排教练排班" },
      { id: "coach-review", name: "评价", description: "管理教练评价" },
    ],
  },
  {
    id: "venue",
    name: "场馆管理",
    icon: <Home className="h-4 w-4" />,
    permissions: [
      { id: "venue-view", name: "查看", description: "查看场馆信息" },
      { id: "venue-create", name: "创建", description: "创建新场馆" },
      { id: "venue-edit", name: "编辑场馆信息" },
      { id: "venue-delete", name: "删除场馆" },
      { id: "venue-booking", name: "预订", description: "管理场馆预订" },
      { id: "venue-equipment", name: "设备", description: "管理场馆设备" },
    ],
  },
  {
    id: "order",
    name: "订单管理",
    icon: <ShoppingCart className="h-4 w-4" />,
    permissions: [
      { id: "order-view", name: "查看", description: "查看订单信息" },
      { id: "order-create", name: "创建", description: "创建新订单" },
      { id: "order-edit", name: "编辑订单信息" },
      { id: "order-delete", name: "删除订单" },
      { id: "order-refund", name: "退款", description: "处理退款申请" },
      { id: "order-export", name: "导出", description: "导出订单数据" },
    ],
  },
  {
    id: "payment",
    name: "支付管理",
    icon: <CreditCard className="h-4 w-4" />,
    permissions: [
      { id: "payment-view", name: "查看", description: "查看支付记录" },
      { id: "payment-config", name: "配置", description: "配置支付方式" },
      { id: "payment-refund", name: "退款", description: "执行退款操作" },
      { id: "payment-reconciliation", name: "对账", description: "执行对账操作" },
    ],
  },
  {
    id: "statistics",
    name: "统计分析",
    icon: <BarChart2 className="h-4 w-4" />,
    permissions: [
      { id: "statistics-view", name: "查看", description: "查看统计数据" },
      { id: "statistics-export", name: "导出", description: "导出统计报表" },
      { id: "statistics-member", name: "会员分析", description: "查看会员分析" },
      { id: "statistics-course", name: "课程分析", description: "查看课程分析" },
      { id: "statistics-revenue", name: "收入分析", description: "查看收入分析" },
      { id: "statistics-coach", name: "教练分析", description: "查看教练分析" },
    ],
  },
  {
    id: "settings",
    name: "系统设置",
    icon: <Settings className="h-4 w-4" />,
    permissions: [
      { id: "settings-basic", name: "基础设置", description: "修改基础设置" },
      { id: "settings-user", name: "用户管理", description: "管理系统用户" },
      { id: "settings-role", name: "角色权限", description: "管理角色权限" },
      { id: "settings-notification", name: "通知设置", description: "配置通知设置" },
      { id: "settings-backup", name: "数据备份", description: "执行数据备份" },
      { id: "settings-log", name: "系统日志", description: "查看系统日志" },
    ],
  },
]

const roles = [
  {
    id: "super-admin",
    name: "超级管理员",
    description: "拥有系统所有权限，可以管理所有功能和数据",
    userCount: 1,
    isSystem: true,
  },
  {
    id: "venue-admin",
    name: "场馆管理员",
    description: "管理场馆相关的所有功能，包括课程、会员、教练等",
    userCount: 3,
    isSystem: true,
  },
  {
    id: "course-admin",
    name: "课程管理员",
    description: "管理课程相关的功能，包括课程安排、类型设置等",
    userCount: 2,
    isSystem: true,
  },
  {
    id: "member-admin",
    name: "会员管理员",
    description: "管理会员相关的功能，包括会员信息、会员卡等",
    userCount: 2,
    isSystem: true,
  },
  {
    id: "reception",
    name: "前台接待",
    description: "负责前台接待工作，包括会员签到、课程预约等",
    userCount: 4,
    isSystem: true,
  },
  {
    id: "finance",
    name: "财务人员",
    description: "管理财务相关功能，包括订单、支付、对账等",
    userCount: 2,
    isSystem: true,
  },
  {
    id: "coach",
    name: "教练",
    description: "查看自己的课程安排、会员信息等",
    userCount: 8,
    isSystem: true,
  },
  {
    id: "custom-role",
    name: "自定义角色",
    description: "根据需求自定义的角色权限",
    userCount: 0,
    isSystem: false,
  },
]

export function RolePermissions() {
  const [selectedRole, setSelectedRole] = useState("super-admin")
  const [openModules, setOpenModules] = useState({})
  const [addRoleOpen, setAddRoleOpen] = useState(false)
  const [editRoleOpen, setEditRoleOpen] = useState(false)
  const [currentRole, setCurrentRole] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const toggleModule = (moduleId) => {
    setOpenModules((prev) => ({
      ...prev,
      [moduleId]: !prev[moduleId],
    }))
  }

  const handleRefresh = () => {
    setIsLoading(true)
    // 模拟刷新操作
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "刷新成功",
        description: "角色权限数据已更新",
      })
    }, 1000)
  }

  const handleEditRole = (role) => {
    setCurrentRole(role)
    setEditRoleOpen(true)
  }

  const handleDeleteRole = (roleId) => {
    if (roles.find((r) => r.id === roleId).isSystem) {
      toast({
        title: "无法删除",
        description: "系统预设角色不能删除",
        variant: "destructive",
      })
      return
    }

    toast({
      title: "角色已删除",
      description: `角色 ID ${roleId} 已成功删除`,
      variant: "destructive",
    })
  }

  const handleDuplicateRole = (roleId) => {
    toast({
      title: "角色已复制",
      description: `已创建角色 ID ${roleId} 的副本`,
    })
  }

  const handleSavePermissions = () => {
    setIsLoading(true)
    // 模拟保存操作
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "权限已保存",
        description: "角色权限设置已成功保存",
      })
    }, 1000)
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>角色权限</CardTitle>
            <CardDescription>管理系统角色和权限设置</CardDescription>
          </div>
          <div className="flex gap-2">
            <Dialog open={addRoleOpen} onOpenChange={setAddRoleOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  添加��色
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>添加新角色</DialogTitle>
                  <DialogDescription>创建新的系统角色并分配权限</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="role-name" className="text-right">
                      角色名称
                    </Label>
                    <Input id="role-name" className="col-span-3" placeholder="输入角色名称" />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="role-description" className="text-right">
                      角色描述
                    </Label>
                    <Textarea id="role-description" className="col-span-3" placeholder="输入角色描述" rows={3} />
                  </div>

                  <Separator className="my-2" />

                  <div className="space-y-2">
                    <Label>权限模板</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2 border rounded-md p-3">
                        <Checkbox id="template-venue-admin" />
                        <div>
                          <Label htmlFor="template-venue-admin">场馆管理员</Label>
                          <p className="text-xs text-muted-foreground">场馆相关的所有管理权限</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 border rounded-md p-3">
                        <Checkbox id="template-course-admin" />
                        <div>
                          <Label htmlFor="template-course-admin">课程管理员</Label>
                          <p className="text-xs text-muted-foreground">课程相关的所有管理权限</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 border rounded-md p-3">
                        <Checkbox id="template-member-admin" />
                        <div>
                          <Label htmlFor="template-member-admin">会员管理员</Label>
                          <p className="text-xs text-muted-foreground">会员相关的所有管理权限</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 border rounded-md p-3">
                        <Checkbox id="template-reception" />
                        <div>
                          <Label htmlFor="template-reception">前台接待</Label>
                          <p className="text-xs text-muted-foreground">前台日常工作所需权限</p>
                        </div>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      选择权限模板可以快速设置常用权限组合，您也可以在创建后进一步自定义
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox id="customize-now" />
                    <Label htmlFor="customize-now">立即自定义权限</Label>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setAddRoleOpen(false)}>
                    取消
                  </Button>
                  <Button
                    onClick={() => {
                      setAddRoleOpen(false)
                      toast({
                        title: "角色创建成功",
                        description: "新角色已成功添加到系统",
                      })
                    }}
                  >
                    创建角色
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              {isLoading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-6">
            <div className="md:w-1/3 space-y-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input placeholder="搜索角色..." className="pl-8" />
              </div>

              <div className="border rounded-md">
                {roles.map((role) => (
                  <div
                    key={role.id}
                    className={`flex items-center justify-between p-3 cursor-pointer hover:bg-muted ${selectedRole === role.id ? "bg-muted" : ""} ${role.id !== roles[roles.length - 1].id ? "border-b" : ""}`}
                    onClick={() => setSelectedRole(role.id)}
                  >
                    <div className="flex flex-col">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{role.name}</span>
                        {role.isSystem && (
                          <Badge variant="outline" className="text-xs">
                            系统
                          </Badge>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground">{role.userCount} 个用户</span>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>角色操作</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleEditRole(role)}>
                          <Pencil className="mr-2 h-4 w-4" />
                          编辑角色
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDuplicateRole(role.id)}>
                          <Copy className="mr-2 h-4 w-4" />
                          复制角色
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className={role.isSystem ? "text-muted" : "text-red-600"}
                          disabled={role.isSystem}
                          onClick={() => handleDeleteRole(role.id)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除角色
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>

              <div className="rounded-md border p-4 bg-muted/50">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="h-4 w-4 text-muted-foreground" />
                  <h3 className="text-sm font-medium">角色说明</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  系统预设角色不可删除，但可以修改其权限设置。自定义角色可以完全自由配置。
                </p>
                <div className="mt-2 text-sm">
                  <div className="flex items-center gap-1">
                    <Shield className="h-3 w-3 text-green-500" />
                    <span className="text-green-600">绿色</span>
                    <span className="text-muted-foreground">- 已授权</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Shield className="h-3 w-3 text-red-500" />
                    <span className="text-red-600">红色</span>
                    <span className="text-muted-foreground">- 已禁用</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Shield className="h-3 w-3 text-muted-foreground" />
                    <span className="text-muted-foreground">灰色 - 未设置</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="md:w-2/3 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold">
                    {roles.find((r) => r.id === selectedRole)?.name || "角色权限"}
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    {roles.find((r) => r.id === selectedRole)?.description || "设置角色的权限"}
                  </p>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <FileText className="mr-2 h-4 w-4" />
                    导出权限
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSavePermissions}
                    disabled={isLoading || selectedRole === "super-admin"}
                  >
                    {isLoading ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        保存中...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        保存权限
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {selectedRole === "super-admin" && (
                <div className="rounded-md border p-4 bg-yellow-50 text-yellow-800 mb-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5" />
                    <p className="font-medium">超级管理员拥有所有权限</p>
                  </div>
                  <p className="mt-1 text-sm">超级管理员角色默认拥有系统中的所有权限，且不可修改。请谨慎分配此角色。</p>
                </div>
              )}

              <div className="space-y-4">
                {modules.map((module) => (
                  <Collapsible
                    key={module.id}
                    open={openModules[module.id]}
                    onOpenChange={() => toggleModule(module.id)}
                    className="border rounded-md overflow-hidden"
                  >
                    <CollapsibleTrigger className="flex items-center justify-between w-full p-4 hover:bg-muted/50">
                      <div className="flex items-center gap-2">
                        {module.icon}
                        <span className="font-medium">{module.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {selectedRole !== "super-admin" && (
                          <Switch checked={true} disabled={selectedRole === "super-admin"} className="mr-2" />
                        )}
                        {openModules[module.id] ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <Separator />
                      <div className="p-4 space-y-2">
                        {module.permissions.map((permission) => (
                          <div key={permission.id} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Shield className={`h-4 w-4 ${selectedRole === "super-admin" ? "text-green-500" : ""}`} />
                              <Label htmlFor={`${selectedRole}-${permission.id}`} className="flex items-center gap-2">
                                <span>{permission.name}</span>
                                <span className="text-sm text-muted-foreground">({permission.description})</span>
                              </Label>
                            </div>
                            <Checkbox
                              id={`${selectedRole}-${permission.id}`}
                              defaultChecked={selectedRole === "super-admin"}
                              disabled={selectedRole === "super-admin"}
                            />
                          </div>
                        ))}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                ))}
              </div>

              {selectedRole !== "super-admin" && (
                <div className="flex justify-end mt-4">
                  <Button onClick={handleSavePermissions} disabled={isLoading}>
                    {isLoading ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        保存中...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        保存权限设置
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <Dialog open={editRoleOpen} onOpenChange={setEditRoleOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑角色</DialogTitle>
            <DialogDescription>修改角色信息和基本设置</DialogDescription>
          </DialogHeader>
          {currentRole && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-role-name" className="text-right">
                  角色名称
                </Label>
                <Input
                  id="edit-role-name"
                  className="col-span-3"
                  defaultValue={currentRole.name}
                  disabled={currentRole.isSystem}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-role-description" className="text-right">
                  角色描述
                </Label>
                <Textarea
                  id="edit-role-description"
                  className="col-span-3"
                  defaultValue={currentRole.description}
                  rows={3}
                />
              </div>

              <Separator className="my-2" />

              <div className="grid grid-cols-4 items-center gap-4">
                <div className="text-right">
                  <Label>角色类型</Label>
                </div>
                <div className="col-span-3">
                  <Badge variant={currentRole.isSystem ? "secondary" : "outline"}>
                    {currentRole.isSystem ? "系统预设" : "自定义"}
                  </Badge>
                  {currentRole.isSystem && (
                    <p className="text-xs text-muted-foreground mt-1">
                      系统预设角色的基本信息不可修改，但可以调整权限设置
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <div className="text-right">
                  <Label htmlFor="role-priority">优先级</Label>
                </div>
                <div className="col-span-3">
                  <Input id="role-priority" type="number" defaultValue="10" className="w-24" />
                  <p className="text-xs text-muted-foreground mt-1">
                    当用户拥有多个角色时，高优先级的角色权限将覆盖低优先级的角色权限
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <div className="text-right">
                  <Label>数据权限</Label>
                </div>
                <div className="col-span-3 space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="data-all"
                      defaultChecked={currentRole.id === "super-admin"}
                      disabled={currentRole.id === "super-admin"}
                    />
                    <Label htmlFor="data-all">所有数据</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="data-department"
                      defaultChecked={currentRole.id === "venue-admin"}
                      disabled={currentRole.id === "super-admin"}
                    />
                    <Label htmlFor="data-department">本部门数据</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="data-self"
                      defaultChecked={currentRole.id === "coach"}
                      disabled={currentRole.id === "super-admin"}
                    />
                    <Label htmlFor="data-self">仅个人数据</Label>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditRoleOpen(false)}>
              取消
            </Button>
            <Button
              onClick={() => {
                setEditRoleOpen(false)
                toast({
                  title: "角色更新成功",
                  description: "角色信息已成功更新",
                })
              }}
            >
              保存更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

