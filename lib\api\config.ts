/**
 * API 配置文件
 */

// 获取API基础URL - 支持服务端和客户端不同配置
const getApiBaseUrl = () => {
  // 服务端渲染时使用内部网络地址
  if (typeof window === 'undefined') {
    return process.env.API_BASE_URL || 'http://localhost:8000';
  }
  // 客户端使用公网地址
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
};

// API 基础配置
export const API_CONFIG = {
  // 基础 URL，根据环境和运行时配置
  BASE_URL: getApiBaseUrl(),
  // 请求超时时间（毫秒）
  TIMEOUT: 10000,
  // 请求头
  HEADERS: {
    'Content-Type': 'application/json',
  },
} as const;

// 响应状态码
export const STATUS_CODE = {
  SUCCESS: 200,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
} as const;

// 响应消息
export const MESSAGE = {
  SUCCESS: '操作成功',
  ERROR: '操作失败',
  UNAUTHORIZED: '未授权，请重新登录',
  FORBIDDEN: '拒绝访问',
  NOT_FOUND: '请求的资源不存在',
  SERVER_ERROR: '服务器错误',
} as const;