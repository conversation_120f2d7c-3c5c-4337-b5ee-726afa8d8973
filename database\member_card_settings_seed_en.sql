-- Member Card Advanced Settings Seed Data

-- 1. Member Card Advanced Settings Data
INSERT INTO member_card_advanced_settings (
  card_type_id, tenant_id, leave_option, leave_times_limit, leave_days_limit, auto_activate_days,
  max_people_per_class, daily_booking_limit, weekly_booking_limit, weekly_calculation_type,
  monthly_booking_limit, monthly_calculation_type, advance_booking_days, advance_booking_unlimited,
  custom_time_enabled, available_days, available_time_slots
) VALUES
-- Annual card settings (ID=1)
(1, 2, 'no_limit', NULL, NULL, 120, 1, 3, 4, 'natural_week', 5, 'natural_month', NULL, TRUE, FALSE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 NULL),

-- Half-year card settings (ID=2)
(2, 2, 'no_limit', NULL, NULL, 90, 1, 2, 3, 'natural_week', 4, 'natural_month', 7, FALSE, TRUE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 JSON_ARRAY(
   JSON_OBJECT('startHour', '06', 'startMinute', '00', 'endHour', '22', 'endMinute', '00'),
   JSON_OBJECT('startHour', '08', 'startMinute', '00', 'endHour', '20', 'endMinute', '00')
 )),

-- Quarterly card settings (ID=3)
(3, 2, 'limited', 2, 10, 60, 1, 2, 2, 'natural_week', 3, 'natural_month', 3, FALSE, FALSE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 NULL),

-- Monthly card settings (ID=4)
(4, 2, 'limited', 1, 5, 30, 1, 1, 2, 'natural_week', 2, 'natural_month', 1, FALSE, FALSE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 NULL),

-- Session card settings (ID=5)
(5, 2, 'no_allow', NULL, NULL, 180, 1, 1, 1, 'natural_week', 1, 'natural_month', 0, FALSE, FALSE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 NULL),

-- Stored value card settings (ID=6)
(6, 2, 'no_limit', NULL, NULL, 365, 1, 5, 10, 'natural_week', 20, 'natural_month', NULL, TRUE, FALSE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 NULL),

-- Trial card settings (ID=7)
(7, 2, 'no_allow', NULL, NULL, 7, 1, 1, 1, 'natural_week', 1, 'natural_month', 0, FALSE, FALSE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 NULL),

-- VIP annual card settings (ID=8)
(8, 2, 'no_limit', NULL, NULL, 180, 2, 5, 8, 'natural_week', 15, 'natural_month', NULL, TRUE, TRUE,
 JSON_OBJECT('monday', true, 'tuesday', true, 'wednesday', true, 'thursday', true, 'friday', true, 'saturday', true, 'sunday', true),
 JSON_ARRAY(
   JSON_OBJECT('startHour', '06', 'startMinute', '00', 'endHour', '23', 'endMinute', '00')
 ));

-- 2. Member Card User Settings Data
INSERT INTO member_card_user_settings (
  card_type_id, tenant_id, booking_interval_enabled, booking_interval_minutes,
  pending_booking_limit, cancel_limit_enabled, cancel_limit_count, cancel_limit_period,
  same_course_daily_limit, peak_time_enabled, peak_start_time, peak_end_time, peak_daily_limit,
  priority_enabled, priority_hours, priority_description
) VALUES
-- Annual card user settings
(1, 2, FALSE, 0, 0, FALSE, 0, 'week', 1, FALSE, '18:00:00', '21:00:00', 1, FALSE, 24, 'Priority booking for members'),
-- Half-year card user settings
(2, 2, TRUE, 60, 2, TRUE, 3, 'week', 1, TRUE, '18:00:00', '21:00:00', 1, TRUE, 24, 'Priority booking for members'),
-- Quarterly card user settings
(3, 2, TRUE, 120, 1, TRUE, 2, 'week', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, 'Priority booking for members'),
-- Monthly card user settings
(4, 2, TRUE, 180, 1, TRUE, 1, 'week', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, 'Priority booking for members'),
-- Session card user settings
(5, 2, TRUE, 240, 1, TRUE, 1, 'day', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, 'Priority booking for members'),
-- Stored value card user settings
(6, 2, FALSE, 0, 0, FALSE, 0, 'week', 2, FALSE, '18:00:00', '21:00:00', 2, FALSE, 24, 'Priority booking for members'),
-- Trial card user settings
(7, 2, TRUE, 1440, 1, TRUE, 0, 'day', 1, TRUE, '18:00:00', '21:00:00', 1, FALSE, 24, 'Priority booking for members'),
-- VIP annual card user settings
(8, 2, FALSE, 0, 0, FALSE, 0, 'week', 2, FALSE, '18:00:00', '21:00:00', 3, TRUE, 48, 'VIP priority booking 48 hours');

-- 3. Member Card Course Settings Data
INSERT INTO member_card_course_settings (
  card_type_id, tenant_id, consumption_rule, consumption_description,
  gift_class_count, gift_value_coefficient, all_courses_enabled
) VALUES
-- Annual card course settings
(1, 2, 'AVERAGE', 'Average consumption rule', 0, 1.0, TRUE),
-- Half-year card course settings
(2, 2, 'AVERAGE', 'Average consumption rule', 0, 1.0, TRUE),
-- Quarterly card course settings
(3, 2, 'AVERAGE', 'Average consumption rule', 0, 1.0, TRUE),
-- Monthly card course settings
(4, 2, 'AVERAGE', 'Average consumption rule', 0, 1.0, TRUE),
-- Session card course settings
(5, 2, 'FIXED', 'Fixed consumption per class', 0, 1.0, FALSE),
-- Stored value card course settings
(6, 2, 'CUSTOM', 'Custom consumption by course type', 0, 1.0, TRUE),
-- Trial card course settings
(7, 2, 'FIXED', 'Trial card for specific courses', 1, 1.0, FALSE),
-- VIP annual card course settings
(8, 2, 'AVERAGE', 'VIP average consumption rule', 5, 1.2, TRUE);

-- 4. Member Card Course Associations Data
INSERT INTO member_card_course_associations (
  card_type_id, course_type_id, tenant_id, is_enabled, consumption_times,
  course_type_name, course_duration
) VALUES
-- Annual card associations
(1, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(1, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),
(1, 17, 2, TRUE, 1.0, 'Yin Yoga', 60),
(1, 18, 2, TRUE, 1.0, 'Aerial Yoga', 60),
(1, 19, 2, TRUE, 1.0, 'Prenatal Yoga', 60),

-- Half-year card associations
(2, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(2, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),
(2, 17, 2, TRUE, 1.0, 'Yin Yoga', 60),
(2, 18, 2, TRUE, 1.0, 'Aerial Yoga', 60),
(2, 19, 2, TRUE, 1.0, 'Prenatal Yoga', 60),

-- Quarterly card associations
(3, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(3, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),
(3, 18, 2, TRUE, 1.0, 'Aerial Yoga', 60),

-- Monthly card associations
(4, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(4, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),

-- Session card associations
(5, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(5, 16, 2, TRUE, 1.0, 'Flow Yoga', 60),

-- Stored value card associations
(6, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),
(6, 16, 2, TRUE, 1.5, 'Flow Yoga', 60),
(6, 17, 2, TRUE, 2.0, 'Yin Yoga', 60),
(6, 18, 2, TRUE, 2.0, 'Aerial Yoga', 60),
(6, 19, 2, TRUE, 1.0, 'Prenatal Yoga', 60),

-- Trial card associations
(7, 15, 2, TRUE, 1.0, 'Hatha Yoga', 60),

-- VIP annual card associations
(8, 15, 2, TRUE, 0.8, 'Hatha Yoga', 60),
(8, 16, 2, TRUE, 0.8, 'Flow Yoga', 60),
(8, 17, 2, TRUE, 0.8, 'Yin Yoga', 60),
(8, 18, 2, TRUE, 0.8, 'Aerial Yoga', 60),
(8, 19, 2, TRUE, 0.8, 'Prenatal Yoga', 60);

-- 5. Member Card Sales Settings Data
INSERT INTO member_card_sales_settings (
  card_type_id, tenant_id, enable_discount, discount_percentage,
  enable_promotion, promotion_type, promotion_description, price_description,
  max_sales_total, max_sales_daily, max_per_user, sale_start_date, sale_end_date
) VALUES
-- Annual card sales settings
(1, 2, TRUE, 15.00, TRUE, 'new', 'New member 15% discount', 'Annual card 365 days unlimited', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- Half-year card sales settings
(2, 2, TRUE, 10.00, TRUE, 'renewal', 'Renewal member 10% discount', 'Half-year card 180 days unlimited', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- Quarterly card sales settings
(3, 2, FALSE, NULL, TRUE, 'group', 'Group purchase discount', 'Quarterly card 90 days unlimited', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- Monthly card sales settings
(4, 2, FALSE, NULL, FALSE, NULL, NULL, 'Monthly card 30 days unlimited', NULL, NULL, 2, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- Session card sales settings
(5, 2, FALSE, NULL, FALSE, NULL, NULL, 'Session card 10 classes', NULL, NULL, 3, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- Stored value card sales settings
(6, 2, TRUE, 5.00, TRUE, 'new', 'Stored value bonus', 'Stored value card flexible payment', NULL, NULL, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- Trial card sales settings
(7, 2, FALSE, NULL, TRUE, 'new', 'New member trial price', 'Trial card 1 class for new members', 100, 10, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59'),
-- VIP annual card sales settings
(8, 2, TRUE, 20.00, TRUE, 'new', 'VIP card 20% discount and exclusive service', 'VIP annual card 365 days unlimited with exclusive service', 50, 2, 1, '2025-01-01 00:00:00', '2025-12-31 23:59:59');
