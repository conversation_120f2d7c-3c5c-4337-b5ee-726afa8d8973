import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface LogisticsCompany {
  id: string
  name: string
  code: string
  trackingUrl: string
  status: string
  defaultCost: string
  freeThreshold: string
  priority: number
  description?: string
  logo?: string
  apiKey?: string
  apiSecret?: string
}

interface CompanyDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  company: LogisticsCompany | null
  mode: "add" | "edit"
}

export function CompanyDialog({ open, onOpenChange, company, mode }: CompanyDialogProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [formData, setFormData] = useState<LogisticsCompany>(
    company || {
      id: "",
      name: "",
      code: "",
      trackingUrl: "",
      status: "active",
      defaultCost: "0.00",
      freeThreshold: "0.00",
      priority: 0,
      description: "",
      logo: "",
      apiKey: "",
      apiSecret: "",
    }
  )

  const handleInputChange = (field: keyof LogisticsCompany, value: string | number | boolean) => {
    setFormData({
      ...formData,
      [field]: value,
    })
  }

  const handleSubmit = () => {
    console.log("保存物流公司信息:", formData)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{mode === "add" ? "添加物流公司" : "编辑物流公司"}</DialogTitle>
          <DialogDescription>
            {mode === "add" ? "添加新的物流公司到系统" : `编辑 ${company?.name} 的信息`}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="pricing">运费设置</TabsTrigger>
            <TabsTrigger value="api">API配置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">物流公司名称</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="例如：顺丰速运"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="code">物流公司代码</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => handleInputChange("code", e.target.value)}
                    placeholder="例如：SF"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="trackingUrl">物流查询网址</Label>
                  <Input
                    id="trackingUrl"
                    value={formData.trackingUrl}
                    onChange={(e) => handleInputChange("trackingUrl", e.target.value)}
                    placeholder="例如：https://www.sf-express.com/cn/sc/dynamic_function/waybill/"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="logo">公司Logo URL</Label>
                  <Input
                    id="logo"
                    value={formData.logo || ""}
                    onChange={(e) => handleInputChange("logo", e.target.value)}
                    placeholder="输入Logo图片URL"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">公司描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description || ""}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="添加物流公司描述信息"
                    className="min-h-[120px]"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="status" className="cursor-pointer">启用状态</Label>
                  <Switch
                    id="status"
                    checked={formData.status === "active"}
                    onCheckedChange={(checked) => handleInputChange("status", checked ? "active" : "inactive")}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="pricing" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">运费设置</CardTitle>
                <CardDescription>设置物流公司的默认运费和免邮门槛</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="defaultCost">默认运费 (元)</Label>
                    <Input
                      id="defaultCost"
                      type="number"
                      value={formData.defaultCost}
                      onChange={(e) => handleInputChange("defaultCost", e.target.value)}
                      placeholder="0.00"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="freeThreshold">免邮门槛 (元)</Label>
                    <Input
                      id="freeThreshold"
                      type="number"
                      value={formData.freeThreshold}
                      onChange={(e) => handleInputChange("freeThreshold", e.target.value)}
                      placeholder="0.00"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">优先级 (数字越小优先级越高)</Label>
                  <Input
                    id="priority"
                    type="number"
                    value={formData.priority}
                    onChange={(e) => handleInputChange("priority", parseInt(e.target.value))}
                    placeholder="1"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="api" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">API配置</CardTitle>
                <CardDescription>配置物流公司API接口信息，用于自动获取物流轨迹</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <Input
                    id="apiKey"
                    value={formData.apiKey || ""}
                    onChange={(e) => handleInputChange("apiKey", e.target.value)}
                    placeholder="输入API Key"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="apiSecret">API Secret</Label>
                  <Input
                    id="apiSecret"
                    type="password"
                    value={formData.apiSecret || ""}
                    onChange={(e) => handleInputChange("apiSecret", e.target.value)}
                    placeholder="输入API Secret"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit}>
            {mode === "add" ? "添加物流公司" : "保存修改"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
