"use client"

import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { useState } from "react"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface MaintenanceScheduleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  equipmentId?: string
  equipmentName?: string
}

export function MaintenanceScheduleDialog({
  open,
  onOpenChange,
  equipmentId = "E001",
  equipmentName = "瑜伽垫",
}: MaintenanceScheduleDialogProps) {
  const [date, setDate] = useState<Date | undefined>(new Date())

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>安排设备维护</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label>设备信息</Label>
            <div className="p-3 border rounded-md">
              <p className="font-medium">{equipmentName}</p>
              <p className="text-sm text-muted-foreground">设备编号: {equipmentId}</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="maintenance-type">维护类型</Label>
            <Select defaultValue="regular">
              <SelectTrigger id="maintenance-type">
                <SelectValue placeholder="选择维护类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="regular">常规维护</SelectItem>
                <SelectItem value="repair">维修</SelectItem>
                <SelectItem value="replace">更换部件</SelectItem>
                <SelectItem value="inspection">安全检查</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>计划维护日期</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "yyyy-MM-dd") : "选择日期"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar mode="single" selected={date} onSelect={setDate} initialFocus />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label htmlFor="staff">维护人员</Label>
            <Select>
              <SelectTrigger id="staff">
                <SelectValue placeholder="选择维护人员" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="staff1">张维修</SelectItem>
                <SelectItem value="staff2">李维修</SelectItem>
                <SelectItem value="staff3">王维修</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="estimated-cost">预计费用（元）</Label>
            <Input id="estimated-cost" type="number" min="0" placeholder="输入预计费用" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="estimated-duration">预计时长（小时）</Label>
            <Input id="estimated-duration" type="number" min="0.5" step="0.5" placeholder="输入预计时长" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">维护说明</Label>
            <Textarea id="notes" placeholder="输入维护说明和注意事项" rows={3} />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={() => onOpenChange(false)}>确认安排</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

