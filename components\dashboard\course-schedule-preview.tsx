"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useRouter } from "next/navigation"
import {
  Calendar,
  Clock,
  Users,
  MapPin,
  ChevronLeft,
  ChevronRight,
  Plus,
  UserPlus,
  Bell
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// 模拟数据
const coursesData = {
  today: [
    {
      id: "YG001",
      name: "基础瑜伽入门",
      startTime: "10:00",
      endTime: "11:30",
      duration: 90,
      coach: "张教练",
      venue: "1号瑜伽室",
      bookings: 12,
      capacity: 15,
      status: "upcoming", // upcoming, ongoing, full, completed, cancelled
      type: "基础课程",
      waitlist: 2,
    },
    {
      id: "YG002",
      name: "高级瑜伽进阶",
      startTime: "14:00",
      endTime: "15:30",
      duration: 90,
      coach: "李教练",
      venue: "2号瑜伽室",
      bookings: 8,
      capacity: 10,
      status: "ongoing",
      type: "进阶课程",
      waitlist: 0,
    },
    {
      id: "YG003",
      name: "阴瑜伽放松",
      startTime: "16:00",
      endTime: "17:00",
      duration: 60,
      coach: "王教练",
      venue: "3号瑜伽室",
      bookings: 15,
      capacity: 15,
      status: "full",
      type: "放松课程",
      waitlist: 5,
    },
    {
      id: "YG004",
      name: "孕产瑜伽",
      startTime: "18:30",
      endTime: "19:30",
      duration: 60,
      coach: "赵教练",
      venue: "1号瑜伽室",
      bookings: 6,
      capacity: 10,
      status: "upcoming",
      type: "专项课程",
      waitlist: 0,
    },
  ],
  tomorrow: [
    {
      id: "YG005",
      name: "哈他瑜伽",
      startTime: "09:00",
      endTime: "10:30",
      duration: 90,
      coach: "张教练",
      venue: "2号瑜伽室",
      bookings: 10,
      capacity: 15,
      status: "upcoming",
      type: "基础课程",
      waitlist: 0,
    },
    {
      id: "YG006",
      name: "空中瑜伽",
      startTime: "11:00",
      endTime: "12:30",
      duration: 90,
      coach: "李教练",
      venue: "1号瑜伽室",
      bookings: 8,
      capacity: 8,
      status: "full",
      type: "专项课程",
      waitlist: 3,
    },
  ]
}

export function CourseSchedulePreview() {
  const router = useRouter()
  const [selectedDay, setSelectedDay] = useState("today")
  const [currentDate, setCurrentDate] = useState(new Date())

  // 获取当前日期和明天的日期
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  // 格式化日期
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  }

  // 获取状态对应的标签样式和文本
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "upcoming":
        return { variant: "outline" as const, text: "即将开始", color: "text-blue-500" }
      case "ongoing":
        return { variant: "default" as const, text: "进行中", color: "text-green-500" }
      case "full":
        return { variant: "secondary" as const, text: "已满员", color: "text-amber-500" }
      case "completed":
        return { variant: "outline" as const, text: "已结束", color: "text-gray-500" }
      case "cancelled":
        return { variant: "destructive" as const, text: "已取消", color: "text-red-500" }
      default:
        return { variant: "outline" as const, text: "未知状态", color: "text-gray-500" }
    }
  }

  // 获取课程类型对应的样式
  const getCourseTypeStyle = (type: string) => {
    switch(type) {
      case "基础课程":
        return "bg-blue-50 text-blue-700"
      case "进阶课程":
        return "bg-purple-50 text-purple-700"
      case "放松课程":
        return "bg-green-50 text-green-700"
      case "专项课程":
        return "bg-amber-50 text-amber-700"
      default:
        return "bg-gray-50 text-gray-700"
    }
  }

  // 获取当前选择的日期的课程
  const currentCourses = selectedDay === "today" ? coursesData.today : coursesData.tomorrow

  return (
    <Card className="border shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium flex items-center">
            <Calendar className="mr-2 h-5 w-5 text-primary" />
            课程安排
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => {}}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center bg-muted rounded-md p-1 h-8">
              <button
                className={`text-xs px-3 rounded-sm h-6 flex items-center justify-center ${selectedDay === "today" ? "bg-background shadow-sm" : ""}`}
                onClick={() => setSelectedDay("today")}
              >
                今日
              </button>
              <button
                className={`text-xs px-3 rounded-sm h-6 flex items-center justify-center ${selectedDay === "tomorrow" ? "bg-background shadow-sm" : ""}`}
                onClick={() => setSelectedDay("tomorrow")}
              >
                明日
              </button>
            </div>
            <Button variant="outline" size="icon" className="h-8 w-8" onClick={() => {}}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          {selectedDay === "today" ? formatDate(today) : formatDate(tomorrow)}
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {currentCourses.length > 0 ? (
            currentCourses.map((course) => {
              const statusBadge = getStatusBadge(course.status)
              const typeStyle = getCourseTypeStyle(course.type)

              return (
                <div
                  key={course.id}
                  className="flex items-center p-3 rounded-md border hover:bg-muted/50 cursor-pointer transition-colors"
                  onClick={() => router.push(`/courses/${course.id}`)}
                >
                  <div className="w-16 text-center">
                    <div className="text-sm font-medium">{course.startTime}</div>
                    <div className="text-xs text-muted-foreground">{course.duration}分钟</div>
                  </div>
                  <div className="ml-4 flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{course.name}</span>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${typeStyle}`}>{course.type}</span>
                    </div>
                    <div className="text-xs text-muted-foreground flex items-center gap-2">
                      <span className="flex items-center"><Users className="h-3 w-3 mr-1" />{course.bookings}/{course.capacity}人</span>
                      <span className="flex items-center"><MapPin className="h-3 w-3 mr-1" />{course.venue}</span>
                      <span className="flex items-center"><Clock className="h-3 w-3 mr-1" />{course.startTime}-{course.endTime}</span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <Badge variant={statusBadge.variant} className={cn("ml-2", statusBadge.color)}>
                      {statusBadge.text}
                    </Badge>
                    {course.waitlist > 0 && (
                      <span className="text-xs text-amber-500 flex items-center">
                        <Bell className="h-3 w-3 mr-1" />
                        排队: {course.waitlist}人
                      </span>
                    )}
                  </div>
                </div>
              )
            })
          ) : (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Calendar className="h-10 w-10 text-muted-foreground mb-3" />
              <p className="text-muted-foreground">当日暂无课程安排</p>
              <Button variant="outline" className="mt-4" onClick={() => router.push('/courses/add')}>
                <Plus className="h-4 w-4 mr-2" />
                添加课程
              </Button>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2 border-t">
        <Button variant="outline" size="sm" onClick={() => router.push('/courses/schedule')}>
          查看全部课程
        </Button>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button size="sm" onClick={() => router.push('/booking-records')}>
                <UserPlus className="h-4 w-4 mr-2" />
                代预约
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>为会员代预约课程</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </CardFooter>
    </Card>
  )
}
