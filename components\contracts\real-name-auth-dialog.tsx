"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { FileUploader } from "@/components/file-uploader"
import { Loader2, Shield, CheckCircle } from "lucide-react"

// 个人认证表单验证模式
const personalFormSchema = z.object({
  name: z.string().min(2, {
    message: "姓名至少需要2个字符",
  }),
  idType: z.enum(["ID_CARD", "PASSPORT"], {
    required_error: "请选择证件类型",
  }),
  idNumber: z.string().min(1, {
    message: "请输入证件号码",
  }),
  mobile: z.string().min(11, {
    message: "请输入有效的手机号码",
  }),
  frontImage: z.any().refine((file) => file && file.length > 0, {
    message: "请上传证件正面照片",
  }),
  backImage: z.any().optional(),
})

// 企业认证表单验证模式
const enterpriseFormSchema = z.object({
  companyName: z.string().min(2, {
    message: "企业名称至少需要2个字符",
  }),
  creditCode: z.string().min(18, {
    message: "请输入正确的统一社会信用代码",
  }),
  legalRepName: z.string().min(2, {
    message: "法定代表人姓名至少需要2个字符",
  }),
  contactName: z.string().min(2, {
    message: "联系人姓名至少需要2个字符",
  }),
  contactMobile: z.string().min(11, {
    message: "请输入有效的手机号码",
  }),
  businessLicense: z.any().refine((file) => file && file.length > 0, {
    message: "请上传营业执照照片",
  }),
  legalRepIdType: z.enum(["ID_CARD", "PASSPORT"], {
    required_error: "请选择证件类型",
  }),
  legalRepIdNumber: z.string().min(1, {
    message: "请输入证件号码",
  }),
  legalRepIdFrontImage: z.any().refine((file) => file && file.length > 0, {
    message: "请上传法定代表人证件正面照片",
  }),
  legalRepIdBackImage: z.any().optional(),
})

interface RealNameAuthDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onAuthSuccess?: () => void
}

export function RealNameAuthDialog({
  open,
  onOpenChange,
  onAuthSuccess,
}: RealNameAuthDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [step, setStep] = useState<"form" | "verifying" | "success">("form")
  const [authType, setAuthType] = useState<"personal" | "enterprise">("personal")

  // 个人认证相关状态
  const [frontImage, setFrontImage] = useState<File | null>(null)
  const [backImage, setBackImage] = useState<File | null>(null)

  // 企业认证相关状态
  const [businessLicenseImage, setBusinessLicenseImage] = useState<File | null>(null)
  const [legalRepFrontImage, setLegalRepFrontImage] = useState<File | null>(null)
  const [legalRepBackImage, setLegalRepBackImage] = useState<File | null>(null)

  // 初始化个人认证表单
  const personalForm = useForm<z.infer<typeof personalFormSchema>>({
    resolver: zodResolver(personalFormSchema),
    defaultValues: {
      name: "",
      idType: "ID_CARD",
      idNumber: "",
      mobile: "",
    },
  })

  // 初始化企业认证表单
  const enterpriseForm = useForm<z.infer<typeof enterpriseFormSchema>>({
    resolver: zodResolver(enterpriseFormSchema),
    defaultValues: {
      companyName: "",
      creditCode: "",
      legalRepName: "",
      contactName: "",
      contactMobile: "",
      legalRepIdType: "ID_CARD",
      legalRepIdNumber: "",
    },
  })

  // 处理证件正面照片上传
  const handleFrontImageUpload = (files: File[]) => {
    if (files.length > 0) {
      setFrontImage(files[0])
      personalForm.setValue("frontImage", files)
    }
  }

  // 处理证件背面照片上传
  const handleBackImageUpload = (files: File[]) => {
    if (files.length > 0) {
      setBackImage(files[0])
      personalForm.setValue("backImage", files)
    }
  }

  // 处理营业执照上传
  const handleBusinessLicenseUpload = (files: File[]) => {
    if (files.length > 0) {
      setBusinessLicenseImage(files[0])
      enterpriseForm.setValue("businessLicense", files)
    }
  }

  // 处理法定代表人证件正面照片上传
  const handleLegalRepFrontImageUpload = (files: File[]) => {
    if (files.length > 0) {
      setLegalRepFrontImage(files[0])
      enterpriseForm.setValue("legalRepIdFrontImage", files)
    }
  }

  // 处理法定代表人证件背面照片上传
  const handleLegalRepBackImageUpload = (files: File[]) => {
    if (files.length > 0) {
      setLegalRepBackImage(files[0])
      enterpriseForm.setValue("legalRepIdBackImage", files)
    }
  }

  // 提交个人认证表单
  const onPersonalSubmit = async (values: z.infer<typeof personalFormSchema>) => {
    setIsSubmitting(true)
    setStep("verifying")

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 3000))

      // 创建FormData对象
      const formData = new FormData()
      formData.append("authType", "personal")
      formData.append("name", values.name)
      formData.append("idType", values.idType)
      formData.append("idNumber", values.idNumber)
      formData.append("mobile", values.mobile)

      if (frontImage) {
        formData.append("frontImage", frontImage)
      }

      if (backImage) {
        formData.append("backImage", backImage)
      }

      console.log("个人实名认证提交:", formData)

      // 模拟认证成功
      setStep("success")

      // 显示成功提示
      toast({
        title: "认证成功",
        description: "您的个人实名认证已通过",
      })

      // 延迟关闭对话框
      setTimeout(() => {
        // 调用成功回调
        if (onAuthSuccess) {
          onAuthSuccess()
        }

        // 重置表单
        personalForm.reset()
        setFrontImage(null)
        setBackImage(null)
        setStep("form")

        // 关闭对话框
        onOpenChange(false)
      }, 2000)
    } catch (error) {
      console.error("认证失败:", error)
      setStep("form")
      toast({
        title: "认证失败",
        description: "实名认证过程中发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 提交企业认证表单
  const onEnterpriseSubmit = async (values: z.infer<typeof enterpriseFormSchema>) => {
    setIsSubmitting(true)
    setStep("verifying")

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 3000))

      // 创建FormData对象
      const formData = new FormData()
      formData.append("authType", "enterprise")
      formData.append("companyName", values.companyName)
      formData.append("creditCode", values.creditCode)
      formData.append("legalRepName", values.legalRepName)
      formData.append("contactName", values.contactName)
      formData.append("contactMobile", values.contactMobile)
      formData.append("legalRepIdType", values.legalRepIdType)
      formData.append("legalRepIdNumber", values.legalRepIdNumber)

      if (businessLicenseImage) {
        formData.append("businessLicense", businessLicenseImage)
      }

      if (legalRepFrontImage) {
        formData.append("legalRepIdFrontImage", legalRepFrontImage)
      }

      if (legalRepBackImage) {
        formData.append("legalRepIdBackImage", legalRepBackImage)
      }

      console.log("企业实名认证提交:", formData)

      // 模拟认证成功
      setStep("success")

      // 显示成功提示
      toast({
        title: "认证成功",
        description: "您的企业实名认证已通过",
      })

      // 延迟关闭对话框
      setTimeout(() => {
        // 调用成功回调
        if (onAuthSuccess) {
          onAuthSuccess()
        }

        // 重置表单
        enterpriseForm.reset()
        setBusinessLicenseImage(null)
        setLegalRepFrontImage(null)
        setLegalRepBackImage(null)
        setStep("form")

        // 关闭对话框
        onOpenChange(false)
      }, 2000)
    } catch (error) {
      console.error("认证失败:", error)
      setStep("form")
      toast({
        title: "认证失败",
        description: "企业实名认证过程中发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>实名认证</DialogTitle>
          <DialogDescription>
            完成实名认证后，您可以使用电子签章功能
          </DialogDescription>
        </DialogHeader>

        {step === "form" && (
          <div className="space-y-6">
            <div className="flex justify-center mb-4">
              <div className="inline-flex rounded-md shadow-sm">
                <Button
                  variant={authType === "personal" ? "default" : "outline"}
                  className="rounded-r-none"
                  onClick={() => setAuthType("personal")}
                >
                  个人认证
                </Button>
                <Button
                  variant={authType === "enterprise" ? "default" : "outline"}
                  className="rounded-l-none"
                  onClick={() => setAuthType("enterprise")}
                >
                  企业认证
                </Button>
              </div>
            </div>

            {authType === "personal" ? (
              <Form {...personalForm}>
                <form onSubmit={personalForm.handleSubmit(onPersonalSubmit)} className="space-y-6">
                  <FormField
                    control={personalForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>真实姓名</FormLabel>
                        <FormControl>
                          <Input placeholder="输入您的真实姓名" {...field} />
                        </FormControl>
                        <FormDescription>
                          请输入与证件上一致的姓名
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={personalForm.control}
                    name="idType"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>证件类型</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex flex-col space-y-1"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="ID_CARD" id="id-card" />
                              <Label htmlFor="id-card">身份证</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="PASSPORT" id="passport" />
                              <Label htmlFor="passport">护照</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={personalForm.control}
                    name="idNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>证件号码</FormLabel>
                        <FormControl>
                          <Input placeholder="输入证件号码" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={personalForm.control}
                    name="mobile"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>手机号码</FormLabel>
                        <FormControl>
                          <Input placeholder="输入手机号码" {...field} />
                        </FormControl>
                        <FormDescription>
                          用于接收验证码和签署通知
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={personalForm.control}
                    name="frontImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>证件正面照片</FormLabel>
                        <FormControl>
                          <FileUploader
                            onFilesUploaded={handleFrontImageUpload}
                            maxFiles={1}
                            maxSize={5 * 1024 * 1024} // 5MB
                            accept=".jpg,.jpeg,.png"
                          />
                        </FormControl>
                        <FormDescription>
                          上传证件正面照片，支持JPG、PNG格式，最大5MB
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {personalForm.watch("idType") === "ID_CARD" && (
                    <FormField
                      control={personalForm.control}
                      name="backImage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>证件背面照片</FormLabel>
                          <FormControl>
                            <FileUploader
                              onFilesUploaded={handleBackImageUpload}
                              maxFiles={1}
                              maxSize={5 * 1024 * 1024} // 5MB
                              accept=".jpg,.jpeg,.png"
                            />
                          </FormControl>
                          <FormDescription>
                            上传证件背面照片，支持JPG、PNG格式，最大5MB
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => onOpenChange(false)}
                    >
                      取消
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      提交认证
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            ) : (
              <Form {...enterpriseForm}>
                <form onSubmit={enterpriseForm.handleSubmit(onEnterpriseSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={enterpriseForm.control}
                      name="companyName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>企业名称</FormLabel>
                          <FormControl>
                            <Input placeholder="输入企业名称" {...field} />
                          </FormControl>
                          <FormDescription>
                            请输入与营业执照上一致的企业名称
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={enterpriseForm.control}
                      name="creditCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>统一社会信用代码</FormLabel>
                          <FormControl>
                            <Input placeholder="输入统一社会信用代码" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={enterpriseForm.control}
                    name="businessLicense"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>营业执照照片</FormLabel>
                        <FormControl>
                          <FileUploader
                            onFilesUploaded={handleBusinessLicenseUpload}
                            maxFiles={1}
                            maxSize={5 * 1024 * 1024} // 5MB
                            accept=".jpg,.jpeg,.png"
                          />
                        </FormControl>
                        <FormDescription>
                          上传营业执照照片，支持JPG、PNG格式，最大5MB
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={enterpriseForm.control}
                      name="legalRepName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>法定代表人姓名</FormLabel>
                          <FormControl>
                            <Input placeholder="输入法定代表人姓名" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={enterpriseForm.control}
                      name="legalRepIdType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>法定代表人证件类型</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex space-x-4"
                            >
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="ID_CARD" id="legal-id-card" />
                                <Label htmlFor="legal-id-card">身份证</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="PASSPORT" id="legal-passport" />
                                <Label htmlFor="legal-passport">护照</Label>
                              </div>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={enterpriseForm.control}
                    name="legalRepIdNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>法定代表人证件号码</FormLabel>
                        <FormControl>
                          <Input placeholder="输入法定代表人证件号码" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={enterpriseForm.control}
                      name="legalRepIdFrontImage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>法定代表人证件正面照片</FormLabel>
                          <FormControl>
                            <FileUploader
                              onFilesUploaded={handleLegalRepFrontImageUpload}
                              maxFiles={1}
                              maxSize={5 * 1024 * 1024} // 5MB
                              accept=".jpg,.jpeg,.png"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {enterpriseForm.watch("legalRepIdType") === "ID_CARD" && (
                      <FormField
                        control={enterpriseForm.control}
                        name="legalRepIdBackImage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>法定代表人证件背面照片</FormLabel>
                            <FormControl>
                              <FileUploader
                                onFilesUploaded={handleLegalRepBackImageUpload}
                                maxFiles={1}
                                maxSize={5 * 1024 * 1024} // 5MB
                                accept=".jpg,.jpeg,.png"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={enterpriseForm.control}
                      name="contactName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>联系人姓名</FormLabel>
                          <FormControl>
                            <Input placeholder="输入联系人姓名" {...field} />
                          </FormControl>
                          <FormDescription>
                            企业联系人，可与法定代表人不同
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={enterpriseForm.control}
                      name="contactMobile"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>联系人手机号码</FormLabel>
                          <FormControl>
                            <Input placeholder="输入联系人手机号码" {...field} />
                          </FormControl>
                          <FormDescription>
                            用于接收验证码和签署通知
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => onOpenChange(false)}
                    >
                      取消
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      提交认证
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            )}
          </div>
        )}

        {step === "verifying" && (
          <div className="flex flex-col items-center justify-center py-10 space-y-4">
            <Loader2 className="h-16 w-16 text-primary animate-spin" />
            <h3 className="text-lg font-medium">正在验证{authType === "personal" ? "个人" : "企业"}信息</h3>
            <p className="text-sm text-muted-foreground text-center">
              我们正在验证您提供的{authType === "personal" ? "身份" : "企业"}信息，这可能需要几分钟时间。
              <br />请不要关闭此窗口。
            </p>
          </div>
        )}

        {step === "success" && (
          <div className="flex flex-col items-center justify-center py-10 space-y-4">
            <div className="rounded-full bg-green-100 p-3">
              <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
            <h3 className="text-lg font-medium">认证成功</h3>
            <p className="text-sm text-muted-foreground text-center">
              您的{authType === "personal" ? "个人" : "企业"}实名认证已通过，现在可以使用电子签章功能了。
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
