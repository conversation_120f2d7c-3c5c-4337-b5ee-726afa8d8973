import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { RecipientSelectionDialog } from './recipient-selection-dialog'

// 课程类型
interface CourseType {
  id: number
  name: string
}

// 角色类型
interface Role {
  id: number
  name: string
}

// 员工类型
interface Employee {
  id: number
  name: string
  role: number // 角色ID
}

interface MessageRule {
  id: number
  name: string
  template: string
  smsEnabled: boolean
  appEnabled: boolean
  emailEnabled?: boolean
  sendRule?: string
  courseTypes?: number[] // 支持的课程类型ID列表
  reminderRoles?: number[] // 接收提醒的角色ID列表
  reminderEmployees?: number[] // 接收提醒的员工ID列表
}

interface CourseRecipientDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  rule: MessageRule | null
  onSave: (rule: MessageRule) => void
}

// 示例角色数据
const roles: Role[] = [
  { id: 1, name: "场馆管理员" },
  { id: 2, name: "前台" },
  { id: 3, name: "教练" },
  { id: 4, name: "销售" },
  { id: 5, name: "财务" },
]

// 示例员工数据
const employees: Employee[] = [
  { id: 1, name: "张三", role: 1 },
  { id: 2, name: "李四", role: 2 },
  { id: 3, name: "王五", role: 2 },
  { id: 4, name: "赵六", role: 3 },
  { id: 5, name: "钱七", role: 3 },
  { id: 6, name: "孙八", role: 4 },
  { id: 7, name: "周九", role: 5 },
]

// 示例课程类型数据
const courseTypes: CourseType[] = [
  { id: 1, name: "团体课" },
  { id: 2, name: "私教课" },
  { id: 3, name: "小班课" },
  { id: 4, name: "精品课" },
  { id: 5, name: "教练培训课" },
  { id: 6, name: "体验课" },
  { id: 7, name: "特色课" },
  { id: 8, name: "季卡课" },
]

export function CourseRecipientDialog({
  open,
  onOpenChange,
  rule,
  onSave
}: CourseRecipientDialogProps) {
  const [selectedRoles, setSelectedRoles] = useState<number[]>([])
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([])
  const [recipientDialogOpen, setRecipientDialogOpen] = useState(false)
  const [selectedCourseTypes, setSelectedCourseTypes] = useState<number[]>([])

  // 当对话框打开时，初始化设置
  useEffect(() => {
    if (open && rule) {
      // 设置课程类型
      setSelectedCourseTypes(rule.courseTypes || [])

      // 默认选中教练角色
      const defaultRoles = rule.reminderRoles || [3] // 默认选中教练角色(ID为3)
      setSelectedRoles(defaultRoles)

      // 根据选中的角色，自动选中对应的员工
      let employeeIds: number[] = []

      // 如果有已保存的员工选择，则使用已保存的
      if (rule.reminderEmployees && rule.reminderEmployees.length > 0) {
        employeeIds = rule.reminderEmployees
      } else {
        // 否则根据选中的角色自动选择员工
        defaultRoles.forEach(roleId => {
          const roleEmployees = employees.filter(emp => emp.role === roleId).map(emp => emp.id)
          employeeIds = [...employeeIds, ...roleEmployees]
        })
      }

      setSelectedEmployees(employeeIds)
    }
  }, [open, rule])

  // 处理课程类型选择变化
  const handleCourseTypeChange = (courseId: number, checked: boolean) => {
    if (checked) {
      setSelectedCourseTypes(prev => [...prev, courseId])
    } else {
      setSelectedCourseTypes(prev => prev.filter(id => id !== courseId))
    }
  }

  // 处理保存按钮点击
  const handleSave = () => {
    if (rule) {
      const updatedRule = {
        ...rule,
        courseTypes: selectedCourseTypes,
        reminderRoles: selectedRoles,
        reminderEmployees: selectedEmployees
      }

      onSave(updatedRule)
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md p-6 rounded-md">
        <DialogHeader>
          <DialogTitle className="text-lg font-medium text-center border-b pb-4">
            {rule?.name || "课程消息"}接收人设置
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 my-4">
          {/* 课程类型选择 */}
          <div className="space-y-4">
            <div className="flex items-start">
              <Label className="w-32 text-right mr-4 text-gray-700 pt-1">课程类型</Label>
              <div className="space-y-3">
                <div className="space-y-1">
                  <Label className="text-sm text-gray-500 block">选择支持的课程类型</Label>
                  <div className="grid grid-cols-2 gap-2 pl-1">
                    {courseTypes.map(course => (
                      <div key={course.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`course-${course.id}`}
                          checked={selectedCourseTypes.includes(course.id)}
                          onCheckedChange={(checked) =>
                            handleCourseTypeChange(course.id, checked as boolean)
                          }
                        />
                        <Label
                          htmlFor={`course-${course.id}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {course.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 接收人设置 */}
          <div className="space-y-4">
            <div className="flex items-center">
              <Label className="w-32 text-right mr-4 text-gray-700">接收人设置</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setRecipientDialogOpen(true)}
                className="text-sm"
              >
                设置接收人
              </Button>
            </div>

            {/* 显示已选择的角色 */}
            {selectedRoles.length > 0 && (
              <div className="flex items-start">
                <Label className="w-32 text-right mr-4 text-gray-700 invisible">已选角色</Label>
                <div className="flex flex-wrap gap-2">
                  {selectedRoles.map(roleId => {
                    const role = roles.find(r => r.id === roleId)
                    return role ? (
                      <div key={roleId} className="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded">
                        {role.name}
                      </div>
                    ) : null
                  })}
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="mt-6">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="px-6"
          >
            取消
          </Button>
          <Button
            onClick={handleSave}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6"
          >
            确定
          </Button>
        </DialogFooter>

        {/* 接收人设置对话框 */}
        <RecipientSelectionDialog
          open={recipientDialogOpen}
          onOpenChange={setRecipientDialogOpen}
          title={`${rule?.name || "消息"}接收人设置`}
          selectedRoles={selectedRoles}
          selectedEmployees={selectedEmployees}
          onSave={(roles, employees) => {
            setSelectedRoles(roles)
            setSelectedEmployees(employees)
          }}
          roles={roles}
          employees={employees}
          defaultSelectedRoleId={3} // 默认选中教练角色
        />
      </DialogContent>
    </Dialog>
  )
}
