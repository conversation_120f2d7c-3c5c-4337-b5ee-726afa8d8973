"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Search, 
  Calendar, 
  Clock, 
  TrendingUp, 
  Users, 
  Plus,
  ArrowUpRight,
  ArrowDownRight,
  BarChart,
  PieChart,
  LineChart,
  CalendarDays,
  UserPlus,
  Layers,
  MoreHorizontal,
  CheckCircle,
  AlertTriangle,
  Lightbulb,
  ThumbsUp,
  ThumbsDown
} from "lucide-react"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// 模拟课程优化数据
const courseOptimizationData = [
  {
    id: "OPT001",
    title: "周六上午瑜伽课程需求高",
    description: "周六10:00-12:00时段预约率达95%，建议增加同类型课程。",
    courseType: "基础瑜伽",
    timeSlot: "周六 10:00-12:00",
    bookingRate: 95,
    waitlistCount: 12,
    trend: "上升",
    trendValue: 15,
    recommendedAction: "增加课程",
    impact: "高",
    status: "未处理",
    createdAt: "2023-06-01"
  },
  {
    id: "OPT002",
    title: "周一晚间课程预约率低",
    description: "周一19:00-20:30时段预约率仅为35%，建议调整课程类型或取消。",
    courseType: "高级瑜伽",
    timeSlot: "周一 19:00-20:30",
    bookingRate: 35,
    waitlistCount: 0,
    trend: "下降",
    trendValue: 10,
    recommendedAction: "调整课程",
    impact: "中",
    status: "已处理",
    createdAt: "2023-05-25"
  },
  {
    id: "OPT003",
    title: "阴瑜伽课程需求增加",
    description: "阴瑜伽课程近3个月预约率提升25%，建议增加课程数量。",
    courseType: "阴瑜伽",
    timeSlot: "全天段",
    bookingRate: 85,
    waitlistCount: 8,
    trend: "上升",
    trendValue: 25,
    recommendedAction: "增加课程",
    impact: "高",
    status: "未处理",
    createdAt: "2023-06-03"
  },
  {
    id: "OPT004",
    title: "李教练课程评分下降",
    description: "李教练的课程评分近期下降15%，建议关注教学质量。",
    courseType: "多种类型",
    timeSlot: "全天段",
    bookingRate: 60,
    waitlistCount: 0,
    trend: "下降",
    trendValue: 15,
    recommendedAction: "教练培训",
    impact: "高",
    status: "处理中",
    createdAt: "2023-06-02"
  },
  {
    id: "OPT005",
    title: "瑜伽轮课程预约率高",
    description: "瑜伽轮专题课程预约率达90%，建议增加此类课程。",
    courseType: "瑜伽轮",
    timeSlot: "周末全天",
    bookingRate: 90,
    waitlistCount: 10,
    trend: "上升",
    trendValue: 20,
    recommendedAction: "增加课程",
    impact: "中",
    status: "未处理",
    createdAt: "2023-06-05"
  }
];

// 模拟热门时段数据
const popularTimeSlots = [
  { day: "周一", hour: 10, bookingRate: 45 },
  { day: "周一", hour: 14, bookingRate: 60 },
  { day: "周一", hour: 19, bookingRate: 35 },
  { day: "周二", hour: 10, bookingRate: 55 },
  { day: "周二", hour: 14, bookingRate: 65 },
  { day: "周二", hour: 19, bookingRate: 75 },
  { day: "周三", hour: 10, bookingRate: 50 },
  { day: "周三", hour: 14, bookingRate: 70 },
  { day: "周三", hour: 19, bookingRate: 80 },
  { day: "周四", hour: 10, bookingRate: 60 },
  { day: "周四", hour: 14, bookingRate: 75 },
  { day: "周四", hour: 19, bookingRate: 85 },
  { day: "周五", hour: 10, bookingRate: 65 },
  { day: "周五", hour: 14, bookingRate: 70 },
  { day: "周五", hour: 19, bookingRate: 90 },
  { day: "周六", hour: 10, bookingRate: 95 },
  { day: "周六", hour: 14, bookingRate: 90 },
  { day: "周六", hour: 19, bookingRate: 85 },
  { day: "周日", hour: 10, bookingRate: 85 },
  { day: "周日", hour: 14, bookingRate: 80 },
  { day: "周日", hour: 19, bookingRate: 70 }
];

// 模拟课程类型数据
const courseTypeData = [
  { type: "基础瑜伽", bookingRate: 85, trend: "上升", trendValue: 5 },
  { type: "高级瑜伽", bookingRate: 65, trend: "稳定", trendValue: 0 },
  { type: "阴瑜伽", bookingRate: 85, trend: "上升", trendValue: 25 },
  { type: "瑜伽轮", bookingRate: 90, trend: "上升", trendValue: 20 },
  { type: "空中瑜伽", bookingRate: 75, trend: "上升", trendValue: 10 },
  { type: "理疗瑜伽", bookingRate: 60, trend: "下降", trendValue: 5 },
  { type: "孕产瑜伽", bookingRate: 70, trend: "上升", trendValue: 15 }
];

export default function CourseOptimizationPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("suggestions")
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [impactFilter, setImpactFilter] = useState("all")

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "未处理":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">未处理</Badge>
      case "处理中":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">处理中</Badge>
      case "已处理":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已处理</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取趋势样式
  const getTrendBadge = (trend: string, value: number) => {
    if (trend === "上升") {
      return (
        <div className="flex items-center text-green-600">
          <ArrowUpRight className="h-4 w-4 mr-1" />
          <span>+{value}%</span>
        </div>
      )
    } else if (trend === "下降") {
      return (
        <div className="flex items-center text-red-600">
          <ArrowDownRight className="h-4 w-4 mr-1" />
          <span>-{value}%</span>
        </div>
      )
    } else {
      return (
        <div className="flex items-center text-gray-600">
          <span>持平</span>
        </div>
      )
    }
  }

  // 获取预约率样式
  const getBookingRateStyle = (rate: number) => {
    if (rate >= 80) {
      return "text-green-600"
    } else if (rate >= 50) {
      return "text-amber-600"
    } else {
      return "text-red-600"
    }
  }

  // 获取影响力样式
  const getImpactBadge = (impact: string) => {
    switch(impact) {
      case "高":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">高</Badge>
      case "中":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">中</Badge>
      case "低":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">低</Badge>
      default:
        return <Badge>{impact}</Badge>
    }
  }

  // 过滤优化建议
  const filteredSuggestions = courseOptimizationData.filter(suggestion => {
    // 基本搜索过滤
    const searchFilter = 
      searchQuery === "" || 
      suggestion.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      suggestion.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      suggestion.courseType.toLowerCase().includes(searchQuery.toLowerCase());
    
    // 状态过滤
    const statusFilterResult = 
      statusFilter === "all" || 
      suggestion.status === statusFilter;
    
    // 影响力过滤
    const impactFilterResult = 
      impactFilter === "all" || 
      suggestion.impact === impactFilter;
    
    return searchFilter && statusFilterResult && impactFilterResult;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">课程优化建议</h1>
          <p className="text-muted-foreground">
            基于数据分析的课程安排优化建议，提高场馆利用率和会员满意度
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <BarChart className="h-4 w-4 mr-2" />
            查看完整报告
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            添加课程
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 max-w-md">
          <TabsTrigger value="suggestions">优化建议</TabsTrigger>
          <TabsTrigger value="time-analysis">时段分析</TabsTrigger>
          <TabsTrigger value="course-analysis">课程分析</TabsTrigger>
        </TabsList>
        
        <TabsContent value="suggestions" className="space-y-4 mt-4">
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="relative w-full md:w-1/3">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索优化建议、课程类型"
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex flex-1 gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="状态筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="未处理">未处理</SelectItem>
                  <SelectItem value="处理中">处理中</SelectItem>
                  <SelectItem value="已处理">已处理</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={impactFilter} onValueChange={setImpactFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="影响力筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部影响力</SelectItem>
                  <SelectItem value="高">高</SelectItem>
                  <SelectItem value="中">中</SelectItem>
                  <SelectItem value="低">低</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>优化建议</TableHead>
                    <TableHead>课程类型</TableHead>
                    <TableHead>时段</TableHead>
                    <TableHead>预约率</TableHead>
                    <TableHead>趋势</TableHead>
                    <TableHead>影响力</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSuggestions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        没有找到符合条件的优化建议
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredSuggestions.map((suggestion) => (
                      <TableRow key={suggestion.id}>
                        <TableCell>
                          <div className="flex items-start gap-3">
                            <div className="p-2 rounded-full bg-blue-50">
                              <Lightbulb className="h-4 w-4 text-blue-500" />
                            </div>
                            <div>
                              <div className="font-medium">{suggestion.title}</div>
                              <div className="text-xs text-muted-foreground mt-1">{suggestion.description}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{suggestion.courseType}</TableCell>
                        <TableCell>{suggestion.timeSlot}</TableCell>
                        <TableCell>
                          <div className={`font-medium ${getBookingRateStyle(suggestion.bookingRate)}`}>
                            {suggestion.bookingRate}%
                          </div>
                          <Progress 
                            value={suggestion.bookingRate} 
                            className="h-2 w-24" 
                            indicatorClassName={
                              suggestion.bookingRate >= 80 ? "bg-green-500" : 
                              suggestion.bookingRate >= 50 ? "bg-amber-500" : 
                              "bg-red-500"
                            }
                          />
                        </TableCell>
                        <TableCell>
                          {getTrendBadge(suggestion.trend, suggestion.trendValue)}
                        </TableCell>
                        <TableCell>
                          {getImpactBadge(suggestion.impact)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(suggestion.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>建议操作</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                标记为处理中
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                标记为已处理
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <ThumbsUp className="h-4 w-4 mr-2" />
                                接受建议
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <ThumbsDown className="h-4 w-4 mr-2" />
                                拒绝建议
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="time-analysis" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>热门时段分析</CardTitle>
              <CardDescription>
                各时段预约率热力图，帮助优化课程安排
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <LineChart className="h-16 w-16 mx-auto mb-4 text-primary/40" />
                  <p>时段分析图表将在此显示</p>
                  <p className="text-sm">包含各时段预约率、上课人数等数据</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="course-analysis" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>课程类型分析</CardTitle>
              <CardDescription>
                各类课程受欢迎程度和趋势分析
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <PieChart className="h-16 w-16 mx-auto mb-4 text-primary/40" />
                  <p>课程类型分析图表将在此显示</p>
                  <p className="text-sm">包含各类课程预约率、趋势变化等数据</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
