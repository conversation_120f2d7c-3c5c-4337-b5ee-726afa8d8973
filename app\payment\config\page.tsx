"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"

export default function PaymentConfigPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">支付配置</h1>
        <Button>保存配置</Button>
      </div>

      <Tabs defaultValue="wechat" className="space-y-4">
        <TabsList>
          <TabsTrigger value="wechat">微信支付配置</TabsTrigger>
          <TabsTrigger value="alipay">支付宝配置</TabsTrigger>
        </TabsList>

        <TabsContent value="wechat" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>微信支付配置</CardTitle>
              <CardDescription>配置微信支付接入参数</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="wechat-appid">AppID</Label>
                <Input id="wechat-appid" placeholder="输入微信AppID" />
                <p className="text-sm text-muted-foreground">微信公众平台或小程序的AppID</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="wechat-mchid">商户ID</Label>
                <Input id="wechat-mchid" placeholder="输入微信商户ID" />
                <p className="text-sm text-muted-foreground">微信支付商户平台的商户号</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="wechat-apikey">API密钥</Label>
                <Input id="wechat-apikey" type="password" placeholder="输入微信API密钥" />
                <p className="text-sm text-muted-foreground">微信支付商户平台设置的API密钥</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="wechat-cert">API证书</Label>
                <div className="flex gap-2">
                  <Input id="wechat-cert" type="file" />
                  <Button variant="outline">上传</Button>
                </div>
                <p className="text-sm text-muted-foreground">微信支付商户平台下载的API证书</p>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label htmlFor="wechat-notify-url">支付结果通知URL</Label>
                <Input id="wechat-notify-url" value="https://youryogastudio.com/api/wechat/payment/notify" readOnly />
                <p className="text-sm text-muted-foreground">请在微信商户平台设置此通知URL</p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="wechat-enabled">启用微信支付</Label>
                  <p className="text-sm text-muted-foreground">开启后可使用微信支付功能</p>
                </div>
                <Switch id="wechat-enabled" defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alipay" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付宝配置</CardTitle>
              <CardDescription>配置支付宝接入参数</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="alipay-appid">AppID</Label>
                <Input id="alipay-appid" placeholder="输入支付宝AppID" />
                <p className="text-sm text-muted-foreground">支付宝开放平台的应用ID</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="alipay-private-key">应用私钥</Label>
                <Textarea id="alipay-private-key" placeholder="输入应用私钥" className="font-mono text-xs" />
                <p className="text-sm text-muted-foreground">用于签名的应用私钥</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="alipay-public-key">支付宝公钥</Label>
                <Textarea id="alipay-public-key" placeholder="输入支付宝公钥" className="font-mono text-xs" />
                <p className="text-sm text-muted-foreground">用于验签的支付宝公钥</p>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label htmlFor="alipay-notify-url">支付结果通知URL</Label>
                <Input id="alipay-notify-url" value="https://youryogastudio.com/api/alipay/payment/notify" readOnly />
                <p className="text-sm text-muted-foreground">请在支付宝开放平台设置此通知URL</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="alipay-return-url">支付完成跳转URL</Label>
                <Input id="alipay-return-url" value="https://youryogastudio.com/payment/result" />
                <p className="text-sm text-muted-foreground">支付完成后跳转的页面URL</p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="alipay-enabled">启用支付宝</Label>
                  <p className="text-sm text-muted-foreground">开启后可使用支付宝功能</p>
                </div>
                <Switch id="alipay-enabled" defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

