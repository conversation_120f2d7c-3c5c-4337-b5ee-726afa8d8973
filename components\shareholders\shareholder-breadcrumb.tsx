"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { ChevronRight } from "lucide-react"
import { Share2, Users, Wallet, BarChart2 } from "lucide-react"

// 定义面包屑路径映射
const breadcrumbMap = {
  "/shareholders": {
    label: "股东列表",
    icon: <Users className="h-4 w-4" />
  },
  "/shareholders/types": {
    label: "股东类型",
    icon: <Share2 className="h-4 w-4" />
  },
  "/shareholders/dividends": {
    label: "分红记录",
    icon: <Wallet className="h-4 w-4" />
  },
  "/shareholders/statistics": {
    label: "业绩统计",
    icon: <BarChart2 className="h-4 w-4" />
  }
}

export function ShareholderBreadcrumb() {
  const pathname = usePathname()

  // 获取当前页面的标签
  const currentPageLabel = breadcrumbMap[pathname]?.label || "股东管理"
  const currentPageIcon = breadcrumbMap[pathname]?.icon

  // 判断是否是子页面
  const isSubPage = pathname !== "/shareholders"

  return (
    <div className="mb-6 text-sm text-muted-foreground">
      <div className="flex items-center space-x-1">
        <Link href="/premium-services" className="hover:text-foreground transition-colors">
          增值服务
        </Link>
        <ChevronRight className="h-4 w-4" />

        {isSubPage ? (
          <>
            <Link href="/shareholders" className="hover:text-foreground transition-colors">
              共享股东
            </Link>
            <ChevronRight className="h-4 w-4" />
            <span className="text-foreground font-medium">
              {currentPageLabel}
            </span>
          </>
        ) : (
          <span className="text-foreground font-medium">
            共享股东
          </span>
        )}
      </div>
    </div>
  )
}
