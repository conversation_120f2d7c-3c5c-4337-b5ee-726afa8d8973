"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  BookOpen,
  FileText,
  Video,
  File,
  Plus,
  Trash2,
  Edit,
  MoveUp,
  MoveDown,
  Upload,
  Play,
  Clock
} from "lucide-react"

interface CourseChapterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  courseId: string;
  onSave: (data: any) => void;
}

interface Chapter {
  id: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
}

interface Lesson {
  id: string;
  title: string;
  description: string;
  duration: string;
  type: "video" | "audio" | "text";
  url: string;
  order: number;
}

export function CourseChapterDialog({ 
  open, 
  onOpenChange, 
  courseId, 
  onSave 
}: CourseChapterDialogProps) {
  const [loading, setLoading] = useState(false)
  const [chapters, setChapters] = useState<Chapter[]>([])
  const { toast } = useToast()
  
  // 编辑状态
  const [editingChapter, setEditingChapter] = useState<Chapter | null>(null)
  const [editingLesson, setEditingLesson] = useState<Lesson | null>(null)
  const [showChapterDialog, setShowChapterDialog] = useState(false)
  const [showLessonDialog, setShowLessonDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteItemType, setDeleteItemType] = useState<"chapter" | "lesson">("chapter")
  const [deleteItemId, setDeleteItemId] = useState("")
  const [parentChapterId, setParentChapterId] = useState("")
  
  // 表单数据
  const [chapterForm, setChapterForm] = useState({
    title: "",
    description: ""
  })
  
  const [lessonForm, setLessonForm] = useState({
    title: "",
    description: "",
    duration: "",
    type: "video" as "video" | "audio" | "text",
    url: ""
  })

  // 加载课程章节数据
  useEffect(() => {
    if (open && courseId) {
      loadCourseChapters()
    }
  }, [open, courseId])

  // 加载课程章节
  const loadCourseChapters = () => {
    setLoading(true)
    
    // 模拟API请求
    setTimeout(() => {
      // 模拟数据
      const mockChapters: Chapter[] = [
        {
          id: "ch1",
          title: "第1章：瑜伽基础入门",
          description: "本章介绍瑜伽的基本概念和入门姿势",
          order: 1,
          lessons: [
            {
              id: "l1",
              title: "瑜伽呼吸法基础",
              description: "学习瑜伽中的基础呼吸技巧",
              duration: "15:00",
              type: "video",
              url: "/videos/yoga-breathing.mp4",
              order: 1
            },
            {
              id: "l2",
              title: "基础站姿与平衡",
              description: "学习瑜伽中的基础站姿和平衡技巧",
              duration: "20:00",
              type: "video",
              url: "/videos/yoga-standing.mp4",
              order: 2
            }
          ]
        },
        {
          id: "ch2",
          title: "第2章：瑜伽体式进阶",
          description: "本章介绍进阶的瑜伽体式和流程",
          order: 2,
          lessons: [
            {
              id: "l3",
              title: "太阳礼拜A",
              description: "学习经典的太阳礼拜A序列",
              duration: "25:00",
              type: "video",
              url: "/videos/sun-salutation-a.mp4",
              order: 1
            }
          ]
        }
      ]
      
      setChapters(mockChapters)
      setLoading(false)
    }, 500)
  }

  // 处理添加章节
  const handleAddChapter = () => {
    setEditingChapter(null)
    setChapterForm({
      title: "",
      description: ""
    })
    setShowChapterDialog(true)
  }

  // 处理编辑章节
  const handleEditChapter = (chapter: Chapter) => {
    setEditingChapter(chapter)
    setChapterForm({
      title: chapter.title,
      description: chapter.description
    })
    setShowChapterDialog(true)
  }

  // 处理添加课时
  const handleAddLesson = (chapterId: string) => {
    setEditingLesson(null)
    setParentChapterId(chapterId)
    setLessonForm({
      title: "",
      description: "",
      duration: "",
      type: "video",
      url: ""
    })
    setShowLessonDialog(true)
  }

  // 处理编辑课时
  const handleEditLesson = (lesson: Lesson, chapterId: string) => {
    setEditingLesson(lesson)
    setParentChapterId(chapterId)
    setLessonForm({
      title: lesson.title,
      description: lesson.description,
      duration: lesson.duration,
      type: lesson.type,
      url: lesson.url
    })
    setShowLessonDialog(true)
  }

  // 处理删除章节
  const handleDeleteChapter = (chapterId: string) => {
    setDeleteItemType("chapter")
    setDeleteItemId(chapterId)
    setShowDeleteDialog(true)
  }

  // 处理删除课时
  const handleDeleteLesson = (lessonId: string, chapterId: string) => {
    setDeleteItemType("lesson")
    setDeleteItemId(lessonId)
    setParentChapterId(chapterId)
    setShowDeleteDialog(true)
  }

  // 确认删除
  const confirmDelete = () => {
    if (deleteItemType === "chapter") {
      // 删除章节
      setChapters(prev => prev.filter(ch => ch.id !== deleteItemId))
      toast({
        title: "删除成功",
        description: "章节已成功删除",
      })
    } else {
      // 删除课时
      setChapters(prev => prev.map(ch => {
        if (ch.id === parentChapterId) {
          return {
            ...ch,
            lessons: ch.lessons.filter(l => l.id !== deleteItemId)
          }
        }
        return ch
      }))
      toast({
        title: "删除成功",
        description: "课时已成功删除",
      })
    }
    setShowDeleteDialog(false)
  }

  // 保存章节
  const saveChapter = () => {
    if (!chapterForm.title) {
      toast({
        title: "验证失败",
        description: "请填写章节标题",
        variant: "destructive"
      })
      return
    }
    
    if (editingChapter) {
      // 更新章节
      setChapters(prev => prev.map(ch => {
        if (ch.id === editingChapter.id) {
          return {
            ...ch,
            title: chapterForm.title,
            description: chapterForm.description
          }
        }
        return ch
      }))
    } else {
      // 添加新章节
      const newChapter: Chapter = {
        id: `ch${Date.now()}`,
        title: chapterForm.title,
        description: chapterForm.description,
        order: chapters.length + 1,
        lessons: []
      }
      setChapters(prev => [...prev, newChapter])
    }
    
    setShowChapterDialog(false)
    toast({
      title: editingChapter ? "更新成功" : "添加成功",
      description: editingChapter ? "章节已更新" : "新章节已添加",
    })
  }

  // 保存课时
  const saveLesson = () => {
    if (!lessonForm.title) {
      toast({
        title: "验证失败",
        description: "请填写课时标题",
        variant: "destructive"
      })
      return
    }
    
    if (editingLesson) {
      // 更新课时
      setChapters(prev => prev.map(ch => {
        if (ch.id === parentChapterId) {
          return {
            ...ch,
            lessons: ch.lessons.map(l => {
              if (l.id === editingLesson.id) {
                return {
                  ...l,
                  title: lessonForm.title,
                  description: lessonForm.description,
                  duration: lessonForm.duration,
                  type: lessonForm.type,
                  url: lessonForm.url
                }
              }
              return l
            })
          }
        }
        return ch
      }))
    } else {
      // 添加新课时
      const chapter = chapters.find(ch => ch.id === parentChapterId)
      if (!chapter) return
      
      const newLesson: Lesson = {
        id: `l${Date.now()}`,
        title: lessonForm.title,
        description: lessonForm.description,
        duration: lessonForm.duration,
        type: lessonForm.type,
        url: lessonForm.url,
        order: chapter.lessons.length + 1
      }
      
      setChapters(prev => prev.map(ch => {
        if (ch.id === parentChapterId) {
          return {
            ...ch,
            lessons: [...ch.lessons, newLesson]
          }
        }
        return ch
      }))
    }
    
    setShowLessonDialog(false)
    toast({
      title: editingLesson ? "更新成功" : "添加成功",
      description: editingLesson ? "课时已更新" : "新课时已添加",
    })
  }

  // 获取课时类型图标
  const getLessonTypeIcon = (type: string) => {
    switch (type) {
      case "video":
        return <Video className="h-4 w-4 text-blue-500" />
      case "audio":
        return <FileText className="h-4 w-4 text-green-500" />
      case "text":
        return <File className="h-4 w-4 text-orange-500" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  // 保存所有更改
  const handleSaveAll = () => {
    // 这里应该调用API保存所有章节和课时
    onSave(chapters)
    toast({
      title: "保存成功",
      description: "课程章节已成功保存",
    })
    onOpenChange(false)
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>课程章节管理</DialogTitle>
            <DialogDescription>
              管理课程的章节和课时内容
            </DialogDescription>
          </DialogHeader>

          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Button onClick={handleAddChapter}>
                  <Plus className="mr-2 h-4 w-4" />
                  添加章节
                </Button>
                
                <div className="text-sm text-muted-foreground">
                  共 {chapters.length} 个章节，{chapters.reduce((acc, ch) => acc + ch.lessons.length, 0)} 个课时
                </div>
              </div>
              
              {chapters.length === 0 ? (
                <div className="text-center py-12 border rounded-md">
                  <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">暂无章节</h3>
                  <p className="text-muted-foreground mb-4">
                    点击"添加章节"按钮开始创建课程内容
                  </p>
                  <Button onClick={handleAddChapter}>
                    <Plus className="mr-2 h-4 w-4" />
                    添加章节
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {chapters.map((chapter) => (
                    <Card key={chapter.id}>
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-base">{chapter.title}</CardTitle>
                            {chapter.description && (
                              <CardDescription>{chapter.description}</CardDescription>
                            )}
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="icon" onClick={() => handleEditChapter(chapter)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="icon" className="text-red-500" onClick={() => handleDeleteChapter(chapter.id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {chapter.lessons.map((lesson) => (
                            <div key={lesson.id} className="flex items-center justify-between border-b pb-2">
                              <div className="flex items-center">
                                <div className="w-8 h-8 bg-muted rounded-md flex items-center justify-center mr-3">
                                  {lesson.order}
                                </div>
                                <div>
                                  <div className="font-medium flex items-center">
                                    {getLessonTypeIcon(lesson.type)}
                                    <span className="ml-1">{lesson.title}</span>
                                  </div>
                                  <div className="text-xs text-muted-foreground flex items-center mt-1">
                                    <Clock className="h-3 w-3 mr-1" />
                                    {lesson.duration}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button variant="ghost" size="icon" onClick={() => handleEditLesson(lesson, chapter.id)}>
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="icon" className="text-red-500" onClick={() => handleDeleteLesson(lesson.id, chapter.id)}>
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          ))}
                          
                          <Button 
                            variant="outline" 
                            className="w-full mt-2"
                            onClick={() => handleAddLesson(chapter.id)}
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            添加课时
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleSaveAll}>
              保存所有更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 章节编辑对话框 */}
      <Dialog open={showChapterDialog} onOpenChange={setShowChapterDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{editingChapter ? "编辑章节" : "添加章节"}</DialogTitle>
            <DialogDescription>
              {editingChapter ? "修改章节信息" : "创建新的课程章节"}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="chapterTitle">章节标题 <span className="text-red-500">*</span></Label>
              <Input
                id="chapterTitle"
                placeholder="例如：第1章：瑜伽基础入门"
                value={chapterForm.title}
                onChange={(e) => setChapterForm(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="chapterDescription">章节描述 (可选)</Label>
              <Textarea
                id="chapterDescription"
                placeholder="简要描述本章节内容"
                value={chapterForm.description}
                onChange={(e) => setChapterForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowChapterDialog(false)}>
              取消
            </Button>
            <Button onClick={saveChapter}>
              {editingChapter ? "更新章节" : "添加章节"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 课时编辑对话框 */}
      <Dialog open={showLessonDialog} onOpenChange={setShowLessonDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{editingLesson ? "编辑课时" : "添加课时"}</DialogTitle>
            <DialogDescription>
              {editingLesson ? "修改课时信息" : "创建新的课时内容"}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="lessonTitle">课时标题 <span className="text-red-500">*</span></Label>
              <Input
                id="lessonTitle"
                placeholder="例如：瑜伽呼吸法基础"
                value={lessonForm.title}
                onChange={(e) => setLessonForm(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="lessonDescription">课时描述 (可选)</Label>
              <Textarea
                id="lessonDescription"
                placeholder="简要描述本课时内容"
                value={lessonForm.description}
                onChange={(e) => setLessonForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="lessonType">内容类型</Label>
                <select
                  id="lessonType"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={lessonForm.type}
                  onChange={(e) => setLessonForm(prev => ({ ...prev, type: e.target.value as "video" | "audio" | "text" }))}
                >
                  <option value="video">视频</option>
                  <option value="audio">音频</option>
                  <option value="text">图文</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="lessonDuration">时长</Label>
                <Input
                  id="lessonDuration"
                  placeholder="例如：15:00"
                  value={lessonForm.duration}
                  onChange={(e) => setLessonForm(prev => ({ ...prev, duration: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="lessonUrl">内容链接</Label>
              <div className="flex gap-2">
                <Input
                  id="lessonUrl"
                  placeholder="输入内容链接或上传文件"
                  value={lessonForm.url}
                  onChange={(e) => setLessonForm(prev => ({ ...prev, url: e.target.value }))}
                  className="flex-1"
                />
                <Button variant="outline" type="button">
                  <Upload className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                {lessonForm.type === "video" ? "支持MP4、WebM格式" : 
                 lessonForm.type === "audio" ? "支持MP3、WAV格式" : 
                 "支持Markdown或HTML格式"}
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLessonDialog(false)}>
              取消
            </Button>
            <Button onClick={saveLesson}>
              {editingLesson ? "更新课时" : "添加课时"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这个{deleteItemType === "chapter" ? "章节" : "课时"}吗？此操作无法撤销。
              {deleteItemType === "chapter" && "删除章节将同时删除其中的所有课时。"}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
