"use client"

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"

interface ColorPickerProps {
  color: string
  onChange: (color: string) => void
  presetColors?: string[]
}

export function ColorPicker({ color, onChange, presetColors = [] }: ColorPickerProps) {
  const [selectedColor, setSelectedColor] = useState(color || "#000000")
  const [isOpen, setIsOpen] = useState(false)

  // 当外部color属性变化时更新内部状态
  useEffect(() => {
    setSelectedColor(color)
  }, [color])

  // 选择颜色
  const handleColorSelect = (newColor: string) => {
    setSelectedColor(newColor)
    onChange(newColor)
    setIsOpen(false)
  }

  // 处理颜色输入框变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value
    setSelectedColor(newColor)
    onChange(newColor)
  }

  return (
    <div className="flex items-center space-x-2">
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-[42px] h-[42px] p-1 rounded-md border"
            style={{ backgroundColor: selectedColor }}
            aria-label="选择颜色"
          >
            <span className="sr-only">选择颜色</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-3">
          <div className="space-y-3">
            <div className="grid grid-cols-5 gap-2">
              {presetColors.map((presetColor) => (
                <button
                  key={presetColor}
                  type="button"
                  className={cn(
                    "h-8 w-8 rounded-md border",
                    selectedColor === presetColor && "ring-2 ring-offset-2 ring-primary"
                  )}
                  style={{ backgroundColor: presetColor }}
                  onClick={() => handleColorSelect(presetColor)}
                  aria-label={`选择颜色 ${presetColor}`}
                />
              ))}
            </div>
            <div className="flex items-center space-x-2">
              <div
                className="h-8 w-8 rounded-md border"
                style={{ backgroundColor: selectedColor }}
              />
              <Input
                type="text"
                value={selectedColor}
                onChange={handleInputChange}
                className="h-8"
                placeholder="#000000"
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
      <Input
        type="text"
        value={selectedColor}
        onChange={handleInputChange}
        placeholder="#000000"
        className="w-[100px] h-[42px]"
      />
    </div>
  )
}

