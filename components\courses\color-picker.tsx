"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Check, ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface ColorPickerProps {
  color: string
  onChange: (color: string) => void
  presetColors?: string[]
}

export function ColorPicker({
  color,
  onChange,
  presetColors = ["#000000", "#FF0000", "#00FF00", "#0000FF"],
}: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [currentColor, setCurrentColor] = useState(color)
  const [customColor, setCustomColor] = useState(color)
  const colorInputRef = useRef<HTMLInputElement>(null)

  // 同步外部颜色变化
  useEffect(() => {
    setCurrentColor(color)
    setCustomColor(color)
  }, [color])

  // 选择预设颜色
  const handleSelectColor = (selectedColor: string) => {
    setCurrentColor(selectedColor)
    onChange(selectedColor)
    setIsOpen(false)
  }

  // 处理自定义颜色变化
  const handleCustomColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomColor(e.target.value)
  }

  // 应用自定义颜色
  const applyCustomColor = () => {
    setCurrentColor(customColor)
    onChange(customColor)
    setIsOpen(false)
  }

  // 打开颜色选择器时聚焦自定义颜色输入框
  useEffect(() => {
    if (isOpen && colorInputRef.current) {
      colorInputRef.current.focus()
    }
  }, [isOpen])

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="w-full justify-between" onClick={() => setIsOpen(true)}>
          <div className="flex items-center gap-2">
            <div className="h-5 w-5 rounded-full border" style={{ backgroundColor: currentColor }} />
            <span>{currentColor}</span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-3">
        <div className="grid gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-sm">预设颜色</h4>
            <div className="grid grid-cols-5 gap-2">
              {presetColors.map((presetColor) => (
                <button
                  key={presetColor}
                  className={cn(
                    "h-8 w-8 rounded-full border flex items-center justify-center",
                    currentColor === presetColor && "ring-2 ring-primary ring-offset-2",
                  )}
                  style={{ backgroundColor: presetColor }}
                  onClick={() => handleSelectColor(presetColor)}
                >
                  {currentColor === presetColor && <Check className="h-4 w-4 text-white" />}
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm">自定义颜色</h4>
            <div className="flex gap-2">
              <div className="h-8 w-8 rounded-full border" style={{ backgroundColor: customColor }} />
              <input
                ref={colorInputRef}
                type="text"
                value={customColor}
                onChange={handleCustomColorChange}
                className="flex h-8 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="#RRGGBB"
                pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"
              />
              <Button size="sm" className="h-8 px-2" onClick={applyCustomColor}>
                应用
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm">颜色选择器</h4>
            <input
              type="color"
              value={customColor}
              onChange={(e) => setCustomColor(e.target.value)}
              className="w-full h-10 cursor-pointer"
            />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}

