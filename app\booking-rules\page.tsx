"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Home as HomeIcon, Save as SaveIcon, Undo as UndoIcon, Settings, BookOpen, ChevronDown, ChevronRight, Calendar, Users, AlertTriangle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// 课程类型定义
const courseTypes = [
  { id: "group", name: "团课", color: "#4285F4", description: "多人参与的团体课程" },
  { id: "small", name: "小班课", color: "#34A853", description: "小班制精品课程" },
  { id: "premium", name: "精品课", color: "#FBBC05", description: "高端精品课程" },
  { id: "private", name: "私教课", color: "#EA4335", description: "一对一私人教练课程" },
  { id: "training", name: "教培课", color: "#9C27B0", description: "教练培训课程" },
]

export default function BookingRulesPage() {
  const [activeTab, setActiveTab] = useState("common")
  const [hasChanges, setHasChanges] = useState(false)
  const { toast } = useToast()

  // 全局设置状态
  const [globalSettings, setGlobalSettings] = useState({
    // 关键设置
    maxBookableCount: 15, // 会员最多可预约天数的课程

    // 预约方式设置
    bookingMode: 'limited', // 'limited' 限制预约, 'unlimited' 扣减天数
    limitedBookingsPerMonth: 5, // 限制预约模式：一个自然月内，每超过X次预约未到，X天内不能自主预约
    limitedRestrictDays: 5, // 限制预约模式：限制天数
    unlimitedBookingsPerMonth: 0, // 扣减天数模式：一个自然月内，每超过X次预约未到，扣减使用卡期限X天
    unlimitedDeductDays: 0, // 扣减天数模式：扣减天数

    // 期限卡约课设置
    periodCardEnabled: true, // 开启

    // 教室冲突限制
    classroomConflictEnabled: true, // 开启教室冲突限制

    // 预约设置
    advanceBookingDays: 7,
    bookingDeadlineMinutes: 30,
    maxBookingsPerDay: 3,
    maxBookingsPerWeek: 15,
    allowSelfBooking: true,
    allowStaffBooking: true,

    // 签到设置
    checkinStartMinutes: 30,
    checkinEndMinutes: 15,
    allowSelfCheckin: true,
    allowStaffCheckin: true,

    // 取消设置
    cancelDeadlineMinutes: 120,
    allowSelfCancel: true,
    allowStaffCancel: true,

    // 候补设置
    waitlistEnabled: true,
    maxWaitlistSize: 10,
    waitlistStopMinutes: 30,
  })

  // 课程类型规则状态
  const [courseTypeRules, setCourseTypeRules] = useState({
    group: { enabled: false },
    small: { enabled: false },
    premium: { enabled: true, advanceBookingDays: 14, maxBookingsPerDay: 2 },
    private: { enabled: true, advanceBookingDays: 30, bookingDeadlineMinutes: 240 },
    training: { enabled: false },
  })

  const [collapsedSections, setCollapsedSections] = useState({
    bookableCount: false,
    missedBooking: false,
    classroomConflict: false,
    memberLevel: false,
    memberTag: false,
  })

  const toggleSection = (section: keyof typeof collapsedSections) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const handleChange = () => {
    setHasChanges(true)
  }

  const handleSave = () => {
    console.log("保存预约规则设置")
    toast({
      title: "设置已保存",
      description: "预约规则设置已成功保存",
    })
    setHasChanges(false)
  }

  const handleReset = () => {
    console.log("重置预约规则设置")
    toast({
      title: "设置已重置",
      description: "预约规则设置已重置为默认值",
    })
    setHasChanges(false)
  }

  const updateGlobalSetting = (field: string, value: any) => {
    setGlobalSettings(prev => ({
      ...prev,
      [field]: value
    }))
    handleChange()
  }

  const updateCourseTypeRule = (courseType: string, field: string, value: any) => {
    setCourseTypeRules(prev => ({
      ...prev,
      [courseType]: {
        ...prev[courseType],
        [field]: value
      }
    }))
    handleChange()
  }

  const toggleCourseTypeRule = (courseType: string) => {
    updateCourseTypeRule(courseType, 'enabled', !courseTypeRules[courseType]?.enabled)
  }

  return (
    <div className="space-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard" className="flex items-center">
              <HomeIcon className="h-4 w-4" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>预约规则</BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">预约规则设置</h1>
          <p className="text-muted-foreground">
            设置各课程类型的预约规则和通用默认设置
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={!hasChanges}
          >
            <UndoIcon className="mr-2 h-4 w-4" />
            重置
          </Button>
          <Button
            onClick={handleSave}
            disabled={!hasChanges}
          >
            <SaveIcon className="mr-2 h-4 w-4" />
            保存设置
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="h-auto p-1 bg-muted/50 w-full justify-start overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
          <div className="flex gap-1 min-w-max">
            <TabsTrigger value="common" className="flex items-center whitespace-nowrap px-3 py-2 text-sm">
              <Settings className="h-4 w-4 mr-1" />
              通用设置
            </TabsTrigger>
            <TabsTrigger value="member" className="flex items-center whitespace-nowrap px-3 py-2 text-sm">
              <Users className="h-4 w-4 mr-1" />
              会员设置
            </TabsTrigger>
            {courseTypes.map((type) => (
              <TabsTrigger key={type.id} value={type.id} className="flex items-center whitespace-nowrap px-3 py-2 text-sm">
                <div
                  className="w-3 h-3 rounded-full mr-1"
                  style={{ backgroundColor: type.color }}
                />
                {type.name}
              </TabsTrigger>
            ))}
          </div>
        </TabsList>

        {/* 通用设置 */}
        <TabsContent value="common" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>通用预约规则</CardTitle>
              <CardDescription>
                设置预约系统的通用规则和默认参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* 可预约天数设置 */}
              <Collapsible open={!collapsedSections.bookableCount} onOpenChange={() => toggleSection('bookableCount')}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="text-base font-medium text-gray-900">可预约天数</h3>
                  </div>
                  {collapsedSections.bookableCount ? (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 pt-4 px-4 pb-4 border-l border-r border-b border-gray-200 rounded-b-lg bg-white">
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <Label htmlFor="max-bookable-count" className="text-sm font-medium text-gray-900">可预约天数</Label>
                      <div className="flex items-center space-x-2">
                        <Input
                          id="max-bookable-count"
                          type="number"
                          value={globalSettings.maxBookableCount}
                          onChange={(e) => updateGlobalSetting('maxBookableCount', parseInt(e.target.value) || 0)}
                          className="w-24 h-9 text-center"
                        />
                        <span className="text-sm text-gray-600">天</span>
                      </div>
                      <p className="text-xs text-gray-500">
                        会员最多可预约天数的课程，0表示不限
                      </p>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* 失约设置 */}
              <Collapsible open={!collapsedSections.missedBooking} onOpenChange={() => toggleSection('missedBooking')}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="text-base font-medium text-gray-900">失约设置</h3>
                  </div>
                  {collapsedSections.missedBooking ? (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 pt-4 px-4 pb-4 border-l border-r border-b border-gray-200 rounded-b-lg bg-white">
                  <div className="space-y-6">
                    {/* 期限卡约课设置 */}
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium text-gray-900">期限卡约课设置</Label>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={globalSettings.periodCardEnabled}
                            onCheckedChange={(checked) => updateGlobalSetting('periodCardEnabled', checked)}
                          />
                          <span className="text-sm text-gray-600">
                            {globalSettings.periodCardEnabled ? '开启' : '关闭'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* 失约方式 */}
                    <div className="space-y-4">
                      <Label className="text-sm font-medium text-gray-900">失约方式</Label>

                      {/* 限制预约模式 */}
                      <div className="space-y-3">
                        <div className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg bg-white">
                          <div className="flex items-center mt-1">
                            <input
                              type="radio"
                              name="bookingMode"
                              checked={globalSettings.bookingMode === 'limited'}
                              onChange={() => updateGlobalSetting('bookingMode', 'limited')}
                              className="h-4 w-4 text-primary border-gray-300 focus:ring-primary"
                            />
                          </div>
                          <div className="flex-1 space-y-3">
                            <div className="flex items-center flex-wrap gap-2">
                              <span className="text-sm text-gray-700">限制预约，一个自然月内，每超过</span>
                              <Input
                                type="number"
                                value={globalSettings.limitedBookingsPerMonth}
                                onChange={(e) => updateGlobalSetting('limitedBookingsPerMonth', parseInt(e.target.value) || 0)}
                                className="w-16 h-8 text-center"
                                disabled={globalSettings.bookingMode !== 'limited'}
                              />
                              <span className="text-sm text-gray-700">次预约未到，</span>
                              <Input
                                type="number"
                                value={globalSettings.limitedRestrictDays}
                                onChange={(e) => updateGlobalSetting('limitedRestrictDays', parseInt(e.target.value) || 0)}
                                className="w-16 h-8 text-center"
                                disabled={globalSettings.bookingMode !== 'limited'}
                              />
                              <span className="text-sm text-gray-700">天内不能自主预约</span>
                            </div>
                          </div>
                        </div>

                        {/* 扣减天数模式 */}
                        <div className="flex items-start space-x-3 p-4 border border-gray-200 rounded-lg bg-white">
                          <div className="flex items-center mt-1">
                            <input
                              type="radio"
                              name="bookingMode"
                              checked={globalSettings.bookingMode === 'unlimited'}
                              onChange={() => updateGlobalSetting('bookingMode', 'unlimited')}
                              className="h-4 w-4 text-primary border-gray-300 focus:ring-primary"
                            />
                          </div>
                          <div className="flex-1 space-y-3">
                            <div className="flex items-center flex-wrap gap-2">
                              <span className="text-sm text-gray-700">扣减天数，一个自然月内，每超过</span>
                              <Input
                                type="number"
                                value={globalSettings.unlimitedBookingsPerMonth}
                                onChange={(e) => updateGlobalSetting('unlimitedBookingsPerMonth', parseInt(e.target.value) || 0)}
                                className="w-16 h-8 text-center"
                                disabled={globalSettings.bookingMode !== 'unlimited'}
                                placeholder="填数字"
                              />
                              <span className="text-sm text-gray-700">次预约未到，扣减使用卡期限</span>
                              <Input
                                type="number"
                                value={globalSettings.unlimitedDeductDays}
                                onChange={(e) => updateGlobalSetting('unlimitedDeductDays', parseInt(e.target.value) || 0)}
                                className="w-16 h-8 text-center"
                                disabled={globalSettings.bookingMode !== 'unlimited'}
                                placeholder="填数字"
                              />
                              <span className="text-sm text-gray-700">天</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* 教室冲突限制 */}
              <Collapsible open={!collapsedSections.classroomConflict} onOpenChange={() => toggleSection('classroomConflict')}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center">
                    <Settings className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="text-base font-medium text-gray-900">教室冲突限制</h3>
                  </div>
                  {collapsedSections.classroomConflict ? (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 pt-4 px-4 pb-4 border-l border-r border-b border-gray-200 rounded-b-lg bg-white">
                  <div className="space-y-4">
                    {/* 教室冲突限制开关 */}
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium text-gray-900">教室冲突限制</Label>
                        <p className="text-xs text-gray-500">
                          开启后，一个教室同一时段只能排一节课
                        </p>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={globalSettings.classroomConflictEnabled}
                            onCheckedChange={(checked) => updateGlobalSetting('classroomConflictEnabled', checked)}
                          />
                          <span className="text-sm text-gray-600">
                            {globalSettings.classroomConflictEnabled ? '开启' : '关闭'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* 说明信息 */}
                    {globalSettings.classroomConflictEnabled && (
                      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-start space-x-2">
                          <div className="flex-shrink-0 mt-0.5">
                            <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">i</span>
                            </div>
                          </div>
                          <div className="text-sm text-blue-700">
                            <p className="font-medium mb-1">教室冲突限制说明：</p>
                            <ul className="space-y-1 text-xs">
                              <li>• 开启后，系统将检查教室时间冲突</li>
                              <li>• 同一教室在同一时段只能安排一节课程</li>
                              <li>• 排课时会自动提示冲突并阻止重复排课</li>
                              <li>• 适用于所有课程类型和教室</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CollapsibleContent>
              </Collapsible>

            </CardContent>
          </Card>
        </TabsContent>

        {/* 会员设置 */}
        <TabsContent value="member" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>会员预约规则</CardTitle>
              <CardDescription>
                设置会员等级、标签的优待或限制规则，优先级高于课程类型设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">

              {/* 会员等级设置 */}
              <Collapsible open={!collapsedSections.memberLevel} onOpenChange={() => toggleSection('memberLevel')}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="text-base font-medium text-gray-900">会员等级规则</h3>
                  </div>
                  {collapsedSections.memberLevel ? (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="vip-advance-days">VIP会员提前预约天数</Label>
                      <Input
                        id="vip-advance-days"
                        type="number"
                        defaultValue="14"
                      />
                      <p className="text-xs text-muted-foreground">
                        VIP会员可以比普通会员提前更多天预约
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="vip-max-bookings">VIP会员每日最大预约数</Label>
                      <Input
                        id="vip-max-bookings"
                        type="number"
                        defaultValue="5"
                      />
                      <p className="text-xs text-muted-foreground">
                        VIP会员每日可预约的最大课程数
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <Label>VIP会员优先预约</Label>
                      <p className="text-sm text-muted-foreground">
                        VIP会员在课程满员时可优先预约
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* 会员标签设置 */}
              <Collapsible open={!collapsedSections.memberTag} onOpenChange={() => toggleSection('memberTag')}>
                <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                  <div className="flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2 text-primary" />
                    <h3 className="text-base font-medium text-gray-900">会员标签规则</h3>
                  </div>
                  {collapsedSections.memberTag ? (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  )}
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <Label>新会员限制</Label>
                        <p className="text-sm text-muted-foreground">
                          新会员预约限制更严格
                        </p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <Label>黑名单限制</Label>
                        <p className="text-sm text-muted-foreground">
                          黑名单会员禁止预约
                        </p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 课程类型设置 */}
        {courseTypes.map((type) => (
          <TabsContent key={type.id} value={type.id} className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <div
                    className="w-5 h-5 rounded-full mr-2"
                    style={{ backgroundColor: type.color }}
                  />
                  {type.name}规则
                </CardTitle>
                <CardDescription>
                  {type.description}的预约规则设置
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium flex items-center">
                        <div
                          className="w-4 h-4 rounded-full mr-2"
                          style={{ backgroundColor: type.color }}
                        />
                        {type.name}
                      </h3>
                      <p className="text-sm text-muted-foreground">{type.description}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Label htmlFor={`${type.id}-enabled`}>启用自定义规则</Label>
                      <Switch
                        id={`${type.id}-enabled`}
                        checked={courseTypeRules[type.id]?.enabled || false}
                        onCheckedChange={() => toggleCourseTypeRule(type.id)}
                      />
                    </div>
                  </div>

                  {courseTypeRules[type.id]?.enabled ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`${type.id}-advance-days`}>提前预约天数</Label>
                          <Input
                            id={`${type.id}-advance-days`}
                            type="number"
                            value={courseTypeRules[type.id]?.advanceBookingDays || ''}
                            onChange={(e) => updateCourseTypeRule(type.id, 'advanceBookingDays', parseInt(e.target.value) || 0)}
                            placeholder="使用通用设置"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor={`${type.id}-deadline`}>预约截止时间（分钟）</Label>
                          <Input
                            id={`${type.id}-deadline`}
                            type="number"
                            value={courseTypeRules[type.id]?.bookingDeadlineMinutes || ''}
                            onChange={(e) => updateCourseTypeRule(type.id, 'bookingDeadlineMinutes', parseInt(e.target.value) || 0)}
                            placeholder="使用通用设置"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`${type.id}-max-day`}>每日最大预约数</Label>
                          <Input
                            id={`${type.id}-max-day`}
                            type="number"
                            value={courseTypeRules[type.id]?.maxBookingsPerDay || ''}
                            onChange={(e) => updateCourseTypeRule(type.id, 'maxBookingsPerDay', parseInt(e.target.value) || 0)}
                            placeholder="使用通用设置"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor={`${type.id}-cancel-deadline`}>取消截止时间（分钟）</Label>
                          <Input
                            id={`${type.id}-cancel-deadline`}
                            type="number"
                            value={courseTypeRules[type.id]?.cancelDeadlineMinutes || ''}
                            onChange={(e) => updateCourseTypeRule(type.id, 'cancelDeadlineMinutes', parseInt(e.target.value) || 0)}
                            placeholder="使用通用设置"
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>该课程类型使用通用规则</p>
                      <p className="text-sm">启用自定义规则以设置特定参数</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}

      </Tabs>
    </div>
  )
}
