// 检查tenant表结构
const mysql = require('mysql2/promise');

async function checkTenantStructure() {
  console.log('检查tenant表结构...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    // 1. 检查tenant表结构
    console.log('\n1. 检查tenant表结构:');
    const [columns] = await connection.execute('DESCRIBE tenant');
    console.log('tenant表字段:');
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? col.Key : ''} ${col.Default !== null ? `DEFAULT ${col.Default}` : ''}`);
    });

    // 2. 检查现有数据
    console.log('\n2. 检查现有数据:');
    const [tenants] = await connection.execute('SELECT * FROM tenant');
    console.log(`租户数量: ${tenants.length}`);
    if (tenants.length > 0) {
      console.log('租户列表:');
      tenants.forEach((tenant, index) => {
        console.log(`  ${index + 1}. ${JSON.stringify(tenant)}`);
      });
    }

    await connection.end();
    console.log('\n✓ 检查完成');

  } catch (error) {
    console.error('检查失败:', error.message);
  }
}

checkTenantStructure();
