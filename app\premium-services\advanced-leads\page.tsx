"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import {
  ChevronRight,
  Info,
  Lock,
  Star,
  Sparkles,
  ArrowRight,
  CheckCircle2,
  Clock,
  Activity,
  Smartphone,
  Globe,
  BarChart,
  UserCheck,
  BellRing
} from "lucide-react"
import Link from "next/link"

export default function AdvancedLeadsPage() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <div className="flex items-center gap-2">
            <div className="text-sm breadcrumbs">
              <ul className="flex items-center space-x-1">
                <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
                <li><ChevronRight className="h-4 w-4" /></li>
                <li>高级潜客管理</li>
              </ul>
            </div>
            <Badge variant="outline" className="ml-2">高级版</Badge>
          </div>
          <h1 className="text-2xl font-semibold tracking-tight mt-1">高级潜客管理</h1>
          <p className="text-muted-foreground">全面的潜客跟进与转化系统，提升获客效率</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href="/leads">
              基础版潜客管理
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
          <Button>
            <Sparkles className="mr-2 h-4 w-4" />
            升级高级版
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">功能概览</TabsTrigger>
          <TabsTrigger value="features">详细功能</TabsTrigger>
          <TabsTrigger value="pricing">价格与升级</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 mt-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>高级版功能</AlertTitle>
            <AlertDescription>
              高级潜客管理是专业版的增值功能，提供更强大的潜客跟进、分析和转化工具，帮助您提高获客效率和转化率。
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">智能跟进提醒</CardTitle>
                <CardDescription>自动提醒跟进时间，不错过任何潜在客户</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-amber-500">
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 fill-amber-500" />
                  </div>
                  <Lock className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">潜客画像分析</CardTitle>
                <CardDescription>深入了解潜客特征，精准营销</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-amber-500">
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 fill-amber-500" />
                  </div>
                  <Lock className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">转化漏斗分析</CardTitle>
                <CardDescription>分析潜客转化各环节数据，优化流程</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-amber-500">
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1 fill-amber-500" />
                    <Star className="h-4 w-4 mr-1" />
                  </div>
                  <Lock className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>基础版 vs 高级版</CardTitle>
                <CardDescription>功能对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 font-medium">
                    <div>功能</div>
                    <div>基础版</div>
                    <div>高级版</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>潜客记录</div>
                    <div className="text-center">✓</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>基础跟进</div>
                    <div className="text-center">✓</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>智能跟进提醒</div>
                    <div className="text-center">✗</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>潜客画像分析</div>
                    <div className="text-center">✗</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>转化漏斗分析</div>
                    <div className="text-center">✗</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>访客行为分析</div>
                    <div className="text-center">✗</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>访问路径追踪</div>
                    <div className="text-center">✗</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>停留时长分析</div>
                    <div className="text-center">✗</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>客户端数据</div>
                    <div className="text-center">✗</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>自动化营销</div>
                    <div className="text-center">✗</div>
                    <div className="text-center">✓</div>
                  </div>
                  <div className="grid grid-cols-3 gap-4 items-center">
                    <div>潜客评分</div>
                    <div className="text-center">✗</div>
                    <div className="text-center">✓</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>高级版优势</CardTitle>
                <CardDescription>提升获客效率与转化率</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">提高跟进效率</p>
                      <p className="text-sm text-muted-foreground">智能提醒系统确保及时跟进，不错过任何潜在客户</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">精准营销</p>
                      <p className="text-sm text-muted-foreground">基于潜客画像分析，提供个性化营销方案</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">优化转化流程</p>
                      <p className="text-sm text-muted-foreground">通过漏斗分析，找出转化瓶颈，提高整体转化率</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-2">
                    <CheckCircle2 className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">自动化营销</p>
                      <p className="text-sm text-muted-foreground">根据潜客行为自动触发营销活动，减少人工操作</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="features" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>高级潜客管理详细功能</CardTitle>
              <CardDescription>全面了解高级版提供的强大功能</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">智能跟进提醒</h3>
                  <p className="text-muted-foreground">根据潜客状态和跟进历史，自动生成最佳跟进时间建议，并通过系统通知、短信或邮件提醒销售人员。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">潜客画像分析</h3>
                  <p className="text-muted-foreground">收集和分析潜客的兴趣、行为和需求，生成详细的潜客画像，帮助销售人员更好地了解潜客，提供个性化服务。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">转化漏斗分析</h3>
                  <p className="text-muted-foreground">追踪潜客从获取到转化的全过程，分析各环节的转化率和流失率，找出转化瓶颈，优化销售流程。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">自动化营销</h3>
                  <p className="text-muted-foreground">根据潜客的行为和状态，自动触发相应的营销活动，如发送课程介绍、优惠信息等，提高转化效率。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">潜客评分</h3>
                  <p className="text-muted-foreground">基于多维度数据，对潜客进行自动评分，帮助销售团队优先跟进高价值潜客，提高资源利用效率。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">访客行为分析</h3>
                  <p className="text-muted-foreground">深入分析潜客在网站、小程序等平台的浏览行为，包括页面停留时长、浏览路径、点击热点等，了解潜客兴趣和决策过程。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">访问路径追踪</h3>
                  <p className="text-muted-foreground">追踪潜客从入口到转化的完整访问路径，识别最有效的转化路径和潜在的流失点，优化用户体验和转化流程。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">停留时长分析</h3>
                  <p className="text-muted-foreground">分析潜客在各页面的停留时长，识别最受关注的内容和可能的兴趣点，为精准营销提供数据支持。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">客户端数据分析</h3>
                  <p className="text-muted-foreground">收集并分析潜客使用的设备类型、操作系统、浏览器等信息，优化不同平台的用户体验，提高转化率。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">多渠道获客分析</h3>
                  <p className="text-muted-foreground">分析不同渠道获取的潜客质量和转化率，优化营销投入，提高获客ROI。</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-lg font-medium">团队协作</h3>
                  <p className="text-muted-foreground">支持销售团队协作跟进潜客，记录沟通历史，确保信息透明共享，提高团队效率。</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pricing" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>价格与升级</CardTitle>
              <CardDescription>选择适合您的方案</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="border rounded-lg p-6">
                    <h3 className="text-xl font-medium mb-2">月度订阅</h3>
                    <div className="text-3xl font-bold mb-4">¥299<span className="text-base font-normal text-muted-foreground">/月</span></div>
                    <ul className="space-y-2 mb-6">
                      <li className="flex items-center">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        <span>所有高级潜客管理功能</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        <span>每月可随时取消</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        <span>标准技术支持</span>
                      </li>
                    </ul>
                    <Button className="w-full">选择此方案</Button>
                  </div>

                  <div className="border rounded-lg p-6 bg-primary/5 border-primary/20">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-xl font-medium">年度订阅</h3>
                      <Badge>推荐</Badge>
                    </div>
                    <div className="text-3xl font-bold mb-1">¥2,388<span className="text-base font-normal text-muted-foreground">/年</span></div>
                    <p className="text-sm text-muted-foreground mb-4">相当于¥199/月，节省33%</p>
                    <ul className="space-y-2 mb-6">
                      <li className="flex items-center">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        <span>所有高级潜客管理功能</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        <span>两个月免费使用</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        <span>优先技术支持</span>
                      </li>
                      <li className="flex items-center">
                        <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                        <span>免费获取功能更新</span>
                      </li>
                    </ul>
                    <Button className="w-full">选择此方案</Button>
                  </div>
                </div>

                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-medium mb-2">企业定制方案</h3>
                  <p className="text-sm text-muted-foreground mb-4">需要更多功能或多门店管理？我们提供企业级定制方案。</p>
                  <Button variant="outline">联系销售顾问</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
