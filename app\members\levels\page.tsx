"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Plus, Search, MoreHorizontal, Edit, Trash2, Settings, Download, Upload, RefreshCw } from "lucide-react"
import { MemberLevelDialog } from "@/components/members/levels/member-level-dialog"
import { LevelSettingsDialog } from "@/components/members/levels/level-settings-dialog"

// 模拟会员等级数据
const memberLevels = [
  {
    id: "1",
    name: "标准会员",
    description: "基础会员等级，适用于所有新注册会员",
    color: "#3b82f6",
    isDefault: true,
    memberCount: 156,
    createdAt: "2023-01-01",
    updatedAt: "2023-01-01",
    status: "active",
    benefits: [
      "可预约基础课程",
      "可使用基础设施",
    ],
    upgradeRules: "无自动升级规则",
    order: 1,
  },
  {
    id: "2",
    name: "银卡会员",
    description: "中级会员，累计消费满2000元自动升级",
    color: "#94a3b8",
    isDefault: false,
    memberCount: 89,
    createdAt: "2023-01-01",
    updatedAt: "2023-02-15",
    status: "active",
    benefits: [
      "可预约所有常规课程",
      "享受商品9.5折优惠",
      "生日赠送体验课1次",
    ],
    upgradeRules: "累计消费满5000元自动升级为金卡会员",
    order: 2,
  },
  {
    id: "3",
    name: "金卡会员",
    description: "高级会员，累计消费满5000元自动升级",
    color: "#eab308",
    isDefault: false,
    memberCount: 45,
    createdAt: "2023-01-01",
    updatedAt: "2023-03-10",
    status: "active",
    benefits: [
      "可预约所有课程",
      "享受商品9折优惠",
      "生日赠送体验课2次",
      "每月赠送1次私教课",
    ],
    upgradeRules: "累计消费满10000元自动升级为白金会员",
    order: 3,
  },
  {
    id: "4",
    name: "白金会员",
    description: "最高级别会员，累计消费满10000元自动升级",
    color: "#a855f7",
    isDefault: false,
    memberCount: 23,
    createdAt: "2023-01-01",
    updatedAt: "2023-04-05",
    status: "active",
    benefits: [
      "可预约所有课程",
      "享受商品8.5折优惠",
      "生日赠送体验课3次",
      "每月赠送2次私教课",
      "专属更衣柜",
      "免费停车",
    ],
    upgradeRules: "最高等级，无自动升级规则",
    order: 4,
  },
]

export default function MemberLevelsPage() {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [showLevelDialog, setShowLevelDialog] = useState(false)
  const [editingLevel, setEditingLevel] = useState<any>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [levelToDelete, setLevelToDelete] = useState<any>(null)
  const [isFeatureEnabled, setIsFeatureEnabled] = useState(true)
  const [showLevelSettings, setShowLevelSettings] = useState(false)

  const filteredLevels = memberLevels.filter(
    (level) =>
      level.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      level.description.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleAddLevel = () => {
    setEditingLevel(null)
    setShowLevelDialog(true)
  }

  const handleEditLevel = (level: any) => {
    setEditingLevel(level)
    setShowLevelDialog(true)
  }

  const handleDeleteLevel = (level: any) => {
    setLevelToDelete(level)
    setShowDeleteDialog(true)
  }

  const confirmDeleteLevel = () => {
    // 这里应该有API调用来删除会员等级
    toast({
      title: "会员等级已删除",
      description: `${levelToDelete.name}已成功删除`,
    })
    setShowDeleteDialog(false)
  }

  const toggleFeature = (enabled: boolean) => {
    setIsFeatureEnabled(enabled)
    toast({
      title: enabled ? "会员等级功能已启用" : "会员等级功能已禁用",
      description: enabled ? "会员等级功能现在可用" : "会员等级功能已被禁用，会员列表将不显示等级信息",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">会员等级管理</h1>
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowLevelSettings(true)}
            className="hover:bg-gray-100 active:bg-gray-200 transition-colors"
          >
            <Settings className="mr-2 h-4 w-4" />
            等级设置
          </Button>
          <Button
            onClick={handleAddLevel}
            className="hover:bg-blue-600 active:bg-blue-700 transition-colors"
          >
            <Plus className="mr-2 h-4 w-4" />
            添加等级
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>会员等级功能</CardTitle>
          <CardDescription>启用或禁用会员等级系统</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="feature-toggle">会员等级功能</Label>
              <p className="text-sm text-muted-foreground">
                启用后，会员将根据设定的规则自动分配等级，并在会员列表中显示
              </p>
            </div>
            <Switch
              id="feature-toggle"
              checked={isFeatureEnabled}
              onCheckedChange={toggleFeature}
            />
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <div className="relative w-full">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索会员等级..."
            className="pl-8 w-full max-w-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]"></TableHead>
              <TableHead>等级名称</TableHead>
              <TableHead>描述</TableHead>
              <TableHead className="text-center">会员数量</TableHead>
              <TableHead className="text-center">购买条件</TableHead>
              <TableHead className="text-center">状态</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLevels.map((level) => (
              <TableRow key={level.id}>
                <TableCell>
                  <div
                    className="h-4 w-4 rounded-full"
                    style={{ backgroundColor: level.color }}
                  />
                </TableCell>
                <TableCell className="font-medium">{level.name}</TableCell>
                <TableCell className="max-w-[300px] truncate">
                  {level.description}
                </TableCell>
                <TableCell className="text-center">{level.memberCount}</TableCell>
                <TableCell className="text-center">
                  {level.isDefault ? "基础会员" : level.upgradeRules.split("，")[0]}
                </TableCell>
                <TableCell className="text-center">
                  <Badge
                    variant="outline"
                    className={`${level.status === "active" ? "bg-blue-50 text-blue-600 hover:bg-blue-50" : "bg-gray-50 text-gray-600 hover:bg-gray-50"}`}
                  >
                    {level.status === "active" ? "启用" : "禁用"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditLevel(level)}
                    className="hover:bg-gray-100 active:bg-gray-200 transition-colors"
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    编辑
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <MemberLevelDialog
        open={showLevelDialog}
        onOpenChange={setShowLevelDialog}
        level={editingLevel}
      />

      <LevelSettingsDialog
        open={showLevelSettings}
        onOpenChange={setShowLevelSettings}
      />

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除会员等级</AlertDialogTitle>
            <AlertDialogDescription>
              {levelToDelete && (
                <>
                  您确定要删除 <strong>{levelToDelete.name}</strong> 等级吗？此操作无法撤销，
                  该等级下的会员将被重新分配到默认等级。
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteLevel} className="bg-destructive text-destructive-foreground">
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
