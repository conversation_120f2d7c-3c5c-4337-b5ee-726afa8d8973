"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  Search, QrCode, CreditCard, Calendar, User, CheckCircle,
  Clock, ShoppingBag, Smartphone, Coffee, Users, X, Plus,
  ChevronRight, ArrowRight, Printer, Receipt, DollarSign,
  Minus, Calculator
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// 商品类型定义
interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  type: 'membership' | 'course' | 'product' | 'virtual';
  icon: React.ReactNode;
  discount?: string;
  discountType?: 'percent' | 'member' | 'coupon';
  deliveryType?: 'logistics' | 'self_pickup' | 'verification';
  stock?: number;
  sku?: string;
  thumbnail?: string;
}

// 购物车商品类型
interface CartItem extends Product {
  quantity: number;
}

// 客户类型
interface Customer {
  id: string;
  name: string;
  phone: string;
  avatar?: string;
  membershipType?: string;
  points?: number;
  balance?: number;
  expiryDate?: string;
}

// 优惠券类型
interface Coupon {
  id: string;
  name: string;
  discount: number;
  minSpend: number;
  expiryDate: string;
  used: boolean;
}

export default function CashierPage() {
  const [activeTab, setActiveTab] = useState("verification")
  const [searchQuery, setSearchQuery] = useState("")
  const [showVerifyDialog, setShowVerifyDialog] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [showBookingDialog, setShowBookingDialog] = useState(false)
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [verificationSource, setVerificationSource] = useState<"shop" | "meituan" | "douyin" | "other">("shop")

  // 会员搜索状态
  const [memberSearchQuery, setMemberSearchQuery] = useState("")
  const [showMemberSearchResults, setShowMemberSearchResults] = useState(false)
  const [memberSearchResults, setMemberSearchResults] = useState<Customer[]>([])

  // 模拟会员数据
  const members: Customer[] = [
    {
      id: "member-1",
      name: "张三",
      phone: "13800138001",
      avatar: "/images/avatar-1.jpg",
      membershipType: "高级会员",
      points: 1280,
      balance: 500,
      expiryDate: "2023-12-31"
    },
    {
      id: "member-2",
      name: "李四",
      phone: "13900139002",
      avatar: "/images/avatar-2.jpg",
      membershipType: "普通会员",
      points: 680,
      balance: 200,
      expiryDate: "2023-11-30"
    },
    {
      id: "member-3",
      name: "王五",
      phone: "13700137003",
      avatar: "/images/avatar-3.jpg",
      membershipType: "VIP会员",
      points: 2500,
      balance: 1200,
      expiryDate: "2024-06-30"
    }
  ]

  // 搜索会员
  const searchMembers = (query: string) => {
    if (!query.trim()) {
      setMemberSearchResults([]);
      setShowMemberSearchResults(false);
      return;
    }

    const results = members.filter(
      member =>
        member.name.includes(query) ||
        member.phone.includes(query)
    );

    setMemberSearchResults(results);
    setShowMemberSearchResults(true);
  }

  // 处理会员搜索输入变化
  const handleMemberSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setMemberSearchQuery(query);
    searchMembers(query);
  }

  // 选择会员
  const selectMember = (member: Customer) => {
    setSelectedCustomer(member);
    setShowMemberSearchResults(false);
    setMemberSearchQuery("");
  }

  // 创建访客
  const createVisitor = (name: string, phone: string) => {
    const visitor: Customer = {
      id: `visitor-${Date.now()}`,
      name,
      phone
    };

    setSelectedCustomer(visitor);
    return visitor;
  }

  // 购物车状态
  const [cartItems, setCartItems] = useState<CartItem[]>([
    {
      id: "membership1",
      name: "高级会员卡 - 月卡",
      price: 388.00,
      originalPrice: 388.00,
      type: "membership",
      icon: <ShoppingBag className="h-5 w-5 text-primary" />,
      quantity: 1,
      discountType: "member",
      discount: "会员价"
    },
    {
      id: "course1",
      name: "瑜伽私教课 - 单次",
      price: 199.00,
      originalPrice: 220.00,
      type: "course",
      icon: <Calendar className="h-5 w-5 text-primary" />,
      quantity: 2,
      discountType: "percent",
      discount: "9折"
    }
  ])

  // 支付方式状态
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>("card")
  const [paymentAmount, setPaymentAmount] = useState<string>("736.00")
  const [changeAmount, setChangeAmount] = useState<number | null>(null)

  // 优惠券状态
  const [availableCoupons, setAvailableCoupons] = useState<Coupon[]>([
    {
      id: "coupon1",
      name: "满500减50优惠券",
      discount: 50,
      minSpend: 500,
      expiryDate: "2023-12-31",
      used: false
    }
  ])
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null)
  const [usePoints, setUsePoints] = useState<boolean>(false)
  const [pointsToUse, setPointsToUse] = useState<number>(0)

  // 计算购物车总价
  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  }

  // 计算原价总计
  const calculateOriginalTotal = () => {
    return cartItems.reduce((total, item) => total + ((item.originalPrice || item.price) * item.quantity), 0);
  }

  // 计算优惠金额
  const calculateDiscount = () => {
    const originalTotal = calculateOriginalTotal();
    const subtotal = calculateSubtotal();
    let discount = originalTotal - subtotal;

    // 添加优惠券折扣
    if (selectedCoupon && subtotal >= selectedCoupon.minSpend) {
      discount += selectedCoupon.discount;
    }

    // 添加积分折扣
    if (usePoints && pointsToUse > 0) {
      // 假设100积分=1元
      discount += pointsToUse / 100;
    }

    return discount;
  }

  // 计算应付金额
  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const discount = calculateDiscount();
    return subtotal - discount;
  }

  // 更新支付金额
  useEffect(() => {
    setPaymentAmount(calculateTotal().toFixed(2));
  }, [cartItems, selectedCoupon, usePoints, pointsToUse]);

  // 添加商品到购物车
  const addToCart = (product: Product) => {
    setCartItems(prev => {
      const existingItem = prev.find(item => item.id === product.id);
      if (existingItem) {
        return prev.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prev, { ...product, quantity: 1 }];
      }
    });
  }

  // 从购物车移除商品
  const removeFromCart = (productId: string) => {
    setCartItems(prev => prev.filter(item => item.id !== productId));
  }

  // 更新购物车商品数量
  const updateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) return;

    setCartItems(prev =>
      prev.map(item =>
        item.id === productId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
  }

  // 清空购物车
  const clearCart = () => {
    setCartItems([]);
  }

  // 计算找零
  const calculateChange = () => {
    const total = calculateTotal();
    const paid = parseFloat(paymentAmount);

    if (paid >= total) {
      setChangeAmount(paid - total);
    } else {
      setChangeAmount(null);
    }
  }

  // 使用优惠券
  const applyCoupon = (coupon: Coupon) => {
    setSelectedCoupon(coupon);
  }

  // 取消使用优惠券
  const removeCoupon = () => {
    setSelectedCoupon(null);
  }

  // 使用积分
  const applyPoints = (points: number) => {
    setUsePoints(true);
    setPointsToUse(points);
  }

  // 处理核销
  const handleVerify = (item: any) => {
    setSelectedItem(item)
    setShowVerifyDialog(true)
  }

  // 处理支付
  const handlePayment = () => {
    setShowPaymentDialog(true)
  }

  // 处理代约课
  const handleBooking = (customer: any) => {
    setSelectedCustomer(customer)
    setShowBookingDialog(true)
  }

  // 确认核销
  const confirmVerification = () => {
    // 这里应该有API调用来完成核销
    alert(`已成功核销: ${selectedItem.name}`)
    setShowVerifyDialog(false)
  }

  // 确认支付
  const confirmPayment = () => {
    alert(`支付成功！金额: ¥${calculateTotal().toFixed(2)}`);
    setShowPaymentDialog(false);
    clearCart();
  }

  // 商城商品列表
  const mallProducts: Product[] = [
    {
      id: "product-1",
      name: "瑜伽垫",
      price: 128.00,
      originalPrice: 168.00,
      type: "product",
      icon: <ShoppingBag className="h-5 w-5 mb-1" />,
      discountType: "percent",
      discount: "7.6折",
      deliveryType: "logistics",
      stock: 50,
      sku: "YG-MAT-001",
      thumbnail: "/images/yoga-mat.jpg"
    },
    {
      id: "product-2",
      name: "瑜伽服套装",
      price: 299.00,
      originalPrice: 399.00,
      type: "product",
      icon: <ShoppingBag className="h-5 w-5 mb-1" />,
      discountType: "percent",
      discount: "7.5折",
      deliveryType: "logistics",
      stock: 30,
      sku: "YG-CLOTH-001",
      thumbnail: "/images/yoga-clothes.jpg"
    },
    {
      id: "product-3",
      name: "瑜伽砖",
      price: 39.00,
      originalPrice: 49.00,
      type: "product",
      icon: <ShoppingBag className="h-5 w-5 mb-1" />,
      discountType: "percent",
      discount: "8折",
      deliveryType: "self_pickup",
      stock: 100,
      sku: "YG-BRICK-001",
      thumbnail: "/images/yoga-brick.jpg"
    },
    {
      id: "virtual-1",
      name: "线上瑜伽课程",
      price: 99.00,
      originalPrice: 199.00,
      type: "virtual",
      icon: <Smartphone className="h-5 w-5 mb-1" />,
      discountType: "percent",
      discount: "5折",
      deliveryType: "verification",
      sku: "YG-ONLINE-001",
      thumbnail: "/images/online-course.jpg"
    },
    {
      id: "virtual-2",
      name: "会员身份卡",
      price: 365.00,
      type: "virtual",
      icon: <CreditCard className="h-5 w-5 mb-1" />,
      deliveryType: "verification",
      sku: "YG-MEMBER-001",
      thumbnail: "/images/member-card.jpg"
    }
  ];

  // 快捷商品列表
  const quickProducts: Product[] = [
    {
      id: "private-course",
      name: "私教课",
      price: 199.00,
      originalPrice: 220.00,
      type: "course",
      icon: <Calendar className="h-5 w-5 mb-1" />,
      discountType: "percent",
      discount: "9折"
    },
    {
      id: "group-course",
      name: "团体课",
      price: 99.00,
      type: "course",
      icon: <Users className="h-5 w-5 mb-1" />
    },
    {
      id: "membership-recharge",
      name: "会员充值",
      price: 388.00,
      type: "membership",
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mb-1">
        <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5l6.74-6.76z"></path>
        <line x1="16" y1="8" x2="2" y2="22"></line>
        <line x1="17.5" y1="15" x2="9" y2="15"></line>
      </svg>
    }
  ]

  // 搜索商品状态
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [showAddProductDialog, setShowAddProductDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  // 搜索商品
  const searchProducts = (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    const results = [...mallProducts, ...quickProducts].filter(
      product => product.name.toLowerCase().includes(query.toLowerCase()) ||
                 product.sku?.toLowerCase().includes(query.toLowerCase())
    );

    setSearchResults(results);
    setShowSearchResults(true);
  }

  // 处理搜索输入变化
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    searchProducts(query);
  }

  // 选择商品
  const selectProduct = (product: Product) => {
    setSelectedProduct(product);
    setShowAddProductDialog(true);
    setShowSearchResults(false);
  }

  // 添加商品到购物车（带数量）
  const addProductToCart = (product: Product, quantity: number = 1) => {
    setCartItems(prev => {
      const existingItem = prev.find(item => item.id === product.id);
      if (existingItem) {
        return prev.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      } else {
        return [...prev, { ...product, quantity }];
      }
    });

    setShowAddProductDialog(false);
  }

  // 模拟待核销数据
  const pendingVerifications = [
    {
      id: "v1",
      code: "YG20230501001",
      name: "高级会员卡 - 年卡",
      customer: {
        name: "张三",
        phone: "138****1234",
        avatar: "/avatars/01.png"
      },
      source: "shop",
      orderTime: "2023-05-01 14:30",
      expireTime: "2023-05-08 23:59",
      price: "3688.00"
    },
    {
      id: "v2",
      code: "MT20230502002",
      name: "单次瑜伽体验课",
      customer: {
        name: "李四",
        phone: "139****5678",
        avatar: "/avatars/02.png"
      },
      source: "meituan",
      orderTime: "2023-05-02 10:15",
      expireTime: "2023-06-02 23:59",
      price: "99.00"
    },
    {
      id: "v3",
      code: "DY20230503003",
      name: "瑜伽入门课程 - 5次",
      customer: {
        name: "王五",
        phone: "137****9012",
        avatar: "/avatars/03.png"
      },
      source: "douyin",
      orderTime: "2023-05-03 16:45",
      expireTime: "2023-07-03 23:59",
      price: "399.00"
    }
  ]

  // 模拟会员数据
  const customers = [
    {
      id: "c1",
      name: "张三",
      phone: "13812341234",
      avatar: "/avatars/01.png",
      membershipType: "高级会员",
      remainingClasses: 12,
      expiryDate: "2024-05-01"
    },
    {
      id: "c2",
      name: "李四",
      phone: "13956785678",
      avatar: "/avatars/02.png",
      membershipType: "普通会员",
      remainingClasses: 5,
      expiryDate: "2023-12-15"
    },
    {
      id: "c3",
      name: "王五",
      phone: "13790129012",
      avatar: "/avatars/03.png",
      membershipType: "体验会员",
      remainingClasses: 1,
      expiryDate: "2023-10-30"
    }
  ]

  // 模拟课程数据
  const classes = [
    { id: "cl1", name: "哈他瑜伽", time: "周一 10:00-11:30", teacher: "王老师", remainingSpots: 5 },
    { id: "cl2", name: "阴瑜伽", time: "周二 19:00-20:30", teacher: "李老师", remainingSpots: 3 },
    { id: "cl3", name: "流瑜伽", time: "周三 15:00-16:30", teacher: "张老师", remainingSpots: 8 },
    { id: "cl4", name: "理疗瑜伽", time: "周四 20:00-21:30", teacher: "刘老师", remainingSpots: 4 },
    { id: "cl5", name: "普拉提", time: "周五 18:00-19:30", teacher: "陈老师", remainingSpots: 6 }
  ]

  // 获取渠道图标
  const getSourceIcon = (source: string) => {
    switch (source) {
      case "meituan":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">美团</Badge>
      case "douyin":
        return <Badge variant="outline" className="bg-black text-white">抖音</Badge>
      case "shop":
      default:
        return <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">商城</Badge>
    }
  }

  // 过滤待核销项目
  const filteredVerifications = pendingVerifications.filter(item => {
    if (verificationSource !== "other" && item.source !== verificationSource) {
      return false
    }

    const searchLower = searchQuery.toLowerCase()
    return (
      item.code.toLowerCase().includes(searchLower) ||
      item.name.toLowerCase().includes(searchLower) ||
      item.customer.name.toLowerCase().includes(searchLower) ||
      item.customer.phone.includes(searchQuery)
    )
  })

  // 过滤会员
  const filteredCustomers = customers.filter(customer => {
    const searchLower = searchQuery.toLowerCase()
    return (
      customer.name.toLowerCase().includes(searchLower) ||
      customer.phone.includes(searchQuery) ||
      customer.membershipType.toLowerCase().includes(searchLower)
    )
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">收银台</h1>
        <div className="flex gap-2">
          <Button onClick={handlePayment}>
            <DollarSign className="mr-2 h-4 w-4" />
            收款
          </Button>
          <Button variant="outline">
            <QrCode className="mr-2 h-4 w-4" />
            扫码
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center gap-2 max-w-md">
          <Input
            placeholder="搜索核销码、订单号、客户信息..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="verification">核销管理</TabsTrigger>
          <TabsTrigger value="booking">代约课</TabsTrigger>
        </TabsList>

        <TabsContent value="verification" className="mt-4">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>核销管理</CardTitle>
                  <CardDescription>处理商城、美团、抖音等渠道的核销</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Select value={verificationSource} onValueChange={(value: any) => setVerificationSource(value)}>
                    <SelectTrigger className="w-[130px]">
                      <SelectValue placeholder="选择渠道" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="shop">商城</SelectItem>
                      <SelectItem value="meituan">美团</SelectItem>
                      <SelectItem value="douyin">抖音</SelectItem>
                      <SelectItem value="other">全部渠道</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredVerifications.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    没有找到待核销的项目
                  </div>
                ) : (
                  filteredVerifications.map((item) => (
                    <div key={item.id} className="flex items-center justify-between border-b pb-4">
                      <div className="flex items-center gap-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={item.customer.avatar} alt={item.customer.name} />
                          <AvatarFallback>{item.customer.name.slice(0, 1)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            {item.name}
                            {getSourceIcon(item.source)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {item.customer.name} | {item.customer.phone}
                          </div>
                          <div className="text-xs text-muted-foreground mt-1 flex items-center gap-2">
                            <span>订单号: {item.code}</span>
                            <span>•</span>
                            <span className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {item.expireTime} 到期
                            </span>
                          </div>
                        </div>
                      </div>
                      <Button onClick={() => handleVerify(item)}>
                        核销
                        <ChevronRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="booking" className="mt-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>代约课</CardTitle>
              <CardDescription>为会员预约课程</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredCustomers.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    没有找到会员
                  </div>
                ) : (
                  filteredCustomers.map((customer) => (
                    <div key={customer.id} className="flex items-center justify-between border-b pb-4">
                      <div className="flex items-center gap-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={customer.avatar} alt={customer.name} />
                          <AvatarFallback>{customer.name.slice(0, 1)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{customer.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {customer.phone} | {customer.membershipType}
                          </div>
                          <div className="text-xs text-muted-foreground mt-1 flex items-center gap-2">
                            <span className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {customer.expiryDate} 到期
                            </span>
                            <span>•</span>
                            <span>剩余课时: {customer.remainingClasses}</span>
                          </div>
                        </div>
                      </div>
                      <Button onClick={() => handleBooking(customer)}>
                        约课
                        <ChevronRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 商品搜索结果 */}
      <Dialog open={showSearchResults} onOpenChange={setShowSearchResults}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>搜索商品</DialogTitle>
            <DialogDescription>
              选择要添加到购物车的商品
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {searchResults.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">未找到商品</div>
            ) : (
              <div className="space-y-2">
                {searchResults.map((product) => (
                  <div
                    key={product.id}
                    className="flex items-center gap-3 p-3 hover:bg-muted rounded-md cursor-pointer border"
                    onClick={() => selectProduct(product)}
                  >
                    <div className="w-12 h-12 bg-primary/10 rounded-md flex items-center justify-center">
                      {product.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">{product.name}</div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">¥{product.price.toFixed(2)}</span>
                        {product.discount && (
                          <Badge variant="outline" className="text-xs px-1.5 py-0 h-5 bg-red-50 text-red-600">
                            {product.discount}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full">
                      {product.type === 'product' && '实物商品'}
                      {product.type === 'virtual' && '虚拟商品'}
                      {product.type === 'course' && '课程'}
                      {product.type === 'membership' && '会员'}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSearchResults(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 会员搜索结果 */}
      <Dialog open={showMemberSearchResults} onOpenChange={setShowMemberSearchResults}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>搜索会员</DialogTitle>
            <DialogDescription>
              选择要添加到订单的会员
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {memberSearchResults.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">未找到会员</div>
            ) : (
              <div className="space-y-2">
                {memberSearchResults.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center gap-3 p-3 hover:bg-muted rounded-md cursor-pointer border"
                    onClick={() => selectMember(member)}
                  >
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-medium">{member.name}</div>
                      <div className="text-sm text-muted-foreground">{member.phone}</div>
                      {member.points && (
                        <div className="text-xs text-muted-foreground mt-1">
                          积分: {member.points} | 余额: ¥{member.balance?.toFixed(2) || '0.00'}
                        </div>
                      )}
                    </div>
                    {member.membershipType && (
                      <Badge variant="outline" className="ml-auto px-2 py-1">
                        {member.membershipType}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowMemberSearchResults(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加商品对话框 */}
      <Dialog open={showAddProductDialog} onOpenChange={setShowAddProductDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>添加商品</DialogTitle>
            <DialogDescription>
              请选择商品数量和规格
            </DialogDescription>
          </DialogHeader>

          {selectedProduct && (
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary/10 rounded-md flex items-center justify-center">
                  {selectedProduct.icon}
                </div>
                <div className="flex-1">
                  <div className="font-medium">{selectedProduct.name}</div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">¥{selectedProduct.price.toFixed(2)}</span>
                    {selectedProduct.discount && (
                      <Badge variant="outline" className="text-xs px-1.5 py-0 h-5 bg-red-50 text-red-600">
                        {selectedProduct.discount}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              <div className="border rounded-md p-3">
                <div className="text-sm font-medium mb-2">数量</div>
                <div className="flex items-center">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => {
                      const newQuantity = Math.max(1, (selectedProduct as CartItem).quantity ? (selectedProduct as CartItem).quantity - 1 : 0);
                      setSelectedProduct({...selectedProduct, quantity: newQuantity});
                    }}
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="w-12 text-center">
                    {(selectedProduct as CartItem).quantity || 1}
                  </span>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => {
                      const newQuantity = ((selectedProduct as CartItem).quantity || 1) + 1;
                      setSelectedProduct({...selectedProduct, quantity: newQuantity});
                    }}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {selectedProduct.type === 'product' && (
                <div className="border rounded-md p-3">
                  <div className="text-sm font-medium mb-2">配送方式</div>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant={selectedProduct.deliveryType === 'logistics' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedProduct({...selectedProduct, deliveryType: 'logistics'})}
                    >
                      物流配送
                    </Button>
                    <Button
                      variant={selectedProduct.deliveryType === 'self_pickup' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedProduct({...selectedProduct, deliveryType: 'self_pickup'})}
                    >
                      门店自提
                    </Button>
                  </div>
                </div>
              )}

              {selectedProduct.type === 'virtual' && (
                <div className="border rounded-md p-3">
                  <div className="text-sm font-medium mb-2">使用方式</div>
                  <div className="text-sm text-muted-foreground">
                    虚拟商品将通过验证码方式使用，购买后可在"我的订单"中查看
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter className="sm:justify-between">
            <Button variant="outline" onClick={() => setShowAddProductDialog(false)}>取消</Button>
            <Button
              onClick={() => addProductToCart(
                selectedProduct!,
                (selectedProduct as CartItem).quantity || 1
              )}
            >
              添加到购物车
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 核销确认对话框 */}
      <Dialog open={showVerifyDialog} onOpenChange={setShowVerifyDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>确认核销</DialogTitle>
            <DialogDescription>
              请确认以下信息无误后进行核销
            </DialogDescription>
          </DialogHeader>

          {selectedItem && (
            <div className="space-y-4 py-4">
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={selectedItem.customer.avatar} alt={selectedItem.customer.name} />
                  <AvatarFallback>{selectedItem.customer.name.slice(0, 1)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium text-lg">{selectedItem.customer.name}</div>
                  <div className="text-sm text-muted-foreground">{selectedItem.customer.phone}</div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">商品名称</span>
                  <span className="font-medium">{selectedItem.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">核销码</span>
                  <span className="font-medium">{selectedItem.code}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">订单来源</span>
                  <span>{getSourceIcon(selectedItem.source)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">下单时间</span>
                  <span>{selectedItem.orderTime}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">到期时间</span>
                  <span>{selectedItem.expireTime}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">金额</span>
                  <span className="font-medium">¥{selectedItem.price}</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="verifyNote">备注 (可选)</Label>
                <Textarea id="verifyNote" placeholder="添加核销备注..." />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowVerifyDialog(false)}>取消</Button>
            <Button onClick={confirmVerification}>确认核销</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 收款对话框 */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>收银台</DialogTitle>
            <DialogDescription>
              选择商品和支付方式
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">选择商品</h3>
                <div className="flex gap-2">
                  <div className="relative">
                    <Input
                      placeholder="搜索商品..."
                      value={searchQuery}
                      onChange={handleSearchChange}
                      className="w-[200px]"
                    />
                    {searchQuery && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full z-10"
                        onClick={() => {
                          setSearchQuery("");
                          setShowSearchResults(false);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowAddProductDialog(true);
                      setSelectedProduct(mallProducts[0]);
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    添加商品
                  </Button>
                </div>
              </div>

              {/* 快捷商品选择 */}
              <div className="flex gap-2 overflow-x-auto pb-2">
                {quickProducts.map((product) => (
                  <Button
                    key={product.id}
                    variant="outline"
                    className="h-auto py-2 px-3 flex flex-col items-center justify-center min-w-[80px]"
                    onClick={() => addToCart(product)}
                  >
                    {product.icon}
                    <span className="text-xs">{product.name}</span>
                  </Button>
                ))}
                <Button
                  variant="outline"
                  className="h-auto py-2 px-3 flex flex-col items-center justify-center min-w-[80px]"
                  onClick={() => alert("更多商品选择功能即将上线")}
                >
                  <Plus className="h-5 w-5 mb-1" />
                  <span className="text-xs">更多</span>
                </Button>
              </div>

              <div className="border rounded-md">
                <div className="p-4 border-b">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">购物车</div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 text-red-500 hover:text-red-700"
                      onClick={clearCart}
                    >
                      <X className="h-4 w-4 mr-1" />
                      清空
                    </Button>
                  </div>
                </div>

                <div className="p-4 space-y-3">
                  {cartItems.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      购物车为空，请添加商品
                    </div>
                  ) : (
                    cartItems.map((item) => (
                      <div key={item.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                            {item.icon}
                          </div>
                          <div>
                            <div className="font-medium">{item.name}</div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-muted-foreground">¥{item.price.toFixed(2)}</span>
                              {item.discount && (
                                <Badge
                                  variant="outline"
                                  className={`text-xs px-1.5 py-0 h-5 ${
                                    item.discountType === 'member'
                                      ? 'bg-blue-50 text-blue-600 hover:bg-blue-50'
                                      : 'bg-red-50 text-red-600 hover:bg-red-50'
                                  }`}
                                >
                                  {item.discount}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center border rounded-md">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 rounded-none"
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            <span className="w-8 text-center">{item.quantity}</span>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 rounded-none"
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => removeFromCart(item.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                <div className="p-4 border-t bg-muted/30">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>小计</span>
                      <span>¥{calculateSubtotal().toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>优惠</span>
                      <span className="text-red-500">-¥{calculateDiscount().toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between font-medium">
                      <span>总计</span>
                      <span>¥{calculateTotal().toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">客户信息</h3>
                  <div className="flex gap-2">
                    <div className="relative">
                      <Input
                        placeholder="搜索会员..."
                        value={memberSearchQuery}
                        onChange={handleMemberSearchChange}
                        className="w-[200px]"
                      />
                      {memberSearchQuery && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-full z-10"
                          onClick={() => {
                            setMemberSearchQuery("");
                            setShowMemberSearchResults(false);
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => alert("扫码功能即将上线")}
                    >
                      <QrCode className="h-4 w-4 mr-2" />
                      扫码
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md p-4">
                  <div className="flex items-center gap-3">
                    {selectedCustomer ? (
                      <>
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={selectedCustomer.avatar} alt={selectedCustomer.name} />
                          <AvatarFallback>{selectedCustomer.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="font-medium">{selectedCustomer.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {selectedCustomer.phone}
                            {selectedCustomer.membershipType && ` | ${selectedCustomer.membershipType}`}
                          </div>
                          {selectedCustomer.points && (
                            <div className="text-xs text-muted-foreground mt-1">
                              积分: {selectedCustomer.points} | 余额: ¥{selectedCustomer.balance?.toFixed(2) || '0.00'}
                            </div>
                          )}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedCustomer(null)}
                        >
                          <X className="h-4 w-4 mr-2" />
                          清除
                        </Button>
                      </>
                    ) : (
                      <>
                        <Avatar className="h-10 w-10">
                          <AvatarFallback>访</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="font-medium">访客</div>
                          <div className="text-sm text-muted-foreground">未登记会员信息</div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => alert("扫码功能即将上线")}
                          >
                            <QrCode className="h-4 w-4 mr-2" />
                            扫码
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const visitor = createVisitor("访客", "未登记");
                              setSelectedCustomer(visitor);
                            }}
                          >
                            <User className="h-4 w-4 mr-2" />
                            添加会员
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* 优惠券和积分 */}
                <div className="border rounded-md p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">优惠</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 text-primary"
                      onClick={() => alert("更多优惠券选择功能即将上线")}
                    >
                      选择优惠券
                    </Button>
                  </div>

                  <div className="flex flex-col gap-2">
                    {availableCoupons.map((coupon) => (
                      <div
                        key={coupon.id}
                        className={`border border-dashed rounded-md p-3 flex items-center justify-between ${
                          selectedCoupon?.id === coupon.id
                            ? 'border-primary bg-primary/10'
                            : 'border-primary/50 bg-primary/5'
                        }`}
                      >
                        <div>
                          <div className="text-sm font-medium text-primary">{coupon.name}</div>
                          <div className="text-xs text-muted-foreground">有效期至：{coupon.expiryDate}</div>
                        </div>
                        {selectedCoupon?.id === coupon.id ? (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 text-red-500"
                            onClick={() => removeCoupon()}
                          >
                            取消
                          </Button>
                        ) : (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 text-primary"
                            onClick={() => applyCoupon(coupon)}
                          >
                            使用
                          </Button>
                        )}
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">可用积分</span>
                    <div className="flex items-center gap-2">
                      <span>1280 积分</span>
                      {usePoints ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 text-red-500"
                          onClick={() => setUsePoints(false)}
                        >
                          取消使用
                        </Button>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 text-primary"
                          onClick={() => applyPoints(1280)}
                        >
                          使用积分
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Input placeholder="输入优惠码" className="h-9" />
                    <Button
                      size="sm"
                      className="ml-2 h-9"
                      onClick={() => alert("优惠码应用功能即将上线")}
                    >
                      应用
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium">支付方式</h3>

              <div className="grid grid-cols-2 gap-3">
                {/* 刷卡支付 */}
                <div
                  className={`border rounded-md p-4 flex items-center gap-3 cursor-pointer hover:bg-muted/50 relative ${
                    selectedPaymentMethod === 'card' ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                  onClick={() => setSelectedPaymentMethod('card')}
                >
                  <div className="w-10 h-10 bg-blue-100 rounded-md flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                      <path d="M2 9V5c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v4" />
                      <rect width="20" height="12" x="2" y="9" rx="2" />
                      <circle cx="12" cy="15" r="2" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium">刷卡支付</div>
                    <div className="text-xs text-muted-foreground">银行卡、信用卡</div>
                  </div>
                  {selectedPaymentMethod === 'card' && (
                    <div className="absolute top-2 right-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    </div>
                  )}
                </div>

                {/* 微信支付 */}
                <div
                  className={`border rounded-md p-4 flex items-center gap-3 cursor-pointer hover:bg-muted/50 relative ${
                    selectedPaymentMethod === 'wechat' ? 'bg-green-50 border-green-200' : ''
                  }`}
                  onClick={() => setSelectedPaymentMethod('wechat')}
                >
                  <div className="w-10 h-10 bg-green-100 rounded-md flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-green-600">
                      <path d="M9 12h6" />
                      <path d="M12 9v6" />
                      <path d="M4 8V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-2" />
                      <path d="M2 14h6v6H2z" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium">微信支付</div>
                    <div className="text-xs text-muted-foreground">扫码支付</div>
                  </div>
                  {selectedPaymentMethod === 'wechat' && (
                    <div className="absolute top-2 right-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    </div>
                  )}
                </div>

                {/* 支付宝 */}
                <div
                  className={`border rounded-md p-4 flex items-center gap-3 cursor-pointer hover:bg-muted/50 relative ${
                    selectedPaymentMethod === 'alipay' ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                  onClick={() => setSelectedPaymentMethod('alipay')}
                >
                  <div className="w-10 h-10 bg-blue-100 rounded-md flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                      <path d="M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium">支付宝</div>
                    <div className="text-xs text-muted-foreground">扫码支付</div>
                  </div>
                  {selectedPaymentMethod === 'alipay' && (
                    <div className="absolute top-2 right-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    </div>
                  )}
                </div>

                {/* 现金支付 */}
                <div
                  className={`border rounded-md p-4 flex items-center gap-3 cursor-pointer hover:bg-muted/50 relative ${
                    selectedPaymentMethod === 'cash' ? 'bg-yellow-50 border-yellow-200' : ''
                  }`}
                  onClick={() => setSelectedPaymentMethod('cash')}
                >
                  <div className="w-10 h-10 bg-yellow-100 rounded-md flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div>
                    <div className="font-medium">现金支付</div>
                    <div className="text-xs text-muted-foreground">收取现金</div>
                  </div>
                  {selectedPaymentMethod === 'cash' && (
                    <div className="absolute top-2 right-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    </div>
                  )}
                </div>

                {/* 会员余额 */}
                <div
                  className={`border rounded-md p-4 flex items-center gap-3 cursor-pointer hover:bg-muted/50 relative ${
                    selectedPaymentMethod === 'balance' ? 'bg-purple-50 border-purple-200' : ''
                  }`}
                  onClick={() => setSelectedPaymentMethod('balance')}
                >
                  <div className="w-10 h-10 bg-purple-100 rounded-md flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-purple-600">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium">会员余额</div>
                    <div className="text-xs text-muted-foreground">账户余额支付</div>
                  </div>
                  {selectedPaymentMethod === 'balance' && (
                    <div className="absolute top-2 right-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    </div>
                  )}
                </div>

                {/* 其他支付 */}
                <div
                  className={`border rounded-md p-4 flex items-center gap-3 cursor-pointer hover:bg-muted/50 relative ${
                    selectedPaymentMethod === 'other' ? 'bg-orange-50 border-orange-200' : ''
                  }`}
                  onClick={() => setSelectedPaymentMethod('other')}
                >
                  <div className="w-10 h-10 bg-orange-100 rounded-md flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-orange-600">
                      <rect width="20" height="14" x="2" y="5" rx="2" />
                      <line x1="2" x2="22" y1="10" y2="10" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium">其他支付</div>
                    <div className="text-xs text-muted-foreground">更多支付方式</div>
                  </div>
                  {selectedPaymentMethod === 'other' && (
                    <div className="absolute top-2 right-2">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    </div>
                  )}
                </div>
              </div>

              {/* 支付金额输入 */}
              <div className="border rounded-md p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">支付金额</h3>
                  <div className="text-sm text-muted-foreground">应付: ¥{calculateTotal().toFixed(2)}</div>
                </div>

                <div className="flex items-center">
                  <div className="relative flex-1">
                    <span className="absolute left-3 top-2.5">¥</span>
                    <Input
                      className="pl-7"
                      value={paymentAmount}
                      onChange={(e) => setPaymentAmount(e.target.value)}
                    />
                  </div>
                  <Button
                    size="sm"
                    className="ml-2"
                    onClick={calculateChange}
                  >
                    <Calculator className="h-4 w-4 mr-2" />
                    找零计算
                  </Button>
                </div>

                {changeAmount !== null && (
                  <div className="flex justify-between text-sm font-medium">
                    <span>找零金额</span>
                    <span className="text-primary">¥{changeAmount.toFixed(2)}</span>
                  </div>
                )}
              </div>

              <div className="space-y-3 pt-4">
                <h3 className="font-medium">备注</h3>
                <Textarea placeholder="添加订单备注..." />
              </div>

              <div className="pt-4 mt-4 border-t">
                <div className="mb-4 space-y-2">
                  <div className="flex justify-between text-sm font-medium">
                    <span>已选商品</span>
                    <span>{cartItems.length}件</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold">
                    <span>应付金额</span>
                    <span className="text-primary">¥{calculateTotal().toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>优惠金额</span>
                    <span>-¥{calculateDiscount().toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>原价总计</span>
                    <span className="line-through">¥{calculateOriginalTotal().toFixed(2)}</span>
                  </div>
                </div>

                <Button
                  className="w-full h-12 text-lg font-bold bg-primary hover:bg-primary/90"
                  onClick={confirmPayment}
                  disabled={cartItems.length === 0}
                >
                  确认支付 ¥{calculateTotal().toFixed(2)}
                </Button>

                <div className="flex justify-between mt-3">
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => alert("打印小票功能即将上线")}
                    >
                      <Printer className="h-4 w-4 mr-2" />
                      打印小票
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => alert("查看订单功能即将上线")}
                    >
                      <Receipt className="h-4 w-4 mr-2" />
                      查看订单
                    </Button>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                    onClick={() => {
                      setShowPaymentDialog(false);
                      clearCart();
                    }}
                  >
                    <X className="h-4 w-4 mr-2" />
                    取消交易
                  </Button>
                </div>

                <div className="mt-4 text-xs text-center text-muted-foreground">
                  收银员：王小明 | 终端号：POS-001 | 交易流水号：TX20230615001
                </div>

                <div className="mt-3 text-xs text-center">
                  <Button
                    variant="link"
                    className="h-auto p-0 text-xs text-muted-foreground"
                    onClick={() => alert("查看收银记录功能即将上线")}
                  >
                    查看收银记录
                  </Button>
                  <span className="mx-2">|</span>
                  <Button
                    variant="link"
                    className="h-auto p-0 text-xs text-muted-foreground"
                    onClick={() => alert("交接班功能即将上线")}
                  >
                    交接班
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 代约课对话框 */}
      <Dialog open={showBookingDialog} onOpenChange={setShowBookingDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>代约课</DialogTitle>
            <DialogDescription>
              {selectedCustomer ? `为 ${selectedCustomer.name} 预约课程` : "选择会员和课程进行预约"}
            </DialogDescription>
          </DialogHeader>

          {selectedCustomer && (
            <div className="space-y-6 py-4">
              <div className="flex items-center gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={selectedCustomer.avatar} alt={selectedCustomer.name} />
                  <AvatarFallback>{selectedCustomer.name.slice(0, 1)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="font-medium text-lg">{selectedCustomer.name}</div>
                  <div className="text-sm text-muted-foreground">{selectedCustomer.phone} | {selectedCustomer.membershipType}</div>
                </div>
                <div className="text-right">
                  <div className="font-medium">剩余课时</div>
                  <div className="text-2xl font-bold text-primary">{selectedCustomer.remainingClasses}</div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">选择课程</h3>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="课程类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部课程</SelectItem>
                      <SelectItem value="group">团体课</SelectItem>
                      <SelectItem value="private">私教课</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  {classes.map((classItem) => (
                    <div key={classItem.id} className="border rounded-md p-4 flex items-center justify-between hover:bg-muted/30 cursor-pointer">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-md flex items-center justify-center">
                          <Users className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <div className="font-medium">{classItem.name}</div>
                          <div className="text-sm text-muted-foreground">{classItem.time} | {classItem.teacher}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">剩余 {classItem.remainingSpots} 位</Badge>
                        <Button size="sm">
                          预约
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
