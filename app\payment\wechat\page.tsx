"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { QrCode, Search, AlertCircle, RefreshCw } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"

export default function WechatPaymentPage() {
  const [amount, setAmount] = useState("")
  const [description, setDescription] = useState("")
  const [qrCodeGenerated, setQrCodeGenerated] = useState(false)
  const [xmlData, setXmlData] = useState("")

  const [recentPayments] = useState([
    {
      id: "WX20250328123456",
      amount: "¥199.00",
      description: "基础瑜伽月卡",
      time: "2025-03-28 14:30:25",
      status: "success",
      type: "扫码支付",
    },
    {
      id: "WX20250328234567",
      amount: "¥299.00",
      description: "高级瑜伽季卡",
      time: "2025-03-28 11:15:42",
      status: "success",
      type: "小程序支付",
    },
    {
      id: "WX20250328345678",
      amount: "¥99.00",
      description: "单次体验课",
      time: "2025-03-28 09:45:18",
      status: "pending",
      type: "扫码支付",
    },
  ])

  const handleGenerateQRCode = () => {
    if (!amount || Number.parseFloat(amount) <= 0) {
      alert("请输入有效的金额")
      return
    }

    setQrCodeGenerated(true)
  }

  const handleProcessXML = () => {
    if (!xmlData.trim()) {
      alert("请输入XML数据")
      return
    }

    alert("XML数据处理成功")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">微信支付</h1>
      </div>

      <Tabs defaultValue="qrcode" className="space-y-4">
        <TabsList>
          <TabsTrigger value="qrcode">扫码支付</TabsTrigger>
          <TabsTrigger value="miniprogram">小程序支付</TabsTrigger>
          <TabsTrigger value="notification">支付结果通知</TabsTrigger>
          <TabsTrigger value="xml">XML数据处理</TabsTrigger>
          <TabsTrigger value="query">支付状态查询</TabsTrigger>
          <TabsTrigger value="refund">退款处理</TabsTrigger>
        </TabsList>

        <TabsContent value="qrcode" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>微信扫码支付</CardTitle>
              <CardDescription>生成微信支付二维码供用户扫码支付</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="amount">支付金额（元）</Label>
                    <Input
                      id="amount"
                      placeholder="请输入金额"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">商品描述</Label>
                    <Input
                      id="description"
                      placeholder="请输入商品描述"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                    />
                  </div>

                  <Button onClick={handleGenerateQRCode}>生成支付二维码</Button>
                </div>

                <div className="flex flex-col items-center justify-center">
                  {qrCodeGenerated ? (
                    <div className="text-center">
                      <div className="border-4 border-[#22AB39] p-4 inline-block mb-4">
                        <QrCode className="h-48 w-48 text-[#22AB39]" />
                      </div>
                      <div className="text-lg font-medium">¥{amount}</div>
                      <div className="text-sm text-muted-foreground">{description || "微信扫码支付"}</div>
                      <Button variant="outline" className="mt-4" onClick={() => setQrCodeGenerated(false)}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        重新生成
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <QrCode className="h-48 w-48 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">请输入金额并点击生成支付二维码</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>最近支付记录</CardTitle>
              <CardDescription>显示最近的微信支付记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>交易单号</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>商品描述</TableHead>
                    <TableHead>支付方式</TableHead>
                    <TableHead>支付时间</TableHead>
                    <TableHead>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentPayments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">{payment.id}</TableCell>
                      <TableCell>{payment.amount}</TableCell>
                      <TableCell>{payment.description}</TableCell>
                      <TableCell>{payment.type}</TableCell>
                      <TableCell>{payment.time}</TableCell>
                      <TableCell>
                        <Badge variant={payment.status === "success" ? "default" : "outline"}>
                          {payment.status === "success" ? "支付成功" : "待支付"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="miniprogram" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>小程序支付</CardTitle>
              <CardDescription>生成小程序支付参数</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="mp-amount">支付金额（元）</Label>
                    <Input id="mp-amount" placeholder="请输入金额" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="mp-description">商品描述</Label>
                    <Input id="mp-description" placeholder="请输入商品描述" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="mp-openid">用户OpenID</Label>
                    <Input id="mp-openid" placeholder="请输入用户OpenID" />
                  </div>

                  <Button>生成支付参数</Button>
                </div>

                <div className="border rounded-md p-4">
                  <h3 className="font-medium mb-2">支付参数</h3>
                  <pre className="bg-muted p-4 rounded-md text-xs overflow-auto">
                    {`{
  "appId": "wx123456789abcdef",
  "timeStamp": "1616237138",
  "nonceStr": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
  "package": "prepay_id=wx201410272009395522657a690389285100",
  "signType": "RSA",
  "paySign": "oR9d8PuhnIc+YZ8cBHFCwfgpaK9gd7vaRvkYD7rthRAZ..."
}`}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notification" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付结果通知</CardTitle>
              <CardDescription>接收微信支付结果通知</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="notify-url">通知URL</Label>
                <Input id="notify-url" value="https://youryogastudio.com/api/wechat/payment/notify" readOnly />
                <p className="text-sm text-muted-foreground">请在微信商户平台设置此通知URL</p>
              </div>

              <div className="space-y-2">
                <Label>最近通知记录</Label>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>通知时间</TableHead>
                      <TableHead>交易单号</TableHead>
                      <TableHead>金额</TableHead>
                      <TableHead>状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>2025-03-28 14:30:28</TableCell>
                      <TableCell>WX20250328123456</TableCell>
                      <TableCell>¥199.00</TableCell>
                      <TableCell>
                        <Badge>支付成功</Badge>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-28 11:15:45</TableCell>
                      <TableCell>WX20250328234567</TableCell>
                      <TableCell>¥299.00</TableCell>
                      <TableCell>
                        <Badge>支付成功</Badge>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="xml" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>XML数据处理</CardTitle>
              <CardDescription>处理微信支付XML格式数据</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="xml-data">XML数据</Label>
                <Textarea
                  id="xml-data"
                  placeholder="请输入XML数据"
                  className="min-h-[200px] font-mono"
                  value={xmlData}
                  onChange={(e) => setXmlData(e.target.value)}
                />
              </div>

              <Button onClick={handleProcessXML}>处理XML数据</Button>

              <div className="space-y-2">
                <Label>处理结果</Label>
                <div className="border rounded-md p-4 bg-muted">
                  <pre className="text-xs overflow-auto">
                    {`{
  "return_code": "SUCCESS",
  "return_msg": "OK",
  "appid": "wx123456789abcdef",
  "mch_id": "**********",
  "nonce_str": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
  "sign": "C380BEC2BFD727A4B6845133519F3AD6",
  "result_code": "SUCCESS",
  "openid": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o",
  "trade_type": "JSAPI",
  "bank_type": "CMC",
  "total_fee": "1",
  "fee_type": "CNY",
  "transaction_id": "1008450740201411110005820873",
  "out_trade_no": "**********",
  "time_end": "**************"
}`}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="query" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付状态查询</CardTitle>
              <CardDescription>查询微信支付交易状态</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input placeholder="输入交易单号或商户订单号" />
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <div className="flex items-center justify-center p-8 border rounded-lg">
                <div className="text-center">
                  <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="font-medium text-lg mb-2">暂无查询结果</h3>
                  <p className="text-sm text-muted-foreground">请输入交易单号并点击查询按钮</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="refund" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>退款处理</CardTitle>
              <CardDescription>处理微信支付退款</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="transaction-id">交易单号</Label>
                    <Input id="transaction-id" placeholder="请输入微信支付交易单号" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="refund-amount">退款金额（元）</Label>
                    <Input id="refund-amount" placeholder="请输入退款金额" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="refund-reason">退款原因</Label>
                    <Input id="refund-reason" placeholder="请输入退款原因" />
                  </div>

                  <Button>申请退款</Button>
                </div>

                <div>
                  <h3 className="font-medium mb-2">退款记录</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>退款单号</TableHead>
                        <TableHead>金额</TableHead>
                        <TableHead>状态</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>RF20250327001</TableCell>
                        <TableCell>¥99.00</TableCell>
                        <TableCell>
                          <Badge>退款成功</Badge>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>RF20250326002</TableCell>
                        <TableCell>¥199.00</TableCell>
                        <TableCell>
                          <Badge variant="outline">处理中</Badge>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

