"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, MapPin, User } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"

interface BookingRecordsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
  }
}

// 模拟预约记录数据
const bookingRecords = [
  {
    id: "B001",
    courseName: "高级瑜伽课",
    dateTime: "2023-03-25 10:00-11:30",
    status: "completed", // completed, upcoming, cancelled
    instructor: "王教练",
    venue: "瑜伽室A",
    bookingTime: "2023-03-20 15:30",
    attendanceStatus: "attended", // attended, absent, late
  },
  {
    id: "B002",
    courseName: "普拉提入门",
    dateTime: "2023-03-20 15:00-16:00",
    status: "completed",
    instructor: "李教练",
    venue: "多功能厅",
    bookingTime: "2023-03-15 09:45",
    attendanceStatus: "attended",
  },
  {
    id: "B003",
    courseName: "冥想课",
    dateTime: "2023-03-15 18:30-19:30",
    status: "completed",
    instructor: "张教练",
    venue: "冥想室",
    bookingTime: "2023-03-10 14:20",
    attendanceStatus: "late",
  },
  {
    id: "B004",
    courseName: "瑜伽基础班",
    dateTime: "2023-04-05 09:00-10:30",
    status: "upcoming",
    instructor: "王教练",
    venue: "瑜伽室B",
    bookingTime: "2023-03-28 16:15",
    attendanceStatus: "",
  },
  {
    id: "B005",
    courseName: "普拉提进阶",
    dateTime: "2023-03-10 14:00-15:00",
    status: "cancelled",
    instructor: "李教练",
    venue: "多功能厅",
    bookingTime: "2023-03-05 11:30",
    attendanceStatus: "",
    cancelReason: "教练临时有事",
  },
]

export function BookingRecordsDialog({ open, onOpenChange, member }: BookingRecordsDialogProps) {
  const [activeTab, setActiveTab] = useState<"all" | "upcoming" | "completed" | "cancelled">("all")
  const [searchQuery, setSearchQuery] = useState("")
  
  // 根据标签和搜索过滤预约记录
  const filteredRecords = bookingRecords.filter(record => {
    // 根据标签过滤
    if (activeTab === "upcoming" && record.status !== "upcoming") return false
    if (activeTab === "completed" && record.status !== "completed") return false
    if (activeTab === "cancelled" && record.status !== "cancelled") return false
    
    // 根据搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        record.courseName.toLowerCase().includes(query) ||
        record.instructor.toLowerCase().includes(query) ||
        record.venue.toLowerCase().includes(query)
      )
    }
    
    return true
  })
  
  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge variant="outline" className="bg-green-50 text-green-700">已完成</Badge>
      case "upcoming":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700">即将到来</Badge>
      case "cancelled":
        return <Badge variant="outline" className="bg-red-50 text-red-700">已取消</Badge>
      default:
        return null
    }
  }
  
  // 获取出勤状态标签
  const getAttendanceBadge = (status: string) => {
    switch (status) {
      case "attended":
        return <Badge variant="outline" className="bg-green-50 text-green-700">已出席</Badge>
      case "absent":
        return <Badge variant="outline" className="bg-red-50 text-red-700">缺席</Badge>
      case "late":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700">迟到</Badge>
      default:
        return null
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>预约记录</DialogTitle>
          <DialogDescription>
            会员 {member.name} 的课程预约记录
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            <Input 
              placeholder="搜索课程、教练或场地..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-[300px]"
            />
            
            <Select value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部预约</SelectItem>
                <SelectItem value="upcoming">即将到来</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {filteredRecords.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchQuery ? (
                <p>没有找到匹配的预约记录</p>
              ) : (
                <p>没有{activeTab === "all" ? "" : activeTab === "upcoming" ? "即将到来的" : activeTab === "completed" ? "已完成的" : "已取消的"}预约记录</p>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredRecords.map((record) => (
                <Card key={record.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{record.courseName}</CardTitle>
                        <CardDescription className="flex items-center mt-1">
                          <Calendar className="h-4 w-4 mr-1" />
                          {record.dateTime}
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        {getStatusBadge(record.status)}
                        {record.attendanceStatus && getAttendanceBadge(record.attendanceStatus)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-muted-foreground mr-1">教练:</span>
                        {record.instructor}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-muted-foreground mr-1">场地:</span>
                        {record.venue}
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-muted-foreground mr-1">预约时间:</span>
                        {record.bookingTime}
                      </div>
                      {record.cancelReason && (
                        <div className="sm:col-span-2 text-red-500">
                          <span className="font-medium">取消原因:</span> {record.cancelReason}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
