"use client"

import { useState } from "react"
import { Calendar, CreditCard, Layers, User, Home } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// 定义功能图标类型
interface FeatureIcon {
  id: number
  name: string
  icon: string
  bgColor: string
  iconColor: string
}

export default function DraggableSimplePage() {
  // 功能图标状态
  const [features, setFeatures] = useState<FeatureIcon[]>([
    { id: 1, name: "预约", icon: "calendar", bgColor: "bg-purple-100", iconColor: "text-purple-600" },
    { id: 2, name: "会员卡", icon: "card", bgColor: "bg-red-100", iconColor: "text-red-500" },
    { id: 3, name: "课程", icon: "course", bgColor: "bg-indigo-100", iconColor: "text-indigo-600" },
    { id: 4, name: "我的", icon: "user", bgColor: "bg-pink-100", iconColor: "text-pink-500" },
  ])
  
  // 标题和副标题状态
  const [title, setTitle] = useState("静心瑜伽")
  const [subtitle, setSubtitle] = useState("欢迎来到静心瑜伽，开启您的瑜伽之旅")
  
  // 移动功能图标
  const moveFeature = (dragIndex: number, hoverIndex: number) => {
    const newFeatures = [...features]
    const draggedFeature = newFeatures[dragIndex]
    newFeatures.splice(dragIndex, 1)
    newFeatures.splice(hoverIndex, 0, draggedFeature)
    setFeatures(newFeatures)
  }
  
  // 渲染图标
  const renderIcon = (iconName: string) => {
    switch (iconName) {
      case "calendar":
        return <Calendar className="h-6 w-6" />
      case "card":
        return <CreditCard className="h-6 w-6" />
      case "course":
        return <Layers className="h-6 w-6" />
      case "user":
        return <User className="h-6 w-6" />
      default:
        return null
    }
  }
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row gap-6">
        {/* 左侧预览 */}
        <div className="md:w-1/2">
          <div className="relative w-[375px] h-[700px] mx-auto bg-white rounded-[40px] shadow-xl overflow-hidden border-8 border-black">
            <div className="absolute top-0 left-0 right-0 h-6 bg-black flex justify-center items-end pb-1">
              <div className="w-20 h-4 bg-black rounded-b-xl"></div>
            </div>
            
            <div className="h-full pt-6 overflow-hidden">
              <div className="h-[calc(100%-6px)] overflow-y-auto pb-16">
                {/* 状态栏 - 模拟手机状态栏 */}
                <div className="bg-white px-4 py-2 flex justify-between items-center text-xs text-gray-700">
                  <span>9:41</span>
                  <div className="flex items-center gap-1">
                    <div className="w-4 h-2 bg-black rounded-sm"></div>
                    <div className="w-2 h-2 bg-black rounded-full"></div>
                    <div className="w-2 h-2 bg-black rounded-full"></div>
                  </div>
                </div>
                
                {/* 头部 */}
                <div className="bg-gray-100 p-4 text-center">
                  <h1 className="text-xl font-bold">{title}</h1>
                  <p className="text-sm text-gray-600 mt-1">
                    {subtitle}
                  </p>
                </div>
                
                {/* 功能图标 - 可拖拽 */}
                <div className="grid grid-cols-4 gap-4 p-4 relative">
                  {features.map((feature, index) => (
                    <div 
                      key={feature.id} 
                      className="flex flex-col items-center cursor-move"
                      draggable
                      onDragStart={(e) => {
                        e.dataTransfer.setData("text/plain", index.toString())
                      }}
                      onDragOver={(e) => e.preventDefault()}
                      onDrop={(e) => {
                        e.preventDefault()
                        const dragIndex = parseInt(e.dataTransfer.getData("text/plain"))
                        moveFeature(dragIndex, index)
                      }}
                    >
                      <div className={`w-14 h-14 rounded-lg ${feature.bgColor} flex items-center justify-center mb-1`}>
                        <div className={feature.iconColor}>
                          {renderIcon(feature.icon)}
                        </div>
                      </div>
                      <span className="text-xs">{feature.name}</span>
                    </div>
                  ))}
                  <div className="absolute -top-3 right-2 bg-gray-100 text-xs px-2 py-1 rounded text-gray-500">
                    拖动调整顺序
                  </div>
                </div>
                
                {/* 底部导航 */}
                <div className="mt-auto sticky bottom-0 left-0 right-0 bg-white border-t flex justify-around py-2">
                  <div className="flex flex-col items-center text-gray-400">
                    <Home className="h-6 w-6" />
                    <span className="text-xs mt-1">首页</span>
                  </div>
                  
                  <div className="flex flex-col items-center text-gray-400">
                    <Calendar className="h-6 w-6" />
                    <span className="text-xs mt-1">预约</span>
                  </div>
                  
                  <div className="flex flex-col items-center text-gray-400">
                    <Layers className="h-6 w-6" />
                    <span className="text-xs mt-1">课程</span>
                  </div>
                  
                  <div className="flex flex-col items-center text-gray-400">
                    <User className="h-6 w-6" />
                    <span className="text-xs mt-1">我的</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-1/3 h-1 bg-black rounded-full"></div>
          </div>
        </div>
        
        {/* 右侧设置 */}
        <div className="md:w-1/2">
          <Card className="p-6">
            <h2 className="text-xl font-bold mb-4">小程序设置</h2>
            
            <Tabs defaultValue="content">
              <TabsList className="mb-4">
                <TabsTrigger value="content">内容设置</TabsTrigger>
                <TabsTrigger value="features">功能设置</TabsTrigger>
                <TabsTrigger value="style">样式设置</TabsTrigger>
              </TabsList>
              
              <TabsContent value="content">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="title">标题</Label>
                    <Input 
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="subtitle">副标题</Label>
                    <Input 
                      id="subtitle"
                      value={subtitle}
                      onChange={(e) => setSubtitle(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="features">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">功能排序</h3>
                  <p className="text-sm text-gray-500">拖动左侧预览中的功能图标可以调整顺序</p>
                  
                  <div className="mt-4">
                    <Label>当前功能顺序</Label>
                    <div className="mt-2 space-y-2">
                      {features.map((feature, index) => (
                        <div key={feature.id} className="flex items-center p-2 bg-gray-50 rounded-md">
                          <span className="w-6 h-6 flex items-center justify-center bg-gray-200 rounded-full mr-2">
                            {index + 1}
                          </span>
                          <div 
                            className={`w-6 h-6 rounded-md mr-2 ${feature.bgColor} ${feature.iconColor} flex items-center justify-center`}
                          >
                            {renderIcon(feature.icon)}
                          </div>
                          <span>{feature.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="style">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">颜色设置</h3>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {features.map((feature) => (
                      <div key={feature.id}>
                        <Label>{feature.name}背景色</Label>
                        <div className="flex mt-1">
                          <div 
                            className={`w-10 h-10 rounded-md mr-2 ${feature.bgColor}`}
                          ></div>
                          <select 
                            className="flex-1 border rounded-md px-2"
                            value={feature.bgColor}
                            onChange={(e) => {
                              const newFeatures = [...features]
                              const index = newFeatures.findIndex(f => f.id === feature.id)
                              newFeatures[index] = { ...feature, bgColor: e.target.value }
                              setFeatures(newFeatures)
                            }}
                          >
                            <option value="bg-purple-100">紫色</option>
                            <option value="bg-red-100">红色</option>
                            <option value="bg-indigo-100">靛蓝色</option>
                            <option value="bg-pink-100">粉色</option>
                            <option value="bg-blue-100">蓝色</option>
                            <option value="bg-green-100">绿色</option>
                            <option value="bg-yellow-100">黄色</option>
                            <option value="bg-orange-100">橙色</option>
                          </select>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </Card>
        </div>
      </div>
    </div>
  )
}
