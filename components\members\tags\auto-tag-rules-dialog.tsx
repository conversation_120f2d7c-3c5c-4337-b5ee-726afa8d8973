"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Plus, Trash2, Tag, Settings, History, AlertCircle } from "lucide-react"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

type AutoTagRulesDialogProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
}

// Mock data for auto tag rules
const autoTagRules = [
  {
    id: 1,
    name: "新会员规则",
    tag: "新会员",
    tagColor: "#4285F4",
    enabled: true,
    conditions: [
      {
        id: 1,
        field: "注册时间",
        operator: "小于",
        value: "30",
        unit: "天",
      },
    ],
    lastRun: "2023-11-20 08:00",
    matchedMembers: 45,
  },
  {
    id: 2,
    name: "VIP会员规则",
    tag: "VIP",
    tagColor: "#FBBC05",
    enabled: true,
    conditions: [
      {
        id: 1,
        field: "消费金额",
        operator: "大于",
        value: "5000",
        unit: "元",
      },
    ],
    lastRun: "2023-11-20 08:00",
    matchedMembers: 28,
  },
  {
    id: 3,
    name: "潜在流失规则",
    tag: "潜在流失",
    tagColor: "#EA4335",
    enabled: true,
    conditions: [
      {
        id: 1,
        field: "最后访问",
        operator: "大于",
        value: "30",
        unit: "天",
      },
    ],
    lastRun: "2023-11-20 08:00",
    matchedMembers: 32,
  },
  {
    id: 4,
    name: "瑜伽爱好者规则",
    tag: "瑜伽爱好者",
    tagColor: "#34A853",
    enabled: true,
    conditions: [
      {
        id: 1,
        field: "课程类型",
        operator: "包含",
        value: "瑜伽",
        unit: "",
      },
      {
        id: 2,
        field: "课程次数",
        operator: "大于",
        value: "20",
        unit: "次",
      },
    ],
    lastRun: "2023-11-20 08:00",
    matchedMembers: 65,
  },
]

// Mock data for rule execution history
const ruleHistory = [
  {
    id: 1,
    date: "2023-11-20 08:00",
    rulesRun: 8,
    membersProcessed: 150,
    tagsAdded: 12,
    tagsRemoved: 5,
    duration: "45秒",
  },
  {
    id: 2,
    date: "2023-11-19 08:00",
    rulesRun: 8,
    membersProcessed: 148,
    tagsAdded: 8,
    tagsRemoved: 3,
    duration: "42秒",
  },
  {
    id: 3,
    date: "2023-11-18 08:00",
    rulesRun: 8,
    membersProcessed: 145,
    tagsAdded: 15,
    tagsRemoved: 7,
    duration: "40秒",
  },
  {
    id: 4,
    date: "2023-11-17 08:00",
    rulesRun: 7,
    membersProcessed: 142,
    tagsAdded: 10,
    tagsRemoved: 4,
    duration: "38秒",
  },
]

export function AutoTagRulesDialog({ open, onOpenChange }: AutoTagRulesDialogProps) {
  const [activeTab, setActiveTab] = useState("rules")

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>自动标签规则管理</DialogTitle>
          <DialogDescription>
            管理自动为会员添加标签的规则，系统将根据这些规则自动为符合条件的会员添加或移除标签
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="rules" onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="rules">
              <Tag className="mr-2 h-4 w-4" />
              标签规则
            </TabsTrigger>
            <TabsTrigger value="history">
              <History className="mr-2 h-4 w-4" />
              执行历史
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="mr-2 h-4 w-4" />
              规则设置
            </TabsTrigger>
          </TabsList>

          <TabsContent value="rules" className="space-y-4">
            <div className="flex justify-between items-center">
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                添加规则
              </Button>
              <Button variant="outline">立即执行所有规则</Button>
            </div>

            <div className="space-y-4">
              {autoTagRules.map((rule) => (
                <Card key={rule.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          {rule.name}
                          {rule.enabled ? (
                            <Badge variant="secondary">已启用</Badge>
                          ) : (
                            <Badge variant="outline">已禁用</Badge>
                          )}
                        </CardTitle>
                        <CardDescription className="flex items-center gap-2 mt-1">
                          <div className="flex items-center">
                            <div className="h-3 w-3 rounded-full mr-1" style={{ backgroundColor: rule.tagColor }} />
                            标签: {rule.tag}
                          </div>
                          <span>|</span>
                          <div>匹配会员: {rule.matchedMembers}</div>
                          <span>|</span>
                          <div>最后执行: {rule.lastRun}</div>
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch checked={rule.enabled} />
                        <Button variant="outline" size="sm">
                          编辑
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium">规则条件（满足所有条件）:</h4>
                      <div className="pl-4 space-y-2">
                        {rule.conditions.map((condition) => (
                          <div key={condition.id} className="flex items-center gap-2">
                            <Badge variant="outline">{condition.field}</Badge>
                            <span>{condition.operator}</span>
                            <Badge>
                              {condition.value}
                              {condition.unit}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between pt-0">
                    <Button variant="ghost" size="sm">
                      查看匹配会员
                    </Button>
                    <Button variant="ghost" size="sm" className="text-red-600">
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除规则
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>规则执行历史</CardTitle>
                <CardDescription>自动标签规则的执行历史记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="grid grid-cols-6 bg-muted p-3 text-sm font-medium">
                    <div>执行时间</div>
                    <div>规则数</div>
                    <div>处理会员</div>
                    <div>添加标签</div>
                    <div>移除标签</div>
                    <div>执行时长</div>
                  </div>
                  {ruleHistory.map((history) => (
                    <div key={history.id} className="grid grid-cols-6 p-3 text-sm border-t">
                      <div>{history.date}</div>
                      <div>{history.rulesRun}</div>
                      <div>{history.membersProcessed}</div>
                      <div className="text-green-600">+{history.tagsAdded}</div>
                      <div className="text-red-600">-{history.tagsRemoved}</div>
                      <div>{history.duration}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>规则执行设置</CardTitle>
                <CardDescription>配置自动标签规则的执行方式和频率</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between space-x-2">
                  <Label htmlFor="auto-run" className="flex-1">
                    <div className="font-medium">自动执行规则</div>
                    <p className="text-sm text-muted-foreground">
                      启用后，系统将按照设定的频率自动执行所有已启用的规则
                    </p>
                  </Label>
                  <Switch id="auto-run" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="run-frequency">执行频率</Label>
                  <Select defaultValue="daily">
                    <SelectTrigger id="run-frequency">
                      <SelectValue placeholder="选择执行频率" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">每小时</SelectItem>
                      <SelectItem value="daily">每天</SelectItem>
                      <SelectItem value="weekly">每周</SelectItem>
                      <SelectItem value="monthly">每月</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="run-time">执行时间</Label>
                  <Select defaultValue="08:00">
                    <SelectTrigger id="run-time">
                      <SelectValue placeholder="选择执行时间" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="00:00">00:00</SelectItem>
                      <SelectItem value="04:00">04:00</SelectItem>
                      <SelectItem value="08:00">08:00</SelectItem>
                      <SelectItem value="12:00">12:00</SelectItem>
                      <SelectItem value="16:00">16:00</SelectItem>
                      <SelectItem value="20:00">20:00</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="rounded-md bg-yellow-50 p-4 border border-yellow-200">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertCircle className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">注意事项</h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          自动标签规则会根据会员数据自动添加或移除标签。请确保规则设置正确，避免错误标记会员。
                          规则执行可能需要一定时间，建议在系统负载较低的时间段执行。
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="advanced">
                <AccordionTrigger>高级设置</AccordionTrigger>
                <AccordionContent className="space-y-4 pt-2">
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="remove-tags" className="flex-1">
                      <div className="font-medium">自动移除标签</div>
                      <p className="text-sm text-muted-foreground">当会员不再满足条件时，自动移除对应标签</p>
                    </Label>
                    <Switch id="remove-tags" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="notify-changes" className="flex-1">
                      <div className="font-medium">标签变更通知</div>
                      <p className="text-sm text-muted-foreground">当会员标签发生变化时，通知管理员</p>
                    </Label>
                    <Switch id="notify-changes" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="history-retention">历史记录保留时间</Label>
                    <Select defaultValue="90">
                      <SelectTrigger id="history-retention">
                        <SelectValue placeholder="选择保留时间" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30">30天</SelectItem>
                        <SelectItem value="60">60天</SelectItem>
                        <SelectItem value="90">90天</SelectItem>
                        <SelectItem value="180">180天</SelectItem>
                        <SelectItem value="365">365天</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </TabsContent>
        </Tabs>

        <DialogFooter>{activeTab === "settings" && <Button type="submit">保存设置</Button>}</DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

