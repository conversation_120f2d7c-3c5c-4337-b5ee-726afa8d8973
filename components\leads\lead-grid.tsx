"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  Eye,
  Edit,
  PhoneCall,
  Mail,
  Calendar,
  UserCheck
} from "lucide-react"
import { LeadDetailDialog } from "@/components/leads/lead-detail-dialog-new"
import { mockLeads } from "@/lib/mock-data/leads-data"

interface LeadGridProps {
  searchQuery?: string
  statusFilter?: string
}

export function LeadGrid({ searchQuery = "", statusFilter = "all" }: LeadGridProps) {
  const [selectedLead, setSelectedLead] = useState<any>(null)
  const [isDetailOpen, setIsDetailOpen] = useState(false)

  // 过滤潜客数据
  const filteredLeads = mockLeads.filter((lead) => {
    // 状态过滤
    if (statusFilter !== "all" && lead.status !== statusFilter) {
      return false
    }

    // 搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        lead.name.toLowerCase().includes(query) ||
        lead.phone.includes(query) ||
        lead.source.toLowerCase().includes(query)
      )
    }

    return true
  })

  // 查看潜客详情
  const handleViewDetail = (lead: any) => {
    setSelectedLead(lead)
    setIsDetailOpen(true)
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return <Badge variant="outline">新获取</Badge>
      case "contacted":
        return <Badge variant="secondary">已联系</Badge>
      case "qualified":
        return <Badge variant="default">已确认</Badge>
      case "negotiating":
        return <Badge variant="warning">洽谈中</Badge>
      case "converted":
        return <Badge variant="success">已转化</Badge>
      case "lost":
        return <Badge variant="destructive">已流失</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredLeads.length > 0 ? (
          filteredLeads.map((lead) => (
            <Card key={lead.id} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarFallback>{lead.name[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{lead.name}</div>
                        <div className="text-sm text-muted-foreground">{lead.gender === "male" ? "男" : "女"}</div>
                      </div>
                    </div>
                    {getStatusBadge(lead.status)}
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center">
                      <PhoneCall className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{lead.phone}</span>
                    </div>
                    {lead.email && (
                      <div className="flex items-center">
                        <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{lead.email}</span>
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">来源：</span>
                      <span>{lead.source}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">意向度：</span>
                      <span className="flex">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <svg
                            key={i}
                            className={`h-4 w-4 ${i < lead.interest ? "text-yellow-400 fill-yellow-400" : "text-gray-300 fill-gray-300"}`}
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                          </svg>
                        ))}
                      </span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">获取时间：</span>
                      <span>{lead.createdAt}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">负责人：</span>
                      <span>{lead.assignedTo}</span>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <div className="text-sm">
                      <span className="text-muted-foreground">最近跟进：</span>
                      <span>{lead.lastFollowUp}</span>
                    </div>
                    {lead.notes && (
                      <div className="mt-2 text-sm">
                        <span className="text-muted-foreground">备注：</span>
                        <p className="line-clamp-2">{lead.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between p-4 pt-0 border-t">
                <Button variant="outline" size="sm" onClick={() => handleViewDetail(lead)}>
                  <Eye className="mr-2 h-4 w-4" />
                  详情
                </Button>
                <Button variant="outline" size="sm">
                  <Calendar className="mr-2 h-4 w-4" />
                  跟进
                </Button>
                <Button variant="outline" size="sm">
                  <UserCheck className="mr-2 h-4 w-4" />
                  转化
                </Button>
                <Button variant="outline" size="sm">
                  <Edit className="mr-2 h-4 w-4" />
                  编辑
                </Button>
              </CardFooter>
            </Card>
          ))
        ) : (
          <div className="col-span-full text-center py-10">没有找到匹配的潜客</div>
        )}
      </div>

      {selectedLead && (
        <LeadDetailDialog
          lead={selectedLead}
          open={isDetailOpen}
          onOpenChange={setIsDetailOpen}
        />
      )}
    </>
  )
}
