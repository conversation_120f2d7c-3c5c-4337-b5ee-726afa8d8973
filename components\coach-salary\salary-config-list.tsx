"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { MoreHorizontal, Edit, Trash, Settings, Copy } from "lucide-react"
import { EditSalaryConfigDialog } from "./edit-salary-config-dialog"
import { CourseRatesDialog } from "./course-rates-dialog"

// 薪资类型映射
const salaryTypeMap = {
  fixed: { label: "固定薪资", color: "bg-gray-100 text-gray-800 hover:bg-gray-100/80" },
  hourly: { label: "课时费", color: "bg-blue-100 text-blue-800 hover:bg-blue-100/80" },
  mixed: { label: "底薪+课时费", color: "bg-green-100 text-green-800 hover:bg-green-100/80" },
  commission: { label: "底薪+提成", color: "bg-purple-100 text-purple-800 hover:bg-purple-100/80" },
  full: { label: "底薪+课时费+提成", color: "bg-orange-100 text-orange-800 hover:bg-orange-100/80" },
}

// 模拟数据
const mockSalaryConfigs = [
  {
    id: "SC001",
    coachId: "C001",
    coachName: "张教练",
    salaryType: "full",
    baseSalary: 5000,
    hourlyRate: 200,
    commissionRate: 10,
    probationSalaryPercentage: 80,
    isProbation: false,
    probationEndDate: "2023-08-15",
    paymentDay: 10,
    createdAt: "2023-05-15",
  },
  {
    id: "SC002",
    coachId: "C002",
    coachName: "李教练",
    salaryType: "mixed",
    baseSalary: 4000,
    hourlyRate: 180,
    commissionRate: 0,
    probationSalaryPercentage: 80,
    isProbation: false,
    probationEndDate: "2023-09-22",
    paymentDay: 10,
    createdAt: "2023-07-22",
  },
  {
    id: "SC003",
    coachId: "C003",
    coachName: "王教练",
    salaryType: "commission",
    baseSalary: 3500,
    hourlyRate: 0,
    commissionRate: 15,
    probationSalaryPercentage: 80,
    isProbation: true,
    probationEndDate: "2023-12-10",
    paymentDay: 10,
    createdAt: "2023-09-10",
  },
  {
    id: "SC004",
    coachId: "C004",
    coachName: "赵教练",
    salaryType: "hourly",
    baseSalary: 0,
    hourlyRate: 220,
    commissionRate: 0,
    probationSalaryPercentage: 80,
    isProbation: true,
    probationEndDate: "2023-11-20",
    paymentDay: 10,
    createdAt: "2023-08-20",
  },
]

export function SalaryConfigList() {
  const [searchKeyword, setSearchKeyword] = useState("")
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showCourseRatesDialog, setShowCourseRatesDialog] = useState(false)
  const [currentConfig, setCurrentConfig] = useState<any>(null)

  // 过滤配置
  const filteredConfigs = mockSalaryConfigs.filter(config => 
    config.coachName.includes(searchKeyword)
  )

  // 打开编辑对话框
  const openEditDialog = (config: any) => {
    setCurrentConfig(config)
    setShowEditDialog(true)
  }

  // 打开课程费率对话框
  const openCourseRatesDialog = (config: any) => {
    setCurrentConfig(config)
    setShowCourseRatesDialog(true)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Input
          placeholder="搜索教练姓名..."
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>教练</TableHead>
              <TableHead>薪资类型</TableHead>
              <TableHead className="text-right">底薪(元/月)</TableHead>
              <TableHead className="text-right">课时费(元/课时)</TableHead>
              <TableHead className="text-right">提成比例(%)</TableHead>
              <TableHead>试用期</TableHead>
              <TableHead>试用期薪资比例</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredConfigs.map((config) => (
              <TableRow key={config.id}>
                <TableCell className="font-medium">{config.coachName}</TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={salaryTypeMap[config.salaryType as keyof typeof salaryTypeMap].color}
                  >
                    {salaryTypeMap[config.salaryType as keyof typeof salaryTypeMap].label}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  {config.baseSalary > 0 ? `¥${config.baseSalary.toLocaleString()}` : "-"}
                </TableCell>
                <TableCell className="text-right">
                  {config.hourlyRate > 0 ? `¥${config.hourlyRate.toLocaleString()}` : "-"}
                </TableCell>
                <TableCell className="text-right">
                  {config.commissionRate > 0 ? `${config.commissionRate}%` : "-"}
                </TableCell>
                <TableCell>
                  {config.isProbation ? (
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100/80">
                      试用期中
                    </Badge>
                  ) : "否"}
                </TableCell>
                <TableCell>{config.probationSalaryPercentage}%</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">打开菜单</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>操作</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => openEditDialog(config)}>
                        <Edit className="mr-2 h-4 w-4" />
                        编辑配置
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => openCourseRatesDialog(config)}>
                        <Settings className="mr-2 h-4 w-4" />
                        课程费率设置
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Copy className="mr-2 h-4 w-4" />
                        复制配置
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash className="mr-2 h-4 w-4" />
                        删除配置
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* 编辑薪资配置对话框 */}
      <EditSalaryConfigDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        config={currentConfig}
      />

      {/* 课程费率设置对话框 */}
      <CourseRatesDialog
        open={showCourseRatesDialog}
        onOpenChange={setShowCourseRatesDialog}
        config={currentConfig}
      />
    </div>
  )
}
