"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Plus, X, Award } from "lucide-react"

interface MemberLevelDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  level?: any
}

export function MemberLevelDialog({ open, onOpenChange, level }: MemberLevelDialogProps) {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    color: "#3b82f6",
    isDefault: false,
    status: "active",
    benefits: [""],
    upgradeRules: "",
    order: 0,
    upgradeCondition: "",
    memberCount: 0
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    if (level) {
      setFormData({
        name: level.name,
        description: level.description,
        color: level.color,
        isDefault: level.isDefault,
        status: level.status,
        benefits: level.benefits,
        upgradeRules: level.upgradeRules,
        order: level.order,
        upgradeCondition: level.isDefault ? "基础会员" : level.upgradeRules.split("，")[0],
        memberCount: level.memberCount || 0
      })
    } else {
      setFormData({
        name: "",
        description: "",
        color: "#3b82f6",
        isDefault: false,
        status: "active",
        benefits: [""],
        upgradeRules: "",
        order: 0,
        upgradeCondition: "",
        memberCount: 0
      })
    }
  }, [level, open])

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleBenefitChange = (index: number, value: string) => {
    const newBenefits = [...formData.benefits]
    newBenefits[index] = value
    handleChange("benefits", newBenefits)
  }

  const addBenefit = () => {
    handleChange("benefits", [...formData.benefits, ""])
  }

  const removeBenefit = (index: number) => {
    const newBenefits = formData.benefits.filter((_, i) => i !== index)
    handleChange("benefits", newBenefits)
  }

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "请输入等级名称",
        variant: "destructive",
      })
      return
    }

    // 过滤掉空的权益
    const filteredBenefits = formData.benefits.filter((benefit) => benefit.trim() !== "")
    if (filteredBenefits.length === 0) {
      toast({
        title: "请至少添加一项会员权益",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: level ? "会员等级已更新" : "会员等级已创建",
        description: `${formData.name} 已成功${level ? "更新" : "创建"}`,
      })

      onOpenChange(false)
    } catch (error) {
      toast({
        title: "操作失败",
        description: "保存会员等级时出错，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{level ? "编辑会员等级" : "添加会员等级"}</DialogTitle>
          <DialogDescription>
            {level
              ? "修改会员等级信息、权益和升级规则"
              : "创建新的会员等级，设置权益和升级规则"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="level-name">等级名称</Label>
              <Input
                id="level-name"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                placeholder="例如：金卡会员"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="level-color">等级颜色</Label>
              <div className="flex items-center gap-2">
                <div
                  className="h-6 w-6 rounded-full border"
                  style={{ backgroundColor: formData.color }}
                />
                <Input
                  id="level-color"
                  type="color"
                  value={formData.color}
                  onChange={(e) => handleChange("color", e.target.value)}
                  className="w-16 h-8 p-1"
                />
                <span className="text-sm text-muted-foreground">{formData.color}</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="level-description">等级描述</Label>
            <Textarea
              id="level-description"
              value={formData.description}
              onChange={(e) => handleChange("description", e.target.value)}
              placeholder="描述这个会员等级的特点和适用人群..."
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="upgrade-condition">购买条件</Label>
            <Input
              id="upgrade-condition"
              value={formData.upgradeCondition}
              onChange={(e) => {
                handleChange("upgradeCondition", e.target.value)
                if (!formData.isDefault) {
                  handleChange("upgradeRules", e.target.value)
                }
              }}
              placeholder="例如：累计消费满2000元"
              disabled={formData.isDefault}
            />
            <p className="text-sm text-muted-foreground">
              {formData.isDefault ? "基础会员无需购买条件" : "设置会员达到此等级的条件"}
            </p>
          </div>

          <div className="space-y-4">
            <Label>会员权益</Label>
            <div className="space-y-3">
              {formData.benefits.map((benefit, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    value={benefit}
                    onChange={(e) => handleBenefitChange(index, e.target.value)}
                    placeholder="例如：享受商品9折优惠"
                  />
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeBenefit(index)}
                    disabled={formData.benefits.length === 1}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={addBenefit}
            >
              <Plus className="mr-2 h-4 w-4" />
              添加权益
            </Button>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="is-default">设为默认等级</Label>
                <p className="text-sm text-muted-foreground">
                  新会员将自动分配到默认等级
                </p>
              </div>
              <Switch
                id="is-default"
                checked={formData.isDefault}
                onCheckedChange={(checked) => {
                  handleChange("isDefault", checked)
                  if (checked) {
                    handleChange("upgradeCondition", "基础会员")
                  } else {
                    handleChange("upgradeCondition", "")
                  }
                }}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="status">启用此等级</Label>
                <p className="text-sm text-muted-foreground">
                  禁用后，此等级将不会显示
                </p>
              </div>
              <Switch
                id="status"
                checked={formData.status === "active"}
                onCheckedChange={(checked) =>
                  handleChange("status", checked ? "active" : "inactive")
                }
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "保存中..." : "保存"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
