"use client"

import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CreditCard, Wallet, BarChart2, Clock, Hash } from "lucide-react"

interface CardValueDisplayProps {
  cardType: "time" | "count" | "value" // 期限卡、次数卡、储值卡
  originalValue: number // 原始价值（购买价格）
  consumedValue: number // 已消耗价值
  remainingValue: number // 剩余价值
  
  // 期限卡特有属性
  totalDays?: number
  usedDays?: number
  remainingDays?: number
  
  // 次数卡特有属性
  totalCount?: number
  usedCount?: number
  remainingCount?: number
  
  // 储值卡特有属性
  totalAmount?: number
  usedAmount?: number
  remainingAmount?: number
  
  // 显示选项
  compact?: boolean // 是否使用紧凑模式
  showDetails?: boolean // 是否显示详细信息
  showChart?: boolean // 是否显示图表
}

export function CardValueDisplay({
  cardType,
  originalValue,
  consumedValue,
  remainingValue,
  totalDays,
  usedDays,
  remainingDays,
  totalCount,
  usedCount,
  remainingCount,
  totalAmount,
  usedAmount,
  remainingAmount,
  compact = false,
  showDetails = true,
  showChart = true
}: CardValueDisplayProps) {
  // 计算消耗百分比
  const consumptionPercentage = Math.round((consumedValue / originalValue) * 100) || 0
  const remainingPercentage = 100 - consumptionPercentage
  
  // 格式化金额显示
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(value)
  }
  
  // 获取卡类型标签
  const getCardTypeBadge = () => {
    switch (cardType) {
      case "time":
        return <Badge className="bg-green-500"><Clock className="mr-1 h-3 w-3" />期限卡</Badge>
      case "count":
        return <Badge className="bg-blue-500"><Hash className="mr-1 h-3 w-3" />次数卡</Badge>
      case "value":
        return <Badge className="bg-purple-500"><Wallet className="mr-1 h-3 w-3" />储值卡</Badge>
      default:
        return <Badge>未知类型</Badge>
    }
  }
  
  if (compact) {
    // 紧凑模式，适用于列表显示
    return (
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Wallet className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">卡价值:</span>
          </div>
          <div className="text-right">
            <span className="font-medium">{formatCurrency(remainingValue)}</span>
            <span className="text-xs text-muted-foreground ml-1">/ {formatCurrency(originalValue)}</span>
          </div>
        </div>
        <Progress value={remainingPercentage} className="h-2" />
      </div>
    )
  }
  
  // 标准模式，显示详细信息
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center gap-2">
            <Wallet className="h-5 w-5" />
            会员卡价值
          </CardTitle>
          {getCardTypeBadge()}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 价值进度条 */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>剩余价值: {formatCurrency(remainingValue)}</span>
              <span>{remainingPercentage}%</span>
            </div>
            <Progress value={remainingPercentage} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>已消耗: {formatCurrency(consumedValue)}</span>
              <span>总价值: {formatCurrency(originalValue)}</span>
            </div>
          </div>
          
          {showDetails && (
            <>
              <Separator />
              
              {/* 根据卡类型显示不同的详细信息 */}
              {cardType === "time" && remainingDays !== undefined && totalDays !== undefined && (
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-xs text-muted-foreground">总天数</p>
                    <p className="font-medium">{totalDays}天</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">已使用</p>
                    <p className="font-medium">{usedDays || totalDays - remainingDays}天</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">剩余天数</p>
                    <p className="font-medium">{remainingDays}天</p>
                  </div>
                </div>
              )}
              
              {cardType === "count" && remainingCount !== undefined && totalCount !== undefined && (
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-xs text-muted-foreground">总次数</p>
                    <p className="font-medium">{totalCount}次</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">已使用</p>
                    <p className="font-medium">{usedCount || totalCount - remainingCount}次</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">剩余次数</p>
                    <p className="font-medium">{remainingCount}次</p>
                  </div>
                </div>
              )}
              
              {cardType === "value" && remainingAmount !== undefined && totalAmount !== undefined && (
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-xs text-muted-foreground">总金额</p>
                    <p className="font-medium">{formatCurrency(totalAmount)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">已使用</p>
                    <p className="font-medium">{formatCurrency(usedAmount || totalAmount - remainingAmount)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">剩余金额</p>
                    <p className="font-medium">{formatCurrency(remainingAmount)}</p>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
