"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { ArrowLeft, CalendarDays, Upload } from "lucide-react"

// 表单验证模式
const seriesCourseFormSchema = z.object({
  name: z.string().min(1, "请输入课程名称"),
  description: z.string().min(1, "请输入课程描述"),
  courseType: z.string().min(1, "请选择课程类型"),
  totalSessions: z.string().min(1, "请输入总课时数"),
  startDate: z.string().min(1, "请选择开始日期"),
  endDate: z.string().min(1, "请选择结束日期"),
  price: z.string().min(1, "请输入课程价格"),
  maxStudents: z.string().min(1, "请输入最大学员数"),
  instructor: z.string().min(1, "请选择主讲教练"),
  status: z.string().min(1, "请选择状态"),
  tags: z.string().optional(),
})

export default function CreateSeriesCoursePage() {
  const router = useRouter()
  const [coverImage, setCoverImage] = useState<string | null>(null)
  
  // 创建表单
  const form = useForm<z.infer<typeof seriesCourseFormSchema>>({
    resolver: zodResolver(seriesCourseFormSchema),
    defaultValues: {
      name: "",
      description: "",
      courseType: "",
      totalSessions: "",
      startDate: "",
      endDate: "",
      price: "",
      maxStudents: "",
      instructor: "",
      status: "draft",
      tags: "",
    },
  })
  
  // 处理表单提交
  const onSubmit = (values: z.infer<typeof seriesCourseFormSchema>) => {
    console.log("创建系列课程:", values, "封面图片:", coverImage)
    
    toast({
      title: "创建系列课程成功",
      description: `已成功创建系列课程 ${values.name}。`,
    })
    
    // 跳转到系列课程列表页
    router.push("/courses/series-courses")
  }
  
  // 处理封面图片上传
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = () => {
        setCoverImage(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" onClick={() => router.push("/courses/series-courses")}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold tracking-tight ml-2">创建系列课程</h1>
      </div>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                  <CardDescription>设置系列课程的基本信息</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>课程名称</FormLabel>
                        <FormControl>
                          <Input placeholder="输入课程名称" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="courseType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>课程类型</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择课程类型" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="瑜伽">瑜伽</SelectItem>
                              <SelectItem value="普拉提">普拉提</SelectItem>
                              <SelectItem value="教练培训">教练培训</SelectItem>
                              <SelectItem value="特色课程">特色课程</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="instructor"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>主讲教练</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择主讲教练" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="李教练">李教练</SelectItem>
                              <SelectItem value="王教练">王教练</SelectItem>
                              <SelectItem value="张教练">张教练</SelectItem>
                              <SelectItem value="刘教练">刘教练</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>课程描述</FormLabel>
                        <FormControl>
                          <Textarea placeholder="输入课程描述" className="min-h-32" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="tags"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>标签</FormLabel>
                        <FormControl>
                          <Input placeholder="输入标签，用逗号分隔" {...field} />
                        </FormControl>
                        <FormDescription>
                          多个标签请用逗号分隔，例如：初级,入门,哈他瑜伽
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>课程设置</CardTitle>
                  <CardDescription>设置系列课程的详细信息</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="totalSessions"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>总课时数</FormLabel>
                          <FormControl>
                            <Input type="number" min="1" placeholder="输入总课时数" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="maxStudents"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>最大学员数</FormLabel>
                          <FormControl>
                            <Input type="number" min="1" placeholder="输入最大学员数" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="startDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>开始日期</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="endDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>结束日期</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>课程价格</FormLabel>
                          <FormControl>
                            <Input type="number" min="0" placeholder="输入课程价格" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>状态</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择状态" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="draft">草稿</SelectItem>
                              <SelectItem value="published">已发布</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>封面图片</CardTitle>
                  <CardDescription>上传系列课程的封面图片</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-6 h-48">
                    {coverImage ? (
                      <div className="relative w-full h-full">
                        <img 
                          src={coverImage} 
                          alt="课程封面" 
                          className="w-full h-full object-cover rounded-lg"
                        />
                        <Button
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={() => setCoverImage(null)}
                        >
                          删除
                        </Button>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center text-center">
                        <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                        <p className="text-sm text-muted-foreground mb-2">点击或拖拽上传图片</p>
                        <Input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          id="cover-image"
                          onChange={handleImageUpload}
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById("cover-image")?.click()}
                        >
                          选择图片
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>操作</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button type="submit" className="w-full">
                    创建系列课程
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    className="w-full"
                    onClick={() => router.push("/courses/series-courses")}
                  >
                    取消
                  </Button>
                </CardContent>
              </Card>
              
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>提示</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-sm text-muted-foreground space-y-2">
                    <p>
                      <CalendarDays className="inline-block h-4 w-4 mr-1" />
                      创建系列课程后，您可以在课程详情页添加具体的课程排期。
                    </p>
                    <p>
                      系列课程创建后，会员可以一次性报名整个系列课程，您也可以为所有报名学员一键约课。
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </Form>
    </div>
  )
}
