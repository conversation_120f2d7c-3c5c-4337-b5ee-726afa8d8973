"use client"

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface CardDistributionChartProps {
  className?: string
}

export function CardDistributionChart({ className }: CardDistributionChartProps) {
  // 模拟会员卡分布数据
  const cardDistributionData = [
    { name: "年卡", members: 280, color: "#4F46E5" },
    { name: "季卡", members: 185, color: "#06B6D4" },
    { name: "月卡", members: 120, color: "#10B981" },
    { name: "次卡", members: 60, color: "#F59E0B" },
    { name: "体验卡", members: 20, color: "#EC4899" },
  ]
  
  // 计算总会员数
  const totalMembers = cardDistributionData.reduce((sum, item) => sum + item.members, 0)
  
  // 计算每种卡的百分比
  const cardDistributionWithPercentage = cardDistributionData.map(item => ({
    ...item,
    percentage: Math.round((item.members / totalMembers) * 100)
  }))
  
  // 排序，从高到低
  cardDistributionWithPercentage.sort((a, b) => b.members - a.members)
  
  // 渲染饼图
  const renderPieChart = () => {
    let cumulativePercentage = 0
    
    return (
      <div className="relative mx-auto h-[200px] w-[200px]">
        {/* 中心文字 */}
        <div className="absolute inset-0 flex items-center justify-center rounded-full border-8 border-background">
          <div className="text-center">
            <div className="text-2xl font-bold">{totalMembers}</div>
            <div className="text-xs text-muted-foreground">持卡会员总数</div>
          </div>
        </div>
        
        {/* 饼图 */}
        <svg className="h-full w-full -rotate-90" viewBox="0 0 100 100">
          {cardDistributionWithPercentage.map((item, index) => {
            const startPercentage = cumulativePercentage
            cumulativePercentage += item.percentage
            
            // 计算圆弧路径
            const startAngle = startPercentage * 3.6 // 3.6 = 360 / 100
            const endAngle = cumulativePercentage * 3.6
            
            // 计算圆弧的起点和终点坐标
            const startX = 50 + 40 * Math.cos((startAngle * Math.PI) / 180)
            const startY = 50 + 40 * Math.sin((startAngle * Math.PI) / 180)
            const endX = 50 + 40 * Math.cos((endAngle * Math.PI) / 180)
            const endY = 50 + 40 * Math.sin((endAngle * Math.PI) / 180)
            
            // 确定是否需要使用大弧（large-arc-flag）
            const largeArcFlag = item.percentage > 50 ? 1 : 0
            
            // 构建SVG路径
            const path = `
              M 50 50
              L ${startX} ${startY}
              A 40 40 0 ${largeArcFlag} 1 ${endX} ${endY}
              Z
            `
            
            return (
              <path
                key={index}
                d={path}
                fill={item.color}
                stroke="white"
                strokeWidth="1"
                className="transition-opacity duration-300 hover:opacity-80"
              />
            )
          })}
        </svg>
      </div>
    )
  }
  
  // 渲染图例
  const renderLegend = () => {
    return (
      <div className="mt-4 grid grid-cols-2 gap-4">
        {cardDistributionWithPercentage.map((item, index) => (
          <div key={index} className="flex items-center gap-2">
            <div 
              className="h-3 w-3 rounded-sm" 
              style={{ backgroundColor: item.color }}
            />
            <div className="flex flex-1 items-center justify-between">
              <span className="text-sm">{item.name}</span>
              <div className="text-right">
                <span className="text-sm font-medium">{item.members}</span>
                <span className="ml-1 text-xs text-muted-foreground">({item.percentage}%)</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="text-base">会员卡分布</CardTitle>
      </CardHeader>
      <CardContent>
        {renderPieChart()}
        {renderLegend()}
      </CardContent>
    </Card>
  )
}
