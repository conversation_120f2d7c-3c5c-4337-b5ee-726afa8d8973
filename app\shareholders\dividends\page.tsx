"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Download,
  Filter,
  MoreHorizontal,
  Plus,
  Search,
  TrendingUp,
  Wallet,
  CreditCard,
  ArrowUpDown,
  AlertTriangle,
  Eye,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { DividendDetailDialog } from "@/components/shareholders/dividends/dividend-detail-dialog"
import { AddDividendDialog } from "@/components/shareholders/dividends/add-dividend-dialog"
import { ShareholderBreadcrumb } from "@/components/shareholders/shareholder-breadcrumb"
import { ShareholderNav } from "@/components/shareholders/shareholder-nav"

// 模拟数据
const dividendRecords = [
  {
    id: "1",
    shareholder: {
      id: "101",
      name: "张三",
      type: "消费型股东",
      avatar: "/avatars/01.png",
    },
    amount: 520,
    status: "已发放",
    period: "2023年5月",
    referrals: 5,
    referralAmount: 10400,
    dividendRate: "5%",
    createdAt: "2023-06-01",
    paidAt: "2023-06-05",
    paymentMethod: "微信支付",
  },
  {
    id: "2",
    shareholder: {
      id: "102",
      name: "李四",
      type: "投资型股东",
      avatar: "/avatars/02.png",
    },
    amount: 1200,
    status: "已发放",
    period: "2023年5月",
    referrals: 0,
    referralAmount: 0,
    dividendRate: "10%年化",
    createdAt: "2023-06-01",
    paidAt: "2023-06-05",
    paymentMethod: "银行转账",
  },
  {
    id: "3",
    shareholder: {
      id: "103",
      name: "王五",
      type: "资源型股东",
      avatar: "/avatars/03.png",
    },
    amount: 750,
    status: "待发放",
    period: "2023年5月",
    referrals: 15,
    referralAmount: 0,
    dividendRate: "50元/人",
    createdAt: "2023-06-01",
    paidAt: null,
    paymentMethod: null,
  },
  {
    id: "4",
    shareholder: {
      id: "104",
      name: "赵六",
      type: "员工型股东",
      avatar: "/avatars/04.png",
    },
    amount: 860,
    status: "已发放",
    period: "2023年5月",
    referrals: 0,
    referralAmount: 43000,
    dividendRate: "2%",
    createdAt: "2023-06-01",
    paidAt: "2023-06-03",
    paymentMethod: "微信支付",
  },
  {
    id: "5",
    shareholder: {
      id: "105",
      name: "美丽美容院",
      type: "联盟型股东",
      avatar: "/avatars/05.png",
    },
    amount: 630,
    status: "已发放",
    period: "2023年5月",
    referrals: 8,
    referralAmount: 21000,
    dividendRate: "3%",
    createdAt: "2023-06-01",
    paidAt: "2023-06-04",
    paymentMethod: "支付宝",
  },
]

export default function DividendsPage() {
  const [period, setPeriod] = useState("2023年5月")
  const [status, setStatus] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [date, setDate] = useState<Date>()
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [selectedDividend, setSelectedDividend] = useState<any>(null)

  // 过滤分红记录
  const filteredRecords = dividendRecords.filter((record) => {
    if (status !== "all" && record.status !== status) return false
    if (
      searchQuery &&
      !record.shareholder.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !record.shareholder.type.toLowerCase().includes(searchQuery.toLowerCase())
    )
      return false
    return true
  })

  // 查看详情
  const handleViewDetail = (record: any) => {
    setSelectedDividend(record)
    setShowDetailDialog(true)
  }

  // 添加分红记录
  const handleAddDividend = () => {
    setShowAddDialog(true)
  }

  return (
    <div className="space-y-6">
      <ShareholderBreadcrumb />
      <ShareholderNav />

      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">股东分红记录</h1>
        <div className="flex gap-2">
          <Button onClick={handleAddDividend}>
            <Plus className="mr-2 h-4 w-4" />
            添加分红
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">本期分红总额</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥3,960.00</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                较上期增长 12.5%
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">分红股东数</CardTitle>
            <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                较上期增长 25%
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待发放金额</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥750.00</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-red-500 flex items-center">
                <AlertTriangle className="mr-1 h-3 w-3" />
                1笔待发放
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">引流客户数</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">28</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                较上期增长 40%
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center gap-2 max-w-md">
          <Input
            placeholder="搜索股东姓名或类型..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
          <Button variant="outline" size="icon">
            <Search className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择周期" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2023年5月">2023年5月</SelectItem>
              <SelectItem value="2023年4月">2023年4月</SelectItem>
              <SelectItem value="2023年3月">2023年3月</SelectItem>
              <SelectItem value="2023年2月">2023年2月</SelectItem>
              <SelectItem value="2023年1月">2023年1月</SelectItem>
            </SelectContent>
          </Select>

          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="已发放">已发放</SelectItem>
              <SelectItem value="待发放">待发放</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>股东信息</TableHead>
              <TableHead>股东类型</TableHead>
              <TableHead>分红金额</TableHead>
              <TableHead>分红周期</TableHead>
              <TableHead>引流客户</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredRecords.map((record) => (
              <TableRow key={record.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={record.shareholder.avatar} alt={record.shareholder.name} />
                      <AvatarFallback>{record.shareholder.name[0]}</AvatarFallback>
                    </Avatar>
                    <div>{record.shareholder.name}</div>
                  </div>
                </TableCell>
                <TableCell>{record.shareholder.type}</TableCell>
                <TableCell>¥{record.amount.toFixed(2)}</TableCell>
                <TableCell>{record.period}</TableCell>
                <TableCell>{record.referrals}人</TableCell>
                <TableCell>
                  <Badge variant={record.status === "已发放" ? "default" : "secondary"}>
                    {record.status}
                  </Badge>
                </TableCell>
                <TableCell>{record.createdAt}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>操作</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleViewDetail(record)}>
                        <Eye className="mr-2 h-4 w-4" />
                        查看详情
                      </DropdownMenuItem>
                      {record.status === "待发放" && (
                        <DropdownMenuItem>
                          <Wallet className="mr-2 h-4 w-4" />
                          发放分红
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <DividendDetailDialog
        open={showDetailDialog}
        onOpenChange={setShowDetailDialog}
        dividend={selectedDividend}
      />
      <AddDividendDialog open={showAddDialog} onOpenChange={setShowAddDialog} />
    </div>
  )
}
