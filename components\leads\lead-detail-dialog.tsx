"use client"

import { useState } from "react"
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  PhoneCall, 
  Mail, 
  Calendar, 
  Clock, 
  Edit, 
  UserCheck, 
  MessageSquare, 
  Plus, 
  FileText, 
  Tag 
} from "lucide-react"
import { LeadFollowUpForm } from "@/components/leads/lead-follow-up-form"

interface LeadDetailDialogProps {
  lead: any
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function LeadDetailDialog({ lead, open, onOpenChange }: LeadDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("info")
  const [showFollowUpForm, setShowFollowUpForm] = useState(false)

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return <Badge variant="outline">新获取</Badge>
      case "contacted":
        return <Badge variant="secondary">已联系</Badge>
      case "qualified":
        return <Badge variant="default">已确认</Badge>
      case "negotiating":
        return <Badge variant="warning">洽谈中</Badge>
      case "converted":
        return <Badge variant="success">已转化</Badge>
      case "lost":
        return <Badge variant="destructive">已流失</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 模拟跟进记录数据
  const followUps = [
    {
      id: 1,
      date: "2023-12-15",
      time: "14:30",
      type: "phone",
      content: "电话联系客户，客户表示对我们的瑜伽课程很感兴趣，但目前时间安排有些紧张，约定下周再次联系。",
      staff: "张三",
      nextFollowUp: "2023-12-22",
    },
    {
      id: 2,
      date: "2023-12-22",
      time: "16:00",
      type: "visit",
      content: "客户到店参观，对场地环境和教练资质表示满意，对价格有些顾虑，已提供会员卡折扣方案，客户考虑中。",
      staff: "李四",
      nextFollowUp: "2023-12-25",
    },
    {
      id: 3,
      date: "2023-12-25",
      time: "10:15",
      type: "wechat",
      content: "通过微信发送了课程表和优惠活动信息，客户表示会尽快决定。",
      staff: "张三",
      nextFollowUp: "2023-12-28",
    },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>潜客详情</span>
            {getStatusBadge(lead.status)}
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col md:flex-row gap-6 mt-4">
          <div className="w-full md:w-1/3">
            <div className="flex flex-col items-center text-center mb-6">
              <Avatar className="h-24 w-24 mb-4">
                <AvatarFallback className="text-2xl">{lead.name[0]}</AvatarFallback>
              </Avatar>
              <h2 className="text-xl font-semibold">{lead.name}</h2>
              <p className="text-muted-foreground">{lead.gender === "male" ? "男" : "女"}</p>
              
              <div className="flex mt-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <svg
                    key={i}
                    className={`h-5 w-5 ${i < lead.interest ? "text-yellow-400 fill-yellow-400" : "text-gray-300 fill-gray-300"}`}
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                ))}
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <PhoneCall className="mr-3 h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">手机号码</p>
                  <p>{lead.phone}</p>
                </div>
              </div>
              
              {lead.email && (
                <div className="flex items-center">
                  <Mail className="mr-3 h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">电子邮箱</p>
                    <p>{lead.email}</p>
                  </div>
                </div>
              )}
              
              <div className="flex items-center">
                <Tag className="mr-3 h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">来源渠道</p>
                  <p>{lead.source}</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <Calendar className="mr-3 h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">获取时间</p>
                  <p>{lead.createdAt}</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <UserCheck className="mr-3 h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">负责人</p>
                  <p>{lead.assignedTo}</p>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">备注</p>
                <p className="text-sm">{lead.notes || "暂无备注"}</p>
              </div>
            </div>
            
            <div className="mt-6 space-y-2">
              <Button className="w-full">
                <Edit className="mr-2 h-4 w-4" />
                编辑信息
              </Button>
              <Button className="w-full" variant="secondary">
                <UserCheck className="mr-2 h-4 w-4" />
                转为会员
              </Button>
            </div>
          </div>
          
          <div className="w-full md:w-2/3">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="followup">跟进记录</TabsTrigger>
                <TabsTrigger value="interests">兴趣偏好</TabsTrigger>
                <TabsTrigger value="activities">活动记录</TabsTrigger>
              </TabsList>
              
              <TabsContent value="followup" className="space-y-4 mt-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium">跟进记录</h3>
                  <Button onClick={() => setShowFollowUpForm(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    添加跟进
                  </Button>
                </div>
                
                {showFollowUpForm && (
                  <Card>
                    <CardHeader>
                      <CardTitle>添加跟进记录</CardTitle>
                      <CardDescription>记录与潜客的沟通内容和后续计划</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <LeadFollowUpForm 
                        leadId={lead.id} 
                        onSuccess={() => setShowFollowUpForm(false)} 
                        onCancel={() => setShowFollowUpForm(false)}
                      />
                    </CardContent>
                  </Card>
                )}
                
                <div className="space-y-4">
                  {followUps.map((followUp) => (
                    <Card key={followUp.id}>
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center">
                            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                            <span>{followUp.date}</span>
                            <Clock className="ml-4 mr-2 h-4 w-4 text-muted-foreground" />
                            <span>{followUp.time}</span>
                          </div>
                          <Badge variant="outline">
                            {followUp.type === "phone" && "电话沟通"}
                            {followUp.type === "wechat" && "微信沟通"}
                            {followUp.type === "visit" && "到店参观"}
                            {followUp.type === "email" && "邮件沟通"}
                          </Badge>
                        </div>
                        
                        <div className="mt-2">
                          <p className="whitespace-pre-line">{followUp.content}</p>
                        </div>
                        
                        <div className="flex justify-between items-center mt-4 text-sm text-muted-foreground">
                          <div>跟进人: {followUp.staff}</div>
                          <div>下次跟进: {followUp.nextFollowUp}</div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="interests" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>兴趣偏好</CardTitle>
                    <CardDescription>记录潜客的兴趣爱好和课程偏好</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">暂无兴趣偏好记录</p>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="activities" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>活动记录</CardTitle>
                    <CardDescription>潜客参与的活动和互动记录</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">暂无活动记录</p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
