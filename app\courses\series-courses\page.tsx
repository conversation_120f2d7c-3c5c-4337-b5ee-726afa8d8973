"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { 
  Calendar, 
  ChevronRight, 
  Clock, 
  Edit, 
  FileText, 
  Filter, 
  Plus, 
  Search, 
  Settings, 
  Users, 
  X 
} from "lucide-react"

import { mockSeriesCourses } from "@/lib/mock/series-courses"
import { SeriesCourse, SeriesCourseStatus } from "@/types/series-courses"

export default function SeriesCoursesPage() {
  const router = useRouter()
  const [searchKeyword, setSearchKeyword] = useState("")
  const [statusFilter, setStatusFilter] = useState<SeriesCourseStatus | "all">("all")
  const [showAutoBookingDialog, setShowAutoBookingDialog] = useState(false)
  const [selectedCourseId, setSelectedCourseId] = useState<string | null>(null)
  
  // 筛选课程
  const filteredCourses = mockSeriesCourses.filter(course => {
    const matchesKeyword = 
      searchKeyword === "" || 
      course.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      course.description.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      course.instructor.toLowerCase().includes(searchKeyword.toLowerCase())
    
    const matchesStatus = statusFilter === "all" || course.status === statusFilter
    
    return matchesKeyword && matchesStatus
  })
  
  // 处理自动约课
  const handleAutoBooking = (courseId: string) => {
    setSelectedCourseId(courseId)
    setShowAutoBookingDialog(true)
  }
  
  // 确认自动约课
  const confirmAutoBooking = () => {
    toast({
      title: "自动约课成功",
      description: "已为所有报名学员自动约课，并发送通知。",
    })
    setShowAutoBookingDialog(false)
  }
  
  // 获取状态标签样式
  const getStatusBadge = (status: SeriesCourseStatus) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline">草稿</Badge>
      case "published":
        return <Badge variant="default">已发布</Badge>
      case "completed":
        return <Badge variant="secondary">已结束</Badge>
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">系列课程管理</h1>
          <p className="text-muted-foreground mt-1">
            管理系列课程、排期和学员报名
          </p>
        </div>
        <Button onClick={() => router.push("/courses/series-courses/create")} className="mt-4 md:mt-0">
          <Plus className="mr-2 h-4 w-4" />
          创建系列课程
        </Button>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索课程名称、描述或教练"
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            className="pl-10 pr-10"
          />
          {searchKeyword && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
              onClick={() => setSearchKeyword("")}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as SeriesCourseStatus | "all")}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="状态筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="draft">草稿</SelectItem>
              <SelectItem value="published">已发布</SelectItem>
              <SelectItem value="completed">已结束</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Tabs defaultValue="grid" className="mb-6">
        <TabsList>
          <TabsTrigger value="grid">卡片视图</TabsTrigger>
          <TabsTrigger value="list">列表视图</TabsTrigger>
        </TabsList>
        
        <TabsContent value="grid" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCourses.map((course) => (
              <Card key={course.id} className="overflow-hidden">
                {course.coverImage && (
                  <div className="h-48 overflow-hidden">
                    <img 
                      src={course.coverImage} 
                      alt={course.name} 
                      className="w-full h-full object-cover transition-transform hover:scale-105"
                    />
                  </div>
                )}
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-xl">{course.name}</CardTitle>
                    {getStatusBadge(course.status)}
                  </div>
                  <CardDescription>{course.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">课程类型</span>
                      <span>{course.courseType}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">主讲教练</span>
                      <span>{course.instructor}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">课时数</span>
                      <span>{course.totalSessions}节</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">学员人数</span>
                      <span>{course.enrolledStudents}/{course.maxStudents}人</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">课程价格</span>
                      <span className="font-medium">¥{course.price}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">课程周期</span>
                      <span>{course.startDate} 至 {course.endDate}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-wrap gap-2">
                  <Button 
                    variant="default" 
                    size="sm"
                    onClick={() => router.push(`/courses/series-courses/${course.id}`)}
                  >
                    详情
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => router.push(`/courses/series-courses/${course.id}/schedule`)}
                  >
                    <Calendar className="mr-1 h-3 w-3" />
                    排期
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => router.push(`/courses/series-courses/${course.id}/enrollments`)}
                  >
                    <Users className="mr-1 h-3 w-3" />
                    报名
                  </Button>
                  {course.status === "published" && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleAutoBooking(course.id)}
                    >
                      <Clock className="mr-1 h-3 w-3" />
                      一键约课
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="list" className="mt-6">
          <div className="border rounded-md overflow-hidden">
            <div className="grid grid-cols-12 bg-muted px-4 py-3 font-medium text-sm">
              <div className="col-span-3">课程名称</div>
              <div className="col-span-2">课程类型</div>
              <div className="col-span-2">主讲教练</div>
              <div className="col-span-1">课时数</div>
              <div className="col-span-1">学员人数</div>
              <div className="col-span-1">状态</div>
              <div className="col-span-2 text-right">操作</div>
            </div>
            
            <div className="divide-y">
              {filteredCourses.map((course) => (
                <div key={course.id} className="grid grid-cols-12 px-4 py-3 hover:bg-accent/50">
                  <div className="col-span-3 flex items-center">
                    <div>
                      <div className="font-medium">{course.name}</div>
                      <div className="text-xs text-muted-foreground">{course.startDate} 至 {course.endDate}</div>
                    </div>
                  </div>
                  <div className="col-span-2 flex items-center">
                    <span>{course.courseType}</span>
                  </div>
                  <div className="col-span-2 flex items-center">
                    <span>{course.instructor}</span>
                  </div>
                  <div className="col-span-1 flex items-center">
                    <span>{course.totalSessions}节</span>
                  </div>
                  <div className="col-span-1 flex items-center">
                    <span>{course.enrolledStudents}/{course.maxStudents}</span>
                  </div>
                  <div className="col-span-1 flex items-center">
                    {getStatusBadge(course.status)}
                  </div>
                  <div className="col-span-2 flex items-center justify-end gap-2">
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => router.push(`/courses/series-courses/${course.id}`)}
                    >
                      <FileText className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => router.push(`/courses/series-courses/${course.id}/edit`)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => router.push(`/courses/series-courses/${course.id}/schedule`)}
                    >
                      <Calendar className="h-4 w-4" />
                    </Button>
                    {course.status === "published" && (
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => handleAutoBooking(course.id)}
                      >
                        <Clock className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>
      </Tabs>
      
      {/* 自动约课确认对话框 */}
      <Dialog open={showAutoBookingDialog} onOpenChange={setShowAutoBookingDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认自动约课</DialogTitle>
            <DialogDescription>
              系统将为所有报名学员自动约课，并发送通知。
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>
              您确定要为
              <span className="font-medium">
                {selectedCourseId && mockSeriesCourses.find(c => c.id === selectedCourseId)?.name}
              </span>
              的所有报名学员自动约课吗？
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              此操作将为所有报名学员自动约课，如果学员已经约过某节课，将不会重复约课。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAutoBookingDialog(false)}>
              取消
            </Button>
            <Button onClick={confirmAutoBooking}>
              确认约课
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
