"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { Label } from "@/components/ui/label"
import { 
  Store, Plus, Users, MapPin, Globe, BarChart3, CalendarDays, Settings, 
  BookOpen, CreditCard, UserCog, Check, X, Filter, Search, FileSpreadsheet, 
  Clock, Building, UserPlus, Download, Upload, Pencil, LinkIcon, Unlink
} from "lucide-react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "@/components/ui/use-toast"
import Link from "next/link"

// 连锁数据接口
interface ChainArea {
  id: string | number;
  name: string;
  manager: string;
  storesCount: number;
  region: string;
  createdAt: string;
  status: 'active' | 'inactive';
  description: string;
}

// 门店迁移记录接口
interface StoreTransfer {
  id: string | number;
  storeName: string;
  fromArea: string;
  toArea: string;
  transferDate: string;
  reason: string;
  status: 'completed' | 'pending' | 'cancelled';
}

// 门店接口
interface StoreData {
  id: string | number;
  name: string;
  address: string;
  phone: string;
  managerName: string;
  area?: string;
  type?: 'flagship' | 'standard' | 'mini';
  status: 'active' | 'inactive' | 'pending';
}

// 课程类型接口
interface CourseType {
  id: string | number;
  name: string;
  description: string;
  storesCount: number;
  status: 'active' | 'inactive';
  isChainSync: boolean; // 是否连锁同步
  storeIds: (string | number)[]; // 应用的门店ID列表
}

// 会员卡接口
interface MemberCard {
  id: string | number;
  name: string;
  price: number;
  validDays: number;
  status: 'active' | 'inactive';
  description: string;
  isChainSync: boolean; // 是否连锁同步
  storeIds: (string | number)[]; // 应用的门店ID列表
}

// 员工接口
interface StaffMember {
  id: string | number;
  name: string;
  position: string;
  phone: string;
  email: string;
  status: 'active' | 'inactive';
  isChainSync: boolean; // 是否连锁同步
  storeIds: (string | number)[]; // 可工作的门店ID列表
}

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, { message: "区域名称至少需要2个字符" }).max(50, { message: "区域名称不能超过50个字符" }),
  manager: z.string().min(2, { message: "负责人姓名至少需要2个字符" }).max(20, { message: "负责人姓名不能超过20个字符" }),
  region: z.string().min(2, { message: "所属区域至少需要2个字符" }).max(50, { message: "所属区域不能超过50个字符" }),
  description: z.string().max(500, { message: "描述不能超过500个字符" }).optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function ChainManagementPage() {
  // 连锁区域数据
  const [areas, setAreas] = useState<ChainArea[]>([
    {
      id: 1,
      name: "华北区",
      manager: "王经理",
      storesCount: 8,
      region: "北京、天津、河北、内蒙古",
      createdAt: "2023-01-01",
      status: 'active',
      description: "覆盖华北地区主要城市的连锁门店管理"
    },
    {
      id: 2,
      name: "华东区",
      manager: "张经理",
      storesCount: 12,
      region: "上海、江苏、浙江、安徽",
      createdAt: "2023-01-15",
      status: 'active',
      description: "覆盖华东地区核心城市和发达地区"
    },
    {
      id: 3,
      name: "华南区",
      manager: "李经理",
      storesCount: 7,
      region: "广东、广西、海南、福建",
      createdAt: "2023-02-01",
      status: 'active',
      description: "负责华南地区门店的拓展和管理"
    },
    {
      id: 4,
      name: "西南区",
      manager: "赵经理",
      storesCount: 5,
      region: "四川、重庆、云南、贵州",
      createdAt: "2023-03-01",
      status: 'active',
      description: "西南地区连锁门店运营和管理"
    },
    {
      id: 5,
      name: "国际区",
      manager: "吴经理",
      storesCount: 2,
      region: "东南亚",
      createdAt: "2023-06-01",
      status: 'inactive',
      description: "负责国际市场拓展和门店管理"
    }
  ]);
  
  // 门店迁移记录
  const [transfers, setTransfers] = useState<StoreTransfer[]>([
    {
      id: 1,
      storeName: "北京朝阳门店",
      fromArea: "华北区",
      toArea: "东北区",
      transferDate: "2023-05-15",
      reason: "行政区划调整",
      status: 'completed'
    },
    {
      id: 2,
      storeName: "上海静安门店",
      fromArea: "华东区",
      toArea: "高端门店区",
      transferDate: "2023-07-20",
      reason: "品牌升级调整",
      status: 'completed'
    },
    {
      id: 3,
      storeName: "广州天河门店",
      fromArea: "华南区",
      toArea: "华东区",
      transferDate: "2023-08-10",
      reason: "内部资源优化",
      status: 'pending'
    }
  ]);

  // 门店数据
  const [stores, setStores] = useState<StoreData[]>([
    { id: 1, name: "静心瑜伽旗舰店", address: "北京市朝阳区建国路88号", phone: "13800138001", managerName: "张晓华", area: "华北区", type: "flagship", status: "active" },
    { id: 2, name: "静心瑜伽国贸店", address: "北京市朝阳区国贸中心12号楼", phone: "13900139002", managerName: "李明", area: "华北区", type: "standard", status: "active" },
    { id: 3, name: "静心瑜伽望京店", address: "北京市朝阳区望京SOHO T1", phone: "13800138003", managerName: "王芳", area: "华北区", type: "standard", status: "active" },
    { id: 4, name: "静心瑜伽上地店", address: "北京市海淀区上地信息路甲28号", phone: "13800138004", managerName: "赵静", area: "华北区", type: "standard", status: "active" },
    { id: 5, name: "静心瑜伽五道口店", address: "北京市海淀区五道口华清商务会馆", phone: "13900139005", managerName: "刘伟", area: "华北区", type: "mini", status: "inactive" },
    { id: 6, name: "静心瑜伽上海中心店", address: "上海市浦东新区陆家嘴环路1000号", phone: "13800138006", managerName: "陈静", area: "华东区", type: "flagship", status: "active" },
    { id: 7, name: "静心瑜伽徐家汇店", address: "上海市徐汇区虹桥路1号港汇广场", phone: "13900139007", managerName: "林峰", area: "华东区", type: "standard", status: "active" },
    { id: 8, name: "静心瑜伽广州天河店", address: "广州市天河区天河路385号", phone: "13800138008", managerName: "黄丽", area: "华南区", type: "standard", status: "active" }
  ]);
  
  // 课程类型数据
  const [courseTypes, setCourseTypes] = useState<CourseType[]>([
    {
      id: 1,
      name: "瑜伽基础课程",
      description: "适合初学者的基础瑜伽课程",
      storesCount: 12,
      status: "active",
      isChainSync: true,
      storeIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    },
    {
      id: 2,
      name: "高级瑜伽课程",
      description: "针对有基础的学员的进阶课程",
      storesCount: 8,
      status: "active",
      isChainSync: false,
      storeIds: [1, 2, 4, 5, 8, 9, 10, 11]
    },
    {
      id: 3,
      name: "流瑜伽课程",
      description: "连贯流畅的瑜伽练习",
      storesCount: 15,
      status: "active",
      isChainSync: true,
      storeIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
    },
    {
      id: 4,
      name: "孕产瑜伽课程",
      description: "专为孕妇和产后恢复设计的瑜伽课程",
      storesCount: 5,
      status: "active",
      isChainSync: true,
      storeIds: [1, 4, 6, 8, 10]
    },
    {
      id: 5,
      name: "热瑜伽课程",
      description: "在高温环境下进行的瑜伽练习",
      storesCount: 7,
      status: "inactive",
      isChainSync: false,
      storeIds: [1, 2, 5, 8, 9, 12, 15]
    }
  ]);
  
  // 会员卡数据
  const [memberCards, setMemberCards] = useState<MemberCard[]>([
    {
      id: 1,
      name: "月卡会员",
      price: 299,
      validDays: 30,
      status: "active",
      description: "30天有效期，不限次数使用",
      isChainSync: true,
      storeIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]
    },
    {
      id: 2,
      name: "季卡会员",
      price: 799,
      validDays: 90,
      status: "active",
      description: "90天有效期，不限次数使用",
      isChainSync: true,
      storeIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]
    },
    {
      id: 3,
      name: "年卡会员",
      price: 2599,
      validDays: 365,
      status: "active",
      description: "365天有效期，不限次数使用",
      isChainSync: true,
      storeIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]
    },
    {
      id: 4,
      name: "体验卡",
      price: 99,
      validDays: 7,
      status: "active",
      description: "7天有效期，限新会员购买",
      isChainSync: false,
      storeIds: [1, 4, 8, 12, 16]
    },
    {
      id: 5,
      name: "私教套餐卡",
      price: 1999,
      validDays: 180,
      status: "active",
      description: "包含10次私教课程",
      isChainSync: false,
      storeIds: [1, 2, 4, 8, 12]
    }
  ]);
  
  // 员工数据
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([
    {
      id: 1,
      name: "李教练",
      position: "高级瑜伽教练",
      phone: "13800138001",
      email: "<EMAIL>",
      status: "active",
      isChainSync: false,
      storeIds: [1, 2, 3, 4, 5]
    },
    {
      id: 2,
      name: "王教练",
      position: "瑜伽教练",
      phone: "13900139002",
      email: "<EMAIL>",
      status: "active",
      isChainSync: true,
      storeIds: [1, 3, 5, 7, 9, 11, 13, 15, 17]
    },
    {
      id: 3,
      name: "张经理",
      position: "区域经理",
      phone: "13800138003",
      email: "<EMAIL>",
      status: "active",
      isChainSync: true,
      storeIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]
    },
    {
      id: 4,
      name: "赵教练",
      position: "资深瑜伽教练",
      phone: "13900139004",
      email: "<EMAIL>",
      status: "active",
      isChainSync: true,
      storeIds: [2, 4, 6, 8, 10, 12, 14, 16, 18]
    },
    {
      id: 5,
      name: "刘助教",
      position: "瑜伽助教",
      phone: "13800138005",
      email: "<EMAIL>",
      status: "inactive",
      isChainSync: false,
      storeIds: [1, 5, 10, 15]
    }
  ]);
  
  const [activeTab, setActiveTab] = useState<string>("areas");
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [selectedArea, setSelectedArea] = useState<ChainArea | null>(null);
  const [openStoreSelectDialog, setOpenStoreSelectDialog] = useState(false);
  const [selectedStores, setSelectedStores] = useState<(string | number)[]>([]);
  const [currentOperation, setCurrentOperation] = useState<'add' | 'edit' | null>(null);
  const [selectedItem, setSelectedItem] = useState<any>(null); // 当前选中的项目（课程类型/会员卡/员工）
  const [itemType, setItemType] = useState<'course' | 'card' | 'staff' | null>(null);

  // 创建表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      manager: "",
      region: "",
      description: ""
    }
  });

  // 处理添加区域
  const handleAddArea = (values: FormValues) => {
    try {
      // 创建新区域对象
      const newArea: ChainArea = {
        id: Date.now(),
        name: values.name,
        manager: values.manager,
        storesCount: 0,
        region: values.region,
        description: values.description || "",
        createdAt: new Date().toISOString().split('T')[0],
        status: 'active'
      };

      // 更新区域列表
      const updatedAreas = [...areas, newArea];
      setAreas(updatedAreas);
      
      toast({
        title: "区域添加成功",
        description: `${values.name} 已成功添加`,
      });

      // 关闭对话框并重置表单
      setOpenAddDialog(false);
      form.reset();
    } catch (error) {
      console.error('添加区域失败:', error);
      toast({
        title: "添加区域失败",
        description: "请稍后再试",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">连锁管理</h1>
          <p className="text-muted-foreground">管理连锁门店和区域</p>
        </div>
        <Button onClick={() => {
          setSelectedArea(null);
          setOpenAddDialog(true);
        }}>
          <Plus className="mr-2 h-4 w-4" />
          添加区域
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">区域数量</CardTitle>
              <CardDescription>连锁区域总数</CardDescription>
            </div>
            <div className="text-2xl font-bold">{areas.length}</div>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">总门店数量</CardTitle>
              <CardDescription>所有连锁门店数量</CardDescription>
            </div>
            <div className="text-2xl font-bold">{areas.reduce((acc, area) => acc + area.storesCount, 0)}</div>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">活跃区域</CardTitle>
              <CardDescription>状态为活跃的区域</CardDescription>
            </div>
            <div className="text-2xl font-bold">{areas.filter(area => area.status === 'active').length}</div>
          </CardHeader>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium">区域覆盖率</CardTitle>
              <CardDescription>门店覆盖区域比例</CardDescription>
            </div>
            <div className="text-2xl font-bold">92%</div>
          </CardHeader>
        </Card>
      </div>

      <Tabs defaultValue="areas" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-6 w-full">
          <TabsTrigger value="areas">区域管理</TabsTrigger>
          <TabsTrigger value="stores">门店调整</TabsTrigger>
          <TabsTrigger value="courses">课程类型管理</TabsTrigger>
          <TabsTrigger value="members">会员卡管理</TabsTrigger>
          <TabsTrigger value="staff">员工管理</TabsTrigger>
          <TabsTrigger value="settings">
            <Link href="/stores/chain/sync" className="flex items-center">
              连锁设置
            </Link>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="areas">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>区域管理</CardTitle>
                <CardDescription>管理连锁区域和负责人</CardDescription>
              </div>
              <div className="flex space-x-2">
                <Input placeholder="搜索区域..." className="w-64" />
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  筛选
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>区域名称</TableHead>
                    <TableHead>负责人</TableHead>
                    <TableHead>所属区域</TableHead>
                    <TableHead>门店数量</TableHead>
                    <TableHead>创建日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {areas.map((area) => (
                    <TableRow key={area.id}>
                      <TableCell className="font-medium">{area.name}</TableCell>
                      <TableCell>{area.manager}</TableCell>
                      <TableCell>{area.region}</TableCell>
                      <TableCell>{area.storesCount} 家门店</TableCell>
                      <TableCell>{area.createdAt}</TableCell>
                      <TableCell>
                        <Badge variant={area.status === "active" ? "default" : "secondary"}>
                          {area.status === "active" ? "活跃" : "停用"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" className="mr-1" onClick={() => {
                          setSelectedArea(area);
                          form.reset({
                            name: area.name,
                            manager: area.manager,
                            region: area.region,
                            description: area.description,
                          });
                          setOpenAddDialog(true);
                        }}>
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          {area.status === "active" ? 
                            <X className="h-4 w-4 text-red-500" /> : 
                            <Check className="h-4 w-4 text-green-500" />
                          }
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stores">
          <Card>
            <CardHeader>
              <CardTitle>门店区域调整记录</CardTitle>
              <CardDescription>查看门店在不同区域间的调整历史</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>门店名称</TableHead>
                    <TableHead>原区域</TableHead>
                    <TableHead>目标区域</TableHead>
                    <TableHead>调整日期</TableHead>
                    <TableHead>调整原因</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transfers.map((transfer) => (
                    <TableRow key={transfer.id}>
                      <TableCell className="font-medium">{transfer.storeName}</TableCell>
                      <TableCell>{transfer.fromArea}</TableCell>
                      <TableCell>{transfer.toArea}</TableCell>
                      <TableCell>{transfer.transferDate}</TableCell>
                      <TableCell>{transfer.reason}</TableCell>
                      <TableCell>
                        <Badge variant={
                          transfer.status === 'completed' ? "default" : 
                          transfer.status === 'pending' ? "secondary" : "destructive"
                        }>
                          {transfer.status === 'completed' ? '已完成' : 
                           transfer.status === 'pending' ? '处理中' : '已取消'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">详情</Button>
                        {transfer.status === 'pending' && (
                          <Button variant="ghost" size="sm">审批</Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="courses">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>课程类型管理</CardTitle>
                <CardDescription>管理连锁门店的课程类型设置</CardDescription>
              </div>
              <Button onClick={() => {
                setCurrentOperation('add');
                setItemType('course');
                setSelectedStores([]);
                setOpenStoreSelectDialog(true);
              }}>
                <Plus className="mr-2 h-4 w-4" />
                添加课程类型
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>课程名称</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>门店数</TableHead>
                    <TableHead className="w-[100px]">状态</TableHead>
                    <TableHead className="w-[120px]">连锁同步</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {courseTypes.map((courseType) => (
                    <TableRow key={courseType.id}>
                      <TableCell className="font-medium">{courseType.name}</TableCell>
                      <TableCell>{courseType.description}</TableCell>
                      <TableCell>{courseType.storesCount} 家门店</TableCell>
                      <TableCell>
                        <Badge variant={courseType.status === "active" ? "default" : "secondary"}>
                          {courseType.status === "active" ? "启用" : "停用"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={courseType.isChainSync ? "default" : "outline"} className={courseType.isChainSync ? "bg-green-500 hover:bg-green-600" : ""}>
                          {courseType.isChainSync ? "已同步" : "未同步"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" className="mr-1" onClick={() => {
                          setSelectedItem(courseType);
                          setSelectedStores(courseType.storeIds);
                          setCurrentOperation('edit');
                          setItemType('course');
                          setOpenStoreSelectDialog(true);
                        }}>
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          {courseType.isChainSync 
                            ? <Unlink className="h-4 w-4 text-red-500" /> 
                            : <LinkIcon className="h-4 w-4 text-green-500" />
                          }
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>会员卡管理</CardTitle>
                <CardDescription>管理连锁门店的会员卡设置</CardDescription>
              </div>
              <Button onClick={() => {
                setCurrentOperation('add');
                setItemType('card');
                setSelectedStores([]);
                setOpenStoreSelectDialog(true);
              }}>
                <Plus className="mr-2 h-4 w-4" />
                添加会员卡
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>会员卡名称</TableHead>
                    <TableHead>价格 (元)</TableHead>
                    <TableHead>有效期 (天)</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead className="w-[100px]">状态</TableHead>
                    <TableHead className="w-[120px]">连锁同步</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {memberCards.map((card) => (
                    <TableRow key={card.id}>
                      <TableCell className="font-medium">{card.name}</TableCell>
                      <TableCell>¥{card.price}</TableCell>
                      <TableCell>{card.validDays}</TableCell>
                      <TableCell>{card.description}</TableCell>
                      <TableCell>
                        <Badge variant={card.status === "active" ? "default" : "secondary"}>
                          {card.status === "active" ? "启用" : "停用"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={card.isChainSync ? "default" : "outline"} className={card.isChainSync ? "bg-green-500 hover:bg-green-600" : ""}>
                          {card.isChainSync ? "已同步" : "未同步"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" className="mr-1" onClick={() => {
                          setSelectedItem(card);
                          setSelectedStores(card.storeIds);
                          setCurrentOperation('edit');
                          setItemType('card');
                          setOpenStoreSelectDialog(true);
                        }}>
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          {card.isChainSync 
                            ? <Unlink className="h-4 w-4 text-red-500" /> 
                            : <LinkIcon className="h-4 w-4 text-green-500" />
                          }
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="staff">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>员工管理</CardTitle>
                <CardDescription>管理连锁门店的员工设置</CardDescription>
              </div>
              <Button onClick={() => {
                setCurrentOperation('add');
                setItemType('staff');
                setSelectedStores([]);
                setOpenStoreSelectDialog(true);
              }}>
                <Plus className="mr-2 h-4 w-4" />
                添加员工
              </Button>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>姓名</TableHead>
                    <TableHead>职位</TableHead>
                    <TableHead>联系电话</TableHead>
                    <TableHead>邮箱</TableHead>
                    <TableHead className="w-[100px]">状态</TableHead>
                    <TableHead className="w-[120px]">连锁同步</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {staffMembers.map((staff) => (
                    <TableRow key={staff.id}>
                      <TableCell className="font-medium">{staff.name}</TableCell>
                      <TableCell>{staff.position}</TableCell>
                      <TableCell>{staff.phone}</TableCell>
                      <TableCell>{staff.email}</TableCell>
                      <TableCell>
                        <Badge variant={staff.status === "active" ? "default" : "secondary"}>
                          {staff.status === "active" ? "在职" : "离职"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={staff.isChainSync ? "default" : "outline"} className={staff.isChainSync ? "bg-green-500 hover:bg-green-600" : ""}>
                          {staff.isChainSync ? "已同步" : "未同步"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" className="mr-1" onClick={() => {
                          setSelectedItem(staff);
                          setSelectedStores(staff.storeIds);
                          setCurrentOperation('edit');
                          setItemType('staff');
                          setOpenStoreSelectDialog(true);
                        }}>
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon">
                          {staff.isChainSync 
                            ? <Unlink className="h-4 w-4 text-red-500" /> 
                            : <LinkIcon className="h-4 w-4 text-green-500" />
                          }
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>连锁同步设置</CardTitle>
              <CardDescription>管理连锁门店数据同步设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between border p-4 rounded-lg">
                  <div className="space-y-0.5">
                    <h3 className="font-medium">课程类型同步</h3>
                    <p className="text-sm text-muted-foreground">在所有门店同步课程类型设置</p>
                  </div>
                  <Button>配置同步</Button>
                </div>
                
                <div className="flex items-center justify-between border p-4 rounded-lg">
                  <div className="space-y-0.5">
                    <h3 className="font-medium">会员卡同步</h3>
                    <p className="text-sm text-muted-foreground">在所有门店同步会员卡设置</p>
                  </div>
                  <Button>配置同步</Button>
                </div>
                
                <div className="flex items-center justify-between border p-4 rounded-lg">
                  <div className="space-y-0.5">
                    <h3 className="font-medium">员工同步</h3>
                    <p className="text-sm text-muted-foreground">在所有门店同步员工设置</p>
                  </div>
                  <Button>配置同步</Button>
                </div>
                
                <div className="flex items-center justify-between border p-4 rounded-lg">
                  <div className="space-y-0.5">
                    <h3 className="font-medium">自动同步设置</h3>
                    <p className="text-sm text-muted-foreground">配置自动同步规则和频率</p>
                  </div>
                  <Button>配置规则</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={openAddDialog} onOpenChange={setOpenAddDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>添加连锁区域</DialogTitle>
            <DialogDescription>
              创建新的连锁门店管理区域，设置区域负责人。
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddArea)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>区域名称</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：华北区" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="manager"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>区域负责人</FormLabel>
                    <FormControl>
                      <Input placeholder="负责人姓名" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="region"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>覆盖地区</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：北京、天津、河北" {...field} />
                    </FormControl>
                    <FormDescription>
                      列出该区域管理的主要城市或省份
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>区域描述</FormLabel>
                    <FormControl>
                      <Textarea placeholder="请输入区域管理职责和说明" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setOpenAddDialog(false)}>取消</Button>
                <Button type="submit">创建区域</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <Dialog open={openStoreSelectDialog} onOpenChange={setOpenStoreSelectDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {currentOperation === 'add' ? '添加' : '编辑'} 
              {itemType === 'course' ? '课程类型' : itemType === 'card' ? '会员卡' : '员工'}
            </DialogTitle>
            <DialogDescription>
              选择应用的门店
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">名称</Label>
                <Input id="name" defaultValue={selectedItem?.name || ''} />
              </div>
              
              {itemType === 'card' && (
                <div className="space-y-2">
                  <Label htmlFor="price">价格 (元)</Label>
                  <Input id="price" type="number" defaultValue={selectedItem?.price || ''} />
                </div>
              )}
              
              {itemType === 'staff' && (
                <div className="space-y-2">
                  <Label htmlFor="position">职位</Label>
                  <Input id="position" defaultValue={selectedItem?.position || ''} />
                </div>
              )}
              
              <div className="space-y-2 col-span-2">
                <Label htmlFor="description">描述</Label>
                <Textarea id="description" defaultValue={selectedItem?.description || ''} />
              </div>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>选择门店</Label>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" onClick={() => setSelectedStores([])}>
                    清除选择
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => {
                    setSelectedStores(stores.map(store => store.id))
                  }}>
                    全选
                  </Button>
                </div>
              </div>
              
              <ScrollArea className="h-[200px] border rounded-md p-2">
                <div className="grid grid-cols-2 gap-2">
                  {stores.map((store) => (
                    <div key={store.id} className="flex items-center space-x-2">
                      <Checkbox 
                        id={`store-${store.id}`} 
                        checked={selectedStores.includes(store.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedStores([...selectedStores, store.id])
                          } else {
                            setSelectedStores(selectedStores.filter(id => id !== store.id))
                          }
                        }}
                      />
                      <Label 
                        htmlFor={`store-${store.id}`}
                        className="text-sm cursor-pointer flex-1 flex items-center justify-between"
                      >
                        <span>{store.name}</span>
                        <Badge variant="outline" className="text-xs">{store.area}</Badge>
                      </Label>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              <div className="text-sm text-muted-foreground">
                已选择 {selectedStores.length} 家门店 
                {selectedStores.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {selectedStores.length === stores.length ? '全部门店' : '部分门店'}
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox id="chain-sync" defaultChecked={selectedItem?.isChainSync} />
              <Label htmlFor="chain-sync">启用连锁同步</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenStoreSelectDialog(false)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: `${currentOperation === 'add' ? '添加' : '更新'}成功`,
                description: `${itemType === 'course' ? '课程类型' : itemType === 'card' ? '会员卡' : '员工'}已${currentOperation === 'add' ? '添加' : '更新'}`,
              })
              setOpenStoreSelectDialog(false)
            }}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 