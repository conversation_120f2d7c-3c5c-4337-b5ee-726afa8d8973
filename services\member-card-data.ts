// 会员卡数据服务
// 这个服务提供会员卡数据，确保所有组件使用相同的数据源

// 定义会员卡类型
export interface MemberCard {
  id: string;
  memberName: string;
  memberPhone: string;
  memberId: string;
  cardType: string;
  cardTypeBadge: string;
  cardTypeColor: string;
  startDate: string;
  endDate: string | null;
  status: string;
  remainingDays: number | null;
  remainingCount: number | null;
  remainingValue: number | null;
  price: number;
  actualPrice: number;
  paymentMethod: string;
  activationDate: string;
  lastUsedDate: string;
  usageCount: number;
  isTrialCard: boolean;
  isAutoRenew: boolean;
  tags: string[];
  notes: string;
  createdAt: string;
  createdBy: string;
  // 详情页面需要的额外字段
  cardName?: string;
  cardCategory?: "time" | "count" | "value";
  memberAvatar?: string;
  memberLevel?: string;
  memberJoinDate?: string;
  memberTotalSpent?: string;
  memberVisitCount?: number;
  memberLastVisit?: string;
  usageRecords?: any[];
  consumptionRecords?: any[];
  paymentRecords?: any[];
  operationRecords?: any[];
  totalDays?: number;
  usedDays?: number;
  totalCount?: number;
  usedCount?: number;
}

// 模拟会员卡数据
export const memberCards: MemberCard[] = [
  {
    id: "MC001",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "瑜伽年卡",
    cardName: "瑜伽年卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    status: "有效",
    remainingDays: 120,
    remainingCount: null,
    remainingValue: 2000,
    price: 3600,
    actualPrice: 3200,
    paymentMethod: "微信支付",
    activationDate: "2023-01-01",
    lastUsedDate: "2023-09-15",
    usageCount: 45,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "老客户"],
    notes: "会员续卡，享受8.9折优惠",
    createdAt: "2022-12-25",
    createdBy: "管理员",
    memberAvatar: "/avatars/01.png",
    memberLevel: "金卡会员",
    memberJoinDate: "2020-05-15",
    memberTotalSpent: "¥12,500",
    memberVisitCount: 120,
    memberLastVisit: "2023-09-15",
    totalDays: 365,
    usedDays: 245,
    usageRecords: [
      { id: "UR001", date: "2023-09-15", course: "高级瑜伽", instructor: "王教练", duration: "90分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-10", course: "普拉提", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-09-05", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-15",
        type: "class",
        itemName: "高级瑜伽",
        originalPrice: 200,
        consumedValue: 120,
        remainingValue: 2400,
        instructor: "王教练",
        location: "一号教室",
        notes: "会员提前15分钟到场"
      },
      {
        id: "CR002",
        date: "2023-09-10",
        type: "class",
        itemName: "普拉提",
        originalPrice: 180,
        consumedValue: 100,
        remainingValue: 2520,
        instructor: "李教练",
        location: "二号教室"
      },
      {
        id: "CR003",
        date: "2023-09-05",
        type: "class",
        itemName: "瑜伽入门",
        originalPrice: 150,
        consumedValue: 80,
        remainingValue: 2620,
        instructor: "张教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2022-12-25", type: "购卡", amount: "¥3200", method: "微信支付", status: "已完成", operator: "管理员" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-09-20",
        operation: "解冻会员卡",
        operator: "李经理",
        note: "会员请假结束，恢复正常使用"
      },
      {
        id: "OR002",
        date: "2023-08-15",
        operation: "冻结会员卡",
        operator: "王前台",
        note: "会员请假，暂停卡片使用"
      }
    ]
  },
  {
    id: "MC002",
    memberName: "李四",
    memberPhone: "13800138002",
    memberId: "M002",
    cardType: "瑜伽季卡",
    cardName: "瑜伽季卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#2196F3",
    startDate: "2023-03-01",
    endDate: "2023-05-31",
    status: "已过期",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 1200,
    actualPrice: 1200,
    paymentMethod: "现金",
    activationDate: "2023-03-01",
    lastUsedDate: "2023-05-30",
    usageCount: 28,
    isTrialCard: false,
    isAutoRenew: false,
    tags: [],
    notes: "",
    createdAt: "2023-02-28",
    createdBy: "前台",
    memberAvatar: "/avatars/02.png",
    memberLevel: "银卡会员",
    memberJoinDate: "2021-06-15",
    memberTotalSpent: "¥8,500",
    memberVisitCount: 45,
    memberLastVisit: "2023-09-30",
    totalDays: 90,
    usedDays: 90,
    usageRecords: [
      { id: "UR001", date: "2023-05-30", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-05-15", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-05-01", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-05-30",
        type: "class",
        itemName: "瑜伽基础课",
        originalPrice: 150,
        consumedValue: 100,
        remainingValue: 0,
        instructor: "李教练",
        location: "一号教室",
        notes: "会员提前15分钟到场"
      },
      {
        id: "CR002",
        date: "2023-05-15",
        type: "class",
        itemName: "瑜伽基础课",
        originalPrice: 150,
        consumedValue: 100,
        remainingValue: 100,
        instructor: "李教练",
        location: "一号教室"
      },
      {
        id: "CR003",
        date: "2023-05-01",
        type: "class",
        itemName: "瑜伽基础课",
        originalPrice: 150,
        consumedValue: 100,
        remainingValue: 200,
        instructor: "李教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-02-28", type: "购卡", amount: "¥1200", method: "现金", status: "已完成", operator: "前台" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-05-31",
        operation: "会员卡过期",
        operator: "系统",
        note: "瑜伽季卡自动过期"
      },
      {
        id: "OR002",
        date: "2023-02-28",
        operation: "创建会员卡",
        operator: "前台",
        note: "新购瑜伽季卡"
      }
    ]
  },
  {
    id: "MC003",
    memberName: "王五",
    memberPhone: "13800138003",
    memberId: "M003",
    cardType: "私教次卡",
    cardName: "私教次卡",
    cardCategory: "count",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-06-01",
    endDate: "2024-05-31",
    status: "冻结",
    remainingDays: 240,
    remainingCount: 15,
    remainingValue: null,
    price: 3000,
    actualPrice: 2800,
    paymentMethod: "支付宝",
    activationDate: "2023-06-01",
    lastUsedDate: "2023-08-15",
    usageCount: 5,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "私教"],
    notes: "会员因个人原因申请冻结卡片",
    createdAt: "2023-05-28",
    createdBy: "销售",
    memberAvatar: "/avatars/03.png",
    memberLevel: "金卡会员",
    memberJoinDate: "2022-01-10",
    memberTotalSpent: "¥9,800",
    memberVisitCount: 68,
    memberLastVisit: "2023-08-15",
    totalDays: 365,
    usedDays: 75,
    totalCount: 20,
    usedCount: 5,
    usageRecords: [
      { id: "UR001", date: "2023-08-15", course: "私教课", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-08-01", course: "私教课", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-07-15", course: "私教课", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-08-15",
        type: "class",
        itemName: "私教课",
        originalPrice: 300,
        consumedValue: 280,
        remainingValue: 2240,
        instructor: "张教练",
        location: "私教区",
        notes: "会员要求加强核心训练"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-05-28", type: "购卡", amount: "¥2800", method: "支付宝", status: "已完成", operator: "销售" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-08-20",
        operation: "冻结会员卡",
        operator: "王经理",
        note: "会员出差，申请冻结3个月"
      },
      {
        id: "OR002",
        date: "2023-06-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC004",
    memberName: "赵六",
    memberPhone: "13800138004",
    memberId: "M004",
    cardType: "瑜伽储值卡",
    cardName: "瑜伽储值卡",
    cardCategory: "value",
    cardTypeBadge: "储值卡",
    cardTypeColor: "#FF9800",
    startDate: "2023-01-15",
    endDate: null,
    status: "有效",
    remainingDays: null,
    remainingCount: null,
    remainingValue: 3500,
    price: 5000,
    actualPrice: 5000,
    paymentMethod: "银行转账",
    activationDate: "2023-01-15",
    lastUsedDate: "2023-09-20",
    usageCount: 12,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "高消费"],
    notes: "充值5000元，赠送1000元",
    createdAt: "2023-01-10",
    createdBy: "管理员",
    memberAvatar: "/avatars/04.png",
    memberLevel: "钻石会员",
    memberJoinDate: "2019-05-20",
    memberTotalSpent: "¥15,000",
    memberVisitCount: 150,
    memberLastVisit: "2023-09-20",
    usageRecords: [
      { id: "UR001", date: "2023-09-20", course: "高级瑜伽", instructor: "王教练", duration: "90分钟", consumption: "¥200", status: "已完成" },
      { id: "UR002", date: "2023-09-10", course: "普拉提", instructor: "李教练", duration: "60分钟", consumption: "¥150", status: "已完成" },
      { id: "UR003", date: "2023-09-01", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "¥150", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-20",
        type: "class",
        itemName: "高级瑜伽",
        originalPrice: 200,
        consumedValue: 200,
        remainingValue: 3500,
        instructor: "王教练",
        location: "一号教室",
        notes: "会员要求靠近教练"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-01-10", type: "充值", amount: "¥5000", method: "银行转账", status: "已完成", operator: "管理员" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-01-15",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC005",
    memberName: "钱七",
    memberPhone: "13800138005",
    memberId: "M005",
    cardType: "瑜伽体验卡",
    cardName: "瑜伽体验卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#E91E63",
    startDate: "2023-09-01",
    endDate: "2023-09-15",
    status: "已过期",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 199,
    actualPrice: 99,
    paymentMethod: "微信支付",
    activationDate: "2023-09-01",
    lastUsedDate: "2023-09-10",
    usageCount: 3,
    isTrialCard: true,
    isAutoRenew: false,
    tags: ["新客户", "体验卡"],
    notes: "新客户体验价",
    createdAt: "2023-08-30",
    createdBy: "前台",
    memberAvatar: "/avatars/05.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-08-30",
    memberTotalSpent: "¥99",
    memberVisitCount: 3,
    memberLastVisit: "2023-09-10",
    totalDays: 15,
    usedDays: 15,
    usageRecords: [
      { id: "UR001", date: "2023-09-10", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-05", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-09-01", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-10",
        type: "class",
        itemName: "瑜伽入门",
        originalPrice: 150,
        consumedValue: 33,
        remainingValue: 0,
        instructor: "张教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-08-30", type: "购卡", amount: "¥99", method: "微信支付", status: "已完成", operator: "前台" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-09-15",
        operation: "会员卡过期",
        operator: "系统",
        note: "体验卡自动过期"
      },
      {
        id: "OR002",
        date: "2023-09-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC006",
    memberName: "孙八",
    memberPhone: "13800138006",
    memberId: "M006",
    cardType: "瑜伽月卡",
    cardName: "瑜伽月卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#00BCD4",
    startDate: "2023-09-01",
    endDate: "2023-09-30",
    status: "请假中",
    remainingDays: 10,
    remainingCount: null,
    remainingValue: null,
    price: 450,
    actualPrice: 450,
    paymentMethod: "支付宝",
    activationDate: "2023-09-01",
    lastUsedDate: "2023-09-15",
    usageCount: 5,
    isTrialCard: false,
    isAutoRenew: true,
    tags: ["新客户"],
    notes: "会员因病请假",
    createdAt: "2023-08-30",
    createdBy: "前台",
    memberAvatar: "/avatars/06.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-08-30",
    memberTotalSpent: "¥450",
    memberVisitCount: 5,
    memberLastVisit: "2023-09-15",
    totalDays: 30,
    usedDays: 15,
    usageRecords: [
      { id: "UR001", date: "2023-09-15", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-10", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-09-05", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-15",
        type: "class",
        itemName: "瑜伽基础课",
        originalPrice: 150,
        consumedValue: 75,
        remainingValue: 225,
        instructor: "李教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-08-30", type: "购卡", amount: "¥450", method: "支付宝", status: "已完成", operator: "前台" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-09-16",
        operation: "会员卡请假",
        operator: "王前台",
        note: "会员因病请假，预计10天"
      },
      {
        id: "OR002",
        date: "2023-09-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC007",
    memberName: "周九",
    memberPhone: "13800138007",
    memberId: "M007",
    cardType: "私教次卡",
    cardName: "私教次卡",
    cardCategory: "count",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-08-01",
    endDate: "2024-07-31",
    status: "未激活",
    remainingDays: 335,
    remainingCount: 20,
    remainingValue: null,
    price: 4000,
    actualPrice: 3800,
    paymentMethod: "微信支付",
    activationDate: null,
    lastUsedDate: null,
    usageCount: 0,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["新客户", "私教"],
    notes: "会员尚未开始使用",
    createdAt: "2023-08-01",
    createdBy: "销售",
    memberAvatar: "/avatars/07.png",
    memberLevel: "银卡会员",
    memberJoinDate: "2023-08-01",
    memberTotalSpent: "¥3800",
    memberVisitCount: 0,
    memberLastVisit: null,
    totalDays: 365,
    usedDays: 0,
    totalCount: 20,
    usedCount: 0,
    usageRecords: [],
    consumptionRecords: [],
    paymentRecords: [
      { id: "PR001", date: "2023-08-01", type: "购卡", amount: "¥3800", method: "微信支付", status: "已完成", operator: "销售" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-08-01",
        operation: "创建会员卡",
        operator: "销售",
        note: "新购私教次卡，等待激活"
      }
    ]
  },
  {
    id: "MC008",
    memberName: "吴十",
    memberPhone: "13800138008",
    memberId: "M008",
    cardType: "瑜伽年卡",
    cardName: "瑜伽年卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    status: "已退卡",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 3600,
    actualPrice: 3600,
    paymentMethod: "微信支付",
    activationDate: "2023-01-01",
    lastUsedDate: "2023-03-15",
    usageCount: 20,
    isTrialCard: false,
    isAutoRenew: false,
    tags: [],
    notes: "会员因搬家申请退卡",
    createdAt: "2022-12-28",
    createdBy: "前台",
    memberAvatar: "/avatars/08.png",
    memberLevel: "金卡会员",
    memberJoinDate: "2021-05-10",
    memberTotalSpent: "¥10,800",
    memberVisitCount: 85,
    memberLastVisit: "2023-03-15",
    totalDays: 365,
    usedDays: 74,
    usageRecords: [
      { id: "UR001", date: "2023-03-15", course: "高级瑜伽", instructor: "王教练", duration: "90分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-03-10", course: "普拉提", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-03-05", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-03-15",
        type: "class",
        itemName: "高级瑜伽",
        originalPrice: 200,
        consumedValue: 120,
        remainingValue: 2400,
        instructor: "王教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2022-12-28", type: "购卡", amount: "¥3600", method: "微信支付", status: "已完成", operator: "前台" },
      { id: "PR002", date: "2023-03-20", type: "退款", amount: "¥2700", method: "原路退回", status: "已完成", operator: "经理" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-03-20",
        operation: "退卡",
        operator: "经理",
        note: "会员因搬家申请退卡，按比例退款"
      },
      {
        id: "OR002",
        date: "2023-01-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC009",
    memberName: "郑十一",
    memberPhone: "13800138009",
    memberId: "M009",
    cardType: "瑜伽季卡",
    cardName: "瑜伽季卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#2196F3",
    startDate: "2023-07-01",
    endDate: "2023-09-30",
    status: "已过期",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 1200,
    actualPrice: 1080,
    paymentMethod: "支付宝",
    activationDate: "2023-07-01",
    lastUsedDate: "2023-09-28",
    usageCount: 25,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["学生"],
    notes: "学生特惠价",
    createdAt: "2023-06-28",
    createdBy: "前台",
    memberAvatar: "/avatars/09.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-06-28",
    memberTotalSpent: "¥1080",
    memberVisitCount: 25,
    memberLastVisit: "2023-09-28",
    totalDays: 92,
    usedDays: 92,
    usageRecords: [
      { id: "UR001", date: "2023-09-28", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-20", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-09-15", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-28",
        type: "class",
        itemName: "瑜伽基础课",
        originalPrice: 150,
        consumedValue: 100,
        remainingValue: 0,
        instructor: "李教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-06-28", type: "购卡", amount: "¥1080", method: "支付宝", status: "已完成", operator: "前台" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-09-30",
        operation: "会员卡过期",
        operator: "系统",
        note: "瑜伽季卡自动过期"
      },
      {
        id: "OR002",
        date: "2023-07-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC010",
    memberName: "陈十二",
    memberPhone: "13800138010",
    memberId: "M010",
    cardType: "瑜伽年卡",
    cardName: "瑜伽年卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    status: "有效",
    remainingDays: 92,
    remainingCount: null,
    remainingValue: null,
    price: 3600,
    actualPrice: 3600,
    paymentMethod: "微信支付",
    activationDate: "2023-01-01",
    lastUsedDate: "2023-09-25",
    usageCount: 80,
    isTrialCard: false,
    isAutoRenew: true,
    tags: ["VIP", "高频用户"],
    notes: "会员使用频率高",
    createdAt: "2022-12-28",
    createdBy: "销售",
    memberAvatar: "/avatars/10.png",
    memberLevel: "金卡会员",
    memberJoinDate: "2021-03-15",
    memberTotalSpent: "¥12,600",
    memberVisitCount: 180,
    memberLastVisit: "2023-09-25",
    totalDays: 365,
    usedDays: 273,
    usageRecords: [
      { id: "UR001", date: "2023-09-25", course: "高级瑜伽", instructor: "王教练", duration: "90分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-20", course: "普拉提", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-09-15", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-25",
        type: "class",
        itemName: "高级瑜伽",
        originalPrice: 200,
        consumedValue: 120,
        remainingValue: 1200,
        instructor: "王教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2022-12-28", type: "购卡", amount: "¥3600", method: "微信支付", status: "已完成", operator: "销售" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-01-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC011",
    memberName: "林十三",
    memberPhone: "13800138011",
    memberId: "M011",
    cardType: "瑜伽次卡",
    cardName: "瑜伽次卡",
    cardCategory: "count",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-07-01",
    endDate: "2024-06-30",
    status: "有效",
    remainingDays: 274,
    remainingCount: 35,
    remainingValue: null,
    price: 2000,
    actualPrice: 1800,
    paymentMethod: "支付宝",
    activationDate: "2023-07-01",
    lastUsedDate: "2023-09-20",
    usageCount: 5,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["新客户"],
    notes: "新客户优惠价",
    createdAt: "2023-06-28",
    createdBy: "前台",
    memberAvatar: "/avatars/11.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-06-28",
    memberTotalSpent: "¥1800",
    memberVisitCount: 5,
    memberLastVisit: "2023-09-20",
    totalDays: 365,
    usedDays: 91,
    totalCount: 40,
    usedCount: 5,
    usageRecords: [
      { id: "UR001", date: "2023-09-20", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-10", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-09-01", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-20",
        type: "class",
        itemName: "瑜伽基础课",
        originalPrice: 150,
        consumedValue: 45,
        remainingValue: 1575,
        instructor: "李教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-06-28", type: "购卡", amount: "¥1800", method: "支付宝", status: "已完成", operator: "前台" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-07-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC012",
    memberName: "黄十四",
    memberPhone: "13800138012",
    memberId: "M012",
    cardType: "瑜伽+普拉提联合卡",
    cardName: "瑜伽+普拉提联合卡",
    cardCategory: "time",
    cardTypeBadge: "组合卡",
    cardTypeColor: "#673AB7",
    startDate: "2023-05-01",
    endDate: "2024-04-30",
    status: "有效",
    remainingDays: 213,
    remainingCount: null,
    remainingValue: null,
    price: 5000,
    actualPrice: 4500,
    paymentMethod: "微信支付",
    activationDate: "2023-05-01",
    lastUsedDate: "2023-09-28",
    usageCount: 40,
    isTrialCard: false,
    isAutoRenew: true,
    tags: ["VIP", "组合卡"],
    notes: "组合卡优惠套餐",
    createdAt: "2023-04-28",
    createdBy: "销售",
    memberAvatar: "/avatars/12.png",
    memberLevel: "金卡会员",
    memberJoinDate: "2022-01-15",
    memberTotalSpent: "¥12,500",
    memberVisitCount: 120,
    memberLastVisit: "2023-09-28",
    totalDays: 365,
    usedDays: 152,
    usageRecords: [
      { id: "UR001", date: "2023-09-28", course: "高级普拉提", instructor: "王教练", duration: "90分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-20", course: "高级瑜伽", instructor: "李教练", duration: "90分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-09-15", course: "普拉提", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-28",
        type: "class",
        itemName: "高级普拉提",
        originalPrice: 200,
        consumedValue: 150,
        remainingValue: 3000,
        instructor: "王教练",
        location: "二号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-04-28", type: "购卡", amount: "¥4500", method: "微信支付", status: "已完成", operator: "销售" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-05-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC013",
    memberName: "刘十五",
    memberPhone: "13800138013",
    memberId: "M013",
    cardType: "孕妇瑜伽卡",
    cardName: "孕妇瑜伽卡",
    cardCategory: "count",
    cardTypeBadge: "次卡",
    cardTypeColor: "#E91E63",
    startDate: "2023-08-01",
    endDate: "2024-01-31",
    status: "有效",
    remainingDays: 123,
    remainingCount: 12,
    remainingValue: null,
    price: 1800,
    actualPrice: 1800,
    paymentMethod: "微信支付",
    activationDate: "2023-08-01",
    lastUsedDate: "2023-09-25",
    usageCount: 8,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["孕妇", "特殊人群"],
    notes: "孕期专属课程",
    createdAt: "2023-07-28",
    createdBy: "前台",
    memberAvatar: "/avatars/13.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-07-28",
    memberTotalSpent: "¥1800",
    memberVisitCount: 8,
    memberLastVisit: "2023-09-25",
    totalDays: 184,
    usedDays: 61,
    totalCount: 20,
    usedCount: 8,
    usageRecords: [
      { id: "UR001", date: "2023-09-25", course: "孕妇瑜伽", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-18", course: "孕妇瑜伽", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-09-11", course: "孕妇瑜伽", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-25",
        type: "class",
        itemName: "孕妇瑜伽",
        originalPrice: 180,
        consumedValue: 90,
        remainingValue: 1080,
        instructor: "张教练",
        location: "三号教室",
        notes: "会员需要特殊照顾"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-07-28", type: "购卡", amount: "¥1800", method: "微信支付", status: "已完成", operator: "前台" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-08-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC014",
    memberName: "张十六",
    memberPhone: "13800138014",
    memberId: "M014",
    cardType: "高温瑜伽卡",
    cardName: "高温瑜伽卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#FF5722",
    startDate: "2023-06-01",
    endDate: "2023-08-31",
    status: "已过期",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 1500,
    actualPrice: 1200,
    paymentMethod: "支付宝",
    activationDate: "2023-06-01",
    lastUsedDate: "2023-08-30",
    usageCount: 30,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["夏季限定"],
    notes: "夏季高温瑜伽特惠卡",
    createdAt: "2023-05-28",
    createdBy: "销售",
    memberAvatar: "/avatars/14.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-05-28",
    memberTotalSpent: "¥1200",
    memberVisitCount: 30,
    memberLastVisit: "2023-08-30",
    totalDays: 92,
    usedDays: 92,
    usageRecords: [
      { id: "UR001", date: "2023-08-30", course: "高温瑜伽", instructor: "王教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-08-25", course: "高温瑜伽", instructor: "王教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-08-20", course: "高温瑜伽", instructor: "王教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-08-30",
        type: "class",
        itemName: "高温瑜伽",
        originalPrice: 180,
        consumedValue: 40,
        remainingValue: 0,
        instructor: "王教练",
        location: "高温教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-05-28", type: "购卡", amount: "¥1200", method: "支付宝", status: "已完成", operator: "销售" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-08-31",
        operation: "会员卡过期",
        operator: "系统",
        note: "高温瑜伽卡自动过期"
      },
      {
        id: "OR002",
        date: "2023-06-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC015",
    memberName: "李十七",
    memberPhone: "13800138015",
    memberId: "M015",
    cardType: "亲子瑜伽卡",
    cardName: "亲子瑜伽卡",
    cardCategory: "count",
    cardTypeBadge: "次卡",
    cardTypeColor: "#8BC34A",
    startDate: "2023-08-15",
    endDate: "2024-02-14",
    status: "有效",
    remainingDays: 137,
    remainingCount: 8,
    remainingValue: null,
    price: 1600,
    actualPrice: 1600,
    paymentMethod: "微信支付",
    activationDate: "2023-08-15",
    lastUsedDate: "2023-09-15",
    usageCount: 2,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["亲子"],
    notes: "一大一小亲子卡",
    createdAt: "2023-08-10",
    createdBy: "前台",
    memberAvatar: "/avatars/15.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-08-10",
    memberTotalSpent: "¥1600",
    memberVisitCount: 2,
    memberLastVisit: "2023-09-15",
    totalDays: 184,
    usedDays: 31,
    totalCount: 10,
    usedCount: 2,
    usageRecords: [
      { id: "UR001", date: "2023-09-15", course: "亲子瑜伽", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-01", course: "亲子瑜伽", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-15",
        type: "class",
        itemName: "亲子瑜伽",
        originalPrice: 200,
        consumedValue: 160,
        remainingValue: 1280,
        instructor: "张教练",
        location: "亲子教室",
        notes: "一大一小"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-08-10", type: "购卡", amount: "¥1600", method: "微信支付", status: "已完成", operator: "前台" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-08-15",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC016",
    memberName: "王十八",
    memberPhone: "13800138016",
    memberId: "M016",
    cardType: "瑜伽月卡",
    cardName: "瑜伽月卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#FF9800",
    startDate: "2023-09-01",
    endDate: "2023-09-30",
    status: "有效",
    remainingDays: 10,
    remainingCount: null,
    remainingValue: null,
    price: 450,
    actualPrice: 450,
    paymentMethod: "微信支付",
    activationDate: "2023-09-01",
    lastUsedDate: "2023-09-20",
    usageCount: 8,
    isTrialCard: false,
    isAutoRenew: true,
    tags: ["新客户"],
    notes: "首次购买月卡",
    createdAt: "2023-08-30",
    createdBy: "前台",
    memberAvatar: "/avatars/16.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-08-30",
    memberTotalSpent: "¥450",
    memberVisitCount: 8,
    memberLastVisit: "2023-09-20",
    totalDays: 30,
    usedDays: 20,
    usageRecords: [
      { id: "UR001", date: "2023-09-20", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-09-15", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-09-10", course: "瑜伽基础课", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-20",
        type: "class",
        itemName: "瑜伽基础课",
        originalPrice: 150,
        consumedValue: 120,
        remainingValue: 240,
        instructor: "李教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-08-30", type: "购卡", amount: "¥450", method: "微信支付", status: "已完成", operator: "前台" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-09-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC017",
    memberName: "赵十九",
    memberPhone: "13800138017",
    memberId: "M017",
    cardType: "瑜伽储值卡",
    cardName: "瑜伽储值卡",
    cardCategory: "value",
    cardTypeBadge: "储值卡",
    cardTypeColor: "#FF9800",
    startDate: "2023-03-01",
    endDate: null,
    status: "有效",
    remainingDays: null,
    remainingCount: null,
    remainingValue: 1200,
    price: 3000,
    actualPrice: 3000,
    paymentMethod: "银行转账",
    activationDate: "2023-03-01",
    lastUsedDate: "2023-09-15",
    usageCount: 20,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP"],
    notes: "充值3000元，赠送600元",
    createdAt: "2023-02-28",
    createdBy: "销售",
    memberAvatar: "/avatars/17.png",
    memberLevel: "金卡会员",
    memberJoinDate: "2022-05-10",
    memberTotalSpent: "¥8,000",
    memberVisitCount: 85,
    memberLastVisit: "2023-09-15",
    usageRecords: [
      { id: "UR001", date: "2023-09-15", course: "高级瑜伽", instructor: "王教练", duration: "90分钟", consumption: "¥200", status: "已完成" },
      { id: "UR002", date: "2023-09-10", course: "普拉提", instructor: "李教练", duration: "60分钟", consumption: "¥150", status: "已完成" },
      { id: "UR003", date: "2023-09-05", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "¥150", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-15",
        type: "class",
        itemName: "高级瑜伽",
        originalPrice: 200,
        consumedValue: 200,
        remainingValue: 1200,
        instructor: "王教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-02-28", type: "充值", amount: "¥3000", method: "银行转账", status: "已完成", operator: "销售" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-03-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC018",
    memberName: "钱二十",
    memberPhone: "13800138018",
    memberId: "M018",
    cardType: "瑜伽年卡",
    cardName: "瑜伽年卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    status: "冻结",
    remainingDays: 92,
    remainingCount: null,
    remainingValue: null,
    price: 3600,
    actualPrice: 3240,
    paymentMethod: "微信支付",
    activationDate: "2023-01-01",
    lastUsedDate: "2023-07-15",
    usageCount: 35,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "老客户"],
    notes: "会员因伤申请冻结",
    createdAt: "2022-12-25",
    createdBy: "销售",
    memberAvatar: "/avatars/18.png",
    memberLevel: "金卡会员",
    memberJoinDate: "2021-06-15",
    memberTotalSpent: "¥9,800",
    memberVisitCount: 120,
    memberLastVisit: "2023-07-15",
    totalDays: 365,
    usedDays: 196,
    usageRecords: [
      { id: "UR001", date: "2023-07-15", course: "高级瑜伽", instructor: "王教练", duration: "90分钟", consumption: "1次课", status: "已完成" },
      { id: "UR002", date: "2023-07-10", course: "普拉提", instructor: "李教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
      { id: "UR003", date: "2023-07-05", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-07-15",
        type: "class",
        itemName: "高级瑜伽",
        originalPrice: 200,
        consumedValue: 120,
        remainingValue: 1800,
        instructor: "王教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2022-12-25", type: "购卡", amount: "¥3240", method: "微信支付", status: "已完成", operator: "销售" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-07-20",
        operation: "冻结会员卡",
        operator: "王经理",
        note: "会员因伤申请冻结3个月"
      },
      {
        id: "OR002",
        date: "2023-01-01",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC019",
    memberName: "孙二十一",
    memberPhone: "13800138019",
    memberId: "M019",
    cardType: "瑜伽体验卡",
    cardName: "瑜伽体验卡",
    cardCategory: "time",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#E91E63",
    startDate: "2023-09-20",
    endDate: "2023-10-04",
    status: "有效",
    remainingDays: 14,
    remainingCount: null,
    remainingValue: null,
    price: 199,
    actualPrice: 99,
    paymentMethod: "微信支付",
    activationDate: "2023-09-20",
    lastUsedDate: "2023-09-20",
    usageCount: 1,
    isTrialCard: true,
    isAutoRenew: false,
    tags: ["新客户", "体验卡"],
    notes: "新客户体验价",
    createdAt: "2023-09-19",
    createdBy: "前台",
    memberAvatar: "/avatars/19.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-09-19",
    memberTotalSpent: "¥99",
    memberVisitCount: 1,
    memberLastVisit: "2023-09-20",
    totalDays: 15,
    usedDays: 1,
    usageRecords: [
      { id: "UR001", date: "2023-09-20", course: "瑜伽入门", instructor: "张教练", duration: "60分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-20",
        type: "class",
        itemName: "瑜伽入门",
        originalPrice: 150,
        consumedValue: 33,
        remainingValue: 66,
        instructor: "张教练",
        location: "一号教室"
      }
    ],
    paymentRecords: [
      { id: "PR001", date: "2023-09-19", type: "购卡", amount: "¥99", method: "微信支付", status: "已完成", operator: "前台" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-09-20",
        operation: "激活会员卡",
        operator: "前台",
        note: "会员首次使用，自动激活"
      }
    ]
  },
  {
    id: "MC020",
    memberName: "李二十二",
    memberPhone: "13800138020",
    memberId: "M020",
    cardType: "瑜伽次卡",
    cardName: "瑜伽次卡",
    cardCategory: "count",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-09-01",
    endDate: "2024-08-31",
    status: "未激活",
    remainingDays: 365,
    remainingCount: 20,
    remainingValue: null,
    price: 2000,
    actualPrice: 1800,
    paymentMethod: "支付宝",
    activationDate: null,
    lastUsedDate: null,
    usageCount: 0,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["新客户"],
    notes: "新客户优惠价",
    createdAt: "2023-09-01",
    createdBy: "销售",
    memberAvatar: "/avatars/20.png",
    memberLevel: "普通会员",
    memberJoinDate: "2023-09-01",
    memberTotalSpent: "¥1800",
    memberVisitCount: 0,
    memberLastVisit: null,
    totalDays: 365,
    usedDays: 0,
    totalCount: 20,
    usedCount: 0,
    usageRecords: [],
    consumptionRecords: [],
    paymentRecords: [
      { id: "PR001", date: "2023-09-01", type: "购卡", amount: "¥1800", method: "支付宝", status: "已完成", operator: "销售" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-09-01",
        operation: "创建会员卡",
        operator: "销售",
        note: "新购次卡，等待激活"
      }
    ]
  }
];

// 根据ID获取会员卡
export function getMemberCardById(id: string): MemberCard | undefined {
  return memberCards.find(card => card.id === id);
}

// 添加更多会员卡数据
export function addMemberCards(cards: MemberCard[]): void {
  memberCards.push(...cards);
}
