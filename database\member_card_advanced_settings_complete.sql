-- 会员卡完整高级设置数据表（基于页面真实字段分析）
-- 分析来源：components/members/cards/add-member-card-dialog.tsx 和 edit-member-card-dialog.tsx

-- 删除现有表
DROP TABLE IF EXISTS member_card_course_associations;
DROP TABLE IF EXISTS member_card_sales_settings;
DROP TABLE IF EXISTS member_card_course_settings;
DROP TABLE IF EXISTS member_card_user_settings;
DROP TABLE IF EXISTS member_card_advanced_settings;

-- 1. 会员卡高级设置主表（卡相关设置）
CREATE TABLE member_card_advanced_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- Leave option settings
  leave_option ENUM('no_allow', 'no_limit', 'limited') DEFAULT 'no_limit' COMMENT 'Leave option',
  leave_times_limit INT DEFAULT NULL COMMENT 'Leave times limit',
  leave_days_limit INT DEFAULT NULL COMMENT 'Leave days limit',

  -- Card activation settings
  auto_activate_days INT DEFAULT 120 COMMENT 'Auto activate days',

  -- Booking limit settings
  max_people_per_class INT DEFAULT 1 COMMENT 'Max people per class',
  daily_booking_limit INT DEFAULT 3 COMMENT 'Daily booking limit',
  weekly_booking_limit INT DEFAULT 4 COMMENT 'Weekly booking limit',
  weekly_calculation_type ENUM('natural_week', 'card_cycle') DEFAULT 'natural_week' COMMENT 'Weekly calculation type',
  monthly_booking_limit INT DEFAULT 5 COMMENT 'Monthly booking limit',
  monthly_calculation_type ENUM('natural_month', 'card_cycle') DEFAULT 'natural_month' COMMENT 'Monthly calculation type',

  -- Advance booking settings
  advance_booking_days INT DEFAULT NULL COMMENT 'Advance booking days',
  advance_booking_unlimited BOOLEAN DEFAULT TRUE COMMENT 'Advance booking unlimited',

  -- Available time settings
  custom_time_enabled BOOLEAN DEFAULT FALSE COMMENT 'Custom time enabled',
  available_days JSON COMMENT 'Available days config',
  available_time_slots JSON COMMENT 'Available time slots config',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Created at',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated at',
  
  UNIQUE KEY unique_card_tenant (card_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Member Card Advanced Settings';

-- 2. 会员卡用卡人设置表
CREATE TABLE member_card_user_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 约课间隔限制
  booking_interval_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用约课间隔限制',
  booking_interval_minutes INT DEFAULT 0 COMMENT '约课间隔时间（分钟），0表示不限制',
  
  -- 预约次数限制
  pending_booking_limit INT DEFAULT 0 COMMENT '未结束课程预约次数限制，0表示不限制',
  
  -- 取消预约限制
  cancel_limit_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用取消预约次数限制',
  cancel_limit_count INT DEFAULT 0 COMMENT '取消预约次数限制',
  cancel_limit_period ENUM('day', 'week', 'month') DEFAULT 'week' COMMENT '取消限制周期',
  
  -- 同类课程限制
  same_course_daily_limit INT DEFAULT 1 COMMENT '同类课程每日限制次数',
  
  -- 高峰时段限制
  peak_time_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用高峰时段限制',
  peak_start_time TIME DEFAULT '18:00:00' COMMENT '高峰时段开始时间',
  peak_end_time TIME DEFAULT '21:00:00' COMMENT '高峰时段结束时间',
  peak_daily_limit INT DEFAULT 1 COMMENT '高峰时段每日预约限制',
  
  -- 预约优先级
  priority_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用预约优先级',
  priority_hours INT DEFAULT 24 COMMENT '优先预约时间（小时）',
  priority_description VARCHAR(200) DEFAULT '本周尚未参加此类课程的会员优先预约' COMMENT '优先级描述',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY unique_card_tenant (card_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡用卡人设置表';

-- 3. 会员卡课程关联设置表
CREATE TABLE member_card_course_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 消耗规则设置
  consumption_rule ENUM('AVERAGE', 'FIXED', 'CUSTOM') DEFAULT 'AVERAGE' COMMENT '消耗规则：平均消耗/固定消耗/自定义',
  consumption_description VARCHAR(200) DEFAULT '会员卡总价值将平均分配到每次课程消耗中' COMMENT '消耗规则描述',
  
  -- 赠送设置
  gift_class_count INT DEFAULT 0 COMMENT '赠送课时数',
  gift_value_coefficient DECIMAL(5,2) DEFAULT 1.0 COMMENT '赠送课程价值系数',
  
  -- 适用课程设置
  all_courses_enabled BOOLEAN DEFAULT TRUE COMMENT '是否适用所有课程',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY unique_card_tenant (card_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡课程设置表';

-- 4. 会员卡课程关联明细表
CREATE TABLE member_card_course_associations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  course_type_id INT NOT NULL COMMENT '课程类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 关联设置
  is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用此课程类型',
  consumption_times DECIMAL(5,2) DEFAULT 1.0 COMMENT '消耗次数',
  
  -- 课程信息（冗余存储，便于查询）
  course_type_name VARCHAR(100) COMMENT '课程类型名称',
  course_duration INT COMMENT '课程时长（分钟）',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY unique_card_course_tenant (card_type_id, course_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE,
  FOREIGN KEY (course_type_id) REFERENCES coursetype(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡课程关联明细表';

-- 5. 会员卡销售设置表（扩展功能，页面中暂未完全实现）
CREATE TABLE member_card_sales_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 折扣设置
  enable_discount BOOLEAN DEFAULT FALSE COMMENT '启用折扣',
  discount_percentage DECIMAL(5,2) COMMENT '折扣百分比',
  
  -- 促销设置
  enable_promotion BOOLEAN DEFAULT FALSE COMMENT '启用促销',
  promotion_type ENUM('new', 'renewal', 'group', 'holiday') COMMENT '促销类型',
  promotion_description VARCHAR(200) COMMENT '促销描述',
  
  -- 价格说明
  price_description TEXT COMMENT '价格说明',
  
  -- 销售限制
  max_sales_total INT COMMENT '总销售上限',
  max_sales_daily INT COMMENT '每日销售上限',
  max_per_user INT DEFAULT 1 COMMENT '每人最大购买数量',
  
  -- 销售时间
  sale_start_date DATETIME COMMENT '销售开始时间',
  sale_end_date DATETIME COMMENT '销售结束时间',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY unique_card_tenant (card_type_id, tenant_id),
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡销售设置表';

-- 创建索引
CREATE INDEX idx_card_type_tenant ON member_card_advanced_settings(card_type_id, tenant_id);
CREATE INDEX idx_card_type_tenant_user ON member_card_user_settings(card_type_id, tenant_id);
CREATE INDEX idx_card_type_tenant_course ON member_card_course_settings(card_type_id, tenant_id);
CREATE INDEX idx_card_course_tenant ON member_card_course_associations(card_type_id, course_type_id, tenant_id);
CREATE INDEX idx_card_type_tenant_sales ON member_card_sales_settings(card_type_id, tenant_id);
