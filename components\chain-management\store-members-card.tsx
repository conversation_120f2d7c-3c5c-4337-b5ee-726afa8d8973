"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Search, 
  Download, 
  Plus, 
  Filter, 
  MoreHorizontal, 
  ChevronLeft, 
  ChevronRight,
  UserPlus,
  Mail,
  Phone,
  Calendar,
  CreditCard
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface StoreMembersCardProps {
  store: any
}

export function StoreMembersCard({ store }: StoreMembersCardProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedLevel, setSelectedLevel] = useState("all")
  const [selectedCardType, setSelectedCardType] = useState("all")
  const itemsPerPage = 10

  // 模拟会员数据
  const members = [
    {
      id: "1",
      name: "张三",
      phone: "138****1234",
      level: "金卡",
      cardType: "年卡",
      joinDate: "2022-03-15",
      lastVisit: "2023-06-08",
      status: "active",
      email: "<EMAIL>",
      birthday: "1990-05-20",
      gender: "男",
      totalSpent: 5800,
      visitCount: 48,
    },
    {
      id: "2",
      name: "李四",
      phone: "139****5678",
      level: "银卡",
      cardType: "季卡",
      joinDate: "2022-08-10",
      lastVisit: "2023-06-05",
      status: "active",
      email: "<EMAIL>",
      birthday: "1988-11-12",
      gender: "女",
      totalSpent: 2300,
      visitCount: 25,
    },
    {
      id: "3",
      name: "王五",
      phone: "137****9012",
      level: "铜卡",
      cardType: "月卡",
      joinDate: "2023-01-20",
      lastVisit: "2023-05-28",
      status: "active",
      email: "<EMAIL>",
      birthday: "1995-03-08",
      gender: "男",
      totalSpent: 1200,
      visitCount: 15,
    },
    {
      id: "4",
      name: "赵六",
      phone: "136****3456",
      level: "金卡",
      cardType: "年卡",
      joinDate: "2021-11-05",
      lastVisit: "2023-06-01",
      status: "active",
      email: "<EMAIL>",
      birthday: "1985-07-15",
      gender: "女",
      totalSpent: 6500,
      visitCount: 62,
    },
    {
      id: "5",
      name: "钱七",
      phone: "135****7890",
      level: "银卡",
      cardType: "次卡",
      joinDate: "2022-05-18",
      lastVisit: "2023-05-20",
      status: "inactive",
      email: "<EMAIL>",
      birthday: "1992-09-30",
      gender: "女",
      totalSpent: 3200,
      visitCount: 30,
    },
    {
      id: "6",
      name: "孙八",
      phone: "134****1234",
      level: "铜卡",
      cardType: "月卡",
      joinDate: "2023-02-10",
      lastVisit: "2023-06-07",
      status: "active",
      email: "<EMAIL>",
      birthday: "1998-01-25",
      gender: "男",
      totalSpent: 900,
      visitCount: 12,
    },
    {
      id: "7",
      name: "周九",
      phone: "133****5678",
      level: "金卡",
      cardType: "年卡",
      joinDate: "2021-09-15",
      lastVisit: "2023-06-03",
      status: "active",
      email: "<EMAIL>",
      birthday: "1987-12-05",
      gender: "女",
      totalSpent: 7200,
      visitCount: 75,
    },
    {
      id: "8",
      name: "吴十",
      phone: "132****9012",
      level: "银卡",
      cardType: "季卡",
      joinDate: "2022-07-20",
      lastVisit: "2023-05-15",
      status: "inactive",
      email: "<EMAIL>",
      birthday: "1993-06-18",
      gender: "男",
      totalSpent: 2800,
      visitCount: 28,
    },
    {
      id: "9",
      name: "郑十一",
      phone: "131****3456",
      level: "铜卡",
      cardType: "次卡",
      joinDate: "2023-03-05",
      lastVisit: "2023-06-02",
      status: "active",
      email: "<EMAIL>",
      birthday: "1996-04-10",
      gender: "女",
      totalSpent: 1500,
      visitCount: 18,
    },
    {
      id: "10",
      name: "王十二",
      phone: "130****7890",
      level: "金卡",
      cardType: "年卡",
      joinDate: "2021-12-10",
      lastVisit: "2023-06-09",
      status: "active",
      email: "<EMAIL>",
      birthday: "1989-08-22",
      gender: "男",
      totalSpent: 6800,
      visitCount: 65,
    },
    {
      id: "11",
      name: "李十三",
      phone: "139****4321",
      level: "银卡",
      cardType: "季卡",
      joinDate: "2022-06-15",
      lastVisit: "2023-05-25",
      status: "active",
      email: "<EMAIL>",
      birthday: "1991-10-15",
      gender: "女",
      totalSpent: 3500,
      visitCount: 32,
    },
    {
      id: "12",
      name: "张十四",
      phone: "138****8765",
      level: "铜卡",
      cardType: "月卡",
      joinDate: "2023-01-05",
      lastVisit: "2023-06-04",
      status: "active",
      email: "<EMAIL>",
      birthday: "1997-02-28",
      gender: "男",
      totalSpent: 1100,
      visitCount: 14,
    },
  ]

  // 过滤会员
  const filteredMembers = members.filter(
    (member) => {
      const matchesSearch = 
        member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        member.phone.toLowerCase().includes(searchQuery.toLowerCase()) ||
        member.email.toLowerCase().includes(searchQuery.toLowerCase())
      
      const matchesLevel = selectedLevel === "all" || member.level === selectedLevel
      const matchesCardType = selectedCardType === "all" || member.cardType === selectedCardType

      return matchesSearch && matchesLevel && matchesCardType
    }
  )

  // 分页
  const totalPages = Math.ceil(filteredMembers.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedMembers = filteredMembers.slice(startIndex, startIndex + itemsPerPage)

  // 获取会员等级徽章颜色
  const getLevelBadgeColor = (level: string) => {
    switch (level) {
      case "金卡":
        return "bg-yellow-500 hover:bg-yellow-600"
      case "银卡":
        return "bg-gray-400 hover:bg-gray-500"
      case "铜卡":
        return "bg-amber-600 hover:bg-amber-700"
      default:
        return "bg-blue-500 hover:bg-blue-600"
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <CardTitle>会员管理</CardTitle>
            <CardDescription>管理门店的会员信息</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              导出
            </Button>
            <Button size="sm">
              <UserPlus className="mr-2 h-4 w-4" />
              添加会员
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-1/3 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索会员..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="会员等级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部等级</SelectItem>
                  <SelectItem value="金卡">金卡</SelectItem>
                  <SelectItem value="银卡">银卡</SelectItem>
                  <SelectItem value="铜卡">铜卡</SelectItem>
                </SelectContent>
              </Select>
              <Select value={selectedCardType} onValueChange={setSelectedCardType}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="卡类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部卡型</SelectItem>
                  <SelectItem value="年卡">年卡</SelectItem>
                  <SelectItem value="季卡">季卡</SelectItem>
                  <SelectItem value="月卡">月卡</SelectItem>
                  <SelectItem value="次卡">次卡</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>会员姓名</TableHead>
                  <TableHead>联系方式</TableHead>
                  <TableHead>会员等级</TableHead>
                  <TableHead>卡类型</TableHead>
                  <TableHead>加入日期</TableHead>
                  <TableHead>最近到访</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell className="font-medium">{member.name}</TableCell>
                    <TableCell>{member.phone}</TableCell>
                    <TableCell>
                      <Badge className={getLevelBadgeColor(member.level)}>
                        {member.level}
                      </Badge>
                    </TableCell>
                    <TableCell>{member.cardType}</TableCell>
                    <TableCell>{member.joinDate}</TableCell>
                    <TableCell>{member.lastVisit}</TableCell>
                    <TableCell>
                      <Badge variant={member.status === "active" ? "default" : "secondary"}>
                        {member.status === "active" ? "活跃" : "非活跃"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <CreditCard className="mr-2 h-4 w-4" />
                            查看会员卡
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Calendar className="mr-2 h-4 w-4" />
                            预约记录
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Mail className="mr-2 h-4 w-4" />
                            发送消息
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Phone className="mr-2 h-4 w-4" />
                            联系会员
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                显示 {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredMembers.length)} 条，共 {filteredMembers.length} 条
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-sm">
                  第 {currentPage} 页，共 {totalPages} 页
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
