"use client"

import React, { createContext, useContext, useState, useEffect } from "react"
import { logService, OperationLog, LogQueryParams, logOperation } from "@/lib/services/log-service"

// 日志上下文类型
interface LogContextType {
  // 日志记录函数
  logOperation: (
    module: string,
    action: string,
    details: string,
    status?: "success" | "failed" | "warning",
    metadata?: Record<string, any>
  ) => void
  
  logSuccess: (
    module: string,
    action: string,
    details: string,
    metadata?: Record<string, any>
  ) => void
  
  logFailure: (
    module: string,
    action: string,
    details: string,
    metadata?: Record<string, any>
  ) => void
  
  logWarning: (
    module: string,
    action: string,
    details: string,
    metadata?: Record<string, any>
  ) => void
  
  // 日志查询函数
  getLogs: (params?: LogQueryParams) => Promise<{
    logs: OperationLog[]
    total: number
  }>
  
  // 日志导出函数
  exportLogs: (params?: LogQueryParams) => Promise<void>
  
  // 日志清除函数
  clearLogs: (params?: { module?: string; before?: string }) => Promise<boolean>
  
  // 日志设置
  logSettings: {
    enabled: boolean
    logUserActions: boolean
    logApiCalls: boolean
    logSecurity: boolean
    retentionDays: number
  }
  
  // 更新日志设置
  updateLogSettings: (settings: Partial<LogContextType["logSettings"]>) => void
}

// 创建日志上下文
const LogContext = createContext<LogContextType | undefined>(undefined)

// 日志提供者组件
export function LogProvider({ children }: { children: React.ReactNode }) {
  // 日志设置状态
  const [logSettings, setLogSettings] = useState({
    enabled: true,
    logUserActions: true,
    logApiCalls: false,
    logSecurity: true,
    retentionDays: 90
  })
  
  // 从localStorage加载日志设置
  useEffect(() => {
    const savedSettings = localStorage.getItem("logSettings")
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings)
        setLogSettings(prevSettings => ({
          ...prevSettings,
          ...parsedSettings
        }))
      } catch (e) {
        console.error("解析日志设置失败:", e)
      }
    }
  }, [])
  
  // 保存日志设置到localStorage
  const updateLogSettings = (settings: Partial<LogContextType["logSettings"]>) => {
    setLogSettings(prevSettings => {
      const newSettings = { ...prevSettings, ...settings }
      localStorage.setItem("logSettings", JSON.stringify(newSettings))
      return newSettings
    })
  }
  
  // 记录成功操作
  const logSuccess = (
    module: string,
    action: string,
    details: string,
    metadata?: Record<string, any>
  ) => {
    if (logSettings.enabled) {
      logOperation(module, action, details, "success", metadata)
    }
  }
  
  // 记录失败操作
  const logFailure = (
    module: string,
    action: string,
    details: string,
    metadata?: Record<string, any>
  ) => {
    if (logSettings.enabled) {
      logOperation(module, action, details, "failed", metadata)
    }
  }
  
  // 记录警告操作
  const logWarning = (
    module: string,
    action: string,
    details: string,
    metadata?: Record<string, any>
  ) => {
    if (logSettings.enabled) {
      logOperation(module, action, details, "warning", metadata)
    }
  }
  
  // 获取日志列表
  const getLogs = async (params?: LogQueryParams) => {
    return await logService.getOperationLogs(params)
  }
  
  // 导出日志
  const exportLogs = async (params?: LogQueryParams) => {
    const blob = await logService.exportLogs(params)
    if (blob) {
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `operation_logs_${new Date().toISOString().split("T")[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    }
  }
  
  // 清除日志
  const clearLogs = async (params?: { module?: string; before?: string }) => {
    return await logService.clearLogs(params)
  }
  
  // 上下文值
  const contextValue: LogContextType = {
    logOperation: (module, action, details, status = "success", metadata) => {
      if (logSettings.enabled) {
        logOperation(module, action, details, status, metadata)
      }
    },
    logSuccess,
    logFailure,
    logWarning,
    getLogs,
    exportLogs,
    clearLogs,
    logSettings,
    updateLogSettings
  }
  
  return (
    <LogContext.Provider value={contextValue}>
      {children}
    </LogContext.Provider>
  )
}

// 使用日志上下文的Hook
export function useLog() {
  const context = useContext(LogContext)
  if (context === undefined) {
    throw new Error("useLog must be used within a LogProvider")
  }
  return context
}
