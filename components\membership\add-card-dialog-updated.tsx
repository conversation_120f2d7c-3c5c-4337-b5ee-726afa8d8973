"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogFooter } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { CardCourseAssociation } from "./card-course-association"
import { useToast } from "@/hooks/use-toast"

interface AddCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave?: (cardData: any) => void
}

export function AddCardDialog({
  open,
  onOpenChange,
  onSave
}: AddCardDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")
  const [cardName, setCardName] = useState("")
  const [cardType, setCardType] = useState("time")
  const [validity, setValidity] = useState("365")
  const [price, setPrice] = useState("")
  const [selectedCourses, setSelectedCourses] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 处理表单提交
  const handleSubmit = async () => {
    if (!cardName || !price) {
      toast({
        title: "请完善信息",
        description: "会员卡名称和价格为必填项",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 准备会员卡数据
      const cardData = {
        name: cardName,
        type: cardType,
        validity: parseInt(validity),
        price: parseFloat(price),
        relatedCourseIds: selectedCourses
      }

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      if (onSave) {
        onSave(cardData)
      }

      toast({
        title: "创建成功",
        description: `会员卡 ${cardName} 已创建`,
      })

      // 重置表单
      setCardName("")
      setCardType("time")
      setValidity("365")
      setPrice("")
      setSelectedCourses([])
      setActiveTab("basic")

      // 关闭对话框
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "创建失败",
        description: "创建会员卡时出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>添加会员卡</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="price">价格设置</TabsTrigger>
            <TabsTrigger value="courses">关联课程</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
          </TabsList>

          {/* 基本信息 */}
          <TabsContent value="basic" className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="card-name">会员卡名称</Label>
                <Input
                  id="card-name"
                  placeholder="输入会员卡名称"
                  value={cardName}
                  onChange={(e) => setCardName(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="card-type">会员卡类型</Label>
                <div className="flex space-x-4">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="time-card"
                      name="card-type"
                      value="time"
                      checked={cardType === "time"}
                      onChange={() => setCardType("time")}
                      className="mr-2"
                    />
                    <Label htmlFor="time-card" className="cursor-pointer">期限卡</Label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="count-card"
                      name="card-type"
                      value="count"
                      checked={cardType === "count"}
                      onChange={() => setCardType("count")}
                      className="mr-2"
                    />
                    <Label htmlFor="count-card" className="cursor-pointer">次数卡</Label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="value-card"
                      name="card-type"
                      value="value"
                      checked={cardType === "value"}
                      onChange={() => setCardType("value")}
                      className="mr-2"
                    />
                    <Label htmlFor="value-card" className="cursor-pointer">储值卡</Label>
                  </div>
                </div>
              </div>

              {cardType === "time" && (
                <div className="space-y-2">
                  <Label htmlFor="validity">有效期（天）</Label>
                  <Input
                    id="validity"
                    type="number"
                    placeholder="输入有效期天数"
                    value={validity}
                    onChange={(e) => setValidity(e.target.value)}
                  />
                </div>
              )}

              {cardType === "count" && (
                <div className="space-y-2">
                  <Label htmlFor="count">课程次数</Label>
                  <Input
                    id="count"
                    type="number"
                    placeholder="输入课程次数"
                    value={validity}
                    onChange={(e) => setValidity(e.target.value)}
                  />
                </div>
              )}

              {cardType === "value" && (
                <div className="space-y-2">
                  <Label htmlFor="value">储值金额</Label>
                  <Input
                    id="value"
                    type="number"
                    placeholder="输入储值金额"
                    value={validity}
                    onChange={(e) => setValidity(e.target.value)}
                  />
                </div>
              )}
            </div>
          </TabsContent>

          {/* 价格设置 */}
          <TabsContent value="price" className="py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="price">会员卡价格</Label>
                <Input
                  id="price"
                  type="number"
                  placeholder="输入会员卡价格"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                />
              </div>
            </div>
          </TabsContent>

          {/* 关联课程 */}
          <TabsContent value="courses" className="py-4">
            <CardCourseAssociation
              initialSelectedCourses={selectedCourses}
              onChange={setSelectedCourses}
            />
          </TabsContent>

          {/* 高级设置 */}
          <TabsContent value="advanced" className="py-4">
            <div className="text-center text-muted-foreground py-8">
              高级设置内容将在此显示
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "创建中..." : "创建会员卡"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
