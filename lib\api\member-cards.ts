import { memberCardApi as unifiedMemberCardApi } from '@/lib/api';

// 会员卡接口类型
export interface MemberCard {
  id: number;
  tenant_id: number;
  name: string;
  description?: string;
  price: number;
  original_price?: number;
  validity_days: number;
  usage_limit: string;
  card_type: string;
  status: string;
  members_count: number;
  sales_count: number;
  created_at: string;
  updated_at: string;
}

export interface MemberCardFilter {
  tenantId?: string;
  status?: string;
  cardType?: string;
  keyword?: string;
  page?: number;
  pageSize?: number;
}

export interface MemberCardListResponse {
  list: MemberCard[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 会员卡API - 使用统一的API调用方式
export const memberCardApi = {
  // 获取会员卡列表
  getAll: async (filter?: MemberCardFilter) => {
    try {
      console.log('发送会员卡列表请求，参数:', filter);

      const response = await unifiedMemberCardApi.getAllMemberCards(filter);

      console.log('会员卡列表API响应:', response);

      return response;
    } catch (error) {
      console.error('获取会员卡列表失败:', error);
      throw error;
    }
  },

  // 获取单个会员卡详情
  getById: async (id: number) => {
    try {
      console.log('获取会员卡详情，ID:', id);

      const response = await unifiedMemberCardApi.getMemberCardById(id.toString());

      console.log('会员卡详情API响应:', response);

      return response;
    } catch (error) {
      console.error('获取会员卡详情失败:', error);
      throw error;
    }
  },

  // 创建会员卡
  create: async (data: Partial<MemberCard>) => {
    try {
      console.log('创建会员卡，数据:', data);

      const response = await unifiedMemberCardApi.createMemberCard(data);

      console.log('创建会员卡API响应:', response);

      return response;
    } catch (error) {
      console.error('创建会员卡失败:', error);
      throw error;
    }
  },

  // 更新会员卡
  update: async (id: number, data: Partial<MemberCard>) => {
    try {
      console.log('更新会员卡，ID:', id, '数据:', data);

      const response = await unifiedMemberCardApi.updateMemberCard(id.toString(), data);

      console.log('更新会员卡API响应:', response);

      return response;
    } catch (error) {
      console.error('更新会员卡失败:', error);
      throw error;
    }
  },

  // 删除会员卡
  delete: async (id: number) => {
    try {
      console.log('删除会员卡，ID:', id);

      const response = await unifiedMemberCardApi.deleteMemberCard(id.toString());

      console.log('删除会员卡API响应:', response);

      return response;
    } catch (error) {
      console.error('删除会员卡失败:', error);
      throw error;
    }
  },

  // 更改会员卡状态
  changeStatus: async (id: number, status: string) => {
    try {
      console.log('更改会员卡状态，ID:', id, '状态:', status);

      const response = await unifiedMemberCardApi.updateMemberCardStatus(id.toString(), status);

      console.log('更改会员卡状态API响应:', response);

      return response;
    } catch (error) {
      console.error('更改会员卡状态失败:', error);
      throw error;
    }
  },

  // 复制会员卡类型
  copyMemberCard: async (id: string) => {
    try {
      console.log('复制会员卡类型，ID:', id);

      const response = await unifiedMemberCardApi.copyMemberCard(id);

      console.log('复制会员卡类型API响应:', response);

      return response;
    } catch (error) {
      console.error('复制会员卡类型失败:', error);
      throw error;
    }
  }
};

// 会员卡工具函数
export const memberCardUtils = {
  // 格式化价格
  formatPrice: (price: number) => {
    return `¥${price.toLocaleString()}`;
  },

  // 获取卡片类别
  getCardCategory: (cardType: string) => {
    if (cardType.includes('年') || cardType.includes('月') || cardType.includes('季') || cardType.includes('天')) {
      return 'time';
    } else if (cardType.includes('次')) {
      return 'count';
    } else if (cardType.includes('储值') || cardType.includes('金额')) {
      return 'value';
    }
    return 'time';
  },

  // 获取状态颜色
  getStatusColor: (status: string) => {
    switch (status) {
      case '销售中':
        return '#10b981';
      case '已下架':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  },

  // 获取卡片类型颜色
  getCardTypeColor: (cardType: string) => {
    const colors = [
      '#4f46e5', '#0ea5e9', '#10b981', '#f59e0b', 
      '#8b5cf6', '#ec4899', '#ef4444', '#06b6d4'
    ];
    
    // 根据卡片类型名称生成一个稳定的颜色索引
    let hash = 0;
    for (let i = 0; i < cardType.length; i++) {
      hash = cardType.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  },

  // 判断是否为体验卡
  isTrialCard: (cardType: string) => {
    return cardType.includes('体验');
  }
};
