"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"

const data = [
  { name: "1月", value: 120 },
  { name: "2月", value: 150 },
  { name: "3月", value: 180 },
  { name: "4月", value: 220 },
  { name: "5月", value: 280 },
  { name: "6月", value: 250 },
  { name: "7月", value: 300 },
  { name: "8月", value: 340 },
  { name: "9月", value: 380 },
  { name: "10月", value: 420 },
  { name: "11月", value: 450 },
  { name: "12月", value: 500 },
]

export function BookingTrends() {
  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>课程预约趋势</CardTitle>
          <CardDescription>课程预约数量变化趋势</CardDescription>
        </div>
        <Tabs defaultValue="month">
          <TabsList>
            <TabsTrigger value="day">日</TabsTrigger>
            <TabsTrigger value="week">周</TabsTrigger>
            <TabsTrigger value="month">月</TabsTrigger>
            <TabsTrigger value="year">年</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={data}
              margin={{
                top: 5,
                right: 10,
                left: 10,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="name" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#4285F4"
                strokeWidth={2}
                dot={{ r: 4 }}
                activeDot={{ r: 6 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}

