"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { TimePicker } from "@/components/time-picker"
import { useToast } from "@/components/ui/use-toast"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { cn } from "@/lib/utils"
import {
  Building,
  MapPin,
  Phone,
  Mail,
  Globe,
  Clock,
  Calendar,
  Image,
  Upload,
  Save,
  Trash2,
  CalendarIcon,
  AlertCircle
} from "lucide-react"

// 模拟场馆设置数据
const venueSettings = {
  basicInfo: {
    name: "静心瑜伽美学生活馆",
    description: "静心瑜伽美学生活馆是一家专注于提供高品质瑜伽课程的场馆，我们拥有专业的教练团队和舒适的环境，致力于帮助每一位会员找到身心平衡。",
    address: "北京市朝阳区三里屯SOHO 5号楼3层301",
    phone: "010-12345678",
    email: "<EMAIL>",
    website: "https://www.jinxinyoga.com",
    logo: "/logo.png",
    coverImage: "/cover.jpg",
    businessHours: {
      weekdays: { open: "09:00", close: "22:00" },
      weekend: { open: "10:00", close: "20:00" },
      holiday: { open: "10:00", close: "18:00" },
    },
  },
  operationSettings: {
    allowOnlineBooking: true,
    bookingAdvanceDays: 7,
    cancelationDeadlineHours: 2,
    maxClassSize: 20,
    enableWaitlist: true,
    waitlistLimit: 5,
    autoConfirmBooking: true,
    sendBookingReminders: true,
    reminderHoursBeforeClass: 3,
  },
  holidaySettings: {
    holidays: [
      { id: 1, date: "2023-10-01", name: "国庆节", isOpen: false },
      { id: 2, date: "2023-10-02", name: "国庆节", isOpen: false },
      { id: 3, date: "2023-10-03", name: "国庆节", isOpen: false },
      { id: 4, date: "2024-01-01", name: "元旦", isOpen: false },
      { id: 5, date: "2024-02-10", name: "春节", isOpen: false },
      { id: 6, date: "2024-04-01", name: "元旦", isOpen: false },
      { id: 7, date: "2024-02-10", name: "春节", isOpen: false },
    ],
  },
}

export default function VenueSettingsPage() {
  const { toast } = useToast()
  const [settings, setSettings] = useState(venueSettings)
  const [activeTab, setActiveTab] = useState("basic")

  // 节假日相关状态
  const [isAddHolidayDialogOpen, setIsAddHolidayDialogOpen] = useState(false)
  const [newHolidayDate, setNewHolidayDate] = useState<Date>()
  const [newHolidayName, setNewHolidayName] = useState("")
  const [newHolidayIsOpen, setNewHolidayIsOpen] = useState(false)

  // 更新基本信息
  const updateBasicInfo = (key: string, value: string) => {
    setSettings({
      ...settings,
      basicInfo: {
        ...settings.basicInfo,
        [key]: value,
      },
    })
  }

  // 更新营业时间
  const updateBusinessHours = (period: string, type: string, value: string) => {
    setSettings({
      ...settings,
      basicInfo: {
        ...settings.basicInfo,
        businessHours: {
          ...settings.basicInfo.businessHours,
          [period]: {
            ...settings.basicInfo.businessHours[period as keyof typeof settings.basicInfo.businessHours],
            [type]: value,
          },
        },
      },
    })
  }

  // 更新运营设置
  const updateOperationSettings = (key: string, value: any) => {
    setSettings({
      ...settings,
      operationSettings: {
        ...settings.operationSettings,
        [key]: value,
      },
    })
  }

  // 更新节假日设置
  const updateHolidaySetting = (id: number, key: string, value: any) => {
    setSettings({
      ...settings,
      holidaySettings: {
        ...settings.holidaySettings,
        holidays: settings.holidaySettings.holidays.map(holiday =>
          holiday.id === id ? { ...holiday, [key]: value } : holiday
        )
      }
    })
  }

  // 添加新节假日
  const addHoliday = () => {
    if (!newHolidayDate || !newHolidayName) {
      toast({
        title: "添加失败",
        description: "请填写完整的节假日信息",
        variant: "destructive"
      })
      return
    }

    const formattedDate = format(newHolidayDate, "yyyy-MM-dd")
    const newId = Math.max(...settings.holidaySettings.holidays.map(h => h.id), 0) + 1

    setSettings({
      ...settings,
      holidaySettings: {
        ...settings.holidaySettings,
        holidays: [
          ...settings.holidaySettings.holidays,
          {
            id: newId,
            date: formattedDate,
            name: newHolidayName,
            isOpen: newHolidayIsOpen
          }
        ]
      }
    })

    // 重置表单
    setNewHolidayDate(undefined)
    setNewHolidayName("")
    setNewHolidayIsOpen(false)
    setIsAddHolidayDialogOpen(false)

    toast({
      title: "添加成功",
      description: `已添加节假日：${newHolidayName}`,
    })
  }

  // 删除节假日
  const deleteHoliday = (id: number) => {
    setSettings({
      ...settings,
      holidaySettings: {
        ...settings.holidaySettings,
        holidays: settings.holidaySettings.holidays.filter(holiday => holiday.id !== id)
      }
    })

    toast({
      title: "删除成功",
      description: "已删除选中的节假日",
    })
  }

  // 保存设置
  const saveSettings = () => {
    // 这里可以添加保存到后端的逻辑
    toast({
      title: "设置已保存",
      description: "场馆设置已成功更新。",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">场馆设置</h1>
        <Button onClick={saveSettings}>
          <Save className="mr-2 h-4 w-4" />
          保存设置
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="operation">运营设置</TabsTrigger>
          <TabsTrigger value="holidays">节假日设置</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>场馆基本信息</CardTitle>
              <CardDescription>
                设置场馆的基本信息，这些信息将显示在会员端小程序和官网上
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">场馆名称</Label>
                <div className="flex items-center">
                  <Building className="mr-2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="name"
                    value={settings.basicInfo.name}
                    onChange={(e) => updateBasicInfo("name", e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">场馆简介</Label>
                <Textarea
                  id="description"
                  rows={4}
                  value={settings.basicInfo.description}
                  onChange={(e) => updateBasicInfo("description", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">场馆地址</Label>
                <div className="flex items-center">
                  <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="address"
                    value={settings.basicInfo.address}
                    onChange={(e) => updateBasicInfo("address", e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">联系电话</Label>
                <div className="flex items-center">
                  <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="phone"
                    value={settings.basicInfo.phone}
                    onChange={(e) => updateBasicInfo("phone", e.target.value)}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">营业时间</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>工作日</Label>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                        <Input
                          value={settings.basicInfo.businessHours.weekdays.open}
                          onChange={(e) => updateBusinessHours("weekdays", "open", e.target.value)}
                          placeholder="开始时间"
                        />
                      </div>
                      <span>至</span>
                      <Input
                        value={settings.basicInfo.businessHours.weekdays.close}
                        onChange={(e) => updateBusinessHours("weekdays", "close", e.target.value)}
                        placeholder="结束时间"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>周末</Label>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                        <Input
                          value={settings.basicInfo.businessHours.weekend.open}
                          onChange={(e) => updateBusinessHours("weekend", "open", e.target.value)}
                          placeholder="开始时间"
                        />
                      </div>
                      <span>至</span>
                      <Input
                        value={settings.basicInfo.businessHours.weekend.close}
                        onChange={(e) => updateBusinessHours("weekend", "close", e.target.value)}
                        placeholder="结束时间"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>节假日</Label>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                        <Input
                          value={settings.basicInfo.businessHours.holiday.open}
                          onChange={(e) => updateBusinessHours("holiday", "open", e.target.value)}
                          placeholder="开始时间"
                        />
                      </div>
                      <span>至</span>
                      <Input
                        value={settings.basicInfo.businessHours.holiday.close}
                        onChange={(e) => updateBusinessHours("holiday", "close", e.target.value)}
                        placeholder="结束时间"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">场馆图片</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label>场馆Logo</Label>
                    <div className="border rounded-md p-4 flex flex-col items-center justify-center">
                      <div className="w-32 h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
                        <Image className="h-8 w-8 text-gray-400" />
                      </div>
                      <Button variant="outline" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        上传Logo
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>场馆封面图</Label>
                    <div className="border rounded-md p-4 flex flex-col items-center justify-center">
                      <div className="w-full h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
                        <Image className="h-8 w-8 text-gray-400" />
                      </div>
                      <Button variant="outline" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        上传封面图
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>运营设置</CardTitle>
              <CardDescription>
                配置场馆的运营规则，包括预约、取消和提醒等设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between space-y-0">
                <div className="space-y-0.5">
                  <Label>允许在线预约</Label>
                  <p className="text-sm text-muted-foreground">
                    开启后，会员可以通过小程序预约课程
                  </p>
                </div>
                <Switch
                  checked={settings.operationSettings.allowOnlineBooking}
                  onCheckedChange={(checked) => updateOperationSettings("allowOnlineBooking", checked)}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">预约规则</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bookingAdvanceDays">提前预约天数</Label>
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="bookingAdvanceDays"
                        type="number"
                        min="1"
                        max="30"
                        value={settings.operationSettings.bookingAdvanceDays}
                        onChange={(e) => updateOperationSettings("bookingAdvanceDays", parseInt(e.target.value))}
                      />
                      <span className="ml-2">天</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      会员最多可以提前多少天预约课程
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cancelationDeadlineHours">取消预约截止时间</Label>
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="cancelationDeadlineHours"
                        type="number"
                        min="0"
                        max="48"
                        value={settings.operationSettings.cancelationDeadlineHours}
                        onChange={(e) => updateOperationSettings("cancelationDeadlineHours", parseInt(e.target.value))}
                      />
                      <span className="ml-2">小时</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      课程开始前多少小时内不允许取消预约
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maxClassSize">最大班级人数</Label>
                    <Input
                      id="maxClassSize"
                      type="number"
                      min="1"
                      max="100"
                      value={settings.operationSettings.maxClassSize}
                      onChange={(e) => updateOperationSettings("maxClassSize", parseInt(e.target.value))}
                    />
                    <p className="text-xs text-muted-foreground">
                      默认课程最大容纳人数，可在具体课程中单独设置
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableWaitlist">启用候补名单</Label>
                      <Switch
                        id="enableWaitlist"
                        checked={settings.operationSettings.enableWaitlist}
                        onCheckedChange={(checked) => updateOperationSettings("enableWaitlist", checked)}
                      />
                    </div>
                    <Input
                      id="waitlistLimit"
                      type="number"
                      min="1"
                      max="20"
                      value={settings.operationSettings.waitlistLimit}
                      onChange={(e) => updateOperationSettings("waitlistLimit", parseInt(e.target.value))}
                      disabled={!settings.operationSettings.enableWaitlist}
                    />
                    <p className="text-xs text-muted-foreground">
                      候补名单最大人数
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">通知设置</h3>
                <div className="flex items-center justify-between space-y-0">
                  <div className="space-y-0.5">
                    <Label>自动确认预约</Label>
                    <p className="text-sm text-muted-foreground">
                      开启后，会员预约课程将自动确认，无需人工审核
                    </p>
                  </div>
                  <Switch
                    checked={settings.operationSettings.autoConfirmBooking}
                    onCheckedChange={(checked) => updateOperationSettings("autoConfirmBooking", checked)}
                  />
                </div>

                <div className="flex items-center justify-between space-y-0">
                  <div className="space-y-0.5">
                    <Label>发送预约提醒</Label>
                    <p className="text-sm text-muted-foreground">
                      开启后，系统将在课程开始前发送提醒消息
                    </p>
                  </div>
                  <Switch
                    checked={settings.operationSettings.sendBookingReminders}
                    onCheckedChange={(checked) => updateOperationSettings("sendBookingReminders", checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reminderHoursBeforeClass">提醒时间</Label>
                  <div className="flex items-center">
                    <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="reminderHoursBeforeClass"
                      type="number"
                      min="1"
                      max="24"
                      value={settings.operationSettings.reminderHoursBeforeClass}
                      onChange={(e) => updateOperationSettings("reminderHoursBeforeClass", parseInt(e.target.value))}
                      disabled={!settings.operationSettings.sendBookingReminders}
                    />
                    <span className="ml-2">小时前</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    课程开始前多少小时发送提醒
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="holidays" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>节假日设置</CardTitle>
              <CardDescription>
                设置场馆的节假日安排，包括是否营业及特殊营业时间
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-end mb-4">
                <Button onClick={() => setIsAddHolidayDialogOpen(true)}>
                  <Calendar className="mr-2 h-4 w-4" />
                  添加节假日
                </Button>
              </div>

              <div className="space-y-4">
                {settings.holidaySettings.holidays.map((holiday) => (
                  <div key={holiday.id} className="flex items-center justify-between p-4 border rounded-md">
                    <div className="flex items-center gap-4">
                      <Calendar className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{holiday.name}</p>
                        <p className="text-sm text-muted-foreground">{holiday.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={holiday.isOpen}
                          onCheckedChange={(checked) => updateHolidaySetting(holiday.id, 'isOpen', checked)}
                        />
                        <span className="text-sm">{holiday.isOpen ? "营业" : "不营业"}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => deleteHoliday(holiday.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 添加节假日对话框 */}
          <Dialog open={isAddHolidayDialogOpen} onOpenChange={setIsAddHolidayDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>添加节假日</DialogTitle>
                <DialogDescription>
                  设置节假日信息，包括日期、名称和是否营业
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="holiday-date">节假日日期</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        id="holiday-date"
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !newHolidayDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {newHolidayDate ? format(newHolidayDate, "yyyy-MM-dd", { locale: zhCN }) : "选择日期"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={newHolidayDate}
                        onSelect={setNewHolidayDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="holiday-name">节假日名称</Label>
                  <Input
                    id="holiday-name"
                    value={newHolidayName}
                    onChange={(e) => setNewHolidayName(e.target.value)}
                    placeholder="例如：国庆节、春节等"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="holiday-is-open"
                    checked={newHolidayIsOpen}
                    onCheckedChange={setNewHolidayIsOpen}
                  />
                  <Label htmlFor="holiday-is-open">节假日是否营业</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddHolidayDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={addHoliday}>
                  添加
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </TabsContent>
      </Tabs>
    </div>
  )
}
