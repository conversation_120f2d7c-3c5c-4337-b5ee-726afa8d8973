-- 会员卡类型表 (Member Card Types)
-- 这个表定义了会员卡的模板/类型，比如"月卡"、"年卡"、"次卡"等

CREATE TABLE IF NOT EXISTS member_card_types (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 基本信息
  name VARCHAR(100) NOT NULL COMMENT '卡类型名称，如：月卡、年卡、10次卡',
  description TEXT COMMENT '卡类型描述',
  
  -- 价格信息
  price DECIMAL(10,2) NOT NULL COMMENT '售价',
  original_price DECIMAL(10,2) COMMENT '原价（用于显示折扣）',
  
  -- 使用规则
  validity_days INT COMMENT '有效期天数，NULL表示永久有效',
  usage_limit VARCHAR(50) COMMENT '使用限制，如：不限次数、10次、储值500元',
  
  -- 卡类型分类
  card_category ENUM('time', 'count', 'value') NOT NULL COMMENT '卡片类别：time=期限卡，count=次数卡，value=储值卡',
  card_type VARCHAR(50) NOT NULL COMMENT '具体卡类型标识',
  
  -- 状态管理
  status ENUM('销售中', '已下架', '已停用') DEFAULT '销售中' COMMENT '卡类型状态',
  
  -- 销售统计
  total_sold INT DEFAULT 0 COMMENT '总销售数量',
  active_cards INT DEFAULT 0 COMMENT '当前有效卡数量',
  
  -- 显示设置
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  color VARCHAR(7) COMMENT '显示颜色',
  
  -- 特殊标记
  is_trial_card BOOLEAN DEFAULT FALSE COMMENT '是否为体验卡',
  is_gift_card BOOLEAN DEFAULT FALSE COMMENT '是否为赠送卡',
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_status (status),
  INDEX idx_card_category (card_category),
  INDEX idx_display_order (display_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡类型表';

-- 会员卡实例表 (Member Cards)
-- 这个表存储发给具体会员的卡

CREATE TABLE IF NOT EXISTS member_cards (
  id INT AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 关联信息
  card_type_id INT NOT NULL COMMENT '关联的卡类型ID',
  member_id INT COMMENT '持卡会员ID',
  
  -- 卡片信息
  card_number VARCHAR(50) UNIQUE NOT NULL COMMENT '卡号',
  
  -- 使用状态
  status ENUM('有效', '已过期', '已冻结', '请假中', '已退卡') DEFAULT '有效' COMMENT '卡片状态',
  
  -- 时间信息
  issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发卡时间',
  activated_at TIMESTAMP NULL COMMENT '激活时间',
  expires_at TIMESTAMP NULL COMMENT '过期时间',
  
  -- 使用统计
  used_count INT DEFAULT 0 COMMENT '已使用次数',
  remaining_count INT COMMENT '剩余次数',
  remaining_value DECIMAL(10,2) COMMENT '剩余金额（储值卡）',
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 外键约束
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE RESTRICT,
  
  -- 索引
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_member_id (member_id),
  INDEX idx_card_number (card_number),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡实例表';

-- 插入一些示例会员卡类型数据
INSERT INTO member_card_types (tenant_id, name, description, price, original_price, validity_days, usage_limit, card_category, card_type, status, display_order, color, is_trial_card) VALUES
(2, '年卡', '365天不限次数瑜伽课程', 3680.00, 4680.00, 365, '不限次数', 'time', '年卡', '销售中', 1, '#4f46e5', FALSE),
(2, '季卡', '90天不限次数瑜伽课程', 1280.00, 1680.00, 90, '不限次数', 'time', '季卡', '销售中', 2, '#0ea5e9', FALSE),
(2, '月卡', '30天不限次数瑜伽课程', 580.00, 680.00, 30, '不限次数', 'time', '月卡', '销售中', 3, '#10b981', FALSE),
(2, '体验卡', '7天3次体验课程', 99.00, 199.00, 7, '最多3次', 'count', '体验卡', '销售中', 4, '#f59e0b', TRUE),
(2, '10次卡', '10次课程，180天有效', 880.00, 1080.00, 180, '10次', 'count', '次卡', '销售中', 5, '#8b5cf6', FALSE),
(2, '20次卡', '20次课程，365天有效', 1580.00, 1880.00, 365, '20次', 'count', '次卡', '已下架', 6, '#ec4899', FALSE),
(2, '储值卡500', '储值500元，365天有效', 450.00, 500.00, 365, '储值500元', 'value', '储值卡', '销售中', 7, '#06b6d4', FALSE),
(2, '储值卡1000', '储值1000元，365天有效', 850.00, 1000.00, 365, '储值1000元', 'value', '储值卡', '销售中', 8, '#0284c7', FALSE);
