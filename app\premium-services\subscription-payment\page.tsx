"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { ChevronRight, CreditCard, QrCode, RefreshCw, AlertCircle, CheckCircle, FileText, Smartphone, Calendar, Clock, DollarSign, Users, Repeat, Settings, Info, Download, Upload } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function SubscriptionPaymentPage() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("overview")
  const [showAgreementDialog, setShowAgreementDialog] = useState(false)
  const [showTestDialog, setShowTestDialog] = useState(false)
  const [showQRCode, setShowQRCode] = useState(false)
  const [testAmount, setTestAmount] = useState("0.01")
  const [testPeriod, setTestPeriod] = useState("month")
  const [testChannel, setTestChannel] = useState("alipay")

  // 模拟订阅记录数据
  const subscriptionRecords = [
    {
      id: "SUB20250328001",
      user: "张三",
      plan: "瑜伽月度会员",
      amount: "¥199.00",
      startDate: "2025-03-01",
      nextPaymentDate: "2025-04-01",
      status: "active",
      paymentMethod: "支付宝",
    },
    {
      id: "SUB20250327002",
      user: "李四",
      plan: "瑜伽季度会员",
      amount: "¥499.00",
      startDate: "2025-03-01",
      nextPaymentDate: "2025-06-01",
      status: "active",
      paymentMethod: "微信支付",
    },
    {
      id: "SUB20250326003",
      user: "王五",
      plan: "瑜伽年度会员",
      amount: "¥1,899.00",
      startDate: "2025-03-01",
      nextPaymentDate: "2026-03-01",
      status: "active",
      paymentMethod: "支付宝",
    },
    {
      id: "SUB20250325004",
      user: "赵六",
      plan: "瑜伽月度会员",
      amount: "¥199.00",
      startDate: "2025-02-01",
      nextPaymentDate: "2025-03-01",
      status: "canceled",
      paymentMethod: "微信支付",
    },
  ]

  // 模拟支付记录数据
  const paymentRecords = [
    {
      id: "PAY20250328001",
      subscriptionId: "SUB20250328001",
      user: "张三",
      plan: "瑜伽月度会员",
      amount: "¥199.00",
      date: "2025-03-01",
      status: "success",
      paymentMethod: "支付宝",
    },
    {
      id: "PAY20250327002",
      subscriptionId: "SUB20250327002",
      user: "李四",
      plan: "瑜伽季度会员",
      amount: "¥499.00",
      date: "2025-03-01",
      status: "success",
      paymentMethod: "微信支付",
    },
    {
      id: "PAY20250326003",
      subscriptionId: "SUB20250326003",
      user: "王五",
      plan: "瑜伽年度会员",
      amount: "¥1,899.00",
      date: "2025-03-01",
      status: "success",
      paymentMethod: "支付宝",
    },
    {
      id: "PAY20250225004",
      subscriptionId: "SUB20250325004",
      user: "赵六",
      plan: "瑜伽月度会员",
      amount: "¥199.00",
      date: "2025-02-01",
      status: "success",
      paymentMethod: "微信支付",
    },
  ]

  // 处理测试支付
  const handleTestPayment = () => {
    setShowTestDialog(false)
    setShowQRCode(true)

    // 模拟支付成功
    setTimeout(() => {
      setShowQRCode(false)
      toast({
        title: "测试支付成功",
        description: `已成功完成${testChannel === 'alipay' ? '支付宝' : '微信支付'}测试支付`,
      })
    }, 5000)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-1">
        <div className="text-sm breadcrumbs">
          <ul className="flex items-center space-x-1">
            <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
            <li><ChevronRight className="h-4 w-4" /></li>
            <li>订阅支付功能</li>
          </ul>
        </div>
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold tracking-tight">订阅支付功能</h1>
          <Badge variant="default">已开通</Badge>
        </div>
        <p className="text-muted-foreground">
          支持微信支付和支付宝的周期性扣款功能，适用于会员服务、订阅内容等场景
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 max-w-3xl">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="subscriptions">订阅管理</TabsTrigger>
          <TabsTrigger value="payments">支付记录</TabsTrigger>
          <TabsTrigger value="settings">配置设置</TabsTrigger>
          <TabsTrigger value="help">帮助文档</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">活跃订阅</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">3</div>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">本月收入</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">¥2,597.00</div>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">续订率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">95%</div>
                  <Repeat className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>支付渠道状态</CardTitle>
                <CardDescription>订阅支付渠道开通状态</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="h-8 w-8 rounded-full bg-[#1677FF] flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <p className="font-medium">支付宝订阅支付</p>
                      <p className="text-sm text-muted-foreground">支持周期性扣款</p>
                    </div>
                  </div>
                  <Badge variant="default">已开通</Badge>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="h-8 w-8 rounded-full bg-[#22AB39] flex items-center justify-center">
                      <CreditCard className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <p className="font-medium">微信支付订阅支付</p>
                      <p className="text-sm text-muted-foreground">支持周期性扣款</p>
                    </div>
                  </div>
                  <Badge variant="outline">申请中</Badge>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" onClick={() => setShowTestDialog(true)}>
                  测试支付
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>最近订阅</CardTitle>
                <CardDescription>最近的订阅记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {subscriptionRecords.filter(record => record.status === "active").slice(0, 3).map((record) => (
                    <div key={record.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{record.user}</p>
                        <p className="text-sm text-muted-foreground">{record.plan}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{record.amount}</p>
                        <p className="text-sm text-muted-foreground">下次扣款: {record.nextPaymentDate}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" onClick={() => setActiveTab("subscriptions")}>
                  查看全部
                </Button>
              </CardFooter>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>快速入门</CardTitle>
              <CardDescription>开始使用订阅支付功能</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">1. 配置支付渠道</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      配置支付宝和微信支付的商户信息和密钥
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full" onClick={() => setActiveTab("settings")}>
                      前往配置
                    </Button>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">2. 创建订阅计划</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      创建不同周期和价格的订阅计划
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full" onClick={() => setActiveTab("settings")}>
                      创建计划
                    </Button>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">3. 查看用户协议</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      查看并修改用户订阅协议模板
                    </p>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full" onClick={() => setShowAgreementDialog(true)}>
                      查看协议
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscriptions" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>订阅管理</CardTitle>
                  <CardDescription>管理所有用户的订阅</CardDescription>
                </div>
                <Button>
                  <FileText className="mr-2 h-4 w-4" />
                  导出数据
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>订阅ID</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>订阅计划</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>开始日期</TableHead>
                    <TableHead>下次扣款</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>支付方式</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {subscriptionRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium">{record.id}</TableCell>
                      <TableCell>{record.user}</TableCell>
                      <TableCell>{record.plan}</TableCell>
                      <TableCell>{record.amount}</TableCell>
                      <TableCell>{record.startDate}</TableCell>
                      <TableCell>{record.nextPaymentDate}</TableCell>
                      <TableCell>
                        <Badge variant={record.status === "active" ? "default" : "secondary"}>
                          {record.status === "active" ? "活跃" : "已取消"}
                        </Badge>
                      </TableCell>
                      <TableCell>{record.paymentMethod}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>支付记录</CardTitle>
                  <CardDescription>所有订阅支付的交易记录</CardDescription>
                </div>
                <Button>
                  <FileText className="mr-2 h-4 w-4" />
                  导出数据
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>交易ID</TableHead>
                    <TableHead>订阅ID</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>订阅计划</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>支付日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>支付方式</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paymentRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium">{record.id}</TableCell>
                      <TableCell>{record.subscriptionId}</TableCell>
                      <TableCell>{record.user}</TableCell>
                      <TableCell>{record.plan}</TableCell>
                      <TableCell>{record.amount}</TableCell>
                      <TableCell>{record.date}</TableCell>
                      <TableCell>
                        <Badge variant={record.status === "success" ? "default" : "destructive"}>
                          {record.status === "success" ? "成功" : "失败"}
                        </Badge>
                      </TableCell>
                      <TableCell>{record.paymentMethod}</TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="settings" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>支付渠道配置</CardTitle>
              <CardDescription>配置支付宝和微信支付的商户信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-4">支付宝配置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="alipay-app-id">AppID</Label>
                    <Input id="alipay-app-id" defaultValue="2021000000000000" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="alipay-private-key">应用私钥</Label>
                    <Input id="alipay-private-key" type="password" defaultValue="••••••••••••••••" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="alipay-public-key">支付宝公钥</Label>
                    <Input id="alipay-public-key" type="password" defaultValue="••••••••••••••••" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="alipay-notify-url">异步通知地址</Label>
                    <Input id="alipay-notify-url" defaultValue="https://youryogastudio.com/api/alipay/notify" />
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="space-y-0.5">
                    <Label htmlFor="alipay-enabled">启用支付宝订阅支付</Label>
                    <p className="text-sm text-muted-foreground">开启后，用户可以使用支付宝进行订阅支付</p>
                  </div>
                  <Switch id="alipay-enabled" defaultChecked />
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-medium mb-4">微信支付配置</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="wechat-app-id">AppID</Label>
                    <Input id="wechat-app-id" defaultValue="wx1234567890123456" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="wechat-mch-id">商户号</Label>
                    <Input id="wechat-mch-id" defaultValue="1234567890" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="wechat-api-key">API密钥</Label>
                    <Input id="wechat-api-key" type="password" defaultValue="••••••••••••••••" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="wechat-notify-url">异步通知地址</Label>
                    <Input id="wechat-notify-url" defaultValue="https://youryogastudio.com/api/wechat/notify" />
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="space-y-0.5">
                    <Label htmlFor="wechat-enabled">启用微信订阅支付</Label>
                    <p className="text-sm text-muted-foreground">开启后，用户可以使用微信进行订阅支付</p>
                  </div>
                  <Switch id="wechat-enabled" />
                </div>

                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>申请提示</AlertTitle>
                  <AlertDescription>
                    微信支付的订阅支付功能需要特殊申请，请联系微信支付商务经理或拨打95017进行申请。
                  </AlertDescription>
                </Alert>
              </div>

              <Separator />

              <div>
                <h3 className="text-lg font-medium mb-4">订阅计划配置</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base">月度会员</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">价格</span>
                            <span className="font-medium">¥199/月</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">周期</span>
                            <span>每月</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">状态</span>
                            <Badge variant="default">已启用</Badge>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button variant="outline" className="w-full">编辑</Button>
                      </CardFooter>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base">季度会员</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">价格</span>
                            <span className="font-medium">¥499/季度</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">周期</span>
                            <span>每3个月</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">状态</span>
                            <Badge variant="default">已启用</Badge>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button variant="outline" className="w-full">编辑</Button>
                      </CardFooter>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="text-base">年度会员</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">价格</span>
                            <span className="font-medium">¥1,899/年</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">周期</span>
                            <span>每12个月</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">状态</span>
                            <Badge variant="default">已启用</Badge>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button variant="outline" className="w-full">编辑</Button>
                      </CardFooter>
                    </Card>
                  </div>

                  <Button>
                    <FileText className="mr-2 h-4 w-4" />
                    添加订阅计划
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              <Button variant="outline">取消</Button>
              <Button onClick={() => {
                toast({
                  title: "配置已保存",
                  description: "订阅支付配置已成功保存",
                })
              }}>保存配置</Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="help" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>帮助文档</CardTitle>
              <CardDescription>了解如何使用订阅支付功能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">什么是订阅支付？</h3>
                <p className="text-muted-foreground">
                  订阅支付（也称为周期性扣款、商户代扣、自动扣费等）是指用户授权商户在特定条件下从其账户中自动扣款的支付方式。
                  这种支付方式特别适用于会员服务、订阅内容、分期付款等场景。
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">支付宝订阅支付</h3>
                <div className="space-y-2">
                  <p className="text-muted-foreground">
                    支付宝的订阅支付功能主要称为"商户代扣"或"周期扣款"，是一种基于用户授权的自动扣款服务。
                  </p>
                  <h4 className="font-medium">申请条件</h4>
                  <ul className="list-disc pl-5 text-muted-foreground">
                    <li>需要企业支付宝账户</li>
                    <li>需要完成企业实名认证</li>
                    <li>需要开通支付宝商家服务</li>
                    <li>特定行业需要相关经营资质（如健身行业需要营业执照等）</li>
                  </ul>
                  <h4 className="font-medium">扣款限制</h4>
                  <ul className="list-disc pl-5 text-muted-foreground">
                    <li>单笔扣款金额上限为5000元</li>
                    <li>每个自然月累计扣款不超过50000元</li>
                    <li>可设置扣款通知和扣款提醒</li>
                  </ul>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">微信支付订阅支付</h3>
                <div className="space-y-2">
                  <p className="text-muted-foreground">
                    微信支付的订阅支付功能主要称为"商户代扣"或"自动扣费"，允许商户在获得用户授权后定期从用户账户扣款。
                  </p>
                  <h4 className="font-medium">申请条件</h4>
                  <ul className="list-disc pl-5 text-muted-foreground">
                    <li>需要微信支付商户号</li>
                    <li>需要完成企业实名认证</li>
                    <li>需要开通特约商户功能</li>
                    <li>需要申请开通"自动扣费"产品权限</li>
                  </ul>
                  <h4 className="font-medium">申请流程</h4>
                  <p className="text-muted-foreground">
                    微信支付的自动扣费功能需要特殊申请，不在常规产品列表中显示。申请途径：
                  </p>
                  <ul className="list-disc pl-5 text-muted-foreground">
                    <li>联系微信支付商务经理</li>
                    <li>拨打微信支付商户服务热线 95017 咨询并申请商务对接</li>
                    <li>通过服务商申请</li>
                  </ul>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">常见问题</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium">如何处理扣款失败？</h4>
                    <p className="text-muted-foreground">
                      系统会根据失败原因（余额不足、账户异常等）采取不同策略，包括重试扣款或通知用户。
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium">用户如何取消订阅？</h4>
                    <p className="text-muted-foreground">
                      用户可以在会员中心或通过客服申请取消订阅，系统会调用相应的解约接口，解除签约关系。
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium">如何确保合规？</h4>
                    <p className="text-muted-foreground">
                      确保在签约前明确展示扣款规则和条款，保存用户授权证据，提供便捷的取消订阅途径，符合个人信息保护法规要求。
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  下载完整文档
                </Button>
                <Button variant="outline">
                  <Info className="mr-2 h-4 w-4" />
                  联系技术支持
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 用户协议对话框 */}
      <Dialog open={showAgreementDialog} onOpenChange={setShowAgreementDialog}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>订阅支付用户协议</DialogTitle>
            <DialogDescription>
              用户在使用订阅支付功能前需要同意的协议内容
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="agreement-title">协议标题</Label>
              <Input id="agreement-title" defaultValue="瑜伽馆会员订阅服务协议" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="agreement-content">协议内容</Label>
              <Textarea
                id="agreement-content"
                rows={15}
                defaultValue={`尊敬的用户：

感谢您选择我们的瑜伽会员订阅服务。在您开通订阅服务前，请仔细阅读本协议的全部内容。

一、服务说明
1.1 订阅服务内容：您订阅的会员服务包括但不限于瑜伽课程、场地使用、教练指导等权益。
1.2 订阅周期：根据您选择的套餐不同，订阅周期可能为月度、季度或年度。
1.3 自动续费：在订阅期限到期前，系统将自动从您的支付账户中扣款，为您延续会员服务。

二、扣款授权
2.1 您同意并授权我们在订阅期限到期前，通过支付宝/微信支付从您的账户中自动扣取下一期的订阅费用。
2.2 扣款时间通常为当前订阅周期到期前24小时。
2.3 每次扣款前，系统将向您发送扣款提醒通知。

三、取消订阅
3.1 您可以随时通过会员中心或联系客服取消订阅服务。
3.2 取消订阅后，系统将不再自动扣款，但当前订阅周期内的服务将继续有效至周期结束。
3.3 订阅取消后，如需重新开通，需要重新授权。

四、退款政策
4.1 订阅费用一经扣除，除法律规定的情形外，原则上不予退还。
4.2 如因系统故障导致服务无法使用，可申请相应的服务延期或退款。

五、价格调整
5.1 我们保留调整订阅价格的权利。
5.2 价格调整将提前30天通知您，您可以选择接受新价格或取消订阅。

六、其他条款
6.1 本协议的解释权归我公司所有。
6.2 本协议受中华人民共和国法律管辖。

特别提示：开通订阅即表示您已阅读并同意本协议的全部内容。`}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAgreementDialog(false)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: "协议已保存",
                description: "订阅支付用户协议已成功保存",
              })
              setShowAgreementDialog(false)
            }}>保存协议</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 测试支付对话框 */}
      <Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>测试订阅支付</DialogTitle>
            <DialogDescription>
              进行一次测试支付以验证支付渠道配置是否正确
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="test-amount">测试金额（元）</Label>
              <Input
                id="test-amount"
                value={testAmount}
                onChange={(e) => setTestAmount(e.target.value)}
                placeholder="请输入测试金额"
              />
              <p className="text-xs text-muted-foreground">建议使用小额如0.01元进行测试</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="test-period">订阅周期</Label>
              <Select value={testPeriod} onValueChange={setTestPeriod}>
                <SelectTrigger id="test-period">
                  <SelectValue placeholder="选择订阅周期" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="month">月度（每月）</SelectItem>
                  <SelectItem value="quarter">季度（每3个月）</SelectItem>
                  <SelectItem value="year">年度（每12个月）</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="test-channel">支付渠道</Label>
              <Select value={testChannel} onValueChange={setTestChannel}>
                <SelectTrigger id="test-channel">
                  <SelectValue placeholder="选择支付渠道" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="alipay">支付宝</SelectItem>
                  <SelectItem value="wechat">微信支付</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTestDialog(false)}>取消</Button>
            <Button onClick={handleTestPayment}>开始测试</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 支付二维码对话框 */}
      <Dialog open={showQRCode} onOpenChange={setShowQRCode}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>扫码支付</DialogTitle>
            <DialogDescription>
              请使用{testChannel === 'alipay' ? '支付宝' : '微信'}扫描下方二维码完成支付
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col items-center justify-center py-6">
            <div className={`border-4 ${testChannel === 'alipay' ? 'border-[#1677FF]' : 'border-[#22AB39]'} p-4 inline-block mb-4`}>
              <QrCode className={`h-48 w-48 ${testChannel === 'alipay' ? 'text-[#1677FF]' : 'text-[#22AB39]'}`} />
            </div>
            <div className="text-lg font-medium">¥{testAmount}</div>
            <div className="text-sm text-muted-foreground">测试订阅支付</div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowQRCode(false)}>取消支付</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
