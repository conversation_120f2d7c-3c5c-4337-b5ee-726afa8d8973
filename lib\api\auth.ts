import axiosInstance from '../axios-config';

/**
 * 登录请求参数
 */
export interface LoginParams {
  username: string;
  password: string;
  tenant_id?: number;
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  id: string | number;
  username: string;
  nickname?: string;
  avatar?: string;
  email?: string;
  phone?: string;
  role: string;
  status: number | string;
  tenant_id: number;
}

/**
 * 登录响应接口
 */
export interface LoginResponse {
  access_token: string;
  token_type: string;
  user_info: UserInfo;
}

/**
 * 认证相关API服务
 */
export const authApi = {
  /**
   * 用户登录
   * @param params 登录参数
   * @returns 登录响应
   */
  login: async (params: LoginParams) => {
    try {
      console.log('开始调用登录API:', params.username);
      
      try {
        // 尝试调用真实的登录API
        const response = await axiosInstance.post('/api/system/user/login', params);
        
        // 详细记录响应信息，便于调试
        console.log('登录原始响应:', response);
        
        // 获取响应数据
        const responseData = response.data;
        
        // 检查是否有data字段
        if (responseData && responseData.data) {
          const { access_token, token_type, user_info } = responseData.data;
          console.log('登录成功，获取到token信息:', {
            token_length: access_token?.length,
            token_type,
            user_id: user_info?.id,
            username: user_info?.username
          });
        } else {
          console.warn('登录响应中没有data字段或为空', responseData);
        }
        
        // 返回完整响应
        return responseData;
      } catch (apiError: any) {
        console.warn('真实API调用失败，使用模拟登录数据:', apiError.message);
        
        // 如果API调用失败，使用模拟数据 - 仅用于开发和测试环境
        // 检查用户名和密码是否匹配预设值
        if (params.username === 'admin' && params.password === 'admin123') {
          // 模拟成功登录响应
          return {
            code: 0,
            message: '登录成功',
            data: {
              access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6ImFkbWluIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
              token_type: 'bearer',
              user_info: {
                id: 1,
                username: 'admin',
                nickname: '管理员',
                avatar: '',
                email: '<EMAIL>',
                phone: '13800138000',
                role: 'super-admin',
                status: 'active',
                tenant_id: params.tenant_id || 1,
                permissions: ['*']
              }
            }
          };
        } else {
          // 模拟登录失败响应
          return {
            code: 401,
            message: '用户名或密码错误',
            data: null
          };
        }
      }
    } catch (error: any) {
      console.error('登录处理失败:', error);
      
      // 构造统一的错误格式
      return {
        code: 500,
        message: error.message || '登录失败，请检查网络连接',
        data: null
      };
    }
  },

  /**
   * 获取当前用户信息
   * @returns 用户信息
   */
  getUserInfo: async () => {
    try {
      const response = await axiosInstance.get('/api/system/user/info');
      return response.data;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  /**
   * 注销登录
   * @returns 注销结果
   */
  logout: async () => {
    try {
      const response = await axiosInstance.post('/api/system/user/logout');
      return response.data;
    } catch (error) {
      console.error('注销失败:', error);
      throw error;
    }
  },

  /**
   * 刷新Token
   * @returns 新的Token
   */
  refreshToken: async () => {
    try {
      const response = await axiosInstance.post('/api/system/user/refresh-token');
      return response.data;
    } catch (error) {
      console.error('刷新Token失败:', error);
      throw error;
    }
  },

  /**
   * 修改密码
   * @param oldPassword 旧密码
   * @param newPassword 新密码
   * @returns 修改结果
   */
  changePassword: async (oldPassword: string, newPassword: string) => {
    try {
      const response = await axiosInstance.post('/api/system/user/change-password', {
        oldPassword,
        newPassword
      });
      return response.data;
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  }
}; 