"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { HelpCircle } from "lucide-react"
import { ConsumptionRuleDialog } from "./consumption-rule-dialog"

interface RuleInfoButtonProps {
  ruleType?: "custom" | "average" | "actual" | "weighted"
  className?: string
  variant?: "default" | "outline" | "ghost" | "link"
}

export function RuleInfoButton({ 
  ruleType = "custom", 
  className,
  variant = "outline"
}: RuleInfoButtonProps) {
  const [dialogOpen, setDialogOpen] = useState(false)
  
  return (
    <>
      <Button 
        variant={variant} 
        size="sm" 
        className={className}
        onClick={() => setDialogOpen(true)}
      >
        <HelpCircle className="mr-1 h-4 w-4" />
        规则说明
      </Button>
      
      <ConsumptionRuleDialog 
        open={dialogOpen} 
        onOpenChange={setDialogOpen} 
        ruleType={ruleType}
      />
    </>
  )
}
