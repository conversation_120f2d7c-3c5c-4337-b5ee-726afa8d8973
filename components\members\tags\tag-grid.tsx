"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoreHorizontal, Pencil, Trash2, Users } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

type TagGridProps = {
  tags: any[]
  onTagClick: (tag: any) => void
  onEditClick: (tag: any) => void
  systemOnly?: boolean
}

export function TagGrid({ tags, onTagClick, onEditClick, systemOnly = false }: TagGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {tags.map((tag) => (
        <Card key={tag.id} className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => onTagClick(tag)}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <div className="flex items-center gap-2">
                <div className="h-6 w-6 rounded-full" style={{ backgroundColor: tag.color }} />
                <h3 className="font-semibold text-lg">{tag.name}</h3>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>操作</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation()
                      onTagClick(tag)
                    }}
                  >
                    <Users className="mr-2 h-4 w-4" />
                    查看会员
                  </DropdownMenuItem>
                  {!systemOnly && (
                    <>
                      <DropdownMenuItem
                        onClick={(e) => {
                          e.stopPropagation()
                          onEditClick(tag)
                        }}
                      >
                        <Pencil className="mr-2 h-4 w-4" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600" onClick={(e) => e.stopPropagation()}>
                        <Trash2 className="mr-2 h-4 w-4" />
                        删除
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground line-clamp-2">{tag.description}</p>
            <div className="flex flex-wrap gap-2 mt-3">
              <Badge variant="outline">{tag.category}</Badge>
              <Badge variant={tag.isAutoTag ? "secondary" : "outline"}>{tag.isAutoTag ? "自动" : "手动"}</Badge>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <div className="flex items-center text-sm text-muted-foreground">
              <Users className="mr-2 h-4 w-4" />
              <span>{tag.members} 位会员</span>
            </div>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

