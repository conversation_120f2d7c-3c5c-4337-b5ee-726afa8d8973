"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

const data = [
  {
    name: "1月",
    total: 1800,
  },
  {
    name: "2月",
    total: 2200,
  },
  {
    name: "3月",
    total: 2800,
  },
  {
    name: "4月",
    total: 2400,
  },
  {
    name: "5月",
    total: 2900,
  },
  {
    name: "6月",
    total: 3300,
  },
  {
    name: "7月",
    total: 3200,
  },
  {
    name: "8月",
    total: 3580,
  },
  {
    name: "9月",
    total: 3900,
  },
  {
    name: "10月",
    total: 4100,
  },
  {
    name: "11月",
    total: 4500,
  },
  {
    name: "12月",
    total: 5200,
  },
]

export function OverviewChart() {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <BarChart data={data}>
        <XAxis dataKey="name" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
        <YAxis
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={(value) => `¥${value}`}
        />
        <Bar dataKey="total" fill="currentColor" radius={[4, 4, 0, 0]} className="fill-primary" />
      </BarChart>
    </ResponsiveContainer>
  )
}

