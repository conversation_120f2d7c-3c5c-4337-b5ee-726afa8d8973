"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface PointRuleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editRule?: any // 如果是编辑，传入规则对象
}

export function PointRuleDialog({ open, onOpenChange, editRule }: PointRuleDialogProps) {
  const [ruleType, setRuleType] = useState(editRule?.type || "earn")
  const [calculationType, setCalculationType] = useState(editRule?.calculationType || "fixed")

  const isEditing = !!editRule

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEditing ? "编辑积分规则" : "添加积分规则"}</DialogTitle>
          <DialogDescription>
            {isEditing ? "修改现有积分规则的详细设置" : "创建新的积分获取或使用规则"}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">基本设置</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="rule-name">规则名称</Label>
                <Input id="rule-name" placeholder="输入规则名称" defaultValue={editRule?.name || ""} />
              </div>

              <div className="space-y-2">
                <Label>规则类型</Label>
                <RadioGroup defaultValue={ruleType} onValueChange={setRuleType} className="flex flex-col space-y-1">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="earn" id="earn" />
                    <Label htmlFor="earn">积分获取</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="spend" id="spend" />
                    <Label htmlFor="spend">积分使用</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <Label>积分计算方式</Label>
                <RadioGroup
                  defaultValue={calculationType}
                  onValueChange={setCalculationType}
                  className="flex flex-col space-y-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="fixed" id="fixed" />
                    <Label htmlFor="fixed">固定积分值</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="percentage" id="percentage" />
                    <Label htmlFor="percentage">按比例计算</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <Label htmlFor="points-value">
                  {calculationType === "fixed" ? "积分值" : ruleType === "earn" ? "积分比例 (%)" : "抵扣比例 (%)"}
                </Label>
                <Input
                  id="points-value"
                  type="number"
                  min="0"
                  step={calculationType === "percentage" ? "0.1" : "1"}
                  placeholder={calculationType === "fixed" ? "输入积分值" : "输入百分比"}
                  defaultValue={editRule?.value || ""}
                />
                {calculationType === "percentage" && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {ruleType === "earn"
                      ? "例如：输入1表示消费金额的1%将转换为积分"
                      : "例如：输入10表示100积分可抵扣10%的消费金额"}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="trigger-action">触发动作</Label>
                <Select defaultValue={editRule?.triggerAction || ""}>
                  <SelectTrigger id="trigger-action">
                    <SelectValue placeholder="选择触发动作" />
                  </SelectTrigger>
                  <SelectContent>
                    {ruleType === "earn" ? (
                      <>
                        <SelectItem value="purchase">课程购买</SelectItem>
                        <SelectItem value="checkin">课程签到</SelectItem>
                        <SelectItem value="referral">会员推荐</SelectItem>
                        <SelectItem value="birthday">生日特权</SelectItem>
                        <SelectItem value="registration">注册奖励</SelectItem>
                        <SelectItem value="review">评价奖励</SelectItem>
                        <SelectItem value="activity">活动参与</SelectItem>
                      </>
                    ) : (
                      <>
                        <SelectItem value="discount">课程抵扣</SelectItem>
                        <SelectItem value="product">兑换商品</SelectItem>
                        <SelectItem value="coupon">兑换优惠券</SelectItem>
                        <SelectItem value="service">增值服务</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="rule-description">规则描述</Label>
                <Textarea
                  id="rule-description"
                  placeholder="输入规则描述，将显示给会员"
                  rows={3}
                  defaultValue={editRule?.description || ""}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="member-level">适用会员等级</Label>
                <Select defaultValue={editRule?.memberLevel || "all"}>
                  <SelectTrigger id="member-level">
                    <SelectValue placeholder="选择会员等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有会员</SelectItem>
                    <SelectItem value="silver">银卡会员及以上</SelectItem>
                    <SelectItem value="gold">金卡会员及以上</SelectItem>
                    <SelectItem value="platinum">铂金会员及以上</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>规则状态</Label>
                <div className="flex items-center space-x-2">
                  <Switch id="rule-status" defaultChecked={editRule?.status !== "inactive"} />
                  <Label htmlFor="rule-status">启用规则</Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-points">单次最大积分</Label>
                <Input
                  id="max-points"
                  type="number"
                  min="0"
                  placeholder="0表示不限制"
                  defaultValue={editRule?.maxPoints || ""}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {ruleType === "earn"
                    ? "单次操作最多可获得的积分数量，0表示不限制"
                    : "单次操作最多可使用的积分数量，0表示不限制"}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="daily-limit">每日限制</Label>
                <Input
                  id="daily-limit"
                  type="number"
                  min="0"
                  placeholder="0表示不限制"
                  defaultValue={editRule?.dailyLimit || ""}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {ruleType === "earn"
                    ? "会员每日通过此规则最多可获得的积分数量，0表示不限制"
                    : "会员每日通过此规则最多可使用的积分数量，0表示不限制"}
                </p>
              </div>

              <div className="space-y-2">
                <Label>积分通知</Label>
                <div className="flex items-center space-x-2">
                  <Switch id="notify-member" defaultChecked={editRule?.notifyMember !== false} />
                  <Label htmlFor="notify-member">通知会员</Label>
                </div>
                <p className="text-xs text-muted-foreground mt-1">开启后，会员在积分变动时会收到通知</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="admin-notes">管理员备注</Label>
                <Textarea
                  id="admin-notes"
                  placeholder="输入管理员备注，仅管理员可见"
                  rows={2}
                  defaultValue={editRule?.adminNotes || ""}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button type="submit">{isEditing ? "保存修改" : "添加规则"}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

