"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  ArrowLeft, 
  Star, 
  Calendar, 
  Phone, 
  Mail, 
  Briefcase, 
  GraduationCap, 
  DollarSign,
  BarChart
} from "lucide-react"

// 模拟教练数据
const mockCoaches = [
  {
    id: "C001",
    name: "张教练",
    avatar: "/avatars/coach-01.jpg",
    status: "active",
    rating: 4.8,
    phone: "138****1234",
    email: "<EMAIL>",
    joinDate: "2023-05-15",
    gender: "女",
    age: 28,
    idNumber: "310******1234",
    address: "上海市浦东新区张江高科技园区",
    emergencyContact: "张先生 (138****5678)",
    specialty: ["基础瑜伽", "高级瑜伽"],
    courses: 120,
    students: 450,
    completionRate: 98,
    attendanceRate: 99,
    revenue: 45000,
    hourlyRate: 200,
    reviews: [
      {
        id: 1,
        studentName: "李同学",
        rating: 5,
        comment: "张教练非常专业，课程安排合理，动作讲解清晰，很有耐心。",
        date: "2025-03-20",
        course: "基础瑜伽入门"
      },
      {
        id: 2,
        studentName: "王同学",
        rating: 4,
        comment: "教练很专业，但课程节奏有点快，希望能更适合初学者。",
        date: "2025-03-15",
        course: "高级瑜伽进阶"
      }
    ]
  }
]

export default function CoachDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const coachId = params.id
  const [coach, setCoach] = useState<any>(null)
  
  // 获取教练数据
  useEffect(() => {
    const foundCoach = mockCoaches.find(c => c.id === coachId)
    if (foundCoach) {
      setCoach(foundCoach)
    }
  }, [coachId])
  
  // 如果教练不存在，显示加载中
  if (!coach) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold tracking-tight ml-2">加载中...</h1>
        </div>
      </div>
    )
  }
  
  return (
    <Dialog open={true} onOpenChange={() => router.back()}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <h2 className="text-xl font-semibold">{coach.name}</h2>
            <Badge
              className="ml-2"
              variant={coach.status === "active" ? "default" : coach.status === "leave" ? "secondary" : "outline"}
            >
              {coach.status === "active" ? "在职" : coach.status === "leave" ? "请假中" : "已离职"}
            </Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            返回
          </Button>
        </div>
        
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {/* 左侧信息栏 */}
          <div className="col-span-1">
            <div className="flex flex-col items-center text-center">
              <Avatar className="h-24 w-24">
                <AvatarImage src={coach.avatar} alt={coach.name} />
                <AvatarFallback className="text-xl">{coach.name[0]}</AvatarFallback>
              </Avatar>
              <h2 className="mt-3 text-xl font-semibold">{coach.name}</h2>
              <p className="text-sm text-muted-foreground">ID: {coach.id}</p>
              <div className="mt-2 flex items-center">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="ml-1 font-medium">{coach.rating}</span>
              </div>
              
              <div className="mt-4 flex flex-wrap justify-center gap-1">
                {coach.specialty.map((spec: string) => (
                  <Badge key={spec} variant="outline" className="text-xs">
                    {spec}
                  </Badge>
                ))}
              </div>
            </div>
            
            <div className="mt-6 space-y-3">
              <div className="flex items-center">
                <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>{coach.phone}</span>
              </div>
              <div className="flex items-center">
                <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>{coach.email}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>入职日期: {coach.joinDate}</span>
              </div>
            </div>
            
            <div className="mt-6 grid grid-cols-2 gap-2">
              <Button variant="outline" className="w-full" size="sm">
                <Phone className="mr-2 h-4 w-4" />
                电话
              </Button>
              <Button variant="outline" className="w-full" size="sm">
                <Mail className="mr-2 h-4 w-4" />
                邮件
              </Button>
            </div>
          </div>
          
          {/* 右侧主要内容 */}
          <div className="col-span-2">
            <Tabs defaultValue="info" className="space-y-4">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="info">基本信息</TabsTrigger>
                <TabsTrigger value="schedule">排课情况</TabsTrigger>
                <TabsTrigger value="performance">业绩统计</TabsTrigger>
                <TabsTrigger value="reviews">学员评价</TabsTrigger>
              </TabsList>
              
              <TabsContent value="info" className="space-y-4 max-h-[60vh] overflow-y-auto pr-2">
                <Card>
                  <CardHeader>
                    <CardTitle>个人资料</CardTitle>
                    <CardDescription>教练的详细个人资料和背景信息</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <Label className="text-muted-foreground">姓名</Label>
                        <p className="font-medium">{coach.name}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">性别</Label>
                        <p className="font-medium">{coach.gender}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">年龄</Label>
                        <p className="font-medium">{coach.age}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">身份证号</Label>
                        <p className="font-medium">{coach.idNumber}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">联系电话</Label>
                        <p className="font-medium">{coach.phone}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">电子邮箱</Label>
                        <p className="font-medium">{coach.email}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">住址</Label>
                        <p className="font-medium">{coach.address}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">紧急联系人</Label>
                        <p className="font-medium">{coach.emergencyContact}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>专业背景</CardTitle>
                    <CardDescription>教练的专业资质和工作经验</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className="text-muted-foreground">资质证书</Label>
                      <div className="mt-1 space-y-2">
                        <div className="flex items-center">
                          <GraduationCap className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>国际瑜伽联盟认证教练 (RYT-200)</span>
                        </div>
                        <div className="flex items-center">
                          <GraduationCap className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>阴瑜伽高级教练认证</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-muted-foreground">工作经验</Label>
                      <div className="mt-1 space-y-2">
                        <div className="flex items-start">
                          <Briefcase className="mr-2 h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">瑜伽时光工作室</p>
                            <p className="text-sm text-muted-foreground">2020-2023 | 高级瑜伽教练</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Briefcase className="mr-2 h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">和平瑜伽中心</p>
                            <p className="text-sm text-muted-foreground">2018-2020 | 瑜伽教练</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>合同信息</CardTitle>
                    <CardDescription>教练的合同和薪资信息</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <Label className="text-muted-foreground">入职日期</Label>
                        <p className="font-medium">{coach.joinDate}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">合同到期日</Label>
                        <p className="font-medium">2026-05-14</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">薪资类型</Label>
                        <p className="font-medium">底薪+课时费+提成</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">底薪</Label>
                        <p className="font-medium">¥5,000/月</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">课时费</Label>
                        <p className="font-medium">¥{coach.hourlyRate}/课时</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">提成比例</Label>
                        <p className="font-medium">销售额的5%</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="schedule">
                <Card>
                  <CardHeader>
                    <CardTitle>排课情况</CardTitle>
                    <CardDescription>教练的课程安排和上课时间表</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">暂无排课数据</p>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="performance">
                <Card>
                  <CardHeader>
                    <CardTitle>业绩统计</CardTitle>
                    <CardDescription>教练的课程和学员数据统计</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">总课程数</span>
                        <span className="font-medium">{coach.courses}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">总学员数</span>
                        <span className="font-medium">{coach.students}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">出勤率</span>
                        <span className="font-medium">{coach.attendanceRate}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">课程完成率</span>
                        <span className="font-medium">{coach.completionRate}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">总收入</span>
                        <span className="font-medium">¥{coach.revenue}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="reviews">
                <Card>
                  <CardHeader>
                    <CardTitle>学员评价</CardTitle>
                    <CardDescription>学员对教练的评价和反馈</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="text-2xl font-bold">{coach.rating}</div>
                          <div className="flex">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`h-5 w-5 ${
                                  i < Math.floor(coach.rating)
                                    ? "fill-yellow-400 text-yellow-400"
                                    : i < coach.rating
                                      ? "fill-yellow-400 text-yellow-400 opacity-50"
                                      : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                          <div className="text-sm text-muted-foreground">基于 68 条评价</div>
                        </div>
                      </div>

                      <div className="space-y-3 max-h-[40vh] overflow-y-auto pr-2">
                        {coach.reviews.map((review: any) => (
                          <Card key={review.id}>
                            <CardContent className="p-4">
                              <div className="flex justify-between">
                                <div className="font-medium">{review.studentName}</div>
                                <div className="text-sm text-muted-foreground">{review.date}</div>
                              </div>
                              <div className="flex items-center mt-1">
                                {Array.from({ length: 5 }).map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-4 w-4 ${
                                      i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                              <div className="mt-2">{review.comment}</div>
                              <div className="mt-1 text-sm text-muted-foreground">{review.course}</div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
