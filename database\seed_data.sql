-- 瑜伽馆管理系统种子数据

-- 1. 插入系统用户
INSERT INTO users (tenant_id, username, password, nickname, email, phone, role, status) VALUES
(1, 'admin', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '<EMAIL>', '13800138000', 'admin', 'active'),
(1, 'manager1', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '店长张三', '<EMAIL>', '13800138001', 'manager', 'active'),
(1, 'reception1', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '前台小李', '<EMAIL>', '13800138002', 'reception', 'active'),
(2, 'admin2', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '分店管理员', '<EMAIL>', '13800138003', 'admin', 'active'),
(2, 'coach1', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '教练王老师', '<EMAIL>', '13800138004', 'coach', 'active'),
(2, 'coach2', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '教练李老师', '<EMAIL>', '13800138005', 'coach', 'active');

-- 2. 插入会员数据
INSERT INTO members (tenant_id, member_no, name, phone, email, gender, birthday, level, points, total_spent, status, source) VALUES
(1, 'M001001', '张小美', '13900139001', '<EMAIL>', 'female', '1990-05-15', 'gold', 1200, 5680.00, 'active', 'online'),
(1, 'M001002', '李健康', '13900139002', '<EMAIL>', 'male', '1985-08-22', 'silver', 800, 3200.00, 'active', 'referral'),
(1, 'M001003', '王瑜伽', '13900139003', '<EMAIL>', 'female', '1992-12-10', 'platinum', 2500, 12800.00, 'active', 'offline'),
(1, 'M001004', '陈静心', '13900139004', '<EMAIL>', 'female', '1988-03-18', 'bronze', 300, 1200.00, 'active', 'promotion'),
(2, 'M002001', '刘柔韧', '13900139005', '<EMAIL>', 'female', '1991-07-25', 'gold', 1500, 6800.00, 'active', 'online'),
(2, 'M002002', '赵平衡', '13900139006', '<EMAIL>', 'male', '1987-11-30', 'silver', 600, 2400.00, 'active', 'offline'),
(2, 'M002003', '孙呼吸', '13900139007', '<EMAIL>', 'female', '1993-09-12', 'diamond', 3200, 18500.00, 'active', 'referral'),
(2, 'M002004', '周冥想', '13900139008', '<EMAIL>', 'female', '1989-04-08', 'gold', 1800, 8200.00, 'active', 'online');

-- 3. 插入会员卡实例数据
INSERT INTO member_cards (tenant_id, member_id, card_type_id, card_no, status, remaining_times, remaining_amount, remaining_days, activated_at, expires_at, purchase_price) VALUES
-- 租户1的会员卡
(1, 1, 1, 'C001001001', 'active', NULL, NULL, 280, '2024-01-15 10:00:00', '2024-12-31 23:59:59', 3680.00),
(1, 2, 2, 'C001002001', 'active', NULL, NULL, 65, '2024-03-01 09:30:00', '2024-05-30 23:59:59', 1280.00),
(1, 3, 3, 'C001003001', 'active', 8, NULL, NULL, '2024-02-10 14:20:00', '2024-08-10 23:59:59', 800.00),
(1, 4, 4, 'C001004001', 'active', NULL, 1850.50, NULL, '2024-04-05 16:45:00', '2024-10-05 23:59:59', 2000.00),
-- 租户2的会员卡
(2, 5, 1, 'C002001001', 'active', NULL, NULL, 320, '2024-01-20 11:15:00', '2025-01-19 23:59:59', 3680.00),
(2, 6, 2, 'C002002001', 'active', NULL, NULL, 45, '2024-04-10 08:45:00', '2024-07-09 23:59:59', 1280.00),
(2, 7, 5, 'C002003001', 'active', 15, NULL, NULL, '2024-03-15 13:30:00', '2024-09-15 23:59:59', 1500.00),
(2, 8, 1, 'C002004001', 'active', NULL, NULL, 295, '2024-02-28 10:20:00', '2025-02-27 23:59:59', 3680.00);

-- 4. 插入课程预约数据
INSERT INTO bookings (tenant_id, member_id, course_id, member_card_id, booking_no, status, booking_time, check_in_time, consumed_times, consumed_amount) VALUES
-- 租户1的预约记录
(1, 1, 1, 1, 'B001001001', 'completed', '2024-06-10 09:00:00', '2024-06-10 09:05:00', 0, 0),
(1, 1, 2, 1, 'B001001002', 'completed', '2024-06-12 10:30:00', '2024-06-12 10:25:00', 0, 0),
(1, 2, 3, 2, 'B001002001', 'completed', '2024-06-11 14:00:00', '2024-06-11 13:58:00', 0, 0),
(1, 3, 1, 3, 'B001003001', 'completed', '2024-06-13 09:00:00', '2024-06-13 09:02:00', 1, 0),
(1, 4, 4, 4, 'B001004001', 'completed', '2024-06-14 16:00:00', '2024-06-14 15:55:00', 0, 100.00),
-- 租户2的预约记录
(2, 5, 5, 5, 'B002001001', 'completed', '2024-06-10 10:00:00', '2024-06-10 10:03:00', 0, 0),
(2, 6, 6, 6, 'B002002001', 'booked', '2024-06-15 15:30:00', NULL, 0, 0),
(2, 7, 7, 7, 'B002003001', 'completed', '2024-06-12 11:00:00', '2024-06-12 10:58:00', 1, 0),
(2, 8, 5, 8, 'B002004001', 'checked_in', '2024-06-14 10:00:00', '2024-06-14 10:05:00', 0, 0);

-- 5. 插入订单数据
INSERT INTO orders (tenant_id, member_id, order_no, type, total_amount, paid_amount, status, payment_method, payment_time) VALUES
-- 租户1的订单
(1, 1, 'O001001001', 'member_card', 3680.00, 3680.00, 'paid', 'alipay', '2024-01-15 10:00:00'),
(1, 2, 'O001002001', 'member_card', 1280.00, 1280.00, 'paid', 'wechat', '2024-03-01 09:30:00'),
(1, 3, 'O001003001', 'member_card', 800.00, 800.00, 'paid', 'cash', '2024-02-10 14:20:00'),
(1, 4, 'O001004001', 'member_card', 2000.00, 2000.00, 'paid', 'card', '2024-04-05 16:45:00'),
-- 租户2的订单
(2, 5, 'O002001001', 'member_card', 3680.00, 3680.00, 'paid', 'alipay', '2024-01-20 11:15:00'),
(2, 6, 'O002002001', 'member_card', 1280.00, 1280.00, 'paid', 'wechat', '2024-04-10 08:45:00'),
(2, 7, 'O002003001', 'member_card', 1500.00, 1500.00, 'paid', 'alipay', '2024-03-15 13:30:00'),
(2, 8, 'O002004001', 'member_card', 3680.00, 3680.00, 'paid', 'transfer', '2024-02-28 10:20:00');

-- 6. 插入订单明细数据
INSERT INTO order_items (order_id, item_type, item_id, item_name, quantity, unit_price, total_price) VALUES
-- 租户1的订单明细
(1, 'member_card', 1, '年卡', 1, 3680.00, 3680.00),
(2, 'member_card', 2, '季卡', 1, 1280.00, 1280.00),
(3, 'member_card', 3, '10次卡', 1, 800.00, 800.00),
(4, 'member_card', 4, '储值卡2000', 1, 2000.00, 2000.00),
-- 租户2的订单明细
(5, 'member_card', 1, '年卡', 1, 3680.00, 3680.00),
(6, 'member_card', 2, '季卡', 1, 1280.00, 1280.00),
(7, 'member_card', 5, '20次卡', 1, 1500.00, 1500.00),
(8, 'member_card', 1, '年卡', 1, 3680.00, 3680.00);

-- 7. 更新会员卡类型的销售统计
UPDATE member_card_types SET 
  total_sold = (SELECT COUNT(*) FROM member_cards WHERE card_type_id = member_card_types.id),
  active_cards = (SELECT COUNT(*) FROM member_cards WHERE card_type_id = member_card_types.id AND status = 'active')
WHERE id IN (1, 2, 3, 4, 5, 6, 7, 8);
