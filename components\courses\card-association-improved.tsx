"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"

// 模拟会员卡数据 - 实际应用中应该从API获取
const membershipCards = [
  { id: "MC001", name: "瑜伽年卡", color: "#4CAF50", type: "time", validity: "365天", limit: "不限次数" },
  { id: "MC002", name: "瑜伽季卡", color: "#2196F3", type: "time", validity: "90天", limit: "不限次数" },
  { id: "MC003", name: "瑜伽月卡", color: "#FF9800", type: "time", validity: "30天", limit: "不限次数" },
  { id: "MC004", name: "私教次卡", color: "#9C27B0", type: "count", validity: "180天", limit: "20次" },
  { id: "MC005", name: "体验卡", color: "#F44336", type: "count", validity: "7天", limit: "3次" },
  { id: "MC006", name: "储值卡", color: "#8B5CF6", type: "value", validity: "365天", limit: "1000元" },
]

// 卡片类别
const cardCategories = [
  { id: "time", name: "时间卡" },
  { id: "count", name: "次数卡" },
  { id: "value", name: "储值卡" }
]

interface CardAssociationImprovedProps {
  selectedCards: string[]
  onSelectedCardsChange: (selectedCards: string[]) => void
  cardConsumption: any
  onCardConsumptionChange: (cardConsumption: any) => void
}

export function CardAssociationImproved({
  selectedCards,
  onSelectedCardsChange,
  cardConsumption,
  onCardConsumptionChange
}: CardAssociationImprovedProps) {
  // 处理卡片选择
  const handleCardSelect = (cardId: string, checked: boolean) => {
    if (checked) {
      onSelectedCardsChange([...selectedCards, cardId])
    } else {
      onSelectedCardsChange(selectedCards.filter(id => id !== cardId))
    }
  }

  // 清除所有选择
  const handleClearAll = () => {
    onSelectedCardsChange([])
  }

  // 选择所有卡片
  const handleSelectAll = () => {
    onSelectedCardsChange(membershipCards.map(card => card.id))
  }

  // 处理分类全选
  const handleSelectCategory = (categoryType: string, checked: boolean) => {
    const cardsInCategory = membershipCards.filter(card => card.type === categoryType)

    if (checked) {
      // 添加该分类下所有未选中的卡片
      const newSelectedCards = [...selectedCards]
      cardsInCategory.forEach(card => {
        if (!newSelectedCards.includes(card.id)) {
          newSelectedCards.push(card.id)
        }
      })
      onSelectedCardsChange(newSelectedCards)
    } else {
      // 移除该分类下所有卡片
      onSelectedCardsChange(selectedCards.filter(
        id => !cardsInCategory.some(card => card.id === id)
      ))
    }
  }

  // 检查分类是否全选
  const isCategoryAllSelected = (categoryType: string) => {
    const cardsInCategory = membershipCards.filter(card => card.type === categoryType)
    return cardsInCategory.every(card => selectedCards.includes(card.id))
  }

  // 处理消耗值变更
  const handleConsumptionChange = (cardId: string, type: string, value: number) => {
    const newCustomConsumption = {
      ...(cardConsumption?.customConsumption || {}),
      [cardId]: {
        ...(cardConsumption?.customConsumption?.[cardId] || {}),
        [type === "count" ? "count" : "value"]: value
      }
    }

    onCardConsumptionChange({
      ...cardConsumption,
      customConsumption: newCustomConsumption
    })
  }

  // 获取卡片消耗值
  const getConsumptionValue = (cardId: string, type: string) => {
    if (type === "count") {
      return cardConsumption?.customConsumption?.[cardId]?.count ||
             cardConsumption?.countCardConsumption || 1
    } else {
      return cardConsumption?.customConsumption?.[cardId]?.value ||
             cardConsumption?.valueCardConsumption || 100
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>会员卡类型</Label>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="select-all-cards"
            checked={selectedCards.length === membershipCards.length}
            onCheckedChange={(checked) => {
              if (checked) {
                handleSelectAll()
              } else {
                handleClearAll()
              }
            }}
          />
          <label
            htmlFor="select-all-cards"
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            全选
          </label>
        </div>
      </div>

      <div className="border rounded-md max-h-[400px] overflow-y-auto">
        {cardCategories.map((category) => {
          const cardsInCategory = membershipCards.filter(card => card.type === category.id)
          
          if (cardsInCategory.length === 0) return null
          
          return (
            <div key={category.id} className="p-4 border-b last:border-b-0">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium">{category.name}</h3>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id={`select-category-${category.id}`}
                    checked={isCategoryAllSelected(category.id)}
                    onCheckedChange={(checked) => handleSelectCategory(category.id, !!checked)}
                  />
                  <label
                    htmlFor={`select-category-${category.id}`}
                    className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    全选
                  </label>
                  <Badge variant="outline" className="text-xs">
                    {cardsInCategory.length}张
                  </Badge>
                </div>
              </div>
              
              {/* 会员卡列表表头 */}
              <div className="grid grid-cols-12 bg-muted px-2 py-2 rounded-md mb-2 text-xs font-medium">
                <div className="col-span-5">会员卡名称</div>
                <div className="col-span-3 text-center">有效期/限制</div>
                <div className="col-span-4 text-center">消耗值</div>
              </div>
              
              <div className="space-y-1">
                {cardsInCategory.map(card => (
                  <div 
                    key={card.id} 
                    className={cn(
                      "grid grid-cols-12 items-center py-2 px-2 hover:bg-accent/50 rounded-md",
                      selectedCards.includes(card.id) && "bg-primary/5"
                    )}
                  >
                    <div className="col-span-5 flex items-center">
                      <div
                        className="h-3 w-3 rounded-full mr-2"
                        style={{ backgroundColor: card.color }}
                      />
                      <label
                        htmlFor={`card-${card.id}`}
                        className="font-medium text-sm cursor-pointer"
                      >
                        {card.name}
                      </label>
                    </div>
                    
                    <div className="col-span-3 text-center text-xs text-muted-foreground">
                      {card.type === "time" ? card.validity : card.limit}
                    </div>
                    
                    <div className="col-span-4 flex items-center justify-center">
                      {card.type !== "time" && (
                        <div className="flex items-center mr-2">
                          <Input
                            type="number"
                            min={card.type === "count" ? "1" : "0"}
                            value={getConsumptionValue(card.id, card.type)}
                            onChange={(e) => {
                              const value = parseInt(e.target.value) || (card.type === "count" ? 1 : 0)
                              handleConsumptionChange(card.id, card.type, value)
                            }}
                            className="w-16 h-8 text-xs mr-1"
                          />
                          <span className="text-xs mr-2">{card.type === "count" ? "次" : "元"}</span>
                        </div>
                      )}
                      <Checkbox
                        id={`card-${card.id}`}
                        checked={selectedCards.includes(card.id)}
                        onCheckedChange={(checked) => handleCardSelect(card.id, !!checked)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        })}
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm">
          已选择 <span className="font-medium">{selectedCards.length}</span> 张会员卡
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearAll}
            disabled={selectedCards.length === 0}
          >
            清除全部
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
            disabled={selectedCards.length === membershipCards.length}
          >
            选择全部
          </Button>
        </div>
      </div>
    </div>
  )
}
