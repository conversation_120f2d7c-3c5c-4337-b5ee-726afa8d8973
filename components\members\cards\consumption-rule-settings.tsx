"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { RuleInfoIcon } from "./rule-info-icon"

interface ConsumptionRuleSettingsProps {
  cardType: "count" | "time" | "value"
  initialRule?: string
  onChange?: (rule: string) => void
}

export function ConsumptionRuleSettings({
  cardType,
  initialRule = "average",
  onChange
}: ConsumptionRuleSettingsProps) {
  const [consumptionRule, setConsumptionRule] = useState(initialRule)
  
  // 处理规则变更
  const handleRuleChange = (value: string) => {
    setConsumptionRule(value)
    if (onChange) {
      onChange(value)
    }
  }
  
  // 如果是时间卡，不需要显示消耗规则设置
  if (cardType === "time") {
    return null
  }
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base">消耗规则设置</CardTitle>
            <RuleInfoIcon 
              ruleType={consumptionRule === "average" ? "average" : 
                        consumptionRule === "fixed" ? "actual" : 
                        consumptionRule === "custom" ? "custom" : "weighted"}
            />
          </div>
        </CardHeader>
        <CardContent>
          <RadioGroup 
            value={consumptionRule} 
            onValueChange={handleRuleChange}
            className="space-y-4"
          >
            <div className="flex items-start space-x-2">
              <RadioGroupItem value="average" id="average" className="mt-1" />
              <div className="grid gap-1.5">
                <Label htmlFor="average" className="font-medium">平均消耗（总价值平均分配）</Label>
                <p className="text-sm text-muted-foreground">
                  会员卡总价值将平均分配到每次课程消耗中。例如：1000元的10次卡，每次消耗100元。
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-2">
              <RadioGroupItem value="fixed" id="fixed" className="mt-1" />
              <div className="grid gap-1.5">
                <Label htmlFor="fixed" className="font-medium">实际价格（按课程标准价格）</Label>
                <p className="text-sm text-muted-foreground">
                  根据课程的标准价格进行消耗。例如：标准价格为150元的课程，消耗150元。
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-2">
              <RadioGroupItem value="custom" id="custom" className="mt-1" />
              <div className="grid gap-1.5">
                <Label htmlFor="custom" className="font-medium">自定义消耗（每节课单独设置）</Label>
                <p className="text-sm text-muted-foreground">
                  为每种课程类型单独设置消耗值。例如：空中瑜伽消耗2次，垫上瑜伽消耗1次。
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-2">
              <RadioGroupItem value="weighted" id="weighted" className="mt-1" />
              <div className="grid gap-1.5">
                <Label htmlFor="weighted" className="font-medium">加权计算（按权重系数计算）</Label>
                <p className="text-sm text-muted-foreground">
                  为不同课程类型设置权重系数。例如：基础消耗为1次，空中瑜伽权重为1.5，则消耗1.5次。
                </p>
              </div>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>
    </div>
  )
}
