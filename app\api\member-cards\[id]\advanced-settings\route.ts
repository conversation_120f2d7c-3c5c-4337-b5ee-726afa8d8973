import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 获取会员卡高级设置
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
    console.log('获取会员卡高级设置，ID:', id);

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询会员卡高级设置
      const [advancedSettings] = await connection.execute(
        'SELECT * FROM member_card_advanced_settings WHERE card_type_id = ?',
        [parseInt(id)]
      );

      // 查询用卡人设置
      const [userSettings] = await connection.execute(
        'SELECT * FROM member_card_user_settings WHERE card_type_id = ?',
        [parseInt(id)]
      );

      // 查询课程设置
      const [courseSettings] = await connection.execute(
        'SELECT * FROM member_card_course_settings WHERE card_type_id = ?',
        [parseInt(id)]
      );

      // 查询课程关联
      const [courseAssociations] = await connection.execute(
        `SELECT
          mca.*,
          ct.name as course_type_name,
          ct.description as course_type_description
        FROM member_card_course_associations mca
        LEFT JOIN coursetype ct ON mca.course_type_id = ct.id
        WHERE mca.card_type_id = ?`,
        [parseInt(id)]
      );

      // 查询销售设置
      const [salesSettings] = await connection.execute(
        'SELECT * FROM member_card_sales_settings WHERE card_type_id = ?',
        [parseInt(id)]
      );

      const settings = {
        advanced: (advancedSettings as any[])[0] || null,
        user: (userSettings as any[])[0] || null,
        course: (courseSettings as any[])[0] || null,
        courseAssociations: courseAssociations || [],
        sales: (salesSettings as any[])[0] || null
      };

      console.log('获取会员卡高级设置成功');

      return NextResponse.json({
        code: 200,
        data: settings,
        msg: '获取会员卡高级设置成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取会员卡高级设置失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取会员卡高级设置失败',
      data: null
    }, { status: 500 });
  }
}

// 更新会员卡高级设置
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
    const data = await req.json();
    console.log('更新会员卡高级设置，ID:', id, '数据:', data);

    const { advanced, user, course, courseAssociations, sales } = data;

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.beginTransaction();

      // 更新高级设置
      if (advanced) {
        const advancedFields = [];
        const advancedValues = [];

        Object.keys(advanced).forEach(key => {
          if (key !== 'id' && key !== 'card_type_id' && key !== 'tenant_id' && key !== 'created_at' && key !== 'updated_at') {
            advancedFields.push(`${key} = ?`);
            advancedValues.push(advanced[key]);
          }
        });

        if (advancedFields.length > 0) {
          advancedValues.push(parseInt(id));
          await connection.execute(
            `UPDATE member_card_advanced_settings SET ${advancedFields.join(', ')} WHERE card_type_id = ?`,
            advancedValues
          );
        }
      }

      // 更新用户设置
      if (user) {
        const userFields = [];
        const userValues = [];

        Object.keys(user).forEach(key => {
          if (key !== 'id' && key !== 'card_type_id' && key !== 'tenant_id' && key !== 'created_at' && key !== 'updated_at') {
            userFields.push(`${key} = ?`);
            userValues.push(user[key]);
          }
        });

        if (userFields.length > 0) {
          userValues.push(parseInt(id));
          await connection.execute(
            `UPDATE member_card_user_settings SET ${userFields.join(', ')} WHERE card_type_id = ?`,
            userValues
          );
        }
      }

      // 更新课程设置
      if (course) {
        const courseFields = [];
        const courseValues = [];

        Object.keys(course).forEach(key => {
          if (key !== 'id' && key !== 'card_type_id' && key !== 'tenant_id' && key !== 'created_at' && key !== 'updated_at') {
            courseFields.push(`${key} = ?`);
            courseValues.push(course[key]);
          }
        });

        if (courseFields.length > 0) {
          courseValues.push(parseInt(id));
          await connection.execute(
            `UPDATE member_card_course_settings SET ${courseFields.join(', ')} WHERE card_type_id = ?`,
            courseValues
          );
        }
      }

      // 更新课程关联
      if (courseAssociations && Array.isArray(courseAssociations)) {
        // 先删除现有关联
        await connection.execute(
          'DELETE FROM member_card_course_associations WHERE card_type_id = ?',
          [parseInt(id)]
        );

        // 插入新关联
        for (const association of courseAssociations) {
          await connection.execute(
            `INSERT INTO member_card_course_associations
            (card_type_id, course_type_id, tenant_id, is_enabled, consumption_times, course_type_name, course_duration)
            VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              parseInt(id),
              association.course_type_id,
              association.tenant_id || 2, // 默认租户ID
              association.is_enabled !== false, // 默认启用
              association.consumption_times || 1.0,
              association.course_type_name || '',
              association.course_duration || 60
            ]
          );
        }
      }

      // 更新销售设置
      if (sales) {
        const salesFields = [];
        const salesValues = [];

        Object.keys(sales).forEach(key => {
          if (key !== 'id' && key !== 'card_type_id' && key !== 'tenant_id' && key !== 'created_at' && key !== 'updated_at') {
            salesFields.push(`${key} = ?`);
            salesValues.push(sales[key]);
          }
        });

        if (salesFields.length > 0) {
          salesValues.push(parseInt(id));
          await connection.execute(
            `UPDATE member_card_sales_settings SET ${salesFields.join(', ')} WHERE card_type_id = ?`,
            salesValues
          );
        }
      }

      await connection.commit();

      console.log('更新会员卡高级设置成功');

      return NextResponse.json({
        code: 200,
        data: { id: parseInt(id) },
        msg: '更新会员卡高级设置成功'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  } catch (error: any) {
    console.error('更新会员卡高级设置失败:', error);
    return NextResponse.json({
      code: 500,
      msg: error.message || '更新会员卡高级设置失败',
      data: null
    }, { status: 500 });
  }
}


