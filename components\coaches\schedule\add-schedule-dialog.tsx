"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import { useState } from "react"
import { DatePicker } from "@/components/coaches/schedule/date-picker"

interface AddScheduleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddScheduleDialog({ open, onOpenChange }: AddScheduleDialogProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [isRecurring, setIsRecurring] = useState(false)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>添加排班</DialogTitle>
          <DialogDescription>为教练添加新的课程排班</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="coach">选择教练</Label>
            <Select>
              <SelectTrigger id="coach">
                <SelectValue placeholder="请选择教练" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">张教练</SelectItem>
                <SelectItem value="2">李教练</SelectItem>
                <SelectItem value="3">王教练</SelectItem>
                <SelectItem value="4">赵教练</SelectItem>
                <SelectItem value="5">刘教练</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="course-type">课程类型</Label>
            <Select>
              <SelectTrigger id="course-type">
                <SelectValue placeholder="请选择课程类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="basic">基础瑜伽</SelectItem>
                <SelectItem value="advanced">高级瑜伽</SelectItem>
                <SelectItem value="yin">阴瑜伽</SelectItem>
                <SelectItem value="prenatal">孕产瑜伽</SelectItem>
                <SelectItem value="aerial">空中瑜伽</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="venue">场地</Label>
            <Select>
              <SelectTrigger id="venue">
                <SelectValue placeholder="请选择场地" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">1号瑜伽室</SelectItem>
                <SelectItem value="2">2号瑜伽室</SelectItem>
                <SelectItem value="3">3号瑜伽室</SelectItem>
                <SelectItem value="4">空中瑜伽室</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>日期</Label>
            <DatePicker date={selectedDate} onSelect={setSelectedDate} />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start-time">开始时间</Label>
              <Input id="start-time" type="time" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end-time">结束时间</Label>
              <Input id="end-time" type="time" />
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="recurring"
                checked={isRecurring}
                onCheckedChange={(checked) => setIsRecurring(checked === true)}
              />
              <Label htmlFor="recurring" className="font-normal">
                重复排班
              </Label>
            </div>
          </div>

          {isRecurring && (
            <div className="space-y-4 rounded-md border p-4">
              <div className="space-y-2">
                <Label>重复频率</Label>
                <Select defaultValue="weekly">
                  <SelectTrigger>
                    <SelectValue placeholder="请选择重复频率" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">每天</SelectItem>
                    <SelectItem value="weekly">每周</SelectItem>
                    <SelectItem value="biweekly">每两周</SelectItem>
                    <SelectItem value="monthly">每月</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>重复日期</Label>
                <div className="flex flex-wrap gap-2">
                  {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Checkbox id={`day-${index}`} />
                      <Label htmlFor={`day-${index}`} className="font-normal">
                        {day}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="end-date">结束日期</Label>
                <Input id="end-date" type="date" />
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="capacity">课程容量</Label>
            <Input id="capacity" type="number" defaultValue="20" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">备注</Label>
            <Textarea id="notes" placeholder="添加关于此排班的备注信息" />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

