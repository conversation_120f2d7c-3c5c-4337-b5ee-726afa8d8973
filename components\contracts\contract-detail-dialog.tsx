"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import {
  FileText,
  Download,
  Send,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  User,
  Building2,
  Phone,
  Mail,
  FileSignature,
  Trash2,
  Eye
} from "lucide-react"
import { ContractTemplatePreview } from "./contract-template-preview"

interface ContractDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  contract: any
}

export function ContractDetailDialog({ open, onOpenChange, contract }: ContractDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("overview")

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <Badge className="bg-green-500">已完成</Badge>
      case "SIGNING":
        return <Badge className="bg-blue-500">签署中</Badge>
      case "DRAFT":
        return <Badge variant="outline">草稿</Badge>
      case "REJECTED":
        return <Badge variant="destructive">已拒绝</Badge>
      case "EXPIRED":
        return <Badge variant="secondary">已过期</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <CheckCircle className="h-8 w-8 text-green-500" />
      case "SIGNING":
        return <Clock className="h-8 w-8 text-blue-500" />
      case "DRAFT":
        return <FileText className="h-8 w-8 text-gray-500" />
      case "REJECTED":
        return <XCircle className="h-8 w-8 text-red-500" />
      case "EXPIRED":
        return <AlertCircle className="h-8 w-8 text-yellow-500" />
      default:
        return <FileText className="h-8 w-8 text-gray-500" />
    }
  }

  // 下载合同
  const handleDownload = () => {
    toast({
      title: "开始下载",
      description: "合同文件下载已开始",
    })
  }

  // 查看合同
  const handleViewContract = () => {
    // 如果合同有关联的模板，则打开模板预览
    if (contract.templateName) {
      // 根据模板名称构建URL
      // 将中文模板名称映射到英文文件名
      let templateFileName = '';

      switch (contract.templateName) {
        case "标准会员协议":
          templateFileName = "standard-member-agreement.html";
          break;
        case "会员卡标准协议":
          templateFileName = "membership-card-agreement.html";
          break;
        case "标准教练合同":
          templateFileName = "coach-employment-contract.html";
          break;
        case "场地租赁标准协议":
          templateFileName = "venue-rental-agreement.html";
          break;
        case "设备采购标准协议":
          templateFileName = "equipment-purchase-agreement.html";
          break;
        case "消费型股东协议":
          templateFileName = "consumer-shareholder-agreement.html";
          break;
        case "投资型股东协议":
          templateFileName = "investor-shareholder-agreement.html";
          break;
        case "资源型股东协议":
          templateFileName = "resource-shareholder-agreement.html";
          break;
        case "员工型股东协议":
          templateFileName = "employee-shareholder-agreement.html";
          break;
        case "联盟型股东协议":
          templateFileName = "alliance-shareholder-agreement.html";
          break;
        default:
          // 如果没有匹配的模板，尝试使用原来的方法
          templateFileName = `${contract.templateName.toLowerCase().replace(/\s+/g, '-')}.html`;
      }

      const templateUrl = `/contract-templates/${templateFileName}`;
      window.open(templateUrl, '_blank');
    } else {
      toast({
        title: "查看失败",
        description: "合同预览暂不可用",
        variant: "destructive"
      })
    }
  }

  // 发起签署
  const handleInitiateSign = () => {
    toast({
      title: "发起签署",
      description: "合同签署流程已启动",
    })
  }

  // 催签
  const handleRemind = () => {
    toast({
      title: "催签成功",
      description: "已向未签署方发送提醒",
    })
  }

  // 作废合同
  const handleInvalidate = () => {
    toast({
      title: "作废成功",
      description: "合同已作废",
    })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            {contract.title}
          </DialogTitle>
          <DialogDescription>
            合同编号: {contract.serialNumber}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">合同概览</TabsTrigger>
            <TabsTrigger value="parties">签署方</TabsTrigger>
            <TabsTrigger value="history">签署记录</TabsTrigger>
          </TabsList>

          {/* 合同概览 */}
          <TabsContent value="overview" className="space-y-4 mt-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">合同状态</h3>
                  {getStatusBadge(contract.status)}
                </div>
                <div className="flex flex-col items-center justify-center py-6">
                  {getStatusIcon(contract.status)}
                  <p className="mt-2 text-center">
                    {contract.status === "COMPLETED" && "所有方已完成签署"}
                    {contract.status === "SIGNING" && "等待签署方完成签署"}
                    {contract.status === "DRAFT" && "合同草稿尚未发起签署"}
                    {contract.status === "REJECTED" && "签署方拒绝签署"}
                    {contract.status === "EXPIRED" && "合同已过期"}
                  </p>
                  <div className="mt-4 w-full">
                    {contract.status === "DRAFT" && (
                      <Button className="w-full" onClick={handleInitiateSign}>
                        <Send className="mr-2 h-4 w-4" />
                        发起签署
                      </Button>
                    )}
                    {contract.status === "SIGNING" && (
                      <Button className="w-full" onClick={handleRemind}>
                        <AlertCircle className="mr-2 h-4 w-4" />
                        催签
                      </Button>
                    )}
                    {contract.status === "COMPLETED" && (
                      <Button className="w-full" onClick={handleDownload}>
                        <Download className="mr-2 h-4 w-4" />
                        下载合同
                      </Button>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex-1 border rounded-lg p-4">
                <h3 className="text-lg font-medium mb-4">合同信息</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">合同标题:</span>
                    <span className="font-medium">{contract.title}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">合同编号:</span>
                    <span className="font-medium">{contract.serialNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">合同模板:</span>
                    <span className="font-medium">{contract.templateName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">合同类型:</span>
                    <span className="font-medium">{contract.category}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">创建时间:</span>
                    <span className="font-medium">{contract.createdAt}</span>
                  </div>
                  {contract.completedAt && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">完成时间:</span>
                      <span className="font-medium">{contract.completedAt}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">合同预览</h3>
              <ContractTemplatePreview
                previewUrl={(() => {
                  // 根据模板名称构建URL
                  if (!contract.templateName) return null;

                  let templateFileName = '';
                  switch (contract.templateName) {
                    case "标准会员协议":
                      templateFileName = "standard-member-agreement.html";
                      break;
                    case "会员卡标准协议":
                      templateFileName = "membership-card-agreement.html";
                      break;
                    case "标准教练合同":
                      templateFileName = "coach-employment-contract.html";
                      break;
                    case "场地租赁标准协议":
                      templateFileName = "venue-rental-agreement.html";
                      break;
                    case "设备采购标准协议":
                      templateFileName = "equipment-purchase-agreement.html";
                      break;
                    case "消费型股东协议":
                      templateFileName = "consumer-shareholder-agreement.html";
                      break;
                    case "投资型股东协议":
                      templateFileName = "investor-shareholder-agreement.html";
                      break;
                    case "资源型股东协议":
                      templateFileName = "resource-shareholder-agreement.html";
                      break;
                    case "员工型股东协议":
                      templateFileName = "employee-shareholder-agreement.html";
                      break;
                    case "联盟型股东协议":
                      templateFileName = "alliance-shareholder-agreement.html";
                      break;
                    default:
                      templateFileName = `${contract.templateName.toLowerCase().replace(/\s+/g, '-')}.html`;
                  }

                  return `/contract-templates/${templateFileName}`;
                })()}
                templateName={contract.templateName || ""}
                onPreview={handleViewContract}
              />
            </div>
          </TabsContent>

          {/* 签署方 */}
          <TabsContent value="parties" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {contract.parties.map((party: any, index: number) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center mb-4">
                    {party.type === "COMPANY" ? (
                      <Building2 className="h-8 w-8 mr-2 text-blue-500" />
                    ) : (
                      <User className="h-8 w-8 mr-2 text-green-500" />
                    )}
                    <div>
                      <h3 className="font-medium">{party.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {party.type === "COMPANY" ? "企业签署方" : "个人签署方"}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    {party.mobile && (
                      <div className="flex items-center text-sm">
                        <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{party.mobile}</span>
                      </div>
                    )}
                    {party.email && (
                      <div className="flex items-center text-sm">
                        <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{party.email}</span>
                      </div>
                    )}
                  </div>
                  <div className="mt-4 pt-4 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">签署状态</span>
                      {contract.status === "COMPLETED" ? (
                        <Badge className="bg-green-500">已签署</Badge>
                      ) : contract.status === "REJECTED" ? (
                        <Badge variant="destructive">已拒绝</Badge>
                      ) : contract.status === "SIGNING" ? (
                        <Badge className="bg-blue-500">待签署</Badge>
                      ) : (
                        <Badge variant="outline">未开始</Badge>
                      )}
                    </div>
                    {contract.status === "SIGNING" && (
                      <Button variant="outline" size="sm" className="w-full mt-2">
                        <Send className="mr-2 h-4 w-4" />
                        发送签署链接
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          {/* 签署记录 */}
          <TabsContent value="history" className="space-y-4 mt-4">
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-4">签署流程记录</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-4">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-500">
                      <FileText className="h-4 w-4" />
                    </div>
                  </div>
                  <div>
                    <p className="font-medium">合同创建</p>
                    <p className="text-sm text-muted-foreground">{contract.createdAt}</p>
                    <p className="text-sm mt-1">系统创建了合同</p>
                  </div>
                </div>

                {contract.status !== "DRAFT" && (
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-4">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-500">
                        <Send className="h-4 w-4" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">发起签署</p>
                      <p className="text-sm text-muted-foreground">{contract.createdAt}</p>
                      <p className="text-sm mt-1">系统发起了合同签署流程</p>
                    </div>
                  </div>
                )}

                {contract.status === "COMPLETED" && (
                  <>
                    {contract.parties.map((party: any, index: number) => (
                      <div key={index} className="flex items-start">
                        <div className="flex-shrink-0 mr-4">
                          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-500">
                            <FileSignature className="h-4 w-4" />
                          </div>
                        </div>
                        <div>
                          <p className="font-medium">{party.name} 完成签署</p>
                          <p className="text-sm text-muted-foreground">{contract.completedAt}</p>
                          <p className="text-sm mt-1">{party.type === "COMPANY" ? "企业" : "个人"}签署方完成了签署</p>
                        </div>
                      </div>
                    ))}

                    <div className="flex items-start">
                      <div className="flex-shrink-0 mr-4">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-500">
                          <CheckCircle className="h-4 w-4" />
                        </div>
                      </div>
                      <div>
                        <p className="font-medium">合同签署完成</p>
                        <p className="text-sm text-muted-foreground">{contract.completedAt}</p>
                        <p className="text-sm mt-1">所有签署方已完成签署，合同生效</p>
                      </div>
                    </div>
                  </>
                )}

                {contract.status === "REJECTED" && (
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mr-4">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-red-100 text-red-500">
                        <XCircle className="h-4 w-4" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">签署被拒绝</p>
                      <p className="text-sm text-muted-foreground">2023-06-16 10:30</p>
                      <p className="text-sm mt-1">签署方拒绝了签署请求</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          {contract.status === "DRAFT" && (
            <Button variant="destructive" onClick={handleInvalidate}>
              <Trash2 className="mr-2 h-4 w-4" />
              删除合同
            </Button>
          )}
          {contract.status !== "DRAFT" && contract.status !== "COMPLETED" && (
            <Button variant="destructive" onClick={handleInvalidate}>
              <XCircle className="mr-2 h-4 w-4" />
              作废合同
            </Button>
          )}
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
