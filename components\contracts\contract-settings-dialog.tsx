"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { Settings, Save, RefreshCw } from "lucide-react"

interface ContractSettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ContractSettingsDialog({ open, onOpenChange }: ContractSettingsDialogProps) {
  const [settings, setSettings] = useState({
    appId: "fadada123456",
    appSecret: "******************************",
    serverUrl: "https://api.fadada.com/api/v2",
    callbackUrl: "https://api.yourdomain.com/webhook/fadada",
    companyName: "静心瑜伽馆",
    companyId: "91110000123456789X",
    legalRepresentative: "张三",
    contactPhone: "010-12345678",
    enableAutoArchive: true,
    archiveDays: "30",
    enableReminder: true,
    reminderDays: "3",
    reminderInterval: "24",
  })

  const handleChange = (field: string, value: string | boolean) => {
    setSettings({
      ...settings,
      [field]: value
    })
  }

  const handleSave = () => {
    // 模拟保存设置
    console.log("保存设置:", settings)
    
    toast({
      title: "保存成功",
      description: "法大大电子合同设置已更新",
    })
    
    onOpenChange(false)
  }

  const handleReset = () => {
    // 重置为默认设置
    setSettings({
      appId: "fadada123456",
      appSecret: "******************************",
      serverUrl: "https://api.fadada.com/api/v2",
      callbackUrl: "https://api.yourdomain.com/webhook/fadada",
      companyName: "静心瑜伽馆",
      companyId: "91110000123456789X",
      legalRepresentative: "张三",
      contactPhone: "010-12345678",
      enableAutoArchive: true,
      archiveDays: "30",
      enableReminder: true,
      reminderDays: "3",
      reminderInterval: "24",
    })
    
    toast({
      title: "重置成功",
      description: "设置已重置为默认值",
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Settings className="mr-2 h-5 w-5" />
            法大大电子合同设置
          </DialogTitle>
          <DialogDescription>
            配置法大大电子合同的API参数和系统设置
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">API配置</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="appId">应用ID (AppId)</Label>
                <Input 
                  id="appId" 
                  value={settings.appId}
                  onChange={(e) => handleChange("appId", e.target.value)}
                />
                <p className="text-xs text-muted-foreground">在法大大开发者平台获取的应用ID</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="appSecret">应用密钥 (AppSecret)</Label>
                <Input 
                  id="appSecret" 
                  type="password" 
                  value={settings.appSecret}
                  onChange={(e) => handleChange("appSecret", e.target.value)}
                />
                <p className="text-xs text-muted-foreground">在法大大开发者平台获取的应用密钥</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="serverUrl">服务器地址</Label>
                <Input 
                  id="serverUrl" 
                  value={settings.serverUrl}
                  onChange={(e) => handleChange("serverUrl", e.target.value)}
                />
                <p className="text-xs text-muted-foreground">法大大API服务器地址</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="callbackUrl">回调地址</Label>
                <Input 
                  id="callbackUrl" 
                  value={settings.callbackUrl}
                  onChange={(e) => handleChange("callbackUrl", e.target.value)}
                />
                <p className="text-xs text-muted-foreground">接收法大大回调通知的地址</p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">公司信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="companyName">公司名称</Label>
                <Input 
                  id="companyName" 
                  value={settings.companyName}
                  onChange={(e) => handleChange("companyName", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="companyId">统一社会信用代码</Label>
                <Input 
                  id="companyId" 
                  value={settings.companyId}
                  onChange={(e) => handleChange("companyId", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="legalRepresentative">法定代表人</Label>
                <Input 
                  id="legalRepresentative" 
                  value={settings.legalRepresentative}
                  onChange={(e) => handleChange("legalRepresentative", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactPhone">联系电话</Label>
                <Input 
                  id="contactPhone" 
                  value={settings.contactPhone}
                  onChange={(e) => handleChange("contactPhone", e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">系统设置</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enableAutoArchive">自动归档</Label>
                  <p className="text-xs text-muted-foreground">
                    自动归档已完成的合同
                  </p>
                </div>
                <Switch 
                  id="enableAutoArchive" 
                  checked={settings.enableAutoArchive}
                  onCheckedChange={(checked) => handleChange("enableAutoArchive", checked)}
                />
              </div>
              
              {settings.enableAutoArchive && (
                <div className="ml-6 space-y-2">
                  <Label htmlFor="archiveDays">归档天数</Label>
                  <Input 
                    id="archiveDays" 
                    type="number" 
                    value={settings.archiveDays}
                    onChange={(e) => handleChange("archiveDays", e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    合同完成后多少天自动归档
                  </p>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="enableReminder">签署提醒</Label>
                  <p className="text-xs text-muted-foreground">
                    自动提醒未签署的合同
                  </p>
                </div>
                <Switch 
                  id="enableReminder" 
                  checked={settings.enableReminder}
                  onCheckedChange={(checked) => handleChange("enableReminder", checked)}
                />
              </div>
              
              {settings.enableReminder && (
                <div className="ml-6 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="reminderDays">提醒天数</Label>
                    <Input 
                      id="reminderDays" 
                      type="number" 
                      value={settings.reminderDays}
                      onChange={(e) => handleChange("reminderDays", e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      合同发起后多少天未签署开始提醒
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="reminderInterval">提醒间隔（小时）</Label>
                    <Input 
                      id="reminderInterval" 
                      type="number" 
                      value={settings.reminderInterval}
                      onChange={(e) => handleChange("reminderInterval", e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground">
                      两次提醒之间的间隔时间
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            重置
          </Button>
          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            保存设置
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
