import { NextRequest, NextResponse } from 'next/server';
import { courseTypeService } from '@/services/course-type-service';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id, 10);
    const courseType = courseTypeService.getById(id);
    
    if (!courseType) {
      return NextResponse.json(
        { code: 404, msg: '课程类型不存在' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: courseType
    });
  } catch (error) {
    console.error('获取课程类型详情错误:', error);
    return NextResponse.json(
      { code: 500, msg: '获取数据失败', error: String(error) },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id, 10);
    const data = await request.json();
    
    // 检查课程类型是否存在
    const existingType = courseTypeService.getById(id);
    if (!existingType) {
      return NextResponse.json(
        { code: 404, msg: '课程类型不存在' },
        { status: 404 }
      );
    }
    
    // 更新课程类型
    const updatedType = courseTypeService.update(id, data);
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: updatedType
    });
  } catch (error) {
    console.error('更新课程类型错误:', error);
    return NextResponse.json(
      { code: 500, msg: '更新失败', error: String(error) },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id, 10);
    
    // 检查课程类型是否存在
    const existingType = courseTypeService.getById(id);
    if (!existingType) {
      return NextResponse.json(
        { code: 404, msg: '课程类型不存在' },
        { status: 404 }
      );
    }
    
    // 检查是否有关联课程
    if (existingType.courses > 0) {
      return NextResponse.json(
        { code: 400, msg: '该课程类型下有关联课程，无法删除' },
        { status: 400 }
      );
    }
    
    // 删除课程类型
    const result = courseTypeService.delete(id);
    
    return NextResponse.json({
      code: 200,
      msg: result ? 'Success' : '删除失败',
      data: { success: result }
    });
  } catch (error) {
    console.error('删除课程类型错误:', error);
    return NextResponse.json(
      { code: 500, msg: '删除失败', error: String(error) },
      { status: 500 }
    );
  }
} 