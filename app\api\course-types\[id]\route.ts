import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// 获取路径参数
interface Params {
  params: {
    id: string;
  };
}

// GET /api/course-types/[id]
export async function GET(request: NextRequest, { params }: Params) {
  try {
    const { id: paramId } = await params;
    const id = parseInt(paramId);
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的课程类型ID',
        data: null
      }, { status: 400 });
    }
    
    // 查询课程类型，包含课程数量统计
    const courseType = await prisma.courseType.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      }
    });

    // 检查是否存在
    if (!courseType) {
      return NextResponse.json({
        code: 404,
        msg: '未找到课程类型',
        data: null
      }, { status: 404 });
    }

    // 更新课程数量统计
    const actualCourseCount = courseType._count.courses;
    if (courseType.course_count !== actualCourseCount) {
      await prisma.courseType.update({
        where: { id },
        data: { course_count: actualCourseCount }
      });
    }

    // 格式化返回数据
    const formattedType = {
      id: courseType.id.toString(),
      name: courseType.name,
      description: courseType.description || '',
      color: courseType.color || '#3b82f6',
      status: courseType.status === 1 ? 'active' : 'inactive',
      displayOrder: courseType.display_order || 0,
      courseCount: actualCourseCount,
      courses: actualCourseCount, // 兼容前端
      createdAt: courseType.created_at ? courseType.created_at.toISOString().split('T')[0] : '',
      updatedAt: courseType.updated_at ? courseType.updated_at.toISOString().split('T')[0] : ''
    };
    
    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '获取课程类型成功',
      data: formattedType
    });
  } catch (error: any) {
    console.error('获取课程类型详情失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `获取课程类型详情失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}

// PUT /api/course-types/[id]
export async function PUT(request: NextRequest, { params }: Params) {
  try {
    const { id: paramId } = await params;
    const id = parseInt(paramId);
    const body = await request.json();
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的课程类型ID',
        data: null
      }, { status: 400 });
    }
    
    // 检查课程类型是否存在
    const existingType = await prisma.courseType.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      }
    });

    if (!existingType) {
      return NextResponse.json({
        code: 404,
        msg: '未找到课程类型',
        data: null
      }, { status: 404 });
    }

    console.log('更新课程类型API接收数据:', { id, ...body });

    // 如果更新了名称，检查是否重复
    if (body.name && body.name !== existingType.name) {
      const duplicateType = await prisma.courseType.findFirst({
        where: {
          tenant_id: existingType.tenant_id,
          name: body.name,
          id: { not: id }
        }
      });

      if (duplicateType) {
        return NextResponse.json({
          code: 400,
          msg: '课程类型名称已存在',
          data: null
        }, { status: 400 });
      }
    }

    // 准备更新数据
    const updateData: any = {};

    // 只更新提供的字段
    if (body.name !== undefined) updateData.name = body.name;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.color !== undefined) updateData.color = body.color;
    if (body.displayOrder !== undefined) updateData.display_order = body.displayOrder;
    if (body.status !== undefined) updateData.status = body.status === 'active' ? 1 : 0;

    // 更新课程类型
    const updatedType = await prisma.courseType.update({
      where: { id },
      data: updateData,
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      }
    });

    // 更新课程数量统计
    const actualCourseCount = updatedType._count.courses;
    if (updatedType.course_count !== actualCourseCount) {
      await prisma.courseType.update({
        where: { id },
        data: { course_count: actualCourseCount }
      });
    }

    console.log('课程类型更新成功:', updatedType);

    // 格式化返回数据
    const formattedType = {
      id: updatedType.id.toString(),
      name: updatedType.name,
      description: updatedType.description || '',
      color: updatedType.color || '#3b82f6',
      status: updatedType.status === 1 ? 'active' : 'inactive',
      displayOrder: updatedType.display_order || 0,
      courseCount: actualCourseCount,
      courses: actualCourseCount, // 兼容前端
      createdAt: updatedType.created_at ? updatedType.created_at.toISOString().split('T')[0] : '',
      updatedAt: updatedType.updated_at ? updatedType.updated_at.toISOString().split('T')[0] : ''
    };
    
    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '更新课程类型成功',
      data: formattedType
    });
  } catch (error: any) {
    console.error('更新课程类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `更新课程类型失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}

// DELETE /api/course-types/[id]
export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    const { id: paramId } = await params;
    const id = parseInt(paramId);
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的课程类型ID',
        data: null
      }, { status: 400 });
    }
    
    // 检查课程类型是否存在，包含课程数量统计
    const existingType = await prisma.courseType.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      }
    });

    if (!existingType) {
      return NextResponse.json({
        code: 404,
        msg: '未找到课程类型',
        data: null
      }, { status: 404 });
    }

    console.log('删除课程类型API:', id, '关联课程数量:', existingType._count.courses);

    // 检查是否有关联课程
    if (existingType._count.courses > 0) {
      return NextResponse.json({
        code: 400,
        msg: `该课程类型下有 ${existingType._count.courses} 个关联课程，无法删除`,
        data: null
      }, { status: 400 });
    }
    
    // 删除课程类型
    await prisma.courseType.delete({
      where: { id }
    });

    console.log('课程类型删除成功:', id);

    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '删除课程类型成功',
      data: null
    });
  } catch (error: any) {
    console.error('删除课程类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `删除课程类型失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}