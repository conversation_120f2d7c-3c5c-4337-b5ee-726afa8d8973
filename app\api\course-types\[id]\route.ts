import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// 获取路径参数
interface Params {
  params: {
    id: string;
  };
}

// GET /api/course-types/[id]
export async function GET(request: NextRequest, { params }: Params) {
  try {
    const id = parseInt(params.id);
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的课程类型ID',
        data: null
      }, { status: 400 });
    }
    
    // 查询课程类型
    const courseType = await prisma.courseType.findUnique({
      where: { id }
    });
    
    // 检查是否存在
    if (!courseType) {
      return NextResponse.json({
        code: 404,
        msg: '未找到课程类型',
        data: null
      }, { status: 404 });
    }
    
    // 格式化返回数据
    const formattedType = {
      id: courseType.id,
      name: courseType.name,
      description: courseType.description || '',
      color: courseType.color || '#3b82f6',
      status: courseType.status === 1 ? 'active' : 'inactive',
      displayOrder: courseType.display_order || 0,
      courseCount: courseType.course_count || 0,
      createdAt: courseType.created_at ? courseType.created_at.toISOString() : '',
      updatedAt: courseType.updated_at ? courseType.updated_at.toISOString() : ''
    };
    
    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '获取课程类型成功',
      data: formattedType
    });
  } catch (error: any) {
    console.error('获取课程类型详情失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `获取课程类型详情失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}

// PUT /api/course-types/[id]
export async function PUT(request: NextRequest, { params }: Params) {
  try {
    const id = parseInt(params.id);
    const body = await request.json();
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的课程类型ID',
        data: null
      }, { status: 400 });
    }
    
    // 检查课程类型是否存在
    const existingType = await prisma.courseType.findUnique({
      where: { id }
    });
    
    if (!existingType) {
      return NextResponse.json({
        code: 404,
        msg: '未找到课程类型',
        data: null
      }, { status: 404 });
    }
    
    // 准备更新数据
    const updateData: any = {};
    
    // 只更新提供的字段
    if (body.name !== undefined) updateData.name = body.name;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.color !== undefined) updateData.color = body.color;
    if (body.displayOrder !== undefined) updateData.display_order = body.displayOrder;
    if (body.status !== undefined) updateData.status = body.status === 'active' ? 1 : 0;
    
    // 更新课程类型
    const updatedType = await prisma.courseType.update({
      where: { id },
      data: updateData
    });
    
    // 格式化返回数据
    const formattedType = {
      id: updatedType.id,
      name: updatedType.name,
      description: updatedType.description || '',
      color: updatedType.color || '#3b82f6',
      status: updatedType.status === 1 ? 'active' : 'inactive',
      displayOrder: updatedType.display_order || 0,
      courseCount: updatedType.course_count || 0,
      createdAt: updatedType.created_at ? updatedType.created_at.toISOString() : '',
      updatedAt: updatedType.updated_at ? updatedType.updated_at.toISOString() : ''
    };
    
    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '更新课程类型成功',
      data: formattedType
    });
  } catch (error: any) {
    console.error('更新课程类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `更新课程类型失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}

// DELETE /api/course-types/[id]
export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    const id = parseInt(params.id);
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的课程类型ID',
        data: null
      }, { status: 400 });
    }
    
    // 检查课程类型是否存在
    const existingType = await prisma.courseType.findUnique({
      where: { id }
    });
    
    if (!existingType) {
      return NextResponse.json({
        code: 404,
        msg: '未找到课程类型',
        data: null
      }, { status: 404 });
    }
    
    // 检查是否有关联课程
    if (existingType.course_count && existingType.course_count > 0) {
      return NextResponse.json({
        code: 400,
        msg: '该课程类型下有关联课程，无法删除',
        data: null
      }, { status: 400 });
    }
    
    // 删除课程类型
    await prisma.courseType.delete({
      where: { id }
    });
    
    // 返回成功响应
    return NextResponse.json({
      code: 200,
      msg: '删除课程类型成功',
      data: null
    });
  } catch (error: any) {
    console.error('删除课程类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: `删除课程类型失败: ${error.message || '服务器错误'}`,
      data: null
    }, { status: 500 });
  }
}