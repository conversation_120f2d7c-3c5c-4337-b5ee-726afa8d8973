"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  ArrowLeftRight, 
  Plus, 
  Calendar, 
  UserCircle, 
  Box, 
  BookOpen, 
  TrendingUp, 
  PercentCircle,
  CircleDollarSign, 
  ArrowDownUp 
} from "lucide-react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "@/components/ui/use-toast"
import { Progress } from "@/components/ui/progress"

// 资源类型
type ResourceType = 'coach' | 'equipment' | 'course' | 'material';

// 资源调配记录接口
interface ResourceAllocation {
  id: string | number;
  resourceName: string;
  resourceType: ResourceType;
  fromStore: string;
  toStore: string;
  quantity: number;
  allocatedAt: string;
  returnDate?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  reason: string;
}

// 资源共享率接口
interface ResourceSharing {
  storeId: number;
  storeName: string;
  coachSharing: number;
  equipmentSharing: number;
  courseSharing: number;
  overallSharing: number;
}

// 表单验证模式
const formSchema = z.object({
  resourceName: z.string().min(2, { message: "资源名称至少需要2个字符" }).max(50, { message: "资源名称不能超过50个字符" }),
  resourceType: z.enum(['coach', 'equipment', 'course', 'material'], { message: "请选择资源类型" }),
  fromStore: z.string().min(2, { message: "请选择源门店" }).max(50),
  toStore: z.string().min(2, { message: "请选择目标门店" }).max(50),
  quantity: z.number().min(1, { message: "数量必须大于0" }),
  returnDate: z.string().optional(),
  reason: z.string().min(5, { message: "请简要说明调配原因" }).max(200, { message: "描述不能超过200个字符" }),
});

type FormValues = z.infer<typeof formSchema>;

export default function ResourceAllocationPage() {
  const [allocations, setAllocations] = useState<ResourceAllocation[]>([
    {
      id: 1,
      resourceName: "高级瑜伽垫",
      resourceType: 'equipment',
      fromStore: "北京朝阳门店",
      toStore: "北京海淀门店",
      quantity: 20,
      allocatedAt: "2023-06-15",
      returnDate: "2023-09-15",
      status: 'in_progress',
      reason: "海淀门店开展高端课程需要"
    },
    {
      id: 2,
      resourceName: "王教练",
      resourceType: 'coach',
      fromStore: "上海静安门店",
      toStore: "上海浦东门店",
      quantity: 1,
      allocatedAt: "2023-07-01",
      returnDate: "2023-08-01",
      status: 'completed',
      reason: "浦东门店高级课程短期教学支持"
    },
    {
      id: 3,
      resourceName: "瑜伽理疗课程",
      resourceType: 'course',
      fromStore: "广州天河门店",
      toStore: "深圳南山门店",
      quantity: 1,
      allocatedAt: "2023-07-10",
      status: 'pending',
      reason: "课程共享测试"
    },
    {
      id: 4,
      resourceName: "瑜伽体位教学手册",
      resourceType: 'material',
      fromStore: "成都锦江门店",
      toStore: "成都武侯门店",
      quantity: 15,
      allocatedAt: "2023-06-20",
      status: 'completed',
      reason: "教学资料共享"
    },
    {
      id: 5,
      resourceName: "张教练",
      resourceType: 'coach',
      fromStore: "杭州西湖门店",
      toStore: "杭州滨江门店",
      quantity: 1,
      allocatedAt: "2023-08-01",
      returnDate: "2023-08-31",
      status: 'in_progress',
      reason: "滨江门店教练短缺临时支援"
    }
  ]);
  
  const [sharingStats, setSharingStats] = useState<ResourceSharing[]>([
    {
      storeId: 1,
      storeName: "北京朝阳门店",
      coachSharing: 75,
      equipmentSharing: 60,
      courseSharing: 90,
      overallSharing: 75
    },
    {
      storeId: 2,
      storeName: "上海静安门店",
      coachSharing: 80,
      equipmentSharing: 70,
      courseSharing: 95,
      overallSharing: 82
    },
    {
      storeId: 3,
      storeName: "广州天河门店",
      coachSharing: 65,
      equipmentSharing: 55,
      courseSharing: 85,
      overallSharing: 68
    },
    {
      storeId: 4,
      storeName: "深圳南山门店",
      coachSharing: 70,
      equipmentSharing: 60,
      courseSharing: 80,
      overallSharing: 70
    },
    {
      storeId: 5,
      storeName: "杭州西湖门店",
      coachSharing: 85,
      equipmentSharing: 75,
      courseSharing: 90,
      overallSharing: 83
    }
  ]);
  
  const [stores] = useState([
    { id: 1, name: "北京朝阳门店" },
    { id: 2, name: "北京海淀门店" },
    { id: 3, name: "上海静安门店" },
    { id: 4, name: "上海浦东门店" },
    { id: 5, name: "广州天河门店" },
    { id: 6, name: "深圳南山门店" },
    { id: 7, name: "成都锦江门店" },
    { id: 8, name: "成都武侯门店" },
    { id: 9, name: "杭州西湖门店" },
    { id: 10, name: "杭州滨江门店" }
  ]);
  
  const [activeTab, setActiveTab] = useState<string>("allocations");
  const [openAddDialog, setOpenAddDialog] = useState(false);

  // 创建表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      resourceName: "",
      resourceType: 'equipment',
      fromStore: "",
      toStore: "",
      quantity: 1,
      returnDate: "",
      reason: ""
    }
  });

  // 处理添加资源调配
  const handleAddAllocation = (values: FormValues) => {
    try {
      // 创建新资源调配对象
      const newAllocation: ResourceAllocation = {
        id: Date.now(),
        resourceName: values.resourceName,
        resourceType: values.resourceType,
        fromStore: values.fromStore,
        toStore: values.toStore,
        quantity: values.quantity,
        allocatedAt: new Date().toISOString().split('T')[0],
        returnDate: values.returnDate,
        status: 'pending',
        reason: values.reason
      };

      // 更新资源调配列表
      const updatedAllocations = [...allocations, newAllocation];
      setAllocations(updatedAllocations);
      
      toast({
        title: "资源调配申请已提交",
        description: `${values.resourceName} 的调配请求已创建`,
      });

      // 关闭对话框并重置表单
      setOpenAddDialog(false);
      form.reset();
    } catch (error) {
      console.error('添加资源调配失败:', error);
      toast({
        title: "添加资源调配失败",
        description: "请稍后再试",
        variant: "destructive"
      });
    }
  };

  // 获取资源类型显示名称
  const getResourceTypeName = (type: ResourceType) => {
    switch(type) {
      case 'coach': return '教练';
      case 'equipment': return '设备';
      case 'course': return '课程';
      case 'material': return '教材';
      default: return '其他';
    }
  };

  // 获取资源类型图标
  const getResourceTypeIcon = (type: ResourceType) => {
    switch(type) {
      case 'coach': return <UserCircle className="h-4 w-4" />;
      case 'equipment': return <Box className="h-4 w-4" />;
      case 'course': return <Calendar className="h-4 w-4" />;
      case 'material': return <BookOpen className="h-4 w-4" />;
      default: return null;
    }
  };

  return (
    <div className="container max-w-6xl py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">资源调配</h1>
        <Button onClick={() => setOpenAddDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          新增调配
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">进行中的调配</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allocations.filter(a => a.status === 'in_progress').length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">待审批调配</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allocations.filter(a => a.status === 'pending').length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">资源共享率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(sharingStats.reduce((acc, stat) => acc + stat.overallSharing, 0) / sharingStats.length)}%
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">总调配次数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allocations.length}</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="allocations">调配记录</TabsTrigger>
          <TabsTrigger value="sharing">资源共享率</TabsTrigger>
        </TabsList>
        
        <TabsContent value="allocations">
          <Card>
            <CardHeader>
              <CardTitle>资源调配记录</CardTitle>
              <CardDescription>查看各门店间资源调配情况</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>资源名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>源门店</TableHead>
                    <TableHead>目标门店</TableHead>
                    <TableHead>数量</TableHead>
                    <TableHead>调配日期</TableHead>
                    <TableHead>归还日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {allocations.map((allocation) => (
                    <TableRow key={allocation.id}>
                      <TableCell className="font-medium">{allocation.resourceName}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          {getResourceTypeIcon(allocation.resourceType)}
                          <span>{getResourceTypeName(allocation.resourceType)}</span>
                        </div>
                      </TableCell>
                      <TableCell>{allocation.fromStore}</TableCell>
                      <TableCell>{allocation.toStore}</TableCell>
                      <TableCell>{allocation.quantity}</TableCell>
                      <TableCell>{allocation.allocatedAt}</TableCell>
                      <TableCell>{allocation.returnDate || '无需归还'}</TableCell>
                      <TableCell>
                        <Badge variant={
                          allocation.status === 'completed' ? "default" : 
                          allocation.status === 'in_progress' ? "secondary" :
                          allocation.status === 'pending' ? "outline" : "destructive"
                        }>
                          {allocation.status === 'completed' ? '已完成' : 
                           allocation.status === 'in_progress' ? '进行中' :
                           allocation.status === 'pending' ? '待审批' : '已取消'}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">详情</Button>
                        {allocation.status === 'pending' && (
                          <Button variant="ghost" size="sm">审批</Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="sharing">
          <Card>
            <CardHeader>
              <CardTitle>门店资源共享情况</CardTitle>
              <CardDescription>各门店资源共享率分析</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>门店名称</TableHead>
                    <TableHead>教练共享率</TableHead>
                    <TableHead>设备共享率</TableHead>
                    <TableHead>课程共享率</TableHead>
                    <TableHead>总体共享率</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sharingStats.map((stat) => (
                    <TableRow key={stat.storeId}>
                      <TableCell className="font-medium">{stat.storeName}</TableCell>
                      <TableCell>
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">{stat.coachSharing}%</span>
                          </div>
                          <Progress value={stat.coachSharing} className="h-2" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">{stat.equipmentSharing}%</span>
                          </div>
                          <Progress value={stat.equipmentSharing} className="h-2" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">{stat.courseSharing}%</span>
                          </div>
                          <Progress value={stat.courseSharing} className="h-2" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{stat.overallSharing}%</span>
                          </div>
                          <Progress value={stat.overallSharing} className="h-2" />
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">详情</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 添加资源调配对话框 */}
      <Dialog open={openAddDialog} onOpenChange={setOpenAddDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>创建资源调配</DialogTitle>
            <DialogDescription>
              在连锁门店之间调配资源，提高资源利用率。
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddAllocation)} className="space-y-4">
              <FormField
                control={form.control}
                name="resourceName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>资源名称</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：高级瑜伽垫" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="resourceType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>资源类型</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择资源类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="coach">教练</SelectItem>
                        <SelectItem value="equipment">设备</SelectItem>
                        <SelectItem value="course">课程</SelectItem>
                        <SelectItem value="material">教材</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="fromStore"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>源门店</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择源门店" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {stores.map(store => (
                            <SelectItem key={store.id} value={store.name}>{store.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="toStore"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>目标门店</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择目标门店" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {stores.map(store => (
                            <SelectItem key={store.id} value={store.name}>{store.name}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="quantity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>数量</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          min="1" 
                          {...field} 
                          onChange={e => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="returnDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>归还日期 (选填)</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormDescription>
                        如无需归还可留空
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>调配原因</FormLabel>
                    <FormControl>
                      <Textarea placeholder="请简要说明调配原因" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setOpenAddDialog(false)}>取消</Button>
                <Button type="submit">提交申请</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
} 