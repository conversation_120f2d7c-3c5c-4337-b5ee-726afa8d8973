"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  CreditCard,
  CheckCircle,
  Plus,
  Trash2,
  Edit,
  MoveUp,
  MoveDown,
  Tag,
  Percent,
  Gift,
  Calendar,
  Clock
} from "lucide-react"

interface MembershipBenefitsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  membershipId: string;
  onSave: (data: any) => void;
}

interface Benefit {
  id: string;
  title: string;
  description: string;
  type: "access" | "discount" | "gift" | "priority" | "other";
  value?: string;
  order: number;
}

export function MembershipBenefitsDialog({ 
  open, 
  onOpenChange, 
  membershipId, 
  onSave 
}: MembershipBenefitsDialogProps) {
  const [loading, setLoading] = useState(false)
  const [benefits, setBenefits] = useState<Benefit[]>([])
  const { toast } = useToast()
  
  // 编辑状态
  const [editingBenefit, setEditingBenefit] = useState<Benefit | null>(null)
  const [showBenefitDialog, setShowBenefitDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteBenefitId, setDeleteBenefitId] = useState("")
  
  // 表单数据
  const [benefitForm, setBenefitForm] = useState({
    title: "",
    description: "",
    type: "access" as "access" | "discount" | "gift" | "priority" | "other",
    value: ""
  })
  
  // 会员卡基本信息
  const [membershipInfo, setMembershipInfo] = useState({
    name: "",
    price: "",
    validityPeriod: "",
    autoActivate: true
  })

  // 加载会员卡权益数据
  useEffect(() => {
    if (open && membershipId) {
      loadMembershipBenefits()
    }
  }, [open, membershipId])

  // 加载会员卡权益
  const loadMembershipBenefits = () => {
    setLoading(true)
    
    // 模拟API请求
    setTimeout(() => {
      // 模拟数据
      const mockMembershipInfo = {
        name: "高级会员卡 - 年卡",
        price: "3688.00",
        validityPeriod: "365",
        autoActivate: true
      }
      
      const mockBenefits: Benefit[] = [
        {
          id: "b1",
          title: "不限次数上课",
          description: "可以不限次数参加所有常规课程",
          type: "access",
          order: 1
        },
        {
          id: "b2",
          title: "专属更衣柜",
          description: "独享专属储物柜，无需每次携带装备",
          type: "gift",
          order: 2
        },
        {
          id: "b3",
          title: "免费停车",
          description: "馆内停车场免费停车",
          type: "gift",
          order: 3
        },
        {
          id: "b4",
          title: "优先预约",
          description: "可提前3天预约热门课程",
          type: "priority",
          value: "3天",
          order: 4
        },
        {
          id: "b5",
          title: "会员专属活动",
          description: "可参加每月会员专属活动",
          type: "access",
          order: 5
        }
      ]
      
      setMembershipInfo(mockMembershipInfo)
      setBenefits(mockBenefits)
      setLoading(false)
    }, 500)
  }

  // 处理添加权益
  const handleAddBenefit = () => {
    setEditingBenefit(null)
    setBenefitForm({
      title: "",
      description: "",
      type: "access",
      value: ""
    })
    setShowBenefitDialog(true)
  }

  // 处理编辑权益
  const handleEditBenefit = (benefit: Benefit) => {
    setEditingBenefit(benefit)
    setBenefitForm({
      title: benefit.title,
      description: benefit.description,
      type: benefit.type,
      value: benefit.value || ""
    })
    setShowBenefitDialog(true)
  }

  // 处理删除权益
  const handleDeleteBenefit = (benefitId: string) => {
    setDeleteBenefitId(benefitId)
    setShowDeleteDialog(true)
  }

  // 确认删除
  const confirmDelete = () => {
    // 删除权益
    setBenefits(prev => prev.filter(b => b.id !== deleteBenefitId))
    toast({
      title: "删除成功",
      description: "权益已成功删除",
    })
    setShowDeleteDialog(false)
  }

  // 保存权益
  const saveBenefit = () => {
    if (!benefitForm.title) {
      toast({
        title: "验证失败",
        description: "请填写权益标题",
        variant: "destructive"
      })
      return
    }
    
    if (editingBenefit) {
      // 更新权益
      setBenefits(prev => prev.map(b => {
        if (b.id === editingBenefit.id) {
          return {
            ...b,
            title: benefitForm.title,
            description: benefitForm.description,
            type: benefitForm.type,
            value: benefitForm.value
          }
        }
        return b
      }))
    } else {
      // 添加新权益
      const newBenefit: Benefit = {
        id: `b${Date.now()}`,
        title: benefitForm.title,
        description: benefitForm.description,
        type: benefitForm.type,
        value: benefitForm.value,
        order: benefits.length + 1
      }
      setBenefits(prev => [...prev, newBenefit])
    }
    
    setShowBenefitDialog(false)
    toast({
      title: editingBenefit ? "更新成功" : "添加成功",
      description: editingBenefit ? "权益已更新" : "新权益已添加",
    })
  }

  // 移动权益顺序
  const moveBenefit = (id: string, direction: "up" | "down") => {
    const index = benefits.findIndex(b => b.id === id)
    if (index === -1) return
    
    if (direction === "up" && index > 0) {
      const newBenefits = [...benefits]
      const temp = newBenefits[index]
      newBenefits[index] = newBenefits[index - 1]
      newBenefits[index - 1] = temp
      
      // 更新顺序
      newBenefits.forEach((b, i) => {
        b.order = i + 1
      })
      
      setBenefits(newBenefits)
    } else if (direction === "down" && index < benefits.length - 1) {
      const newBenefits = [...benefits]
      const temp = newBenefits[index]
      newBenefits[index] = newBenefits[index + 1]
      newBenefits[index + 1] = temp
      
      // 更新顺序
      newBenefits.forEach((b, i) => {
        b.order = i + 1
      })
      
      setBenefits(newBenefits)
    }
  }

  // 获取权益类型图标
  const getBenefitTypeIcon = (type: string) => {
    switch (type) {
      case "access":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "discount":
        return <Percent className="h-4 w-4 text-blue-500" />
      case "gift":
        return <Gift className="h-4 w-4 text-purple-500" />
      case "priority":
        return <Calendar className="h-4 w-4 text-orange-500" />
      default:
        return <Tag className="h-4 w-4 text-gray-500" />
    }
  }

  // 获取权益类型名称
  const getBenefitTypeName = (type: string) => {
    switch (type) {
      case "access":
        return "使用权限"
      case "discount":
        return "折扣优惠"
      case "gift":
        return "赠送礼品"
      case "priority":
        return "优先权益"
      default:
        return "其他权益"
    }
  }

  // 保存所有更改
  const handleSaveAll = () => {
    // 这里应该调用API保存所有权益
    onSave(benefits)
    toast({
      title: "保存成功",
      description: "会员卡权益已成功保存",
    })
    onOpenChange(false)
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>会员卡权益管理</DialogTitle>
            <DialogDescription>
              管理会员卡包含的权益和优惠
            </DialogDescription>
          </DialogHeader>

          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
            </div>
          ) : (
            <div className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center">
                    <CreditCard className="h-4 w-4 mr-2 text-blue-500" />
                    会员卡信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">会员卡名称</div>
                    <div className="font-medium">{membershipInfo.name}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">价格</div>
                    <div className="font-medium">¥{membershipInfo.price}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">有效期</div>
                    <div className="font-medium">
                      {membershipInfo.validityPeriod === "forever" ? "永久有效" : `${membershipInfo.validityPeriod}天`}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">激活方式</div>
                    <div className="font-medium">
                      {membershipInfo.autoActivate ? "自动激活" : "手动激活"}
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <div className="flex justify-between items-center">
                <Button onClick={handleAddBenefit}>
                  <Plus className="mr-2 h-4 w-4" />
                  添加权益
                </Button>
                
                <div className="text-sm text-muted-foreground">
                  共 {benefits.length} 个权益
                </div>
              </div>
              
              {benefits.length === 0 ? (
                <div className="text-center py-12 border rounded-md">
                  <Gift className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">暂无权益</h3>
                  <p className="text-muted-foreground mb-4">
                    点击"添加权益"按钮开始创建会员卡权益
                  </p>
                  <Button onClick={handleAddBenefit}>
                    <Plus className="mr-2 h-4 w-4" />
                    添加权益
                  </Button>
                </div>
              ) : (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">权益列表</CardTitle>
                    <CardDescription>
                      会员卡包含的所有权益，可拖动调整顺序
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {benefits.map((benefit, index) => (
                        <div key={benefit.id} className="flex items-center justify-between border-b pb-3">
                          <div className="flex items-center">
                            <div className="flex flex-col items-center mr-3">
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-6 w-6" 
                                disabled={index === 0}
                                onClick={() => moveBenefit(benefit.id, "up")}
                              >
                                <MoveUp className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-6 w-6" 
                                disabled={index === benefits.length - 1}
                                onClick={() => moveBenefit(benefit.id, "down")}
                              >
                                <MoveDown className="h-4 w-4" />
                              </Button>
                            </div>
                            <div>
                              <div className="font-medium flex items-center">
                                {getBenefitTypeIcon(benefit.type)}
                                <span className="ml-1">{benefit.title}</span>
                                {benefit.value && (
                                  <span className="ml-2 text-xs bg-muted px-2 py-0.5 rounded-full">
                                    {benefit.value}
                                  </span>
                                )}
                              </div>
                              {benefit.description && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  {benefit.description}
                                </div>
                              )}
                              <div className="text-xs text-muted-foreground mt-1 flex items-center">
                                <Tag className="h-3 w-3 mr-1" />
                                {getBenefitTypeName(benefit.type)}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="ghost" size="icon" onClick={() => handleEditBenefit(benefit)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" className="text-red-500" onClick={() => handleDeleteBenefit(benefit.id)}>
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleSaveAll}>
              保存所有更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 权益编辑对话框 */}
      <Dialog open={showBenefitDialog} onOpenChange={setShowBenefitDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{editingBenefit ? "编辑权益" : "添加权益"}</DialogTitle>
            <DialogDescription>
              {editingBenefit ? "修改权益信息" : "创建新的会员卡权益"}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="benefitTitle">权益标题 <span className="text-red-500">*</span></Label>
              <Input
                id="benefitTitle"
                placeholder="例如：不限次数上课"
                value={benefitForm.title}
                onChange={(e) => setBenefitForm(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="benefitDescription">权益描述 (可选)</Label>
              <Textarea
                id="benefitDescription"
                placeholder="详细描述此权益"
                value={benefitForm.description}
                onChange={(e) => setBenefitForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="benefitType">权益类型</Label>
              <select
                id="benefitType"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={benefitForm.type}
                onChange={(e) => setBenefitForm(prev => ({ ...prev, type: e.target.value as any }))}
              >
                <option value="access">使用权限</option>
                <option value="discount">折扣优惠</option>
                <option value="gift">赠送礼品</option>
                <option value="priority">优先权益</option>
                <option value="other">其他权益</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="benefitValue">权益值 (可选)</Label>
              <Input
                id="benefitValue"
                placeholder={
                  benefitForm.type === "discount" ? "例如：8折" : 
                  benefitForm.type === "priority" ? "例如：3天" : 
                  "例如：每月1次"
                }
                value={benefitForm.value}
                onChange={(e) => setBenefitForm(prev => ({ ...prev, value: e.target.value }))}
              />
              <p className="text-xs text-muted-foreground">
                {benefitForm.type === "discount" ? "折扣比例或金额" : 
                 benefitForm.type === "priority" ? "优先时间或次数" : 
                 benefitForm.type === "gift" ? "赠品数量或规格" :
                 "权益的具体数值，如次数、时间等"}
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBenefitDialog(false)}>
              取消
            </Button>
            <Button onClick={saveBenefit}>
              {editingBenefit ? "更新权益" : "添加权益"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这个权益吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
