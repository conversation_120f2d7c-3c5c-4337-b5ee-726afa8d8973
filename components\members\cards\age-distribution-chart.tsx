"use client"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface AgeDistributionChartProps {
  cardId: number | string
  className?: string
}

export function AgeDistributionChart({ cardId, className }: AgeDistributionChartProps) {
  // 模拟年龄分布数据
  const ageData = [
    { range: "18-24岁", count: 15, color: "#4F46E5" },
    { range: "25-34岁", count: 42, color: "#06B6D4" },
    { range: "35-44岁", count: 28, color: "#10B981" },
    { range: "45-54岁", count: 10, color: "#F59E0B" },
    { range: "55岁以上", count: 5, color: "#EF4444" },
  ]
  
  // 计算总数
  const total = ageData.reduce((sum, item) => sum + item.count, 0)
  
  // 获取最大值，用于计算柱状图高度
  const maxCount = Math.max(...ageData.map(item => item.count))
  
  // 渲染柱状图
  const renderBarChart = () => {
    return (
      <div className="flex h-[180px] items-end justify-between gap-1 px-2 pt-6">
        {ageData.map((item, index) => {
          const height = (item.count / maxCount) * 150
          return (
            <div key={index} className="flex flex-col items-center">
              <div 
                className="w-10 rounded-t-sm transition-all duration-300 hover:opacity-80"
                style={{ height: `${height}px`, backgroundColor: item.color }}
              >
                <div className="flex h-full items-center justify-center text-xs font-medium text-white">
                  {item.count}%
                </div>
              </div>
              <div className="mt-2 text-center">
                <div className="text-xs">{item.range}</div>
                <div className="text-xs text-muted-foreground">{Math.round(item.count * total / 100)} 人</div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }
  
  // 渲染年龄分布统计
  const renderAgeStats = () => {
    // 计算平均年龄（假设使用每个范围的中间值）
    const ageMidpoints = [21, 29.5, 39.5, 49.5, 60]
    const weightedSum = ageData.reduce((sum, item, index) => 
      sum + (item.count * ageMidpoints[index]), 0)
    const averageAge = Math.round(weightedSum / 100)
    
    // 找出最多的年龄段
    const mostCommonAgeGroup = ageData.reduce((prev, current) => 
      (prev.count > current.count) ? prev : current)
    
    return (
      <div className="mt-4 flex justify-center gap-8">
        <div className="text-center">
          <div className="text-xs text-muted-foreground">平均年龄</div>
          <div className="text-lg font-medium">{averageAge}岁</div>
        </div>
        <div className="text-center">
          <div className="text-xs text-muted-foreground">主要年龄段</div>
          <div className="text-lg font-medium">{mostCommonAgeGroup.range}</div>
        </div>
      </div>
    )
  }
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-6">
        <h3 className="mb-4 text-center text-sm font-medium">会员年龄分布</h3>
        {renderBarChart()}
        {renderAgeStats()}
      </CardContent>
    </Card>
  )
}
