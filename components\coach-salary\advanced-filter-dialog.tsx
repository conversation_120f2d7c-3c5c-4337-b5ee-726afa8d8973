"use client"

import { useState } from "react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Filter, X } from "lucide-react"

// 模拟数据 - 教练列表
const mockCoaches = [
  { id: "all", name: "所有教练" },
  { id: "C001", name: "张教练" },
  { id: "C002", name: "李教练" },
  { id: "C003", name: "王教练" },
  { id: "C004", name: "赵教练" },
]

interface AdvancedFilterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AdvancedFilterDialog({ open, onOpenChange }: AdvancedFilterDialogProps) {
  const [selectedCoach, setSelectedCoach] = useState<string>("all")
  const [salaryType, setSalaryType] = useState<string>("all")
  const [minSalary, setMinSalary] = useState<string>("")
  const [maxSalary, setMaxSalary] = useState<string>("")
  const [status, setStatus] = useState<string[]>(["pending", "approved", "paid", "rejected"])
  const [startMonth, setStartMonth] = useState<string>("")
  const [endMonth, setEndMonth] = useState<string>("")

  // 处理状态选择
  const handleStatusChange = (value: string, checked: boolean) => {
    if (checked) {
      setStatus([...status, value])
    } else {
      setStatus(status.filter(s => s !== value))
    }
  }

  // 重置筛选条件
  const resetFilters = () => {
    setSelectedCoach("all")
    setSalaryType("all")
    setMinSalary("")
    setMaxSalary("")
    setStatus(["pending", "approved", "paid", "rejected"])
    setStartMonth("")
    setEndMonth("")
  }

  // 应用筛选条件
  const applyFilters = () => {
    // 构建筛选条件
    const filters = {
      coachId: selectedCoach !== "all" ? selectedCoach : undefined,
      salaryType: salaryType !== "all" ? salaryType : undefined,
      minSalary: minSalary ? parseFloat(minSalary) : undefined,
      maxSalary: maxSalary ? parseFloat(maxSalary) : undefined,
      status: status.length < 4 ? status : undefined,
      startMonth,
      endMonth,
    }
    
    // 这里应该调用API应用筛选条件
    console.log("应用筛选条件:", filters)
    
    // 关闭对话框
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
          <DialogDescription>
            设置筛选条件以查找特定的薪资记录
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="coach" className="text-right">
              教练
            </Label>
            <Select
              value={selectedCoach}
              onValueChange={setSelectedCoach}
            >
              <SelectTrigger id="coach" className="col-span-3">
                <SelectValue placeholder="请选择教练" />
              </SelectTrigger>
              <SelectContent>
                {mockCoaches.map((coach) => (
                  <SelectItem key={coach.id} value={coach.id}>
                    {coach.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="salary-type" className="text-right">
              薪资类型
            </Label>
            <Select
              value={salaryType}
              onValueChange={setSalaryType}
            >
              <SelectTrigger id="salary-type" className="col-span-3">
                <SelectValue placeholder="请选择薪资类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                <SelectItem value="fixed">固定薪资</SelectItem>
                <SelectItem value="hourly">课时费</SelectItem>
                <SelectItem value="mixed">底薪+课时费</SelectItem>
                <SelectItem value="commission">底薪+提成</SelectItem>
                <SelectItem value="full">底薪+课时费+提成</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="salary-range" className="text-right">
              薪资范围
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="min-salary"
                type="number"
                min="0"
                placeholder="最低薪资"
                value={minSalary}
                onChange={(e) => setMinSalary(e.target.value)}
              />
              <span>至</span>
              <Input
                id="max-salary"
                type="number"
                min="0"
                placeholder="最高薪资"
                value={maxSalary}
                onChange={(e) => setMaxSalary(e.target.value)}
              />
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right">
              薪资状态
            </Label>
            <div className="col-span-3 space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-pending"
                  checked={status.includes("pending")}
                  onCheckedChange={(checked) => handleStatusChange("pending", !!checked)}
                />
                <label
                  htmlFor="status-pending"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  待审核
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-approved"
                  checked={status.includes("approved")}
                  onCheckedChange={(checked) => handleStatusChange("approved", !!checked)}
                />
                <label
                  htmlFor="status-approved"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  已审核
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-paid"
                  checked={status.includes("paid")}
                  onCheckedChange={(checked) => handleStatusChange("paid", !!checked)}
                />
                <label
                  htmlFor="status-paid"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  已发放
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-rejected"
                  checked={status.includes("rejected")}
                  onCheckedChange={(checked) => handleStatusChange("rejected", !!checked)}
                />
                <label
                  htmlFor="status-rejected"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  已拒绝
                </label>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="month-range" className="text-right">
              月份范围
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="start-month"
                type="month"
                placeholder="起始月份"
                value={startMonth}
                onChange={(e) => setStartMonth(e.target.value)}
              />
              <span>至</span>
              <Input
                id="end-month"
                type="month"
                placeholder="结束月份"
                value={endMonth}
                onChange={(e) => setEndMonth(e.target.value)}
              />
            </div>
          </div>
        </div>
        <DialogFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={resetFilters} className="gap-2">
            <X className="h-4 w-4" />
            重置
          </Button>
          <Button onClick={applyFilters} className="gap-2">
            <Filter className="h-4 w-4" />
            应用筛选
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
