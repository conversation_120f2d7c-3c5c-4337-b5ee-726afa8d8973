"use client"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, Filter, Download, Search } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { useState } from "react"
import { AddBookingDialog } from "@/components/venues/bookings/add-booking-dialog"
import { BookingDetailDialog } from "@/components/venues/bookings/booking-detail-dialog"
import { DatePickerWithRangeZh } from "@/components/venues/bookings/date-range-picker-zh"

const venueBookings = [
  {
    id: "B001",
    venue: "1号瑜伽室",
    course: "基础瑜伽入门",
    coach: "张教练",
    date: "2025-03-28",
    time: "10:00-11:30",
    status: "confirmed",
    bookings: 12,
    capacity: 15,
  },
  {
    id: "B002",
    venue: "2号瑜伽室",
    course: "高级瑜伽进阶",
    coach: "李教练",
    date: "2025-03-28",
    time: "14:00-15:30",
    status: "confirmed",
    bookings: 8,
    capacity: 10,
  },
  {
    id: "B003",
    venue: "3号瑜伽室",
    course: "阴瑜伽放松",
    coach: "王教练",
    date: "2025-03-28",
    time: "16:00-17:00",
    status: "confirmed",
    bookings: 15,
    capacity: 15,
  },
  {
    id: "B004",
    venue: "1号瑜伽室",
    course: "孕产瑜伽特训",
    coach: "赵教练",
    date: "2025-03-28",
    time: "18:30-20:00",
    status: "pending",
    bookings: 6,
    capacity: 8,
  },
  {
    id: "B005",
    venue: "4号瑜伽室",
    course: "空中瑜伽体验",
    coach: "刘教练",
    date: "2025-03-29",
    time: "09:00-10:30",
    status: "confirmed",
    bookings: 5,
    capacity: 8,
  },
]

export default function VenueBookingsPage() {
  const [isAddBookingOpen, setIsAddBookingOpen] = useState(false)
  const [isDetailOpen, setIsDetailOpen] = useState(false)
  const [selectedBooking, setSelectedBooking] = useState<(typeof venueBookings)[0] | null>(null)
  const [searchQuery, setSearchQuery] = useState("")

  const handleViewDetail = (booking: (typeof venueBookings)[0]) => {
    setSelectedBooking(booking)
    setIsDetailOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">场地预订</h1>
        <div className="flex gap-2">
          <DatePickerWithRangeZh />
          <Button onClick={() => setIsAddBookingOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加预订
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/3">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索预订信息"
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择场地" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有场地</SelectItem>
              <SelectItem value="1">1号瑜伽室</SelectItem>
              <SelectItem value="2">2号瑜伽室</SelectItem>
              <SelectItem value="3">3号瑜伽室</SelectItem>
              <SelectItem value="4">4号瑜伽室</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="预订状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有状态</SelectItem>
              <SelectItem value="confirmed">已确认</SelectItem>
              <SelectItem value="pending">待确认</SelectItem>
              <SelectItem value="cancelled">已取消</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>
        <Button variant="outline" size="sm" className="ml-auto">
          <Download className="mr-2 h-4 w-4" />
          导出
        </Button>
      </div>

      <Tabs defaultValue="list">
        <TabsList>
          <TabsTrigger value="list">列表视图</TabsTrigger>
          <TabsTrigger value="calendar">日历视图</TabsTrigger>
        </TabsList>
        <TabsContent value="list" className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>预订编号</TableHead>
                    <TableHead>场地</TableHead>
                    <TableHead>课程</TableHead>
                    <TableHead>教练</TableHead>
                    <TableHead>日期</TableHead>
                    <TableHead>时间</TableHead>
                    <TableHead>预约/容量</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {venueBookings.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell className="font-medium">{booking.id}</TableCell>
                      <TableCell>{booking.venue}</TableCell>
                      <TableCell>{booking.course}</TableCell>
                      <TableCell>{booking.coach}</TableCell>
                      <TableCell>{booking.date}</TableCell>
                      <TableCell>{booking.time}</TableCell>
                      <TableCell>
                        {booking.bookings}/{booking.capacity}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            booking.status === "confirmed"
                              ? "default"
                              : booking.status === "pending"
                                ? "outline"
                                : "secondary"
                          }
                        >
                          {booking.status === "confirmed"
                            ? "已确认"
                            : booking.status === "pending"
                              ? "待确认"
                              : "已取消"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" onClick={() => handleViewDetail(booking)}>
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="calendar" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>2025年3月</CardTitle>
              <CardDescription>场地预订日历</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-7 gap-1">
                {["日", "一", "二", "三", "四", "五", "六"].map((day, index) => (
                  <div key={index} className="text-center font-medium p-2">
                    {day}
                  </div>
                ))}
                {Array.from({ length: 35 }).map((_, index) => {
                  // 假设3月1日是周六，前面有5天是2月的
                  const day = index - 5
                  const isCurrentMonth = day >= 0 && day < 31
                  const date = isCurrentMonth ? day + 1 : day < 0 ? 29 + day : day - 30
                  const isToday = isCurrentMonth && date === 28

                  return (
                    <div
                      key={index}
                      className={cn(
                        "h-24 border p-1 relative",
                        isCurrentMonth ? "bg-white" : "bg-gray-50 text-gray-400",
                        isToday ? "border-primary" : "",
                      )}
                    >
                      <div
                        className={cn(
                          "text-xs font-medium",
                          isToday
                            ? "bg-primary text-primary-foreground rounded-full w-5 h-5 flex items-center justify-center"
                            : "",
                        )}
                      >
                        {date}
                      </div>
                      {isCurrentMonth && date === 28 && (
                        <>
                          <div
                            className="mt-1 text-xs bg-blue-100 rounded p-0.5 truncate border-l-2 border-blue-500 cursor-pointer"
                            onClick={() => handleViewDetail(venueBookings[0])}
                          >
                            基础瑜伽 10:00
                          </div>
                          <div
                            className="mt-1 text-xs bg-green-100 rounded p-0.5 truncate border-l-2 border-green-500 cursor-pointer"
                            onClick={() => handleViewDetail(venueBookings[1])}
                          >
                            高级瑜伽 14:00
                          </div>
                          <div
                            className="mt-1 text-xs bg-yellow-100 rounded p-0.5 truncate border-l-2 border-yellow-500 cursor-pointer"
                            onClick={() => handleViewDetail(venueBookings[2])}
                          >
                            阴瑜伽 16:00
                          </div>
                        </>
                      )}
                      {isCurrentMonth && date === 29 && (
                        <div
                          className="mt-1 text-xs bg-pink-100 rounded p-0.5 truncate border-l-2 border-pink-500 cursor-pointer"
                          onClick={() => handleViewDetail(venueBookings[4])}
                        >
                          空中瑜伽 09:00
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <AddBookingDialog open={isAddBookingOpen} onOpenChange={setIsAddBookingOpen} />
      {selectedBooking && (
        <BookingDetailDialog booking={selectedBooking} open={isDetailOpen} onOpenChange={setIsDetailOpen} />
      )}
    </div>
  )
}

