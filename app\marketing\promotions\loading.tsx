import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"

export default function PromotionsLoading() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-8 w-[200px]" />
          <Skeleton className="h-4 w-[300px] mt-2" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-[120px]" />
          <Skeleton className="h-10 w-[100px]" />
          <Skeleton className="h-10 w-[100px]" />
        </div>
      </div>

      <div className="flex items-center gap-4">
        <Skeleton className="h-10 flex-1" />
        <Skeleton className="h-10 w-10" />
        <Skeleton className="h-10 w-[180px]" />
      </div>

      <div>
        <Skeleton className="h-10 w-[400px] mb-4" />

        <Card>
          <CardContent className="p-0">
            <div className="p-4">
              <Skeleton className="h-8 w-full mb-4" />

              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between py-4">
                  <Skeleton className="h-12 w-[200px]" />
                  <Skeleton className="h-6 w-[80px]" />
                  <Skeleton className="h-10 w-[100px]" />
                  <Skeleton className="h-6 w-[60px]" />
                  <Skeleton className="h-6 w-[80px]" />
                  <Skeleton className="h-6 w-[60px]" />
                  <Skeleton className="h-8 w-[40px]" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

