"use client"

import { Calendar, CreditCard, Layers, User } from "lucide-react"

export function SimpleHomePage() {
  return (
    <div className="flex flex-col h-full bg-white">
      {/* 头部 */}
      <div className="bg-gray-100 p-4 text-center">
        <h1 className="text-xl font-bold">静心瑜伽</h1>
        <p className="text-sm text-gray-600 mt-1">
          欢迎来到静心瑜伽，开启您的瑜伽之旅
        </p>
      </div>
      
      {/* 功能图标 */}
      <div className="grid grid-cols-4 gap-4 p-4">
        <div className="flex flex-col items-center">
          <div className="w-14 h-14 rounded-lg bg-purple-100 flex items-center justify-center mb-1">
            <Calendar className="h-6 w-6 text-purple-600" />
          </div>
          <span className="text-xs">预约</span>
        </div>
        
        <div className="flex flex-col items-center">
          <div className="w-14 h-14 rounded-lg bg-red-100 flex items-center justify-center mb-1">
            <CreditCard className="h-6 w-6 text-red-500" />
          </div>
          <span className="text-xs">会员卡</span>
        </div>
        
        <div className="flex flex-col items-center">
          <div className="w-14 h-14 rounded-lg bg-indigo-100 flex items-center justify-center mb-1">
            <Layers className="h-6 w-6 text-indigo-600" />
          </div>
          <span className="text-xs">课程</span>
        </div>
        
        <div className="flex flex-col items-center">
          <div className="w-14 h-14 rounded-lg bg-pink-100 flex items-center justify-center mb-1">
            <User className="h-6 w-6 text-pink-500" />
          </div>
          <span className="text-xs">我的</span>
        </div>
      </div>
      
      {/* 底部导航 */}
      <div className="mt-auto border-t border-gray-200 flex justify-around py-2">
        <div className="flex flex-col items-center text-gray-400">
          <svg viewBox="0 0 24 24" className="h-6 w-6" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
            <polyline points="9 22 9 12 15 12 15 22" />
          </svg>
          <span className="text-xs mt-1">首页</span>
        </div>
        
        <div className="flex flex-col items-center text-gray-400">
          <Calendar className="h-6 w-6" />
          <span className="text-xs mt-1">预约</span>
        </div>
        
        <div className="flex flex-col items-center text-gray-400">
          <Layers className="h-6 w-6" />
          <span className="text-xs mt-1">课程</span>
        </div>
        
        <div className="flex flex-col items-center text-gray-400">
          <User className="h-6 w-6" />
          <span className="text-xs mt-1">我的</span>
        </div>
      </div>
    </div>
  )
}
