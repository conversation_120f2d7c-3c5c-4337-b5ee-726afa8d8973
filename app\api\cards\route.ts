import { NextResponse } from 'next/server';

// 卡类型数据结构
export interface CardType {
  id: number;
  tenant_id: number;
  store_id: number | null; // null 表示总部通用卡
  card_name: string;
  card_type: string; // 次卡、时效卡、年卡等
  price: number;
  original_price: number;
  valid_period: number; // 有效期(天)
  total_count?: number; // 次卡总次数
  description: string;
  features: string[];
  restrictions: string[];
  status: number; // 1-启用中，0-已停用
  is_universal: boolean; // 是否是通用卡（可在连锁所有门店使用）
  created_at: string;
  updated_at: string;
}

// 模拟卡类型数据
const CARD_TYPES: CardType[] = [
  // 总部通用卡
  {
    id: 1,
    tenant_id: 1,
    store_id: null, // 总部卡
    card_name: '静心瑜伽通用年卡',
    card_type: '年卡',
    price: 3699,
    original_price: 4699,
    valid_period: 365,
    description: '全年不限次数瑜伽课程，适用于静心瑜伽所有门店',
    features: ['全年无限次', '所有门店通用', '专属会员活动'],
    restrictions: ['不含私教课程', '不可转让'],
    status: 1,
    is_universal: true,
    created_at: '2023-01-01T08:00:00Z',
    updated_at: '2023-01-01T08:00:00Z'
  },
  {
    id: 2,
    tenant_id: 1,
    store_id: null, // 总部卡
    card_name: '静心瑜伽通用季卡',
    card_type: '季卡',
    price: 1699,
    original_price: 1999,
    valid_period: 90,
    description: '90天不限次数瑜伽课程，适用于静心瑜伽所有门店',
    features: ['90天无限次', '所有门店通用', '赠送瑜伽垫一个'],
    restrictions: ['不含私教课程', '不可转让', '不可续费'],
    status: 1,
    is_universal: true,
    created_at: '2023-01-01T08:30:00Z',
    updated_at: '2023-01-01T08:30:00Z'
  },
  {
    id: 3,
    tenant_id: 1,
    store_id: null, // 总部卡
    card_name: '静心瑜伽通用次卡30次',
    card_type: '次卡',
    price: 999,
    original_price: 1299,
    valid_period: 180,
    total_count: 30,
    description: '30次瑜伽课程，有效期180天，适用于静心瑜伽所有门店',
    features: ['30次瑜伽课程', '所有门店通用', '灵活安排时间'],
    restrictions: ['不含私教课程', '过期作废'],
    status: 1,
    is_universal: true,
    created_at: '2023-01-02T09:00:00Z',
    updated_at: '2023-01-02T09:00:00Z'
  },
  // 分店专属卡
  {
    id: 4,
    tenant_id: 1,
    store_id: 1, // 三里屯总店
    card_name: '三里屯店专享卡',
    card_type: '月卡',
    price: 599,
    original_price: 699,
    valid_period: 30,
    description: '30天不限次数瑜伽课程，仅限三里屯店使用',
    features: ['30天无限次', '赠送一对一体测'],
    restrictions: ['仅限三里屯店使用', '不含私教课程'],
    status: 1,
    is_universal: false,
    created_at: '2023-01-03T10:00:00Z',
    updated_at: '2023-01-03T10:00:00Z'
  },
  {
    id: 5,
    tenant_id: 1,
    store_id: 2, // 国贸分店
    card_name: '国贸店专享次卡',
    card_type: '次卡',
    price: 399,
    original_price: 499,
    valid_period: 90,
    total_count: 10,
    description: '10次瑜伽课程，有效期90天，仅限国贸店使用',
    features: ['10次瑜伽课程', '赠送精美礼品'],
    restrictions: ['仅限国贸店使用', '不含私教课程', '过期作废'],
    status: 1,
    is_universal: false,
    created_at: '2023-01-04T11:00:00Z',
    updated_at: '2023-01-04T11:00:00Z'
  }
];

// GET 获取卡类型列表
export async function GET(request: Request) {
  try {
    // 获取查询参数
    const url = new URL(request.url);
    const storeId = url.searchParams.get('store_id');
    const universal = url.searchParams.get('universal');
    
    // 获取租户ID - 默认为1 (静心瑜伽馆)
    let tenantId = '1';
    const tenantIdHeader = request.headers.get('X-Tenant-ID');
    if (tenantIdHeader) {
      tenantId = tenantIdHeader;
    }
    
    // 根据查询条件过滤卡类型
    let filteredCards = CARD_TYPES.filter(card => 
      card.tenant_id.toString() === tenantId
    );
    
    // 如果指定了门店ID，则过滤出该门店的卡和通用卡
    if (storeId) {
      filteredCards = filteredCards.filter(card => 
        card.store_id === parseInt(storeId) || card.store_id === null
      );
    }
    
    // 如果指定了只查询通用卡
    if (universal === 'true') {
      filteredCards = filteredCards.filter(card => card.is_universal);
    }
    
    // 返回卡类型列表
    return NextResponse.json({
      code: 0,
      message: '获取卡类型列表成功',
      data: {
        cards: filteredCards,
        total: filteredCards.length
      }
    });
  } catch (error) {
    console.error('获取卡类型列表失败:', error);
    return NextResponse.json(
      { code: 500, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST 创建新的卡类型
export async function POST(request: Request) {
  try {
    // 解析请求体
    const body = await request.json();
    
    // 获取租户ID - 默认为1 (静心瑜伽馆)
    let tenantId = 1;
    const tenantIdHeader = request.headers.get('X-Tenant-ID');
    if (tenantIdHeader) {
      tenantId = parseInt(tenantIdHeader);
    }
    
    // 创建新卡类型记录
    const newCard: CardType = {
      ...body,
      id: CARD_TYPES.length + 1,
      tenant_id: tenantId,
      status: body.status || 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // 将新卡类型添加到模拟数据中
    CARD_TYPES.push(newCard);
    
    // 返回创建成功的响应
    return NextResponse.json({
      code: 0,
      message: '创建卡类型成功',
      data: newCard
    });
  } catch (error) {
    console.error('创建卡类型失败:', error);
    return NextResponse.json(
      { code: 500, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}