"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { AlertCircle, Info } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function ReviewSettingsPage() {
  // 基本设置
  const [enableReviews, setEnableReviews] = useState(true)
  const [reviewApproval, setReviewApproval] = useState(true)
  const [allowAnonymous, setAllowAnonymous] = useState(false)
  const [minReviewLength, setMinReviewLength] = useState(10)
  const [maxReviewLength, setMaxReviewLength] = useState(500)
  
  // 评分设置
  const [ratingSystem, setRatingSystem] = useState("star")
  const [minRating, setMinRating] = useState(1)
  const [maxRating, setMaxRating] = useState(5)
  const [ratingLabels, setRatingLabels] = useState({
    1: "非常不满意",
    2: "不满意",
    3: "一般",
    4: "满意",
    5: "非常满意",
  })
  
  // 通知设置
  const [notifyNewReview, setNotifyNewReview] = useState(true)
  const [notifyReviewReply, setNotifyReviewReply] = useState(true)
  const [notifyLowRating, setNotifyLowRating] = useState(true)
  const [lowRatingThreshold, setLowRatingThreshold] = useState(3)
  
  // 权限设置
  const [rolesCanManageReviews, setRolesCanManageReviews] = useState([
    "超级管理员",
    "店长",
  ])
  const [rolesCanReplyReviews, setRolesCanReplyReviews] = useState([
    "超级管理员",
    "店长",
    "教练",
  ])
  
  // 保存设置
  const handleSaveSettings = () => {
    // 实际项目中应该调用API保存设置
    toast({
      title: "设置已保存",
      description: "评价系统设置已成功更新",
    })
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">评价设置</h1>
        <p className="text-muted-foreground mt-1">配置课程与教练评价系统的各项设置</p>
      </div>

      <Tabs defaultValue="basic">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">基本设置</TabsTrigger>
          <TabsTrigger value="rating">评分设置</TabsTrigger>
          <TabsTrigger value="notification">通知设置</TabsTrigger>
          <TabsTrigger value="permission">权限设置</TabsTrigger>
        </TabsList>
        
        {/* 基本设置 */}
        <TabsContent value="basic" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>基本设置</CardTitle>
              <CardDescription>配置评价系统的基本功能和行为</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>启用评价系统</Label>
                  <p className="text-sm text-muted-foreground">允许会员对课程和教练进行评价</p>
                </div>
                <Switch checked={enableReviews} onCheckedChange={setEnableReviews} />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>评价需要审核</Label>
                  <p className="text-sm text-muted-foreground">新评价需要管理员审核后才能显示</p>
                </div>
                <Switch checked={reviewApproval} onCheckedChange={setReviewApproval} />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>允许匿名评价</Label>
                  <p className="text-sm text-muted-foreground">允许会员匿名发表评价</p>
                </div>
                <Switch checked={allowAnonymous} onCheckedChange={setAllowAnonymous} />
              </div>
              
              <Separator />
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="min-review-length">最小评价长度</Label>
                  <Input
                    id="min-review-length"
                    type="number"
                    min={0}
                    max={100}
                    value={minReviewLength}
                    onChange={(e) => setMinReviewLength(parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">评价内容的最小字符数</p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="max-review-length">最大评价长度</Label>
                  <Input
                    id="max-review-length"
                    type="number"
                    min={100}
                    max={2000}
                    value={maxReviewLength}
                    onChange={(e) => setMaxReviewLength(parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground">评价内容的最大字符数</p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings}>保存设置</Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* 评分设置 */}
        <TabsContent value="rating" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>评分设置</CardTitle>
              <CardDescription>配置评价系统的评分机制</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="rating-system">评分系统</Label>
                <Select value={ratingSystem} onValueChange={setRatingSystem}>
                  <SelectTrigger id="rating-system">
                    <SelectValue placeholder="选择评分系统" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="star">星级评分 (1-5星)</SelectItem>
                    <SelectItem value="number">数字评分 (1-10分)</SelectItem>
                    <SelectItem value="emoji">表情评分 (😡😕😐😊😄)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">选择评价系统使用的评分方式</p>
              </div>
              
              <Separator />
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="min-rating">最低评分</Label>
                  <Input
                    id="min-rating"
                    type="number"
                    min={1}
                    max={3}
                    value={minRating}
                    onChange={(e) => setMinRating(parseInt(e.target.value))}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="max-rating">最高评分</Label>
                  <Input
                    id="max-rating"
                    type="number"
                    min={3}
                    max={10}
                    value={maxRating}
                    onChange={(e) => setMaxRating(parseInt(e.target.value))}
                  />
                </div>
              </div>
              
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>提示</AlertTitle>
                <AlertDescription>
                  评分标签设置功能将在下一版本中提供，敬请期待。
                </AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings}>保存设置</Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* 通知设置 */}
        <TabsContent value="notification" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>通知设置</CardTitle>
              <CardDescription>配置评价相关的通知规则</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>新评价通知</Label>
                  <p className="text-sm text-muted-foreground">收到新评价时通知管理员</p>
                </div>
                <Switch checked={notifyNewReview} onCheckedChange={setNotifyNewReview} />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>评价回复通知</Label>
                  <p className="text-sm text-muted-foreground">评价被回复时通知会员</p>
                </div>
                <Switch checked={notifyReviewReply} onCheckedChange={setNotifyReviewReply} />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>低评分通知</Label>
                  <p className="text-sm text-muted-foreground">收到低评分时立即通知管理员</p>
                </div>
                <Switch checked={notifyLowRating} onCheckedChange={setNotifyLowRating} />
              </div>
              
              {notifyLowRating && (
                <div className="space-y-2 ml-6">
                  <Label htmlFor="low-rating-threshold">低评分阈值</Label>
                  <Select 
                    value={lowRatingThreshold.toString()} 
                    onValueChange={(value) => setLowRatingThreshold(parseInt(value))}
                  >
                    <SelectTrigger id="low-rating-threshold">
                      <SelectValue placeholder="选择低评分阈值" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1星及以下</SelectItem>
                      <SelectItem value="2">2星及以下</SelectItem>
                      <SelectItem value="3">3星及以下</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">低于或等于此评分将触发通知</p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings}>保存设置</Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* 权限设置 */}
        <TabsContent value="permission" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>权限设置</CardTitle>
              <CardDescription>配置评价系统的权限控制</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>可管理评价的角色</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {["超级管理员", "店长", "会籍顾问", "前台"].map((role) => (
                    <div key={role} className="flex items-center space-x-2">
                      <Checkbox 
                        id={`manage-${role}`} 
                        checked={rolesCanManageReviews.includes(role)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setRolesCanManageReviews([...rolesCanManageReviews, role])
                          } else {
                            setRolesCanManageReviews(
                              rolesCanManageReviews.filter((r) => r !== role)
                            )
                          }
                        }}
                      />
                      <Label htmlFor={`manage-${role}`} className="font-normal">
                        {role}
                      </Label>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground">可以审核、隐藏和删除评价的角色</p>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <Label>可回复评价的角色</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {["超级管理员", "店长", "教练", "会籍顾问", "前台"].map((role) => (
                    <div key={role} className="flex items-center space-x-2">
                      <Checkbox 
                        id={`reply-${role}`} 
                        checked={rolesCanReplyReviews.includes(role)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setRolesCanReplyReviews([...rolesCanReplyReviews, role])
                          } else {
                            setRolesCanReplyReviews(
                              rolesCanReplyReviews.filter((r) => r !== role)
                            )
                          }
                        }}
                      />
                      <Label htmlFor={`reply-${role}`} className="font-normal">
                        {role}
                      </Label>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground">可以回复评价的角色</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleSaveSettings}>保存设置</Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
