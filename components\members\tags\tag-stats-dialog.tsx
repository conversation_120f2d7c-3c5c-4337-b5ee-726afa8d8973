import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from "lucide-react"

type TagStatsDialogProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function TagStatsDialog({ open, onOpenChange }: TagStatsDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>标签统计分析</DialogTitle>
          <DialogDescription>查看会员标签的使用情况和分布统计</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">总体概览</TabsTrigger>
            <TabsTrigger value="distribution">标签分布</TabsTrigger>
            <TabsTrigger value="trends">使用趋势</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">标签总数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">24</div>
                  <p className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+3</span>
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">标签使用次数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">358</div>
                  <p className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+42</span>
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">平均标签/会员</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">2.4</div>
                  <p className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+0.3</span>
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>标签类型分布</CardTitle>
                  <CardDescription>按标签类型统计</CardDescription>
                </CardHeader>
                <CardContent className="flex justify-center items-center p-6">
                  <div className="flex items-center justify-center h-[200px] w-[200px] rounded-full bg-muted relative">
                    <PieChart className="h-16 w-16 text-muted-foreground" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">图表</p>
                        <p className="text-sm text-muted-foreground">占位符</p>
                      </div>
                    </div>
                  </div>
                  <div className="ml-8 space-y-2">
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full bg-blue-500 mr-2"></div>
                      <span className="text-sm">系统标签 (25%)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full bg-yellow-500 mr-2"></div>
                      <span className="text-sm">消费标签 (20%)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
                      <span className="text-sm">兴趣标签 (30%)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full bg-purple-500 mr-2"></div>
                      <span className="text-sm">活跃度标签 (15%)</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full bg-gray-500 mr-2"></div>
                      <span className="text-sm">其他标签 (10%)</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>标签使用排名</CardTitle>
                  <CardDescription>使用频率最高的标签</CardDescription>
                </CardHeader>
                <CardContent className="flex justify-center items-center p-6">
                  <div className="w-full h-[200px] flex items-end justify-between gap-2">
                    <div className="flex flex-col items-center">
                      <div className="bg-blue-500 w-12 h-[160px] rounded-t-md"></div>
                      <span className="text-xs mt-1">新会员</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="bg-purple-500 w-12 h-[140px] rounded-t-md"></div>
                      <span className="text-xs mt-1">老会员</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="bg-green-500 w-12 h-[120px] rounded-t-md"></div>
                      <span className="text-xs mt-1">瑜伽爱好者</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="bg-yellow-500 w-12 h-[100px] rounded-t-md"></div>
                      <span className="text-xs mt-1">VIP</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="bg-red-500 w-12 h-[80px] rounded-t-md"></div>
                      <span className="text-xs mt-1">潜在流失</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="bg-cyan-500 w-12 h-[60px] rounded-t-md"></div>
                      <span className="text-xs mt-1">高频消费</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="distribution" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>会员标签分布</CardTitle>
                <CardDescription>会员拥有的标签数量分布</CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center items-center p-6">
                <div className="w-full h-[250px] flex items-end justify-between gap-2">
                  <div className="flex flex-col items-center">
                    <div className="bg-gray-200 w-16 h-[50px] rounded-t-md"></div>
                    <span className="text-xs mt-1">0个标签</span>
                    <span className="text-xs text-muted-foreground">5%</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <div className="bg-blue-200 w-16 h-[100px] rounded-t-md"></div>
                    <span className="text-xs mt-1">1个标签</span>
                    <span className="text-xs text-muted-foreground">20%</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <div className="bg-blue-300 w-16 h-[150px] rounded-t-md"></div>
                    <span className="text-xs mt-1">2个标签</span>
                    <span className="text-xs text-muted-foreground">30%</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <div className="bg-blue-400 w-16 h-[125px] rounded-t-md"></div>
                    <span className="text-xs mt-1">3个标签</span>
                    <span className="text-xs text-muted-foreground">25%</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <div className="bg-blue-500 w-16 h-[75px] rounded-t-md"></div>
                    <span className="text-xs mt-1">4个标签</span>
                    <span className="text-xs text-muted-foreground">15%</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <div className="bg-blue-600 w-16 h-[25px] rounded-t-md"></div>
                    <span className="text-xs mt-1">5个以上</span>
                    <span className="text-xs text-muted-foreground">5%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>标签��叠分析</CardTitle>
                  <CardDescription>标签之间的重叠程度</CardDescription>
                </CardHeader>
                <CardContent className="flex justify-center items-center p-6 h-[250px]">
                  <div className="flex items-center justify-center h-full w-full relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <BarChart className="h-16 w-16 text-muted-foreground mx-auto" />
                        <p className="text-sm text-muted-foreground mt-2">标签重叠分析图表</p>
                        <p className="text-sm text-muted-foreground">占位符</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>标签关联度</CardTitle>
                  <CardDescription>标签之间的关联程度</CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                        <span className="text-sm">新会员 + VIP</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-blue-500 h-2.5 rounded-full" style={{ width: "10%" }}></div>
                      </div>
                      <span className="text-sm">10%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span className="text-sm">瑜伽爱好者 + 高频消费</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-green-500 h-2.5 rounded-full" style={{ width: "65%" }}></div>
                      </div>
                      <span className="text-sm">65%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                        <span className="text-sm">老会员 + VIP</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-purple-500 h-2.5 rounded-full" style={{ width: "75%" }}></div>
                      </div>
                      <span className="text-sm">75%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                        <span className="text-sm">潜在流失 + 高频消费</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-red-500 h-2.5 rounded-full" style={{ width: "5%" }}></div>
                      </div>
                      <span className="text-sm">5%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                        <span className="text-sm">周末班 + 瑜伽爱好者</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-yellow-500 h-2.5 rounded-full" style={{ width: "45%" }}></div>
                      </div>
                      <span className="text-sm">45%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>标签使用趋势</CardTitle>
                <CardDescription>过去6个月标签使用情况</CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center items-center p-6 h-[300px]">
                <div className="flex items-center justify-center h-full w-full relative">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <LineChart className="h-16 w-16 text-muted-foreground mx-auto" />
                      <p className="text-sm text-muted-foreground mt-2">标签使用趋势图表</p>
                      <p className="text-sm text-muted-foreground">占位符</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>增长最快的标签</CardTitle>
                  <CardDescription>过去30天增长最快的标签</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span className="text-sm">瑜伽爱好者</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-green-500 h-2.5 rounded-full" style={{ width: "120%" }}></div>
                      </div>
                      <span className="text-sm">+120%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                        <span className="text-sm">周末班</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-yellow-500 h-2.5 rounded-full" style={{ width: "85%" }}></div>
                      </div>
                      <span className="text-sm">+85%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                        <span className="text-sm">新会员</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-blue-500 h-2.5 rounded-full" style={{ width: "60%" }}></div>
                      </div>
                      <span className="text-sm">+60%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-cyan-500 mr-2"></div>
                        <span className="text-sm">高频消费</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-cyan-500 h-2.5 rounded-full" style={{ width: "45%" }}></div>
                      </div>
                      <span className="text-sm">+45%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>减少最多的标签</CardTitle>
                  <CardDescription>过去30天减少最多的标签</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                        <span className="text-sm">潜在流失</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-red-500 h-2.5 rounded-full" style={{ width: "30%" }}></div>
                      </div>
                      <span className="text-sm">-30%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
                        <span className="text-sm">教练推荐</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-orange-500 h-2.5 rounded-full" style={{ width: "25%" }}></div>
                      </div>
                      <span className="text-sm">-25%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                        <span className="text-sm">老会员</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-purple-500 h-2.5 rounded-full" style={{ width: "15%" }}></div>
                      </div>
                      <span className="text-sm">-15%</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-3 h-3 rounded-full bg-pink-500 mr-2"></div>
                        <span className="text-sm">其他标签</span>
                      </div>
                      <div className="w-[60%] bg-gray-200 rounded-full h-2.5">
                        <div className="bg-pink-500 h-2.5 rounded-full" style={{ width: "10%" }}></div>
                      </div>
                      <span className="text-sm">-10%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

