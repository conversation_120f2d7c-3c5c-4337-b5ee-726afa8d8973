"use client"

import { useState } from "react"
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  <PERSON>alog<PERSON>ooter, 
  <PERSON>alogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/components/ui/use-toast"
import { Download, Upload, AlertCircle, FileSpreadsheet, CheckCircle2 } from "lucide-react"

interface ImportLeadsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ImportLeadsDialog({ open, onOpenChange }: ImportLeadsDialogProps) {
  const { toast } = useToast()
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle")
  const [errorMessage, setErrorMessage] = useState("")

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      // 检查文件类型
      if (!selectedFile.name.endsWith('.xlsx') && !selectedFile.name.endsWith('.csv')) {
        toast({
          title: "文件格式错误",
          description: "请上传 Excel (.xlsx) 或 CSV (.csv) 格式的文件",
          variant: "destructive",
        })
        return
      }
      
      // 检查文件大小 (限制为 5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        toast({
          title: "文件过大",
          description: "文件大小不能超过 5MB",
          variant: "destructive",
        })
        return
      }
      
      setFile(selectedFile)
      setUploadStatus("idle")
      setErrorMessage("")
    }
  }

  // 处理文件上传
  const handleUpload = async () => {
    if (!file) {
      toast({
        title: "请选择文件",
        description: "请先选择要导入的潜客数据文件",
        variant: "destructive",
      })
      return
    }
    
    setIsUploading(true)
    setUploadStatus("uploading")
    setUploadProgress(0)
    
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 95) {
          clearInterval(progressInterval)
          return prev
        }
        return prev + 5
      })
    }, 200)
    
    try {
      // 这里应该调用API上传文件
      // const formData = new FormData()
      // formData.append('file', file)
      // const response = await fetch('/api/leads/import', {
      //   method: 'POST',
      //   body: formData,
      // })
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 模拟成功响应
      clearInterval(progressInterval)
      setUploadProgress(100)
      setUploadStatus("success")
      
      toast({
        title: "导入成功",
        description: `成功导入 ${Math.floor(Math.random() * 50) + 10} 条潜客数据`,
      })
      
      // 3秒后关闭对话框
      setTimeout(() => {
        onOpenChange(false)
        setFile(null)
        setUploadStatus("idle")
        setUploadProgress(0)
      }, 3000)
    } catch (error) {
      clearInterval(progressInterval)
      setUploadStatus("error")
      setErrorMessage("导入失败，请检查文件格式是否正确，然后重试")
      
      toast({
        title: "导入失败",
        description: "上传文件时出错，请重试",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
    }
  }

  // 下载模板
  const handleDownloadTemplate = () => {
    // 实际应用中应该提供一个真实的模板下载链接
    toast({
      title: "模板下载",
      description: "潜客导入模板已开始下载",
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>导入潜客数据</DialogTitle>
          <DialogDescription>
            批量导入潜客数据，支持 Excel 和 CSV 格式
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>导入说明</AlertTitle>
            <AlertDescription>
              请确保您的文件符合导入模板格式，必填字段包括：姓名、手机号码、来源渠道。
            </AlertDescription>
          </Alert>
          
          <div className="space-y-2">
            <Label htmlFor="template">导入模板</Label>
            <div className="flex items-center gap-2">
              <FileSpreadsheet className="h-5 w-5 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">潜客导入模板.xlsx</span>
              <Button variant="outline" size="sm" className="ml-auto" onClick={handleDownloadTemplate}>
                <Download className="mr-2 h-4 w-4" />
                下载模板
              </Button>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="file">选择文件</Label>
            <div className="flex items-center gap-2">
              <Input
                id="file"
                type="file"
                accept=".xlsx,.csv"
                onChange={handleFileChange}
                disabled={isUploading || uploadStatus === "success"}
              />
            </div>
            {file && (
              <p className="text-sm text-muted-foreground">
                已选择: {file.name} ({(file.size / 1024).toFixed(2)} KB)
              </p>
            )}
          </div>
          
          {uploadStatus === "uploading" && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>上传进度</span>
                <span>{uploadProgress}%</span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2.5">
                <div
                  className="bg-primary h-2.5 rounded-full"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          )}
          
          {uploadStatus === "success" && (
            <div className="flex items-center gap-2 text-green-600 bg-green-50 p-3 rounded-md">
              <CheckCircle2 className="h-5 w-5" />
              <span>潜客数据导入成功！</span>
            </div>
          )}
          
          {uploadStatus === "error" && (
            <div className="flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-md">
              <AlertCircle className="h-5 w-5" />
              <span>{errorMessage}</span>
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUploading}
          >
            取消
          </Button>
          <Button
            type="button"
            onClick={handleUpload}
            disabled={!file || isUploading || uploadStatus === "success"}
          >
            {isUploading ? (
              <>
                <Upload className="mr-2 h-4 w-4 animate-spin" />
                上传中...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                开始导入
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
