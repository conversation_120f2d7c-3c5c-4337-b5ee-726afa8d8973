"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  StarIcon,
  Filter,
  Download,
  BarChart2,
  Search,
  Calendar,
  CheckCircle,
  XCircle,
  EyeOff,
  Eye,
  MessageSquare,
  RefreshCw,
  AlertTriangle,
  ThumbsUp,
  ThumbsDown,
  MoreHorizontal,
  ChevronDown
} from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

import { ReviewDetailDialog } from "@/components/reviews/review-detail-dialog"
import { ReviewStatsDialog } from "@/components/reviews/review-stats-dialog"
import { AdvancedFilterDialog } from "@/components/reviews/advanced-filter-dialog"
import { Review, ReviewFilterParams, ReviewStats } from "@/lib/types/review"
import { reviewApi } from "@/lib/api/review"

// 模拟数据 - 教练列表
const coaches = [
  { id: 1, name: "张教练" },
  { id: 2, name: "李教练" },
  { id: 3, name: "王教练" },
  { id: 4, name: "赵教练" },
  { id: 5, name: "刘教练" },
]

// 模拟数据 - 课程类型
const courseTypes = [
  { id: 1, name: "基础瑜伽" },
  { id: 2, name: "高级瑜伽" },
  { id: 3, name: "阴瑜伽" },
  { id: 4, name: "孕产瑜伽" },
  { id: 5, name: "空中瑜伽" },
  { id: 6, name: "理疗瑜伽" },
  { id: 7, name: "热瑜伽" },
]

export default function ReviewsPage() {
  // 状态管理
  const [reviews, setReviews] = useState<Review[]>([])
  const [stats, setStats] = useState<ReviewStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedReview, setSelectedReview] = useState<Review | null>(null)
  const [showReviewDetail, setShowReviewDetail] = useState(false)
  const [showStats, setShowStats] = useState(false)
  const [showFilter, setShowFilter] = useState(false)
  const [activeTab, setActiveTab] = useState("all")
  const [searchKeyword, setSearchKeyword] = useState("")
  const [selectedCoach, setSelectedCoach] = useState("all")
  const [selectedCourseType, setSelectedCourseType] = useState("all")
  const [selectedRating, setSelectedRating] = useState("all")
  const [selectedReviews, setSelectedReviews] = useState<number[]>([])
  const [sortBy, setSortBy] = useState("date-desc")
  const [currentPage, setCurrentPage] = useState(1)
  const [currentFilters, setCurrentFilters] = useState<ReviewFilterParams>({})
  const itemsPerPage = 10

  // 加载评价数据
  const loadReviews = async () => {
    setLoading(true)
    try {
      // 构建筛选参数
      const filters: ReviewFilterParams = { ...currentFilters }

      // 标签页筛选
      if (activeTab !== "all") {
        filters.status = activeTab as any
      }

      // 关键词搜索
      if (searchKeyword.trim()) {
        filters.keyword = searchKeyword
      }

      // 教练筛选
      if (selectedCoach !== "all") {
        filters.coachId = selectedCoach
      }

      // 课程类型筛选
      if (selectedCourseType !== "all") {
        filters.courseType = selectedCourseType
      }

      // 评分筛选
      if (selectedRating !== "all") {
        filters.rating = selectedRating
      }

      // 排序
      filters.sortBy = sortBy

      // 调用API
      const response = await reviewApi.getReviews(filters)
      setReviews(response.data)
      setStats(response.stats)
    } catch (error) {
      console.error("加载评价失败:", error)
      toast({
        title: "加载失败",
        description: "无法加载评价数据，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 首次加载和筛选条件变化时重新加载数据
  useEffect(() => {
    loadReviews()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, selectedCoach, selectedCourseType, selectedRating, sortBy, currentFilters])

  // 搜索关键词时使用防抖
  useEffect(() => {
    const timer = setTimeout(() => {
      loadReviews()
    }, 500)

    return () => clearTimeout(timer)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchKeyword])

  // 处理评价点击
  const handleReviewClick = (review: Review) => {
    setSelectedReview(review)
    setShowReviewDetail(true)
  }

  // 处理评价选择
  const toggleSelectReview = (reviewId: number) => {
    if (selectedReviews.includes(reviewId)) {
      setSelectedReviews(selectedReviews.filter(id => id !== reviewId))
    } else {
      setSelectedReviews([...selectedReviews, reviewId])
    }
  }

  // 处理全选/取消全选
  const toggleSelectAll = (reviews: Review[]) => {
    if (selectedReviews.length === reviews.length) {
      setSelectedReviews([])
    } else {
      setSelectedReviews(reviews.map(review => review.id))
    }
  }

  // 批量操作处理
  const handleBatchAction = async (action: string) => {
    if (selectedReviews.length === 0) {
      toast({
        title: "请先选择评价",
        description: "您需要先选择至少一条评价才能执行此操作",
        variant: "destructive",
      })
      return
    }

    let actionText = ""
    let successText = ""
    let status = ""

    switch (action) {
      case "approve":
        actionText = "批准"
        successText = "已批准"
        status = "published"
        break
      case "reject":
        actionText = "拒绝"
        successText = "已拒绝"
        status = "hidden"
        break
      case "hide":
        actionText = "隐藏"
        successText = "已隐藏"
        status = "hidden"
        break
      case "show":
        actionText = "显示"
        successText = "已显示"
        status = "published"
        break
      case "delete":
        actionText = "删除"
        successText = "已删除"
        status = "deleted" // 实际项目中可能需要单独的删除API
        break
    }

    if (window.confirm(`确定要${actionText}选中的 ${selectedReviews.length} 条评价吗？`)) {
      try {
        await reviewApi.batchUpdateStatus(selectedReviews, status)

        toast({
          title: `批量${actionText}成功`,
          description: `${selectedReviews.length} 条评价${successText}`,
        })

        setSelectedReviews([])
        loadReviews()
      } catch (error) {
        toast({
          title: `批量${actionText}失败`,
          description: "操作失败，请稍后重试",
          variant: "destructive",
        })
      }
    }
  }

  // 处理回复评价
  const handleReply = (reviewId: number) => {
    const review = reviews.find(r => r.id === reviewId)
    if (review) {
      setSelectedReview(review)
      setShowReviewDetail(true)
    }
  }

  // 处理审核评价
  const handleReviewAction = async (reviewId: number, action: string) => {
    let actionText = ""
    let successText = ""
    let status = ""

    switch (action) {
      case "approve":
        actionText = "批准"
        successText = "已批准并发布"
        status = "published"
        break
      case "reject":
        actionText = "拒绝"
        successText = "已拒绝"
        status = "hidden"
        break
      case "hide":
        actionText = "隐藏"
        successText = "已隐藏"
        status = "hidden"
        break
      case "show":
        actionText = "显示"
        successText = "已显示"
        status = "published"
        break
      case "delete":
        actionText = "删除"
        successText = "已删除"
        status = "deleted" // 实际项目中可能需要单独的删除API
        break
    }

    if (window.confirm(`确定要${actionText}此评价吗？`)) {
      try {
        await reviewApi.updateReviewStatus(reviewId, status)

        toast({
          title: `评价${actionText}成功`,
          description: `评价${successText}`,
        })

        loadReviews()
      } catch (error) {
        toast({
          title: `评价${actionText}失败`,
          description: "操作失败，请稍后重试",
          variant: "destructive",
        })
      }
    }
  }

  // 获取当前页面的评价
  const getCurrentPageReviews = () => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return reviews.slice(startIndex, startIndex + itemsPerPage)
  }

  // 计算总页数
  const totalPages = Math.ceil(reviews.length / itemsPerPage)

  // 获取评分样式
  const getRatingStyle = (rating: number) => {
    return "text-primary"
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge variant="default">已发布</Badge>
      case "pending":
        return <Badge variant="secondary">待审核</Badge>
      case "hidden":
        return <Badge variant="outline">已隐藏</Badge>
      default:
        return null
    }
  }

  // 应用高级筛选
  const handleApplyAdvancedFilter = (filters: ReviewFilterParams) => {
    setCurrentFilters(filters)
    setCurrentPage(1)
  }

  // 处理评价更新
  const handleReviewUpdated = () => {
    loadReviews()
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">课程与教练评价</h1>

        <div className="flex flex-wrap gap-2">
          {stats && (
            <Card className="border-dashed">
              <CardContent className="p-2 flex items-center gap-4">
                <div className="text-center">
                  <div className="text-lg font-semibold">{stats.total}</div>
                  <div className="text-xs text-muted-foreground">总评价</div>
                </div>
                <Separator orientation="vertical" className="h-8" />
                <div className="text-center">
                  <div className="text-lg font-semibold">{stats.averageRating.toFixed(1)}</div>
                  <div className="text-xs text-muted-foreground">平均评分</div>
                </div>
                <Separator orientation="vertical" className="h-8" />
                <div className="text-center">
                  <div className="text-lg font-semibold">{stats.pending}</div>
                  <div className="text-xs text-muted-foreground">待审核</div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={() => setShowFilter(true)}>
              <Filter className="h-4 w-4" />
              <span className="sr-only">高级筛选</span>
            </Button>
            <Button variant="outline" size="icon" onClick={() => setShowStats(true)}>
              <BarChart2 className="h-4 w-4" />
              <span className="sr-only">统计分析</span>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">更多操作</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>更多操作</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Download className="mr-2 h-4 w-4" />
                  导出评价
                </DropdownMenuItem>
                <DropdownMenuItem onClick={loadReviews}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  刷新数据
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all" className="relative">
            全部评价
            {stats && <Badge className="ml-1 bg-primary/10 text-primary hover:bg-primary/20">{stats.total}</Badge>}
          </TabsTrigger>
          <TabsTrigger value="published" className="relative">
            已发布
            {stats && <Badge className="ml-1 bg-primary/10 text-primary hover:bg-primary/20">{stats.published}</Badge>}
          </TabsTrigger>
          <TabsTrigger value="pending" className="relative">
            待审核
            {stats && <Badge className="ml-1 bg-primary/10 text-primary hover:bg-primary/20">{stats.pending}</Badge>}
          </TabsTrigger>
          <TabsTrigger value="hidden" className="relative">
            已隐藏
            {stats && <Badge className="ml-1 bg-primary/10 text-primary hover:bg-primary/20">{stats.hidden}</Badge>}
          </TabsTrigger>
        </TabsList>

        <div className="mt-4 flex flex-col gap-4 md:flex-row">
          <div className="w-full md:w-1/3 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索教练、会员或评价内容"
              className="pl-10"
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
            />
          </div>
          <div className="w-full md:w-1/4">
            <Select value={selectedCoach} onValueChange={setSelectedCoach}>
              <SelectTrigger>
                <SelectValue placeholder="选择教练" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有教练</SelectItem>
                {coaches.map((coach) => (
                  <SelectItem key={coach.id} value={coach.id.toString()}>
                    {coach.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="w-full md:w-1/4">
            <Select value={selectedCourseType} onValueChange={setSelectedCourseType}>
              <SelectTrigger>
                <SelectValue placeholder="课程类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                {courseTypes.map((type) => (
                  <SelectItem key={type.id} value={type.name}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="w-full md:w-1/6">
            <Select value={selectedRating} onValueChange={setSelectedRating}>
              <SelectTrigger>
                <SelectValue placeholder="评分筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有评分</SelectItem>
                <SelectItem value="5">5星</SelectItem>
                <SelectItem value="4">4星</SelectItem>
                <SelectItem value="3">3星及以下</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full md:w-1/6">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="排序方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date-desc">最新评价</SelectItem>
                <SelectItem value="date-asc">最早评价</SelectItem>
                <SelectItem value="rating-desc">评分从高到低</SelectItem>
                <SelectItem value="rating-asc">评分从低到高</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </Tabs>

      {/* 批量操作工具栏 */}
      {selectedReviews.length > 0 && (
        <Card className="mb-4 bg-muted/30">
          <CardContent className="p-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">已选择 {selectedReviews.length} 条评价</span>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => handleBatchAction("approve")}>
                <CheckCircle className="mr-2 h-4 w-4" />
                批量通过
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleBatchAction("reject")}>
                <XCircle className="mr-2 h-4 w-4" />
                批量拒绝
              </Button>
              <Button variant="outline" size="sm" onClick={() => handleBatchAction("hide")}>
                <EyeOff className="mr-2 h-4 w-4" />
                批量隐藏
              </Button>
              <Button variant="destructive" size="sm" onClick={() => handleBatchAction("delete")}>
                <Trash className="mr-2 h-4 w-4" />
                批量删除
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 评价列表 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-4">
            <CardTitle>评价列表</CardTitle>
            <div className="text-sm text-muted-foreground">
              共 {reviews.length} 条评价
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedReviews.length > 0 && selectedReviews.length === getCurrentPageReviews().length}
                        onCheckedChange={() => toggleSelectAll(getCurrentPageReviews())}
                      />
                    </TableHead>
                    <TableHead>会员</TableHead>
                    <TableHead>教练</TableHead>
                    <TableHead>课程</TableHead>
                    <TableHead>评分</TableHead>
                    <TableHead>评价内容</TableHead>
                    <TableHead>日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {getCurrentPageReviews().length > 0 ? (
                    getCurrentPageReviews().map((review) => (
                      <TableRow key={review.id}>
                        <TableCell className="w-12">
                          <Checkbox
                            checked={selectedReviews.includes(review.id)}
                            onCheckedChange={() => toggleSelectReview(review.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={review.member.avatar} alt={review.member.name} />
                              <AvatarFallback>{review.member.name[0]}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{review.member.name}</div>
                              <div className="text-xs text-muted-foreground">{review.member.level}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={review.coach.avatar} alt={review.coach.name} />
                              <AvatarFallback>{review.coach.name[0]}</AvatarFallback>
                            </Avatar>
                            <span>{review.coach.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{review.course.name}</div>
                            <div className="text-xs text-muted-foreground">{review.course.type}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <span className={`font-medium mr-1 ${getRatingStyle(review.rating)}`}>{review.rating}</span>
                            <div className="flex">
                              {Array.from({ length: 5 }).map((_, i) => (
                                <StarIcon
                                  key={i}
                                  className={`h-3 w-3 ${
                                    i < review.rating ? "text-primary fill-primary" : "text-gray-300"
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="max-w-[300px]">
                          <div className="truncate">{review.comment}</div>
                        </TableCell>
                        <TableCell>
                          <div className="whitespace-nowrap">{review.date}</div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(review.status)}
                          {!review.hasReply && review.status === "published" && (
                            <Badge variant="outline" className="ml-1">未回复</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleReviewClick(review)}
                            >
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">查看详情</span>
                            </Button>

                            {!review.hasReply && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleReply(review.id)}
                              >
                                <MessageSquare className="h-4 w-4" />
                                <span className="sr-only">回复</span>
                              </Button>
                            )}

                            {review.status === "pending" && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="text-green-600 hover:text-green-700 hover:bg-green-50"
                                  onClick={() => handleReviewAction(review.id, "approve")}
                                >
                                  <CheckCircle className="h-4 w-4" />
                                  <span className="sr-only">通过</span>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  onClick={() => handleReviewAction(review.id, "reject")}
                                >
                                  <XCircle className="h-4 w-4" />
                                  <span className="sr-only">拒绝</span>
                                </Button>
                              </>
                            )}

                            {review.status === "published" && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleReviewAction(review.id, "hide")}
                              >
                                <EyeOff className="h-4 w-4" />
                                <span className="sr-only">隐藏</span>
                              </Button>
                            )}

                            {review.status === "hidden" && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleReviewAction(review.id, "show")}
                              >
                                <Eye className="h-4 w-4" />
                                <span className="sr-only">显示</span>
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={9} className="h-32 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <AlertTriangle className="h-8 w-8 mb-2" />
                          <p>没有找到符合条件的评价</p>
                          <Button variant="link" onClick={() => {
                            setSearchKeyword("")
                            setSelectedCoach("all")
                            setSelectedCourseType("all")
                            setSelectedRating("all")
                            setCurrentFilters({})
                          }}>
                            清除筛选条件
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex items-center justify-between p-4 border-t">
          <div className="text-sm text-muted-foreground">
            显示 {getCurrentPageReviews().length} 条，共 {reviews.length} 条
          </div>

          {totalPages > 1 && (
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  />
                </PaginationItem>

                {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                  let pageNumber: number

                  // 显示当前页附近的页码
                  if (totalPages <= 5) {
                    pageNumber = i + 1
                  } else if (currentPage <= 3) {
                    pageNumber = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    pageNumber = totalPages - 4 + i
                  } else {
                    pageNumber = currentPage - 2 + i
                  }

                  return (
                    <PaginationItem key={i}>
                      <PaginationLink
                        isActive={currentPage === pageNumber}
                        onClick={() => setCurrentPage(pageNumber)}
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  )
                })}

                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <>
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink onClick={() => setCurrentPage(totalPages)}>
                        {totalPages}
                      </PaginationLink>
                    </PaginationItem>
                  </>
                )}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </CardFooter>
      </Card>

      {/* 页面底部信息 */}
      <div className="mt-8 border-t pt-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 text-sm text-muted-foreground">
          <div>
            <p>提示：您可以通过点击表格中的操作按钮来管理评价，或使用批量操作功能同时处理多条评价。</p>
            <p>评价管理支持审核、回复、隐藏和删除操作。您还可以查看评价统计分析以了解整体情况。</p>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              <Badge variant="default">已发布</Badge>
              <span>- 已审核并公开显示</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="secondary">待审核</Badge>
              <span>- 等待管理员审核</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="outline">已隐藏</Badge>
              <span>- 已审核但不公开显示</span>
            </div>
          </div>
        </div>
      </div>

      {/* 对话框组件 */}
      {selectedReview && (
        <ReviewDetailDialog
          open={showReviewDetail}
          onOpenChange={(open) => {
            setShowReviewDetail(open)
            if (!open) {
              // 关闭对话框时可以刷新数据
              loadReviews()
            }
          }}
          review={selectedReview}
          onReviewUpdated={handleReviewUpdated}
        />
      )}

      {stats && (
        <ReviewStatsDialog
          open={showStats}
          onOpenChange={setShowStats}
          stats={stats}
        />
      )}

      <AdvancedFilterDialog
        open={showFilter}
        onOpenChange={setShowFilter}
        onApplyFilter={handleApplyAdvancedFilter}
        initialFilters={currentFilters}
      />
    </div>
  )
}
