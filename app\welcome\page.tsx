"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, ArrowRight, Settings, MapPin, Users, CalendarDays, CreditCard, UserCircle } from "lucide-react"
import { useOnboarding } from "@/contexts/onboarding-context"

export default function WelcomePage() {
  const router = useRouter()
  const { steps, setShowOnboarding } = useOnboarding()

  // 当页面加载时，显示引导
  useEffect(() => {
    setShowOnboarding(true)
  }, [setShowOnboarding])

  return (
    <div className="container max-w-5xl mx-auto py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold tracking-tight">欢迎使用瑜伽工作室管理系统</h1>
        <p className="text-muted-foreground mt-2">
          请按照以下步骤完成系统初始化，开始您的瑜伽工作室管理之旅
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl">系统初始化指南</CardTitle>
            <CardDescription>
              完成这些步骤后，您的系统将准备就绪
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {steps.map((step, index) => (
                <li key={step.id} className="flex items-start gap-3">
                  <div className="h-6 w-6 flex-shrink-0 rounded-full bg-primary/10 text-primary flex items-center justify-center">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium">{step.title}</p>
                    <p className="text-sm text-muted-foreground">{step.description}</p>
                  </div>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button 
              className="w-full" 
              onClick={() => {
                router.push(steps[0].path)
              }}
            >
              开始初始化
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl">系统功能概览</CardTitle>
            <CardDescription>
              瑜伽工作室管理系统提供全方位的管理功能
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="border rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Settings className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">系统设置</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  配置工作室基本信息、员工账号和权限
                </p>
              </div>
              <div className="border rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <MapPin className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">场馆管理</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  管理场馆、教室和设备信息
                </p>
              </div>
              <div className="border rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">会员管理</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  管理会员信息、会员卡和会员标签
                </p>
              </div>
              <div className="border rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <CalendarDays className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">课程管理</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  管理课程类型、排课和预约
                </p>
              </div>
              <div className="border rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <UserCircle className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">教练管理</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  管理教练信息、排班和评价
                </p>
              </div>
              <div className="border rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  <CreditCard className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">订单管理</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  管理订单、退款和支付设置
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-xl">快速入门视频</CardTitle>
          <CardDescription>
            观看以下视频，快速了解系统的使用方法
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
            <p className="text-muted-foreground">视频教程（即将上线）</p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline">查看更多教程</Button>
          <Button variant="outline" onClick={() => router.push("/")}>
            返回首页
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
