import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 获取会员卡课程关联
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
    console.log('获取会员卡课程关联，ID:', id);

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询课程关联
      const [courseAssociations] = await connection.execute(
        `SELECT 
          mca.*,
          ct.name as course_type_name,
          ct.description as course_type_description,
          ct.color as course_type_color
        FROM member_card_course_associations mca
        LEFT JOIN coursetype ct ON mca.course_type_id = ct.id
        WHERE mca.card_type_id = ?
        ORDER BY ct.display_order ASC, ct.name ASC`,
        [parseInt(id)]
      );

      // 查询所有可用的课程类型
      const [allCourseTypes] = await connection.execute(
        `SELECT id, name, description, color, display_order
        FROM coursetype 
        WHERE tenant_id = 2
        ORDER BY display_order ASC, name ASC`
      );

      console.log('获取会员卡课程关联成功');

      return NextResponse.json({
        code: 200,
        data: {
          associations: courseAssociations || [],
          availableCourseTypes: allCourseTypes || []
        },
        msg: '获取会员卡课程关联成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取会员卡课程关联失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取会员卡课程关联失败',
      data: null
    }, { status: 500 });
  }
}

// 更新会员卡课程关联
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
    const data = await req.json();
    console.log('更新会员卡课程关联，ID:', id, '数据:', data);

    const { associations, courseSettings } = data;

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.beginTransaction();

      // 先删除现有关联
      await connection.execute(
        'DELETE FROM member_card_course_associations WHERE card_type_id = ?',
        [parseInt(id)]
      );

      // 插入新关联
      if (associations && Array.isArray(associations)) {
        for (const association of associations) {
          // 获取课程类型信息
          const [courseTypeInfo] = await connection.execute(
            'SELECT name, description FROM coursetype WHERE id = ?',
            [association.course_type_id]
          );

          const courseType = (courseTypeInfo as any[])[0];

          await connection.execute(
            `INSERT INTO member_card_course_associations 
            (card_type_id, course_type_id, tenant_id, is_enabled, consumption_times, course_type_name, course_duration)
            VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              parseInt(id),
              association.course_type_id,
              association.tenant_id || 2,
              association.is_enabled !== false,
              association.consumption_times || 1.0,
              courseType?.name || '',
              association.course_duration || 60
            ]
          );
        }
      }

      // 更新课程设置
      if (courseSettings) {
        // 检查是否存在课程设置记录
        const [existingSettings] = await connection.execute(
          'SELECT id FROM member_card_course_settings WHERE card_type_id = ?',
          [parseInt(id)]
        );

        if ((existingSettings as any[]).length > 0) {
          // 更新现有记录
          const settingsFields = [];
          const settingsValues = [];

          Object.keys(courseSettings).forEach(key => {
            if (key !== 'id' && key !== 'card_type_id' && key !== 'tenant_id' && key !== 'created_at' && key !== 'updated_at') {
              settingsFields.push(`${key} = ?`);
              settingsValues.push(courseSettings[key]);
            }
          });

          if (settingsFields.length > 0) {
            settingsValues.push(parseInt(id));
            await connection.execute(
              `UPDATE member_card_course_settings SET ${settingsFields.join(', ')} WHERE card_type_id = ?`,
              settingsValues
            );
          }
        } else {
          // 创建新记录
          await connection.execute(
            `INSERT INTO member_card_course_settings 
            (card_type_id, tenant_id, consumption_rule, consumption_description, gift_class_count, gift_value_coefficient, all_courses_enabled)
            VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              parseInt(id),
              courseSettings.tenant_id || 2,
              courseSettings.consumption_rule || 'AVERAGE',
              courseSettings.consumption_description || 'Average consumption rule',
              courseSettings.gift_class_count || 0,
              courseSettings.gift_value_coefficient || 1.0,
              courseSettings.all_courses_enabled !== false
            ]
          );
        }
      }

      await connection.commit();

      console.log('更新会员卡课程关联成功');

      return NextResponse.json({
        code: 200,
        data: { id: parseInt(id) },
        msg: '更新会员卡课程关联成功'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  } catch (error: any) {
    console.error('更新会员卡课程关联失败:', error);
    return NextResponse.json({
      code: 500,
      msg: error.message || '更新会员卡课程关联失败',
      data: null
    }, { status: 500 });
  }
}

// 批量更新课程关联
export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
    const data = await req.json();
    console.log('批量更新会员卡课程关联，ID:', id, '数据:', data);

    const { selectedCourseIds, consumptionSettings } = data;

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.beginTransaction();

      // 先删除现有关联
      await connection.execute(
        'DELETE FROM member_card_course_associations WHERE card_type_id = ?',
        [parseInt(id)]
      );

      // 为选中的课程类型创建关联
      if (selectedCourseIds && Array.isArray(selectedCourseIds)) {
        for (const courseTypeId of selectedCourseIds) {
          // 获取课程类型信息
          const [courseTypeInfo] = await connection.execute(
            'SELECT name, description FROM coursetype WHERE id = ?',
            [courseTypeId]
          );

          const courseType = (courseTypeInfo as any[])[0];
          const consumption = consumptionSettings?.[courseTypeId] || { count: 1, amount: 100 };

          await connection.execute(
            `INSERT INTO member_card_course_associations 
            (card_type_id, course_type_id, tenant_id, is_enabled, consumption_times, course_type_name, course_duration)
            VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              parseInt(id),
              courseTypeId,
              2, // 默认租户ID
              true,
              consumption.count || consumption.amount || 1.0,
              courseType?.name || '',
              60 // 默认课程时长
            ]
          );
        }
      }

      await connection.commit();

      console.log('批量更新会员卡课程关联成功');

      return NextResponse.json({
        code: 200,
        data: { id: parseInt(id), count: selectedCourseIds?.length || 0 },
        msg: '批量更新会员卡课程关联成功'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  } catch (error: any) {
    console.error('批量更新会员卡课程关联失败:', error);
    return NextResponse.json({
      code: 500,
      msg: error.message || '批量更新会员卡课程关联失败',
      data: null
    }, { status: 500 });
  }
}
