"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Eye, EyeOff, AlertTriangle, CheckCircle2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface ChangeCardStatusDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  card: {
    id: number | string
    name: string
    color: string
    status: string
  } | null
  onStatusChange: (cardId: number | string, newStatus: string, reason: string) => void
}

export function ChangeCardStatusDialog({
  open,
  onOpenChange,
  card,
  onStatusChange
}: ChangeCardStatusDialogProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [reason, setReason] = useState("")
  
  const isActivating = card?.status === "inactive"
  const actionText = isActivating ? "上架" : "下架"
  
  const handleStatusChange = async () => {
    if (!card) return
    
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 调用父组件的回调函数
      const newStatus = card.status === "active" ? "inactive" : "active"
      onStatusChange(card.id, newStatus, reason)
      
      toast({
        title: `${actionText}成功`,
        description: `${card.name} 会员卡已${actionText}`,
      })
      
      onOpenChange(false)
    } catch (error) {
      toast({
        title: `${actionText}失败`,
        description: `${actionText}会员卡时出现错误，请重试`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleOpenChange = (open: boolean) => {
    if (open) {
      setReason("")
    }
    onOpenChange(open)
  }

  if (!card) return null

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isActivating ? (
              <Eye className="h-5 w-5 text-primary" />
            ) : (
              <EyeOff className="h-5 w-5 text-primary" />
            )}
            {actionText}会员卡
          </DialogTitle>
          <DialogDescription>
            确定要{actionText} <span className="font-medium" style={{ color: card.color }}>{card.name}</span> 会员卡吗？
          </DialogDescription>
        </DialogHeader>

        {!isActivating && (
          <Alert variant="warning">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>注意</AlertTitle>
            <AlertDescription>
              下架后，该会员卡将不再显示在销售页面，已购买的会员不受影响。
            </AlertDescription>
          </Alert>
        )}

        {isActivating && (
          <Alert>
            <CheckCircle2 className="h-4 w-4" />
            <AlertTitle>提示</AlertTitle>
            <AlertDescription>
              上架后，该会员卡将显示在销售页面，客户可以购买。
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-2 py-2">
          <Label htmlFor="status-change-reason">
            {actionText}原因（可选）
          </Label>
          <Textarea
            id="status-change-reason"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder={`请输入${actionText}原因，仅内部可见`}
            className="min-h-[80px]"
          />
        </div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={handleStatusChange} 
            disabled={isSubmitting}
            variant={isActivating ? "default" : "destructive"}
          >
            {isSubmitting ? `${actionText}中...` : `确认${actionText}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
