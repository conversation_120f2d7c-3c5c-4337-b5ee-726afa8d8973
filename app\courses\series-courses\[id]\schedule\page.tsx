"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  ArrowLeft,
  Calendar,
  Clock,
  Edit,
  Plus,
  Trash2
} from "lucide-react"

import { mockSeriesCourses, mockSeriesSchedules } from "@/lib/mock/series-courses"
import { SeriesSchedule, SeriesScheduleStatus } from "@/types/series-courses"

// 表单验证模式
const scheduleFormSchema = z.object({
  sessionNumber: z.string().min(1, "请输入课次"),
  title: z.string().min(1, "请输入课程标题"),
  date: z.string().min(1, "请选择上课日期"),
  startTime: z.string().min(1, "请选择开始时间"),
  endTime: z.string().min(1, "请选择结束时间"),
  venue: z.string().min(1, "请选择场地"),
  instructor: z.string().min(1, "请选择教练"),
  description: z.string().optional(),
  status: z.string().min(1, "请选择状态"),
})

export default function SeriesCourseSchedulePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const courseId = params.id
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedSchedule, setSelectedSchedule] = useState<SeriesSchedule | null>(null)

  // 周期性课程设置状态
  const [isRecurring, setIsRecurring] = useState(false)
  const [endDate, setEndDate] = useState("")
  const [selectedDays, setSelectedDays] = useState<string[]>([])
  const [capacity, setCapacity] = useState(15)

  // 获取课程详情
  const course = mockSeriesCourses.find(c => c.id === courseId)

  // 获取课程排期
  const schedules = mockSeriesSchedules.filter(s => s.seriesCourseId === courseId)

  // 如果课程不存在，显示错误信息
  if (!course) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold tracking-tight ml-2">课程不存在</h1>
        </div>
        <p>未找到ID为 {courseId} 的系列课程。</p>
        <Button onClick={() => router.push("/courses/series-courses")} className="mt-4">
          返回系列课程列表
        </Button>
      </div>
    )
  }

  // 添加排期表单
  const addForm = useForm<z.infer<typeof scheduleFormSchema>>({
    resolver: zodResolver(scheduleFormSchema),
    defaultValues: {
      sessionNumber: (schedules.length + 1).toString(),
      title: "",
      date: "",
      startTime: "",
      endTime: "",
      venue: "",
      instructor: course.instructor,
      description: "",
      status: "scheduled",
    },
  })

  // 编辑排期表单
  const editForm = useForm<z.infer<typeof scheduleFormSchema>>({
    resolver: zodResolver(scheduleFormSchema),
    defaultValues: {
      sessionNumber: "",
      title: "",
      date: "",
      startTime: "",
      endTime: "",
      venue: "",
      instructor: "",
      description: "",
      status: "",
    },
  })

  // 处理添加排期
  const handleAddSchedule = (values: z.infer<typeof scheduleFormSchema>) => {
    console.log("添加排期:", values)
    toast({
      title: "添加排期成功",
      description: `已成功添加第${values.sessionNumber}节课程排期。`,
    })
    setShowAddDialog(false)
    addForm.reset()
  }

  // 处理编辑排期
  const handleEditSchedule = (values: z.infer<typeof scheduleFormSchema>) => {
    console.log("编辑排期:", values)
    toast({
      title: "编辑排期成功",
      description: `已成功更新第${values.sessionNumber}节课程排期。`,
    })
    setShowEditDialog(false)
  }

  // 处理删除排期
  const handleDeleteSchedule = () => {
    if (!selectedSchedule) return

    console.log("删除排期:", selectedSchedule)
    toast({
      title: "删除排期成功",
      description: `已成功删除第${selectedSchedule.sessionNumber}节课程排期。`,
    })
    setShowDeleteDialog(false)
    setSelectedSchedule(null)
  }

  // 打开编辑对话框
  const openEditDialog = (schedule: SeriesSchedule) => {
    setSelectedSchedule(schedule)
    editForm.reset({
      sessionNumber: schedule.sessionNumber.toString(),
      title: schedule.title,
      date: schedule.date,
      startTime: schedule.startTime,
      endTime: schedule.endTime,
      venue: schedule.venue,
      instructor: schedule.instructor,
      description: schedule.description || "",
      status: schedule.status,
    })
    setShowEditDialog(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (schedule: SeriesSchedule) => {
    setSelectedSchedule(schedule)
    setShowDeleteDialog(true)
  }

  // 处理星期选择
  const handleDayToggle = (day: string) => {
    setSelectedDays(prev =>
      prev.includes(day)
        ? prev.filter(d => d !== day)
        : [...prev, day]
    )
  }

  // 获取状态标签样式
  const getStatusBadge = (status: SeriesScheduleStatus) => {
    switch (status) {
      case "scheduled":
        return <Badge variant="outline">待上课</Badge>
      case "completed":
        return <Badge variant="default">已完成</Badge>
      case "cancelled":
        return <Badge variant="destructive">已取消</Badge>
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" onClick={() => router.push(`/courses/series-courses/${courseId}`)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold tracking-tight ml-2">课程排期管理</h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>{course.name}</CardTitle>
          <CardDescription>
            {course.courseType} | {course.instructor} | {course.totalSessions}节课 | {course.startDate} 至 {course.endDate}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-muted-foreground">
                已安排 <span className="font-medium">{schedules.length}</span> 节课，
                共计划 <span className="font-medium">{course.totalSessions}</span> 节课
              </p>
            </div>
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              添加排期
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="border rounded-md overflow-hidden">
        <div className="grid grid-cols-12 bg-muted px-4 py-3 font-medium text-sm">
          <div className="col-span-1">课次</div>
          <div className="col-span-3">课程标题</div>
          <div className="col-span-2">上课日期</div>
          <div className="col-span-2">上课时间</div>
          <div className="col-span-1">场地</div>
          <div className="col-span-1">教练</div>
          <div className="col-span-1">状态</div>
          <div className="col-span-1 text-right">操作</div>
        </div>

        {schedules.length === 0 ? (
          <div className="px-4 py-8 text-center text-muted-foreground">
            暂无课程排期，请点击"添加排期"按钮添加。
          </div>
        ) : (
          <div className="divide-y">
            {schedules.map((schedule) => (
              <div key={schedule.id} className="grid grid-cols-12 px-4 py-3 hover:bg-accent/50">
                <div className="col-span-1 flex items-center">
                  <span>第{schedule.sessionNumber}节</span>
                </div>
                <div className="col-span-3 flex items-center">
                  <span>{schedule.title}</span>
                </div>
                <div className="col-span-2 flex items-center">
                  <span>{schedule.date}</span>
                </div>
                <div className="col-span-2 flex items-center">
                  <span>{schedule.startTime}-{schedule.endTime}</span>
                </div>
                <div className="col-span-1 flex items-center">
                  <span>{schedule.venue}</span>
                </div>
                <div className="col-span-1 flex items-center">
                  <span>{schedule.instructor}</span>
                </div>
                <div className="col-span-1 flex items-center">
                  {getStatusBadge(schedule.status)}
                </div>
                <div className="col-span-1 flex items-center justify-end gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => openEditDialog(schedule)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => openDeleteDialog(schedule)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 添加排期对话框 */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>添加课程排期</DialogTitle>
            <DialogDescription>
              创建新的课程排期，填写以下信息。
            </DialogDescription>
          </DialogHeader>

          <Form {...addForm}>
            <form onSubmit={addForm.handleSubmit(handleAddSchedule)} className="space-y-6">
              {/* 周期性课程设置 */}
              <div className="flex items-center space-x-2 p-4 bg-gray-50 rounded-lg">
                <Switch
                  id="isRecurring"
                  checked={isRecurring}
                  onCheckedChange={setIsRecurring}
                />
                <Label htmlFor="isRecurring" className="text-sm font-medium">设置为周期性课程</Label>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={addForm.control}
                  name="sessionNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>课次</FormLabel>
                      <FormControl>
                        <Input placeholder="输入课次" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>状态</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择状态" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="scheduled">待上课</SelectItem>
                          <SelectItem value="completed">已完成</SelectItem>
                          <SelectItem value="cancelled">已取消</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={addForm.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>课程名称</FormLabel>
                      <FormControl>
                        <Input placeholder="输入课程名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <Label htmlFor="capacity">容量</Label>
                  <Input
                    id="capacity"
                    type="number"
                    value={capacity}
                    onChange={(e) => setCapacity(parseInt(e.target.value) || 15)}
                    placeholder="输入容量"
                    min="1"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={addForm.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>开始日期</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {isRecurring && (
                  <div className="space-y-2">
                    <Label htmlFor="endDate">结束日期</Label>
                    <Input
                      id="endDate"
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      placeholder="选择结束日期"
                    />
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={addForm.control}
                  name="startTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>开始时间</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="endTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>结束时间</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {isRecurring && (
                <div className="space-y-2">
                  <Label>重复日期</Label>
                  <div className="flex flex-wrap gap-2">
                    {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day) => (
                      <div key={day} className="flex items-center space-x-2">
                        <Checkbox
                          id={`day-${day}`}
                          checked={selectedDays.includes(day)}
                          onCheckedChange={() => handleDayToggle(day)}
                        />
                        <Label htmlFor={`day-${day}`} className="text-sm">{day}</Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={addForm.control}
                  name="venue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>场地</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择场地" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="瑜伽室1">瑜伽室1</SelectItem>
                          <SelectItem value="瑜伽室2">瑜伽室2</SelectItem>
                          <SelectItem value="普拉提室">普拉提室</SelectItem>
                          <SelectItem value="多功能厅">多功能厅</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="instructor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>教练</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择教练" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="李教练">李教练</SelectItem>
                          <SelectItem value="王教练">王教练</SelectItem>
                          <SelectItem value="张教练">张教练</SelectItem>
                          <SelectItem value="刘教练">刘教练</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={addForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>课程描述</FormLabel>
                    <FormControl>
                      <Textarea placeholder="输入课程描述（选填）" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowAddDialog(false)}>
                  取消
                </Button>
                <Button type="submit">保存</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 编辑排期对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑课程排期</DialogTitle>
            <DialogDescription>
              编辑系列课程的课次排期。
            </DialogDescription>
          </DialogHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditSchedule)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="sessionNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>课次</FormLabel>
                      <FormControl>
                        <Input placeholder="输入课次" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>状态</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择状态" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="scheduled">待上课</SelectItem>
                          <SelectItem value="completed">已完成</SelectItem>
                          <SelectItem value="cancelled">已取消</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>课程标题</FormLabel>
                    <FormControl>
                      <Input placeholder="输入课程标题" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={editForm.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>上课日期</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="startTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>开始时间</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="endTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>结束时间</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="venue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>场地</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择场地" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="瑜伽室1">瑜伽室1</SelectItem>
                          <SelectItem value="瑜伽室2">瑜伽室2</SelectItem>
                          <SelectItem value="普拉提室">普拉提室</SelectItem>
                          <SelectItem value="多功能厅">多功能厅</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="instructor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>教练</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择教练" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="李教练">李教练</SelectItem>
                          <SelectItem value="王教练">王教练</SelectItem>
                          <SelectItem value="张教练">张教练</SelectItem>
                          <SelectItem value="刘教练">刘教练</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>课程描述</FormLabel>
                    <FormControl>
                      <Textarea placeholder="输入课程描述（选填）" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowEditDialog(false)}>
                  取消
                </Button>
                <Button type="submit">保存</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 删除排期确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这个课程排期吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedSchedule && (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">课次:</span>
                  <span>第{selectedSchedule.sessionNumber}节</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">课程标题:</span>
                  <span>{selectedSchedule.title}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">上课时间:</span>
                  <span>{selectedSchedule.date} {selectedSchedule.startTime}-{selectedSchedule.endTime}</span>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteSchedule}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
