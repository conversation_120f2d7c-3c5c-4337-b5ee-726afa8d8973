"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Calendar, Download, TrendingUp, Users, DollarSign, BookOpen, Award, Bell } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import Link from "next/link"

export default function StatisticsDashboardPage() {
  const [dateRange, setDateRange] = useState("30days")
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isAlertDialogOpen, setIsAlertDialogOpen] = useState(false)
  const [selectedMetric, setSelectedMetric] = useState("revenue")

  // 模拟数据
  const kpiData = {
    revenue: {
      value: "¥458,623",
      change: "+12.3%",
      isPositive: true,
      target: "¥500,000",
      progress: 92,
    },
    members: {
      value: "1,284",
      change: "+8.5%",
      isPositive: true,
      target: "1,500",
      progress: 86,
    },
    courses: {
      value: "245",
      change: "+5.2%",
      isPositive: true,
      target: "300",
      progress: 82,
    },
    coaches: {
      value: "28",
      change: "+3.6%",
      isPositive: true,
      target: "30",
      progress: 93,
    },
  }

  const alerts = [
    { id: 1, type: "warning", message: "会员续费率低于目标 5%", module: "会员", date: "今天" },
    { id: 2, type: "danger", message: "瑜伽教室 A 利用率下降 15%", module: "场地", date: "昨天" },
    { id: 3, type: "info", message: "新增会员增长率超出预期 10%", module: "会员", date: "3天前" },
    { id: 4, type: "success", message: "营销活动 ROI 达到目标的 120%", module: "营销", date: "5天前" },
  ]

  const recommendations = [
    { id: 1, message: "增加周末高峰时段的课程数量，预计可提升收入 8%", module: "课程" },
    { id: 2, message: "对即将到期的会员发起针对性营销，提高续费率", module: "会员" },
    { id: 3, message: "优化教练排班，提高工时利用率", module: "教练" },
    { id: 4, message: "调整低利用率时段的价格策略，吸引更多会员", module: "定价" },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">数据统计仪表盘</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Calendar className="h-4 w-4" />
            <span>选择时间范围</span>
          </Button>
          <Button variant="outline" onClick={() => setIsExportDialogOpen(true)}>
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
          <Button variant="default" className="relative">
            <Bell className="h-4 w-4" />
            <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] text-white">
              4
            </span>
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex flex-1 gap-4">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="thismonth">本月</SelectItem>
              <SelectItem value="lastmonth">上月</SelectItem>
              <SelectItem value="thisyear">今年</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card
          className={`${selectedMetric === "revenue" ? "border-primary" : ""} cursor-pointer hover:border-primary transition-colors`}
          onClick={() => setSelectedMetric("revenue")}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpiData.revenue.value}</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                较上期{" "}
                <span className={kpiData.revenue.isPositive ? "text-green-500" : "text-red-500"}>
                  {kpiData.revenue.change}
                </span>
              </p>
              <Badge variant="outline">目标: {kpiData.revenue.target}</Badge>
            </div>
            <Progress value={kpiData.revenue.progress} className="mt-2 h-1.5" />
          </CardContent>
        </Card>
        <Card
          className={`${selectedMetric === "members" ? "border-primary" : ""} cursor-pointer hover:border-primary transition-colors`}
          onClick={() => setSelectedMetric("members")}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">会员数量</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpiData.members.value}</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                较上期{" "}
                <span className={kpiData.members.isPositive ? "text-green-500" : "text-red-500"}>
                  {kpiData.members.change}
                </span>
              </p>
              <Badge variant="outline">目标: {kpiData.members.target}</Badge>
            </div>
            <Progress value={kpiData.members.progress} className="mt-2 h-1.5" />
          </CardContent>
        </Card>
        <Card
          className={`${selectedMetric === "courses" ? "border-primary" : ""} cursor-pointer hover:border-primary transition-colors`}
          onClick={() => setSelectedMetric("courses")}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">课程数量</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpiData.courses.value}</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                较上期{" "}
                <span className={kpiData.courses.isPositive ? "text-green-500" : "text-red-500"}>
                  {kpiData.courses.change}
                </span>
              </p>
              <Badge variant="outline">目标: {kpiData.courses.target}</Badge>
            </div>
            <Progress value={kpiData.courses.progress} className="mt-2 h-1.5" />
          </CardContent>
        </Card>
        <Card
          className={`${selectedMetric === "coaches" ? "border-primary" : ""} cursor-pointer hover:border-primary transition-colors`}
          onClick={() => setSelectedMetric("coaches")}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">教练数量</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpiData.coaches.value}</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                较上期{" "}
                <span className={kpiData.coaches.isPositive ? "text-green-500" : "text-red-500"}>
                  {kpiData.coaches.change}
                </span>
              </p>
              <Badge variant="outline">目标: {kpiData.coaches.target}</Badge>
            </div>
            <Progress value={kpiData.coaches.progress} className="mt-2 h-1.5" />
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">业务概览</TabsTrigger>
          <TabsTrigger value="trends">趋势分析</TabsTrigger>
          <TabsTrigger value="alerts">异常预警</TabsTrigger>
          <TabsTrigger value="recommendations">优化建议</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>业务指标概览</CardTitle>
                <CardDescription>关键业务指标完成情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span>收入目标</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {kpiData.revenue.value} / {kpiData.revenue.target}
                        </span>
                        <Badge variant={kpiData.revenue.progress >= 90 ? "success" : "default"}>
                          {kpiData.revenue.progress}%
                        </Badge>
                      </div>
                    </div>
                    <Progress value={kpiData.revenue.progress} className="h-2" />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>会员目标</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {kpiData.members.value} / {kpiData.members.target}
                        </span>
                        <Badge variant={kpiData.members.progress >= 90 ? "success" : "default"}>
                          {kpiData.members.progress}%
                        </Badge>
                      </div>
                    </div>
                    <Progress value={kpiData.members.progress} className="h-2" />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <BookOpen className="h-4 w-4 text-muted-foreground" />
                        <span>课程目标</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {kpiData.courses.value} / {kpiData.courses.target}
                        </span>
                        <Badge variant={kpiData.courses.progress >= 90 ? "success" : "default"}>
                          {kpiData.courses.progress}%
                        </Badge>
                      </div>
                    </div>
                    <Progress value={kpiData.courses.progress} className="h-2" />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Award className="h-4 w-4 text-muted-foreground" />
                        <span>教练目标</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {kpiData.coaches.value} / {kpiData.coaches.target}
                        </span>
                        <Badge variant={kpiData.coaches.progress >= 90 ? "success" : "default"}>
                          {kpiData.coaches.progress}%
                        </Badge>
                      </div>
                    </div>
                    <Progress value={kpiData.coaches.progress} className="h-2" />
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" className="ml-auto">
                  设置目标
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>业务模块分析</CardTitle>
                <CardDescription>各业务模块数据分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <Link
                    href="/statistics/members"
                    className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors"
                  >
                    <Users className="h-8 w-8 text-primary mb-2" />
                    <div className="text-sm font-medium">会员分析</div>
                  </Link>
                  <Link
                    href="/statistics/courses"
                    className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors"
                  >
                    <BookOpen className="h-8 w-8 text-primary mb-2" />
                    <div className="text-sm font-medium">课程分析</div>
                  </Link>
                  <Link
                    href="/statistics/revenue"
                    className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors"
                  >
                    <DollarSign className="h-8 w-8 text-primary mb-2" />
                    <div className="text-sm font-medium">收入分析</div>
                  </Link>
                  <Link
                    href="/statistics/coaches"
                    className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors"
                  >
                    <Award className="h-8 w-8 text-primary mb-2" />
                    <div className="text-sm font-medium">教练分析</div>
                  </Link>
                  <Link
                    href="/statistics/marketing"
                    className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors"
                  >
                    <TrendingUp className="h-8 w-8 text-primary mb-2" />
                    <div className="text-sm font-medium">营销分析</div>
                  </Link>
                  <Link
                    href="/statistics/operational"
                    className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors"
                  >
                    <Bell className="h-8 w-8 text-primary mb-2" />
                    <div className="text-sm font-medium">运营分析</div>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>数据洞察</CardTitle>
              <CardDescription>基于当前数据的业务洞察</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-4 p-4 bg-muted rounded-lg">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20">
                    <TrendingUp className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold">收入增长点分析</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      本月收入增长主要来自于高级瑜伽课程（+18%）和会员卡销售（+15%），建议继续加强这两个方向的营销投入。
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4 p-4 bg-muted rounded-lg">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold">会员行为分析</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      数据显示，参加3次以上团课的会员续费率提高了25%，建议为新会员提供更多参与团课的激励措施。
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4 p-4 bg-muted rounded-lg">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20">
                    <Award className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold">教练绩效分析</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      评分最高的5位教练贡献了总收入的42%，建议提供更多激励措施保留这些核心教练，并培养更多高绩效教练。
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看更多洞察
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>关键指标趋势</CardTitle>
              <CardDescription>过去30天关键指标变化趋势</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <div className="h-full w-full rounded-md border bg-muted flex items-center justify-center">
                <p className="text-muted-foreground">关键指标趋势图表</p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Select defaultValue="revenue">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择指标" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="revenue">收入</SelectItem>
                  <SelectItem value="members">会员数</SelectItem>
                  <SelectItem value="courses">课程数</SelectItem>
                  <SelectItem value="coaches">教练数</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm">
                导出数据
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>同比分析</CardTitle>
                <CardDescription>与去年同期数据对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>收入</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">+23.5%</span>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          增长
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "76.5%" }}></div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-primary" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>去年: ¥371,354</span>
                      <span>今年: ¥458,623</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>会员数</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">+15.8%</span>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          增长
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "84.2%" }}></div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-primary" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>去年: 1,108</span>
                      <span>今年: 1,284</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>课程数</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">+8.4%</span>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          增长
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "91.6%" }}></div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-primary" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>去年: 226</span>
                      <span>今年: 245</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>环比分析</CardTitle>
                <CardDescription>与上月数据对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>收入</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">+5.2%</span>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          增长
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "94.8%" }}></div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-primary" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>上月: ¥435,953</span>
                      <span>本月: ¥458,623</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>会员数</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">+2.8%</span>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          增长
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "97.2%" }}></div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-primary" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>上月: 1,249</span>
                      <span>本月: 1,284</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>课程数</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">+1.2%</span>
                        <Badge variant="outline" className="bg-green-50 text-green-700">
                          增长
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "98.8%" }}></div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-primary" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>上月: 242</span>
                      <span>本月: 245</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>数据异常预警</CardTitle>
              <CardDescription>系统检测到的数据异常情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`flex items-start gap-4 p-4 rounded-lg ${
                      alert.type === "warning"
                        ? "bg-yellow-50"
                        : alert.type === "danger"
                          ? "bg-red-50"
                          : alert.type === "info"
                            ? "bg-blue-50"
                            : "bg-green-50"
                    }`}
                  >
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-full ${
                        alert.type === "warning"
                          ? "bg-yellow-100"
                          : alert.type === "danger"
                            ? "bg-red-100"
                            : alert.type === "info"
                              ? "bg-blue-100"
                              : "bg-green-100"
                      }`}
                    >
                      <Bell
                        className={`h-5 w-5 ${
                          alert.type === "warning"
                            ? "text-yellow-600"
                            : alert.type === "danger"
                              ? "text-red-600"
                              : alert.type === "info"
                                ? "text-blue-600"
                                : "text-green-600"
                        }`}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-semibold">{alert.message}</h4>
                        <Badge variant="outline">{alert.module}</Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">{alert.date}</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={() => setIsAlertDialogOpen(true)}
                    >
                      <span className="sr-only">查看详情</span>
                      <span>···</span>
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看所有预警
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>预警设置</CardTitle>
              <CardDescription>自定义数据异常预警规则</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2">
                    <Checkbox id="revenue-alert" defaultChecked />
                    <label htmlFor="revenue-alert" className="text-sm font-medium">
                      收入异常预警
                    </label>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">变化阈值:</span>
                    <Select defaultValue="10">
                      <SelectTrigger className="w-[80px] h-8">
                        <SelectValue placeholder="选择阈值" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5%</SelectItem>
                        <SelectItem value="10">10%</SelectItem>
                        <SelectItem value="15">15%</SelectItem>
                        <SelectItem value="20">20%</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2">
                    <Checkbox id="member-alert" defaultChecked />
                    <label htmlFor="member-alert" className="text-sm font-medium">
                      会员流失预警
                    </label>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">变化阈值:</span>
                    <Select defaultValue="5">
                      <SelectTrigger className="w-[80px] h-8">
                        <SelectValue placeholder="选择阈值" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="3">3%</SelectItem>
                        <SelectItem value="5">5%</SelectItem>
                        <SelectItem value="8">8%</SelectItem>
                        <SelectItem value="10">10%</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2">
                    <Checkbox id="venue-alert" defaultChecked />
                    <label htmlFor="venue-alert" className="text-sm font-medium">
                      场地利用率预警
                    </label>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">变化阈值:</span>
                    <Select defaultValue="15">
                      <SelectTrigger className="w-[80px] h-8">
                        <SelectValue placeholder="选择阈值" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10%</SelectItem>
                        <SelectItem value="15">15%</SelectItem>
                        <SelectItem value="20">20%</SelectItem>
                        <SelectItem value="25">25%</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div className="flex items-center gap-2">
                    <Checkbox id="coach-alert" defaultChecked />
                    <label htmlFor="coach-alert" className="text-sm font-medium">
                      教练评分预警
                    </label>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">变化阈值:</span>
                    <Select defaultValue="0.5">
                      <SelectTrigger className="w-[80px] h-8">
                        <SelectValue placeholder="选择阈值" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0.3">0.3</SelectItem>
                        <SelectItem value="0.5">0.5</SelectItem>
                        <SelectItem value="0.8">0.8</SelectItem>
                        <SelectItem value="1.0">1.0</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                添加新预警
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>业务优化建议</CardTitle>
              <CardDescription>基于数据分析的业务优化建议</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations.map((rec) => (
                  <div key={rec.id} className="flex items-start gap-4 p-4 bg-muted rounded-lg">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20">
                      <TrendingUp className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-semibold">{rec.message}</h4>
                        <Badge variant="outline">{rec.module}</Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="h-8">
                        忽略
                      </Button>
                      <Button size="sm" className="h-8">
                        采纳
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看历史建议
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>自动优化设置</CardTitle>
              <CardDescription>配置系统自动优化规则</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <h4 className="text-sm font-medium">自动调整课程价格</h4>
                    <p className="text-xs text-muted-foreground mt-1">根据预约情况自动调整课程价格，提高低峰期预约率</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">状态:</span>
                    <Select defaultValue="enabled">
                      <SelectTrigger className="w-[100px] h-8">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="enabled">已启用</SelectItem>
                        <SelectItem value="disabled">已禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <h4 className="text-sm font-medium">自动发送会员续费提醒</h4>
                    <p className="text-xs text-muted-foreground mt-1">会员卡到期前自动发送续费提醒，提高续费率</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">状态:</span>
                    <Select defaultValue="enabled">
                      <SelectTrigger className="w-[100px] h-8">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="enabled">已启用</SelectItem>
                        <SelectItem value="disabled">已禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <h4 className="text-sm font-medium">自动优化教练排班</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      根据历史数据自动优化教练排班，提高教练工时利用率
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">状态:</span>
                    <Select defaultValue="disabled">
                      <SelectTrigger className="w-[100px] h-8">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="enabled">已启用</SelectItem>
                        <SelectItem value="disabled">已禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <h4 className="text-sm font-medium">自动推荐个性化课程</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      根据会员历史数据自动推荐个性化课程，提高会员活跃度
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">状态:</span>
                    <Select defaultValue="enabled">
                      <SelectTrigger className="w-[100px] h-8">
                        <SelectValue placeholder="选择状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="enabled">已启用</SelectItem>
                        <SelectItem value="disabled">已禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                添加新规则
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 导出报表对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>导出数据报表</DialogTitle>
            <DialogDescription>选择导出格式和内容</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>报表类型</Label>
              <Select defaultValue="summary">
                <SelectTrigger>
                  <SelectValue placeholder="选择报表类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">汇总报表</SelectItem>
                  <SelectItem value="revenue">收入分析报表</SelectItem>
                  <SelectItem value="members">会员分析报表</SelectItem>
                  <SelectItem value="courses">课程分析报表</SelectItem>
                  <SelectItem value="coaches">教练分析报表</SelectItem>
                  <SelectItem value="all">完整报表（包含所有数据）</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>导出格式</Label>
              <Select defaultValue="excel">
                <SelectTrigger>
                  <SelectValue placeholder="选择导出格式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="csv">CSV (.csv)</SelectItem>
                  <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>时间范围</Label>
              <Select defaultValue={dateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">最近7天</SelectItem>
                  <SelectItem value="30days">最近30天</SelectItem>
                  <SelectItem value="90days">最近90天</SelectItem>
                  <SelectItem value="thismonth">本月</SelectItem>
                  <SelectItem value="lastmonth">上月</SelectItem>
                  <SelectItem value="thisyear">今年</SelectItem>
                  <SelectItem value="custom">自定义</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            <div className="space-y-2">
              <Label>报表选项</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="include-charts" defaultChecked />
                  <label htmlFor="include-charts" className="text-sm">
                    包含图表
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="include-raw-data" defaultChecked />
                  <label htmlFor="include-raw-data" className="text-sm">
                    包含原始数据
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="include-comparison" defaultChecked />
                  <label htmlFor="include-comparison" className="text-sm">
                    包含同比/环比分析
                  </label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="report-name">报表名称</Label>
              <Input id="report-name" defaultValue="数据统计分析报表" />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setIsExportDialogOpen(false)}>导出</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 预警详情对话框 */}
      <Dialog open={isAlertDialogOpen} onOpenChange={setIsAlertDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>预警详情</DialogTitle>
            <DialogDescription>查看预警详细信息和处理建议</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 rounded-lg">
              <h3 className="text-sm font-semibold">会员续费率低于目标 5%</h3>
              <p className="text-sm text-muted-foreground mt-2">
                本月会员续费率为 75%，低于设定目标 80%，差异 5%。这可能导致会员流失增加，影响长期收入。
              </p>
            </div>

            <div>
              <h3 className="text-sm font-semibold mb-2">可能原因分析</h3>
              <ul className="space-y-1 text-sm text-muted-foreground list-disc pl-5">
                <li>会员满意度下降</li>
                <li>竞争对手推出更具吸引力的会员方案</li>
                <li>续费提醒不及时或不到位</li>
                <li>会员价值感知不足</li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold mb-2">处理建议</h3>
              <ul className="space-y-1 text-sm text-muted-foreground list-disc pl-5">
                <li>对即将到期的会员发起针对性营销</li>
                <li>提供续费优惠或增值服务</li>
                <li>调查会员满意度，了解流失原因</li>
                <li>优化会员体验，提高会员黏性</li>
              </ul>
            </div>

            <div className="flex items-center gap-2 p-4 bg-muted rounded-lg">
              <Checkbox id="auto-fix" />
              <label htmlFor="auto-fix" className="text-sm">
                启用自动优化建议，系统将自动执行相关优化措施
              </label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAlertDialogOpen(false)}>
              稍后处理
            </Button>
            <Button onClick={() => setIsAlertDialogOpen(false)}>标记为已处理</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

