"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@/components/date-picker"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Download, Calendar, Clock, RefreshCw } from "lucide-react"

export default function OrderExportPage() {
  const [exportFormat, setExportFormat] = useState("excel")
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  const [selectedFields, setSelectedFields] = useState([
    "orderId",
    "orderDate",
    "memberName",
    "orderType",
    "amount",
    "paymentMethod",
    "status",
  ])
  const [scheduleName, setScheduleName] = useState("")
  const [scheduleFrequency, setScheduleFrequency] = useState("weekly")

  const handleFieldToggle = (field: string) => {
    if (selectedFields.includes(field)) {
      setSelectedFields(selectedFields.filter((f) => f !== field))
    } else {
      setSelectedFields([...selectedFields, field])
    }
  }

  const handleExport = () => {
    alert("导出订单数据")
  }

  const handleScheduleSave = () => {
    alert("保存定时导出任务")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">订单导出</h1>
      </div>

      <Tabs defaultValue="manual" className="space-y-4">
        <TabsList>
          <TabsTrigger value="manual">手动导出</TabsTrigger>
          <TabsTrigger value="scheduled">定时导出</TabsTrigger>
          <TabsTrigger value="history">导出历史</TabsTrigger>
        </TabsList>

        <TabsContent value="manual" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>导出设置</CardTitle>
              <CardDescription>配置导出的订单数据范围和格式</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="export-format">导出格式</Label>
                  <Select value={exportFormat} onValueChange={setExportFormat}>
                    <SelectTrigger id="export-format">
                      <SelectValue placeholder="选择导出格式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                      <SelectItem value="csv">CSV (.csv)</SelectItem>
                      <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>订单状态</Label>
                  <Select defaultValue="all">
                    <SelectTrigger>
                      <SelectValue placeholder="选择订单状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="paid">已支付</SelectItem>
                      <SelectItem value="pending">待支付</SelectItem>
                      <SelectItem value="refunded">已退款</SelectItem>
                      <SelectItem value="cancelled">已取消</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>日期范围</Label>
                <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                  <DatePicker placeholder="开始日期" selected={startDate} onSelect={setStartDate} />
                  <span className="hidden sm:block">至</span>
                  <DatePicker placeholder="结束日期" selected={endDate} onSelect={setEndDate} />
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label>选择导出字段</Label>
                <div className="grid grid-cols-2 gap-2 sm:grid-cols-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-orderId"
                      checked={selectedFields.includes("orderId")}
                      onCheckedChange={() => handleFieldToggle("orderId")}
                    />
                    <label htmlFor="field-orderId" className="text-sm">
                      订单编号
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-orderDate"
                      checked={selectedFields.includes("orderDate")}
                      onCheckedChange={() => handleFieldToggle("orderDate")}
                    />
                    <label htmlFor="field-orderDate" className="text-sm">
                      下单时间
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-memberName"
                      checked={selectedFields.includes("memberName")}
                      onCheckedChange={() => handleFieldToggle("memberName")}
                    />
                    <label htmlFor="field-memberName" className="text-sm">
                      会员姓名
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-memberPhone"
                      checked={selectedFields.includes("memberPhone")}
                      onCheckedChange={() => handleFieldToggle("memberPhone")}
                    />
                    <label htmlFor="field-memberPhone" className="text-sm">
                      会员电话
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-orderType"
                      checked={selectedFields.includes("orderType")}
                      onCheckedChange={() => handleFieldToggle("orderType")}
                    />
                    <label htmlFor="field-orderType" className="text-sm">
                      订单类型
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-product"
                      checked={selectedFields.includes("product")}
                      onCheckedChange={() => handleFieldToggle("product")}
                    />
                    <label htmlFor="field-product" className="text-sm">
                      商品/服务
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-amount"
                      checked={selectedFields.includes("amount")}
                      onCheckedChange={() => handleFieldToggle("amount")}
                    />
                    <label htmlFor="field-amount" className="text-sm">
                      金额
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-paymentMethod"
                      checked={selectedFields.includes("paymentMethod")}
                      onCheckedChange={() => handleFieldToggle("paymentMethod")}
                    />
                    <label htmlFor="field-paymentMethod" className="text-sm">
                      支付方式
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-status"
                      checked={selectedFields.includes("status")}
                      onCheckedChange={() => handleFieldToggle("status")}
                    />
                    <label htmlFor="field-status" className="text-sm">
                      订单状态
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-venue"
                      checked={selectedFields.includes("venue")}
                      onCheckedChange={() => handleFieldToggle("venue")}
                    />
                    <label htmlFor="field-venue" className="text-sm">
                      门店
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-staff"
                      checked={selectedFields.includes("staff")}
                      onCheckedChange={() => handleFieldToggle("staff")}
                    />
                    <label htmlFor="field-staff" className="text-sm">
                      操作员
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="field-notes"
                      checked={selectedFields.includes("notes")}
                      onCheckedChange={() => handleFieldToggle("notes")}
                    />
                    <label htmlFor="field-notes" className="text-sm">
                      备注
                    </label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                导出数据
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>定时导出设置</CardTitle>
              <CardDescription>设置定期自动导出订单数据</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="schedule-name">任务名称</Label>
                <Input
                  id="schedule-name"
                  placeholder="例如：每周订单报表"
                  value={scheduleName}
                  onChange={(e) => setScheduleName(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="schedule-frequency">导出频率</Label>
                  <Select value={scheduleFrequency} onValueChange={setScheduleFrequency}>
                    <SelectTrigger id="schedule-frequency">
                      <SelectValue placeholder="选择导出频率" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">每天</SelectItem>
                      <SelectItem value="weekly">每周</SelectItem>
                      <SelectItem value="monthly">每月</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="export-format">导出格式</Label>
                  <Select defaultValue="excel">
                    <SelectTrigger id="export-format">
                      <SelectValue placeholder="选择导出格式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                      <SelectItem value="csv">CSV (.csv)</SelectItem>
                      <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>接收邮箱</Label>
                <Input placeholder="输入接收导出文件的邮箱地址" type="email" />
              </div>

              <Separator />

              <div className="space-y-2">
                <Label>选择导出字段</Label>
                <div className="grid grid-cols-2 gap-2 sm:grid-cols-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="schedule-field-orderId" defaultChecked />
                    <label htmlFor="schedule-field-orderId" className="text-sm">
                      订单编号
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="schedule-field-orderDate" defaultChecked />
                    <label htmlFor="schedule-field-orderDate" className="text-sm">
                      下单时间
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="schedule-field-memberName" defaultChecked />
                    <label htmlFor="schedule-field-memberName" className="text-sm">
                      会员姓名
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="schedule-field-orderType" defaultChecked />
                    <label htmlFor="schedule-field-orderType" className="text-sm">
                      订单类型
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="schedule-field-amount" defaultChecked />
                    <label htmlFor="schedule-field-amount" className="text-sm">
                      金额
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="schedule-field-status" defaultChecked />
                    <label htmlFor="schedule-field-status" className="text-sm">
                      订单状态
                    </label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleScheduleSave}>
                <Calendar className="mr-2 h-4 w-4" />
                保存定时任务
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>现有定时任务</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between border-b pb-4">
                  <div>
                    <h3 className="font-medium">每周订单报表</h3>
                    <p className="text-sm text-muted-foreground">每周一 08:00 自动导出</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Clock className="mr-2 h-4 w-4" />
                      编辑
                    </Button>
                    <Button variant="outline" size="sm" className="text-red-500">
                      删除
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between border-b pb-4">
                  <div>
                    <h3 className="font-medium">月度财务报表</h3>
                    <p className="text-sm text-muted-foreground">每月1日 00:00 自动导出</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Clock className="mr-2 h-4 w-4" />
                      编辑
                    </Button>
                    <Button variant="outline" size="sm" className="text-red-500">
                      删除
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>导出历史记录</CardTitle>
              <CardDescription>查看最近的导出任务记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between border-b pb-4">
                  <div>
                    <h3 className="font-medium">订单数据导出</h3>
                    <p className="text-sm text-muted-foreground">2025-03-28 15:30:22</p>
                    <p className="text-sm">Excel格式 | 共125条记录</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <RefreshCw className="mr-2 h-4 w-4" />
                      重新导出
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      下载
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between border-b pb-4">
                  <div>
                    <h3 className="font-medium">每周订单报表</h3>
                    <p className="text-sm text-muted-foreground">2025-03-25 08:00:05</p>
                    <p className="text-sm">Excel格式 | 共87条记录</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <RefreshCw className="mr-2 h-4 w-4" />
                      重新���出
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      下载
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between border-b pb-4">
                  <div>
                    <h3 className="font-medium">月度财务报表</h3>
                    <p className="text-sm text-muted-foreground">2025-03-01 00:00:12</p>
                    <p className="text-sm">Excel格式 | 共342条记录</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <RefreshCw className="mr-2 h-4 w-4" />
                      重新导出
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      下载
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

