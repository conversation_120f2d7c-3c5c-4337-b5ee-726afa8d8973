"use client"

import { DialogTrigger } from "@/components/ui/dialog"
import { CheckedState } from "@radix-ui/react-checkbox"

import { useState, useEffect } from "react"
import {
  Download,
  Filter,
  MoreHorizontal,
  Pencil,
  RefreshCw,
  Search,
  Shield,
  Trash2,
  Upload,
  UserPlus,
  Key,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { systemApi, UserInfo, UserListParams } from "@/lib/api/system"

// 用户操作日志接口定义
interface UserLog {
  id: number;
  userId: string;
  action: string;
  ip: string;
  time: string;
  status: 'success' | 'failed';
}

export function UserManagement() {
  const [users, setUsers] = useState<UserInfo[]>([])
  const [total, setTotal] = useState(0)
  const [searchParams, setSearchParams] = useState<UserListParams>({
    page: 1,
    pageSize: 10,
    keyword: '',
    status: undefined
  })
  const [open, setOpen] = useState(false)
  const [editOpen, setEditOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<UserInfo | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [viewMode, setViewMode] = useState("table")
  const [showInactive, setShowInactive] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState<UserInfo | null>(null)
  const [userLogs, setUserLogs] = useState<UserLog[]>([]) // 用户日志，初始为空数组
  const { toast } = useToast()
  const [isSearching, setIsSearching] = useState(false)
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout>()

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setIsLoading(true)
      setIsSearching(!!searchQuery)
      
      console.log('发起获取用户列表请求，参数:', {
        ...searchParams,
        status: showInactive ? undefined : 'active'
      })
      
      // 检查认证状态
      const token = localStorage.getItem('token') || sessionStorage.getItem('token')
      if (!token) {
        console.error('获取用户列表失败: 未找到认证令牌')
        toast({
          title: "获取用户列表失败",
          description: "用户未登录或会话已过期",
          variant: "destructive",
        })
        setUsers([])
        setTotal(0)
        return
      }
      
      console.log('开始API请求，认证状态:', { 
        tokenLength: token.length,
        tokenPrefix: token.substring(0, 10) + '...',
        tokenType: localStorage.getItem('token_type') || sessionStorage.getItem('token_type') || 'Bearer'
      })
      
      // 判断是否使用模拟数据（仅用于测试）
      const useMockData = false
      
      if (useMockData) {
        console.log('使用模拟数据')
        
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 模拟用户数据
        const mockUsers = Array.from({ length: 10 }, (_, i) => ({
          id: `${i + 1}`,
          username: `user${i + 1}`,
          name: `用户${i + 1}`,
          nickname: `昵称${i + 1}`,
          avatar: '',
          email: `user${i + 1}@example.com`,
          phone: `1380013800${i}`,
          role: i === 0 ? 'admin' : (i < 3 ? 'manager' : 'user'),
          status: i % 5 === 0 ? 'disabled' : 'active',
          lastLoginTime: new Date().toISOString(),
          lastLogin: new Date().toISOString(),
          loginCount: Math.floor(Math.random() * 100),
          department: `门店${Math.floor(i / 3) + 1}`,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString(),
        }))
        
        setUsers(mockUsers)
        setTotal(50)
      } else {
        // 真实API调用
        const response = await systemApi.getUsers({
          ...searchParams,
          status: showInactive ? undefined : 'active'
        })
        
        console.log('获取用户列表返回:', response)
        
        if (response?.code === 0 && response?.data) {
          // 根据API返回的实际数据结构进行解析
          const { total, items } = response.data;
          
          // 打印完整数据结构以便调试
          console.log('完整的API响应数据结构:', JSON.stringify(response));
          console.log('数据样本 - 第一条用户记录:', items && items.length > 0 ? JSON.stringify(items[0]) : '无数据');
          
          // 处理用户数据，确保每个字段都有值
          const processedUsers = items ? items.map((user: any) => ({
            id: user.id || '',
            username: user.username || '',
            nickname: user.nickname || user.name || '',
            email: user.email || '',
            phone: user.phone || '',
            role: user.role_name || user.role || '',
            status: user.status || 'active',
            department: user.branch_name || user.department || '',
            avatar: user.avatar || '',
            lastLogin: user.last_login || user.lastLogin || user.lastLoginTime || '',
            loginCount: user.loginCount || user.login_count || 0,
            createTime: user.createTime || user.create_time || '',
            updateTime: user.updateTime || user.update_time || ''
          })) : [];
          
          console.log('处理后的用户数据:', processedUsers.length > 0 ? processedUsers[0] : '无数据');
          
          // 使用处理后的数据
          setUsers(processedUsers || []);
          setTotal(total || 0);
        } else {
          console.error('获取用户列表失败: 无效的响应数据', response)
          toast({
            title: "获取用户列表失败",
            description: response?.message || "接口返回的数据格式不正确",
            variant: "destructive",
          })
          setUsers([])
          setTotal(0)
        }
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
      toast({
        title: "获取用户列表失败",
        description: "请稍后重试",
        variant: "destructive",
      })
      setUsers([])
      setTotal(0)
    } finally {
      setIsLoading(false)
      setIsSearching(false)
    }
  }
  
  // 获取用户日志
  const fetchUserLogs = async (userId: string) => {
    try {
      // 调用API获取用户日志
      // 示例: const response = await axiosInstance.get(`/api/system/users/${userId}/logs`);
      // 目前没有实现，暂时返回空数组
      setUserLogs([]);
    } catch (error) {
      console.error('获取用户日志失败:', error);
      setUserLogs([]);
    }
  }

  useEffect(() => {
    console.log('参数变化，触发获取用户列表:', {
      searchParams,
      showInactive,
      searchQuery
    });
    fetchUsers();
  }, [searchParams.page, searchParams.pageSize, searchParams.keyword, showInactive]);
  
  // 当选择用户查看详情时，获取该用户的日志
  useEffect(() => {
    if (selectedUser?.id && editOpen) {
      fetchUserLogs(selectedUser.id);
    }
  }, [selectedUser, editOpen])

  const handleRefresh = () => {
    fetchUsers()
  }

  const handleEditUser = async (values: any) => {
    try {
      if (!selectedUser?.id) return
      const response = await systemApi.updateUser(selectedUser.id, values)
      
      if (response && response.code === 0) {
        toast({
          title: "更新成功",
          description: "用户信息已更新",
        })
        setEditOpen(false)
        fetchUsers()
      } else {
        toast({
          title: "更新失败",
          description: response?.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('更新用户失败:', error)
      toast({
        title: "更新失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  const handleResetPassword = async (userId: string) => {
    try {
      const response = await systemApi.resetUserPassword(userId, "123456") // 默认密码
      
      if (response && response.code === 0) {
        toast({
          title: "重置密码成功",
          description: "新密码已发送给用户",
        })
      } else {
        toast({
          title: "重置密码失败",
          description: response?.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('重置密码失败:', error)
      toast({
        title: "重置密码失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  const handleDeleteUser = async (userId: string | undefined) => {
    if (!userId) {
      toast({
        title: "删除失败",
        description: "用户ID不能为空",
        variant: "destructive",
      })
      return
    }
    
    try {
      const response = await systemApi.deleteUser(userId)
      
      if (response && response.code === 0) {
        toast({
          title: "删除成功",
          description: "用户已删除",
        })
        setDeleteDialogOpen(false)
        fetchUsers()
      } else {
        toast({
          title: "删除失败",
          description: response?.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('删除用户失败:', error)
      toast({
        title: "删除失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  const handleStatusChange = async (userId: string | undefined, currentStatus: string) => {
    if (!userId) {
      toast({
        title: "操作失败",
        description: "用户ID不能为空",
        variant: "destructive",
      })
      return
    }
    
    try {
      const newStatus = currentStatus === 'active' ? 'disabled' : 'active'
      const response = await systemApi.updateUserStatus(userId, newStatus)
      
      if (response && response.code === 0) {
        toast({
          title: "状态更新成功",
          description: `用户已${newStatus === 'active' ? '启用' : '禁用'}`,
        })
        fetchUsers()
      } else {
        toast({
          title: "状态更新失败",
          description: response?.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('修改状态失败:', error)
      toast({
        title: "状态更新失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  const confirmDelete = (user: UserInfo) => {
    setUserToDelete(user)
    setDeleteDialogOpen(true)
  }

  const handleShowInactiveChange = (checked: CheckedState) => {
    setShowInactive(checked === true)
  }

  // 处理搜索输入
  const handleSearch = (value: string) => {
    setSearchQuery(value)
    
    // 清除之前的定时器
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }
    
    // 设置新的定时器，延迟500ms执行搜索
    const timeout = setTimeout(() => {
      setSearchParams(prev => ({
        ...prev,
        page: 1, // 搜索时重置到第一页
        keyword: value
      }))
    }, 500)
    
    setSearchTimeout(timeout)
  }

  // 处理分页
  const handlePageChange = (type: 'prev' | 'next') => {
    setSearchParams(prev => ({
      ...prev,
      page: type === 'prev' ? Math.max(1, prev.page - 1) : prev.page + 1
    }))
  }

  // 处理每页显示数量变化
  const handlePageSizeChange = (value: string) => {
    const newPageSize = parseInt(value)
    if (isNaN(newPageSize)) return
    
    setSearchParams(prev => ({
      ...prev,
      page: 1, // 改变每页数量时重置到第一页
      pageSize: newPageSize
    }))
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>用户管理</CardTitle>
            <CardDescription>管理系统用户账号和权限</CardDescription>
          </div>
          <div className="flex gap-2">
            <Dialog open={open} onOpenChange={setOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  添加用户
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>添加新用户</DialogTitle>
                  <DialogDescription>创建新的系统用户账号并分配角色权限</DialogDescription>
                </DialogHeader>
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="basic">基本信息</TabsTrigger>
                    <TabsTrigger value="advanced">高级设置</TabsTrigger>
                  </TabsList>
                  <TabsContent value="basic" className="space-y-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">姓名</Label>
                        <Input id="name" placeholder="输入用户姓名" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">邮箱</Label>
                        <Input id="email" type="email" placeholder="输入用户邮箱" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="password">密码</Label>
                        <Input id="password" type="password" placeholder="输入初始密码" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="confirm-password">确认密码</Label>
                        <Input id="confirm-password" type="password" placeholder="再次输入密码" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="role">角色</Label>
                        <Select>
                          <SelectTrigger id="role">
                            <SelectValue placeholder="选择角色" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="super-admin">超级管理员</SelectItem>
                            <SelectItem value="venue-admin">场馆管理员</SelectItem>
                            <SelectItem value="course-admin">课程管理员</SelectItem>
                            <SelectItem value="member-admin">会员管理员</SelectItem>
                            <SelectItem value="reception">前台接待</SelectItem>
                            <SelectItem value="finance">财务人员</SelectItem>
                            <SelectItem value="coach">教练</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="department">所属门店</Label>
                        <Select>
                          <SelectTrigger id="department">
                            <SelectValue placeholder="选择门店" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="headquarters">总部</SelectItem>
                            <SelectItem value="beijing">北京分馆</SelectItem>
                            <SelectItem value="shanghai">上海分馆</SelectItem>
                            <SelectItem value="guangzhou">广州分馆</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">手机号码</Label>
                      <Input id="phone" placeholder="输入手机号码" />
                    </div>
                  </TabsContent>
                  <TabsContent value="advanced" className="space-y-4 py-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="force-reset">首次登录强制修改密码</Label>
                          <p className="text-sm text-muted-foreground">用户首次登录时必须修改初始密码</p>
                        </div>
                        <Switch id="force-reset" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="two-factor">启用双因素认证</Label>
                          <p className="text-sm text-muted-foreground">要求用户使用双因素认证登录</p>
                        </div>
                        <Switch id="two-factor" />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="ip-restriction">IP限制</Label>
                          <p className="text-sm text-muted-foreground">限制用户登录的IP地址</p>
                        </div>
                        <Switch id="ip-restriction" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="allowed-ips">允许的IP地址</Label>
                        <Input id="allowed-ips" placeholder="例如：*********** 或 ***********/24" />
                        <p className="text-xs text-muted-foreground">多个IP请用逗号分隔，留空表示不限制</p>
                      </div>

                      <Separator className="my-4" />

                      <div className="space-y-2">
                        <Label>访问时间限制</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="start-time" className="text-sm">
                              开始时间
                            </Label>
                            <Input id="start-time" type="time" defaultValue="09:00" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="end-time" className="text-sm">
                              结束时间
                            </Label>
                            <Input id="end-time" type="time" defaultValue="18:00" />
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 mt-2">
                          <Checkbox id="no-time-limit" />
                          <Label htmlFor="no-time-limit" className="text-sm">
                            不限制访问时间
                          </Label>
                        </div>
                      </div>

                      <Separator className="my-4" />

                      <div className="space-y-2">
                        <Label>账号有效期</Label>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="valid-from" className="text-sm">
                              开始日期
                            </Label>
                            <Input id="valid-from" type="date" />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="valid-to" className="text-sm">
                              结束日期
                            </Label>
                            <Input id="valid-to" type="date" />
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 mt-2">
                          <Checkbox id="no-expiry" defaultChecked />
                          <Label htmlFor="no-expiry" className="text-sm">
                            永不过期
                          </Label>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setOpen(false)}>
                    取消
                  </Button>
                  <Button
                    onClick={() => {
                      setOpen(false)
                      toast({
                        title: "用户创建成功",
                        description: "新用户已成功添加到系统",
                      })
                    }}
                  >
                    创建用户
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              {isLoading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>筛选选项</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div className="p-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={showInactive}
                      onCheckedChange={handleShowInactiveChange}
                      id="show-inactive"
                    />
                    <Label htmlFor="show-inactive">显示非活跃用户</Label>
                  </div>
                </div>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Download className="mr-2 h-4 w-4" />
                  导出用户列表
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Upload className="mr-2 h-4 w-4" />
                  导入用户
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-4">
            <div className="relative flex-1 mr-4">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索用户名、邮箱或角色..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
              />
              {isSearching && (
                <div className="absolute right-2 top-2.5">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                className={viewMode === "table" ? "bg-muted" : ""}
                onClick={() => setViewMode("table")}
              >
                <FileText className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={viewMode === "grid" ? "bg-muted" : ""}
                onClick={() => setViewMode("grid")}
              >
                <div className="grid grid-cols-2 gap-0.5">
                  <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
                  <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
                  <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
                  <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
                </div>
              </Button>
            </div>
          </div>

          {viewMode === "table" ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>用户名</TableHead>
                    <TableHead>邮箱</TableHead>
                    <TableHead>角色</TableHead>
                    <TableHead>门店</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>最后登录</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={user.avatar} alt={user.username || ''} />
                            <AvatarFallback>{(user.username || '')[0]}</AvatarFallback>
                          </Avatar>
                          <span className="font-medium">{user.username || user.nickname || ''}</span>
                        </div>
                      </TableCell>
                      <TableCell>{user.email || ''}</TableCell>
                      <TableCell>{user.role || ''}</TableCell>
                      <TableCell>{user.department || ''}</TableCell>
                      <TableCell>
                        <Badge variant={user.status === "active" ? "default" : "secondary"}>
                          {user.status === "active" ? "活跃" : "不活跃"}
                        </Badge>
                      </TableCell>
                      <TableCell>{user.lastLogin || ''}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => {
                              setSelectedUser(user)
                              setEditOpen(true)
                            }}>
                              <Pencil className="mr-2 h-4 w-4" />
                              编辑用户
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleResetPassword(user.id)}>
                              <Key className="mr-2 h-4 w-4" />
                              重置密码
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleStatusChange(user.id, user.status)}>
                              {user.status === "active" ? (
                                <>
                                  <XCircle className="mr-2 h-4 w-4" />
                                  停用账号
                                </>
                              ) : (
                                <>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  激活账号
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                // 打开用户日志对话框
                              }}
                            >
                              <Clock className="mr-2 h-4 w-4" />
                              查看操作日志
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600" onClick={() => confirmDelete(user)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除用户
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {users.map((user) => (
                <Card key={user.id} className="overflow-hidden">
                  <CardHeader className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={user.avatar} alt={user.username || ''} />
                          <AvatarFallback>{(user.username || '')[0]}</AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-base">{user.username || user.nickname || ''}</CardTitle>
                          <CardDescription className="text-xs">{user.email || ''}</CardDescription>
                        </div>
                      </div>
                      <Badge variant={user.status === "active" ? "default" : "secondary"}>
                        {user.status === "active" ? "活跃" : "不活跃"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex flex-col">
                        <span className="text-muted-foreground">角色</span>
                        <span>{user.role || ''}</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground">门店</span>
                        <span>{user.department || ''}</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground">最后登录</span>
                        <span>{user.lastLogin || ''}</span>
                      </div>
                      <div className="flex flex-col">
                        <span className="text-muted-foreground">登录次数</span>
                        <span>{user.loginCount || 0}</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="p-4 pt-0 flex justify-between">
                    <Button variant="outline" size="sm" onClick={() => {
                      setSelectedUser(user)
                      setEditOpen(true)
                    }}>
                      <Pencil className="mr-2 h-4 w-4" />
                      编辑
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                          更多
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleResetPassword(user.id)}>
                          <Key className="mr-2 h-4 w-4" />
                          重置密码
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleStatusChange(user.id, user.status)}>
                          {user.status === "active" ? (
                            <>
                              <XCircle className="mr-2 h-4 w-4" />
                              停用账号
                            </>
                          ) : (
                            <>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              激活账号
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600" onClick={() => confirmDelete(user)}>
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除用户
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              共 {total} 条记录，每页显示
            </span>
            <Select
              value={String(searchParams.pageSize)}
              onValueChange={handlePageSizeChange}
            >
              <SelectTrigger className="w-[80px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-muted-foreground">条</span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={searchParams.page <= 1 || isLoading}
              onClick={() => handlePageChange('prev')}
            >
              上一页
            </Button>
            <div className="flex items-center gap-1">
              <span className="text-sm font-medium">{searchParams.page}</span>
              <span className="text-sm text-muted-foreground">/</span>
              <span className="text-sm text-muted-foreground">
                {Math.max(1, Math.ceil(total / searchParams.pageSize))}
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              disabled={searchParams.page >= Math.ceil(total / searchParams.pageSize) || isLoading}
              onClick={() => handlePageChange('next')}
            >
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      <Dialog open={editOpen} onOpenChange={setEditOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑用户</DialogTitle>
            <DialogDescription>修改用户信息和权限设置</DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">基本信息</TabsTrigger>
                <TabsTrigger value="permissions">权限设置</TabsTrigger>
                <TabsTrigger value="logs">操作日志</TabsTrigger>
              </TabsList>
              <TabsContent value="basic" className="space-y-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-name">姓名</Label>
                    <Input id="edit-name" defaultValue={selectedUser.name} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-email">邮箱</Label>
                    <Input id="edit-email" type="email" defaultValue={selectedUser.email} />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-role">角色</Label>
                    <Select
                      defaultValue={
                        selectedUser.role === "超级管理员"
                          ? "super-admin"
                          : selectedUser.role === "场馆管理员"
                            ? "venue-admin"
                            : selectedUser.role === "课程管理员"
                              ? "course-admin"
                              : selectedUser.role === "会员管理员"
                                ? "member-admin"
                                : selectedUser.role === "前台接待"
                                  ? "reception"
                                  : selectedUser.role === "财务人员"
                                    ? "finance"
                                    : "coach"
                      }
                    >
                      <SelectTrigger id="edit-role">
                        <SelectValue placeholder="选择角色" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="super-admin">超级管理员</SelectItem>
                        <SelectItem value="venue-admin">场馆管理员</SelectItem>
                        <SelectItem value="course-admin">课程管理员</SelectItem>
                        <SelectItem value="member-admin">会员管理员</SelectItem>
                        <SelectItem value="reception">前台接待</SelectItem>
                        <SelectItem value="finance">财务人员</SelectItem>
                        <SelectItem value="coach">教练</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-department">所属门店</Label>
                    <Select
                      defaultValue={
                        selectedUser.department === "总部"
                          ? "headquarters"
                          : selectedUser.department === "北京分馆"
                            ? "beijing"
                            : selectedUser.department === "上海分馆"
                              ? "shanghai"
                              : "guangzhou"
                      }
                    >
                      <SelectTrigger id="edit-department">
                        <SelectValue placeholder="选择门店" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="headquarters">总部</SelectItem>
                        <SelectItem value="beijing">北京分馆</SelectItem>
                        <SelectItem value="shanghai">上海分馆</SelectItem>
                        <SelectItem value="guangzhou">广州分馆</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-phone">手机号码</Label>
                  <Input id="edit-phone" defaultValue={selectedUser.phone} />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="edit-status">账号状态</Label>
                    <p className="text-sm text-muted-foreground">设置用户账号的活跃状态</p>
                  </div>
                  <Switch
                    id="edit-status"
                    checked={selectedUser.status === "active"}
                    onCheckedChange={(checked) => {
                      // 更新状态
                    }}
                  />
                </div>
              </TabsContent>
              <TabsContent value="permissions" className="space-y-4 py-4">
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">模块权限</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="perm-dashboard" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        工作台
                      </Label>
                      <Select defaultValue="read">
                        <SelectTrigger className="w-[120px]">
                          <SelectValue placeholder="选择权限" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">无权限</SelectItem>
                          <SelectItem value="read">只读</SelectItem>
                          <SelectItem value="write">读写</SelectItem>
                          <SelectItem value="admin">管理</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="perm-members" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        会员管理
                      </Label>
                      <Select defaultValue="write">
                        <SelectTrigger className="w-[120px]">
                          <SelectValue placeholder="选择权限" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">无权限</SelectItem>
                          <SelectItem value="read">只读</SelectItem>
                          <SelectItem value="write">读写</SelectItem>
                          <SelectItem value="admin">管理</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="perm-courses" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        课程管理
                      </Label>
                      <Select defaultValue="read">
                        <SelectTrigger className="w-[120px]">
                          <SelectValue placeholder="选择权限" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">无权限</SelectItem>
                          <SelectItem value="read">只读</SelectItem>
                          <SelectItem value="write">读写</SelectItem>
                          <SelectItem value="admin">管理</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="perm-coaches" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        教练管理
                      </Label>
                      <Select defaultValue="read">
                        <SelectTrigger className="w-[120px]">
                          <SelectValue placeholder="选择权限" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">无权限</SelectItem>
                          <SelectItem value="read">只读</SelectItem>
                          <SelectItem value="write">读写</SelectItem>
                          <SelectItem value="admin">管理</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="perm-venues" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        场馆管理
                      </Label>
                      <Select defaultValue="none">
                        <SelectTrigger className="w-[120px]">
                          <SelectValue placeholder="选择权限" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">无权限</SelectItem>
                          <SelectItem value="read">只读</SelectItem>
                          <SelectItem value="write">读写</SelectItem>
                          <SelectItem value="admin">管理</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="perm-orders" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        订单管理
                      </Label>
                      <Select defaultValue="read">
                        <SelectTrigger className="w-[120px]">
                          <SelectValue placeholder="选择权限" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">无权限</SelectItem>
                          <SelectItem value="read">只读</SelectItem>
                          <SelectItem value="write">读写</SelectItem>
                          <SelectItem value="admin">管理</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="perm-statistics" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        统计分析
                      </Label>
                      <Select defaultValue="read">
                        <SelectTrigger className="w-[120px]">
                          <SelectValue placeholder="选择权限" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">无权限</SelectItem>
                          <SelectItem value="read">只读</SelectItem>
                          <SelectItem value="write">读写</SelectItem>
                          <SelectItem value="admin">管理</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="perm-settings" className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        系统设置
                      </Label>
                      <Select defaultValue="none">
                        <SelectTrigger className="w-[120px]">
                          <SelectValue placeholder="选择权限" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">无权限</SelectItem>
                          <SelectItem value="read">只读</SelectItem>
                          <SelectItem value="write">读写</SelectItem>
                          <SelectItem value="admin">管理</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Separator className="my-4" />

                  <h3 className="text-sm font-medium">特殊权限</h3>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="perm-export" />
                      <Label htmlFor="perm-export">允许导出数据</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="perm-delete" />
                      <Label htmlFor="perm-delete">允许删除数据</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="perm-financial" />
                      <Label htmlFor="perm-financial">查看财务数据</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="perm-sensitive" />
                      <Label htmlFor="perm-sensitive">查看敏感信息</Label>
                    </div>
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="logs" className="space-y-4 py-4">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>操作</TableHead>
                        <TableHead>IP地址</TableHead>
                        <TableHead>时间</TableHead>
                        <TableHead>状态</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {userLogs.map((log: UserLog) => (
                        <TableRow key={log.id}>
                          <TableCell>{log.action}</TableCell>
                          <TableCell>{log.ip}</TableCell>
                          <TableCell>{log.time}</TableCell>
                          <TableCell>
                            <Badge variant={log.status === 'success' ? 'default' : 'destructive'}>
                              {log.status === 'success' ? '成功' : '失败'}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
                {userLogs.length === 0 && (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">暂无操作日志记录</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditOpen(false)}>
              取消
            </Button>
            <Button
              onClick={() => {
                setEditOpen(false)
                toast({
                  title: "用户更新成功",
                  description: "用户信息已成功更新",
                })
              }}
            >
              保存更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除用户</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除用户 "{userToDelete?.name}" 吗？此操作无法撤销，用户的所有数据将被永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700"
              onClick={() => handleDeleteUser(userToDelete?.id)}
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

