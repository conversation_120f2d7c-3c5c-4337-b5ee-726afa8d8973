import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga',
  charset: 'utf8mb4'
};

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;

  try {
    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询原始会员卡类型
      const [originalRows] = await connection.execute(
        'SELECT * FROM member_card_types WHERE id = ?',
        [parseInt(id)]
      );

      if ((originalRows as any[]).length === 0) {
        return NextResponse.json({
          code: 404,
          msg: '会员卡类型不存在',
          data: null
        }, { status: 404 });
      }

      const original = (originalRows as any[])[0];

      // 创建复制的数据
      const copyData = {
        ...original,
        name: `${original.name} - 副本`,
        status: '已下架', // 复制的卡类型默认为下架状态
        total_sold: 0,
        active_cards: 0,
        display_order: 0
      };

      // 删除不需要复制的字段
      delete copyData.id;
      delete copyData.created_at;
      delete copyData.updated_at;

      // 插入复制的会员卡类型
      const [result] = await connection.execute(
        `INSERT INTO member_card_types (
          tenant_id, name, description, price, original_price, validity_days, 
          usage_limit, card_category, card_type, status, total_sold, active_cards, 
          display_order, color, is_trial_card, is_gift_card
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          copyData.tenant_id,
          copyData.name,
          copyData.description,
          copyData.price,
          copyData.original_price,
          copyData.validity_days,
          copyData.usage_limit,
          copyData.card_category,
          copyData.card_type,
          copyData.status,
          copyData.total_sold,
          copyData.active_cards,
          copyData.display_order,
          copyData.color,
          copyData.is_trial_card,
          copyData.is_gift_card
        ]
      );

      const insertId = (result as any).insertId;

      // 查询新创建的会员卡类型
      const [newCardRows] = await connection.execute(
        'SELECT * FROM member_card_types WHERE id = ?',
        [insertId]
      );

      return NextResponse.json({
        code: 0,
        msg: '会员卡类型复制成功',
        data: (newCardRows as any[])[0]
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('复制会员卡类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '复制会员卡类型失败',
      data: null
    }, { status: 500 });
  }
}
