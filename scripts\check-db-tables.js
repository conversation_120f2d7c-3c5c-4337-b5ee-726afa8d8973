// 检查数据库表结构
const mysql = require('mysql2/promise');

async function checkTables() {
  console.log('检查数据库表结构...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    // 1. 检查所有表
    console.log('\n1. 检查所有表:');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('数据库中的表:');
    tables.forEach((table, index) => {
      console.log(`  ${index + 1}. ${Object.values(table)[0]}`);
    });

    // 2. 检查Coach表结构
    console.log('\n2. 检查Coach表:');
    try {
      const [coachColumns] = await connection.execute('DESCRIBE Coach');
      console.log('Coach表结构:');
      coachColumns.forEach(col => {
        console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? col.Key : ''}`);
      });
    } catch (error) {
      console.log('Coach表不存在');
    }

    // 3. 检查Venue表结构
    console.log('\n3. 检查Venue表:');
    try {
      const [venueColumns] = await connection.execute('DESCRIBE Venue');
      console.log('Venue表结构:');
      venueColumns.forEach(col => {
        console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? col.Key : ''}`);
      });
    } catch (error) {
      console.log('Venue表不存在');
    }

    // 4. 检查Course表结构
    console.log('\n4. 检查Course表:');
    try {
      const [courseColumns] = await connection.execute('DESCRIBE Course');
      console.log('Course表结构:');
      courseColumns.forEach(col => {
        console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? col.Key : ''}`);
      });
    } catch (error) {
      console.log('Course表不存在');
    }

    await connection.end();
    console.log('\n✓ 数据库检查完成');

  } catch (error) {
    console.error('数据库检查失败:', error.message);
  }
}

checkTables();
