"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, ChevronRight, Settings, Calendar, Clock, Users, AlertTriangle } from "lucide-react"

// 课程类型定义
const courseTypes = [
  { id: "group", name: "团课", color: "#4285F4", description: "多人参与的团体课程" },
  { id: "small", name: "小班课", color: "#34A853", description: "小班制精品课程" },
  { id: "premium", name: "精品课", color: "#FBBC05", description: "高端精品课程" },
  { id: "private", name: "私教课", color: "#EA4335", description: "一对一私人教练课程" },
  { id: "training", name: "教培课", color: "#9C27B0", description: "教练培训课程" },
]

interface CourseTypeRules {
  enabled: boolean
  // 预约设置
  advanceBookingDays?: number
  bookingDeadlineMinutes?: number
  maxBookingsPerDay?: number
  maxBookingsPerWeek?: number
  allowSelfBooking?: boolean
  allowStaffBooking?: boolean
  // 签到设置
  checkinStartMinutes?: number
  checkinEndMinutes?: number
  allowSelfCheckin?: boolean
  // 取消设置
  cancelDeadlineMinutes?: number
  allowSelfCancel?: boolean
  // 候补设置
  waitlistEnabled?: boolean
  maxWaitlistSize?: number
  waitlistStopMinutes?: number
}

interface CourseTypeBookingRulesProps {
  onChange: () => void
}

export function CourseTypeBookingRules({ onChange }: CourseTypeBookingRulesProps) {
  const [activeTab, setActiveTab] = useState("group")
  const [rules, setRules] = useState<Record<string, CourseTypeRules>>({
    group: { enabled: false },
    small: { enabled: false },
    premium: { enabled: true, advanceBookingDays: 14, maxBookingsPerDay: 2 },
    private: { enabled: true, advanceBookingDays: 30, bookingDeadlineMinutes: 240 },
    training: { enabled: false },
  })

  const [collapsedSections, setCollapsedSections] = useState<Record<string, Record<string, boolean>>>({})

  const toggleSection = (courseType: string, section: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [courseType]: {
        ...prev[courseType],
        [section]: !prev[courseType]?.[section]
      }
    }))
  }

  const updateRule = (courseType: string, field: keyof CourseTypeRules, value: any) => {
    setRules(prev => ({
      ...prev,
      [courseType]: {
        ...prev[courseType],
        [field]: value
      }
    }))
    onChange()
  }

  const toggleCourseTypeRule = (courseType: string) => {
    updateRule(courseType, 'enabled', !rules[courseType]?.enabled)
  }

  const renderRuleSection = (courseType: string, currentRules: CourseTypeRules) => {
    if (!currentRules.enabled) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>该课程类型使用通用规则</p>
          <p className="text-sm">启用自定义规则以覆盖通用设置</p>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {/* 预约设置 */}
        <Collapsible 
          open={!collapsedSections[courseType]?.booking} 
          onOpenChange={() => toggleSection(courseType, 'booking')}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-primary" />
              <h4 className="text-sm font-medium">预约设置</h4>
            </div>
            {collapsedSections[courseType]?.booking ? (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor={`${courseType}-advance-days`}>提前预约天数</Label>
                <Input
                  id={`${courseType}-advance-days`}
                  type="number"
                  value={currentRules.advanceBookingDays || ''}
                  onChange={(e) => updateRule(courseType, 'advanceBookingDays', parseInt(e.target.value) || 0)}
                  placeholder="使用通用设置"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={`${courseType}-deadline`}>预约截止时间（分钟）</Label>
                <Input
                  id={`${courseType}-deadline`}
                  type="number"
                  value={currentRules.bookingDeadlineMinutes || ''}
                  onChange={(e) => updateRule(courseType, 'bookingDeadlineMinutes', parseInt(e.target.value) || 0)}
                  placeholder="使用通用设置"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor={`${courseType}-max-day`}>每日最大预约数</Label>
                <Input
                  id={`${courseType}-max-day`}
                  type="number"
                  value={currentRules.maxBookingsPerDay || ''}
                  onChange={(e) => updateRule(courseType, 'maxBookingsPerDay', parseInt(e.target.value) || 0)}
                  placeholder="使用通用设置"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={`${courseType}-max-week`}>每周最大预约数</Label>
                <Input
                  id={`${courseType}-max-week`}
                  type="number"
                  value={currentRules.maxBookingsPerWeek || ''}
                  onChange={(e) => updateRule(courseType, 'maxBookingsPerWeek', parseInt(e.target.value) || 0)}
                  placeholder="使用通用设置"
                />
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* 签到设置 */}
        <Collapsible 
          open={!collapsedSections[courseType]?.checkin} 
          onOpenChange={() => toggleSection(courseType, 'checkin')}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2 text-primary" />
              <h4 className="text-sm font-medium">签到设置</h4>
            </div>
            {collapsedSections[courseType]?.checkin ? (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor={`${courseType}-checkin-start`}>签到开始时间（分钟）</Label>
                <Input
                  id={`${courseType}-checkin-start`}
                  type="number"
                  value={currentRules.checkinStartMinutes || ''}
                  onChange={(e) => updateRule(courseType, 'checkinStartMinutes', parseInt(e.target.value) || 0)}
                  placeholder="使用通用设置"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={`${courseType}-checkin-end`}>签到结束时间（分钟）</Label>
                <Input
                  id={`${courseType}-checkin-end`}
                  type="number"
                  value={currentRules.checkinEndMinutes || ''}
                  onChange={(e) => updateRule(courseType, 'checkinEndMinutes', parseInt(e.target.value) || 0)}
                  placeholder="使用通用设置"
                />
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* 取消设置 */}
        <Collapsible 
          open={!collapsedSections[courseType]?.cancel} 
          onOpenChange={() => toggleSection(courseType, 'cancel')}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2 text-primary" />
              <h4 className="text-sm font-medium">取消设置</h4>
            </div>
            {collapsedSections[courseType]?.cancel ? (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-3">
            <div className="space-y-2">
              <Label htmlFor={`${courseType}-cancel-deadline`}>取消截止时间（分钟）</Label>
              <Input
                id={`${courseType}-cancel-deadline`}
                type="number"
                value={currentRules.cancelDeadlineMinutes || ''}
                onChange={(e) => updateRule(courseType, 'cancelDeadlineMinutes', parseInt(e.target.value) || 0)}
                placeholder="使用通用设置"
              />
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* 候补设置 */}
        <Collapsible 
          open={!collapsedSections[courseType]?.waitlist} 
          onOpenChange={() => toggleSection(courseType, 'waitlist')}
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2 text-primary" />
              <h4 className="text-sm font-medium">候补设置</h4>
            </div>
            {collapsedSections[courseType]?.waitlist ? (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-3 pt-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor={`${courseType}-waitlist-size`}>最大候补人数</Label>
                <Input
                  id={`${courseType}-waitlist-size`}
                  type="number"
                  value={currentRules.maxWaitlistSize || ''}
                  onChange={(e) => updateRule(courseType, 'maxWaitlistSize', parseInt(e.target.value) || 0)}
                  placeholder="使用通用设置"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={`${courseType}-waitlist-stop`}>候补停止时间（分钟）</Label>
                <Input
                  id={`${courseType}-waitlist-stop`}
                  type="number"
                  value={currentRules.waitlistStopMinutes || ''}
                  onChange={(e) => updateRule(courseType, 'waitlistStopMinutes', parseInt(e.target.value) || 0)}
                  placeholder="使用通用设置"
                />
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>课程类型规则</CardTitle>
        <CardDescription>
          为不同课程类型设置特定的预约规则，这些规则将覆盖通用规则
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-5 w-full">
            {courseTypes.map((type) => (
              <TabsTrigger key={type.id} value={type.id} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-2" 
                  style={{ backgroundColor: type.color }}
                />
                {type.name}
                {rules[type.id]?.enabled && (
                  <Badge variant="secondary" className="ml-1 text-xs">自定义</Badge>
                )}
              </TabsTrigger>
            ))}
          </TabsList>
          
          {courseTypes.map((type) => (
            <TabsContent key={type.id} value={type.id} className="mt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <div 
                        className="w-4 h-4 rounded-full mr-2" 
                        style={{ backgroundColor: type.color }}
                      />
                      {type.name}
                    </h3>
                    <p className="text-sm text-muted-foreground">{type.description}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Label htmlFor={`${type.id}-enabled`}>启用自定义规则</Label>
                    <Switch
                      id={`${type.id}-enabled`}
                      checked={rules[type.id]?.enabled || false}
                      onCheckedChange={() => toggleCourseTypeRule(type.id)}
                    />
                  </div>
                </div>
                
                {renderRuleSection(type.id, rules[type.id] || { enabled: false })}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  )
}
