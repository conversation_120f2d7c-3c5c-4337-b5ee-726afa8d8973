import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 获取预约列表
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get('tenantId');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const keyword = searchParams.get('keyword');
    const status = searchParams.get('status');
    const courseId = searchParams.get('courseId');
    const memberId = searchParams.get('memberId');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const sortBy = searchParams.get('sortBy') || 'newest';

    console.log('获取预约列表，参数:', { tenantId, page, pageSize, keyword, status, courseId, memberId, dateFrom, dateTo, sortBy });

    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID',
        data: null
      }, { status: 400 });
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 构建查询条件
      let whereConditions = ['b.tenant_id = ?'];
      let queryParams: any[] = [parseInt(tenantId)];

      if (keyword) {
        whereConditions.push('(m.name LIKE ? OR m.phone LIKE ? OR c.title LIKE ? OR b.booking_no LIKE ?)');
        const keywordPattern = `%${keyword}%`;
        queryParams.push(keywordPattern, keywordPattern, keywordPattern, keywordPattern);
      }

      if (status && status !== 'all') {
        whereConditions.push('b.status = ?');
        queryParams.push(status);
      }

      if (courseId) {
        whereConditions.push('b.course_id = ?');
        queryParams.push(parseInt(courseId));
      }

      if (memberId) {
        whereConditions.push('b.member_id = ?');
        queryParams.push(parseInt(memberId));
      }

      if (dateFrom) {
        whereConditions.push('DATE(b.booking_time) >= ?');
        queryParams.push(dateFrom);
      }

      if (dateTo) {
        whereConditions.push('DATE(b.booking_time) <= ?');
        queryParams.push(dateTo);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 构建排序条件
      let orderBy = 'ORDER BY b.booking_time DESC';
      switch (sortBy) {
        case 'newest':
          orderBy = 'ORDER BY b.booking_time DESC';
          break;
        case 'oldest':
          orderBy = 'ORDER BY b.booking_time ASC';
          break;
        case 'status':
          orderBy = 'ORDER BY b.status ASC, b.booking_time DESC';
          break;
        case 'member':
          orderBy = 'ORDER BY m.name ASC, b.booking_time DESC';
          break;
        case 'course':
          orderBy = 'ORDER BY c.title ASC, b.booking_time DESC';
          break;
      }

      // 查询总数
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM bookings b
        LEFT JOIN members m ON b.member_id = m.id
        LEFT JOIN course c ON b.course_id = c.id
        ${whereClause}
      `;
      const [countResult] = await connection.execute(countQuery, queryParams);
      const total = (countResult as any[])[0].total;

      // 查询预约列表
      const offset = (page - 1) * pageSize;
      const listQuery = `
        SELECT 
          b.*,
          m.name as member_name,
          m.phone as member_phone,
          m.member_no,
          c.title as course_name,
          c.duration as course_duration,
          c.price as course_price,
          mct.name as card_type_name,
          mc.card_no
        FROM bookings b
        LEFT JOIN members m ON b.member_id = m.id
        LEFT JOIN course c ON b.course_id = c.id
        LEFT JOIN member_cards mc ON b.member_card_id = mc.id
        LEFT JOIN member_card_types mct ON mc.card_type_id = mct.id
        ${whereClause}
        ${orderBy}
        LIMIT ? OFFSET ?
      `;
      
      const listParams = [...queryParams, pageSize, offset];
      const [bookings] = await connection.execute(listQuery, listParams);

      console.log('查询预约成功，总数:', total, '当前页:', (bookings as any[]).length);

      return NextResponse.json({
        code: 200,
        data: {
          list: bookings,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        },
        msg: '获取预约列表成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取预约列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取预约列表失败',
      data: null
    }, { status: 500 });
  }
}

// 创建预约
export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    console.log('创建预约，数据:', data);

    const {
      tenant_id,
      member_id,
      course_id,
      member_card_id,
      booking_time,
      notes
    } = data;

    // 验证必填字段
    if (!tenant_id || !member_id || !course_id || !booking_time) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段：租户ID、会员ID、课程ID、预约时间',
        data: null
      }, { status: 400 });
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.beginTransaction();

      // 检查会员是否存在
      const [memberCheck] = await connection.execute(
        'SELECT id, status FROM members WHERE id = ? AND tenant_id = ?',
        [member_id, tenant_id]
      );

      if ((memberCheck as any[]).length === 0) {
        throw new Error('会员不存在');
      }

      if ((memberCheck as any[])[0].status !== 'active') {
        throw new Error('会员状态异常，无法预约');
      }

      // 检查课程是否存在
      const [courseCheck] = await connection.execute(
        'SELECT id, status, capacity FROM course WHERE id = ? AND tenant_id = ?',
        [course_id, tenant_id]
      );

      if ((courseCheck as any[]).length === 0) {
        throw new Error('课程不存在');
      }

      if ((courseCheck as any[])[0].status !== 'active') {
        throw new Error('课程已停用，无法预约');
      }

      // 检查会员卡（如果提供）
      if (member_card_id) {
        const [cardCheck] = await connection.execute(
          'SELECT id, status, remaining_times, remaining_amount FROM member_cards WHERE id = ? AND member_id = ?',
          [member_card_id, member_id]
        );

        if ((cardCheck as any[]).length === 0) {
          throw new Error('会员卡不存在');
        }

        if ((cardCheck as any[])[0].status !== 'active') {
          throw new Error('会员卡状态异常，无法使用');
        }
      }

      // 生成预约编号
      const bookingNo = `B${tenant_id.toString().padStart(3, '0')}${Date.now().toString().slice(-6)}`;

      // 插入预约记录
      const insertQuery = `
        INSERT INTO bookings (
          tenant_id, member_id, course_id, member_card_id, booking_no, 
          booking_time, notes, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'booked')
      `;

      const [result] = await connection.execute(insertQuery, [
        tenant_id, member_id, course_id, member_card_id, bookingNo, booking_time, notes
      ]);

      const bookingId = (result as any).insertId;

      await connection.commit();

      console.log('创建预约成功，ID:', bookingId);

      return NextResponse.json({
        code: 200,
        data: {
          id: bookingId,
          booking_no: bookingNo,
          status: 'booked'
        },
        msg: '创建预约成功'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  } catch (error: any) {
    console.error('创建预约失败:', error);
    return NextResponse.json({
      code: 500,
      msg: error.message || '创建预约失败',
      data: null
    }, { status: 500 });
  }
}
