"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface SalesTrendChartProps {
  cardId: number | string
  className?: string
  title?: string
  showControls?: boolean
}

export function SalesTrendChart({ 
  cardId, 
  className,
  title = "销售趋势",
  showControls = true
}: SalesTrendChartProps) {
  const [timeRange, setTimeRange] = useState("month")
  const [chartType, setChartType] = useState("bar")
  
  // 模拟销售数据
  const monthlySalesData = [
    { month: "1月", sales: 5, revenue: 18000 },
    { month: "2月", sales: 8, revenue: 28800 },
    { month: "3月", sales: 12, revenue: 43200 },
    { month: "4月", sales: 7, revenue: 25200 },
    { month: "5月", sales: 10, revenue: 36000 },
    { month: "6月", sales: 15, revenue: 54000 },
  ]
  
  const weeklySalesData = [
    { week: "第1周", sales: 3, revenue: 10800 },
    { week: "第2周", sales: 2, revenue: 7200 },
    { week: "第3周", sales: 4, revenue: 14400 },
    { week: "第4周", sales: 5, revenue: 18000 },
  ]
  
  const dailySalesData = [
    { day: "周一", sales: 1, revenue: 3600 },
    { day: "周二", sales: 0, revenue: 0 },
    { day: "周三", sales: 2, revenue: 7200 },
    { day: "周四", sales: 1, revenue: 3600 },
    { day: "周五", sales: 3, revenue: 10800 },
    { day: "周六", sales: 4, revenue: 14400 },
    { day: "周日", sales: 2, revenue: 7200 },
  ]
  
  // 根据选择的时间范围获取数据
  const getChartData = () => {
    switch (timeRange) {
      case "day":
        return dailySalesData
      case "week":
        return weeklySalesData
      case "month":
      default:
        return monthlySalesData
    }
  }
  
  const data = getChartData()
  
  // 获取标签（X轴）
  const getLabels = () => {
    if (timeRange === "day") return data.map(item => item.day)
    if (timeRange === "week") return data.map(item => item.week)
    return data.map(item => item.month)
  }
  
  // 获取最大值，用于计算柱状图高度
  const maxSales = Math.max(...data.map(item => item.sales))
  
  // 渲染柱状图
  const renderBarChart = () => {
    return (
      <div className="flex h-[200px] items-end justify-between gap-2 pt-6">
        {data.map((item, index) => {
          const height = (item.sales / maxSales) * 150
          return (
            <div key={index} className="flex flex-col items-center">
              <div 
                className="w-12 rounded-t-sm bg-primary transition-all duration-300 hover:opacity-80"
                style={{ height: `${height}px` }}
              >
                <div className="flex h-full items-center justify-center text-xs font-medium text-primary-foreground">
                  {item.sales > 0 && item.sales}
                </div>
              </div>
              <div className="mt-2 text-xs">{getLabels()[index]}</div>
              <div className="text-xs font-medium">¥{(item.revenue / 1000).toFixed(1)}k</div>
            </div>
          )
        })}
      </div>
    )
  }
  
  // 渲染折线图
  const renderLineChart = () => {
    // 计算点的位置
    const points = data.map((item, index) => {
      const x = (index / (data.length - 1)) * 100
      const y = 100 - (item.sales / maxSales) * 100
      return { x, y, sales: item.sales, revenue: item.revenue }
    })
    
    // 生成SVG路径
    const pathData = points.map((point, index) => 
      `${index === 0 ? "M" : "L"} ${point.x} ${point.y}`
    ).join(" ")
    
    return (
      <div className="h-[200px] w-full pt-6">
        <div className="relative h-[150px] w-full">
          {/* 背景网格线 */}
          <div className="absolute inset-0 grid grid-cols-6 grid-rows-4">
            {Array.from({ length: 24 }).map((_, i) => (
              <div key={i} className="border-b border-r border-gray-100"></div>
            ))}
          </div>
          
          {/* 折线图 */}
          <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <path
              d={pathData}
              fill="none"
              stroke="hsl(var(--primary))"
              strokeWidth="2"
              vectorEffect="non-scaling-stroke"
            />
          </svg>
          
          {/* 数据点 */}
          {points.map((point, index) => (
            <div
              key={index}
              className="absolute h-3 w-3 -translate-x-1.5 -translate-y-1.5 rounded-full bg-primary"
              style={{ left: `${point.x}%`, top: `${point.y}%` }}
              title={`销量: ${point.sales}, 收入: ¥${point.revenue}`}
            ></div>
          ))}
        </div>
        
        {/* X轴标签 */}
        <div className="mt-2 flex justify-between">
          {getLabels().map((label, index) => (
            <div key={index} className="text-xs">
              {label}
              <div className="text-xs font-medium">¥{(data[index].revenue / 1000).toFixed(1)}k</div>
            </div>
          ))}
        </div>
      </div>
    )
  }
  
  return (
    <Card className={className}>
      <CardHeader className={cn("flex-row items-center justify-between pb-2", 
        !showControls && "pb-0")}>
        <CardTitle className="text-base">{title}</CardTitle>
        {showControls && (
          <div className="flex items-center gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="h-8 w-[120px]">
                <SelectValue placeholder="选择时间范围" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">最近7天</SelectItem>
                <SelectItem value="week">最近4周</SelectItem>
                <SelectItem value="month">最近6个月</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="flex rounded-md border">
              <button
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-l-md",
                  chartType === "bar" ? "bg-primary text-primary-foreground" : "bg-transparent"
                )}
                onClick={() => setChartType("bar")}
              >
                <BarChart className="h-4 w-4" />
              </button>
              <button
                className={cn(
                  "flex h-8 w-8 items-center justify-center rounded-r-md",
                  chartType === "line" ? "bg-primary text-primary-foreground" : "bg-transparent"
                )}
                onClick={() => setChartType("line")}
              >
                <LineChart className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent>
        {chartType === "bar" ? renderBarChart() : renderLineChart()}
      </CardContent>
    </Card>
  )
}
