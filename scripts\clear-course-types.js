// 清空课程类型数据以测试空状态
async function clearCourseTypes() {
  console.log('开始清空课程类型数据...');
  
  try {
    // 1. 首先获取所有课程类型
    console.log('\n1. 获取所有课程类型:');
    const listResponse = await fetch('http://localhost:3001/api/course-types?tenantId=1');
    const listResult = await listResponse.json();
    
    if (listResult.code !== 200) {
      throw new Error(`获取课程类型列表失败: ${listResult.msg}`);
    }
    
    const courseTypes = listResult.data.list;
    console.log(`✓ 找到 ${courseTypes.length} 个课程类型`);
    
    if (courseTypes.length === 0) {
      console.log('数据库中没有课程类型数据，无需清空');
      return;
    }
    
    // 2. 逐个删除课程类型
    console.log('\n2. 开始删除课程类型:');
    let deletedCount = 0;
    let skippedCount = 0;
    
    for (const type of courseTypes) {
      console.log(`删除课程类型: ${type.name} (ID: ${type.id})`);
      
      const deleteResponse = await fetch(`http://localhost:3001/api/course-types/${type.id}`, {
        method: 'DELETE'
      });
      
      const deleteResult = await deleteResponse.json();
      
      if (deleteResult.code === 200) {
        console.log(`  ✓ 删除成功: ${type.name}`);
        deletedCount++;
      } else {
        console.log(`  ✗ 删除失败: ${type.name} - ${deleteResult.msg}`);
        skippedCount++;
      }
    }
    
    // 3. 验证删除结果
    console.log('\n3. 验证删除结果:');
    const finalResponse = await fetch('http://localhost:3001/api/course-types?tenantId=1');
    const finalResult = await finalResponse.json();
    
    if (finalResult.code === 200) {
      console.log(`✓ 当前剩余课程类型: ${finalResult.data.list.length} 个`);
    }
    
    console.log('\n✓ 清空操作完成!');
    console.log(`删除成功: ${deletedCount} 个`);
    console.log(`跳过: ${skippedCount} 个`);
    
    console.log('\n现在可以测试空状态下的添加课程类型功能了！');
    console.log('请访问: http://localhost:3001/courses/types');
    
  } catch (error) {
    console.error('清空失败:', error.message);
  }
}

clearCourseTypes();
