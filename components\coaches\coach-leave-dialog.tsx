"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alogFooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { CalendarIcon, Clock } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface CoachLeaveDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  coach: {
    id: string
    name: string
    avatar: string
    status: string
  } | null
  onLeaveSubmit?: (coachId: string, startDate: Date, endDate: Date, leaveType: string, reason: string) => void
}

export function CoachLeaveDialog({ open, onOpenChange, coach, onLeaveSubmit }: CoachLeaveDialogProps) {
  const { toast } = useToast()
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [leaveType, setLeaveType] = useState("personal")
  const [reason, setReason] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 重置表单
  const resetForm = () => {
    setStartDate(undefined)
    setEndDate(undefined)
    setLeaveType("personal")
    setReason("")
  }

  // 处理对话框关闭
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetForm()
    }
    onOpenChange(open)
  }

  const handleSubmit = async () => {
    if (!coach) return

    if (!startDate || !endDate) {
      toast({
        title: "请选择日期",
        description: "请假开始和结束日期不能为空",
        variant: "destructive",
      })
      return
    }

    if (startDate > endDate) {
      toast({
        title: "日期错误",
        description: "结束日期不能早于开始日期",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 如果提供了回调函数，则调用它
      if (onLeaveSubmit) {
        await onLeaveSubmit(coach.id, startDate, endDate, leaveType, reason)
      } else {
        // 否则模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 更新教练状态的逻辑将在父组件中处理
        toast({
          title: "请假申请已提交",
          description: `${coach.name}的请假申请已成功提交，请假期间：${format(startDate, 'yyyy-MM-dd')} 至 ${format(endDate, 'yyyy-MM-dd')}`,
        })
      }
      
      handleOpenChange(false)
    } catch (error) {
      toast({
        title: "操作失败",
        description: "请假申请提交失败，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!coach) return null

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>教练请假</DialogTitle>
          <DialogDescription>
            设置教练请假期间，教练将无法被排课和预约
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex items-center gap-4">
              <Avatar className="h-10 w-10">
                <AvatarImage src={coach.avatar} alt={coach.name} />
                <AvatarFallback>{coach.name[0]}</AvatarFallback>
              </Avatar>
              <div>
                <h4 className="font-medium">{coach.name}</h4>
                <p className="text-sm text-muted-foreground">教练ID: {coach.id}</p>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="leave-type">请假类型</Label>
            <Select value={leaveType} onValueChange={setLeaveType}>
              <SelectTrigger id="leave-type">
                <SelectValue placeholder="选择请假类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="personal">个人原因</SelectItem>
                <SelectItem value="business">工作原因</SelectItem>
                <SelectItem value="health">健康原因</SelectItem>
                <SelectItem value="travel">出行原因</SelectItem>
                <SelectItem value="other">其他原因</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start-date">开始日期</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="start-date"
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, 'yyyy-MM-dd') : <span>选择日期</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                    locale={zhCN}
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="end-date">结束日期</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="end-date"
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, 'yyyy-MM-dd') : <span>选择日期</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                    locale={zhCN}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="reason">请假原因</Label>
            <Textarea
              id="reason"
              placeholder="请输入详细请假原因..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
            />
          </div>
          
          <div className="rounded-md bg-muted p-3 text-sm">
            <p className="font-medium">请假说明：</p>
            <ul className="list-disc pl-5 pt-2 text-muted-foreground">
              <li>请假期间教练将无法被排课</li>
              <li>请假期间已排课程将需要重新安排</li>
              <li>请假结束后将自动恢复在职状态</li>
            </ul>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => handleOpenChange(false)}>取消</Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "提交中..." : "提交请假"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
