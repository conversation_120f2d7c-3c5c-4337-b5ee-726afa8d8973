"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Calendar, Download, TrendingUp, Users, Share2, DollarSign, Target } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export default function MarketingStatisticsPage() {
  const [dateRange, setDateRange] = useState("30days")
  const [campaignType, setCampaignType] = useState("all")
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">营销效果分析</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Calendar className="h-4 w-4" />
            <span>选择时间范围</span>
          </Button>
          <Button variant="outline" onClick={() => setIsExportDialogOpen(true)}>
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex flex-1 gap-4">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="thismonth">本月</SelectItem>
              <SelectItem value="lastmonth">上月</SelectItem>
              <SelectItem value="thisyear">今年</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>

          <Select value={campaignType} onValueChange={setCampaignType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="活动类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部活动</SelectItem>
              <SelectItem value="discount">折扣活动</SelectItem>
              <SelectItem value="coupon">优惠券</SelectItem>
              <SelectItem value="referral">会员推荐</SelectItem>
              <SelectItem value="social">社交媒体</SelectItem>
              <SelectItem value="event">线下活动</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总营销支出</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥28,560</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-green-500">+12.5%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">新增会员</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">142</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-green-500">+18.3%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">营销ROI</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3.8x</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-green-500">+0.5x</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">获客成本</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥201</div>
            <p className="text-xs text-muted-foreground">
              较上期 <span className="text-red-500">+5.2%</span>
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="campaigns" className="space-y-4">
        <TabsList>
          <TabsTrigger value="campaigns">营销活动分析</TabsTrigger>
          <TabsTrigger value="coupons">优惠券分析</TabsTrigger>
          <TabsTrigger value="referrals">会员推荐分析</TabsTrigger>
          <TabsTrigger value="social">社交媒体分析</TabsTrigger>
          <TabsTrigger value="acquisition">获客成本分析</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>营销活动效果对比</CardTitle>
              <CardDescription>各营销活动的投入产出比对比</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                {/* 这里是营销活动效果对比图表 */}
                <div className="space-y-8">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-green-500"></div>
                        <span>春季瑜伽体验营</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>ROI: 5.2x</span>
                        <span className="text-muted-foreground">支出: ¥8,500</span>
                        <span className="text-green-600">收入: ¥44,200</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 rounded-full bg-green-500" style={{ width: "85%" }}></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                        <span>会员续费优惠月</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>ROI: 4.8x</span>
                        <span className="text-muted-foreground">支出: ¥6,200</span>
                        <span className="text-green-600">收入: ¥29,760</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 rounded-full bg-blue-500" style={{ width: "78%" }}></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                        <span>新店开业特惠</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>ROI: 3.5x</span>
                        <span className="text-muted-foreground">支出: ¥12,000</span>
                        <span className="text-green-600">收入: ¥42,000</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 rounded-full bg-purple-500" style={{ width: "65%" }}></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                        <span>暑期亲子瑜伽</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>ROI: 2.8x</span>
                        <span className="text-muted-foreground">支出: ¥5,800</span>
                        <span className="text-green-600">收入: ¥16,240</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 rounded-full bg-yellow-500" style={{ width: "52%" }}></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-red-500"></div>
                        <span>瑜伽装备促销</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>ROI: 1.5x</span>
                        <span className="text-muted-foreground">支出: ¥3,200</span>
                        <span className="text-green-600">收入: ¥4,800</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 rounded-full bg-red-500" style={{ width: "28%" }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看详细数据
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>活动转化率</CardTitle>
                <CardDescription>各活动的转化率对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是活动转化率图表 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>春季瑜伽体验营</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">32.5%</span>
                        <Badge variant="outline" className="bg-green-50">
                          高
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>会员续费优惠月</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">28.3%</span>
                        <Badge variant="outline" className="bg-green-50">
                          高
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>新店开业特惠</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">22.7%</span>
                        <Badge variant="outline" className="bg-blue-50">
                          中
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>暑期亲子瑜伽</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">18.5%</span>
                        <Badge variant="outline" className="bg-blue-50">
                          中
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>瑜伽装备促销</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">12.2%</span>
                        <Badge variant="outline" className="bg-red-50">
                          低
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>活动参与度</CardTitle>
                <CardDescription>各活动的参与人数和互动量</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是活动参与度图表 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>春季瑜伽体验营</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">325人</span>
                        <span className="text-muted-foreground">互动量: 1,280</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>会员续费优惠月</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">283人</span>
                        <span className="text-muted-foreground">互动量: 920</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>新店开业特惠</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">420人</span>
                        <span className="text-muted-foreground">互动量: 1,560</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>暑期亲子瑜伽</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">185人</span>
                        <span className="text-muted-foreground">互动量: 720</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>瑜伽装备促销</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">122人</span>
                        <span className="text-muted-foreground">互动量: 380</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="coupons" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>优惠券使用情况</CardTitle>
              <CardDescription>各类优惠券的发放和使用情况</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>优惠券名称</TableHead>
                    <TableHead>发放数量</TableHead>
                    <TableHead>使用数量</TableHead>
                    <TableHead>使用率</TableHead>
                    <TableHead>优惠总额</TableHead>
                    <TableHead>带动消费</TableHead>
                    <TableHead>ROI</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">新人首课立减50元</TableCell>
                    <TableCell>500</TableCell>
                    <TableCell>325</TableCell>
                    <TableCell>65.0%</TableCell>
                    <TableCell>¥16,250</TableCell>
                    <TableCell>¥81,250</TableCell>
                    <TableCell className="text-green-600">5.0x</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">会员生日礼券100元</TableCell>
                    <TableCell>280</TableCell>
                    <TableCell>210</TableCell>
                    <TableCell>75.0%</TableCell>
                    <TableCell>¥21,000</TableCell>
                    <TableCell>¥84,000</TableCell>
                    <TableCell className="text-green-600">4.0x</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">季卡9折优惠券</TableCell>
                    <TableCell>350</TableCell>
                    <TableCell>245</TableCell>
                    <TableCell>70.0%</TableCell>
                    <TableCell>¥24,500</TableCell>
                    <TableCell>¥220,500</TableCell>
                    <TableCell className="text-green-600">9.0x</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">年卡8.5折优惠券</TableCell>
                    <TableCell>200</TableCell>
                    <TableCell>120</TableCell>
                    <TableCell>60.0%</TableCell>
                    <TableCell>¥36,000</TableCell>
                    <TableCell>¥204,000</TableCell>
                    <TableCell className="text-green-600">5.7x</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">瑜伽装备8折券</TableCell>
                    <TableCell>400</TableCell>
                    <TableCell>180</TableCell>
                    <TableCell>45.0%</TableCell>
                    <TableCell>¥7,200</TableCell>
                    <TableCell>¥28,800</TableCell>
                    <TableCell className="text-green-600">4.0x</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看更多优惠券
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>优惠券使用趋势</CardTitle>
                <CardDescription>优惠券使用数量随时间变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是优惠券使用趋势图表 */}
                  <div className="flex h-[300px] items-end gap-2">
                    {Array.from({ length: 30 }).map((_, i) => {
                      const height = 30 + Math.random() * 70
                      return (
                        <div key={i} className="relative flex-1">
                          <div className="bg-primary rounded-t-md w-full" style={{ height: `${height}%` }}></div>
                          {i % 5 === 0 && (
                            <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                              {i + 1}日
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>优惠券使用渠道</CardTitle>
                <CardDescription>不同渠道优惠券使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是优惠券使用渠道图表 */}
                  <div className="space-y-8">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-green-500"></div>
                          <span>微信小程序</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>420张</span>
                          <span className="text-muted-foreground">38.9%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "38.9%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                          <span>官方网站</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>280张</span>
                          <span className="text-muted-foreground">25.9%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-blue-500" style={{ width: "25.9%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                          <span>线下门店</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>220张</span>
                          <span className="text-muted-foreground">20.4%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-purple-500" style={{ width: "20.4%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                          <span>第三方平台</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>160张</span>
                          <span className="text-muted-foreground">14.8%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-yellow-500" style={{ width: "14.8%" }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="referrals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>会员推荐效果</CardTitle>
              <CardDescription>会员推荐计划的效果分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg">
                  <Share2 className="h-8 w-8 text-primary mb-2" />
                  <div className="text-2xl font-bold">285</div>
                  <p className="text-sm text-muted-foreground">推荐总人数</p>
                </div>
                <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg">
                  <Users className="h-8 w-8 text-primary mb-2" />
                  <div className="text-2xl font-bold">168</div>
                  <p className="text-sm text-muted-foreground">成功转化人数</p>
                </div>
                <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg">
                  <TrendingUp className="h-8 w-8 text-primary mb-2" />
                  <div className="text-2xl font-bold">58.9%</div>
                  <p className="text-sm text-muted-foreground">转化率</p>
                </div>
              </div>

              <div className="mt-6">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>推荐人</TableHead>
                      <TableHead>推荐人数</TableHead>
                      <TableHead>成功转化</TableHead>
                      <TableHead>转化率</TableHead>
                      <TableHead>推荐奖励</TableHead>
                      <TableHead>带动消费</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">张女士</TableCell>
                      <TableCell>12</TableCell>
                      <TableCell>8</TableCell>
                      <TableCell>66.7%</TableCell>
                      <TableCell>¥800</TableCell>
                      <TableCell>¥12,800</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">李先生</TableCell>
                      <TableCell>10</TableCell>
                      <TableCell>7</TableCell>
                      <TableCell>70.0%</TableCell>
                      <TableCell>¥700</TableCell>
                      <TableCell>¥10,500</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">王女士</TableCell>
                      <TableCell>8</TableCell>
                      <TableCell>5</TableCell>
                      <TableCell>62.5%</TableCell>
                      <TableCell>¥500</TableCell>
                      <TableCell>¥7,500</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">赵先生</TableCell>
                      <TableCell>7</TableCell>
                      <TableCell>4</TableCell>
                      <TableCell>57.1%</TableCell>
                      <TableCell>¥400</TableCell>
                      <TableCell>¥6,000</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">刘女士</TableCell>
                      <TableCell>6</TableCell>
                      <TableCell>4</TableCell>
                      <TableCell>66.7%</TableCell>
                      <TableCell>¥400</TableCell>
                      <TableCell>¥6,400</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看完整排行榜
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>推荐奖励使用情况</CardTitle>
                <CardDescription>会员推荐奖励的使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是推荐奖励使用情况图表 */}
                  <div className="space-y-8">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-green-500"></div>
                          <span>已使用</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>¥12,500</span>
                          <span className="text-muted-foreground">62.5%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "62.5%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                          <span>未使用</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>¥7,500</span>
                          <span className="text-muted-foreground">37.5%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-blue-500" style={{ width: "37.5%" }}></div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-8 space-y-4">
                    <div className="flex items-center justify-between">
                      <span>课程抵扣</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">¥8,200</span>
                        <Badge>65.6%</Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>会员卡抵扣</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">¥3,500</span>
                        <Badge>28.0%</Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>商品抵扣</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">¥800</span>
                        <Badge>6.4%</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>推荐来源分析</CardTitle>
                <CardDescription>会员推荐的来源渠道分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是推荐来源分析图表 */}
                  <div className="space-y-8">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-green-500"></div>
                          <span>微信朋友圈</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>125人</span>
                          <span className="text-muted-foreground">43.9%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "43.9%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                          <span>微信群</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>85人</span>
                          <span className="text-muted-foreground">29.8%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-blue-500" style={{ width: "29.8%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                          <span>线下口碑</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>45人</span>
                          <span className="text-muted-foreground">15.8%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-purple-500" style={{ width: "15.8%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                          <span>其他社交媒体</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>30人</span>
                          <span className="text-muted-foreground">10.5%</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-yellow-500" style={{ width: "10.5%" }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="social" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>社交媒体效果分析</CardTitle>
              <CardDescription>各社交媒体平台的营销效果对比</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>平台</TableHead>
                    <TableHead>曝光量</TableHead>
                    <TableHead>互动量</TableHead>
                    <TableHead>点击量</TableHead>
                    <TableHead>转化量</TableHead>
                    <TableHead>转化率</TableHead>
                    <TableHead>投入成本</TableHead>
                    <TableHead>ROI</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">微信公众号</TableCell>
                    <TableCell>25,800</TableCell>
                    <TableCell>3,850</TableCell>
                    <TableCell>2,320</TableCell>
                    <TableCell>85</TableCell>
                    <TableCell>3.7%</TableCell>
                    <TableCell>¥5,000</TableCell>
                    <TableCell className="text-green-600">4.2x</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">抖音</TableCell>
                    <TableCell>42,500</TableCell>
                    <TableCell>8,200</TableCell>
                    <TableCell>3,600</TableCell>
                    <TableCell>72</TableCell>
                    <TableCell>2.0%</TableCell>
                    <TableCell>¥8,000</TableCell>
                    <TableCell className="text-green-600">2.8x</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">小红书</TableCell>
                    <TableCell>18,600</TableCell>
                    <TableCell>4,200</TableCell>
                    <TableCell>1,850</TableCell>
                    <TableCell>65</TableCell>
                    <TableCell>3.5%</TableCell>
                    <TableCell>¥4,500</TableCell>
                    <TableCell className="text-green-600">3.6x</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">微博</TableCell>
                    <TableCell>12,500</TableCell>
                    <TableCell>1,800</TableCell>
                    <TableCell>950</TableCell>
                    <TableCell>28</TableCell>
                    <TableCell>2.9%</TableCell>
                    <TableCell>¥3,000</TableCell>
                    <TableCell className="text-green-600">2.3x</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">朋友圈广告</TableCell>
                    <TableCell>32,000</TableCell>
                    <TableCell>2,800</TableCell>
                    <TableCell>1,650</TableCell>
                    <TableCell>58</TableCell>
                    <TableCell>3.5%</TableCell>
                    <TableCell>¥6,000</TableCell>
                    <TableCell className="text-green-600">2.4x</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看详细数据
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>社交媒体内容效果</CardTitle>
                <CardDescription>不同类型内容的效果对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是社交媒体内容效果图表 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>教练示范视频</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">互动率: 18.5%</span>
                        <Badge variant="outline" className="bg-green-50">
                          高
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>会员体验分享</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">互动率: 15.2%</span>
                        <Badge variant="outline" className="bg-green-50">
                          高
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>瑜伽知识科普</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">互动率: 12.8%</span>
                        <Badge variant="outline" className="bg-blue-50">
                          中
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>活动宣传</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">互动率: 10.5%</span>
                        <Badge variant="outline" className="bg-blue-50">
                          中
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>优惠信息</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">互动率: 8.2%</span>
                        <Badge variant="outline" className="bg-red-50">
                          低
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>社交媒体用户画像</CardTitle>
                <CardDescription>社交媒体关注者的用户画像</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是社交媒体用户画像图表 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">年龄分布</h3>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs">18-24岁</span>
                          <span className="text-xs">15%</span>
                        </div>
                        <div className="h-1.5 w-full rounded-full bg-muted">
                          <div className="h-1.5 rounded-full bg-primary" style={{ width: "15%" }}></div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs">25-34岁</span>
                          <span className="text-xs">42%</span>
                        </div>
                        <div className="h-1.5 w-full rounded-full bg-muted">
                          <div className="h-1.5 rounded-full bg-primary" style={{ width: "42%" }}></div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs">35-44岁</span>
                          <span className="text-xs">28%</span>
                        </div>
                        <div className="h-1.5 w-full rounded-full bg-muted">
                          <div className="h-1.5 rounded-full bg-primary" style={{ width: "28%" }}></div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs">45岁以上</span>
                          <span className="text-xs">15%</span>
                        </div>
                        <div className="h-1.5 w-full rounded-full bg-muted">
                          <div className="h-1.5 rounded-full bg-primary" style={{ width: "15%" }}></div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">性别比例</h3>
                      <div className="flex h-24 items-center">
                        <div className="relative h-24 w-24">
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="text-xs font-medium">女性</div>
                          </div>
                          <svg className="h-full w-full" viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="40" fill="none" stroke="#e2e8f0" strokeWidth="10" />
                            <circle
                              cx="50"
                              cy="50"
                              r="40"
                              fill="none"
                              stroke="hsl(var(--primary))"
                              strokeWidth="10"
                              strokeDasharray="251.2"
                              strokeDashoffset="75.36"
                              transform="rotate(-90 50 50)"
                            />
                          </svg>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm">女性: 70%</div>
                          <div className="text-sm">男性: 30%</div>
                        </div>
                      </div>

                      <h3 className="text-sm font-medium mt-4">兴趣偏好</h3>
                      <div className="flex flex-wrap gap-2">
                        <Badge>健康生活</Badge>
                        <Badge>减压放松</Badge>
                        <Badge>塑形</Badge>
                        <Badge>瑜伽服饰</Badge>
                        <Badge>冥想</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="acquisition" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>获客成本分析</CardTitle>
              <CardDescription>不同渠道的获客成本对比</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>获客渠道</TableHead>
                    <TableHead>新增会员</TableHead>
                    <TableHead>营销支出</TableHead>
                    <TableHead>获客成本</TableHead>
                    <TableHead>会员价值</TableHead>
                    <TableHead>投资回报比</TableHead>
                    <TableHead>留存率</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">会员推荐</TableCell>
                    <TableCell>42</TableCell>
                    <TableCell>¥4,200</TableCell>
                    <TableCell className="text-green-600">¥100</TableCell>
                    <TableCell>¥1,850</TableCell>
                    <TableCell className="text-green-600">18.5x</TableCell>
                    <TableCell>92%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">社交媒体</TableCell>
                    <TableCell>38</TableCell>
                    <TableCell>¥8,500</TableCell>
                    <TableCell className="text-yellow-600">¥224</TableCell>
                    <TableCell>¥1,650</TableCell>
                    <TableCell className="text-green-600">7.4x</TableCell>
                    <TableCell>85%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">线下活动</TableCell>
                    <TableCell>25</TableCell>
                    <TableCell>¥6,200</TableCell>
                    <TableCell className="text-yellow-600">¥248</TableCell>
                    <TableCell>¥1,750</TableCell>
                    <TableCell className="text-green-600">7.1x</TableCell>
                    <TableCell>88%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">搜索引擎</TableCell>
                    <TableCell>18</TableCell>
                    <TableCell>¥4,500</TableCell>
                    <TableCell className="text-yellow-600">¥250</TableCell>
                    <TableCell>¥1,580</TableCell>
                    <TableCell className="text-green-600">6.3x</TableCell>
                    <TableCell>82%</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">第三方平台</TableCell>
                    <TableCell>15</TableCell>
                    <TableCell>¥5,100</TableCell>
                    <TableCell className="text-red-600">¥340</TableCell>
                    <TableCell>¥1,480</TableCell>
                    <TableCell className="text-green-600">4.4x</TableCell>
                    <TableCell>78%</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看详细数据
              </Button>
            </CardFooter>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>获客成本趋势</CardTitle>
                <CardDescription>获客成本随时间变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是获客成本趋势图表 */}
                  <div className="flex h-[300px] items-end gap-2">
                    {Array.from({ length: 12 }).map((_, i) => {
                      const height = 40 + Math.random() * 60
                      return (
                        <div key={i} className="relative flex-1">
                          <div className="bg-primary rounded-t-md w-full" style={{ height: `${height}%` }}></div>
                          <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                            {i + 1}月
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>会员生命周期价值</CardTitle>
                <CardDescription>不同渠道获取会员的生命周期价值</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {/* 这里是会员生命周期价值图表 */}
                  <div className="space-y-8">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-green-500"></div>
                          <span>会员推荐</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>¥8,500</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "85%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                          <span>线下活动</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>¥7,800</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-blue-500" style={{ width: "78%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-purple-500"></div>
                          <span>社交媒体</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>¥7,200</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-purple-500" style={{ width: "72%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                          <span>搜索引擎</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>¥6,500</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-yellow-500" style={{ width: "65%" }}></div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full bg-red-500"></div>
                          <span>第三方平台</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span>¥5,800</span>
                        </div>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-red-500" style={{ width: "58%" }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 导出报表对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>导出营销分析报表</DialogTitle>
            <DialogDescription>选择导出格式和内容</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>报表类型</Label>
              <Select defaultValue="summary">
                <SelectTrigger>
                  <SelectValue placeholder="选择报表类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">汇总报表</SelectItem>
                  <SelectItem value="campaigns">营销活动分析报表</SelectItem>
                  <SelectItem value="coupons">优惠券分析报表</SelectItem>
                  <SelectItem value="referrals">会员推荐分析报表</SelectItem>
                  <SelectItem value="social">社交媒体分析报表</SelectItem>
                  <SelectItem value="all">完整报表（包含所有数据）</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>导出格式</Label>
              <Select defaultValue="excel">
                <SelectTrigger>
                  <SelectValue placeholder="选择导出格式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="csv">CSV (.csv)</SelectItem>
                  <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>时间范围</Label>
              <Select defaultValue={dateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7days">最近7天</SelectItem>
                  <SelectItem value="30days">最近30天</SelectItem>
                  <SelectItem value="90days">最近90天</SelectItem>
                  <SelectItem value="thismonth">本月</SelectItem>
                  <SelectItem value="lastmonth">上月</SelectItem>
                  <SelectItem value="thisyear">今年</SelectItem>
                  <SelectItem value="custom">自定义</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="report-name">报表名称</Label>
              <Input id="report-name" defaultValue="营销效果分析报表" />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setIsExportDialogOpen(false)}>导出</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

