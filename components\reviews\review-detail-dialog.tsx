"use client"

import { useState } from "react"
import { toast } from "@/components/ui/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>L<PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  Eye,
  EyeOff,
  MessageSquare,
  Star as StarIcon,
  ThumbsDown,
  ThumbsUp,
  User,
  XCircle,
} from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Review } from "@/lib/types/review"
import { reviewApi } from "@/lib/api/review"

interface ReviewDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  review: Review | null
  onReviewUpdated?: () => void
}

export function ReviewDetailDialog({
  open,
  onOpenChange,
  review,
  onReviewUpdated
}: ReviewDetailDialogProps) {
  const [replyText, setReplyText] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState("detail")

  if (!review) return null

  // 获取评分样式
  const getRatingStyle = (rating: number) => {
    if (rating >= 4.5) return "text-green-600"
    if (rating >= 3.5) return "text-blue-600"
    if (rating >= 2.5) return "text-yellow-600"
    return "text-red-600"
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge variant="default">已发布</Badge>
      case "pending":
        return <Badge variant="secondary">待审核</Badge>
      case "hidden":
        return <Badge variant="outline">已隐藏</Badge>
      default:
        return null
    }
  }

  // 获取情感分析标签
  const getSentimentBadge = (sentiment: string) => {
    switch (sentiment) {
      case "positive":
        return <Badge variant="outline" className="bg-primary/10 text-primary">正面评价</Badge>
      case "neutral":
        return <Badge variant="outline">中性评价</Badge>
      case "negative":
        return <Badge variant="outline" className="bg-destructive/10 text-destructive">负面评价</Badge>
      default:
        return null
    }
  }

  // 处理回复提交
  const handleSubmitReply = async () => {
    if (!replyText.trim()) {
      toast({
        title: "回复内容不能为空",
        description: "请输入回复内容",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    try {
      await reviewApi.replyToReview(review.id, replyText)
      toast({
        title: "回复成功",
        description: "您的回复已发送给会员",
      })
      setReplyText("")
      if (onReviewUpdated) {
        onReviewUpdated()
      }
      setActiveTab("detail")
    } catch (error) {
      toast({
        title: "回复失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 处理评价状态更新
  const handleUpdateStatus = async (status: string) => {
    try {
      await reviewApi.updateReviewStatus(review.id, status)

      let actionText = ""
      switch (status) {
        case "published":
          actionText = "发布"
          break
        case "hidden":
          actionText = "隐藏"
          break
        case "pending":
          actionText = "设为待审核"
          break
      }

      toast({
        title: `${actionText}成功`,
        description: `评价已${actionText}`,
      })

      if (onReviewUpdated) {
        onReviewUpdated()
      }
    } catch (error) {
      toast({
        title: "操作失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>评价详情</DialogTitle>
          <DialogDescription>查看和管理课程与教练评价</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="detail" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="detail">评价详情</TabsTrigger>
            <TabsTrigger value="reply">回复管理</TabsTrigger>
            <TabsTrigger value="analysis">评价分析</TabsTrigger>
          </TabsList>

          {/* 评价详情标签页 */}
          <TabsContent value="detail" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 会员信息 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">会员信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={review.member.avatar} alt={review.member.name} />
                      <AvatarFallback>{review.member.name[0]}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{review.member.name}</div>
                      <div className="text-sm text-muted-foreground">{review.member.level}</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>会员ID: {review.memberId}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>入会日期: {review.member.joinDate}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 评价信息 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">评价信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-semibold">{review.rating}</span>
                      <div className="flex">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <StarIcon
                            key={i}
                            className={`h-4 w-4 ${
                              i < review.rating ? "text-primary fill-primary" : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(review.status)}
                      {getSentimentBadge(review.sentiment)}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>评价日期: {review.date}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-4 w-4 text-muted-foreground" />
                      <span>回复状态: {review.hasReply ? "已回复" : "未回复"}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 课程和教练信息 */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">课程与教练信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-muted-foreground">课程信息</Label>
                    <div className="font-medium">{review.course.name}</div>
                    <div className="text-sm text-muted-foreground">类型: {review.course.type}</div>
                    <div className="text-sm text-muted-foreground">课程ID: {review.courseId}</div>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-muted-foreground">教练信息</Label>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={review.coach.avatar} alt={review.coach.name} />
                        <AvatarFallback>{review.coach.name[0]}</AvatarFallback>
                      </Avatar>
                      <span className="font-medium">{review.coach.name}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">教练ID: {review.coachId}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 评价内容 */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">评价内容</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-3 bg-muted/30 rounded-md">
                  {review.comment}
                </div>

                {review.hasReply && (
                  <div className="space-y-2">
                    <Label className="text-muted-foreground">回复内容</Label>
                    <div className="p-3 bg-blue-50 rounded-md">
                      {review.reply}
                      <div className="text-xs text-muted-foreground mt-2">
                        回复时间: {review.replyDate}
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex flex-wrap gap-2 mt-2">
                  {review.keywords.map((keyword, index) => (
                    <Badge key={index} variant="outline" className="bg-gray-50">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 回复管理标签页 */}
          <TabsContent value="reply" className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <CardTitle>回复评价</CardTitle>
                <CardDescription>
                  回复会员的评价，提供解释或感谢
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-3 bg-muted/30 rounded-md">
                  <div className="flex items-center gap-2 mb-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={review.member.avatar} alt={review.member.name} />
                      <AvatarFallback>{review.member.name[0]}</AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{review.member.name}</span>
                    <span className="text-sm text-muted-foreground">({review.date})</span>
                  </div>
                  {review.comment}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reply">您的回复</Label>
                  <Textarea
                    id="reply"
                    placeholder="输入回复内容..."
                    rows={5}
                    value={replyText || review.reply || ''}
                    onChange={(e) => setReplyText(e.target.value)}
                  />
                </div>

                <Button
                  onClick={handleSubmitReply}
                  disabled={isSubmitting || !replyText.trim()}
                >
                  {isSubmitting ? "提交中..." : "提交回复"}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 评价分析标签页 */}
          <TabsContent value="analysis" className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <CardTitle>评价分析</CardTitle>
                <CardDescription>
                  评价的情感分析和关键词提取
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label className="text-muted-foreground">情感分析</Label>
                    <div className="flex items-center gap-2">
                      {review.sentiment === "positive" && (
                        <>
                          <ThumbsUp className="h-5 w-5 text-primary" />
                          <span className="font-medium text-primary">正面评价</span>
                        </>
                      )}
                      {review.sentiment === "neutral" && (
                        <>
                          <AlertTriangle className="h-5 w-5 text-muted-foreground" />
                          <span className="font-medium text-muted-foreground">中性评价</span>
                        </>
                      )}
                      {review.sentiment === "negative" && (
                        <>
                          <ThumbsDown className="h-5 w-5 text-destructive" />
                          <span className="font-medium text-destructive">负面评价</span>
                        </>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-muted-foreground">关键词提取</Label>
                    <div className="flex flex-wrap gap-2">
                      {review.keywords.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-gray-50">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-muted-foreground">建议操作</Label>
                    <div className="text-sm">
                      {review.sentiment === "positive" && (
                        <span>积极评价，建议回复感谢并鼓励会员继续参与课程。</span>
                      )}
                      {review.sentiment === "neutral" && (
                        <span>中性评价，建议回复并了解会员的具体需求，提供改进方案。</span>
                      )}
                      {review.sentiment === "negative" && (
                        <span>负面评价，建议尽快回复并解决会员提出的问题，必要时提供补偿。</span>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {review.status === "pending" && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-green-600 hover:text-green-700 hover:bg-green-50"
                  onClick={() => handleUpdateStatus("published")}
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  通过并发布
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  onClick={() => handleUpdateStatus("hidden")}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  拒绝并隐藏
                </Button>
              </>
            )}

            {review.status === "published" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleUpdateStatus("hidden")}
              >
                <EyeOff className="mr-2 h-4 w-4" />
                隐藏评价
              </Button>
            )}

            {review.status === "hidden" && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleUpdateStatus("published")}
              >
                <Eye className="mr-2 h-4 w-4" />
                显示评价
              </Button>
            )}
          </div>

          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
