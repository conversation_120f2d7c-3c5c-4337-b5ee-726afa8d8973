import { 
  SeriesCourse, 
  SeriesSchedule, 
  SeriesEnrollment, 
  SeriesAttendance 
} from "@/types/series-courses";

// 模拟系列课程数据
export const mockSeriesCourses: SeriesCourse[] = [
  {
    id: "sc-001",
    name: "瑜伽初级入门系列课程",
    description: "适合瑜伽初学者的系统性入门课程，从基础姿势开始，逐步提升难度。",
    courseType: "瑜伽",
    totalSessions: 8,
    startDate: "2023-06-01",
    endDate: "2023-07-20",
    price: 1280,
    maxStudents: 15,
    instructor: "李教练",
    instructorId: "coach-001",
    status: "published",
    coverImage: "/images/yoga-beginner.jpg",
    tags: ["初级", "入门", "哈他瑜伽"],
    enrolledStudents: 12
  },
  {
    id: "sc-002",
    name: "普拉提核心训练系列课程",
    description: "专注于核心肌群训练的普拉提系列课程，帮助学员塑造完美腰腹线条。",
    courseType: "普拉提",
    totalSessions: 10,
    startDate: "2023-06-15",
    endDate: "2023-08-17",
    price: 1680,
    maxStudents: 12,
    instructor: "王教练",
    instructorId: "coach-002",
    status: "published",
    coverImage: "/images/pilates-core.jpg",
    tags: ["普拉提", "核心训练", "中级"],
    enrolledStudents: 8
  },
  {
    id: "sc-003",
    name: "高级瑜伽进阶系列课程",
    description: "针对有一定瑜伽基础的学员，提供更具挑战性的高级瑜伽训练。",
    courseType: "瑜伽",
    totalSessions: 12,
    startDate: "2023-07-01",
    endDate: "2023-09-16",
    price: 2160,
    maxStudents: 10,
    instructor: "张教练",
    instructorId: "coach-003",
    status: "draft",
    coverImage: "/images/yoga-advanced.jpg",
    tags: ["高级", "进阶", "阿斯汤加"],
    enrolledStudents: 0
  },
  {
    id: "sc-004",
    name: "孕产瑜伽特别系列课程",
    description: "专为孕期和产后女性设计的安全、舒适的瑜伽课程，促进身心健康。",
    courseType: "瑜伽",
    totalSessions: 6,
    startDate: "2023-06-10",
    endDate: "2023-07-15",
    price: 1080,
    maxStudents: 8,
    instructor: "刘教练",
    instructorId: "coach-004",
    status: "published",
    coverImage: "/images/prenatal-yoga.jpg",
    tags: ["孕产瑜伽", "女性", "温和"],
    enrolledStudents: 5
  },
  {
    id: "sc-005",
    name: "瑜伽教练培训系列课程",
    description: "专业瑜伽教练培训课程，完成后可获得瑜伽教练认证证书。",
    courseType: "教练培训",
    totalSessions: 20,
    startDate: "2023-08-01",
    endDate: "2023-10-20",
    price: 6800,
    maxStudents: 15,
    instructor: "赵教练",
    instructorId: "coach-005",
    status: "draft",
    coverImage: "/images/yoga-teacher-training.jpg",
    tags: ["教练培训", "认证", "专业"],
    enrolledStudents: 0
  }
];

// 模拟系列课程排期数据
export const mockSeriesSchedules: SeriesSchedule[] = [
  // 瑜伽初级入门系列课程排期
  {
    id: "ss-001",
    seriesCourseId: "sc-001",
    sessionNumber: 1,
    title: "瑜伽基础姿势与呼吸法",
    date: "2023-06-01",
    startTime: "19:00",
    endTime: "20:30",
    venue: "瑜伽室1",
    venueId: "venue-001",
    instructor: "李教练",
    instructorId: "coach-001",
    description: "学习基础瑜伽姿势和正确的呼吸方法",
    status: "completed"
  },
  {
    id: "ss-002",
    seriesCourseId: "sc-001",
    sessionNumber: 2,
    title: "站姿体式练习",
    date: "2023-06-08",
    startTime: "19:00",
    endTime: "20:30",
    venue: "瑜伽室1",
    venueId: "venue-001",
    instructor: "李教练",
    instructorId: "coach-001",
    description: "学习和练习基础站姿体式",
    status: "completed"
  },
  {
    id: "ss-003",
    seriesCourseId: "sc-001",
    sessionNumber: 3,
    title: "坐姿体式练习",
    date: "2023-06-15",
    startTime: "19:00",
    endTime: "20:30",
    venue: "瑜伽室1",
    venueId: "venue-001",
    instructor: "李教练",
    instructorId: "coach-001",
    description: "学习和练习基础坐姿体式",
    status: "scheduled"
  },
  
  // 普拉提核心训练系列课程排期
  {
    id: "ss-011",
    seriesCourseId: "sc-002",
    sessionNumber: 1,
    title: "普拉提基础动作",
    date: "2023-06-15",
    startTime: "10:00",
    endTime: "11:30",
    venue: "普拉提室",
    venueId: "venue-002",
    instructor: "王教练",
    instructorId: "coach-002",
    description: "学习普拉提基础动作和呼吸方法",
    status: "completed"
  },
  {
    id: "ss-012",
    seriesCourseId: "sc-002",
    sessionNumber: 2,
    title: "核心肌群激活",
    date: "2023-06-22",
    startTime: "10:00",
    endTime: "11:30",
    venue: "普拉提室",
    venueId: "venue-002",
    instructor: "王教练",
    instructorId: "coach-002",
    description: "学习如何正确激活和使用核心肌群",
    status: "scheduled"
  }
];

// 模拟系列课程报名数据
export const mockSeriesEnrollments: SeriesEnrollment[] = [
  {
    id: "se-001",
    seriesCourseId: "sc-001",
    memberId: "member-001",
    memberName: "张三",
    enrollDate: "2023-05-20",
    paymentStatus: "paid",
    paymentAmount: 1280,
    membershipCardId: "card-001",
    membershipCardName: "瑜伽年卡"
  },
  {
    id: "se-002",
    seriesCourseId: "sc-001",
    memberId: "member-002",
    memberName: "李四",
    enrollDate: "2023-05-21",
    paymentStatus: "paid",
    paymentAmount: 1280,
    membershipCardId: "card-002",
    membershipCardName: "瑜伽季卡"
  },
  {
    id: "se-003",
    seriesCourseId: "sc-002",
    memberId: "member-003",
    memberName: "王五",
    enrollDate: "2023-06-01",
    paymentStatus: "paid",
    paymentAmount: 1680,
    membershipCardId: "card-003",
    membershipCardName: "普拉提月卡"
  }
];

// 模拟系列课程出勤记录
export const mockSeriesAttendances: SeriesAttendance[] = [
  {
    id: "sa-001",
    seriesScheduleId: "ss-001",
    seriesCourseId: "sc-001",
    memberId: "member-001",
    memberName: "张三",
    status: "attended",
    checkInTime: "2023-06-01 19:05"
  },
  {
    id: "sa-002",
    seriesScheduleId: "ss-001",
    seriesCourseId: "sc-001",
    memberId: "member-002",
    memberName: "李四",
    status: "attended",
    checkInTime: "2023-06-01 18:55"
  },
  {
    id: "sa-003",
    seriesScheduleId: "ss-002",
    seriesCourseId: "sc-001",
    memberId: "member-001",
    memberName: "张三",
    status: "absent"
  },
  {
    id: "sa-004",
    seriesScheduleId: "ss-002",
    seriesCourseId: "sc-001",
    memberId: "member-002",
    memberName: "李四",
    status: "attended",
    checkInTime: "2023-06-08 18:50"
  },
  {
    id: "sa-005",
    seriesScheduleId: "ss-011",
    seriesCourseId: "sc-002",
    memberId: "member-003",
    memberName: "王五",
    status: "attended",
    checkInTime: "2023-06-15 09:55"
  }
];

// 模拟自动约课结果
export const mockAutoBookingResult: AutoBookingResult = {
  success: [
    {
      memberId: "member-001",
      memberName: "张三",
      scheduleId: "ss-003",
      sessionNumber: 3
    },
    {
      memberId: "member-002",
      memberName: "李四",
      scheduleId: "ss-003",
      sessionNumber: 3
    }
  ],
  failed: [
    {
      memberId: "member-005",
      memberName: "赵六",
      scheduleId: "ss-003",
      sessionNumber: 3,
      error: "会员卡已过期"
    }
  ]
};
