"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import {
  Calendar,
  Download,
  TrendingUp,
  Users,
  UserPlus,
  UserMinus,
  Heart,
  Star,
  Clock,
  Activity,
  DollarSign,
  Share2,
  MessageSquare,
  Filter,
  Save,
  BarChart4,
  Layers,
  ChevronDown,
  ChevronUp,
  Search,
  RefreshCw,
  Zap,
  Target,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON><PERSON>, <PERSON>lt<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MoreHorizontal, Maximize2, SlidersHorizontal, User, Edit, Ban, Plus, AlertTriangle } from "lucide-react"

export default function MemberStatistics() {
  const [dateRange, setDateRange] = useState("30days")
  const [memberType, setMemberType] = useState("all")
  const [viewMode, setViewMode] = useState("charts")
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isCompareDialogOpen, setIsCompareDialogOpen] = useState(false)
  const [isCustomizeDialogOpen, setIsCustomizeDialogOpen] = useState(false)
  const [isAdvancedFilterOpen, setIsAdvancedFilterOpen] = useState(false)
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null)
  const [selectedMember, setSelectedMember] = useState<string | null>(null)
  const [isLifecycleExpanded, setIsLifecycleExpanded] = useState(false)
  const [savedViews, setSavedViews] = useState([
    { id: "default", name: "默认视图", isDefault: true },
    { id: "retention", name: "留存分析视图", isDefault: false },
    { id: "value", name: "价值分析视图", isDefault: false },
  ])
  const [currentView, setCurrentView] = useState("default")
  const [isLoading, setIsLoading] = useState(false)
  const [showTips, setShowTips] = useState(true)
  const [compareGroups, setCompareGroups] = useState([
    { id: "new", name: "新会员", isSelected: true },
    { id: "active", name: "活跃会员", isSelected: true },
    { id: "risk", name: "流失风险", isSelected: false },
    { id: "inactive", name: "非活跃会员", isSelected: false },
  ])

  // 模拟会员细分数据
  const memberSegments = [
    { id: "new", name: "新会员", count: 285, percentage: 22.2, growth: "+15.2%" },
    { id: "active", name: "活跃会员", count: 520, percentage: 40.5, growth: "+8.3%" },
    { id: "risk", name: "流失风险", count: 180, percentage: 14.0, growth: "-3.5%" },
    { id: "inactive", name: "非活跃会员", count: 299, percentage: 23.3, growth: "+2.1%" },
  ]

  // 模拟会员来源数据
  const memberSources = [
    { source: "线下门店", count: 485, percentage: 37.8 },
    { source: "官网注册", count: 320, percentage: 24.9 },
    { source: "会员推荐", count: 245, percentage: 19.1 },
    { source: "营销活动", count: 180, percentage: 14.0 },
    { source: "其他渠道", count: 54, percentage: 4.2 },
  ]

  // 模拟会员消费行为数据
  const memberBehaviors = [
    { behavior: "团体课", count: 720, percentage: 56.1 },
    { behavior: "私教课", count: 380, percentage: 29.6 },
    { behavior: "购买商品", count: 210, percentage: 16.4 },
    { behavior: "场地预约", count: 150, percentage: 11.7 },
    { behavior: "活动参与", count: 320, percentage: 24.9 },
  ]

  // 模拟会员标签数据
  const memberTags = [
    { tag: "瑜伽爱好者", count: 520, percentage: 40.5 },
    { tag: "健身达人", count: 380, percentage: 29.6 },
    { tag: "早起锻炼", count: 310, percentage: 24.1 },
    { tag: "周末活跃", count: 420, percentage: 32.7 },
    { tag: "高消费", count: 185, percentage: 14.4 },
    { tag: "社交活跃", count: 230, percentage: 17.9 },
    { tag: "长期会员", count: 350, percentage: 27.3 },
    { tag: "新手入门", count: 210, percentage: 16.4 },
  ]

  // 模拟会员列表数据
  const memberList = [
    {
      id: "m001",
      name: "张三",
      avatar: "/placeholder.svg?height=40&width=40",
      age: 28,
      gender: "男",
      memberType: "年卡会员",
      joinDate: "2023-01-15",
      lastVisit: "2023-05-20",
      totalSpent: 5800,
      status: "活跃",
    },
    {
      id: "m002",
      name: "李四",
      avatar: "/placeholder.svg?height=40&width=40",
      age: 35,
      gender: "女",
      memberType: "季卡会员",
      joinDate: "2022-11-05",
      lastVisit: "2023-05-18",
      totalSpent: 3200,
      status: "活跃",
    },
    {
      id: "m003",
      name: "王五",
      avatar: "/placeholder.svg?height=40&width=40",
      age: 42,
      gender: "男",
      memberType: "月卡会员",
      joinDate: "2023-04-10",
      lastVisit: "2023-05-15",
      totalSpent: 1200,
      status: "新会员",
    },
    {
      id: "m004",
      name: "赵六",
      avatar: "/placeholder.svg?height=40&width=40",
      age: 31,
      gender: "女",
      memberType: "年卡会员",
      joinDate: "2022-08-20",
      lastVisit: "2023-03-10",
      totalSpent: 7500,
      status: "流失风险",
    },
    {
      id: "m005",
      name: "钱七",
      avatar: "/placeholder.svg?height=40&width=40",
      age: 26,
      gender: "女",
      memberType: "VIP会员",
      joinDate: "2022-05-12",
      lastVisit: "2023-05-19",
      totalSpent: 12000,
      status: "活跃",
    },
  ]

  // 模拟会员生命周期数据
  const lifecycleStages = [
    { stage: "获取", count: 285, percentage: 22.2, conversion: "100%" },
    { stage: "激活", count: 245, percentage: 19.1, conversion: "86%" },
    { stage: "参与", count: 210, percentage: 16.4, conversion: "86%" },
    { stage: "转化", count: 180, percentage: 14.0, conversion: "86%" },
    { stage: "保留", count: 150, percentage: 11.7, conversion: "83%" },
    { stage: "忠诚", count: 120, percentage: 9.3, conversion: "80%" },
    { stage: "推荐", count: 85, percentage: 6.6, conversion: "71%" },
  ]

  // 模拟会员健康数据
  const healthMetrics = [
    { metric: "出勤率", value: "68%", trend: "+5.2%" },
    { metric: "课程完成率", value: "82%", trend: "+3.1%" },
    { metric: "健身目标达成", value: "45%", trend: "+8.7%" },
    { metric: "平均锻炼时长", value: "65分钟", trend: "+12.3%" },
  ]

  // 模拟会员满意度数据
  const satisfactionData = [
    { level: "非常满意", percentage: 42 },
    { level: "满意", percentage: 38 },
    { level: "一般", percentage: 15 },
    { level: "不满意", percentage: 4 },
    { level: "非常不满意", percentage: 1 },
  ]

  // 处理会员细分点击
  const handleSegmentClick = (segmentId: string) => {
    setSelectedSegment(segmentId)
    setIsDetailDialogOpen(true)
  }

  // 处理会员点击
  const handleMemberClick = (memberId: string) => {
    setSelectedMember(memberId)
    // 这里可以添加打开会员详情的逻辑
  }

  // 模拟数据加载
  const refreshData = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
    }, 1500)
  }

  // 保存当前视图
  const saveCurrentView = (viewName: string) => {
    const newView = {
      id: `view_${Date.now()}`,
      name: viewName,
      isDefault: false,
    }
    setSavedViews([...savedViews, newView])
    setCurrentView(newView.id)
    setIsCustomizeDialogOpen(false)
  }

  // 处理比较组选择
  const toggleCompareGroup = (groupId: string) => {
    setCompareGroups(
      compareGroups.map((group) => (group.id === groupId ? { ...group, isSelected: !group.isSelected } : group)),
    )
  }

  return (
    <div className="space-y-6">
      {/* 顶部操作栏 */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">会员数据分析</h1>
          <p className="text-sm text-muted-foreground">深入分析会员数据，优化会员管理策略</p>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" className="gap-2" onClick={() => setIsAdvancedFilterOpen(true)}>
            <Filter className="h-4 w-4" />
            <span>高级筛选</span>
          </Button>
          <Button variant="outline" className="gap-2">
            <Calendar className="h-4 w-4" />
            <span>选择时间范围</span>
          </Button>
          <Button variant="outline" onClick={() => setIsExportDialogOpen(true)}>
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <BarChart4 className="mr-2 h-4 w-4" />
                <span>视图</span>
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuLabel>选择视图</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {savedViews.map(view => (
                <DropdownMenuItem 
                  key={view.id} 
                  className={currentView === view.id ? "bg-muted" : ""}
                  onClick={() => setCurrentView(view.id)}
                >
                  {view.name}
                  {view.isDefault && <Badge variant="outline" className="ml-2">默认</Badge>}
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setIsCustomizeDialogOpen(true)}>
                <Save className="mr-2 h-4 w-4" />
                保存当前视图
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button variant="default" onClick={refreshData} disabled={isLoading}>
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                加载中...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                刷新数据
              </>
            )}
          </Button>
        </div>
      </div>

      {/* 筛选条件 */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex flex-1 flex-wrap gap-4">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="thismonth">本月</SelectItem>
              <SelectItem value="lastmonth">上月</SelectItem>
              <SelectItem value="thisyear">今年</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>

          <Select value={memberType} onValueChange={setMemberType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="会员类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部会员</SelectItem>
              <SelectItem value="monthly">月卡会员</SelectItem>
              <SelectItem value="quarterly">季卡会员</SelectItem>
              <SelectItem value="yearly">年卡会员</SelectItem>
              <SelectItem value="vip">VIP会员</SelectItem>
            </SelectContent>
          </Select>

          <div className="relative w-full max-w-[300px]">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索会员名称、标签或属性..."
              className="pl-8"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button 
            variant={viewMode === "charts" ? "default" : "outline"} 
            size="sm" 
            className="h-9 w-9 p-0" 
            onClick={() => setViewMode("charts")}
          >
            <BarChart4 className="h-4 w-4" />
            <span className="sr-only">图表视图</span>
          </Button>
          <Button 
            variant={viewMode === "table" ? "default" : "outline"} 
            size="sm" 
            className="h-9 w-9 p-0" 
            onClick={() => setViewMode("table")}
          >
            <Layers className="h-4 w-4" />
            <span className="sr-only">表格视图</span>
          </Button>
        </div>
      </div>

      {/* 提示信息 */}
      {showTips && (
        <div className="relative rounded-lg border bg-card p-4 text-card-foreground shadow-sm">
          <div className="flex items-start gap-4">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/20">
              <Zap className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-semibold">数据分析提示</h4>
              <p className="mt-1 text-sm text-muted-foreground">
                通过会员生命周期分析，您可以了解会员从获取到忠诚的全过程转化率。数据显示，提高会员激活阶段的转化率可以显著提升整体留存率。
              </p>
              <div className="mt-2 flex items-center gap-2">
                <Button variant="outline" size="sm">
                  查看详细指南
                </Button>
                <Button variant="link" size="sm" onClick={() => setIsCompareDialogOpen(true)}>
                  比较不同会员群体
                </Button>
              </div>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 w-8 p-0" 
              onClick={() => setShowTips(false)}
            >
              <span className="sr-only">关闭</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-4 w-4"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </Button>
          </div>
        </div>
      )}

      {/* 关键指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <TooltipProvider>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总会员数</CardTitle>
              <Tooltip>
                <TooltipTrigger>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>当前所有状态的会员总数</p>
                </TooltipContent>
              </Tooltip>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,284</div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  较上期 <span className="text-green-500">+8.5%</span>
                </p>
                <Badge variant="outline">目标: 1,500</Badge>
              </div>
              <Progress value={85.6} className="mt-2 h-1.5" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">新增会员</CardTitle>
              <Tooltip>
                <TooltipTrigger>
                  <UserPlus className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>本期新注册的会员数量</p>
                </TooltipContent>
              </Tooltip>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">142</div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  较上期 <span className="text-green-500">+12.3%</span>
                </p>
                <Badge variant="outline">目标: 150</Badge>
              </div>
              <Progress value={94.7} className="mt-2 h-1.5" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">流失会员</CardTitle>
              <Tooltip>
                <TooltipTrigger>
                  <UserMinus className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>本期流失的会员数量</p>
                </TooltipContent>
              </Tooltip>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">28</div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  较上期 <span className="text-red-500">+3.2%</span>
                </p>
                <Badge variant="outline">目标: {'<'}25</Badge>
              </div>
              <Progress value={112} className="mt-2 h-1.5 bg-red-100">
                <div className="h-full bg-red-500" style={{ width: '112%' }}></div>
              </Progress>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">续费率</CardTitle>
              <Tooltip>
                <TooltipTrigger>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>会员卡到期后选择续费的比例</p>
                </TooltipContent>
              </Tooltip>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">75.8%</div>
              <div className="flex items-center justify-between">
                <p className="text-xs text-muted-foreground">
                  较上期 <span className="text-green-500">+2.5%</span>
                </p>
                <Badge variant="outline">目标: 80%</Badge>
              </div>
              <Progress value={94.8} className="mt-2 h-1.5" />
            </CardContent>
          </Card>
        </TooltipProvider>
      </div>

      {/* 会员生命周期分析 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>会员生命周期分析</CardTitle>
            <CardDescription>会员从获取到忠诚的全过程转化率</CardDescription>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 w-8 p-0" 
            onClick={() => setIsLifecycleExpanded(!isLifecycleExpanded)}
          >
            {isLifecycleExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="relative">
              <div className="flex items-center justify-between">
                {lifecycleStages.map((stage, index) => (
                  <div key={index} className="flex flex-col items-center">
                    <div className={`flex h-12 w-12 items-center justify-center rounded-full ${
                      index === 0 ? 'bg-green-100 text-green-700' :
                      index === lifecycleStages.length - 1 ? 'bg-blue-100 text-blue-700' :
                      'bg-primary/20 text-primary'
                    }`}>
                      {index === 0 ? <UserPlus className="h-5 w-5" /> :
                       index === 1 ? <Activity className="h-5 w-5" /> :
                       index === 2 ? <Clock className="h-5 w-5" /> :
                       index === 3 ? <DollarSign className="h-5 w-5" /> :
                       index === 4 ? <Heart className="h-5 w-5" /> :
                       index === 5 ? <Star className="h-5 w-5" /> :
                       <Share2 className="h-5 w-5" />}
                    </div>
                    <div className="mt-2 text-center">
                      <div className="text-sm font-medium">{stage.stage}</div>
                      <div className="text-xs text-muted-foreground">{stage.count}人</div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="absolute left-[calc(6rem-1px)] right-[calc(6rem-1px)] top-6 h-0.5 bg-muted">
                <div className="relative h-full">
                  {lifecycleStages.slice(0, -1).map((stage, index) => (
                    <div 
                      key={index} 
                      className="absolute flex h-6 items-center justify-center text-xs font-medium"
                      style={{ 
                        left: `${(index / (lifecycleStages.length - 2)) * 100}%`, 
                        transform: 'translateX(-50%) translateY(-100%)' 
                      }}
                    >
                      <Badge variant="outline">{stage.conversion}</Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {isLifecycleExpanded && (
              <div className="mt-8 space-y-6">
                <Separator />
                
                <div className="grid gap-6 md:grid-cols-2">
                  <div>
                    <h4 className="mb-4 text-sm font-semibold">各阶段会员特征</h4>
                    <div className="space-y-4">
                      <div className="rounded-lg border p-4">
                        <div className="flex items-center gap-2">
                          <UserPlus className="h-4 w-4 text-green-600" />
                          <h5 className="text-sm font-medium">获取阶段</h5>
                        </div>
                        <p className="mt-2 text-xs text-muted-foreground">
                          新注册会员，主要来源为线下门店(45%)和官网注册(30%)。平均年龄28岁，女性占比62%。
                        </p>
                      </div>
                      
                      <div className="rounded-lg border p-4">
                        <div className="flex items-center gap-2">
                          <Activity className="h-4 w-4 text-primary" />
                          <h5 className="text-sm font-medium">激活阶段</h5>
                        </div>
                        <p className="mt-2 text-xs text-muted-foreground">
                          完成首次课程体验的会员，平均访问频率为每周1-2次。主要参与团体课程，对私教课程兴趣增长中。
                        </p>
                      </div>
                      
                      <div className="rounded-lg border p-4">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-primary" />
                          <h5 className="text-sm font-medium">参与阶段</h5>
                        </div>
                        <p className="mt-2 text-xs text-muted-foreground">
                          稳定参与课程的会员，平均访问频率为每周2-3次。开始尝试多种课程类型，社交互动增加。
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="mb-4 text-sm font-semibold">阶段转化优化建议</h4>
                    <div className="space-y-4">
                      <div className="rounded-lg border p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Target className="h-4 w-4 text-primary" />
                            <h5 className="text-sm font-medium">获取到激活</h5>
                          </div>
                          <Badge>优先级高</Badge>
                        </div>
                        <p className="mt-2 text-xs text-muted-foreground">
                          为新会员提供个性化的入门指导和首次体验课程，提高首次访问满意度。
                        </p>
                        <div className="mt-2 flex items-center gap-2">
                          <Button variant="outline" size="sm">应用建议</Button>
                        </div>
                      </div>
                      
                      <div className="rounded-lg border p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Target className="h-4 w-4 text-primary" />
                            <h5 className="text-sm font-medium">参与到转化</h5>
                          </div>
                          <Badge>优先级中</Badge>
                        </div>
                        <p className="mt-2 text-xs text-muted-foreground">
                          针对参与阶段会员提供会员专属优惠和增值服务，提高消费转化率。
                        </p>
                        <div className="mt-2 flex items-center gap-2">
                          <Button variant="outline" size="sm">应用建议</Button>
                        </div>
                      </div>
                      
                      <div className="rounded-lg border p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Target className="h-4 w-4 text-primary" />
                            <h5 className="text-sm font-medium">保留到忠诚</h5>
                          </div>
                          <Badge>优先级高</Badge>
                        </div>
                        <p className="mt-2 text-xs text-muted-foreground">
                          为长期会员提供专属活动和社区参与机会，增强会员归属感和忠诚度。
                        </p>
                        <div className="mt-2 flex items-center gap-2">
                          <Button variant="outline" size="sm">应用建议</Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" className="ml-auto">
            查看详细分析
          </Button>
        </CardFooter>
      </Card>
  ;<Tabs defaultValue="overview" className="space-y-4">
    <TabsList className="grid w-full grid-cols-2 md:grid-cols-5 lg:grid-cols-7">
      <TabsTrigger value="overview">会员概览</TabsTrigger>
      <TabsTrigger value="segments">会员细分</TabsTrigger>
      <TabsTrigger value="behavior">消费行为</TabsTrigger>
      <TabsTrigger value="portrait">会员画像</TabsTrigger>
      <TabsTrigger value="health">健康数据</TabsTrigger>
      <TabsTrigger value="feedback">满意度</TabsTrigger>
      <TabsTrigger value="prediction">预测分析</TabsTrigger>
    </TabsList>

    {/* 会员概览标签内容 */}
    <TabsContent value="overview" className="space-y-4">
    {viewMode === "charts" ? (
      <>
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader className="flex items-center justify-between">
              <div>
                <CardTitle>会员增长趋势</CardTitle>
                <CardDescription>会员数量随时间变化趋势</CardDescription>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Download className="mr-2 h-4 w-4" />
                    <span>导出数据</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Share2 className="mr-2 h-4 w-4" />
                    <span>分享图表</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Maximize2 className="mr-2 h-4 w-4" />
                    <span>全屏查看</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardHeader>
            <CardContent className="h-[300px]">
              <div className="h-full w-full rounded-md border bg-muted flex items-center justify-center">
                <p className="text-muted-foreground">会员增长趋势图表</p>
              </div>
            </CardContent>
            <CardFooter className="flex items-center justify-between">
              <Select defaultValue="total">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择指标" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="total">总会员数</SelectItem>
                  <SelectItem value="new">新增会员</SelectItem>
                  <SelectItem value="churn">流失会员</SelectItem>
                  <SelectItem value="renewal">续费率</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Calendar className="mr-2 h-4 w-4" />
                  选择日期
                </Button>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  筛选
                </Button>
              </div>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader className="flex items-center justify-between">
              <div>
                <CardTitle>会员来源分布</CardTitle>
                <CardDescription>会员获取渠道分布</CardDescription>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Download className="mr-2 h-4 w-4" />
                    <span>导出数据</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Share2 className="mr-2 h-4 w-4" />
                    <span>分享图表</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Maximize2 className="mr-2 h-4 w-4" />
                    <span>全屏查看</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {memberSources.map((source, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`h-3 w-3 rounded-full ${
                          index === 0 ? 'bg-green-500' :
                          index === 1 ? 'bg-blue-500' :
                          index === 2 ? 'bg-purple-500' :
                          index === 3 ? 'bg-yellow-500' :
                          'bg-red-500'
                        }`}></div>
                        <span>{source.source}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{source.count}人</span>
                        <span className="text-muted-foreground">{source.percentage}%</span>
                      </div>
                    </div>
                    <div className="relative">
                      <Progress value={source.percentage} className="h-2" />
                      <div className="absolute -right-4 -top-1 text-xs">
                        {index === 0 && <Badge variant="outline" className="bg-green-50 text-green-700">+5.2%</Badge>}
                        {index === 1 && <Badge variant="outline" className="bg-blue-50 text-blue-700">+3.8%</Badge>}
                        {index === 2 && <Badge variant="outline" className="bg-purple-50 text-purple-700">+12.5%</Badge>}
                        {index === 3 && <Badge variant="outline" className="bg-yellow-50 text-yellow-700">-2.1%</Badge>}
                        {index === 4 && <Badge variant="outline" className="bg-red-50 text-red-700">-1.5%</Badge>}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="ml-auto">
                查看详细数据
              </Button>
            </CardFooter>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex items-center justify-between">
            <div>
              <CardTitle>会员类型分布</CardTitle>
              <CardDescription>不同会员卡类型的分布情况</CardDescription>
            </div>
            <Tabs defaultValue="pie" className="w-[200px]">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="pie">饼图</TabsTrigger>
                <TabsTrigger value="bar">柱状图</TabsTrigger>
                <TabsTrigger value="data">数据</TabsTrigger>
              </TabsList>
            </Tabs>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors cursor-pointer">
                <div className="text-2xl font-bold">485</div>
                <div className="text-sm text-muted-foreground mt-1">月卡会员</div>
                <Badge className="mt-2">37.8%</Badge>
                <div className="text-xs mt-2 text-green-500">+5.2%</div>
                <div className="mt-2 text-xs text-muted-foreground">平均消费: ¥580/月</div>
              </div>
              <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors cursor-pointer">
                <div className="text-2xl font-bold">320</div>
                <div className="text-sm text-muted-foreground mt-1">季卡会员</div>
                <Badge className="mt-2">24.9%</Badge>
                <div className="text-xs mt-2 text-green-500">+3.8%</div>
                <div className="mt-2 text-xs text-muted-foreground">平均消费: ¥520/月</div>
              </div>
              <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors cursor-pointer">
                <div className="text-2xl font-bold">245</div>
                <div className="text-sm text-muted-foreground mt-1">年卡会员</div>
                <Badge className="mt-2">19.1%</Badge>
                <div className="text-xs mt-2 text-green-500">+2.5%</div>
                <div className="mt-2 text-xs text-muted-foreground">平均消费: ¥450/月</div>
              </div>
              <div className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg hover:bg-muted/80 transition-colors cursor-pointer">
                <div className="text-2xl font-bold">234</div>
                <div className="text-sm text-muted-foreground mt-1">VIP会员</div>
                <Badge className="mt-2">18.2%</Badge>
                <div className="text-xs mt-2 text-green-500">+8.3%</div>
                <div className="mt-2 text-xs text-muted-foreground">平均消费: ¥1,200/月</div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              总会员数: 1,284人 | 较上月: <span className="text-green-500">+8.5%</span>
            </div>
            <Button variant="outline" size="sm">
              查看详细数据
            </Button>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>会员地域分布</CardTitle>
            <CardDescription>会员所在地区分布情况</CardDescription>
          </CardHeader>
          <CardContent className="h-[400px]">
            <div className="h-full w-full rounded-md border bg-muted flex items-center justify-center">
              <p className="text-muted-foreground">会员地域分布地图</p>
            </div>
          </CardContent>
          <CardFooter className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-red-500"></div>
                <span className="text-sm">高密度</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                <span className="text-sm">中密度</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-green-500"></div>
                <span className="text-sm">低密度</span>
              </div>
            </div>
            <Button variant="outline" size="sm">
              查看详细数据
            </Button>
          </CardFooter>
        </Card>
      </>
    ) : (
      <Card>
        <CardHeader className="flex items-center justify-between">
          <div>
            <CardTitle>会员列表</CardTitle>
            <CardDescription>所有会员的详细信息</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              导出
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              筛选
            </Button>
            <Button variant="outline" size="sm">
              <SlidersHorizontal className="mr-2 h-4 w-4" />
              列设置
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">
                    <Checkbox />
                  </TableHead>
                  <TableHead>会员信息</TableHead>
                  <TableHead>会员类型</TableHead>
                  <TableHead>注册日期</TableHead>
                  <TableHead>最近访问</TableHead>
                  <TableHead>消费金额</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {memberList.map((member) => (
                  <TableRow key={member.id} onClick={() => handleMemberClick(member.id)} className="cursor-pointer">
                    <TableCell>
                      <Checkbox onClick={(e) => e.stopPropagation()} />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={member.avatar} alt={member.name} />
                          <AvatarFallback>{member.name.slice(0, 1)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {member.age}岁 | {member.gender}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{member.memberType}</TableCell>
                    <TableCell>{member.joinDate}</TableCell>
                    <TableCell>{member.lastVisit}</TableCell>
                    <TableCell>¥{member.totalSpent}</TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          member.status === "活跃"
                            ? "default"
                            : member.status === "新会员"
                              ? "outline"
                              : "destructive"
                        }
                      >
                        {member.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            // 查看详情逻辑
                          }}>
                            <User className="mr-2 h-4 w-4" />
                            <span>查看详情</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            // 编辑会员逻辑
                          }}>
                            <Edit className="mr-2 h-4 w-4" />
                            <span>编辑会员</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            // 发送消息逻辑
                          }}>
                            <MessageSquare className="mr-2 h-4 w-4" />
                            <span>发送消息</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            // 停用会员逻辑
                          }} className="text-red-600">
                            <Ban className="mr-2 h-4 w-4" />
                            <span>停用会员</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <div className="text-xs text-muted-foreground">显示 1-5 条，共 1,284 条</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" className="w-8 p-0">1</Button>
            <Button variant="outline" size="sm" className="w-8 p-0">2</Button>
            <Button variant="outline" size="sm" className="w-8 p-0">3</Button>
            <Button variant="outline" size="sm" className="w-8 p-0">...</Button>
            <Button variant="outline" size="sm" className="w-8 p-0">50</Button>
            <Button variant="outline" size="sm">
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>
    )}
  </TabsContent>

    {/* 会员细分标签内容 */}
    <TabsContent value="segments" className="space-y-4">
    <Card>
      <CardHeader className="flex items-center justify-between">
        <div>
          <CardTitle>会员细分分析</CardTitle>
          <CardDescription>基于活跃度的会员细分</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Plus className="mr-2 h-4 w-4" />
            创建细分
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新数据
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {memberSegments.map((segment) => (
            <div
              key={segment.id}
              className="flex flex-col items-center justify-center p-6 bg-muted rounded-lg cursor-pointer hover:bg-muted/80 transition-colors"
              onClick={() => handleSegmentClick(segment.id)}
            >
              <div className={`h-12 w-12 rounded-full flex items-center justify-center mb-3 ${
                segment.id === 'new' ? 'bg-green-100 text-green-700' :
                segment.id === 'active' ? 'bg-blue-100 text-blue-700' :
                segment.id === 'risk' ? 'bg-red-100 text-red-700' :
                'bg-yellow-100 text-yellow-700'
              }`}>
                {segment.id === 'new' && <UserPlus className="h-6 w-6" />}
                {segment.id === 'active' && <Activity className="h-6 w-6" />}
                {segment.id === 'risk' && <AlertTriangle className="h-6 w-6" />}
                {segment.id === 'inactive' && <UserMinus className="h-6 w-6" />}
              </div>
              <div className="text-2xl font-bold">{segment.count}</div>
              <div className="text-sm text-muted-foreground mt-1">{segment.name}</div>
              <Badge className="mt-2">{segment.percentage}%</Badge>
              <div className={`text-xs mt-2 ${segment.growth.startsWith("+") ? "text-green-500" : "text-red-500"}`}>
                {segment.growth}
              </div>
              <div className="mt-3 flex items-center gap-2">
                <Button variant="outline" size="sm" className="h-7 px-2 text-xs" onClick={(e) => {
                  e.stopPropagation();
                  // 查看详情逻辑
                }}>
                  查看详情
                </Button>
                <Button variant="ghost" size="sm" className="h-7 px-2 text-xs" onClick={(e) => {
                  e.stopPropagation();
                  // 导出数据逻辑
                }}>
                  导出
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          上次更新: 2023-05-20 15:30
        </div>
        <Button variant="outline" size="sm">
          自定义细分
        </Button>
      </CardFooter>
    </Card>

    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader className="flex items-center justify-between">
          <div>
            <CardTitle>会员价值分布</CardTitle>
            <CardDescription>基于消费金额的会员价值分布</CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                <span>导出数据</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share2 className="mr-2 h-4 w-4" />
                <span>分享图表</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Maximize2 className="mr-2 h-4 w-4" />
                <span>全屏查看</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span>高价值会员 ({">"}¥5000)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">185人</span>
                  <span className="text-muted-foreground">14.4%</span>
                </div>
              </div>
              <div className="relative">
                <Progress value={14.4} className="h-2" />
                <div className="absolute -right-4 -top-1 text-xs">
                  <Badge variant="outline" className="bg-green-50 text-green-700">+3.2%</Badge>
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                平均消费: ¥8,500 | 续费率: 92%
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                  <span>中高价值会员 (¥3000-5000)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">320人</span>
                  <span className="text-muted-foreground">24.9%</span>
                </div>
              </div>
              <div className="relative">
                <Progress value={24.9} className="h-2" />
                <div className="absolute -right-4 -top-1 text-xs">
                  <Badge variant="outline" className="bg-green-50 text-green-700">+2.8%</Badge>
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                平均消费: ¥3,850 | 续费率: 85%
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                  <span>中价值会员 (¥1000-3000)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">485人</span>
                  <span className="text-muted-foreground">37.8%</span>
                </div>
              </div>
              <div className="relative">
                <Progress value={37.8} className="h-2" />
                <div className="absolute -right-4 -top-1 text-xs">
                  <Badge variant="outline" className="bg-green-50 text-green-700">+1.5%</Badge>
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                平均消费: ¥1,850 | 续费率: 75%
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-red-500"></div>
                  <span>低价值会员 ({"<"}¥1000)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">294人</span>
                  <span className="text-muted-foreground">22.9%</span>
                </div>
              </div>
              <div className="relative">
                <Progress value={22.9} className="h-2" />
                <div className="absolute -right-4 -top-1 text-xs">
                  <Badge variant="outline" className="bg-red-50 text-red-700">-1.2%</Badge>
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                平均消费: ¥650 | 续费率: 45%
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" className="ml-auto">
            查看详细数据
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader className="flex items-center justify-between">
          <div>
            <CardTitle>会员活跃度分布</CardTitle>
            <CardDescription>基于访问频率的会员活跃度分布</CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                <span>导出数据</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share2 className="mr-2 h-4 w-4" />
                <span>分享图表</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Maximize2 className="mr-2 h-4 w-4" />
                <span>全屏查看</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-green-500"></div>
                  <span>高频活跃 (每周3次以上)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">245人</span>
                  <span className="text-muted-foreground">19.1%</span>
                </div>
              </div>
              <div className="relative">
                <Progress value={19.1} className="h-2" />
                <div className="absolute -right-4 -top-1 text-xs">
                  <Badge variant="outline" className="bg-green-50 text-green-700">+4.5%</Badge>
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                平均消费: ¥2,850 | 续费率: 95%
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                  <span>中频活跃 (每周1-2次)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">420人</span>
                  <span className="text-muted-foreground">32.7%</span>
                </div>
              </div>
              <div className="relative">
                <Progress value={32.7} className="h-2" />
                <div className="absolute -right-4 -top-1 text-xs">
                  <Badge variant="outline" className="bg-green-50 text-green-700">+2.3%</Badge>
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                平均消费: ¥1,650 | 续费率: 82%
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                  <span>低频活跃 (每月1-3次)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">320人</span>
                  <span className="text-muted-foreground">24.9%</span>
                </div>
              </div>
              <div className="relative">
                <Progress value={24.9} className="h-2" />
                <div className="absolute -right-4 -top-1 text-xs">
                  <Badge variant="outline" className="bg-yellow-50 text-yellow-700">+0.8%</Badge>
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                平均消费: ¥980 | 续费率: 65%
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full bg-red-500"></div>
                  <span>非活跃 (超过1个月未访问)</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">299人</span>
                  <span className="text-muted-foreground">23.3%</span>
                </div>
              </div>
              <div className="relative">
                <Progress value={23.3} className="h-2" />
                <div className="absolute -right-4 -top-1 text-xs">
                  <Badge variant="outline" className="bg-red-50 text-red-700">+1.5%</Badge>
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                平均消费: ¥350 | 续费率: 35%
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" className="ml-auto">
            查看详细数据
          </Button>
        </CardFooter>
      </Card>
    </div>
    
    <Card>
      <CardHeader className="flex items-center justify-between">
        <div>
          <CardTitle>会员细分趋势</CardTitle>
          <CardDescription>各会员细分随时间变化趋势</CardDescription>
        </div>
        <Select defaultValue="6months">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="选择时间范围" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="3months">最近3个月</SelectItem>
            <SelectItem value="6months">最近6个月</SelectItem>
            <SelectItem value="12months">最近12个月</SelectItem>
            <SelectItem value="custom">自定义范围</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="h-[400px]">
        <div className="h-full w-full rounded-md border bg-muted flex items-center justify-center">
          <p className="text-muted-foreground">会员细分趋势图表</p>
        </div>
      </CardContent>
      <CardFooter className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-green-500"></div>
            <span className="text-sm">新会员</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-blue-500"></div>
            <span className="text-sm">活跃会员</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-red-500"></div>
            <span className="text-sm">流失风险</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
            <span className="text-sm">非活跃会员</span>
          </div>
        </div>
        <Button variant="outline" size="sm">
          导出趋势数据
        </Button>
      </CardFooter>
    </Card>
    
    <Card>
      <CardHeader className="flex items-center justify-between">
        <div>
          <CardTitle>会员转化漏斗</CardTitle>
          <CardDescription>会员从获取到忠诚的转化漏斗</CardDescription>
        </div>
        <Button variant="outline" size="sm">
          <Settings className="mr-2 h-4 w-4" />
          漏斗设置
        </Button>
      </CardHeader>
      <CardContent className="h-[400px]">
        <div className="h-full w-full rounded-md border bg-muted flex items-center justify-center">
          <p className="text-muted-foreground">会员转化漏斗图表</p>
        </div>
      </CardContent>
      <CardFooter className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          转化率: 获取→激活 (86%) → 参与 (86%) → 转化 (86%) → 保留 (83%) → 忠诚 (80%) → 推荐 (71%)
        </div>
        <Button variant="outline" size="sm">
          查看详细分析
        </Button>
      </CardFooter>
    </Card>
  </TabsContent>

    {/* 消费行为标签内容 */}
    <TabsContent value="behavior" className="space-y-4">
    <Card>
      <CardHeader className="flex items-center justify-between">
        <div>
          <CardTitle>会员消费行为分析</CardTitle>
          <CardDescription>会员消费偏好和行为分析</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <Select defaultValue="30days">
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="custom">自定义范围</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {memberBehaviors.map((behavior, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className={`h-3 w-3 rounded-full ${
                    index === 0 ? 'bg-green-500' :
                    index === 1 ? 'bg-blue-500' :
                    index === 2 ? 'bg-purple-500' :
                    index === 3 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}></div>
                  <span>{behavior.behavior}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">{behavior.count}人</span>
                  <span className="text-muted-foreground">{behavior.percentage}%</span>
                </div>
              </div>
              <div className="relative">
                <Progress value={behavior.percentage} className="h-2" />
                <div className="absolute -right-4 -top-1 text-xs">
                  {index === 0 && <Badge variant="outline" className="bg-green-50 text-green-700">+5.2%</Badge>}
                  {index === 1 && <Badge variant="outline" className="bg-blue-50 text-blue-700">+3.8%</Badge>}
                  {index === 2 && <Badge variant="outline" className="bg-purple-50 text-purple-700">-1.5%</Badge>}
                  {index === 3 && <Badge variant="outline" className="bg-yellow-50 text-yellow-700">+2.1%</Badge>}
                  {index === 4 && <Badge variant="outline" className="bg-red-50 text-red-700">+8.5%</Badge>}
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                {index === 0 && "平均课程时长: 60分钟 | 平均消费: ¥150/次 | 满意度: 4.8/5.0"}
                {index === 1 && "平均课程时长: 45分钟 | 平均消费: ¥350/次 | 满意度: 4.9/5.0"}
                {index === 2 && "平均订单金额: ¥280 | 复购率: 35% | 满意度: 4.5/5.0"}
                {index === 3 && "平均使用时长: 90分钟 | 平均消费: ¥120/次 | 满意度: 4.6/5.0"}
                {index === 4 && "平均参与时长: 120分钟 | 参与频率: 每月1.5次 | 满意度: 4.7/5.0"}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          数据更新时间: 2023-05-20 15:30
        </div>
        <Button variant="outline" size="sm">
          查看详细数据
        </Button>
      </CardFooter>
    </Card>

    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader className="flex items-center justify-between">
          <div>
            <CardTitle>课程偏好分析</CardTitle>
            <CardDescription>会员课程类型偏好分析</CardDescription>
          </div>
          <Tabs defaultValue="pie" className="w-[200px]">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="pie">饼图</TabsTrigger>
              <TabsTrigger value="bar">柱状图</TabsTrigger>
              <TabsTrigger value="data">数据</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        <CardContent className="h-[300px]">
          <div className="h-full w-full rounded-md border bg-muted flex items-center justify-center">
            <p className="text-muted-foreground">课程偏好分析图表</p>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-green-500"></div>
              <span className="text-sm">瑜伽课 (42%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-blue-500"></div>
              <span className="text-sm">普拉提 (28%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-purple-500"></div>
              <span className="text-sm">舞蹈课 (15%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
              <span className="text-sm">其他 (15%)</span>
            </div>
          </div>
          <Button variant="outline" size="sm">
            查看详细数据
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader className="flex items-center justify-between">
          <div>
            <CardTitle>时段偏好分析</CardTitle>
            <CardDescription>会员访问时段偏好分析</CardDescription>
          </div>
          <Select defaultValue="weekday">
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="选择日期类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="weekday">工作日</SelectItem>
              <SelectItem value="weekend">周末</SelectItem>
              <SelectItem value="all">全部日期</SelectItem>
            </SelectContent>
          </Select>
        </CardHeader>
        <CardContent className="h-[300px]">
          <div className="h-full w-full rounded-md border bg-muted flex items-center justify-center">
            <p className="text-muted-foreground">时段偏好分析图表</p>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            高峰时段: 早上 (7:00-9:00), 晚上 (18:00-21:00)
          </div>
          <Button variant="outline" size="sm">
            查看详细数据
          </Button>
        </CardFooter>
      </Card>
    </div>

    <Card>
      <CardHeader className="flex items-center justify-between">
        <div>
          <CardTitle>会员消费路径分析</CardTitle>
          <CardDescription>会员消费行为路径分析</CardDescription>
        </div>
        <Button variant="outline" size="sm">
          <Settings className="mr-2 h-4 w-4" />
          路径设置
        </Button>
      </CardHeader>
      <CardContent className="h-[400px]">
        <div className="h-full w-full rounded-md border bg-muted flex items-center justify-center">
          <p className="text-muted-foreground">会员消费路径分析图表</p>
        </div>
      </CardContent>
      <CardFooter className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          主要路径: 浏览课程 → 预约体验课 → 购买课程包 → 定期参与 → 购买周边产品
        </div>
        <Button variant="outline" size="sm">
          查看详细分析
        </Button>
      </CardFooter>
    </Card>
    
    <Card>
      <CardHeader className="flex items-center justify-between">
        <div>
          <CardTitle>消费频率分析</CardTitle>
          <CardDescription>会员消费频率和周期分析</CardDescription>
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择会员类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部会员</SelectItem>
            <SelectItem value="monthly">月卡会员</SelectItem>
            <SelectItem value="quarterly">季卡会员</SelectItem>
            <SelectItem value="yearly">年卡会员</SelectItem>
            <SelectItem value="vip">VIP会员</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="rounded-lg border p-4">
            <div className="flex items-center justify-between">
\

\

\

