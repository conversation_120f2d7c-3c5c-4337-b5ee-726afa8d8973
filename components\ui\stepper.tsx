"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface StepProps {
  title: string
  description?: string
  completed?: boolean
  current?: boolean
}

export function Step({
  title,
  description,
  completed,
  current
}: StepProps) {
  return (
    <div className="flex items-center">
      <div
        className={cn(
          "flex h-8 w-8 items-center justify-center rounded-full border text-xs font-semibold",
          completed ? "border-primary bg-primary text-primary-foreground" : 
          current ? "border-primary text-primary" : 
          "border-muted-foreground/30 text-muted-foreground"
        )}
      >
        {completed ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-4 w-4"
          >
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        ) : (
          <span>{completed ? "✓" : current ? "·" : ""}</span>
        )}
      </div>
      <div className="ml-3">
        <p className={cn(
          "text-sm font-medium", 
          current ? "text-foreground" : 
          completed ? "text-foreground" : 
          "text-muted-foreground"
        )}>
          {title}
        </p>
        {description && (
          <p className="text-xs text-muted-foreground">
            {description}
          </p>
        )}
      </div>
    </div>
  )
}

interface StepperProps {
  currentStep: number
  children: React.ReactNode
  className?: string
}

export function Stepper({
  currentStep,
  children,
  className
}: StepperProps) {
  const steps = React.Children.toArray(children)

  return (
    <div className={cn("space-y-6", className)}>
      <div className="flex flex-col space-y-6 sm:flex-row sm:space-y-0 sm:space-x-6">
        {steps.map((step, index) => {
          const isCompleted = index < currentStep
          const isCurrent = index === currentStep

          if (React.isValidElement<StepProps>(step)) {
            return React.cloneElement(step, {
              ...step.props,
              key: index,
              completed: isCompleted,
              current: isCurrent,
            })
          }

          return null
        })}
      </div>
      <div className="flex w-full items-center gap-1">
        {steps.map((_, index) => (
          <div
            key={index}
            className={cn(
              "h-1.5 w-full rounded-full",
              index <= currentStep ? "bg-primary" : "bg-muted"
            )}
          />
        ))}
      </div>
    </div>
  )
} 