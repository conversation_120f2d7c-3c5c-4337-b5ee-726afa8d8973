"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Target } from "lucide-react"

// 目标类型定义
export type TargetType = "bookings" | "activeMembers" | "revenue" | "renewalRate"

// 目标数据接口
export interface TargetData {
  bookings: number
  activeMembers: number
  revenue: number
  renewalRate: number
}

// 目标周期类型
export type TargetPeriod = "daily" | "weekly" | "monthly" | "quarterly" | "yearly"

// 组件属性接口
interface SetTargetDialogProps {
  currentTargets: {
    bookings: { target: number | string }
    activeMembers: { target: number | string }
    revenue: { target: string }
    renewalRate: { target: string }
  }
  onSaveTargets: (targets: TargetData, period: TargetPeriod) => void
}

export function SetTargetDialog({ currentTargets, onSaveTargets }: SetTargetDialogProps) {
  // 状态管理
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<TargetType>("bookings")
  const [targetPeriod, setTargetPeriod] = useState<TargetPeriod>("monthly")
  
  // 解析当前目标值
  const parseCurrentTarget = (value: string | number): number => {
    if (typeof value === 'number') return value
    
    // 处理带有货币符号的字符串，如 "¥350,000"
    if (typeof value === 'string' && value.includes('¥')) {
      return Number(value.replace('¥', '').replace(/,/g, ''))
    }
    
    // 处理带有百分号的字符串，如 "95%"
    if (typeof value === 'string' && value.includes('%')) {
      return Number(value.replace('%', ''))
    }
    
    return Number(value)
  }
  
  // 目标值状态
  const [targets, setTargets] = useState<TargetData>({
    bookings: parseCurrentTarget(currentTargets.bookings.target),
    activeMembers: parseCurrentTarget(currentTargets.activeMembers.target),
    revenue: parseCurrentTarget(currentTargets.revenue.target),
    renewalRate: parseCurrentTarget(currentTargets.renewalRate.target)
  })

  // 处理目标值变更
  const handleTargetChange = (type: TargetType, value: string) => {
    setTargets(prev => ({
      ...prev,
      [type]: type === 'renewalRate' ? Number(value) : Number(value)
    }))
  }

  // 处理保存
  const handleSave = () => {
    onSaveTargets(targets, targetPeriod)
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Target className="h-4 w-4 mr-2" />
          设置目标
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>设置业务目标</DialogTitle>
          <DialogDescription>
            为您的业务设定明确的目标，帮助团队更好地执行和跟踪进度。
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="mb-4">
            <Label htmlFor="target-period">目标周期</Label>
            <Select value={targetPeriod} onValueChange={(value: TargetPeriod) => setTargetPeriod(value)}>
              <SelectTrigger id="target-period" className="mt-1">
                <SelectValue placeholder="选择目标周期" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">每日目标</SelectItem>
                <SelectItem value="weekly">每周目标</SelectItem>
                <SelectItem value="monthly">每月目标</SelectItem>
                <SelectItem value="quarterly">季度目标</SelectItem>
                <SelectItem value="yearly">年度目标</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Tabs value={activeTab} onValueChange={(value: TargetType) => setActiveTab(value)}>
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="bookings">预约数量</TabsTrigger>
              <TabsTrigger value="activeMembers">活跃会员</TabsTrigger>
              <TabsTrigger value="revenue">营业收入</TabsTrigger>
              <TabsTrigger value="renewalRate">续费率</TabsTrigger>
            </TabsList>
            
            <TabsContent value="bookings" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="bookings-target">预约数量目标</Label>
                <Input
                  id="bookings-target"
                  type="number"
                  value={targets.bookings}
                  onChange={(e) => handleTargetChange('bookings', e.target.value)}
                  placeholder="例如：150"
                />
                <p className="text-xs text-muted-foreground">
                  当前目标：{currentTargets.bookings.target} 人次
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="activeMembers" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="members-target">活跃会员目标</Label>
                <Input
                  id="members-target"
                  type="number"
                  value={targets.activeMembers}
                  onChange={(e) => handleTargetChange('activeMembers', e.target.value)}
                  placeholder="例如：600"
                />
                <p className="text-xs text-muted-foreground">
                  当前目标：{currentTargets.activeMembers.target} 位会员
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="revenue" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="revenue-target">营业收入目标 (¥)</Label>
                <Input
                  id="revenue-target"
                  type="number"
                  value={targets.revenue}
                  onChange={(e) => handleTargetChange('revenue', e.target.value)}
                  placeholder="例如：350000"
                />
                <p className="text-xs text-muted-foreground">
                  当前目标：{currentTargets.revenue.target}
                </p>
              </div>
            </TabsContent>
            
            <TabsContent value="renewalRate" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="renewal-target">续费率目标 (%)</Label>
                <Input
                  id="renewal-target"
                  type="number"
                  min="0"
                  max="100"
                  value={targets.renewalRate}
                  onChange={(e) => handleTargetChange('renewalRate', e.target.value)}
                  placeholder="例如：95"
                />
                <p className="text-xs text-muted-foreground">
                  当前目标：{currentTargets.renewalRate.target}
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>取消</Button>
          <Button onClick={handleSave}>保存目标</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
