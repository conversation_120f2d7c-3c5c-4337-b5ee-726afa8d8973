"use client"

import { useState } from "react"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"
import { FileUploader } from "@/components/file-uploader"
import { Pen, Upload, CheckCircle } from "lucide-react"

// 表单验证模式
const uploadFormSchema = z.object({
  name: z.string().min(1, {
    message: "请输入签章名称",
  }),
  signatureImage: z.any().refine((file) => file && file.length > 0, {
    message: "请上传签章图片",
  }),
})

// 手写签名表单验证模式
const drawFormSchema = z.object({
  name: z.string().min(1, {
    message: "请输入签章名称",
  }),
  signatureData: z.string().min(1, {
    message: "请绘制签名",
  }),
})

interface SignatureUploadDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onUploadSuccess?: () => void
}

export function SignatureUploadDialog({
  open,
  onOpenChange,
  onUploadSuccess,
}: SignatureUploadDialogProps) {
  const [activeTab, setActiveTab] = useState("upload")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [signatureImage, setSignatureImage] = useState<File | null>(null)
  const [signatureData, setSignatureData] = useState<string>("")
  const [canvasRef, setCanvasRef] = useState<HTMLCanvasElement | null>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [lastX, setLastX] = useState(0)
  const [lastY, setLastY] = useState(0)

  // 初始化上传表单
  const uploadForm = useForm<z.infer<typeof uploadFormSchema>>({
    resolver: zodResolver(uploadFormSchema),
    defaultValues: {
      name: "",
    },
  })

  // 初始化手写表单
  const drawForm = useForm<z.infer<typeof drawFormSchema>>({
    resolver: zodResolver(drawFormSchema),
    defaultValues: {
      name: "",
      signatureData: "",
    },
  })

  // 处理签章图片上传
  const handleSignatureImageUpload = (files: File[]) => {
    if (files.length > 0) {
      setSignatureImage(files[0])
      uploadForm.setValue("signatureImage", files)
    }
  }

  // 初始化画布
  const initCanvas = (canvas: HTMLCanvasElement | null) => {
    if (!canvas) return
    
    setCanvasRef(canvas)
    const ctx = canvas.getContext('2d')
    if (ctx) {
      ctx.fillStyle = 'white'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      ctx.lineJoin = 'round'
      ctx.lineCap = 'round'
      ctx.lineWidth = 3
      ctx.strokeStyle = 'black'
    }
  }

  // 开始绘制
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef) return
    
    const rect = canvasRef.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    
    setIsDrawing(true)
    setLastX(x)
    setLastY(y)
  }

  // 绘制
  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !canvasRef) return
    
    const rect = canvasRef.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    
    const ctx = canvasRef.getContext('2d')
    if (ctx) {
      ctx.beginPath()
      ctx.moveTo(lastX, lastY)
      ctx.lineTo(x, y)
      ctx.stroke()
    }
    
    setLastX(x)
    setLastY(y)
  }

  // 结束绘制
  const endDrawing = () => {
    setIsDrawing(false)
    
    if (canvasRef) {
      const signatureDataUrl = canvasRef.toDataURL('image/png')
      setSignatureData(signatureDataUrl)
      drawForm.setValue("signatureData", signatureDataUrl)
    }
  }

  // 清除画布
  const clearCanvas = () => {
    if (!canvasRef) return
    
    const ctx = canvasRef.getContext('2d')
    if (ctx) {
      ctx.fillStyle = 'white'
      ctx.fillRect(0, 0, canvasRef.width, canvasRef.height)
    }
    
    setSignatureData("")
    drawForm.setValue("signatureData", "")
  }

  // 提交上传表单
  const onUploadSubmit = async (values: z.infer<typeof uploadFormSchema>) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 创建FormData对象
      const formData = new FormData()
      formData.append("name", values.name)
      
      if (signatureImage) {
        formData.append("signatureImage", signatureImage)
      }
      
      console.log("上传签章:", formData)
      
      // 显示成功提示
      toast({
        title: "上传成功",
        description: `签章 ${values.name} 已成功上传`,
      })
      
      // 调用成功回调
      if (onUploadSuccess) {
        onUploadSuccess()
      }
      
      // 重置表单
      uploadForm.reset()
      setSignatureImage(null)
      
      // 关闭对话框
      onOpenChange(false)
    } catch (error) {
      console.error("上传失败:", error)
      toast({
        title: "上传失败",
        description: "签章上传过程中发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 提交手写表单
  const onDrawSubmit = async (values: z.infer<typeof drawFormSchema>) => {
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 创建FormData对象
      const formData = new FormData()
      formData.append("name", values.name)
      formData.append("signatureData", values.signatureData)
      
      console.log("保存手写签名:", formData)
      
      // 显示成功提示
      toast({
        title: "保存成功",
        description: `签名 ${values.name} 已成功保存`,
      })
      
      // 调用成功回调
      if (onUploadSuccess) {
        onUploadSuccess()
      }
      
      // 重置表单
      drawForm.reset()
      clearCanvas()
      
      // 关闭对话框
      onOpenChange(false)
    } catch (error) {
      console.error("保存失败:", error)
      toast({
        title: "保存失败",
        description: "签名保存过程中发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>添加电子签章</DialogTitle>
          <DialogDescription>
            上传或绘制您的电子签章，用于合同签署
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">上传签章</TabsTrigger>
            <TabsTrigger value="draw">手写签名</TabsTrigger>
          </TabsList>
          
          <TabsContent value="upload" className="space-y-4 mt-4">
            <Form {...uploadForm}>
              <form onSubmit={uploadForm.handleSubmit(onUploadSubmit)} className="space-y-6">
                <FormField
                  control={uploadForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>签章名称</FormLabel>
                      <FormControl>
                        <Input placeholder="输入签章名称" {...field} />
                      </FormControl>
                      <FormDescription>
                        为您的签章取一个易于识别的名称
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={uploadForm.control}
                  name="signatureImage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>签章图片</FormLabel>
                      <FormControl>
                        <FileUploader
                          onFilesUploaded={handleSignatureImageUpload}
                          maxFiles={1}
                          maxSize={2 * 1024 * 1024} // 2MB
                          accept=".jpg,.jpeg,.png"
                        />
                      </FormControl>
                      <FormDescription>
                        上传签章图片，支持JPG、PNG格式，最大2MB，建议使用透明背景
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                  >
                    取消
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <span className="animate-spin mr-2">⏳</span>
                        上传中...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        上传签章
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>
          
          <TabsContent value="draw" className="space-y-4 mt-4">
            <Form {...drawForm}>
              <form onSubmit={drawForm.handleSubmit(onDrawSubmit)} className="space-y-6">
                <FormField
                  control={drawForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>签名名称</FormLabel>
                      <FormControl>
                        <Input placeholder="输入签名名称" {...field} />
                      </FormControl>
                      <FormDescription>
                        为您的手写签名取一个易于识别的名称
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={drawForm.control}
                  name="signatureData"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>手写签名</FormLabel>
                      <FormControl>
                        <div className="space-y-2">
                          <div className="border rounded-md overflow-hidden">
                            <canvas
                              ref={initCanvas}
                              width={450}
                              height={200}
                              className="bg-white cursor-crosshair w-full"
                              onMouseDown={startDrawing}
                              onMouseMove={draw}
                              onMouseUp={endDrawing}
                              onMouseLeave={endDrawing}
                            />
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={clearCanvas}
                          >
                            清除
                          </Button>
                        </div>
                      </FormControl>
                      <FormDescription>
                        在上方区域绘制您的签名
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => onOpenChange(false)}
                  >
                    取消
                  </Button>
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (
                      <>
                        <span className="animate-spin mr-2">⏳</span>
                        保存中...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="mr-2 h-4 w-4" />
                        保存签名
                      </>
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
