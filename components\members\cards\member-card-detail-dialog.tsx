"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar, Clock, DollarSign, Users, Pencil, Tag, BarChart, Settings, ShoppingCart, BookOpen } from "lucide-react"
import { ConsumptionRecords } from "./consumption-records"
import { SalesTrendChart } from "./sales-trend-chart"
import { PaymentMethodChart } from "./payment-method-chart"
import { SalesRecords } from "./sales-records"
import { MemberStatistics } from "./member-statistics"
import { GenderDistributionChart } from "./gender-distribution-chart"
import { AgeDistributionChart } from "./age-distribution-chart"
import { CardMembers } from "./card-members"

interface MemberCardDetailDialogProps {
  card: any
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function MemberCardDetailDialog({ card, open, onOpenChange }: MemberCardDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("overview")

  // 模拟会员卡持有者数据
  const cardHolders = [
    {
      id: "M001",
      name: "张三",
      avatar: "/placeholder.svg?height=32&width=32",
      purchaseDate: "2023-01-15",
      expiryDate: "2024-01-14",
      remaining: "无限次",
      status: "active",
    },
    {
      id: "M002",
      name: "李四",
      avatar: "/placeholder.svg?height=32&width=32",
      purchaseDate: "2023-02-20",
      expiryDate: "2024-02-19",
      remaining: "无限次",
      status: "active",
    },
    {
      id: "M003",
      name: "王五",
      avatar: "/placeholder.svg?height=32&width=32",
      purchaseDate: "2023-03-10",
      expiryDate: "2024-03-09",
      remaining: "无限次",
      status: "active",
    },
  ]

  // 模拟销售记录数据
  const salesRecords = [
    {
      id: "S001",
      member: "张三",
      date: "2023-01-15",
      amount: "¥3,600",
      paymentMethod: "微信支付",
      status: "已完成",
    },
    {
      id: "S002",
      member: "李四",
      date: "2023-02-20",
      amount: "¥3,600",
      paymentMethod: "支付宝",
      status: "已完成",
    },
    {
      id: "S003",
      member: "王五",
      date: "2023-03-10",
      amount: "¥3,240",
      paymentMethod: "微信支付",
      status: "已完成",
      discount: "9折优惠",
    },
  ]

  // 模拟价格调整历史
  const priceHistory = [
    {
      date: "2023-02-20",
      oldPrice: "¥4,800",
      newPrice: "¥3,600",
      reason: "春季促销活动",
    },
    {
      date: "2022-12-01",
      oldPrice: "¥5,200",
      newPrice: "¥4,800",
      reason: "价格调整",
    },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>会员卡详情</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col items-start justify-between gap-4 md:flex-row">
          <div className="flex items-center gap-4">
            <div className="h-12 w-12 rounded-full" style={{ backgroundColor: card.color }} />
            <div>
              <h2 className="text-2xl font-bold">{card.name}</h2>
              <p className="text-muted-foreground">{card.description}</p>
            </div>
          </div>

          <Badge variant={card.status === "active" ? "default" : "secondary"} className="h-6">
            {card.status === "active" ? "销售中" : "已下架"}
          </Badge>
        </div>

        <Separator />

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">
              <Tag className="mr-2 h-4 w-4" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="members">
              <Users className="mr-2 h-4 w-4" />
              持卡会员
            </TabsTrigger>
            <TabsTrigger value="consumption">
              <BookOpen className="mr-2 h-4 w-4" />
              消费记录
            </TabsTrigger>
            <TabsTrigger value="sales">
              <ShoppingCart className="mr-2 h-4 w-4" />
              销售记录
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="mr-2 h-4 w-4" />
              卡片设置
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">持卡会员</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">{card.members}</div>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">销售数量</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">{card.salesCount}</div>
                    <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">销售收入</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold">{card.revenue}</div>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Tag className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">会员卡名称</span>
                    </div>
                    <span>{card.name}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">有效期</span>
                    </div>
                    <span>{card.validity}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">使用限制</span>
                    </div>
                    <span>{card.limit}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">当前价格</span>
                    </div>
                    <div>
                      <span className="font-medium">{card.price}</span>
                      {card.originalPrice !== card.price && (
                        <span className="ml-2 text-sm text-muted-foreground line-through">{card.originalPrice}</span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">创建时间</span>
                    </div>
                    <span>{card.createdAt}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">最后更新</span>
                    </div>
                    <span>{card.updatedAt}</span>
                  </div>
                </CardContent>
              </Card>

              <SalesTrendChart cardId={card.id} />
            </div>

            <Card>
              <CardHeader>
                <CardTitle>价格调整历史</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {priceHistory.map((history, index) => (
                    <div key={index} className="flex items-center justify-between rounded-lg border p-3">
                      <div>
                        <p className="font-medium">{history.date}</p>
                        <p className="text-sm text-muted-foreground">{history.reason}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground line-through">{history.oldPrice}</span>
                        <ArrowRightIcon className="h-4 w-4" />
                        <span className="font-medium">{history.newPrice}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="members" className="mt-4">
            <CardMembers cardId={card.id} />
          </TabsContent>

          <TabsContent value="consumption" className="mt-4">
            <ConsumptionRecords membershipId={card.id} />
          </TabsContent>

          <TabsContent value="sales" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>销售记录</CardTitle>
                <CardDescription>会员卡销售历史记录</CardDescription>
              </CardHeader>
              <CardContent>
                <SalesRecords cardId={card.id} />
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <SalesTrendChart
                cardId={card.id}
                title="销售趋势"
                showControls={false}
              />
              <PaymentMethodChart cardId={card.id} />
            </div>
          </TabsContent>

          <TabsContent value="settings" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>卡片设置</CardTitle>
                <CardDescription>管理会员卡的设置和状态</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">会员卡状态</h4>
                      <p className="text-sm text-muted-foreground">控制会员卡是否在前台显示并可购买</p>
                    </div>
                    <Button variant={card.status === "active" ? "destructive" : "default"}>
                      {card.status === "active" ? "下架" : "上架"}
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">编辑会员卡名称</h4>
                      <p className="text-sm text-muted-foreground">修改会员卡的基本信息和设置</p>
                    </div>
                    <Button>
                      <Pencil className="mr-2 h-4 w-4" />
                      编辑
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">调整价格</h4>
                      <p className="text-sm text-muted-foreground">修改会员卡的价格和促销设置</p>
                    </div>
                    <Button variant="outline">
                      <DollarSign className="mr-2 h-4 w-4" />
                      调整价格
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">查看统计</h4>
                      <p className="text-sm text-muted-foreground">查看会员卡的详细统计数据</p>
                    </div>
                    <Button variant="outline">
                      <BarChart className="mr-2 h-4 w-4" />
                      查看统计
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-destructive">删除会员卡</h4>
                      <p className="text-sm text-muted-foreground">删除此会员卡（不会影响已购买的会员）</p>
                    </div>
                    <Button variant="destructive">删除</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

function ArrowRightIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M5 12h14" />
      <path d="m12 5 7 7-7 7" />
    </svg>
  )
}

