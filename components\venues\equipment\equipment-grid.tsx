"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON>, Pencil, Settings } from "lucide-react"

interface EquipmentGridProps {
  equipments: Array<{
    id: string
    name: string
    venue: string
    quantity: number
    status: string
    lastMaintenance: string
    nextMaintenance: string
    brand: string
    purchaseDate: string
    price: number
    lifespan: string
  }>
  onViewDetail: (equipment: any) => void
}

export function EquipmentGrid({ equipments, onViewDetail }: EquipmentGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {equipments.map((equipment) => (
        <Card key={equipment.id} className="overflow-hidden">
          <CardHeader>
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">{equipment.name}</h3>
              <Badge variant={equipment.status === "normal" ? "default" : "destructive"}>
                {equipment.status === "normal" ? "正常" : "维护中"}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              {equipment.id} | {equipment.venue}
            </p>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">数量:</span>
              <span>{equipment.quantity}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">品牌:</span>
              <span>{equipment.brand}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">购买日期:</span>
              <span>{equipment.purchaseDate}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">上次维护:</span>
              <span>{equipment.lastMaintenance}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">下次维护:</span>
              <span>{equipment.nextMaintenance}</span>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" size="sm" onClick={() => onViewDetail(equipment)}>
              <Eye className="mr-2 h-4 w-4" />
              详情
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              维护
            </Button>
            <Button variant="outline" size="sm">
              <Pencil className="mr-2 h-4 w-4" />
              编辑
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

