# 瑜伽后台管理系统部署文档

## 项目概述

瑜伽后台管理系统是一个专业的瑜伽馆管理系统，提供会员管理、课程管理、教练管理、场地管理、订单管理等核心功能，以及增值服务如共享股东、法大大电子合同等高级功能。本文档提供系统部署到正式环境的完整指南。

## 技术栈

本项目基于以下技术栈开发：

- **前端框架**：Next.js 15.2.4 (App Router)
- **UI 组件库**：Shadcn UI (基于 Radix UI)
- **样式解决方案**：Tailwind CSS 3.4.17
- **图表库**：Recharts
- **日期处理**：date-fns
- **图标库**：Lucide React 0.454.0
- **表单处理**：React Hook Form + Zod 验证
- **React 版本**：React 19

## 系统要求

- **Node.js**: 18.x 或更高版本（强烈推荐 20.x LTS）
  - 注意：项目使用 Next.js 15.2.4，该版本要求 Node.js 18.18.0 或更高版本
  - 推荐使用 nvm 管理 Node.js 版本
- **包管理器**:
  - npm 9.x 或更高版本
  - 或 yarn 1.22.x 或更高版本
  - 或 pnpm 8.x 或更高版本（推荐，项目使用 pnpm）
- **浏览器支持**: 现代浏览器 (Chrome, Firefox, Safari, Edge)
- **服务器要求**:
  - 至少 2GB RAM
  - 至少 10GB 磁盘空间
  - 推荐 Linux 服务器 (Ubuntu 20.04 LTS 或更高版本)
![alt text](image.png)
## 依赖清单

### 生产依赖

```json
{
  "@hookform/resolvers": "latest",
  "@radix-ui/react-accordion": "^1.2.2",
  "@radix-ui/react-alert-dialog": "^1.1.4",
  "@radix-ui/react-aspect-ratio": "^1.1.1",
  "@radix-ui/react-avatar": "^1.1.2",
  "@radix-ui/react-checkbox": "^1.1.3",
  "@radix-ui/react-collapsible": "^1.1.2",
  "@radix-ui/react-context-menu": "^2.2.4",
  "@radix-ui/react-dialog": "^1.1.4",
  "@radix-ui/react-dropdown-menu": "^2.1.4",
  "@radix-ui/react-hover-card": "^1.1.4",
  "@radix-ui/react-label": "^2.1.1",
  "@radix-ui/react-menubar": "^1.1.4",
  "@radix-ui/react-navigation-menu": "^1.2.3",
  "@radix-ui/react-popover": "^1.1.4",
  "@radix-ui/react-progress": "^1.1.1",
  "@radix-ui/react-radio-group": "^1.2.2",
  "@radix-ui/react-scroll-area": "^1.2.2",
  "@radix-ui/react-select": "^2.1.4",
  "@radix-ui/react-separator": "^1.1.1",
  "@radix-ui/react-slider": "^1.2.2",
  "@radix-ui/react-slot": "^1.1.1",
  "@radix-ui/react-switch": "^1.1.2",
  "@radix-ui/react-tabs": "^1.1.2",
  "@radix-ui/react-toast": "^1.2.4",
  "@radix-ui/react-toggle": "^1.1.1",
  "@radix-ui/react-toggle-group": "^1.1.1",
  "@radix-ui/react-tooltip": "^1.1.6",
  "autoprefixer": "^10.4.20",
  "class-variance-authority": "^0.7.1",
  "clsx": "^2.1.1",
  "cmdk": "1.0.4",
  "date-fns": "latest",
  "embla-carousel-react": "8.5.1",
  "input-otp": "1.4.1",
  "lucide-react": "^0.454.0",
  "next": "15.2.4",
  "next-themes": "^0.4.4",
  "react": "^19",
  "react-day-picker": "latest",
  "react-dom": "^19",
  "react-hook-form": "latest",
  "react-resizable-panels": "^2.1.7",
  "recharts": "latest",
  "sonner": "^1.7.1",
  "tailwind-merge": "^2.5.5",
  "tailwindcss-animate": "^1.0.7",
  "vaul": "^0.9.6",
  "zod": "latest",
  "html2canvas": "latest",
  "framer-motion": "^11.0.8"
}
```

### 开发依赖

```json
{
  "@types/node": "^22",
  "@types/react": "^19",
  "@types/react-dom": "^19",
  "postcss": "^8",
  "tailwindcss": "^3.4.17",
  "typescript": "^5"
}
```

## 部署步骤

### 1. 准备环境

1. 确保服务器已安装 Node.js 和 npm/yarn/pnpm
   ```bash
   # 检查 Node.js 版本
   node -v

   # 检查 npm 版本
   npm -v
   ```

2. 安装 PM2 用于进程管理（推荐）
   ```bash
   npm install -g pm2
   ```

### 2. 获取代码

1. 克隆代码仓库
   ```bash
   git clone <仓库地址> yogahoutai
   cd yogahoutai
   ```

2. 安装依赖
   ```bash
   npm install
   # 或
   yarn install
   # 或
   pnpm install
   ```

### 3. 环境配置

1. 创建环境变量文件 `.env.production`
   ```
   # 基础配置
   NEXT_PUBLIC_API_BASE_URL=https://api.example.com/api

   # 法大大电子合同配置
   NEXT_PUBLIC_FADADA_APP_ID=your_app_id
   NEXT_PUBLIC_FADADA_APP_SECRET=your_app_secret

   # 图片服务配置
   NEXT_PUBLIC_IMAGE_DOMAIN=ai-public.mastergo.com

   # 其他第三方服务配置
   # ...
   ```

2. 配置 Next.js（如需要）
   - 项目使用 `next.config.mjs` 和 `v0-user-next.config.mjs` 两个配置文件
   - `v0-user-next.config.mjs` 包含自定义配置，确保该文件存在并包含以下内容：

   ```javascript
   // 自定义Next.js配置
   /** @type {import('next').NextConfig} */
   const userNextConfig = {
     // 启用图片优化
     images: {
       domains: ['ai-public.mastergo.com'],
     },
     // 禁用严格模式以避免开发中的双重渲染
     reactStrictMode: false,
   };

   export default userNextConfig;
   ```

3. 确认 PostCSS 配置
   - 确保 `postcss.config.mjs` 文件存在并包含以下内容：

   ```javascript
   /** @type {import('postcss-load-config').Config} */
   const config = {
     plugins: {
       tailwindcss: {},
     },
   };

   export default config;
   ```

### 4. 构建应用

```bash
# 构建生产版本
npm run build
# 或
yarn build
# 或
pnpm build
```

### 5. 启动应用

#### 使用 PM2 启动（推荐）

创建 PM2 配置文件 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [
    {
      name: "yoga-admin",
      script: "node_modules/next/dist/bin/next",
      args: "start",
      instances: "max",
      exec_mode: "cluster",
      watch: false,
      env: {
        PORT: 3000,
        NODE_ENV: "production",
      },
    },
  ],
};
```

启动应用：

```bash
pm2 start ecosystem.config.js
```

#### 直接启动

```bash
npm run start
# 或
yarn start
# 或
pnpm start
```

### 6. 配置 Nginx 反向代理

创建 Nginx 配置文件：

```nginx
server {
    listen 80;
    server_name admin.youryogastudio.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

重启 Nginx：

```bash
sudo service nginx restart
```

### 7. SSL 配置（推荐）

使用 Let's Encrypt 配置 SSL：

```bash
sudo certbot --nginx -d admin.youryogastudio.com
```

## 更新部署

1. 拉取最新代码
   ```bash
   git pull origin main
   ```

2. 安装依赖
   ```bash
   npm install
   ```

3. 重新构建
   ```bash
   npm run build
   ```

4. 重启应用
   ```bash
   pm2 restart yoga-admin
   ```

## 监控与维护

### PM2 监控

```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs yoga-admin

# 监控资源使用
pm2 monit
```

### 日志管理

- 应用日志位于 `~/.pm2/logs/`
- Nginx 访问日志位于 `/var/log/nginx/access.log`
- Nginx 错误日志位于 `/var/log/nginx/error.log`

### 备份策略

1. 定期备份数据库
2. 备份环境配置文件
3. 使用 Git 管理代码版本

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查 Node.js 版本是否兼容
   - 检查环境变量配置
   - 查看错误日志 `pm2 logs yoga-admin`

2. **页面加载缓慢**
   - 检查服务器资源使用情况
   - 考虑增加 PM2 实例数
   - 检查数据库查询性能

3. **API 连接失败**
   - 确认 API 服务是否正常运行
   - 检查网络连接和防火墙设置
   - 验证 API 密钥和认证信息

## 部署验证

部署完成后，请执行以下步骤验证系统是否正常运行：

1. **基本功能验证**
   - 访问系统登录页面，确认页面正常加载
   - 使用测试账号登录系统，确认登录功能正常
   - 测试核心功能：会员管理、课程管理、教练管理等
   - 验证图表和数据展示是否正常

2. **性能验证**
   - 使用浏览器开发者工具检查页面加载时间
   - 检查 API 请求响应时间
   - 验证在不同设备和浏览器上的兼容性

3. **安全验证**
   - 确认 HTTPS 配置正确
   - 验证登录和权限控制功能
   - 检查敏感信息是否加密传输

## 联系支持

如遇到无法解决的问题，请联系技术支持团队：

- 邮箱：<EMAIL>
- 电话：400-123-4567

11111111111111111111111