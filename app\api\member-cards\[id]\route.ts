import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 获取单个会员卡详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    
    console.log('获取会员卡详情，ID:', id);

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询会员卡详情
      const [rows] = await connection.execute(
        'SELECT * FROM membercard WHERE id = ?',
        [parseInt(id)]
      );

      const memberCards = rows as any[];

      if (memberCards.length === 0) {
        return NextResponse.json({
          code: 404,
          msg: '会员卡不存在',
          data: null
        }, { status: 404 });
      }

      const memberCard = memberCards[0];

      console.log('会员卡详情查询成功:', memberCard.name);

      return NextResponse.json({
        code: 0,
        data: memberCard,
        msg: '获取会员卡详情成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取会员卡详情失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取会员卡详情失败',
      data: null
    }, { status: 500 });
  }
}

// 更新会员卡
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const data = await request.json();
    
    console.log('更新会员卡，ID:', id, '数据:', data);

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      if (data.name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(data.name);
      }
      if (data.description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(data.description);
      }
      if (data.price !== undefined) {
        updateFields.push('price = ?');
        updateValues.push(data.price);
      }
      if (data.original_price !== undefined) {
        updateFields.push('original_price = ?');
        updateValues.push(data.original_price);
      }
      if (data.validity_days !== undefined) {
        updateFields.push('validity_days = ?');
        updateValues.push(data.validity_days);
      }
      if (data.usage_limit !== undefined) {
        updateFields.push('usage_limit = ?');
        updateValues.push(data.usage_limit);
      }
      if (data.card_type !== undefined) {
        updateFields.push('card_type = ?');
        updateValues.push(data.card_type);
      }
      if (data.status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(data.status);
      }

      updateFields.push('updated_at = NOW()');
      updateValues.push(parseInt(id));

      // 执行更新
      await connection.execute(
        `UPDATE membercard SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      // 查询更新后的数据
      const [updatedRows] = await connection.execute(
        'SELECT * FROM membercard WHERE id = ?',
        [parseInt(id)]
      );

      const updatedCard = (updatedRows as any[])[0];

      console.log('会员卡更新成功:', updatedCard.name);

      return NextResponse.json({
        code: 0,
        data: updatedCard,
        msg: '更新会员卡成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('更新会员卡失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '更新会员卡失败',
      data: null
    }, { status: 500 });
  }
}

// 删除会员卡
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    console.log('删除会员卡，ID:', id);

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 检查会员卡是否存在
      const [existingRows] = await connection.execute(
        'SELECT * FROM membercard WHERE id = ?',
        [parseInt(id)]
      );

      if ((existingRows as any[]).length === 0) {
        return NextResponse.json({
          code: 404,
          msg: '会员卡不存在',
          data: null
        }, { status: 404 });
      }

      // 删除会员卡
      await connection.execute(
        'DELETE FROM membercard WHERE id = ?',
        [parseInt(id)]
      );

      console.log('会员卡删除成功，ID:', id);

      return NextResponse.json({
        code: 0,
        data: null,
        msg: '删除会员卡成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('删除会员卡失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '删除会员卡失败',
      data: null
    }, { status: 500 });
  }
} 