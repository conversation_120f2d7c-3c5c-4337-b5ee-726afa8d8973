import axios, { AxiosResponse } from 'axios';
import { toast } from '@/hooks/use-toast';
import { CourseType, CourseTypeFilter } from '@/types/course-type';

// 扩展响应类型以支持不同的后端API响应格式
interface ExtendedAxiosResponse extends AxiosResponse {
  records?: any[];
}

// 获取正确的API基础URL
const getApiBaseUrl = () => {
  // 在客户端，使用当前域名
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  // 在服务端，使用环境变量或默认值
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3004';
};

const API_BASE_URL = getApiBaseUrl();
console.log('API基础URL:', API_BASE_URL);

const instance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 在这里可以添加统一的请求头，如token等
    console.log('发出请求:', config.url, config.data);
    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 统一处理响应数据
    console.log('请求成功:', response.config.url, response.data);
    return response.data;
  },
  (error) => {
    // 统一错误处理
    const url = error.config?.url || '未知URL';
    const status = error.response?.status || '未知状态';
    const data = error.response?.data || '无响应数据';

    console.error('请求失败:', url, status, data);

    const message = error.response?.data?.message || error.message || '请求失败';
    toast({
      title: '请求错误',
      description: message,
      variant: 'destructive',
    });
    return Promise.reject(error);
  }
);

// 课程相关API
export const courseApi = {
  // 获取所有课程
  getAllCourses: (params?: {
    page?: number;
    pageSize?: number;
    keyword?: string;
    type?: string;
    status?: string;
    coach?: string;
    venue?: string;
    priceMin?: number;
    priceMax?: number;
    capacityMin?: number;
    capacityMax?: number;
    dayOfWeek?: string[];
    timeOfDay?: string[];
    sortBy?: string;
  }) => instance.get('/api/courses', { params }),

  // 获取课程详情
  getCourseById: (id: string) => instance.get(`/api/courses/${id}`),

  // 创建课程
  createCourse: (data: any) => {
    console.log('API实际发送数据:', data);
    return instance.post('/api/courses', data)
      .catch(error => {
        console.error('课程创建请求失败:', error.response || error);
        if (error.response?.status === 422) {
          console.error('请求数据验证失败, 详情:', error.response.data);
        }
        throw error;
      });
  },

  // 更新课程
  updateCourse: (id: string, data: any) => {
    console.log('API更新发送数据:', data);
    return instance.put(`/api/courses/${id}`, data)
      .catch(error => {
        console.error('课程更新请求失败:', error.response || error);
        throw error;
      });
  },

  // 删除课程
  deleteCourse: (id: string) => instance.delete(`/api/courses/${id}`),

  // 批量删除课程
  deleteCourses: (ids: string[]) => instance.post('/api/courses/batch', { action: 'delete', ids }),

  // 应用高级筛选
  applyAdvancedFilter: (data: {
    priceMin?: number;
    priceMax?: number;
    capacityMin?: number;
    capacityMax?: number;
    dayOfWeek?: string[];
    timeOfDay?: string[];
  }) => instance.get('/api/courses', { params: data }),

  // 导出课程
  exportCourses: (ids?: string[]) => {
    const params = ids && ids.length > 0 ? { ids: ids.join(',') } : undefined;
    // 设置直接下载
    window.location.href = `${API_BASE_URL}/api/courses/export${params ? `?ids=${params.ids}` : ''}`;
    // 返回一个已解析的Promise
    return Promise.resolve({ success: true });
  },

  // 导入课程
  importCourses: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    return instance.post('/api/courses/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 复制课程
  copyCourse: (id: string) => instance.post(`/api/courses/copy/${id}`),

  // 更新课程状态
  updateCourseStatus: (id: string, status: string) =>
    instance.put(`/api/courses/${id}`, { status }),

  // 课程类型相关API
  // 获取所有课程类型
  getAllCourseTypes: () => instance.get('/api/course-types'),

  // 获取课程类型详情
  getCourseTypeById: (id: string) => instance.get(`/api/course-types/${id}`),

  // 创建课程类型
  createCourseType: (data: any) => instance.post('/api/course-types', data),

  // 更新课程类型
  updateCourseType: (id: string, data: any) =>
    instance.put(`/api/course-types/${id}`, data),

  // 删除课程类型
  deleteCourseType: (id: string) => instance.delete(`/api/course-types/${id}`),
};

// 教练相关API
export const coachApi = {
  // 获取所有教练
  getAllCoaches: (params?: {
    tenantId?: string;
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
  }) => instance.get('/api/coaches', { params }),

  // 获取教练详情
  getCoachById: (id: string) => instance.get(`/api/coaches/${id}`),

  // 创建教练
  createCoach: (data: any) => instance.post('/api/coaches', data),

  // 更新教练
  updateCoach: (id: string, data: any) => instance.put(`/api/coaches/${id}`, data),

  // 删除教练
  deleteCoach: (id: string) => instance.delete(`/api/coaches/${id}`),
};

// 场地相关API
export const venueApi = {
  // 获取所有场地
  getAllVenues: (params?: {
    tenantId?: string;
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
  }) => instance.get('/api/venues', { params }),

  // 获取场地详情
  getVenueById: (id: string) => instance.get(`/api/venues/${id}`),

  // 创建场地
  createVenue: (data: any) => instance.post('/api/venues', data),

  // 更新场地
  updateVenue: (id: string, data: any) => instance.put(`/api/venues/${id}`, data),

  // 删除场地
  deleteVenue: (id: string) => instance.delete(`/api/venues/${id}`),
};

// 会员卡类型相关API
export const memberCardApi = {
  // 获取所有会员卡类型
  getAllMemberCards: (params?: {
    tenantId?: string;
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
    cardType?: string;
  }) => instance.get('/api/member-cards', { params }),

  // 获取会员卡类型详情
  getMemberCardById: (id: string) => instance.get(`/api/member-cards/${id}`),

  // 创建会员卡类型
  createMemberCard: (data: any) => instance.post('/api/member-cards', data),

  // 更新会员卡类型
  updateMemberCard: (id: string, data: any) => instance.put(`/api/member-cards/${id}`, data),

  // 删除会员卡类型
  deleteMemberCard: (id: string) => instance.delete(`/api/member-cards/${id}`),

  // 更新会员卡类型状态
  updateMemberCardStatus: (id: string, status: string) =>
    instance.put(`/api/member-cards/${id}/status`, { status }),

  // 批量删除会员卡类型
  deleteMemberCards: (ids: string[]) =>
    instance.post('/api/member-cards/batch', { action: 'delete', ids }),

  // 复制会员卡类型
  copyMemberCard: (id: string) => instance.post(`/api/member-cards/copy/${id}`),
};

// 门店相关API
export const storeApi = {
  // 获取所有门店
  getAllStores: (params?: {
    tenantId?: string;
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
  }) => instance.get('/api/stores', { params }),

  // 获取门店详情
  getStoreById: (id: string) => instance.get(`/api/stores/${id}`),

  // 创建门店
  createStore: (data: any) => instance.post('/api/stores', data),

  // 更新门店
  updateStore: (id: string, data: any) => instance.put(`/api/stores/${id}`, data),

  // 删除门店
  deleteStore: (id: string) => instance.delete(`/api/stores/${id}`),

  // 更新门店状态
  updateStoreStatus: (id: string, status: string) =>
    instance.put(`/api/stores/${id}/status`, { status }),
};



// 支付相关API
const paymentApi = {
  // 支付宝支付
  alipay: {
    createQRCode: (data: { amount: number; description: string }) =>
      instance.post('/api/alipay/qrcode', data),

    queryPayment: (tradeNo: string) =>
      instance.get(`/api/alipay/query/${tradeNo}`),

    refund: (data: { tradeNo: string; amount: number; reason: string }) =>
      instance.post('/api/alipay/refund', data),
  },

  // 微信支付
  wechat: {
    createQRCode: (data: { amount: number; description: string }) =>
      instance.post('/api/wechat/qrcode', data),
  },

  // 银联支付
  unionpay: {
    createQRCode: (data: { amount: number; description: string }) =>
      instance.post('/api/unionpay/qrcode', data),
  },
};

export { paymentApi };

// 课程类型API
export const courseTypeApi = {
  // 获取所有课程类型
  getAll: (filter?: CourseTypeFilter) => instance.get('/api/course-types', { params: filter }),

  // 获取单个课程类型
  getById: (id: number) => instance.get(`/api/course-types/${id}`),

  // 创建课程类型
  create: (data: Partial<CourseType>) => instance.post('/api/course-types', data),

  // 更新课程类型
  update: (id: number, data: Partial<CourseType>) => 
    instance.put(`/api/course-types/${id}`, data),

  // 删除课程类型
  delete: (id: number) => instance.delete(`/api/course-types/${id}`),

  // 搜索课程类型
  search: (query: string) => instance.get('/api/course-types/search', { params: { q: query } }),

  // 获取统计信息
  getStats: () => instance.get('/api/course-types/stats'),
};

export default instance;