"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { Lightbulb, TrendingUp, AlertTriangle, Users } from "lucide-react"

// 模拟数据
const insights = [
  {
    id: 1,
    title: "会员流失风险提醒",
    description: "检测到15名会员超过30天未到访，建议发送关怀消息或优惠券挽留。",
    href: "/members/retention",
    icon: <AlertTriangle className="h-5 w-5 text-amber-500" />,
    actionText: "查看详情",
  },
  {
    id: 2,
    title: "课程优化建议",
    description: "周六10:00-12:00时段预约率达95%，建议增加同类型课程。",
    href: "/courses/optimization",
    icon: <TrendingUp className="h-5 w-5 text-blue-500" />,
    actionText: "查看详情",
  },
  {
    id: 3,
    title: "收入增长机会",
    description: "高级瑜伽课程预约率高于平均水平30%，建议适当增加课程价格或数量。",
    href: "/revenue/opportunities",
    icon: <Users className="h-5 w-5 text-green-500" />,
    actionText: "查看详情",
  },
]

export function IntelligentInsights() {
  const router = useRouter()

  return (
    <Card className="border-primary/20 bg-primary/5">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-primary" />
          <span>智能洞察</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {insights.map((insight) => (
            <div
              key={insight.id}
              className="p-4 bg-background rounded-lg shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="flex items-center gap-2 mb-2">
                {insight.icon}
                <h4 className="font-medium">{insight.title}</h4>
              </div>
              <p className="text-sm text-muted-foreground mb-3">
                {insight.description}
              </p>
              <Button
                variant="link"
                className="p-0 h-auto text-primary hover:text-primary/80 flex items-center gap-1"
                onClick={() => router.push(insight.href)}
              >
                {insight.actionText}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="ml-1 h-3 w-3"
                >
                  <path d="M5 12h14"></path>
                  <path d="m12 5 7 7-7 7"></path>
                </svg>
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
