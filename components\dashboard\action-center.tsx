"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import {
  UserPlus,
  CalendarPlus,
  CheckSquare,
  CreditCard,
  ShoppingBag,
  Gift,
  ClipboardList,
  BarChart4,
  MessageSquare,
  Bell,
  FileText,
  Settings
} from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useState } from "react"

export function ActionCenter() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("quick")

  const quickActions = [
    {
      icon: <UserPlus className="h-5 w-5" />,
      label: "添加会员",
      description: "新会员登记入会",
      href: "/members/add",
      color: "bg-blue-50 text-blue-600",
    },
    {
      icon: <CalendarPlus className="h-5 w-5" />,
      label: "创建课程",
      description: "添加新课程安排",
      href: "/courses/add",
      color: "bg-purple-50 text-purple-600",
    },
    {
      icon: <CheckSquare className="h-5 w-5" />,
      label: "会员签到",
      description: "课程签到与核销",
      href: "/checkin",
      color: "bg-green-50 text-green-600",
    },
    {
      icon: <CreditCard className="h-5 w-5" />,
      label: "收款",
      description: "会员缴费与收款",
      href: "/payment",
      color: "bg-amber-50 text-amber-600",
    },
    {
      icon: <ShoppingBag className="h-5 w-5" />,
      label: "销售商品",
      description: "瑜伽用品销售",
      href: "/products/sell",
      color: "bg-pink-50 text-pink-600",
    },
    {
      icon: <Gift className="h-5 w-5" />,
      label: "营销活动",
      description: "优惠券与活动",
      href: "/marketing",
      color: "bg-red-50 text-red-600",
    },
  ]

  const frequentActions = [
    {
      icon: <ClipboardList className="h-5 w-5" />,
      label: "排课管理",
      description: "课程排期安排",
      href: "/courses/schedule",
      color: "bg-indigo-50 text-indigo-600",
    },
    {
      icon: <BarChart4 className="h-5 w-5" />,
      label: "数据报表",
      description: "业务数据分析",
      href: "/statistics/dashboard",
      color: "bg-cyan-50 text-cyan-600",
    },
    {
      icon: <MessageSquare className="h-5 w-5" />,
      label: "消息通知",
      description: "发送会员通知",
      href: "/messages",
      color: "bg-teal-50 text-teal-600",
    },
    {
      icon: <Bell className="h-5 w-5" />,
      label: "预约管理",
      description: "查看预约记录",
      href: "/booking-records",
      color: "bg-orange-50 text-orange-600",
    },
    {
      icon: <FileText className="h-5 w-5" />,
      label: "会员卡管理",
      description: "管理会员卡种类",
      href: "/members/cards",
      color: "bg-violet-50 text-violet-600",
    },
    {
      icon: <Settings className="h-5 w-5" />,
      label: "系统设置",
      description: "配置系统参数",
      href: "/settings",
      color: "bg-gray-50 text-gray-600",
    },
  ]

  return (
    <Card className="border shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">操作中心</CardTitle>
          <div className="w-auto">
            <div className="flex items-center bg-muted rounded-md p-1 h-8">
              <button
                className={`text-xs px-3 rounded-sm h-6 flex items-center justify-center ${activeTab === "quick" ? "bg-background shadow-sm" : ""}`}
                onClick={() => setActiveTab("quick")}
              >
                快捷操作
              </button>
              <button
                className={`text-xs px-3 rounded-sm h-6 flex items-center justify-center ${activeTab === "frequent" ? "bg-background shadow-sm" : ""}`}
                onClick={() => setActiveTab("frequent")}
              >
                常用功能
              </button>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {activeTab === "quick" ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 pt-2">
            {quickActions.map((action, index) => (
              <div
                key={index}
                className="flex flex-col items-center justify-center p-3 rounded-lg border hover:border-primary/50 hover:bg-muted/30 cursor-pointer transition-all text-center"
                onClick={() => router.push(action.href)}
              >
                <div className={`p-3 rounded-full ${action.color} mb-2`}>
                  {action.icon}
                </div>
                <div className="font-medium text-sm">{action.label}</div>
                <div className="text-xs text-muted-foreground mt-1">{action.description}</div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 pt-2">
            {frequentActions.map((action, index) => (
              <div
                key={index}
                className="flex flex-col items-center justify-center p-3 rounded-lg border hover:border-primary/50 hover:bg-muted/30 cursor-pointer transition-all text-center"
                onClick={() => router.push(action.href)}
              >
                <div className={`p-3 rounded-full ${action.color} mb-2`}>
                  {action.icon}
                </div>
                <div className="font-medium text-sm">{action.label}</div>
                <div className="text-xs text-muted-foreground mt-1">{action.description}</div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
