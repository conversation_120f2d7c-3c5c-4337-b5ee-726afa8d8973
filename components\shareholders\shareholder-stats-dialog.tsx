"use client"

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Users,
  Wallet,
  Share2,
  CreditCard,
  BarChart3,
  PieChart,
  LineChart,
  Download,
  Calendar,
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useState } from "react"

// 导入Recharts库
import {
  ResponsiveContainer,
  <PERSON><PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  Tooltip,
  Legend,
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LineChart as RechartsLineChart,
  Line
} from 'recharts';

// 模拟数据 - 股东类型分布
const shareholderTypeData = [
  { name: '消费型股东', value: 28 },
  { name: '投资型股东', value: 12 },
  { name: '资源型股东', value: 15 },
  { name: '员工型股东', value: 8 },
  { name: '联盟型股东', value: 6 },
];

// 模拟数据 - 分红金额分布
const dividendData = [
  { name: '消费型股东', value: 42000 },
  { name: '投资型股东', value: 36000 },
  { name: '资源型股东', value: 28000 },
  { name: '员工型股东', value: 15000 },
  { name: '联盟型股东', value: 7500 },
];

// 模拟数据 - 股东增长趋势
const shareholderGrowthData = [
  { month: '1月', shareholders: 45 },
  { month: '2月', shareholders: 52 },
  { month: '3月', shareholders: 58 },
  { month: '4月', shareholders: 63 },
  { month: '5月', shareholders: 69 },
];

// 模拟数据 - 引流客户趋势
const customerTrendData = [
  { month: '1月', customers: 120 },
  { month: '2月', customers: 150 },
  { month: '3月', customers: 180 },
  { month: '4月', customers: 220 },
  { month: '5月', customers: 280 },
  { month: '6月', customers: 350 },
];

// 图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

interface ShareholderStatsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ShareholderStatsDialog({
  open,
  onOpenChange,
}: ShareholderStatsDialogProps) {
  const [period, setPeriod] = useState("all")

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>股东统计</DialogTitle>
          <DialogDescription>
            查看股东相关的统计数据和分析
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-between items-center py-4">
          <h2 className="text-xl font-semibold">股东概览</h2>
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部时间</SelectItem>
              <SelectItem value="month">本月</SelectItem>
              <SelectItem value="quarter">本季度</SelectItem>
              <SelectItem value="year">本年度</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">股东总数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">69</div>
              <p className="text-xs text-muted-foreground">
                较上月增长 8.5%
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">累计分红金额</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥128,430.00</div>
              <p className="text-xs text-muted-foreground">
                较上月增长 12.5%
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">引流客户数</CardTitle>
              <Share2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,284</div>
              <p className="text-xs text-muted-foreground">
                较上月增长 8.3%
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">引流消费额</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥543,000.00</div>
              <p className="text-xs text-muted-foreground">
                较上月增长 15.2%
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-2 gap-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>股东类型分布</CardTitle>
              <CardDescription>各类型股东数量占比</CardDescription>
            </CardHeader>
            <CardContent>
              {/* 使用Recharts库实现实际的饼图 */}
              <div className="h-60">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={shareholderTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {shareholderTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>分红金额分布</CardTitle>
              <CardDescription>各类型股东分红金额占比</CardDescription>
            </CardHeader>
            <CardContent>
              {/* 使用Recharts库实现实际的柱状图 */}
              <div className="h-60">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart
                    data={dividendData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="value" fill="#8884d8" name="分红金额" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="mt-4">
          <CardHeader>
            <CardTitle>股东增长趋势</CardTitle>
            <CardDescription>股东数量随时间变化趋势</CardDescription>
          </CardHeader>
          <CardContent>
            {/* 使用Recharts库实现实际的折线图 */}
            <div className="h-60">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsLineChart
                  data={shareholderGrowthData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="shareholders"
                    stroke="#8884d8"
                    activeDot={{ r: 8 }}
                    name="股东数量"
                  />
                </RechartsLineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="mt-4">
          <CardHeader>
            <CardTitle>引流客户趋势</CardTitle>
            <CardDescription>引流客户数量随时间变化趋势</CardDescription>
          </CardHeader>
          <CardContent>
            {/* 使用Recharts库实现实际的折线图 */}
            <div className="h-60">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsLineChart
                  data={customerTrendData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="customers"
                    stroke="#82ca9d"
                    activeDot={{ r: 8 }}
                    name="引流客户数"
                  />
                </RechartsLineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <DialogFooter className="mt-6">
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            选择日期范围
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
          <Button onClick={() => onOpenChange(false)}>关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
