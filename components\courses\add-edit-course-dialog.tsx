"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { X, <PERSON>ader2, ChevronDown, ChevronRight } from "lucide-react"
import { courseApi } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { FileUploader } from "@/components/file-uploader"
import { CardAssociationTabs } from "./card-association-tabs"

// 模拟数据
const courseTypes = [
  { id: "group", name: "团课", color: "#4285F4" },
  { id: "small", name: "小班课", color: "#34A853" },
  { id: "premium", name: "精品课", color: "#FBBC05" },
  { id: "private", name: "私教课", color: "#EA4335" },
  { id: "training", name: "教培课", color: "#9C27B0" },
]

const coaches = [
  { id: "1", name: "张教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "2", name: "李教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "3", name: "王教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "4", name: "赵教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "5", name: "刘教练", avatar: "/placeholder.svg?height=32&width=32" },
]

const venues = [
  { id: "1", name: "1号瑜伽室", capacity: 15 },
  { id: "2", name: "2号瑜伽室", capacity: 10 },
  { id: "3", name: "3号瑜伽室", capacity: 15 },
  { id: "4", name: "4号瑜伽室", capacity: 8 },
  { id: "5", name: "私教室", capacity: 2 },
]

interface AddEditCourseDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  course: any
  onSave: (course: any) => void
  existingCourses?: any[] // 现有课程列表，用于检查名称重复
}

export function AddEditCourseDialog({ open, onOpenChange, course, onSave, existingCourses = [] }: AddEditCourseDialogProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  // 折叠状态管理
  const [collapsedSections, setCollapsedSections] = useState({
    booking: false,
    waitlist: false,
    checkin: false,
    cancellation: false,
    payment: false,
  })

  const toggleSection = (section: keyof typeof collapsedSections) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }
  // 定义卡消耗设置类型
  interface CardConsumption {
    countCardConsumption: number;
    valueCardConsumption: number;
    useDefaultConsumption: boolean;
    enableAdvancedConsumption: boolean;
    customConsumption: Record<string, { count?: number; value?: number }>;
  }

  // 定义表单数据类型
  interface CourseFormData {
    id: string;
    name: string;
    type: string;
    coach: string;
    venue: string;
    price: string;
    capacity: string;
    duration: string; // 课程时长（分钟）
    description: string;
    isRecurring: boolean;
    startDate: Date;
    endDate: Date | null;
    days: string[];
    startTime: string;
    endTime: string;
    tags: string[];
    requiresEquipment: boolean;
    equipmentList: string;
    notes: string;
    isPrivate: boolean;
    allowWaitlist: boolean;
    maxWaitlist: string;
    cancellationPolicy: string;
    prerequisites: string;
    level: string;
    courseColor: string; // 课程颜色
    coverImage: string;
    membershipCards: string[];
    cardConsumption: CardConsumption;
    // 预约设置
    bookingMode: string; // 预约方式：自主预约、限制预约时间
    bookingTime: string; // 预约时间
    bookingDeadline: string; // 预约截止时间
    // 排队候补设置
    enableWaitlist: boolean; // 排队候补开关
    maxWaitlistCount: string; // 最大排队人数
    waitlistStopTime: string; // 停止排队时间
    waitlistCancelTime: string; // 停止候补时间
    // 签到设置
    enableSelfCheckin: boolean; // 自主签到开关
    checkinTime: string; // 签到时间
    enableScanCheckin: boolean; // 扫码签到开关
    scanCheckinTime: string; // 扫码签到时间
    // 取消设置
    cancellationTime: string; // 取消时间
    // 付费课程
    isPaidCourse: boolean; // 付费课程开关
    paymentType: string; // 付费类型：关闭
  }

  const [formData, setFormData] = useState<CourseFormData>({
    id: "",
    name: "",
    type: "basic",
    coach: "1",
    venue: "1",
    price: "",
    capacity: "",
    duration: "90", // 课程时长（分钟）
    description: "",
    isRecurring: false,
    startDate: new Date(),
    endDate: null,
    days: [],
    startTime: "10:00",
    endTime: "11:30",
    tags: [],
    requiresEquipment: false,
    equipmentList: "",
    notes: "",
    isPrivate: false,
    allowWaitlist: true,
    maxWaitlist: "5",
    cancellationPolicy: "24小时前可取消",
    prerequisites: "",
    level: "1",
    courseColor: "#4285F4",
    coverImage: "",
    membershipCards: [],
    cardConsumption: {
      countCardConsumption: 1,
      valueCardConsumption: 100,
      useDefaultConsumption: true,
      enableAdvancedConsumption: false,
      customConsumption: {},
    },
    // 预约设置
    bookingMode: "自主预约",
    bookingTime: "每天18:00 - 20:00 之间接受预约",
    bookingDeadline: "课程预约人数未满，会员需早于开课前120分钟自主预约",
    // 排队候补设置
    enableWaitlist: true,
    maxWaitlistCount: "12",
    waitlistStopTime: "开课前30分钟",
    waitlistCancelTime: "开课前30分钟",
    // 签到设置
    enableSelfCheckin: true,
    checkinTime: "课程当天，会员可随时签到",
    enableScanCheckin: true,
    scanCheckinTime: "课程当天，会员可在课程开始前10分钟至课程结束后15分钟内签到",
    // 取消设置
    cancellationTime: "会员可在课程开始前35分钟自主取消预约",
    // 付费课程
    isPaidCourse: false,
    paymentType: "关闭",
  })

  // 初始化表单数据
  useEffect(() => {
    if (course) {
      // 编辑模式：填充现有课程数据
      setFormData({
        ...formData,
        id: course.id || "",
        name: course.name || "",
        type: course.type || "basic",
        coach: course.coach || "1",
        venue: course.venue || "1",
        price: typeof course.price === 'string'
          ? course.price.replace("¥", "").replace("/次", "")
          : course.price?.toString() || "",
        capacity: course.capacity?.toString() || "",
        duration: course.duration?.toString() || "90",
        description: course.description || "",
        level: course.level?.toString() || "1",
        courseColor: course.courseColor || "#4285F4",
      })
    } else {
      // 添加模式：重置表单
      setFormData({
        id: "",
        name: "",
        type: "basic",
        coach: "1",
        venue: "1",
        price: "",
        capacity: "",
        duration: "90",
        description: "",
        isRecurring: false,
        startDate: new Date(),
        endDate: null,
        days: [],
        startTime: "10:00",
        endTime: "11:30",
        tags: [],
        requiresEquipment: false,
        equipmentList: "",
        notes: "",
        isPrivate: false,
        allowWaitlist: true,
        maxWaitlist: "5",
        cancellationPolicy: "24小时前可取消",
        prerequisites: "",
        level: "1",
        courseColor: "#4285F4",
        coverImage: "",
        membershipCards: [],
        cardConsumption: {
          countCardConsumption: 1,
          valueCardConsumption: 100,
          useDefaultConsumption: true,
          enableAdvancedConsumption: false,
          customConsumption: {},
        },
        // 预约设置
        bookingMode: "自主预约",
        bookingTime: "每天18:00 - 20:00 之间接受预约",
        bookingDeadline: "课程预约人数未满，会员需早于开课前120分钟自主预约",
        // 排队候补设置
        enableWaitlist: true,
        maxWaitlistCount: "12",
        waitlistStopTime: "开课前30分钟",
        waitlistCancelTime: "开课前30分钟",
        // 签到设置
        enableSelfCheckin: true,
        checkinTime: "课程当天，会员可随时签到",
        enableScanCheckin: true,
        scanCheckinTime: "课程当天，会员可在课程开始前10分钟至课程结束后15分钟内签到",
        // 取消设置
        cancellationTime: "会员可在课程开始前35分钟自主取消预约",
        // 付费课程
        isPaidCourse: false,
        paymentType: "关闭",
      })
    }
  }, [course])

  // 处理表单字段变更
  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }



  // 处理标签添加
  const handleAddTag = (tag: string) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tag],
      }))
    }
  }

  // 处理标签删除
  const handleRemoveTag = (tag: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((t) => t !== tag),
    }))
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 表单验证
    if (!formData.name) {
      toast({
        title: "表单验证错误",
        description: "请输入课程名称",
        variant: "destructive",
      })
      return
    }

    // 检查课程名称是否重复（编辑时排除自身）
    const isDuplicateName = existingCourses.some(existingCourse =>
      existingCourse.name === formData.name && existingCourse.id !== formData.id
    )

    if (isDuplicateName) {
      toast({
        title: "表单验证错误",
        description: "课程名称已存在，请使用其他名称",
        variant: "destructive",
      })
      return
    }

    if (!formData.price) {
      toast({
        title: "表单验证错误",
        description: "请输入课程价格",
        variant: "destructive",
      })
      return
    }

    if (!formData.capacity) {
      toast({
        title: "表单验证错误",
        description: "请输入课程容量",
        variant: "destructive",
      })
      return
    }

    if (!formData.duration) {
      toast({
        title: "表单验证错误",
        description: "请输入课程时长",
        variant: "destructive",
      })
      return
    }

    try {
      setLoading(true)

      // 严格按照API文档的格式构建请求数据
      const courseData = {
        title: formData.name,
        description: formData.description || "", // 确保不是undefined
        price: Number(formData.price),
        cover: "", // 默认空字符串
        type: formData.type,
        content: "", // 默认空字符串
        duration: Number(formData.duration),
        level: formData.level,
        coachId: formData.coach, // 注意这里使用coachId而不是coach
        time: `${format(formData.startDate, "yyyy-MM-dd")} ${formData.startTime}-${formData.endTime}`,
        venue: formData.venue,
        capacity: Number(formData.capacity),
        membershipCards: formData.membershipCards // 添加会员卡关联数据
      }

      // 添加调试日志
      console.log('按照API文档格式的请求数据:', courseData);

      // 使用courseApi进行请求
      const response = await courseApi.createCourse(courseData);
      console.log('请求成功:', response);

      toast({
        title: "创建成功",
        description: "新课程已成功创建",
      });

      // 将API响应数据格式化为前端所需的格式
      const savedCourse = {
        id: response?.data?.id || `temp-${Date.now()}`,
        name: formData.name,
        type: formData.type,
        coach: formData.coach,
        venue: formData.venue,
        price: `¥${formData.price}/次`,
        capacity: parseInt(formData.capacity),
        description: formData.description,
        membershipCards: formData.membershipCards,
      };

      // 调用保存回调
      onSave(savedCourse);

    } catch (error: any) {
      console.error('保存课程失败:', error);

      toast({
        title: "创建失败",
        description: error.message || "操作过程中发生错误，请重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={cn(
        "sm:max-w-[800px] sm:max-h-[90vh]",
        "flex flex-col"
      )}>
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{course ? "编辑课程" : "添加课程"}</DialogTitle>
          <DialogDescription>{course ? "修改课程信息" : "填写以下信息创建新课程"}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex flex-col flex-1 min-h-0">
            <TabsList className="grid w-full grid-cols-4 flex-shrink-0">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="details">详细信息</TabsTrigger>
              <TabsTrigger value="membership">会员卡关联</TabsTrigger>
              <TabsTrigger value="settings">高级设置</TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-y-auto min-h-0">{/* 滚动容器 */}

            <TabsContent value="basic" className="space-y-6 py-4">
              {/* 课程图片 + 表单字段 */}
              <div className="grid grid-cols-3 gap-6">
                {/* 左侧：课程图片 (16:9比例) */}
                <div className="space-y-2">
                  <Label htmlFor="coverImage">课程封面图片</Label>
                  {!formData.coverImage ? (
                    <div className="relative">
                      <img
                        src="https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=225&fit=crop&crop=center"
                        alt="默认课程封面"
                        className="w-full aspect-video object-cover rounded-md border border-gray-200"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center rounded-md">
                        <div className="text-center">
                          <FileUploader
                            onFilesUploaded={(files) => {
                              if (files.length > 0) {
                                // 在实际应用中，这里会上传图片并获取URL
                                handleChange("coverImage", URL.createObjectURL(files[0]))
                              }
                            }}
                            maxFiles={1}
                            maxSize={5 * 1024 * 1024} // 5MB
                            accept="image/*"
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="relative rounded-md overflow-hidden border border-gray-200">
                      <img
                        src={formData.coverImage}
                        alt="课程封面"
                        className="w-full aspect-video object-cover"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                        <Button
                          type="button"
                          variant="secondary"
                          size="sm"
                          onClick={() => handleChange("coverImage", "")}
                        >
                          <X className="h-4 w-4 mr-2" />
                          移除图片
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                {/* 右侧：表单字段 (2行2列) */}
                <div className="col-span-2 grid grid-cols-2 grid-rows-2 gap-4">
                  {/* 第一行 */}
                  <div className="space-y-2">
                    <Label htmlFor="name">
                      课程名称 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleChange("name", e.target.value)}
                      placeholder="输入课程名称"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type">
                      课程类型 <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.type} onValueChange={(value) => handleChange("type", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择课程类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {courseTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            <div className="flex items-center gap-2">
                              <div className="h-3 w-3 rounded-full" style={{ backgroundColor: type.color }} />
                              <span>{type.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* 第二行 */}
                  <div className="space-y-2">
                    <Label htmlFor="coach">
                      教练 <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.coach} onValueChange={(value) => handleChange("coach", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择教练" />
                      </SelectTrigger>
                      <SelectContent>
                        {coaches.map((coach) => (
                          <SelectItem key={coach.id} value={coach.id}>
                            {coach.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="venue">
                      场地 <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.venue} onValueChange={(value) => handleChange("venue", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择场地" />
                      </SelectTrigger>
                      <SelectContent>
                        {venues.map((venue) => (
                          <SelectItem key={venue.id} value={venue.id}>
                            {venue.name} (容量: {venue.capacity})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* 第二行：价格、容量、课程时长 (3列) */}
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">
                    价格 (元/次) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="price"
                    value={formData.price}
                    onChange={(e) => handleChange("price", e.target.value)}
                    placeholder="输入价格"
                    type="number"
                    min="0"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="capacity">
                    容量 <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="capacity"
                    value={formData.capacity}
                    onChange={(e) => handleChange("capacity", e.target.value)}
                    placeholder="输入容量"
                    type="number"
                    min="1"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="duration">
                    课程时长 <span className="text-red-500">*</span>
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="duration"
                      value={formData.duration}
                      onChange={(e) => handleChange("duration", e.target.value)}
                      placeholder="输入时长"
                      type="number"
                      min="15"
                      max="300"
                      required
                      className="flex-1"
                    />
                    <span className="text-sm text-muted-foreground">分钟</span>
                  </div>
                </div>
              </div>

              {/* 第三行：课程难度、课程颜色 (2列) */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="level">课程难度</Label>
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          type="button"
                          onClick={() => handleChange("level", star.toString())}
                          className={`text-lg transition-colors ${
                            parseInt(formData.level || "1") >= star
                              ? "text-yellow-400 hover:text-yellow-500"
                              : "text-gray-300 hover:text-gray-400"
                          }`}
                        >
                          ★
                        </button>
                      ))}
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {parseInt(formData.level || "1") === 1 && "初级"}
                      {parseInt(formData.level || "1") === 2 && "入门"}
                      {parseInt(formData.level || "1") === 3 && "中级"}
                      {parseInt(formData.level || "1") === 4 && "高级"}
                      {parseInt(formData.level || "1") === 5 && "专业"}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="courseColor">课程颜色</Label>
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-2">
                      {[
                        "#4285F4", "#34A853", "#FBBC05", "#EA4335", "#9C27B0",
                        "#FF6D91", "#00BCD4", "#FF9800", "#795548", "#607D8B"
                      ].map((color) => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => handleChange("courseColor", color)}
                          className={`w-6 h-6 rounded-full border-2 transition-all ${
                            formData.courseColor === color
                              ? "border-gray-800 scale-110"
                              : "border-gray-300 hover:border-gray-500"
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <div
                      className="w-4 h-4 rounded-full border border-gray-300"
                      style={{ backgroundColor: formData.courseColor || "#4285F4" }}
                    />
                  </div>
                </div>
              </div>

              {/* 底部：课程描述 */}
              <div className="space-y-2">
                <Label htmlFor="description">课程描述</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleChange("description", e.target.value)}
                  placeholder="输入课程描述"
                  rows={4}
                />
              </div>
            </TabsContent>



            <TabsContent value="details" className="space-y-4 py-4">
              <div className="space-y-2">
                <Label>课程标签</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X className="h-3 w-3 cursor-pointer" onClick={() => handleRemoveTag(tag)} />
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    id="tag"
                    placeholder="输入标签"
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault()
                        handleAddTag((e.target as HTMLInputElement).value)
                        ;(e.target as HTMLInputElement).value = ""
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={(e) => {
                      const input = e.currentTarget.previousElementSibling as HTMLInputElement
                      handleAddTag(input.value)
                      input.value = ""
                    }}
                  >
                    添加
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="requiresEquipment"
                    checked={formData.requiresEquipment}
                    onCheckedChange={(checked) => handleChange("requiresEquipment", checked)}
                  />
                  <Label htmlFor="requiresEquipment">需要特殊设备</Label>
                </div>

                {formData.requiresEquipment && (
                  <Textarea
                    id="equipmentList"
                    value={formData.equipmentList}
                    onChange={(e) => handleChange("equipmentList", e.target.value)}
                    placeholder="列出所需设备，每行一项"
                    rows={3}
                  />
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="prerequisites">课程前提条件</Label>
                <Textarea
                  id="prerequisites"
                  value={formData.prerequisites}
                  onChange={(e) => handleChange("prerequisites", e.target.value)}
                  placeholder="输入参加课程所需的前提条件"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">内部备注</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleChange("notes", e.target.value)}
                  placeholder="仅管理员可见的内部备注"
                  rows={3}
                />
              </div>
            </TabsContent>

            <TabsContent value="membership" className="space-y-4 py-4">
              <CardAssociationTabs
                selectedCards={formData.membershipCards}
                onSelectedCardsChange={(selectedCards) => handleChange("membershipCards", selectedCards)}
                cardConsumption={formData.cardConsumption}
                onCardConsumptionChange={(cardConsumption) => handleChange("cardConsumption", cardConsumption)}
              />
            </TabsContent>

            <TabsContent value="settings" className="space-y-4 py-4">
              <div className="space-y-4">
                {/* 预约设置 */}
                <Collapsible open={!collapsedSections.booking} onOpenChange={() => toggleSection('booking')}>
                  <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                    <h3 className="text-base font-medium text-gray-900">预约设置</h3>
                    {collapsedSections.booking ? (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 pt-3">
                    <div className="space-y-2">
                      <Label htmlFor="bookingMode">预约方式</Label>
                      <Select value={formData.bookingMode} onValueChange={(value) => handleChange("bookingMode", value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="选择预约方式" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="自主预约">自主预约</SelectItem>
                          <SelectItem value="限制预约时间">限制预约时间</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bookingTime">预约时间</Label>
                      <Input
                        id="bookingTime"
                        value={formData.bookingTime}
                        onChange={(e) => handleChange("bookingTime", e.target.value)}
                        placeholder="输入预约时间"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bookingDeadline">预约截止时间</Label>
                      <Input
                        id="bookingDeadline"
                        value={formData.bookingDeadline}
                        onChange={(e) => handleChange("bookingDeadline", e.target.value)}
                        placeholder="输入预约截止时间"
                      />
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                {/* 排队候补设置 */}
                <Collapsible open={!collapsedSections.waitlist} onOpenChange={() => toggleSection('waitlist')}>
                  <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                    <h3 className="text-base font-medium text-gray-900">排队候补设置</h3>
                    {collapsedSections.waitlist ? (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 pt-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="enableWaitlist"
                        checked={formData.enableWaitlist}
                        onCheckedChange={(checked) => handleChange("enableWaitlist", checked)}
                      />
                      <Label htmlFor="enableWaitlist">排队候补</Label>
                      <span className="text-sm text-muted-foreground">开启</span>
                    </div>

                    {formData.enableWaitlist && (
                      <>
                        <div className="space-y-2 pl-6">
                          <Label htmlFor="maxWaitlistCount">最大排队人数</Label>
                          <div className="flex items-center space-x-2">
                            <Input
                              id="maxWaitlistCount"
                              value={formData.maxWaitlistCount}
                              onChange={(e) => handleChange("maxWaitlistCount", e.target.value)}
                              type="number"
                              min="1"
                              className="w-20"
                            />
                            <span className="text-sm text-muted-foreground">人</span>
                          </div>
                        </div>

                        <div className="space-y-2 pl-6">
                          <Label htmlFor="waitlistStopTime">停止排队时间</Label>
                          <Input
                            id="waitlistStopTime"
                            value={formData.waitlistStopTime}
                            onChange={(e) => handleChange("waitlistStopTime", e.target.value)}
                            placeholder="输入停止排队时间"
                          />
                        </div>

                        <div className="space-y-2 pl-6">
                          <Label htmlFor="waitlistCancelTime">停止候补时间</Label>
                          <Input
                            id="waitlistCancelTime"
                            value={formData.waitlistCancelTime}
                            onChange={(e) => handleChange("waitlistCancelTime", e.target.value)}
                            placeholder="输入停止候补时间"
                          />
                        </div>
                      </>
                    )}
                  </CollapsibleContent>
                </Collapsible>

                {/* 签到设置 */}
                <Collapsible open={!collapsedSections.checkin} onOpenChange={() => toggleSection('checkin')}>
                  <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                    <h3 className="text-base font-medium text-gray-900">签到设置</h3>
                    {collapsedSections.checkin ? (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 pt-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="enableSelfCheckin"
                        checked={formData.enableSelfCheckin}
                        onCheckedChange={(checked) => handleChange("enableSelfCheckin", checked)}
                      />
                      <Label htmlFor="enableSelfCheckin">自主签到</Label>
                      <span className="text-sm text-muted-foreground">开启</span>
                    </div>

                    {formData.enableSelfCheckin && (
                      <div className="space-y-2 pl-6">
                        <Label htmlFor="checkinTime">签到时间</Label>
                        <Input
                          id="checkinTime"
                          value={formData.checkinTime}
                          onChange={(e) => handleChange("checkinTime", e.target.value)}
                          placeholder="输入签到时间"
                        />
                      </div>
                    )}

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="enableScanCheckin"
                        checked={formData.enableScanCheckin}
                        onCheckedChange={(checked) => handleChange("enableScanCheckin", checked)}
                      />
                      <Label htmlFor="enableScanCheckin">扫码签到</Label>
                      <span className="text-sm text-muted-foreground">开启</span>
                    </div>

                    {formData.enableScanCheckin && (
                      <div className="space-y-2 pl-6">
                        <Label htmlFor="scanCheckinTime">签到时间</Label>
                        <Input
                          id="scanCheckinTime"
                          value={formData.scanCheckinTime}
                          onChange={(e) => handleChange("scanCheckinTime", e.target.value)}
                          placeholder="输入扫码签到时间"
                        />
                      </div>
                    )}
                  </CollapsibleContent>
                </Collapsible>

                {/* 取消设置 */}
                <Collapsible open={!collapsedSections.cancellation} onOpenChange={() => toggleSection('cancellation')}>
                  <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                    <h3 className="text-base font-medium text-gray-900">取消设置</h3>
                    {collapsedSections.cancellation ? (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 pt-3">
                    <div className="space-y-2">
                      <Label htmlFor="cancellationTime">取消时间</Label>
                      <Input
                        id="cancellationTime"
                        value={formData.cancellationTime}
                        onChange={(e) => handleChange("cancellationTime", e.target.value)}
                        placeholder="输入取消时间"
                      />
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                {/* 付费课程 */}
                <Collapsible open={!collapsedSections.payment} onOpenChange={() => toggleSection('payment')}>
                  <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                    <h3 className="text-base font-medium text-gray-900">付费课程</h3>
                    {collapsedSections.payment ? (
                      <ChevronRight className="h-4 w-4 text-gray-500" />
                    ) : (
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    )}
                  </CollapsibleTrigger>
                  <CollapsibleContent className="space-y-3 pt-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isPaidCourse"
                        checked={formData.isPaidCourse}
                        onCheckedChange={(checked) => handleChange("isPaidCourse", checked)}
                      />
                      <Label htmlFor="isPaidCourse">付费课程</Label>
                      <span className="text-sm text-muted-foreground">{formData.isPaidCourse ? "开启" : "关闭"}</span>
                    </div>

                    {formData.isPaidCourse && (
                      <div className="space-y-2 pl-6">
                        <Label htmlFor="paymentType">付费类型</Label>
                        <Select value={formData.paymentType} onValueChange={(value) => handleChange("paymentType", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="选择付费类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="关闭">关闭</SelectItem>
                            <SelectItem value="单次付费">单次付费</SelectItem>
                            <SelectItem value="包月付费">包月付费</SelectItem>
                            <SelectItem value="包年付费">包年付费</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </CollapsibleContent>
                </Collapsible>

                {/* 预约取消设置提示 */}
                <div className="bg-orange-50 border border-orange-200 rounded-md p-4">
                  <div className="flex items-start space-x-2">
                    <div className="w-4 h-4 rounded-full bg-orange-400 mt-0.5 flex-shrink-0"></div>
                    <div className="text-sm text-orange-800">
                      <p className="font-medium">预约取消设置仅针对会员自行的预约，无卡支付行的预约不支持会员自主预约取消</p>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            </div>{/* 关闭滚动容器 */}
          </Tabs>

          <DialogFooter className="flex justify-between items-center pt-4 flex-shrink-0">
            <div className="text-sm text-muted-foreground">
              <span className="text-red-500">*</span> 表示必填字段
            </div>
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {course ? "更新课程" : "创建课程"}
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

