"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  MoreHorizontal,
  Pencil,
  Users,
  Clock,
  Calendar,
  User,
  CreditCard,
  Tag,
  Receipt,
  MessageSquare,
  Trash2,
  Eye,
  EyeOff,
  Copy,
  FileText,
  BarChart2
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// 获取状态标签
const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return "销售中"
    case "inactive":
      return "已下架"
    case "expired":
      return "已过期"
    case "frozen":
      return "已冻结"
    case "onLeave":
      return "请假中"
    case "refunded":
      return "已退卡"
    default:
      return "未知"
  }
}

interface MemberCardGridProps {
  cards: any[]
  onCardClick: (card: any) => void
  onQuickAction?: (action: string, card: any) => void
}

export function MemberCardGrid({ cards, onCardClick, onQuickAction }: MemberCardGridProps) {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
      {cards.map((card) => (
        <Card key={card.id} className="overflow-hidden">
          <CardHeader className="relative p-0">
            <div className="absolute left-0 top-0 h-1 w-full" style={{ backgroundColor: card.color }} />
            <div
              className="flex cursor-pointer flex-col items-center p-6 pt-8 hover:bg-muted/50"
              onClick={() => onCardClick(card)}
            >
              <div
                className="flex h-16 w-16 items-center justify-center rounded-full"
                style={{ backgroundColor: card.color + "20" }}
              >
                <div className="h-12 w-12 rounded-full" style={{ backgroundColor: card.color }} />
              </div>
              <h3 className="mt-2 text-lg font-semibold">{card.name}</h3>
              <p className="text-center text-sm text-muted-foreground">{card.description}</p>
              <Badge className="mt-2" variant={card.status === "active" ? "default" : "secondary"}>
                {getStatusBadge(card.status)}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="space-y-1">
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <p>有效期</p>
                </div>
                <p className="font-medium">{card.validity}</p>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  <p>使用限制</p>
                </div>
                <p className="font-medium">{card.limit}</p>
              </div>
              <div className="space-y-1">
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Users className="h-3 w-3" />
                  <p>持卡会员</p>
                </div>
                <p className="font-medium">{card.members}</p>
              </div>
              <div className="space-y-1">
                <p className="text-muted-foreground">价格</p>
                <div>
                  <span className="font-medium">{card.price}</span>
                  {card.originalPrice !== card.price && (
                    <span className="ml-1 text-xs text-muted-foreground line-through">{card.originalPrice}</span>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between p-4 pt-0">
            <Button variant="outline" size="sm" onClick={() => onCardClick(card)}>
              查看详情
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>操作</DropdownMenuLabel>
                <DropdownMenuSeparator />

                <DropdownMenuItem onClick={() => onCardClick(card)}>
                  <Eye className="mr-2 h-4 w-4" />
                  查看详情
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onQuickAction && onQuickAction("edit", card)}>
                  <Pencil className="mr-2 h-4 w-4" />
                  编辑信息
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onQuickAction && onQuickAction("copy", card)}>
                  <Copy className="mr-2 h-4 w-4" />
                  复制卡模板
                </DropdownMenuItem>

                <DropdownMenuItem onClick={() => onQuickAction && onQuickAction("member-list", card)}>
                  <Users className="mr-2 h-4 w-4" />
                  持卡会员列表
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onQuickAction && onQuickAction("sales-analysis", card)}>
                  <BarChart2 className="mr-2 h-4 w-4" />
                  销售数据分析
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onQuickAction && onQuickAction("export-report", card)}>
                  <FileText className="mr-2 h-4 w-4" />
                  导出会员报表
                </DropdownMenuItem>

                {card.status === "active" ? (
                  <DropdownMenuItem onClick={() => onQuickAction && onQuickAction("change-status", card)}>
                    <EyeOff className="mr-2 h-4 w-4" />
                    下架会员卡
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem onClick={() => onQuickAction && onQuickAction("change-status", card)}>
                    <Eye className="mr-2 h-4 w-4" />
                    上架会员卡
                  </DropdownMenuItem>
                )}

                <DropdownMenuItem
                  className="text-red-600"
                  onClick={() => onQuickAction && onQuickAction("delete", card)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  删除会员卡
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}

