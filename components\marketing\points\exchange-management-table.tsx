"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar, ChevronLeft, ChevronRight, Download, Filter, Search, Truck } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

// Update the ExchangeManagementTable component to enhance the View shipping information and Details dialogs

// 1. Add imports for dialogs at the top of the file
import { Dialog, DialogContent, <PERSON>alogDescription, Di<PERSON><PERSON>eader, Di<PERSON>T<PERSON><PERSON> } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"

// Add these imports at the top
import { CheckIcon, Printer, InfoIcon, RefreshCcw, ShoppingBag } from "lucide-react"

// 2. Replace the ExchangeManagementTable function with the updated version including dialogs
export function ExchangeManagementTable() {
  const [date, setDate] = useState<Date>()
  const [shippingDialogOpen, setShippingDialogOpen] = useState(false)
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<(typeof exchangeRecords)[0] | null>(null)

  const handleViewShipping = (record: (typeof exchangeRecords)[0]) => {
    setSelectedRecord(record)
    setShippingDialogOpen(true)
  }

  const handleViewDetail = (record: (typeof exchangeRecords)[0]) => {
    setSelectedRecord(record)
    setDetailDialogOpen(true)
  }

  const handleProcessRecord = (record: (typeof exchangeRecords)[0]) => {
    // This would typically update the record status via an API call
    console.log("Processing record:", record)
    // For demo purposes, open the details dialog
    handleViewDetail(record)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="relative w-[300px]">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="搜索会员姓名或手机号..." className="pl-8" />
          </div>

          <Select defaultValue="all">
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="商品类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="course">课程</SelectItem>
              <SelectItem value="product">实物商品</SelectItem>
              <SelectItem value="coupon">优惠券</SelectItem>
              <SelectItem value="service">增值服务</SelectItem>
            </SelectContent>
          </Select>

          <Select defaultValue="all">
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="兑换状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="pending">待处理</SelectItem>
              <SelectItem value="processing">处理中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
              <SelectItem value="cancelled">已取消</SelectItem>
            </SelectContent>
          </Select>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-[180px] justify-start text-left font-normal", !date && "text-muted-foreground")}
              >
                <Calendar className="mr-2 h-4 w-4" />
                {date ? format(date, "yyyy年MM月dd日") : "选择日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent mode="single" selected={date} onSelect={setDate} />
            </PopoverContent>
          </Popover>

          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          导出记录
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>会员信息</TableHead>
              <TableHead>兑换商品</TableHead>
              <TableHead>消耗积分</TableHead>
              <TableHead>兑换时间</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>收货信息</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {exchangeRecords.map((record) => (
              <TableRow key={record.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={record.memberAvatar} alt={record.memberName} />
                      <AvatarFallback>{record.memberName.substring(0, 1)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{record.memberName}</div>
                      <div className="text-xs text-muted-foreground">{record.memberPhone}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Badge variant={getItemTypeBadgeVariant(record.itemType)}>{getItemTypeName(record.itemType)}</Badge>
                    <span>{record.itemName}</span>
                  </div>
                </TableCell>
                <TableCell>{record.points}</TableCell>
                <TableCell>{record.date}</TableCell>
                <TableCell>
                  <Badge variant={getStatusBadgeVariant(record.status)}>{getStatusName(record.status)}</Badge>
                </TableCell>
                <TableCell>
                  {record.itemType === "product" ? (
                    <Button variant="ghost" size="sm" className="h-8 gap-1" onClick={() => handleViewShipping(record)}>
                      <Truck className="h-3 w-3" />
                      查看
                    </Button>
                  ) : (
                    <span className="text-muted-foreground text-sm">-</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="sm" onClick={() => handleViewDetail(record)}>
                      详情
                    </Button>
                    {record.status === "pending" && (
                      <Button variant="ghost" size="sm" onClick={() => handleProcessRecord(record)}>
                        处理
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">显示 1 至 10 条，共 38 条记录</div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" disabled>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" className="h-8 w-8">
            1
          </Button>
          <Button variant="outline" size="sm" className="h-8 w-8">
            2
          </Button>
          <Button variant="outline" size="sm" className="h-8 w-8">
            3
          </Button>
          <Button variant="outline" size="sm" className="h-8 w-8">
            4
          </Button>
          <Button variant="outline" size="icon">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Shipping Information Dialog */}
      <Dialog open={shippingDialogOpen} onOpenChange={setShippingDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>物流配送信息</DialogTitle>
            <DialogDescription>查看商品配送和物流追踪信息</DialogDescription>
          </DialogHeader>
          {selectedRecord && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">订单信息</h4>
                  <p className="text-sm">订单编号: EX{selectedRecord.id}2023</p>
                  <p className="text-sm">兑换时间: {selectedRecord.date}</p>
                  <p className="text-sm">商品名称: {selectedRecord.itemName}</p>
                  <p className="text-sm">兑换状态: {getStatusName(selectedRecord.status)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">收货人信息</h4>
                  <p className="text-sm">姓名: {selectedRecord.memberName}</p>
                  <p className="text-sm">电话: {selectedRecord.memberPhone}</p>
                  <p className="text-sm">地址: 上海市浦东新区张江高科技园区博云路123号4幢</p>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="text-sm font-medium mb-2">物流信息</h4>
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <span className="text-sm font-medium">快递公司: 顺丰速运</span>
                  </div>
                  <div>
                    <span className="text-sm">快递单号: SF1234567890123</span>
                  </div>
                </div>

                <Card>
                  <CardContent className="p-4">
                    <ScrollArea className="h-[200px] pr-4">
                      <div className="space-y-4">
                        {logisticsUpdates.map((update, index) => (
                          <div key={index} className="relative pl-6 pb-4">
                            {index !== logisticsUpdates.length - 1 && (
                              <div className="absolute left-[11px] top-[22px] bottom-0 w-[1px] bg-muted" />
                            )}
                            <div className="flex gap-3 items-start">
                              <div
                                className={`w-5 h-5 rounded-full mt-0.5 flex items-center justify-center ${index === 0 ? "bg-green-500" : "bg-muted"}`}
                              >
                                {index === 0 ? (
                                  <CheckIcon className="h-3 w-3 text-white" />
                                ) : (
                                  <div className="w-2 h-2 rounded-full bg-muted-foreground" />
                                )}
                              </div>
                              <div>
                                <p className={`text-sm font-medium ${index === 0 ? "text-green-500" : ""}`}>
                                  {update.status}
                                </p>
                                <p className="text-xs text-muted-foreground">{update.time}</p>
                                <p className="text-sm mt-1">{update.description}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShippingDialogOpen(false)}>
                  关闭
                </Button>
                <Button variant="outline">
                  <Printer className="h-4 w-4 mr-2" />
                  打印物流单
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Order Detail Dialog */}
      <Dialog open={detailDialogOpen} onOpenChange={setDetailDialogOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>兑换详情</DialogTitle>
            <DialogDescription>查看积分兑换订单详细信息</DialogDescription>
          </DialogHeader>
          {selectedRecord && (
            <div className="space-y-4">
              <Tabs defaultValue="details">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">基本信息</TabsTrigger>
                  <TabsTrigger value="shipping">配送信息</TabsTrigger>
                  <TabsTrigger value="operation">操作记录</TabsTrigger>
                </TabsList>

                <TabsContent value="details" className="space-y-4 pt-4">
                  <div className="grid grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="p-4 pb-2">
                        <CardTitle className="text-sm">兑换信息</CardTitle>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <p className="text-sm mb-1">
                          订单编号: <span className="font-medium">EX{selectedRecord.id}2023</span>
                        </p>
                        <p className="text-sm mb-1">
                          兑换时间: <span className="font-medium">{selectedRecord.date}</span>
                        </p>
                        <p className="text-sm mb-1">
                          消耗积分: <span className="font-medium">{selectedRecord.points}</span>
                        </p>
                        <p className="text-sm mb-1">
                          订单状态:
                          <Badge className="ml-1" variant={getStatusBadgeVariant(selectedRecord.status)}>
                            {getStatusName(selectedRecord.status)}
                          </Badge>
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="p-4 pb-2">
                        <CardTitle className="text-sm">会员信息</CardTitle>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="flex items-center gap-2 mb-2">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={selectedRecord.memberAvatar} alt={selectedRecord.memberName} />
                            <AvatarFallback>{selectedRecord.memberName.substring(0, 1)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{selectedRecord.memberName}</p>
                            <p className="text-xs text-muted-foreground">{selectedRecord.memberPhone}</p>
                          </div>
                        </div>
                        <p className="text-sm mb-1">
                          会员等级: <span className="font-medium">金牌会员</span>
                        </p>
                        <p className="text-sm mb-1">
                          剩余积分: <span className="font-medium">3,240</span>
                        </p>
                        <p className="text-sm mb-1">
                          会员ID: <span className="font-medium">M{selectedRecord.id}001</span>
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="p-4 pb-2">
                        <CardTitle className="text-sm">商品信息</CardTitle>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="h-12 w-12 rounded bg-muted flex items-center justify-center">
                            <ShoppingBag className="h-6 w-6 text-muted-foreground" />
                          </div>
                          <div>
                            <p className="font-medium">{selectedRecord.itemName}</p>
                            <Badge variant={getItemTypeBadgeVariant(selectedRecord.itemType)}>
                              {getItemTypeName(selectedRecord.itemType)}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm mb-1">
                          商品ID: <span className="font-medium">P{selectedRecord.id}002</span>
                        </p>
                        <p className="text-sm mb-1">
                          兑换数量: <span className="font-medium">1</span>
                        </p>
                        <p className="text-sm mb-1">
                          所需积分: <span className="font-medium">{selectedRecord.points}</span>
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  {selectedRecord.itemType === "product" && (
                    <Card>
                      <CardHeader className="p-4 pb-2">
                        <CardTitle className="text-sm">收货信息</CardTitle>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm mb-1">
                              收货人: <span className="font-medium">{selectedRecord.memberName}</span>
                            </p>
                            <p className="text-sm mb-1">
                              联系电话: <span className="font-medium">{selectedRecord.memberPhone}</span>
                            </p>
                            <p className="text-sm mb-1">
                              收货地址: <span className="font-medium">上海市浦东新区张江高科技园区博云路123号4幢</span>
                            </p>
                          </div>
                          <div>
                            <p className="text-sm mb-1">
                              物流公司: <span className="font-medium">顺丰速运</span>
                            </p>
                            <p className="text-sm mb-1">
                              物流单号: <span className="font-medium">SF1234567890123</span>
                            </p>
                            <p className="text-sm mb-1">
                              发货时间: <span className="font-medium">2023-04-22 14:20</span>
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {selectedRecord.itemType === "coupon" && (
                    <Card>
                      <CardHeader className="p-4 pb-2">
                        <CardTitle className="text-sm">优惠券信息</CardTitle>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm mb-1">
                              优惠券码: <span className="font-medium">YOGA8DISC2023</span>
                            </p>
                            <p className="text-sm mb-1">
                              面值: <span className="font-medium">8折优惠</span>
                            </p>
                            <p className="text-sm mb-1">
                              有效期: <span className="font-medium">2023-04-22 至 2023-05-22</span>
                            </p>
                          </div>
                          <div>
                            <p className="text-sm mb-1">
                              使用状���: <span className="font-medium">未使用</span>
                            </p>
                            <p className="text-sm mb-1">
                              适用范围: <span className="font-medium">会员月卡</span>
                            </p>
                            <p className="text-sm mb-1">
                              限制条件: <span className="font-medium">仅限新会员使用</span>
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {selectedRecord.itemType === "course" && (
                    <Card>
                      <CardHeader className="p-4 pb-2">
                        <CardTitle className="text-sm">课程信息</CardTitle>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm mb-1">
                              课程类型: <span className="font-medium">瑜伽体验课</span>
                            </p>
                            <p className="text-sm mb-1">
                              课程时长: <span className="font-medium">60分钟</span>
                            </p>
                            <p className="text-sm mb-1">
                              有效期: <span className="font-medium">2023-04-22 至 2023-07-22</span>
                            </p>
                          </div>
                          <div>
                            <p className="text-sm mb-1">
                              使用状态: <span className="font-medium">已使用</span>
                            </p>
                            <p className="text-sm mb-1">
                              上课时间: <span className="font-medium">2023-04-25 10:00</span>
                            </p>
                            <p className="text-sm mb-1">
                              上课教练: <span className="font-medium">王教练</span>
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <div className="flex justify-end gap-2">
                    {selectedRecord.status === "pending" && (
                      <Button onClick={() => console.log("处理订单", selectedRecord)}>处理订单</Button>
                    )}
                    {selectedRecord.status === "processing" && selectedRecord.itemType === "product" && (
                      <Button onClick={() => console.log("完成发货", selectedRecord)}>完成发货</Button>
                    )}
                    <Button variant="outline" onClick={() => setDetailDialogOpen(false)}>
                      关闭
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="shipping" className="space-y-4 pt-4">
                  {selectedRecord.itemType === "product" ? (
                    <>
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h3 className="text-lg font-medium">物流追踪</h3>
                          <p className="text-sm text-muted-foreground">快递单号: SF1234567890123 (顺丰速运)</p>
                        </div>
                        <Button variant="outline" size="sm">
                          <RefreshCcw className="h-4 w-4 mr-2" />
                          刷新物流
                        </Button>
                      </div>

                      <Card>
                        <CardContent className="p-4">
                          <ScrollArea className="h-[300px] pr-4">
                            <div className="space-y-4">
                              {logisticsUpdates.map((update, index) => (
                                <div key={index} className="relative pl-6 pb-4">
                                  {index !== logisticsUpdates.length - 1 && (
                                    <div className="absolute left-[11px] top-[22px] bottom-0 w-[1px] bg-muted" />
                                  )}
                                  <div className="flex gap-3 items-start">
                                    <div
                                      className={`w-5 h-5 rounded-full mt-0.5 flex items-center justify-center ${index === 0 ? "bg-green-500" : "bg-muted"}`}
                                    >
                                      {index === 0 ? (
                                        <CheckIcon className="h-3 w-3 text-white" />
                                      ) : (
                                        <div className="w-2 h-2 rounded-full bg-muted-foreground" />
                                      )}
                                    </div>
                                    <div>
                                      <p className={`text-sm font-medium ${index === 0 ? "text-green-500" : ""}`}>
                                        {update.status}
                                      </p>
                                      <p className="text-xs text-muted-foreground">{update.time}</p>
                                      <p className="text-sm mt-1">{update.description}</p>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12">
                      <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-4">
                        <InfoIcon className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium mb-1">非实物商品</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        该兑换商品为{getItemTypeName(selectedRecord.itemType)}，无需物流配送
                      </p>
                      {selectedRecord.itemType === "coupon" && <Button variant="outline">查看优惠券详情</Button>}
                      {selectedRecord.itemType === "course" && <Button variant="outline">查看课程详情</Button>}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="operation" className="space-y-4 pt-4">
                  <div className="mb-4">
                    <h3 className="text-lg font-medium">操作记录</h3>
                    <p className="text-sm text-muted-foreground">兑换订单的所有操作记录</p>
                  </div>

                  <Card>
                    <CardContent className="p-4">
                      <ScrollArea className="h-[300px] pr-4">
                        <div className="space-y-4">
                          {operationLogs.map((log, index) => (
                            <div key={index} className="relative pl-6 pb-4">
                              {index !== operationLogs.length - 1 && (
                                <div className="absolute left-[11px] top-[22px] bottom-0 w-[1px] bg-muted" />
                              )}
                              <div className="flex gap-3 items-start">
                                <div className="w-5 h-5 rounded-full mt-0.5 flex items-center justify-center bg-muted">
                                  <div className="w-2 h-2 rounded-full bg-muted-foreground" />
                                </div>
                                <div>
                                  <p className="text-sm font-medium">{log.action}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {log.time} · {log.operator}
                                  </p>
                                  <p className="text-sm mt-1">{log.description}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// 模拟数据
const exchangeRecords = [
  {
    id: 1,
    memberName: "张小明",
    memberPhone: "138****1234",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "瑜伽单次体验课",
    itemType: "course",
    points: 500,
    date: "2023-04-22 14:30",
    status: "completed",
  },
  {
    id: 2,
    memberName: "李华",
    memberPhone: "139****5678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "高级瑜伽垫",
    itemType: "product",
    points: 2000,
    date: "2023-04-22 09:15",
    status: "processing",
  },
  {
    id: 3,
    memberName: "王芳",
    memberPhone: "137****9012",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "会员月卡8折券",
    itemType: "coupon",
    points: 1500,
    date: "2023-04-21 16:45",
    status: "completed",
  },
  {
    id: 4,
    memberName: "赵敏",
    memberPhone: "136****3456",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "私教课1次",
    itemType: "course",
    points: 3000,
    date: "2023-04-21 10:00",
    status: "pending",
  },
  {
    id: 5,
    memberName: "刘伟",
    memberPhone: "135****7890",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "瑜伽服套装",
    itemType: "product",
    points: 2500,
    date: "2023-04-20 15:30",
    status: "processing",
  },
  {
    id: 6,
    memberName: "陈静",
    memberPhone: "134****2345",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "会员生日礼包",
    itemType: "product",
    points: 1000,
    date: "2023-04-20 11:20",
    status: "completed",
  },
  {
    id: 7,
    memberName: "张小明",
    memberPhone: "138****1234",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "瑜伽单次体验课",
    itemType: "course",
    points: 500,
    date: "2023-04-19 16:00",
    status: "cancelled",
  },
  {
    id: 8,
    memberName: "王芳",
    memberPhone: "137****9012",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "会员月卡8折券",
    itemType: "coupon",
    points: 1500,
    date: "2023-04-19 14:30",
    status: "completed",
  },
  {
    id: 9,
    memberName: "李华",
    memberPhone: "139****5678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "高级瑜伽垫",
    itemType: "product",
    points: 2000,
    date: "2023-04-18 14:15",
    status: "completed",
  },
  {
    id: 10,
    memberName: "赵敏",
    memberPhone: "136****3456",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    itemName: "私教课1次",
    itemType: "course",
    points: 3000,
    date: "2023-04-18 10:30",
    status: "completed",
  },
]

// Add these mock data for logistics tracking
const logisticsUpdates = [
  {
    status: "已签收",
    time: "2023-04-25 14:30",
    description: "您的快递已由本人签收，感谢使用顺丰快递，期待再次为您服务",
  },
  {
    status: "派送中",
    time: "2023-04-25 09:12",
    description: "快递员王师傅（电话：13800138000）正在为您派送，请保持电话畅通",
  },
  {
    status: "运输中",
    time: "2023-04-24 20:45",
    description: "快件已到达上海浦东集散中心",
  },
  {
    status: "运输中",
    time: "2023-04-24 08:30",
    description: "快件已从上海市发出，下一站上海浦东集散中心",
  },
  {
    status: "已揽收",
    time: "2023-04-23 16:20",
    description: "顺丰快递员已揽收",
  },
  {
    status: "待揽收",
    time: "2023-04-23 14:05",
    description: "卖家已发货",
  },
  {
    status: "处理中",
    time: "2023-04-22 10:15",
    description: "订单已确认，仓库正在打包中",
  },
]

// Add operation logs mock data
const operationLogs = [
  {
    action: "订单状态变更",
    time: "2023-04-25 14:30",
    operator: "系统",
    description: "订单状态从「处理中」变更为「已完成」",
  },
  {
    action: "物流信息更新",
    time: "2023-04-23 16:20",
    operator: "系统",
    description: "添加物流信息，快递单号：SF1234567890123（顺丰速运）",
  },
  {
    action: "发货处理",
    time: "2023-04-23 14:05",
    operator: "李管理员",
    description: "确认发货，发往：上海市浦东新区张江高科技园区",
  },
  {
    action: "订单状态变更",
    time: "2023-04-22 15:40",
    operator: "王管理员",
    description: "订单状态从「待处理」变更为「处理中」",
  },
  {
    action: "审核通过",
    time: "2023-04-22 15:35",
    operator: "王管理员",
    description: "积分兑换申请审核通过",
  },
  {
    action: "创建订单",
    time: "2023-04-22 09:15",
    operator: "系统",
    description: "会员「李华」创建积分兑换订单，兑换商品「高级瑜伽垫」",
  },
]

// 辅助函数
function getItemTypeBadgeVariant(type: string) {
  switch (type) {
    case "course":
      return "secondary"
    case "product":
      return "default"
    case "coupon":
      return "outline"
    case "service":
      return "success"
    default:
      return "default"
  }
}

function getItemTypeName(type: string) {
  switch (type) {
    case "course":
      return "课程"
    case "product":
      return "实物"
    case "coupon":
      return "优惠券"
    case "service":
      return "服务"
    default:
      return "其他"
  }
}

function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "pending":
      return "outline"
    case "processing":
      return "secondary"
    case "completed":
      return "success"
    case "cancelled":
      return "destructive"
    default:
      return "default"
  }
}

function getStatusName(status: string) {
  switch (status) {
    case "pending":
      return "待处理"
    case "processing":
      return "处理中"
    case "completed":
      return "已完成"
    case "cancelled":
      return "已取消"
    default:
      return "未知"
  }
}

