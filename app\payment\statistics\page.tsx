"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Download, ArrowUpDown, TrendingUp, TrendingDown, CreditCard, Wallet, AlertTriangle } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export default function PaymentStatisticsPage() {
  const [dateRange, setDateRange] = useState("30days")
  const [paymentType, setPaymentType] = useState("all")

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">支付统计分析</h1>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          导出报表
        </Button>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex flex-1 gap-4">
          <Select value={paymentType} onValueChange={setPaymentType}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="支付渠道" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部渠道</SelectItem>
              <SelectItem value="wechat">微信支付</SelectItem>
              <SelectItem value="alipay">支付宝</SelectItem>
              <SelectItem value="unionpay">银联支付</SelectItem>
              <SelectItem value="cash">现金支付</SelectItem>
            </SelectContent>
          </Select>

          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="thismonth">本月</SelectItem>
              <SelectItem value="lastmonth">上月</SelectItem>
              <SelectItem value="thisyear">今年</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总交易金额</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥128,430.00</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                较上期增长 12.5%
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">交易笔数</CardTitle>
            <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,284</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                较上期增长 8.3%
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">退款金额</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥5,430.00</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-red-500 flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                较上期增长 2.1%
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">退款率</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.2%</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 flex items-center">
                <TrendingDown className="mr-1 h-3 w-3" />
                较上期下降 0.5%
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="channel" className="space-y-4">
        <TabsList>
          <TabsTrigger value="channel">支付渠道分析</TabsTrigger>
          <TabsTrigger value="trend">支付趋势分析</TabsTrigger>
          <TabsTrigger value="refund">退款分析</TabsTrigger>
          <TabsTrigger value="conversion">转化率分析</TabsTrigger>
        </TabsList>

        <TabsContent value="channel" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付渠道分布</CardTitle>
              <CardDescription>各支付渠道交易金额和笔数分布</CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
              <div className="h-[300px]">
                {/* 这里是支付渠道分布图表 */}
                <div className="space-y-8">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-green-500"></div>
                        <span>微信支付</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>¥68,430.00</span>
                        <span className="text-muted-foreground">53.3%</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 rounded-full bg-green-500" style={{ width: "53.3%" }}></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                        <span>支付宝</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>¥42,580.00</span>
                        <span className="text-muted-foreground">33.2%</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 rounded-full bg-blue-500" style={{ width: "33.2%" }}></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-red-500"></div>
                        <span>银联支付</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>¥12,420.00</span>
                        <span className="text-muted-foreground">9.7%</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 rounded-full bg-red-500" style={{ width: "9.7%" }}></div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                        <span>现金支付</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <span>¥5,000.00</span>
                        <span className="text-muted-foreground">3.9%</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-muted">
                      <div className="h-2 rounded-full bg-yellow-500" style={{ width: "3.9%" }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>支付渠道交易笔数</CardTitle>
                <CardDescription>各支付渠道交易笔数统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  {/* 这里是支付渠道交易笔数图表 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>微信支付</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">684笔</span>
                        <Badge>53.3%</Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>支付宝</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">426笔</span>
                        <Badge>33.2%</Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>银联支付</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">124笔</span>
                        <Badge>9.7%</Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>现金支付</span>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">50笔</span>
                        <Badge>3.9%</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>支付渠道平均交易金额</CardTitle>
                <CardDescription>各支付渠道平均交易金额统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  {/* 这里是支付渠道平均交易金额图表 */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>微信支付</span>
                      <span className="font-medium">¥100.04</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>支付宝</span>
                      <span className="font-medium">¥99.95</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>银联支付</span>
                      <span className="font-medium">¥100.16</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>现金支付</span>
                      <span className="font-medium">¥100.00</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trend" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付金额趋势</CardTitle>
              <CardDescription>支付金额随时间变化趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {/* 这里是支付金额趋势图表 */}
                <div className="flex h-[300px] items-end gap-2">
                  {Array.from({ length: 30 }).map((_, i) => {
                    const height = 30 + Math.random() * 70
                    return (
                      <div key={i} className="relative flex-1">
                        <div className="bg-primary rounded-t-md w-full" style={{ height: `${height}%` }}></div>
                        {i % 5 === 0 && (
                          <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                            {i + 1}日
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>日交易峰值时段</CardTitle>
                <CardDescription>一天中交易量最高的时段</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  {/* 这里是日交易峰值时段图表 */}
                  <div className="flex h-[200px] items-end gap-2">
                    {Array.from({ length: 24 }).map((_, i) => {
                      const height = i >= 8 && i <= 22 ? 20 + Math.random() * 80 : Math.random() * 20
                      return (
                        <div key={i} className="relative flex-1">
                          <div className="bg-primary rounded-t-md w-full" style={{ height: `${height}%` }}></div>
                          {i % 3 === 0 && (
                            <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                              {i}:00
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>周交易分布</CardTitle>
                <CardDescription>一周中各天的交易分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  {/* 这里是周交易分布图表 */}
                  <div className="flex h-[200px] items-end gap-6">
                    {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, i) => {
                      const height = 40 + Math.random() * 60
                      const isWeekend = i >= 5
                      return (
                        <div key={i} className="relative flex-1">
                          <div
                            className={`rounded-t-md w-full ${isWeekend ? "bg-blue-500" : "bg-primary"}`}
                            style={{ height: `${height}%` }}
                          ></div>
                          <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                            {day}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="refund" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>退款率趋势</CardTitle>
              <CardDescription>退款率随时间变化趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {/* 这里是退款率趋势图表 */}
                <div className="flex h-[300px] items-end gap-2">
                  {Array.from({ length: 30 }).map((_, i) => {
                    const height = 2 + Math.random() * 8
                    return (
                      <div key={i} className="relative flex-1">
                        <div className="bg-red-500 rounded-t-md w-full" style={{ height: `${height}%` }}></div>
                        {i % 5 === 0 && (
                          <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-muted-foreground">
                            {i + 1}日
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>退款原因分布</CardTitle>
                <CardDescription>退款原因统计分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  {/* 这里是退款原因分布图表 */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>个人原因</span>
                        <span className="font-medium">42.5%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-red-500" style={{ width: "42.5%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>时间冲突</span>
                        <span className="font-medium">28.3%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-orange-500" style={{ width: "28.3%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>健康原因</span>
                        <span className="font-medium">15.7%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-yellow-500" style={{ width: "15.7%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>服务质量</span>
                        <span className="font-medium">8.2%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-blue-500" style={{ width: "8.2%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>其他原因</span>
                        <span className="font-medium">5.3%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-purple-500" style={{ width: "5.3%" }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>各渠道退款率</CardTitle>
                <CardDescription>不同支付渠道的退款率对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  {/* 这里是各渠道退款率图表 */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>微信支付</span>
                        <span className="font-medium">4.2%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "4.2%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>支付宝</span>
                        <span className="font-medium">3.8%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-blue-500" style={{ width: "3.8%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>银联支付</span>
                        <span className="font-medium">5.1%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-red-500" style={{ width: "5.1%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>现金支付</span>
                        <span className="font-medium">2.0%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-yellow-500" style={{ width: "2.0%" }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="conversion" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付转化率</CardTitle>
              <CardDescription>从下单到支付成功的转化率</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {/* 这里是支付转化率图表 */}
                <div className="flex flex-col items-center justify-center h-full">
                  <div className="relative h-64 w-64">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-4xl font-bold">87.5%</div>
                    </div>
                    <svg className="h-full w-full" viewBox="0 0 100 100">
                      <circle cx="50" cy="50" r="40" fill="none" stroke="#e2e8f0" strokeWidth="10" />
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="none"
                        stroke="hsl(var(--primary))"
                        strokeWidth="10"
                        strokeDasharray="251.2"
                        strokeDashoffset="31.4"
                        transform="rotate(-90 50 50)"
                      />
                    </svg>
                  </div>
                  <div className="text-sm text-muted-foreground mt-4">较上期提升 2.3%</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>各渠道支付转化率</CardTitle>
                <CardDescription>不同支付渠道的支付转化率对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  {/* 这里是各渠道支付转化率图表 */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>微信支付</span>
                        <span className="font-medium">92.5%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-green-500" style={{ width: "92.5%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>支付宝</span>
                        <span className="font-medium">90.8%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-blue-500" style={{ width: "90.8%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>银联支付</span>
                        <span className="font-medium">85.1%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-red-500" style={{ width: "85.1%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>现金支付</span>
                        <span className="font-medium">100%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <div className="h-2 rounded-full bg-yellow-500" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>支付转化漏斗</CardTitle>
                <CardDescription>从浏览到支付的转化漏斗</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  {/* 这里是支付转化漏斗图表 */}
                  <div className="space-y-4">
                    <div className="relative">
                      <div className="flex items-center justify-between">
                        <span>浏览商品</span>
                        <span className="font-medium">100%</span>
                      </div>
                      <div className="h-8 w-full bg-blue-100 mt-1 flex items-center justify-center text-xs">
                        10,000人
                      </div>
                    </div>
                    <div className="relative">
                      <div className="flex items-center justify-between">
                        <span>加入购物车</span>
                        <span className="font-medium">45%</span>
                      </div>
                      <div className="h-8 w-[45%] bg-blue-300 mt-1 flex items-center justify-center text-xs">
                        4,500人
                      </div>
                    </div>
                    <div className="relative">
                      <div className="flex items-center justify-between">
                        <span>提交订单</span>
                        <span className="font-medium">25%</span>
                      </div>
                      <div className="h-8 w-[25%] bg-blue-500 mt-1 flex items-center justify-center text-xs text-white">
                        2,500人
                      </div>
                    </div>
                    <div className="relative">
                      <div className="flex items-center justify-between">
                        <span>完成支付</span>
                        <span className="font-medium">21.9%</span>
                      </div>
                      <div className="h-8 w-[21.9%] bg-blue-700 mt-1 flex items-center justify-center text-xs text-white">
                        2,190人
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

