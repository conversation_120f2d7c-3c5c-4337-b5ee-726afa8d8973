"use client"

import React, { useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import { 
  CheckCircle, 
  ChevronLeft, 
  ChevronRight, 
  HelpCircle, 
  X, 
  List, 
  ArrowRight 
} from "lucide-react"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useOnboarding } from "@/contexts/onboarding-context"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useAuth } from "@/contexts/auth-context"

export function OnboardingGuide() {
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated } = useAuth() // 获取用户登录状态
  const { 
    steps, 
    currentStepIndex, 
    isOnboardingComplete, 
    showOnboarding, 
    setShowOnboarding,
    markStepAsCompleted, 
    nextStep, 
    previousStep, 
    goToStep, 
    dismissOnboarding 
  } = useOnboarding()

  const currentStep = steps[currentStepIndex]
  const completedSteps = steps.filter(step => step.completed).length
  const progress = (completedSteps / steps.length) * 100

  // 当路径变化时，检查是否匹配当前步骤的路径
  useEffect(() => {
    if (pathname === currentStep?.path) {
      setShowOnboarding(true)
    }
  }, [pathname, currentStep, setShowOnboarding])

  // 处理完成当前步骤
  const handleCompleteStep = () => {
    markStepAsCompleted(currentStep.id)
    if (currentStepIndex < steps.length - 1) {
      nextStep()
      // 导航到下一步的路径
      const nextStepPath = steps[currentStepIndex + 1].path
      router.push(nextStepPath)
    } else {
      setShowOnboarding(false)
    }
  }

  // 处理跳转到步骤
  const handleGoToStep = (index: number) => {
    goToStep(index)
    router.push(steps[index].path)
  }

  // 如果用户未登录，不显示新手指引
  if (!isAuthenticated) {
    return null;
  }
  
  return (
    <>
      {/* 引导对话框 */}
      <Dialog open={showOnboarding} onOpenChange={setShowOnboarding}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <span className="text-primary">步骤 {currentStepIndex + 1}/{steps.length}:</span> {currentStep?.title}
            </DialogTitle>
            <DialogDescription>
              {currentStep?.description}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Progress value={progress} className="h-2 mb-4" />
            
            <div className="space-y-4">
              <Alert>
                <HelpCircle className="h-4 w-4" />
                <AlertTitle>提示</AlertTitle>
                <AlertDescription>
                  {currentStepIndex === 0 && "从设置工作室基本信息开始，为系统运行奠定基础。"}
                  {currentStepIndex === 1 && "添加您的场馆和教室信息，这将用于后续的排课。"}
                  {currentStepIndex === 2 && "创建员工账号并分配适当的权限，确保系统安全。"}
                  {currentStepIndex === 3 && "设置课程类型，这是排课和会员卡关联的基础。"}
                  {currentStepIndex === 4 && "创建会员卡类型，设置价格和有效期等信息。"}
                  {currentStepIndex === 5 && "关联会员卡和课程类型，确定哪些卡可以上哪些课。"}
                  {currentStepIndex === 6 && "添加教练信息，包括专长和可授课的课程类型。"}
                  {currentStepIndex === 7 && "设置课程排期，创建固定课程和临时课程。"}
                  {currentStepIndex === 8 && "配置预约规则，包括预约时间、取消和签到规则。"}
                  {currentStepIndex === 9 && "导入会员信息或手动添加会员和会员卡数据。"}
                  {currentStepIndex === 10 && "测试系统功能，确保一切正常运行。"}
                </AlertDescription>
              </Alert>

              {/* 当前步骤的详细说明 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">操作指南</CardTitle>
                </CardHeader>
                <CardContent className="text-sm">
                  {currentStepIndex === 0 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入系统设置页面</li>
                      <li>填写工作室名称、地址、联系电话等基本信息</li>
                      <li>上传工作室logo</li>
                      <li>设置系统默认语言和时区</li>
                      <li>配置短信通知模板（如需）</li>
                      <li>保存设置</li>
                    </ol>
                  )}
                  {currentStepIndex === 1 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入场馆管理页面</li>
                      <li>添加场馆信息（如有多个分店）</li>
                      <li>为每个场馆添加教室</li>
                      <li>设置教室名称、容量和设施信息</li>
                      <li>上传教室照片（可选）</li>
                      <li>保存设置</li>
                    </ol>
                  )}
                  {currentStepIndex === 2 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入员工管理页面</li>
                      <li>添加管理员账号</li>
                      <li>添加前台接待账号</li>
                      <li>添加财务人员账号</li>
                      <li>添加教练账号</li>
                      <li>为每个账号分配适当的权限</li>
                    </ol>
                  )}
                  {currentStepIndex === 3 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入课程类型页面</li>
                      <li>添加课程大类（如瑜伽、普拉提等）</li>
                      <li>在每个大类下添加具体课程类型</li>
                      <li>设置课程名称、描述、时长、难度级别等信息</li>
                      <li>上传课程图片</li>
                      <li>保存设置</li>
                    </ol>
                  )}
                  {currentStepIndex === 4 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入会员卡类型页面</li>
                      <li>添加期限卡类型（如年卡、季卡、月卡等）</li>
                      <li>添加次卡类型（如10次卡、20次卡等）</li>
                      <li>添加私教卡类型</li>
                      <li>设置有效期、价格、使用限制等信息</li>
                      <li>保存设置</li>
                    </ol>
                  )}
                  {currentStepIndex === 5 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入会员卡课程类型关联页面</li>
                      <li>为每种会员卡类型设置可用的课程类型</li>
                      <li>设置不同课程类型的消耗权重</li>
                      <li>设置特定课程类型的使用次数限制（如适用）</li>
                      <li>保存设置</li>
                    </ol>
                  )}
                  {currentStepIndex === 6 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入教练管理页面</li>
                      <li>添加教练基本信息（姓名、联系方式等）</li>
                      <li>设置教练专长领域和等级</li>
                      <li>上传教练照片和简介</li>
                      <li>设置教练可授课的课程类型</li>
                      <li>保存设置</li>
                    </ol>
                  )}
                  {currentStepIndex === 7 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入排课管理页面</li>
                      <li>设置排课周期</li>
                      <li>添加固定课程（选择课程类型、教练、教室、时间）</li>
                      <li>添加临时课程（如需）</li>
                      <li>设置课程容量和有效期</li>
                      <li>保存设置</li>
                    </ol>
                  )}
                  {currentStepIndex === 8 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入预约规则页面</li>
                      <li>设置预约开放天数和截止时间</li>
                      <li>设置取消规则和爽约记录</li>
                      <li>设置签到方式和时间限制</li>
                      <li>设置特殊课程的预约规则</li>
                      <li>保存设置</li>
                    </ol>
                  )}
                  {currentStepIndex === 9 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>进入会员管理页面</li>
                      <li>准备会员数据（Excel格式）</li>
                      <li>使用系统导入功能批量导入会员数据</li>
                      <li>或手动添加重要会员信息</li>
                      <li>验证导入的会员数据</li>
                    </ol>
                  )}
                  {currentStepIndex === 10 && (
                    <ol className="list-decimal pl-4 space-y-2">
                      <li>测试会员注册流程</li>
                      <li>测试课程预约流程</li>
                      <li>测试签到流程</li>
                      <li>测试会员卡购买和消费流程</li>
                      <li>测试报表和数据统计功能</li>
                    </ol>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          <DialogFooter className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowOnboarding(false)}
              >
                稍后再说
              </Button>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm">
                    <List className="h-4 w-4 mr-2" />
                    所有步骤
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-2">
                    <h4 className="font-medium">初始化步骤</h4>
                    <ul className="space-y-1">
                      {steps.map((step, index) => (
                        <li key={step.id} className="flex items-center justify-between">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="justify-start w-full"
                            onClick={() => handleGoToStep(index)}
                          >
                            <span className="flex items-center">
                              {step.completed ? (
                                <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                              ) : (
                                <span className="h-4 w-4 mr-2 flex items-center justify-center rounded-full bg-muted text-xs">
                                  {index + 1}
                                </span>
                              )}
                              <span className={step.completed ? "text-muted-foreground" : ""}>
                                {step.title}
                              </span>
                            </span>
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={previousStep}
                disabled={currentStepIndex === 0}
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                上一步
              </Button>
              {currentStepIndex < steps.length - 1 ? (
                <Button size="sm" onClick={handleCompleteStep}>
                  下一步
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button size="sm" onClick={handleCompleteStep}>
                  完成
                  <CheckCircle className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 引导按钮 */}
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          size="sm"
          className="rounded-full h-12 w-12 p-0 shadow-lg"
          onClick={() => setShowOnboarding(true)}
        >
          <HelpCircle className="h-6 w-6" />
        </Button>
      </div>

      {/* 新手引导提示 - 仅在首次访问时显示 */}
      {pathname === "/" && !isOnboardingComplete && (
        <div className="fixed bottom-20 right-4 z-50 max-w-xs">
          <Card className="border-primary animate-pulse">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">欢迎使用瑜伽工作室管理系统</CardTitle>
              <CardDescription>
                我们为您准备了初始化引导，帮助您快速上手
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <Button 
                size="sm" 
                className="w-full"
                onClick={() => {
                  setShowOnboarding(true)
                  router.push(steps[0].path)
                }}
              >
                开始引导
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardFooter>
          </Card>
        </div>
      )}
    </>
  )
}
