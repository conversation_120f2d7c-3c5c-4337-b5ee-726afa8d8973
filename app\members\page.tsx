"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MemberTable } from "@/components/member-table"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ChevronDown, Download, Filter, Grid3X3, LayoutList, Plus, RefreshCw, Upload } from "lucide-react"
import { MemberGrid } from "@/components/members/member-grid"
import { AddMemberDialog } from "@/components/members/add-member-dialog"
import { ImportMembersDialog } from "@/components/members/import-members-dialog"
import { AdvancedFilterDialog } from "@/components/members/advanced-filter-dialog"

export default function MembersPage() {
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showFilterDialog, setShowFilterDialog] = useState(false)

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">会员管理</h1>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={() => setShowFilterDialog(true)}>
            <Filter className="mr-2 h-4 w-4" />
            高级筛选
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <RefreshCw className="mr-2 h-4 w-4" />
                导入/导出
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowImportDialog(true)}>
                <Upload className="mr-2 h-4 w-4" />
                导入会员
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                导出会员
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                下载导入模板
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <div className="flex rounded-md border">
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              className="rounded-r-none"
              onClick={() => setViewMode("list")}
            >
              <LayoutList className="h-4 w-4" />
              <span className="sr-only">列表视图</span>
            </Button>
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              className="rounded-l-none"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
              <span className="sr-only">网格视图</span>
            </Button>
          </div>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加会员
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <Card>
          <CardContent className="p-3">
            <div className="flex flex-col gap-4 md:flex-row">
              <div className="w-full md:w-1/3">
                <Input placeholder="搜索会员姓名、手机号、会员卡号" />
              </div>
              <div className="flex flex-1 flex-wrap gap-4">
                <Select defaultValue="all">
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="会员状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="active">活跃</SelectItem>
                    <SelectItem value="inactive">不活跃</SelectItem>
                    <SelectItem value="expired">已过期</SelectItem>
                  </SelectContent>
                </Select>

                <Select defaultValue="all">
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="会员等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部等级</SelectItem>
                    <SelectItem value="standard">标准会员</SelectItem>
                    <SelectItem value="silver">银卡会员</SelectItem>
                    <SelectItem value="gold">金卡会员</SelectItem>
                    <SelectItem value="platinum">白金会员</SelectItem>
                  </SelectContent>
                </Select>

                <Select defaultValue="all">
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="标签" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部标签</SelectItem>
                    <SelectItem value="new">新会员</SelectItem>
                    <SelectItem value="vip">VIP</SelectItem>
                    <SelectItem value="potential">潜在流失</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="all" className="w-full">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="all" className="relative">
                全部会员
                <Badge className="ml-2 bg-primary/10 text-primary hover:bg-primary/20">350</Badge>
              </TabsTrigger>
              <TabsTrigger value="active">
                活跃会员
                <Badge className="ml-2 bg-primary/10 text-primary hover:bg-primary/20">245</Badge>
              </TabsTrigger>
              <TabsTrigger value="expiring">
                即将到期
                <Badge className="ml-2 bg-primary/10 text-primary hover:bg-primary/20">32</Badge>
              </TabsTrigger>
              <TabsTrigger value="new">
                新会员
                <Badge className="ml-2 bg-primary/10 text-primary hover:bg-primary/20">45</Badge>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="all" className="mt-4">
            {viewMode === "list" ? <MemberTable /> : <MemberGrid />}
          </TabsContent>
          <TabsContent value="active" className="mt-4">
            {viewMode === "list" ? <MemberTable /> : <MemberGrid />}
          </TabsContent>
          <TabsContent value="expiring" className="mt-4">
            {viewMode === "list" ? <MemberTable /> : <MemberGrid />}
          </TabsContent>
          <TabsContent value="new" className="mt-4">
            {viewMode === "list" ? <MemberTable /> : <MemberGrid />}
          </TabsContent>
        </Tabs>
      </div>

      <AddMemberDialog open={showAddDialog} onOpenChange={setShowAddDialog} />
      <ImportMembersDialog open={showImportDialog} onOpenChange={setShowImportDialog} />
      <AdvancedFilterDialog open={showFilterDialog} onOpenChange={setShowFilterDialog} />
    </div>
  )
}

