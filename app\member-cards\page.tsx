"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Search, 
  Calendar, 
  Clock, 
  Filter, 
  Download, 
  ChevronLeft, 
  ChevronRight,
  CreditCard,
  User,
  Users,
  CalendarDays,
  Gift,
  AlertCircle,
  CheckCircle
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// 模拟会员卡数据
const memberCardData = [
  {
    id: "M001",
    name: "张三",
    cardType: "年卡",
    cardNumber: "YK20230001",
    validUntil: "2023-12-31",
    balance: 0,
    status: "有效",
    lastVisit: "2023-05-10",
    birthday: "1990-05-20",
    joinDate: "2022-05-16"
  },
  {
    id: "M002",
    name: "李四",
    cardType: "次卡",
    cardNumber: "CK20230015",
    validUntil: "2023-08-15",
    balance: 8,
    status: "有效",
    lastVisit: "2023-05-01",
    birthday: "1985-05-17",
    joinDate: "2023-01-10"
  },
  {
    id: "M003",
    name: "王五",
    cardType: "储值卡",
    cardNumber: "CZK20230022",
    validUntil: "2023-06-30",
    balance: 350,
    status: "即将到期",
    lastVisit: "2023-05-15",
    birthday: "1992-06-05",
    joinDate: "2022-06-30"
  },
  {
    id: "M004",
    name: "赵六",
    cardType: "年卡",
    cardNumber: "YK20230045",
    validUntil: "2023-05-20",
    balance: 0,
    status: "即将到期",
    lastVisit: "2023-04-01",
    birthday: "1988-05-16",
    joinDate: "2022-05-20"
  },
  {
    id: "M005",
    name: "钱七",
    cardType: "次卡",
    cardNumber: "CK20230033",
    validUntil: "2023-07-15",
    balance: 2,
    status: "余额不足",
    lastVisit: "2023-05-12",
    birthday: "1995-07-22",
    joinDate: "2023-02-15"
  }
];

export default function MemberCardsPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("valid")
  const [searchQuery, setSearchQuery] = useState("")
  const [cardTypeFilter, setCardTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")

  // 获取状态样式
  const getStatusStyle = (status: string) => {
    switch(status) {
      case "有效":
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case "即将到期":
        return "bg-amber-100 text-amber-800 hover:bg-amber-100";
      case "余额不足":
        return "bg-red-100 text-red-800 hover:bg-red-100";
      default:
        return "";
    }
  };

  // 获取今天的日期
  const today = new Date();
  const formattedDate = today.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });

  // 筛选会员卡数据
  const filteredCards = memberCardData.filter(card => {
    // 搜索条件
    const matchesSearch = searchQuery === "" || 
      card.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      card.cardNumber.toLowerCase().includes(searchQuery.toLowerCase());
    
    // 卡类型筛选
    const matchesCardType = cardTypeFilter === "all" || 
      (cardTypeFilter === "year" && card.cardType === "年卡") ||
      (cardTypeFilter === "count" && card.cardType === "次卡") ||
      (cardTypeFilter === "value" && card.cardType === "储值卡");
    
    // 状态筛选
    const matchesStatus = statusFilter === "all" || 
      (statusFilter === "valid" && card.status === "有效") ||
      (statusFilter === "expiring" && card.status === "即将到期") ||
      (statusFilter === "lowBalance" && card.status === "余额不足");
    
    return matchesSearch && matchesCardType && matchesStatus;
  });

  // 根据标签页筛选
  const getTabFilteredCards = () => {
    switch(activeTab) {
      case "valid":
        return filteredCards.filter(card => card.status === "有效");
      case "expiring":
        return filteredCards.filter(card => card.status === "即将到期");
      case "birthday":
        // 筛选本月生日的会员
        const currentMonth = today.getMonth() + 1;
        return filteredCards.filter(card => {
          const birthMonth = parseInt(card.birthday.split('-')[1]);
          return birthMonth === currentMonth;
        });
      case "anniversary":
        // 筛选本月入会纪念日的会员
        const currentMonth2 = today.getMonth() + 1;
        return filteredCards.filter(card => {
          const joinMonth = parseInt(card.joinDate.split('-')[1]);
          return joinMonth === currentMonth2;
        });
      default:
        return filteredCards;
    }
  };

  const displayCards = getTabFilteredCards();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">会员卡管理</h1>
          <p className="text-muted-foreground">
            查看和管理会员卡信息
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <CreditCard className="mr-2 h-4 w-4" />
            开卡
          </Button>
          <Button>
            <User className="mr-2 h-4 w-4" />
            会员管理
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
        <div className="flex-1 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索会员姓名/卡号..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Select value={cardTypeFilter} onValueChange={setCardTypeFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="卡类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="year">年卡</SelectItem>
                <SelectItem value="count">次卡</SelectItem>
                <SelectItem value="value">储值卡</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="卡状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="valid">有效</SelectItem>
                <SelectItem value="expiring">即将到期</SelectItem>
                <SelectItem value="lowBalance">余额不足</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            筛选
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-4">
          <TabsTrigger value="valid">有效卡</TabsTrigger>
          <TabsTrigger value="expiring">即将到期</TabsTrigger>
          <TabsTrigger value="birthday">本月生日</TabsTrigger>
          <TabsTrigger value="anniversary">入会纪念</TabsTrigger>
        </TabsList>
        
        <TabsContent value={activeTab} className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle>会员卡信息</CardTitle>
                <div className="text-sm text-muted-foreground">
                  {formattedDate}
                </div>
              </div>
              <CardDescription>
                {activeTab === "valid" && "当前有效的会员卡"}
                {activeTab === "expiring" && "30天内即将到期的会员卡"}
                {activeTab === "birthday" && "本月生日的会员"}
                {activeTab === "anniversary" && "本月入会纪念日的会员"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>会员姓名</TableHead>
                    <TableHead>卡类型</TableHead>
                    <TableHead>卡号</TableHead>
                    <TableHead>有效期至</TableHead>
                    <TableHead>余额/次数</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {displayCards.length > 0 ? (
                    displayCards.map((card) => (
                      <TableRow key={card.id}>
                        <TableCell className="font-medium">{card.name}</TableCell>
                        <TableCell>{card.cardType}</TableCell>
                        <TableCell>{card.cardNumber}</TableCell>
                        <TableCell>{card.validUntil}</TableCell>
                        <TableCell>{card.balance}</TableCell>
                        <TableCell>
                          <Badge className={getStatusStyle(card.status)}>
                            {card.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            查看
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        {activeTab === "valid" && <CheckCircle className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />}
                        {activeTab === "expiring" && <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />}
                        {activeTab === "birthday" && <Gift className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />}
                        {activeTab === "anniversary" && <Calendar className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />}
                        <p>暂无符合条件的会员卡信息</p>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
