"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Users, Share2, Wallet, BarChart2 } from "lucide-react"

// 定义导航项
const navItems = [
  {
    name: "股东管理",
    href: "/shareholders",
  },
  {
    name: "股东类型",
    href: "/shareholders/types",
  },
  {
    name: "分红记录",
    href: "/shareholders/dividends",
  },
  {
    name: "业绩统计",
    href: "/shareholders/statistics",
  }
]

export function ShareholderNav() {
  const pathname = usePathname()

  return (
    <div className="w-full mb-6">
      <div className="flex border-b">
        {navItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "px-6 py-3 text-sm font-medium text-center transition-colors",
              pathname === item.href
                ? "bg-white text-foreground border-b-2 border-primary"
                : "bg-muted/30 text-muted-foreground hover:bg-muted/50"
            )}
          >
            {item.name}
          </Link>
        ))}
      </div>
    </div>
  )
}
