"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Building2,
  Users,
  Settings,
  BarChart4,
  Plus,
  Search,
  RefreshCw,
  ChevronRight,
  ChevronLeft,
  ArrowUpDown,
  FileText,
  Layers,
  Share2,
  Shuffle,
  Calendar,
  ShoppingBag,
  UserCog,
  Upload,
  LayoutGrid,
  List
} from "lucide-react"
import { StoreCard } from "@/components/chain-management/store-card"
import { AddStoreDialog } from "@/components/chain-management/add-store-dialog"
import { CrossStoreStatsCard } from "@/components/chain-management/cross-store-stats-card"
import { StandardsManagementCard } from "@/components/chain-management/standards-management-card"
import { ResourceAllocationCard } from "@/components/chain-management/resource-allocation-card"

// 模拟门店数据
const stores = [
  {
    id: "1",
    name: "瑜伽中心旗舰店",
    address: "北京市朝阳区建国路88号",
    phone: "010-12345678",
    manager: "张经理",
    status: "active",
    memberCount: 450,
    coachCount: 12,
    monthlyRevenue: 120000,
    openDate: "2020-05-15",
    lastSync: "2023-06-10T08:30:00",
  },
  {
    id: "2",
    name: "瑜伽中心西城店",
    address: "北京市西城区西单北大街120号",
    phone: "010-87654321",
    manager: "李经理",
    status: "active",
    memberCount: 320,
    coachCount: 8,
    monthlyRevenue: 85000,
    openDate: "2021-03-20",
    lastSync: "2023-06-09T17:45:00",
  },
  {
    id: "3",
    name: "瑜伽中心海淀店",
    address: "北京市海淀区中关村大街28号",
    phone: "010-56781234",
    manager: "王经理",
    status: "active",
    memberCount: 380,
    coachCount: 10,
    monthlyRevenue: 95000,
    openDate: "2021-08-10",
    lastSync: "2023-06-10T09:15:00",
  },
  {
    id: "4",
    name: "瑜伽中心通州店",
    address: "北京市通州区新华大街56号",
    phone: "010-43215678",
    manager: "赵经理",
    status: "inactive",
    memberCount: 180,
    coachCount: 5,
    monthlyRevenue: 45000,
    openDate: "2022-04-05",
    lastSync: "2023-06-08T14:20:00",
  },
]

// 模拟跨店统计数据
const crossStoreStats = {
  totalMembers: 1330,
  totalCoaches: 35,
  totalRevenue: 345000,
  crossStoreVisits: 128,
  sharedResources: 15,
  standardCompliance: 92,
}

export default function ChainManagementPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("overview")
  const [showAddStoreDialog, setShowAddStoreDialog] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // 过滤门店
  const filteredStores = stores.filter(
    (store) =>
      store.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      store.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
      store.manager.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // 刷新数据
  const refreshData = () => {
    setIsLoading(true)
    // 模拟API请求
    setTimeout(() => {
      setIsLoading(false)
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">瑜伽工作室连锁管理</h1>
          <p className="text-muted-foreground">多门店统一管理解决方案，支持跨店会员服务与数据同步</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button variant="outline" onClick={refreshData} disabled={isLoading}>
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                同步中...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                同步数据
              </>
            )}
          </Button>
          <Button onClick={() => setShowAddStoreDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加门店
          </Button>
        </div>
      </div>

      <div className="text-sm breadcrumbs">
        <ul className="flex items-center space-x-1">
          <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li>瑜伽工作室连锁管理</li>
        </ul>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="stores">门店管理</TabsTrigger>
          <TabsTrigger value="cross-store">跨店服务</TabsTrigger>
          <TabsTrigger value="standards">标准化管理</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">门店总数</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{stores.length}</div>
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  {stores.filter(s => s.status === "active").length} 家正常营业
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">会员总数</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">{crossStoreStats.totalMembers}</div>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  {crossStoreStats.crossStoreVisits} 次跨店访问（本月）
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">月营收总额</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold">¥{crossStoreStats.totalRevenue.toLocaleString()}</div>
                  <BarChart4 className="h-4 w-4 text-muted-foreground" />
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  较上月增长 8.5%
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CrossStoreStatsCard stats={crossStoreStats} />
            <StandardsManagementCard />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ResourceAllocationCard />
            <Card>
              <CardHeader>
                <CardTitle>最近活动</CardTitle>
                <CardDescription>连锁门店最近的重要活动</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-2">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <UserCog className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">新教练培训</p>
                      <p className="text-xs text-muted-foreground">所有门店新入职教练统一培训</p>
                      <p className="text-xs text-muted-foreground mt-1">2023-06-15</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="bg-green-100 p-2 rounded-full">
                      <Calendar className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">夏季课程更新</p>
                      <p className="text-xs text-muted-foreground">所有门店同步更新夏季特色课程</p>
                      <p className="text-xs text-muted-foreground mt-1">2023-06-10</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="bg-purple-100 p-2 rounded-full">
                      <ShoppingBag className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">会员卡促销活动</p>
                      <p className="text-xs text-muted-foreground">连锁门店统一会员卡促销活动</p>
                      <p className="text-xs text-muted-foreground mt-1">2023-06-05</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="stores" className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="w-full md:w-1/3 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索门店..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <Select defaultValue="all">
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">营业中</SelectItem>
                  <SelectItem value="inactive">已关闭</SelectItem>
                  <SelectItem value="preparing">筹备中</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="all">
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="地区" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部地区</SelectItem>
                  <SelectItem value="朝阳区">朝阳区</SelectItem>
                  <SelectItem value="海淀区">海淀区</SelectItem>
                  <SelectItem value="西城区">西城区</SelectItem>
                  <SelectItem value="通州区">通州区</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="name">
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="排序" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">按名称</SelectItem>
                  <SelectItem value="memberCount">按会员数</SelectItem>
                  <SelectItem value="revenue">按营收</SelectItem>
                  <SelectItem value="openDate">按开业日期</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1 flex justify-end gap-2">
              <Button variant="outline" size="sm">
                <Upload className="mr-2 h-4 w-4" />
                导入
              </Button>
              <Button variant="outline" size="sm">
                <FileText className="mr-2 h-4 w-4" />
                导出
              </Button>
              <Button variant="outline" size="sm">
                <LayoutGrid className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredStores.map((store) => (
              <StoreCard key={store.id} store={store} />
            ))}
          </div>

          {/* 批量操作 */}
          <div className="flex items-center justify-between border-t pt-4">
            <div className="flex items-center gap-2">
              <Checkbox id="select-all" />
              <label htmlFor="select-all" className="text-sm">全选</label>
              <Button variant="outline" size="sm" className="ml-4">
                批量同步
              </Button>
              <Button variant="outline" size="sm">
                批量导出
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm">第 1 页，共 1 页</span>
              <Button variant="outline" size="sm">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="cross-store" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>跨店会员服务</CardTitle>
                <CardDescription>管理跨门店会员服务和权益</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Share2 className="h-5 w-5 text-blue-500" />
                      <div>
                        <p className="font-medium">跨店预约</p>
                        <p className="text-sm text-muted-foreground">允许会员在任意门店预约课程</p>
                      </div>
                    </div>
                    <Badge>已启用</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Shuffle className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium">跨店消费</p>
                        <p className="text-sm text-muted-foreground">会员卡可在任意门店消费</p>
                      </div>
                    </div>
                    <Badge>已启用</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Layers className="h-5 w-5 text-orange-500" />
                      <div>
                        <p className="font-medium">跨店积分</p>
                        <p className="text-sm text-muted-foreground">会员积分跨店累积与使用</p>
                      </div>
                    </div>
                    <Badge variant="outline">未启用</Badge>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">配置跨店服务</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>跨店数据同步</CardTitle>
                <CardDescription>管理门店间的数据同步策略</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-500" />
                      <div>
                        <p className="font-medium">会员数据</p>
                        <p className="text-sm text-muted-foreground">会员信息跨店同步</p>
                      </div>
                    </div>
                    <Badge>实时同步</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium">课程数据</p>
                        <p className="text-sm text-muted-foreground">课程信息跨店同步</p>
                      </div>
                    </div>
                    <Badge>每日同步</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <BarChart4 className="h-5 w-5 text-purple-500" />
                      <div>
                        <p className="font-medium">财务数据</p>
                        <p className="text-sm text-muted-foreground">财务信息跨店同步</p>
                      </div>
                    </div>
                    <Badge>每周同步</Badge>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">配置同步策略</Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="standards" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>标准化管理</CardTitle>
                <CardDescription>管理连锁门店的标准化流程和规范</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-blue-500" />
                      <div>
                        <p className="font-medium">服务标准</p>
                        <p className="text-sm text-muted-foreground">统一的会员服务标准和流程</p>
                      </div>
                    </div>
                    <Badge>已发布</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <UserCog className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium">教练标准</p>
                        <p className="text-sm text-muted-foreground">教练培训和授课标准</p>
                      </div>
                    </div>
                    <Badge>已发布</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Settings className="h-5 w-5 text-orange-500" />
                      <div>
                        <p className="font-medium">运营标准</p>
                        <p className="text-sm text-muted-foreground">日常运营和管理标准</p>
                      </div>
                    </div>
                    <Badge variant="outline">草稿</Badge>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">管理标准文档</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>标准执行检查</CardTitle>
                <CardDescription>监控各门店标准执行情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">服务标准执行率</span>
                      <span className="text-sm font-medium">95%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: "95%" }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">教练标准执行率</span>
                      <span className="text-sm font-medium">88%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: "88%" }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">运营标准执行率</span>
                      <span className="text-sm font-medium">78%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-orange-600 h-2 rounded-full" style={{ width: "78%" }}></div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">查看详细报告</Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      <AddStoreDialog open={showAddStoreDialog} onOpenChange={setShowAddStoreDialog} />
    </div>
  )
}
