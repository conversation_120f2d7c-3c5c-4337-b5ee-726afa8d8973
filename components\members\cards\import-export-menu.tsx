"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { ImportCardDialog } from "./import-card-dialog"
import { ExportCardDialog } from "./export-card-dialog"
import { Upload, Download, FileUp, FileDown } from "lucide-react"

interface ImportExportMenuProps {
  cards: any[]
  onImportComplete: (cards: any[]) => void
}

export function ImportExportMenu({ cards, onImportComplete }: ImportExportMenuProps) {
  const [importDialogOpen, setImportDialogOpen] = useState(false)
  const [exportDialogOpen, setExportDialogOpen] = useState(false)
  
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">
            导入/导出
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setImportDialogOpen(true)}>
            <Upload className="mr-2 h-4 w-4" />
            导入会员卡
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setExportDialogOpen(true)}>
            <Download className="mr-2 h-4 w-4" />
            导出会员卡
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      
      <ImportCardDialog 
        open={importDialogOpen} 
        onOpenChange={setImportDialogOpen}
        onImportComplete={onImportComplete}
      />
      
      <ExportCardDialog 
        open={exportDialogOpen} 
        onOpenChange={setExportDialogOpen}
        cards={cards}
      />
    </>
  )
}
