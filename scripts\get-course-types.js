// 获取课程类型数据
async function getCourseTypes() {
  console.log('获取课程类型数据...');
  
  try {
    const response = await fetch('http://localhost:3005/api/course-types?tenantId=1');
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('课程类型列表:');
      result.data.list.forEach(type => {
        console.log(`ID: ${type.id}, 名称: ${type.name}`);
      });
    } else {
      console.log(`获取失败: ${result.msg}`);
    }
  } catch (error) {
    console.error('获取出错:', error.message);
  }
}

getCourseTypes();
