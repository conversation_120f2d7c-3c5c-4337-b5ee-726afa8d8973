"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON>ada<PERSON> } from "next"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { UserManagement } from "@/components/contracts/enterprise/user-management"
import { CertUpdate } from "@/components/contracts/enterprise/cert-update"
import { CertAudit } from "@/components/contracts/enterprise/cert-audit"
import { EmployeeSign } from "@/components/contracts/enterprise/employee-sign"
import { usePremiumServices } from "@/hooks/use-premium-services"
import { ChevronLeft, ChevronRight } from "lucide-react"

export default function EnterpriseContractPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { config, loading } = usePremiumServices()
  const [activeTab, setActiveTab] = useState("users")

  // 从URL参数中获取初始标签
  useEffect(() => {
    const tab = searchParams.get("tab")
    if (tab && ["users", "cert-update", "cert-audit", "employee-sign"].includes(tab)) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // 检查是否启用了电子合同服务
  const isServiceEnabled = !loading && config?.eContract?.enabled

  // 获取启用的功能
  const enabledFeatures = !loading && config?.eContract?.features
    ? {
        enterpriseUsers: config.eContract.features.enterpriseUsers,
        certUpdate: config.eContract.features.certUpdate,
        certAudit: config.eContract.features.certAudit,
        employeeSign: config.eContract.features.employeeSign,
      }
    : {
        enterpriseUsers: false,
        certUpdate: false,
        certAudit: false,
        employeeSign: false,
      }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <Link href="/premium-services/e-contract">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h2 className="text-2xl font-bold tracking-tight">企业电子合同管理</h2>
          </div>
          <p className="text-muted-foreground ml-10">
            管理企业用户、认证信息和员工签署功能
          </p>
        </div>
        <Link href="/premium-services/e-contract">
          <Button variant="outline">返回电子合同</Button>
        </Link>
      </div>

      <div className="text-sm breadcrumbs">
        <ul className="flex items-center space-x-1">
          <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li><Link href="/premium-services/e-contract" className="text-muted-foreground hover:text-foreground">法大大电子合同</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li>企业电子合同管理</li>
        </ul>
      </div>

      <Separator />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">企业多用户管理</TabsTrigger>
          <TabsTrigger value="cert-update">认证信息变更</TabsTrigger>
          <TabsTrigger value="cert-audit">认证信息审核</TabsTrigger>
          <TabsTrigger value="employee-sign">员工个人签署</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <UserManagement />
        </TabsContent>

        <TabsContent value="cert-update" className="space-y-4">
          <CertUpdate />
        </TabsContent>

        <TabsContent value="cert-audit" className="space-y-4">
          <CertAudit />
        </TabsContent>

        <TabsContent value="employee-sign" className="space-y-4">
          <EmployeeSign />
        </TabsContent>
      </Tabs>
    </div>
  )
}
