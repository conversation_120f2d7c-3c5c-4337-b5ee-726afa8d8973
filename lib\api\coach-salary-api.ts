import instance from './index';

/**
 * 教练薪资计算系统API
 */
export const coachSalaryApi = {
  /**
   * 薪资配置相关API
   */
  salaryConfig: {
    /**
     * 获取教练薪资配置
     * @param coachId 教练ID
     */
    getCoachSalaryConfig: (coachId: string) => 
      instance.get(`/api/coach-salary/config/${coachId}`),
    
    /**
     * 创建教练薪资配置
     * @param data 薪资配置数据
     */
    createSalaryConfig: (data: any) => 
      instance.post('/api/coach-salary/config/create', data),
    
    /**
     * 更新教练薪资配置
     * @param coachId 教练ID
     * @param data 薪资配置数据
     */
    updateSalaryConfig: (coachId: string, data: any) => 
      instance.put(`/api/coach-salary/config/update/${coachId}`, data),
    
    /**
     * 获取课程类型课时费配置
     * @param coachId 教练ID
     */
    getCourseTypeHourlyRates: (coachId: string) => 
      instance.get(`/api/coach-salary/hourly-rates/${coachId}`),
    
    /**
     * 设置课程类型课时费
     * @param coachId 教练ID
     * @param data 课时费配置数据
     */
    setCourseTypeHourlyRate: (coachId: string, data: any) => 
      instance.post(`/api/coach-salary/hourly-rates/${coachId}`, data),
    
    /**
     * 获取课程类型提成比例配置
     * @param coachId 教练ID
     */
    getCourseCommissionRates: (coachId: string) => 
      instance.get(`/api/coach-salary/commission-rates/${coachId}`),
    
    /**
     * 设置课程类型提成比例
     * @param coachId 教练ID
     * @param data 提成比例配置数据
     */
    setCourseCommissionRate: (coachId: string, data: any) => 
      instance.post(`/api/coach-salary/commission-rates/${coachId}`, data),
  },
  
  /**
   * 课时记录相关API
   */
  classRecords: {
    /**
     * 获取教练课时记录
     * @param coachId 教练ID
     * @param params 查询参数
     */
    getCoachClassRecords: (coachId: string, params?: { 
      startDate?: string; 
      endDate?: string;
      status?: string;
      courseTypeId?: string;
    }) => instance.get(`/api/coach-salary/class-records/${coachId}`, { params }),
    
    /**
     * 添加教练课时记录
     * @param data 课时记录数据
     */
    addClassRecord: (data: any) => 
      instance.post('/api/coach-salary/class-records/add', data),
    
    /**
     * 更新教练课时记录
     * @param recordId 记录ID
     * @param data 课时记录数据
     */
    updateClassRecord: (recordId: string, data: any) => 
      instance.put(`/api/coach-salary/class-records/update/${recordId}`, data),
    
    /**
     * 删除教练课时记录
     * @param recordId 记录ID
     */
    deleteClassRecord: (recordId: string) => 
      instance.delete(`/api/coach-salary/class-records/delete/${recordId}`),
    
    /**
     * 批量导入教练课时记录
     * @param data 课时记录数据数组
     */
    batchImportClassRecords: (data: any[]) => 
      instance.post('/api/coach-salary/class-records/batch-import', data),
  },
  
  /**
   * 薪资计算相关API
   */
  salaryCalculation: {
    /**
     * 计算教练薪资
     * @param coachId 教练ID
     * @param params 计算参数
     */
    calculateSalary: (coachId: string, params: { 
      month: string; // 薪资月份(YYYY-MM)
      includeBonus?: boolean; // 是否包含奖金
      includeDeduction?: boolean; // 是否包含扣款
    }) => instance.post(`/api/coach-salary/calculate/${coachId}`, params),
    
    /**
     * 批量计算教练薪资
     * @param params 计算参数
     */
    batchCalculateSalary: (params: { 
      coachIds: string[]; // 教练ID数组
      month: string; // 薪资月份(YYYY-MM)
      includeBonus?: boolean; // 是否包含奖金
      includeDeduction?: boolean; // 是否包含扣款
    }) => instance.post('/api/coach-salary/batch-calculate', params),
    
    /**
     * 添加奖金
     * @param coachId 教练ID
     * @param data 奖金数据
     */
    addBonus: (coachId: string, data: { 
      month: string; // 薪资月份(YYYY-MM)
      amount: number; // 奖金金额
      reason: string; // 奖金原因
    }) => instance.post(`/api/coach-salary/bonus/${coachId}`, data),
    
    /**
     * 添加扣款
     * @param coachId 教练ID
     * @param data 扣款数据
     */
    addDeduction: (coachId: string, data: { 
      month: string; // 薪资月份(YYYY-MM)
      amount: number; // 扣款金额
      reason: string; // 扣款原因
    }) => instance.post(`/api/coach-salary/deduction/${coachId}`, data),
  },
  
  /**
   * 薪资记录相关API
   */
  salaryRecords: {
    /**
     * 获取教练薪资记录列表
     * @param params 查询参数
     */
    getSalaryRecords: (params?: { 
      coachId?: string;
      month?: string;
      status?: string;
      page?: number;
      pageSize?: number;
    }) => instance.get('/api/coach-salary/records', { params }),
    
    /**
     * 获取教练薪资记录详情
     * @param recordId 记录ID
     */
    getSalaryRecordDetail: (recordId: string) => 
      instance.get(`/api/coach-salary/records/detail/${recordId}`),
    
    /**
     * 审核教练薪资记录
     * @param recordId 记录ID
     * @param data 审核数据
     */
    approveSalaryRecord: (recordId: string, data: { 
      status: 'approved' | 'rejected';
      remark?: string;
    }) => instance.put(`/api/coach-salary/records/approve/${recordId}`, data),
    
    /**
     * 标记薪资记录为已发放
     * @param recordId 记录ID
     * @param data 发放数据
     */
    markAsPaid: (recordId: string, data: { 
      paymentDate: string;
      paymentMethod: string;
      remark?: string;
    }) => instance.put(`/api/coach-salary/records/paid/${recordId}`, data),
    
    /**
     * 批量审核薪资记录
     * @param data 批量审核数据
     */
    batchApproveSalaryRecords: (data: { 
      recordIds: string[];
      status: 'approved' | 'rejected';
      remark?: string;
    }) => instance.put('/api/coach-salary/records/batch-approve', data),
    
    /**
     * 批量标记薪资记录为已发放
     * @param data 批量发放数据
     */
    batchMarkAsPaid: (data: { 
      recordIds: string[];
      paymentDate: string;
      paymentMethod: string;
      remark?: string;
    }) => instance.put('/api/coach-salary/records/batch-paid', data),
  },
  
  /**
   * 薪资报表相关API
   */
  salaryReports: {
    /**
     * 获取薪资统计报表
     * @param params 查询参数
     */
    getSalaryStatistics: (params: { 
      startMonth: string; // 开始月份(YYYY-MM)
      endMonth: string; // 结束月份(YYYY-MM)
      groupBy?: 'coach' | 'month' | 'salaryType'; // 分组方式
    }) => instance.get('/api/coach-salary/reports/statistics', { params }),
    
    /**
     * 获取教练薪资趋势报表
     * @param coachId 教练ID
     * @param params 查询参数
     */
    getCoachSalaryTrend: (coachId: string, params: { 
      startMonth: string; // 开始月份(YYYY-MM)
      endMonth: string; // 结束月份(YYYY-MM)
    }) => instance.get(`/api/coach-salary/reports/trend/${coachId}`, { params }),
    
    /**
     * 导出薪资报表
     * @param params 查询参数
     */
    exportSalaryReport: (params: { 
      month: string; // 薪资月份(YYYY-MM)
      coachIds?: string[]; // 教练ID数组
      format?: 'excel' | 'pdf'; // 导出格式
    }) => instance.post('/api/coach-salary/reports/export', params, { 
      responseType: 'blob' 
    }),
  }
};

export default coachSalaryApi;
