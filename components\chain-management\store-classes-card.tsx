"use client"

import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar, 
  Clock, 
  Users, 
  ChevronRight,
  Dumbbell
} from "lucide-react"

interface StoreClassesCardProps {
  store: any
}

export function StoreClassesCard({ store }: StoreClassesCardProps) {
  // 模拟课程数据
  const classes = [
    {
      id: "1",
      name: "哈他瑜伽基础班",
      type: "团课",
      coach: "王教练",
      time: "09:00-10:30",
      date: "2023-06-12",
      capacity: 20,
      enrolled: 18,
      status: "upcoming",
      room: "1号教室",
    },
    {
      id: "2",
      name: "流瑜伽进阶班",
      type: "小班课",
      coach: "李教练",
      time: "11:00-12:30",
      date: "2023-06-12",
      capacity: 12,
      enrolled: 10,
      status: "upcoming",
      room: "2号教室",
    },
    {
      id: "3",
      name: "阴瑜伽放松班",
      type: "团课",
      coach: "张教练",
      time: "14:00-15:30",
      date: "2023-06-12",
      capacity: 20,
      enrolled: 15,
      status: "upcoming",
      room: "1号教室",
    },
    {
      id: "4",
      name: "普拉提核心训练",
      type: "精品课",
      coach: "赵教练",
      time: "16:00-17:30",
      date: "2023-06-12",
      capacity: 15,
      enrolled: 12,
      status: "upcoming",
      room: "3号教室",
    },
  ]

  // 获取课程类型徽章颜色
  const getClassTypeBadgeColor = (type: string) => {
    switch (type) {
      case "团课":
        return "bg-blue-500 hover:bg-blue-600"
      case "小班课":
        return "bg-green-500 hover:bg-green-600"
      case "精品课":
        return "bg-yellow-500 hover:bg-yellow-600"
      case "私教课":
        return "bg-red-500 hover:bg-red-600"
      case "教培课":
        return "bg-purple-500 hover:bg-purple-600"
      default:
        return "bg-gray-500 hover:bg-gray-600"
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Dumbbell className="h-5 w-5 text-blue-500" />
              今日课程
            </CardTitle>
            <CardDescription>门店今日课程安排</CardDescription>
          </div>
          <Badge variant="outline">4 节课程</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {classes.map((classItem) => (
            <div key={classItem.id} className="flex items-start justify-between border-b pb-3">
              <div>
                <div className="font-medium">{classItem.name}</div>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={getClassTypeBadgeColor(classItem.type)}>
                    {classItem.type}
                  </Badge>
                  <span className="text-sm text-muted-foreground">{classItem.coach}</span>
                </div>
                <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span>{classItem.time}</span>
                  <span>|</span>
                  <span>{classItem.room}</span>
                </div>
              </div>
              <div className="flex flex-col items-end">
                <div className="flex items-center gap-1 text-sm">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {classItem.enrolled}/{classItem.capacity}
                  </span>
                </div>
                <div className="w-16 bg-gray-200 rounded-full h-1.5 mt-1">
                  <div 
                    className="bg-blue-600 h-1.5 rounded-full" 
                    style={{ width: `${(classItem.enrolled / classItem.capacity) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" size="sm">
          <Calendar className="mr-2 h-4 w-4" />
          课程表
        </Button>
        <Button size="sm">
          查看全部
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  )
}
