import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios';

// 定义API响应数据结构
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 获取API基础URL - 支持服务端和客户端不同配置
const getApiBaseUrl = () => {
  // 服务端渲染时使用内部网络地址
  if (typeof window === 'undefined') {
    return process.env.API_BASE_URL || 'http://localhost:8000';
  }
  // 客户端使用公网地址
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
};

// 输出API基础URL信息便于调试
console.log('API基础URL:', getApiBaseUrl());

// 创建一个axios实例
const axiosInstance: AxiosInstance = axios.create({
  // 根据运行环境动态配置API基础URL
  baseURL: getApiBaseUrl(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
    console.log(`准备发送请求: ${config.method?.toUpperCase()} ${config.url}`)

    // 简化token获取逻辑
    let token = ''

    //  标记读取了哪个存储位置
    let storageSrc = "";

    try {
      // 从localStorage或sessionStorage获取token
      const localToken = localStorage.getItem('token');
      if (localToken) {
        token = localToken;
        storageSrc = "localStorage";
      } else {
        const sessionToken = sessionStorage.getItem('token');
        if (sessionToken) {
          token = sessionToken;
          storageSrc = "sessionStorage";
        }
      }

      console.log(`获取token结果: ${token ? '成功' : '失败'}, 来源: ${storageSrc || '无'}, 长度: ${token?.length || 0}`);

      if (token && config.headers) {
        // 不使用任何变量，直接硬编码小写的bearer
        console.log('设置authorization头 (硬编码小写bearer)')

        // 移除可能存在的Authorization和authorization头，避免冲突
        delete config.headers['Authorization']
        delete config.headers['authorization']

        // 直接使用小写形式设置
        config.headers['authorization'] = 'bearer ' + token

        console.log('Authorization头已设置为: bearer ' + token.substring(0, 10) + '...')
      }

      // 获取租户ID
      const tenantId = localStorage.getItem('tenant_id') || sessionStorage.getItem('tenant_id')
      if (tenantId && config.headers) {
        config.headers['X-Tenant-ID'] = tenantId
      }
    } catch (error) {
      console.error('设置请求头失败:', error)
    }

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    // 对成功的响应进行处理
    const res = response.data;
    const url = response.config.url || '';

    // 记录成功响应
    console.log(`响应 ${url}`, {
      状态: response.status,
      业务码: res.code,
      数据长度: res.data ? (typeof res.data === 'object' ? Object.keys(res.data).length : '非对象') : '无数据'
    });

    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response;
    }

    // 特殊处理登录请求，直接返回响应
    if (url.includes('/login')) {
      return response;
    }

    // 如果是非0状态码，视为业务错误
    if (res.code !== undefined && res.code !== 0) {
      console.error(`业务错误(${res.code}):`, res.message);

      // 401错误需要特殊处理，但不要在所有请求都直接跳转
      // 仅在明确的登录失效场景才跳转，如token无效或过期
      if (res.code === 401 && res.message &&
          (res.message.includes('token') ||
           res.message.includes('登录') ||
           res.message.includes('认证'))) {

        console.error('认证失败(401):', res.message);

        // 确认是认证失败再清除token并重定向
        if (typeof window !== 'undefined') {
          // 清除所有相关存储
          localStorage.removeItem('token');
          localStorage.removeItem('token_type');
          localStorage.removeItem('userInfo');
          localStorage.removeItem('tenant_id');
          sessionStorage.removeItem('token');
          sessionStorage.removeItem('token_type');
          sessionStorage.removeItem('userInfo');
          sessionStorage.removeItem('tenant_id');

          // 跳转到登录页，附加当前URL作为重定向参数
          const currentPath = window.location.pathname;
          if (currentPath !== '/login') {
            window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
          } else {
            window.location.href = '/login';
          }
        }
      } else if (res.code === 403) {
        console.error('权限不足(403):', res.message);

        // 权限不足，重定向到无权限页面
        if (typeof window !== 'undefined' && !url.includes('/settings/users')) {
          // 跳转到无权限页面
          window.location.href = '/403';
        }
      }

      // 返回原始响应但使用Promise.reject
      throw new Error(res.message || '请求失败');
    }

    return response;
  },
  (error) => {
    // 处理响应错误
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data, config } = error.response;
      const url = config.url || '';

      console.error(`请求错误 ${url}`, {
        状态码: status,
        错误信息: data?.message || '未知错误',
        请求方法: config.method?.toUpperCase()
      });

      // 特殊处理登录请求的错误
      if (url.includes('/login')) {
        return Promise.reject(error);
      }

      // 处理401错误 - 但在用户管理页面有特殊处理
      if (status === 401) {
        console.error('HTTP认证失败(401):', data?.message);

        // 避免在用户管理页面频繁跳转登录
        if (typeof window !== 'undefined' && !url.includes('/settings/users')) {
          // 清除所有相关存储
          localStorage.removeItem('token');
          localStorage.removeItem('token_type');
          localStorage.removeItem('userInfo');
          localStorage.removeItem('tenant_id');
          sessionStorage.removeItem('token');
          sessionStorage.removeItem('token_type');
          sessionStorage.removeItem('userInfo');
          sessionStorage.removeItem('tenant_id');

          // 跳转到登录页，附加当前URL作为重定向参数
          const currentPath = window.location.pathname;
          if (currentPath !== '/login') {
            window.location.href = `/login?redirect=${encodeURIComponent(currentPath)}`;
          } else {
            window.location.href = '/login';
          }
        }
      } else if (status === 403) {
        console.error('HTTP权限不足(403):', data?.message);

        // 避免在用户管理页面频繁跳转
        if (typeof window !== 'undefined' && !url.includes('/settings/users')) {
          // 跳转到无权限页面
          window.location.href = '/403';
        }
      } else if (status === 404) {
        console.error('资源不存在(404):', url);
      } else if (status >= 500) {
        console.error('服务器错误:', data?.message);
      }
    } else if (error.request) {
      // 请求已发送但未收到响应
      console.error('网络错误，请检查您的网络连接和服务器状态');
    } else {
      // 请求配置出错
      console.error('请求配置错误:', error.message);
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;