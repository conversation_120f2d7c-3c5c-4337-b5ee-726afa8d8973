"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { MoreHorizontal, Pencil, Trash2, Eye, Copy, Calendar, Users, BarChart3, Clock, TrendingUp, FileX } from "lucide-react"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface CourseTableProps {
  visibleColumns: {
    id: boolean
    name: boolean
    type: boolean
    defaultCoach: boolean // 默认教练
    defaultVenue: boolean // 默认场地
    price: boolean
    duration: boolean // 课程时长
    level: boolean // 课程难度
    capacity: boolean
    schedules: boolean // 排期统计
    bookings: boolean // 已预约人数
    rating: boolean // 平均评分
    status: boolean
    tags: boolean // 课程标签
    equipment: boolean // 器材要求
    notes: boolean // 备注
  }
  onSelectedItemsChange?: (items: string[]) => void
  onEditCourse?: (course: any) => void
  onCopyCourse?: (course: any) => void // 复制课程回调
  onDeleteCourse?: (courseId: string) => void // 删除课程回调
  courses?: any[] // 课程数据
  loading?: boolean // 加载状态
  total?: number // 总记录数
  page?: number // 当前页码
  pageSize?: number // 每页条数
  onPageChange?: (page: number) => void // 页码变更回调
  onPageSizeChange?: (pageSize: number) => void // 每页条数变更回调
}

export function CourseTable({
  visibleColumns,
  onSelectedItemsChange,
  onEditCourse,
  onCopyCourse,
  onDeleteCourse,
  courses = [], // 使用传入的课程数据，默认为空数组
  loading = false, // 默认非加载状态
  total = 0, // 默认总数为0
  page = 1, // 默认第1页
  pageSize = 10, // 默认每页10条
  onPageChange, // 页码变更回调
  onPageSizeChange // 每页条数变更回调
}: CourseTableProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [viewCourse, setViewCourse] = useState<any>(null)
  const [showViewDialog, setShowViewDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [courseToDelete, setCourseToDelete] = useState<any>(null)

  // 计算总页数
  const totalPages = Math.ceil(total / pageSize)

  // 空状态展示
  if (!loading && courses.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <div className="text-center">
            <FileX className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-medium">没有找到课程</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              {selectedItems.length > 0 || Object.values(visibleColumns).some(v => !v) ? 
                "尝试重置筛选条件或更改搜索关键词" : 
                "您可以添加新课程或重置筛选条件"}
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 当前显示的课程数据，使用传入的courses
  // const currentCourses = courses

  // 处理选择所有项
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = courses.map((course) => course.id)
      setSelectedItems(allIds)
      if (onSelectedItemsChange) {
        onSelectedItemsChange(allIds)
      }
    } else {
      setSelectedItems([])
      if (onSelectedItemsChange) {
        onSelectedItemsChange([])
      }
    }
  }

  // 处理选择单个项
  const handleSelectItem = (id: string, checked: boolean) => {
    let newSelectedItems: string[]

    if (checked) {
      newSelectedItems = [...selectedItems, id]
    } else {
      newSelectedItems = selectedItems.filter((item) => item !== id)
    }

    setSelectedItems(newSelectedItems)
    if (onSelectedItemsChange) {
      onSelectedItemsChange(newSelectedItems)
    }
  }

  // 处理查看课程详情
  const handleViewCourse = (course: any) => {
    setViewCourse(course)
    setShowViewDialog(true)
  }

  // 处理复制课程
  const handleCopyCourse = (course: any) => {
    try {
      // 生成建议的课程名称（避免重名）
      let suggestedName = `${course.name} - 副本`
      let counter = 1

      // 检查是否存在同名课程，如果存在则添加数字后缀
      while (courses.some(c => c.name === suggestedName)) {
        counter++
        suggestedName = `${course.name} - 副本${counter}`
      }

      // 创建课程副本数据
      const copiedCourse = {
        ...course,
        id: `${course.id}_copy_${Date.now()}`,
        name: suggestedName,
        status: 'inactive',
        statusName: '未开始',
        createdAt: new Date().toISOString(),
        totalBookings: 0,
        activeSchedules: 0,
        totalSchedules: 0
      }

      // 调用父组件的复制回调，传入复制的课程数据以便在编辑对话框中使用
      if (onCopyCourse) {
        onCopyCourse(copiedCourse)
      }

    } catch (error) {
      console.error('复制课程失败:', error)
      toast({
        title: "复制失败",
        description: "复制课程时发生错误，请重试",
        variant: "destructive"
      })
    }
  }

  // 处理删除课程
  const handleDeleteCourse = (course: any) => {
    setCourseToDelete(course)
    setShowDeleteDialog(true)
  }

  // 确认删除课程
  const confirmDeleteCourse = () => {
    if (courseToDelete) {
      try {
        // 调用父组件的删除回调
        if (onDeleteCourse) {
          onDeleteCourse(courseToDelete.id)
        }

        // 显示成功提示
        toast({
          title: "删除成功",
          description: `课程 "${courseToDelete.name}" 已删除`,
          variant: "default"
        })

        // 关闭对话框并清理状态
        setShowDeleteDialog(false)
        setCourseToDelete(null)
      } catch (error) {
        console.error('删除课程失败:', error)
        toast({
          title: "删除失败",
          description: "删除课程时发生错误，请重试",
          variant: "destructive"
        })
      }
    }
  }

  // 处理页码变更
  const handlePageChange = (newPage: number) => {
    if (onPageChange) {
      onPageChange(newPage)
    }
    // 清除选择
    setSelectedItems([])
    if (onSelectedItemsChange) {
      onSelectedItemsChange([])
    }
  }

  // 处理每页条数变更
  const handlePageSizeChange = (newPageSize: number) => {
    if (onPageSizeChange) {
      onPageSizeChange(newPageSize)
    }
  }

  // 生成页码链接
  const renderPaginationItems = () => {
    const items = []

    // 始终显示第一页
    items.push(
      <PaginationItem key="first">
        <PaginationLink onClick={() => handlePageChange(1)} isActive={page === 1}>
          1
        </PaginationLink>
      </PaginationItem>,
    )

    // 如果当前页大于3，显示省略号
    if (page > 3) {
      items.push(
        <PaginationItem key="ellipsis1">
          <PaginationEllipsis />
        </PaginationItem>,
      )
    }

    // 显示当前页附近的页码
    for (let i = Math.max(2, page - 1); i <= Math.min(totalPages - 1, page + 1); i++) {
      if (i === 1 || i === totalPages) continue // 跳过第一页和最后一页，因为它们已经单独处理

      items.push(
        <PaginationItem key={i}>
          <PaginationLink onClick={() => handlePageChange(i)} isActive={page === i}>
            {i}
          </PaginationLink>
        </PaginationItem>,
      )
    }

    // 如果当前页小于总页数-2，显示省略号
    if (page < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis2">
          <PaginationEllipsis />
        </PaginationItem>,
      )
    }

    // 如果总页数大于1，始终显示最后一页
    if (totalPages > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink onClick={() => handlePageChange(totalPages)} isActive={page === totalPages}>
            {totalPages}
          </PaginationLink>
        </PaginationItem>,
      )
    }

    return items
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <Checkbox
                  checked={courses.length > 0 && selectedItems.length === courses.length}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all"
                />
              </TableHead>
              {visibleColumns.id && <TableHead>课程编号</TableHead>}
              {visibleColumns.name && <TableHead>课程名称</TableHead>}
              {visibleColumns.type && <TableHead>课程类型</TableHead>}
              {visibleColumns.defaultCoach && <TableHead>默认教练</TableHead>}
              {visibleColumns.defaultVenue && <TableHead>默认场地</TableHead>}
              {visibleColumns.price && <TableHead>标准价格</TableHead>}
              {visibleColumns.duration && <TableHead>时长</TableHead>}
              {visibleColumns.level && <TableHead>难度</TableHead>}
              {visibleColumns.capacity && <TableHead>标准容量</TableHead>}
              {visibleColumns.schedules && <TableHead>排期统计</TableHead>}
              {visibleColumns.bookings && <TableHead>总预约</TableHead>}
              {visibleColumns.rating && <TableHead>平均评分</TableHead>}
              {visibleColumns.status && <TableHead>状态</TableHead>}
              {visibleColumns.tags && <TableHead>标签</TableHead>}
              {visibleColumns.equipment && <TableHead>器材</TableHead>}
              {visibleColumns.notes && <TableHead>备注</TableHead>}
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              // 加载状态骨架屏效果
              Array.from({ length: pageSize }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  <TableCell>
                    <div className="h-4 w-4 bg-muted animate-pulse rounded" />
                  </TableCell>
                  {visibleColumns.id && <TableCell><div className="h-4 w-16 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.name && <TableCell><div className="h-4 w-32 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.type && <TableCell><div className="h-6 w-20 bg-muted animate-pulse rounded-full" /></TableCell>}
                  {visibleColumns.defaultCoach && (
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="h-6 w-6 bg-muted animate-pulse rounded-full" />
                        <div className="h-4 w-16 bg-muted animate-pulse rounded" />
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.defaultVenue && <TableCell><div className="h-4 w-20 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.price && <TableCell><div className="h-4 w-12 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.duration && <TableCell><div className="h-4 w-12 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.level && <TableCell><div className="h-4 w-16 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.capacity && <TableCell><div className="h-4 w-12 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.schedules && <TableCell><div className="h-4 w-16 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.bookings && <TableCell><div className="h-4 w-12 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.rating && <TableCell><div className="h-4 w-12 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.status && <TableCell><div className="h-6 w-16 bg-muted animate-pulse rounded-full" /></TableCell>}
                  {visibleColumns.tags && <TableCell><div className="h-4 w-20 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.equipment && <TableCell><div className="h-4 w-16 bg-muted animate-pulse rounded" /></TableCell>}
                  {visibleColumns.notes && <TableCell><div className="h-4 w-24 bg-muted animate-pulse rounded" /></TableCell>}
                  <TableCell className="text-right">
                    <div className="h-8 w-8 bg-muted animate-pulse rounded-full ml-auto" />
                  </TableCell>
                </TableRow>
              ))
            ) : courses.length === 0 ? (
              // 无数据状态
              <TableRow>
                <TableCell colSpan={Object.values(visibleColumns).filter(Boolean).length + 2} className="text-center py-10">
                  <div className="flex flex-col items-center gap-2">
                    <div className="h-12 w-12 rounded-full border flex items-center justify-center text-muted-foreground">
                      <Calendar className="h-6 w-6" />
                    </div>
                    <h3 className="font-semibold text-lg">暂无课程数据</h3>
                    <p className="text-muted-foreground">没有找到符合条件的课程记录</p>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              // 课程数据
              courses.map((course) => (
                <TableRow key={course.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedItems.includes(course.id)}
                      onCheckedChange={(checked) => handleSelectItem(course.id, !!checked)}
                      aria-label={`Select ${course.name}`}
                    />
                  </TableCell>
                  {visibleColumns.id && <TableCell className="font-medium">{course.id}</TableCell>}
                  {visibleColumns.name && <TableCell>{course.name}</TableCell>}
                  {visibleColumns.type && (
                    <TableCell>
                      <Badge style={{ backgroundColor: course.typeColor, color: "white" }}>{course.typeName}</Badge>
                    </TableCell>
                  )}
                  {visibleColumns.defaultCoach && (
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={course.coachAvatar} alt={course.defaultCoachName} />
                          <AvatarFallback>{course.defaultCoachName?.[0] || "?"}</AvatarFallback>
                        </Avatar>
                        <span>{course.defaultCoachName || "未分配"}</span>
                      </div>
                    </TableCell>
                  )}

                  {visibleColumns.defaultVenue && (
                    <TableCell>
                      {course.defaultVenueName ? (
                        <span title={course.defaultVenueName}>{course.defaultVenueName}</span>
                      ) : course.defaultVenue ? (
                        <span className="text-muted-foreground" title={`场地ID: ${course.defaultVenue}`}>
                          场地ID: {course.defaultVenue.substring(0, 6)}{course.defaultVenue.length > 6 ? '...' : ''}
                        </span>
                      ) : (
                        <span className="text-muted-foreground italic">未设置场地</span>
                      )}
                    </TableCell>
                  )}
                  {visibleColumns.price && (
                    <TableCell>
                      {typeof course.price === 'number' ? `¥${course.price}` : course.price}
                    </TableCell>
                  )}
                  {visibleColumns.duration && (
                    <TableCell>
                      {course.duration}分钟
                    </TableCell>
                  )}
                  {visibleColumns.level && (
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <span
                            key={star}
                            className={`text-sm ${
                              parseInt(course.level?.toString() || "1") >= star
                                ? "text-yellow-400"
                                : "text-gray-300"
                            }`}
                          >
                            ★
                          </span>
                        ))}
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.capacity && (
                    <TableCell>
                      {course.capacity}人
                    </TableCell>
                  )}
                  {visibleColumns.schedules && (
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span>{course.activeSchedules || 0}</span>
                        <span className="text-muted-foreground">/{course.totalSchedules || 0}</span>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.bookings && (
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span>{course.totalBookings || 0}</span>
                        <span className="text-muted-foreground text-xs">总预约</span>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.rating && (
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span className="text-yellow-400">★</span>
                        <span>{course.averageRating?.toFixed(1) || "暂无"}</span>
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.status && (
                    <TableCell>
                      <Badge
                        variant={
                          course.status === "active"
                            ? "default"
                            : course.status === "upcoming"
                              ? "outline"
                              : course.status === "ended"
                                ? "secondary"
                                : "destructive"
                        }
                      >
                        {course.statusName}
                      </Badge>
                    </TableCell>
                  )}
                  {visibleColumns.tags && (
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {course.tags && course.tags.length > 0 ? (
                          course.tags.slice(0, 2).map((tag: string, index: number) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))
                        ) : (
                          <span className="text-muted-foreground text-xs">无标签</span>
                        )}
                        {course.tags && course.tags.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{course.tags.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                  )}
                  {visibleColumns.equipment && (
                    <TableCell>
                      {course.requiresEquipment ? (
                        <Badge variant="outline" className="text-xs">
                          需要器材
                        </Badge>
                      ) : (
                        <span className="text-muted-foreground text-xs">无需器材</span>
                      )}
                    </TableCell>
                  )}
                  {visibleColumns.notes && (
                    <TableCell>
                      {course.notes ? (
                        <span className="text-xs" title={course.notes}>
                          {course.notes.length > 20 ? `${course.notes.substring(0, 20)}...` : course.notes}
                        </span>
                      ) : (
                        <span className="text-muted-foreground text-xs">无备注</span>
                      )}
                    </TableCell>
                  )}
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleViewCourse(course)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEditCourse?.(course)}>
                          <Pencil className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleCopyCourse(course)}>
                          <Copy className="mr-2 h-4 w-4" />
                          复制
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteCourse(course)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <div className="border-t pt-4 mt-2">
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground"style={{width: '250px'}}>
            显示 {(page - 1) * pageSize + 1} 到 {Math.min(page * pageSize, total)} 条，共 {total} 条
          </div>

          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => page > 1 ? handlePageChange(Math.max(1, page - 1)) : undefined}
                  aria-disabled={page === 1}
                  className={page === 1 ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>

              {renderPaginationItems()}

              <PaginationItem>
                <PaginationNext
                  onClick={() => page < totalPages ? handlePageChange(Math.min(totalPages, page + 1)) : undefined}
                  aria-disabled={page === totalPages}
                  className={page === totalPages ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>

          <div className="flex items-center gap-1">
            <span className="text-sm text-muted-foreground" style={{width: '30px'}}>每页</span>
            <Select
              value={pageSize.toString()}
              onValueChange={(value) => {
                handlePageSizeChange(Number.parseInt(value))
              }}
            >
              <SelectTrigger className="w-[60px] h-8">
                <SelectValue placeholder={pageSize.toString()} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-muted-foreground">条</span>
          </div>
        </div>
      </div>


      {/* 课程详情对话框 */}
      <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {viewCourse?.name}
              <Badge
                variant={
                  viewCourse?.status === "active"
                    ? "default"
                    : viewCourse?.status === "upcoming"
                      ? "outline"
                      : viewCourse?.status === "ended"
                        ? "secondary"
                        : "destructive"
                }
              >
                {viewCourse?.statusName}
              </Badge>
            </DialogTitle>
            <DialogDescription>查看课程的详细信息和统计数据</DialogDescription>
          </DialogHeader>

          {viewCourse && (
            <Tabs defaultValue="info" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="info">基本信息</TabsTrigger>
                <TabsTrigger value="schedules">排期管理</TabsTrigger>
                <TabsTrigger value="stats">数据统计</TabsTrigger>
              </TabsList>

              <TabsContent value="info" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程编号</p>
                    <p className="font-medium">{viewCourse.id}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程类型</p>
                    <Badge style={{ backgroundColor: viewCourse.typeColor, color: "white" }}>{viewCourse.typeName}</Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">默认教练</p>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={viewCourse.coachAvatar} alt={viewCourse.defaultCoachName || viewCourse.coachName} />
                        <AvatarFallback>{(viewCourse.defaultCoachName || viewCourse.coachName)?.[0] || "?"}</AvatarFallback>
                      </Avatar>
                      <span>{viewCourse.defaultCoachName || viewCourse.coachName || "未分配"}</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">默认场地</p>
                    <p>{viewCourse.defaultVenueName || viewCourse.venueName || viewCourse.venue || '未设置场地'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程时长</p>
                    <p>{viewCourse.duration}分钟</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">标准价格</p>
                    <p>{typeof viewCourse.price === 'number' ? `¥${viewCourse.price}` : viewCourse.price}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">标准容量</p>
                    <p>{viewCourse.capacity}人</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程难度</p>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <span key={i} className={`text-sm ${i < (viewCourse.level || 1) ? 'text-yellow-500' : 'text-gray-300'}`}>
                          ★
                        </span>
                      ))}
                      <span className="text-sm text-muted-foreground ml-1">({viewCourse.level || 1}星)</span>
                    </div>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">课程描述</p>
                  <p className="mt-1">{viewCourse.description}</p>
                </div>

                {viewCourse.tags && viewCourse.tags.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">课程标签</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {viewCourse.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {viewCourse.requiresEquipment && viewCourse.equipmentList && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">所需器材</p>
                    <p className="mt-1">{viewCourse.equipmentList}</p>
                  </div>
                )}

                {viewCourse.prerequisites && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">参与条件</p>
                    <p className="mt-1">{viewCourse.prerequisites}</p>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-muted-foreground">创建时间</p>
                  <p className="mt-1">{new Date(viewCourse.createdAt).toLocaleString("zh-CN")}</p>
                </div>
              </TabsContent>

              <TabsContent value="schedules" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>排期管理</CardTitle>
                    <CardDescription>查看和管理课程的排期信息</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">总排期数</p>
                          <p className="text-xl font-bold">{viewCourse.totalSchedules || 0}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-green-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">活跃排期</p>
                          <p className="text-xl font-bold">{viewCourse.activeSchedules || 0}</p>
                        </div>
                      </div>
                    </div>

                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>日期</TableHead>
                          <TableHead>时间</TableHead>
                          <TableHead>教练</TableHead>
                          <TableHead>场地</TableHead>
                          <TableHead>预约/容量</TableHead>
                          <TableHead>状态</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell>2024-01-15</TableCell>
                          <TableCell>10:00-11:30</TableCell>
                          <TableCell>张教练</TableCell>
                          <TableCell>1号瑜伽室</TableCell>
                          <TableCell>12/15</TableCell>
                          <TableCell><Badge variant="default">进行中</Badge></TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>2024-01-17</TableCell>
                          <TableCell>14:00-15:30</TableCell>
                          <TableCell>张教练</TableCell>
                          <TableCell>1号瑜伽室</TableCell>
                          <TableCell>8/15</TableCell>
                          <TableCell><Badge variant="outline">即将开始</Badge></TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>2024-01-19</TableCell>
                          <TableCell>16:00-17:30</TableCell>
                          <TableCell>张教练</TableCell>
                          <TableCell>1号瑜伽室</TableCell>
                          <TableCell>15/15</TableCell>
                          <TableCell><Badge variant="default">已满员</Badge></TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="stats" className="space-y-4 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <Users className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">总预约数</p>
                          <p className="text-2xl font-bold">{viewCourse.totalBookings || 0}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <BarChart3 className="h-5 w-5 text-green-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">平均出席率</p>
                          <p className="text-2xl font-bold">87%</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <TrendingUp className="h-5 w-5 text-orange-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">平均评分</p>
                          <p className="text-2xl font-bold">{viewCourse.averageRating?.toFixed(1) || "4.5"}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-5 w-5 text-purple-500" />
                        <div>
                          <p className="text-sm font-medium text-muted-foreground">本月课次</p>
                          <p className="text-2xl font-bold">12</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>预约趋势</CardTitle>
                      <CardDescription>近6个月预约数据</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[200px] flex items-end gap-2">
                        {[28, 35, 42, 38, 45, 52].map((count, index) => (
                          <div key={index} className="flex flex-col items-center flex-1">
                            <div
                              className="w-full bg-primary rounded-t-sm"
                              style={{
                                height: `${(count / 52) * 150}px`,
                              }}
                            />
                            <div className="text-xs mt-2">{index + 1}月</div>
                            <div className="text-xs text-muted-foreground">{count}</div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>会员反馈</CardTitle>
                      <CardDescription>最近的评价和建议</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="border-l-4 border-green-500 pl-3">
                          <p className="text-sm">"课程内容很丰富，教练很专业！"</p>
                          <p className="text-xs text-muted-foreground">★★★★★ - 张会员</p>
                        </div>
                        <div className="border-l-4 border-blue-500 pl-3">
                          <p className="text-sm">"动作讲解很详细，适合初学者。"</p>
                          <p className="text-xs text-muted-foreground">★★★★☆ - 李会员</p>
                        </div>
                        <div className="border-l-4 border-yellow-500 pl-3">
                          <p className="text-sm">"希望能增加一些进阶动作。"</p>
                          <p className="text-xs text-muted-foreground">★★★★☆ - 王会员</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowViewDialog(false)}>
              关闭
            </Button>
            <Button
              onClick={() => {
                setShowViewDialog(false)
                onEditCourse?.(viewCourse)
              }}
            >
              <Pencil className="mr-2 h-4 w-4" />
              编辑课程
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除课程 "{courseToDelete?.name}" 吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDeleteCourse}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

