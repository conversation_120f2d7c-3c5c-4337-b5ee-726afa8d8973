"use client"

import { UserManagement } from "@/components/settings/user-management"
import { useEffect } from "react"
import { useToast } from "@/hooks/use-toast"

export default function UsersPage() {
  const { toast } = useToast()
  
  // 页面加载时处理
  useEffect(() => {
    console.log('用户管理页面已加载')
    
    // 更新页面标题
    document.title = "用户管理 - 瑜伽管理系统"
    
    // 错误处理函数
    const handleError = (event: ErrorEvent) => {
      console.error('页面错误:', event.error)
      toast({
        title: "页面错误",
        description: "加载发生错误，请检查控制台",
        variant: "destructive",
      })
    }
    
    // 添加全局错误监听
    window.addEventListener('error', handleError)
    
    // 清理函数
    return () => {
      window.removeEventListener('error', handleError)
    }
  }, [toast])
  
  return (
    <div className="flex flex-col space-y-6">
      <div className="flex flex-col space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">用户管理</h2>
        <p className="text-muted-foreground">管理系统用户、角色和访问权限设置。</p>
      </div>
      <UserManagement />
    </div>
  )
}

