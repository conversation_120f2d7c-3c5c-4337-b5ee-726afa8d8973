"use client"

import { useState } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { QrCode, BarChart, Settings, ClipboardList } from "lucide-react"

export default function VerificationSystemPage() {
  const [activeTab, setActiveTab] = useState("overview")

  // 渠道核销系统的功能模块
  const modules = [
    {
      id: "meituan",
      name: "美团核销",
      description: "处理美团平台的订单核销，支持扫码核销、手动输入核销和批量核销",
      icon: <QrCode className="h-8 w-8 text-yellow-500" />,
      route: "/verification/meituan",
      stats: {
        today: 12,
        week: 87,
        month: 342
      }
    },
    {
      id: "douyin",
      name: "抖音核销",
      description: "处理抖音平台的订单核销，支持扫码核销和手动输入核销",
      icon: <QrCode className="h-8 w-8 text-black" />,
      route: "/verification/douyin",
      stats: {
        today: 8,
        week: 53,
        month: 215
      }
    },
    {
      id: "records",
      name: "核销记录",
      description: "查询和管理所有渠道的核销记录，支持按时间、渠道和状态筛选",
      icon: <ClipboardList className="h-8 w-8 text-blue-500" />,
      route: "/verification/records",
      stats: {
        total: 1254,
        success: 1203,
        failed: 51
      }
    },
    {
      id: "settings",
      name: "核销设置",
      description: "配置各渠道的核销参数，包括API密钥、回调地址和核销规则",
      icon: <Settings className="h-8 w-8 text-gray-500" />,
      route: "/verification/settings",
      stats: {
        channels: 2,
        rules: 6,
        webhooks: 4
      }
    }
  ]

  // 核销统计数据
  const stats = {
    total: 1254,
    meituan: 783,
    douyin: 471,
    today: 20,
    week: 140,
    month: 557
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">渠道核销系统</h1>
          <p className="text-muted-foreground">
            管理美团、抖音等第三方渠道的订单核销
          </p>
        </div>
        <Button asChild>
          <Link href="/verification/meituan">
            <QrCode className="mr-2 h-4 w-4" />
            扫码核销
          </Link>
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">系统概览</TabsTrigger>
          <TabsTrigger value="modules">功能模块</TabsTrigger>
          <TabsTrigger value="stats">核销统计</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* 核销统计卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总核销次数</CardTitle>
                <QrCode className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.total}</div>
                <p className="text-xs text-muted-foreground">
                  所有渠道的总核销次数
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">今日核销</CardTitle>
                <QrCode className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.today}</div>
                <p className="text-xs text-muted-foreground">
                  今日完成的核销次数
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">美团核销</CardTitle>
                <QrCode className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.meituan}</div>
                <p className="text-xs text-muted-foreground">
                  美团平台的核销次数
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">抖音核销</CardTitle>
                <QrCode className="h-4 w-4 text-black" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.douyin}</div>
                <p className="text-xs text-muted-foreground">
                  抖音平台的核销次数
                </p>
              </CardContent>
            </Card>
          </div>

          {/* 快速访问模块 */}
          <Card>
            <CardHeader>
              <CardTitle>快速访问</CardTitle>
              <CardDescription>
                访问常用的核销功能模块
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {modules.map((module) => (
                  <Link key={module.id} href={module.route}>
                    <Card className="h-full cursor-pointer hover:bg-muted/50 transition-colors">
                      <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-2">
                        <CardTitle className="text-base font-medium">{module.name}</CardTitle>
                        {module.icon}
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {module.description}
                        </p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="modules" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {modules.map((module) => (
              <Card key={module.id} className="overflow-hidden">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {module.icon}
                      <CardTitle>{module.name}</CardTitle>
                    </div>
                  </div>
                  <CardDescription>{module.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center">
                    <div>
                      {module.id === "meituan" || module.id === "douyin" ? (
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">今日核销:</span>
                            <span>{module.stats.today}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">本周核销:</span>
                            <span>{module.stats.week}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">本月核销:</span>
                            <span>{module.stats.month}</span>
                          </div>
                        </div>
                      ) : module.id === "records" ? (
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">总记录数:</span>
                            <span>{module.stats.total}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">成功核销:</span>
                            <span>{module.stats.success}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">失败核销:</span>
                            <span>{module.stats.failed}</span>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">已配置渠道:</span>
                            <span>{module.stats.channels}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">核销规则:</span>
                            <span>{module.stats.rules}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Webhook:</span>
                            <span>{module.stats.webhooks}</span>
                          </div>
                        </div>
                      )}
                    </div>
                    <Button asChild>
                      <Link href={module.route}>访问</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>核销统计</CardTitle>
              <CardDescription>
                查看各渠道的核销数据统计
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center">
                <p className="text-muted-foreground">此处将显示核销数据统计图表</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
