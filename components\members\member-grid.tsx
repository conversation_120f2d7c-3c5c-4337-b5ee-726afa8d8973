"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>eader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { MoreHorizontal, Pencil, CreditCard, User, Calendar, MessageSquare } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { MemberDetailDialog } from "@/components/members/member-detail-dialog"

const members = [
  {
    id: "M001",
    name: "张三",
    avatar: "/placeholder.svg?height=64&width=64",
    phone: "138****1234",
    level: "金卡会员",
    card: "次卡",
    remaining: "15次",
    expiry: "2025-12-31",
    status: "active",
    tags: ["新会员", "瑜伽爱好者"],
    lastVisit: "2023-03-25",
    totalSpent: "¥3,600",
    joinDate: "2023-01-15",
  },
  {
    id: "M002",
    name: "李四",
    avatar: "/placeholder.svg?height=64&width=64",
    phone: "139****5678",
    level: "银卡会员",
    card: "月卡",
    remaining: "无限次",
    expiry: "2025-04-30",
    status: "active",
    tags: ["老会员"],
    lastVisit: "2023-03-28",
    totalSpent: "¥2,400",
    joinDate: "2022-05-20",
  },
  {
    id: "M003",
    name: "王五",
    avatar: "/placeholder.svg?height=64&width=64",
    phone: "137****9012",
    level: "白金会员",
    card: "年卡",
    remaining: "无限次",
    expiry: "2026-03-15",
    status: "active",
    tags: ["VIP", "教练推荐"],
    lastVisit: "2023-03-27",
    totalSpent: "¥8,800",
    joinDate: "2022-03-10",
  },
  {
    id: "M004",
    name: "赵六",
    avatar: "/placeholder.svg?height=64&width=64",
    phone: "136****3456",
    level: "标准会员",
    card: "次卡",
    remaining: "3次",
    expiry: "2025-05-20",
    status: "active",
    tags: ["潜在流失"],
    lastVisit: "2023-02-15",
    totalSpent: "¥1,200",
    joinDate: "2022-11-05",
  },
  {
    id: "M005",
    name: "钱七",
    avatar: "/placeholder.svg?height=64&width=64",
    phone: "135****7890",
    level: "银卡会员",
    card: "季卡",
    remaining: "无限次",
    expiry: "2025-06-30",
    status: "inactive",
    tags: ["已流失"],
    lastVisit: "2023-01-10",
    totalSpent: "¥3,200",
    joinDate: "2022-07-18",
  },
  {
    id: "M006",
    name: "孙八",
    avatar: "/placeholder.svg?height=64&width=64",
    phone: "134****1234",
    level: "金卡会员",
    card: "年卡",
    remaining: "无限次",
    expiry: "2026-01-15",
    status: "active",
    tags: ["VIP", "瑜伽爱好者"],
    lastVisit: "2023-03-26",
    totalSpent: "¥7,200",
    joinDate: "2022-01-10",
  },
]

export function MemberGrid() {
  const [selectedMembers, setSelectedMembers] = useState<string[]>([])
  const [detailMember, setDetailMember] = useState<(typeof members)[0] | null>(null)

  const toggleSelectMember = (id: string) => {
    if (selectedMembers.includes(id)) {
      setSelectedMembers(selectedMembers.filter((memberId) => memberId !== id))
    } else {
      setSelectedMembers([...selectedMembers, id])
    }
  }

  const openMemberDetail = (member: (typeof members)[0]) => {
    setDetailMember(member)
  }

  return (
    <>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {members.map((member) => (
          <Card key={member.id} className="overflow-hidden">
            <CardHeader className="relative p-0">
              <div
                className={`absolute left-0 top-0 h-1 w-full ${
                  member.level.includes("白金")
                    ? "bg-purple-500"
                    : member.level.includes("金卡")
                      ? "bg-yellow-500"
                      : member.level.includes("银卡")
                        ? "bg-gray-400"
                        : "bg-blue-500"
                }`}
              />
              <div className="absolute right-2 top-2 z-10">
                <Checkbox
                  checked={selectedMembers.includes(member.id)}
                  onCheckedChange={() => toggleSelectMember(member.id)}
                  aria-label={`选择会员 ${member.name}`}
                />
              </div>
              <div
                className="flex cursor-pointer flex-col items-center p-6 pt-8 hover:bg-muted/50"
                onClick={() => openMemberDetail(member)}
              >
                <Avatar className="h-20 w-20">
                  <AvatarImage src={member.avatar} alt={member.name} />
                  <AvatarFallback className="text-xl">{member.name[0]}</AvatarFallback>
                </Avatar>
                <h3 className="mt-2 text-lg font-semibold">{member.name}</h3>
                <p className="text-sm text-muted-foreground">{member.phone}</p>
                <Badge className="mt-2" variant={member.status === "active" ? "default" : "secondary"}>
                  {member.status === "active" ? "活跃" : "不活跃"}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-4">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="space-y-1">
                  <p className="text-muted-foreground">会员等级</p>
                  <Badge
                    className={`${
                      member.level.includes("白金")
                        ? "bg-purple-500 hover:bg-purple-600"
                        : member.level.includes("金卡")
                          ? "bg-yellow-500 hover:bg-yellow-600 text-black"
                          : member.level.includes("银卡")
                            ? "bg-gray-400 hover:bg-gray-500"
                            : "bg-blue-500 hover:bg-blue-600"
                    }`}
                  >
                    {member.level}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <p className="text-muted-foreground">注册日期</p>
                  <p className="font-medium">{member.joinDate}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-muted-foreground">最近到访</p>
                  <p className="font-medium">{member.lastVisit}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-muted-foreground">累计消费</p>
                  <p className="font-medium">{member.totalSpent}</p>
                </div>
              </div>
              <div className="mt-3 flex flex-wrap gap-1">
                {member.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between p-4 pt-0">
              <Button variant="outline" size="sm" onClick={() => openMemberDetail(member)}>
                <User className="mr-2 h-4 w-4" />
                详情
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>操作</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => window.location.href = `/members/cards/list?memberId=${member.id}`}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    查看会员卡
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Calendar className="mr-2 h-4 w-4" />
                    预约记录
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    发送消息
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Pencil className="mr-2 h-4 w-4" />
                    编辑信息
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardFooter>
          </Card>
        ))}
      </div>

      {selectedMembers.length > 0 && (
        <div className="mt-4 flex items-center gap-2">
          <span className="text-sm text-muted-foreground">已选择 {selectedMembers.length} 个会员</span>
          <Button variant="outline" size="sm">
            批量导出
          </Button>
          <Button variant="outline" size="sm">
            批量标签
          </Button>
          <Button variant="destructive" size="sm">
            批量删除
          </Button>
        </div>
      )}

      {detailMember && (
        <MemberDetailDialog
          member={detailMember}
          open={!!detailMember}
          onOpenChange={(open) => !open && setDetailMember(null)}
        />
      )}
    </>
  )
}

