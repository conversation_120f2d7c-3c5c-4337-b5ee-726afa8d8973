"use client"

import { useState, useEffect } from "react"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"

interface PositionEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  position?: {
    id?: string
    name: string
    department: string
    description: string
    isActive?: boolean
  } | null
  onSave: (position: any) => void
}

const departments = [
  { id: "1", name: "行政部" },
  { id: "2", name: "教学部" },
  { id: "3", name: "销售部" },
  { id: "4", name: "财务部" },
  { id: "5", name: "后勤部" },
]

export function PositionEditDialog({ open, onOpenChange, position, onSave }: PositionEditDialogProps) {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    id: "",
    name: "",
    department: "",
    description: "",
    isActive: true
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 当编辑现有职位时，填充表单数据
  useEffect(() => {
    if (position) {
      setFormData({
        id: position.id || "",
        name: position.name || "",
        department: position.department || "",
        description: position.description || "",
        isActive: position.isActive !== undefined ? position.isActive : true
      })
      setErrors({})
    } else {
      // 新建职位时重置表单
      setFormData({
        id: "",
        name: "",
        department: "",
        description: "",
        isActive: true
      })
    }
  }, [position])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.name.trim()) {
      newErrors.name = "职位名称不能为空"
    }
    
    if (!formData.department) {
      newErrors.department = "请选择所属部门"
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData)
      toast({
        title: position?.id ? "职位已更新" : "职位已创建",
        description: `职位 ${formData.name} 已成功${position?.id ? "更新" : "创建"}`
      })
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{position?.id ? "编辑职位" : "添加新职位"}</DialogTitle>
          <DialogDescription>
            {position?.id ? "修改职位信息" : "创建新的职位并设置基本信息"}
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              职位名称
            </Label>
            <div className="col-span-3">
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="请输入职位名称"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="department" className="text-right">
              所属部门
            </Label>
            <div className="col-span-3">
              <Select 
                value={formData.department} 
                onValueChange={(value) => setFormData({ ...formData, department: value })}
              >
                <SelectTrigger id="department" className={errors.department ? "border-red-500" : ""}>
                  <SelectValue placeholder="选择部门" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.department && <p className="text-red-500 text-sm mt-1">{errors.department}</p>}
            </div>
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              职位描述
            </Label>
            <div className="col-span-3">
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="请输入职位描述"
                className="resize-none"
                rows={3}
              />
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit}>
            {position?.id ? "保存修改" : "创建职位"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
