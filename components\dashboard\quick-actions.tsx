"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import {
  UserPlus,
  CalendarPlus,
  CheckSquare,
  CreditCard,
  FileText,
  ClipboardList,
  BarChart4,
  ShoppingBag,
  Gift,
  MessageSquare,
  Clock,
  Zap
} from "lucide-react"

export function QuickActions() {
  const router = useRouter()

  const actions = [
    {
      icon: <UserPlus className="h-5 w-5" />,
      label: "添加会员",
      description: "新会员登记入会",
      href: "/members/add",
      color: "bg-blue-50 text-blue-600",
    },
    {
      icon: <CalendarPlus className="h-5 w-5" />,
      label: "创建课程",
      description: "添加新课程安排",
      href: "/courses/add",
      color: "bg-purple-50 text-purple-600",
    },
    {
      icon: <CheckSquare className="h-5 w-5" />,
      label: "会员签到",
      description: "课程签到与核销",
      href: "/checkin",
      color: "bg-green-50 text-green-600",
    },
    {
      icon: <CreditCard className="h-5 w-5" />,
      label: "收款",
      description: "会员缴费与收款",
      href: "/payment",
      color: "bg-amber-50 text-amber-600",
    },
    {
      icon: <ShoppingBag className="h-5 w-5" />,
      label: "销售商品",
      description: "瑜伽用品销售",
      href: "/products/sell",
      color: "bg-pink-50 text-pink-600",
    },
    {
      icon: <Gift className="h-5 w-5" />,
      label: "营销活动",
      description: "优惠券与活动",
      href: "/marketing",
      color: "bg-red-50 text-red-600",
    },
    {
      icon: <ClipboardList className="h-5 w-5" />,
      label: "排课管理",
      description: "课程排期安排",
      href: "/courses/schedule",
      color: "bg-indigo-50 text-indigo-600",
    },
    {
      icon: <BarChart4 className="h-5 w-5" />,
      label: "数据报表",
      description: "业务数据分析",
      href: "/statistics/dashboard",
      color: "bg-cyan-50 text-cyan-600",
    },
  ]

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium">快捷操作</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {actions.map((action, index) => (
            <div
              key={index}
              className="flex items-start gap-3 p-3 rounded-lg border hover:border-primary/50 hover:bg-muted/30 cursor-pointer transition-all"
              onClick={() => router.push(action.href)}
            >
              <div className={`p-2 rounded-full ${action.color}`}>
                {action.icon}
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm">{action.label}</div>
                <div className="text-xs text-muted-foreground">{action.description}</div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
