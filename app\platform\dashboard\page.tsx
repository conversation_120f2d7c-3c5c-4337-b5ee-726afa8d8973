"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/components/ui/use-toast"
import { AlertCircle, CheckCircle, XCircle, Users, Store, Clock, FileCheck } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"

// 租户数据接口
interface TenantData {
  id?: number;
  companyName: string;
  username: string;
  email: string;
  phone: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
}

export default function PlatformDashboard() {
  const [pendingTenants, setPendingTenants] = useState<TenantData[]>([]);
  const [approvedTenants, setApprovedTenants] = useState<TenantData[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  const { user } = useAuth();

  // 获取租户列表
  useEffect(() => {
    const fetchTenants = async () => {
      setLoading(true);
      try {
        // 实际应用中应该从API获取数据
        // const response = await fetch('/api/platform/tenants');
        // const data = await response.json();
        
        // 模拟数据
        const registeredTenant = localStorage.getItem('registered_tenant');
        let registeredData: TenantData[] = [];
        
        if (registeredTenant) {
          const tenant = JSON.parse(registeredTenant);
          registeredData = [{
            id: 1,
            companyName: tenant.companyName,
            username: tenant.username,
            email: tenant.email,
            phone: tenant.phone,
            status: tenant.status,
            createdAt: tenant.createdAt
          }];
        }
        
        // 区分待审核和已审核的租户
        const pending = registeredData.filter(tenant => tenant.status === 'pending');
        const approved = registeredData.filter(tenant => tenant.status === 'approved');
        
        setPendingTenants(pending);
        setApprovedTenants(approved);
      } catch (error) {
        console.error('获取租户列表失败:', error);
        toast({
          title: "获取租户列表失败",
          description: "请稍后再试",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTenants();
  }, [toast]);

  // 处理租户审核
  const handleReviewTenant = async (tenantId: number, action: 'approve' | 'reject') => {
    try {
      // 实际应用中应该调用API
      // const response = await fetch(`/api/platform/tenants/${tenantId}/review`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ action })
      // });
      
      // 模拟API调用
      const registeredTenant = localStorage.getItem('registered_tenant');
      if (registeredTenant) {
        const tenant = JSON.parse(registeredTenant);
        tenant.status = action === 'approve' ? 'approved' : 'rejected';
        localStorage.setItem('registered_tenant', JSON.stringify(tenant));
        
        // 更新状态
        const updatedPending = pendingTenants.filter(t => t.id !== tenantId);
        setPendingTenants(updatedPending);
        
        if (action === 'approve') {
          const approvedTenant = pendingTenants.find(t => t.id === tenantId);
          if (approvedTenant) {
            approvedTenant.status = 'approved';
            setApprovedTenants([...approvedTenants, approvedTenant]);
          }
        }
        
        toast({
          title: action === 'approve' ? "租户审核通过" : "租户审核拒绝",
          description: `已${action === 'approve' ? '通过' : '拒绝'}租户 ${tenant.companyName} 的注册申请`,
        });
      }
    } catch (error) {
      console.error('租户审核失败:', error);
      toast({
        title: "租户审核失败",
        description: "请稍后再试",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="container max-w-6xl py-6 space-y-6">
      <div className="flex justify-between">
        <h1 className="text-2xl font-bold">平台管理仪表盘</h1>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">待审核租户</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingTenants.length}</div>
            <p className="text-xs text-muted-foreground">需要审核的新租户申请</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">活跃租户</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{approvedTenants.length}</div>
            <p className="text-xs text-muted-foreground">已审核通过的租户</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">系统状态</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Badge variant="default" className="bg-green-500">正常运行</Badge>
              <span className="ml-2 text-xs text-muted-foreground">所有服务正常</span>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>待审核租户</CardTitle>
          <CardDescription>
            审核新的租户注册申请
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-6">
              <p>加载中...</p>
            </div>
          ) : pendingTenants.length === 0 ? (
            <div className="text-center py-10">
              <div className="rounded-full bg-muted w-12 h-12 flex items-center justify-center mx-auto mb-4">
                <FileCheck className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">没有待审核的租户</h3>
              <p className="text-muted-foreground">当前没有需要审核的租户申请</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>公司名称</TableHead>
                  <TableHead>管理员用户名</TableHead>
                  <TableHead>联系方式</TableHead>
                  <TableHead>申请时间</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pendingTenants.map((tenant) => (
                  <TableRow key={tenant.id}>
                    <TableCell className="font-medium">{tenant.companyName}</TableCell>
                    <TableCell>{tenant.username}</TableCell>
                    <TableCell>
                      <div>{tenant.email}</div>
                      <div className="text-xs text-muted-foreground">{tenant.phone}</div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Clock className="mr-1 h-3 w-3 text-muted-foreground" />
                        {new Date(tenant.createdAt).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                        <Clock className="mr-1 h-3 w-3" /> 待审核
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mr-2 text-green-600 border-green-200 hover:bg-green-50"
                        onClick={() => handleReviewTenant(tenant.id || 1, 'approve')}
                      >
                        <CheckCircle className="mr-1 h-4 w-4" />
                        通过
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-red-600 border-red-200 hover:bg-red-50"
                        onClick={() => handleReviewTenant(tenant.id || 1, 'reject')}
                      >
                        <XCircle className="mr-1 h-4 w-4" />
                        拒绝
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>已审核租户</CardTitle>
          <CardDescription>
            已通过审核的租户列表
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-6">
              <p>加载中...</p>
            </div>
          ) : approvedTenants.length === 0 ? (
            <div className="text-center py-10">
              <div className="rounded-full bg-muted w-12 h-12 flex items-center justify-center mx-auto mb-4">
                <Users className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">没有已审核租户</h3>
              <p className="text-muted-foreground">当前系统中没有已审核通过的租户</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>公司名称</TableHead>
                  <TableHead>管理员用户名</TableHead>
                  <TableHead>联系方式</TableHead>
                  <TableHead>门店数量</TableHead>
                  <TableHead>审核时间</TableHead>
                  <TableHead>状态</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {approvedTenants.map((tenant) => (
                  <TableRow key={tenant.id}>
                    <TableCell className="font-medium">{tenant.companyName}</TableCell>
                    <TableCell>{tenant.username}</TableCell>
                    <TableCell>
                      <div>{tenant.email}</div>
                      <div className="text-xs text-muted-foreground">{tenant.phone}</div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Store className="mr-1 h-3 w-3 text-muted-foreground" />
                        {localStorage.getItem('hasStores') === 'true' ? 
                          (JSON.parse(localStorage.getItem('stores') || '[]').length) : 0}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Clock className="mr-1 h-3 w-3 text-muted-foreground" />
                        {new Date(tenant.createdAt).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="default" className="bg-green-500">
                        <CheckCircle className="mr-1 h-3 w-3" /> 已通过
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 