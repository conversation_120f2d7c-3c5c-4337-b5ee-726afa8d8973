"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"
import { Share2, Users, ArrowUpDown } from "lucide-react"

interface CrossStoreStatsCardProps {
  stats: {
    totalMembers: number
    totalCoaches: number
    totalRevenue: number
    crossStoreVisits: number
    sharedResources: number
    standardCompliance: number
  }
}

export function CrossStoreStatsCard({ stats }: CrossStoreStatsCardProps) {
  // 跨店访问数据
  const visitData = [
    { name: "朝阳店", visits: 45 },
    { name: "西城店", visits: 32 },
    { name: "海淀店", visits: 38 },
    { name: "通州店", visits: 13 },
  ]

  // 会员分布数据
  const memberDistribution = [
    { name: "朝阳店", value: 450, color: "#4285F4" },
    { name: "西城店", value: 320, color: "#34A853" },
    { name: "海淀店", value: 380, color: "#FBBC05" },
    { name: "通州店", value: 180, color: "#EA4335" },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Share2 className="h-5 w-5 text-blue-500" />
          跨店服务统计
        </CardTitle>
        <CardDescription>会员跨门店访问和服务使用情况</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h4 className="text-sm font-medium mb-2">跨店访问次数（本月）</h4>
            <ResponsiveContainer width="100%" height={150}>
              <BarChart data={visitData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="name" fontSize={12} tickMargin={5} />
                <YAxis fontSize={12} tickMargin={5} />
                <Tooltip 
                  formatter={(value) => [`${value} 次`, "访问次数"]}
                  labelFormatter={(label) => `${label}`}
                />
                <Bar dataKey="visits" fill="#4285F4" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <div>
            <h4 className="text-sm font-medium mb-2">会员分布</h4>
            <div className="flex items-center">
              <ResponsiveContainer width="60%" height={150}>
                <PieChart>
                  <Pie
                    data={memberDistribution}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={60}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {memberDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value) => [`${value} 人`, "会员数"]}
                    labelFormatter={(label) => `${label}`}
                  />
                </PieChart>
              </ResponsiveContainer>
              <div className="space-y-2">
                {memberDistribution.map((entry, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }}></div>
                    <span className="text-xs">{entry.name}: {entry.value}人</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-sm">跨店会员占比</span>
                <span className="text-sm font-medium">32%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: "32%" }}></div>
              </div>
            </div>
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span className="text-sm">跨店课程预约率</span>
                <span className="text-sm font-medium">18%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: "18%" }}></div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full">
          <ArrowUpDown className="mr-2 h-4 w-4" />
          查看详细数据
        </Button>
      </CardFooter>
    </Card>
  )
}
