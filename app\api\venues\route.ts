import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/venues - 获取场地列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const keyword = searchParams.get('keyword');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');

    console.log('场地API接收参数:', { tenantId, keyword, status, page, pageSize });

    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID',
        data: null
      }, { status: 400 });
    }

    // 构建查询条件
    const where: any = {
      tenant_id: parseInt(tenantId)
    };

    // 关键词搜索
    if (keyword) {
      where.OR = [
        { name: { contains: keyword } },
        { location: { contains: keyword } },
        { description: { contains: keyword } }
      ];
    }

    // 状态筛选
    if (status && status !== 'all') {
      where.status = status === 'active' ? 1 : 0;
    }

    // 查询总数
    const total = await prisma.venue.count({ where });

    // 查询场地列表
    const venues = await prisma.venue.findMany({
      where,
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      },
      orderBy: { created_at: 'desc' },
      skip: (page - 1) * pageSize,
      take: pageSize
    });

    // 格式化返回数据
    const formattedVenues = venues.map(venue => ({
      id: venue.id,
      name: venue.name,
      location: venue.location,
      capacity: venue.capacity,
      area: venue.area,
      equipment: venue.equipment ? JSON.parse(venue.equipment) : [],
      description: venue.description,
      hourlyRate: venue.hourly_rate,
      status: venue.status === 1 ? 'active' : 'inactive',
      courseCount: venue._count.courses,
      createdAt: venue.created_at?.toISOString().split('T')[0],
      updatedAt: venue.updated_at?.toISOString().split('T')[0]
    }));

    console.log(`场地查询结果: ${formattedVenues.length} 条记录，总计: ${total}`);

    return NextResponse.json({
      code: 200,
      msg: '获取场地列表成功',
      data: {
        list: formattedVenues,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取场地列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取场地列表失败',
      data: null
    }, { status: 500 });
  }
}

// POST /api/venues - 创建场地
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      tenantId,
      name,
      location,
      capacity,
      area,
      equipment,
      description,
      hourlyRate,
      status
    } = body;

    console.log('创建场地API接收数据:', body);

    // 验证必填字段
    if (!tenantId || !name) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }

    // 检查场地名称是否重复
    const existingVenue = await prisma.venue.findFirst({
      where: {
        tenant_id: parseInt(tenantId),
        name: name
      }
    });

    if (existingVenue) {
      return NextResponse.json({
        code: 400,
        msg: '场地名称已存在',
        data: null
      }, { status: 400 });
    }

    // 创建场地
    const venue = await prisma.venue.create({
      data: {
        tenant_id: parseInt(tenantId),
        name,
        location,
        capacity: capacity ? parseInt(capacity) : null,
        area: area ? parseFloat(area) : null,
        equipment: equipment ? JSON.stringify(equipment) : null,
        description,
        hourly_rate: hourlyRate ? parseFloat(hourlyRate) : null,
        status: status === 'active' ? 1 : 0
      }
    });

    console.log('场地创建成功:', venue.id);

    return NextResponse.json({
      code: 200,
      msg: '创建场地成功',
      data: {
        id: venue.id,
        name: venue.name,
        location: venue.location,
        capacity: venue.capacity,
        area: venue.area,
        equipment: venue.equipment ? JSON.parse(venue.equipment) : [],
        description: venue.description,
        hourlyRate: venue.hourly_rate,
        status: venue.status === 1 ? 'active' : 'inactive',
        createdAt: venue.created_at?.toISOString().split('T')[0]
      }
    });

  } catch (error) {
    console.error('创建场地失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '创建场地失败',
      data: null
    }, { status: 500 });
  }
}
