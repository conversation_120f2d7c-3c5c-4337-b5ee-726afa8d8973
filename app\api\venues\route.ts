import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// 定义场地接口
interface Venue {
  id: number;
  name: string;
  location?: string | null;
  capacity: number | null;
  description: string | null;
  status: number | null;
}

// GET /api/venues - 获取场地列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const keyword = searchParams.get('keyword');
    const status = searchParams.get('status');

    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID',
        data: null
      }, { status: 400 });
    }

    // 模拟场地数据
    const mockVenues: Venue[] = [
      {
        id: 1,
        name: '1号瑜伽室',
        location: '一楼东侧',
        capacity: 15,
        description: '适合小班课程',
        status: 1
      },
      {
        id: 2,
        name: '2号瑜伽室',
        location: '一楼西侧',
        capacity: 20,
        description: '适合中班课程',
        status: 1
      },
      {
        id: 3,
        name: '3号瑜伽室',
        location: '二楼',
        capacity: 30,
        description: '适合大班课程',
        status: 1
      },
      {
        id: 4,
        name: '私教室',
        location: '三楼',
        capacity: 2,
        description: '适合一对一教学',
        status: 1
      },
      {
        id: 5,
        name: '户外平台',
        location: '顶楼',
        capacity: 25,
        description: '适合户外课程',
        status: 1
      }
    ];

    // 根据关键词筛选
    let filteredVenues = [...mockVenues];
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      filteredVenues = filteredVenues.filter(
        venue => 
          venue.name.toLowerCase().includes(lowerKeyword) || 
          venue.location?.toLowerCase().includes(lowerKeyword) || 
          venue.description?.toLowerCase().includes(lowerKeyword)
      );
    }

    // 根据状态筛选
    if (status && status !== 'all') {
      const statusValue = status === 'active' ? 1 : 0;
      filteredVenues = filteredVenues.filter(venue => venue.status === statusValue);
    }

    // 格式化返回数据
    const formattedVenues = filteredVenues.map((venue: Venue) => ({
      id: venue.id.toString(),
      name: venue.name,
      location: venue.location || '',
      capacity: venue.capacity || 0,
      description: venue.description || '',
      status: venue.status === 1 ? 'active' : 'inactive'
    }));

    return NextResponse.json({
      code: 200,
      msg: '获取场地列表成功',
      data: {
        list: formattedVenues,
        total: formattedVenues.length
      }
    });

  } catch (error) {
    console.error('获取场地列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取场地列表失败',
      data: null
    }, { status: 500 });
  }
}

// POST /api/venues - 创建场地
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      tenantId,
      name,
      location,
      capacity,
      area,
      equipment,
      description,
      hourlyRate,
      status
    } = body;

    console.log('创建场地API接收数据:', body);

    // 验证必填字段
    if (!tenantId || !name) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }

    // 检查场地名称是否重复
    const existingVenue = await prisma.venue.findFirst({
      where: {
        tenant_id: parseInt(tenantId),
        name: name
      }
    });

    if (existingVenue) {
      return NextResponse.json({
        code: 400,
        msg: '场地名称已存在',
        data: null
      }, { status: 400 });
    }

    // 创建场地
    const venue = await prisma.venue.create({
      data: {
        tenant_id: parseInt(tenantId),
        name,
        location,
        capacity: capacity ? parseInt(capacity) : null,
        area: area ? parseFloat(area) : null,
        equipment: equipment ? JSON.stringify(equipment) : null,
        description,
        hourly_rate: hourlyRate ? parseFloat(hourlyRate) : null,
        status: status === 'active' ? 1 : 0
      }
    });

    console.log('场地创建成功:', venue.id);

    return NextResponse.json({
      code: 200,
      msg: '创建场地成功',
      data: {
        id: venue.id,
        name: venue.name,
        location: venue.location,
        capacity: venue.capacity,
        area: venue.area,
        equipment: venue.equipment ? JSON.parse(venue.equipment) : [],
        description: venue.description,
        hourlyRate: venue.hourly_rate,
        status: venue.status === 1 ? 'active' : 'inactive',
        createdAt: venue.created_at?.toISOString().split('T')[0]
      }
    });

  } catch (error) {
    console.error('创建场地失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '创建场地失败',
      data: null
    }, { status: 500 });
  }
}
