import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function PaymentSettingsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">支付设置</h1>
        <Button>保存设置</Button>
      </div>

      <Tabs defaultValue="payment-methods" className="space-y-4">
        <TabsList>
          <TabsTrigger value="payment-methods">支付方式</TabsTrigger>
          <TabsTrigger value="refund-policy">退款政策</TabsTrigger>
          <TabsTrigger value="invoice">发票设置</TabsTrigger>
        </TabsList>

        <TabsContent value="payment-methods" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付方式设置</CardTitle>
              <CardDescription>配置可用的支付方式</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="wechat-pay">微信支付</Label>
                    <p className="text-sm text-muted-foreground">启用微信支付功能</p>
                  </div>
                  <Switch id="wechat-pay" defaultChecked />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="wechat-merchant-id">商户号</Label>
                    <Input id="wechat-merchant-id" defaultValue="********90" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="wechat-api-key">API密钥</Label>
                    <Input id="wechat-api-key" type="password" defaultValue="••••••••••••••••" />
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="alipay">支付宝</Label>
                    <p className="text-sm text-muted-foreground">启用支付宝功能</p>
                  </div>
                  <Switch id="alipay" defaultChecked />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="alipay-partner-id">合作伙伴ID</Label>
                    <Input id="alipay-partner-id" defaultValue="2088********9012" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="alipay-private-key">私钥</Label>
                    <Input id="alipay-private-key" type="password" defaultValue="••••••••••••••••" />
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="bank-transfer">银行卡支付</Label>
                    <p className="text-sm text-muted-foreground">启用银行卡支付功能</p>
                  </div>
                  <Switch id="bank-transfer" defaultChecked />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="bank-merchant-id">商户号</Label>
                    <Input id="bank-merchant-id" defaultValue="*********" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank-terminal-id">终端ID</Label>
                    <Input id="bank-terminal-id" defaultValue="TERM12345" />
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="cash">现金支付</Label>
                    <p className="text-sm text-muted-foreground">启用现金支付功能（仅限线下）</p>
                  </div>
                  <Switch id="cash" defaultChecked />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="refund-policy" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>退款政策设置</CardTitle>
              <CardDescription>配置退款规则和限制</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="refund-period">退款期限（天）</Label>
                  <Input id="refund-period" type="number" defaultValue="7" />
                  <p className="text-sm text-muted-foreground">购买后多少天内可申请退款</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="course-refund-policy">课程退款政策</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="course-refund-before">开课前退款手续费</Label>
                      <div className="flex items-center">
                        <Input id="course-refund-before" type="number" defaultValue="10" />
                        <span className="ml-2">%</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="course-refund-after">开课后退款手续费</Label>
                      <div className="flex items-center">
                        <Input id="course-refund-after" type="number" defaultValue="30" />
                        <span className="ml-2">%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="membership-refund-policy">会员卡退款政策</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="membership-refund-unused">未使用退款手续费</Label>
                      <div className="flex items-center">
                        <Input id="membership-refund-unused" type="number" defaultValue="5" />
                        <span className="ml-2">%</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="membership-refund-used">已使用退款手续费</Label>
                      <div className="flex items-center">
                        <Input id="membership-refund-used" type="number" defaultValue="20" />
                        <span className="ml-2">%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="product-refund-policy">商品退款政策</Label>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="product-refund-unopened">未拆封退款手续费</Label>
                      <div className="flex items-center">
                        <Input id="product-refund-unopened" type="number" defaultValue="0" />
                        <span className="ml-2">%</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="product-refund-opened">已拆封退款手续费</Label>
                      <div className="flex items-center">
                        <Input id="product-refund-opened" type="number" defaultValue="15" />
                        <span className="ml-2">%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-approve">自动审批</Label>
                    <p className="text-sm text-muted-foreground">符合条件的退款请求自动审批通过</p>
                  </div>
                  <Switch id="auto-approve" defaultChecked />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invoice" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>发票设置</CardTitle>
              <CardDescription>配置发票信息和规则</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="enable-invoice">启用发票功能</Label>
                    <p className="text-sm text-muted-foreground">允许会员申请发票</p>
                  </div>
                  <Switch id="enable-invoice" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="invoice-title">发票抬头</Label>
                  <Input id="invoice-title" defaultValue="静心瑜伽馆" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tax-number">税号</Label>
                  <Input id="tax-number" defaultValue="91110105MA00B7GT2R" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="invoice-address">单位地址</Label>
                  <Input id="invoice-address" defaultValue="北京市朝阳区建国路88号" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="invoice-phone">电话号码</Label>
                  <Input id="invoice-phone" defaultValue="010-********" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bank-name">开户银行</Label>
                  <Input id="bank-name" defaultValue="中国工商银行北京朝阳支行" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bank-account">银行账号</Label>
                  <Input id="bank-account" defaultValue="6212 2612 3456 7890" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-invoice">自动开具发票</Label>
                    <p className="text-sm text-muted-foreground">订单完成后自动开具发票</p>
                  </div>
                  <Switch id="auto-invoice" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

