import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Pencil, AlertTriangle } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface VenueDetailDialogProps {
  venue: {
    id: string
    name: string
    capacity: number
    equipment: string
    courses: number
    utilization: string
    status: string
    area: number
    location: string
    openTime: string
    closeTime: string
    maintenanceDate: string
    image?: string
  }
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function VenueDetailDialog({ venue, open, onOpenChange }: VenueDetailDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>{venue.name} 详情</span>
            <Badge
              variant={
                venue.status === "available" ? "default" : venue.status === "maintenance" ? "destructive" : "secondary"
              }
            >
              {venue.status === "available" ? "可用" : venue.status === "maintenance" ? "维护中" : "已预订"}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="info">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="info">基本信息</TabsTrigger>
            <TabsTrigger value="schedule">排期情况</TabsTrigger>
            <TabsTrigger value="stats">使用统计</TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <img
                  src={venue.image || "/placeholder.svg?height=200&width=300"}
                  alt={venue.name}
                  className="w-full h-48 object-cover rounded-md"
                />
              </div>
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-sm font-medium">场地ID</p>
                    <p className="text-sm">{venue.id}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">位置</p>
                    <p className="text-sm">{venue.location}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">容纳人数</p>
                    <p className="text-sm">{venue.capacity}人</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">面积</p>
                    <p className="text-sm">{venue.area}㎡</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">开放时间</p>
                    <p className="text-sm">
                      {venue.openTime}-{venue.closeTime}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">使用率</p>
                    <p className="text-sm">{venue.utilization}</p>
                  </div>
                </div>
                {venue.status === "maintenance" && (
                  <div>
                    <p className="text-sm font-medium">维护截止日期</p>
                    <p className="text-sm">{venue.maintenanceDate}</p>
                  </div>
                )}
              </div>
            </div>

            <div>
              <p className="text-sm font-medium">设备配置</p>
              <p className="text-sm">{venue.equipment}</p>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" size="sm">
                <Calendar className="mr-2 h-4 w-4" />
                查看排期
              </Button>
              <Button variant="outline" size="sm">
                <Pencil className="mr-2 h-4 w-4" />
                编辑信息
              </Button>
              {venue.status === "available" ? (
                <Button variant="outline" size="sm">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  标记为维护中
                </Button>
              ) : venue.status === "maintenance" ? (
                <Button variant="outline" size="sm">
                  标记为可用
                </Button>
              ) : null}
            </div>
          </TabsContent>

          <TabsContent value="schedule">
            <Card>
              <CardHeader>
                <CardTitle>近期排期</CardTitle>
                <CardDescription>显示未来7天的课程安排</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-md p-3">
                    <p className="font-medium">2025-03-30 (今天)</p>
                    <div className="mt-2 space-y-2">
                      <div className="bg-muted p-2 rounded-md flex justify-between">
                        <div>
                          <p className="font-medium">基础瑜伽入门</p>
                          <p className="text-sm text-muted-foreground">10:00-11:30 | 张教练</p>
                        </div>
                        <Badge>已预订 12/15</Badge>
                      </div>
                      <div className="bg-muted p-2 rounded-md flex justify-between">
                        <div>
                          <p className="font-medium">高级瑜伽进阶</p>
                          <p className="text-sm text-muted-foreground">14:00-15:30 | 李教练</p>
                        </div>
                        <Badge>已预订 8/15</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="border rounded-md p-3">
                    <p className="font-medium">2025-03-31 (明天)</p>
                    <div className="mt-2 space-y-2">
                      <div className="bg-muted p-2 rounded-md flex justify-between">
                        <div>
                          <p className="font-medium">阴瑜伽放松</p>
                          <p className="text-sm text-muted-foreground">09:00-10:00 | 王教练</p>
                        </div>
                        <Badge>已预订 10/15</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats">
            <Card>
              <CardHeader>
                <CardTitle>使用统计</CardTitle>
                <CardDescription>过去30天的场地使用情况</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-muted p-4 rounded-md text-center">
                    <p className="text-sm text-muted-foreground">总课程数</p>
                    <p className="text-2xl font-bold">{venue.courses}</p>
                  </div>
                  <div className="bg-muted p-4 rounded-md text-center">
                    <p className="text-sm text-muted-foreground">平均每日课程</p>
                    <p className="text-2xl font-bold">{Math.round(venue.courses / 30)}</p>
                  </div>
                  <div className="bg-muted p-4 rounded-md text-center">
                    <p className="text-sm text-muted-foreground">使用率</p>
                    <p className="text-2xl font-bold">{venue.utilization}</p>
                  </div>
                </div>

                <div>
                  <p className="font-medium mb-2">每周使用情况</p>
                  <div className="h-40 flex items-end space-x-2">
                    {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, i) => {
                      const height = Math.floor(Math.random() * 60) + 20
                      return (
                        <div key={day} className="flex flex-col items-center">
                          <div className="bg-primary w-10 rounded-t-md" style={{ height: `${height}%` }}></div>
                          <span className="text-xs mt-1">{day}</span>
                        </div>
                      )
                    })}
                  </div>
                </div>

                <div>
                  <p className="font-medium mb-2">热门课程类型</p>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <span className="w-24">基础瑜伽</span>
                      <div className="flex-1 h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "65%" }}></div>
                      </div>
                      <span className="ml-2 text-sm">65%</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-24">高级瑜伽</span>
                      <div className="flex-1 h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "25%" }}></div>
                      </div>
                      <span className="ml-2 text-sm">25%</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-24">阴瑜伽</span>
                      <div className="flex-1 h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "10%" }}></div>
                      </div>
                      <span className="ml-2 text-sm">10%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

