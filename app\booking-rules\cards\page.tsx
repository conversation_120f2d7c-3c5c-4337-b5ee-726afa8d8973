"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Calendar, Save, Plus, Trash, Clock, ArrowLeft, Settings, FileText, CreditCard, Layers } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { useRouter } from "next/navigation"
import { CardBookingRules } from "@/components/booking-rules/card-booking-rules"

// 模拟会员卡类型数据
const cardTypes = [
  { id: "MT001", name: "瑜伽年卡", color: "#4CAF50", type: "期限卡" },
  { id: "MT002", name: "瑜伽季卡", color: "#2196F3", type: "期限卡" },
  { id: "MT003", name: "瑜伽月卡", color: "#FF9800", type: "期限卡" },
  { id: "MT004", name: "私教次卡", color: "#9C27B0", type: "次卡" },
  { id: "MT005", name: "体验卡", color: "#F44336", type: "次卡" },
  { id: "MT006", name: "储值卡", color: "#607D8B", type: "储值卡" },
]

export default function CardBookingRulesPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("global")
  const [selectedCardType, setSelectedCardType] = useState("")
  const [selectedCardCategory, setSelectedCardCategory] = useState("")

  // 根据选择的卡类别筛选卡类型
  const filteredCardTypes = selectedCardCategory && selectedCardCategory !== "all"
    ? cardTypes.filter(card => card.type === selectedCardCategory)
    : cardTypes

  // 保存设置
  const handleSave = (settings: any) => {
    console.log("保存会员卡预约规则设置:", settings)
    toast({
      title: "规则已保存",
      description: "会员卡预约规则已成功保存",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.push("/booking-rules")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">会员卡预约规则</h1>
            <p className="text-muted-foreground">
              设置会员卡相关的预约限制和优先权
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Layers className="h-5 w-5 mr-2 text-primary" />
            会员卡预约规则层级
          </CardTitle>
          <CardDescription>
            会员卡预约规则分为两个层级：全局规则和会员卡类型规则
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm">
              规则按照从具体到一般的顺序应用，更具体的规则会覆盖更一般的规则：
            </p>
            <ol className="list-decimal list-inside space-y-1 text-sm ml-4">
              <li>会员卡类型规则（最高优先级）</li>
              <li>全局会员卡规则（最低优先级）</li>
            </ol>
            <p className="text-sm">
              例如，如果一个会员卡类型有自己的预约规则，那么这个规则会覆盖全局规则。
            </p>
            <p className="text-sm mt-4">
              <strong>注意：</strong> 会员卡规则的优先级低于课程规则和会员规则。当规则冲突时，课程规则优先，其次是会员规则，最后是会员卡规则。
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>选择规则层级</CardTitle>
            <CardDescription>
              选择要设置的预约规则层级
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="global">全局</TabsTrigger>
                  <TabsTrigger value="card-type">卡类型</TabsTrigger>
                </TabsList>
              </Tabs>

              {activeTab === "card-type" && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="card-category-filter">卡类别筛选</Label>
                    <Select value={selectedCardCategory} onValueChange={(value) => {
                      setSelectedCardCategory(value)
                      setSelectedCardType("")
                    }}>
                      <SelectTrigger id="card-category-filter">
                        <SelectValue placeholder="选择卡类别（可选）" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部类别</SelectItem>
                        <SelectItem value="期限卡">期限卡</SelectItem>
                        <SelectItem value="次卡">次卡</SelectItem>
                        <SelectItem value="储值卡">储值卡</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="card-type-select">会员卡类型</Label>
                    <Select value={selectedCardType} onValueChange={setSelectedCardType}>
                      <SelectTrigger id="card-type-select">
                        <SelectValue placeholder="选择会员卡类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredCardTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            <div className="flex items-center">
                              <div
                                className="w-3 h-3 rounded-full mr-2"
                                style={{ backgroundColor: type.color }}
                              ></div>
                              {type.name} ({type.type})
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="md:col-span-3">
          {activeTab === "global" ? (
            <CardBookingRules
              level="global"
              onSave={handleSave}
            />
          ) : !selectedCardType ? (
            <Card>
              <CardContent className="flex items-center justify-center min-h-[400px]">
                <div className="text-center text-muted-foreground">
                  <p>请先选择一个会员卡类型</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <CardBookingRules
              level="cardType"
              cardTypeId={selectedCardType}
              onSave={handleSave}
            />
          )}
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => router.push("/booking-rules")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回规则管理
        </Button>

        <Button variant="outline" onClick={() => router.push("/members/cards")}>
          <CreditCard className="mr-2 h-4 w-4" />
          会员卡类型
        </Button>
      </div>
    </div>
  )
}
