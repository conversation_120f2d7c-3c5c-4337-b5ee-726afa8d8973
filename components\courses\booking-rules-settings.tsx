"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Info, Save } from "lucide-react"

interface BookingRulesSettingsProps {
  courseType?: "group" | "private" | "premium";
  level?: "global" | "cardType" | "course";
  cardTypeId?: string;
  courseId?: string;
  onSave?: (settings: any) => void;
}

export function BookingRulesSettings({ 
  courseType = "group", 
  level = "global",
  cardTypeId,
  courseId,
  onSave 
}: BookingRulesSettingsProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [settings, setSettings] = useState({
    // 会员卡约束设置
    cardConstraints: {
      expiredCardEnabled: true,
      bookingLimitEnabled: true,
      bookingLimitPerDay: 5,
      bookingLimitDays: 5,
      deductionMode: "limit",
      deductionValue: 1,
    },
    
    // 预约时间设置
    bookingTime: {
      bookingMode: "self-limited",
      advanceBookingDays: 7,
      advanceBookingTime: "anytime",
      bookingDeadline: "before-start",
      bookingDeadlineMinutes: 30,
    },
    
    // 排队候补设置
    waitlist: {
      waitlistEnabled: true,
      maxWaitlistCount: 10,
      stopWaitlistMinutes: 30,
      stopSubstituteMinutes: 30,
    },
    
    // 开课人数限制
    attendanceLimit: {
      limitEnabled: false,
      minAttendance: 0,
      autoCancelMinutes: 0,
    },
    
    // 签到设置
    checkIn: {
      selfCheckInEnabled: true,
      checkInTimeOption: "anytime",
      checkInBeforeEndMinutes: 0,
      qrCodeCheckInEnabled: true,
      faceCheckInEnabled: false,
      checkInTimeRangeStart: 10,
      checkInTimeRangeEnd: 15,
    },
    
    // 取消设置
    cancellation: {
      selfCancelEnabled: true,
      cancelBeforeStartMinutes: 35,
      cancelAfterStartMinutes: 0,
      cancelAfterEndMinutes: 0,
    },
    
    // 其他设置
    others: {
      attendeeDisplayMode: "count-total",
      showAttendees: true,
      showAttendancePercentage: true,
    }
  })
  
  // 处理设置变更
  const handleSettingChange = (category: string, setting: string, value: any) => {
    setSettings({
      ...settings,
      [category]: {
        ...settings[category as keyof typeof settings],
        [setting]: value
      }
    })
  }
  
  // 保存设置
  const handleSave = () => {
    console.log("保存预约规则设置:", settings)
    if (onSave) {
      onSave(settings)
    }
  }
  
  // 获取当前层级的标题
  const getLevelTitle = () => {
    switch (level) {
      case "global":
        return "全局预约规则设置";
      case "cardType":
        return "会员卡类型预约规则设置";
      case "course":
        return "课程预约规则设置";
      default:
        return "预约规则设置";
    }
  }
  
  // 获取当前层级的描述
  const getLevelDescription = () => {
    switch (level) {
      case "global":
        return "设置适用于所有会员卡和课程的全局预约规则";
      case "cardType":
        return "设置适用于特定会员卡类型的预约规则，将覆盖全局设置";
      case "course":
        return "设置适用于特定课程的预约规则，将覆盖全局设置和会员卡类型设置";
      default:
        return "设置课程预约规则";
    }
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{getLevelTitle()}</CardTitle>
        <CardDescription>
          {getLevelDescription()}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>预约规则层级说明</AlertTitle>
          <AlertDescription>
            预约规则分为三个层级：全局设置、会员卡类型设置和具体课程设置。下级设置会覆盖上级设置。
            当前正在编辑的是{level === "global" ? "全局" : level === "cardType" ? "会员卡类型" : "课程"}层级的设置。
          </AlertDescription>
        </Alert>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="basic">基本设置</TabsTrigger>
            <TabsTrigger value="booking">预约设置</TabsTrigger>
            <TabsTrigger value="checkin">签到设置</TabsTrigger>
            <TabsTrigger value="others">其他设置</TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic" className="space-y-6 pt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">会员卡约束设置</h3>
              <Separator />
              
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="expired-card"
                    checked={settings.cardConstraints.expiredCardEnabled}
                    onCheckedChange={(checked) => 
                      handleSettingChange("cardConstraints", "expiredCardEnabled", checked)
                    }
                  />
                  <Label htmlFor="expired-card">期限卡失效约束</Label>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="booking-limit"
                    checked={settings.cardConstraints.bookingLimitEnabled}
                    onCheckedChange={(checked) => 
                      handleSettingChange("cardConstraints", "bookingLimitEnabled", checked)
                    }
                  />
                  <Label htmlFor="booking-limit">限制预约</Label>
                </div>
                
                {settings.cardConstraints.bookingLimitEnabled && (
                  <div className="grid grid-cols-2 gap-4 pl-6 pt-2">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="booking-limit-per-day">每天最多预约</Label>
                      <Input
                        id="booking-limit-per-day"
                        type="number"
                        className="w-20"
                        value={settings.cardConstraints.bookingLimitPerDay}
                        onChange={(e) => 
                          handleSettingChange("cardConstraints", "bookingLimitPerDay", parseInt(e.target.value) || 0)
                        }
                      />
                      <span>次课程</span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="booking-limit-days">未来</Label>
                      <Input
                        id="booking-limit-days"
                        type="number"
                        className="w-20"
                        value={settings.cardConstraints.bookingLimitDays}
                        onChange={(e) => 
                          handleSettingChange("cardConstraints", "bookingLimitDays", parseInt(e.target.value) || 0)
                        }
                      />
                      <span>天内不能自主预约</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="booking" className="space-y-6 pt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">预约时间设置</h3>
              <Separator />
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>预约方式</Label>
                  <RadioGroup 
                    value={settings.bookingTime.bookingMode}
                    onValueChange={(value) => 
                      handleSettingChange("bookingTime", "bookingMode", value)
                    }
                    className="space-y-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="no-self" id="no-self-booking" />
                      <Label htmlFor="no-self-booking">不支持会员自主预约</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="self-unlimited" id="self-unlimited" />
                      <Label htmlFor="self-unlimited">自主预约，不限制预约时间</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="self-limited" id="self-limited" />
                      <Label htmlFor="self-limited">自主预约，限制预约时间</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="checkin" className="space-y-6 pt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">签到设置</h3>
              <Separator />
              
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="self-checkin"
                    checked={settings.checkIn.selfCheckInEnabled}
                    onCheckedChange={(checked) => 
                      handleSettingChange("checkIn", "selfCheckInEnabled", checked)
                    }
                  />
                  <Label htmlFor="self-checkin">自主签到</Label>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="others" className="space-y-6 pt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">其他设置</h3>
              <Separator />
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>约课人数显示</Label>
                  <RadioGroup 
                    value={settings.others.attendeeDisplayMode}
                    onValueChange={(value) => 
                      handleSettingChange("others", "attendeeDisplayMode", value)
                    }
                    className="space-y-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="available" id="display-available" />
                      <Label htmlFor="display-available">微信端"约课"页面的约课人数显示"还可预约几人"</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="count-total" id="display-count-total" />
                      <Label htmlFor="display-count-total">微信端"约课"页面的约课人数显示"已约人数/容纳人数"</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-end">
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            保存设置
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
