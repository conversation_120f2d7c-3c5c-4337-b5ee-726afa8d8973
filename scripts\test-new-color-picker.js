// 测试新的颜色选择器功能
async function testNewColorPicker() {
  console.log('开始测试新的颜色选择器功能...');
  
  try {
    // 1. 测试预设颜色
    console.log('\n测试预设颜色:');
    const presetColors = [
      { name: '预设蓝色', color: '#4285F4' },
      { name: '预设绿色', color: '#34A853' },
      { name: '预设黄色', color: '#FBBC05' },
      { name: '预设红色', color: '#EA4335' },
      { name: '预设紫色', color: '#9C27B0' },
      { name: '预设粉色', color: '#FF6D91' }
    ];
    
    for (const testType of presetColors) {
      const response = await fetch('http://localhost:3005/api/course-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId: 1,
          name: testType.name,
          description: `使用预设颜色${testType.color}的课程类型`,
          color: testType.color,
          displayOrder: Math.floor(Math.random() * 100),
          status: 'active'
        })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        console.log(`✓ 预设颜色创建成功: ${testType.name} (${testType.color})`);
      } else {
        console.log(`✗ 预设颜色创建失败: ${testType.name} - ${result.msg}`);
      }
    }
    
    // 2. 测试自定义颜色
    console.log('\n测试自定义颜色:');
    const customColors = [
      { name: '自定义深紫', color: '#4a148c' },
      { name: '自定义深橙', color: '#e65100' },
      { name: '自定义深青', color: '#006064' },
      { name: '自定义深粉', color: '#ad1457' }
    ];
    
    for (const customType of customColors) {
      const response = await fetch('http://localhost:3005/api/course-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId: 1,
          name: customType.name,
          description: `使用自定义颜色${customType.color}的课程类型`,
          color: customType.color,
          displayOrder: Math.floor(Math.random() * 100),
          status: 'active'
        })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        console.log(`✓ 自定义颜色创建成功: ${customType.name} (${customType.color})`);
      } else {
        console.log(`✗ 自定义颜色创建失败: ${customType.name} - ${result.msg}`);
      }
    }
    
    // 3. 验证颜色显示
    console.log('\n验证所有课程类型的颜色:');
    const response = await fetch('http://localhost:3005/api/course-types?tenantId=1&sortBy=name');
    const result = await response.json();
    
    if (result.code === 200) {
      console.log(`总共有 ${result.data.list.length} 个课程类型:`);
      result.data.list.forEach((type, index) => {
        console.log(`  ${index + 1}. ${type.name}: ${type.color}`);
      });
    }
    
    console.log('\n✓ 新颜色选择器功能测试完成!');
    console.log('请在浏览器中访问课程类型页面，点击"添加课程类型"按钮测试颜色选择器界面。');
    console.log('新的颜色选择器特性:');
    console.log('  - 预设颜色圆点，6列网格布局');
    console.log('  - 选中状态有蓝色光环效果');
    console.log('  - 自定义颜色输入框和HTML5颜色选择器');
    console.log('  - 实时颜色预览');
    console.log('  - 优化的对话框布局');
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testNewColorPicker();
