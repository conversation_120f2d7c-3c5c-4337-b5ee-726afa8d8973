import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

const courses = [
  {
    id: "YG001",
    name: "基础瑜伽入门",
    coach: {
      name: "张教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    time: "2025-03-28 10:00-11:30",
    venue: "1号瑜伽室",
    bookings: 12,
    capacity: 15,
    status: "upcoming",
  },
  {
    id: "YG002",
    name: "高级瑜伽进阶",
    coach: {
      name: "李教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    time: "2025-03-28 14:00-15:30",
    venue: "2号瑜伽室",
    bookings: 8,
    capacity: 10,
    status: "upcoming",
  },
  {
    id: "YG003",
    name: "阴瑜伽放松",
    coach: {
      name: "王教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    time: "2025-03-28 16:00-17:00",
    venue: "3号瑜伽室",
    bookings: 15,
    capacity: 15,
    status: "full",
  },
  {
    id: "YG004",
    name: "孕产瑜伽特训",
    coach: {
      name: "赵教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    time: "2025-03-28 18:30-20:00",
    venue: "1号瑜伽室",
    bookings: 6,
    capacity: 8,
    status: "upcoming",
  },
  {
    id: "YG005",
    name: "空中瑜伽体验",
    coach: {
      name: "刘教练",
      avatar: "/placeholder.svg?height=32&width=32",
    },
    time: "2025-03-29 09:00-10:30",
    venue: "4号瑜伽室",
    bookings: 5,
    capacity: 8,
    status: "upcoming",
  },
]

export function RecentCourses() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>最近课程安排</CardTitle>
        <CardDescription>近期课程预约情况</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>课程编号</TableHead>
              <TableHead>课程名称</TableHead>
              <TableHead>教练</TableHead>
              <TableHead>时间</TableHead>
              <TableHead>场地</TableHead>
              <TableHead>预约/容量</TableHead>
              <TableHead>状态</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {courses.map((course) => (
              <TableRow key={course.id}>
                <TableCell className="font-medium">{course.id}</TableCell>
                <TableCell>{course.name}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={course.coach.avatar} alt={course.coach.name} />
                      <AvatarFallback>{course.coach.name[0]}</AvatarFallback>
                    </Avatar>
                    <span>{course.coach.name}</span>
                  </div>
                </TableCell>
                <TableCell>{course.time}</TableCell>
                <TableCell>{course.venue}</TableCell>
                <TableCell>
                  {course.bookings}/{course.capacity}
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      course.status === "upcoming" ? "outline" : course.status === "full" ? "secondary" : "default"
                    }
                  >
                    {course.status === "upcoming" ? "即将开始" : course.status === "full" ? "已满员" : "进行中"}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

