"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import {
  Calendar,
  CreditCard,
  History,
  MessageSquare,
  Pencil,
  Tag,
  User,
  Clock,
  DollarSign,
  Award,
  MapPin,
  Phone,
  Mail,
  Cake,
} from "lucide-react"
import { EditMemberDialog } from "./edit-member-dialog"
import { ManageTagsDialog } from "./manage-tags-dialog"
import { SendMessageDialog } from "./send-message-dialog"

interface MemberDetailDialogProps {
  member: any
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function MemberDetailDialog({ member, open, onOpenChange }: MemberDetailDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("profile")
  const [editDialogOpen, setEditDialogOpen] = useState(false)

  const [tagsDialogOpen, setTagsDialogOpen] = useState(false)
  const [messageDialogOpen, setMessageDialogOpen] = useState(false)


  const handleSaveMember = (updatedMember: any) => {
    toast({
      title: "会员信息已更新",
      description: `${updatedMember.name} 的信息已成功保存`,
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>会员详情</DialogTitle>
        </DialogHeader>

        <div className="flex flex-col items-center justify-center py-4 md:flex-row md:items-start md:justify-start md:gap-6">
          <Avatar className="h-24 w-24">
            <AvatarImage src={member.avatar} alt={member.name} />
            <AvatarFallback className="text-2xl">{member.name[0]}</AvatarFallback>
          </Avatar>

          <div className="mt-4 flex flex-1 flex-col text-center md:mt-0 md:text-left">
            <div className="flex flex-col items-center gap-2 md:flex-row">
              <h2 className="text-2xl font-bold">{member.name}</h2>
              <Badge variant={member.status === "active" ? "default" : "secondary"} className="ml-0 md:ml-2">
                {member.status === "active" ? "活跃" : "不活跃"}
              </Badge>
            </div>

            <p className="text-muted-foreground">{member.phone}</p>

            <div className="mt-2 flex flex-wrap justify-center gap-1 md:justify-start">
              {member.tags.map((tag: string) => (
                <Badge key={tag} variant="outline">
                  {tag}
                </Badge>
              ))}
            </div>

            <div className="mt-4 flex flex-wrap justify-center gap-2 md:justify-start">
              <Button size="sm" onClick={() => setEditDialogOpen(true)}>
                <Pencil className="mr-2 h-4 w-4" />
                编辑信息
              </Button>
              <Button size="sm" variant="outline" onClick={() => window.location.href = `/members/cards/list?memberId=${member.id}`}>
                <CreditCard className="mr-2 h-4 w-4" />
                查看会员卡
              </Button>
              <Button size="sm" variant="outline" onClick={() => setTagsDialogOpen(true)}>
                <Tag className="mr-2 h-4 w-4" />
                标签管理
              </Button>
              <Button size="sm" variant="outline" onClick={() => setMessageDialogOpen(true)}>
                <MessageSquare className="mr-2 h-4 w-4" />
                发送消息
              </Button>
            </div>
          </div>
        </div>

        <Separator />

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile">
              <User className="mr-2 h-4 w-4" />
              个人资料
            </TabsTrigger>
            <TabsTrigger value="membership">
              <CreditCard className="mr-2 h-4 w-4" />
              会员卡信息
            </TabsTrigger>
            <TabsTrigger value="bookings">
              <Calendar className="mr-2 h-4 w-4" />
              预约记录
            </TabsTrigger>
            <TabsTrigger value="transactions">
              <History className="mr-2 h-4 w-4" />
              消费记录
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">会员ID</span>
                    </div>
                    <span>{member.id}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">手机号</span>
                    </div>
                    <span>{member.phone}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">邮箱</span>
                    </div>
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Cake className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">生日</span>
                    </div>
                    <span>1990-01-01</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">地址</span>
                    </div>
                    <span>北京市朝阳区</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>会员统计</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">注册日期</span>
                    </div>
                    <span>{member.joinDate}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">最近到访</span>
                    </div>
                    <span>{member.lastVisit}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">累计消费</span>
                    </div>
                    <span>{member.totalSpent}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Award className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">会员等级</span>
                    </div>
                    <span>{member.level}</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>健康信息</CardTitle>
                <CardDescription>会员的健康状况和特殊需求</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <h4 className="font-medium">健康状况</h4>
                    <p className="text-sm text-muted-foreground">无特殊健康问题</p>
                  </div>
                  <div>
                    <h4 className="font-medium">运动偏好</h4>
                    <p className="text-sm text-muted-foreground">瑜伽、普拉提</p>
                  </div>
                  <div>
                    <h4 className="font-medium">特殊需求</h4>
                    <p className="text-sm text-muted-foreground">无</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="membership" className="mt-4 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>会员卡信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex flex-col items-center justify-center p-6 text-center">
                    <CreditCard className="h-12 w-12 text-primary mb-4" />
                    <h3 className="text-xl font-semibold mb-2">会员卡管理已移至专门页面</h3>
                    <p className="text-muted-foreground mb-6">
                      为了提供更好的会员卡管理体验，我们已将会员卡功能移至专门的会员卡列表页面。
                      您可以在那里查看和管理该会员的所有会员卡信息。
                    </p>
                    <Button onClick={() => window.location.href = `/members/cards/list?memberId=${member.id}`}>
                      <CreditCard className="mr-2 h-4 w-4" />
                      前往会员卡列表
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="bookings" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>预约记录</CardTitle>
                <CardDescription>会员的课程预约历史</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="rounded-lg border p-4">
                    <div className="flex flex-col justify-between gap-2 sm:flex-row">
                      <div>
                        <h4 className="font-semibold">高级瑜伽课</h4>
                        <p className="text-sm text-muted-foreground">2023-03-25 10:00-11:30</p>
                      </div>
                      <Badge>已完成</Badge>
                    </div>
                    <div className="mt-2 text-sm">
                      <span className="text-muted-foreground">教练:</span> 王教练
                      <span className="ml-4 text-muted-foreground">场地:</span> 瑜伽室A
                    </div>
                  </div>

                  <div className="rounded-lg border p-4">
                    <div className="flex flex-col justify-between gap-2 sm:flex-row">
                      <div>
                        <h4 className="font-semibold">普拉提入门</h4>
                        <p className="text-sm text-muted-foreground">2023-03-20 15:00-16:00</p>
                      </div>
                      <Badge>已完成</Badge>
                    </div>
                    <div className="mt-2 text-sm">
                      <span className="text-muted-foreground">教练:</span> 李教练
                      <span className="ml-4 text-muted-foreground">场地:</span> 多功能厅
                    </div>
                  </div>

                  <div className="rounded-lg border p-4">
                    <div className="flex flex-col justify-between gap-2 sm:flex-row">
                      <div>
                        <h4 className="font-semibold">冥想课</h4>
                        <p className="text-sm text-muted-foreground">2023-03-15 18:30-19:30</p>
                      </div>
                      <Badge>已完成</Badge>
                    </div>
                    <div className="mt-2 text-sm">
                      <span className="text-muted-foreground">教练:</span> 张教练
                      <span className="ml-4 text-muted-foreground">场地:</span> 冥想室
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transactions" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>消费记录</CardTitle>
                <CardDescription>会员的消费和支付历史</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="rounded-lg border p-4">
                    <div className="flex flex-col justify-between gap-2 sm:flex-row">
                      <div>
                        <h4 className="font-semibold">会员卡续费 - 次卡20次</h4>
                        <p className="text-sm text-muted-foreground">2023-03-01 14:25</p>
                      </div>
                      <div className="text-right font-medium">¥1,600</div>
                    </div>
                    <div className="mt-2 flex justify-between text-sm">
                      <span className="text-muted-foreground">支付方式: 微信支付</span>
                      <Badge variant="outline">已完成</Badge>
                    </div>
                  </div>

                  <div className="rounded-lg border p-4">
                    <div className="flex flex-col justify-between gap-2 sm:flex-row">
                      <div>
                        <h4 className="font-semibold">私教课程 - 5次</h4>
                        <p className="text-sm text-muted-foreground">2023-02-15 10:10</p>
                      </div>
                      <div className="text-right font-medium">¥2,000</div>
                    </div>
                    <div className="mt-2 flex justify-between text-sm">
                      <span className="text-muted-foreground">支付方式: 支付宝</span>
                      <Badge variant="outline">已完成</Badge>
                    </div>
                  </div>

                  <div className="rounded-lg border p-4">
                    <div className="flex flex-col justify-between gap-2 sm:flex-row">
                      <div>
                        <h4 className="font-semibold">瑜伽垫</h4>
                        <p className="text-sm text-muted-foreground">2023-01-20 16:45</p>
                      </div>
                      <div className="text-right font-medium">¥280</div>
                    </div>
                    <div className="mt-2 flex justify-between text-sm">
                      <span className="text-muted-foreground">支付方式: 微信支付</span>
                      <Badge variant="outline">已完成</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 编辑会员信息对话框 */}
        <EditMemberDialog
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          member={member}
          onSave={handleSaveMember}
        />

        {/* 标签管理对话框 */}
        <ManageTagsDialog
          open={tagsDialogOpen}
          onOpenChange={setTagsDialogOpen}
          member={member}
        />

        {/* 发送消息对话框 */}
        <SendMessageDialog
          open={messageDialogOpen}
          onOpenChange={setMessageDialogOpen}
          member={member}
        />


      </DialogContent>
    </Dialog>
  )
}

