"use client"

import { createContext, useContext, useEffect, ReactNode, useState } from "react"
import { useRouter, usePathname } from "next/navigation"

// 用户信息接口
export interface UserInfo {
  id: number | string
  username: string
  nickname?: string
  avatar?: string
  email?: string
  phone?: string
  role: string
  status: string
  tenant_id: number
  permissions?: string[]
}

// 认证上下文接口
interface AuthContextType {
  user: UserInfo | null
  token: string | null
  isAuthenticated: boolean
  login: (token: string, user: UserInfo, rememberMe?: boolean, tokenType?: string) => void
  logout: () => void
  updateUser: (user: Partial<UserInfo>) => void
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 认证提供者组件属性
interface AuthProviderProps {
  children: ReactNode
}

// 认证提供者组件
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<UserInfo | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false)
  const [loading, setLoading] = useState<boolean>(true)
  const router = useRouter()
  const pathname = usePathname()

  // 从本地存储初始化用户状态
  useEffect(() => {
    // 尝试从localStorage和sessionStorage获取用户信息
    const storedToken = localStorage.getItem('token') || sessionStorage.getItem('token')
    const storedUserInfo = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo')

    console.log('初始化认证状态:', { hasToken: !!storedToken, hasUserInfo: !!storedUserInfo })

    if (storedToken && storedUserInfo) {
      try {
        const parsedUser = JSON.parse(storedUserInfo)
        setUser(parsedUser)
        setToken(storedToken)
        setIsAuthenticated(true)
        console.log('已从存储中恢复用户状态:', { username: parsedUser.username, role: parsedUser.role })
      } catch (error) {
        // 解析错误，清除存储
        console.error('Failed to parse user info', error)
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        sessionStorage.removeItem('token')
        sessionStorage.removeItem('userInfo')
      }
    }
    
    setLoading(false)
  }, [])

  // 登录方法
  const login = (token: string, user: UserInfo, rememberMe: boolean = false, tokenType: string = 'Bearer') => {
    console.log('登录状态更新:', { 
      token: token.substring(0, 10) + '...', 
      user, 
      rememberMe 
    })
    
    const storage = rememberMe ? localStorage : sessionStorage
    
    try {
      // 清除可能存在的旧值
      localStorage.removeItem('token');
      localStorage.removeItem('token_type');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('tenant_id');
      sessionStorage.removeItem('token');
      sessionStorage.removeItem('token_type');
      sessionStorage.removeItem('userInfo');
      sessionStorage.removeItem('tenant_id');
      
      // 存储token和用户信息
      storage.setItem('token', token)
      // 不存储token_type，让axios拦截器直接使用'bearer'
      storage.setItem('userInfo', JSON.stringify(user))
      storage.setItem('tenant_id', user.tenant_id?.toString() || '1')
      
      // 更新状态
      setToken(token)
      setUser(user)
      setIsAuthenticated(true)
      
      console.log('登录状态已更新:', { 
        isAuthenticated: true,
        tokenSaved: true,
        tokenLength: token.length,
        storage: rememberMe ? 'localStorage' : 'sessionStorage'
      })
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  }

  // 注销方法
  const logout = () => {
    // 清除存储
    localStorage.removeItem('token')
    localStorage.removeItem('token_type')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('tenant_id')
    sessionStorage.removeItem('token')
    sessionStorage.removeItem('token_type')
    sessionStorage.removeItem('userInfo')
    sessionStorage.removeItem('tenant_id')
    
    // 更新状态
    setToken(null)
    setUser(null)
    setIsAuthenticated(false)
    
    // 重定向到登录页
    router.push('/login')
  }

  // 更新用户信息
  const updateUser = (userData: Partial<UserInfo>) => {
    if (user) {
      const updatedUser = { ...user, ...userData }
      
      // 更新本地存储
      const storage = localStorage.getItem('token') ? localStorage : sessionStorage
      storage.setItem('userInfo', JSON.stringify(updatedUser))
      
      // 更新状态
      setUser(updatedUser)
    }
  }
  
  // 路由权限控制
  useEffect(() => {
    // 如果页面已加载且用户未认证，则在访问需要认证的页面时重定向到登录页
    if (!loading) {
      console.log('权限路由检查:', { 
        isAuthenticated, 
        pathname, 
        username: user?.username,
        userRole: user?.role
      })
      
      // 公开路径，不需要登录即可访问
      const publicPaths = ['/', '/login', '/register', '/forgot-password', '/403', '/home']
      
      // 临时禁用路由重定向，允许访问所有页面
      /* 
      // 如果用户未登录且访问需要授权的页面，重定向到登录页
      if (!isAuthenticated && 
          !publicPaths.includes(pathname) && 
          !pathname.startsWith('/_next') && 
          !pathname.startsWith('/api')) {
        console.log('未认证，重定向到登录页')
        router.push('/login')
      } 
      // 如果用户已登录且在登录页面，重定向到仪表盘
      else if (isAuthenticated && pathname === '/login') {
        console.log('已认证，从登录页重定向到仪表盘')
        router.push('/dashboard')
      }
      */
      
      // 临时自动登录，使所有页面可访问
      if (!isAuthenticated && 
          !publicPaths.includes(pathname) && 
          !pathname.startsWith('/_next') && 
          !pathname.startsWith('/api')) {
        console.log('自动登录以访问页面:', pathname)
        login("temp-token-for-testing", {
          id: 1,
          username: "admin",
          nickname: "管理员",
          role: "admin",
          status: "active",
          tenant_id: 1
        }, true);
      }
    }
  }, [isAuthenticated, loading, pathname, router, user])

  // 认证上下文值
  const value = {
    user,
    token,
    isAuthenticated,
    login,
    logout,
    updateUser,
  }

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  )
}

// 认证上下文钩子
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 