"use client"

import { useState } from "react"
import Image from "next/image"
import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Icon } from "./icons"

export function MiniProgramHomePage() {
  // 模拟数据
  const [bannerNotice, setBannerNotice] = useState("场馆公告：2025年国庆放假通知请点击！！！")

  const features = [
    { id: 1, name: "场馆介绍", icon: "venue-intro", color: "#4EADFF" },
    { id: 2, name: "场馆教练", icon: "trainer", color: "#FF9966" },
    { id: 3, name: "每日打卡", icon: "checkin", color: "#5B8DEF" },
    { id: 4, name: "在线售卡", icon: "membership", color: "#FF9966" },
    { id: 5, name: "在线课程", icon: "course", color: "#4EADFF" },
  ]

  const groupClasses = [
    {
      id: 1,
      title: "瑜伽课训练营",
      location: "文澜大教室",
      trainer: {
        name: "<PERSON>",
        avatar: "/avatars/trainer1.jpg"
      },
      date: "10/15",
      availableSpots: 10,
      bookedSpots: 10,
      rating: 5,
      startTime: "14:30",
      endTime: "15:30"
    },
    {
      id: 2,
      title: "瑜伽课训练营",
      location: "文澜大教室",
      trainer: {
        name: "Sarah",
        avatar: "/avatars/trainer2.jpg"
      },
      date: "10/15",
      availableSpots: 10,
      bookedSpots: 10,
      rating: 5,
      startTime: "14:30",
      endTime: "15:30"
    },
    {
      id: 3,
      title: "瑜伽课训练营",
      location: "文澜大教室",
      trainer: {
        name: "Mike",
        avatar: "/avatars/trainer3.jpg"
      },
      date: "10/15",
      availableSpots: 10,
      bookedSpots: 10,
      rating: 5,
      startTime: "14:30",
      endTime: "15:30"
    }
  ]

  const privateClasses = [
    { id: 1, title: "私教课程1", trainer: "教练A", image: "/classes/private1.jpg" },
    { id: 2, title: "私教课程2", trainer: "教练B", image: "/classes/private2.jpg" },
    { id: 3, title: "私教课程3", trainer: "教练C", image: "/classes/private3.jpg" },
    { id: 4, title: "私教课程4", trainer: "教练D", image: "/classes/private4.jpg" }
  ]

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* 状态栏 - 模拟手机状态栏 */}
      <div className="bg-white px-4 py-2 flex justify-between items-center text-xs text-gray-700">
        <span>12:00</span>
        <div className="flex items-center gap-1">
          <span>●●●</span>
          <span>📶</span>
          <span>🔋</span>
        </div>
      </div>

      {/* 头部横幅 */}
      <div className="relative w-full h-48">
        <div className="absolute inset-0 bg-black/20 z-10 flex items-center justify-center">
          <div className="text-white text-center">
            <h1 className="text-xl font-bold">静心瑜伽美学生活馆 ✨</h1>
          </div>
        </div>
        <div className="absolute top-4 right-4 z-20 flex gap-2">
          <div className="w-8 h-8 bg-white/80 rounded-full flex items-center justify-center">
            <Icon name="menu" size={16} />
          </div>
          <div className="w-8 h-8 bg-white/80 rounded-full flex items-center justify-center">
            <Icon name="plus" size={16} />
          </div>
        </div>
        <div className="w-full h-full overflow-hidden">
          {/* 使用背景图片样式，避免图片加载问题 */}
          <div
            className="w-full h-full bg-cover bg-center"
            style={{
              backgroundImage: `url('https://images.unsplash.com/photo-1545205597-3d9d02c29597?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80')`
            }}
          ></div>
        </div>
      </div>

      {/* 功能导航 */}
      <div className="bg-white px-4 py-5 flex justify-between">
        {features.map(feature => (
          <div key={feature.id} className="flex flex-col items-center gap-1">
            <div
              className="w-12 h-12 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: `${feature.color}20` }}
            >
              <div
                className="w-8 h-8 rounded-md flex items-center justify-center text-white"
                style={{ backgroundColor: feature.color }}
              >
                <Icon name={feature.icon} size={18} color="white" />
              </div>
            </div>
            <span className="text-xs">{feature.name}</span>
          </div>
        ))}
      </div>

      {/* 活动横幅 */}
      <div className="mx-4 my-3 rounded-xl overflow-hidden bg-indigo-600 text-white relative h-24">
        <div className="absolute inset-0 flex items-center px-6">
          <div className="flex-1">
            <div className="text-lg font-bold">
              🎵 喵出你的2022
            </div>
            <div className="text-lg font-bold">
              收藏的第一首歌 ~
            </div>
            <div className="mt-1 text-xs bg-white/20 inline-block px-2 py-0.5 rounded-full">
              参与赢取全年免费音乐会员
            </div>
          </div>
          <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center">
                <span className="text-xl">▶</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 公告栏 */}
      <div className="mx-4 flex items-center bg-white rounded-md px-3 py-2 text-sm">
        <span className="text-orange-500 mr-2">📢</span>
        <div className="flex-1 truncate">{bannerNotice}</div>
        <span className="text-gray-400 ml-2">查看 &gt;</span>
      </div>

      {/* 团课推荐 */}
      <div className="mt-4 px-4">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold">团课推荐</h2>
          <span className="text-sm text-gray-500">更多 &gt;</span>
        </div>

        <div className="space-y-3">
          {groupClasses.map(course => (
            <div key={course.id} className="bg-white rounded-lg p-3 flex items-center">
              <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-200 mr-3">
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  {course.trainer.name[0]}
                </div>
              </div>
              <div className="flex-1">
                <div className="text-xs text-gray-500">
                  可预约数 {course.availableSpots}/{course.availableSpots + course.bookedSpots} | 还可预约{course.availableSpots}人
                </div>
                <div className="font-medium">{course.title} ({course.location})</div>
                <div className="flex items-center text-xs text-yellow-500">
                  难度系数：{'★'.repeat(course.rating)}
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold">{course.startTime}</div>
                <div className="text-xs text-gray-500">{course.endTime}结束</div>
                <button className="mt-1 bg-orange-500 text-white text-xs px-3 py-1 rounded-full">
                  立即预约
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 私教推荐 */}
      <div className="mt-4 px-4 pb-20">
        <div className="flex justify-between items-center mb-3">
          <h2 className="text-lg font-bold">私教推荐</h2>
          <span className="text-sm text-gray-500">更多 &gt;</span>
        </div>

        <div className="grid grid-cols-4 gap-2">
          {privateClasses.map(course => (
            <div key={course.id} className="bg-white rounded-lg overflow-hidden">
              <div className="h-20 bg-gray-200 flex items-center justify-center text-gray-400">
                图片
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 底部导航 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t flex justify-around py-2">
        <div className="flex flex-col items-center text-orange-500">
          <Icon name="venue-intro" size={20} color="currentColor" />
          <span className="text-xs">首页</span>
        </div>
        <div className="flex flex-col items-center text-gray-400">
          <Icon name="calendar" size={20} color="currentColor" />
          <span className="text-xs">约课</span>
        </div>
        <div className="flex flex-col items-center text-gray-400">
          <Icon name="user" size={20} color="currentColor" />
          <span className="text-xs">我的</span>
        </div>
      </div>
    </div>
  )
}
