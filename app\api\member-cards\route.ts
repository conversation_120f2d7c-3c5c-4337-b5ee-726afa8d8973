import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 会员卡数据接口
interface MemberCardData {
  name: string;
  description?: string;
  price: number;
  original_price?: number;
  validity_days: number;
  usage_limit: string;
  card_type: string;
  status?: string;
}

// 获取会员卡列表
export async function GET(req: NextRequest) {
  try {
    // 从URL参数中获取租户ID和筛选条件
    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get('tenantId');
    const status = searchParams.get('status');
    const cardType = searchParams.get('cardType');
    const keyword = searchParams.get('keyword');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');

    console.log('会员卡API接收参数:', { tenantId, status, cardType, keyword, page, pageSize });

    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID参数',
        data: null
      }, { status: 400 });
    }
    
    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 构建查询条件
      let whereConditions = ['tenant_id = ?'];
      let queryParams: any[] = [parseInt(tenantId)];

      // 状态筛选
      if (status && status !== 'all' && status !== '全部状态') {
        whereConditions.push('status = ?');
        queryParams.push(status === 'active' ? '销售中' : '已下架');
      }

      // 卡类型筛选
      if (cardType && cardType !== 'all' && cardType !== '全部类别') {
        whereConditions.push('card_type = ?');
        queryParams.push(cardType);
      }

      // 关键词搜索
      if (keyword) {
        whereConditions.push('(name LIKE ? OR description LIKE ? OR card_type LIKE ?)');
        const searchTerm = `%${keyword}%`;
        queryParams.push(searchTerm, searchTerm, searchTerm);
      }

      // 构建完整的查询语句
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      const offset = (page - 1) * pageSize;

      // 查询会员卡列表
      const [rows] = await connection.execute(
        `SELECT * FROM membercard ${whereClause} ORDER BY created_at DESC LIMIT ? OFFSET ?`,
        [...queryParams, pageSize, offset]
      );

      // 查询总数
      const [countResult] = await connection.execute(
        `SELECT COUNT(*) as total FROM membercard ${whereClause}`,
        queryParams
      );

      const total = (countResult as any[])[0].total;

      console.log('会员卡查询结果:', (rows as any[]).length, '条记录，总计:', total);

      return NextResponse.json({
        code: 0,
        data: {
          list: rows,
          total: total,
          page: page,
          pageSize: pageSize,
          totalPages: Math.ceil(total / pageSize)
        },
        msg: '获取会员卡列表成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取会员卡列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取会员卡列表失败',
      data: null
    }, { status: 500 });
  }
}

// 添加会员卡
export async function POST(req: NextRequest) {
  try {
    const data = await req.json();

    console.log('创建会员卡API接收数据:', data);

    // 验证必填字段
    if (!data.tenant_id || !data.name || !data.price || !data.validity_days || !data.usage_limit || !data.card_type) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 插入会员卡数据
      const [result] = await connection.execute(
        `INSERT INTO membercard (tenant_id, name, description, price, original_price, validity_days, usage_limit, card_type, status, members_count, sales_count, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          data.tenant_id,
          data.name,
          data.description || null,
          data.price,
          data.original_price || null,
          data.validity_days,
          data.usage_limit,
          data.card_type,
          data.status || '销售中',
          0, // members_count
          0  // sales_count
        ]
      );

      const insertId = (result as any).insertId;

      // 查询新创建的会员卡
      const [newCard] = await connection.execute(
        'SELECT * FROM membercard WHERE id = ?',
        [insertId]
      );

      console.log('创建会员卡成功，ID:', insertId);

      return NextResponse.json({
        code: 0,
        data: (newCard as any[])[0],
        msg: '添加会员卡成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('添加会员卡失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '添加会员卡失败',
      data: null
    }, { status: 500 });
  }
}