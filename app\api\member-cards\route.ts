import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 会员卡数据接口
interface MemberCardData {
  name: string;
  description?: string;
  price: number;
  original_price?: number;
  validity_days: number;
  usage_limit: string;
  card_type: string;
  status?: string;
}

// 获取会员卡列表
export async function GET(req: NextRequest) {
  try {
    // 从URL参数中获取租户ID和筛选条件
    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get('tenantId');
    const status = searchParams.get('status');
    const cardType = searchParams.get('cardType');
    const keyword = searchParams.get('keyword');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');

    console.log('会员卡API接收参数:', { tenantId, status, cardType, keyword, page, pageSize });

    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID参数',
        data: null
      }, { status: 400 });
    }
    
    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 构建查询条件
      let whereConditions = ['tenant_id = ?'];
      let queryParams: any[] = [parseInt(tenantId)];

      // 状态筛选
      if (status && status !== 'all' && status !== '全部状态') {
        whereConditions.push('status = ?');
        queryParams.push(status === 'active' ? '销售中' : '已下架');
      }

      // 卡类型筛选
      if (cardType && cardType !== 'all' && cardType !== '全部类别') {
        whereConditions.push('card_type = ?');
        queryParams.push(cardType);
      }

      // 关键词搜索
      if (keyword) {
        whereConditions.push('(name LIKE ? OR description LIKE ? OR card_type LIKE ?)');
        const searchTerm = `%${keyword}%`;
        queryParams.push(searchTerm, searchTerm, searchTerm);
      }

      // 构建完整的查询语句
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      const offset = (page - 1) * pageSize;

      console.log('查询参数:', queryParams);
      console.log('WHERE子句:', whereClause);
      console.log('分页参数:', { pageSize, offset });

      // 尝试使用字符串拼接而不是参数化查询来解决MySQL驱动问题
      const sql = `SELECT
        id, tenant_id, name, description, price, original_price,
        validity_days, usage_limit, card_category, card_type, status,
        total_sold as sales_count, active_cards as members_count,
        display_order, color, is_trial_card, is_gift_card,
        created_at, updated_at
      FROM member_card_types WHERE tenant_id = ${parseInt(tenantId)} ORDER BY display_order ASC, created_at DESC LIMIT ${pageSize} OFFSET ${offset}`;

      console.log('执行SQL:', sql);

      // 查询会员卡类型列表
      const [rows] = await connection.execute(sql);

      // 查询总数
      const countSql = `SELECT COUNT(*) as total FROM member_card_types WHERE tenant_id = ${parseInt(tenantId)}`;
      const [countResult] = await connection.execute(countSql);

      const total = (countResult as any[])[0].total;

      console.log('会员卡查询结果:', (rows as any[]).length, '条记录，总计:', total);

      return NextResponse.json({
        code: 0,
        data: {
          list: rows,
          total: total,
          page: page,
          pageSize: pageSize,
          totalPages: Math.ceil(total / pageSize)
        },
        msg: '获取会员卡列表成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取会员卡列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取会员卡列表失败',
      data: null
    }, { status: 500 });
  }
}

// 添加会员卡
export async function POST(req: NextRequest) {
  try {
    const data = await req.json();

    console.log('创建会员卡API接收数据:', data);

    // 验证必填字段
    if (!data.tenant_id || !data.name || !data.price || !data.card_category || !data.card_type) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      await connection.beginTransaction();

      // 插入会员卡类型数据
      const [result] = await connection.execute(
        `INSERT INTO member_card_types (tenant_id, name, description, price, original_price, validity_days, usage_limit, card_category, card_type, status, total_sold, active_cards, display_order, color, is_trial_card, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          data.tenant_id,
          data.name,
          data.description || null,
          data.price,
          data.original_price || null,
          data.validity_days,
          data.usage_limit,
          data.card_category,
          data.card_type,
          data.status || '销售中',
          0, // total_sold
          0, // active_cards
          data.display_order || 0,
          data.color || null,
          data.is_trial_card || false
        ]
      );

      const insertId = (result as any).insertId;

      // 创建默认的高级设置
      await connection.execute(
        `INSERT INTO member_card_advanced_settings
        (card_type_id, tenant_id, leave_option, auto_activate_days, max_people_per_class, daily_booking_limit, weekly_booking_limit, monthly_booking_limit, advance_booking_unlimited, custom_time_enabled)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [insertId, data.tenant_id, 'no_limit', 120, 1, 3, 4, 5, true, false]
      );

      // 创建默认的用户设置
      await connection.execute(
        `INSERT INTO member_card_user_settings
        (card_type_id, tenant_id, booking_interval_enabled, pending_booking_limit, cancel_limit_enabled, same_course_daily_limit, peak_time_enabled, priority_enabled)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [insertId, data.tenant_id, false, 0, false, 1, false, false]
      );

      // 创建默认的课程设置
      await connection.execute(
        `INSERT INTO member_card_course_settings
        (card_type_id, tenant_id, consumption_rule, consumption_description, gift_class_count, gift_value_coefficient, all_courses_enabled)
        VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [insertId, data.tenant_id, 'AVERAGE', 'Average consumption rule', 0, 1.0, true]
      );

      // 创建默认的销售设置
      await connection.execute(
        `INSERT INTO member_card_sales_settings
        (card_type_id, tenant_id, enable_discount, enable_promotion, max_per_user)
        VALUES (?, ?, ?, ?, ?)`,
        [insertId, data.tenant_id, false, false, 1]
      );

      await connection.commit();

      // 查询新创建的会员卡类型
      const [newCard] = await connection.execute(
        'SELECT * FROM member_card_types WHERE id = ?',
        [insertId]
      );

      console.log('创建会员卡成功，ID:', insertId);

      return NextResponse.json({
        code: 0,
        data: (newCard as any[])[0],
        msg: '添加会员卡成功'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('添加会员卡失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '添加会员卡失败',
      data: null
    }, { status: 500 });
  }
}

// 更新会员卡
export async function PUT(req: NextRequest) {
  try {
    const data = await req.json();
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    console.log('更新会员卡API接收数据:', { id, data });

    // 验证ID
    if (!id) {
      return NextResponse.json({
        code: 400,
        msg: '缺少会员卡ID',
        data: null
      }, { status: 400 });
    }

    // 验证必填字段
    if (!data.name || !data.price || !data.card_category || !data.card_type) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 更新会员卡类型数据
      const [result] = await connection.execute(
        `UPDATE member_card_types SET
         name = ?, description = ?, price = ?, original_price = ?,
         validity_days = ?, usage_limit = ?, card_category = ?, card_type = ?,
         status = ?, display_order = ?, color = ?, is_trial_card = ?, updated_at = NOW()
         WHERE id = ?`,
        [
          data.name,
          data.description || null,
          data.price,
          data.original_price || null,
          data.validity_days,
          data.usage_limit,
          data.card_category,
          data.card_type,
          data.status || '销售中',
          data.display_order || 0,
          data.color || null,
          data.is_trial_card || false,
          parseInt(id)
        ]
      );

      const affectedRows = (result as any).affectedRows;

      if (affectedRows === 0) {
        return NextResponse.json({
          code: 404,
          msg: '会员卡不存在',
          data: null
        }, { status: 404 });
      }

      // 查询更新后的会员卡类型
      const [updatedCard] = await connection.execute(
        'SELECT * FROM member_card_types WHERE id = ?',
        [parseInt(id)]
      );

      console.log('更新会员卡成功，ID:', id);

      return NextResponse.json({
        code: 0,
        data: (updatedCard as any[])[0],
        msg: '更新会员卡成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('更新会员卡失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '更新会员卡失败',
      data: null
    }, { status: 500 });
  }
}

// 删除会员卡
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    console.log('删除会员卡API接收ID:', id);

    // 验证ID
    if (!id) {
      return NextResponse.json({
        code: 400,
        msg: '缺少会员卡ID',
        data: null
      }, { status: 400 });
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 检查是否有关联的会员卡实例
      const [memberCards] = await connection.execute(
        'SELECT COUNT(*) as count FROM member_cards WHERE card_type_id = ?',
        [parseInt(id)]
      );

      const memberCardCount = (memberCards as any[])[0].count;

      if (memberCardCount > 0) {
        return NextResponse.json({
          code: 400,
          msg: `无法删除，该会员卡类型下还有 ${memberCardCount} 张会员卡`,
          data: null
        }, { status: 400 });
      }

      // 删除会员卡类型
      const [result] = await connection.execute(
        'DELETE FROM member_card_types WHERE id = ?',
        [parseInt(id)]
      );

      const affectedRows = (result as any).affectedRows;

      if (affectedRows === 0) {
        return NextResponse.json({
          code: 404,
          msg: '会员卡不存在',
          data: null
        }, { status: 404 });
      }

      console.log('删除会员卡成功，ID:', id);

      return NextResponse.json({
        code: 0,
        data: null,
        msg: '删除会员卡成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('删除会员卡失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '删除会员卡失败',
      data: null
    }, { status: 500 });
  }
}