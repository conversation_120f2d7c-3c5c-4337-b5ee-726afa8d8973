"use client"

import React, { useState, useRef, useEffect } from "react"
import BusinessHoursBasedSchedule from "./components/business-hours-based-schedule"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/components/ui/use-toast"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MoreHorizontal, Plus, Search, UserPlus, Shield, Upload, Camera, Trash2, Eye, Pencil, Clock, UserMinus } from "lucide-react"

// 雇佣类型
const employmentTypes = {
  FULL_TIME: "全职",
  PART_TIME: "兼职",
  EXTERNAL: "外聘"
}

// 课程类型
const courseTypes = {
  GROUP: "团体课",
  PRIVATE: "私教课",
  SMALL: "小班课",
  WORKSHOP: "工作坊",
  ALL: "全部课程"
}

// 定义日期类型
type DayKey = "周一" | "周二" | "周三" | "周四" | "周五" | "周六" | "周日";

// 定义休息时间类型
type BreakTime = {
  type: string;
  name: string;
  start: string;
  end: string;
};

// 定义周次类型
type WeekType = "thisWeek" | "nextWeek";

// 定义私教时间设置表单类型
type PrivateCoachingSchedule = {
  allDaysSame: boolean;
  currentWeek: WeekType; // 当前选择的周次
  weeks: {
    [key in WeekType]: {
      days: {
        [key in DayKey]: {
          enabled: boolean;
          workTime: {
            start: string;
            end: string;
          }
        }
      };
      breakTimes: BreakTime[];
      customBreakTimes: BreakTime[];
    }
  };
};

// 定义员工数据类型
type StaffMember = {
  id: number;
  name: string;
  gender: string;
  avatar: string;
  roles: string[];
  status: string;
  phone: string;
  email: string;
  joinDate: string;
  employmentType: string;
  isTeachingSales: boolean;
  specialties?: string; // 教练专长
  bio?: string; // 教练简介
  advancedSettings?: {
    courseTypes?: string[];
    privateCoachingSettings?: {
      enabled: boolean;
      workSchedule?: PrivateCoachingSchedule;
      // 保留旧格式以兼容现有数据
      availableDays?: string[];
      timeSlots?: { start: string; end: string; }[];
    };
  };
};

// 模拟员工数据
const staffData: StaffMember[] = [
  {
    id: 1,
    name: "张三",
    gender: "男",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=zhang",
    roles: ["前台"],
    status: "在职",
    phone: "13800138001",
    email: "<EMAIL>",
    joinDate: "2022-01-15",
    employmentType: employmentTypes.FULL_TIME,
    isTeachingSales: false,
    advancedSettings: undefined
  },
  {
    id: 2,
    name: "李四",
    gender: "男",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=li",
    roles: ["会籍顾问"],
    status: "在职",
    phone: "13800138002",
    email: "<EMAIL>",
    joinDate: "2022-03-10",
    employmentType: employmentTypes.FULL_TIME,
    isTeachingSales: false,
    advancedSettings: undefined
  },
  {
    id: 3,
    name: "王五",
    gender: "男",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=wang",
    roles: ["教练", "会籍顾问"],
    status: "在职",
    phone: "13800138003",
    email: "<EMAIL>",
    joinDate: "2022-05-20",
    employmentType: employmentTypes.FULL_TIME,
    isTeachingSales: true,
    advancedSettings: {
      courseTypes: [courseTypes.GROUP, courseTypes.PRIVATE, courseTypes.SMALL],
      privateCoachingSettings: {
        enabled: true,
        availableDays: ["周一", "周三", "周五", "周六", "周日"],
        timeSlots: [
          { start: "09:00", end: "12:00" },
          { start: "14:00", end: "18:00" }
        ]
      }
    }
  }
]

// 角色数据 - 简化为基础角色
const roles = [
  // 管理角色
  {
    id: 1,
    name: "超级管理员",
    description: "拥有系统所有权限，不可编辑",
    isSystem: true,
    editable: false,
    category: "管理",
    hasAdvancedSettings: false
  },
  {
    id: 2,
    name: "店长",
    description: "管理场馆相关的所有功能",
    isSystem: true,
    editable: true,
    category: "管理",
    hasAdvancedSettings: false
  },
  // 教学角色
  {
    id: 3,
    name: "教练",
    description: "负责课程教学",
    isSystem: true,
    editable: true,
    category: "教学",
    hasAdvancedSettings: true
  },
  // 销售角色
  {
    id: 6,
    name: "会籍顾问",
    description: "负责会员销售和服务",
    isSystem: true,
    editable: true,
    category: "销售",
    hasAdvancedSettings: false
  },
  // 服务角色
  {
    id: 8,
    name: "前台",
    description: "负责前台接待工作",
    isSystem: true,
    editable: true,
    category: "服务",
    hasAdvancedSettings: false
  }
]

export default function StaffManagementPage() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedRole, setSelectedRole] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [isAddStaffDialogOpen, setIsAddStaffDialogOpen] = useState(false)
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false)
  const [isPrivateCoachingSettingsDialogOpen, setIsPrivateCoachingSettingsDialogOpen] = useState(false)
  const [currentRole, setCurrentRole] = useState<any>(null)
  const [currentStaff, setCurrentStaff] = useState<any>(null)

  // 检查URL参数，如果有openAddCoach=true，则自动打开添加员工对话框并预设教练角色
  useEffect(() => {
    // 只在客户端执行
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      const openAddCoach = urlParams.get('openAddCoach')

      if (openAddCoach === 'true') {
        // 打开添加员工对话框
        setIsAddStaffDialogOpen(true)

        // 预设教练角色
        const coachRoleId = roles.find(r => r.name === "教练")?.id
        if (coachRoleId) {
          setNewStaffForm(prev => ({
            ...prev,
            selectedRoles: [coachRoleId],
            showAdvancedSettings: true,
            advancedSettings: {
              ...prev.advancedSettings,
              selectedCourseTypes: [courseTypes.GROUP, courseTypes.PRIVATE]
            }
          }))
        }

        // 清除URL参数，避免刷新页面时重复触发
        window.history.replaceState({}, document.title, window.location.pathname)
      }
    }
  }, [])



  // 私教时间设置模式
  const [scheduleMode, setScheduleMode] = useState<"manual" | "business-hours">("manual");

  // 私教时间设置表单
  const [privateCoachingSettingsForm, setPrivateCoachingSettingsForm] = useState<PrivateCoachingSchedule>({
    allDaysSame: true,
    currentWeek: "thisWeek",
    weeks: {
      thisWeek: {
        days: {
          "周一": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周二": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周三": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周四": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周五": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周六": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周日": { enabled: true, workTime: { start: "09:00", end: "21:00" } }
        },
        breakTimes: [
          { type: "lunch", name: "午餐时间", start: "12:00", end: "13:00" },
          { type: "dinner", name: "晚餐时间", start: "18:00", end: "19:00" }
        ],
        customBreakTimes: []
      },
      nextWeek: {
        days: {
          "周一": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周二": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周三": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周四": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周五": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周六": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
          "周日": { enabled: true, workTime: { start: "09:00", end: "21:00" } }
        },
        breakTimes: [
          { type: "lunch", name: "午餐时间", start: "12:00", end: "13:00" },
          { type: "dinner", name: "晚餐时间", start: "18:00", end: "19:00" }
        ],
        customBreakTimes: []
      }
    }
  })

  // 基于营业时间的私教时间设置
  const [businessHoursSchedule, setBusinessHoursSchedule] = useState({
    businessHours: {
      "周一": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
      "周二": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
      "周三": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
      "周四": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
      "周五": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
      "周六": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
      "周日": { enabled: true, workTime: { start: "09:00", end: "21:00" } }
    },
    defaultBreakTimes: [
      { type: "lunch", name: "午餐时间", start: "12:00", end: "13:00" },
      { type: "dinner", name: "晚餐时间", start: "18:00", end: "19:00" }
    ],
    specialDates: []
  })

  // 添加员工表单状态
  const [newStaffForm, setNewStaffForm] = useState({
    name: "",
    phone: "",
    gender: "女", // 默认性别为女
    email: "",
    joinDate: "",
    status: "在职",
    selectedRoles: [] as number[],
    employmentType: employmentTypes.FULL_TIME,
    isTeachingSales: true, // 默认开启教销一体
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=default",
    avatarFile: null as File | null,
    specialties: "", // 教练专长
    bio: "", // 教练简介
    advancedSettings: {
      selectedCourseTypes: [] as string[],
      privateCoachingSettings: {
        enabled: false,
        availableDays: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        timeSlots: [
          { start: "09:00", end: "12:00" },
          { start: "14:00", end: "18:00" }
        ]
      }
    },
    showAdvancedSettings: false
  })

  // 头像上传引用
  const avatarInputRef = useRef<HTMLInputElement>(null)

  // 处理角色选择变化
  const handleRoleChange = (roleId: number, checked: boolean) => {
    const role = roles.find(r => r.id === roleId);

    if (checked) {
      // 添加角色
      const newSelectedRoles = [...newStaffForm.selectedRoles, roleId];

      // 如果角色有高级设置，显示高级设置面板
      const showAdvanced = role?.hasAdvancedSettings ||
                          newSelectedRoles.some(id => roles.find(r => r.id === id)?.hasAdvancedSettings);

      setNewStaffForm({
        ...newStaffForm,
        selectedRoles: newSelectedRoles,
        showAdvancedSettings: showAdvanced
      });
    } else {
      // 移除角色
      const newSelectedRoles = newStaffForm.selectedRoles.filter(id => id !== roleId);

      // 检查是否还需要显示高级设置
      const showAdvanced = newSelectedRoles.some(id => roles.find(r => r.id === id)?.hasAdvancedSettings);

      setNewStaffForm({
        ...newStaffForm,
        selectedRoles: newSelectedRoles,
        showAdvancedSettings: showAdvanced
      });
    }
  }

  // 处理课程类型选择变化
  const handleCourseTypeChange = (courseType: string, checked: boolean) => {
    // 检查是否选择了私教课
    const isPrivateCoachingSelected = courseType === courseTypes.PRIVATE && checked;
    const wasPrivateCoachingSelected = newStaffForm.advancedSettings.selectedCourseTypes.includes(courseTypes.PRIVATE);
    const shouldEnablePrivateCoaching = isPrivateCoachingSelected ||
      (wasPrivateCoachingSelected && courseType !== courseTypes.PRIVATE);

    if (checked) {
      setNewStaffForm({
        ...newStaffForm,
        advancedSettings: {
          ...newStaffForm.advancedSettings,
          selectedCourseTypes: [...newStaffForm.advancedSettings.selectedCourseTypes, courseType],
          privateCoachingSettings: {
            ...newStaffForm.advancedSettings.privateCoachingSettings,
            enabled: shouldEnablePrivateCoaching
          }
        }
      });
    } else {
      // 如果取消选择私教课，禁用私教课设置
      const privateCoachingEnabled = courseType === courseTypes.PRIVATE ? false :
        newStaffForm.advancedSettings.privateCoachingSettings.enabled;

      setNewStaffForm({
        ...newStaffForm,
        advancedSettings: {
          ...newStaffForm.advancedSettings,
          selectedCourseTypes: newStaffForm.advancedSettings.selectedCourseTypes.filter(type => type !== courseType),
          privateCoachingSettings: {
            ...newStaffForm.advancedSettings.privateCoachingSettings,
            enabled: privateCoachingEnabled
          }
        }
      });
    }
  }

  // 注意：以下私教课时间设置相关函数已从UI中移除，但保留在代码中以备将来使用
  // 如需重新启用，请参考备份或历史版本

  // 处理周次切换
  const handleWeekChange = (week: WeekType) => {
    setPrivateCoachingSettingsForm({
      ...privateCoachingSettingsForm,
      currentWeek: week
    });
  }

  // 打开私教时间设置对话框
  const openPrivateCoachingSettingsDialog = (staff: any) => {
    setCurrentStaff(staff);

    // 初始化表单数据
    // 如果员工已有私教课时间设置，则使用已有设置
    if (staff.advancedSettings?.privateCoachingSettings?.workSchedule) {
      // 检查是否有模式设置
      if (staff.advancedSettings?.privateCoachingSettings?.scheduleMode) {
        setScheduleMode(staff.advancedSettings.privateCoachingSettings.scheduleMode);
      } else {
        setScheduleMode("manual"); // 默认使用手动模式
      }

      setPrivateCoachingSettingsForm(staff.advancedSettings.privateCoachingSettings.workSchedule);

      // 如果有基于营业时间的设置
      if (staff.advancedSettings?.privateCoachingSettings?.businessHoursSchedule) {
        setBusinessHoursSchedule(staff.advancedSettings.privateCoachingSettings.businessHoursSchedule);
      }
    } else {
      // 否则使用默认设置
      setScheduleMode("manual");
      setPrivateCoachingSettingsForm({
        allDaysSame: true,
        currentWeek: "thisWeek",
        weeks: {
          thisWeek: {
            days: {
              "周一": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周二": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周三": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周四": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周五": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周六": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周日": { enabled: true, workTime: { start: "09:00", end: "21:00" } }
            },
            breakTimes: [
              { type: "lunch", name: "午餐时间", start: "12:00", end: "13:00" },
              { type: "dinner", name: "晚餐时间", start: "18:00", end: "19:00" }
            ],
            customBreakTimes: []
          },
          nextWeek: {
            days: {
              "周一": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周二": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周三": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周四": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周五": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周六": { enabled: true, workTime: { start: "09:00", end: "21:00" } },
              "周日": { enabled: true, workTime: { start: "09:00", end: "21:00" } }
            },
            breakTimes: [
              { type: "lunch", name: "午餐时间", start: "12:00", end: "13:00" },
              { type: "dinner", name: "晚餐时间", start: "18:00", end: "19:00" }
            ],
            customBreakTimes: []
          }
        }
      });
    }

    setIsPrivateCoachingSettingsDialogOpen(true);
  }

  // 处理模式切换
  const handleScheduleModeChange = (mode: "manual" | "business-hours") => {
    setScheduleMode(mode);
  }

  // 保存基于营业时间的私教时间设置
  const saveBusinessHoursSchedule = (data: any) => {
    setBusinessHoursSchedule(data);

    if (!currentStaff) return;

    // 更新员工数据
    const updatedStaffData = staffData.map(staff => {
      if (staff.id === currentStaff.id) {
        // 创建或更新高级设置
        const advancedSettings = staff.advancedSettings || { courseTypes: [] };
        const courseTypes = advancedSettings.courseTypes || [];

        return {
          ...staff,
          advancedSettings: {
            courseTypes: courseTypes,
            privateCoachingSettings: {
              enabled: true,
              scheduleMode: "business-hours",
              businessHoursSchedule: data,
              workSchedule: privateCoachingSettingsForm
            }
          }
        };
      }
      return staff;
    });

    // 更新员工数据
    staffData.splice(0, staffData.length, ...updatedStaffData);

    // 关闭对话框
    setIsPrivateCoachingSettingsDialogOpen(false);

    // 显示成功提示
    toast({
      title: "私教时间设置已更新",
      description: `${currentStaff.name} 的私教课时间设置已成功更新（基于营业时间模式）`,
    });
  }

  // 处理"每个工作日作息时间都一样"选项变化
  const handleAllDaysSameChange = (checked: boolean) => {
    // 如果选中"所有天相同"，则将所有天的时间设置为与周一相同
    if (checked) {
      const currentWeek = privateCoachingSettingsForm.currentWeek;
      const mondaySettings = privateCoachingSettingsForm.weeks[currentWeek].days["周一"];
      const updatedDays = { ...privateCoachingSettingsForm.weeks[currentWeek].days };

      (Object.keys(updatedDays) as DayKey[]).forEach(day => {
        if (day !== "周一") {
          updatedDays[day] = { ...mondaySettings };
        }
      });

      const updatedWeeks = { ...privateCoachingSettingsForm.weeks };
      updatedWeeks[currentWeek] = {
        ...privateCoachingSettingsForm.weeks[currentWeek],
        days: updatedDays
      };

      setPrivateCoachingSettingsForm({
        ...privateCoachingSettingsForm,
        allDaysSame: checked,
        weeks: updatedWeeks
      });
    } else {
      setPrivateCoachingSettingsForm({
        ...privateCoachingSettingsForm,
        allDaysSame: checked
      });
    }
  }

  // 处理某天是否上班的开关变化
  const handleDayEnabledChange = (day: DayKey, enabled: boolean) => {
    const currentWeek = privateCoachingSettingsForm.currentWeek;
    const updatedDays = { ...privateCoachingSettingsForm.weeks[currentWeek].days };
    updatedDays[day] = {
      ...updatedDays[day],
      enabled
    };

    // 如果选中了"所有天相同"，则更新所有天
    if (privateCoachingSettingsForm.allDaysSame) {
      (Object.keys(updatedDays) as DayKey[]).forEach(d => {
        updatedDays[d] = { ...updatedDays[d], enabled };
      });
    }

    const updatedWeeks = { ...privateCoachingSettingsForm.weeks };
    updatedWeeks[currentWeek] = {
      ...privateCoachingSettingsForm.weeks[currentWeek],
      days: updatedDays
    };

    setPrivateCoachingSettingsForm({
      ...privateCoachingSettingsForm,
      weeks: updatedWeeks
    });
  }

  // 处理工作时间变化
  const handleWorkTimeChange = (day: DayKey, field: 'start' | 'end', value: string) => {
    const currentWeek = privateCoachingSettingsForm.currentWeek;
    const updatedDays = { ...privateCoachingSettingsForm.weeks[currentWeek].days };
    updatedDays[day] = {
      ...updatedDays[day],
      workTime: {
        ...updatedDays[day].workTime,
        [field]: value
      }
    };

    // 如果选中了"所有天相同"，则更新所有天
    if (privateCoachingSettingsForm.allDaysSame) {
      (Object.keys(updatedDays) as DayKey[]).forEach(d => {
        if (d !== day) {
          updatedDays[d] = {
            ...updatedDays[d],
            workTime: {
              ...updatedDays[d].workTime,
              [field]: value
            }
          };
        }
      });
    }

    const updatedWeeks = { ...privateCoachingSettingsForm.weeks };
    updatedWeeks[currentWeek] = {
      ...privateCoachingSettingsForm.weeks[currentWeek],
      days: updatedDays
    };

    setPrivateCoachingSettingsForm({
      ...privateCoachingSettingsForm,
      weeks: updatedWeeks
    });
  }

  // 处理休息时间变化
  const handleBreakTimeChange = (index: number, field: 'start' | 'end', value: string) => {
    const currentWeek = privateCoachingSettingsForm.currentWeek;
    const breakTimes = [...privateCoachingSettingsForm.weeks[currentWeek].breakTimes];
    breakTimes[index] = { ...breakTimes[index], [field]: value };

    const updatedWeeks = { ...privateCoachingSettingsForm.weeks };
    updatedWeeks[currentWeek] = {
      ...privateCoachingSettingsForm.weeks[currentWeek],
      breakTimes
    };

    setPrivateCoachingSettingsForm({
      ...privateCoachingSettingsForm,
      weeks: updatedWeeks
    });
  }

  // 删除休息时间
  const removeBreakTime = (index: number) => {
    const currentWeek = privateCoachingSettingsForm.currentWeek;
    const breakTimes = [...privateCoachingSettingsForm.weeks[currentWeek].breakTimes];
    breakTimes.splice(index, 1);

    const updatedWeeks = { ...privateCoachingSettingsForm.weeks };
    updatedWeeks[currentWeek] = {
      ...privateCoachingSettingsForm.weeks[currentWeek],
      breakTimes
    };

    setPrivateCoachingSettingsForm({
      ...privateCoachingSettingsForm,
      weeks: updatedWeeks
    });
  }

  // 添加新的休息时间
  const addCustomBreakTime = () => {
    const currentWeek = privateCoachingSettingsForm.currentWeek;
    const customBreakTimes = [...privateCoachingSettingsForm.weeks[currentWeek].customBreakTimes];
    customBreakTimes.push({
      type: "custom",
      name: "休息时间",
      start: "15:00",
      end: "16:00"
    });

    const updatedWeeks = { ...privateCoachingSettingsForm.weeks };
    updatedWeeks[currentWeek] = {
      ...privateCoachingSettingsForm.weeks[currentWeek],
      customBreakTimes
    };

    setPrivateCoachingSettingsForm({
      ...privateCoachingSettingsForm,
      weeks: updatedWeeks
    });
  }

  // 处理自定义休息时间变化
  const handleCustomBreakTimeChange = (index: number, field: 'name' | 'start' | 'end', value: string) => {
    const currentWeek = privateCoachingSettingsForm.currentWeek;
    const customBreakTimes = [...privateCoachingSettingsForm.weeks[currentWeek].customBreakTimes];
    customBreakTimes[index] = { ...customBreakTimes[index], [field]: value };

    const updatedWeeks = { ...privateCoachingSettingsForm.weeks };
    updatedWeeks[currentWeek] = {
      ...privateCoachingSettingsForm.weeks[currentWeek],
      customBreakTimes
    };

    setPrivateCoachingSettingsForm({
      ...privateCoachingSettingsForm,
      weeks: updatedWeeks
    });
  }

  // 删除自定义休息时间
  const removeCustomBreakTime = (index: number) => {
    const currentWeek = privateCoachingSettingsForm.currentWeek;
    const customBreakTimes = [...privateCoachingSettingsForm.weeks[currentWeek].customBreakTimes];
    customBreakTimes.splice(index, 1);

    const updatedWeeks = { ...privateCoachingSettingsForm.weeks };
    updatedWeeks[currentWeek] = {
      ...privateCoachingSettingsForm.weeks[currentWeek],
      customBreakTimes
    };

    setPrivateCoachingSettingsForm({
      ...privateCoachingSettingsForm,
      weeks: updatedWeeks
    });
  }

  // 保存私教时间设置
  const savePrivateCoachingSettings = () => {
    if (!currentStaff) return;

    // 更新员工数据
    const updatedStaffData = staffData.map(staff => {
      if (staff.id === currentStaff.id) {
        // 创建或更新高级设置
        const advancedSettings = staff.advancedSettings || { courseTypes: [] };
        const courseTypes = advancedSettings.courseTypes || [];

        return {
          ...staff,
          advancedSettings: {
            courseTypes: courseTypes,
            privateCoachingSettings: {
              enabled: true,
              scheduleMode: "manual", // 手动模式
              workSchedule: privateCoachingSettingsForm,
              businessHoursSchedule: businessHoursSchedule // 保留营业时间设置，以便切换回来
            }
          }
        };
      }
      return staff;
    });

    // 更新员工数据
    staffData.splice(0, staffData.length, ...updatedStaffData);

    // 关闭对话框
    setIsPrivateCoachingSettingsDialogOpen(false);

    // 显示成功提示
    const weekText = privateCoachingSettingsForm.currentWeek === "thisWeek" ? "本周" : "下周";
    toast({
      title: "私教时间设置已更新",
      description: `${currentStaff.name} 的${weekText}私教课时间设置已成功更新（手动模式）`,
    });
  }

  // 处理教销一体选择变化
  const handleTeachingSalesChange = (checked: boolean) => {
    // 找到教练和会籍顾问的角色ID
    const coachRoleId = roles.find(r => r.name === "教练")?.id;
    const membershipConsultantRoleId = roles.find(r => r.name === "会籍顾问")?.id;

    let newSelectedRoles = [...newStaffForm.selectedRoles];

    if (checked) {
      // 如果开启教销一体，确保教练和会籍顾问角色都被选中
      if (coachRoleId && !newSelectedRoles.includes(coachRoleId)) {
        newSelectedRoles.push(coachRoleId);
      }
      if (membershipConsultantRoleId && !newSelectedRoles.includes(membershipConsultantRoleId)) {
        newSelectedRoles.push(membershipConsultantRoleId);
      }
    } else {
      // 如果关闭教销一体，移除教练和会籍顾问角色
      if (coachRoleId) {
        newSelectedRoles = newSelectedRoles.filter(id => id !== coachRoleId);
      }
      if (membershipConsultantRoleId) {
        newSelectedRoles = newSelectedRoles.filter(id => id !== membershipConsultantRoleId);
      }
    }

    // 检查是否需要显示高级设置
    const showAdvanced = newSelectedRoles.some(id => roles.find(r => r.id === id)?.hasAdvancedSettings);

    setNewStaffForm({
      ...newStaffForm,
      isTeachingSales: checked,
      selectedRoles: newSelectedRoles,
      showAdvancedSettings: showAdvanced
    });
  }



  // 处理头像上传
  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 创建临时URL以预览图片
      const imageUrl = URL.createObjectURL(file);
      setNewStaffForm({
        ...newStaffForm,
        avatar: imageUrl,
        avatarFile: file
      });
    }
  }

  // 触发头像上传点击
  const triggerAvatarUpload = () => {
    avatarInputRef.current?.click();
  }

  // 当对话框打开时，如果教销一体开关打开，自动选中教练和会籍顾问角色
  useEffect(() => {
    if (isAddStaffDialogOpen && newStaffForm.isTeachingSales) {
      // 找到教练和会籍顾问的角色ID
      const coachRoleId = roles.find(r => r.name === "教练")?.id;
      const membershipConsultantRoleId = roles.find(r => r.name === "会籍顾问")?.id;

      let newSelectedRoles = [...newStaffForm.selectedRoles];
      let changed = false;

      // 如果开启教销一体，确保教练和会籍顾问角色都被选中
      if (coachRoleId && !newSelectedRoles.includes(coachRoleId)) {
        newSelectedRoles.push(coachRoleId);
        changed = true;
      }
      if (membershipConsultantRoleId && !newSelectedRoles.includes(membershipConsultantRoleId)) {
        newSelectedRoles.push(membershipConsultantRoleId);
        changed = true;
      }

      // 只有当角色发生变化时才更新状态
      if (changed) {
        // 检查是否需要显示高级设置
        const showAdvanced = newSelectedRoles.some(id => roles.find(r => r.id === id)?.hasAdvancedSettings);

        setNewStaffForm({
          ...newStaffForm,
          selectedRoles: newSelectedRoles,
          showAdvancedSettings: showAdvanced
        });
      }
    }
  }, [isAddStaffDialogOpen]);

  // 过滤员工数据
  const filteredStaff = staffData.filter((staff) => {
    const matchesSearch = staff.name.includes(searchTerm) ||
                         staff.phone.includes(searchTerm) ||
                         staff.email.includes(searchTerm)
    const matchesRole = selectedRole === "all" || staff.roles.includes(selectedRole)
    const matchesStatus = selectedStatus === "all" || staff.status === selectedStatus

    return matchesSearch && matchesRole && matchesStatus
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">员工管理</h1>
        <Button onClick={() => setIsAddStaffDialogOpen(true)}>
          <UserPlus className="mr-2 h-4 w-4" />
          添加员工
        </Button>
      </div>

      <Tabs defaultValue="list">
        <TabsList>
          <TabsTrigger value="list">员工列表</TabsTrigger>
          <TabsTrigger value="roles">角色管理</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>员工列表</CardTitle>
              <CardDescription>
                管理场馆的所有员工信息，包括前台、会籍顾问、教练等
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="搜索员工姓名、电话或邮箱..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <Select
                  value={selectedRole}
                  onValueChange={setSelectedRole}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="选择角色" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有角色</SelectItem>
                    <SelectGroup>
                      <SelectLabel>管理角色</SelectLabel>
                      {roles.filter(r => r.category === "管理").map((role) => (
                        <SelectItem key={role.id} value={role.name}>
                          {role.name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                    <SelectGroup>
                      <SelectLabel>教学角色</SelectLabel>
                      {roles.filter(r => r.category === "教学").map((role) => (
                        <SelectItem key={role.id} value={role.name}>
                          {role.name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                    <SelectGroup>
                      <SelectLabel>销售角色</SelectLabel>
                      {roles.filter(r => r.category === "销售").map((role) => (
                        <SelectItem key={role.id} value={role.name}>
                          {role.name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                    <SelectGroup>
                      <SelectLabel>服务角色</SelectLabel>
                      {roles.filter(r => r.category === "服务" || r.category === "其他").map((role) => (
                        <SelectItem key={role.id} value={role.name}>
                          {role.name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <Select
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="在职">在职</SelectItem>
                    <SelectItem value="离职">离职</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>员工</TableHead>
                    <TableHead>性别</TableHead>
                    <TableHead>角色</TableHead>
                    <TableHead>雇佣类型</TableHead>
                    <TableHead>联系方式</TableHead>
                    <TableHead>入职日期</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStaff.map((staff) => (
                    <TableRow key={staff.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={staff.avatar} alt={staff.name} />
                            <AvatarFallback>{staff.name.slice(0, 1)}</AvatarFallback>
                          </Avatar>
                          <span>{staff.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{staff.gender}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {staff.roles.map((role, index) => (
                            <Badge key={index} variant="outline" className="mr-1">
                              {role}
                            </Badge>
                          ))}
                          {staff.isTeachingSales && (
                            <Badge variant="secondary" className="mr-1">
                              教销一体
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            staff.employmentType === employmentTypes.FULL_TIME ? "default" :
                            staff.employmentType === employmentTypes.PART_TIME ? "secondary" :
                            "outline"
                          }
                        >
                          {staff.employmentType}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-xs">{staff.phone}</span>
                          <span className="text-xs text-muted-foreground">{staff.email}</span>
                        </div>
                      </TableCell>
                      <TableCell>{staff.joinDate}</TableCell>
                      <TableCell>
                        <Badge variant={staff.status === "在职" ? "default" : "secondary"}>
                          {staff.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => {
                              // 查看详情功能 - 设置当前员工并打开添加员工对话框（用于查看）
                              setCurrentStaff(staff);
                              setIsAddStaffDialogOpen(true);
                              toast({
                                title: "查看详情",
                                description: `查看员工 ${staff.name} 的详细信息`,
                              });
                            }}
                          >
                            <Eye className="h-4 w-4" />
                            <span className="sr-only">查看详情</span>
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">打开菜单</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>操作</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => {
                                // 编辑信息功能
                                setCurrentStaff(staff);
                                setIsAddStaffDialogOpen(true);
                                toast({
                                  title: "编辑信息",
                                  description: `编辑员工 ${staff.name} 的信息`,
                                });
                              }}>
                                <Pencil className="mr-2 h-4 w-4" />
                                编辑信息
                              </DropdownMenuItem>

                            {/* 只有教练角色且在职状态才显示私教时间设置选项 */}
                            {staff.roles.includes("教练") && staff.status === "在职" && (
                              <DropdownMenuItem onClick={() => openPrivateCoachingSettingsDialog(staff)}>
                                <Clock className="mr-2 h-4 w-4" />
                                私教时间设置
                              </DropdownMenuItem>
                            )}

                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive"
                              onClick={() => {
                                // 设为离职功能
                                const newStatus = staff.status === "在职" ? "离职" : "在职";

                                // 更新员工状态
                                const updatedStaffData = staffData.map(s => {
                                  if (s.id === staff.id) {
                                    return { ...s, status: newStatus };
                                  }
                                  return s;
                                });

                                // 更新员工数据
                                staffData.splice(0, staffData.length, ...updatedStaffData);

                                // 强制重新渲染
                                setSearchTerm(searchTerm + " ");
                                setSearchTerm(searchTerm);

                                toast({
                                  title: newStatus === "离职" ? "设为离职" : "恢复在职",
                                  description: `员工 ${staff.name} 已${newStatus === "离职" ? "设为离职" : "恢复在职"}状态`,
                                  variant: newStatus === "离职" ? "destructive" : "default",
                                });
                              }}
                            >
                              <UserMinus className="mr-2 h-4 w-4" />
                              {staff.status === "在职" ? "设为离职" : "恢复在职"}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>角色管理</CardTitle>
                <CardDescription>
                  管理系统角色和权限分配
                </CardDescription>
              </div>
              <Button
                onClick={() => {
                  setCurrentRole(null);
                  setIsRoleDialogOpen(true);
                }}
                className="bg-primary hover:bg-primary/90"
              >
                <Plus className="mr-2 h-4 w-4" />
                添加自定义角色
              </Button>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between mb-4">
                <p className="text-sm text-muted-foreground">系统预设角色和自定义角色列表</p>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>角色名称</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {roles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell className="font-medium">{role.name}</TableCell>
                      <TableCell>{role.description}</TableCell>
                      <TableCell>
                        <Badge variant={role.isSystem ? "default" : "outline"}>
                          {role.isSystem ? "系统预设" : "自定义"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        {role.editable ? (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setCurrentRole(role);
                              setIsRoleDialogOpen(true);
                            }}
                          >
                            编辑
                          </Button>
                        ) : (
                          <Button variant="ghost" size="sm" disabled>
                            不可编辑
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 添加/编辑/查看员工对话框 */}
      <Dialog open={isAddStaffDialogOpen} onOpenChange={setIsAddStaffDialogOpen}>
        <DialogContent className="sm:max-w-[650px] md:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {currentStaff ? (
                <>员工详情</>
              ) : (
                <>添加新员工</>
              )}
            </DialogTitle>
            <DialogDescription>
              {currentStaff ? (
                <>查看员工详细信息</>
              ) : (
                <>填写新员工的基本信息，创建后可以进一步完善详细资料</>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {/* 头像上传区域 */}
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Avatar className="h-24 w-24 cursor-pointer" onClick={triggerAvatarUpload}>
                  <AvatarImage src={newStaffForm.avatar} alt="员工头像" />
                  <AvatarFallback className="text-2xl">
                    {newStaffForm.name ? newStaffForm.name.slice(0, 1) : "?"}
                  </AvatarFallback>
                </Avatar>
                <div
                  className="absolute bottom-0 right-0 p-1 bg-primary text-white rounded-full cursor-pointer"
                  onClick={triggerAvatarUpload}
                >
                  <Camera className="h-4 w-4" />
                </div>
                <input
                  type="file"
                  ref={avatarInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="mb-2">
                  <Label htmlFor="name" className="flex items-center">
                    姓名 <span className="text-red-500 ml-1">*</span>
                  </Label>
                </div>
                <div className="h-10"> {/* 固定高度容器 */}
                  <Input
                    id="name"
                    placeholder="请输入员工姓名"
                    value={newStaffForm.name}
                    onChange={(e) => setNewStaffForm({...newStaffForm, name: e.target.value})}
                    className="h-10"
                  />
                </div>
              </div>
              <div>
                <div className="mb-2">
                  <Label htmlFor="phone" className="flex items-center">
                    手机号码 <span className="text-red-500 ml-1">*</span>
                  </Label>
                </div>
                <div className="h-10"> {/* 固定高度容器 */}
                  <Input
                    id="phone"
                    placeholder="请输入手机号码"
                    value={newStaffForm.phone}
                    onChange={(e) => setNewStaffForm({...newStaffForm, phone: e.target.value})}
                    className="h-10"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="mb-2">
                  <Label htmlFor="gender" className="flex items-center">
                    性别 <span className="text-red-500 ml-1">*</span>
                  </Label>
                </div>
                <div className="h-10"> {/* 固定高度容器 */}
                  <Select
                    value={newStaffForm.gender}
                    onValueChange={(value) => setNewStaffForm({...newStaffForm, gender: value})}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="选择性别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="男">男</SelectItem>
                      <SelectItem value="女">女</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <div className="mb-2">
                  <Label htmlFor="email" className="flex items-center">电子邮箱</Label>
                </div>
                <div className="h-10"> {/* 固定高度容器 */}
                  <Input
                    id="email"
                    type="email"
                    placeholder="请输入电子邮箱"
                    value={newStaffForm.email}
                    onChange={(e) => setNewStaffForm({...newStaffForm, email: e.target.value})}
                    className="h-10"
                  />
                </div>
              </div>
            </div>

            {/* 基础设置部分 */}
            <div className="space-y-4 border rounded-md p-4 mb-4">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium">基础设置</h3>
                <Badge variant="default">必填</Badge>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="mb-2">
                    <Label htmlFor="roles" className="flex items-center">
                      角色 <span className="text-red-500 ml-1">*</span>
                    </Label>
                  </div>
                  <div className="border rounded-md p-2 max-h-[120px] overflow-y-auto">
                    <div className="grid grid-cols-2 gap-1">
                      {roles.map((role) => (
                        <div key={role.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`role-${role.id}`}
                            disabled={role.name === "超级管理员"}
                            checked={newStaffForm.selectedRoles.includes(role.id)}
                            onCheckedChange={(checked) => handleRoleChange(role.id, !!checked)}
                          />
                          <Label
                            htmlFor={`role-${role.id}`}
                            className="text-sm font-normal cursor-pointer"
                          >
                            {role.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="mb-2">
                      <Label htmlFor="employmentType" className="flex items-center">雇佣类型</Label>
                    </div>
                    <div className="h-10"> {/* 固定高度容器 */}
                      <Select
                        value={newStaffForm.employmentType}
                        onValueChange={(value) => setNewStaffForm({...newStaffForm, employmentType: value})}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder="选择雇佣类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={employmentTypes.FULL_TIME}>{employmentTypes.FULL_TIME}</SelectItem>
                          <SelectItem value={employmentTypes.PART_TIME}>{employmentTypes.PART_TIME}</SelectItem>
                          <SelectItem value={employmentTypes.EXTERNAL}>{employmentTypes.EXTERNAL}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* 教销一体开关 - 放在雇佣类型下方，靠右对齐 */}
                  <div className="flex items-center justify-between mt-4">
                    <Label
                      htmlFor="teaching-sales"
                      className="text-sm font-normal"
                    >
                      教销一体
                    </Label>
                    <Switch
                      id="teaching-sales"
                      checked={newStaffForm.isTeachingSales}
                      onCheckedChange={(checked) => handleTeachingSalesChange(checked)}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 高级设置部分 - 仅当选择了有高级设置的角色时显示 */}
            {newStaffForm.showAdvancedSettings && (
              <div className="space-y-4 border rounded-md p-4 mb-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">高级设置</h3>
                  <Badge variant="outline">可选</Badge>
                </div>

                {/* 教练角色的高级设置 */}
                {newStaffForm.selectedRoles.some(id => {
                  const role = roles.find(r => r.id === id);
                  return role?.category === "教学";
                }) && (
                  <div className="space-y-3">
                    {/* 课程类型选择 */}
                    <div className="space-y-1">
                      <Label className="text-sm">课程类型</Label>
                      <div className="border rounded-md p-2 bg-background">
                        <div className="grid grid-cols-3 gap-1">
                          {Object.entries(courseTypes).map(([key, value]) => (
                            <div key={key} className="flex items-center space-x-1">
                              <Checkbox
                                id={`course-${key}`}
                                checked={newStaffForm.advancedSettings.selectedCourseTypes.includes(value)}
                                onCheckedChange={(checked) => handleCourseTypeChange(value, !!checked)}
                              />
                              <Label
                                htmlFor={`course-${key}`}
                                className="text-sm font-normal cursor-pointer"
                              >
                                {value}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        选择教练可以教授的课程类型，可多选
                      </p>
                    </div>

                    {/* 教练专长和简介 */}
                    <div className="space-y-3 mt-4 border-t pt-4">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">教练专长与简介</Label>
                        <Badge variant="outline">教练专用</Badge>
                      </div>

                      {/* 专长 */}
                      <div className="space-y-2">
                        <Label className="text-sm">专长</Label>
                        <Input
                          placeholder="例如：瑜伽、普拉提、力量训练等"
                          value={newStaffForm.specialties}
                          onChange={(e) => setNewStaffForm({...newStaffForm, specialties: e.target.value})}
                        />
                      </div>

                      {/* 简介 */}
                      <div className="space-y-2">
                        <Label className="text-sm">简介</Label>
                        <Textarea
                          placeholder="请输入教练简介，将展示给会员"
                          value={newStaffForm.bio}
                          onChange={(e) => setNewStaffForm({...newStaffForm, bio: e.target.value})}
                          className="min-h-[100px]"
                        />
                      </div>
                    </div>

                    {/* 私教课时间设置 - 仅当选择了私教课时显示 */}
                    {newStaffForm.advancedSettings.selectedCourseTypes.includes(courseTypes.PRIVATE) && (
                      <div className="space-y-3 mt-4 border-t pt-4">
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">私教课时间设置</Label>
                          <Badge variant="outline">私教专用</Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          请在员工列表中通过"私教时间设置"功能设置教练的可上课时间
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}



            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="mb-2">
                  <Label htmlFor="status" className="flex items-center">
                    状态 <span className="text-red-500 ml-1">*</span>
                  </Label>
                </div>
                <div className="h-10"> {/* 固定高度容器 */}
                  <Select
                    value={newStaffForm.status}
                    onValueChange={(value) => setNewStaffForm({...newStaffForm, status: value})}
                  >
                    <SelectTrigger className="h-10">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="在职">在职</SelectItem>
                      <SelectItem value="离职">离职</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <div className="mb-2">
                  <Label htmlFor="joinDate" className="flex items-center">
                    入职日期 <span className="text-red-500 ml-1">*</span>
                  </Label>
                </div>
                <div className="h-10"> {/* 固定高度容器 */}
                  <Input
                    id="joinDate"
                    type="date"
                    value={newStaffForm.joinDate}
                    onChange={(e) => setNewStaffForm({...newStaffForm, joinDate: e.target.value})}
                    className="h-10"
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddStaffDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              // 验证表单
              if (!newStaffForm.name) {
                toast({
                  title: "请输入员工姓名",
                  variant: "destructive",
                })
                return
              }

              if (!newStaffForm.phone) {
                toast({
                  title: "请输入手机号码",
                  variant: "destructive",
                })
                return
              }

              if (newStaffForm.selectedRoles.length === 0) {
                toast({
                  title: "请至少选择一个角色",
                  variant: "destructive",
                })
                return
              }

              // 创建新员工
              const newStaff = {
                id: staffData.length + 1,
                name: newStaffForm.name,
                gender: newStaffForm.gender,
                avatar: newStaffForm.avatar, // 使用上传的头像或默认头像
                roles: newStaffForm.selectedRoles.map(id => roles.find(r => r.id === id)?.name || ""),
                status: newStaffForm.status,
                phone: newStaffForm.phone,
                email: newStaffForm.email,
                joinDate: newStaffForm.joinDate || new Date().toISOString().split('T')[0],
                employmentType: newStaffForm.employmentType,
                isTeachingSales: newStaffForm.isTeachingSales,
                // 只有当角色包含教练时，才添加专长和简介
                ...(newStaffForm.selectedRoles.some(id => {
                  const role = roles.find(r => r.id === id);
                  return role?.category === "教学";
                }) ? {
                  specialties: newStaffForm.specialties,
                  bio: newStaffForm.bio
                } : {}),
                advancedSettings: newStaffForm.showAdvancedSettings ? {
                  courseTypes: newStaffForm.advancedSettings.selectedCourseTypes,
                  privateCoachingSettings: newStaffForm.advancedSettings.selectedCourseTypes.includes(courseTypes.PRIVATE) ?
                    newStaffForm.advancedSettings.privateCoachingSettings : undefined
                } : { courseTypes: [] }
              }

              // 添加到员工列表
              staffData.push(newStaff)

              // 重置表单
              setNewStaffForm({
                name: "",
                phone: "",
                gender: "女",
                email: "",
                joinDate: "",
                status: "在职",
                selectedRoles: [],
                employmentType: employmentTypes.FULL_TIME,
                isTeachingSales: true, // 默认开启教销一体
                avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=default", // 重置为默认头像
                avatarFile: null,
                specialties: "", // 重置教练专长
                bio: "", // 重置教练简介
                advancedSettings: {
                  selectedCourseTypes: [],
                  privateCoachingSettings: {
                    enabled: false,
                    availableDays: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
                    timeSlots: [
                      { start: "09:00", end: "12:00" },
                      { start: "14:00", end: "18:00" }
                    ]
                  }
                },
                showAdvancedSettings: false
              })

              // 关闭对话框
              setIsAddStaffDialogOpen(false)

              // 显示成功提示
              toast({
                title: "员工添加成功",
                description: `员工 ${newStaffForm.name} 已成功添加`,
              })
            }}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 私教时间设置对话框 */}
      <Dialog open={isPrivateCoachingSettingsDialogOpen} onOpenChange={setIsPrivateCoachingSettingsDialogOpen}>
        <DialogContent className="sm:max-w-[700px] md:max-w-[800px] p-6">
          <DialogHeader className="pb-4">
            <DialogTitle className="text-xl font-semibold">私教时间设置</DialogTitle>
            <DialogDescription className="text-base text-muted-foreground">
              {currentStaff?.name ? `为 ${currentStaff.name} 设置私教课可用时间` : "设置私教课可用时间"}
            </DialogDescription>
          </DialogHeader>

          {/* 设置模式选择 */}
          <div className="border rounded-md p-3 bg-muted/20 mb-4">
            <div className="text-sm font-medium mb-2">选择设置模式：</div>
            <div className="flex space-x-4">
              <div
                className={`flex-1 border rounded-md p-3 cursor-pointer ${scheduleMode === "manual" ? "border-primary bg-primary/5" : "border-muted"}`}
                onClick={() => handleScheduleModeChange("manual")}
              >
                <div className="font-medium mb-1 flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  手动设置模式
                </div>
                <div className="text-sm text-muted-foreground">
                  手动设置每周每天的可用时间，可分别设置本周和下周
                </div>
              </div>
              <div
                className={`flex-1 border rounded-md p-3 cursor-pointer ${scheduleMode === "business-hours" ? "border-primary bg-primary/5" : "border-muted"}`}
                onClick={() => handleScheduleModeChange("business-hours")}
              >
                <div className="font-medium mb-1 flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  基于营业时间模式
                </div>
                <div className="text-sm text-muted-foreground">
                  基于场馆营业时间减去固定休息时间，支持特殊日期/请假设置
                </div>
              </div>
            </div>
          </div>

          {scheduleMode === "manual" ? (
            <div className="py-2">
              <div className="space-y-6">
                {/* 周次选择器 */}
                <div className="flex items-center space-x-4 border rounded-md p-3 bg-muted/20">
                  <div className="text-sm font-medium">选择周次：</div>
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant={privateCoachingSettingsForm.currentWeek === "thisWeek" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleWeekChange("thisWeek")}
                      className={privateCoachingSettingsForm.currentWeek === "thisWeek" ? "bg-primary text-primary-foreground" : ""}
                    >
                      本周
                    </Button>
                    <Button
                      type="button"
                      variant={privateCoachingSettingsForm.currentWeek === "nextWeek" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleWeekChange("nextWeek")}
                      className={privateCoachingSettingsForm.currentWeek === "nextWeek" ? "bg-primary text-primary-foreground" : ""}
                    >
                      下周
                    </Button>
                  </div>
                </div>

                {/* 每个工作日作息时间都一样 */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="all-days-same"
                    checked={privateCoachingSettingsForm.allDaysSame}
                    onCheckedChange={(checked) => handleAllDaysSameChange(!!checked)}
                    className="h-5 w-5 rounded-sm border-primary data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                  />
                  <Label
                    htmlFor="all-days-same"
                    className="text-sm font-normal cursor-pointer"
                  >
                    每个工作日作息时间都一样（私教课时间将沿用到会员预约的私教可选时间，建议真实设置。）
                  </Label>
                </div>

                {/* 工作时间表格 */}
                <div className="border rounded-md overflow-hidden">
                  <table className="w-full">
                    <thead>
                      <tr className="bg-muted/30">
                        <th className="p-2 text-left w-[100px]"></th>
                        {(Object.keys(privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days) as DayKey[]).map(day => (
                          <th key={day} className="p-2 text-center">
                            {day}
                          </th>
                        ))}
                      </tr>
                      <tr>
                        <th className="p-2 text-left">上班</th>
                        {(Object.keys(privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days) as DayKey[]).map(day => (
                          <th key={day} className="p-2 text-center">
                            <Switch
                              checked={privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days[day].enabled}
                              onCheckedChange={(checked) => handleDayEnabledChange(day, checked)}
                              className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                            />
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="p-1 text-left border-t">上班<br/>时间</td>
                        {(Object.keys(privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days) as DayKey[]).map(day => (
                          <td key={day} className="p-1 text-center border-t">
                            <Input
                              type="time"
                              value={privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days[day].workTime.start}
                              onChange={(e) => handleWorkTimeChange(day, 'start', e.target.value)}
                              className="h-8 text-center border-0 border-b focus-visible:ring-0 focus-visible:border-primary px-1"
                              disabled={!privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days[day].enabled}
                            />
                          </td>
                        ))}
                      </tr>
                      <tr>
                        <td className="p-1 text-left border-t">下班<br/>时间</td>
                        {(Object.keys(privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days) as DayKey[]).map(day => (
                          <td key={day} className="p-1 text-center border-t">
                            <Input
                              type="time"
                              value={privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days[day].workTime.end}
                              onChange={(e) => handleWorkTimeChange(day, 'end', e.target.value)}
                              className="h-8 text-center border-0 border-b focus-visible:ring-0 focus-visible:border-primary px-1"
                              disabled={!privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days[day].enabled}
                            />
                          </td>
                        ))}
                      </tr>

                      {/* 休息时间 */}
                      {privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].breakTimes.map((breakTime, index) => (
                        <React.Fragment key={`${breakTime.type}-${index}`}>
                          <tr>
                            <td className="p-1 text-left border-t">
                              {breakTime.name}
                            </td>
                            {(Object.keys(privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days) as DayKey[]).map(day => (
                              <td key={day} className="p-1 text-center border-t">
                                <Input
                                  type="time"
                                  value={breakTime.start}
                                  onChange={(e) => handleBreakTimeChange(index, 'start', e.target.value)}
                                  className="h-8 text-center border-0 border-b focus-visible:ring-0 focus-visible:border-primary px-1"
                                  disabled={!privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days[day].enabled}
                                />
                              </td>
                            ))}
                            <td className="p-1 text-center border-t">
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => removeBreakTime(index)}
                                className="text-muted-foreground hover:text-destructive h-8 w-8 p-0"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                          <tr>
                            <td className="p-1 text-left border-t"></td>
                            {(Object.keys(privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days) as DayKey[]).map(day => (
                              <td key={day} className="p-1 text-center border-t">
                                <Input
                                  type="time"
                                  value={breakTime.end}
                                  onChange={(e) => handleBreakTimeChange(index, 'end', e.target.value)}
                                  className="h-8 text-center border-0 border-b focus-visible:ring-0 focus-visible:border-primary px-1"
                                  disabled={!privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days[day].enabled}
                                />
                              </td>
                            ))}
                            <td className="p-1 text-center border-t"></td>
                          </tr>
                        </React.Fragment>
                      ))}

                      {/* 自定义休息时间 */}
                      {privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].customBreakTimes.map((breakTime, index) => (
                        <React.Fragment key={`custom-${index}`}>
                          <tr>
                            <td className="p-1 text-left border-t">
                              <Input
                                type="text"
                                value={breakTime.name}
                                onChange={(e) => handleCustomBreakTimeChange(index, 'name', e.target.value)}
                                className="h-8 border-0 border-b focus-visible:ring-0 focus-visible:border-primary px-1"
                                placeholder="休息名称"
                              />
                            </td>
                            {(Object.keys(privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days) as DayKey[]).map(day => (
                              <td key={day} className="p-1 text-center border-t">
                                <Input
                                  type="time"
                                  value={breakTime.start}
                                  onChange={(e) => handleCustomBreakTimeChange(index, 'start', e.target.value)}
                                  className="h-8 text-center border-0 border-b focus-visible:ring-0 focus-visible:border-primary px-1"
                                  disabled={!privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days[day].enabled}
                                />
                              </td>
                            ))}
                            <td className="p-1 text-center border-t">
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                onClick={() => removeCustomBreakTime(index)}
                                className="text-muted-foreground hover:text-destructive h-8 w-8 p-0"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                          <tr>
                            <td className="p-1 text-left border-t"></td>
                            {(Object.keys(privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days) as DayKey[]).map(day => (
                              <td key={day} className="p-1 text-center border-t">
                                <Input
                                  type="time"
                                  value={breakTime.end}
                                  onChange={(e) => handleCustomBreakTimeChange(index, 'end', e.target.value)}
                                  className="h-8 text-center border-0 border-b focus-visible:ring-0 focus-visible:border-primary px-1"
                                  disabled={!privateCoachingSettingsForm.weeks[privateCoachingSettingsForm.currentWeek].days[day].enabled}
                                />
                              </td>
                            ))}
                            <td className="p-1 text-center border-t"></td>
                          </tr>
                        </React.Fragment>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* 添加其他休息按钮 */}
                <div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addCustomBreakTime}
                    className="text-primary border-primary hover:bg-primary/10 mt-2"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    新增其他休息
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <BusinessHoursBasedSchedule
              initialData={businessHoursSchedule}
              onSave={saveBusinessHoursSchedule}
              onCancel={() => setIsPrivateCoachingSettingsDialogOpen(false)}
              staffName={currentStaff?.name}
            />
          )}
          {scheduleMode === "manual" && (
            <DialogFooter className="pt-4 gap-3">
              <Button
                variant="outline"
                onClick={() => setIsPrivateCoachingSettingsDialogOpen(false)}
                className="min-w-[80px]"
              >
                取消
              </Button>
              <Button
                onClick={savePrivateCoachingSettings}
                className="min-w-[80px] bg-primary hover:bg-primary/90"
              >
                保存
              </Button>
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>

      {/* 角色编辑对话框 */}
      <Dialog open={isRoleDialogOpen} onOpenChange={setIsRoleDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{currentRole ? (currentRole.isSystem && !currentRole.editable ? "查看角色" : "编辑角色") : "添加角色"}</DialogTitle>
            <DialogDescription>
              {currentRole ? (currentRole.isSystem && !currentRole.editable ? "查看系统预设角色信息" : "修改角色信息和权限") : "创建新的自定义角色"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role-name" className="text-right">
                角色名称
              </Label>
              <Input
                id="role-name"
                defaultValue={currentRole?.name || ""}
                className="col-span-3"
                disabled={currentRole?.isSystem && !currentRole?.editable}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role-description" className="text-right">
                角色描述
              </Label>
              <Textarea
                id="role-description"
                defaultValue={currentRole?.description || ""}
                className="col-span-3"
                disabled={currentRole?.isSystem && !currentRole?.editable}
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right pt-2">
                权限设置
              </Label>
              <div className="col-span-3 space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="perm-course" disabled={currentRole?.isSystem && !currentRole?.editable} defaultChecked={currentRole?.name === "教练"} />
                    <Label htmlFor="perm-course">课程管理权限</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="perm-member" disabled={currentRole?.isSystem && !currentRole?.editable} defaultChecked={currentRole?.name === "会籍顾问"} />
                    <Label htmlFor="perm-member">会员管理权限</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="perm-finance" disabled={currentRole?.isSystem && !currentRole?.editable} />
                    <Label htmlFor="perm-finance">财务管理权限</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="perm-staff" disabled={currentRole?.isSystem && !currentRole?.editable} defaultChecked={currentRole?.name === "店长"} />
                    <Label htmlFor="perm-staff">员工管理权限</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="perm-system" disabled={currentRole?.isSystem && !currentRole?.editable} defaultChecked={currentRole?.name === "超级管理员" || currentRole?.name === "店长"} />
                    <Label htmlFor="perm-system">系统设置权限</Label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRoleDialogOpen(false)}>
              {currentRole?.isSystem && !currentRole?.editable ? "关闭" : "取消"}
            </Button>
            {!(currentRole?.isSystem && !currentRole?.editable) && (
              <Button onClick={() => {
                // 这里添加保存角色的逻辑
                setIsRoleDialogOpen(false);
                // 显示成功提示
                toast({
                  title: currentRole ? "角色已更新" : "角色已添加",
                  description: `角色 "${currentRole?.name || "自定义角色"}" ${currentRole ? "已更新" : "已添加"}`,
                })
              }}>
                保存
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
