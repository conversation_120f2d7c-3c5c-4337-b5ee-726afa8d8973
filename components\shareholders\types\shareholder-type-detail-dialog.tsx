"use client"

import { useState, useRef } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Info,
  Users,
  Wallet,
  FileText,
  BarChart3,
  Pencil,
  Download,
  Share2,
  FileSignature,
  Eye,
  ExternalLink,
} from "lucide-react"
import { ShareholderTypeDialog } from "./shareholder-type-dialog"
import { toast } from "@/hooks/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from "recharts"

interface ShareholderTypeDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  type: any
}

export function ShareholderTypeDetailDialog({
  open,
  onOpenChange,
  type,
}: ShareholderTypeDetailDialogProps) {
  const [chartType, setChartType] = useState("monthly")
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [activeTab, setActiveTab] = useState("info")
  const chartRef = useRef(null)

  // 处理编辑按钮点击
  const handleEdit = () => {
    setShowEditDialog(true)
  }

  // 处理导出数据
  const handleExportData = () => {
    let exportData
    let fileName

    // 根据当前活动的标签页决定导出的数据
    switch (activeTab) {
      case "info":
        exportData = {
          typeInfo: {
            name: type.name,
            description: type.description,
            color: type.color,
            createdAt: type.createdAt,
            memberCount: type.memberCount,
          },
          dividendInfo: {
            rules: type.dividendRules,
            ratio: type.dividendRatio,
            amount: type.dividendAmount,
          },
          benefits: {
            product: type.productBenefits,
            service: type.serviceBenefits,
            discount: type.discountBenefits,
            coupon: type.couponBenefits,
            other: type.otherBenefits,
          },
          performance: chartType === "monthly"
            ? monthlyPerformanceData
            : chartType === "ranking"
              ? shareholderPerformanceData
              : customerConsumptionData
        }
        fileName = `${type.name}-基本信息.json`
        break
      case "shareholders":
        exportData = shareholders
        fileName = `${type.name}-股东列表.json`
        break
      case "dividends":
        exportData = dividendRecords
        fileName = `${type.name}-分红记录.json`
        break
      default:
        exportData = { type }
        fileName = `${type.name}-全部数据.json`
    }

    // 创建下载链接
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`

    const link = document.createElement('a')
    link.setAttribute('href', dataUri)
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "导出成功",
      description: `数据已导出到文件 ${fileName}`,
    })
  }

  if (!type) return null

  // 模拟股东数据
  const shareholders = [
    {
      id: "1",
      name: "张三",
      phone: "13800138001",
      joinDate: "2023-05-15",
      referrals: 5,
      totalDividend: 2500,
      avatar: "/avatars/01.png",
    },
    {
      id: "2",
      name: "李四",
      phone: "13900139001",
      joinDate: "2023-04-20",
      referrals: 0,
      totalDividend: 5000,
      avatar: "/avatars/02.png",
    },
    {
      id: "3",
      name: "王五",
      phone: "13700137001",
      joinDate: "2023-06-01",
      referrals: 15,
      totalDividend: 3750,
      avatar: "/avatars/03.png",
    },
  ]

  // 模拟分红记录数据
  const dividendRecords = [
    {
      id: "1",
      period: "2023年5月",
      shareholderCount: 5,
      totalAmount: 3960,
      avgAmount: 792,
      status: "已发放",
    },
    {
      id: "2",
      period: "2023年4月",
      shareholderCount: 4,
      totalAmount: 3200,
      avgAmount: 800,
      status: "已发放",
    },
    {
      id: "3",
      period: "2023年3月",
      shareholderCount: 4,
      totalAmount: 3600,
      avgAmount: 900,
      status: "已发放",
    },
  ]

  // 模拟业绩统计数据 - 月度业绩
  const monthlyPerformanceData = [
    { month: "1月", 引流客户: 5, 分红金额: 1200 },
    { month: "2月", 引流客户: 8, 分红金额: 1800 },
    { month: "3月", 引流客户: 12, 分红金额: 2400 },
    { month: "4月", 引流客户: 10, 分红金额: 2000 },
    { month: "5月", 引流客户: 15, 分红金额: 3000 },
    { month: "6月", 引流客户: 18, 分红金额: 3600 },
  ]

  // 模拟业绩统计数据 - 股东业绩排名
  const shareholderPerformanceData = [
    { name: "张三", 引流客户: 15, 分红金额: 3750 },
    { name: "李四", 引流客户: 8, 分红金额: 2000 },
    { name: "王五", 引流客户: 12, 分红金额: 3000 },
    { name: "赵六", 引流客户: 6, 分红金额: 1500 },
    { name: "钱七", 引流客户: 10, 分红金额: 2500 },
  ]

  // 模拟业绩统计数据 - 客户消费类型分布
  const customerConsumptionData = [
    { name: "会员卡", value: 45 },
    { name: "单次课程", value: 25 },
    { name: "私教课", value: 20 },
    { name: "产品购买", value: 10 },
  ]

  // 图表颜色
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>股东类型详情</DialogTitle>
            <DialogDescription>
              查看股东类型的详细信息、股东列表和分红记录
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center gap-4 py-4">
            <div className="h-12 w-12 rounded-full flex items-center justify-center text-white" style={{ backgroundColor: type.color }}>
              <Users className="h-6 w-6" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">{type.name}</h2>
              <p className="text-sm text-muted-foreground">{type.description}</p>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-4">
              <TabsTrigger value="info">基本信息</TabsTrigger>
              <TabsTrigger value="shareholders">股东列表</TabsTrigger>
              <TabsTrigger value="dividends">分红记录</TabsTrigger>
              <TabsTrigger value="contracts">合同模板</TabsTrigger>
            </TabsList>

          <TabsContent value="info" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>类型信息</CardTitle>
                <CardDescription>股东类型的基本信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-1">类型名称</h3>
                  <p className="text-sm">{type.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">类型描述</h3>
                  <p className="text-sm">{type.description}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">创建时间</h3>
                  <p className="text-sm">{type.createdAt}</p>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">股东数量</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{type.memberCount}人</div>
                  <p className="text-xs text-muted-foreground">
                    占总股东数的15%
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">累计分红</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">¥12,500.00</div>
                  <p className="text-xs text-muted-foreground">
                    平均每人¥1,562.50
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>分红权益</CardTitle>
                <CardDescription>该类型股东的分红规则和权益</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-1">分红规则</h3>
                  <p className="text-sm">{type.dividendRules}</p>
                </div>
                {type.dividendRatio && (
                  <div>
                    <h3 className="text-sm font-medium mb-1">分红比例</h3>
                    <p className="text-sm">{type.dividendRatio}</p>
                  </div>
                )}
                {type.dividendAmount && (
                  <div>
                    <h3 className="text-sm font-medium mb-1">分红金额</h3>
                    <p className="text-sm">{type.dividendAmount}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>其他权益</CardTitle>
                <CardDescription>该类型股东的其他权益和福利</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 p-4 border rounded-lg">
                  <h2 className="text-xl font-bold">其他权益</h2>
                  <p className="text-sm text-muted-foreground mb-2">该类型股东的其他权益和福利</p>

                  {type.productBenefits && (
                    <div className="mt-4">
                      <h3 className="text-base font-semibold mb-2">产品权益</h3>
                      <p className="text-sm">{type.productBenefits}</p>
                    </div>
                  )}

                  {type.serviceBenefits && (
                    <div className="mt-4">
                      <h3 className="text-base font-semibold mb-2">服务权益</h3>
                      <p className="text-sm">{type.serviceBenefits}</p>
                    </div>
                  )}

                  {type.discountBenefits && (
                    <div className="mt-4">
                      <h3 className="text-base font-semibold mb-2">折扣权益</h3>
                      <p className="text-sm">{type.discountBenefits}</p>
                    </div>
                  )}

                  {type.couponBenefits && (
                    <div className="mt-4">
                      <h3 className="text-base font-semibold mb-2">优惠券权益</h3>
                      <p className="text-sm">{type.couponBenefits}</p>
                    </div>
                  )}

                  {type.otherBenefits && (
                    <div className="mt-4">
                      <h3 className="text-base font-semibold mb-2">其他权益</h3>
                      <p className="text-sm">{type.otherBenefits}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>业绩统计</CardTitle>
                    <CardDescription>该类型股东的业绩统计</CardDescription>
                  </div>
                  <Select
                    value={chartType}
                    onValueChange={(value) => setChartType(value)}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="选择统计类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">月度业绩</SelectItem>
                      <SelectItem value="ranking">股东排名</SelectItem>
                      <SelectItem value="consumption">消费分布</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs value={chartType} onValueChange={setChartType} className="w-full">
                  <TabsList className="grid grid-cols-3 mb-4">
                    <TabsTrigger value="monthly">月度业绩</TabsTrigger>
                    <TabsTrigger value="ranking">股东排名</TabsTrigger>
                    <TabsTrigger value="consumption">消费分布</TabsTrigger>
                  </TabsList>

                  {/* 月度业绩图表 */}
                  <TabsContent value="monthly">
                    <div className="h-60">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={monthlyPerformanceData}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis yAxisId="left" orientation="left" />
                          <YAxis yAxisId="right" orientation="right" />
                          <Tooltip />
                          <Legend />
                          <Bar yAxisId="left" dataKey="引流客户" fill="#8884d8" name="引流客户数" />
                          <Bar yAxisId="right" dataKey="分红金额" fill="#82ca9d" name="分红金额(元)" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </TabsContent>

                  {/* 股东排名图表 */}
                  <TabsContent value="ranking">
                    <div className="h-60">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={shareholderPerformanceData}
                          layout="vertical"
                          margin={{
                            top: 20,
                            right: 30,
                            left: 60,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" />
                          <YAxis dataKey="name" type="category" />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="引流客户" fill="#8884d8" name="引流客户数" />
                          <Bar dataKey="分红金额" fill="#82ca9d" name="分红金额(元)" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </TabsContent>

                  {/* 消费分布图表 */}
                  <TabsContent value="consumption">
                    <div className="h-60">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={customerConsumptionData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {customerConsumptionData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="shareholders">
            <Card>
              <CardHeader>
                <CardTitle>股东列表</CardTitle>
                <CardDescription>该类型下的所有股东</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>股东信息</TableHead>
                      <TableHead>加入日期</TableHead>
                      <TableHead>引流客户</TableHead>
                      <TableHead>累计分红</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {shareholders.map((shareholder) => (
                      <TableRow key={shareholder.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={shareholder.avatar} alt={shareholder.name} />
                              <AvatarFallback>{shareholder.name[0]}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{shareholder.name}</div>
                              <div className="text-sm text-muted-foreground">{shareholder.phone}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{shareholder.joinDate}</TableCell>
                        <TableCell>{shareholder.referrals}人</TableCell>
                        <TableCell>¥{shareholder.totalDividend.toFixed(2)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="dividends">
            <Card>
              <CardHeader>
                <CardTitle>分红记录</CardTitle>
                <CardDescription>该类型的历史分红记录</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>分红周期</TableHead>
                      <TableHead>股东数量</TableHead>
                      <TableHead>分红总额</TableHead>
                      <TableHead>人均分红</TableHead>
                      <TableHead>状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dividendRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>{record.period}</TableCell>
                        <TableCell>{record.shareholderCount}人</TableCell>
                        <TableCell>¥{record.totalAmount.toFixed(2)}</TableCell>
                        <TableCell>¥{record.avgAmount.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant={record.status === "已发放" ? "default" : "secondary"}>
                            {record.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contracts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>合同模板</CardTitle>
                <CardDescription>该股东类型可用的合同模板</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="border-2 border-primary/20">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between">
                        <CardTitle className="text-base">{type.name}协议</CardTitle>
                        <Badge>默认模板</Badge>
                      </div>
                      <CardDescription>适用于{type.name}的权益和分红协议</CardDescription>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm text-muted-foreground">
                        该合同模板包含了{type.name}的所有权益和分红规则，是新股东加入时的标准协议。
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button variant="outline" size="sm">
                        <Eye className="mr-2 h-4 w-4" />
                        预览
                      </Button>
                      <Button size="sm">
                        <FileSignature className="mr-2 h-4 w-4" />
                        使用模板
                      </Button>
                    </CardFooter>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">{type.name}权益确认书</CardTitle>
                      <CardDescription>确认{type.name}的权益详情</CardDescription>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm text-muted-foreground">
                        该文档详细列出了{type.name}可享受的所有权益，包括分红比例、会员卡折扣等。
                      </p>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button variant="outline" size="sm">
                        <Eye className="mr-2 h-4 w-4" />
                        预览
                      </Button>
                      <Button size="sm">
                        <FileSignature className="mr-2 h-4 w-4" />
                        使用模板
                      </Button>
                    </CardFooter>
                  </Card>
                </div>

                <div className="mt-4 flex justify-center">
                  <Button variant="outline" className="w-full max-w-md">
                    <ExternalLink className="mr-2 h-4 w-4" />
                    查看更多合同模板
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleExportData}>
              <Download className="mr-2 h-4 w-4" />
              导出数据
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleEdit}>
              <Pencil className="mr-2 h-4 w-4" />
              编辑
            </Button>
            <Button onClick={() => onOpenChange(false)}>关闭</Button>
          </div>
        </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <ShareholderTypeDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        type={type}
      />
    </>
  )
}
