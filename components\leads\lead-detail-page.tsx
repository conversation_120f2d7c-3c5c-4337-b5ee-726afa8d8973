"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  PhoneCall, 
  Mail, 
  Calendar, 
  Clock, 
  Edit, 
  UserCheck, 
  MessageSquare, 
  Plus, 
  FileText, 
  Tag,
  ArrowLeft,
  Globe,
  MapPin,
  Star,
  X
} from "lucide-react"
import { LeadFollowUpForm } from "@/components/leads/lead-follow-up-form"
import Link from "next/link"

interface LeadDetailPageProps {
  lead: any
  onClose?: () => void
}

export function LeadDetailPage({ lead, onClose }: LeadDetailPageProps) {
  const [activeTab, setActiveTab] = useState("followup")
  const [showFollowUpForm, setShowFollowUpForm] = useState(false)

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return <Badge variant="outline">新获取</Badge>
      case "contacted":
        return <Badge variant="secondary">已联系</Badge>
      case "qualified":
        return <Badge variant="default">已确认</Badge>
      case "negotiating":
        return <Badge variant="warning">洽谈中</Badge>
      case "converted":
        return <Badge variant="success">已转化</Badge>
      case "lost":
        return <Badge variant="destructive">已流失</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 模拟跟进记录数据
  const followUps = [
    {
      id: 1,
      date: "2023-12-15",
      time: "14:30",
      type: "phone",
      content: "电话联系客户，客户表示对我们的瑜伽课程很感兴趣，但目前时间安排有些紧张，约定下周再次联系。",
      staff: "张三",
      nextFollowUp: "2023-12-22",
    },
    {
      id: 2,
      date: "2023-12-22",
      time: "16:00",
      type: "visit",
      content: "客户到店参观，对场地环境和教练资质表示满意，对价格有些顾虑，已提供会员卡折扣方案，客户考虑中。",
      staff: "李四",
      nextFollowUp: "2023-12-25",
    },
    {
      id: 3,
      date: "2023-12-25",
      time: "10:15",
      type: "wechat",
      content: "通过微信发送了课程表和优惠活动信息，客户表示会尽快决定。",
      staff: "张三",
      nextFollowUp: "2023-12-28",
    },
  ]

  return (
    <div className="bg-background">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          {onClose && (
            <Button variant="ghost" size="icon" onClick={onClose} className="mr-2">
              <X className="h-5 w-5" />
            </Button>
          )}
          <Link href="/leads">
            <Button variant="ghost" size="sm" className="gap-1">
              <ArrowLeft className="h-4 w-4" />
              返回潜客列表
            </Button>
          </Link>
          <h1 className="text-xl font-semibold ml-4">潜客详情</h1>
          <div className="ml-3">{getStatusBadge(lead.status)}</div>
        </div>
        <div className="flex gap-2">
          <Button size="sm" variant="outline">
            <Edit className="mr-2 h-4 w-4" />
            编辑信息
          </Button>
          <Button size="sm">
            <UserCheck className="mr-2 h-4 w-4" />
            转为会员
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* 左侧信息栏 */}
        <div className="md:col-span-1">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col items-center text-center mb-6">
                <Avatar className="h-24 w-24 mb-4">
                  <AvatarFallback className="text-2xl bg-primary/10 text-primary">{lead.name[0]}</AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-semibold">{lead.name}</h2>
                <p className="text-muted-foreground">{lead.gender === "male" ? "男" : "女"}</p>
                <div className="flex items-center mt-2">
                  {Array(5).fill(0).map((_, i) => (
                    <Star key={i} className={`h-4 w-4 ${i < 4 ? "text-yellow-400" : "text-muted-foreground/30"}`} />
                  ))}
                </div>
              </div>
              
              <Separator className="my-4" />
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <PhoneCall className="mr-3 h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">手机号码</p>
                    <p>{lead.phone}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Mail className="mr-3 h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">电子邮箱</p>
                    <p className="truncate max-w-[200px]">{lead.email || "未提供"}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Globe className="mr-3 h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">来源渠道</p>
                    <p>{lead.source}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <Calendar className="mr-3 h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">获取时间</p>
                    <p>{lead.createdAt}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <UserCheck className="mr-3 h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">负责人</p>
                    <p>{lead.assignedTo}</p>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <MapPin className="mr-3 h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">所在区域</p>
                    <p>{lead.location || "未知"}</p>
                  </div>
                </div>
              </div>
              
              <Separator className="my-4" />
              
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">备注</p>
                <p className="text-sm">{lead.notes || "对瑜伽课程很感兴趣，特别是阴瑜伽和流瑜伽，希望能够改善睡眠质量和减轻工作压力。"}</p>
              </div>
              
              <div className="mt-6 space-y-2">
                <Button className="w-full" variant="outline">
                  <PhoneCall className="mr-2 h-4 w-4" />
                  拨打电话
                </Button>
                <Button className="w-full" variant="outline">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  发送消息
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* 右侧内容区 */}
        <div className="md:col-span-3">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="followup">跟进记录</TabsTrigger>
              <TabsTrigger value="interests">兴趣偏好</TabsTrigger>
              <TabsTrigger value="activities">活动记录</TabsTrigger>
            </TabsList>
            
            <TabsContent value="followup" className="space-y-4 mt-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">跟进记录</h3>
                <Button onClick={() => setShowFollowUpForm(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  添加跟进
                </Button>
              </div>
              
              {showFollowUpForm && (
                <Card>
                  <CardHeader>
                    <CardTitle>添加跟进记录</CardTitle>
                    <CardDescription>记录与潜客的沟通内容和后续计划</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <LeadFollowUpForm 
                      leadId={lead.id} 
                      onSuccess={() => setShowFollowUpForm(false)} 
                      onCancel={() => setShowFollowUpForm(false)}
                    />
                  </CardContent>
                </Card>
              )}
              
              <div className="space-y-4">
                {followUps.map((followUp) => (
                  <Card key={followUp.id} className="border-l-4 border-l-primary">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>{followUp.date}</span>
                          <Clock className="ml-4 mr-2 h-4 w-4 text-muted-foreground" />
                          <span>{followUp.time}</span>
                        </div>
                        <Badge variant="outline">
                          {followUp.type === "phone" && "电话沟通"}
                          {followUp.type === "wechat" && "微信沟通"}
                          {followUp.type === "visit" && "到店参观"}
                          {followUp.type === "email" && "邮件沟通"}
                        </Badge>
                      </div>
                      
                      <div className="mt-2">
                        <p className="whitespace-pre-line">{followUp.content}</p>
                      </div>
                      
                      <div className="flex justify-between items-center mt-3 text-sm">
                        <div className="text-muted-foreground">
                          跟进人: <span className="font-medium">{followUp.staff}</span>
                        </div>
                        <div className="text-muted-foreground">
                          下次跟进: <span className="font-medium">{followUp.nextFollowUp}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="interests" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>兴趣偏好</CardTitle>
                  <CardDescription>潜客的兴趣和偏好分析</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">感兴趣的课程</h4>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="secondary">阴瑜伽</Badge>
                        <Badge variant="secondary">流瑜伽</Badge>
                        <Badge variant="outline">普拉提</Badge>
                        <Badge variant="outline">冥想课</Badge>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">关注的教练</h4>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="secondary">王教练</Badge>
                        <Badge variant="outline">李教练</Badge>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">预算范围</h4>
                      <Badge>3000-5000元</Badge>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">练习目标</h4>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="secondary">改善睡眠</Badge>
                        <Badge variant="secondary">减轻压力</Badge>
                        <Badge variant="outline">塑形</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="activities" className="mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>活动记录</CardTitle>
                  <CardDescription>潜客参与的活动和互动记录</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">暂无活动记录</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
