"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Star, Calendar, BarChart, Phone, Mail, Briefcase, GraduationCap, DollarSign } from "lucide-react"

interface CoachDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  coach: any
}

export function CoachDetailDialog({ open, onOpenChange, coach }: CoachDetailDialogProps) {
  if (!coach) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>教练详情</DialogTitle>
          <DialogDescription>查看教练的详细信息和相关数据</DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="col-span-1">
            <div className="flex flex-col items-center text-center">
              <Avatar className="h-24 w-24">
                <AvatarImage src={coach.avatar} alt={coach.name} />
                <AvatarFallback className="text-xl">{coach.name[0]}</AvatarFallback>
              </Avatar>
              <h2 className="mt-3 text-xl font-semibold">{coach.name}</h2>
              <p className="text-sm text-muted-foreground">ID: {coach.id}</p>
              <div className="mt-2 flex items-center">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="ml-1 font-medium">{coach.rating}</span>
              </div>
              <Badge
                className="mt-3"
                variant={coach.status === "active" ? "default" : coach.status === "leave" ? "secondary" : "outline"}
              >
                {coach.status === "active" ? "在职" : coach.status === "leave" ? "请假中" : "已离职"}
              </Badge>
              <div className="mt-4 flex flex-wrap justify-center gap-1">
                {coach.specialty.map((spec: string) => (
                  <Badge key={spec} variant="outline" className="text-xs">
                    {spec}
                  </Badge>
                ))}
              </div>

              <div className="mt-6 w-full space-y-3">
                <div className="flex items-center">
                  <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>{coach.phone}</span>
                </div>
                <div className="flex items-center">
                  <Mail className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>{coach.email}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>入职日期: {coach.joinDate}</span>
                </div>
              </div>

              <div className="mt-6 flex w-full justify-between gap-2">
                <Button variant="outline" className="flex-1">
                  <Phone className="mr-2 h-4 w-4" />
                  电话
                </Button>
                <Button variant="outline" className="flex-1">
                  <Mail className="mr-2 h-4 w-4" />
                  邮件
                </Button>
              </div>
            </div>
          </div>

          <div className="col-span-2">
            <Tabs defaultValue="info">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="info">基本信息</TabsTrigger>
                <TabsTrigger value="schedule">排课情况</TabsTrigger>
                <TabsTrigger value="performance">业绩统计</TabsTrigger>
                <TabsTrigger value="hourlyRates">课时费设置</TabsTrigger>
                <TabsTrigger value="reviews">学员评价</TabsTrigger>
              </TabsList>
              <TabsContent value="info" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>个人资料</CardTitle>
                    <CardDescription>教练的详细个人资料和背景信息</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <Label className="text-muted-foreground">姓名</Label>
                        <p className="font-medium">{coach.name}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">性别</Label>
                        <p className="font-medium">女</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">年龄</Label>
                        <p className="font-medium">28</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">身份证号</Label>
                        <p className="font-medium">310******1234</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">联系电话</Label>
                        <p className="font-medium">{coach.phone}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">电子邮箱</Label>
                        <p className="font-medium">{coach.email}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">住址</Label>
                        <p className="font-medium">上海市浦东新区张江高科技园区</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">紧急联系人</Label>
                        <p className="font-medium">张先生 (138****5678)</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>专业背景</CardTitle>
                    <CardDescription>教练的专业资质和工作经验</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className="text-muted-foreground">教学专长</Label>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {coach.specialty.map((spec: string) => (
                          <Badge key={spec} variant="outline">
                            {spec}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">资质证书</Label>
                      <div className="mt-1 space-y-2">
                        <div className="flex items-center">
                          <GraduationCap className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>国际瑜伽联盟认证教练 (RYT-200)</span>
                        </div>
                        <div className="flex items-center">
                          <GraduationCap className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>阴瑜伽高级教练认证</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">工作经验</Label>
                      <div className="mt-1 space-y-2">
                        <div className="flex items-start">
                          <Briefcase className="mr-2 h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">瑜伽时光工作室</p>
                            <p className="text-sm text-muted-foreground">2020-2023 | 高级瑜伽教练</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Briefcase className="mr-2 h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">和平瑜伽中心</p>
                            <p className="text-sm text-muted-foreground">2018-2020 | 瑜伽教练</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>合同信息</CardTitle>
                    <CardDescription>教练的合同和薪资信息</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <Label className="text-muted-foreground">入职日期</Label>
                        <p className="font-medium">{coach.joinDate}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">合同到期日</Label>
                        <p className="font-medium">2026-05-14</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">薪资类型</Label>
                        <p className="font-medium">底薪+课时费+提成</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">底薪</Label>
                        <p className="font-medium">¥5,000/月</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">课时费</Label>
                        <p className="font-medium">¥200/课时</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">提成比例</Label>
                        <p className="font-medium">私教课销售额的15%</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="schedule">
                <Card>
                  <CardHeader>
                    <CardTitle>本周排课</CardTitle>
                    <CardDescription>教练本周的课程安排</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-7 gap-2">
                        {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, index) => (
                          <div key={index} className="text-center font-medium">
                            {day}
                          </div>
                        ))}
                        {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, index) => (
                          <div key={index} className="h-32 rounded-md border p-2">
                            {index === 0 && (
                              <div className="mb-2 rounded bg-blue-100 p-1 text-xs">
                                <div className="font-medium">基础瑜伽</div>
                                <div>10:00-11:30</div>
                              </div>
                            )}
                            {index === 2 && (
                              <div className="mb-2 rounded bg-green-100 p-1 text-xs">
                                <div className="font-medium">高级瑜伽</div>
                                <div>14:00-15:30</div>
                              </div>
                            )}
                            {index === 4 && (
                              <div className="mb-2 rounded bg-purple-100 p-1 text-xs">
                                <div className="font-medium">阴瑜伽</div>
                                <div>16:00-17:30</div>
                              </div>
                            )}
                            {index === 5 && (
                              <>
                                <div className="mb-2 rounded bg-yellow-100 p-1 text-xs">
                                  <div className="font-medium">基础瑜伽</div>
                                  <div>09:00-10:30</div>
                                </div>
                                <div className="rounded bg-pink-100 p-1 text-xs">
                                  <div className="font-medium">高级瑜伽</div>
                                  <div>15:00-16:30</div>
                                </div>
                              </>
                            )}
                          </div>
                        ))}
                      </div>
                      <div className="flex justify-end">
                        <Button variant="outline">
                          <Calendar className="mr-2 h-4 w-4" />
                          查看完整排课
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="performance">
                <Card>
                  <CardHeader>
                    <CardTitle>业绩概览</CardTitle>
                    <CardDescription>教练的课程和学员数据统计</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardDescription>总课程数</CardDescription>
                          <CardTitle className="text-2xl">{coach.courses}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-xs text-muted-foreground">
                            较上月 <span className="text-green-500">+2</span>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardDescription>总学员数</CardDescription>
                          <CardTitle className="text-2xl">{coach.students}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-xs text-muted-foreground">
                            较上月 <span className="text-green-500">+5</span>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardDescription>平均评分</CardDescription>
                          <CardTitle className="text-2xl">{coach.rating}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-xs text-muted-foreground">
                            较上月 <span className="text-green-500">+0.1</span>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardDescription>本月课时</CardDescription>
                          <CardTitle className="text-2xl">24</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-xs text-muted-foreground">
                            较上月 <span className="text-green-500">+3</span>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardDescription>本月收入</CardDescription>
                          <CardTitle className="text-2xl">¥12,800</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-xs text-muted-foreground">
                            较上月 <span className="text-green-500">+¥1,200</span>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="pb-2">
                          <CardDescription>出勤率</CardDescription>
                          <CardTitle className="text-2xl">98%</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-xs text-muted-foreground">
                            较上月 <span className="text-green-500">+2%</span>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                    <div className="mt-6 flex justify-end">
                      <Button variant="outline">
                        <BarChart className="mr-2 h-4 w-4" />
                        查看详细报表
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="hourlyRates">
                <Card>
                  <CardHeader>
                    <CardTitle>课时费设置</CardTitle>
                    <CardDescription>教练的课程课时费标准</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-sm font-medium mb-3">默认课时费（按课程类型）</h3>
                        <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                          {[
                            { type: "团课", color: "#4285F4", rate: 100 },
                            { type: "小班课", color: "#34A853", rate: 120 },
                            { type: "精品课", color: "#FBBC05", rate: 150 },
                            { type: "私教课", color: "#EA4335", rate: 200 },
                            { type: "教培课", color: "#9C27B0", rate: 180 },
                          ].map((item, index) => (
                            <div key={index} className="flex items-center justify-between rounded-md border p-3">
                              <div className="flex items-center gap-2">
                                <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                                <span>{item.type}</span>
                              </div>
                              <div className="flex items-center gap-1 font-medium">
                                <DollarSign className="h-4 w-4 text-muted-foreground" />
                                <span>¥{item.rate}/课时</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium mb-3">具体课程课时费</h3>
                        <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                          {[
                            { course: "哈他瑜伽初级", type: "团课", color: "#4285F4", rate: 120 },
                            { course: "高级瑜伽进阶", type: "精品课", color: "#FBBC05", rate: 180 },
                          ].map((item, index) => (
                            <div key={index} className="flex items-center justify-between rounded-md border p-3">
                              <div>
                                <div className="flex items-center gap-2">
                                  <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                                  <span className="font-medium">{item.course}</span>
                                </div>
                                <div className="text-xs text-muted-foreground mt-1">{item.type}</div>
                              </div>
                              <div className="flex items-center gap-1 font-medium">
                                <DollarSign className="h-4 w-4 text-muted-foreground" />
                                <span>¥{item.rate}/课时</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="flex justify-end">
                        <Button variant="outline" onClick={() => window.location.href = "/courses/hourly-rates"}>
                          <DollarSign className="mr-2 h-4 w-4" />
                          管理课时费
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="reviews">
                <Card>
                  <CardHeader>
                    <CardTitle>学员评价</CardTitle>
                    <CardDescription>学员对教练的评价和反馈</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="text-2xl font-bold">{coach.rating}</div>
                          <div className="flex">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`h-5 w-5 ${
                                  i < Math.floor(coach.rating)
                                    ? "fill-yellow-400 text-yellow-400"
                                    : i < coach.rating
                                      ? "fill-yellow-400 text-yellow-400 opacity-50"
                                      : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                          <div className="text-sm text-muted-foreground">基于 68 条评价</div>
                        </div>
                        <Button variant="outline" size="sm">
                          查看全部评价
                        </Button>
                      </div>

                      <div className="space-y-3">
                        {[
                          {
                            name: "李同学",
                            avatar: "/placeholder.svg?height=32&width=32",
                            rating: 5,
                            date: "2025-03-20",
                            comment: "张教练非常专业，课程安排合理，动作讲解清晰，很有耐心。",
                            course: "基础瑜伽入门",
                          },
                          {
                            name: "王同学",
                            avatar: "/placeholder.svg?height=32&width=32",
                            rating: 4,
                            date: "2025-03-15",
                            comment: "教练很专业，但课程节奏有点快，希望能更适合初学者。",
                            course: "高级瑜伽进阶",
                          },
                          {
                            name: "赵同学",
                            avatar: "/placeholder.svg?height=32&width=32",
                            rating: 5,
                            date: "2025-03-10",
                            comment: "张教练的课非常棒，每次都能学到新东西，而且氛围很好。",
                            course: "阴瑜伽放松",
                          },
                        ].map((review, index) => (
                          <div key={index} className="rounded-lg border p-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={review.avatar} alt={review.name} />
                                  <AvatarFallback>{review.name[0]}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{review.name}</div>
                                  <div className="text-xs text-muted-foreground">{review.course}</div>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="flex">
                                  {Array.from({ length: 5 }).map((_, i) => (
                                    <Star
                                      key={i}
                                      className={`h-3 w-3 ${
                                        i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                                      }`}
                                    />
                                  ))}
                                </div>
                                <div className="text-xs text-muted-foreground">{review.date}</div>
                              </div>
                            </div>
                            <p className="mt-2 text-sm">{review.comment}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
          <Button>编辑信息</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

