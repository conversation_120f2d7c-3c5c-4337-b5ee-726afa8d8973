import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 获取会员详情
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    console.log('获取会员详情，ID:', id);

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 查询会员基本信息
      const [memberResult] = await connection.execute(
        `SELECT 
          m.*,
          COUNT(mc.id) as total_cards,
          COUNT(CASE WHEN mc.status = 'active' THEN 1 END) as active_cards,
          COUNT(b.id) as total_bookings,
          COUNT(CASE WHEN b.status = 'completed' THEN 1 END) as completed_bookings
        FROM members m
        LEFT JOIN member_cards mc ON m.id = mc.member_id
        LEFT JOIN bookings b ON m.id = b.member_id
        WHERE m.id = ?
        GROUP BY m.id`,
        [parseInt(id)]
      );

      if ((memberResult as any[]).length === 0) {
        return NextResponse.json({
          code: 404,
          msg: '会员不存在',
          data: null
        }, { status: 404 });
      }

      const member = (memberResult as any[])[0];

      // 查询会员卡信息
      const [cardsResult] = await connection.execute(
        `SELECT 
          mc.*,
          mct.name as card_type_name,
          mct.card_category,
          mct.color
        FROM member_cards mc
        LEFT JOIN member_card_types mct ON mc.card_type_id = mct.id
        WHERE mc.member_id = ?
        ORDER BY mc.created_at DESC`,
        [parseInt(id)]
      );

      // 查询最近预约记录
      const [bookingsResult] = await connection.execute(
        `SELECT 
          b.*,
          c.title as course_name,
          c.duration
        FROM bookings b
        LEFT JOIN course c ON b.course_id = c.id
        WHERE b.member_id = ?
        ORDER BY b.booking_time DESC
        LIMIT 10`,
        [parseInt(id)]
      );

      // 查询订单记录
      const [ordersResult] = await connection.execute(
        `SELECT 
          o.*,
          GROUP_CONCAT(oi.item_name) as items
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.member_id = ?
        GROUP BY o.id
        ORDER BY o.created_at DESC
        LIMIT 10`,
        [parseInt(id)]
      );

      const memberDetail = {
        ...member,
        cards: cardsResult,
        recent_bookings: bookingsResult,
        recent_orders: ordersResult
      };

      console.log('获取会员详情成功');

      return NextResponse.json({
        code: 200,
        data: memberDetail,
        msg: '获取会员详情成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取会员详情失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取会员详情失败',
      data: null
    }, { status: 500 });
  }
}

// 更新会员信息
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    const data = await req.json();
    console.log('更新会员信息，ID:', id, '数据:', data);

    const {
      name,
      phone,
      email,
      gender,
      birthday,
      emergency_contact,
      emergency_phone,
      address,
      notes,
      level,
      status
    } = data;

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 检查会员是否存在
      const [existingMember] = await connection.execute(
        'SELECT id, tenant_id FROM members WHERE id = ?',
        [parseInt(id)]
      );

      if ((existingMember as any[]).length === 0) {
        return NextResponse.json({
          code: 404,
          msg: '会员不存在',
          data: null
        }, { status: 404 });
      }

      const tenantId = (existingMember as any[])[0].tenant_id;

      // 如果更新手机号，检查是否与其他会员冲突
      if (phone) {
        const [phoneCheck] = await connection.execute(
          'SELECT id FROM members WHERE phone = ? AND tenant_id = ? AND id != ?',
          [phone, tenantId, parseInt(id)]
        );

        if ((phoneCheck as any[]).length > 0) {
          return NextResponse.json({
            code: 400,
            msg: '该手机号已被其他会员使用',
            data: null
          }, { status: 400 });
        }
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      if (name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(name);
      }
      if (phone !== undefined) {
        updateFields.push('phone = ?');
        updateValues.push(phone);
      }
      if (email !== undefined) {
        updateFields.push('email = ?');
        updateValues.push(email);
      }
      if (gender !== undefined) {
        updateFields.push('gender = ?');
        updateValues.push(gender);
      }
      if (birthday !== undefined) {
        updateFields.push('birthday = ?');
        updateValues.push(birthday);
      }
      if (emergency_contact !== undefined) {
        updateFields.push('emergency_contact = ?');
        updateValues.push(emergency_contact);
      }
      if (emergency_phone !== undefined) {
        updateFields.push('emergency_phone = ?');
        updateValues.push(emergency_phone);
      }
      if (address !== undefined) {
        updateFields.push('address = ?');
        updateValues.push(address);
      }
      if (notes !== undefined) {
        updateFields.push('notes = ?');
        updateValues.push(notes);
      }
      if (level !== undefined) {
        updateFields.push('level = ?');
        updateValues.push(level);
      }
      if (status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(status);
      }

      if (updateFields.length === 0) {
        return NextResponse.json({
          code: 400,
          msg: '没有需要更新的字段',
          data: null
        }, { status: 400 });
      }

      // 执行更新
      updateValues.push(parseInt(id));
      const updateQuery = `UPDATE members SET ${updateFields.join(', ')} WHERE id = ?`;
      
      await connection.execute(updateQuery, updateValues);

      console.log('更新会员信息成功');

      return NextResponse.json({
        code: 200,
        data: { id: parseInt(id) },
        msg: '更新会员信息成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('更新会员信息失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '更新会员信息失败',
      data: null
    }, { status: 500 });
  }
}

// 删除会员
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    console.log('删除会员，ID:', id);

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 检查会员是否存在
      const [existingMember] = await connection.execute(
        'SELECT id FROM members WHERE id = ?',
        [parseInt(id)]
      );

      if ((existingMember as any[]).length === 0) {
        return NextResponse.json({
          code: 404,
          msg: '会员不存在',
          data: null
        }, { status: 404 });
      }

      // 检查是否有活跃的会员卡
      const [activeCards] = await connection.execute(
        'SELECT COUNT(*) as count FROM member_cards WHERE member_id = ? AND status = "active"',
        [parseInt(id)]
      );

      if ((activeCards as any[])[0].count > 0) {
        return NextResponse.json({
          code: 400,
          msg: '该会员还有活跃的会员卡，无法删除',
          data: null
        }, { status: 400 });
      }

      // 删除会员（由于外键约束，相关数据会自动删除）
      await connection.execute('DELETE FROM members WHERE id = ?', [parseInt(id)]);

      console.log('删除会员成功');

      return NextResponse.json({
        code: 200,
        data: null,
        msg: '删除会员成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('删除会员失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '删除会员失败',
      data: null
    }, { status: 500 });
  }
}
