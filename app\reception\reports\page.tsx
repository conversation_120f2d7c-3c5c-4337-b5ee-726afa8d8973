"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { format, subDays } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  ArrowLeft,
  Calendar,
  Download,
  Printer,
  Share2,
  BarChart,
  CreditCard,
  Users,
  DollarSign,
  FileText,
  CheckCircle2,
  XCircle,
  ChevronRight,
  ChevronLeft,
  UserPlus
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { toast } from "sonner"
import {
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from "recharts"

// 模拟日结报表数据
const dailyReportData = {
  date: "2023-05-16",
  summary: {
    totalRevenue: 12580.50,
    membershipSales: 8500.00,
    courseSales: 2800.00,
    productSales: 1280.50,
    cashPayments: 3500.00,
    wechatPayments: 6200.50,
    alipayPayments: 2880.00,
    newMembers: 5,
    totalVisits: 68,
    courseAttendance: 42,
    noShows: 3
  },
  revenueByCategory: [
    { name: "会员卡", value: 8500.00 },
    { name: "课程", value: 2800.00 },
    { name: "商品", value: 1280.50 }
  ],
  paymentMethods: [
    { name: "现金", value: 3500.00 },
    { name: "微信", value: 6200.50 },
    { name: "支付宝", value: 2880.00 }
  ],
  hourlyVisits: [
    { hour: "08:00", visits: 5 },
    { hour: "09:00", visits: 8 },
    { hour: "10:00", visits: 12 },
    { hour: "11:00", visits: 7 },
    { hour: "12:00", visits: 3 },
    { hour: "13:00", visits: 4 },
    { hour: "14:00", visits: 6 },
    { hour: "15:00", visits: 9 },
    { hour: "16:00", visits: 5 },
    { hour: "17:00", visits: 4 },
    { hour: "18:00", visits: 3 },
    { hour: "19:00", visits: 2 }
  ],
  topSellingItems: [
    { name: "瑜伽年卡", category: "会员卡", quantity: 2, revenue: 6800.00 },
    { name: "私教课程包", category: "课程", quantity: 3, revenue: 2400.00 },
    { name: "瑜伽垫", category: "商品", quantity: 4, revenue: 1120.00 },
    { name: "瑜伽月卡", category: "会员卡", quantity: 2, revenue: 1700.00 },
    { name: "团体课程", category: "课程", quantity: 2, revenue: 400.00 }
  ],
  courseAttendance: [
    { name: "空中瑜伽", booked: 12, attended: 10, noShow: 2 },
    { name: "哈他瑜伽", booked: 15, attended: 14, noShow: 1 },
    { name: "普拉提", booked: 8, attended: 8, noShow: 0 },
    { name: "流瑜伽", booked: 10, attended: 10, noShow: 0 }
  ]
}

// 模拟历史报表列表
const reportHistory = [
  { date: "2023-05-16", revenue: 12580.50, visits: 68, status: "已完成" },
  { date: "2023-05-15", revenue: 10250.00, visits: 55, status: "已完成" },
  { date: "2023-05-14", revenue: 8750.50, visits: 42, status: "已完成" },
  { date: "2023-05-13", revenue: 15680.00, visits: 73, status: "已完成" },
  { date: "2023-05-12", revenue: 9850.50, visits: 61, status: "已完成" }
]

// 颜色配置
const COLORS = {
  revenue: ["#8884d8", "#82ca9d", "#ffc658"],
  payment: ["#ff8042", "#00C49F", "#0088FE"],
  attendance: {
    attended: "#4ade80",
    noShow: "#f87171"
  }
}

export default function DailyReportPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("summary")
  const [currentDate, setCurrentDate] = useState(new Date())
  const [showGenerateDialog, setShowGenerateDialog] = useState(false)

  // 日期导航
  const goToPreviousDay = () => {
    setCurrentDate(prev => subDays(prev, 1))
  }

  const goToNextDay = () => {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)

    if (currentDate < tomorrow) {
      setCurrentDate(prev => new Date(prev.setDate(prev.getDate() + 1)))
    }
  }

  // 生成日结报表
  const generateReport = () => {
    // 在实际应用中，这里会调用API生成日结报表
    toast.success("日结报表生成成功！");
    setShowGenerateDialog(false);
  }

  // 格式化金额
  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/reception">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">日结报表</h1>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={goToPreviousDay}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-muted-foreground" />
            <span className="text-lg font-medium">
              {format(currentDate, "yyyy-MM-dd", { locale: zhCN })}
            </span>
          </div>
          <Button variant="outline" size="icon" onClick={goToNextDay}>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Printer className="h-4 w-4 mr-2" />
            打印报表
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            导出报表
          </Button>
          <Button onClick={() => setShowGenerateDialog(true)}>
            <FileText className="h-4 w-4 mr-2" />
            生成报表
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="summary">报表概览</TabsTrigger>
          <TabsTrigger value="revenue">收入分析</TabsTrigger>
          <TabsTrigger value="attendance">到访分析</TabsTrigger>
          <TabsTrigger value="history">历史报表</TabsTrigger>
        </TabsList>

        {/* 报表概览 */}
        <TabsContent value="summary" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总收入</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(dailyReportData.summary.totalRevenue)}</div>
                <p className="text-xs text-muted-foreground">
                  较前一天 +{formatCurrency(2330.50)}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">到访人数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dailyReportData.summary.totalVisits}</div>
                <p className="text-xs text-muted-foreground">
                  较前一天 +13 人次
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">新增会员</CardTitle>
                <UserPlus className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dailyReportData.summary.newMembers}</div>
                <p className="text-xs text-muted-foreground">
                  较前一天 +2 人
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">课程出勤率</CardTitle>
                <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{Math.round((dailyReportData.summary.courseAttendance / (dailyReportData.summary.courseAttendance + dailyReportData.summary.noShows)) * 100)}%</div>
                <p className="text-xs text-muted-foreground">
                  {dailyReportData.summary.courseAttendance} 出席 / {dailyReportData.summary.noShows} 爽约
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>收入分类</CardTitle>
                <CardDescription>
                  各类别收入占比
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={dailyReportData.revenueByCategory}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {dailyReportData.revenueByCategory.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS.revenue[index % COLORS.revenue.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>支付方式</CardTitle>
                <CardDescription>
                  各支付方式占比
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={dailyReportData.paymentMethods}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {dailyReportData.paymentMethods.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS.payment[index % COLORS.payment.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>热销商品/服务</CardTitle>
              <CardDescription>
                当日销售额最高的商品和服务
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>名称</TableHead>
                    <TableHead>类别</TableHead>
                    <TableHead>数量</TableHead>
                    <TableHead className="text-right">金额</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dailyReportData.topSellingItems.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {item.category}
                        </Badge>
                      </TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.revenue)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 收入分析 */}
        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>收入明细</CardTitle>
              <CardDescription>
                当日各类收入明细
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                <div>
                  <h3 className="text-lg font-medium mb-4">收入总览</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 rounded-lg border">
                      <div className="text-sm text-muted-foreground">会员卡销售</div>
                      <div className="text-2xl font-bold mt-1">{formatCurrency(dailyReportData.summary.membershipSales)}</div>
                      <div className="text-xs text-muted-foreground mt-1">占总收入 {Math.round((dailyReportData.summary.membershipSales / dailyReportData.summary.totalRevenue) * 100)}%</div>
                    </div>
                    <div className="p-4 rounded-lg border">
                      <div className="text-sm text-muted-foreground">课程销售</div>
                      <div className="text-2xl font-bold mt-1">{formatCurrency(dailyReportData.summary.courseSales)}</div>
                      <div className="text-xs text-muted-foreground mt-1">占总收入 {Math.round((dailyReportData.summary.courseSales / dailyReportData.summary.totalRevenue) * 100)}%</div>
                    </div>
                    <div className="p-4 rounded-lg border">
                      <div className="text-sm text-muted-foreground">商品销售</div>
                      <div className="text-2xl font-bold mt-1">{formatCurrency(dailyReportData.summary.productSales)}</div>
                      <div className="text-xs text-muted-foreground mt-1">占总收入 {Math.round((dailyReportData.summary.productSales / dailyReportData.summary.totalRevenue) * 100)}%</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-4">支付方式</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 rounded-lg border">
                      <div className="text-sm text-muted-foreground">现金支付</div>
                      <div className="text-2xl font-bold mt-1">{formatCurrency(dailyReportData.summary.cashPayments)}</div>
                      <div className="text-xs text-muted-foreground mt-1">占总收入 {Math.round((dailyReportData.summary.cashPayments / dailyReportData.summary.totalRevenue) * 100)}%</div>
                    </div>
                    <div className="p-4 rounded-lg border">
                      <div className="text-sm text-muted-foreground">微信支付</div>
                      <div className="text-2xl font-bold mt-1">{formatCurrency(dailyReportData.summary.wechatPayments)}</div>
                      <div className="text-xs text-muted-foreground mt-1">占总收入 {Math.round((dailyReportData.summary.wechatPayments / dailyReportData.summary.totalRevenue) * 100)}%</div>
                    </div>
                    <div className="p-4 rounded-lg border">
                      <div className="text-sm text-muted-foreground">支付宝支付</div>
                      <div className="text-2xl font-bold mt-1">{formatCurrency(dailyReportData.summary.alipayPayments)}</div>
                      <div className="text-xs text-muted-foreground mt-1">占总收入 {Math.round((dailyReportData.summary.alipayPayments / dailyReportData.summary.totalRevenue) * 100)}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 到访分析 */}
        <TabsContent value="attendance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>到访时段分布</CardTitle>
              <CardDescription>
                会员到访时段分布情况
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsBarChart
                  data={dailyReportData.hourlyVisits}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="visits" fill="#8884d8" name="到访人数" />
                </RechartsBarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>课程出勤情况</CardTitle>
              <CardDescription>
                各课程预约与实际出勤情况
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>课程名称</TableHead>
                    <TableHead>预约人数</TableHead>
                    <TableHead>实际出勤</TableHead>
                    <TableHead>爽约人数</TableHead>
                    <TableHead className="text-right">出勤率</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dailyReportData.courseAttendance.map((course, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{course.name}</TableCell>
                      <TableCell>{course.booked}</TableCell>
                      <TableCell>{course.attended}</TableCell>
                      <TableCell>{course.noShow}</TableCell>
                      <TableCell className="text-right">
                        <Badge variant={course.noShow === 0 ? "success" : "outline"}>
                          {Math.round((course.attended / course.booked) * 100)}%
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 历史报表 */}
        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>历史日结报表</CardTitle>
              <CardDescription>
                查看历史日结报表记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>日期</TableHead>
                    <TableHead>总收入</TableHead>
                    <TableHead>到访人数</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reportHistory.map((report, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{report.date}</TableCell>
                      <TableCell>{formatCurrency(report.revenue)}</TableCell>
                      <TableCell>{report.visits}</TableCell>
                      <TableCell>
                        <Badge variant="success">
                          {report.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm">
                          查看详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 生成报表对话框 */}
      <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>生成日结报表</DialogTitle>
            <DialogDescription>
              确认生成当日日结报表？生成后将无法修改。
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="p-4 rounded-lg border bg-muted/50">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium">报表日期</div>
                  <div>{format(currentDate, "yyyy-MM-dd", { locale: zhCN })}</div>
                </div>
                <Separator className="my-2" />
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium">总收入</div>
                  <div>{formatCurrency(dailyReportData.summary.totalRevenue)}</div>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <div className="text-sm font-medium">到访人数</div>
                  <div>{dailyReportData.summary.totalVisits} 人次</div>
                </div>
                <div className="flex items-center justify-between mt-1">
                  <div className="text-sm font-medium">课程出勤</div>
                  <div>{dailyReportData.summary.courseAttendance} 人次</div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="report-notes">备注</Label>
                <textarea
                  id="report-notes"
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="添加报表备注信息..."
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowGenerateDialog(false)}>取消</Button>
            <Button onClick={generateReport}>确认生成</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
