"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Calendar,
  Clock,
  Copy,
  Download,
  Edit,
  Eye,
  Filter,
  MoreHorizontal,
  Pause,
  Play,
  Plus,
  QrCode,
  RefreshCw,
  Search,
  Send,
  Share2,
  Trash,
  Upload,
  Users,
  Tags,
  MessageSquare,
  MessageCircle,
  Image,
} from "lucide-react"

export default function PromotionsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">促销活动</h1>
          <p className="text-muted-foreground">创建和管理各类促销活动，提升会员参与度和销售额</p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建活动
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[700px]">
              <DialogHeader>
                <DialogTitle>创建新促销活动</DialogTitle>
                <DialogDescription>设置活动的基本信息、规则和奖励机制</DialogDescription>
              </DialogHeader>
              <Tabs defaultValue="basic" className="mt-2">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">基本信息</TabsTrigger>
                  <TabsTrigger value="rules">活动规则</TabsTrigger>
                  <TabsTrigger value="target">目标人群</TabsTrigger>
                  <TabsTrigger value="display">展示设置</TabsTrigger>
                </TabsList>
                <TabsContent value="basic" className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="promotion-name" className="text-sm font-medium">
                        活动名称
                      </label>
                      <Input id="promotion-name" placeholder="例如：春季会员招募活动" />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="promotion-type" className="text-sm font-medium">
                        活动类型
                      </label>
                      <Select>
                        <SelectTrigger id="promotion-type">
                          <SelectValue placeholder="选择类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="discount">限时折扣</SelectItem>
                          <SelectItem value="group">拼团活动</SelectItem>
                          <SelectItem value="flash">秒杀活动</SelectItem>
                          <SelectItem value="referral">会员推荐</SelectItem>
                          <SelectItem value="gift">买赠活动</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">活动时间</label>
                      <div className="flex gap-2">
                        <Button variant="outline" size="icon" className="h-10 w-10">
                          <Calendar className="h-4 w-4" />
                        </Button>
                        <Input placeholder="开始日期" />
                        <Input placeholder="结束日期" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="daily-time" className="text-sm font-medium">
                        每日活动时间
                      </label>
                      <div className="flex gap-2">
                        <Button variant="outline" size="icon" className="h-10 w-10">
                          <Clock className="h-4 w-4" />
                        </Button>
                        <Input placeholder="开始时间" />
                        <Input placeholder="结束时间" />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="description" className="text-sm font-medium">
                      活动说明
                    </label>
                    <Input id="description" placeholder="活动详细说明和规则" />
                  </div>
                </TabsContent>
                <TabsContent value="rules" className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label htmlFor="applicable-courses" className="text-sm font-medium">
                      适用课程/商品
                    </label>
                    <Select>
                      <SelectTrigger id="applicable-courses">
                        <SelectValue placeholder="选择适用范围" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部课程</SelectItem>
                        <SelectItem value="yoga">瑜伽课程</SelectItem>
                        <SelectItem value="pilates">普拉提课程</SelectItem>
                        <SelectItem value="fitness">健身课程</SelectItem>
                        <SelectItem value="membership">会员卡</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="discount-type" className="text-sm font-medium">
                      优惠类型
                    </label>
                    <Select>
                      <SelectTrigger id="discount-type">
                        <SelectValue placeholder="选择优惠类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">折扣百分比</SelectItem>
                        <SelectItem value="fixed">固定金额</SelectItem>
                        <SelectItem value="free">免费赠送</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="discount-value" className="text-sm font-medium">
                      优惠力度
                    </label>
                    <Input id="discount-value" placeholder="例如：80% 或 ¥50" />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="min-purchase" className="text-sm font-medium">
                      最低消费要求
                    </label>
                    <Input id="min-purchase" placeholder="例如：¥200 或 不限制" />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="user-limit" className="text-sm font-medium">
                      参与人数限制
                    </label>
                    <Input id="user-limit" placeholder="例如：不限制或具体数量" />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="per-user-limit" className="text-sm font-medium">
                      每人参与次数
                    </label>
                    <Input id="per-user-limit" placeholder="例如：1次或不限制" />
                  </div>
                </TabsContent>
                <TabsContent value="target" className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label htmlFor="target-members" className="text-sm font-medium">
                      目标会员
                    </label>
                    <Select>
                      <SelectTrigger id="target-members">
                        <SelectValue placeholder="选择目标会员" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部会员</SelectItem>
                        <SelectItem value="new">新会员</SelectItem>
                        <SelectItem value="existing">老会员</SelectItem>
                        <SelectItem value="inactive">流失会员</SelectItem>
                        <SelectItem value="custom">自定义标签</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="member-tags" className="text-sm font-medium">
                      会员标签
                    </label>
                    <Select>
                      <SelectTrigger id="member-tags">
                        <SelectValue placeholder="选择会员标签" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="vip">VIP会员</SelectItem>
                        <SelectItem value="yoga-lover">瑜伽爱好者</SelectItem>
                        <SelectItem value="pilates-lover">普拉提爱好者</SelectItem>
                        <SelectItem value="morning">晨练一族</SelectItem>
                        <SelectItem value="evening">夜练一族</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="notification" className="text-sm font-medium">
                      通知方式
                    </label>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="notify-sms" className="h-4 w-4 rounded border-gray-300" />
                        <label htmlFor="notify-sms">短信通知</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="notify-wechat" className="h-4 w-4 rounded border-gray-300" />
                        <label htmlFor="notify-wechat">微信通知</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="notify-app" className="h-4 w-4 rounded border-gray-300" />
                        <label htmlFor="notify-app">APP推送</label>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="display" className="space-y-4 py-4">
                  <div className="space-y-2">
                    <label htmlFor="promotion-image" className="text-sm font-medium">
                      活动图片
                    </label>
                    <div className="flex items-center gap-4">
                      <div className="h-32 w-32 rounded-md border border-dashed border-gray-300 flex items-center justify-center">
                        <Button variant="ghost">
                          <Upload className="mr-2 h-4 w-4" />
                          上传图片
                        </Button>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <p>支持 JPG、PNG 格式</p>
                        <p>建议尺寸 800x400 像素</p>
                        <p>文件大小不超过 2MB</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="display-position" className="text-sm font-medium">
                      展示位置
                    </label>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="pos-home" className="h-4 w-4 rounded border-gray-300" />
                        <label htmlFor="pos-home">首页轮播</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="pos-course" className="h-4 w-4 rounded border-gray-300" />
                        <label htmlFor="pos-course">课程页面</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="pos-member" className="h-4 w-4 rounded border-gray-300" />
                        <label htmlFor="pos-member">会员中心</label>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="display-priority" className="text-sm font-medium">
                      展示优先级
                    </label>
                    <Select>
                      <SelectTrigger id="display-priority">
                        <SelectValue placeholder="选择优先级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">高</SelectItem>
                        <SelectItem value="medium">中</SelectItem>
                        <SelectItem value="low">低</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>
              </Tabs>
              <DialogFooter>
                <Button variant="outline">保存为草稿</Button>
                <Button>创建活动</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            导入
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="搜索活动名称、类型或描述..." className="pl-8" />
        </div>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="状态筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="active">进行中</SelectItem>
            <SelectItem value="upcoming">未开始</SelectItem>
            <SelectItem value="ended">已结束</SelectItem>
            <SelectItem value="draft">草稿</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">全部活动</TabsTrigger>
          <TabsTrigger value="discount">限时折扣</TabsTrigger>
          <TabsTrigger value="group">拼团活动</TabsTrigger>
          <TabsTrigger value="flash">秒杀活动</TabsTrigger>
          <TabsTrigger value="referral">会员推荐</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>活动名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>活动时间</TableHead>
                    <TableHead>参与人数</TableHead>
                    <TableHead>转化率</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {promotions.map((promotion) => (
                    <TableRow key={promotion.id}>
                      <TableCell className="font-medium">
                        <div className="flex flex-col">
                          <span>{promotion.name}</span>
                          <span className="text-xs text-muted-foreground">{promotion.description}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getBadgeVariant(promotion.type)}>{getPromotionTypeName(promotion.type)}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-xs">{promotion.startDate}</span>
                          <span className="text-xs">至</span>
                          <span className="text-xs">{promotion.endDate}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{promotion.participants}</span>
                          {promotion.status === "active" && (
                            <span className="text-xs text-green-600">+{Math.floor(Math.random() * 10)} 今日</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="w-full max-w-[80px]">
                            <div className="h-2 rounded-full bg-gray-200">
                              <div
                                className="h-2 rounded-full bg-green-500"
                                style={{ width: promotion.conversionRate.replace("%", "") + "%" }}
                              ></div>
                            </div>
                          </div>
                          <span className="ml-2">{promotion.conversionRate}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(promotion.status)}>
                          {getStatusName(promotion.status)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-[160px]">
                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  查看详情
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[800px]">
                                <DialogHeader>
                                  <DialogTitle>活动详情</DialogTitle>
                                </DialogHeader>
                                <div className="grid grid-cols-3 gap-4">
                                  <div className="col-span-2 space-y-4">
                                    <Card>
                                      <CardHeader className="pb-2">
                                        <CardTitle className="text-lg">基本信息</CardTitle>
                                      </CardHeader>
                                      <CardContent>
                                        <div className="grid grid-cols-2 gap-4">
                                          <div>
                                            <p className="text-sm text-muted-foreground">活动名称</p>
                                            <p>春季会员招募活动</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">活动类型</p>
                                            <Badge variant="secondary">限时折扣</Badge>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">活动时间</p>
                                            <p>2025-04-01 至 2025-04-30</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">每日活动时间</p>
                                            <p>全天</p>
                                          </div>
                                          <div className="col-span-2">
                                            <p className="text-sm text-muted-foreground">活动说明</p>
                                            <p>新会员首月半价，赠送瑜伽垫</p>
                                          </div>
                                        </div>
                                      </CardContent>
                                    </Card>

                                    <Card>
                                      <CardHeader className="pb-2">
                                        <CardTitle className="text-lg">活动规则</CardTitle>
                                      </CardHeader>
                                      <CardContent>
                                        <div className="grid grid-cols-2 gap-4">
                                          <div>
                                            <p className="text-sm text-muted-foreground">适用课程/商品</p>
                                            <p>全部课程</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">优惠类型</p>
                                            <p>折扣百分比</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">优惠力度</p>
                                            <p>50%</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">最低消费要求</p>
                                            <p>不限制</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">参与人数限制</p>
                                            <p>不限制</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">每人参与次数</p>
                                            <p>1次</p>
                                          </div>
                                        </div>
                                      </CardContent>
                                    </Card>

                                    <Card>
                                      <CardHeader className="pb-2">
                                        <CardTitle className="text-lg">目标人群</CardTitle>
                                      </CardHeader>
                                      <CardContent>
                                        <div className="grid grid-cols-2 gap-4">
                                          <div>
                                            <p className="text-sm text-muted-foreground">目标会员</p>
                                            <p>新会员</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">会员标签</p>
                                            <p>不限制</p>
                                          </div>
                                          <div className="col-span-2">
                                            <p className="text-sm text-muted-foreground">通知方式</p>
                                            <div className="flex gap-2 mt-1">
                                              <Badge variant="outline">短信通知</Badge>
                                              <Badge variant="outline">微信通知</Badge>
                                            </div>
                                          </div>
                                        </div>
                                      </CardContent>
                                    </Card>
                                  </div>

                                  <div className="space-y-4">
                                    <Card>
                                      <CardHeader className="pb-2">
                                        <CardTitle className="text-lg">活动状态</CardTitle>
                                      </CardHeader>
                                      <CardContent>
                                        <div className="flex flex-col items-center justify-center space-y-2">
                                          <Badge variant="success" className="mb-2">
                                            进行中
                                          </Badge>
                                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                                            <div
                                              className="bg-green-600 h-2.5 rounded-full"
                                              style={{ width: "45%" }}
                                            ></div>
                                          </div>
                                          <p className="text-sm text-muted-foreground">已进行 45%</p>
                                          <p className="text-sm">剩余 16 天</p>
                                        </div>
                                      </CardContent>
                                    </Card>

                                    <Card>
                                      <CardHeader className="pb-2">
                                        <CardTitle className="text-lg">活动数据</CardTitle>
                                      </CardHeader>
                                      <CardContent>
                                        <div className="space-y-4">
                                          <div>
                                            <p className="text-sm text-muted-foreground">参与人数</p>
                                            <p className="text-2xl font-bold">156</p>
                                            <p className="text-xs text-green-600">较昨日 +12</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">转化率</p>
                                            <p className="text-2xl font-bold">32%</p>
                                            <p className="text-xs text-green-600">较昨日 +2.5%</p>
                                          </div>
                                          <div>
                                            <p className="text-sm text-muted-foreground">带动销售额</p>
                                            <p className="text-2xl font-bold">¥15,600</p>
                                            <p className="text-xs text-green-600">较昨日 +¥1,200</p>
                                          </div>
                                        </div>
                                      </CardContent>
                                    </Card>

                                    <div className="flex flex-col gap-2">
                                      <Button>
                                        <Edit className="mr-2 h-4 w-4" />
                                        编辑活动
                                      </Button>
                                      <Button variant="outline">
                                        <Share2 className="mr-2 h-4 w-4" />
                                        分享活动
                                      </Button>
                                      <Button variant="outline">
                                        <Pause className="mr-2 h-4 w-4" />
                                        暂停活动
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>

                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[700px]">
                                <DialogHeader>
                                  <DialogTitle>编辑促销活动</DialogTitle>
                                  <DialogDescription>修改活动的信息和设置</DialogDescription>
                                </DialogHeader>
                                <Tabs defaultValue="basic" className="mt-2">
                                  <TabsList className="grid w-full grid-cols-4">
                                    <TabsTrigger value="basic">基本信息</TabsTrigger>
                                    <TabsTrigger value="rules">活动规则</TabsTrigger>
                                    <TabsTrigger value="target">目标人群</TabsTrigger>
                                    <TabsTrigger value="display">展示设置</TabsTrigger>
                                  </TabsList>
                                  <TabsContent value="basic" className="space-y-4 py-4">
                                    <div className="grid grid-cols-2 gap-4">
                                      <div className="space-y-2">
                                        <label htmlFor="promotion-name" className="text-sm font-medium">
                                          活动名称
                                        </label>
                                        <Input id="promotion-name" defaultValue="春季会员招募活动" />
                                      </div>
                                      <div className="space-y-2">
                                        <label htmlFor="promotion-type" className="text-sm font-medium">
                                          活动类型
                                        </label>
                                        <Select defaultValue="discount">
                                          <SelectTrigger id="promotion-type">
                                            <SelectValue placeholder="选择类型" />
                                          </SelectTrigger>
                                          <SelectContent>
                                            <SelectItem value="discount">限时折扣</SelectItem>
                                            <SelectItem value="group">拼团活动</SelectItem>
                                            <SelectItem value="flash">秒杀活动</SelectItem>
                                            <SelectItem value="referral">会员推荐</SelectItem>
                                            <SelectItem value="gift">买赠活动</SelectItem>
                                          </SelectContent>
                                        </Select>
                                      </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                      <div className="space-y-2">
                                        <label className="text-sm font-medium">活动时间</label>
                                        <div className="flex gap-2">
                                          <Button variant="outline" size="icon" className="h-10 w-10">
                                            <Calendar className="h-4 w-4" />
                                          </Button>
                                          <Input defaultValue="2025-04-01" />
                                          <Input defaultValue="2025-04-30" />
                                        </div>
                                      </div>
                                      <div className="space-y-2">
                                        <label htmlFor="daily-time" className="text-sm font-medium">
                                          每日活动时间
                                        </label>
                                        <div className="flex gap-2">
                                          <Button variant="outline" size="icon" className="h-10 w-10">
                                            <Clock className="h-4 w-4" />
                                          </Button>
                                          <Input defaultValue="00:00" />
                                          <Input defaultValue="23:59" />
                                        </div>
                                      </div>
                                    </div>

                                    <div className="space-y-2">
                                      <label htmlFor="description" className="text-sm font-medium">
                                        活动说明
                                      </label>
                                      <Input id="description" defaultValue="新会员首月半价，赠送瑜伽垫" />
                                    </div>
                                  </TabsContent>
                                  <TabsContent value="rules" className="space-y-4 py-4">
                                    {/* 活动规则内容 */}
                                  </TabsContent>
                                  <TabsContent value="target" className="space-y-4 py-4">
                                    {/* 目标人群内容 */}
                                  </TabsContent>
                                  <TabsContent value="display" className="space-y-4 py-4">
                                    {/* 展示设置内容 */}
                                  </TabsContent>
                                </Tabs>
                                <DialogFooter>
                                  <Button variant="outline">取消</Button>
                                  <Button>保存修改</Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>

                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Send className="mr-2 h-4 w-4" />
                                  发放
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[600px]">
                                <DialogHeader>
                                  <DialogTitle>发放活动</DialogTitle>
                                  <DialogDescription>选择发放方式和目标会员</DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4 py-4">
                                  <div className="space-y-2">
                                    <label className="text-sm font-medium">发放方式</label>
                                    <div className="grid grid-cols-2 gap-4">
                                      <div className="flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer hover:bg-gray-50 bg-gray-50 border-primary">
                                        <Users className="h-8 w-8 mb-2 text-primary" />
                                        <span className="text-sm font-medium">指定会员</span>
                                      </div>
                                      <div className="flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer hover:bg-gray-50">
                                        <Tags className="h-8 w-8 mb-2 text-muted-foreground" />
                                        <span className="text-sm font-medium">会员标签</span>
                                      </div>
                                      <div className="flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer hover:bg-gray-50">
                                        <QrCode className="h-8 w-8 mb-2 text-muted-foreground" />
                                        <span className="text-sm font-medium">生成二维码</span>
                                      </div>
                                      <div className="flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer hover:bg-gray-50">
                                        <MessageSquare className="h-8 w-8 mb-2 text-muted-foreground" />
                                        <span className="text-sm font-medium">短信通知</span>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                      <label className="text-sm font-medium">选择会员</label>
                                      <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
                                        全选
                                      </Button>
                                    </div>
                                    <div className="border rounded-md p-4 max-h-[200px] overflow-y-auto">
                                      <div className="space-y-2">
                                        {Array.from({ length: 5 }).map((_, i) => (
                                          <div
                                            key={i}
                                            className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md"
                                          >
                                            <div className="flex items-center">
                                              <input type="checkbox" className="h-4 w-4 rounded border-gray-300 mr-2" />
                                              <div className="h-8 w-8 rounded-full bg-gray-200 mr-2"></div>
                                              <div>
                                                <p className="text-sm font-medium">会员 {i + 1}</p>
                                                <p className="text-xs text-muted-foreground">138****{1000 + i}</p>
                                              </div>
                                            </div>
                                            <Badge variant="outline">VIP会员</Badge>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </div>

                                  <div className="space-y-2">
                                    <label htmlFor="message" className="text-sm font-medium">
                                      附加消息
                                    </label>
                                    <Input id="message" placeholder="可选，发放时附带的消息" />
                                  </div>

                                  <div className="space-y-2">
                                    <label className="text-sm font-medium">发放设置</label>
                                    <div className="flex items-center space-x-2">
                                      <input
                                        type="checkbox"
                                        id="notify-immediately"
                                        className="h-4 w-4 rounded border-gray-300"
                                        defaultChecked
                                      />
                                      <label htmlFor="notify-immediately" className="text-sm">
                                        立即通知会员
                                      </label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <input
                                        type="checkbox"
                                        id="track-usage"
                                        className="h-4 w-4 rounded border-gray-300"
                                        defaultChecked
                                      />
                                      <label htmlFor="track-usage" className="text-sm">
                                        跟踪使用情况
                                      </label>
                                    </div>
                                  </div>
                                </div>
                                <DialogFooter>
                                  <Button variant="outline">取消</Button>
                                  <Button>确认发放</Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>

                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <QrCode className="mr-2 h-4 w-4" />
                                  生成二维码
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[500px]">
                                <DialogHeader>
                                  <DialogTitle>生成活动二维码</DialogTitle>
                                  <DialogDescription>生成二维码供会员扫码参与活动</DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4 py-4">
                                  <div className="space-y-2">
                                    <label htmlFor="qrcode-name" className="text-sm font-medium">
                                      二维码名称
                                    </label>
                                    <Input id="qrcode-name" placeholder="例如：前台展示二维码" />
                                  </div>

                                  <div className="space-y-2">
                                    <label htmlFor="qrcode-limit" className="text-sm font-medium">
                                      领取限制
                                    </label>
                                    <Input id="qrcode-limit" placeholder="例如：不限制或具体数量" />
                                  </div>

                                  <div className="space-y-2">
                                    <label htmlFor="qrcode-expiry" className="text-sm font-medium">
                                      有效期
                                    </label>
                                    <div className="flex gap-2">
                                      <Button variant="outline" size="icon" className="h-10 w-10">
                                        <Calendar className="h-4 w-4" />
                                      </Button>
                                      <Input placeholder="截止日期" />
                                    </div>
                                  </div>

                                  <div className="space-y-2">
                                    <label className="text-sm font-medium">二维码样式</label>
                                    <div className="grid grid-cols-3 gap-2">
                                      <div className="border rounded-md p-2 flex items-center justify-center cursor-pointer hover:bg-gray-50 bg-gray-50 border-primary">
                                        <div className="h-16 w-16 bg-gray-200 rounded-md"></div>
                                      </div>
                                      <div className="border rounded-md p-2 flex items-center justify-center cursor-pointer hover:bg-gray-50">
                                        <div className="h-16 w-16 bg-gray-200 rounded-md"></div>
                                      </div>
                                      <div className="border rounded-md p-2 flex items-center justify-center cursor-pointer hover:bg-gray-50">
                                        <div className="h-16 w-16 bg-gray-200 rounded-md"></div>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="flex justify-center py-4">
                                    <div className="h-40 w-40 bg-gray-100 rounded-md flex items-center justify-center">
                                      <QrCode className="h-20 w-20 text-gray-400" />
                                    </div>
                                  </div>

                                  <div className="space-y-2">
                                    <label htmlFor="qrcode-desc" className="text-sm font-medium">
                                      描述信息
                                    </label>
                                    <Input id="qrcode-desc" placeholder="二维码下方显示的文字" />
                                  </div>
                                </div>
                                <DialogFooter>
                                  <Button variant="outline">取消</Button>
                                  <Button>生成二维码</Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>

                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Share2 className="mr-2 h-4 w-4" />
                                  分享
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[500px]">
                                <DialogHeader>
                                  <DialogTitle>分享活动</DialogTitle>
                                  <DialogDescription>将活动分享给潜在客户</DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4 py-4">
                                  <div className="flex justify-center">
                                    <div className="h-40 w-full max-w-[300px] bg-gray-100 rounded-md flex items-center justify-center">
                                      <div className="text-center">
                                        <p className="font-medium">春季会员招募活动</p>
                                        <p className="text-sm text-muted-foreground">新会员首月半价，赠送瑜伽垫</p>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="space-y-2">
                                    <label className="text-sm font-medium">分享渠道</label>
                                    <div className="grid grid-cols-4 gap-4">
                                      <div className="flex flex-col items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-gray-50 bg-gray-50 border-primary">
                                        <div className="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center mb-1">
                                          <MessageCircle className="h-5 w-5 text-white" />
                                        </div>
                                        <span className="text-xs">微信</span>
                                      </div>
                                      <div className="flex flex-col items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-gray-50">
                                        <div className="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center mb-1">
                                          <Users className="h-5 w-5 text-white" />
                                        </div>
                                        <span className="text-xs">微信群</span>
                                      </div>
                                      <div className="flex flex-col items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-gray-50">
                                        <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center mb-1">
                                          <MessageSquare className="h-5 w-5 text-white" />
                                        </div>
                                        <span className="text-xs">微博</span>
                                      </div>
                                      <div className="flex flex-col items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-gray-50">
                                        <div className="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center mb-1">
                                          <Image className="h-5 w-5 text-white" />
                                        </div>
                                        <span className="text-xs">朋友圈</span>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="space-y-2">
                                    <label htmlFor="share-link" className="text-sm font-medium">
                                      分享链接
                                    </label>
                                    <div className="flex gap-2">
                                      <Input id="share-link" value="https://example.com/promotion/123" readOnly />
                                      <Button variant="outline" size="icon">
                                        <Copy className="h-4 w-4" />
                                      </Button>
                                    </div>
                                    <p className="text-xs text-muted-foreground">链接有效期：30天</p>
                                  </div>

                                  <div className="space-y-2">
                                    <label htmlFor="share-text" className="text-sm font-medium">
                                      分享文案
                                    </label>
                                    <Input
                                      id="share-text"
                                      defaultValue="春季会员招募活动开始啦！新会员首月半价，还送瑜伽垫，快来参加吧！"
                                    />
                                  </div>

                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                      <label className="text-sm font-medium">分享设置</label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <input
                                        type="checkbox"
                                        id="track-shares"
                                        className="h-4 w-4 rounded border-gray-300"
                                        defaultChecked
                                      />
                                      <label htmlFor="track-shares" className="text-sm">
                                        跟踪分享数据
                                      </label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <input
                                        type="checkbox"
                                        id="add-watermark"
                                        className="h-4 w-4 rounded border-gray-300"
                                        defaultChecked
                                      />
                                      <label htmlFor="add-watermark" className="text-sm">
                                        添加品牌水印
                                      </label>
                                    </div>
                                  </div>
                                </div>
                                <DialogFooter>
                                  <Button variant="outline">取消</Button>
                                  <Button>分享</Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>

                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  <Copy className="mr-2 h-4 w-4" />
                                  复制
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[500px]">
                                <DialogHeader>
                                  <DialogTitle>复制活动</DialogTitle>
                                  <DialogDescription>创建一个基于当前活动的副本</DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4 py-4">
                                  <div className="space-y-2">
                                    <label htmlFor="copy-name" className="text-sm font-medium">
                                      新活动名称
                                    </label>
                                    <Input id="copy-name" defaultValue="春季会员招募活动 (副本)" />
                                  </div>

                                  <div className="space-y-2">
                                    <label className="text-sm font-medium">复制选项</label>
                                    <div className="flex items-center space-x-2">
                                      <input
                                        type="checkbox"
                                        id="copy-rules"
                                        className="h-4 w-4 rounded border-gray-300"
                                        defaultChecked
                                      />
                                      <label htmlFor="copy-rules" className="text-sm">
                                        复制活动规则
                                      </label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <input
                                        type="checkbox"
                                        id="copy-target"
                                        className="h-4 w-4 rounded border-gray-300"
                                        defaultChecked
                                      />
                                      <label htmlFor="copy-target" className="text-sm">
                                        复制目标人群
                                      </label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <input
                                        type="checkbox"
                                        id="copy-display"
                                        className="h-4 w-4 rounded border-gray-300"
                                        defaultChecked
                                      />
                                      <label htmlFor="copy-display" className="text-sm">
                                        复制展示设置
                                      </label>
                                    </div>
                                  </div>

                                  <div className="space-y-2">
                                    <label className="text-sm font-medium">活动时间</label>
                                    <div className="flex gap-2">
                                      <Button variant="outline" size="icon" className="h-10 w-10">
                                        <Calendar className="h-4 w-4" />
                                      </Button>
                                      <Input placeholder="开始日期" />
                                      <Input placeholder="结束日期" />
                                    </div>
                                    <p className="text-xs text-muted-foreground">新活动的开始和结束时间</p>
                                  </div>

                                  <div className="space-y-2">
                                    <label htmlFor="copy-status" className="text-sm font-medium">
                                      初始状态
                                    </label>
                                    <Select defaultValue="draft">
                                      <SelectTrigger id="copy-status">
                                        <SelectValue placeholder="选择状态" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="active">立即开始</SelectItem>
                                        <SelectItem value="upcoming">计划中</SelectItem>
                                        <SelectItem value="draft">草稿</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>
                                <DialogFooter>
                                  <Button variant="outline">取消</Button>
                                  <Button>创建副本</Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>

                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                  {promotion.status === "active" ? (
                                    <>
                                      <Pause className="mr-2 h-4 w-4" />
                                      暂停
                                    </>
                                  ) : promotion.status === "upcoming" ? (
                                    <>
                                      <Play className="mr-2 h-4 w-4" />
                                      立即开始
                                    </>
                                  ) : (
                                    <>
                                      <RefreshCw className="mr-2 h-4 w-4" />
                                      重新激活
                                    </>
                                  )}
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[400px]">
                                <DialogHeader>
                                  <DialogTitle>
                                    {promotion.status === "active"
                                      ? "确认暂停"
                                      : promotion.status === "upcoming"
                                        ? "立即开始"
                                        : "重新激活"}
                                  </DialogTitle>
                                  <DialogDescription>
                                    {promotion.status === "active"
                                      ? "您确定要暂停此活动吗？"
                                      : promotion.status === "upcoming"
                                        ? "您确定要立即开始此活动吗？"
                                        : "您确定要重新激活此活动吗？"}
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="py-4">
                                  <p className="text-sm text-muted-foreground">
                                    {promotion.status === "active"
                                      ? "暂停后，会员将无法参与此活动，但您可以随时重新激活。已参与的会员不受影响。"
                                      : promotion.status === "upcoming"
                                        ? "立即开始后，活动将对所有会员开放，原定的开始时间将被忽略。"
                                        : "重新激活后，活动将继续进行，会员可以再次参与此活动。"}
                                  </p>

                                  {promotion.status === "active" && (
                                    <div className="mt-4 space-y-2">
                                      <label htmlFor="pause-reason" className="text-sm font-medium">
                                        暂停原因（可选）
                                      </label>
                                      <Input id="pause-reason" placeholder="例如：活动调整中" />
                                    </div>
                                  )}

                                  {promotion.status === "ended" && (
                                    <div className="mt-4 space-y-2">
                                      <label htmlFor="new-end-date" className="text-sm font-medium">
                                        新的结束日期
                                      </label>
                                      <div className="flex gap-2">
                                        <Button variant="outline" size="icon" className="h-10 w-10">
                                          <Calendar className="h-4 w-4" />
                                        </Button>
                                        <Input placeholder="结束日期" />
                                      </div>
                                    </div>
                                  )}
                                </div>
                                <DialogFooter>
                                  <Button variant="outline">取消</Button>
                                  <Button>
                                    {promotion.status === "active"
                                      ? "确认暂停"
                                      : promotion.status === "upcoming"
                                        ? "确认开始"
                                        : "确认激活"}
                                  </Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>

                            <Dialog>
                              <DialogTrigger asChild>
                                <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="text-destructive">
                                  <Trash className="mr-2 h-4 w-4" />
                                  删除
                                </DropdownMenuItem>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[400px]">
                                <DialogHeader>
                                  <DialogTitle>确认删除</DialogTitle>
                                  <DialogDescription>您确定要删除此活动吗？此操作不可撤销。</DialogDescription>
                                </DialogHeader>
                                <div className="py-4">
                                  <p className="text-sm text-muted-foreground">
                                    删除后，所有相关的数据将被永久删除，包括参与记录和统计数据。
                                  </p>

                                  <div className="mt-4 space-y-2">
                                    <div className="flex items-center space-x-2">
                                      <input
                                        type="checkbox"
                                        id="confirm-delete"
                                        className="h-4 w-4 rounded border-gray-300"
                                      />
                                      <label htmlFor="confirm-delete" className="text-sm">
                                        我理解此操作不可撤销
                                      </label>
                                    </div>
                                  </div>
                                </div>
                                <DialogFooter>
                                  <Button variant="outline">取消</Button>
                                  <Button variant="destructive">确认删除</Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="discount" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>限时折扣</CardTitle>
              <CardDescription>在特定时间内对课程或会员卡进行折扣</CardDescription>
            </CardHeader>
            <CardContent>{/* 限时折扣内容 */}</CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="group" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>拼团活动</CardTitle>
              <CardDescription>多人一起购买享受更多优惠</CardDescription>
            </CardHeader>
            <CardContent>{/* 拼团活动内容 */}</CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="flash" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>秒杀活动</CardTitle>
              <CardDescription>限时限量特价优惠</CardDescription>
            </CardHeader>
            <CardContent>{/* 秒杀活动内容 */}</CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="referral" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>会员推荐</CardTitle>
              <CardDescription>老会员推荐新会员双方获益</CardDescription>
            </CardHeader>
            <CardContent>{/* 会员推荐内容 */}</CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      {/* 活动详情对话框 */}
      <Dialog>
        <DialogTrigger asChild>
          <Button className="hidden">查看详情</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>活动详情</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-3 gap-4">
            <div className="col-span-2 space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">基本信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">活动名称</p>
                      <p>春季会员招募活动</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">活动类型</p>
                      <Badge variant="secondary">限时折扣</Badge>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">活动时间</p>
                      <p>2025-04-01 至 2025-04-30</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">每日活动时间</p>
                      <p>全天</p>
                    </div>
                    <div className="col-span-2">
                      <p className="text-sm text-muted-foreground">活动说明</p>
                      <p>新会员首月半价，赠送瑜伽垫</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">活动规则</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">适用课程/商品</p>
                      <p>全部课程</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">优惠类型</p>
                      <p>折扣百分比</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">优惠力度</p>
                      <p>50%</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">最低消费要求</p>
                      <p>不限制</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">参与人数限制</p>
                      <p>不限制</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">每人参与次数</p>
                      <p>1次</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">目标人群</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">目标会员</p>
                      <p>新会员</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">会员标签</p>
                      <p>不限制</p>
                    </div>
                    <div className="col-span-2">
                      <p className="text-sm text-muted-foreground">通知方式</p>
                      <div className="flex gap-2 mt-1">
                        <Badge variant="outline">短信通知</Badge>
                        <Badge variant="outline">微信通知</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">活动状态</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <Badge variant="success" className="mb-2">
                      进行中
                    </Badge>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div className="bg-green-600 h-2.5 rounded-full" style={{ width: "45%" }}></div>
                    </div>
                    <p className="text-sm text-muted-foreground">已进行 45%</p>
                    <p className="text-sm">剩余 16 天</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">活动数据</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-muted-foreground">参与人数</p>
                      <p className="text-2xl font-bold">156</p>
                      <p className="text-xs text-green-600">较昨日 +12</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">转化率</p>
                      <p className="text-2xl font-bold">32%</p>
                      <p className="text-xs text-green-600">较昨日 +2.5%</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">带动销售额</p>
                      <p className="text-2xl font-bold">¥15,600</p>
                      <p className="text-xs text-green-600">较昨日 +¥1,200</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex flex-col gap-2">
                <Button>
                  <Edit className="mr-2 h-4 w-4" />
                  编辑活动
                </Button>
                <Button variant="outline">
                  <Share2 className="mr-2 h-4 w-4" />
                  分享活动
                </Button>
                <Button variant="outline">
                  <Pause className="mr-2 h-4 w-4" />
                  暂停活动
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 发放活动对话框 */}
      <Dialog>
        <DialogTrigger asChild>
          <Button className="hidden">发放活动</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>发放活动</DialogTitle>
            <DialogDescription>选择发放方式和目标会员</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">发放方式</label>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer hover:bg-gray-50">
                  <Users className="h-8 w-8 mb-2 text-primary" />
                  <span className="text-sm font-medium">指定会员</span>
                </div>
                <div className="flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer hover:bg-gray-50">
                  <Tags className="h-8 w-8 mb-2 text-primary" />
                  <span className="text-sm font-medium">会员标签</span>
                </div>
                <div className="flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer hover:bg-gray-50">
                  <QrCode className="h-8 w-8 mb-2 text-primary" />
                  <span className="text-sm font-medium">生成二维码</span>
                </div>
                <div className="flex flex-col items-center justify-center p-4 border rounded-md cursor-pointer hover:bg-gray-50">
                  <MessageSquare className="h-8 w-8 mb-2 text-primary" />
                  <span className="text-sm font-medium">短信通知</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">选择会员</label>
              <div className="border rounded-md p-4 max-h-[200px] overflow-y-auto">
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md">
                      <div className="flex items-center">
                        <input type="checkbox" className="h-4 w-4 rounded border-gray-300 mr-2" />
                        <div className="h-8 w-8 rounded-full bg-gray-200 mr-2"></div>
                        <div>
                          <p className="text-sm font-medium">会员 {i + 1}</p>
                          <p className="text-xs text-muted-foreground">138****{1000 + i}</p>
                        </div>
                      </div>
                      <Badge variant="outline">VIP会员</Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="message" className="text-sm font-medium">
                附加消息
              </label>
              <Input id="message" placeholder="可选，发放时附带的消息" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline">取消</Button>
            <Button>确认发放</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 生成二维码对话框 */}
      <Dialog>
        <DialogTrigger asChild>
          <Button className="hidden">生成二维码</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>生成活动二维码</DialogTitle>
            <DialogDescription>生成二维码供会员扫码参与活动</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="qrcode-name" className="text-sm font-medium">
                二维码名称
              </label>
              <Input id="qrcode-name" placeholder="例如：前台展示二维码" />
            </div>

            <div className="space-y-2">
              <label htmlFor="qrcode-limit" className="text-sm font-medium">
                领取限制
              </label>
              <Input id="qrcode-limit" placeholder="例如：不限制或具体数量" />
            </div>

            <div className="space-y-2">
              <label htmlFor="qrcode-expiry" className="text-sm font-medium">
                有效期
              </label>
              <div className="flex gap-2">
                <Button variant="outline" size="icon" className="h-10 w-10">
                  <Calendar className="h-4 w-4" />
                </Button>
                <Input placeholder="截止日期" />
              </div>
            </div>

            <div className="flex justify-center py-4">
              <div className="h-40 w-40 bg-gray-100 rounded-md flex items-center justify-center">
                <QrCode className="h-20 w-20 text-gray-400" />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline">取消</Button>
            <Button>生成二维码</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 分享活动对话框 */}
      <Dialog>
        <DialogTrigger asChild>
          <Button className="hidden">分享活动</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>分享活动</DialogTitle>
            <DialogDescription>将活动分享给潜在客户</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="flex justify-center">
              <div className="h-40 w-full max-w-[300px] bg-gray-100 rounded-md flex items-center justify-center">
                <div className="text-center">
                  <p className="font-medium">春季会员招募活动</p>
                  <p className="text-sm text-muted-foreground">新会员首月半价，赠送瑜伽垫</p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">分享渠道</label>
              <div className="grid grid-cols-4 gap-4">
                <div className="flex flex-col items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-gray-50">
                  <div className="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center mb-1">
                    <MessageCircle className="h-5 w-5 text-white" />
                  </div>
                  <span className="text-xs">微信</span>
                </div>
                <div className="flex flex-col items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-gray-50">
                  <div className="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center mb-1">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <span className="text-xs">微信群</span>
                </div>
                <div className="flex flex-col items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-gray-50">
                  <div className="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center mb-1">
                    <MessageSquare className="h-5 w-5 text-white" />
                  </div>
                  <span className="text-xs">微博</span>
                </div>
                <div className="flex flex-col items-center justify-center p-2 border rounded-md cursor-pointer hover:bg-gray-50">
                  <div className="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center mb-1">
                    <Image className="h-5 w-5 text-white" />
                  </div>
                  <span className="text-xs">朋友圈</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="share-link" className="text-sm font-medium">
                分享链接
              </label>
              <div className="flex gap-2">
                <Input id="share-link" value="https://example.com/promotion/123" readOnly />
                <Button variant="outline" size="icon">
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="share-text" className="text-sm font-medium">
                分享文案
              </label>
              <Input id="share-text" placeholder="自定义分享文案" />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline">取消</Button>
            <Button>分享</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog>
        <DialogTrigger asChild>
          <Button className="hidden">删除活动</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>您确定要删除此活动吗？此操作不可撤销。</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              删除后，所有相关的数据将被永久删除，包括参与记录和统计数据。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline">取消</Button>
            <Button variant="destructive">确认删除</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 暂停确认对话框 */}
      <Dialog>
        <DialogTrigger asChild>
          <Button className="hidden">暂停活动</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>确认暂停</DialogTitle>
            <DialogDescription>您确定要暂停此活动吗？</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              暂停后，会员将无法参与此活动，但您可以随时重新激活。已参与的会员不受影响。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline">取消</Button>
            <Button>确认暂停</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// 模拟数据
const promotions = [
  {
    id: 1,
    name: "春季会员招募活动",
    description: "新会员首月半价，赠送瑜伽垫",
    type: "discount",
    startDate: "2025-04-01",
    endDate: "2025-04-30",
    participants: 156,
    conversionRate: "32%",
    status: "active",
  },
  {
    id: 2,
    name: "三人成团·高级瑜伽课",
    description: "三人成团享受7折优惠",
    type: "group",
    startDate: "2025-04-10",
    endDate: "2025-05-10",
    participants: 87,
    conversionRate: "45%",
    status: "active",
  },
  {
    id: 3,
    name: "周年庆秒杀",
    description: "每日限量10张年卡5折秒杀",
    type: "flash",
    startDate: "2025-05-01",
    endDate: "2025-05-07",
    participants: 0,
    conversionRate: "0%",
    status: "upcoming",
  },
  {
    id: 4,
    name: "会员推荐计划",
    description: "推荐好友入会，双方获赠10次课",
    type: "referral",
    startDate: "2025-03-01",
    endDate: "2025-06-30",
    participants: 203,
    conversionRate: "28%",
    status: "active",
  },
  {
    id: 5,
    name: "冬季特惠活动",
    description: "冬季课程8.5折",
    type: "discount",
    startDate: "2025-01-01",
    endDate: "2025-02-28",
    participants: 342,
    conversionRate: "38%",
    status: "ended",
  },
]

// 辅助函数
function getBadgeVariant(type: string) {
  switch (type) {
    case "discount":
      return "secondary"
    case "group":
      return "default"
    case "flash":
      return "destructive"
    case "referral":
      return "success"
    case "gift":
      return "outline"
    default:
      return "default"
  }
}

function getPromotionTypeName(type: string) {
  switch (type) {
    case "discount":
      return "限时折扣"
    case "group":
      return "拼团活动"
    case "flash":
      return "秒杀活动"
    case "referral":
      return "会员推荐"
    case "gift":
      return "买赠活动"
    default:
      return "其他"
  }
}

function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "active":
      return "success"
    case "upcoming":
      return "secondary"
    case "ended":
      return "outline"
    case "draft":
      return "default"
    default:
      return "default"
  }
}

function getStatusName(status: string) {
  switch (status) {
    case "active":
      return "进行中"
    case "upcoming":
      return "未开始"
    case "ended":
      return "已结束"
    case "draft":
      return "草稿"
    default:
      return "未知"
  }
}

