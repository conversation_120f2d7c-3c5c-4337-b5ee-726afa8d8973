"use client"

import * as React from "react"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Checkbox } from "@/components/ui/checkbox"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import {
  AlertCircle,
  BarChart3,
  CalendarIcon,
  Check,
  ChevronDown,
  Copy,
  Download,
  Edit,
  Eye,
  Filter,
  Gift,
  HelpCircle,
  Info,
  MoreHorizontal,
  Percent,
  Plus,
  QrCode,
  RefreshCw,
  Search,
  Send,
  Settings,
  Share2,
  ShoppingBag,
  Tag,
  Trash,
  Upload,
  Users,
  X,
  MessageSquare,
} from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useToast } from "@/hooks/use-toast"

// 模拟数据
const coupons = [
  {
    id: 1,
    name: "新人专享8折券",
    code: "NEWUSER20",
    type: "discount",
    value: "8折",
    startDate: "2025-04-01",
    endDate: "2025-04-30",
    total: 1000,
    issued: 328,
    used: 156,
    status: "active",
  },
  {
    id: 2,
    name: "会员日满200减50",
    code: "MEM50OFF",
    type: "fixed",
    value: "满200减50",
    startDate: "2025-04-10",
    endDate: "2025-04-12",
    total: 500,
    issued: 213,
    used: 87,
    status: "upcoming",
  },
  {
    id: 3,
    name: "瑜伽课程体验券",
    code: "YOGAFREE",
    type: "free",
    value: "免费体验1次",
    startDate: "2025-03-15",
    endDate: "2025-04-15",
    total: 300,
    issued: 300,
    used: 245,
    status: "depleted",
  },
  {
    id: 4,
    name: "购课送瑜伽垫",
    code: "GIFTMAT",
    type: "gift",
    value: "赠送瑜伽垫1个",
    startDate: "2025-03-01",
    endDate: "2025-03-31",
    total: 100,
    issued: 87,
    used: 80,
    status: "expired",
  },
  {
    id: 5,
    name: "季度卡95折优惠",
    code: "SEASON5",
    type: "discount",
    value: "95折",
    startDate: "2025-04-01",
    endDate: "2025-06-30",
    total: 2000,
    issued: 412,
    used: 0,
    status: "active",
  },
  {
    id: 6,
    name: "普拉提课程满300减100",
    code: "PILATES100",
    type: "fixed",
    value: "满300减100",
    startDate: "2025-04-15",
    endDate: "2025-05-15",
    total: 800,
    issued: 124,
    used: 0,
    status: "upcoming",
  },
  {
    id: 7,
    name: "会员生日专享券",
    code: "BIRTHDAY50",
    type: "fixed",
    value: "满100减50",
    startDate: "2025-04-01",
    endDate: "2025-12-31",
    total: 5000,
    issued: 1245,
    used: 876,
    status: "active",
  },
  {
    id: 8,
    name: "健身体验课赠送",
    code: "FITNESSFREE",
    type: "free",
    value: "免费体验1次",
    startDate: "2025-04-01",
    endDate: "2025-04-30",
    total: 500,
    issued: 342,
    used: 201,
    status: "active",
  },
]

// 辅助函数
function getBadgeVariant(type: string) {
  switch (type) {
    case "discount":
      return "secondary" as const
    case "fixed":
      return "default" as const
    case "gift":
      return "outline" as const
    case "free":
      return "secondary" as const
    default:
      return "default" as const
  }
}

function getCouponTypeName(type: string) {
  switch (type) {
    case "discount":
      return "折扣券"
    case "fixed":
      return "满减券"
    case "gift":
      return "赠品券"
    case "free":
      return "免费券"
    default:
      return "其他"
  }
}

function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "active":
      return "secondary" as const
    case "upcoming":
      return "secondary" as const
    case "expired":
      return "outline" as const
    case "depleted":
      return "destructive" as const
    default:
      return "default" as const
  }
}

function getStatusName(status: string) {
  switch (status) {
    case "active":
      return "进行中"
    case "upcoming":
      return "未开始"
    case "expired":
      return "已过期"
    case "depleted":
      return "已领完"
    default:
      return "未知"
  }
}

export default function CouponsPage() {
  const [date, setDate] = useState<Date>()
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedTab, setSelectedTab] = useState("all")
  const [selectedCoupons, setSelectedCoupons] = useState<number[]>([])
  const [filterStatus, setFilterStatus] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")

  const [selectedCoupon, setSelectedCoupon] = useState<any>(null)
  const [isDetailDrawerOpen, setIsDetailDrawerOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDistributeDialogOpen, setIsDistributeDialogOpen] = useState(false)
  const [isQrCodeDialogOpen, setIsQrCodeDialogOpen] = useState(false)
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false)
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false)
  const [isPauseAlertOpen, setIsPauseAlertOpen] = useState(false)
  const { toast } = useToast()

  // Filter coupons based on selected tab, status filter, and search query
  const filteredCoupons = coupons.filter((coupon) => {
    const matchesTab = selectedTab === "all" || coupon.type === selectedTab
    const matchesStatus = filterStatus === "all" || coupon.status === filterStatus
    const matchesSearch =
      searchQuery === "" ||
      coupon.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      coupon.code.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesTab && matchesStatus && matchesSearch
  })

  const handleSelectAllCoupons = (checked: boolean) => {
    if (checked) {
      setSelectedCoupons(filteredCoupons.map((coupon) => coupon.id))
    } else {
      setSelectedCoupons([])
    }
  }

  const handleSelectCoupon = (id: number, checked: boolean) => {
    if (checked) {
      setSelectedCoupons([...selectedCoupons, id])
    } else {
      setSelectedCoupons(selectedCoupons.filter((couponId) => couponId !== id))
    }
  }

  const isAllSelected = filteredCoupons.length > 0 && selectedCoupons.length === filteredCoupons.length

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">优惠券管理</h1>
          <p className="text-muted-foreground">创建和管理各类优惠券，提升会员消费体验</p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建优惠券
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[700px]">
              <DialogHeader>
                <DialogTitle>创建新优惠券</DialogTitle>
                <DialogDescription>设置优惠券的基本信息、使用规则和发放方式</DialogDescription>
              </DialogHeader>
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">基本信息</TabsTrigger>
                  <TabsTrigger value="rules">使用规则</TabsTrigger>
                  <TabsTrigger value="distribution">发放设置</TabsTrigger>
                </TabsList>
                <TabsContent value="basic" className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="coupon-name">优惠券名称</Label>
                      <Input id="coupon-name" placeholder="例如：新人专享券" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="coupon-code">优惠券代码</Label>
                      <div className="flex gap-2">
                        <Input id="coupon-code" placeholder="例如：NEWUSER20" />
                        <Button variant="outline" size="icon" className="shrink-0">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">留空将自动生成代码</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>优惠券类型</Label>
                    <RadioGroup defaultValue="discount" className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2 rounded-md border p-3">
                        <RadioGroupItem value="discount" id="discount" />
                        <Label htmlFor="discount" className="flex items-center gap-2 font-normal">
                          <Percent className="h-4 w-4 text-muted-foreground" />
                          折扣券
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 rounded-md border p-3">
                        <RadioGroupItem value="fixed" id="fixed" />
                        <Label htmlFor="fixed" className="flex items-center gap-2 font-normal">
                          <Tag className="h-4 w-4 text-muted-foreground" />
                          满减券
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 rounded-md border p-3">
                        <RadioGroupItem value="gift" id="gift" />
                        <Label htmlFor="gift" className="flex items-center gap-2 font-normal">
                          <Gift className="h-4 w-4 text-muted-foreground" />
                          赠品券
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 rounded-md border p-3">
                        <RadioGroupItem value="free" id="free" />
                        <Label htmlFor="free" className="flex items-center gap-2 font-normal">
                          <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                          免费体验券
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="discount-value">优惠金额/折扣</Label>
                      <div className="flex items-center gap-2">
                        <Input id="discount-value" placeholder="例如：50或8.5" />
                        <Select defaultValue="percent">
                          <SelectTrigger className="w-[100px]">
                            <SelectValue placeholder="单位" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="percent">折</SelectItem>
                            <SelectItem value="amount">元</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="quantity">发放数量</Label>
                      <Input id="quantity" placeholder="例如：1000" />
                      <p className="text-xs text-muted-foreground">设置为0表示不限制数量</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>有效期设置</Label>
                    <RadioGroup defaultValue="fixed" className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="fixed-date" id="fixed-date" />
                        <Label htmlFor="fixed-date" className="font-normal">
                          固定日期
                        </Label>
                      </div>
                      <div className="ml-6 grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>开始日期</Label>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !date && "text-muted-foreground",
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {date ? format(date, "PPP") : "选择日期"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar mode="single" selected={date} onSelect={setDate} />
                            </PopoverContent>
                          </Popover>
                        </div>
                        <div className="space-y-2">
                          <Label>结束日期</Label>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !date && "text-muted-foreground",
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {date ? format(date, "PPP") : "选择日期"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar mode="single" selected={date} onSelect={setDate} />
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="dynamic-date" id="dynamic-date" />
                        <Label htmlFor="dynamic-date" className="font-normal">
                          领取后生效
                        </Label>
                      </div>
                      <div className="ml-6 grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="valid-days">有效天数</Label>
                          <Input id="valid-days" placeholder="例如：30" />
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                </TabsContent>
                <TabsContent value="rules" className="space-y-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="min-amount">最低消费金额</Label>
                      <Input id="min-amount" placeholder="例如：100" />
                      <p className="text-xs text-muted-foreground">设置为0表示无最低消费限制</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-discount">最高优惠金额</Label>
                      <Input id="max-discount" placeholder="例如：200" />
                      <p className="text-xs text-muted-foreground">仅适用于折扣券，设置为0表示无上限</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>适用范围</Label>
                    <RadioGroup defaultValue="all" className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="all-courses" id="all-courses" />
                        <Label htmlFor="all-courses" className="font-normal">
                          全部课程
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="category-courses" id="category-courses" />
                        <Label htmlFor="category-courses" className="font-normal">
                          指定课程类别
                        </Label>
                      </div>
                      <div className="ml-6">
                        <Select>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="选择课程类别" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="yoga">瑜伽课程</SelectItem>
                            <SelectItem value="pilates">普拉提课程</SelectItem>
                            <SelectItem value="fitness">健身课程</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="specific-courses" id="specific-courses" />
                        <Label htmlFor="specific-courses" className="font-normal">
                          指定课程
                        </Label>
                      </div>
                      <div className="ml-6">
                        <Button variant="outline" className="w-full justify-start">
                          <Plus className="mr-2 h-4 w-4" />
                          选择课程
                        </Button>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="space-y-2">
                    <Label>使用限制</Label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="new-members-only" />
                        <Label htmlFor="new-members-only" className="font-normal">
                          仅限新会员使用
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="once-per-user" />
                        <Label htmlFor="once-per-user" className="font-normal">
                          每人限用一次
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="not-with-other-coupons" />
                        <Label htmlFor="not-with-other-coupons" className="font-normal">
                          不可与其他优惠券同时使用
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="specific-membership" />
                        <Label htmlFor="specific-membership" className="font-normal">
                          指定会员等级可用
                        </Label>
                      </div>
                      <div className="ml-6">
                        <Select disabled>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="选择会员等级" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="silver">银卡会员</SelectItem>
                            <SelectItem value="gold">金卡会员</SelectItem>
                            <SelectItem value="platinum">白金会员</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">使用说明</Label>
                    <Textarea
                      id="description"
                      placeholder="例如：仅限新会员使用，不可与其他优惠同享"
                      className="min-h-[100px]"
                    />
                  </div>
                </TabsContent>
                <TabsContent value="distribution" className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label>发放方式</Label>
                    <RadioGroup defaultValue="manual" className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="manual-distribution" id="manual-distribution" />
                        <Label htmlFor="manual-distribution" className="font-normal">
                          手动发放
                        </Label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>管理员手动选择会员发放优惠券</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="auto-distribution" id="auto-distribution" />
                        <Label htmlFor="auto-distribution" className="font-normal">
                          自动发放
                        </Label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>根据设定条件自动发放给符合条件的会员</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="ml-6 space-y-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="new-member-register" />
                          <Label htmlFor="new-member-register" className="font-normal">
                            新会员注册时
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="birthday" />
                          <Label htmlFor="birthday" className="font-normal">
                            会员生日当月
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="membership-upgrade" />
                          <Label htmlFor="membership-upgrade" className="font-normal">
                            会员等级升级时
                          </Label>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="self-service" id="self-service" />
                        <Label htmlFor="self-service" className="font-normal">
                          会员自助领取
                        </Label>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger>
                              <HelpCircle className="h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>会员可在小程序/网站自行领取</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="ml-6 space-y-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="limit-per-user" />
                          <Label htmlFor="limit-per-user" className="font-normal">
                            每人限领次数
                          </Label>
                        </div>
                        <div className="ml-6 flex items-center gap-2">
                          <Input id="limit-times" placeholder="例如：1" className="w-24" />
                          <span className="text-sm">次</span>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="visible">在优惠券中心显示</Label>
                      <Switch id="visible" />
                    </div>
                    <p className="text-xs text-muted-foreground">开启后，该优惠券将在会员优惠券中心展示</p>
                  </div>

                  <div className="space-y-2">
                    <Label>优惠券样式预览</Label>
                    <div className="relative h-[120px] w-full overflow-hidden rounded-lg bg-gradient-to-r from-pink-500 to-purple-500 p-4">
                      <div className="absolute right-0 top-0 h-16 w-16 rounded-bl-full bg-white/20"></div>
                      <div className="flex h-full flex-col justify-between">
                        <div>
                          <h3 className="text-lg font-bold text-white">新人专享8折券</h3>
                          <p className="text-sm text-white/80">全场课程通用</p>
                        </div>
                        <div className="flex items-end justify-between">
                          <div className="text-2xl font-bold text-white">8折</div>
                          <Button size="sm" variant="secondary" className="bg-white/90 text-purple-700 hover:bg-white">
                            立即领取
                          </Button>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <Button variant="ghost" size="sm">
                        <Settings className="mr-2 h-4 w-4" />
                        自定义样式
                      </Button>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={() => setIsCreateDialogOpen(false)}>创建优惠券</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                批量操作
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>
                <Upload className="mr-2 h-4 w-4" />
                导入优惠券
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                导出优惠券
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Send className="mr-2 h-4 w-4" />
                批量发放
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" />
                批量复制
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash className="mr-2 h-4 w-4" />
                批量删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="outline">
            <BarChart3 className="mr-2 h-4 w-4" />
            使用统计
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总优惠券数量</CardTitle>
            <Tag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{coupons.length}</div>
            <p className="text-xs text-muted-foreground">
              较上月 <span className="text-green-500">+12.5%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已领取数量</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,340</div>
            <p className="text-xs text-muted-foreground">
              较上月 <span className="text-green-500">+18.2%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已使用数量</CardTitle>
            <Check className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">568</div>
            <p className="text-xs text-muted-foreground">
              较上月 <span className="text-green-500">+7.4%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">使用转化率</CardTitle>
            <Percent className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">42.3%</div>
            <p className="text-xs text-muted-foreground">
              较上月 <span className="text-red-500">-2.1%</span>
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索优惠券名称、代码或描述..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex gap-2">
                <Filter className="h-4 w-4" />
                筛选
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium">优惠券状态</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {["全部", "进行中", "未开始", "已过期", "已领完"].map((status) => (
                      <div key={status} className="flex items-center space-x-2">
                        <Checkbox id={`status-${status}`} />
                        <Label htmlFor={`status-${status}`} className="font-normal">
                          {status}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                <Separator />
                <div className="space-y-2">
                  <h4 className="font-medium">优惠券类型</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {["全部类型", "折扣券", "满减券", "赠品券", "免费券"].map((type) => (
                      <div key={type} className="flex items-center space-x-2">
                        <Checkbox id={`type-${type}`} />
                        <Label htmlFor={`type-${type}`} className="font-normal">
                          {type}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                <Separator />
                <div className="space-y-2">
                  <h4 className="font-medium">发放时间</h4>
                  <div className="grid gap-2">
                    <div className="flex items-center space-x-2">
                      <Label className="w-12">从</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !date && "text-muted-foreground",
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {date ? format(date, "PPP") : "选择日期"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar mode="single" selected={date} onSelect={setDate} />
                        </PopoverContent>
                      </Popover>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Label className="w-12">至</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !date && "text-muted-foreground",
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {date ? format(date, "PPP") : "选择日期"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar mode="single" selected={date} onSelect={setDate} />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline">重置</Button>
                  <Button>应用筛选</Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          <Select defaultValue="all" value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="状态筛选" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="active">进行中</SelectItem>
              <SelectItem value="upcoming">未开始</SelectItem>
              <SelectItem value="expired">已过期</SelectItem>
              <SelectItem value="depleted">已领完</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="all" value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList>
          <TabsTrigger value="all">全部优惠券</TabsTrigger>
          <TabsTrigger value="discount">折扣券</TabsTrigger>
          <TabsTrigger value="fixed">满减券</TabsTrigger>
          <TabsTrigger value="gift">赠品券</TabsTrigger>
          <TabsTrigger value="free">免费体验券</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <Checkbox checked={isAllSelected} onCheckedChange={handleSelectAllCoupons} />
                    </TableHead>
                    <TableHead>优惠券名称</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>优惠内容</TableHead>
                    <TableHead>有效期</TableHead>
                    <TableHead>发放/已领/已用</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCoupons.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center">
                          <Info className="h-8 w-8 text-muted-foreground" />
                          <p className="mt-2">没有找到符合条件的优惠券</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredCoupons.map((coupon) => (
                      <TableRow key={coupon.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedCoupons.includes(coupon.id)}
                            onCheckedChange={(checked) => handleSelectCoupon(coupon.id, !!checked)}
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span>{coupon.name}</span>
                            <span className="text-xs text-muted-foreground">{coupon.code}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getBadgeVariant(coupon.type)}>{getCouponTypeName(coupon.type)}</Badge>
                        </TableCell>
                        <TableCell>{coupon.value}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-xs">{coupon.startDate}</span>
                            <span className="text-xs">至</span>
                            <span className="text-xs">{coupon.endDate}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <div className="flex items-center gap-1">
                              <span>
                                {coupon.issued}/{coupon.total}
                              </span>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <Info className="h-3 w-3 text-muted-foreground" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>已领取/总数量</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <Progress
                              value={(coupon.issued / coupon.total) * 100}
                              className="h-1 w-full bg-white/30"
                            />
                            <span className="text-xs text-muted-foreground">已使用: {coupon.used || 0}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(coupon.status)}>
                            {getStatusName(coupon.status)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem onClick={() => {
                                setSelectedCoupon(coupon)
                                setIsDetailDrawerOpen(true)
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                setSelectedCoupon(coupon)
                                setIsEditDialogOpen(true)
                              }}>
                                <Edit className="mr-2 h-4 w-4" />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                setSelectedCoupon(coupon)
                                setIsDistributeDialogOpen(true)
                              }}>
                                <Send className="mr-2 h-4 w-4" />
                                发放
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                setSelectedCoupon(coupon)
                                setIsQrCodeDialogOpen(true)
                              }}>
                                <QrCode className="mr-2 h-4 w-4" />
                                生成二维码
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                setSelectedCoupon(coupon)
                                setIsShareDialogOpen(true)
                              }}>
                                <Share2 className="mr-2 h-4 w-4" />
                                分享
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => {
                                setSelectedCoupon(coupon)
                                toast({
                                  title: "优惠券已复制",
                                  description: `已成功复制「${coupon.name}」优惠券`,
                                })
                              }}>
                                <Copy className="mr-2 h-4 w-4" />
                                复制
                              </DropdownMenuItem>
                              {coupon.status === 'active' && (
                                <DropdownMenuItem onClick={() => {
                                  setSelectedCoupon(coupon)
                                  setIsPauseAlertOpen(true)
                                }}>
                                  <X className="mr-2 h-4 w-4" />
                                  暂停
                                </DropdownMenuItem>
                              )}
                              {coupon.status === 'upcoming' && (
                                <DropdownMenuItem onClick={() => {
                                  setSelectedCoupon(coupon)
                                  toast({
                                    title: "优惠券已激活",
                                    description: `「${coupon.name}」已立即开始生效`,
                                  })
                                }}>
                                  <AlertCircle className="mr-2 h-4 w-4" />
                                  立即开始
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                className="text-destructive focus:text-destructive"
                                onClick={() => {
                                  setSelectedCoupon(coupon)
                                  setIsDeleteAlertOpen(true)
                                }}
                              >
                                <Trash className="mr-2 h-4 w-4" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
            <CardFooter className="flex items-center justify-between border-t p-4">
              <div className="text-sm text-muted-foreground">
                显示 {filteredCoupons.length} 个优惠券中的 {Math.min(10, filteredCoupons.length)} 个
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" disabled>
                  上一页
                </Button>
                <Button variant="outline" size="sm" className="px-4">
                  1
                </Button>
                <Button variant="outline" size="sm" disabled>
                  下一页
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="discount" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>折扣券</CardTitle>
              <CardDescription>按商品原价的一定比例进行折扣</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {coupons
                  .filter((coupon) => coupon.type === "discount")
                  .map((coupon) => (
                    <div
                      key={coupon.id}
                      className="relative overflow-hidden rounded-lg border bg-gradient-to-r from-violet-500 to-purple-500 p-5 text-white shadow-sm"
                    >
                      <div className="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-white/20"></div>
                      <div className="mb-4 flex items-center justify-between">
                        <Badge variant="secondary" className="bg-white/90 text-purple-700">
                          {getCouponTypeName(coupon.type)}
                        </Badge>
                        <Badge variant={getStatusBadgeVariant(coupon.status)} className="bg-white/90 text-purple-700">
                          {getStatusName(coupon.status)}
                        </Badge>
                      </div>
                      <h3 className="mb-1 text-lg font-bold">{coupon.name}</h3>
                      <p className="mb-4 text-sm text-white/80">优惠码: {coupon.code}</p>
                      <div className="mb-4 flex items-baseline">
                        <span className="text-3xl font-bold">{coupon.value}</span>
                      </div>
                      <div className="mb-2 text-sm">
                        <span>
                          有效期: {coupon.startDate} 至 {coupon.endDate}
                        </span>
                      </div>
                      <div className="mb-4 text-sm">
                        <span>
                          已领取: {coupon.issued}/{coupon.total}
                        </span>
                        <Progress
                          value={(coupon.issued / coupon.total) * 100}
                          className="h-1 w-full bg-white/30"
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button size="sm" variant="secondary" className="bg-white/90 text-purple-700 hover:bg-white">
                          <Edit className="mr-2 h-3 w-3" />
                          编辑
                        </Button>
                        <Button size="sm" variant="secondary" className="bg-white/90 text-purple-700 hover:bg-white">
                          <Send className="mr-2 h-3 w-3" />
                          发放
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="fixed" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>满减券</CardTitle>
              <CardDescription>满足一定金额后减免固定金额</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {coupons
                  .filter((coupon) => coupon.type === "fixed")
                  .map((coupon) => (
                    <div
                      key={coupon.id}
                      className="relative overflow-hidden rounded-lg border bg-gradient-to-r from-pink-500 to-rose-500 p-5 text-white shadow-sm"
                    >
                      <div className="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-white/20"></div>
                      <div className="mb-4 flex items-center justify-between">
                        <Badge variant="secondary" className="bg-white/90 text-rose-700">
                          {getCouponTypeName(coupon.type)}
                        </Badge>
                        <Badge variant={getStatusBadgeVariant(coupon.status)} className="bg-white/90 text-rose-700">
                          {getStatusName(coupon.status)}
                        </Badge>
                      </div>
                      <h3 className="mb-1 text-lg font-bold">{coupon.name}</h3>
                      <p className="mb-4 text-sm text-white/80">优惠码: {coupon.code}</p>
                      <div className="mb-4 flex items-baseline">
                        <span className="text-3xl font-bold">{coupon.value}</span>
                      </div>
                      <div className="mb-2 text-sm">
                        <span>
                          有效期: {coupon.startDate} 至 {coupon.endDate}
                        </span>
                      </div>
                      <div className="mb-4 text-sm">
                        <span>
                          已领取: {coupon.issued}/{coupon.total}
                        </span>
                        <Progress
                          value={(coupon.issued / coupon.total) * 100}
                          className="h-1 w-full bg-white/30"
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button size="sm" variant="secondary" className="bg-white/90 text-rose-700 hover:bg-white">
                          <Edit className="mr-2 h-3 w-3" />
                          编辑
                        </Button>
                        <Button size="sm" variant="secondary" className="bg-white/90 text-rose-700 hover:bg-white">
                          <Send className="mr-2 h-3 w-3" />
                          发放
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="gift" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>赠品券</CardTitle>
              <CardDescription>购买商品后赠送指定商品</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {coupons
                  .filter((coupon) => coupon.type === "gift")
                  .map((coupon) => (
                    <div
                      key={coupon.id}
                      className="relative overflow-hidden rounded-lg border bg-gradient-to-r from-amber-500 to-yellow-500 p-5 text-white shadow-sm"
                    >
                      <div className="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-white/20"></div>
                      <div className="mb-4 flex items-center justify-between">
                        <Badge variant="secondary" className="bg-white/90 text-amber-700">
                          {getCouponTypeName(coupon.type)}
                        </Badge>
                        <Badge variant={getStatusBadgeVariant(coupon.status)} className="bg-white/90 text-amber-700">
                          {getStatusName(coupon.status)}
                        </Badge>
                      </div>
                      <h3 className="mb-1 text-lg font-bold">{coupon.name}</h3>
                      <p className="mb-4 text-sm text-white/80">优惠码: {coupon.code}</p>
                      <div className="mb-4 flex items-baseline">
                        <span className="text-3xl font-bold">{coupon.value}</span>
                      </div>
                      <div className="mb-2 text-sm">
                        <span>
                          有效期: {coupon.startDate} 至 {coupon.endDate}
                        </span>
                      </div>
                      <div className="mb-4 text-sm">
                        <span>
                          已领取: {coupon.issued}/{coupon.total}
                        </span>
                        <Progress
                          value={(coupon.issued / coupon.total) * 100}
                          className="h-1 w-full bg-white/30"
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button size="sm" variant="secondary" className="bg-white/90 text-amber-700 hover:bg-white">
                          <Edit className="mr-2 h-3 w-3" />
                          编辑
                        </Button>
                        <Button size="sm" variant="secondary" className="bg-white/90 text-amber-700 hover:bg-white">
                          <Send className="mr-2 h-3 w-3" />
                          发放
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="free" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>免费体验券</CardTitle>
              <CardDescription>免费体验指定课程或服务</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                {coupons
                  .filter((coupon) => coupon.type === "free")
                  .map((coupon) => (
                    <div
                      key={coupon.id}
                      className="relative overflow-hidden rounded-lg border bg-gradient-to-r from-emerald-500 to-green-500 p-5 text-white shadow-sm"
                    >
                      <div className="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-white/20"></div>
                      <div className="mb-4 flex items-center justify-between">
                        <Badge variant="secondary" className="bg-white/90 text-emerald-700">
                          {getCouponTypeName(coupon.type)}
                        </Badge>
                        <Badge variant={getStatusBadgeVariant(coupon.status)} className="bg-white/90 text-emerald-700">
                          {getStatusName(coupon.status)}
                        </Badge>
                      </div>
                      <h3 className="mb-1 text-lg font-bold">{coupon.name}</h3>
                      <p className="mb-4 text-sm text-white/80">优惠码: {coupon.code}</p>
                      <div className="mb-4 flex items-baseline">
                        <span className="text-3xl font-bold">{coupon.value}</span>
                      </div>
                      <div className="mb-2 text-sm">
                        <span>
                          有效期: {coupon.startDate} 至 {coupon.endDate}
                        </span>
                      </div>
                      <div className="mb-4 text-sm">
                        <span>
                          已领取: {coupon.issued}/{coupon.total}
                        </span>
                        <Progress
                          value={(coupon.issued / coupon.total) * 100}
                          className="h-1 w-full bg-white/30"
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button size="sm" variant="secondary" className="bg-white/90 text-emerald-700 hover:bg-white">
                          <Edit className="mr-2 h-3 w-3" />
                          编辑
                        </Button>
                        <Button size="sm" variant="secondary" className="bg-white/90 text-emerald-700 hover:bg-white">
                          <Send className="mr-2 h-3 w-3" />
                          发放
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 优惠券详情抽屉 */}
      <Drawer open={isDetailDrawerOpen} onOpenChange={setIsDetailDrawerOpen}>
        <DrawerContent className="max-h-[90vh]">
          <DrawerHeader>
            <DrawerTitle>优惠券详情</DrawerTitle>
            <DrawerDescription>查看优惠券的详细信息和使用情况</DrawerDescription>
          </DrawerHeader>
          <ScrollArea className="h-[calc(90vh-10rem)] px-4">
            {selectedCoupon && (
              <div className="space-y-6">
                <div className="relative h-[160px] w-full overflow-hidden rounded-lg bg-gradient-to-r from-pink-500 to-purple-500 p-6">
                  <div className="absolute right-0 top-0 h-20 w-20 rounded-bl-full bg-white/20"></div>
                  <div className="flex h-full flex-col justify-between">
                    <div>
                      <Badge variant={getBadgeVariant(selectedCoupon.type)} className="mb-2">
                        {getCouponTypeName(selectedCoupon.type)}
                      </Badge>
                      <h3 className="text-xl font-bold text-white">{selectedCoupon.name}</h3>
                      <p className="text-sm text-white/80">优惠码: {selectedCoupon.code}</p>
                    </div>
                    <div className="flex items-end justify-between">
                      <div className="text-3xl font-bold text-white">{selectedCoupon.value}</div>
                      <Badge variant={getStatusBadgeVariant(selectedCoupon.status)}>
                        {getStatusName(selectedCoupon.status)}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">基本信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">优惠券类型:</span>
                        <span>{getCouponTypeName(selectedCoupon.type)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">优惠内容:</span>
                        <span>{selectedCoupon.value}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">创建时间:</span>
                        <span>2025-03-28 14:30</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">创建人:</span>
                        <span>管理员</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">最近修改:</span>
                        <span>2025-03-30 09:15</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">使用规则</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">最低消费:</span>
                        <span>¥100</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">适用范围:</span>
                        <span>全部课程</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">使用限制:</span>
                        <span>每人限用一次</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">有效期:</span>
                        <span>{selectedCoupon.startDate} 至 {selectedCoupon.endDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">使用说明:</span>
                        <span>不可与其他优惠同享</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">发放与使用情况</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">发放总量:</span>
                          <span className="font-medium">{selectedCoupon.total}</span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span>已发放:</span>
                            <span>{selectedCoupon.issued} ({((selectedCoupon.issued / selectedCoupon.total) * 100).toFixed(1)}%)</span>
                          </div>
                          <Progress
                            value={(selectedCoupon.issued / selectedCoupon.total) * 100}
                            className="h-2 bg-white/30"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">已领取:</span>
                          <span className="font-medium">{selectedCoupon.issued}</span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span>已使用:</span>
                            <span>{selectedCoupon.used} ({((selectedCoupon.used / selectedCoupon.issued) * 100).toFixed(1)}%)</span>
                          </div>
                          <Progress
                            value={(selectedCoupon.used / selectedCoupon.issued) * 100}
                            className="h-2 bg-white/30"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">带动消费:</span>
                          <span className="font-medium">¥{(selectedCoupon.used * 200).toLocaleString()}</span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span>平均客单价:</span>
                            <span>¥{selectedCoupon.used > 0 ? (selectedCoupon.used * 200 / selectedCoupon.used).toFixed(2) : 0}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="pt-2">
                      <h4 className="mb-2 text-sm font-medium">最近使用记录</h4>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>使用时间</TableHead>
                            <TableHead>会员</TableHead>
                            <TableHead>订单金额</TableHead>
                            <TableHead>优惠金额</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {[1, 2, 3].map((i) => (
                            <TableRow key={i}>
                              <TableCell>2025-04-0{i} 14:3{i}</TableCell>
                              <TableCell>会员{i}</TableCell>
                              <TableCell>¥{180 + i * 20}</TableCell>
                              <TableCell>¥{36 + i * 4}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </ScrollArea>
          <DrawerFooter className="border-t pt-4">
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsEditDialogOpen(true)}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Button>
              <Button variant="outline" onClick={() => setIsDistributeDialogOpen(true)}>
                <Send className="mr-2 h-4 w-4" />
                发放
              </Button>
              <Button variant="outline" onClick={() => setIsQrCodeDialogOpen(true)}>
                <QrCode className="mr-2 h-4 w-4" />
                生成二维码
              </Button>
            </div>
            <DrawerClose asChild>
              <Button variant="outline">关闭</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>

      {/* 发放优惠券对话框 */}
      <Dialog open={isDistributeDialogOpen} onOpenChange={setIsDistributeDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>发放优惠券</DialogTitle>
            <DialogDescription>
              {selectedCoupon && `将「${selectedCoupon.name}」发放给指定会员`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label>发放方式</Label>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2 rounded-md border p-3">
                  <Checkbox id="method-individual" defaultChecked />
                  <Label htmlFor="method-individual" className="flex items-center gap-2 font-normal">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    指定会员
                  </Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-3">
                  <Checkbox id="method-tag" />
                  <Label htmlFor="method-tag" className="flex items-center gap-2 font-normal">
                    <Filter className="h-4 w-4 text-muted-foreground" />
                    会员标签
                  </Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-3">
                  <Checkbox id="method-qrcode" />
                  <Label htmlFor="method-qrcode" className="flex items-center gap-2 font-normal">
                    <QrCode className="h-4 w-4 text-muted-foreground" />
                    生成二维码
                  </Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-3">
                  <Checkbox id="method-message" />
                  <Label htmlFor="method-message" className="flex items-center gap-2 font-normal">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    短信通知
                  </Label>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>选择会员</Label>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-3 w-3" />
                  筛选会员
                </Button>
              </div>
              <div className="max-h-[200px] overflow-y-auto rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">
                        <Checkbox/>
                      </TableHead>
                      <TableHead>会员姓名</TableHead>
                      <TableHead>手机号码</TableHead>
                      <TableHead>会员等级</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {[
                      { id: 1, name: "张三", phone: "138****1234", level: "金卡会员" },
                      { id: 2, name: "李四", phone: "139****5678", level: "银卡会员" },
                      { id: 3, name: "王五", phone: "137****9012", level: "白金会员" },
                      { id: 4, name: "赵六", phone: "136****3456", level: "普通会员" },
                      { id: 5, name: "钱七", phone: "135****7890", level: "金卡会员" },
                    ].map((member) => (
                      <TableRow key={member.id}>
                        <TableCell>
                          <Checkbox />
                        </TableCell>
                        <TableCell>{member.name}</TableCell>
                        <TableCell>{member.phone}</TableCell>
                        <TableCell>{member.level}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>已选择: 0 位会员</span>
                <Button variant="link" size="sm" className="h-auto p-0">
                  导入会员名单
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">发送消息</Label>
              <Textarea
                id="message"
                placeholder="可选：发送优惠券时附带的消息内容"
                className="min-h-[80px]"
                defaultValue={selectedCoupon ? `亲爱的会员，这是我们为您准备的「${selectedCoupon.name}」优惠券，有效期至${selectedCoupon.endDate}，请在有效期内使用。` : ""}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDistributeDialogOpen(false)}>取消</Button>
            <Button onClick={() => {
              setIsDistributeDialogOpen(false)
              toast({
                title: "优惠券发放成功",
                description: "已成功发放给0位会员",
              })
            }}>确认发放</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 生成二维码对话框 */}
      <Dialog open={isQrCodeDialogOpen} onOpenChange={setIsQrCodeDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>生成优惠券二维码</DialogTitle>
            <DialogDescription>
              {selectedCoupon && `为「${selectedCoupon.name}」生成可扫码领取的二维码`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="flex flex-col items-center justify-center">
              <div className="mb-4 h-48 w-48 rounded bg-white p-4 shadow-sm">
                <QrCode className="h-full w-full text-primary" />
              </div>
              <p className="text-sm text-muted-foreground">扫描二维码领取优惠券</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="qrcode-name">二维码名称</Label>
                <Input id="qrcode-name" defaultValue={selectedCoupon ? selectedCoupon.name : ""} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="qrcode-limit">领取限制</Label>
                <Input id="qrcode-limit" placeholder="例如：500" defaultValue="500" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="qrcode-expire">有效期</Label>
              <div className="flex gap-2">
                <Select defaultValue="7">
                  <SelectTrigger>
                    <SelectValue placeholder="选择有效期" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1天</SelectItem>
                    <SelectItem value="3">3天</SelectItem>
                    <SelectItem value="7">7天</SelectItem>
                    <SelectItem value="30">30天</SelectItem>
                    <SelectItem value="custom">自定义</SelectItem>
                  </SelectContent>
                </Select>
                <Input className="w-full" type="date" disabled />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="qrcode-desc">描述信息</Label>
              <Input id="qrcode-desc" placeholder="例如：线下活动推广使用" />
            </div>
          </div>
          <DialogFooter>
            <div className="flex w-full justify-between">
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  下载
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="mr-2 h-4 w-4" />
                  分享
                </Button>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setIsQrCodeDialogOpen(false)}>取消</Button>
                <Button onClick={() => {
                  setIsQrCodeDialogOpen(false)
                  toast({
                    title: "二维码已生成",
                    description: "优惠券二维码已成功生成",
                  })
                }}>确认生成</Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 分享优惠券对话框 */}
      <Dialog open={isShareDialogOpen} onOpenChange={setIsShareDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>分享优惠券</DialogTitle>
            <DialogDescription>
              {selectedCoupon && `分享「${selectedCoupon.name}」到社交媒体或发送给好友`}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="flex flex-col items-center justify-center">
              <div className="relative mb-4 h-[200px] w-full overflow-hidden rounded-lg bg-gradient-to-r from-pink-500 to-purple-500 p-6">
                <div className="absolute right-0 top-0 h-20 w-20 rounded-bl-full bg-white/20"></div>
                <div className="flex h-full flex-col justify-between">
                  <div>
                    <Badge variant="secondary" className="mb-2 bg-white/90 text-purple-700">
                      {selectedCoupon && getCouponTypeName(selectedCoupon.type)}
                    </Badge>
                    <h3 className="text-xl font-bold text-white">{selectedCoupon && selectedCoupon.name}</h3>
                    <p className="text-sm text-white/80">优惠码: {selectedCoupon && selectedCoupon.code}</p>
                  </div>
                  <div className="flex items-end justify-between">
                    <div className="text-3xl font-bold text-white">{selectedCoupon && selectedCoupon.value}</div>
                    <Button size="sm" variant="secondary" className="bg-white/90 text-purple-700 hover:bg-white">
                      立即领取
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>分享方式</Label>
                <div className="grid grid-cols-4 gap-4">
                  <Button variant="outline" className="flex h-auto flex-col items-center gap-2 p-4">
                    <svg className="h-6 w-6 text-green-600" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8.5,13.5A3,3,0,1,0,5.5,10.5,3,3,0,0,0,8.5,13.5Zm0-4.5A1.5,1.5,0,1,1,7,10.5,1.5,1.5,0,0,1,8.5,9Z"/>
                      <path d="M15.5,13.5A3,3,0,1,0,12.5,10.5,3,3,0,0,0,15.5,13.5Zm0-4.5A1.5,1.5,0,1,1,14,10.5,1.5,1.5,0,0,1,15.5,9Z"/>
                      <path d="M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2Zm0,18a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z"/>
                      <path d="M17.41,13.89C17.17,14.13,15.9,15,15.9,15l-.25,1.32a.49.49,0,0,1-.49.39.43.43,0,0,1-.35-.16L14,15.42a3.67,3.67,0,0,1-2.47.77,3.78,3.78,0,0,1-2.49-.77l-.82,1.13a.43.43,0,0,1-.35.16.49.49,0,0,1-.49-.39L7.1,15s-1.27-.87-1.51-1.11,0-.87.31-.87H8.83a3.51,3.51,0,0,1-.33-1.5A3.41,3.41,0,0,1,12,8.5a3.41,3.41,0,0,1,3.5,3.12,3.51,3.51,0,0,1-.33,1.5H18.1C18.45,13.12,17.65,13.65,17.41,13.89Z"/>
                    </svg>
                    <span className="text-xs">微信</span>
                  </Button>
                  <Button variant="outline" className="flex h-auto flex-col items-center gap-2 p-4">
                    <svg className="h-6 w-6 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M16.6,14c-0.2-0.1-1.5-0.7-1.7-0.8c-0.2-0.1-0.4-0.1-0.6,0.1c-0.2,0.2-0.6,0.8-0.8,1c-0.1,0.2-0.3,0.2-0.5,0.1c-0.7-0.3-1.4-0.7-2-1.2c-0.5-0.5-1-1.1-1.4-1.7c-0.1-0.2,0-0.4,0.1-0.5c0.1-0.1,0.2-0.3,0.4-0.4c0.1-0.1,0.2-0.3,0.2-0.4c0.1-0.1,0.1-0.3,0-0.4c-0.1-0.1-0.6-1.3-0.8-1.8C9.4,7.3,9.2,7.3,9,7.3c-0.1,0-0.3,0-0.5,0C8.3,7.3,8,7.5,7.9,7.6C7.3,8.2,7,8.9,7,9.7c0.1,0.9,0.4,1.8,1,2.6c1.1,1.6,2.5,2.9,4.2,3.7c0.5,0.2,0.9,0.4,1.4,0.5c0.5,0.2,1,0.2,1.6,0.1c0.7-0.1,1.3-0.6,1.7-1.2c0.2-0.4,0.2-0.8,0.1-1.2C17,14.2,16.8,14.1,16.6,14"/>
                    </svg>
                    <span className="text-xs">微信群</span>
                  </Button>
                  <Button variant="outline" className="flex h-auto flex-col items-center gap-2 p-4">
                    <svg className="h-6 w-6 text-red-500" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12.003 2c-5.518 0-9.997 4.48-9.997 9.998 0 5.517 4.48 9.997 9.997 9.997 5.518 0 9.998-4.48 9.998-9.997 0-5.518-4.48-9.998-9.998-9.998zm0 1.5c4.69 0 8.498 3.808 8.498 8.498s-3.808 8.497-8.498 8.497-8.497-3.807-8.497-8.497 3.807-8.498 8.497-8.498zm2.502 8.495c0-.69-.376-1.295-.936-1.616v-.001c.598-.317.998-.95.998-1.677 0-1.05-.852-1.9-1.902-1.9h-2.666v7.191h2.666c1.05 0 1.902-.85 1.902-1.9 0-.727-.4-1.36-.998-1.677.56-.32.936-.926.936-1.615v-.005zm-3.504-3.694h1.566c.234 0 .423.19.423.423 0 .234-.189.423-.423h-1.566v-.846zm1.566 5.694h-1.566v-.846h1.566c.234 0 .423.189.423.423s-.189.423-.423.423z"/>
                    </svg>
                    <span className="text-xs">微博</span>
                  </Button>
                  <Button variant="outline" className="flex h-auto flex-col items-center gap-2 p-4">
                    <svg className="h-6 w-6 text-blue-500" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                    <span className="text-xs">朋友圈</span>
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label>分享链接</Label>
                <div className="flex gap-2">
                  <Input value="https://example.com/coupon/NEWUSER20" readOnly />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      toast({
                        title: "链接已复制",
                        description: "优惠券链接已复制到剪贴板",
                      })
                    }}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="share-message">分享文案</Label>
                <Textarea
                  id="share-message"
                  className="min-h-[80px]"
                  defaultValue={selectedCoupon ? `限时优惠！「${selectedCoupon.name}」${selectedCoupon.value}，有效期至${selectedCoupon.endDate}，赶快领取吧！` : ""}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsShareDialogOpen(false)}>取消</Button>
            <Button onClick={() => {
              setIsShareDialogOpen(false)
              toast({
                title: "优惠券分享成功",
                description: "优惠券已成功分享",
              })
            }}>确认分享</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除优惠券确认对话框 */}
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除优惠券？</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedCoupon && `您确定要删除「${selectedCoupon.name}」优惠券吗？删除后将无法恢复。`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsDeleteAlertOpen(false)}>取消</AlertDialogCancel>
            <AlertDialogAction className="bg-red-500 hover:bg-red-600 text-white" onClick={() => {
              setIsDeleteAlertOpen(false)
              toast({
                title: "优惠券已删除",
                description: selectedCoupon ? `「${selectedCoupon.name}」已成功删除` : "优惠券已成功删除",
              })
            }}>删除</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 暂停优惠券确认对话框 */}
      <AlertDialog open={isPauseAlertOpen} onOpenChange={setIsPauseAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认暂停优惠券？</AlertDialogTitle>
            <AlertDialogDescription>
              {selectedCoupon && `您确定要暂停「${selectedCoupon.name}」优惠券吗？暂停后用户将无法领取和使用。`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsPauseAlertOpen(false)}>取消</AlertDialogCancel>
            <AlertDialogAction onClick={() => {
              setIsPauseAlertOpen(false)
              toast({
                title: "优惠券已暂停",
                description: selectedCoupon ? `「${selectedCoupon.name}」已成功暂停` : "优惠券已成功暂停",
              })
            }}>确认暂停</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 编辑优惠券抽屉 */}
      <Drawer open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DrawerContent className="max-h-[90vh]">
          <DrawerHeader>
            <DrawerTitle>编辑优惠券</DrawerTitle>
            <DrawerDescription>修改优惠券的基本信息、使用规则和发放方式</DrawerDescription>
          </DrawerHeader>
          <ScrollArea className="h-[calc(90vh-10rem)] px-4">
            {selectedCoupon && (
              <div className="space-y-6">
                <div className="relative h-[160px] w-full overflow-hidden rounded-lg bg-gradient-to-r from-pink-500 to-purple-500 p-6">
                  <div className="absolute right-0 top-0 h-20 w-20 rounded-bl-full bg-white/20"></div>
                  <div className="flex h-full flex-col justify-between">
                    <div>
                      <Badge variant={getBadgeVariant(selectedCoupon.type)} className="mb-2">
                        {getCouponTypeName(selectedCoupon.type)}
                      </Badge>
                      <h3 className="text-xl font-bold text-white">{selectedCoupon.name}</h3>
                      <p className="text-sm text-white/80">优惠码: {selectedCoupon.code}</p>
                    </div>
                    <div className="flex items-end justify-between">
                      <div className="text-3xl font-bold text-white">{selectedCoupon.value}</div>
                      <Badge variant={getStatusBadgeVariant(selectedCoupon.status)}>
                        {getStatusName(selectedCoupon.status)}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">基本信息</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">优惠券类型:</span>
                        <span>{getCouponTypeName(selectedCoupon.type)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">优惠内容:</span>
                        <span>{selectedCoupon.value}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">创建时间:</span>
                        <span>2025-03-28 14:30</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">创建人:</span>
                        <span>管理员</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">最近修改:</span>
                        <span>2025-03-30 09:15</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">使用规则</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">最低消费:</span>
                        <span>¥100</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">适用范围:</span>
                        <span>全部课程</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">使用限制:</span>
                        <span>每人限用一次</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">有效期:</span>
                        <span>{selectedCoupon.startDate} 至 {selectedCoupon.endDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">使用说明:</span>
                        <span>不可与其他优惠同享</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">发放与使用情况</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-4 md:grid-cols-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">发放总量:</span>
                          <span className="font-medium">{selectedCoupon.total}</span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span>已发放:</span>
                            <span>{selectedCoupon.issued} ({((selectedCoupon.issued / selectedCoupon.total) * 100).toFixed(1)}%)</span>
                          </div>
                          <Progress
                            value={(selectedCoupon.issued / selectedCoupon.total) * 100}
                            className="h-2 bg-white/30"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">已领取:</span>
                          <span className="font-medium">{selectedCoupon.issued}</span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span>已使用:</span>
                            <span>{selectedCoupon.used} ({((selectedCoupon.used / selectedCoupon.issued) * 100).toFixed(1)}%)</span>
                          </div>
                          <Progress
                            value={(selectedCoupon.used / selectedCoupon.issued) * 100}
                            className="h-2 bg-white/30"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">带动消费:</span>
                          <span className="font-medium">¥{(selectedCoupon.used * 200).toLocaleString()}</span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span>平均客单价:</span>
                            <span>¥{selectedCoupon.used > 0 ? (selectedCoupon.used * 200 / selectedCoupon.used).toFixed(2) : 0}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="pt-2">
                      <h4 className="mb-2 text-sm font-medium">最近使用记录</h4>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>使用时间</TableHead>
                            <TableHead>会员</TableHead>
                            <TableHead>订单金额</TableHead>
                            <TableHead>优惠金额</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {[1, 2, 3].map((i) => (
                            <TableRow key={i}>
                              <TableCell>2025-04-0{i} 14:3{i}</TableCell>
                              <TableCell>会员{i}</TableCell>
                              <TableCell>¥{180 + i * 20}</TableCell>
                              <TableCell>¥{36 + i * 4}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </ScrollArea>
          <DrawerFooter className="border-t pt-4">
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setIsDistributeDialogOpen(true)}>
                <Send className="mr-2 h-4 w-4" />
                发放
              </Button>
              <Button variant="outline" onClick={() => setIsQrCodeDialogOpen(true)}>
                <QrCode className="mr-2 h-4 w-4" />
                生成二维码
              </Button>
            </div>
            <DrawerClose asChild>
              <Button variant="outline">保存</Button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    </div>
  )
}

