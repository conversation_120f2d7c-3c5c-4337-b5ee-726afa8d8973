"use client"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface GenderDistributionChartProps {
  cardId: number | string
  className?: string
}

export function GenderDistributionChart({ cardId, className }: GenderDistributionChartProps) {
  // 模拟性别分布数据
  const genderData = [
    { gender: "女", count: 65, color: "#FF6B8B" },
    { gender: "男", count: 35, color: "#4F46E5" },
  ]
  
  // 计算总数
  const total = genderData.reduce((sum, item) => sum + item.count, 0)
  
  // 渲染饼图
  const renderPieChart = () => {
    return (
      <div className="relative mx-auto h-[180px] w-[180px]">
        {/* 中心文字 */}
        <div className="absolute inset-0 flex items-center justify-center rounded-full border-8 border-background">
          <div className="text-center">
            <div className="text-2xl font-bold">{total}</div>
            <div className="text-xs text-muted-foreground">会员总数</div>
          </div>
        </div>
        
        {/* 饼图 */}
        <svg className="h-full w-full -rotate-90" viewBox="0 0 100 100">
          {/* 女性部分 */}
          <path
            d={`
              M 50 50
              L 50 10
              A 40 40 0 0 1 ${50 + 40 * Math.cos((genderData[0].count / 100) * 2 * Math.PI)} ${50 + 40 * Math.sin((genderData[0].count / 100) * 2 * Math.PI)}
              Z
            `}
            fill={genderData[0].color}
            stroke="white"
            strokeWidth="1"
            className="transition-opacity duration-300 hover:opacity-80"
          />
          
          {/* 男性部分 */}
          <path
            d={`
              M 50 50
              L ${50 + 40 * Math.cos((genderData[0].count / 100) * 2 * Math.PI)} ${50 + 40 * Math.sin((genderData[0].count / 100) * 2 * Math.PI)}
              A 40 40 0 0 1 50 10
              Z
            `}
            fill={genderData[1].color}
            stroke="white"
            strokeWidth="1"
            className="transition-opacity duration-300 hover:opacity-80"
          />
        </svg>
      </div>
    )
  }
  
  // 渲染图例和数据
  const renderLegend = () => {
    return (
      <div className="mt-4 flex justify-center gap-8">
        {genderData.map((item, index) => (
          <div key={index} className="flex flex-col items-center">
            <div className="flex items-center gap-2">
              <div 
                className="h-3 w-3 rounded-sm" 
                style={{ backgroundColor: item.color }}
              />
              <span className="text-sm">{item.gender}</span>
            </div>
            <div className="mt-1 text-center">
              <div className="text-lg font-medium">{item.count}%</div>
              <div className="text-xs text-muted-foreground">{Math.round(item.count * total / 100)} 人</div>
            </div>
          </div>
        ))}
      </div>
    )
  }
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-6">
        <h3 className="mb-4 text-center text-sm font-medium">会员性别分布</h3>
        {renderPieChart()}
        {renderLegend()}
      </CardContent>
    </Card>
  )
}
