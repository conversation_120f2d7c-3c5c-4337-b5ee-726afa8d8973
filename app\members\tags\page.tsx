"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent } from "@/components/ui/card"
import { MoreHorizontal, Pencil, Trash2, Plus, Search, LayoutGrid, List, Users, Tag, BarChart4 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { TagDialog } from "@/components/members/tags/tag-dialog"
import { TagDetailDialog } from "@/components/members/tags/tag-detail-dialog"
import { TagGrid } from "@/components/members/tags/tag-grid"
import { TagStatsDialog } from "@/components/members/tags/tag-stats-dialog"
import { AutoTagRulesDialog } from "@/components/members/tags/auto-tag-rules-dialog"

const memberTags = [
  {
    id: 1,
    name: "新会员",
    color: "#4285F4",
    description: "注册时间不超过30天的会员",
    members: 45,
    category: "系统",
    isAutoTag: true,
    createdAt: "2023-05-15",
  },
  {
    id: 2,
    name: "VIP",
    color: "#FBBC05",
    description: "消费金额超过5000元的会员",
    members: 28,
    category: "消费",
    isAutoTag: true,
    createdAt: "2023-06-20",
  },
  {
    id: 3,
    name: "潜在流失",
    color: "#EA4335",
    description: "超过30天未到店的会员",
    members: 32,
    category: "活跃度",
    isAutoTag: true,
    createdAt: "2023-07-10",
  },
  {
    id: 4,
    name: "瑜伽爱好者",
    color: "#34A853",
    description: "参加瑜伽课程超过20次的会员",
    members: 65,
    category: "兴趣",
    isAutoTag: false,
    createdAt: "2023-08-05",
  },
  {
    id: 5,
    name: "老会员",
    color: "#9C27B0",
    description: "会员时间超过1年的会员",
    members: 78,
    category: "系统",
    isAutoTag: true,
    createdAt: "2023-04-12",
  },
  {
    id: 6,
    name: "教练推荐",
    color: "#FF6D91",
    description: "由教练特别推荐的会员",
    members: 15,
    category: "其他",
    isAutoTag: false,
    createdAt: "2023-09-18",
  },
  {
    id: 7,
    name: "高频消费",
    color: "#00BCD4",
    description: "月均消费超过1000元的会员",
    members: 23,
    category: "消费",
    isAutoTag: true,
    createdAt: "2023-10-05",
  },
  {
    id: 8,
    name: "周末班",
    color: "#FF9800",
    description: "主要在周末参加课程的会员",
    members: 42,
    category: "时间",
    isAutoTag: false,
    createdAt: "2023-11-15",
  },
]

export default function MemberTagsPage() {
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")
  const [openTagDialog, setOpenTagDialog] = useState(false)
  const [openDetailDialog, setOpenDetailDialog] = useState(false)
  const [openStatsDialog, setOpenStatsDialog] = useState(false)
  const [openRulesDialog, setOpenRulesDialog] = useState(false)
  const [selectedTag, setSelectedTag] = useState<any>(null)
  const [searchQuery, setSearchQuery] = useState("")

  const filteredTags = memberTags.filter(
    (tag) =>
      tag.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tag.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tag.category.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleOpenTagDialog = (tag?: any) => {
    setSelectedTag(tag || null)
    setOpenTagDialog(true)
  }

  const handleOpenDetailDialog = (tag: any) => {
    setSelectedTag(tag)
    setOpenDetailDialog(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">会员标签</h1>
        <div className="flex space-x-2">
          <Button onClick={() => setOpenRulesDialog(true)} variant="outline">
            <Tag className="mr-2 h-4 w-4" />
            自动标签规则
          </Button>
          <Button onClick={() => setOpenStatsDialog(true)} variant="outline">
            <BarChart4 className="mr-2 h-4 w-4" />
            标签统计
          </Button>
          <Button onClick={() => handleOpenTagDialog()}>
            <Plus className="mr-2 h-4 w-4" />
            添加标签
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">全部标签</TabsTrigger>
          <TabsTrigger value="system">系统标签</TabsTrigger>
          <TabsTrigger value="custom">自定义标签</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="space-y-4">
          <div className="flex flex-col gap-4 md:flex-row justify-between">
            <div className="w-full md:w-1/3 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索标签"
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="icon"
                onClick={() => setViewMode("grid")}
              >
                <LayoutGrid className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {viewMode === "list" ? (
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">颜色</TableHead>
                      <TableHead>标签名称</TableHead>
                      <TableHead className="hidden md:table-cell">描述</TableHead>
                      <TableHead>会员数量</TableHead>
                      <TableHead className="hidden md:table-cell">分类</TableHead>
                      <TableHead className="hidden md:table-cell">创建时间</TableHead>
                      <TableHead className="hidden md:table-cell">类型</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTags.map((tag) => (
                      <TableRow key={tag.id} className="cursor-pointer" onClick={() => handleOpenDetailDialog(tag)}>
                        <TableCell>
                          <div className="h-6 w-6 rounded-full" style={{ backgroundColor: tag.color }} />
                        </TableCell>
                        <TableCell className="font-medium">{tag.name}</TableCell>
                        <TableCell className="hidden md:table-cell max-w-xs truncate">{tag.description}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                            {tag.members}
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <Badge variant="outline">{tag.category}</Badge>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">{tag.createdAt}</TableCell>
                        <TableCell className="hidden md:table-cell">
                          {tag.isAutoTag ? (
                            <Badge variant="secondary">自动</Badge>
                          ) : (
                            <Badge variant="outline">手动</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>操作</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleOpenDetailDialog(tag)
                                }}
                              >
                                <Users className="mr-2 h-4 w-4" />
                                查看会员
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleOpenTagDialog(tag)
                                }}
                              >
                                <Pencil className="mr-2 h-4 w-4" />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-red-600" onClick={(e) => e.stopPropagation()}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <TagGrid tags={filteredTags} onTagClick={handleOpenDetailDialog} onEditClick={handleOpenTagDialog} />
          )}
        </TabsContent>
        <TabsContent value="system" className="space-y-4">
          {viewMode === "list" ? (
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">颜色</TableHead>
                      <TableHead>标签名称</TableHead>
                      <TableHead className="hidden md:table-cell">描述</TableHead>
                      <TableHead>会员数量</TableHead>
                      <TableHead className="hidden md:table-cell">分类</TableHead>
                      <TableHead className="hidden md:table-cell">创建时间</TableHead>
                      <TableHead className="hidden md:table-cell">类型</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTags
                      .filter((tag) => tag.category === "系统")
                      .map((tag) => (
                        <TableRow key={tag.id} className="cursor-pointer" onClick={() => handleOpenDetailDialog(tag)}>
                          <TableCell>
                            <div className="h-6 w-6 rounded-full" style={{ backgroundColor: tag.color }} />
                          </TableCell>
                          <TableCell className="font-medium">{tag.name}</TableCell>
                          <TableCell className="hidden md:table-cell max-w-xs truncate">{tag.description}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                              {tag.members}
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            <Badge variant="outline">{tag.category}</Badge>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">{tag.createdAt}</TableCell>
                          <TableCell className="hidden md:table-cell">
                            {tag.isAutoTag ? (
                              <Badge variant="secondary">自动</Badge>
                            ) : (
                              <Badge variant="outline">手动</Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>操作</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleOpenDetailDialog(tag)
                                  }}
                                >
                                  <Users className="mr-2 h-4 w-4" />
                                  查看会员
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <TagGrid
              tags={filteredTags.filter((tag) => tag.category === "系统")}
              onTagClick={handleOpenDetailDialog}
              onEditClick={handleOpenTagDialog}
              systemOnly={true}
            />
          )}
        </TabsContent>
        <TabsContent value="custom" className="space-y-4">
          {viewMode === "list" ? (
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">颜色</TableHead>
                      <TableHead>标签名称</TableHead>
                      <TableHead className="hidden md:table-cell">描述</TableHead>
                      <TableHead>会员数量</TableHead>
                      <TableHead className="hidden md:table-cell">分类</TableHead>
                      <TableHead className="hidden md:table-cell">创建时间</TableHead>
                      <TableHead className="hidden md:table-cell">类型</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTags
                      .filter((tag) => tag.category !== "系统")
                      .map((tag) => (
                        <TableRow key={tag.id} className="cursor-pointer" onClick={() => handleOpenDetailDialog(tag)}>
                          <TableCell>
                            <div className="h-6 w-6 rounded-full" style={{ backgroundColor: tag.color }} />
                          </TableCell>
                          <TableCell className="font-medium">{tag.name}</TableCell>
                          <TableCell className="hidden md:table-cell max-w-xs truncate">{tag.description}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                              {tag.members}
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            <Badge variant="outline">{tag.category}</Badge>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">{tag.createdAt}</TableCell>
                          <TableCell className="hidden md:table-cell">
                            {tag.isAutoTag ? (
                              <Badge variant="secondary">自动</Badge>
                            ) : (
                              <Badge variant="outline">手动</Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>操作</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleOpenDetailDialog(tag)
                                  }}
                                >
                                  <Users className="mr-2 h-4 w-4" />
                                  查看会员
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    handleOpenTagDialog(tag)
                                  }}
                                >
                                  <Pencil className="mr-2 h-4 w-4" />
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-red-600" onClick={(e) => e.stopPropagation()}>
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          ) : (
            <TagGrid
              tags={filteredTags.filter((tag) => tag.category !== "系统")}
              onTagClick={handleOpenDetailDialog}
              onEditClick={handleOpenTagDialog}
            />
          )}
        </TabsContent>
      </Tabs>

      <TagDialog open={openTagDialog} onOpenChange={setOpenTagDialog} tag={selectedTag} />

      <TagDetailDialog
        open={openDetailDialog}
        onOpenChange={setOpenDetailDialog}
        tag={selectedTag}
        onEditClick={() => {
          setOpenDetailDialog(false)
          setTimeout(() => handleOpenTagDialog(selectedTag), 100)
        }}
      />

      <TagStatsDialog open={openStatsDialog} onOpenChange={setOpenStatsDialog} />

      <AutoTagRulesDialog open={openRulesDialog} onOpenChange={setOpenRulesDialog} />
    </div>
  )
}

