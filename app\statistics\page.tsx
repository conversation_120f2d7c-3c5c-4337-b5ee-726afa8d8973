"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar, Download, TrendingUp, Users, DollarSign, BookOpen, Award, Bell } from "lucide-react"
import Link from "next/link"

export default function StatisticsPage() {
  const [dateRange, setDateRange] = useState("30days")

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">数据统计分析</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Calendar className="h-4 w-4" />
            <span>选择时间范围</span>
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="flex flex-1 gap-4">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="thismonth">本月</SelectItem>
              <SelectItem value="lastmonth">上月</SelectItem>
              <SelectItem value="thisyear">今年</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Link href="/statistics/dashboard" className="block">
          <Card className="h-full hover:border-primary transition-colors">
            <CardHeader>
              <CardTitle>综合仪表盘</CardTitle>
              <CardDescription>业务关键指标概览和分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-24 items-center justify-center rounded-md border-2 border-dashed">
                <Bell className="h-10 w-10 text-primary opacity-50" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full justify-start">
                查看详情
              </Button>
            </CardFooter>
          </Card>
        </Link>

        <Link href="/statistics/members" className="block">
          <Card className="h-full hover:border-primary transition-colors">
            <CardHeader>
              <CardTitle>会员分析</CardTitle>
              <CardDescription>会员增长、流失和行为分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-24 items-center justify-center rounded-md border-2 border-dashed">
                <Users className="h-10 w-10 text-primary opacity-50" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full justify-start">
                查看详情
              </Button>
            </CardFooter>
          </Card>
        </Link>

        <Link href="/statistics/courses" className="block">
          <Card className="h-full hover:border-primary transition-colors">
            <CardHeader>
              <CardTitle>课程分析</CardTitle>
              <CardDescription>课程预约、满意度和热度分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-24 items-center justify-center rounded-md border-2 border-dashed">
                <BookOpen className="h-10 w-10 text-primary opacity-50" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full justify-start">
                查看详情
              </Button>
            </CardFooter>
          </Card>
        </Link>

        <Link href="/statistics/revenue" className="block">
          <Card className="h-full hover:border-primary transition-colors">
            <CardHeader>
              <CardTitle>收入分析</CardTitle>
              <CardDescription>收入来源、趋势和预测分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-24 items-center justify-center rounded-md border-2 border-dashed">
                <DollarSign className="h-10 w-10 text-primary opacity-50" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full justify-start">
                查看详情
              </Button>
            </CardFooter>
          </Card>
        </Link>

        <Link href="/statistics/coaches" className="block">
          <Card className="h-full hover:border-primary transition-colors">
            <CardHeader>
              <CardTitle>教练分析</CardTitle>
              <CardDescription>教练绩效、评分和工时分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-24 items-center justify-center rounded-md border-2 border-dashed">
                <Award className="h-10 w-10 text-primary opacity-50" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full justify-start">
                查看详情
              </Button>
            </CardFooter>
          </Card>
        </Link>

        <Link href="/statistics/marketing" className="block">
          <Card className="h-full hover:border-primary transition-colors">
            <CardHeader>
              <CardTitle>营销分析</CardTitle>
              <CardDescription>营销活动效果和投资回报分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-24 items-center justify-center rounded-md border-2 border-dashed">
                <TrendingUp className="h-10 w-10 text-primary opacity-50" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full justify-start">
                查看详情
              </Button>
            </CardFooter>
          </Card>
        </Link>

        <Link href="/statistics/operational" className="block">
          <Card className="h-full hover:border-primary transition-colors">
            <CardHeader>
              <CardTitle>运营分析</CardTitle>
              <CardDescription>场地利用率和运营效率分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-24 items-center justify-center rounded-md border-2 border-dashed">
                <Bell className="h-10 w-10 text-primary opacity-50" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full justify-start">
                查看详情
              </Button>
            </CardFooter>
          </Card>
        </Link>

        <Link href="/orders/statistics" className="block">
          <Card className="h-full hover:border-primary transition-colors">
            <CardHeader>
              <CardTitle>订单分析</CardTitle>
              <CardDescription>订单量、金额和转化率分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-24 items-center justify-center rounded-md border-2 border-dashed">
                <DollarSign className="h-10 w-10 text-primary opacity-50" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full justify-start">
                查看详情
              </Button>
            </CardFooter>
          </Card>
        </Link>

        <Link href="/verification/statistics" className="block">
          <Card className="h-full hover:border-primary transition-colors">
            <CardHeader>
              <CardTitle>核销分析</CardTitle>
              <CardDescription>核销数量、金额和转化率分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-24 items-center justify-center rounded-md border-2 border-dashed">
                <Bell className="h-10 w-10 text-primary opacity-50" />
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full justify-start">
                查看详情
              </Button>
            </CardFooter>
          </Card>
        </Link>
      </div>
    </div>
  )
}

