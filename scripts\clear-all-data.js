// 清空所有课程和课程类型数据
async function clearAllData() {
  console.log('开始清空所有数据...');
  
  try {
    // 1. 先删除所有课程
    console.log('\n1. 删除所有课程:');
    const coursesResponse = await fetch('http://localhost:3001/api/courses?tenantId=1&page=1&pageSize=100');
    const coursesResult = await coursesResponse.json();
    
    if (coursesResult.code === 200 && coursesResult.data.list.length > 0) {
      console.log(`找到 ${coursesResult.data.list.length} 个课程`);
      
      for (const course of coursesResult.data.list) {
        const deleteResponse = await fetch(`http://localhost:3001/api/courses/${course.id}`, {
          method: 'DELETE'
        });
        
        const deleteResult = await deleteResponse.json();
        if (deleteResult.code === 200) {
          console.log(`  ✓ 删除课程: ${course.title}`);
        } else {
          console.log(`  ✗ 删除课程失败: ${course.title} - ${deleteResult.msg}`);
        }
      }
    } else {
      console.log('没有找到课程数据');
    }
    
    // 2. 再删除所有课程类型
    console.log('\n2. 删除所有课程类型:');
    const typesResponse = await fetch('http://localhost:3001/api/course-types?tenantId=1');
    const typesResult = await typesResponse.json();
    
    if (typesResult.code === 200 && typesResult.data.list.length > 0) {
      console.log(`找到 ${typesResult.data.list.length} 个课程类型`);
      
      for (const type of typesResult.data.list) {
        const deleteResponse = await fetch(`http://localhost:3001/api/course-types/${type.id}`, {
          method: 'DELETE'
        });
        
        const deleteResult = await deleteResponse.json();
        if (deleteResult.code === 200) {
          console.log(`  ✓ 删除课程类型: ${type.name}`);
        } else {
          console.log(`  ✗ 删除课程类型失败: ${type.name} - ${deleteResult.msg}`);
        }
      }
    } else {
      console.log('没有找到课程类型数据');
    }
    
    // 3. 验证清空结果
    console.log('\n3. 验证清空结果:');
    const finalTypesResponse = await fetch('http://localhost:3001/api/course-types?tenantId=1');
    const finalTypesResult = await finalTypesResponse.json();
    
    const finalCoursesResponse = await fetch('http://localhost:3001/api/courses?tenantId=1&page=1&pageSize=10');
    const finalCoursesResult = await finalCoursesResponse.json();
    
    console.log(`✓ 剩余课程类型: ${finalTypesResult.data?.list?.length || 0} 个`);
    console.log(`✓ 剩余课程: ${finalCoursesResult.data?.list?.length || 0} 个`);
    
    console.log('\n✓ 数据清空完成!');
    console.log('\n现在可以测试空状态下的添加课程类型功能了！');
    console.log('请访问: http://localhost:3001/courses/types');
    
  } catch (error) {
    console.error('清空失败:', error.message);
  }
}

clearAllData();
