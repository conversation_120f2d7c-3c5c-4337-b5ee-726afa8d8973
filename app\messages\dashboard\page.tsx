"use client"

import React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Bell, 
  BarChart3, 
  Users, 
  Calendar, 
  CreditCard, 
  ArrowUpRight, 
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Clock,
  Send
} from "lucide-react"
import { ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, LineChart, Line } from 'recharts'

// 示例数据
const messageStats = {
  total: 12580,
  delivered: 12245,
  read: 9876,
  pending: 335,
  failed: 124
}

const channelData = [
  { name: '短信', value: 4300 },
  { name: '微信', value: 5200 },
  { name: 'APP推送', value: 2800 },
  { name: '系统内通知', value: 280 },
]

const typeData = [
  { name: '预约通知', value: 3800 },
  { name: '课程提醒', value: 2500 },
  { name: '会员卡通知', value: 1800 },
  { name: '营销通知', value: 1200 },
  { name: '系统通知', value: 800 },
]

const deliveryRateData = [
  { name: '周一', 短信: 98.2, 微信: 99.5, APP推送: 97.8 },
  { name: '周二', 短信: 97.5, 微信: 99.3, APP推送: 98.1 },
  { name: '周三', 短信: 98.7, 微信: 99.7, APP推送: 97.5 },
  { name: '周四', 短信: 99.1, 微信: 99.4, APP推送: 98.3 },
  { name: '周五', 短信: 98.4, 微信: 99.6, APP推送: 97.9 },
  { name: '周六', 短信: 97.8, 微信: 99.2, APP推送: 96.8 },
  { name: '周日', 短信: 96.9, 微信: 98.9, APP推送: 96.2 },
]

const recentMessages = [
  { id: 1, type: '预约成功', recipient: '张三', channel: '微信', status: '已送达', time: '10分钟前' },
  { id: 2, type: '课程提醒', recipient: '李四', channel: '短信', status: '已送达', time: '30分钟前' },
  { id: 3, type: '会员卡到期', recipient: '王五', channel: 'APP推送', status: '已读', time: '1小时前' },
  { id: 4, type: '营销活动', recipient: '赵六', channel: '微信', status: '未送达', time: '2小时前' },
  { id: 5, type: '课程取消', recipient: '钱七', channel: '短信', status: '已送达', time: '3小时前' },
]

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

export default function MessageDashboardPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">消息概览</h1>
          <p className="text-sm text-muted-foreground mt-1">查看消息发送统计和最近活动</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Clock className="mr-2 h-4 w-4" />
            最近7天
          </Button>
          <Button>
            <Send className="mr-2 h-4 w-4" />
            发送消息
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">消息总量</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{messageStats.total.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              较上周 <span className="text-green-500">+12.5%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">送达率</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{((messageStats.delivered / messageStats.total) * 100).toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              较上周 <span className="text-green-500">+0.8%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">阅读率</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{((messageStats.read / messageStats.delivered) * 100).toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              较上周 <span className="text-green-500">+1.2%</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待发送</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{messageStats.pending}</div>
            <p className="text-xs text-muted-foreground">
              较昨日 <span className="text-red-500">+15</span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">发送失败</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{messageStats.failed}</div>
            <p className="text-xs text-muted-foreground">
              较上周 <span className="text-green-500">-8.3%</span>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 图表区域 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>消息送达率趋势</CardTitle>
            <CardDescription>按渠道统计的消息送达率</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={deliveryRateData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis domain={[95, 100]} />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="短信" stroke="#0088FE" activeDot={{ r: 8 }} />
                <Line type="monotone" dataKey="微信" stroke="#00C49F" />
                <Line type="monotone" dataKey="APP推送" stroke="#FFBB28" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>消息渠道分布</CardTitle>
            <CardDescription>各渠道消息发送占比</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={channelData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                >
                  {channelData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* 最近消息和消息类型 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>最近发送的消息</CardTitle>
            <CardDescription>查看最近发送的消息记录</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentMessages.map((message) => (
                <div key={message.id} className="flex items-center justify-between border-b pb-2">
                  <div className="flex items-center space-x-4">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{message.type}</p>
                      <p className="text-xs text-muted-foreground">接收人: {message.recipient}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{message.channel}</Badge>
                    <Badge variant={message.status === '未送达' ? 'destructive' : 'secondary'}>
                      {message.status}
                    </Badge>
                    <span className="text-xs text-muted-foreground">{message.time}</span>
                  </div>
                </div>
              ))}
              <Button variant="outline" className="w-full">查看更多</Button>
            </div>
          </CardContent>
        </Card>
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>消息类型分布</CardTitle>
            <CardDescription>各类型消息发送占比</CardDescription>
          </CardHeader>
          <CardContent className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={typeData} layout="vertical">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={80} />
                <Tooltip />
                <Bar dataKey="value" fill="#8884d8">
                  {typeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
