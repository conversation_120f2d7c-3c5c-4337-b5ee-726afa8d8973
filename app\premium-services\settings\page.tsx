"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Save, Bell, Mail, MessageSquare, Calendar, Clock, Users, Shield } from "lucide-react"
import { toast } from "@/hooks/use-toast"

export default function PremiumServicesSettingsPage() {
  const [activeTab, setActiveTab] = useState("general")
  
  // 模拟设置状态
  const [settings, setSettings] = useState({
    general: {
      autoRenewal: true,
      renewalReminder: true,
      reminderDays: "7",
      expirationNotification: true,
      paymentMethod: "wechat",
    },
    notifications: {
      email: true,
      sms: true,
      wechat: true,
      inApp: true,
      serviceActivation: true,
      serviceExpiration: true,
      paymentReminder: true,
      promotions: false,
    },
    access: {
      restrictFeatures: true,
      trialPeriod: true,
      trialDays: "14",
      gracePeriod: true,
      graceDays: "3",
      showUpgradePrompts: true,
    },
    billing: {
      companyName: "瑜伽工作室",
      taxId: "91310000XXXXXXXX3B",
      address: "上海市浦东新区XX路XX号",
      contactPerson: "张三",
      contactPhone: "13800138000",
      contactEmail: "<EMAIL>",
      invoiceType: "electronic",
    }
  })

  // 处理设置变更
  const handleSettingChange = (category, setting, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value
      }
    }))
  }

  // 保存设置
  const saveSettings = () => {
    // 这里应该有API调用来保存设置
    toast({
      title: "设置已保存",
      description: "您的增值服务设置已成功更新。",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">增值服务配置</h1>
        <Button onClick={saveSettings}>
          <Save className="mr-2 h-4 w-4" />
          保存设置
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 max-w-2xl">
          <TabsTrigger value="general">基本设置</TabsTrigger>
          <TabsTrigger value="notifications">通知设置</TabsTrigger>
          <TabsTrigger value="access">访问控制</TabsTrigger>
          <TabsTrigger value="billing">账单信息</TabsTrigger>
        </TabsList>

        {/* 基本设置 */}
        <TabsContent value="general" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>基本设置</CardTitle>
              <CardDescription>配置增值服务的基本参数和行为</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="auto-renewal" className="flex flex-col space-y-1">
                  <span>自动续费</span>
                  <span className="font-normal text-sm text-muted-foreground">
                    服务到期时自动续费，避免服务中断
                  </span>
                </Label>
                <Switch
                  id="auto-renewal"
                  checked={settings.general.autoRenewal}
                  onCheckedChange={(value) => handleSettingChange("general", "autoRenewal", value)}
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="renewal-reminder" className="flex flex-col space-y-1">
                  <span>续费提醒</span>
                  <span className="font-normal text-sm text-muted-foreground">
                    服务到期前发送续费提醒通知
                  </span>
                </Label>
                <Switch
                  id="renewal-reminder"
                  checked={settings.general.renewalReminder}
                  onCheckedChange={(value) => handleSettingChange("general", "renewalReminder", value)}
                />
              </div>
              {settings.general.renewalReminder && (
                <div className="ml-6 mt-2">
                  <Label htmlFor="reminder-days" className="mb-2 block">提前天数</Label>
                  <Input
                    id="reminder-days"
                    type="number"
                    value={settings.general.reminderDays}
                    onChange={(e) => handleSettingChange("general", "reminderDays", e.target.value)}
                    className="w-20"
                    min="1"
                    max="30"
                  />
                </div>
              )}
              <Separator />
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="expiration-notification" className="flex flex-col space-y-1">
                  <span>过期通知</span>
                  <span className="font-normal text-sm text-muted-foreground">
                    服务过期时发送通知
                  </span>
                </Label>
                <Switch
                  id="expiration-notification"
                  checked={settings.general.expirationNotification}
                  onCheckedChange={(value) => handleSettingChange("general", "expirationNotification", value)}
                />
              </div>
              <Separator />
              <div className="space-y-2">
                <Label htmlFor="payment-method">默认支付方式</Label>
                <Select
                  value={settings.general.paymentMethod}
                  onValueChange={(value) => handleSettingChange("general", "paymentMethod", value)}
                >
                  <SelectTrigger id="payment-method" className="w-full">
                    <SelectValue placeholder="选择默认支付方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wechat">微信支付</SelectItem>
                    <SelectItem value="alipay">支付宝</SelectItem>
                    <SelectItem value="bank">银行转账</SelectItem>
                    <SelectItem value="cash">现金支付</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 通知设置 */}
        <TabsContent value="notifications" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>通知设置</CardTitle>
              <CardDescription>配置增值服务相关的通知方式和内容</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">通知渠道</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="email-notification"
                      checked={settings.notifications.email}
                      onCheckedChange={(value) => handleSettingChange("notifications", "email", value)}
                    />
                    <Label htmlFor="email-notification" className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      电子邮件
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="sms-notification"
                      checked={settings.notifications.sms}
                      onCheckedChange={(value) => handleSettingChange("notifications", "sms", value)}
                    />
                    <Label htmlFor="sms-notification" className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      短信
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="wechat-notification"
                      checked={settings.notifications.wechat}
                      onCheckedChange={(value) => handleSettingChange("notifications", "wechat", value)}
                    />
                    <Label htmlFor="wechat-notification" className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      微信
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="inapp-notification"
                      checked={settings.notifications.inApp}
                      onCheckedChange={(value) => handleSettingChange("notifications", "inApp", value)}
                    />
                    <Label htmlFor="inapp-notification" className="flex items-center gap-2">
                      <Bell className="h-4 w-4" />
                      应用内通知
                    </Label>
                  </div>
                </div>
              </div>
              <Separator />
              <div className="space-y-4">
                <h3 className="text-lg font-medium">通知事件</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="service-activation"
                      checked={settings.notifications.serviceActivation}
                      onCheckedChange={(value) => handleSettingChange("notifications", "serviceActivation", value)}
                    />
                    <Label htmlFor="service-activation">服务激活</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="service-expiration"
                      checked={settings.notifications.serviceExpiration}
                      onCheckedChange={(value) => handleSettingChange("notifications", "serviceExpiration", value)}
                    />
                    <Label htmlFor="service-expiration">服务过期</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="payment-reminder"
                      checked={settings.notifications.paymentReminder}
                      onCheckedChange={(value) => handleSettingChange("notifications", "paymentReminder", value)}
                    />
                    <Label htmlFor="payment-reminder">付款提醒</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="promotions"
                      checked={settings.notifications.promotions}
                      onCheckedChange={(value) => handleSettingChange("notifications", "promotions", value)}
                    />
                    <Label htmlFor="promotions">促销信息</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 访问控制 */}
        <TabsContent value="access" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>访问控制</CardTitle>
              <CardDescription>配置增值服务的访问权限和试用策略</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="restrict-features" className="flex flex-col space-y-1">
                  <span>限制功能访问</span>
                  <span className="font-normal text-sm text-muted-foreground">
                    未订购的服务功能将被限制访问
                  </span>
                </Label>
                <Switch
                  id="restrict-features"
                  checked={settings.access.restrictFeatures}
                  onCheckedChange={(value) => handleSettingChange("access", "restrictFeatures", value)}
                />
              </div>
              <Separator />
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="trial-period" className="flex flex-col space-y-1">
                  <span>提供试用期</span>
                  <span className="font-normal text-sm text-muted-foreground">
                    允许用户在付费前试用服务
                  </span>
                </Label>
                <Switch
                  id="trial-period"
                  checked={settings.access.trialPeriod}
                  onCheckedChange={(value) => handleSettingChange("access", "trialPeriod", value)}
                />
              </div>
              {settings.access.trialPeriod && (
                <div className="ml-6 mt-2">
                  <Label htmlFor="trial-days" className="mb-2 block">试用天数</Label>
                  <Input
                    id="trial-days"
                    type="number"
                    value={settings.access.trialDays}
                    onChange={(e) => handleSettingChange("access", "trialDays", e.target.value)}
                    className="w-20"
                    min="1"
                    max="30"
                  />
                </div>
              )}
              <Separator />
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="grace-period" className="flex flex-col space-y-1">
                  <span>宽限期</span>
                  <span className="font-normal text-sm text-muted-foreground">
                    服务过期后的宽限访问期
                  </span>
                </Label>
                <Switch
                  id="grace-period"
                  checked={settings.access.gracePeriod}
                  onCheckedChange={(value) => handleSettingChange("access", "gracePeriod", value)}
                />
              </div>
              {settings.access.gracePeriod && (
                <div className="ml-6 mt-2">
                  <Label htmlFor="grace-days" className="mb-2 block">宽限天数</Label>
                  <Input
                    id="grace-days"
                    type="number"
                    value={settings.access.graceDays}
                    onChange={(e) => handleSettingChange("access", "graceDays", e.target.value)}
                    className="w-20"
                    min="1"
                    max="30"
                  />
                </div>
              )}
              <Separator />
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="upgrade-prompts" className="flex flex-col space-y-1">
                  <span>显示升级提示</span>
                  <span className="font-normal text-sm text-muted-foreground">
                    在未订购服务的功能处显示升级提示
                  </span>
                </Label>
                <Switch
                  id="upgrade-prompts"
                  checked={settings.access.showUpgradePrompts}
                  onCheckedChange={(value) => handleSettingChange("access", "showUpgradePrompts", value)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 账单信息 */}
        <TabsContent value="billing" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>账单信息</CardTitle>
              <CardDescription>配置增值服务的账单和发票信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="company-name">公司名称</Label>
                  <Input
                    id="company-name"
                    value={settings.billing.companyName}
                    onChange={(e) => handleSettingChange("billing", "companyName", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tax-id">税号</Label>
                  <Input
                    id="tax-id"
                    value={settings.billing.taxId}
                    onChange={(e) => handleSettingChange("billing", "taxId", e.target.value)}
                  />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="address">地址</Label>
                  <Input
                    id="address"
                    value={settings.billing.address}
                    onChange={(e) => handleSettingChange("billing", "address", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-person">联系人</Label>
                  <Input
                    id="contact-person"
                    value={settings.billing.contactPerson}
                    onChange={(e) => handleSettingChange("billing", "contactPerson", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-phone">联系电话</Label>
                  <Input
                    id="contact-phone"
                    value={settings.billing.contactPhone}
                    onChange={(e) => handleSettingChange("billing", "contactPhone", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-email">联系邮箱</Label>
                  <Input
                    id="contact-email"
                    type="email"
                    value={settings.billing.contactEmail}
                    onChange={(e) => handleSettingChange("billing", "contactEmail", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="invoice-type">发票类型</Label>
                  <Select
                    value={settings.billing.invoiceType}
                    onValueChange={(value) => handleSettingChange("billing", "invoiceType", value)}
                  >
                    <SelectTrigger id="invoice-type">
                      <SelectValue placeholder="选择发票类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="electronic">电子发票</SelectItem>
                      <SelectItem value="paper">纸质发票</SelectItem>
                      <SelectItem value="vat">增值税专用发票</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSettings} className="ml-auto">
                <Save className="mr-2 h-4 w-4" />
                保存账单信息
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
