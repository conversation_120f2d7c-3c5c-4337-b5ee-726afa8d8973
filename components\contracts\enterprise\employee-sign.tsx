"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { 
  UserCheck, 
  FileSignature, 
  Eye, 
  Download, 
  CheckCircle, 
  Clock, 
  Search, 
  ShieldCheck 
} from "lucide-react"
import { usePremiumServices } from "@/hooks/use-premium-services"

// 模拟待签署合同
const mockPendingContracts = [
  {
    id: "contract-1",
    title: "员工劳动合同",
    type: "LABOR",
    createTime: "2023-06-15 14:30:25",
    creator: "HR部门",
    status: "PENDING",
    deadline: "2023-06-20 23:59:59",
  },
  {
    id: "contract-2",
    title: "保密协议",
    type: "NDA",
    createTime: "2023-06-14 09:20:15",
    creator: "法务部门",
    status: "PENDING",
    deadline: "2023-06-19 23:59:59",
  },
]

// 模拟已签署合同
const mockSignedContracts = [
  {
    id: "contract-3",
    title: "入职协议",
    type: "ONBOARDING",
    createTime: "2023-05-10 14:30:25",
    creator: "HR部门",
    status: "SIGNED",
    signTime: "2023-05-10 15:45:10",
  },
  {
    id: "contract-4",
    title: "培训协议",
    type: "TRAINING",
    createTime: "2023-04-15 09:20:15",
    creator: "培训部门",
    status: "SIGNED",
    signTime: "2023-04-15 11:30:05",
  },
]

// 模拟员工信息
const mockEmployeeInfo = {
  name: "张三",
  department: "技术部",
  position: "高级工程师",
  employeeId: "EMP20230001",
  joinDate: "2023-01-15",
  hasSignature: true,
}

interface EmployeeSignProps {
  className?: string
}

export function EmployeeSign({ className }: EmployeeSignProps) {
  const { config, loading } = usePremiumServices()
  const [pendingContracts, setPendingContracts] = useState(mockPendingContracts)
  const [signedContracts, setSignedContracts] = useState(mockSignedContracts)
  const [employeeInfo, setEmployeeInfo] = useState(mockEmployeeInfo)
  const [selectedContract, setSelectedContract] = useState<any>(null)
  const [showContractDialog, setShowContractDialog] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("pending")
  
  // 检查是否启用了企业员工个人签署功能
  const isFeatureEnabled = !loading && config?.eContract?.enabled && config?.eContract?.features?.employeeSign
  
  // 处理查看合同
  const handleViewContract = (contract: any) => {
    setSelectedContract(contract)
    setShowContractDialog(true)
  }
  
  // 处理签署合同
  const handleSignContract = (contractId: string) => {
    // 在实际应用中，这里应该调用API签署合同
    console.log("签署合同:", contractId)
    
    // 更新合同状态
    const contractToSign = pendingContracts.find(contract => contract.id === contractId)
    if (!contractToSign) return
    
    const updatedPendingContracts = pendingContracts.filter(contract => contract.id !== contractId)
    setPendingContracts(updatedPendingContracts)
    
    // 添加到已签署合同
    const signedContract = {
      ...contractToSign,
      status: "SIGNED",
      signTime: new Date().toLocaleString(),
    }
    setSignedContracts([signedContract, ...signedContracts])
    
    // 关闭对话框
    setShowContractDialog(false)
    setSelectedContract(null)
    
    // 显示成功提示
    toast({
      title: "签署成功",
      description: `合同 ${contractToSign.title} 已成功签署`,
    })
  }
  
  // 处理下载合同
  const handleDownloadContract = (contractId: string) => {
    // 在实际应用中，这里应该调用API下载合同
    console.log("下载合同:", contractId)
    
    // 显示成功提示
    toast({
      title: "下载开始",
      description: "合同文件开始下载",
    })
  }
  
  // 过滤合同列表
  const filteredPendingContracts = pendingContracts.filter(contract => 
    contract.title.includes(searchQuery) || 
    contract.type.includes(searchQuery) || 
    contract.creator.includes(searchQuery)
  )
  
  const filteredSignedContracts = signedContracts.filter(contract => 
    contract.title.includes(searchQuery) || 
    contract.type.includes(searchQuery) || 
    contract.creator.includes(searchQuery)
  )
  
  // 获取合同类型名称
  const getContractTypeName = (type: string) => {
    switch (type) {
      case "LABOR": return "劳动合同"
      case "NDA": return "保密协议"
      case "ONBOARDING": return "入职协议"
      case "TRAINING": return "培训协议"
      default: return type
    }
  }
  
  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return <Badge className="bg-yellow-500">待签署</Badge>
      case "SIGNED":
        return <Badge className="bg-green-500">已签署</Badge>
      case "EXPIRED":
        return <Badge className="bg-red-500">已过期</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }
  
  // 如果功能未启用，显示提示信息
  if (!isFeatureEnabled) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>企业员工个人签署</CardTitle>
          <CardDescription>
            企业员工使用个人身份进行合同签署
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10 space-y-4">
          <ShieldCheck className="h-12 w-12 text-muted-foreground/50" />
          <h3 className="text-lg font-semibold">功能未开通</h3>
          <p className="text-sm text-muted-foreground text-center max-w-md">
            企业员工个人签署是增值服务的高级功能，需要开通后才能使用。
            开通后，企业员工可以使用个人身份签署企业合同。
          </p>
          <Button onClick={() => window.location.href = "/premium-services"}>
            了解详情
          </Button>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <UserCheck className="mr-2 h-5 w-5" />
          企业员工个人签署
        </CardTitle>
        <CardDescription>
          企业员工使用个人身份进行合同签署
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="bg-muted/30 p-4 rounded-md">
            <h3 className="font-medium mb-2">员工信息</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label className="text-muted-foreground text-sm">姓名</Label>
                <div className="font-medium">{employeeInfo.name}</div>
              </div>
              <div>
                <Label className="text-muted-foreground text-sm">部门/职位</Label>
                <div className="font-medium">{employeeInfo.department} / {employeeInfo.position}</div>
              </div>
              <div>
                <Label className="text-muted-foreground text-sm">员工编号</Label>
                <div className="font-medium">{employeeInfo.employeeId}</div>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Input
                placeholder="搜索合同..."
                className="max-w-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                prefix={<Search className="h-4 w-4 text-muted-foreground" />}
              />
            </div>
            
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="pending">
                  待签署
                  {pendingContracts.length > 0 && (
                    <Badge className="ml-2 bg-yellow-500">{pendingContracts.length}</Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="signed">
                  已签署
                  {signedContracts.length > 0 && (
                    <Badge className="ml-2 bg-green-500">{signedContracts.length}</Badge>
                  )}
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="pending" className="mt-4">
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>合同名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead>创建人</TableHead>
                        <TableHead>截止时间</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPendingContracts.length > 0 ? (
                        filteredPendingContracts.map((contract) => (
                          <TableRow key={contract.id}>
                            <TableCell className="font-medium">{contract.title}</TableCell>
                            <TableCell>{getContractTypeName(contract.type)}</TableCell>
                            <TableCell>{contract.createTime}</TableCell>
                            <TableCell>{contract.creator}</TableCell>
                            <TableCell>{contract.deadline}</TableCell>
                            <TableCell>{getStatusBadge(contract.status)}</TableCell>
                            <TableCell className="text-right">
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleViewContract(contract)}
                              >
                                <Eye className="mr-2 h-4 w-4" />
                                查看
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                            暂无待签署合同
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
              
              <TabsContent value="signed" className="mt-4">
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>合同名称</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead>创建人</TableHead>
                        <TableHead>签署时间</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredSignedContracts.length > 0 ? (
                        filteredSignedContracts.map((contract) => (
                          <TableRow key={contract.id}>
                            <TableCell className="font-medium">{contract.title}</TableCell>
                            <TableCell>{getContractTypeName(contract.type)}</TableCell>
                            <TableCell>{contract.createTime}</TableCell>
                            <TableCell>{contract.creator}</TableCell>
                            <TableCell>{contract.signTime}</TableCell>
                            <TableCell>{getStatusBadge(contract.status)}</TableCell>
                            <TableCell className="text-right">
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleDownloadContract(contract.id)}
                              >
                                <Download className="mr-2 h-4 w-4" />
                                下载
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                            暂无已签署合同
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </CardContent>
      
      {/* 合同详情对话框 */}
      <Dialog open={showContractDialog} onOpenChange={setShowContractDialog}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>合同详情</DialogTitle>
            <DialogDescription>
              查看合同详情并进行签署
            </DialogDescription>
          </DialogHeader>
          
          {selectedContract && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">{selectedContract.title}</h3>
                {getStatusBadge(selectedContract.status)}
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-muted-foreground text-sm">合同类型</Label>
                  <div className="font-medium">{getContractTypeName(selectedContract.type)}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground text-sm">创建人</Label>
                  <div className="font-medium">{selectedContract.creator}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground text-sm">创建时间</Label>
                  <div className="font-medium">{selectedContract.createTime}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground text-sm">截止时间</Label>
                  <div className="font-medium">{selectedContract.deadline}</div>
                </div>
              </div>
              
              <div className="border rounded-md p-4 h-80 overflow-auto bg-muted/30">
                <div className="text-center text-muted-foreground">
                  {/* 在实际应用中，这里应该显示合同内容 */}
                  <p className="mb-4">合同预览</p>
                  <p className="text-sm">此处显示合同内容，包括条款、附件等</p>
                </div>
              </div>
              
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowContractDialog(false)}
                >
                  关闭
                </Button>
                {selectedContract.status === "PENDING" && (
                  <Button 
                    onClick={() => handleSignContract(selectedContract.id)}
                    disabled={!employeeInfo.hasSignature}
                  >
                    <FileSignature className="mr-2 h-4 w-4" />
                    签署合同
                  </Button>
                )}
                {selectedContract.status === "SIGNED" && (
                  <Button 
                    onClick={() => handleDownloadContract(selectedContract.id)}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    下载合同
                  </Button>
                )}
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  )
}
