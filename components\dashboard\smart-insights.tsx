"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import { 
  Lightbulb, 
  TrendingUp, 
  AlertTriangle, 
  Users, 
  Calendar, 
  CreditCard,
  ChevronRight,
  Star,
  Gift,
  Clock
} from "lucide-react"

// 模拟数据
const insights = [
  {
    id: 1,
    title: "会员流失风险提醒",
    description: "检测到15名会员超过30天未到访，建议发送关怀消息或优惠券挽留。",
    href: "/members/retention",
    icon: <AlertTriangle className="h-5 w-5 text-amber-500" />,
    actionText: "查看详情",
    priority: "high",
  },
  {
    id: 2,
    title: "课程优化建议",
    description: "周六10:00-12:00时段预约率达95%，建议增加同类型课程。",
    href: "/courses/optimization",
    icon: <Calendar className="h-5 w-5 text-blue-500" />,
    actionText: "查看详情",
    priority: "medium",
  },
  {
    id: 3,
    title: "收入增长机会",
    description: "高级瑜伽课程预约率高于平均水平30%，建议适当增加课程价格或数量。",
    href: "/revenue/opportunities",
    icon: <CreditCard className="h-5 w-5 text-green-500" />,
    actionText: "查看详情",
    priority: "medium",
  },
  {
    id: 4,
    title: "会员生日提醒",
    description: "本周有8位会员生日，可发送生日祝福和专属优惠。",
    href: "/members/birthday",
    icon: <Gift className="h-5 w-5 text-pink-500" />,
    actionText: "发送祝福",
    priority: "high",
  },
  {
    id: 5,
    title: "教练评分异常",
    description: "李教练近期评分下降明显，建议关注教学质量。",
    href: "/coaches/performance",
    icon: <Star className="h-5 w-5 text-purple-500" />,
    actionText: "查看详情",
    priority: "medium",
  },
]

export function SmartInsights() {
  const router = useRouter()

  // 获取优先级样式
  const getPriorityStyle = (priority: string) => {
    switch(priority) {
      case "high":
        return "border-l-4 border-red-500"
      case "medium":
        return "border-l-4 border-amber-500"
      case "low":
        return "border-l-4 border-green-500"
      default:
        return ""
    }
  }

  return (
    <Card className="border shadow-sm">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <Lightbulb className="mr-2 h-5 w-5 text-primary" />
          智能洞察
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {insights.map((insight) => (
            <div
              key={insight.id}
              className={`p-3 bg-background rounded-lg border hover:shadow-sm transition-shadow ${getPriorityStyle(insight.priority)}`}
            >
              <div className="flex items-start">
                <div className="p-2 rounded-full bg-muted">
                  {insight.icon}
                </div>
                <div className="ml-3 flex-1">
                  <h4 className="font-medium text-sm">{insight.title}</h4>
                  <p className="text-xs text-muted-foreground mt-1 mb-2">
                    {insight.description}
                  </p>
                  <div className="flex justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 text-xs text-primary hover:text-primary/80 flex items-center gap-1 -mr-2"
                      onClick={() => router.push(insight.href)}
                    >
                      {insight.actionText}
                      <ChevronRight className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
