import { CourseType, CourseTypeFilter } from "@/types/course-type";

// 模拟课程类型数据，使其与页面UI完全匹配
let mockCourseTypes: CourseType[] = [
  {
    id: 1,
    name: "团课1",
    description: "适合多人参与的常规团体课程，人数通常在15-30人",
    courses: 35,
    status: "active",
    color: "#4285F4",
    createdAt: "2023-05-10",
    updatedAt: "2023-11-15",
  },
  {
    id: 2,
    name: "小班课1",
    description: "小规模教学，人数通常在5-10人，教练能给予更多个性化指导",
    courses: 22,
    status: "active",
    color: "#34A853",
    createdAt: "2023-05-12",
    updatedAt: "2023-10-20",
  },
  {
    id: 3,
    name: "精品课",
    description: "高端定制课程，由资深教练授课，提供优质教学体验",
    courses: 15,
    status: "active",
    color: "#FBBC05",
    createdAt: "2023-06-01",
    updatedAt: "2023-09-15",
  },
  {
    id: 4,
    name: "私教课",
    description: "一对一个性化教学，根据会员需求定制课程内容",
    courses: 28,
    status: "active",
    color: "#EA4335",
    createdAt: "2023-06-15",
    updatedAt: "2023-11-01",
  },
  {
    id: 5,
    name: "教培课",
    description: "针对瑜伽教练的专业培训课程，提升教学技能和专业知识",
    courses: 12,
    status: "active",
    color: "#9C27B0",
    createdAt: "2023-07-01",
    updatedAt: "2023-12-05",
  },
  {
    id: 7,
    name: "空中瑜伽",
    description: "使用吊床辅助的瑜伽课程，增加趣味性和挑战性",
    courses: 12,
    status: "active",
    color: "#FF6D91",
    createdAt: "2023-07-01",
    updatedAt: "2023-10-10",
  },
  {
    id: 6,
    name: "热瑜伽",
    description: "在高温环境下进行的瑜伽课程，促进排汗和新陈代谢",
    courses: 0,
    status: "inactive",
    color: "#FF9800",
    createdAt: "2023-07-15",
    updatedAt: "2023-08-20",
  },
  {
    id: 8,
    name: "普拉提基础",
    description: "基础普拉提课程，注重核心力量和身体控制",
    courses: 20,
    status: "active",
    color: "#00BCD4",
    createdAt: "2023-08-01",
    updatedAt: "2023-11-05",
  },
  {
    id: 9,
    name: "器械普拉提",
    description: "使用专业器械辅助的普拉提课程",
    courses: 15,
    status: "active",
    color: "#607D8B",
    createdAt: "2023-08-15",
    updatedAt: "2023-10-25",
  },
];

let nextId = 10;

export const courseTypeService = {
  getAll: (params: CourseTypeFilter = {}) => {
    let result = [...mockCourseTypes];
    
    // 关键词筛选
    if (params.keyword) {
      result = result.filter(
        (type) =>
          type.name.toLowerCase().includes(params.keyword!.toLowerCase()) ||
          type.description.toLowerCase().includes(params.keyword!.toLowerCase())
      );
    }
    
    // 状态筛选
    if (params.status && params.status !== 'all') {
      result = result.filter((type) => type.status === params.status);
    }
    
    // 关联课程筛选
    if (params.showWithCourses && !params.showWithoutCourses) {
      result = result.filter((type) => type.courses > 0);
    } else if (!params.showWithCourses && params.showWithoutCourses) {
      result = result.filter((type) => type.courses === 0);
    }
    
    // 创建日期筛选
    if (params.createdAfter) {
      const afterDate = new Date(params.createdAfter);
      result = result.filter((type) => new Date(type.createdAt) >= afterDate);
    }
    
    if (params.createdBefore) {
      const beforeDate = new Date(params.createdBefore);
      result = result.filter((type) => new Date(type.createdAt) <= beforeDate);
    }
    
    return result;
  },

  getById: (id: number): CourseType | undefined => {
    return mockCourseTypes.find((type) => type.id === id);
  },

  create: (data: Partial<CourseType>): CourseType => {
    const newType: CourseType = {
      id: nextId++,
      name: data.name || '',
      description: data.description || '',
      courses: 0,
      status: data.status || 'active',
      color: data.color || '#cccccc',
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0],
      displayOrder: data.displayOrder || 0
    };
    mockCourseTypes.push(newType);
    return newType;
  },

  update: (id: number, data: Partial<CourseType>): CourseType | null => {
    const index = mockCourseTypes.findIndex((type) => type.id === id);
    if (index !== -1) {
      mockCourseTypes[index] = { 
        ...mockCourseTypes[index], 
        ...data, 
        updatedAt: new Date().toISOString().split('T')[0] 
      };
      return mockCourseTypes[index];
    }
    return null;
  },

  delete: (id: number): boolean => {
    const index = mockCourseTypes.findIndex((type) => type.id === id);
    if (index !== -1) {
      // 如果有关联课程，不允许删除
      if (mockCourseTypes[index].courses > 0) {
        return false;
      }
      mockCourseTypes.splice(index, 1);
      return true;
    }
    return false;
  },
  
  // 获取课程类型统计信息
  getStats: () => {
    const total = mockCourseTypes.length;
    const active = mockCourseTypes.filter(t => t.status === 'active').length;
    const inactive = total - active;
    const totalCourses = mockCourseTypes.reduce((sum, type) => sum + type.courses, 0);
    
    return {
      total,
      active,
      inactive,
      totalCourses,
      averageCoursesPerType: total > 0 ? totalCourses / total : 0
    };
  }
}; 