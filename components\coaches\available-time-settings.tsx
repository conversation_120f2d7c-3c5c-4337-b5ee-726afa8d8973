"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { format, addDays, startOfWeek, addWeeks } from "date-fns"
import { cn } from "@/lib/utils"
import { CalendarIcon, Clock, Plus, Trash2, Save, X } from "lucide-react"

// 模拟教练数据
const coaches = [
  { id: "1", name: "张教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "2", name: "李教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "3", name: "王教练", avatar: "/placeholder.svg?height=32&width=32" },
]

// 模拟场地数据
const venues = [
  { id: "1", name: "1号瑜伽室", capacity: 15 },
  { id: "2", name: "2号瑜伽室", capacity: 10 },
  { id: "3", name: "私教室", capacity: 2 },
]

// 模拟课程类型数据
const courseTypes = [
  { id: "private", name: "私教课", color: "#EA4335" },
  { id: "small", name: "小班课", color: "#34A853" },
]

interface AvailableTimeSettingsProps {
  coachId?: string;
}

export function AvailableTimeSettings({ coachId }: AvailableTimeSettingsProps) {
  const [activeTab, setActiveTab] = useState("weekly")
  const [selectedCoach, setSelectedCoach] = useState(coachId || "1")
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [weeklySchedule, setWeeklySchedule] = useState<Record<string, any>>({
    monday: [
      { start: "09:00", end: "10:00", available: true, venue: "1", courseType: "private" },
      { start: "10:30", end: "11:30", available: true, venue: "1", courseType: "private" },
      { start: "14:00", end: "15:00", available: true, venue: "3", courseType: "private" },
    ],
    tuesday: [
      { start: "09:00", end: "10:00", available: true, venue: "1", courseType: "private" },
      { start: "10:30", end: "11:30", available: true, venue: "1", courseType: "private" },
    ],
    wednesday: [
      { start: "14:00", end: "15:00", available: true, venue: "3", courseType: "private" },
      { start: "15:30", end: "16:30", available: true, venue: "3", courseType: "private" },
    ],
    thursday: [
      { start: "09:00", end: "10:00", available: true, venue: "1", courseType: "private" },
      { start: "10:30", end: "11:30", available: true, venue: "1", courseType: "private" },
    ],
    friday: [
      { start: "14:00", end: "15:00", available: true, venue: "3", courseType: "private" },
      { start: "15:30", end: "16:30", available: true, venue: "3", courseType: "private" },
    ],
    saturday: [
      { start: "09:00", end: "10:00", available: true, venue: "1", courseType: "small" },
      { start: "10:30", end: "11:30", available: true, venue: "1", courseType: "small" },
      { start: "14:00", end: "15:00", available: true, venue: "1", courseType: "small" },
    ],
    sunday: [],
  })
  
  const [exceptions, setExceptions] = useState<any[]>([
    { date: new Date(2023, 5, 15), slots: [], unavailable: true, reason: "休假" },
    { date: new Date(2023, 5, 20), slots: [
      { start: "09:00", end: "10:00", available: true, venue: "2", courseType: "private" },
      { start: "10:30", end: "11:30", available: true, venue: "2", courseType: "private" },
    ], unavailable: false, reason: "场地变更" },
  ])
  
  const [enableAutoSchedule, setEnableAutoSchedule] = useState(true)
  const [autoScheduleSettings, setAutoScheduleSettings] = useState({
    maxDailySlots: 6,
    maxWeeklySlots: 30,
    minBreakTime: 30, // 分钟
    preferredVenue: "3",
    preferredCourseType: "private",
    excludeHolidays: true,
  })
  
  // 添加新的时间段
  const addTimeSlot = (day: string) => {
    const newSlot = {
      start: "09:00",
      end: "10:00",
      available: true,
      venue: "1",
      courseType: "private"
    }
    
    setWeeklySchedule({
      ...weeklySchedule,
      [day]: [...weeklySchedule[day], newSlot]
    })
  }
  
  // 删除时间段
  const removeTimeSlot = (day: string, index: number) => {
    setWeeklySchedule({
      ...weeklySchedule,
      [day]: weeklySchedule[day].filter((_, i) => i !== index)
    })
  }
  
  // 更新时间段
  const updateTimeSlot = (day: string, index: number, field: string, value: any) => {
    const updatedSlots = [...weeklySchedule[day]]
    updatedSlots[index] = { ...updatedSlots[index], [field]: value }
    
    setWeeklySchedule({
      ...weeklySchedule,
      [day]: updatedSlots
    })
  }
  
  // 保存设置
  const saveSettings = () => {
    console.log("保存教练可约时间设置:", {
      coachId: selectedCoach,
      weeklySchedule,
      exceptions,
      autoScheduleSettings
    })
    
    // 这里应该调用API保存设置
    alert("设置已保存")
  }
  
  // 渲染每周时间表
  const renderWeeklySchedule = () => {
    const days = [
      { key: "monday", label: "周一" },
      { key: "tuesday", label: "周二" },
      { key: "wednesday", label: "周三" },
      { key: "thursday", label: "周四" },
      { key: "friday", label: "周五" },
      { key: "saturday", label: "周六" },
      { key: "sunday", label: "周日" },
    ]
    
    return (
      <div className="space-y-6">
        {days.map((day) => (
          <div key={day.key} className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">{day.label}</h3>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => addTimeSlot(day.key)}
              >
                <Plus className="h-4 w-4 mr-1" /> 添加时段
              </Button>
            </div>
            
            {weeklySchedule[day.key].length === 0 ? (
              <div className="text-sm text-muted-foreground py-2">
                没有设置可约时段
              </div>
            ) : (
              <div className="space-y-2">
                {weeklySchedule[day.key].map((slot, index) => (
                  <div key={index} className="flex items-center space-x-2 p-2 border rounded-md bg-background">
                    <div className="grid grid-cols-5 gap-2 flex-1">
                      <div className="col-span-2 flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <Select 
                          value={slot.start} 
                          onValueChange={(value) => updateTimeSlot(day.key, index, "start", value)}
                        >
                          <SelectTrigger className="h-8">
                            <SelectValue placeholder="开始时间" />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({ length: 24 }).map((_, i) => (
                              <SelectItem key={i} value={`${String(i + 6).padStart(2, '0')}:00`}>
                                {`${String(i + 6).padStart(2, '0')}:00`}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <span>-</span>
                        <Select 
                          value={slot.end} 
                          onValueChange={(value) => updateTimeSlot(day.key, index, "end", value)}
                        >
                          <SelectTrigger className="h-8">
                            <SelectValue placeholder="结束时间" />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({ length: 24 }).map((_, i) => (
                              <SelectItem key={i} value={`${String(i + 7).padStart(2, '0')}:00`}>
                                {`${String(i + 7).padStart(2, '0')}:00`}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Select 
                          value={slot.venue} 
                          onValueChange={(value) => updateTimeSlot(day.key, index, "venue", value)}
                        >
                          <SelectTrigger className="h-8">
                            <SelectValue placeholder="选择场地" />
                          </SelectTrigger>
                          <SelectContent>
                            {venues.map((venue) => (
                              <SelectItem key={venue.id} value={venue.id}>
                                {venue.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Select 
                          value={slot.courseType} 
                          onValueChange={(value) => updateTimeSlot(day.key, index, "courseType", value)}
                        >
                          <SelectTrigger className="h-8">
                            <SelectValue placeholder="课程类型" />
                          </SelectTrigger>
                          <SelectContent>
                            {courseTypes.map((type) => (
                              <SelectItem key={type.id} value={type.id}>
                                <div className="flex items-center">
                                  <div className="h-2 w-2 rounded-full mr-2" style={{ backgroundColor: type.color }}></div>
                                  {type.name}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="flex items-center justify-end">
                        <div className="flex items-center mr-2">
                          <Switch 
                            id={`available-${day.key}-${index}`}
                            checked={slot.available}
                            onCheckedChange={(checked) => updateTimeSlot(day.key, index, "available", checked)}
                          />
                          <Label htmlFor={`available-${day.key}-${index}`} className="ml-2 text-sm">
                            可约
                          </Label>
                        </div>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => removeTimeSlot(day.key, index)}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    )
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>教练可约时间设置</CardTitle>
        <CardDescription>
          设置教练的可约时间段，学员可以在这些时间段内预约私教课或小班课
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <div className="w-1/3">
              <Label htmlFor="coach-select">选择教练</Label>
              <Select value={selectedCoach} onValueChange={setSelectedCoach}>
                <SelectTrigger id="coach-select">
                  <SelectValue placeholder="选择教练" />
                </SelectTrigger>
                <SelectContent>
                  {coaches.map((coach) => (
                    <SelectItem key={coach.id} value={coach.id}>
                      {coach.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex-1 flex justify-end">
              <Button onClick={saveSettings}>
                <Save className="h-4 w-4 mr-2" />
                保存设置
              </Button>
            </div>
          </div>
          
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="weekly">每周固定时间</TabsTrigger>
              <TabsTrigger value="exceptions">特殊日期设置</TabsTrigger>
            </TabsList>
            
            <TabsContent value="weekly" className="space-y-4 pt-4">
              {renderWeeklySchedule()}
            </TabsContent>
            
            <TabsContent value="exceptions" className="space-y-4 pt-4">
              <div className="text-sm text-muted-foreground mb-4">
                设置特殊日期的可约时间，可以覆盖每周固定时间设置
              </div>
              
              {/* 这里可以添加特殊日期设置的界面 */}
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  )
}
