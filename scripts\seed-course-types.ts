import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedCourseTypes() {
  try {
    console.log('开始创建课程类型数据...')

    // 默认租户ID为1
    const tenantId = 1

    // 检查是否已有课程类型数据
    const existingTypes = await prisma.course_type.findMany({
      where: { tenant_id: tenantId }
    })

    if (existingTypes.length > 0) {
      console.log('课程类型数据已存在，跳过创建')
      return
    }

    // 创建课程类型
    const courseTypes = [
      {
        tenant_id: tenantId,
        name: '基础瑜伽',
        description: '适合初学者的基础瑜伽课程',
        color: '#4285F4'
      },
      {
        tenant_id: tenantId,
        name: '进阶瑜伽',
        description: '适合有基础的学员',
        color: '#34A853'
      },
      {
        tenant_id: tenantId,
        name: '阴瑜伽',
        description: '深度放松的阴瑜伽',
        color: '#FBBC05'
      },
      {
        tenant_id: tenantId,
        name: '孕产瑜伽',
        description: '专为孕妇设计的瑜伽',
        color: '#EA4335'
      },
      {
        tenant_id: tenantId,
        name: '空中瑜伽',
        description: '空中瑜伽练习',
        color: '#FF6D91'
      },
      {
        tenant_id: tenantId,
        name: '私教课',
        description: '一对一私教课程',
        color: '#9C27B0'
      }
    ]

    for (const typeData of courseTypes) {
      const courseType = await prisma.course_type.create({
        data: typeData
      })
      console.log(`创建课程类型: ${courseType.name}`)
    }

    console.log('课程类型数据创建完成!')

  } catch (error) {
    console.error('创建课程类型数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedCourseTypes()
}

export { seedCourseTypes }
