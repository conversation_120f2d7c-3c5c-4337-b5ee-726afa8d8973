"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { CreditCard, Plus, Minus } from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface AdjustCountDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
    card: string
    remaining: string
    expiry: string
  }
}

export function AdjustCountDialog({ open, onOpenChange, member }: AdjustCountDialogProps) {
  const { toast } = useToast()
  const [adjustType, setAdjustType] = useState("add")
  const [adjustCount, setAdjustCount] = useState("1")
  const [reason, setReason] = useState("")
  const [reasonType, setReasonType] = useState("compensation")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 计算调整后的剩余次数
  const getNewRemaining = () => {
    const currentRemaining = parseInt(member.remaining)
    const count = parseInt(adjustCount) || 0
    
    if (adjustType === "add") {
      return currentRemaining + count
    } else {
      return Math.max(0, currentRemaining - count)
    }
  }

  const handleSubmit = async () => {
    if (!adjustCount || isNaN(parseInt(adjustCount)) || parseInt(adjustCount) <= 0) {
      toast({
        title: "请输入有效次数",
        description: "调整次数必须是大于0的整数",
        variant: "destructive",
      })
      return
    }

    if (!reason.trim()) {
      toast({
        title: "请输入调整原因",
        description: "请详细说明调整剩余次数的原因",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "剩余次数调整成功",
        description: `${member.name}的会员卡剩余次数已${adjustType === "add" ? "增加" : "减少"}${adjustCount}次，当前剩余：${getNewRemaining()}次`,
      })
      
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "操作失败",
        description: "调整剩余次数失败，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>调整剩余次数</DialogTitle>
          <DialogDescription>
            增加或减少会员卡的剩余可用次数
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex items-center gap-4">
              <CreditCard className="h-8 w-8 text-muted-foreground" />
              <div>
                <h4 className="font-medium">{member.name}</h4>
                <p className="text-sm text-muted-foreground">
                  {member.card} (剩余: {member.remaining}次, 到期: {member.expiry})
                </p>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>调整类型</Label>
            <RadioGroup value={adjustType} onValueChange={setAdjustType} className="flex space-x-4">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="add" id="add" />
                <Label htmlFor="add" className="flex items-center">
                  <Plus className="mr-1 h-4 w-4" />
                  增加次数
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="subtract" id="subtract" />
                <Label htmlFor="subtract" className="flex items-center">
                  <Minus className="mr-1 h-4 w-4" />
                  减少次数
                </Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="adjust-count">调整次数</Label>
            <Input
              id="adjust-count"
              type="number"
              min="1"
              value={adjustCount}
              onChange={(e) => setAdjustCount(e.target.value)}
            />
            <p className="text-sm text-muted-foreground">
              调整后剩余: <span className="font-medium">{getNewRemaining()}</span> 次
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="reason-type">调整原因类型</Label>
            <Select value={reasonType} onValueChange={setReasonType}>
              <SelectTrigger id="reason-type">
                <SelectValue placeholder="选择原因类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="compensation">补偿</SelectItem>
                <SelectItem value="promotion">促销活动</SelectItem>
                <SelectItem value="correction">系统错误修正</SelectItem>
                <SelectItem value="special">特殊情况</SelectItem>
                <SelectItem value="other">其他</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="reason">详细原因</Label>
            <Textarea
              id="reason"
              placeholder="请输入详细的调整原因..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
            />
          </div>
          
          <div className="rounded-md bg-muted p-3 text-sm">
            <p className="font-medium">调整说明：</p>
            <ul className="list-disc pl-5 pt-2 text-muted-foreground">
              <li>调整次数仅适用于次卡类型的会员卡</li>
              <li>所有调整操作将被记录在系统日志中</li>
              <li>请确保有合理的调整原因，避免随意调整</li>
            </ul>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>取消</Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "处理中..." : "确认调整"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
