"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { cn } from "@/lib/utils"
import {
  Save,
  Eye,
  ArrowLeft,
  Palette,
  Layout,
  Settings,
  Layers,
  Grid3X3,
  Sparkles,
  Calendar,
  CreditCard,
  User
} from "lucide-react"
import { SimplePreview } from "./simple-preview"

export default function MemberMiniProgramSettingsPage() {
  // 获取 toast 组件
  const { toast } = useToast()

  // 设置状态
  const [activeTab, setActiveTab] = useState("风格")
  const [showPreview, setShowPreview] = useState(true)

  // 设置选项
  const [settings, setSettings] = useState({
    // 风格设置
    theme: "modern",
    primaryColor: "#8A2BE2", // 紫色
    secondaryColor: "#FF6B6B",
    fontFamily: "default",
    borderRadius: 8,
    darkMode: false,

    // 布局设置
    layoutType: "card",
    headerStyle: "centered",
    navigationStyle: "tabbar",
    cardStyle: "shadow",
    animationLevel: 2,

    // 功能设置
    enableBooking: true,
    enableMemberCard: true,
    enableCourseLibrary: true,
    enablePersonalCenter: true,
    enableNotifications: true,
    enableSocialSharing: true,
    enableFeedback: true,

    // 内容设置
    title: "静心瑜伽",
    subtitle: "欢迎来到静心瑜伽，开启您的瑜伽之旅",
    showCarousel: true,
    showAnnouncement: true,
    announcement: "场馆公告：2025年国庆放假通知请点击！！！",
    showCourses: true,
    showVenueInfo: true,
    venueName: "静心瑜伽美学生活馆",
    venueDescription: "静心瑜伽美学生活馆是一家专注于提供高品质瑜伽课程的场馆，我们拥有专业的教练团队和舒适的环境，致力于帮助每一位会员找到身心平衡。",
    venueAddress: "北京市朝阳区三里屯SOHO 5号楼3层301",
    venueHours: "周一至周日 09:00-22:00",

    // 高级设置
    loadingAnimation: "pulse",
    enableAnalytics: true,
    cacheStrategy: "normal",
    refreshInterval: 30,
    debugMode: false
  })

  // 更新设置
  const updateSetting = (key: string, value: any) => {
    setSettings({
      ...settings,
      [key]: value
    })
  }

  // 保存设置
  const saveSettings = () => {
    // 这里可以添加保存到后端的逻辑
    toast({
      title: "设置已保存",
      description: "会员端小程序设置已成功更新。",
    })
  }

  // 主题预设
  const themePresets = [
    { id: "modern", name: "现代简约", primaryColor: "#8A2BE2", secondaryColor: "#FF6B6B" },
    { id: "natural", name: "自然和谐", primaryColor: "#4CAF50", secondaryColor: "#FFC107" },
    { id: "elegant", name: "优雅高贵", primaryColor: "#9C27B0", secondaryColor: "#E91E63" },
    { id: "ocean", name: "海洋清新", primaryColor: "#03A9F4", secondaryColor: "#00BCD4" },
    { id: "warm", name: "温暖阳光", primaryColor: "#FF9800", secondaryColor: "#F44336" }
  ]

  // 应用主题预设
  const applyThemePreset = (presetId: string) => {
    const preset = themePresets.find(p => p.id === presetId)
    if (preset) {
      setSettings({
        ...settings,
        theme: preset.id,
        primaryColor: preset.primaryColor,
        secondaryColor: preset.secondaryColor
      })
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-semibold">会员端小程序设置</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setShowPreview(!showPreview)}>
            <Eye className="mr-2 h-4 w-4" />
            {showPreview ? "隐藏预览" : "显示预览"}
          </Button>
          <Button onClick={saveSettings}>
            <Save className="mr-2 h-4 w-4" />
            保存
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        {showPreview && (
          <div className="md:col-span-2">
            <SimplePreview settings={settings} />
          </div>
        )}

        <div className={cn("md:col-span-3", showPreview ? "" : "md:col-span-5")}>
          <Card>
            <CardHeader>
              <CardTitle>会员端小程序设置</CardTitle>
              <CardDescription>
                配置会员端小程序的风格、布局和功能
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid grid-cols-5 mb-6">
                  <TabsTrigger value="风格">
                    <Palette className="h-4 w-4 mr-2" />
                    风格
                  </TabsTrigger>
                  <TabsTrigger value="布局">
                    <Layout className="h-4 w-4 mr-2" />
                    布局
                  </TabsTrigger>
                  <TabsTrigger value="功能">
                    <Settings className="h-4 w-4 mr-2" />
                    功能
                  </TabsTrigger>
                  <TabsTrigger value="内容">
                    <Layers className="h-4 w-4 mr-2" />
                    内容
                  </TabsTrigger>
                  <TabsTrigger value="高级">
                    <Sparkles className="h-4 w-4 mr-2" />
                    高级
                  </TabsTrigger>
                </TabsList>

                {/* 风格设置选项卡 */}
                <TabsContent value="风格" className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">主题预设</h3>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                      {themePresets.map((preset) => (
                        <div
                          key={preset.id}
                          className={cn(
                            "border rounded-lg p-3 cursor-pointer transition-all",
                            settings.theme === preset.id
                              ? "border-primary ring-2 ring-primary/20"
                              : "hover:border-primary/50"
                          )}
                          onClick={() => applyThemePreset(preset.id)}
                        >
                          <div className="flex gap-2 mb-2">
                            <div
                              className="w-6 h-6 rounded-full"
                              style={{ backgroundColor: preset.primaryColor }}
                            ></div>
                            <div
                              className="w-6 h-6 rounded-full"
                              style={{ backgroundColor: preset.secondaryColor }}
                            ></div>
                          </div>
                          <div className="text-sm font-medium">{preset.name}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="grid gap-6 md:grid-cols-2">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>主色调</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            type="color"
                            value={settings.primaryColor}
                            onChange={(e) => updateSetting('primaryColor', e.target.value)}
                            className="w-12 h-8 p-1"
                          />
                          <Input
                            type="text"
                            value={settings.primaryColor}
                            onChange={(e) => updateSetting('primaryColor', e.target.value)}
                            className="flex-1"
                          />
                        </div>
                        <p className="text-xs text-muted-foreground">
                          主色调用于导航栏、按钮和重要元素
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label>辅助色调</Label>
                        <div className="flex items-center gap-2">
                          <Input
                            type="color"
                            value={settings.secondaryColor}
                            onChange={(e) => updateSetting('secondaryColor', e.target.value)}
                            className="w-12 h-8 p-1"
                          />
                          <Input
                            type="text"
                            value={settings.secondaryColor}
                            onChange={(e) => updateSetting('secondaryColor', e.target.value)}
                            className="flex-1"
                          />
                        </div>
                        <p className="text-xs text-muted-foreground">
                          辅助色调用于强调和装饰元素
                        </p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>字体风格</Label>
                        <Select
                          value={settings.fontFamily}
                          onValueChange={(value) => updateSetting('fontFamily', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="选择字体风格" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">默认字体</SelectItem>
                            <SelectItem value="modern">现代简约</SelectItem>
                            <SelectItem value="elegant">优雅衬线</SelectItem>
                            <SelectItem value="playful">活泼圆润</SelectItem>
                            <SelectItem value="minimal">极简无衬线</SelectItem>
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground">
                          选择适合您品牌的字体风格
                        </p>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Label>圆角大小</Label>
                          <span className="text-sm">{settings.borderRadius}px</span>
                        </div>
                        <Slider
                          value={[settings.borderRadius]}
                          min={0}
                          max={20}
                          step={1}
                          onValueChange={(value) => updateSetting('borderRadius', value[0])}
                        />
                        <p className="text-xs text-muted-foreground">
                          调整界面元素的圆角大小
                        </p>
                      </div>

                      <div className="flex items-center justify-between space-y-0 pt-2">
                        <div className="space-y-0.5">
                          <Label>深色模式</Label>
                          <p className="text-sm text-muted-foreground">
                            启用深色模式选项
                          </p>
                        </div>
                        <Switch
                          checked={settings.darkMode}
                          onCheckedChange={(checked) => updateSetting('darkMode', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* 布局设置选项卡 */}
                <TabsContent value="布局" className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">布局风格</h3>
                    <RadioGroup
                      value={settings.layoutType}
                      onValueChange={(value) => updateSetting('layoutType', value)}
                      className="grid grid-cols-1 md:grid-cols-3 gap-4"
                    >
                      <div className="flex items-start space-x-2">
                        <RadioGroupItem value="card" id="layout-card" />
                        <div className="grid gap-1.5">
                          <Label htmlFor="layout-card" className="font-medium">卡片式布局</Label>
                          <div className="w-full h-24 bg-muted rounded-md flex items-center justify-center mb-1">
                            <Grid3X3 className="h-8 w-8 text-muted-foreground/50" />
                          </div>
                          <p className="text-sm text-muted-foreground">
                            内容以卡片形式展示，清晰分明
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start space-x-2">
                        <RadioGroupItem value="list" id="layout-list" />
                        <div className="grid gap-1.5">
                          <Label htmlFor="layout-list" className="font-medium">列表式布局</Label>
                          <div className="w-full h-24 bg-muted rounded-md flex items-center justify-center mb-1">
                            <div className="w-8 space-y-2">
                              <div className="h-1 w-full bg-muted-foreground/50 rounded"></div>
                              <div className="h-1 w-full bg-muted-foreground/50 rounded"></div>
                              <div className="h-1 w-full bg-muted-foreground/50 rounded"></div>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            内容以列表形式展示，简洁高效
                          </p>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                </TabsContent>

                {/* 功能设置选项卡 */}
                <TabsContent value="功能" className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">功能开关</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="flex items-center justify-between space-y-0">
                        <div className="space-y-0.5">
                          <Label>预约功能</Label>
                          <p className="text-sm text-muted-foreground">
                            允许会员在小程序中预约课程
                          </p>
                        </div>
                        <Switch
                          checked={settings.enableBooking}
                          onCheckedChange={(checked) => updateSetting('enableBooking', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-y-0">
                        <div className="space-y-0.5">
                          <Label>会员卡功能</Label>
                          <p className="text-sm text-muted-foreground">
                            允许会员查看和管理会员卡
                          </p>
                        </div>
                        <Switch
                          checked={settings.enableMemberCard}
                          onCheckedChange={(checked) => updateSetting('enableMemberCard', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-y-0">
                        <div className="space-y-0.5">
                          <Label>课程库功能</Label>
                          <p className="text-sm text-muted-foreground">
                            允许会员浏览课程库
                          </p>
                        </div>
                        <Switch
                          checked={settings.enableCourseLibrary}
                          onCheckedChange={(checked) => updateSetting('enableCourseLibrary', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-y-0">
                        <div className="space-y-0.5">
                          <Label>个人中心功能</Label>
                          <p className="text-sm text-muted-foreground">
                            允许会员管理个人信息和设置
                          </p>
                        </div>
                        <Switch
                          checked={settings.enablePersonalCenter}
                          onCheckedChange={(checked) => updateSetting('enablePersonalCenter', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* 内容设置选项卡 */}
                <TabsContent value="内容" className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">基本信息</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label htmlFor="title">标题</Label>
                        <Input
                          id="title"
                          value={settings.title}
                          onChange={(e) => updateSetting('title', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="subtitle">副标题</Label>
                        <Input
                          id="subtitle"
                          value={settings.subtitle}
                          onChange={(e) => updateSetting('subtitle', e.target.value)}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">内容模块</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="flex items-center justify-between space-y-0">
                        <div className="space-y-0.5">
                          <Label>轮播图</Label>
                          <p className="text-sm text-muted-foreground">
                            显示顶部轮播图
                          </p>
                        </div>
                        <Switch
                          checked={settings.showCarousel}
                          onCheckedChange={(checked) => updateSetting('showCarousel', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-y-0">
                        <div className="space-y-0.5">
                          <Label>公告栏</Label>
                          <p className="text-sm text-muted-foreground">
                            显示场馆公告信息
                          </p>
                        </div>
                        <Switch
                          checked={settings.showAnnouncement}
                          onCheckedChange={(checked) => updateSetting('showAnnouncement', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-y-0">
                        <div className="space-y-0.5">
                          <Label>课程推荐</Label>
                          <p className="text-sm text-muted-foreground">
                            显示推荐课程列表
                          </p>
                        </div>
                        <Switch
                          checked={settings.showCourses}
                          onCheckedChange={(checked) => updateSetting('showCourses', checked)}
                        />
                      </div>

                      <div className="flex items-center justify-between space-y-0">
                        <div className="space-y-0.5">
                          <Label>场馆简介</Label>
                          <p className="text-sm text-muted-foreground">
                            显示场馆基本信息
                          </p>
                        </div>
                        <Switch
                          checked={settings.showVenueInfo}
                          onCheckedChange={(checked) => updateSetting('showVenueInfo', checked)}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">公告内容</h3>
                    <div className="space-y-2">
                      <Label htmlFor="announcement">公告文本</Label>
                      <Input
                        id="announcement"
                        value={settings.announcement}
                        onChange={(e) => updateSetting('announcement', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">场馆信息</h3>
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="venueName">场馆名称</Label>
                        <Input
                          id="venueName"
                          value={settings.venueName}
                          onChange={(e) => updateSetting('venueName', e.target.value)}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="venueDescription">场馆简介</Label>
                        <Input
                          id="venueDescription"
                          value={settings.venueDescription}
                          onChange={(e) => updateSetting('venueDescription', e.target.value)}
                        />
                      </div>

                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor="venueAddress">场馆地址</Label>
                          <Input
                            id="venueAddress"
                            value={settings.venueAddress}
                            onChange={(e) => updateSetting('venueAddress', e.target.value)}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="venueHours">营业时间</Label>
                          <Input
                            id="venueHours"
                            value={settings.venueHours}
                            onChange={(e) => updateSetting('venueHours', e.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
      <Toaster />
    </div>
  )
}
