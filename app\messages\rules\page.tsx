"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import {
  RefreshCw,
  Mail,
  MessageSquare,
  Smartphone
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { MessageRulesContainer } from "./components/message-rules-container"
import { CourseSelectionDialog } from "./components/course-selection-dialog"
import { CardRuleSettingDialog } from "./components/card-rule-setting-dialog"
import { VenueReminderSettingDialog } from "./components/venue-reminder-setting-dialog"
import { CourseRecipientDialog } from "./components/course-recipient-dialog"
import { SimpleRecipientDialog } from "./components/simple-recipient-dialog"

// 示例数据 - 会员端消息配置
const memberMessageRules = [
  // 课程相关提醒
  {
    group: "课程相关",
    rules: [
      {
        id: 1,
        name: "约团课成功提醒",
        template: "尊敬的{{会员姓名}}，您已成功预约{{日期}}{{时间}}的{{课程名称}}，上课地点：{{场地}}，教练：{{教练}}。请提前15分钟到达，祝您上课愉快！",
        smsEnabled: false,
        appEnabled: true,
        emailEnabled: false,
        sendRule: "预约成功时发送"
      },
      {
        id: 2,
        name: "候补排队成功短信提醒",
        template: "尊敬的{{会员姓名}}，您已成功候补{{日期}}{{时间}}的{{课程名称}}，当前排队位置：{{排队位置}}。有名额空出时我们将立即通知您，请保持手机畅通。",
        smsEnabled: false,
        appEnabled: true,
        emailEnabled: false,
        sendRule: "候补成功时发送"
      },
      {
        id: 3,
        name: "取消团课成功提醒",
        template: "尊敬的{{会员姓名}}，已帮您取消{{日期}}{{时间}}的{{课程名称}}，课程已退回您的账户。如有疑问，请联系前台。",
        smsEnabled: false,
        appEnabled: true,
        emailEnabled: false,
        sendRule: "取消预约成功时发送"
      },
      {
        id: 4,
        name: "约课人数不足课程取消提醒",
        template: "很抱歉，您预约的{{日期}}{{时间}}的{{课程名称}}因人数不足已取消，课程已退回。",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "课程因人数不足取消时发送"
      },
      {
        id: 5,
        name: "团课开课前提醒",
        template: "【课程提醒】尊敬的{{会员姓名}}，您预约的{{课程名称}}将于{{日期}}{{时间}}开始，地点：{{场地}}，教练：{{教练}}。请提前15分钟到达签到。期待您的参与！",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "课程开始前提醒",
        courseTypes: [1, 3, 4], // 默认支持团体课、小班课、精品课
        courseReminderSettings: {
          1: 30, // 团体课提前30分钟
          3: 60, // 小班课提前60分钟
          4: 120 // 精品课提前120分钟
        }
      },
      {
        id: 6,
        name: "团课刷卡提醒",
        template: "您已成功签到{{课程名称}}，祝您上课愉快！",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "刷卡签到成功时发送"
      },
      {
        id: 7,
        name: "团课签到提醒",
        template: "您已成功签到{{课程名称}}，祝您上课愉快！",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "手动签到成功时发送"
      },
      {
        id: 8,
        name: "停课自动取消预约提醒",
        template: "很抱歉，您预约的{{日期}}{{时间}}的{{课程名称}}因故停课，课程已退回。",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "课程停课时发送"
      },
      {
        id: 9,
        name: "失约自动取消预约提醒",
        template: "您未按时到达上课地点，系统已自动取消您预约的{{课程名称}}。",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "课程开始15分钟后未签到时发送"
      },
    ]
  },
  // 会员卡相关提醒
  {
    group: "会员卡相关",
    rules: [
      {
        id: 10,
        name: "会员卡到期提醒",
        template: "【会员卡到期提醒】尊敬的{{会员姓名}}，您的{{会员卡名称}}将于{{到期日期}}到期，为不影响您的使用，请及时续费。现在续费可享受优惠，详情请咨询前台。",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "会员卡到期前7天发送",
        showRuleButton: true
      },
      {
        id: 11,
        name: "储值卡余额提醒",
        template: "【余额提醒】尊敬的{{会员姓名}}，您的{{会员卡名称}}余额不足{{金额}}元/次，为不影响您的使用体验，请及时充值。现在充值满额有优惠，详情请咨询前台。",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "储值卡余额低于设定值时发送",
        showRuleButton: true,
        reminderTimes: 2,
        reminderTime: "07:00",
        balanceThresholds: {
          1: 100, // 储值卡低于100元提醒
          2: 5    // 次卡低于5次提醒
        }
      },
      {
        id: 12,
        name: "会员卡购买成功提醒",
        template: "您已成功购买{{会员卡名称}}，有效期至{{到期日期}}，感谢您的支持！",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "会员卡购买成功时发送",
        showRuleButton: false
      },
      {
        id: 13,
        name: "会员卡续费成功提醒",
        template: "您已成功续费{{会员卡名称}}，有效期延长至{{到期日期}}，感谢您的支持！",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "会员卡续费成功时发送",
        showRuleButton: false
      },
    ]
  },
  // 营销相关提醒
  {
    group: "营销相关",
    serviceType: "marketing", // 营销增值服务
    rules: [
      {
        id: 14,
        name: "活动邀请提醒",
        template: "诚邀您参加{{活动名称}}，时间：{{活动时间}}，地点：{{活动地点}}。",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "活动创建并发布时发送"
      },
      {
        id: 15,
        name: "优惠券发放提醒",
        template: "您有一张{{优惠券名称}}已发放到账，有效期至{{到期日期}}，请及时使用。",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "优惠券发放成功时发送"
      },
      {
        id: 16,
        name: "会员生日祝福",
        template: "【生日祝福】亲爱的{{会员姓名}}，祝您生日快乐！在这特别的日子里，我们为您准备了生日专属礼遇：{{生日优惠}}。期待您的光临，再次祝您生日快乐，健康幸福！",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "会员生日当天发送"
      },
    ]
  },
  // 其它提醒
  {
    group: "其它",
    rules: [
      {
        id: 17,
        name: "系统维护通知",
        template: "尊敬的会员，我们的系统将于{{维护时间}}进行维护升级，期间可能影响部分功能使用。",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "系统维护前24小时发送"
      },
      {
        id: 18,
        name: "新功能上线通知",
        template: "尊敬的会员，我们的{{功能名称}}已上线，欢迎体验！",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "新功能上线时发送"
      },
    ]
  },
]

// 示例数据 - 管理端消息配置
const adminMessageRules = [
  // 场馆代办提醒
  {
    group: "场馆代办提醒",
    rules: [
      {
        id: 101,
        name: "会员生日提醒",
        template: "【生日提醒】<场馆名称>有{{count}}位会员将在{{days}}天内过生日，请及时安排生日关怀活动",
        smsEnabled: false,
        appEnabled: true,
        emailEnabled: false,
        sendRule: "每日早上8点自动检查并发送",
        reminderDays: 8,
        showRuleButton: true,
        reminderRoles: [1, 2] // 默认场馆管理员和前台
      },
      {
        id: 102,
        name: "多久未上课提醒",
        template: "【未上课提醒】<场馆名称>有{{count}}位会员已超过{{days}}天未上课，建议联系了解情况",
        smsEnabled: false,
        appEnabled: true,
        emailEnabled: true,
        sendRule: "每周一早上8点自动检查并发送",
        reminderDays: 10,
        showRuleButton: true,
        reminderRoles: [1, 2, 4] // 默认场馆管理员、前台和销售
      },
      {
        id: 103,
        name: "入会纪念日",
        template: "【入会纪念日】<场馆名称>有{{count}}位会员将在{{days}}天内迎来入会纪念日，可安排专属活动提升会员粘性",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "每月1日自动检查并发送",
        reminderDays: 10,
        showRuleButton: true,
        reminderRoles: [1, 2]
      },
      {
        id: 104,
        name: "会员卡将过期",
        template: "【会员卡到期】<场馆名称>有{{count}}张会员卡将在{{days}}天内到期，请及时联系会员办理续卡",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "每日早上9点自动检查并发送",
        reminderDays: 10,
        showRuleButton: true,
        reminderRoles: [1, 2, 4, 5] // 默认场馆管理员、前台、销售和财务
      },
      {
        id: 105,
        name: "次卡余额",
        template: "【次卡余额提醒】<场馆名称>有{{count}}张次卡余额不足{{threshold}}次，请提醒会员及时充值",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "每周三早上9点自动检查并发送",
        reminderDays: 5, // 次数
        showRuleButton: true,
        reminderRoles: [1, 2]
      },
      {
        id: 106,
        name: "储值卡余额",
        template: "【储值卡余额提醒】<场馆名称>有{{count}}张储值卡余额不足{{threshold}}元，请提醒会员及时充值",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "每周三早上9点自动检查并发送",
        reminderDays: 100, // 金额
        showRuleButton: true,
        reminderRoles: [1, 2, 5] // 默认场馆管理员、前台和财务
      },
      {
        id: 107,
        name: "自动开卡提醒",
        template: "【自动开卡提醒】<场馆名称>有{{count}}张会员卡将在{{days}}天内自动开卡，请提前与会员确认",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "每日早上10点自动检查并发送",
        reminderDays: 10,
        showRuleButton: true,
        reminderRoles: [1, 2]
      },
      {
        id: 108,
        name: "请假到期提醒",
        template: "【请假到期提醒】<场馆名称>有{{count}}张会员卡将在{{days}}天内请假到期，请提前联系会员确认复卡安排",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "每日早上10点自动检查并发送",
        reminderDays: 2,
        showRuleButton: true,
        reminderRoles: [1, 2, 3] // 默认场馆管理员、前台和教练
      },
      {
        id: 109,
        name: "发卡提醒",
        template: "【发卡通知】<场馆名称>员工「{{staff_name}}」通过{{channel}}方式为会员「{{member_name}}」发放了一张{{amount}}元的「{{card_type}}」会员卡",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "新会员卡发放时实时发送",
        showRuleButton: true,
        reminderRoles: [1, 2, 5] // 默认场馆管理员、前台和财务
      },
      {
        id: 110,
        name: "回访提醒提醒",
        template: "【回访提醒】<场馆名称>会员「{{member_name}}」今日需要回访，上次回访时间：{{last_visit_date}}，回访原因：{{reason}}",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "系统设定的回访日期到达时发送",
        showRuleButton: true,
        reminderRoles: [1, 2, 4] // 默认场馆管理员、前台和销售
      },
      {
        id: 111,
        name: "课程取消提醒",
        template: "【课程取消通知】<场馆名称>课程「{{course_name}}」（{{date}} {{time}}）已取消，取消原因：{{reason}}，请及时通知相关会员",
        smsEnabled: false,
        appEnabled: true,
        sendRule: "课程被取消时实时发送",
        showRuleButton: true,
        reminderRoles: [1, 2, 3] // 默认场馆管理员、前台和教练
      },
    ]
  },
  // 预约提醒
  {
    group: "预约提醒",
    rules: [
      {
        id: 201,
        name: "约课成功通知提醒",
        template: "【预约通知】会员「{{member_name}}」已预约您{{date}} {{time}}的「{{course_name}}」私教课，请确认并提前{{minutes}}分钟到场准备",
        smsEnabled: true,
        appEnabled: true,
        emailEnabled: true,
        sendRule: "会员预约私教课成功时实时发送",
        showRuleButton: true,
        courseTypes: [2], // 默认支持私教课
        reminderRoles: [3], // 默认选中教练角色
      },
      {
        id: 202,
        name: "取消约课成功提醒",
        template: "【取消预约通知】会员「{{member_name}}」已取消{{date}} {{time}}的「{{course_name}}」私教课，请及时调整您的课程安排",
        smsEnabled: true,
        appEnabled: true,
        emailEnabled: true,
        sendRule: "会员取消私教课预约时实时发送",
        showRuleButton: true,
        courseTypes: [2], // 默认支持私教课
        reminderRoles: [3] // 默认选中教练角色
      },

    ]
  },
]

// 课程类型数据
const courseTypes = [
  { id: 1, name: "团体课" },
  { id: 2, name: "私教课" },
  { id: 3, name: "小班课" },
  { id: 4, name: "精品课" },
  { id: 5, name: "教练培训课" },
  { id: 6, name: "体验课" },
  { id: 7, name: "特色课" },
  { id: 8, name: "季卡课" },
]

// 角色数据
const roles = [
  { id: 1, name: "场馆管理员" },
  { id: 2, name: "前台" },
  { id: 3, name: "教练" },
  { id: 4, name: "销售" },
  { id: 5, name: "财务" },
]

export default function MessageRulesPage() {
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedRule, setSelectedRule] = useState<any>(null)
  // 模拟已启用的增值服务
  const [enabledServices, setEnabledServices] = useState(["marketing"])
  // 课程选择对话框状态
  const [courseSelectionOpen, setCourseSelectionOpen] = useState(false)
  const [ruleForCourseSelection, setRuleForCourseSelection] = useState<any>(null)

  // 会员卡规则设置对话框状态
  const [cardRuleSettingOpen, setCardRuleSettingOpen] = useState(false)
  const [ruleForCardSetting, setRuleForCardSetting] = useState<any>(null)

  // 场馆代办提醒设置对话框状态
  const [venueReminderSettingOpen, setVenueReminderSettingOpen] = useState(false)
  const [ruleForVenueReminder, setRuleForVenueReminder] = useState<any>(null)

  // 课程接收人设置对话框状态
  const [courseRecipientOpen, setCourseRecipientOpen] = useState(false)
  const [ruleForCourseRecipient, setRuleForCourseRecipient] = useState<any>(null)

  // 简化版接收人设置对话框状态
  const [simpleRecipientOpen, setSimpleRecipientOpen] = useState(false)
  const [ruleForSimpleRecipient, setRuleForSimpleRecipient] = useState<any>(null)

  const handleEditRule = (rule: any) => {
    // 为编辑对话框准备规则数据
    setSelectedRule({
      ...rule,
      channels: [
        ...(rule.smsEnabled ? ["短信"] : []),
        ...(rule.appEnabled ? ["APP"] : []),
        ...(rule.emailEnabled ? ["邮件"] : [])
      ]
    })
    setEditDialogOpen(true)
  }

  const handleSaveRule = () => {
    toast({
      title: "规则已保存",
      description: "消息规则设置已成功更新",
    })
    setEditDialogOpen(false)
  }

  const handleToggleSms = (ruleId: number, currentStatus: boolean) => {
    toast({
      title: `短信通知已${currentStatus ? "关闭" : "开启"}`,
      description: `消息规则短信通知状态已更新`,
    })
  }

  const handleToggleApp = (ruleId: number, currentStatus: boolean) => {
    toast({
      title: `APP推送已${currentStatus ? "关闭" : "开启"}`,
      description: `消息规则APP推送状态已更新`,
    })
  }

  const handleToggleEmail = (ruleId: number, currentStatus: boolean) => {
    toast({
      title: `邮件通知已${currentStatus ? "关闭" : "开启"}`,
      description: `消息规则邮件通知状态已更新`,
    })
  }

  // 处理设置规则按钮点击
  const handleSetRule = (rule: any) => {
    // 根据规则类型决定打开哪个对话框
    if (rule.id === 109 || rule.id === 110 || rule.id === 111) {
      // 发卡提醒、回访提醒、课程取消提醒使用简化版接收人设置对话框
      setRuleForSimpleRecipient(rule)
      setSimpleRecipientOpen(true)
    } else if (rule.id >= 101 && rule.id <= 108) {
      // 其他场馆代办提醒规则使用场馆代办提醒设置对话框
      setRuleForVenueReminder(rule)
      setVenueReminderSettingOpen(true)
    } else if (rule.name.includes("会员卡") || rule.name.includes("储值卡")) {
      // 会员卡相关规则使用会员卡规则设置对话框
      setRuleForCardSetting(rule)
      setCardRuleSettingOpen(true)
    } else if (rule.id === 201 || rule.id === 202) {
      // 预约提醒规则使用简化版接收人设置对话框
      setRuleForSimpleRecipient(rule)
      setSimpleRecipientOpen(true)
    } else {
      // 其他课程相关规则使用课程选择对话框
      setRuleForCourseSelection(rule)
      setCourseSelectionOpen(true)
    }
  }

  // 处理保存课程类型选择
  const handleSaveCourseTypes = (rule: any, selectedCourseTypes: number[], reminderSettings?: {[key: number]: number}) => {
    console.log(`为消息规则 ${rule.name} 设置了课程类型:`, selectedCourseTypes)

    if (reminderSettings && Object.keys(reminderSettings).length > 0) {
      console.log(`为消息规则 ${rule.name} 设置了提醒时间:`, reminderSettings)

      // 生成提醒时间描述
      const reminderDesc = Object.entries(reminderSettings)
        .map(([courseId, minutes]) => {
          const courseType = courseTypes.find(c => c.id === parseInt(courseId))
          return courseType ? `${courseType.name}提前${minutes}分钟` : null
        })
        .filter(Boolean)
        .join('、')

      // 这里应该更新状态中的规则数据，但由于是示例，我们只显示一个提示
      toast({
        title: "发送规则已更新",
        description: `已为 ${rule.name} 设置了支持的课程类型和提醒时间: ${reminderDesc}`,
      })
    } else {
      // 这里应该更新状态中的规则数据，但由于是示例，我们只显示一个提示
      toast({
        title: "发送规则已更新",
        description: `已为 ${rule.name} 设置了支持的课程类型`,
      })
    }
  }

  // 处理保存场馆代办提醒设置
  const handleSaveVenueReminderSetting = (rule: any) => {
    console.log(`为场馆代办提醒 ${rule.name} 更新了设置:`, {
      reminderDays: rule.reminderDays,
      reminderRoles: rule.reminderRoles,
      reminderEmployees: rule.reminderEmployees
    })

    // 更新规则的发送规则文本
    let sendRuleText = ""
    if (rule.name.includes("生日")) {
      sendRuleText = `会员生日前${rule.reminderDays}天提醒`
    } else if (rule.name.includes("未上课")) {
      sendRuleText = `会员连续${rule.reminderDays}天未上课提醒`
    } else if (rule.name.includes("过期")) {
      sendRuleText = `会员卡将在${rule.reminderDays}天内过期提醒`
    } else if (rule.name.includes("次卡")) {
      sendRuleText = `次数少于${rule.reminderDays}次提醒`
    } else if (rule.name.includes("储值卡")) {
      sendRuleText = `余额少于${rule.reminderDays}元提醒`
    } else if (rule.name.includes("开卡")) {
      sendRuleText = `距离自动开卡前${rule.reminderDays}天提醒`
    } else if (rule.name.includes("请假")) {
      sendRuleText = `请假到期前${rule.reminderDays}天提醒`
    }

    // 这里应该更新状态中的规则数据，但由于是示例，我们只显示一个提示
    toast({
      title: "发送规则已更新",
      description: `已为 ${rule.name} 设置了${sendRuleText}`,
    })
  }

  // 处理保存会员卡规则设置
  const handleSaveCardRuleSetting = (rule: any) => {
    console.log(`为消息规则 ${rule.name} 更新了设置:`, {
      reminderDays: rule.reminderDays,
      reminderTimes: rule.reminderTimes,
      reminderTime: rule.reminderTime,
      balanceThresholds: rule.balanceThresholds
    })

    // 更新规则的发送规则文本
    let sendRuleText = ""
    if (rule.name.includes("到期")) {
      sendRuleText = `会员卡到期前${rule.reminderDays}天，每天${rule.reminderTime}发送，共${rule.reminderTimes}次`
    } else if (rule.name.includes("余额")) {
      // 生成余额阈值描述
      let thresholdDesc = ""
      if (rule.balanceThresholds) {
        const cardTypes = [
          { id: 1, name: "储值卡", unit: "元" },
          { id: 2, name: "次卡", unit: "次" }
        ]

        thresholdDesc = Object.entries(rule.balanceThresholds)
          .map(([cardId, threshold]) => {
            const cardType = cardTypes.find(c => c.id === parseInt(cardId))
            return cardType ? `${cardType.name}低于${threshold}${cardType.unit}` : null
          })
          .filter(Boolean)
          .join('、')
      }

      sendRuleText = `${thresholdDesc}时，每天${rule.reminderTime}发送，共${rule.reminderTimes}次`
    } else {
      sendRuleText = `每天${rule.reminderTime}发送，共${rule.reminderTimes}次`
    }

    // 这里应该更新状态中的规则数据，但由于是示例，我们只显示一个提示
    toast({
      title: "发送规则已更新",
      description: `已为 ${rule.name} 设置了发送规则: ${sendRuleText}`,
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">消息规则</h1>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
        </div>
      </div>

      <Tabs defaultValue="member" className="w-full">
        <TabsList>
          <TabsTrigger value="member">
            会员端消息配置
          </TabsTrigger>
          <TabsTrigger value="admin">
            管理端消息配置
          </TabsTrigger>
        </TabsList>

        <TabsContent value="member">
          <MessageRulesContainer
            messageGroups={memberMessageRules}
            enabledServices={enabledServices}
            onToggleSms={handleToggleSms}
            onToggleApp={handleToggleApp}
            onToggleEmail={handleToggleEmail}
            onSetRule={handleSetRule}
          />
        </TabsContent>

        <TabsContent value="admin">
          <MessageRulesContainer
            messageGroups={adminMessageRules}
            enabledServices={enabledServices}
            onToggleSms={handleToggleSms}
            onToggleApp={handleToggleApp}
            onToggleEmail={handleToggleEmail}
            onSetRule={handleSetRule}
          />
        </TabsContent>
      </Tabs>

      {/* 编辑规则对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑消息规则</DialogTitle>
            <DialogDescription>
              {selectedRule ? `配置 ${selectedRule.name} 的规则设置` : "配置消息规则设置"}
            </DialogDescription>
          </DialogHeader>
          {selectedRule && (
            <div className="space-y-5">
              <div className="space-y-2">
                <Label htmlFor="rule-name">消息类型</Label>
                <Input id="rule-name" defaultValue={selectedRule.name} />
              </div>

              <Separator />

              <div className="space-y-3">
                <h3 className="text-md font-medium">消息模板</h3>
                <div className="space-y-2">
                  <Label htmlFor="template-content">模板内容</Label>
                  <Textarea
                    id="template-content"
                    defaultValue={selectedRule.template}
                    rows={4}
                  />
                  <p className="text-xs text-gray-500">
                    可用变量：{"{{date}}"}、{"{{time}}"}、{"{{course_name}}"}、{"{{venue}}"}、{"{{coach}}"}、{"{{member_name}}"}、{"{{card_name}}"}、{"{{card_type}}"}、{"{{expire_date}}"}、{"{{amount}}"}、{"{{count}}"}、{"{{days}}"}、{"{{threshold}}"}、{"{{reason}}"}、{"{{minutes}}"}、{"{{staff_name}}"}、{"{{channel}}"}
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <h3 className="text-md font-medium">通知渠道</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="flex items-center justify-between border p-3 rounded-md">
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="channel-sms" className="font-medium">短信通知</Label>
                    </div>
                    <Switch
                      id="channel-sms"
                      defaultChecked={selectedRule.channels.includes("短信")}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>
                  <div className="flex items-center justify-between border p-3 rounded-md">
                    <div className="flex items-center space-x-2">
                      <Smartphone className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="channel-app" className="font-medium">APP推送</Label>
                    </div>
                    <Switch
                      id="channel-app"
                      defaultChecked={selectedRule.channels.includes("APP")}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>
                  <div className="flex items-center justify-between border p-3 rounded-md">
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <Label htmlFor="channel-email" className="font-medium">邮件通知</Label>
                    </div>
                    <Switch
                      id="channel-email"
                      defaultChecked={selectedRule.channels.includes("邮件")}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>取消</Button>
            <Button onClick={handleSaveRule}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 课程选择对话框 */}
      <CourseSelectionDialog
        open={courseSelectionOpen}
        onOpenChange={setCourseSelectionOpen}
        rule={ruleForCourseSelection}
        onSave={handleSaveCourseTypes}
      />

      {/* 会员卡规则设置对话框 */}
      <CardRuleSettingDialog
        open={cardRuleSettingOpen}
        onOpenChange={setCardRuleSettingOpen}
        rule={ruleForCardSetting}
        onSave={handleSaveCardRuleSetting}
      />

      {/* 场馆代办提醒设置对话框 */}
      <VenueReminderSettingDialog
        open={venueReminderSettingOpen}
        onOpenChange={setVenueReminderSettingOpen}
        rule={ruleForVenueReminder}
        onSave={handleSaveVenueReminderSetting}
      />

      {/* 课程接收人设置对话框 */}
      <CourseRecipientDialog
        open={courseRecipientOpen}
        onOpenChange={setCourseRecipientOpen}
        rule={ruleForCourseRecipient}
        onSave={(rule) => {
          console.log(`为课程消息 ${rule.name} 更新了设置:`, {
            courseTypes: rule.courseTypes,
            reminderRoles: rule.reminderRoles,
            reminderEmployees: rule.reminderEmployees
          })

          // 获取选中的课程类型名称
          const selectedCourseTypeNames = rule.courseTypes?.map(typeId => {
            const courseType = courseTypes.find(c => c.id === typeId)
            return courseType ? courseType.name : ''
          }).filter(Boolean).join('、')

          // 获取选中的角色名称
          const selectedRoleNames = rule.reminderRoles?.map(roleId => {
            const role = roles.find(r => r.id === roleId)
            return role ? role.name : ''
          }).filter(Boolean).join('、')

          toast({
            title: "规则设置已更新",
            description: `已为 ${rule.name} 设置了支持的课程类型(${selectedCourseTypeNames})和接收人(${selectedRoleNames})`,
          })
        }}
      />

      {/* 简化版接收人设置对话框 */}
      <SimpleRecipientDialog
        open={simpleRecipientOpen}
        onOpenChange={setSimpleRecipientOpen}
        rule={ruleForSimpleRecipient}
        onSave={(rule) => {
          console.log(`为消息 ${rule.name} 更新了接收人设置:`, {
            reminderRoles: rule.reminderRoles,
            reminderEmployees: rule.reminderEmployees
          })

          // 获取选中的角色名称
          const selectedRoleNames = rule.reminderRoles?.map(roleId => {
            const role = roles.find(r => r.id === roleId)
            return role ? role.name : ''
          }).filter(Boolean).join('、')

          toast({
            title: "接收人设置已更新",
            description: `已为 ${rule.name} 设置了接收人(${selectedRoleNames})`,
          })
        }}
      />
    </div>
  )
}
