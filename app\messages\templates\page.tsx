"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import {
  FileText,
  Plus,
  Search,
  Filter,
  Settings,
  Edit,
  Trash2,
  Copy,
  Eye,
  Save,
  RefreshCw,
  MessageSquare,
  Mail,
  Smartphone,
  Send,
  Bell
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// 示例数据
const bookingTemplates = [
  {
    id: 1,
    name: "预约成功通知",
    type: "预约通知",
    channels: ["短信", "微信", "APP"],
    updateTime: "2023-05-01",
    status: "启用"
  },
  {
    id: 2,
    name: "取消预约通知",
    type: "预约通知",
    channels: ["短信", "微信"],
    updateTime: "2023-05-02",
    status: "启用"
  },
  {
    id: 3,
    name: "约课人数不足取消通知",
    type: "预约通知",
    channels: ["短信", "微信", "APP"],
    updateTime: "2023-05-03",
    status: "启用"
  },
]

const reminderTemplates = [
  {
    id: 4,
    name: "课程开始提醒",
    type: "提醒通知",
    channels: ["微信", "APP"],
    updateTime: "2023-05-04",
    status: "启用"
  },
  {
    id: 5,
    name: "会员卡到期提醒",
    type: "提醒通知",
    channels: ["短信", "微信", "APP"],
    updateTime: "2023-05-05",
    status: "启用"
  },
]

const marketingTemplates = [
  {
    id: 6,
    name: "促销活动通知",
    type: "营销通知",
    channels: ["微信", "APP"],
    updateTime: "2023-05-06",
    status: "启用"
  },
  {
    id: 7,
    name: "会员生日祝福",
    type: "营销通知",
    channels: ["短信", "微信"],
    updateTime: "2023-05-07",
    status: "启用"
  },
]

const serviceTemplates = [
  {
    id: 8,
    name: "课后反馈请求",
    type: "服务通知",
    channels: ["微信", "APP"],
    updateTime: "2023-05-08",
    status: "启用"
  },
  {
    id: 9,
    name: "会员卡余额变动通知",
    type: "服务通知",
    channels: ["短信", "微信", "APP"],
    updateTime: "2023-05-09",
    status: "启用"
  },
]

const systemTemplates = [
  {
    id: 10,
    name: "系统维护通知",
    type: "系统通知",
    channels: ["系统内通知"],
    updateTime: "2023-05-10",
    status: "启用"
  },
  {
    id: 11,
    name: "新功能上线通知",
    type: "系统通知",
    channels: ["系统内通知"],
    updateTime: "2023-05-11",
    status: "启用"
  },
]

// 模板内容示例
const templateContents = {
  1: `尊敬的{{会员名称}}，您已成功预约{{课程名称}}，上课时间：{{上课时间}}，地点：{{场地}}，教练：{{教练}}。期待您的到来！`,
  4: `尊敬的{{会员名称}}，温馨提醒您，您预约的{{课程名称}}将于{{上课时间}}开始，地点：{{场地}}，教练：{{教练}}。请提前到达，谢谢！`,
  5: `尊敬的{{会员名称}}，您的{{会员卡类型}}将于{{到期日期}}到期，为了不影响您的使用，请及时续费。`,
  7: `亲爱的{{会员名称}}，静心瑜伽馆全体员工祝您生日快乐！作为我们尊贵的会员，我们为您准备了生日专属优惠，详情请咨询前台。`,
}

export default function MessageTemplatesPage() {
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [templateContent, setTemplateContent] = useState("")
  const [previewContent, setPreviewContent] = useState("")

  const handleEditTemplate = (template) => {
    setSelectedTemplate(template)
    setTemplateContent(templateContents[template.id] || "模板内容示例")
    setEditDialogOpen(true)
  }

  const handlePreviewTemplate = (template) => {
    setSelectedTemplate(template)
    const content = templateContents[template.id] || "模板内容示例"
    // 替换模板变量为示例值
    const preview = content
      .replace(/\{\{会员名称\}\}/g, "张三")
      .replace(/\{\{课程名称\}\}/g, "高级瑜伽课程")
      .replace(/\{\{上课时间\}\}/g, "2023-05-01 10:00")
      .replace(/\{\{场地\}\}/g, "A区瑜伽室")
      .replace(/\{\{教练\}\}/g, "李教练")
      .replace(/\{\{会员卡类型\}\}/g, "年卡")
      .replace(/\{\{到期日期\}\}/g, "2023-12-31")

    setPreviewContent(preview)
    setPreviewDialogOpen(true)
  }

  const handleSaveTemplate = () => {
    toast({
      title: "模板已保存",
      description: "消息模板内容已成功更新",
    })
    setEditDialogOpen(false)
  }

  const handleToggleStatus = (id, currentStatus) => {
    toast({
      title: `模板已${currentStatus === "启用" ? "禁用" : "启用"}`,
      description: `消息模板状态已更新为${currentStatus === "启用" ? "禁用" : "启用"}`,
    })
  }

  const renderTemplateTable = (templates) => (
    <div className="rounded-md border">
      <div className="relative w-full overflow-auto">
        <table className="w-full caption-bottom text-sm">
          <thead className="[&_tr]:border-b">
            <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
              <th className="h-12 px-4 text-left align-middle font-medium">模板名称</th>
              <th className="h-12 px-4 text-left align-middle font-medium">模板类型</th>
              <th className="h-12 px-4 text-left align-middle font-medium">适用渠道</th>
              <th className="h-12 px-4 text-left align-middle font-medium">更新时间</th>
              <th className="h-12 px-4 text-left align-middle font-medium">状态</th>
              <th className="h-12 px-4 text-left align-middle font-medium">操作</th>
            </tr>
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {templates
              .filter(template => template.name.toLowerCase().includes(searchQuery.toLowerCase()))
              .map((template) => (
                <tr key={template.id} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                  <td className="p-4 align-middle">{template.name}</td>
                  <td className="p-4 align-middle">{template.type}</td>
                  <td className="p-4 align-middle">
                    <div className="flex flex-wrap gap-1">
                      {template.channels.map((channel) => (
                        <Badge key={channel} variant="outline">{channel}</Badge>
                      ))}
                    </div>
                  </td>
                  <td className="p-4 align-middle">{template.updateTime}</td>
                  <td className="p-4 align-middle">
                    <Switch
                      checked={template.status === "启用"}
                      onCheckedChange={() => handleToggleStatus(template.id, template.status)}
                    />
                  </td>
                  <td className="p-4 align-middle">
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleEditTemplate(template)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handlePreviewTemplate(template)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">消息模板</h1>
          <p className="text-sm text-muted-foreground mt-1">管理各类消息的内容模板</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            重置模板
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            新增模板
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索模板名称..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="模板类型筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部类型</SelectItem>
            <SelectItem value="booking">预约通知</SelectItem>
            <SelectItem value="reminder">提醒通知</SelectItem>
            <SelectItem value="marketing">营销通知</SelectItem>
            <SelectItem value="service">服务通知</SelectItem>
            <SelectItem value="system">系统通知</SelectItem>
          </SelectContent>
        </Select>
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="适用渠道筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部渠道</SelectItem>
            <SelectItem value="sms">短信</SelectItem>
            <SelectItem value="wechat">微信</SelectItem>
            <SelectItem value="app">APP</SelectItem>
            <SelectItem value="system">系统内通知</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="booking" className="space-y-4">
        <TabsList>
          <TabsTrigger value="booking" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            预约通知
          </TabsTrigger>
          <TabsTrigger value="reminder" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            提醒通知
          </TabsTrigger>
          <TabsTrigger value="marketing" className="flex items-center gap-2">
            <Send className="h-4 w-4" />
            营销通知
          </TabsTrigger>
          <TabsTrigger value="service" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            服务通知
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            系统通知
          </TabsTrigger>
        </TabsList>

        <TabsContent value="booking">
          {renderTemplateTable(bookingTemplates)}
        </TabsContent>

        <TabsContent value="reminder">
          {renderTemplateTable(reminderTemplates)}
        </TabsContent>

        <TabsContent value="marketing">
          {renderTemplateTable(marketingTemplates)}
        </TabsContent>

        <TabsContent value="service">
          {renderTemplateTable(serviceTemplates)}
        </TabsContent>

        <TabsContent value="system">
          {renderTemplateTable(systemTemplates)}
        </TabsContent>
      </Tabs>

      {/* 编辑模板对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>编辑消息模板</DialogTitle>
            <DialogDescription>
              {selectedTemplate ? `配置 ${selectedTemplate.name} 的模板内容` : "配置消息模板内容"}
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="template-name">模板名称</Label>
                  <Input id="template-name" defaultValue={selectedTemplate.name} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="template-type">模板类型</Label>
                  <Select defaultValue={selectedTemplate.type.toLowerCase().replace(/\s+/g, '-')}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择模板类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="预约通知">预约通知</SelectItem>
                      <SelectItem value="提醒通知">提醒通知</SelectItem>
                      <SelectItem value="营销通知">营销通知</SelectItem>
                      <SelectItem value="服务通知">服务通知</SelectItem>
                      <SelectItem value="系统通知">系统通知</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">适用渠道</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="channel-sms" defaultChecked={selectedTemplate.channels.includes("短信")} />
                    <Label htmlFor="channel-sms">短信通知</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="channel-wechat" defaultChecked={selectedTemplate.channels.includes("微信")} />
                    <Label htmlFor="channel-wechat">微信通知</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="channel-app" defaultChecked={selectedTemplate.channels.includes("APP")} />
                    <Label htmlFor="channel-app">APP推送</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="channel-system" defaultChecked={selectedTemplate.channels.includes("系统内通知")} />
                    <Label htmlFor="channel-system">系统内通知</Label>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">模板内容</h3>
                <div className="space-y-2">
                  <Label htmlFor="template-content">短信/系统内通知内容</Label>
                  <Textarea
                    id="template-content"
                    value={templateContent}
                    onChange={(e) => setTemplateContent(e.target.value)}
                    className="min-h-[120px]"
                  />
                  <p className="text-sm text-muted-foreground">
                    可用变量：{"{{会员名称}}"}、{"{{课程名称}}"}、{"{{上课时间}}"}、{"{{场地}}"}、{"{{教练}}"}、{"{{会员卡类型}}"}、{"{{到期日期}}"}
                  </p>
                </div>

                {selectedTemplate.channels.includes("微信") && (
                  <div className="space-y-2">
                    <Label htmlFor="wechat-template-id">微信模板消息ID</Label>
                    <Input id="wechat-template-id" defaultValue="TM00001" />
                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div className="space-y-2">
                        <Label htmlFor="wechat-first-param">first参数</Label>
                        <Input id="wechat-first-param" defaultValue="{{first.DATA}}" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="wechat-remark-param">remark参数</Label>
                        <Input id="wechat-remark-param" defaultValue="{{remark.DATA}}" />
                      </div>
                    </div>
                  </div>
                )}

                {selectedTemplate.channels.includes("APP") && (
                  <div className="space-y-2">
                    <Label htmlFor="app-title">APP推送标题</Label>
                    <Input id="app-title" defaultValue={selectedTemplate.name} />
                    <Label htmlFor="app-content" className="mt-2">APP推送内容</Label>
                    <Input id="app-content" defaultValue="您有一条新的通知，请查看" />
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="template-status" defaultChecked={selectedTemplate.status === "启用"} />
                <Label htmlFor="template-status">启用此模板</Label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>取消</Button>
            <Button onClick={handleSaveTemplate}>保存模板</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 预览模板对话框 */}
      <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>模板预览</DialogTitle>
            <DialogDescription>
              {selectedTemplate ? `${selectedTemplate.name} 的预览效果` : "模板预览效果"}
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-6">
              <div className="rounded-lg border p-4">
                <div className="flex items-center gap-2 mb-2">
                  {selectedTemplate.channels.includes("短信") && (
                    <Badge variant="outline" className="bg-blue-50">
                      <Mail className="h-3 w-3 mr-1" />
                      短信
                    </Badge>
                  )}
                  {selectedTemplate.channels.includes("微信") && (
                    <Badge variant="outline" className="bg-green-50">
                      <MessageSquare className="h-3 w-3 mr-1" />
                      微信
                    </Badge>
                  )}
                  {selectedTemplate.channels.includes("APP") && (
                    <Badge variant="outline" className="bg-purple-50">
                      <Smartphone className="h-3 w-3 mr-1" />
                      APP
                    </Badge>
                  )}
                </div>
                <p className="text-sm">{previewContent}</p>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium">预览说明</h4>
                <p className="text-xs text-muted-foreground">
                  此预览展示了模板变量替换后的效果，实际发送时会根据真实数据进行替换。
                </p>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button onClick={() => setPreviewDialogOpen(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
