"use client"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { DateRange } from "react-day-picker"

interface DatePickerProps {
  placeholder?: string
  selected?: Date | null | DateRange
  onSelect: (date: Date | null | DateRange) => void
  className?: string
  mode?: "single" | "range"
}

export function DatePicker({
  placeholder = "选择日期",
  selected,
  onSelect,
  className,
  mode = "single"
}: DatePickerProps) {

  // 处理日期显示
  const formatDate = () => {
    if (!selected) return <span>{placeholder}</span>

    if (mode === "single" && selected instanceof Date) {
      return format(selected, "yyyy-MM-dd")
    }

    if (mode === "range" && typeof selected === "object" && selected !== null && !Array.isArray(selected)) {
      const { from, to } = selected as DateRange
      if (from && to) {
        return `${format(from, "yyyy-MM-dd")} - ${format(to, "yyyy-MM-dd")}`
      } else if (from) {
        return `${format(from, "yyyy-MM-dd")} - `
      }
    }

    return <span>{placeholder}</span>
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn("w-full justify-start text-left font-normal",
            (!selected || (mode === "range" && typeof selected === "object" && !selected.from)) && "text-muted-foreground",
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {formatDate()}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        {mode === "single" ? (
          <Calendar
            mode="single"
            selected={selected instanceof Date ? selected : undefined}
            onSelect={onSelect as (date: Date | undefined) => void}
          />
        ) : (
          <Calendar
            mode="range"
            selected={selected as DateRange}
            onSelect={onSelect as (date: DateRange) => void}
            numberOfMonths={2}
          />
        )}
      </PopoverContent>
    </Popover>
  )
}

