"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"
import { Bell, Info, AlertTriangle, CheckCircle } from "lucide-react"

// 模拟通知数据
const notifications = [
  {
    id: 1,
    type: "info", // info, warning, success, error
    title: "系统更新通知",
    message: "系统将于今晚22:00-23:00进行维护更新，请提前做好准备。",
    time: "10分钟前",
  },
  {
    id: 2,
    type: "warning",
    title: "库存预警",
    message: "瑜伽垫库存不足，当前剩余5个，请及时补充。",
    time: "30分钟前",
  },
  {
    id: 3,
    type: "success",
    title: "数据备份完成",
    message: "系统数据已成功备份，备份时间：2023-03-28 08:00。",
    time: "2小时前",
  },
]

// 获取通知图标
const getNotificationIcon = (type: string) => {
  switch(type) {
    case "info":
      return <Bell className="h-5 w-5 text-blue-500 mt-0.5" />
    case "warning":
      return <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
    case "success":
      return <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
    case "error":
      return <Info className="h-5 w-5 text-red-500 mt-0.5" />
    default:
      return <Bell className="h-5 w-5 text-blue-500 mt-0.5" />
  }
}

export function SystemNotifications() {
  const router = useRouter()
  
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center justify-between">
          <span>系统通知</span>
          <Badge>{notifications.length}条新消息</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {notifications.map((notification) => (
            <div key={notification.id} className="flex items-start gap-3 p-2 rounded-md bg-muted/50">
              {getNotificationIcon(notification.type)}
              <div>
                <div className="font-medium">{notification.title}</div>
                <div className="text-sm text-muted-foreground">
                  {notification.message}
                </div>
                <div className="text-xs text-muted-foreground mt-1">{notification.time}</div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          variant="ghost" 
          className="w-full"
          onClick={() => router.push('/notifications')}
        >
          查看全部通知
        </Button>
      </CardFooter>
    </Card>
  )
}
