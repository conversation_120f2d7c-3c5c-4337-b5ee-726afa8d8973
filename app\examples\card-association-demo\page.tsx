"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { CardAssociationScrollable } from "@/components/courses/card-association-scrollable"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function CardAssociationDemoPage() {
  const [selectedCards, setSelectedCards] = useState<number[]>([])
  
  return (
    <div className="container py-8 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">会员卡关联演示</h1>
          <p className="text-muted-foreground">可滚动的会员卡关联组件，替代原有的选项卡形式</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>原有选项卡形式</CardTitle>
              <CardDescription>
                使用选项卡切换不同类型的会员卡
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="time">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="time">时间卡</TabsTrigger>
                  <TabsTrigger value="count">次数卡</TabsTrigger>
                  <TabsTrigger value="value">储值卡</TabsTrigger>
                </TabsList>
                
                <TabsContent value="time" className="pt-4 space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5">
                      <div className="flex items-center gap-3">
                        <div className="h-4 w-4 rounded-full bg-[#4F46E5]" />
                        <div>
                          <div className="font-medium">瑜伽年卡</div>
                          <div className="text-xs text-muted-foreground">365天</div>
                        </div>
                      </div>
                      <input type="checkbox" className="h-4 w-4" />
                    </div>
                    
                    <div className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5">
                      <div className="flex items-center gap-3">
                        <div className="h-4 w-4 rounded-full bg-[#06B6D4]" />
                        <div>
                          <div className="font-medium">瑜伽季卡</div>
                          <div className="text-xs text-muted-foreground">90天</div>
                        </div>
                      </div>
                      <input type="checkbox" className="h-4 w-4" />
                    </div>
                    
                    <div className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5">
                      <div className="flex items-center gap-3">
                        <div className="h-4 w-4 rounded-full bg-[#10B981]" />
                        <div>
                          <div className="font-medium">瑜伽月卡</div>
                          <div className="text-xs text-muted-foreground">30天</div>
                        </div>
                      </div>
                      <input type="checkbox" className="h-4 w-4" />
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="count" className="pt-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5">
                      <div className="flex items-center gap-3">
                        <div className="h-4 w-4 rounded-full bg-[#F59E0B]" />
                        <div>
                          <div className="font-medium">20次卡</div>
                          <div className="text-xs text-muted-foreground">20次</div>
                        </div>
                      </div>
                      <input type="checkbox" className="h-4 w-4" />
                    </div>
                    
                    <div className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5">
                      <div className="flex items-center gap-3">
                        <div className="h-4 w-4 rounded-full bg-[#EC4899]" />
                        <div>
                          <div className="font-medium">10次卡</div>
                          <div className="text-xs text-muted-foreground">10次</div>
                        </div>
                      </div>
                      <input type="checkbox" className="h-4 w-4" />
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="value" className="pt-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5">
                      <div className="flex items-center gap-3">
                        <div className="h-4 w-4 rounded-full bg-[#EF4444]" />
                        <div>
                          <div className="font-medium">1000元储值卡</div>
                          <div className="text-xs text-muted-foreground">¥1,000</div>
                        </div>
                      </div>
                      <input type="checkbox" className="h-4 w-4" />
                    </div>
                    
                    <div className="flex items-center justify-between p-3 rounded-md border hover:bg-accent/5">
                      <div className="flex items-center gap-3">
                        <div className="h-4 w-4 rounded-full bg-[#F97316]" />
                        <div>
                          <div className="font-medium">2000元储值卡</div>
                          <div className="text-xs text-muted-foreground">¥2,000</div>
                        </div>
                      </div>
                      <input type="checkbox" className="h-4 w-4" />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
          
          <div className="text-center">
            <p className="text-muted-foreground">
              原有选项卡形式需要切换标签页查看不同类型的会员卡，<br />
              用户体验不够流畅，且无法同时查看所有类型的会员卡
            </p>
          </div>
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>新的可滚动形式</CardTitle>
              <CardDescription>
                所有类型的会员卡在一个可滚动的区域内展示
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CardAssociationScrollable
                initialSelectedCards={selectedCards}
                onChange={setSelectedCards}
              />
            </CardContent>
          </Card>
          
          <div className="text-center">
            <p className="text-muted-foreground">
              新的可滚动形式将所有类型的会员卡集中展示，<br />
              用户可以一目了然地查看所有会员卡，并快速进行选择
            </p>
          </div>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>已选择的会员卡</CardTitle>
          <CardDescription>
            当前选择的会员卡ID列表
          </CardDescription>
        </CardHeader>
        <CardContent>
          {selectedCards.length > 0 ? (
            <pre className="bg-muted p-4 rounded-md overflow-auto">
              {JSON.stringify(selectedCards, null, 2)}
            </pre>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              尚未选择任何会员卡
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
