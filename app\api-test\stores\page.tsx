'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface Store {
  id: number;
  tenant_id: number;
  store_name: string;
  store_type: string;
  address: string;
  city: string;
  province: string;
  country: string;
  phone: string;
  email?: string;
  business_hours: string;
  manager_name?: string;
  logo_url?: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export default function StoresTestPage() {
  const [stores, setStores] = useState<Store[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [newStore, setNewStore] = useState({
    store_name: '静心瑜伽馆 - 新店',
    store_type: '分店',
    address: '北京市朝阳区建外SOHO A座1001',
    city: '北京',
    province: '北京',
    country: '中国',
    phone: '010-87654324',
    email: '<EMAIL>',
    business_hours: '09:00-21:00',
    manager_name: '李新',
    logo_url: '/logo/jingxin.png',
    status: 1
  })

  const fetchStores = async () => {
    setLoading(true)
    setError('')
    
    try {
      const response = await fetch('/api/tenant/stores')
      const data = await response.json()
      
      if (data.code === 0) {
        setStores(data.data.stores)
      } else {
        setError(data.message || '获取门店失败')
      }
    } catch (err: any) {
      setError('请求出错: ' + (err.message || '未知错误'))
    } finally {
      setLoading(false)
    }
  }

  const createStore = async () => {
    setLoading(true)
    setError('')
    
    try {
      const response = await fetch('/api/tenant/stores', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newStore)
      })
      
      const data = await response.json()
      
      if (data.code === 0) {
        alert('门店创建成功!')
        fetchStores() // 重新加载门店列表
      } else {
        setError(data.message || '创建门店失败')
      }
    } catch (err: any) {
      setError('请求出错: ' + (err.message || '未知错误'))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStores()
  }, [])

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">连锁管理 API 测试</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>获取门店列表</CardTitle>
            <CardDescription>从 API 获取当前租户的所有门店</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchStores} disabled={loading}>
              {loading ? '加载中...' : '刷新门店列表'}
            </Button>
            
            {error && <p className="text-red-500 mt-4">{error}</p>}
            
            <div className="mt-4">
              <h3 className="font-semibold mb-2">门店列表 ({stores.length})</h3>
              <div className="space-y-4">
                {stores.map((store) => (
                  <div key={store.id} className="p-4 border rounded-md">
                    <h4 className="font-bold">{store.store_name}</h4>
                    <p className="text-sm text-gray-500">{store.store_type} - {store.city}</p>
                    <p className="text-sm">{store.address}</p>
                    <p className="text-sm">联系电话: {store.phone}</p>
                    <p className="text-sm">营业时间: {store.business_hours}</p>
                    <p className="text-sm">状态: {store.status === 1 ? '营业中' : store.status === 0 ? '休息中' : '已关闭'}</p>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>创建新门店</CardTitle>
            <CardDescription>通过 API 创建新的门店记录</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">门店名称</label>
                <input
                  type="text"
                  value={newStore.store_name}
                  onChange={(e) => setNewStore({...newStore, store_name: e.target.value})}
                  className="w-full p-2 border rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">门店地址</label>
                <input
                  type="text"
                  value={newStore.address}
                  onChange={(e) => setNewStore({...newStore, address: e.target.value})}
                  className="w-full p-2 border rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">联系电话</label>
                <input
                  type="text"
                  value={newStore.phone}
                  onChange={(e) => setNewStore({...newStore, phone: e.target.value})}
                  className="w-full p-2 border rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">店长姓名</label>
                <input
                  type="text"
                  value={newStore.manager_name || ''}
                  onChange={(e) => setNewStore({...newStore, manager_name: e.target.value})}
                  className="w-full p-2 border rounded-md"
                />
              </div>
              
              <Button onClick={createStore} disabled={loading}>
                {loading ? '处理中...' : '创建新门店'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}