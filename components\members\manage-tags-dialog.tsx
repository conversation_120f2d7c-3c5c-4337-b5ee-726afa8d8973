"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Tag, Search, Plus, X, Check } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

interface ManageTagsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
    tags: string[]
  }
}

export function ManageTagsDialog({ open, onOpenChange, member }: ManageTagsDialogProps) {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>(member.tags || [])
  const [newTagName, setNewTagName] = useState("")
  const [newTagColor, setNewTagColor] = useState("#4285F4")
  
  // 模拟标签数据
  const allTags = [
    { id: "1", name: "新会员", color: "#4CAF50", count: 45 },
    { id: "2", name: "高频用户", color: "#2196F3", count: 28 },
    { id: "3", name: "VIP", color: "#FFC107", count: 15 },
    { id: "4", name: "潜在流失", color: "#F44336", count: 12 },
    { id: "5", name: "已推荐", color: "#9C27B0", count: 8 },
    { id: "6", name: "生日月", color: "#E91E63", count: 22 },
    { id: "7", name: "瑜伽爱好者", color: "#3F51B5", count: 34 },
    { id: "8", name: "普拉提爱好者", color: "#009688", count: 19 },
    { id: "9", name: "周末班", color: "#FF5722", count: 27 },
    { id: "10", name: "工作日班", color: "#795548", count: 31 },
    { id: "11", name: "早班", color: "#607D8B", count: 16 },
    { id: "12", name: "晚班", color: "#9E9E9E", count: 23 },
  ]
  
  // 根据搜索过滤标签
  const filteredTags = allTags.filter(tag => 
    tag.name.toLowerCase().includes(searchQuery.toLowerCase())
  )
  
  // 处理标签选择
  const toggleTag = (tagId: string) => {
    if (selectedTags.includes(tagId)) {
      setSelectedTags(selectedTags.filter(id => id !== tagId))
    } else {
      setSelectedTags([...selectedTags, tagId])
    }
  }
  
  // 处理保存
  const handleSave = () => {
    // 在实际应用中，这里应该调用API保存数据
    toast({
      title: "标签已更新",
      description: `已为 ${member.name} 更新标签`,
    })
    onOpenChange(false)
  }
  
  // 处理创建新标签
  const handleCreateTag = () => {
    if (!newTagName.trim()) {
      toast({
        title: "标签名称不能为空",
        variant: "destructive",
      })
      return
    }
    
    // 模拟创建新标签
    toast({
      title: "标签已创建",
      description: `已创建标签 "${newTagName}"`,
    })
    
    // 清空输入
    setNewTagName("")
  }
  
  // 获取标签对象
  const getTagById = (tagId: string) => {
    return allTags.find(tag => tag.id === tagId)
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>管理标签</DialogTitle>
          <DialogDescription>
            为 {member.name} 添加或移除标签
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="browse" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="browse">
              <Tag className="mr-2 h-4 w-4" />
              浏览标签
            </TabsTrigger>
            <TabsTrigger value="create">
              <Plus className="mr-2 h-4 w-4" />
              创建标签
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="browse" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索标签..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div>
                <Label className="text-sm text-muted-foreground">已选择的标签</Label>
                <div className="flex flex-wrap gap-2 mt-2 min-h-10 p-2 border rounded-md">
                  {selectedTags.length === 0 ? (
                    <span className="text-sm text-muted-foreground">未选择任何标签</span>
                  ) : (
                    selectedTags.map(tagId => {
                      const tag = getTagById(tagId)
                      if (!tag) return null
                      return (
                        <Badge 
                          key={tagId} 
                          style={{ backgroundColor: tag.color }}
                          className="text-white flex items-center gap-1"
                        >
                          {tag.name}
                          <X 
                            className="h-3 w-3 cursor-pointer" 
                            onClick={() => toggleTag(tagId)}
                          />
                        </Badge>
                      )
                    })
                  )}
                </div>
              </div>
              
              <ScrollArea className="h-[300px] border rounded-md p-4">
                <div className="grid grid-cols-2 gap-2">
                  {filteredTags.map(tag => (
                    <div 
                      key={tag.id}
                      className={`flex items-center justify-between p-2 rounded-md border cursor-pointer hover:bg-muted transition-colors ${
                        selectedTags.includes(tag.id) ? 'bg-muted' : ''
                      }`}
                      onClick={() => toggleTag(tag.id)}
                    >
                      <div className="flex items-center gap-2">
                        <div 
                          className="h-4 w-4 rounded-full" 
                          style={{ backgroundColor: tag.color }}
                        />
                        <span>{tag.name}</span>
                        <span className="text-xs text-muted-foreground">({tag.count})</span>
                      </div>
                      {selectedTags.includes(tag.id) && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
          
          <TabsContent value="create" className="space-y-4 py-4">
            <Card>
              <CardHeader>
                <CardTitle>创建新标签</CardTitle>
                <CardDescription>
                  创建一个新的标签，可以应用于任何会员
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="tag-name">标签名称</Label>
                  <Input
                    id="tag-name"
                    placeholder="输入标签名称"
                    value={newTagName}
                    onChange={(e) => setNewTagName(e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="tag-color">标签颜色</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="tag-color"
                      type="color"
                      value={newTagColor}
                      onChange={(e) => setNewTagColor(e.target.value)}
                      className="w-16 h-10"
                    />
                    <div 
                      className="w-10 h-10 rounded-full border"
                      style={{ backgroundColor: newTagColor }}
                    />
                    <span className="text-sm text-muted-foreground">{newTagColor}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={handleCreateTag}>
                  <Plus className="mr-2 h-4 w-4" />
                  创建标签
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
        
        <DialogFooter className="flex justify-between">
          <div>
            <span className="text-sm text-muted-foreground">已选择 {selectedTags.length} 个标签</span>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleSave}>
              保存更改
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
