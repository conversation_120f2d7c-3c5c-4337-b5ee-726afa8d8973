"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Clock, Users } from "lucide-react"

// 模拟关联课程数据
const relatedCourses = [
  { id: 1, name: "基础瑜伽入门", coach: "张教练", schedule: "每周一 10:00-11:30", members: 12, capacity: 20 },
  { id: 2, name: "基础瑜伽进阶", coach: "李教练", schedule: "每周三 14:00-15:30", members: 15, capacity: 15 },
  { id: 3, name: "基础瑜伽晚间班", coach: "王教练", schedule: "每周五 19:00-20:30", members: 8, capacity: 20 },
  { id: 4, name: "基础瑜伽周末班", coach: "赵教练", schedule: "每周六 09:00-10:30", members: 18, capacity: 20 },
  { id: 5, name: "基础瑜伽强化班", coach: "刘教练", schedule: "每周日 16:00-17:30", members: 10, capacity: 15 },
]

// 模拟使用统计数据
const usageStats = {
  totalBookings: 450,
  averageAttendance: 85,
  popularTimeSlots: [
    { day: "周一", time: "10:00-12:00", count: 120 },
    { day: "周三", time: "14:00-16:00", count: 95 },
    { day: "周六", time: "09:00-11:00", count: 135 },
  ],
  monthlyTrend: [
    { month: "1月", count: 35 },
    { month: "2月", count: 42 },
    { month: "3月", count: 38 },
    { month: "4月", count: 45 },
    { month: "5月", count: 50 },
    { month: "6月", count: 48 },
  ],
}

interface CourseTypeDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  type?: any // 课程类型数据
}

export function CourseTypeDetailDialog({ open, onOpenChange, type }: CourseTypeDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("info")

  if (!type) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="h-5 w-5 rounded-full" style={{ backgroundColor: type.color }} />
            {type.name}
            <Badge variant={type.status === "active" ? "default" : "secondary"} className="ml-2">
              {type.status === "active" ? "启用" : "停用"}
            </Badge>
          </DialogTitle>
          <DialogDescription>{type.description}</DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="info">基本信息</TabsTrigger>
            <TabsTrigger value="courses">关联课程 ({type.courses})</TabsTrigger>
            <TabsTrigger value="stats">使用统计</TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">创建日期</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{type.createdAt}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">最后更新</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{type.updatedAt}</div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">关联课程数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{type.courses}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">显示顺序</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{type.displayOrder || 0}</div>
                </CardContent>
              </Card>
            </div>


          </TabsContent>

          <TabsContent value="courses" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>关联课程</CardTitle>
                <CardDescription>使用此类型的所有课程</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>课程名称</TableHead>
                      <TableHead>教练</TableHead>
                      <TableHead>排期</TableHead>
                      <TableHead>报名/容量</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {relatedCourses.map((course) => (
                      <TableRow key={course.id}>
                        <TableCell className="font-medium">{course.name}</TableCell>
                        <TableCell>{course.coach}</TableCell>
                        <TableCell>{course.schedule}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span>
                              {course.members}/{course.capacity}
                            </span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">总预约次数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{usageStats.totalBookings}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">平均出勤率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{usageStats.averageAttendance}%</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">关联课程</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{type.courses}</div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>热门时段</CardTitle>
                  <CardDescription>此类型课程最受欢迎的时间段</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {usageStats.popularTimeSlots.map((slot, index) => (
                      <div key={index} className="flex items-center">
                        <div className="w-1/3 flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>{slot.day}</span>
                        </div>
                        <div className="w-1/3">
                          <span>{slot.time}</span>
                        </div>
                        <div className="w-1/3 text-right">
                          <span className="font-medium">{slot.count}次</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>月度趋势</CardTitle>
                  <CardDescription>近6个月预约趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-end gap-2">
                    {usageStats.monthlyTrend.map((item, index) => (
                      <div key={index} className="flex flex-col items-center flex-1">
                        <div
                          className="w-full bg-primary rounded-t-sm"
                          style={{
                            height: `${(item.count / Math.max(...usageStats.monthlyTrend.map((i) => i.count))) * 150}px`,
                          }}
                        />
                        <div className="text-xs mt-2">{item.month}</div>
                        <div className="text-xs text-muted-foreground">{item.count}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

