"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Clock, Users, Loader2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// 定义数据类型
interface CourseData {
  id: number
  name: string
  coach: string
  schedule: string
  venue: string
  members: number
  capacity: number
  price: number
  level: string
  duration: number
}

interface StatsData {
  totalBookings: number
  averageAttendance: number
  popularTimeSlots: Array<{
    day: string
    time: string
    count: number
  }>
  monthlyTrend: Array<{
    month: string
    count: number
  }>
}

interface DetailData {
  courseType: any
  courses: CourseData[]
  stats: StatsData
}

interface CourseTypeDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  type?: any // 课程类型数据
}

export function CourseTypeDetailDialog({ open, onOpenChange, type }: CourseTypeDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("info")
  const [detailData, setDetailData] = useState<DetailData | null>(null)
  const [loading, setLoading] = useState(false)

  // 获取详细数据
  const fetchDetailData = async () => {
    if (!type?.id) return

    setLoading(true)
    try {
      const response = await fetch(`/api/course-types/${type.id}/stats`)
      const result = await response.json()

      if (result.code === 200) {
        setDetailData(result.data)
      } else {
        toast({
          title: "获取详情失败",
          description: result.msg || "无法获取课程类型详情",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("获取课程类型详情失败:", error)
      toast({
        title: "获取详情失败",
        description: "网络错误，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 当对话框打开且有类型数据时获取详情
  useEffect(() => {
    if (open && type?.id) {
      fetchDetailData()
    }
  }, [open, type?.id])

  // 当对话框关闭时重置数据
  useEffect(() => {
    if (!open) {
      setDetailData(null)
      setActiveTab("info")
    }
  }, [open])

  if (!type) return null

  // 使用详细数据或回退到传入的type数据
  const displayType = detailData?.courseType || type
  const courses = detailData?.courses || []
  const stats = detailData?.stats || {
    totalBookings: 0,
    averageAttendance: 0,
    popularTimeSlots: [],
    monthlyTrend: []
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="h-5 w-5 rounded-full" style={{ backgroundColor: displayType.color }} />
            {displayType.name}
            <Badge variant={displayType.status === "active" ? "default" : "secondary"} className="ml-2">
              {displayType.status === "active" ? "启用" : "停用"}
            </Badge>
            {loading && <Loader2 className="h-4 w-4 animate-spin" />}
          </DialogTitle>
          <DialogDescription>{displayType.description}</DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="info">基本信息</TabsTrigger>
            <TabsTrigger value="courses">关联课程 ({displayType.courseCount || courses.length})</TabsTrigger>
            <TabsTrigger value="stats">使用统计</TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">创建日期</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{displayType.createdAt || '未知'}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">最后更新</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{displayType.updatedAt || '未知'}</div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">关联课程数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{displayType.courseCount || courses.length}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">显示顺序</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{displayType.displayOrder || 0}</div>
                </CardContent>
              </Card>
            </div>


          </TabsContent>

          <TabsContent value="courses" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>关联课程</CardTitle>
                <CardDescription>使用此类型的所有课程</CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>加载中...</span>
                  </div>
                ) : courses.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>课程名称</TableHead>
                        <TableHead>教练</TableHead>
                        <TableHead>排期</TableHead>
                        <TableHead>场地</TableHead>
                        <TableHead>报名/容量</TableHead>
                        <TableHead>价格</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {courses.map((course) => (
                        <TableRow key={course.id}>
                          <TableCell className="font-medium">{course.name}</TableCell>
                          <TableCell>{course.coach}</TableCell>
                          <TableCell>{course.schedule}</TableCell>
                          <TableCell>{course.venue}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span>
                                {course.members}/{course.capacity}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>¥{course.price}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="flex items-center justify-center py-8 text-muted-foreground">
                    <span>暂无关联课程</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">总预约次数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{loading ? '-' : stats.totalBookings}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">平均出勤率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{loading ? '-' : `${stats.averageAttendance}%`}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">关联课程</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{displayType.courseCount || courses.length}</div>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>热门时段</CardTitle>
                  <CardDescription>此类型课程最受欢迎的时间段</CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex items-center justify-center py-4">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      <span>加载中...</span>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {stats.popularTimeSlots.map((slot, index) => (
                        <div key={index} className="flex items-center">
                          <div className="w-1/3 flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span>{slot.day}</span>
                          </div>
                          <div className="w-1/3">
                            <span>{slot.time}</span>
                          </div>
                          <div className="w-1/3 text-right">
                            <span className="font-medium">{slot.count}次</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>月度趋势</CardTitle>
                  <CardDescription>近6个月预约趋势</CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      <span>加载中...</span>
                    </div>
                  ) : (
                    <div className="h-[200px] flex items-end gap-2">
                      {stats.monthlyTrend.map((item, index) => (
                        <div key={index} className="flex flex-col items-center flex-1">
                          <div
                            className="w-full bg-primary rounded-t-sm"
                            style={{
                              height: `${stats.monthlyTrend.length > 0 ? (item.count / Math.max(...stats.monthlyTrend.map((i) => i.count))) * 150 : 0}px`,
                            }}
                          />
                          <div className="text-xs mt-2">{item.month}</div>
                          <div className="text-xs text-muted-foreground">{item.count}</div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

