"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/hooks/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Trash2,
  CreditCard,
  Ticket,
  Gift,
  Settings,
  Tag,
  Calendar
} from "lucide-react"
import { Separator } from "@/components/ui/separator"

// 分红权益类型
const dividendBenefitSchema = z.object({
  type: z.enum(["percentage", "fixed", "mixed"]), // 百分比、固定金额、混合
  percentageValue: z.string().optional(), // 百分比值，如 "5%"
  fixedValue: z.string().optional(), // 固定金额，如 "50元/人"
  calculationBase: z.enum(["referral", "revenue", "profit", "custom"]).optional(), // 计算基础
  frequency: z.enum(["monthly", "quarterly", "yearly", "custom"]).optional(), // 分红频率
  minimumRequirement: z.string().optional(), // 最低要求，如 "至少推荐3人"
  description: z.string().optional(), // 详细描述
});

// 会员卡权益类型
const membershipBenefitSchema = z.object({
  enabled: z.boolean().default(false), // 是否启用会员卡权益
  discountPercentage: z.number().min(0).max(100).optional(), // 会员卡折扣百分比
  freeCards: z.array(z.object({
    cardTypeId: z.string().optional(), // 会员卡类型ID
    cardTypeName: z.string().optional(), // 会员卡类型名称
    quantity: z.number().min(0).default(0), // 数量
    frequency: z.enum(["once", "monthly", "quarterly", "yearly"]).default("once"), // 发放频率
  })).default([]),
  description: z.string().optional(), // 详细描述
});

// 课程权益类型
const courseBenefitSchema = z.object({
  enabled: z.boolean().default(false), // 是否启用课程权益
  discountPercentage: z.number().min(0).max(100).optional(), // 课程折扣百分比
  freeCourses: z.array(z.object({
    courseTypeId: z.string().optional(), // 课程类型ID
    courseTypeName: z.string().optional(), // 课程类型名称
    quantity: z.number().min(0).default(0), // 数量
    frequency: z.enum(["once", "monthly", "quarterly", "yearly"]).default("once"), // 发放频率
  })).default([]),
  description: z.string().optional(), // 详细描述
});

// 优惠券权益类型
const couponBenefitSchema = z.object({
  enabled: z.boolean().default(false), // 是否启用优惠券权益
  coupons: z.array(z.object({
    couponId: z.string().optional(), // 优惠券ID
    couponName: z.string().optional(), // 优惠券名称
    couponValue: z.string().optional(), // 优惠券面值
    quantity: z.number().min(0).default(0), // 数量
    frequency: z.enum(["once", "monthly", "quarterly", "yearly"]).default("once"), // 发放频率
  })).default([]),
  description: z.string().optional(), // 详细描述
});

// 服务权益类型
const serviceBenefitSchema = z.object({
  enabled: z.boolean().default(false), // 是否启用服务权益
  services: z.array(z.object({
    serviceId: z.string().optional(), // 服务ID
    serviceName: z.string().optional(), // 服务名称
    description: z.string().optional(), // 服务描述
  })).default([]),
  description: z.string().optional(), // 详细描述
});

// 其他权益类型
const otherBenefitSchema = z.object({
  enabled: z.boolean().default(false), // 是否启用其他权益
  benefits: z.array(z.object({
    name: z.string().optional(), // 权益名称
    value: z.string().optional(), // 权益值
    frequency: z.enum(["once", "monthly", "quarterly", "yearly"]).default("once"), // 发放频率
    description: z.string().optional(), // 详细描述
  })).default([]),
  description: z.string().optional(), // 详细描述
});

// 主表单Schema
const formSchema = z.object({
  name: z.string().min(2, {
    message: "类型名称至少需要2个字符",
  }),
  description: z.string().min(5, {
    message: "描述至少需要5个字符",
  }),
  color: z.string().min(4, {
    message: "请选择颜色",
  }),

  // 分红权益 - 结构化
  dividendRules: z.string().min(5, {
    message: "分红规则至少需要5个字符",
  }),
  dividendBenefit: dividendBenefitSchema.default({
    type: "percentage",
    percentageValue: "",
    calculationBase: "referral",
    frequency: "monthly",
  }),

  // 会员卡权益 - 结构化
  membershipBenefit: membershipBenefitSchema.default({
    enabled: false,
    discountPercentage: 100,
    freeCards: [],
  }),

  // 课程权益 - 结构化
  courseBenefit: courseBenefitSchema.default({
    enabled: false,
    discountPercentage: 100,
    freeCourses: [],
  }),

  // 优惠券权益 - 结构化
  couponBenefit: couponBenefitSchema.default({
    enabled: false,
    coupons: [],
  }),

  // 服务权益 - 结构化
  serviceBenefit: serviceBenefitSchema.default({
    enabled: false,
    services: [],
  }),

  // 其他权益 - 结构化
  otherBenefit: otherBenefitSchema.default({
    enabled: false,
    benefits: [],
  }),

  // 保留原有字段用于兼容
  dividendRatio: z.string().optional(),
  dividendAmount: z.string().optional(),
  productBenefits: z.string().optional(),
  serviceBenefits: z.string().optional(),
  discountBenefits: z.string().optional(),
  couponBenefits: z.string().optional(),
  otherBenefits: z.string().optional(),
})

interface ShareholderTypeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  type: any
}

export function ShareholderTypeDialog({ open, onOpenChange, type }: ShareholderTypeDialogProps) {
  const isEditing = !!type

  // 模拟会员卡数据
  const membershipCards = [
    { id: "1", name: "年卡", description: "365天不限次数", price: "¥3,600" },
    { id: "2", name: "季卡", description: "90天不限次数", price: "¥1,200" },
    { id: "3", name: "月卡", description: "30天不限次数", price: "¥450" },
    { id: "4", name: "次卡20次", description: "20次课程，有效期180天", price: "¥1,600" },
  ]

  // 模拟课程数据
  const courseTypes = [
    { id: "1", name: "团课", description: "适合多人参与的常规团体课程" },
    { id: "2", name: "小班课", description: "小规模教学，教练能给予更多个性化指导" },
    { id: "3", name: "精品课", description: "高端定制课程，由资深教练授课" },
    { id: "4", name: "私教课", description: "一对一个性化教学" },
    { id: "5", name: "教培课", description: "针对瑜伽教练的专业培训课程" },
  ]

  // 模拟优惠券数据
  const coupons = [
    { id: "1", name: "新人专享8折券", value: "8折", description: "新会员专享优惠" },
    { id: "2", name: "满200减50", value: "满200减50", description: "消费满200元减50元" },
    { id: "3", name: "课程体验券", value: "免费体验1次", description: "新课程免费体验" },
    { id: "4", name: "生日专享券", value: "满100减50", description: "会员生日专享优惠" },
  ]

  // 模拟服务数据
  const services = [
    { id: "1", name: "专属更衣室", description: "使用专属更衣室服务" },
    { id: "2", name: "专属私教", description: "一对一专属私教指导" },
    { id: "3", name: "优先预约", description: "课程优先预约权" },
    { id: "4", name: "免费停车", description: "免费使用停车场服务" },
  ]

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: type?.name || "",
      description: type?.description || "",
      color: type?.color || "#4CAF50",
      dividendRules: type?.dividendRules || "",

      // 分红权益
      dividendBenefit: type?.dividendBenefit || {
        type: "percentage",
        percentageValue: type?.dividendRatio || "",
        fixedValue: type?.dividendAmount || "",
        calculationBase: "referral",
        frequency: "monthly",
      },

      // 会员卡权益
      membershipBenefit: type?.membershipBenefit || {
        enabled: false,
        discountPercentage: 100,
        freeCards: [],
      },

      // 课程权益
      courseBenefit: type?.courseBenefit || {
        enabled: false,
        discountPercentage: 100,
        freeCourses: [],
      },

      // 优惠券权益
      couponBenefit: type?.couponBenefit || {
        enabled: false,
        coupons: [],
      },

      // 服务权益
      serviceBenefit: type?.serviceBenefit || {
        enabled: false,
        services: [],
      },

      // 其他权益
      otherBenefit: type?.otherBenefit || {
        enabled: false,
        benefits: [],
      },

      // 兼容旧字段
      dividendRatio: type?.dividendRatio || "",
      dividendAmount: type?.dividendAmount || "",
      productBenefits: type?.productBenefits || "",
      serviceBenefits: type?.serviceBenefits || "",
      discountBenefits: type?.discountBenefits || "",
      couponBenefits: type?.couponBenefits || "",
      otherBenefits: type?.otherBenefits || "",
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values)
    toast({
      title: isEditing ? "更新成功" : "添加成功",
      description: isEditing
        ? `股东类型 ${values.name} 已成功更新`
        : `股东类型 ${values.name} 已成功添加`,
    })
    form.reset()
    onOpenChange(false)
  }

  const [activeTab, setActiveTab] = useState("basic")

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? "编辑股东类型" : "添加股东类型"}</DialogTitle>
          <DialogDescription>
            {isEditing ? "修改股东类型信息和权益" : "添加新的股东类型，设置分红规则和权益"}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-3">
                <TabsTrigger value="basic">基本信息</TabsTrigger>
                <TabsTrigger value="dividend">分红权益</TabsTrigger>
                <TabsTrigger value="benefits">其他权益</TabsTrigger>
              </TabsList>

              {/* 基本信息 */}
              <TabsContent value="basic" className="space-y-4 pt-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>类型名称</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入类型名称" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="color"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>标识颜色</FormLabel>
                        <div className="flex gap-2">
                          <FormControl>
                            <Input type="color" {...field} className="w-12 h-10 p-1" />
                          </FormControl>
                          <Input
                            value={field.value}
                            onChange={field.onChange}
                            className="flex-1"
                          />
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>类型描述</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="请输入类型描述"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              {/* 分红权益 */}
              <TabsContent value="dividend" className="space-y-4 pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>分红权益设置</CardTitle>
                    <CardDescription>设置股东的分红规则和计算方式</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <FormField
                      control={form.control}
                      name="dividendRules"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>分红规则描述</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="请输入分红规则描述"
                              className="resize-none"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            简要描述分红规则，将显示在股东类型详情中
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Separator />

                    <div className="space-y-4">
                      <h3 className="text-sm font-medium">分红计算方式</h3>

                      <FormField
                        control={form.control}
                        name="dividendBenefit.type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>分红类型</FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择分红类型" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="percentage">百分比分红</SelectItem>
                                <SelectItem value="fixed">固定金额分红</SelectItem>
                                <SelectItem value="mixed">混合分红</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              选择分红的计算方式
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        {(form.watch("dividendBenefit.type") === "percentage" ||
                          form.watch("dividendBenefit.type") === "mixed") && (
                          <FormField
                            control={form.control}
                            name="dividendBenefit.percentageValue"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>分红比例</FormLabel>
                                <FormControl>
                                  <Input placeholder="例如：5%、10%" {...field} />
                                </FormControl>
                                <FormDescription>
                                  按照百分比计算的分红
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}

                        {(form.watch("dividendBenefit.type") === "fixed" ||
                          form.watch("dividendBenefit.type") === "mixed") && (
                          <FormField
                            control={form.control}
                            name="dividendBenefit.fixedValue"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>固定金额</FormLabel>
                                <FormControl>
                                  <Input placeholder="例如：50元/人、100元/月" {...field} />
                                </FormControl>
                                <FormDescription>
                                  按照固定金额计算的分红
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}
                      </div>

                      <FormField
                        control={form.control}
                        name="dividendBenefit.calculationBase"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>计算基础</FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择计算基础" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="referral">推荐客户消费</SelectItem>
                                <SelectItem value="revenue">门店营业额</SelectItem>
                                <SelectItem value="profit">门店利润</SelectItem>
                                <SelectItem value="custom">自定义</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              分红计算的基础数据来源
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="dividendBenefit.frequency"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>分红频率</FormLabel>
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择分红频率" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="monthly">每月</SelectItem>
                                <SelectItem value="quarterly">每季度</SelectItem>
                                <SelectItem value="yearly">每年</SelectItem>
                                <SelectItem value="custom">自定义</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              分红发放的时间频率
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="dividendBenefit.minimumRequirement"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>最低要求</FormLabel>
                            <FormControl>
                              <Input placeholder="例如：至少推荐3人、最低消费1000元" {...field} />
                            </FormControl>
                            <FormDescription>
                              获得分红的最低条件要求
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* 保留旧字段用于兼容 */}
                    <div className="hidden">
                      <FormField
                        control={form.control}
                        name="dividendRatio"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="dividendAmount"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 其他权益 - 选项卡 */}
              <TabsContent value="benefits" className="space-y-4 pt-4">
                {/* 会员卡权益 */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>会员卡权益</CardTitle>
                        <CardDescription>设置股东可享受的会员卡折扣或免费会员卡</CardDescription>
                      </div>
                      <FormField
                        control={form.control}
                        name="membershipBenefit.enabled"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal">
                              {field.value ? "已启用" : "未启用"}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardHeader>

                  {form.watch("membershipBenefit.enabled") && (
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="membershipBenefit.discountPercentage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>会员卡折扣</FormLabel>
                            <div className="flex items-center gap-2">
                              <FormControl>
                                <Input
                                  type="number"
                                  min={0}
                                  max={100}
                                  placeholder="例如：80表示8折"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <span>%</span>
                            </div>
                            <FormDescription>
                              购买任意会员卡时可享受的折扣，100表示不打折
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium">免费会员卡</h3>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const currentCards = form.getValues("membershipBenefit.freeCards") || [];
                              form.setValue("membershipBenefit.freeCards", [
                                ...currentCards,
                                {
                                  cardTypeId: "",
                                  cardTypeName: "",
                                  quantity: 1,
                                  frequency: "once"
                                }
                              ]);
                            }}
                          >
                            <Plus className="mr-1 h-4 w-4" />
                            添加会员卡
                          </Button>
                        </div>

                        {form.watch("membershipBenefit.freeCards")?.map((_, index) => (
                          <Card key={index} className="p-4">
                            <div className="flex justify-between items-start mb-4">
                              <h4 className="text-sm font-medium">免费会员卡 #{index + 1}</h4>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const currentCards = form.getValues("membershipBenefit.freeCards");
                                  form.setValue(
                                    "membershipBenefit.freeCards",
                                    currentCards.filter((_, i) => i !== index)
                                  );
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name={`membershipBenefit.freeCards.${index}.cardTypeId`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>会员卡类型</FormLabel>
                                    <Select
                                      value={field.value}
                                      onValueChange={(value) => {
                                        field.onChange(value);
                                        // 同时设置会员卡名称
                                        const selectedCard = membershipCards.find(card => card.id === value);
                                        if (selectedCard) {
                                          form.setValue(
                                            `membershipBenefit.freeCards.${index}.cardTypeName`,
                                            selectedCard.name
                                          );
                                        }
                                      }}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="选择会员卡" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {membershipCards.map((card) => (
                                          <SelectItem key={card.id} value={card.id}>
                                            {card.name} - {card.description}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`membershipBenefit.freeCards.${index}.quantity`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>数量</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        min={1}
                                        placeholder="数量"
                                        {...field}
                                        onChange={(e) => field.onChange(Number(e.target.value))}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`membershipBenefit.freeCards.${index}.frequency`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>发放频率</FormLabel>
                                    <Select
                                      value={field.value}
                                      onValueChange={field.onChange}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="选择发放频率" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="once">一次性</SelectItem>
                                        <SelectItem value="monthly">每月</SelectItem>
                                        <SelectItem value="quarterly">每季度</SelectItem>
                                        <SelectItem value="yearly">每年</SelectItem>
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </Card>
                        ))}

                        {(!form.watch("membershipBenefit.freeCards") ||
                          form.watch("membershipBenefit.freeCards").length === 0) && (
                          <div className="flex items-center justify-center h-20 border border-dashed rounded-md">
                            <p className="text-sm text-muted-foreground">暂无免费会员卡，点击上方按钮添加</p>
                          </div>
                        )}
                      </div>

                      <FormField
                        control={form.control}
                        name="membershipBenefit.description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>权益说明</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="请输入会员卡权益的补充说明"
                                className="resize-none"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  )}
                </Card>

                {/* 课程权益 */}
                <Card className="mt-6">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>课程权益</CardTitle>
                        <CardDescription>设置股东可享受的课程折扣或免费课程</CardDescription>
                      </div>
                      <FormField
                        control={form.control}
                        name="courseBenefit.enabled"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal">
                              {field.value ? "已启用" : "未启用"}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardHeader>

                  {form.watch("courseBenefit.enabled") && (
                    <CardContent className="space-y-4">
                      <FormField
                        control={form.control}
                        name="courseBenefit.discountPercentage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>课程折扣</FormLabel>
                            <div className="flex items-center gap-2">
                              <FormControl>
                                <Input
                                  type="number"
                                  min={0}
                                  max={100}
                                  placeholder="例如：80表示8折"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <span>%</span>
                            </div>
                            <FormDescription>
                              购买任意课程时可享受的折扣，100表示不打折
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium">免费课程</h3>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const currentCourses = form.getValues("courseBenefit.freeCourses") || [];
                              form.setValue("courseBenefit.freeCourses", [
                                ...currentCourses,
                                {
                                  courseTypeId: "",
                                  courseTypeName: "",
                                  quantity: 1,
                                  frequency: "once"
                                }
                              ]);
                            }}
                          >
                            <Plus className="mr-1 h-4 w-4" />
                            添加课程
                          </Button>
                        </div>

                        {form.watch("courseBenefit.freeCourses")?.map((_, index) => (
                          <Card key={index} className="p-4">
                            <div className="flex justify-between items-start mb-4">
                              <h4 className="text-sm font-medium">免费课程 #{index + 1}</h4>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const currentCourses = form.getValues("courseBenefit.freeCourses");
                                  form.setValue(
                                    "courseBenefit.freeCourses",
                                    currentCourses.filter((_, i) => i !== index)
                                  );
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name={`courseBenefit.freeCourses.${index}.courseTypeId`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>课程类型</FormLabel>
                                    <Select
                                      value={field.value}
                                      onValueChange={(value) => {
                                        field.onChange(value);
                                        // 同时设置课程名称
                                        const selectedCourse = courseTypes.find(course => course.id === value);
                                        if (selectedCourse) {
                                          form.setValue(
                                            `courseBenefit.freeCourses.${index}.courseTypeName`,
                                            selectedCourse.name
                                          );
                                        }
                                      }}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="选择课程" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {courseTypes.map((course) => (
                                          <SelectItem key={course.id} value={course.id}>
                                            {course.name} - {course.description}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`courseBenefit.freeCourses.${index}.quantity`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>数量</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        min={1}
                                        placeholder="数量"
                                        {...field}
                                        onChange={(e) => field.onChange(Number(e.target.value))}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`courseBenefit.freeCourses.${index}.frequency`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>发放频率</FormLabel>
                                    <Select
                                      value={field.value}
                                      onValueChange={field.onChange}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="选择发放频率" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="once">一次性</SelectItem>
                                        <SelectItem value="monthly">每月</SelectItem>
                                        <SelectItem value="quarterly">每季度</SelectItem>
                                        <SelectItem value="yearly">每年</SelectItem>
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </Card>
                        ))}

                        {(!form.watch("courseBenefit.freeCourses") ||
                          form.watch("courseBenefit.freeCourses").length === 0) && (
                          <div className="flex items-center justify-center h-20 border border-dashed rounded-md">
                            <p className="text-sm text-muted-foreground">暂无免费课程，点击上方按钮添加</p>
                          </div>
                        )}
                      </div>

                      <FormField
                        control={form.control}
                        name="courseBenefit.description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>权益说明</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="请输入课程权益的补充说明"
                                className="resize-none"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  )}
                </Card>

                {/* 优惠券权益 */}
                <Card className="mt-6">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>优惠券权益</CardTitle>
                        <CardDescription>设置股东可获得的优惠券</CardDescription>
                      </div>
                      <FormField
                        control={form.control}
                        name="couponBenefit.enabled"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal">
                              {field.value ? "已启用" : "未启用"}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardHeader>

                  {form.watch("couponBenefit.enabled") && (
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium">优惠券列表</h3>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const currentCoupons = form.getValues("couponBenefit.coupons") || [];
                              form.setValue("couponBenefit.coupons", [
                                ...currentCoupons,
                                {
                                  couponId: "",
                                  couponName: "",
                                  couponValue: "",
                                  quantity: 1,
                                  frequency: "once"
                                }
                              ]);
                            }}
                          >
                            <Plus className="mr-1 h-4 w-4" />
                            添加优惠券
                          </Button>
                        </div>

                        {form.watch("couponBenefit.coupons")?.map((_, index) => (
                          <Card key={index} className="p-4">
                            <div className="flex justify-between items-start mb-4">
                              <h4 className="text-sm font-medium">优惠券 #{index + 1}</h4>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const currentCoupons = form.getValues("couponBenefit.coupons");
                                  form.setValue(
                                    "couponBenefit.coupons",
                                    currentCoupons.filter((_, i) => i !== index)
                                  );
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name={`couponBenefit.coupons.${index}.couponId`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>优惠券类型</FormLabel>
                                    <Select
                                      value={field.value}
                                      onValueChange={(value) => {
                                        field.onChange(value);
                                        // 同时设置优惠券名称和面值
                                        const selectedCoupon = coupons.find(coupon => coupon.id === value);
                                        if (selectedCoupon) {
                                          form.setValue(
                                            `couponBenefit.coupons.${index}.couponName`,
                                            selectedCoupon.name
                                          );
                                          form.setValue(
                                            `couponBenefit.coupons.${index}.couponValue`,
                                            selectedCoupon.value
                                          );
                                        }
                                      }}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="选择优惠券" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {coupons.map((coupon) => (
                                          <SelectItem key={coupon.id} value={coupon.id}>
                                            {coupon.name} - {coupon.value}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`couponBenefit.coupons.${index}.quantity`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>数量</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        min={1}
                                        placeholder="数量"
                                        {...field}
                                        onChange={(e) => field.onChange(Number(e.target.value))}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`couponBenefit.coupons.${index}.frequency`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>发放频率</FormLabel>
                                    <Select
                                      value={field.value}
                                      onValueChange={field.onChange}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="选择发放频率" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="once">一次性</SelectItem>
                                        <SelectItem value="monthly">每月</SelectItem>
                                        <SelectItem value="quarterly">每季度</SelectItem>
                                        <SelectItem value="yearly">每年</SelectItem>
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </Card>
                        ))}

                        {(!form.watch("couponBenefit.coupons") ||
                          form.watch("couponBenefit.coupons").length === 0) && (
                          <div className="flex items-center justify-center h-20 border border-dashed rounded-md">
                            <p className="text-sm text-muted-foreground">暂无优惠券，点击上方按钮添加</p>
                          </div>
                        )}
                      </div>

                      <FormField
                        control={form.control}
                        name="couponBenefit.description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>权益说明</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="请输入优惠券权益的补充说明"
                                className="resize-none"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  )}
                </Card>

                {/* 服务权益 */}
                <Card className="mt-6">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>服务权益</CardTitle>
                        <CardDescription>设置股东可享受的专属服务</CardDescription>
                      </div>
                      <FormField
                        control={form.control}
                        name="serviceBenefit.enabled"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal">
                              {field.value ? "已启用" : "未启用"}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardHeader>

                  {form.watch("serviceBenefit.enabled") && (
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium">服务列表</h3>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const currentServices = form.getValues("serviceBenefit.services") || [];
                              form.setValue("serviceBenefit.services", [
                                ...currentServices,
                                {
                                  serviceId: "",
                                  serviceName: "",
                                  description: ""
                                }
                              ]);
                            }}
                          >
                            <Plus className="mr-1 h-4 w-4" />
                            添加服务
                          </Button>
                        </div>

                        {form.watch("serviceBenefit.services")?.map((_, index) => (
                          <Card key={index} className="p-4">
                            <div className="flex justify-between items-start mb-4">
                              <h4 className="text-sm font-medium">专属服务 #{index + 1}</h4>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const currentServices = form.getValues("serviceBenefit.services");
                                  form.setValue(
                                    "serviceBenefit.services",
                                    currentServices.filter((_, i) => i !== index)
                                  );
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name={`serviceBenefit.services.${index}.serviceId`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>服务类型</FormLabel>
                                    <Select
                                      value={field.value}
                                      onValueChange={(value) => {
                                        field.onChange(value);
                                        // 同时设置服务名称和描述
                                        const selectedService = services.find(service => service.id === value);
                                        if (selectedService) {
                                          form.setValue(
                                            `serviceBenefit.services.${index}.serviceName`,
                                            selectedService.name
                                          );
                                          form.setValue(
                                            `serviceBenefit.services.${index}.description`,
                                            selectedService.description
                                          );
                                        }
                                      }}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="选择服务" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {services.map((service) => (
                                          <SelectItem key={service.id} value={service.id}>
                                            {service.name} - {service.description}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`serviceBenefit.services.${index}.description`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>服务说明</FormLabel>
                                    <FormControl>
                                      <Input
                                        placeholder="服务说明"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </Card>
                        ))}

                        {(!form.watch("serviceBenefit.services") ||
                          form.watch("serviceBenefit.services").length === 0) && (
                          <div className="flex items-center justify-center h-20 border border-dashed rounded-md">
                            <p className="text-sm text-muted-foreground">暂无专属服务，点击上方按钮添加</p>
                          </div>
                        )}
                      </div>

                      <FormField
                        control={form.control}
                        name="serviceBenefit.description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>权益说明</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="请输入服务权益的补充说明"
                                className="resize-none"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  )}
                </Card>

                {/* 其他权益 */}
                <Card className="mt-6">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>其他权益</CardTitle>
                        <CardDescription>设置股东可享受的其他特殊权益</CardDescription>
                      </div>
                      <FormField
                        control={form.control}
                        name="otherBenefit.enabled"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2 space-y-0">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel className="text-sm font-normal">
                              {field.value ? "已启用" : "未启用"}
                            </FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardHeader>

                  {form.watch("otherBenefit.enabled") && (
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium">其他权益列表</h3>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              const currentBenefits = form.getValues("otherBenefit.benefits") || [];
                              form.setValue("otherBenefit.benefits", [
                                ...currentBenefits,
                                {
                                  name: "",
                                  value: "",
                                  frequency: "once",
                                  description: ""
                                }
                              ]);
                            }}
                          >
                            <Plus className="mr-1 h-4 w-4" />
                            添加权益
                          </Button>
                        </div>

                        {form.watch("otherBenefit.benefits")?.map((_, index) => (
                          <Card key={index} className="p-4">
                            <div className="flex justify-between items-start mb-4">
                              <h4 className="text-sm font-medium">特殊权益 #{index + 1}</h4>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  const currentBenefits = form.getValues("otherBenefit.benefits");
                                  form.setValue(
                                    "otherBenefit.benefits",
                                    currentBenefits.filter((_, i) => i !== index)
                                  );
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name={`otherBenefit.benefits.${index}.name`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>权益名称</FormLabel>
                                    <FormControl>
                                      <Input
                                        placeholder="例如：请客卡、生日礼物"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`otherBenefit.benefits.${index}.value`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>权益内容</FormLabel>
                                    <FormControl>
                                      <Input
                                        placeholder="例如：每季度1张、价值200元"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`otherBenefit.benefits.${index}.frequency`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>发放频率</FormLabel>
                                    <Select
                                      value={field.value}
                                      onValueChange={field.onChange}
                                    >
                                      <FormControl>
                                        <SelectTrigger>
                                          <SelectValue placeholder="选择发放频率" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        <SelectItem value="once">一次性</SelectItem>
                                        <SelectItem value="monthly">每月</SelectItem>
                                        <SelectItem value="quarterly">每季度</SelectItem>
                                        <SelectItem value="yearly">每年</SelectItem>
                                      </SelectContent>
                                    </Select>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name={`otherBenefit.benefits.${index}.description`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>权益说明</FormLabel>
                                    <FormControl>
                                      <Input
                                        placeholder="权益的补充说明"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </Card>
                        ))}

                        {(!form.watch("otherBenefit.benefits") ||
                          form.watch("otherBenefit.benefits").length === 0) && (
                          <div className="flex items-center justify-center h-20 border border-dashed rounded-md">
                            <p className="text-sm text-muted-foreground">暂无其他权益，点击上方按钮添加</p>
                          </div>
                        )}
                      </div>

                      <FormField
                        control={form.control}
                        name="otherBenefit.description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>权益说明</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="请输入其他权益的补充说明"
                                className="resize-none"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </CardContent>
                  )}
                </Card>

                {/* 保留原有字段用于兼容 */}
                <div className="hidden">
                  <FormField
                    control={form.control}
                    name="productBenefits"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="serviceBenefits"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="discountBenefits"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="couponBenefits"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="otherBenefits"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button type="submit">{isEditing ? "更新" : "保存"}</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
