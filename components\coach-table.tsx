"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoreHorizontal, Pencil, Calendar, BarChart, Star, UserRoundX, UserRoundCheck, Clock } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useState } from "react"
import { CoachDetailDialog } from "@/components/coaches/coach-detail-dialog"
import { CoachLeaveDialog } from "@/components/coaches/coach-leave-dialog"
import { useToast } from "@/components/ui/use-toast"
import { format } from "date-fns"

const coaches = [
  {
    id: "C001",
    name: "张教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "138****1234",
    specialty: ["基础瑜伽", "高级瑜伽"],
    courses: 12,
    students: 45,
    rating: 4.8,
    status: "active",
    joinDate: "2023-05-15",
    email: "<EMAIL>",
  },
  {
    id: "C002",
    name: "李教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "139****5678",
    specialty: ["高级瑜伽", "阴瑜伽"],
    courses: 8,
    students: 32,
    rating: 4.9,
    status: "active",
    joinDate: "2023-07-22",
    email: "<EMAIL>",
  },
  {
    id: "C003",
    name: "王教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "137****9012",
    specialty: ["阴瑜伽", "孕产瑜伽"],
    courses: 10,
    students: 38,
    rating: 4.7,
    status: "leave",
    joinDate: "2023-03-10",
    email: "<EMAIL>",
  },
  {
    id: "C004",
    name: "赵教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "136****3456",
    specialty: ["孕产瑜伽"],
    courses: 6,
    students: 24,
    rating: 4.6,
    status: "active",
    joinDate: "2023-09-05",
    email: "<EMAIL>",
  },
  {
    id: "C005",
    name: "刘教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "135****7890",
    specialty: ["空中瑜伽", "基础瑜伽"],
    courses: 9,
    students: 36,
    rating: 4.5,
    status: "active",
    joinDate: "2023-11-18",
    email: "<EMAIL>",
  },
  {
    id: "C006",
    name: "陈教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "134****2345",
    specialty: ["基础瑜伽", "阴瑜伽"],
    courses: 7,
    students: 28,
    rating: 4.7,
    status: "resigned",
    joinDate: "2023-01-20",
    email: "<EMAIL>",
  },
]

interface CoachTableProps {
  statusFilter?: string
}

export function CoachTable({ statusFilter }: CoachTableProps) {
  const { toast } = useToast()
  const [selectedCoach, setSelectedCoach] = useState<any>(null)
  const [showDetail, setShowDetail] = useState(false)
  const [showLeaveDialog, setShowLeaveDialog] = useState(false)
  const [coachesData, setCoachesData] = useState(coaches)

  const filteredCoaches = statusFilter
    ? coachesData.filter((coach) => coach.status === statusFilter)
    : coachesData

  const handleRowClick = (coach: any) => {
    setSelectedCoach(coach)
    setShowDetail(true)
  }

  // 处理打开请假对话框
  const handleOpenLeaveDialog = (coach: any) => {
    setSelectedCoach(coach)
    setShowLeaveDialog(true)
  }

  // 处理教练请假提交
  const handleLeaveSubmit = async (coachId: string, startDate: Date, endDate: Date, leaveType: string, reason: string) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新教练状态
    const updatedCoaches = coachesData.map(coach => {
      if (coach.id === coachId) {
        return { ...coach, status: "leave" }
      }
      return coach
    })

    setCoachesData(updatedCoaches)

    toast({
      title: "请假申请已提交",
      description: `教练请假申请已成功提交，请假期间：${format(startDate, 'yyyy-MM-dd')} 至 ${format(endDate, 'yyyy-MM-dd')}`,
    })
  }

  // 处理恢复在职
  const handleResumeWork = async (coach: any) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 更新教练状态
    const updatedCoaches = coachesData.map(c => {
      if (c.id === coach.id) {
        return { ...c, status: "active" }
      }
      return c
    })

    setCoachesData(updatedCoaches)

    toast({
      title: "状态已更新",
      description: `${coach.name}已恢复在职状态`,
    })
  }

  // 处理设为离职
  const handleSetResigned = async (coach: any) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 更新教练状态
    const updatedCoaches = coachesData.map(c => {
      if (c.id === coach.id) {
        return { ...c, status: "resigned" }
      }
      return c
    })

    setCoachesData(updatedCoaches)

    toast({
      title: "状态已更新",
      description: `${coach.name}已设为离职状态`,
      variant: "destructive",
    })
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>教练ID</TableHead>
              <TableHead>教练姓名</TableHead>
              <TableHead>联系方式</TableHead>
              <TableHead>专长</TableHead>
              <TableHead>课程数</TableHead>
              <TableHead>学员数</TableHead>
              <TableHead>评分</TableHead>
              <TableHead>状态</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCoaches.map((coach) => (
              <TableRow key={coach.id} className="cursor-pointer" onClick={() => handleRowClick(coach)}>
                <TableCell className="font-medium">{coach.id}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={coach.avatar} alt={coach.name} />
                      <AvatarFallback>{coach.name[0]}</AvatarFallback>
                    </Avatar>
                    <span>{coach.name}</span>
                  </div>
                </TableCell>
                <TableCell>{coach.phone}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {coach.specialty.map((spec) => (
                      <Badge key={spec} variant="outline" className="text-xs">
                        {spec}
                      </Badge>
                    ))}
                  </div>
                </TableCell>
                <TableCell>{coach.courses}</TableCell>
                <TableCell>{coach.students}</TableCell>
                <TableCell>
                  <div className="flex items-center">
                    {coach.rating}
                    <Star className="ml-1 h-3 w-3 fill-yellow-400 text-yellow-400" />
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={coach.status === "active" ? "default" : coach.status === "leave" ? "secondary" : "outline"}
                  >
                    {coach.status === "active" ? "在职" : coach.status === "leave" ? "请假中" : "已离职"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>操作</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleRowClick(coach)}>
                        <Pencil className="mr-2 h-4 w-4" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Calendar className="mr-2 h-4 w-4" />
                        排课管理
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <BarChart className="mr-2 h-4 w-4" />
                        业绩统计
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Star className="mr-2 h-4 w-4" />
                        查看评价
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {coach.status === "active" ? (
                        <DropdownMenuItem onClick={() => handleOpenLeaveDialog(coach)}>
                          <Clock className="mr-2 h-4 w-4" />
                          设置请假
                        </DropdownMenuItem>
                      ) : coach.status === "leave" ? (
                        <DropdownMenuItem onClick={() => handleResumeWork(coach)}>
                          <UserRoundCheck className="mr-2 h-4 w-4" />
                          恢复在职
                        </DropdownMenuItem>
                      ) : null}
                      {coach.status !== "resigned" && (
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={() => handleSetResigned(coach)}
                        >
                          <UserRoundX className="mr-2 h-4 w-4" />
                          设为离职
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <CoachDetailDialog open={showDetail} onOpenChange={setShowDetail} coach={selectedCoach} />

      {/* 教练请假对话框 */}
      <CoachLeaveDialog
        open={showLeaveDialog}
        onOpenChange={setShowLeaveDialog}
        coach={selectedCoach}
        onLeaveSubmit={handleLeaveSubmit}
      />
    </>
  )
}

