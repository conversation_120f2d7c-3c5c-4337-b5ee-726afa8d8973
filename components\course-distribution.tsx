"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON>, Tooltip } from "recharts"

const data = [
  { name: "基础瑜伽", value: 40, color: "#4285F4" },
  { name: "高级瑜伽", value: 25, color: "#34A853" },
  { name: "阴瑜伽", value: 15, color: "#FBBC05" },
  { name: "孕产瑜伽", value: 10, color: "#EA4335" },
  { name: "空中瑜伽", value: 10, color: "#FF6D91" },
]

export function CourseDistribution() {
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>课程类型分布</CardTitle>
        <CardDescription>各类型课程预约占比</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={data} cx="50%" cy="50%" labelLine={false} outerRadius={80} fill="#8884d8" dataKey="value">
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
              <Legend layout="vertical" verticalAlign="middle" align="right" />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}

