"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Check, Save, TestTube } from "lucide-react"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

export default function VerificationSettingsPage() {
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false)
  const [isSaved, setIsSaved] = useState(false)

  const handleSave = () => {
    setIsSaved(true)
    setTimeout(() => setIsSaved(false), 3000)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">平台设置</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setIsTestDialogOpen(true)}>
            <TestTube className="mr-2 h-4 w-4" />
            测试连接
          </Button>
          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            保存设置
          </Button>
        </div>
      </div>

      {isSaved && (
        <Alert className="bg-green-50 text-green-600 border-green-500">
          <Check className="h-4 w-4" />
          <AlertTitle>保存成功</AlertTitle>
          <AlertDescription>
            平台设置已成功保存
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="meituan" className="space-y-4">
        <TabsList>
          <TabsTrigger value="meituan">美团设置</TabsTrigger>
          <TabsTrigger value="douyin">抖音设置</TabsTrigger>
          <TabsTrigger value="general">通用设置</TabsTrigger>
          <TabsTrigger value="notification">通知设置</TabsTrigger>
          <TabsTrigger value="permission">权限设置</TabsTrigger>
        </TabsList>

        <TabsContent value="meituan" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>美团平台设置</CardTitle>
              <CardDescription>配置美团平台接入参数</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="meituan-appid">AppID</Label>
                <Input id="meituan-appid" placeholder="输入美团AppID" />
                <p className="text-sm text-muted-foreground">在美团开放平台获取的应用ID</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="meituan-appsecret">AppSecret</Label>
                <Input id="meituan-appsecret" type="password" placeholder="输入美团AppSecret" />
                <p className="text-sm text-muted-foreground">在美团开放平台获取的应用密钥</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="meituan-storeid">门店ID</Label>
                <Input id="meituan-storeid" placeholder="输入美团门店ID" />
                <p className="text-sm text-muted-foreground">美团平台的门店ID，可在美团商家后台查看</p>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label htmlFor="meituan-callback">回调地址</Label>
                <Input id="meituan-callback" value="https://youryogastudio.com/api/meituan/callback" readOnly />
                <p className="text-sm text-muted-foreground">请在美团开放平台配置此回调地址</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="meituan-webhook">Webhook地址</Label>
                <Input id="meituan-webhook" value="https://youryogastudio.com/api/meituan/webhook" readOnly />
                <p className="text-sm text-muted-foreground">��在美团开放平台配置此Webhook地址，用于接收订单和核销通知</p>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label>核销规则</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="meituan-auto-verify">自动核销</Label>
                      <p className="text-sm text-muted-foreground">扫码后自动完成核销，无需确认</p>
                    </div>
                    <Switch id="meituan-auto-verify" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="meituan-check-expiry">检查有效期</Label>
                      <p className="text-sm text-muted-foreground">核销时检查券码是否在有效期内</p>
                    </div>
                    <Switch id="meituan-check-expiry" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="meituan-check-product">检查商品匹配</Label>
                      <p className="text-sm text-muted-foreground">核销时检查券码对应的商品是否匹配</p>
                    </div>
                    <Switch id="meituan-check-product" defaultChecked />
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="meituan-enabled">启用美团核销</Label>
                  <p className="text-sm text-muted-foreground">开启后可接收和处理美团订单</p>
                </div>
                <Switch id="meituan-enabled" defaultChecked />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>美团商品映射</CardTitle>
              <CardDescription>配置美团商品与系统商品的映射关系</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>商品映射方式</Label>
                <RadioGroup defaultValue="auto">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="auto" id="mapping-auto" />
                    <Label htmlFor="mapping-auto">自动映射（根据商品名称）</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="manual" id="mapping-manual" />
                    <Label htmlFor="mapping-manual">手动映射（指定对应关系）</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <Label>默认课程类型</Label>
                <Select defaultValue="basic">
                  <SelectTrigger>
                    <SelectValue placeholder="选择默认课程类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="basic">基础瑜伽</SelectItem>
                    <SelectItem value="advanced">高级瑜伽</SelectItem>
                    <SelectItem value="yin">阴瑜伽</SelectItem>
                    <SelectItem value="aerial">空中瑜伽</SelectItem>
                    <SelectItem value="prenatal">孕产瑜伽</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">当无法匹配商品时使用的默认课程类型</p>
              </div>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>商品映射说明</AlertTitle>
                <AlertDescription>
                  商品映射用于将美团平台的商品与系统内的课程进行关联，以便正确处理核销和统计数据。
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="douyin" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>抖音平台设置</CardTitle>
              <CardDescription>配置抖音平台接入参数</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="douyin-appid">AppID</Label>
                <Input id="douyin-appid" placeholder="输入抖音AppID" />
                <p className="text-sm text-muted-foreground">在抖音开放平台获取的应用ID</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="douyin-appsecret">AppSecret</Label>
                <Input id="douyin-appsecret" type="password" placeholder="输入抖音AppSecret" />
                <p className="text-sm text-muted-foreground">在抖音开放平台获取的应用密钥</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="douyin-shopid">店铺ID</Label>
                <Input id="douyin-shopid" placeholder="输入抖音店铺ID" />
                <p className="text-sm text-muted-foreground">抖音平台的店铺ID，可在抖音商家后台查看</p>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label htmlFor="douyin-callback">回调地址</Label>
                <Input id="douyin-callback" value="https://youryogastudio.com/api/douyin/callback" readOnly />
                <p className="text-sm text-muted-foreground">请在抖音开放平台配置此回调地址</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="douyin-webhook">Webhook地址</Label>
                <Input id="douyin-webhook" value="https://youryogastudio.com/api/douyin/webhook" readOnly />
                <p className="text-sm text-muted-foreground">请在抖音开放平台配置此Webhook地址，用于接收订单和核销通知</p>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label>核销规则</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="douyin-auto-verify">自动核销</Label>
                      <p className="text-sm text-muted-foreground">扫码后自动完成核销，无需确认</p>
                    </div>
                    <Switch id="douyin-auto-verify" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="douyin-check-expiry">检查有效期</Label>
                      <p className="text-sm text-muted-foreground">核销时检查券码是否在有效期内</p>
                    </div>
                    <Switch id="douyin-check-expiry" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="douyin-check-product">检查商品匹配</Label>
                      <p className="text-sm text-muted-foreground">核销时检查券码对应的商品是否匹配</p>
                    </div>
                    <Switch id="douyin-check-product" defaultChecked />
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="douyin-enabled">启用抖音核销</Label>
                  <p className="text-sm text-muted-foreground">开启后可接收和处理抖音订单</p>
                </div>
                <Switch id="douyin-enabled" defaultChecked />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>抖音商品映射</CardTitle>
              <CardDescription>配置抖音商品与系统商品的映射关系</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8">
                <p className="text-muted-foreground">抖音商品映射设置与美团类似，可配置自动映射或手动映射</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>通用设置</CardTitle>
              <CardDescription>配置核销的通用功能</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="print-receipt">小票打印</Label>
                  <p className="text-sm text-muted-foreground">核销成功后自动打印小票</p>
                </div>
                <Switch id="print-receipt" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sound-notification">声音提醒</Label>
                  <p className="text-sm text-muted-foreground">核销成功后播放提示音</p>
                </div>
                <Switch id="sound-notification" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-create-member">自动创建会员</Label>
                  <p className="text-sm text-muted-foreground">首次核销时自动创建会员账号</p>
                </div>
                <Switch id="auto-create-member" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="continuous-scan">连续扫码</Label>
                  <p className="text-sm text-muted-foreground">启用连续扫码模式，无需手动点击开始扫码</p>
                </div>
                <Switch id="continuous-scan" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="record-verification">记录核销历史</Label>
                  <p className="text-sm text-muted-foreground">保存所有核销记录，包括成功和失败的记录</p>
                </div>
                <Switch id="record-verification" defaultChecked />
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label>核销异常处理</Label>
                <Select defaultValue="manual">
                  <SelectTrigger>
                    <SelectValue placeholder="选择异常处理方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="manual">手动处理（需人工确认）</SelectItem>
                    <SelectItem value="auto-retry">自动重试（最多3次）</SelectItem>
                    <SelectItem value="ignore">忽略异常（仅记录日志）</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">当核销遇到异常时的处理方式</p>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label htmlFor="receipt-header">小票抬头</Label>
                <Input id="receipt-header" defaultValue="静心瑜伽馆" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="receipt-footer">小票页脚</Label>
                <Input id="receipt-footer" defaultValue="感谢您的光临，欢迎再次惠顾！" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="receipt-contact">联系方式</Label>
                <Input id="receipt-contact" defaultValue="电话：010-12345678" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="receipt-template">小票模板</Label>
                <Textarea
                  id="receipt-template"
                  rows={6}
                  defaultValue={`{header}
--------------------------
券码: {code}
商品: {product}
价格: {price}
核销时间: {time}
操作员: {operator}
--------------------------
{footer}
{contact}`}
                />
                <p className="text-sm text-muted-foreground">使用花括号包裹的变量将在打印时被替换为实际值</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notification" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>通知设置</CardTitle>
              <CardDescription>配置核销相关的通知设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>通知接收人</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-admin">管理员</Label>
                      <p className="text-sm text-muted-foreground">向管理员发送核销通知</p>
                    </div>
                    <Switch id="notify-admin" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-front-desk">前台</Label>
                      <p className="text-sm text-muted-foreground">向前台发送核销通知</p>
                    </div>
                    <Switch id="notify-front-desk" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-coach">教练</Label>
                      <p className="text-sm text-muted-foreground">向相关教练发送核销通知</p>
                    </div>
                    <Switch id="notify-coach" />
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label>通知方式</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-system">系统通知</Label>
                      <p className="text-sm text-muted-foreground">在系统内发送通知</p>
                    </div>
                    <Switch id="notify-system" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-email">邮件通知</Label>
                      <p className="text-sm text-muted-foreground">发送邮件通知</p>
                    </div>
                    <Switch id="notify-email" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-sms">短信通知</Label>
                      <p className="text-sm text-muted-foreground">发送短信通知</p>
                    </div>
                    <Switch id="notify-sms" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-wechat">微信通知</Label>
                      <p className="text-sm text-muted-foreground">发送微信通知</p>
                    </div>
                    <Switch id="notify-wechat" defaultChecked />
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label>通知触发条件</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-on-success">核销成功</Label>
                      <p className="text-sm text-muted-foreground">核销成功时发送通知</p>
                    </div>
                    <Switch id="notify-on-success" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-on-failure">核销失败</Label>
                      <p className="text-sm text-muted-foreground">核销失败时发送通知</p>
                    </div>
                    <Switch id="notify-on-failure" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notify-on-batch">批量核销</Label>
                      <p className="text-sm text-muted-foreground">批量核销完成时发送通知</p>
                    </div>
                    <Switch id="notify-on-batch" defaultChecked />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notification-template">通知模板</Label>
                <Textarea
                  id="notification-template"
                  rows={6}
                  defaultValue={`核销通知
状态: {status}
券码: {code}
商品: {product}
价格: {price}
时间: {time}
操作员: {operator}
`}
                />
                <p className="text-sm text-muted-foreground">使用花括号包裹的变量将在发送通知时被替换为实际值</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permission" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>权限设置</CardTitle>
              <CardDescription>配置核销相关的权限设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>核销权限</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-admin-verify">管理员</Label>
                      <p className="text-sm text-muted-foreground">管理员可以进行核销操作</p>
                    </div>
                    <Switch id="perm-admin-verify" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-front-desk-verify">前台</Label>
                      <p className="text-sm text-muted-foreground">前台可以进行核销操作</p>
                    </div>
                    <Switch id="perm-front-desk-verify" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-coach-verify">教练</Label>
                      <p className="text-sm text-muted-foreground">教练可以进行核销操作</p>
                    </div>
                    <Switch id="perm-coach-verify" />
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label>批量核销权限</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-admin-batch">管理员</Label>
                      <p className="text-sm text-muted-foreground">管理员可以进行批量核销操作</p>
                    </div>
                    <Switch id="perm-admin-batch" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-front-desk-batch">前台</Label>
                      <p className="text-sm text-muted-foreground">前台可以进行批量核销操作</p>
                    </div>
                    <Switch id="perm-front-desk-batch" />
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label>异常处理权限</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-admin-exception">管理员</Label>
                      <p className="text-sm text-muted-foreground">管理员可以处理异常核销记录</p>
                    </div>
                    <Switch id="perm-admin-exception" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-front-desk-exception">前台</Label>
                      <p className="text-sm text-muted-foreground">前台可以处理异常核销记录</p>
                    </div>
                    <Switch id="perm-front-desk-exception" />
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label>统计报表权限</Label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-admin-stats">管理员</Label>
                      <p className="text-sm text-muted-foreground">管理员可以查看统计报表</p>
                    </div>
                    <Switch id="perm-admin-stats" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-front-desk-stats">前台</Label>
                      <p className="text-sm text-muted-foreground">前台可以查看统计报表</p>
                    </div>
                    <Switch id="perm-front-desk-stats" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="perm-coach-stats">教练</Label>
                      <p className="text-sm text-muted-foreground">教练可以查看统计报表</p>
                    </div>
                    <Switch id="perm-coach-stats" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}