"use client"

import { useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { ColorPicker } from "./color-picker"

// 表单验证模式
const formSchema = z.object({
  name: z
    .string()
    .min(2, {
      message: "课程类型名称至少需要2个字符",
    })
    .max(50, {
      message: "课程类型名称不能超过50个字符",
    }),
  description: z
    .string()
    .max(200, {
      message: "描述不能超过200个字符",
    })
    .optional(),
  color: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {
    message: "请选择有效的颜色",
  }),
  isActive: z.boolean().default(true),
  displayOrder: z.number().int().min(0).default(0),
})

type CourseTypeFormValues = z.infer<typeof formSchema>

// 预设颜色
const presetColors = [
  "#4285F4", // 团课
  "#34A853", // 小班课
  "#FBBC05", // 精品课
  "#EA4335", // 私教课
  "#9C27B0", // 教培课
  "#FF6D91",
  "#00BCD4",
  "#FF9800",
  "#607D8B",
  "#795548",
  "#3F51B5",
  "#009688",
]

interface CourseTypeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  type?: any // 编辑时传入的课程类型数据
  onSave: (data: any) => void;
}

export function CourseTypeDialog({ open, onOpenChange, type, onSave }: CourseTypeDialogProps) {
  const isEditMode = !!type

  // 初始化表单
  const form = useForm<CourseTypeFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      color: presetColors[0],
      isActive: true,
      displayOrder: 0,
    }
  })

  // 当编辑模式下，填充表单数据
  useEffect(() => {
    if (isEditMode && type) {
      // 使用setTimeout避免React状态更新冲突
      setTimeout(() => {
        form.reset({
          name: type.name || "",
          description: type.description || "",
          color: type.color || presetColors[0],
          isActive: type.status === "active",
          displayOrder: type.displayOrder || 0,
        });
      }, 0);
    } else if (!isEditMode) {
      form.reset({
        name: "",
        description: "",
        color: presetColors[0],
        isActive: true,
        displayOrder: 0,
      });
    }
  }, [form, isEditMode, type]);

  // 表单提交处理
  const onSubmit = (values: CourseTypeFormValues) => {
    const submittedData = {
      ...(type || {}),
      name: values.name,
      description: values.description || "",
      color: values.color,
      status: values.isActive ? 'active' : 'inactive',
      displayOrder: values.displayOrder
    };
    
    onSave(submittedData);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditMode ? "编辑课程类型" : "添加课程类型"}</DialogTitle>
          <DialogDescription>
            {isEditMode
              ? "修改课程类型的详细信息。课程类型用于对课程进行分类和标识。"
              : "添加新的课程类型。课程类型用于对课程进行分类和标识。"}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>类型名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入课程类型名称" {...field} />
                  </FormControl>
                  <FormDescription>课程类型名称将显示在课程列表和日历中</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="输入课程类型描述（可选）"
                      className="resize-none"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>简要描述这个课程类型的特点和适用人群</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>颜色标识</FormLabel>
                  <FormControl>
                    <ColorPicker color={field.value} onChange={field.onChange} presetColors={presetColors} />
                  </FormControl>
                  <FormDescription>选择一个颜色来标识此课程类型，将用于日历和课程列表中</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>启用状态</FormLabel>
                      <FormDescription>设置此课程类型是否可用</FormDescription>
                    </div>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="displayOrder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>显示顺序</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        {...field}
                        onChange={(e) => field.onChange(Number.parseInt(e.target.value, 10) || 0)}
                        value={field.value}
                      />
                    </FormControl>
                    <FormDescription>数字越小排序越靠前</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button type="submit">保存</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

