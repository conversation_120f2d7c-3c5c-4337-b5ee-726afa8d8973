"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { useForm } from "react-hook-form"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { Loader2, QrCode, Mail, Smartphone } from "lucide-react"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { authApi } from "@/lib/api/auth"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormMessage, FormLabel } from "@/components/ui/form"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle2 } from "lucide-react"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

// 定义登录表单数据类型
interface LoginFormData {
  username: string;
  password: string;
  rememberMe: boolean;
  tenant_id: number;
  verifyCode?: string; // 验证码
  phone?: string; // 手机号
  email?: string; // 邮箱
}

// 表单验证模式
const loginSchema = z.object({
  username: z.string().min(2, { message: "用户名至少需要2个字符" }),
  password: z.string().min(6, { message: "密码至少需要6个字符" }),
  rememberMe: z.boolean().optional(),
  tenant_id: z.number().optional(),
});

type LoginValues = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectUrl = searchParams.get('redirect') || '/';
  const { toast } = useToast();
  const { isAuthenticated, login } = useAuth();
  const [activeTab, setActiveTab] = useState<string>("tenant");
  const [registerSuccess, setRegisterSuccess] = useState(false);

  // 登录方式状态
  const [loginType, setLoginType] = useState<'username' | 'phone' | 'email' | 'qrcode'>('username');

  // 表单状态管理 - 使用正确的 shadcn/ui Form 组件方式
  const form = useForm<LoginValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
      rememberMe: false,
      tenant_id: 1
    }
  })

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)
  // 租户列表
  const [tenants, setTenants] = useState([
    { id: 1, name: '默认租户' },
    { id: 2, name: '北京分馆' },
    { id: 3, name: '上海分馆' }
  ]);
  // 验证码相关状态
  const [verifyCodeImage, setVerifyCodeImage] = useState<string>('Y9X7');
  const [countdown, setCountdown] = useState(0); // 短信验证码倒计时

  // 监听租户选择变化
  const selectedTenantId = form.watch("tenant_id");
  
  // 获取验证码函数
  const getVerifyCode = () => {
    // 模拟获取图形验证码
    // 实际项目中应该调用后端API获取验证码图片
    setVerifyCodeImage('9977');
  }
  
  // 发送短信验证码
  const sendSmsCode = () => {
    if (countdown > 0) return;
    
    // 模拟发送短信验证码
    // 实际项目中应该调用后端API发送短信
    setCountdown(60);
    
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    toast({
      title: "验证码已发送",
      description: "请查看手机短信",
    });
  }
  
  // 获取租户列表
  useEffect(() => {
    // 这里可以添加获取租户列表的API调用
    // 示例: const fetchTenants = async () => { ... }
    // fetchTenants()
    
    // 初始化获取验证码
    getVerifyCode();
  }, [])

  // 检查URL参数，如果有注册成功的标记，显示成功消息
  useEffect(() => {
    if (searchParams.get("register") === "success") {
      setRegisterSuccess(true);
    }
  }, [searchParams]);

  // 登录处理函数
  const onSubmit = async (values: LoginValues) => {
    setIsLoading(true);
    try {
      // 调用真实的登录API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: values.username,
          password: values.password,
          userType: activeTab === "platform" ? "admin" : "tenant"
        })
      });

      const result = await response.json();

      if (result.code === 200) {
        // 登录成功
        const { token, user } = result.data;

        // 使用认证上下文的login方法
        login(token, {
          id: user.id,
          username: user.username,
          nickname: user.nickname,
          role: user.role,
          status: "active",
          tenant_id: user.tenantId || 0,
          permissions: user.permissions
        }, values.rememberMe);

        toast({
          title: "登录成功",
          description: `欢迎回来，${user.nickname}！`,
        });

        // 根据用户角色跳转到不同页面
        if (user.role === 'admin') {
          router.push('/platform/dashboard'); // 平台管理员页面
        } else {
          router.push('/dashboard'); // 租户用户页面
        }
      } else if (result.code === 403) {
        // 账户审核中或被暂停
        toast({
          title: "账户状态异常",
          description: result.message,
          variant: "default"
        });
      } else {
        // 登录失败
        toast({
          title: "登录失败",
          description: result.message || "请检查用户名和密码",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      console.error('登录失败:', error);
      toast({
        title: "登录失败",
        description: "网络连接失败，请稍后再试",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 模拟平台审核
  const handleApproveRegistration = () => {
    const registeredTenant = localStorage.getItem('registered_tenant');
    if (registeredTenant) {
      const tenant = JSON.parse(registeredTenant);
      tenant.status = 'approved';
      localStorage.setItem('registered_tenant', JSON.stringify(tenant));
      
      toast({
        title: "审核通过",
        description: "已通过租户注册申请",
      });
    }
  };

  // 添加测试代码，确认登录页面加载正常
  useEffect(() => {
    console.log('登录页面加载成功');
  }, [])

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[360px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">瑜伽管理系统</h1>
          <p className="text-sm text-muted-foreground">
            专业的瑜伽馆管理解决方案
          </p>
        </div>
        
        {registerSuccess && (
          <Alert className="bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-900">
            <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertTitle className="text-green-800 dark:text-green-400">注册成功</AlertTitle>
            <AlertDescription className="text-green-700 dark:text-green-500">
              您的账户已提交，正在等待平台审核。审核通过后，您将收到通知。
            </AlertDescription>
          </Alert>
        )}
        
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="tenant">租户登录</TabsTrigger>
            <TabsTrigger value="platform">平台管理员</TabsTrigger>
          </TabsList>
          
          <TabsContent value="tenant">
            <Card>
              <CardHeader>
                <CardTitle>租户管理员登录</CardTitle>
                <CardDescription>
                  输入您的账户信息登录
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>用户名</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入用户名" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>密码</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="请输入密码" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="rememberMe"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox 
                              checked={field.value} 
                              onCheckedChange={field.onChange} 
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              记住我
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <Button type="submit" className="w-full" disabled={isLoading}>
                      {isLoading ? "登录中..." : "登录"}
                    </Button>
                  </form>
                </Form>
              </CardContent>
              <CardFooter className="flex flex-col space-y-2">
                <div className="text-sm text-center text-muted-foreground">
                  还没有账户？ <Link href="/register" className="text-primary underline">注册连锁账户</Link>
                </div>
                
                {/* 模拟平台审核按钮 - 实际应用中会删除 */}
                <Button variant="outline" size="sm" onClick={handleApproveRegistration} className="text-xs">
                  (模拟) 平台审核通过
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="platform">
            <Card>
              <CardHeader>
                <CardTitle>平台管理员登录</CardTitle>
                <CardDescription>
                  使用平台管理员账户登录
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="username"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>用户名</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入用户名" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>密码</FormLabel>
                          <FormControl>
                            <Input type="password" placeholder="请输入密码" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="rememberMe"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox 
                              checked={field.value} 
                              onCheckedChange={field.onChange} 
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              记住我
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <Button type="submit" className="w-full" disabled={isLoading}>
                      {isLoading ? "登录中..." : "登录"}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        
        <p className="text-center text-sm text-muted-foreground">
          点击登录即表示您同意我们的{" "}
          <Link href="/terms" className="text-primary underline">
            服务条款
          </Link>
          {" "}和{" "}
          <Link href="/privacy" className="text-primary underline">
            隐私政策
          </Link>
        </p>
      </div>
    </div>
  )
}
