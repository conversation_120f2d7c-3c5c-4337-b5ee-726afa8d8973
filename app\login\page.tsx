"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { useForm } from "react-hook-form"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { Loader2, QrCode, Mail, Smartphone } from "lucide-react"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"

import { authApi } from "@/lib/api/auth"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormMessage, FormLabel } from "@/components/ui/form"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AlertTitle } from "@/components/ui/alert"
import { CheckCircle2 } from "lucide-react"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

// 定义登录表单数据类型
interface LoginFormData {
  username: string;
  password: string;
  rememberMe: boolean;
  tenant_id: number;
  verifyCode?: string; // 验证码
  phone?: string; // 手机号
  email?: string; // 邮箱
}

// 表单验证模式
const loginSchema = z.object({
  username: z.string().min(2, { message: "用户名至少需要2个字符" }),
  password: z.string().min(6, { message: "密码至少需要6个字符" }),
  rememberMe: z.boolean().optional(),
  tenant_id: z.number().optional(),
});

type LoginValues = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectUrl = searchParams.get('redirect') || '/';
  const { toast } = useToast();
  const { isAuthenticated, login } = useAuth();
  const [registerSuccess, setRegisterSuccess] = useState(false);

  // 表单状态管理
  const form = useForm<LoginValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
      rememberMe: false,
    }
  })

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 检查URL参数，如果有注册成功的标记，显示成功消息
  useEffect(() => {
    if (searchParams.get("register") === "success") {
      setRegisterSuccess(true);
    }
  }, [searchParams]);

  // 登录处理函数
  const onSubmit = async (values: LoginValues) => {
    setIsLoading(true);
    try {
      console.log('提交登录请求:', values);

      // 调用真实的登录API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: values.username,
          password: values.password
        })
      });

      const result = await response.json();
      console.log('登录响应:', result);

      if (result.code === 200) {
        // 登录成功
        const { token, user } = result.data;

        // 使用认证上下文的login方法
        login(token, {
          id: user.id,
          username: user.username,
          nickname: user.nickname,
          role: user.role,
          status: "active",
          tenant_id: user.tenantId || 0,
          permissions: user.permissions
        }, values.rememberMe);

        toast({
          title: "登录成功",
          description: `欢迎回来，${user.nickname}！`,
        });

        // 跳转到主页
        router.push('/dashboard');
      } else if (result.code === 403) {
        // 账户审核中或被暂停
        toast({
          title: "账户状态异常",
          description: result.message,
          variant: "default"
        });
      } else {
        // 登录失败
        toast({
          title: "登录失败",
          description: result.message || "请检查用户名和密码",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      console.error('登录失败:', error);
      toast({
        title: "登录失败",
        description: "网络连接失败，请稍后再试",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 添加测试代码，确认登录页面加载正常
  useEffect(() => {
    console.log('登录页面加载成功');
  }, [])

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[360px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">瑜伽管理系统</h1>
          <p className="text-sm text-muted-foreground">
            专业的瑜伽馆管理解决方案
          </p>
        </div>
        
        {registerSuccess && (
          <Alert className="bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-900">
            <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
            <AlertTitle className="text-green-800 dark:text-green-400">注册成功</AlertTitle>
            <AlertDescription className="text-green-700 dark:text-green-500">
              您的账户已提交，正在等待平台审核。审核通过后，您将收到通知。
            </AlertDescription>
          </Alert>
        )}
        
        <Card>
          <CardHeader>
            <CardTitle>用户登录</CardTitle>
            <CardDescription>
              输入您的账户信息登录系统
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>用户名/手机号</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入用户名或手机号" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>密码</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="请输入密码" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="rememberMe"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          记住我
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? "登录中..." : "登录"}
                </Button>
              </form>
            </Form>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <div className="text-sm text-center text-muted-foreground">
              还没有账户？ <Link href="/register" className="text-primary underline">立即注册</Link>
            </div>
          </CardFooter>
        </Card>
        
        <p className="text-center text-sm text-muted-foreground">
          点击登录即表示您同意我们的{" "}
          <Link href="/terms" className="text-primary underline">
            服务条款
          </Link>
          {" "}和{" "}
          <Link href="/privacy" className="text-primary underline">
            隐私政策
          </Link>
        </p>
      </div>
    </div>
  )
}
