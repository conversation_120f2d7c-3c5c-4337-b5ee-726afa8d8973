"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { ColorPicker } from "@/components/courses/color-picker"

const formSchema = z.object({
  name: z.string().min(1, "标签名称不能为空"),
  description: z.string().optional(),
  color: z.string().min(1, "请选择标签颜色"),
  category: z.string().min(1, "请选择标签分类"),
  isAutoTag: z.boolean().default(false),
})

type TagDialogProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
  tag?: any
}

export function TagDialog({ open, onOpenChange, tag }: TagDialogProps) {
  const isEditing = !!tag
  const [selectedColor, setSelectedColor] = useState(tag?.color || "#4285F4")

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: tag?.name || "",
      description: tag?.description || "",
      color: tag?.color || "#4285F4",
      category: tag?.category || "",
      isAutoTag: tag?.isAutoTag || false,
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "编辑标签" : "添加标签"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "修改会员标签信息，编辑后将自动应用到所有已标记的会员。"
              : "创建新的会员标签，用于对会员进行分类和标记。"}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>标签名称</FormLabel>
                  <FormControl>
                    <Input placeholder="输入标签名称" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>标签描述</FormLabel>
                  <FormControl>
                    <Textarea placeholder="输入标签描述（可选）" className="resize-none" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>标签颜色</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-4">
                      <div className="h-10 w-10 rounded-md border" style={{ backgroundColor: selectedColor }} />
                      <ColorPicker
                        color={selectedColor}
                        onChange={(color) => {
                          setSelectedColor(color)
                          field.onChange(color)
                        }}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>标签分类</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="选择标签分类" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="消费">消费</SelectItem>
                      <SelectItem value="活跃度">活跃度</SelectItem>
                      <SelectItem value="兴趣">兴趣</SelectItem>
                      <SelectItem value="时间">时间</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isAutoTag"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">自动标签</FormLabel>
                    <FormDescription>启用后，系统将根据规则自动为符合条件的会员添加此标签</FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button type="submit">保存</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

