"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { getMemberCardById } from "@/services/member-card-data"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { CardValueDisplay } from "./card-value-display"
import { CardConsumptionRecords } from "./card-consumption-records"
import {
  ArrowLeft,
  CreditCard,
  Calendar,
  Clock,
  Tag,
  RefreshCw,
  Edit,
  Trash,
  User,
  Share2,
  CalendarRange,
  Wallet,
  History,
  BarChart2,
  Printer,
  Download,
  MessageSquare,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Sparkles,
  RotateCcw,
  FileText,
  ChevronRight,
  X,
  FileSignature,
  Settings,
  Plus,
  CheckCircle,
  Eye
} from "lucide-react"

// 获取状态标签
const getStatusBadge = (status: string) => {
  switch (status) {
    case "有效":
    case "active":
      return <Badge className="bg-green-500">有效</Badge>
    case "已过期":
    case "expired":
      return <Badge variant="outline" className="text-gray-500">已过期</Badge>
    case "冻结":
    case "已冻结":
    case "frozen":
      return <Badge className="bg-blue-500">已冻结</Badge>
    case "未激活":
    case "inactive":
      return <Badge variant="outline" className="text-yellow-500">未激活</Badge>
    case "请假中":
    case "onLeave":
      return <Badge className="bg-purple-500">请假中</Badge>
    case "已退卡":
    case "refunded":
      return <Badge className="bg-red-500">已退卡</Badge>
    default:
      return <Badge variant="outline">{status || "未知"}</Badge>
  }
}

// 定义对话框类型
type DialogType =
  | "freeze"
  | "unfreeze"
  | "delete"
  | "extend"
  | "transfer"
  | "renew"
  | "leave"
  | "endLeave"
  | "refund"
  | null;

interface MemberCardInstanceDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  cardId: string
}

export function MemberCardInstanceDetailDialog({
  open,
  onOpenChange,
  cardId
}: MemberCardInstanceDetailDialogProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")
  const [dialogType, setDialogType] = useState<DialogType>(null)
  const [dialogNote, setDialogNote] = useState("")
  const [expandedRecords, setExpandedRecords] = useState<string[]>([])

  // 从共享数据服务获取会员卡数据
  // 如果找不到指定ID的会员卡，则使用默认数据
  const defaultCard = {
    id: "MC001",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "瑜伽年卡",
    cardName: "瑜伽年卡",
    cardCategory: "time" as "time" | "count" | "value",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    status: "有效",
    remainingDays: 120,
    remainingCount: null,
    remainingValue: 2000,
    originalValue: 3600,
    consumedValue: 1600,
    totalDays: 365,
    usedDays: 245,
    price: 3600,
    actualPrice: 3200,
    paymentMethod: "微信支付",
    activationDate: "2023-01-01",
    lastUsedDate: "2023-09-15",
    usageCount: 45,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "老客户"],
    notes: "会员续卡，享受8.9折优惠",
    createdAt: "2022-12-25",
    createdBy: "管理员",
    memberAvatar: "/avatars/01.png",
    memberLevel: "金卡会员",
    memberJoinDate: "2020-05-15",
    memberTotalSpent: "¥12,500",
    memberVisitCount: 120,
    memberLastVisit: "2023-09-15",
    totalDays: 365,
    usedDays: 245,
    usageRecords: [
      { id: "UR001", date: "2023-09-15", course: "高级瑜伽", instructor: "王教练", duration: "90分钟", consumption: "1次课", status: "已完成" },
    ],
    consumptionRecords: [
      {
        id: "CR001",
        date: "2023-09-15",
        type: "class",
        itemName: "高级瑜伽",
        originalPrice: 200,
        consumedValue: 120,
        remainingValue: 2400,
        instructor: "王教练",
        location: "一号教室",
      },
    ],
    paymentRecords: [
      { id: "PR001", date: "2022-12-25", type: "购卡", amount: "¥3200", method: "微信支付", status: "已完成", operator: "管理员" },
    ],
    operationRecords: [
      {
        id: "OR001",
        date: "2023-09-20",
        operation: "解冻会员卡",
        operator: "李经理",
        note: "会员请假结束，恢复正常使用"
      },
    ]
  };

  // 获取会员卡数据
  const card = getMemberCardById(cardId) || defaultCard;

  // 确保卡片数据包含必要的字段
  if (!card.cardName) {
    card.cardName = card.cardType;
  }

  // 根据卡片类型设置卡片类别
  if (!card.cardCategory) {
    if (card.remainingDays !== null) {
      card.cardCategory = "time";
    } else if (card.remainingCount !== null) {
      card.cardCategory = "count";
    } else if (card.remainingValue !== null) {
      card.cardCategory = "value";
    } else {
      card.cardCategory = "time"; // 默认为期限卡
    }
  }

  // 确保状态字段格式正确
  if (typeof card.status === "string") {
    if (card.status === "active") {
      card.status = "有效";
    } else if (card.status === "expired") {
      card.status = "已过期";
    } else if (card.status === "frozen") {
      card.status = "冻结";
    } else if (card.status === "refunded") {
      card.status = "已退卡";
    } else if (card.status === "onLeave") {
      card.status = "请假中";
    } else if (card.status === "inactive") {
      card.status = "未激活";
    }
  }

  // 确保价值相关字段正确初始化
  if (card.originalValue === undefined || card.originalValue === null) {
    // 根据卡类型设置默认值
    if (card.cardCategory === "time") {
      card.originalValue = card.price || 0;
    } else if (card.cardCategory === "count") {
      card.originalValue = card.price || 0;
    } else if (card.cardCategory === "value") {
      card.originalValue = card.remainingValue || 0;
    } else {
      card.originalValue = 0;
    }
  }

  // 确保所有卡片类型都有剩余价值
  if (card.remainingValue === undefined || card.remainingValue === null) {
    if (card.cardCategory === "time" && card.totalDays && card.remainingDays !== undefined) {
      // 对于期限卡，根据剩余天数计算剩余价值
      card.remainingValue = Math.round((card.remainingDays / card.totalDays) * card.originalValue);
    } else if (card.cardCategory === "count" && card.totalCount && card.remainingCount !== undefined) {
      // 对于次卡，根据剩余次数计算剩余价值
      card.remainingValue = Math.round((card.remainingCount / card.totalCount) * card.originalValue);
    } else {
      card.remainingValue = 0;
    }
  }

  if (card.consumedValue === undefined || card.consumedValue === null) {
    // 计算已消耗价值
    if (card.cardCategory === "time" && card.totalDays && card.remainingDays !== undefined) {
      const usedDays = card.totalDays - card.remainingDays;
      card.consumedValue = Math.round((usedDays / card.totalDays) * card.originalValue);
    } else if (card.cardCategory === "count" && card.totalCount && card.remainingCount !== undefined) {
      const usedCount = card.totalCount - card.remainingCount;
      card.consumedValue = Math.round((usedCount / card.totalCount) * card.originalValue);
    } else if (card.cardCategory === "value" && card.originalValue && card.remainingValue !== undefined) {
      card.consumedValue = card.originalValue - card.remainingValue;
    } else {
      card.consumedValue = 0;
    }
  }

  // 处理操作按钮点击
  const handleAction = (action: DialogType) => {
    setDialogType(action)
  }

  // 处理编辑按钮点击
  const handleEdit = () => {
    onOpenChange(false)
    router.push(`/members/cards/edit/${cardId}`)
  }

  // 处理展开/折叠记录
  const toggleRecordExpand = (recordId: string) => {
    setExpandedRecords(prev =>
      prev.includes(recordId)
        ? prev.filter(id => id !== recordId)
        : [...prev, recordId]
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: card.cardTypeColor }}
              ></div>
              <DialogTitle>会员卡详情 - {card.cardName}</DialogTitle>
              {/* 状态显示已在外部，此处注释掉 */}
              {/* {getStatusBadge(card.status)} */}
            </div>
            {/* 保留自定义关闭按钮，移除默认关闭按钮 */}
            <Button variant="ghost" size="icon" onClick={() => onOpenChange(false)}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription>卡号: {card.id}</DialogDescription>
        </DialogHeader>

        {/* 操作按钮 - 已在外部实现，此处注释掉
        <div className="flex flex-wrap gap-2 mt-2">
          <Button variant="outline" size="sm" onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            编辑
          </Button>

          {card.status === "有效" ? (
            <Button variant="outline" size="sm" onClick={() => handleAction("freeze")}>
              <Clock className="mr-2 h-4 w-4" />
              冻结
            </Button>
          ) : card.status === "冻结" ? (
            <Button variant="outline" size="sm" onClick={() => handleAction("unfreeze")}>
              <RefreshCw className="mr-2 h-4 w-4" />
              解冻
            </Button>
          ) : null}

          {card.status === "有效" && (
            <Button variant="outline" size="sm" onClick={() => handleAction("leave")}>
              <User className="mr-2 h-4 w-4" />
              请假
            </Button>
          )}

          {card.status !== "已退卡" && (
            <>
              <Button variant="outline" size="sm" onClick={() => handleAction("extend")}>
                <CalendarRange className="mr-2 h-4 w-4" />
                延长有效期
              </Button>

              <Button variant="outline" size="sm" onClick={() => handleAction("transfer")}>
                <Share2 className="mr-2 h-4 w-4" />
                转让
              </Button>

              <Button variant="outline" size="sm" onClick={() => handleAction("refund")}>
                <Wallet className="mr-2 h-4 w-4" />
                退卡
              </Button>
            </>
          )}

          <Button variant="outline" size="sm" onClick={() => window.print()}>
            <Printer className="mr-2 h-4 w-4" />
            打印
          </Button>
        </div>
        */}

        {/* 基本信息和会员信息并排显示 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          {/* 基本信息卡片 - 左侧 */}
          <Card className="md:col-span-2">
            <CardHeader className="pb-2">
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">开始日期</p>
                  <p className="font-medium">{card.startDate}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">结束日期</p>
                  <p className="font-medium">{card.endDate || '无限期'}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">激活日期</p>
                  <p className="font-medium">{card.activationDate || '未激活'}</p>
                </div>

                {card.remainingDays !== null && (
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">剩余天数</p>
                    <p className="font-medium">{card.remainingDays}天</p>
                  </div>
                )}
                {card.remainingCount !== null && (
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">剩余次数</p>
                    <p className="font-medium">{card.remainingCount}次</p>
                  </div>
                )}

                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">最近使用</p>
                  <p className="font-medium">{card.lastUsedDate || '未使用'}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">使用次数</p>
                  <p className="font-medium">{card.usageCount}次</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">创建日期</p>
                  <p className="font-medium">{card.createdAt}</p>
                </div>
              </div>

              {card.tags.length > 0 && (
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground mb-1">标签</p>
                  <div className="flex flex-wrap gap-1">
                    {card.tags.map((tag: string) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {card.notes && (
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground mb-1">备注</p>
                  <p className="text-sm p-2 bg-muted rounded-md">{card.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 会员信息卡片 - 右侧 */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                会员信息
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center mb-4">
                <Avatar className="h-16 w-16 mb-2">
                  <AvatarImage src={card.memberAvatar} alt={card.memberName} />
                  <AvatarFallback>{card.memberName[0]}</AvatarFallback>
                </Avatar>
                <h3 className="text-lg font-semibold">{card.memberName}</h3>
                <p className="text-sm text-muted-foreground">{card.memberPhone}</p>
                <Badge className="mt-1">{card.memberLevel}</Badge>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">会员ID</span>
                  <span className="font-medium">{card.memberId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">注册日期</span>
                  <span className="font-medium">{card.memberJoinDate}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">累计消费</span>
                  <span className="font-medium">{card.memberTotalSpent}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">到访次数</span>
                  <span className="font-medium">{card.memberVisitCount}次</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 会员卡价值显示 - 单行紧凑布局 */}
        <Card className="mt-4">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <Wallet className="h-5 w-5" />
              会员卡价值
            </CardTitle>
          </CardHeader>
          <CardContent className="py-3">
            {/* 价值信息 */}
            <div className="space-y-2 mb-3">
              <div className="flex justify-between text-sm">
                <span>剩余价值: ¥{card.remainingValue !== null && card.remainingValue !== undefined ? card.remainingValue : 0}</span>
                <span>使用进度: {card.originalValue ? Math.round(((card.remainingValue || 0) / card.originalValue) * 100) : 0}%</span>
              </div>
              <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary"
                  style={{ width: `${card.originalValue ? Math.round(((card.remainingValue || 0) / card.originalValue) * 100) : 0}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>已消耗: ¥{card.consumedValue !== null && card.consumedValue !== undefined ? card.consumedValue : 0}</span>
                <span>总价值: ¥{card.originalValue !== null && card.originalValue !== undefined ? card.originalValue : 0}</span>
              </div>
            </div>

            {/* 根据卡类型显示不同的详细信息 */}
            <div className="grid grid-cols-3 gap-4">
              {card.cardCategory === "time" && card.remainingDays !== undefined && card.totalDays !== undefined && (
                <>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-xs text-muted-foreground">总天数</p>
                    <p className="font-medium">{card.totalDays}天</p>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-xs text-muted-foreground">已使用</p>
                    <p className="font-medium">{card.usedDays || (card.totalDays - card.remainingDays)}天</p>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-xs text-muted-foreground">剩余天数</p>
                    <p className="font-medium">{card.remainingDays}天</p>
                  </div>
                </>
              )}

              {card.cardCategory === "count" && card.remainingCount !== undefined && (
                <>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-xs text-muted-foreground">总次数</p>
                    <p className="font-medium">{card.totalCount || 20}次</p>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-xs text-muted-foreground">已使用</p>
                    <p className="font-medium">{card.usedCount || 5}次</p>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-xs text-muted-foreground">剩余次数</p>
                    <p className="font-medium">{card.remainingCount}次</p>
                  </div>
                </>
              )}

              {card.cardCategory === "value" && card.remainingValue !== undefined && (
                <>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-xs text-muted-foreground">总金额</p>
                    <p className="font-medium">¥{card.originalValue !== null && card.originalValue !== undefined ? card.originalValue : 0}</p>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-xs text-muted-foreground">已使用</p>
                    <p className="font-medium">¥{card.consumedValue !== null && card.consumedValue !== undefined ? card.consumedValue : 0}</p>
                  </div>
                  <div className="text-center p-2 bg-muted/30 rounded-md">
                    <p className="text-xs text-muted-foreground">剩余金额</p>
                    <p className="font-medium">¥{card.remainingValue !== null && card.remainingValue !== undefined ? card.remainingValue : 0}</p>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 详细信息标签页 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="basic">
              <CreditCard className="mr-2 h-4 w-4" />
              支付信息
            </TabsTrigger>
            <TabsTrigger value="usage">
              <History className="mr-2 h-4 w-4" />
              使用记录
            </TabsTrigger>
            <TabsTrigger value="consumption">
              <Wallet className="mr-2 h-4 w-4" />
              消费记录
            </TabsTrigger>
            <TabsTrigger value="operations">
              <FileText className="mr-2 h-4 w-4" />
              操作日志
            </TabsTrigger>
            <TabsTrigger value="stats">
              <BarChart2 className="mr-2 h-4 w-4" />
              使用统计
            </TabsTrigger>
            <TabsTrigger value="contracts">
              <FileSignature className="mr-2 h-4 w-4" />
              电子合同
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>支付信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">原价</p>
                    <p className="font-medium">¥{card.price}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">实付金额</p>
                    <p className="font-medium">¥{card.actualPrice}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">支付方式</p>
                    <p className="font-medium">{card.paymentMethod}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">创建人</p>
                    <p className="font-medium">{card.createdBy}</p>
                  </div>
                </div>

                <Separator className="my-4" />

                <h4 className="text-sm font-semibold mb-2">支付记录</h4>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>日期</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>金额</TableHead>
                        <TableHead>支付方式</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>操作人</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {card.paymentRecords && card.paymentRecords.length > 0 ? (
                        card.paymentRecords.map((record: any) => (
                          <TableRow key={record.id}>
                            <TableCell>{record.date}</TableCell>
                            <TableCell>{record.type}</TableCell>
                            <TableCell>{record.amount}</TableCell>
                            <TableCell>{record.method}</TableCell>
                            <TableCell>{record.status}</TableCell>
                            <TableCell>{record.operator}</TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                            暂无支付记录
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="usage" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>使用记录</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>日期</TableHead>
                        <TableHead>课程</TableHead>
                        <TableHead>教练</TableHead>
                        <TableHead>时长</TableHead>
                        <TableHead>消费</TableHead>
                        <TableHead>状态</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {card.usageRecords && card.usageRecords.length > 0 ? (
                        card.usageRecords.map((record: any) => (
                          <TableRow key={record.id}>
                            <TableCell>{record.date}</TableCell>
                            <TableCell>{record.course}</TableCell>
                            <TableCell>{record.instructor}</TableCell>
                            <TableCell>{record.duration}</TableCell>
                            <TableCell>{record.consumption}</TableCell>
                            <TableCell>{record.status}</TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                            暂无使用记录
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end mt-4">
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    导出记录
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="consumption" className="space-y-4">
            <CardConsumptionRecords
              records={card.consumptionRecords}
              cardType={card.cardCategory as "time" | "count" | "value"}
              originalValue={card.originalValue}
            />
          </TabsContent>

          <TabsContent value="operations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>操作日志</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>日期</TableHead>
                        <TableHead>操作</TableHead>
                        <TableHead>操作人</TableHead>
                        <TableHead>备注</TableHead>
                        <TableHead className="w-[50px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {card.operationRecords && card.operationRecords.length > 0 ? (
                        card.operationRecords.map((record: any) => (
                          <>
                            <TableRow
                              key={record.id}
                              className={expandedRecords.includes(record.id) ? "border-b-0" : ""}
                            >
                              <TableCell>{record.date}</TableCell>
                              <TableCell>{record.operation}</TableCell>
                              <TableCell>{record.operator}</TableCell>
                              <TableCell>{record.note}</TableCell>
                              <TableCell>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleRecordExpand(record.id)}
                                >
                                  {expandedRecords.includes(record.id) ? (
                                    <ChevronRight className="h-4 w-4 rotate-90 transition-transform" />
                                  ) : (
                                    <ChevronRight className="h-4 w-4 transition-transform" />
                                  )}
                                </Button>
                              </TableCell>
                            </TableRow>
                            {expandedRecords.includes(record.id) && (
                              <TableRow key={`${record.id}-details`}>
                                <TableCell colSpan={5} className="bg-muted/30 p-4">
                                  <div className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                      {record.details && Object.entries(record.details).map(([key, value]: [string, any]) => {
                                        // 跳过嵌套对象和数组，单独处理
                                        if (typeof value === 'object' && value !== null) return null;

                                        // 将英文键名转换为中文显示
                                        const keyToChinese: Record<string, string> = {
                                          // 通用字段
                                          beforeStatus: "之前状态",
                                          afterStatus: "之后状态",
                                          ipAddress: "IP地址",
                                          deviceInfo: "设备信息",
                                          operationTime: "操作时间",
                                          approvedBy: "审批人",

                                          // 延长有效期相关
                                          beforeEndDate: "延期前结束日期",
                                          afterEndDate: "延期后结束日期",
                                          extendedDays: "延长天数",
                                          reason: "原因",
                                          approvalNumber: "审批编号",

                                          // 转让相关
                                          transferFee: "转让费用",
                                          contractId: "合同编号",

                                          // 请假相关
                                          leaveStartDate: "请假开始日期",
                                          leaveEndDate: "请假结束日期",
                                          leaveDuration: "请假时长",
                                          leaveReason: "请假原因",
                                          actualLeaveDays: "实际请假天数",
                                          newEndDate: "新结束日期",

                                          // 冻结相关
                                          freezeDuration: "冻结时长",
                                          freezeStartDate: "冻结开始日期",
                                          freezeEndDate: "冻结结束日期",

                                          // 惩罚相关
                                          penaltyReason: "惩罚原因",
                                          penaltyType: "惩罚类型",
                                          penaltyAmount: "惩罚数量",
                                          beforeCount: "惩罚前次数",
                                          afterCount: "惩罚后次数",

                                          // 激活相关
                                          activationReason: "激活原因",
                                          activationMethod: "激活方式",
                                          activationTrigger: "激活触发",
                                          startDate: "开始日期",
                                          endDate: "结束日期",

                                          // 创建相关
                                          cardType: "卡类型",
                                          originalPrice: "原价",
                                          discountRate: "折扣率",
                                          actualPrice: "实付金额",
                                          paymentMethod: "支付方式",
                                          transactionId: "交易编号",
                                          salesPerson: "销售人员",

                                          // 课程相关
                                          courseId: "课程ID",
                                          courseName: "课程名称"
                                        };

                                        const formattedKey = keyToChinese[key] || key
                                          .replace(/([A-Z])/g, ' $1')
                                          .replace(/^./, str => str.toUpperCase())
                                          .replace(/([a-z])([A-Z])/g, '$1 $2');

                                        return (
                                          <div key={key} className="space-y-1">
                                            <p className="text-sm text-muted-foreground">{formattedKey}</p>
                                            <p className="text-sm font-medium">{value as string}</p>
                                          </div>
                                        );
                                      })}
                                    </div>

                                    {/* 处理嵌套对象 */}
                                    {record.details && record.details.originalOwner && (
                                      <div className="mt-4">
                                        <h4 className="text-sm font-semibold mb-2">原持卡人信息</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                          <div className="space-y-1">
                                            <p className="text-sm text-muted-foreground">姓名</p>
                                            <p className="text-sm font-medium">{record.details.originalOwner.name}</p>
                                          </div>
                                          <div className="space-y-1">
                                            <p className="text-sm text-muted-foreground">电话</p>
                                            <p className="text-sm font-medium">{record.details.originalOwner.phone}</p>
                                          </div>
                                          <div className="space-y-1">
                                            <p className="text-sm text-muted-foreground">会员ID</p>
                                            <p className="text-sm font-medium">{record.details.originalOwner.memberId}</p>
                                          </div>
                                        </div>
                                      </div>
                                    )}

                                    {record.details && record.details.newOwner && (
                                      <div className="mt-4">
                                        <h4 className="text-sm font-semibold mb-2">新持卡人信息</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                          <div className="space-y-1">
                                            <p className="text-sm text-muted-foreground">姓名</p>
                                            <p className="text-sm font-medium">{record.details.newOwner.name}</p>
                                          </div>
                                          <div className="space-y-1">
                                            <p className="text-sm text-muted-foreground">电话</p>
                                            <p className="text-sm font-medium">{record.details.newOwner.phone}</p>
                                          </div>
                                          <div className="space-y-1">
                                            <p className="text-sm text-muted-foreground">会员ID</p>
                                            <p className="text-sm font-medium">{record.details.newOwner.memberId}</p>
                                          </div>
                                        </div>
                                      </div>
                                    )}

                                    {/* 处理数组 */}
                                    {record.details && record.details.relatedBookings && (
                                      <div className="mt-4">
                                        <h4 className="text-sm font-semibold mb-2">相关预约记录</h4>
                                        <div className="flex flex-wrap gap-2">
                                          {record.details.relatedBookings.map((booking: string) => (
                                            <Badge key={booking} variant="outline">{booking}</Badge>
                                          ))}
                                        </div>
                                      </div>
                                    )}

                                    {record.details && record.details.relatedRecords && (
                                      <div className="mt-4">
                                        <h4 className="text-sm font-semibold mb-2">相关操作记录</h4>
                                        <div className="flex flex-wrap gap-2">
                                          {record.details.relatedRecords.map((relatedRecord: string) => (
                                            <Badge key={relatedRecord} variant="outline">{relatedRecord}</Badge>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                </TableCell>
                              </TableRow>
                            )}
                          </>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                            暂无操作记录
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end mt-4">
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    导出记录
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stats" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>使用统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{card.usageCount}</div>
                        <p className="text-xs text-muted-foreground">总使用次数</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold">
                          {card.remainingDays !== null ? `${card.remainingDays}天` :
                           card.remainingCount !== null ? `${card.remainingCount}次` :
                           card.remainingValue !== null ? `¥${card.remainingValue}` : '无限'}
                        </div>
                        <p className="text-xs text-muted-foreground">剩余额度</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold">
                          {card.usageCount > 0 ? Math.round(card.actualPrice / card.usageCount * 100) / 100 : 0}
                        </div>
                        <p className="text-xs text-muted-foreground">平均单次成本</p>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold">
                          {card.usageRecords && card.usageRecords.length > 0 ?
                            Math.round(card.usageRecords.length / (
                              (new Date().getTime() - new Date(card.activationDate || card.startDate).getTime()) /
                              (1000 * 60 * 60 * 24 * 7)
                            ) * 10) / 10 : 0}
                        </div>
                        <p className="text-xs text-muted-foreground">平均每周使用</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4">使用频率统计</h3>
                  <div className="h-64 w-full bg-muted/30 rounded-md mt-2 relative overflow-hidden">
                    {/* 简单的柱状图示例 */}
                    <div className="absolute bottom-0 left-0 w-full h-full flex items-end justify-around p-4">
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-primary rounded-t-md" style={{ height: '30%' }}></div>
                        <span className="text-xs mt-2">1月</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-primary rounded-t-md" style={{ height: '45%' }}></div>
                        <span className="text-xs mt-2">2月</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-primary rounded-t-md" style={{ height: '60%' }}></div>
                        <span className="text-xs mt-2">3月</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-primary rounded-t-md" style={{ height: '40%' }}></div>
                        <span className="text-xs mt-2">4月</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-primary rounded-t-md" style={{ height: '75%' }}></div>
                        <span className="text-xs mt-2">5月</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-primary rounded-t-md" style={{ height: '55%' }}></div>
                        <span className="text-xs mt-2">6月</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-primary rounded-t-md" style={{ height: '65%' }}></div>
                        <span className="text-xs mt-2">7月</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-primary rounded-t-md" style={{ height: '80%' }}></div>
                        <span className="text-xs mt-2">8月</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="w-12 bg-primary rounded-t-md" style={{ height: '70%' }}></div>
                        <span className="text-xs mt-2">9月</span>
                      </div>
                    </div>

                    {/* Y轴标签 */}
                    <div className="absolute top-0 left-0 h-full flex flex-col justify-between py-4 px-2">
                      <span className="text-xs text-muted-foreground">10次</span>
                      <span className="text-xs text-muted-foreground">5次</span>
                      <span className="text-xs text-muted-foreground">0次</span>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4">课程类型分布</h3>
                  <div className="h-64 w-full bg-muted/30 rounded-md mt-2 relative overflow-hidden">
                    {/* 简单的饼图示例 */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="relative w-40 h-40">
                        <div className="absolute inset-0 rounded-full border-8 border-primary" style={{ clipPath: 'polygon(50% 50%, 100% 50%, 100% 0, 50% 0)' }}></div>
                        <div className="absolute inset-0 rounded-full border-8 border-blue-500" style={{ clipPath: 'polygon(50% 50%, 50% 0, 0 0, 0 50%)' }}></div>
                        <div className="absolute inset-0 rounded-full border-8 border-green-500" style={{ clipPath: 'polygon(50% 50%, 0 50%, 0 100%, 50% 100%)' }}></div>
                        <div className="absolute inset-0 rounded-full border-8 border-yellow-500" style={{ clipPath: 'polygon(50% 50%, 50% 100%, 100% 100%, 100% 50%)' }}></div>
                      </div>
                    </div>

                    {/* 图例 */}
                    <div className="absolute bottom-4 right-4">
                      <div className="bg-white/80 p-2 rounded-md shadow-sm">
                        <div className="flex items-center mb-1">
                          <div className="w-3 h-3 bg-primary rounded-sm mr-2"></div>
                          <span className="text-xs">瑜伽课 (35%)</span>
                        </div>
                        <div className="flex items-center mb-1">
                          <div className="w-3 h-3 bg-blue-500 rounded-sm mr-2"></div>
                          <span className="text-xs">普拉提 (25%)</span>
                        </div>
                        <div className="flex items-center mb-1">
                          <div className="w-3 h-3 bg-green-500 rounded-sm mr-2"></div>
                          <span className="text-xs">私教课 (20%)</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-yellow-500 rounded-sm mr-2"></div>
                          <span className="text-xs">其他 (20%)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contracts" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>法大大电子合同</CardTitle>
                  <Button size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    创建合同
                  </Button>
                </div>
                <CardDescription>
                  查看和管理与此会员卡相关的电子合同
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* 合同列表 */}
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>合同名称</TableHead>
                        <TableHead>签署日期</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell className="font-medium">会员卡购买协议</TableCell>
                        <TableCell>{card.createdAt}</TableCell>
                        <TableCell>
                          <Badge className="bg-green-500">已签署</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">查看</span>
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Download className="h-4 w-4" />
                              <span className="sr-only">下载</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell className="font-medium">会员卡使用条款</TableCell>
                        <TableCell>{card.activationDate}</TableCell>
                        <TableCell>
                          <Badge className="bg-green-500">已签署</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">查看</span>
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Download className="h-4 w-4" />
                              <span className="sr-only">下载</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>

                {/* 法大大服务状态 */}
                <div className="mt-6 space-y-4">
                  <h3 className="text-lg font-medium">法大大服务状态</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border rounded-md p-4">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        <span className="font-medium">电子合同功能已开通</span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        您的账户已开通法大大电子合同服务，可以创建和管理电子合同。
                      </p>
                    </div>
                    <div className="border rounded-md p-4">
                      <div className="flex items-center space-x-2">
                        <FileSignature className="h-5 w-5 text-blue-500" />
                        <span className="font-medium">电子签章已设置</span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        您已上传电子签章，可以直接签署电子合同。
                      </p>
                    </div>
                  </div>
                </div>

                {/* 合同模板 */}
                <div className="mt-6 space-y-4">
                  <h3 className="text-lg font-medium">可用合同模板</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="border rounded-md p-4 hover:border-primary cursor-pointer transition-colors">
                      <div className="flex flex-col items-center text-center">
                        <FileText className="h-8 w-8 text-blue-500 mb-2" />
                        <span className="font-medium">会员卡购买协议</span>
                        <p className="text-xs text-muted-foreground mt-1">
                          适用于会员购买会员卡时签署
                        </p>
                      </div>
                    </div>
                    <div className="border rounded-md p-4 hover:border-primary cursor-pointer transition-colors">
                      <div className="flex flex-col items-center text-center">
                        <FileText className="h-8 w-8 text-green-500 mb-2" />
                        <span className="font-medium">会员卡使用条款</span>
                        <p className="text-xs text-muted-foreground mt-1">
                          适用于会员卡激活时签署
                        </p>
                      </div>
                    </div>
                    <div className="border rounded-md p-4 hover:border-primary cursor-pointer transition-colors">
                      <div className="flex flex-col items-center text-center">
                        <FileText className="h-8 w-8 text-purple-500 mb-2" />
                        <span className="font-medium">会员卡转让协议</span>
                        <p className="text-xs text-muted-foreground mt-1">
                          适用于会员卡转让时签署
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => window.location.href = "/premium-services/e-contract"}>
                  <Settings className="mr-2 h-4 w-4" />
                  管理电子合同设置
                </Button>
                <Button>
                  <FileSignature className="mr-2 h-4 w-4" />
                  创建新合同
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 操作对话框 */}
        {dialogType === "freeze" && (
          <Dialog open={true} onOpenChange={() => setDialogType(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>冻结会员卡</DialogTitle>
                <DialogDescription>
                  冻结后，会员卡将暂时无法使用，但有效期将会延长相应的冻结天数。
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label htmlFor="freezeReason">冻结原因</Label>
                  <Textarea
                    id="freezeReason"
                    placeholder="请输入冻结原因"
                    value={dialogNote}
                    onChange={(e) => setDialogNote(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
                <Button onClick={() => {
                  toast({
                    title: "会员卡已冻结",
                    description: `${card.cardType} 已成功冻结`,
                  })
                  setDialogType(null)
                }}>确认冻结</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {dialogType === "unfreeze" && (
          <Dialog open={true} onOpenChange={() => setDialogType(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>解冻会员卡</DialogTitle>
                <DialogDescription>
                  解冻后，会员卡将恢复正常使用，有效期将自动延长冻结的天数。
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label htmlFor="unfreezeNote">备注</Label>
                  <Textarea
                    id="unfreezeNote"
                    placeholder="请输入备注信息（可选）"
                    value={dialogNote}
                    onChange={(e) => setDialogNote(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
                <Button onClick={() => {
                  toast({
                    title: "会员卡已解冻",
                    description: `${card.cardType} 已成功解冻`,
                  })
                  setDialogType(null)
                }}>确认解冻</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {dialogType === "leave" && (
          <Dialog open={true} onOpenChange={() => setDialogType(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>会员请假</DialogTitle>
                <DialogDescription>
                  请假期间，会员卡将暂停计时，到期日期将自动延长请假天数。
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-2">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="leaveStartDate">开始日期</Label>
                    <Input
                      id="leaveStartDate"
                      type="date"
                      defaultValue={new Date().toISOString().split('T')[0]}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="leaveDays">请假天数</Label>
                    <Input
                      id="leaveDays"
                      type="number"
                      min="1"
                      defaultValue="7"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="leaveReason">请假原因</Label>
                  <Textarea
                    id="leaveReason"
                    placeholder="请输入请假原因"
                    value={dialogNote}
                    onChange={(e) => setDialogNote(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
                <Button onClick={() => {
                  toast({
                    title: "请假申请已提交",
                    description: `${card.cardType} 已成功设置为请假状态`,
                  })
                  setDialogType(null)
                }}>确认请假</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {dialogType === "extend" && (
          <Dialog open={true} onOpenChange={() => setDialogType(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>延长有效期</DialogTitle>
                <DialogDescription>
                  为会员卡延长有效期，不影响其他使用条件。
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-2">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="extendDays">延长天数</Label>
                    <Input
                      id="extendDays"
                      type="number"
                      min="1"
                      defaultValue="30"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="extendFee">收费金额</Label>
                    <Input
                      id="extendFee"
                      type="number"
                      min="0"
                      defaultValue="0"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="extendReason">延期原因</Label>
                  <Textarea
                    id="extendReason"
                    placeholder="请输入延期原因"
                    value={dialogNote}
                    onChange={(e) => setDialogNote(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
                <Button onClick={() => {
                  toast({
                    title: "有效期已延长",
                    description: `${card.cardType} 的有效期已成功延长`,
                  })
                  setDialogType(null)
                }}>确认延长</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {dialogType === "transfer" && (
          <Dialog open={true} onOpenChange={() => setDialogType(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>转让会员卡</DialogTitle>
                <DialogDescription>
                  将会员卡转让给其他会员，转让后原会员将无法使用此卡。
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label htmlFor="transferTarget">转让对象</Label>
                  <Input
                    id="transferTarget"
                    placeholder="请输入会员姓名或手机号"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="transferFee">转让费用</Label>
                  <Input
                    id="transferFee"
                    type="number"
                    min="0"
                    defaultValue="0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="transferReason">转让原因</Label>
                  <Textarea
                    id="transferReason"
                    placeholder="请输入转让原因"
                    value={dialogNote}
                    onChange={(e) => setDialogNote(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
                <Button onClick={() => {
                  toast({
                    title: "转让申请已提交",
                    description: `${card.cardType} 转让申请已提交，等待审核`,
                  })
                  setDialogType(null)
                }}>确认转让</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {dialogType === "refund" && (
          <Dialog open={true} onOpenChange={() => setDialogType(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>退卡申请</DialogTitle>
                <DialogDescription>
                  申请退还会员卡，退卡后会员将无法使用此卡的任何权益。
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-2">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="refundAmount">退款金额</Label>
                    <Input
                      id="refundAmount"
                      type="number"
                      min="0"
                      max={card.actualPrice}
                      defaultValue={Math.round(card.actualPrice * 0.7)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="refundMethod">退款方式</Label>
                    <Select defaultValue="original">
                      <SelectTrigger id="refundMethod">
                        <SelectValue placeholder="选择退款方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="original">原路退回</SelectItem>
                        <SelectItem value="wechat">微信支付</SelectItem>
                        <SelectItem value="alipay">支付宝</SelectItem>
                        <SelectItem value="cash">现金</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="refundReason">退卡原因</Label>
                  <Textarea
                    id="refundReason"
                    placeholder="请输入退卡原因"
                    value={dialogNote}
                    onChange={(e) => setDialogNote(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
                <Button variant="destructive" onClick={() => {
                  toast({
                    title: "退卡申请已提交",
                    description: `${card.cardType} 退卡申请已提交，等待审核`,
                  })
                  setDialogType(null)
                }}>确认退卡</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  )
}
