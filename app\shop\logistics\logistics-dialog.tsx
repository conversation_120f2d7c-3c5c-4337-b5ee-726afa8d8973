import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { TrackingDetail } from "./tracking-detail"

interface ShipmentRecord {
  id: string
  orderId: string
  customerName: string
  trackingNumber: string
  logisticsCompany: string
  shipDate: string
  status: string
  address: string
  products: string[]
  remark: string
}

interface LogisticsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  shipment: ShipmentRecord | null
}

export function LogisticsDialog({ open, onOpenChange, shipment }: LogisticsDialogProps) {
  const [activeTab, setActiveTab] = useState("tracking")
  const [trackingNumber, setTrackingNumber] = useState(shipment?.trackingNumber || "")
  const [logisticsCompany, setLogisticsCompany] = useState(shipment?.logisticsCompany || "")
  const [remark, setRemark] = useState(shipment?.remark || "")
  const [status, setStatus] = useState(shipment?.status || "")

  // 模拟物流公司列表
  const logisticsCompanies = [
    { id: "1", name: "顺丰速运" },
    { id: "2", name: "圆通速递" },
    { id: "3", name: "中通快递" },
    { id: "4", name: "申通快递" },
    { id: "5", name: "韵达快递" },
    { id: "6", name: "京东物流" },
  ]

  // 模拟更新物流信息
  const handleUpdateLogistics = () => {
    console.log("更新物流信息:", {
      id: shipment?.id,
      trackingNumber,
      logisticsCompany,
      status,
      remark,
    })
    onOpenChange(false)
  }

  if (!shipment) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>物流详情 - {shipment.id}</DialogTitle>
          <DialogDescription>
            查看和管理订单 {shipment.orderId} 的物流信息
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="tracking">物流跟踪</TabsTrigger>
            <TabsTrigger value="update">更新物流</TabsTrigger>
          </TabsList>

          <TabsContent value="tracking" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium mb-2">订单信息</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单号:</span>
                    <span className="font-medium">{shipment.orderId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">客户名称:</span>
                    <span>{shipment.customerName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">发货时间:</span>
                    <span>{shipment.shipDate}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium mb-2">收货地址</h3>
                <p className="text-sm">{shipment.address}</p>
                
                <h3 className="text-sm font-medium mt-4 mb-2">商品信息</h3>
                <ul className="text-sm space-y-1">
                  {shipment.products.map((product, index) => (
                    <li key={index}>{product}</li>
                  ))}
                </ul>
              </div>
            </div>

            <TrackingDetail 
              trackingNumber={shipment.trackingNumber}
              logisticsCompany={shipment.logisticsCompany}
              shipDate={shipment.shipDate}
              status={shipment.status}
            />
          </TabsContent>

          <TabsContent value="update" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="logistics-company">物流公司</Label>
                  <Select 
                    value={logisticsCompany} 
                    onValueChange={setLogisticsCompany}
                  >
                    <SelectTrigger id="logistics-company">
                      <SelectValue placeholder="选择物流公司" />
                    </SelectTrigger>
                    <SelectContent>
                      {logisticsCompanies.map((company) => (
                        <SelectItem key={company.id} value={company.name}>
                          {company.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tracking-number">物流单号</Label>
                  <Input
                    id="tracking-number"
                    value={trackingNumber}
                    onChange={(e) => setTrackingNumber(e.target.value)}
                    placeholder="输入物流单号"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">物流状态</Label>
                  <Select value={status} onValueChange={setStatus}>
                    <SelectTrigger id="status">
                      <SelectValue placeholder="选择物流状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">待发货</SelectItem>
                      <SelectItem value="shipping">配送中</SelectItem>
                      <SelectItem value="delivered">已送达</SelectItem>
                      <SelectItem value="returned">已退回</SelectItem>
                      <SelectItem value="exception">异常</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="remark">备注</Label>
                  <Textarea
                    id="remark"
                    value={remark}
                    onChange={(e) => setRemark(e.target.value)}
                    placeholder="添加物流备注信息"
                    className="min-h-[120px]"
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
          {activeTab === "update" && (
            <Button onClick={handleUpdateLogistics}>
              更新物流信息
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
