"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { useState } from "react"

interface AdvancedFilterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onApplyFilter?: (filters: any) => void
}

export function AdvancedFilterDialog({ open, onOpenChange, onApplyFilter }: AdvancedFilterDialogProps) {
  // 筛选状态
  const [statusFilters, setStatusFilters] = useState({
    published: true,
    pending: true,
    hidden: true,
  })
  const [selectedCoach, setSelectedCoach] = useState("all")
  const [selectedCourseType, setSelectedCourseType] = useState("all")
  const [ratingRange, setRatingRange] = useState([3, 5])
  const [dateFrom, setDateFrom] = useState("")
  const [dateTo, setDateTo] = useState("")
  const [hasReply, setHasReply] = useState<boolean | null>(null)
  const [keywords, setKeywords] = useState("")
  const [sentimentFilter, setSentimentFilter] = useState("all")

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
          <DialogDescription>设置更多筛选条件以查找特定评价</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
          <div className="space-y-2">
            <Label>评价状态</Label>
            <div className="grid grid-cols-3 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-published"
                  checked={statusFilters.published}
                  onCheckedChange={(checked) =>
                    setStatusFilters({...statusFilters, published: !!checked})
                  }
                />
                <Label htmlFor="status-published" className="font-normal">
                  已发布
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-pending"
                  checked={statusFilters.pending}
                  onCheckedChange={(checked) =>
                    setStatusFilters({...statusFilters, pending: !!checked})
                  }
                />
                <Label htmlFor="status-pending" className="font-normal">
                  待审核
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="status-hidden"
                  checked={statusFilters.hidden}
                  onCheckedChange={(checked) =>
                    setStatusFilters({...statusFilters, hidden: !!checked})
                  }
                />
                <Label htmlFor="status-hidden" className="font-normal">
                  已隐藏
                </Label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="coach">教练</Label>
            <Select value={selectedCoach} onValueChange={setSelectedCoach}>
              <SelectTrigger id="coach">
                <SelectValue placeholder="选择教练" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有教练</SelectItem>
                <SelectItem value="1">张教练</SelectItem>
                <SelectItem value="2">李教练</SelectItem>
                <SelectItem value="3">王教练</SelectItem>
                <SelectItem value="4">赵教练</SelectItem>
                <SelectItem value="5">刘教练</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="course-type">课程类型</Label>
            <Select value={selectedCourseType} onValueChange={setSelectedCourseType}>
              <SelectTrigger id="course-type">
                <SelectValue placeholder="选择课程类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                <SelectItem value="1">基础瑜伽</SelectItem>
                <SelectItem value="2">高级瑜伽</SelectItem>
                <SelectItem value="3">阴瑜伽</SelectItem>
                <SelectItem value="4">孕产瑜伽</SelectItem>
                <SelectItem value="5">空中瑜伽</SelectItem>
                <SelectItem value="6">理疗瑜伽</SelectItem>
                <SelectItem value="7">热瑜伽</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>评分范围</Label>
            <div className="px-2">
              <Slider
                value={ratingRange}
                max={5}
                min={1}
                step={0.5}
                onValueChange={setRatingRange}
              />
              <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                <span>{ratingRange[0]}</span>
                <span>{ratingRange[1]}</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="date-from">评价日期</Label>
            <div className="flex items-center gap-2">
              <Input
                id="date-from"
                type="date"
                className="w-1/2"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
              />
              <span>至</span>
              <Input
                id="date-to"
                type="date"
                className="w-1/2"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>回复状态</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="has-reply"
                  checked={hasReply === true}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setHasReply(true)
                    } else {
                      setHasReply(null)
                    }
                  }}
                />
                <Label htmlFor="has-reply" className="font-normal">
                  已回复
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="no-reply"
                  checked={hasReply === false}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setHasReply(false)
                    } else {
                      setHasReply(null)
                    }
                  }}
                />
                <Label htmlFor="no-reply" className="font-normal">
                  未回复
                </Label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="sentiment">情感分析</Label>
            <Select value={sentimentFilter} onValueChange={setSentimentFilter}>
              <SelectTrigger id="sentiment">
                <SelectValue placeholder="情感分析" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有情感</SelectItem>
                <SelectItem value="positive">正面评价</SelectItem>
                <SelectItem value="neutral">中性评价</SelectItem>
                <SelectItem value="negative">负面评价</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="keywords">关键词</Label>
            <Input
              id="keywords"
              placeholder="输入关键词，多个关键词用空格分隔"
              value={keywords}
              onChange={(e) => setKeywords(e.target.value)}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            取消
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              setStatusFilters({
                published: true,
                pending: true,
                hidden: true,
              })
              setSelectedCoach("all")
              setSelectedCourseType("all")
              setRatingRange([3, 5])
              setDateFrom("")
              setDateTo("")
              setHasReply(null)
              setKeywords("")
              setSentimentFilter("all")
            }}
          >
            重置
          </Button>
          <Button
            onClick={() => {
              const filters = {
                statusFilters,
                selectedCoach,
                selectedCourseType,
                ratingRange,
                dateFrom,
                dateTo,
                hasReply,
                keywords,
                sentimentFilter,
              }

              if (onApplyFilter) {
                onApplyFilter(filters)
              } else {
                onOpenChange(false)
              }
            }}
          >
            应用筛选
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

