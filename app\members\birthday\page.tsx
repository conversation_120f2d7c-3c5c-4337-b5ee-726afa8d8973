"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Search, 
  Calendar, 
  Gift, 
  Phone,
  Mail,
  MessageSquare,
  MoreHorizontal,
  CheckCircle,
  Bell,
  Send,
  Cake,
  CalendarDays,
  ChevronLeft,
  ChevronRight,
  Heart
} from "lucide-react"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { addDays, format, differenceInDays, isSameMonth, isSameDay } from "date-fns"

// 模拟会员生日数据
const birthdayMembers = [
  {
    id: "M001",
    name: "张三",
    avatar: "/avatars/01.png",
    phone: "13800138001",
    email: "<EMAIL>",
    birthday: "1990-06-15",
    daysUntilBirthday: 5,
    memberSince: "2022-03-10",
    membershipType: "瑜伽年卡",
    visitFrequency: "高",
    spendAmount: 3680,
    tags: ["高频用户", "VIP会员"],
    status: "未发送",
    lastGift: "2022年生日优惠券"
  },
  {
    id: "M002",
    name: "李四",
    avatar: "/avatars/02.png",
    phone: "13800138002",
    email: "<EMAIL>",
    birthday: "1985-06-10",
    daysUntilBirthday: 0,
    memberSince: "2021-05-15",
    membershipType: "瑜伽季卡",
    visitFrequency: "中",
    spendAmount: 1580,
    tags: ["中频用户"],
    status: "已发送",
    lastGift: "2022年生日礼品"
  },
  {
    id: "M003",
    name: "王五",
    avatar: "/avatars/03.png",
    phone: "13800138003",
    email: "<EMAIL>",
    birthday: "1995-06-08",
    daysUntilBirthday: -2,
    memberSince: "2023-01-20",
    membershipType: "瑜伽月卡",
    visitFrequency: "高",
    spendAmount: 680,
    tags: ["高频用户", "新会员"],
    status: "已发送",
    lastGift: "2023年生日免费课程"
  },
  {
    id: "M004",
    name: "赵六",
    avatar: "/avatars/04.png",
    phone: "13800138004",
    email: "<EMAIL>",
    birthday: "1988-06-20",
    daysUntilBirthday: 10,
    memberSince: "2022-08-05",
    membershipType: "瑜伽年卡",
    visitFrequency: "低",
    spendAmount: 3680,
    tags: ["低频用户"],
    status: "未发送",
    lastGift: "2022年生日优惠券"
  },
  {
    id: "M005",
    name: "孙七",
    avatar: "/avatars/05.png",
    phone: "13800138005",
    email: "<EMAIL>",
    birthday: "1992-06-12",
    daysUntilBirthday: 2,
    memberSince: "2021-11-15",
    membershipType: "瑜伽季卡",
    visitFrequency: "中",
    spendAmount: 1580,
    tags: ["中频用户"],
    status: "未发送",
    lastGift: "2022年生日礼品"
  },
  {
    id: "M006",
    name: "周八",
    avatar: "/avatars/06.png",
    phone: "13800138006",
    email: "<EMAIL>",
    birthday: "1993-07-05",
    daysUntilBirthday: 25,
    memberSince: "2022-04-18",
    membershipType: "瑜伽年卡",
    visitFrequency: "高",
    spendAmount: 3680,
    tags: ["高频用户"],
    status: "未发送",
    lastGift: "2022年生日优惠券"
  }
];

// 模拟生日礼品模板
const birthdayGiftTemplates = [
  {
    id: "G001",
    name: "生日专属优惠券",
    description: "赠送会员生日当月专属优惠券，可用于购买会员卡或课程。",
    type: "优惠券",
    value: "200元",
    validPeriod: "30天",
    image: "/gifts/coupon.png"
  },
  {
    id: "G002",
    name: "生日免费体验课",
    description: "赠送会员生日当月免费体验高级课程一次。",
    type: "课程",
    value: "1次课",
    validPeriod: "30天",
    image: "/gifts/class.png"
  },
  {
    id: "G003",
    name: "生日礼品套装",
    description: "精美瑜伽周边礼品套装，包含瑜伽袜、毛巾等实用物品。",
    type: "实物",
    value: "礼品套装",
    validPeriod: "需到店领取",
    image: "/gifts/set.png"
  },
  {
    id: "G004",
    name: "会员卡延期",
    description: "会员卡免费延期7天，适用于所有类型会员卡。",
    type: "延期",
    value: "7天",
    validPeriod: "自动延期",
    image: "/gifts/extension.png"
  }
];

export default function MemberBirthdayPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("this-month")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMembers, setSelectedMembers] = useState<string[]>([])
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedGiftTemplate, setSelectedGiftTemplate] = useState<string | null>(null)
  const [customMessage, setCustomMessage] = useState("亲爱的会员，祝您生日快乐！我们为您准备了专属生日礼物，期待您的到来！")
  const [currentMonth, setCurrentMonth] = useState(new Date())

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "未发送":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">未发送</Badge>
      case "已发送":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已发送</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取生日倒计时样式
  const getBirthdayCountdownStyle = (days: number) => {
    if (days === 0) {
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">今天</Badge>
    } else if (days < 0) {
      return <Badge variant="outline" className="bg-gray-50 text-gray-700 hover:bg-gray-50 border-gray-200">{Math.abs(days)}天前</Badge>
    } else if (days <= 7) {
      return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">{days}天后</Badge>
    } else {
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">{days}天后</Badge>
    }
  }

  // 过滤会员
  const filteredMembers = birthdayMembers.filter(member => {
    // 基本搜索过滤
    const searchFilter = 
      searchQuery === "" || 
      member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.phone.includes(searchQuery);
    
    // 标签页过滤
    const birthdayDate = new Date(member.birthday);
    const tabFilter = 
      (activeTab === "this-month" && isSameMonth(birthdayDate, currentMonth)) || 
      (activeTab === "today" && member.daysUntilBirthday === 0) ||
      (activeTab === "upcoming" && member.daysUntilBirthday > 0 && member.daysUntilBirthday <= 30) ||
      (activeTab === "all");
    
    // 状态过滤
    const statusFilterResult = 
      statusFilter === "all" || 
      member.status === statusFilter;
    
    return searchFilter && tabFilter && statusFilterResult;
  });

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedMembers(filteredMembers.map(member => member.id));
    } else {
      setSelectedMembers([]);
    }
  };

  // 处理批量操作
  const handleBatchOperation = (operation: string) => {
    console.log(`对会员 ${selectedMembers.join(", ")} 执行操作: ${operation}`);
    // 实际应用中，这里会执行相应的批量操作
    alert(`已对 ${selectedMembers.length} 个会员执行 ${operation} 操作`);
    setSelectedMembers([]);
  };

  // 处理月份切换
  const handleMonthChange = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    setCurrentMonth(newMonth);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">会员生日提醒</h1>
          <p className="text-muted-foreground">
            管理会员生日提醒和生日礼品发送
          </p>
        </div>
        <div className="flex items-center gap-2">
          {selectedMembers.length > 0 ? (
            <>
              <Select value={selectedGiftTemplate || ""} onValueChange={setSelectedGiftTemplate}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="选择生日礼品" />
                </SelectTrigger>
                <SelectContent>
                  {birthdayGiftTemplates.map(template => (
                    <SelectItem key={template.id} value={template.id}>{template.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button 
                variant="outline" 
                onClick={() => handleBatchOperation("发送生日祝福")}
                disabled={!selectedGiftTemplate}
              >
                <Gift className="h-4 w-4 mr-2" />
                发送祝福 ({selectedMembers.length})
              </Button>
              <Button variant="outline" onClick={() => handleBatchOperation("标记为已发送")}>
                <CheckCircle className="h-4 w-4 mr-2" />
                标记为已发送
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={() => router.push('/marketing/templates')}>
                <MessageSquare className="h-4 w-4 mr-2" />
                管理祝福模板
              </Button>
              <Button>
                <Gift className="h-4 w-4 mr-2" />
                创建生日活动
              </Button>
            </>
          )}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>生日会员概览</CardTitle>
              <CardDescription>
                {format(currentMonth, 'yyyy年MM月')}有 {birthdayMembers.filter(m => {
                  const birthdayDate = new Date(m.birthday);
                  return isSameMonth(birthdayDate, currentMonth);
                }).length} 位会员生日
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="icon" onClick={() => handleMonthChange('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <div className="font-medium">
                {format(currentMonth, 'yyyy年MM月')}
              </div>
              <Button variant="outline" size="icon" onClick={() => handleMonthChange('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">本月生日</CardTitle>
                <Cake className="h-4 w-4 text-pink-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{birthdayMembers.filter(m => {
                  const birthdayDate = new Date(m.birthday);
                  return isSameMonth(birthdayDate, currentMonth);
                }).length}</div>
                <p className="text-xs text-muted-foreground">
                  本月生日的会员数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">今日生日</CardTitle>
                <Gift className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{birthdayMembers.filter(m => m.daysUntilBirthday === 0).length}</div>
                <p className="text-xs text-muted-foreground">
                  今天生日的会员数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">未发送祝福</CardTitle>
                <Bell className="h-4 w-4 text-amber-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{birthdayMembers.filter(m => {
                  const birthdayDate = new Date(m.birthday);
                  return isSameMonth(birthdayDate, currentMonth) && m.status === "未发送";
                }).length}</div>
                <p className="text-xs text-muted-foreground">
                  未发送生日祝福的会员数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已发送祝福</CardTitle>
                <Heart className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{birthdayMembers.filter(m => {
                  const birthdayDate = new Date(m.birthday);
                  return isSameMonth(birthdayDate, currentMonth) && m.status === "已发送";
                }).length}</div>
                <p className="text-xs text-muted-foreground">
                  已发送生日祝福的会员数量
                </p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 max-w-md">
          <TabsTrigger value="this-month">本月生日</TabsTrigger>
          <TabsTrigger value="today">今日生日</TabsTrigger>
          <TabsTrigger value="upcoming">即将生日</TabsTrigger>
          <TabsTrigger value="all">全部会员</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative w-full md:w-1/3">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索会员姓名或手机号"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-1 gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="发送状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="未发送">未发送</SelectItem>
              <SelectItem value="已发送">已发送</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <Checkbox 
                    checked={selectedMembers.length > 0 && selectedMembers.length === filteredMembers.length}
                    onCheckedChange={handleSelectAll}
                    aria-label="全选"
                  />
                </TableHead>
                <TableHead>会员信息</TableHead>
                <TableHead>生日</TableHead>
                <TableHead>会员类型</TableHead>
                <TableHead>上次礼品</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredMembers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    没有找到符合条件的会员
                  </TableCell>
                </TableRow>
              ) : (
                filteredMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedMembers.includes(member.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedMembers([...selectedMembers, member.id]);
                          } else {
                            setSelectedMembers(selectedMembers.filter(id => id !== member.id));
                          }
                        }}
                        aria-label={`选择会员 ${member.name}`}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-9 w-9">
                          <AvatarImage src={member.avatar} alt={member.name} />
                          <AvatarFallback>{member.name.slice(0, 1)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-xs text-muted-foreground flex items-center gap-2">
                            <span className="flex items-center"><Phone className="h-3 w-3 mr-1" />{member.phone}</span>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{member.birthday}</div>
                      <div className="text-xs">
                        {getBirthdayCountdownStyle(member.daysUntilBirthday)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{member.membershipType}</div>
                      <div className="text-xs text-muted-foreground">
                        会员{differenceInDays(new Date(), new Date(member.memberSince))}天
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{member.lastGift || "无"}</div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(member.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>会员操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => router.push(`/members/${member.id}`)}>
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            发送生日祝福
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            标记为已发送
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            设置提醒
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {selectedMembers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>发送生日祝福</CardTitle>
            <CardDescription>
              为选中的 {selectedMembers.length} 位会员发送生日祝福和礼品
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <div className="font-medium mb-2">选择生日礼品</div>
                <div className="grid grid-cols-2 gap-3">
                  {birthdayGiftTemplates.map(template => (
                    <Card 
                      key={template.id} 
                      className={`cursor-pointer hover:border-primary transition-colors ${selectedGiftTemplate === template.id ? 'border-primary' : ''}`}
                      onClick={() => setSelectedGiftTemplate(template.id)}
                    >
                      <CardHeader className="p-3">
                        <CardTitle className="text-sm">{template.name}</CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 pt-0">
                        <p className="text-xs text-muted-foreground">{template.description}</p>
                        <div className="mt-2 text-xs">
                          <Badge variant="outline">{template.type}</Badge>
                          <span className="ml-2">{template.value}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
              <div>
                <div className="font-medium mb-2">自定义祝福语</div>
                <Textarea 
                  value={customMessage} 
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="输入自定义祝福语"
                  className="h-[150px]"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setSelectedMembers([])}>
              取消
            </Button>
            <Button onClick={() => handleBatchOperation("发送生日祝福")}>
              <Send className="h-4 w-4 mr-2" />
              发送生日祝福
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  )
}
