"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { QrCode, Search, AlertCircle, RefreshCw, CreditCard } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function UnionPayPage() {
  const [amount, setAmount] = useState("")
  const [description, setDescription] = useState("")
  const [qrCodeGenerated, setQrCodeGenerated] = useState(false)
  const [cardNumber, setCardNumber] = useState("")
  const [expiryDate, setExpiryDate] = useState("")
  const [cvv, setCvv] = useState("")
  const [cardholderName, setCardholderName] = useState("")

  const [recentPayments] = useState([
    {
      id: "UP20250328123456",
      amount: "¥199.00",
      description: "基础瑜伽月卡",
      time: "2025-03-28 14:30:25",
      status: "success",
      type: "扫码支付",
    },
    {
      id: "UP20250328234567",
      amount: "¥299.00",
      description: "高级瑜伽季卡",
      time: "2025-03-28 11:15:42",
      status: "success",
      type: "网关支付",
    },
    {
      id: "UP20250328345678",
      amount: "¥99.00",
      description: "单次体验课",
      time: "2025-03-28 09:45:18",
      status: "pending",
      type: "扫码支付",
    },
  ])

  const handleGenerateQRCode = () => {
    if (!amount || Number.parseFloat(amount) <= 0) {
      alert("请输入有效的金额")
      return
    }

    setQrCodeGenerated(true)
  }

  const handleCardPayment = () => {
    if (!cardNumber || !expiryDate || !cvv || !cardholderName) {
      alert("请填写完整的银行卡信息")
      return
    }

    alert("银行卡支付请求已提交")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">银联支付</h1>
      </div>

      <Tabs defaultValue="qrcode" className="space-y-4">
        <TabsList>
          <TabsTrigger value="qrcode">扫码支付</TabsTrigger>
          <TabsTrigger value="gateway">网关支付</TabsTrigger>
          <TabsTrigger value="notification">支付结果通知</TabsTrigger>
          <TabsTrigger value="query">支付状态查询</TabsTrigger>
          <TabsTrigger value="refund">退款处理</TabsTrigger>
        </TabsList>

        <TabsContent value="qrcode" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>银联扫码支付</CardTitle>
              <CardDescription>生成银联支付二维码供用户扫码支付</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="unionpay-amount">支付金额（元）</Label>
                    <Input
                      id="unionpay-amount"
                      placeholder="请输入金额"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="unionpay-description">商品描述</Label>
                    <Input
                      id="unionpay-description"
                      placeholder="请输入商品描述"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="unionpay-bank">银行选择</Label>
                    <Select defaultValue="all">
                      <SelectTrigger id="unionpay-bank">
                        <SelectValue placeholder="选择银行" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部银行</SelectItem>
                        <SelectItem value="icbc">工商银行</SelectItem>
                        <SelectItem value="abc">农业银行</SelectItem>
                        <SelectItem value="boc">中国银行</SelectItem>
                        <SelectItem value="ccb">建设银行</SelectItem>
                        <SelectItem value="cmb">招商银行</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button onClick={handleGenerateQRCode}>生成支付二维码</Button>
                </div>

                <div className="flex flex-col items-center justify-center">
                  {qrCodeGenerated ? (
                    <div className="text-center">
                      <div className="border-4 border-[#D7000F] p-4 inline-block mb-4">
                        <QrCode className="h-48 w-48 text-[#D7000F]" />
                      </div>
                      <div className="text-lg font-medium">¥{amount}</div>
                      <div className="text-sm text-muted-foreground">{description || "银联扫码支付"}</div>
                      <Button variant="outline" className="mt-4" onClick={() => setQrCodeGenerated(false)}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        重新生成
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <QrCode className="h-48 w-48 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">请输入金额并点击生成支付二维码</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>最近支付记录</CardTitle>
              <CardDescription>显示最近的银联支付记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>交易单号</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>商品描述</TableHead>
                    <TableHead>支付方式</TableHead>
                    <TableHead>支付时间</TableHead>
                    <TableHead>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentPayments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">{payment.id}</TableCell>
                      <TableCell>{payment.amount}</TableCell>
                      <TableCell>{payment.description}</TableCell>
                      <TableCell>{payment.type}</TableCell>
                      <TableCell>{payment.time}</TableCell>
                      <TableCell>
                        <Badge variant={payment.status === "success" ? "default" : "outline"}>
                          {payment.status === "success" ? "支付成功" : "待支付"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="gateway" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>银联网关支付</CardTitle>
              <CardDescription>使用银联网关进行支付</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="gateway-amount">支付金额（元）</Label>
                    <Input id="gateway-amount" placeholder="请输入金额" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="gateway-description">商品描述</Label>
                    <Input id="gateway-description" placeholder="请输入商品描述" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="card-number">银行卡号</Label>
                    <Input
                      id="card-number"
                      placeholder="请输入银行卡号"
                      value={cardNumber}
                      onChange={(e) => setCardNumber(e.target.value)}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="expiry-date">有效期 (MM/YY)</Label>
                      <Input
                        id="expiry-date"
                        placeholder="MM/YY"
                        value={expiryDate}
                        onChange={(e) => setExpiryDate(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cvv">安全码</Label>
                      <Input id="cvv" placeholder="CVV" value={cvv} onChange={(e) => setCvv(e.target.value)} />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cardholder-name">持卡人姓名</Label>
                    <Input
                      id="cardholder-name"
                      placeholder="请输入持卡人姓名"
                      value={cardholderName}
                      onChange={(e) => setCardholderName(e.target.value)}
                    />
                  </div>

                  <Button onClick={handleCardPayment}>确认支付</Button>
                </div>

                <div className="flex flex-col items-center justify-center border rounded-md p-4">
                  <CreditCard className="h-24 w-24 text-[#D7000F] mb-4" />
                  <h3 className="font-medium text-lg mb-2">银联网关支付</h3>
                  <p className="text-sm text-muted-foreground mb-4 text-center">请填写银行卡信息完成支付</p>
                  <div className="flex gap-2 justify-center">
                    <img src="/placeholder.svg?height=30&width=40" alt="银联" className="h-8" />
                    <img src="/placeholder.svg?height=30&width=40" alt="工商银行" className="h-8" />
                    <img src="/placeholder.svg?height=30&width=40" alt="农业银行" className="h-8" />
                    <img src="/placeholder.svg?height=30&width=40" alt="中国银行" className="h-8" />
                    <img src="/placeholder.svg?height=30&width=40" alt="建设银行" className="h-8" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notification" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付结果通知</CardTitle>
              <CardDescription>接收银联支付结果通知</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="unionpay-notify-url">通知URL</Label>
                <Input
                  id="unionpay-notify-url"
                  value="https://youryogastudio.com/api/unionpay/payment/notify"
                  readOnly
                />
                <p className="text-sm text-muted-foreground">请在银联商户平台设置此通知URL</p>
              </div>

              <div className="space-y-2">
                <Label>最近通知记录</Label>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>通��时间</TableHead>
                      <TableHead>交易单号</TableHead>
                      <TableHead>金额</TableHead>
                      <TableHead>状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>2025-03-28 14:30:28</TableCell>
                      <TableCell>UP20250328123456</TableCell>
                      <TableCell>¥199.00</TableCell>
                      <TableCell>
                        <Badge>支付成功</Badge>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-28 11:15:45</TableCell>
                      <TableCell>UP20250328234567</TableCell>
                      <TableCell>¥299.00</TableCell>
                      <TableCell>
                        <Badge>支付成功</Badge>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="query" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付状态查询</CardTitle>
              <CardDescription>查询银联交易状态</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input placeholder="输入交易单号或商户订单号" />
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <div className="flex items-center justify-center p-8 border rounded-lg">
                <div className="text-center">
                  <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="font-medium text-lg mb-2">暂无查询结果</h3>
                  <p className="text-sm text-muted-foreground">请输入交易单号并点击查询按钮</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="refund" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>退款处理</CardTitle>
              <CardDescription>处理银联支付退款</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="unionpay-transaction-id">交易单号</Label>
                    <Input id="unionpay-transaction-id" placeholder="请输入银联交易单号" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="unionpay-refund-amount">退款金额（元）</Label>
                    <Input id="unionpay-refund-amount" placeholder="请输入退款金额" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="unionpay-refund-reason">退款原因</Label>
                    <Input id="unionpay-refund-reason" placeholder="请输入退款原因" />
                  </div>

                  <Button>申请退款</Button>
                </div>

                <div>
                  <h3 className="font-medium mb-2">退款记录</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>退款单号</TableHead>
                        <TableHead>金额</TableHead>
                        <TableHead>状态</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>UP-RF20250327001</TableCell>
                        <TableCell>¥99.00</TableCell>
                        <TableCell>
                          <Badge>退款成功</Badge>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>UP-RF20250326002</TableCell>
                        <TableCell>¥199.00</TableCell>
                        <TableCell>
                          <Badge variant="outline">处理中</Badge>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

