"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { QrCode, Search, Check, X, AlertCircle } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export default function DouyinVerificationPage() {
  const [verificationCode, setVerificationCode] = useState("")
  const [verificationResult, setVerificationResult] = useState<null | {
    success: boolean
    message: string
    data?: {
      code: string
      product: string
      price: string
      validUntil: string
      customer?: {
        name: string
        phone: string
        avatar: string
      }
    }
  }>(null)

  const [recentVerifications] = useState([
    {
      id: "**********",
      code: "**********",
      product: "空中瑜伽体验课（60分钟）",
      price: "¥129",
      status: "success",
      verifiedAt: "2025-03-28 15:30:25",
      customer: {
        name: "赵六",
        phone: "136****3456",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "DY23456789",
      code: "DY23456789",
      product: "阴瑜伽单次课（90分钟）",
      price: "¥119",
      status: "success",
      verifiedAt: "2025-03-28 12:15:42",
      customer: {
        name: "钱七",
        phone: "135****7890",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "**********",
      code: "**********",
      product: "瑜伽会员月卡",
      price: "¥599",
      status: "failed",
      verifiedAt: "2025-03-28 10:45:18",
      customer: {
        name: "孙八",
        phone: "134****1234",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
  ])

  const handleVerification = () => {
    // 模拟核销验证
    if (verificationCode.trim() === "") {
      setVerificationResult({
        success: false,
        message: "请输入有效的券码",
      })
      return
    }

    // 模拟成功核销
    if (verificationCode === "**********") {
      setVerificationResult({
        success: true,
        message: "核销成功",
        data: {
          code: "**********",
          product: "空中瑜伽体验课（60分钟）",
          price: "¥129",
          validUntil: "2025-04-30",
          customer: {
            name: "赵六",
            phone: "136****3456",
            avatar: "/placeholder.svg?height=32&width=32",
          },
        },
      })
    } else {
      // 模拟失败核销
      setVerificationResult({
        success: false,
        message: "券码无效或已被使用",
      })
    }
  }

  const handleScanQRCode = () => {
    // 模拟扫码核销
    alert("调用摄像头扫描二维码")
    // 实际项目中，这里会调用摄像头API进行扫码
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">抖音券码核销</h1>
      </div>

      <Tabs defaultValue="manual" className="space-y-4">
        <TabsList>
          <TabsTrigger value="manual">手动输入核销</TabsTrigger>
          <TabsTrigger value="scan">扫码核销</TabsTrigger>
          <TabsTrigger value="orders">抖音订单查询</TabsTrigger>
        </TabsList>

        <TabsContent value="manual" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>手动输入券码</CardTitle>
              <CardDescription>输入抖音券码进行核销</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input
                    placeholder="请输入抖音券码"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                  />
                </div>
                <Button onClick={handleVerification}>核销</Button>
              </div>

              {verificationResult && (
                <Card className={verificationResult.success ? "border-green-500" : "border-red-500"}>
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      <div className={`rounded-full p-2 ${verificationResult.success ? "bg-green-100" : "bg-red-100"}`}>
                        {verificationResult.success ? (
                          <Check className="h-6 w-6 text-green-600" />
                        ) : (
                          <X className="h-6 w-6 text-red-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className={`font-medium ${verificationResult.success ? "text-green-600" : "text-red-600"}`}>
                          {verificationResult.success ? "核销成功" : "核销失败"}
                        </h3>
                        <p className="text-sm text-muted-foreground">{verificationResult.message}</p>

                        {verificationResult.data && (
                          <div className="mt-4 space-y-3">
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div>
                                <span className="text-muted-foreground">券码：</span>
                                <span className="font-medium">{verificationResult.data.code}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">价格：</span>
                                <span className="font-medium">{verificationResult.data.price}</span>
                              </div>
                              <div className="col-span-2">
                                <span className="text-muted-foreground">商品：</span>
                                <span className="font-medium">{verificationResult.data.product}</span>
                              </div>
                              <div className="col-span-2">
                                <span className="text-muted-foreground">有效期至：</span>
                                <span className="font-medium">{verificationResult.data.validUntil}</span>
                              </div>
                            </div>

                            {verificationResult.data.customer && (
                              <div className="flex items-center gap-3 pt-2 border-t">
                                <Avatar>
                                  <AvatarImage src={verificationResult.data.customer.avatar} />
                                  <AvatarFallback>{verificationResult.data.customer.name[0]}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{verificationResult.data.customer.name}</div>
                                  <div className="text-sm text-muted-foreground">
                                    {verificationResult.data.customer.phone}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>最近核销记录</CardTitle>
              <CardDescription>显示最近的抖音券码核销记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>券码</TableHead>
                    <TableHead>商品</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>核销时间</TableHead>
                    <TableHead>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentVerifications.map((verification) => (
                    <TableRow key={verification.id}>
                      <TableCell className="font-medium">{verification.code}</TableCell>
                      <TableCell>{verification.product}</TableCell>
                      <TableCell>{verification.price}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={verification.customer.avatar} />
                            <AvatarFallback>{verification.customer.name[0]}</AvatarFallback>
                          </Avatar>
                          <span>{verification.customer.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{verification.verifiedAt}</TableCell>
                      <TableCell>
                        <Badge variant={verification.status === "success" ? "default" : "destructive"}>
                          {verification.status === "success" ? "成功" : "失败"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scan" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>扫码核销</CardTitle>
              <CardDescription>使用摄像头扫描抖音券码进行核销</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-12 text-center">
                <QrCode className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="font-medium text-lg mb-2">扫描抖音券码</h3>
                <p className="text-sm text-muted-foreground mb-4">点击下方按钮启动摄像头扫描抖音券码</p>
                <Button onClick={handleScanQRCode}>
                  <QrCode className="mr-2 h-4 w-4" />
                  开始扫码
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>抖音订单查询</CardTitle>
              <CardDescription>查询抖音平台订单信息</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input placeholder="输入订单号、手机号或用户名查询" />
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <div className="flex items-center justify-center p-8 border rounded-lg">
                <div className="text-center">
                  <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="font-medium text-lg mb-2">暂无查询结果</h3>
                  <p className="text-sm text-muted-foreground">请输入查询条件并点击查询按钮</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

