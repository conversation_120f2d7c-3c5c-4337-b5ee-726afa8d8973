"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { UserManagement } from "@/components/settings/user-management"
import { RolePermissions } from "@/components/settings/role-permissions"
import {
  AlertCircle,
  Save,
  Upload,
  Download,
  RefreshCw,
  Clock,
  MapPin,
  Phone,
  Mail,
  Globe,
  FileText,
} from "lucide-react"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useState } from "react"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"

export default function SettingsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">系统设置</h1>
      </div>

      <Tabs defaultValue="basic" className="space-y-4">
        {/* <TabsList className="grid w-full grid-cols-5 lg:w-[800px]">
          <TabsTrigger value="basic">基础设置</TabsTrigger>
          <TabsTrigger value="users">员工管理</TabsTrigger>
          <TabsTrigger value="roles">角色权限</TabsTrigger>
          <TabsTrigger value="notification">通知设置</TabsTrigger>
          <TabsTrigger value="advanced">高级设置</TabsTrigger>
        </TabsList> */}

        <TabsContent value="basic" className="space-y-4">
          <BasicSettings />
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <EmployeeManagement />
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <RolePermissions />
        </TabsContent>

        <TabsContent value="notification" className="space-y-4">
          <NotificationSettings />
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <AdvancedSettings />
        </TabsContent>
      </Tabs>
    </div>
  )
}

function BasicSettings() {
  const { toast } = useToast()
  const [isSaving, setIsSaving] = useState(false)

  const handleSave = () => {
    setIsSaving(true)
    // 模拟保存操作
    setTimeout(() => {
      setIsSaving(false)
      toast({
        title: "设置已保存",
        description: "您的基础设置已成功保存",
      })
    }, 1000)
  }

  return (
    <>
          <Card>
        <CardHeader>
          <CardTitle>品牌设置</CardTitle>
          <CardDescription>设置您的品牌标识和风格</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="logo">场馆Logo</Label>
              <div className="flex items-center gap-4">
                <div className="h-20 w-20 rounded-md border flex items-center justify-center">
                  <img src="/placeholder.svg?height=80&width=80" alt="Logo预览" className="max-h-full max-w-full" />
                </div>
                <Button variant="outline" size="sm">
                  <Upload className="mr-2 h-4 w-4" />
                  上传Logo
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">推荐尺寸: 200x200px, 格式: PNG, JPG</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="favicon">网站图标</Label>
              <div className="flex items-center gap-4">
                <div className="h-10 w-10 rounded-md border flex items-center justify-center">
                  <img src="/placeholder.svg?height=40&width=40" alt="图标预览" className="max-h-full max-w-full" />
                </div>
                <Button variant="outline" size="sm">
                  <Upload className="mr-2 h-4 w-4" />
                  上传图标
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">推荐尺寸: 32x32px, 格式: ICO, PNG</p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="theme-color">主题色</Label>
            <div className="flex gap-4">
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  id="theme-color"
                  defaultValue="#4f46e5"
                  className="h-8 w-8 rounded cursor-pointer"
                />
                <span className="text-sm">#4f46e5</span>
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  id="secondary-color"
                  defaultValue="#10b981"
                  className="h-8 w-8 rounded cursor-pointer"
                />
                <span className="text-sm">#10b981</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">选择适合您品牌的主题色和辅助色</p>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline">重置</Button>
            <Button>保存设置</Button>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>场馆信息</CardTitle>
          <CardDescription>设置您的场馆基本信息</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="studio-name">场馆名称</Label>
              <Input id="studio-name" defaultValue="静心瑜伽馆" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="contact-phone">联系电话</Label>
              <div className="flex">
                <Phone className="mr-2 h-4 w-4 opacity-70 mt-3" />
                <Input id="contact-phone" defaultValue="010-12345678" />
              </div>
            </div>

          </div>

          <div className="space-y-2">
            <Label htmlFor="address">场馆地址</Label>
            <div className="flex">
              <MapPin className="mr-2 h-4 w-4 opacity-70 mt-3" />
              <Input id="address" defaultValue="北京市朝阳区建国路88号" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">场馆简介</Label>
            <Textarea
              id="description"
              defaultValue="静心瑜伽馆成立于2015年，是一家专注于提供高品质瑜伽和普拉提课程的专业场馆。我们拥有一支经验丰富的教练团队，致力于为会员提供专业、舒适的练习环境。"
              className="min-h-[100px]"
            />
          </div>

          <Separator className="my-4" />

          <div className="space-y-4">
            <h3 className="text-lg font-medium">营业时间</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="weekday-hours">工作日</Label>
                <div className="flex">
                  <Clock className="mr-2 h-4 w-4 opacity-70 mt-3" />
                  <Input id="weekday-hours" defaultValue="09:00-21:00" />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="weekend-hours">周末</Label>
                <div className="flex">
                  <Clock className="mr-2 h-4 w-4 opacity-70 mt-3" />
                  <Input id="weekend-hours" defaultValue="10:00-20:00" />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="holiday-hours">节假日</Label>
              <div className="flex">
                <Clock className="mr-2 h-4 w-4 opacity-70 mt-3" />
                <Input id="holiday-hours" defaultValue="10:00-19:00" />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="closed-days">休息日设置</Label>
                <p className="text-sm text-muted-foreground">设置每周固定休息日</p>
              </div>
              <Select defaultValue="none">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="选择休息日" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">无固定休息日</SelectItem>
                  <SelectItem value="monday">周一</SelectItem>
                  <SelectItem value="tuesday">周二</SelectItem>
                  <SelectItem value="wednesday">周三</SelectItem>
                  <SelectItem value="thursday">周四</SelectItem>
                  <SelectItem value="friday">周五</SelectItem>
                  <SelectItem value="saturday">周六</SelectItem>
                  <SelectItem value="sunday">周日</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="space-y-4">
            <h3 className="text-lg font-medium">系统设置</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-backup">自动备份</Label>
                  <p className="text-sm text-muted-foreground">每日自动备份系统数据</p>
                </div>
                <Switch id="auto-backup" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sms-notification">短信通知</Label>
                  <p className="text-sm text-muted-foreground">课程预约成功后发送短信通知</p>
                </div>
                <Switch id="sms-notification" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-reminder">自动提醒</Label>
                  <p className="text-sm text-muted-foreground">课程开始前自动提醒会员</p>
                </div>
                <Switch id="auto-reminder" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="maintenance-mode">维护模式</Label>
                  <p className="text-sm text-muted-foreground">开启后会员端将显示维护中</p>
                </div>
                <Switch id="maintenance-mode" />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="debug-mode">调试模式</Label>
                  <p className="text-sm text-muted-foreground">开启后系统将记录详细日志</p>
                </div>
                <Switch id="debug-mode" />
              </div>

              <Separator className="my-4" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="member-level-feature">会员等级功能</Label>
                  <p className="text-sm text-muted-foreground">
                    启用后，会员将根据设定的规则自动分配等级，并在会员列表中显示
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Switch id="member-level-feature" defaultChecked />
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/members/levels">管理等级</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline">重置</Button>
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  保存设置
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>


    </>
  )
}

function NotificationSettings() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>通知设置</CardTitle>
        <CardDescription>配置系统通知方式和内容</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">会员通知</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="booking-notification">预约通知</Label>
                <p className="text-sm text-muted-foreground">会员预约课程成功后发送通知</p>
              </div>
              <Switch id="booking-notification" defaultChecked />
            </div>

            <div className="space-y-2">
              <Label htmlFor="booking-template">预约通知模板</Label>
              <Textarea
                id="booking-template"
                defaultValue={`尊敬的${"{{会员名称}}"}，您已成功预约${"{{课程名称}}"}，上课时间：${"{{上课时间}}"}，地点：${"{{场地}}"}，教练：${"{{教练}}"}。期待您的到来！`}
                className="min-h-[100px]"
              />
              <p className="text-sm text-muted-foreground">
                可用变量：{"{{会员名称}}"}、{"{{课程名称}}"}、{"{{上课时间}}"}、{"{{场地}}"}、{"{{教练}}"}
              </p>
            </div>

            <Separator className="my-4" />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="reminder-notification">课程提醒</Label>
                <p className="text-sm text-muted-foreground">课程开始前提醒会员</p>
              </div>
              <Switch id="reminder-notification" defaultChecked />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reminder-time">提前提醒时间（小时）</Label>
                <Input id="reminder-time" type="number" defaultValue="2" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reminder-channel">提醒渠道</Label>
                <Select defaultValue="all">
                  <SelectTrigger>
                    <SelectValue placeholder="选择提醒渠道" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部渠道</SelectItem>
                    <SelectItem value="sms">仅短信</SelectItem>
                    <SelectItem value="wechat">仅微信</SelectItem>
                    <SelectItem value="app">仅APP推送</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="reminder-template">课程提醒模板</Label>
              <Textarea
                id="reminder-template"
                defaultValue={`尊敬的${"{{会员名称}}"}，温馨提醒您，您预约的${"{{课程名称}}"}将于${"{{上课时间}}"}开始，地点：${"{{场地}}"}，教练：${"{{教练}}"}。请提前到达，谢谢！`}
                className="min-h-[100px]"
              />
            </div>

            <Separator className="my-4" />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="expiry-notification">会员卡到期提醒</Label>
                <p className="text-sm text-muted-foreground">会员卡到期前提醒会员</p>
              </div>
              <Switch id="expiry-notification" defaultChecked />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="expiry-days">提前提醒天数</Label>
                <Input id="expiry-days" type="number" defaultValue="7" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="expiry-repeat">重复提醒</Label>
                <Select defaultValue="once">
                  <SelectTrigger>
                    <SelectValue placeholder="选择重复提醒方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="once">仅提醒一次</SelectItem>
                    <SelectItem value="daily">每天提醒</SelectItem>
                    <SelectItem value="3days">每3天提醒</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="expiry-template">到期提醒模板</Label>
              <Textarea
                id="expiry-template"
                defaultValue={`尊敬的${"{{会员名称}}"}，您的${"{{会员卡类型}}"}将于${"{{到期日期}}"}到期，为了不影响您的使用，请及时续费。`}
                className="min-h-[100px]"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="birthday-notification">生日祝福</Label>
                <p className="text-sm text-muted-foreground">会员生日当天发送祝福</p>
              </div>
              <Switch id="birthday-notification" defaultChecked />
            </div>

            <div className="space-y-2">
              <Label htmlFor="birthday-template">生日祝福模板</Label>
              <Textarea
                id="birthday-template"
                defaultValue={`亲爱的${"{{会员名称}}"}，静心瑜伽馆全体员工祝您生日快乐！作为我们尊贵的会员，我们为您准备了生日专属优惠，详情请咨询前台。`}
                className="min-h-[100px]"
              />
            </div>
          </div>

          <Separator className="my-4" />

          <h3 className="text-lg font-medium">管理员通知</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="new-member-notification">新会员通知</Label>
                <p className="text-sm text-muted-foreground">有新会员注册时通知管理员</p>
              </div>
              <Switch id="new-member-notification" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="full-class-notification">满员通知</Label>
                <p className="text-sm text-muted-foreground">课程预约满员时通知管理员</p>
              </div>
              <Switch id="full-class-notification" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="daily-report-notification">日报通知</Label>
                <p className="text-sm text-muted-foreground">每日发送运营数据报告</p>
              </div>
              <Switch id="daily-report-notification" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="abnormal-notification">异常情况通知</Label>
                <p className="text-sm text-muted-foreground">系统检测到异常情况时通知管理员</p>
              </div>
              <Switch id="abnormal-notification" defaultChecked />
            </div>

            <div className="space-y-2">
              <Label htmlFor="admin-emails">管理员邮箱</Label>
              <Input id="admin-emails" defaultValue="<EMAIL>, <EMAIL>" />
              <p className="text-sm text-muted-foreground">多个邮箱请用逗号分隔</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="admin-phones">管理员手机</Label>
              <Input id="admin-phones" defaultValue="***********, ***********" />
              <p className="text-sm text-muted-foreground">多个手机号请用逗号分隔</p>
            </div>
          </div>

          <Separator className="my-4" />

          <h3 className="text-lg font-medium">通知渠道</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="sms-channel">短信通知</Label>
                <p className="text-sm text-muted-foreground">通过短信发送通知</p>
              </div>
              <Switch id="sms-channel" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="email-channel">邮件通知</Label>
                <p className="text-sm text-muted-foreground">通过邮件发送通知</p>
              </div>
              <Switch id="email-channel" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="wechat-channel">微信通知</Label>
                <p className="text-sm text-muted-foreground">通过微信公众号发送通知</p>
              </div>
              <Switch id="wechat-channel" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="app-channel">APP推送</Label>
                <p className="text-sm text-muted-foreground">通过APP推送通知</p>
              </div>
              <Switch id="app-channel" defaultChecked />
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <Button>保存设置</Button>
        </div>
      </CardContent>
    </Card>
  )
}

function AdvancedSettings() {
  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>数据备份与恢复</CardTitle>
          <CardDescription>管理系统数据的备份和恢复</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="auto-backup-advanced">自动备份</Label>
                <p className="text-sm text-muted-foreground">系统将按设定频率自动备份数据</p>
              </div>
              <Switch id="auto-backup-advanced" defaultChecked />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="backup-frequency">备份频率</Label>
                <Select defaultValue="daily">
                  <SelectTrigger>
                    <SelectValue placeholder="选择备份频率" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">每日备份</SelectItem>
                    <SelectItem value="weekly">每周备份</SelectItem>
                    <SelectItem value="monthly">每月备份</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="backup-time">备份时间</Label>
                <Input id="backup-time" type="time" defaultValue="03:00" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="backup-retention">保留备份数量</Label>
              <Input id="backup-retention" type="number" defaultValue="7" />
              <p className="text-sm text-muted-foreground">超过设定数量的旧备份将被自动删除</p>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="cloud-backup">云端备份</Label>
                <p className="text-sm text-muted-foreground">将备份同步到云存储</p>
              </div>
              <Switch id="cloud-backup" defaultChecked />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cloud-provider">云存储提供商</Label>
              <Select defaultValue="aliyun">
                <SelectTrigger>
                  <SelectValue placeholder="选择云存储提供商" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="aliyun">阿里云OSS</SelectItem>
                  <SelectItem value="tencent">腾讯云COS</SelectItem>
                  <SelectItem value="qiniu">七牛云</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>备份提示</AlertTitle>
              <AlertDescription>建议定期手动备份重要数据，并将备份文件保存在多个不同的位置。</AlertDescription>
            </Alert>

            <div className="flex justify-between">
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                手动备份
              </Button>
              <Button variant="outline">
                <Upload className="mr-2 h-4 w-4" />
                恢复数据
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>系统日志</CardTitle>
          <CardDescription>查看和管理系统操作日志</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="enable-logging">启用日志记录</Label>
                <p className="text-sm text-muted-foreground">记录系统操作和异常情况</p>
              </div>
              <Switch id="enable-logging" defaultChecked />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="log-level">日志级别</Label>
                <Select defaultValue="info">
                  <SelectTrigger>
                    <SelectValue placeholder="选择日志级别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="debug">调试 (Debug)</SelectItem>
                    <SelectItem value="info">信息 (Info)</SelectItem>
                    <SelectItem value="warning">警��� (Warning)</SelectItem>
                    <SelectItem value="error">错误 (Error)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="log-retention">日志保留天数</Label>
                <Input id="log-retention" type="number" defaultValue="30" />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="log-user-actions">记录用户操作</Label>
                <p className="text-sm text-muted-foreground">记录管理员的所有操作</p>
              </div>
              <Switch id="log-user-actions" defaultChecked />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="log-api-calls">记录API调用</Label>
                <p className="text-sm text-muted-foreground">记录所有API请求和响应</p>
              </div>
              <Switch id="log-api-calls" />
            </div>

            <div className="flex justify-between">
              <Button variant="outline" asChild>
                <Link href="/settings/logs">
                  <FileText className="mr-2 h-4 w-4" />
                  查看日志
                </Link>
              </Button>
              <Button variant="outline" className="text-red-500 hover:text-red-700">
                清除日志
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>安全设置</CardTitle>
          <CardDescription>配置系统安全和访问控制</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">密码策略</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="password-complexity">密码复杂度要求</Label>
                  <p className="text-sm text-muted-foreground">要求密码包含大小写字母、数字和特殊字符</p>
                </div>
                <Switch id="password-complexity" defaultChecked />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password-length">最小密码长度</Label>
                <Input id="password-length" type="number" defaultValue="8" />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="password-expiry">密码过期策略</Label>
                  <p className="text-sm text-muted-foreground">要求用户定期更改密码</p>
                </div>
                <Switch id="password-expiry" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password-expiry-days">密码有效期（天）</Label>
                <Input id="password-expiry-days" type="number" defaultValue="90" />
              </div>
            </div>

            <Separator className="my-4" />

            <h3 className="text-lg font-medium">登录安全</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="login-attempts">登录失败限制</Label>
                  <p className="text-sm text-muted-foreground">限制连续登录失败次数</p>
                </div>
                <Switch id="login-attempts" defaultChecked />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="max-attempts">最大尝试次数</Label>
                  <Input id="max-attempts" type="number" defaultValue="5" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lockout-duration">锁定时长（分钟）</Label>
                  <Input id="lockout-duration" type="number" defaultValue="30" />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="two-factor-auth">双因素认证</Label>
                  <p className="text-sm text-muted-foreground">要求管理员使用双因素认证</p>
                </div>
                <Switch id="two-factor-auth" />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="session-timeout">会话超时</Label>
                  <p className="text-sm text-muted-foreground">长时间不活动后自动登出</p>
                </div>
                <Switch id="session-timeout" defaultChecked />
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeout-minutes">超时时间（分钟）</Label>
                <Input id="timeout-minutes" type="number" defaultValue="30" />
              </div>
            </div>

            <Separator className="my-4" />

            <h3 className="text-lg font-medium">IP访问控制</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="ip-restriction">IP访问限制</Label>
                  <p className="text-sm text-muted-foreground">限制可访问管理后台的IP地址</p>
                </div>
                <Switch id="ip-restriction" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="allowed-ips">允许的IP地址</Label>
                <Textarea
                  id="allowed-ips"
                  placeholder="每行输入一个IP地址或IP段，例如：*********** 或 ***********/24"
                  className="min-h-[100px]"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button>保存安全设置</Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>系统集成</CardTitle>
          <CardDescription>配置与第三方系统的集成</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="wechat-integration">微信公众号集成</Label>
                <p className="text-sm text-muted-foreground">与微信公众号对接</p>
              </div>
              <Switch id="wechat-integration" defaultChecked />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="wechat-appid">AppID</Label>
                <Input id="wechat-appid" defaultValue="wx1234567890abcdef" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="wechat-secret">AppSecret</Label>
                <Input id="wechat-secret" type="password" defaultValue="********************************" />
              </div>
            </div>

            <Separator className="my-4" />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="sms-integration">短信服务集成</Label>
                <p className="text-sm text-muted-foreground">与短信服务提供商对接</p>
              </div>
              <Switch id="sms-integration" defaultChecked />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sms-provider">短信服务提供商</Label>
              <Select defaultValue="aliyun">
                <SelectTrigger>
                  <SelectValue placeholder="选择短信服务提供商" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="aliyun">阿里云短信</SelectItem>
                  <SelectItem value="tencent">腾讯云短信</SelectItem>
                  <SelectItem value="netease">网易云信</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="sms-key">AccessKey</Label>
                <Input id="sms-key" defaultValue="LTAIxxxxxxxxxxxxxxxx" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sms-secret">AccessSecret</Label>
                <Input id="sms-secret" type="password" defaultValue="********************************" />
              </div>
            </div>

            <Separator className="my-4" />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="crm-integration">CRM系统集成</Label>
                <p className="text-sm text-muted-foreground">与外部CRM系统对接</p>
              </div>
              <Switch id="crm-integration" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="crm-api-url">API地址</Label>
              <Input id="crm-api-url" placeholder="https://api.example.com/v1/" />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="crm-api-key">API密钥</Label>
                <Input id="crm-api-key" placeholder="输入API密钥" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="crm-sync-frequency">同步频率</Label>
                <Select defaultValue="daily">
                  <SelectTrigger>
                    <SelectValue placeholder="选择同步频率" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="realtime">实时同步</SelectItem>
                    <SelectItem value="hourly">每小时</SelectItem>
                    <SelectItem value="daily">每天</SelectItem>
                    <SelectItem value="weekly">每周</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button>保存集成设置</Button>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

function EmployeeManagement() {
  return <UserManagement />
}

