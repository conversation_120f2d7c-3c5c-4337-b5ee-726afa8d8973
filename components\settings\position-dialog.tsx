"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface PositionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  position?: {
    id?: number
    name: string
    department: string
  } | null
  departments: Array<{ id: number, name: string }>
  onSave: (position: any) => void
}

export function PositionDialog({ open, onOpenChange, position, departments, onSave }: PositionDialogProps) {
  const [name, setName] = useState("")
  const [department, setDepartment] = useState("")
  const [errors, setErrors] = useState<{ name?: string, department?: string }>({})

  useEffect(() => {
    if (position) {
      setName(position.name)
      setDepartment(position.department)
    } else {
      setName("")
      setDepartment("")
    }
    setErrors({})
  }, [position, open])

  const handleSubmit = () => {
    const newErrors: { name?: string, department?: string } = {}
    
    if (!name.trim()) {
      newErrors.name = "职位名称不能为空"
    }
    
    if (!department) {
      newErrors.department = "请选择所属部门"
    }
    
    setErrors(newErrors)
    
    if (Object.keys(newErrors).length === 0) {
      onSave({
        id: position?.id,
        name,
        department
      })
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{position ? "编辑职位" : "添加职位"}</DialogTitle>
          <DialogDescription>
            {position ? "修改职位信息" : "创建新的职位"}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              职位名称
            </Label>
            <div className="col-span-3">
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="department" className="text-right">
              所属部门
            </Label>
            <div className="col-span-3">
              <Select value={department} onValueChange={setDepartment}>
                <SelectTrigger id="department" className={errors.department ? "border-red-500" : ""}>
                  <SelectValue placeholder="选择部门" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept.id} value={dept.name}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.department && <p className="text-red-500 text-sm mt-1">{errors.department}</p>}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit}>
            保存
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
