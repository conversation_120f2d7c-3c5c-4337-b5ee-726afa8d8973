"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { ChevronDown } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface CourseType {
  id?: string
  name: string
  description: string
  color: string
  status: string
  displayOrder?: number
}

interface AddEditCourseTypeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  type?: CourseType | null
  onSave: (data: CourseType) => void
}

export function AddEditCourseTypeDialog({ 
  open, 
  onOpenChange, 
  type, 
  onSave 
}: AddEditCourseTypeDialogProps) {
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [color, setColor] = useState("#3b82f6")
  const [displayOrder, setDisplayOrder] = useState<number>(0)
  const [isActive, setIsActive] = useState(true)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  // 当type变化时更新表单数据
  useEffect(() => {
    console.log('AddEditCourseTypeDialog: type changed:', type)
    if (type) {
      setName(type.name || "")
      setDescription(type.description || "")
      setColor(type.color || "#3b82f6")
      setDisplayOrder(type.displayOrder || 0)
      setIsActive(type.status === "active")
    } else {
      setName("")
      setDescription("")
      setColor("#3b82f6")
      setDisplayOrder(0)
      setIsActive(true)
    }
  }, [type])

  // 当open状态变化时的调试
  useEffect(() => {
    console.log('AddEditCourseTypeDialog: open changed:', open)
  }, [open])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name.trim()) {
      toast({
        title: "验证错误",
        description: "请输入课程类型名称",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    
    try {
      const data: CourseType = {
        ...(type || {}),
        name: name.trim(),
        description: description.trim(),
        color,
        displayOrder,
        status: isActive ? 'active' : 'inactive',
      }
      
      console.log('AddEditCourseTypeDialog: submitting data:', data)
      await onSave(data)
    } catch (error) {
      console.error('AddEditCourseTypeDialog: save error:', error)
      toast({
        title: "保存失败",
        description: "操作过程中出现错误",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  console.log('AddEditCourseTypeDialog render:', { open, type, name, description, color, isActive })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl font-semibold">
            {type ? "编辑课程类型" : "添加课程类型"}
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            {type ? "编辑现有的课程类型信息" : "添加新的课程类型，课程类型用于对课程进行分类和标识。"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-medium text-gray-700">
              类型名称 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入课程类型名称"
              required
              className="w-full"
            />
            <p className="text-xs text-gray-500">课程类型名称将显示在课程列表和日历中</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium text-gray-700">描述</Label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="输入课程类型描述（可选）"
              className="w-full min-h-[80px] px-3 py-2 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500">简要描述这个课程类型的特点和适用人群</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="color" className="text-sm font-medium text-gray-700">颜色标识</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between h-10 px-3"
                  type="button"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="w-6 h-6 rounded-full border border-gray-300"
                      style={{ backgroundColor: color }}
                    />
                    <span className="text-sm font-mono">{color}</span>
                  </div>
                  <ChevronDown className="h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="start">
                <div className="space-y-4">
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-3">预设颜色</div>
                    <div className="grid grid-cols-6 gap-2">
                      {[
                        '#4285F4', '#34A853', '#FBBC05', '#EA4335', '#9C27B0',
                        '#FF6D91', '#00BCD4', '#FF5722', '#607D8B', '#795548',
                        '#3F51B5', '#009688'
                      ].map((colorOption) => (
                        <button
                          key={colorOption}
                          type="button"
                          className={`w-8 h-8 rounded-full border-2 transition-all duration-200 ${
                            color === colorOption
                              ? 'border-gray-800 ring-2 ring-blue-200'
                              : 'border-gray-300 hover:border-gray-500'
                          }`}
                          style={{ backgroundColor: colorOption }}
                          onClick={() => setColor(colorOption)}
                          title={colorOption}
                        />
                      ))}
                    </div>
                  </div>

                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-3">自定义颜色</div>
                    <div className="flex items-center gap-2">
                      <Input
                        type="text"
                        value={color}
                        onChange={(e) => setColor(e.target.value)}
                        placeholder="#4285F4"
                        className="flex-1 font-mono text-sm"
                      />
                      <Button
                        type="button"
                        size="sm"
                        onClick={() => {}}
                        className="px-3"
                      >
                        应用
                      </Button>
                    </div>
                  </div>

                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-2">颜色选择器</div>
                    <input
                      type="color"
                      value={color}
                      onChange={(e) => setColor(e.target.value)}
                      className="w-full h-10 rounded border border-gray-300 cursor-pointer"
                      title="选择颜色"
                    />
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            <p className="text-xs text-gray-500">选择一个颜色来标识此课程类型，将用于日历和课程列表中</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">启用状态</Label>
              <div className="flex items-center gap-2">
                <Checkbox
                  id="isActive"
                  checked={isActive}
                  onCheckedChange={(checked) => setIsActive(!!checked)}
                />
                <Label htmlFor="isActive" className="text-sm">
                  {isActive ? "启用" : "停用"}
                </Label>
              </div>
              <p className="text-xs text-gray-500">设置此课程类型是否启用</p>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">显示顺序</Label>
              <Input
                type="number"
                value={displayOrder}
                onChange={(e) => setDisplayOrder(parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
                className="w-full"
              />
              <p className="text-xs text-gray-500">数字越小排序越靠前</p>
            </div>
          </div>

          <DialogFooter className="pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
              className="min-w-[80px]"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="min-w-[80px]"
            >
              {loading ? "保存中..." : "保存"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
