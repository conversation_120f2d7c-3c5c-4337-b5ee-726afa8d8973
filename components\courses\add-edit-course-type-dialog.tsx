"use client"

import React, { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"

interface CourseType {
  id?: string
  name: string
  description: string
  color: string
  status: string
}

interface AddEditCourseTypeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  type?: CourseType | null
  onSave: (data: CourseType) => void
}

export function AddEditCourseTypeDialog({ 
  open, 
  onOpenChange, 
  type, 
  onSave 
}: AddEditCourseTypeDialogProps) {
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [color, setColor] = useState("#3b82f6")
  const [isActive, setIsActive] = useState(true)
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  // 当type变化时更新表单数据
  useEffect(() => {
    console.log('AddEditCourseTypeDialog: type changed:', type)
    if (type) {
      setName(type.name || "")
      setDescription(type.description || "")
      setColor(type.color || "#3b82f6")
      setIsActive(type.status === "active")
    } else {
      setName("")
      setDescription("")
      setColor("#3b82f6")
      setIsActive(true)
    }
  }, [type])

  // 当open状态变化时的调试
  useEffect(() => {
    console.log('AddEditCourseTypeDialog: open changed:', open)
  }, [open])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name.trim()) {
      toast({
        title: "验证错误",
        description: "请输入课程类型名称",
        variant: "destructive"
      })
      return
    }

    setLoading(true)
    
    try {
      const data: CourseType = {
        ...(type || {}),
        name: name.trim(),
        description: description.trim(),
        color,
        status: isActive ? 'active' : 'inactive',
      }
      
      console.log('AddEditCourseTypeDialog: submitting data:', data)
      await onSave(data)
    } catch (error) {
      console.error('AddEditCourseTypeDialog: save error:', error)
      toast({
        title: "保存失败",
        description: "操作过程中出现错误",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  console.log('AddEditCourseTypeDialog render:', { open, type, name, description, color, isActive })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{type ? "编辑课程类型" : "添加课程类型"}</DialogTitle>
          <DialogDescription>
            {type ? "修改课程类型信息" : "添加新的课程类型"}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">名称 *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入课程类型名称"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Input
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="输入课程类型描述（可选）"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="color">颜色</Label>
            <div className="flex items-center gap-2">
              <div 
                className="w-10 h-10 rounded-md border border-gray-300" 
                style={{ backgroundColor: color }}
              />
              <Input
                id="color"
                type="text"
                value={color}
                onChange={(e) => setColor(e.target.value)}
                placeholder="#RRGGBB"
                className="flex-1"
              />
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Checkbox 
              id="isActive" 
              checked={isActive} 
              onCheckedChange={(checked) => setIsActive(!!checked)}
            />
            <Label htmlFor="isActive">启用</Label>
          </div>
          
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "保存中..." : "保存"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
