"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  ArrowLeft, 
  Plus, 
  Calendar,
  Clock,
  CheckCircle2,
  XCircle,
  Filter,
  Download,
  MoreHorizontal,
  FileText,
  CreditCard,
  DollarSign,
  ClipboardList
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"

// 模拟工作交接记录数据
const handoverRecords = [
  { 
    id: "h1", 
    date: "2023-05-16",
    shiftType: "早班",
    handoverTime: "14:00",
    handoverFrom: {
      id: "e1",
      name: "李明",
      avatar: "/avatars/staff-01.png"
    },
    handoverTo: {
      id: "e2",
      name: "王芳",
      avatar: "/avatars/staff-02.png"
    },
    cashAmount: 2580.50,
    pendingMatters: "1. 张三会员卡续费待处理\n2. 下午4点有新会员咨询",
    completedMatters: "1. 上午课程签到已完成\n2. 场地设备检查已完成",
    notes: "空调温度设置在26度",
    status: "已完成"
  },
  { 
    id: "h2", 
    date: "2023-05-16",
    shiftType: "晚班",
    handoverTime: "22:00",
    handoverFrom: {
      id: "e2",
      name: "王芳",
      avatar: "/avatars/staff-02.png"
    },
    handoverTo: {
      id: "e3",
      name: "张伟",
      avatar: "/avatars/staff-03.png"
    },
    cashAmount: 3650.00,
    pendingMatters: "1. 明天早上8点需要准备大教室\n2. 有3位会员预约了明早的私教课",
    completedMatters: "1. 晚间课程签到已完成\n2. 当日营业额已统计",
    notes: "明天有设备维护，提前告知会员",
    status: "已完成"
  },
  { 
    id: "h3", 
    date: "2023-05-17",
    shiftType: "早班",
    handoverTime: "14:00",
    handoverFrom: {
      id: "e3",
      name: "张伟",
      avatar: "/avatars/staff-03.png"
    },
    handoverTo: {
      id: "e1",
      name: "李明",
      avatar: "/avatars/staff-01.png"
    },
    cashAmount: 1850.00,
    pendingMatters: "1. 赵六会员投诉待处理\n2. 下午有瑜伽垫新货到达",
    completedMatters: "1. 上午课程签到已完成\n2. 设备维护已完成",
    notes: "前门门锁有些问题，已联系维修",
    status: "已完成"
  },
  { 
    id: "h4", 
    date: "2023-05-17",
    shiftType: "晚班",
    handoverTime: "22:00",
    handoverFrom: {
      id: "e1",
      name: "李明",
      avatar: "/avatars/staff-01.png"
    },
    handoverTo: {
      id: "e2",
      name: "王芳",
      avatar: "/avatars/staff-02.png"
    },
    cashAmount: 4200.00,
    pendingMatters: "1. 明天有新课程上线\n2. 需要更新公告栏信息",
    completedMatters: "1. 晚间课程签到已完成\n2. 瑜伽垫已入库",
    notes: "空调遥控器电池需要更换",
    status: "已完成"
  },
  { 
    id: "h5", 
    date: "2023-05-18",
    shiftType: "早班",
    handoverTime: "14:00",
    handoverFrom: {
      id: "e2",
      name: "王芳",
      avatar: "/avatars/staff-02.png"
    },
    handoverTo: {
      id: "e3",
      name: "张伟",
      avatar: "/avatars/staff-03.png"
    },
    cashAmount: 2100.00,
    pendingMatters: "1. 需要联系瑜伽教练确认周末课程\n2. 会员生日礼品准备",
    completedMatters: "1. 上午课程签到已完成\n2. 公告栏信息已更新",
    notes: "前台打印机需要加墨",
    status: "进行中"
  }
]

// 班次类型选项
const shiftTypes = [
  "早班",
  "中班",
  "晚班"
]

export default function HandoverPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [showHandoverDialog, setShowHandoverDialog] = useState(false)
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<any>(null)
  const [newHandover, setNewHandover] = useState({
    shiftType: "早班",
    handoverTo: "",
    cashAmount: "",
    pendingMatters: "",
    completedMatters: "",
    notes: ""
  })

  // 过滤工作交接记录
  const filteredRecords = handoverRecords.filter(record => {
    // 基本搜索过滤
    const searchFilter = 
      searchQuery === "" || 
      record.handoverFrom.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.handoverTo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.date.includes(searchQuery);
    
    // 标签过滤
    const tabFilter = 
      activeTab === "all" || 
      (activeTab === "completed" && record.status === "已完成") ||
      (activeTab === "in-progress" && record.status === "进行中");
    
    return searchFilter && tabFilter;
  });

  // 处理添加工作交接
  const handleAddHandover = () => {
    // 在实际应用中，这里会调用API添加工作交接记录
    toast.success("工作交接记录已创建！");
    setShowHandoverDialog(false);
    
    // 重置表单
    setNewHandover({
      shiftType: "早班",
      handoverTo: "",
      cashAmount: "",
      pendingMatters: "",
      completedMatters: "",
      notes: ""
    });
  }

  // 查看交接详情
  const viewHandoverDetail = (record: any) => {
    setSelectedRecord(record);
    setShowDetailDialog(true);
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/reception">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">前台工作交接</h1>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>工作交接记录</CardTitle>
              <CardDescription>
                管理前台工作交接记录
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button onClick={() => setShowHandoverDialog(true)}>
                <ClipboardList className="h-4 w-4 mr-2" />
                创建交接
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col md:flex-row md:items-center gap-4 justify-between">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full max-w-md">
              <TabsList className="grid grid-cols-3">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="in-progress">进行中</TabsTrigger>
                <TabsTrigger value="completed">已完成</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索日期或员工姓名..."
                  className="pl-8 w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>日期</TableHead>
                <TableHead>班次</TableHead>
                <TableHead>交接人员</TableHead>
                <TableHead>现金金额</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    没有找到符合条件的交接记录
                  </TableCell>
                </TableRow>
              ) : (
                filteredRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <div className="font-medium">{record.date}</div>
                      <div className="text-xs text-muted-foreground">{record.handoverTime}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {record.shiftType}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="flex -space-x-2">
                          <Avatar className="h-8 w-8 border-2 border-background">
                            <AvatarImage src={record.handoverFrom.avatar} alt={record.handoverFrom.name} />
                            <AvatarFallback>{record.handoverFrom.name[0]}</AvatarFallback>
                          </Avatar>
                          <Avatar className="h-8 w-8 border-2 border-background">
                            <AvatarImage src={record.handoverTo.avatar} alt={record.handoverTo.name} />
                            <AvatarFallback>{record.handoverTo.name[0]}</AvatarFallback>
                          </Avatar>
                        </div>
                        <div className="text-sm">
                          {record.handoverFrom.name} → {record.handoverTo.name}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">¥{record.cashAmount.toFixed(2)}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={record.status === "已完成" ? "success" : "default"}>
                        {record.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm" onClick={() => viewHandoverDetail(record)}>
                        查看详情
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-4">
          <div className="text-sm text-muted-foreground">
            共 {filteredRecords.length} 条记录
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 创建工作交接对话框 */}
      <Dialog open={showHandoverDialog} onOpenChange={setShowHandoverDialog}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>创建工作交接</DialogTitle>
            <DialogDescription>
              记录前台工作交接信息
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="shift-type">班次</Label>
                <select
                  id="shift-type"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={newHandover.shiftType}
                  onChange={(e) => setNewHandover({...newHandover, shiftType: e.target.value})}
                >
                  {shiftTypes.map((type) => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="handover-to">交接给</Label>
                <Input
                  id="handover-to"
                  value={newHandover.handoverTo}
                  onChange={(e) => setNewHandover({...newHandover, handoverTo: e.target.value})}
                  placeholder="接班人姓名"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="cash-amount">现金金额</Label>
              <div className="relative">
                <DollarSign className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="cash-amount"
                  type="text"
                  className="pl-8"
                  value={newHandover.cashAmount}
                  onChange={(e) => setNewHandover({...newHandover, cashAmount: e.target.value})}
                  placeholder="0.00"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="pending-matters">待处理事项</Label>
              <textarea
                id="pending-matters"
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newHandover.pendingMatters}
                onChange={(e) => setNewHandover({...newHandover, pendingMatters: e.target.value})}
                placeholder="列出需要接班人处理的事项..."
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="completed-matters">已完成事项</Label>
              <textarea
                id="completed-matters"
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newHandover.completedMatters}
                onChange={(e) => setNewHandover({...newHandover, completedMatters: e.target.value})}
                placeholder="列出已经完成的工作..."
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="notes">备注</Label>
              <textarea
                id="notes"
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newHandover.notes}
                onChange={(e) => setNewHandover({...newHandover, notes: e.target.value})}
                placeholder="添加其他需要注意的事项..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowHandoverDialog(false)}>取消</Button>
            <Button onClick={handleAddHandover}>创建交接</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 交接详情对话框 */}
      {selectedRecord && (
        <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>工作交接详情</DialogTitle>
              <DialogDescription>
                查看工作交接记录详情
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="flex items-center justify-between pb-4 border-b">
                <div>
                  <div className="text-sm text-muted-foreground">交接日期</div>
                  <div className="font-medium">{selectedRecord.date} {selectedRecord.handoverTime}</div>
                </div>
                <Badge variant={selectedRecord.status === "已完成" ? "success" : "default"}>
                  {selectedRecord.status}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-muted-foreground">班次</div>
                  <div className="font-medium">{selectedRecord.shiftType}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">现金金额</div>
                  <div className="font-medium">¥{selectedRecord.cashAmount.toFixed(2)}</div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={selectedRecord.handoverFrom.avatar} alt={selectedRecord.handoverFrom.name} />
                    <AvatarFallback>{selectedRecord.handoverFrom.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="text-sm text-muted-foreground">交接人</div>
                    <div className="font-medium">{selectedRecord.handoverFrom.name}</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={selectedRecord.handoverTo.avatar} alt={selectedRecord.handoverTo.name} />
                    <AvatarFallback>{selectedRecord.handoverTo.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="text-sm text-muted-foreground">接班人</div>
                    <div className="font-medium">{selectedRecord.handoverTo.name}</div>
                  </div>
                </div>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground mb-2">待处理事项</div>
                <div className="p-3 rounded-md bg-muted/50 whitespace-pre-line">
                  {selectedRecord.pendingMatters}
                </div>
              </div>
              
              <div>
                <div className="text-sm text-muted-foreground mb-2">已完成事项</div>
                <div className="p-3 rounded-md bg-muted/50 whitespace-pre-line">
                  {selectedRecord.completedMatters}
                </div>
              </div>
              
              {selectedRecord.notes && (
                <div>
                  <div className="text-sm text-muted-foreground mb-2">备注</div>
                  <div className="p-3 rounded-md bg-muted/50 whitespace-pre-line">
                    {selectedRecord.notes}
                  </div>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button onClick={() => setShowDetailDialog(false)}>关闭</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
