"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { useToast } from "@/hooks/use-toast"
import {
  CreditCard,
  Calendar as CalendarIcon,
  User,
  Search,
  Tag,
  Plus,
  Minus,
  Clock,
  CalendarRange,
  Wallet,
  BarChart4,
  CheckCircle2,
  X,
  Sparkles,
  AlertCircle,
  Loader2
} from "lucide-react"

// 模拟会员卡数据 - 根据传入的cardId返回不同的数据
const getMockCardData = (cardId: string) => {
  // 模拟不同类型的卡数据
  const cardDataMap: Record<string, any> = {
    "MC001": {
      id: "MC001",
      cardNumber: "2305010001",
      cardType: {
        id: "CT001",
        name: "瑜伽年卡",
        type: "期限卡",
        color: "#4CAF50",
        price: 3600,
        validDays: 365,
        isTrialCard: false
      },
      member: {
        id: "M001",
        name: "张三",
        phone: "13800138001",
        avatar: "/avatars/01.png",
        level: "金卡会员"
      },
      status: "active",
      price: 3600,
      actualPrice: 3400,
      startDate: new Date(2023, 0, 1),  // 2023-01-01
      endDate: new Date(2023, 11, 31),  // 2023-12-31
      paymentMethod: "wechat",
      isAutoRenew: true,
      activationType: "immediate",
      isReplacement: false,
      referrer: "李四",
      bonusPoints: "100",
      bonusDays: "30",
      coupon: "new_member",
      notes: "会员生日赠送30天",
      tags: ["VIP", "老客户"],
      createdAt: new Date(2023, 0, 1),
      issueDate: new Date(2023, 0, 1),
      remainingDays: 120,
      remainingCount: null,
      remainingValue: null
    },
    "MC003": {
      id: "MC003",
      cardNumber: "2302150001",
      cardType: {
        id: "CT004",
        name: "私教次卡",
        type: "次卡",
        color: "#9C27B0",
        price: 2000,
        validDays: 180,
        count: 20,
        isTrialCard: false
      },
      member: {
        id: "M003",
        name: "王五",
        phone: "13800138003",
        avatar: "/avatars/03.png",
        level: "铜卡会员"
      },
      status: "active",
      price: 2000,
      actualPrice: 1800,
      startDate: new Date(2023, 1, 15),  // 2023-02-15
      endDate: new Date(2023, 7, 15),    // 2023-08-15
      paymentMethod: "alipay",
      isAutoRenew: false,
      activationType: "immediate",
      isReplacement: false,
      referrer: "赵六",
      bonusPoints: "50",
      bonusDays: "0",
      coupon: "none",
      notes: "新客户优惠",
      tags: ["新客户"],
      createdAt: new Date(2023, 1, 15),
      issueDate: new Date(2023, 1, 15),
      remainingDays: null,
      remainingCount: 8,
      remainingValue: null
    },
    "MC004": {
      id: "MC004",
      cardNumber: "2301100001",
      cardType: {
        id: "CT007",
        name: "储值卡",
        type: "储值卡",
        color: "#607D8B",
        price: 2000,
        validDays: null,
        isTrialCard: false
      },
      member: {
        id: "M004",
        name: "赵六",
        phone: "13800138004",
        avatar: "/avatars/04.png",
        level: "VIP会员"
      },
      status: "active",
      price: 2000,
      actualPrice: 2000,
      startDate: new Date(2023, 0, 10),  // 2023-01-10
      endDate: null,
      paymentMethod: "wechat",
      isAutoRenew: false,
      activationType: "immediate",
      isReplacement: false,
      referrer: "",
      bonusPoints: "200",
      bonusDays: "0",
      coupon: "none",
      notes: "充值2000送200",
      tags: ["VIP"],
      createdAt: new Date(2023, 0, 10),
      issueDate: new Date(2023, 0, 10),
      remainingDays: null,
      remainingCount: null,
      remainingValue: 2000
    }
  };

  // 默认返回MC001的数据，如果找不到对应的cardId
  return cardDataMap[cardId] || cardDataMap["MC001"];
};

// 初始化一个空的模拟数据对象
const mockCardData = {
  id: "",
  cardNumber: "",
  cardType: {
    id: "",
    name: "",
    type: "",
    color: "",
    price: 0,
    validDays: 0,
    isTrialCard: false
  },
  member: {
    id: "",
    name: "",
    phone: "",
    avatar: "",
    level: ""
  },
  status: "",
  price: 0,
  actualPrice: 0,
  startDate: new Date(),
  endDate: null,
  paymentMethod: "",
  isAutoRenew: false,
  activationType: "",
  isReplacement: false,
  referrer: "",
  bonusPoints: "",
  bonusDays: "",
  coupon: "",
  notes: "",
  tags: [],
  createdAt: new Date(),
  issueDate: new Date(),
  remainingDays: null,
  remainingCount: null,
  remainingValue: null
}

interface EditMemberCardInstanceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  cardId: string
  onSuccess?: () => void
}

export function EditMemberCardInstanceDialog({
  open,
  onOpenChange,
  cardId,
  onSuccess
}: EditMemberCardInstanceDialogProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")
  const [loading, setLoading] = useState(true)
  const [cardData, setCardData] = useState<any>(null)
  const [formData, setFormData] = useState({
    cardNumber: "",
    startDate: new Date(),
    endDate: null as Date | null,
    issueDate: new Date(),  // 发卡时间（不可编辑）
    remainingValue: "",  // 储值卡余额
    remainingCount: "",  // 次卡剩余次数
    remainingDays: "",   // 期限卡剩余天数（不可编辑）
    referrer: "",
    notes: "",
  })

  // 加载会员卡数据
  useEffect(() => {
    if (open && cardId) {
      setLoading(true)

      // 模拟API调用
      setTimeout(() => {
        // 根据cardId获取对应的会员卡数据
        const cardData = getMockCardData(cardId)

        // 实际应用中，这里应该是从API获取数据
        setCardData(cardData)

        // 设置表单数据
        setFormData({
          cardNumber: cardData.cardNumber,
          startDate: new Date(cardData.startDate),
          endDate: cardData.endDate ? new Date(cardData.endDate) : null,
          issueDate: new Date(cardData.issueDate || cardData.createdAt),
          remainingValue: cardData.remainingValue ? cardData.remainingValue.toString() : "",
          remainingCount: cardData.remainingCount ? cardData.remainingCount.toString() : "",
          remainingDays: cardData.remainingDays ? cardData.remainingDays.toString() : "",
          referrer: cardData.referrer || "",
          notes: cardData.notes || "",
        })

        setLoading(false)
      }, 1000)
    }
  }, [open, cardId])

  // 处理表单字段变化
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 提交表单
  const handleSubmit = () => {
    // 验证必填字段
    if (!formData.cardNumber) {
      toast({
        title: "请输入卡号",
        description: "卡号是必填项",
        variant: "destructive",
      })
      return
    }

    // 这里应该是API调用来更新会员卡
    // 模拟API调用
    toast({
      title: "正在保存...",
      description: "正在更新会员卡信息",
    })

    setTimeout(() => {
      toast({
        title: "会员卡更新成功",
        description: `会员卡 ${formData.cardNumber} 已成功更新`,
      })

      // 关闭对话框
      onOpenChange(false)

      // 调用成功回调
      if (onSuccess) {
        onSuccess()
      }
    }, 1000)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>编辑会员卡</DialogTitle>
          <DialogDescription>
            编辑会员卡信息，修改必要参数并保存更改。
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">加载会员卡数据...</span>
          </div>
        ) : cardData ? (
          <div className="space-y-6">
            {/* 会员和卡类型信息 */}
            <div className="flex flex-col md:flex-row gap-4">
              <Card className="flex-1">
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">会员信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                      {cardData.member.avatar ? (
                        <img
                          src={cardData.member.avatar}
                          alt={cardData.member.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <User className="h-5 w-5 text-primary" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-medium">{cardData.member.name}</h3>
                      <p className="text-sm text-muted-foreground">{cardData.member.phone}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="flex-1">
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">卡类型信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: cardData.cardType.color }}
                    ></div>
                    <h3 className="font-medium">{cardData.cardType.name}</h3>
                    <Badge variant="outline">{cardData.cardType.type}</Badge>
                    {cardData.cardType.isTrialCard && (
                      <Badge variant="outline" className="bg-pink-50 text-pink-700 border-pink-200">
                        <Sparkles className="mr-1 h-3 w-3" />
                        体验卡
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 卡状态信息 */}
            <div className="flex items-center justify-between bg-muted/50 p-3 rounded-md">
              <div className="flex items-center gap-2">
                <Badge
                  variant="outline"
                  className={cn(
                    "px-2 py-1",
                    cardData.status === "active" ? "bg-green-50 text-green-700 border-green-200" :
                    cardData.status === "expired" ? "bg-gray-50 text-gray-700 border-gray-200" :
                    cardData.status === "frozen" ? "bg-blue-50 text-blue-700 border-blue-200" :
                    cardData.status === "inactive" ? "bg-yellow-50 text-yellow-700 border-yellow-200" :
                    cardData.status === "onLeave" ? "bg-purple-50 text-purple-700 border-purple-200" :
                    "bg-red-50 text-red-700 border-red-200"
                  )}
                >
                  {cardData.status === "active" ? "有效" :
                   cardData.status === "expired" ? "已过期" :
                   cardData.status === "frozen" ? "已冻结" :
                   cardData.status === "inactive" ? "未激活" :
                   cardData.status === "onLeave" ? "请假中" :
                   "已退卡"}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  创建于 {format(new Date(cardData.createdAt), "yyyy年MM月dd日", { locale: zhCN })}
                </span>
              </div>
              <div>
                <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                  {cardData.cardNumber}
                </Badge>
              </div>
            </div>

            {/* 可编辑字段部分 */}
            <div className="space-y-4 border p-4 rounded-md bg-gray-50">
              <h3 className="font-medium">卡信息</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cardNumber">
                    卡号 <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="cardNumber"
                    value={formData.cardNumber}
                    onChange={(e) => handleInputChange("cardNumber", e.target.value)}
                    placeholder="请输入卡号"
                  />
                </div>

                <div className="space-y-2">
                  <Label>发卡时间</Label>
                  <div className="h-10 px-3 py-2 rounded-md border border-input bg-muted flex items-center text-muted-foreground">
                    <CalendarIcon className="mr-2 h-4 w-4 text-muted-foreground" />
                    {formData.issueDate ? (
                      format(formData.issueDate, "yyyy年MM月dd日", { locale: zhCN })
                    ) : (
                      <span>无发卡时间记录</span>
                    )}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>开始日期</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.startDate ? (
                          format(formData.startDate, "yyyy年MM月dd日", { locale: zhCN })
                        ) : (
                          <span>选择日期</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.startDate}
                        onSelect={(date) => handleInputChange("startDate", date || new Date())}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {cardData.cardType.type === "期限卡" && (
                  <div className="space-y-2">
                    <Label>结束日期</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal"
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.endDate ? (
                            format(formData.endDate, "yyyy年MM月dd日", { locale: zhCN })
                          ) : (
                            <span>选择日期</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={formData.endDate || undefined}
                          onSelect={(date) => handleInputChange("endDate", date)}
                          initialFocus
                          disabled={(date) => date < formData.startDate}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                )}
              </div>

              {/* 余额信息 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                {/* 根据卡类型显示不同的余额字段 */}
                <div className="space-y-2">
                  <Label>
                    {cardData.cardType.type === "期限卡" ? "剩余天数" :
                     cardData.cardType.type === "次卡" ? "剩余次数" :
                     "剩余金额"}
                  </Label>

                  {cardData.cardType.type === "期限卡" ? (
                    // 期限卡：只展示剩余天数，不可编辑
                    <div className="h-10 px-3 py-2 rounded-md border border-input bg-muted flex items-center text-muted-foreground">
                      {formData.remainingDays || "0"} 天
                    </div>
                  ) : cardData.cardType.type === "次卡" ? (
                    // 次数卡：显示剩余次数，可以编辑
                    <Input
                      id="remainingCount"
                      type="number"
                      value={formData.remainingCount}
                      onChange={(e) => handleInputChange("remainingCount", e.target.value)}
                      placeholder="剩余次数"
                    />
                  ) : (
                    // 储值卡：显示剩余金额，可以编辑
                    <div className="relative">
                      <span className="absolute left-3 top-2.5">¥</span>
                      <Input
                        id="remainingValue"
                        type="number"
                        className="pl-7"
                        value={formData.remainingValue}
                        onChange={(e) => handleInputChange("remainingValue", e.target.value)}
                        placeholder="剩余金额"
                      />
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="referrer">推荐人</Label>
                  <Input
                    id="referrer"
                    value={formData.referrer}
                    onChange={(e) => handleInputChange("referrer", e.target.value)}
                    placeholder="请输入推荐人姓名/手机号"
                  />
                </div>
              </div>
            </div>

            {/* 备注 */}
            <div className="space-y-2">
              <Label htmlFor="notes">备注</Label>
              <Textarea
                id="notes"
                placeholder="添加备注信息"
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
              />
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                取消
              </Button>

              <Button onClick={handleSubmit}>
                保存更改
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center py-8 text-destructive">
            <AlertCircle className="h-6 w-6 mr-2" />
            <span>无法加载会员卡数据</span>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
