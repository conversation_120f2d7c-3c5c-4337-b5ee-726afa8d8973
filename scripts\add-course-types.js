// 添加一些课程类型数据
const mysql = require('mysql2/promise');

async function addCourseTypes() {
  console.log('开始添加课程类型数据...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    // 添加课程类型数据
    console.log('\n添加课程类型数据:');
    const courseTypes = [
      {
        tenant_id: 1,
        name: '哈他瑜伽',
        description: '传统的瑜伽练习，注重体式的正确性和呼吸的配合',
        color: '#4285F4',
        display_order: 1,
        status: 1
      },
      {
        tenant_id: 1,
        name: '流瑜伽',
        description: '动态的瑜伽练习，体式之间流畅连接',
        color: '#34A853',
        display_order: 2,
        status: 1
      },
      {
        tenant_id: 1,
        name: '阴瑜伽',
        description: '静态的瑜伽练习，长时间保持体式',
        color: '#FBBC05',
        display_order: 3,
        status: 1
      },
      {
        tenant_id: 1,
        name: '空中瑜伽',
        description: '使用吊床进行的瑜伽练习，增加趣味性',
        color: '#EA4335',
        display_order: 4,
        status: 1
      },
      {
        tenant_id: 1,
        name: '孕产瑜伽',
        description: '专为孕妇和产后恢复设计的瑜伽课程',
        color: '#9C27B0',
        display_order: 5,
        status: 1
      },
      {
        tenant_id: 1,
        name: '私教课程',
        description: '一对一个性化瑜伽指导',
        color: '#FF6D91',
        display_order: 6,
        status: 1
      }
    ];

    for (const type of courseTypes) {
      const [result] = await connection.execute(
        `INSERT INTO coursetype (tenant_id, name, description, color, display_order, status, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [type.tenant_id, type.name, type.description, type.color, type.display_order, type.status]
      );
      console.log(`✓ 创建课程类型成功: ${type.name} (ID: ${result.insertId})`);
    }

    // 验证插入结果
    console.log('\n验证插入结果:');
    const [types] = await connection.execute('SELECT COUNT(*) as count FROM coursetype WHERE tenant_id = 1');
    console.log(`✓ 课程类型总数: ${types[0].count} 个`);
    
    // 显示课程类型列表
    const [typesList] = await connection.execute('SELECT id, name, color FROM coursetype WHERE tenant_id = 1 ORDER BY display_order');
    console.log('\n课程类型列表:');
    typesList.forEach((type, index) => {
      console.log(`  ${index + 1}. ${type.name} - ${type.color}`);
    });

    await connection.end();
    console.log('\n✓ 课程类型数据添加完成!');
    
  } catch (error) {
    console.error('添加失败:', error.message);
  }
}

addCourseTypes();
