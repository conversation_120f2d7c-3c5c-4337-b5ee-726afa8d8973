"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { HourlyRateManagement } from "@/components/courses/hourly-rate-management"
import { Download, Upload, FileText, Settings, Info, Save, X, ChevronRight, Search, Plus } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function HourlyRatesPage() {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("rates")
  const [showDefaultSettingsDialog, setShowDefaultSettingsDialog] = useState(false)
  const [showHolidayRateDialog, setShowHolidayRateDialog] = useState(false)
  const [showSubstituteRateDialog, setShowSubstituteRateDialog] = useState(false)
  const [showSettlementCycleDialog, setShowSettlementCycleDialog] = useState(false)
  const [showCalculationMethodDialog, setShowCalculationMethodDialog] = useState(false)
  const [showStudentCountDialog, setShowStudentCountDialog] = useState(false)
  const [showHourlyRateStatsDialog, setShowHourlyRateStatsDialog] = useState(false)
  const [showCourseDetailDialog, setShowCourseDetailDialog] = useState(false)
  const [selectedCourseId, setSelectedCourseId] = useState(null)

  // 默认课时费设置
  const [defaultRates, setDefaultRates] = useState([
    { type: "团课", color: "#4285F4", rate: "100" },
    { type: "小班课", color: "#34A853", rate: "120" },
    { type: "精品课", color: "#FBBC05", rate: "150" },
    { type: "私教课", color: "#EA4335", rate: "200" },
    { type: "教培课", color: "#9C27B0", rate: "180" },
  ])

  // 调整规则设置
  const [holidayRateAdjustment, setHolidayRateAdjustment] = useState("50")
  const [substituteRateAdjustment, setSubstituteRateAdjustment] = useState("20")
  const [settlementCycle, setSettlementCycle] = useState("monthly")
  const [calculationMethod, setCalculationMethod] = useState("actual")
  const [studentCountEnabled, setStudentCountEnabled] = useState(false)
  const [holidayEnabled, setHolidayEnabled] = useState(true)
  const [substituteEnabled, setSubstituteEnabled] = useState(true)

  // 对话框显示状态
  const [showRuleScopeDialog, setShowRuleScopeDialog] = useState(false)
  const [showHolidayRuleScopeDialog, setShowHolidayRuleScopeDialog] = useState(false)
  const [showSubstituteRuleScopeDialog, setShowSubstituteRuleScopeDialog] = useState(false)
  const [showStudentCountRuleScopeDialog, setShowStudentCountRuleScopeDialog] = useState(false)

  // 当前正在设置的规则类型
  const [currentRuleType, setCurrentRuleType] = useState("") // "general", "holiday", "substitute", "studentCount"

  // 应用方式选择 - 通用
  const [applicationMode, setApplicationMode] = useState("global") // global, courseType, specificCourse
  const [selectedCourseTypes, setSelectedCourseTypes] = useState([])
  const [selectedCourses, setSelectedCourses] = useState([])

  // 节假日规则应用方式
  const [holidayApplicationMode, setHolidayApplicationMode] = useState("global")
  const [holidaySelectedCourseTypes, setHolidaySelectedCourseTypes] = useState([])
  const [holidaySelectedCourses, setHolidaySelectedCourses] = useState([])

  // 临时代课规则应用方式
  const [substituteApplicationMode, setSubstituteApplicationMode] = useState("global")
  const [substituteSelectedCourseTypes, setSubstituteSelectedCourseTypes] = useState([])
  const [substituteSelectedCourses, setSubstituteSelectedCourses] = useState([])

  // 课程类型列表
  const courseTypes = [
    { id: 1, type: "团课", color: "#4285F4" },
    { id: 2, type: "小班课", color: "#34A853" },
    { id: 3, type: "精品课", color: "#FBBC05" },
    { id: 4, type: "私教课", color: "#EA4335" },
    { id: 5, type: "教培课", color: "#9C27B0" },
  ]

  // 具体课程列表
  const [specificCourses, setSpecificCourses] = useState([
    { id: 1, name: "哈他瑜伽初级", type: "团课", typeId: 1, instructor: "李教练" },
    { id: 2, name: "流瑜伽中级", type: "小班课", typeId: 2, instructor: "王教练" },
    { id: 3, name: "高级瑜伽进阶", type: "精品课", typeId: 3, instructor: "张教练" },
    { id: 4, name: "私人定制训练", type: "私教课", typeId: 4, instructor: "赵教练" },
    { id: 5, name: "教练培训课程", type: "教培课", typeId: 5, instructor: "刘教练" },
    { id: 6, name: "阴瑜伽放松", type: "团课", typeId: 1, instructor: "李教练" },
    { id: 7, name: "普拉提基础", type: "小班课", typeId: 2, instructor: "王教练" },
    { id: 8, name: "空中瑜伽", type: "精品课", typeId: 3, instructor: "张教练" },
    { id: 9, name: "康复训练", type: "私教课", typeId: 4, instructor: "赵教练" },
    { id: 10, name: "瑜伽教练认证", type: "教培课", typeId: 5, instructor: "刘教练" },
  ])

  // 搜索关键词
  const [searchKeyword, setSearchKeyword] = useState("")

  // 筛选后的课程列表变量
  // 注意：selectedCourses 已在上面定义，这里不需要重复定义

  // 筛选后的课程列表
  const filteredCourses = searchKeyword.trim() === ""
    ? specificCourses
    : specificCourses.filter(course =>
        course.name.includes(searchKeyword) ||
        course.type.includes(searchKeyword) ||
        course.instructor.includes(searchKeyword)
      )

  // 课程详情数据
  const courseDetailData = [
    {
      id: 1,
      name: "私人定制训练",
      type: "私教课",
      coach: "李教练",
      date: "2023-10-15",
      time: "10:00-11:00",
      fee: 200,
      adjustments: [],
      attendees: [
        { name: "张三", card: "私教次卡", consumption: "1次", value: "¥200", status: "已签到" }
      ],
      cards: [
        { name: "私教次卡", type: "次数卡", price: "¥200/次", consumption: "1次", remaining: "剩余9次" }
      ]
    },
    {
      id: 2,
      name: "哈他瑜伽初级",
      type: "团课",
      coach: "李教练",
      date: "2023-10-15",
      time: "14:00-15:00",
      fee: 100,
      adjustments: [],
      attendees: [
        { name: "李四", card: "瑜伽年卡", consumption: "不限次", value: "¥10", status: "已签到" },
        { name: "王五", card: "瑜伽季卡", consumption: "不限次", value: "¥15", status: "已签到" },
        { name: "赵六", card: "瑜伽月卡", consumption: "不限次", value: "¥20", status: "已签到" }
      ],
      cards: [
        { name: "瑜伽年卡", type: "时间卡", price: "¥3600/年", consumption: "不限次", remaining: "剩余245天" },
        { name: "瑜伽季卡", type: "时间卡", price: "¥1200/季", consumption: "不限次", remaining: "剩余65天" },
        { name: "瑜伽月卡", type: "时间卡", price: "¥400/月", consumption: "不限次", remaining: "剩余18天" }
      ]
    },
    {
      id: 3,
      name: "流瑜伽中级",
      type: "小班课",
      coach: "王教练",
      date: "2023-10-15",
      time: "16:00-17:00",
      fee: 120,
      adjustments: [{ type: "holiday", amount: 60 }],
      attendees: [
        { name: "张三", card: "瑜伽年卡", consumption: "不限次", value: "¥12", status: "已签到" },
        { name: "李四", card: "瑜伽季卡", consumption: "不限次", value: "¥18", status: "已签到" }
      ],
      cards: [
        { name: "瑜伽年卡", type: "时间卡", price: "¥3600/年", consumption: "不限次", remaining: "剩余245天" },
        { name: "瑜伽季卡", type: "时间卡", price: "¥1200/季", consumption: "不限次", remaining: "剩余65天" }
      ]
    }
  ]

  // 获取选中课程的详情
  const getSelectedCourseDetail = () => {
    return courseDetailData.find(course => course.id === selectedCourseId) || courseDetailData[0]
  }

  // 学员人数区间设置
  const [studentCountRanges, setStudentCountRanges] = useState([
    { min: 1, max: 5, adjustment: 0, isBase: true },
    { min: 6, max: 10, adjustment: 10, isBase: false },
    { min: 11, max: 15, adjustment: 20, isBase: false },
    { min: 16, max: null, adjustment: 30, isBase: false },
  ])

  // 学员人数调整规则应用方式
  const [studentCountApplicationMode, setStudentCountApplicationMode] = useState("global")
  const [studentCountSelectedCourseTypes, setStudentCountSelectedCourseTypes] = useState([])
  const [studentCountSelectedCourses, setStudentCountSelectedCourses] = useState([])

  // 保存默认课时费设置
  const handleSaveDefaultRates = () => {
    // 这里应该有保存到后端的逻辑
    toast({
      title: "保存成功",
      description: "默认课时费设置已更新"
    })
    setShowDefaultSettingsDialog(false)
  }

  // 保存节假日课时费调整
  const handleSaveHolidayRate = () => {
    toast({
      title: "保存成功",
      description: "节假日课时费调整规则已更新"
    })
    setShowHolidayRateDialog(false)
  }

  // 保存临时代课课时费调整
  const handleSaveSubstituteRate = () => {
    toast({
      title: "保存成功",
      description: "临时代课课时费调整规则已更新"
    })
    setShowSubstituteRateDialog(false)
  }

  // 保存结算周期
  const handleSaveSettlementCycle = () => {
    toast({
      title: "保存成功",
      description: "课时费结算周期已更新"
    })
    setShowSettlementCycleDialog(false)
  }

  // 保存计算方式
  const handleSaveCalculationMethod = () => {
    toast({
      title: "保存成功",
      description: "课时费计算方式已更新"
    })
    setShowCalculationMethodDialog(false)
  }

  // 保存学员人数调整
  const handleSaveStudentCountAdjustment = () => {
    toast({
      title: "保存成功",
      description: "学员人数调整规则已更新"
    })
    setShowStudentCountDialog(false)
  }

  // 添加新的人数区间
  const addStudentCountRange = () => {
    // 限制最多添加十个区间
    if (studentCountRanges.length >= 10) {
      toast({
        title: "无法添加",
        description: "最多只能添加10个人数区间",
        variant: "destructive"
      })
      return
    }

    const lastRange = studentCountRanges[studentCountRanges.length - 1]
    const newMin = lastRange.max ? lastRange.max + 1 : 16
    const newMax = newMin + 4

    setStudentCountRanges([
      ...studentCountRanges,
      { min: newMin, max: newMax, adjustment: lastRange.adjustment + 10, isBase: false }
    ])

    toast({
      title: "已添加",
      description: `已添加新的人数区间: ${newMin}-${newMax}人`
    })
  }

  // 删除人数区间
  const deleteStudentCountRange = (index) => {
    // 不允许删除所有区间，至少保留一个
    if (studentCountRanges.length <= 1) {
      toast({
        title: "无法删除",
        description: "至少需要保留一个人数区间",
        variant: "destructive"
      })
      return
    }

    // 如果删除的是基准区间，则将第一个区间设为基准
    const isRemovingBase = studentCountRanges[index].isBase

    const newRanges = studentCountRanges.filter((_, i) => i !== index)

    if (isRemovingBase && newRanges.length > 0) {
      newRanges[0].isBase = true
      newRanges[0].adjustment = 0
    }

    setStudentCountRanges(newRanges)

    toast({
      title: "已删除",
      description: "已删除选定的人数区间"
    })
  }

  // 更新人数区间
  const updateStudentCountRange = (index, field, value) => {
    const newRanges = [...studentCountRanges]

    if (field === 'min' || field === 'max') {
      // 确保值是数字或null
      const numValue = value === '' ? null : parseInt(value)
      newRanges[index][field] = numValue
    } else if (field === 'adjustment') {
      // 确保调整值是数字
      newRanges[index][field] = parseInt(value) || 0
    } else if (field === 'isBase') {
      // 如果设置了新的基准区间，取消其他区间的基准状态
      if (value === true) {
        newRanges.forEach((range, i) => {
          newRanges[i].isBase = i === index
          // 如果是基准区间，调整值为0
          if (i === index) {
            newRanges[i].adjustment = 0
          }
        })
      } else {
        newRanges[index].isBase = false
      }
    }

    setStudentCountRanges(newRanges)
  }

  // 切换学员人数调整启用状态
  const toggleStudentCountEnabled = () => {
    setStudentCountEnabled(!studentCountEnabled)
    toast({
      title: studentCountEnabled ? "已禁用" : "已启用",
      description: studentCountEnabled
        ? "学员人数调整规则已禁用"
        : "学员人数调整规则已启用"
    })
  }

  // 切换节假日课时费调整启用状态
  const toggleHolidayEnabled = () => {
    setHolidayEnabled(!holidayEnabled)
    toast({
      title: holidayEnabled ? "已禁用" : "已启用",
      description: holidayEnabled
        ? "节假日课时费调整规则已禁用"
        : "节假日课时费调整规则已启用"
    })
  }

  // 切换临时代课调整启用状态
  const toggleSubstituteEnabled = () => {
    setSubstituteEnabled(!substituteEnabled)
    toast({
      title: substituteEnabled ? "已禁用" : "已启用",
      description: substituteEnabled
        ? "临时代课调整规则已禁用"
        : "临时代课调整规则已启用"
    })
  }

  // 打开应用范围设置对话框
  const openRuleScopeDialog = (ruleType = "general") => {
    setCurrentRuleType(ruleType)

    // 根据规则类型打开相应的对话框
    if (ruleType === "holiday") {
      setShowHolidayRuleScopeDialog(true)
    } else if (ruleType === "substitute") {
      setShowSubstituteRuleScopeDialog(true)
    } else if (ruleType === "studentCount") {
      setShowStudentCountRuleScopeDialog(true)
    } else {
      setShowRuleScopeDialog(true)
    }
  }

  // 切换应用方式
  const selectApplicationMode = (mode) => {
    // 根据当前规则类型选择相应的状态更新函数
    if (currentRuleType === "holiday") {
      setHolidayApplicationMode(mode)

      // 如果切换到全局应用，清空选中的课程类型和课程
      if (mode === "global") {
        setHolidaySelectedCourseTypes([])
        setHolidaySelectedCourses([])
      }
    } else if (currentRuleType === "substitute") {
      setSubstituteApplicationMode(mode)

      // 如果切换到全局应用，清空选中的课程类型和课程
      if (mode === "global") {
        setSubstituteSelectedCourseTypes([])
        setSubstituteSelectedCourses([])
      }
    } else if (currentRuleType === "studentCount") {
      setStudentCountApplicationMode(mode)

      // 如果切换到全局应用，清空选中的课程类型和课程
      if (mode === "global") {
        setStudentCountSelectedCourseTypes([])
        setStudentCountSelectedCourses([])
      }
    } else {
      setApplicationMode(mode)

      // 如果切换到全局应用，清空选中的课程类型和课程
      if (mode === "global") {
        setSelectedCourseTypes([])
        setSelectedCourses([])
      }
    }
  }

  // 切换课程类型选择
  const toggleCourseType = (courseTypeId) => {
    // 根据当前规则类型选择相应的状态更新函数
    if (currentRuleType === "holiday") {
      setHolidaySelectedCourseTypes(prev => {
        if (prev.includes(courseTypeId)) {
          return prev.filter(id => id !== courseTypeId)
        } else {
          return [...prev, courseTypeId]
        }
      })
    } else if (currentRuleType === "substitute") {
      setSubstituteSelectedCourseTypes(prev => {
        if (prev.includes(courseTypeId)) {
          return prev.filter(id => id !== courseTypeId)
        } else {
          return [...prev, courseTypeId]
        }
      })
    } else if (currentRuleType === "studentCount") {
      setStudentCountSelectedCourseTypes(prev => {
        if (prev.includes(courseTypeId)) {
          return prev.filter(id => id !== courseTypeId)
        } else {
          return [...prev, courseTypeId]
        }
      })
    } else {
      setSelectedCourseTypes(prev => {
        if (prev.includes(courseTypeId)) {
          return prev.filter(id => id !== courseTypeId)
        } else {
          return [...prev, courseTypeId]
        }
      })
    }
  }

  // 切换具体课程选择
  const toggleCourse = (courseId) => {
    // 根据当前规则类型选择相应的状态更新函数
    if (currentRuleType === "holiday") {
      setHolidaySelectedCourses(prev => {
        if (prev.includes(courseId)) {
          return prev.filter(id => id !== courseId)
        } else {
          return [...prev, courseId]
        }
      })
    } else if (currentRuleType === "substitute") {
      setSubstituteSelectedCourses(prev => {
        if (prev.includes(courseId)) {
          return prev.filter(id => id !== courseId)
        } else {
          return [...prev, courseId]
        }
      })
    } else if (currentRuleType === "studentCount") {
      setStudentCountSelectedCourses(prev => {
        if (prev.includes(courseId)) {
          return prev.filter(id => id !== courseId)
        } else {
          return [...prev, courseId]
        }
      })
    } else {
      setSelectedCourses(prev => {
        if (prev.includes(courseId)) {
          return prev.filter(id => id !== courseId)
        } else {
          return [...prev, courseId]
        }
      })
    }
  }

  // 处理搜索输入变化
  const handleSearchChange = (e) => {
    setSearchKeyword(e.target.value)
  }

  // 清除搜索
  const clearSearch = () => {
    setSearchKeyword("")
  }

  // 保存应用范围设置
  const saveRuleScope = () => {
    let description = ""
    let mode, courseTypesList, coursesList
    let closeDialog

    // 根据当前规则类型获取相应的状态
    if (currentRuleType === "holiday") {
      mode = holidayApplicationMode
      courseTypesList = holidaySelectedCourseTypes
      coursesList = holidaySelectedCourses
      closeDialog = () => setShowHolidayRuleScopeDialog(false)
    } else if (currentRuleType === "substitute") {
      mode = substituteApplicationMode
      courseTypesList = substituteSelectedCourseTypes
      coursesList = substituteSelectedCourses
      closeDialog = () => setShowSubstituteRuleScopeDialog(false)
    } else if (currentRuleType === "studentCount") {
      mode = studentCountApplicationMode
      courseTypesList = studentCountSelectedCourseTypes
      coursesList = studentCountSelectedCourses
      closeDialog = () => setShowStudentCountRuleScopeDialog(false)
    } else {
      mode = applicationMode
      courseTypesList = selectedCourseTypes
      coursesList = selectedCourses
      closeDialog = () => setShowRuleScopeDialog(false)
    }

    // 根据应用方式生成描述
    if (mode === "global") {
      description = "规则将应用于所有课程类型和课程"
    } else if (mode === "courseType") {
      const selectedTypes = courseTypesList.map(id =>
        courseTypes.find(type => type.id === id)?.type
      ).filter(Boolean)

      if (selectedTypes.length === 0) {
        toast({
          title: "无法保存",
          description: "请至少选择一个课程类型",
          variant: "destructive"
        })
        return
      }

      description = `规则将应用于以下课程类型: ${selectedTypes.join(', ')}`
    } else if (mode === "specificCourse") {
      const selectedCourseNames = coursesList.map(id =>
        specificCourses.find(course => course.id === id)?.name
      ).filter(Boolean)

      if (selectedCourseNames.length === 0) {
        toast({
          title: "无法保存",
          description: "请至少选择一个具体课程",
          variant: "destructive"
        })
        return
      }

      description = `规则将应用于以下课程: ${selectedCourseNames.join(', ')}`
    }

    // 显示成功提示
    toast({
      title: "保存成功",
      description
    })

    // 关闭对话框
    closeDialog()
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">课时费管理</h1>
          <p className="text-muted-foreground">管理教练的课程类型课时费标准</p>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowHourlyRateStatsDialog(true)}>
            <FileText className="mr-2 h-4 w-4" />
            课时费统计
          </Button>
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            导入
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="rates">课时费设置</TabsTrigger>
          <TabsTrigger value="stats">统计分析</TabsTrigger>
          <TabsTrigger value="detailed-stats">详细统计</TabsTrigger>
          <TabsTrigger value="settings">全局设置</TabsTrigger>
        </TabsList>

        <TabsContent value="rates" className="mt-6">
          <HourlyRateManagement />
        </TabsContent>

        <TabsContent value="stats" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">平均课时费</CardTitle>
                <CardDescription>所有教练和课程类型</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥135.50</div>
                <p className="text-xs text-muted-foreground mt-1">较上月 +5.2%</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">最高课时费</CardTitle>
                <CardDescription>私教课</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥200.00</div>
                <p className="text-xs text-muted-foreground mt-1">李教练</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">课时费设置数</CardTitle>
                <CardDescription>已配置的课时费记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">6</div>
                <p className="text-xs text-muted-foreground mt-1">覆盖 5 位教练</p>
              </CardContent>
            </Card>
          </div>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>课时费统计</CardTitle>
              <CardDescription>各课程类型的平均课时费</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex flex-col items-center justify-center">
                <div className="w-full max-w-3xl">
                  <div className="space-y-6">
                    {[
                      { type: "团课", color: "#4285F4", avg: 100, count: 8 },
                      { type: "小班课", color: "#34A853", avg: 120, count: 6 },
                      { type: "精品课", color: "#FBBC05", avg: 150, count: 5 },
                      { type: "私教课", color: "#EA4335", avg: 200, count: 4 },
                      { type: "教培课", color: "#9C27B0", avg: 180, count: 3 }
                    ].map((item, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                            <span className="font-medium">{item.type}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">{item.count} 条记录</span>
                            <span className="font-medium">¥{item.avg}</span>
                          </div>
                        </div>
                        <div className="h-2 w-full rounded-full bg-gray-100 overflow-hidden">
                          <div
                            className="h-full rounded-full"
                            style={{
                              width: `${(item.avg / 200) * 100}%`,
                              backgroundColor: item.color
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-6">图表区域 - 课时费统计图表将在此显示</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>课时费分布</CardTitle>
              <CardDescription>按课程和教练的课时费分布情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium mb-4">教练课时费排名</h3>
                  <div className="space-y-3">
                    {[
                      { name: "李教练", avg: 160, count: 5 },
                      { name: "王教练", avg: 145, count: 4 },
                      { name: "张教练", avg: 130, count: 3 },
                      { name: "赵教练", avg: 120, count: 2 },
                      { name: "刘教练", avg: 110, count: 1 }
                    ].map((coach, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
                            {index + 1}
                          </div>
                          <div>
                            <div className="font-medium">{coach.name}</div>
                            <div className="text-xs text-muted-foreground">{coach.count} 个课程</div>
                          </div>
                        </div>
                        <div className="font-medium">¥{coach.avg}</div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-4">课程课时费排名</h3>
                  <div className="space-y-3">
                    {[
                      { name: "私教课", type: "私教课", avg: 200 },
                      { name: "教练培训课", type: "教培课", avg: 180 },
                      { name: "高级瑜伽进阶", type: "精品课", avg: 150 },
                      { name: "流瑜伽中级", type: "小班课", avg: 120 },
                      { name: "哈他瑜伽初级", type: "团课", avg: 100 }
                    ].map((course, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{course.name}</div>
                          <div className="text-xs text-muted-foreground">{course.type}</div>
                        </div>
                        <div className="font-medium">¥{course.avg}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="detailed-stats" className="mt-6">
          <Card className="mb-6">
            <CardHeader>
              <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
                <div>
                  <CardTitle>课时费详细统计</CardTitle>
                  <CardDescription>按教练或课程查看每节课的课时费详情</CardDescription>
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <div className="relative">
                    <Input
                      type="date"
                      placeholder="开始日期"
                      className="w-full sm:w-36"
                    />
                  </div>
                  <div className="relative">
                    <Input
                      type="date"
                      placeholder="结束日期"
                      className="w-full sm:w-36"
                    />
                  </div>
                  <Button variant="outline" size="sm" className="h-10">
                    筛选
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center space-x-2">
                      <div className="h-4 w-4 rounded-full bg-primary"></div>
                      <span className="font-medium">按教练查看</span>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <div className="h-4 w-4 rounded-full bg-gray-300"></div>
                      <span>按课程查看</span>
                    </div>
                  </div>
                  <div className="relative w-full sm:w-64">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索教练姓名"
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="border rounded-md overflow-hidden">
                  <div className="grid grid-cols-12 bg-muted px-4 py-3 font-medium text-sm">
                    <div className="col-span-3">教练</div>
                    <div className="col-span-2">课程类型</div>
                    <div className="col-span-3">课程名称</div>
                    <div className="col-span-2">上课日期</div>
                    <div className="col-span-2 text-right">课时费</div>
                  </div>

                  <div className="divide-y">
                    {[
                      { id: 1, coach: "李教练", type: "私教课", name: "私人定制训练", date: "2023-10-15", time: "10:00-11:00", fee: 200, adjustments: [], attendees: 1, membershipCards: ["私教次卡", "储值卡"] },
                      { id: 2, coach: "李教练", type: "团课", name: "哈他瑜伽初级", date: "2023-10-15", time: "14:00-15:00", fee: 100, adjustments: [], attendees: 8, membershipCards: ["瑜伽年卡", "瑜伽季卡", "瑜伽月卡"] },
                      { id: 3, coach: "王教练", type: "小班课", name: "流瑜伽中级", date: "2023-10-15", time: "16:00-17:00", fee: 120, adjustments: [{ type: "holiday", amount: 60 }], attendees: 6, membershipCards: ["瑜伽年卡", "瑜伽季卡"] },
                      { id: 4, coach: "张教练", type: "精品课", name: "高级瑜伽进阶", date: "2023-10-16", time: "09:00-10:30", fee: 150, adjustments: [{ type: "studentCount", amount: 30 }], attendees: 5, membershipCards: ["瑜伽年卡"] },
                      { id: 5, coach: "赵教练", type: "私教课", name: "康复训练", date: "2023-10-16", time: "11:00-12:00", fee: 200, adjustments: [], attendees: 1, membershipCards: ["私教次卡"] },
                      { id: 6, coach: "刘教练", type: "教培课", name: "瑜伽教练认证", date: "2023-10-16", time: "13:00-15:00", fee: 180, adjustments: [{ type: "substitute", amount: 36 }], attendees: 3, membershipCards: ["教练培训卡"] },
                      { id: 7, coach: "李教练", type: "团课", name: "阴瑜伽放松", date: "2023-10-17", time: "18:00-19:00", fee: 100, adjustments: [], attendees: 10, membershipCards: ["瑜伽年卡", "瑜伽季卡", "瑜伽月卡"] },
                      { id: 8, coach: "王教练", type: "小班课", name: "普拉提基础", date: "2023-10-17", time: "19:00-20:00", fee: 120, adjustments: [], attendees: 4, membershipCards: ["瑜伽年卡", "瑜伽季卡"] },
                      { id: 9, coach: "张教练", type: "精品课", name: "空中瑜伽", date: "2023-10-17", time: "20:00-21:00", fee: 150, adjustments: [], attendees: 5, membershipCards: ["瑜伽年卡"] },
                      { id: 10, coach: "李教练", type: "私教课", name: "私人定制训练", date: "2023-10-18", time: "10:00-11:00", fee: 200, adjustments: [], attendees: 1, membershipCards: ["私教次卡", "储值卡"] },
                    ].map((item, index) => {
                      // 计算总课时费（基础课时费 + 所有调整）
                      const adjustmentTotal = item.adjustments.reduce((sum, adj) => sum + adj.amount, 0)
                      const totalFee = item.fee + adjustmentTotal

                      return (
                        <div
                          key={index}
                          className="grid grid-cols-12 px-4 py-3 hover:bg-accent/50 cursor-pointer"
                          onClick={() => {
                            setSelectedCourseId(item.id)
                            setShowCourseDetailDialog(true)
                          }}
                        >
                          <div className="col-span-3 flex items-center">
                            <div className="font-medium">{item.coach}</div>
                          </div>
                          <div className="col-span-2 flex items-center">
                            <span>{item.type}</span>
                          </div>
                          <div className="col-span-3 flex items-center">
                            <span>{item.name}</span>
                          </div>
                          <div className="col-span-2 flex items-center">
                            <span>{item.date}</span>
                          </div>
                          <div className="col-span-2 text-right">
                            <div className="font-medium">¥{totalFee}</div>
                            {item.adjustments.length > 0 && (
                              <div className="text-xs text-muted-foreground">
                                基础: ¥{item.fee}
                                {item.adjustments.map((adj, i) => (
                                  <span key={i}>
                                    {adj.type === "holiday" && " +节假日"}
                                    {adj.type === "substitute" && " +代课"}
                                    {adj.type === "studentCount" && " +人数"}
                                    : ¥{adj.amount}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>

                  <div className="bg-muted px-4 py-3 flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      显示 1-10 条，共 42 条记录
                    </div>
                    <div className="flex items-center gap-1">
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <ChevronRight className="h-4 w-4 rotate-180" />
                      </Button>
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        1
                      </Button>
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        2
                      </Button>
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        3
                      </Button>
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">课时费汇总</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">总课时费</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">¥5,680.00</div>
                      <p className="text-xs text-muted-foreground mt-1">42 节课</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">李教练课时费</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">¥1,600.00</div>
                      <p className="text-xs text-muted-foreground mt-1">10 节课</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">私教课课时费</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">¥2,400.00</div>
                      <p className="text-xs text-muted-foreground mt-1">12 节课</p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>课时费调整明细</CardTitle>
              <CardDescription>查看所有课时费调整记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border rounded-md overflow-hidden">
                <div className="grid grid-cols-12 bg-muted px-4 py-3 font-medium text-sm">
                  <div className="col-span-2">调整类型</div>
                  <div className="col-span-2">教练</div>
                  <div className="col-span-3">课程</div>
                  <div className="col-span-2">日期</div>
                  <div className="col-span-1">基础课时费</div>
                  <div className="col-span-1">调整金额</div>
                  <div className="col-span-1 text-right">最终课时费</div>
                </div>

                <div className="divide-y">
                  {[
                    { type: "holiday", coach: "王教练", course: "流瑜伽中级", date: "2023-10-15", baseFee: 120, adjustment: 60, totalFee: 180 },
                    { type: "studentCount", coach: "张教练", course: "高级瑜伽进阶", date: "2023-10-16", baseFee: 150, adjustment: 30, totalFee: 180 },
                    { type: "substitute", coach: "刘教练", course: "瑜伽教练认证", date: "2023-10-16", baseFee: 180, adjustment: 36, totalFee: 216 },
                    { type: "holiday", coach: "李教练", course: "私人定制训练", date: "2023-10-01", baseFee: 200, adjustment: 100, totalFee: 300 },
                    { type: "studentCount", coach: "王教练", course: "普拉提基础", date: "2023-10-10", baseFee: 120, adjustment: 24, totalFee: 144 },
                  ].map((item, index) => (
                    <div key={index} className="grid grid-cols-12 px-4 py-3 hover:bg-accent/50 cursor-pointer">
                      <div className="col-span-2 flex items-center">
                        <div className={`h-2 w-2 rounded-full mr-2 ${
                          item.type === "holiday" ? "bg-blue-500" :
                          item.type === "substitute" ? "bg-green-500" :
                          "bg-orange-500"
                        }`} />
                        <span>
                          {item.type === "holiday" && "节假日调整"}
                          {item.type === "substitute" && "临时代课"}
                          {item.type === "studentCount" && "学员人数"}
                        </span>
                      </div>
                      <div className="col-span-2 flex items-center">
                        <span>{item.coach}</span>
                      </div>
                      <div className="col-span-3 flex items-center">
                        <span>{item.course}</span>
                      </div>
                      <div className="col-span-2 flex items-center">
                        <span>{item.date}</span>
                      </div>
                      <div className="col-span-1 flex items-center">
                        <span>¥{item.baseFee}</span>
                      </div>
                      <div className="col-span-1 flex items-center">
                        <span className="text-green-600">+¥{item.adjustment}</span>
                      </div>
                      <div className="col-span-1 text-right font-medium">
                        ¥{item.totalFee}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="bg-muted px-4 py-3 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    显示 1-5 条，共 15 条记录
                  </div>
                  <div className="flex items-center gap-1">
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      <ChevronRight className="h-4 w-4 rotate-180" />
                    </Button>
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      1
                    </Button>
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      2
                    </Button>
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      3
                    </Button>
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>全局课时费设置</CardTitle>
              <CardDescription>设置课时费的全局规则和默认值</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col gap-2">
                <h3 className="font-medium">默认课时费标准</h3>
                <p className="text-sm text-muted-foreground">
                  当没有为特定教练和课程类型设置课时费时，将使用以下默认值
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-2">
                  {[
                    { type: "团课", color: "#4285F4", rate: 100 },
                    { type: "小班课", color: "#34A853", rate: 120 },
                    { type: "精品课", color: "#FBBC05", rate: 150 },
                    { type: "私教课", color: "#EA4335", rate: 200 },
                    { type: "教培课", color: "#9C27B0", rate: 180 },
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-md">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                        <span>{item.type}</span>
                      </div>
                      <span className="font-medium">¥{item.rate}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end">
                <Button onClick={() => setShowDefaultSettingsDialog(true)}>
                  <Settings className="mr-2 h-4 w-4" />
                  修改默认设置
                </Button>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>课时费计算规则</CardTitle>
                <CardDescription>设置课时费的计算方式和规则</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">计算优先级</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="flex h-5 w-5 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs">1</div>
                      <span>具体课程的课时费设置</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex h-5 w-5 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs">2</div>
                      <span>教练的课程类型默认课时费</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex h-5 w-5 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs">3</div>
                      <span>全局默认课时费</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium">课时费结算周期</h3>
                  <div className="flex items-center justify-between p-3 border rounded-md">
                    <div className="flex-1">
                      {settlementCycle === "monthly" && "每月结算"}
                      {settlementCycle === "weekly" && "每周结算"}
                      {settlementCycle === "biweekly" && "每两周结算"}
                    </div>
                    <Button variant="outline" size="sm" onClick={() => setShowSettlementCycleDialog(true)}>修改</Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium">课时费计算方式</h3>
                  <div className="flex items-center justify-between p-3 border rounded-md">
                    <div className="flex-1">
                      {calculationMethod === "actual" && "按实际上课时长计算"}
                      {calculationMethod === "fixed" && "按固定课时计算"}
                      {calculationMethod === "scheduled" && "按排课时长计算"}
                    </div>
                    <Button variant="outline" size="sm" onClick={() => setShowCalculationMethodDialog(true)}>修改</Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>课时费调整规则</CardTitle>
                <CardDescription>设置特殊情况下的课时费调整规则</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">节假日课时费调整</h3>
                    <div
                      className={`flex h-6 w-11 items-center rounded-full p-1 cursor-pointer ${holidayEnabled ? 'bg-primary' : 'bg-muted'}`}
                      onClick={toggleHolidayEnabled}
                    >
                      <div
                        className={`h-4 w-4 rounded-full bg-white transition-transform ${holidayEnabled ? 'translate-x-5' : ''}`}
                      />
                    </div>
                  </div>
                  <div className={`flex items-center justify-between p-3 border rounded-md ${!holidayEnabled ? 'text-muted-foreground' : ''}`}>
                    <div className="flex-1">节假日课时费上浮 {holidayRateAdjustment}%</div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowHolidayRateDialog(true)}
                      disabled={!holidayEnabled}
                    >
                      修改
                    </Button>
                  </div>

                  <p className="text-xs text-muted-foreground mt-2">
                    系统将根据国家法定节假日自动识别，包括元旦、春节、清明节、劳动节、端午节、中秋节和国庆节等
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">临时代课调整</h3>
                    <div
                      className={`flex h-6 w-11 items-center rounded-full p-1 cursor-pointer ${substituteEnabled ? 'bg-primary' : 'bg-muted'}`}
                      onClick={toggleSubstituteEnabled}
                    >
                      <div
                        className={`h-4 w-4 rounded-full bg-white transition-transform ${substituteEnabled ? 'translate-x-5' : ''}`}
                      />
                    </div>
                  </div>
                  <div className={`flex items-center justify-between p-3 border rounded-md ${!substituteEnabled ? 'text-muted-foreground' : ''}`}>
                    <div className="flex-1">临时代课课时费上浮 {substituteRateAdjustment}%</div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowSubstituteRateDialog(true)}
                      disabled={!substituteEnabled}
                    >
                      修改
                    </Button>
                  </div>

                  <p className="text-xs text-muted-foreground mt-2">
                    当原定教练无法上课，由其他教练临时替代时，系统将自动识别为临时代课。需在排课系统中标记为"临时代课"
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">学员人数调整</h3>
                    <div
                      className={`flex h-6 w-11 items-center rounded-full p-1 cursor-pointer ${studentCountEnabled ? 'bg-primary' : 'bg-muted'}`}
                      onClick={toggleStudentCountEnabled}
                    >
                      <div
                        className={`h-4 w-4 rounded-full bg-white transition-transform ${studentCountEnabled ? 'translate-x-5' : ''}`}
                      />
                    </div>
                  </div>
                  <div className={`flex items-center justify-between p-3 border rounded-md ${!studentCountEnabled ? 'text-muted-foreground' : ''}`}>
                    <div className="flex-1">根据学员人数调整课时费</div>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={!studentCountEnabled}
                      onClick={() => studentCountEnabled && setShowStudentCountDialog(true)}
                    >
                      修改
                    </Button>
                  </div>

                  <p className="text-xs text-muted-foreground mt-2">
                    系统将根据实际签到的学员人数自动计算课时费调整。可针对不同课程类型或具体课程单独设置调整规则
                  </p>
                </div>

                <div className="mt-6 pt-4 border-t">
                  <h3 className="text-sm font-medium mb-3">调整规则应用范围</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-md">
                      <div>
                        <div className="font-medium">全局应用</div>
                        <div className="text-xs text-muted-foreground">应用于所有课程类型和课程</div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={openRuleScopeDialog}
                      >
                        设置例外
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* 修改默认课时费设置对话框 */}
      <Dialog open={showDefaultSettingsDialog} onOpenChange={setShowDefaultSettingsDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>修改默认课时费设置</DialogTitle>
            <DialogDescription>
              设置全局默认课时费标准，当没有为特定教练和课程类型设置课时费时，将使用以下默认值
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            {defaultRates.map((item, index) => (
              <div key={index} className="grid gap-2">
                <div className="flex items-center gap-2">
                  <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                  <Label htmlFor={`rate-${index}`}>{item.type}默认课时费</Label>
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    id={`rate-${index}`}
                    type="number"
                    min="0"
                    step="1"
                    value={item.rate}
                    onChange={(e) => {
                      const newRates = [...defaultRates]
                      newRates[index].rate = e.target.value
                      setDefaultRates(newRates)
                    }}
                    className="w-full"
                  />
                  <span className="text-sm text-muted-foreground whitespace-nowrap">元/课时</span>
                </div>
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDefaultSettingsDialog(false)}>
              取消
            </Button>
            <Button onClick={handleSaveDefaultRates}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 节假日课时费调整对话框 */}
      <Dialog open={showHolidayRateDialog} onOpenChange={setShowHolidayRateDialog}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>节假日课时费调整</DialogTitle>
            <DialogDescription>
              设置节假日课时费的调整比例，系统将根据国家法定节假日自动应用此规则
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="holiday-rate">节假日课时费上浮比例</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="holiday-rate"
                  type="number"
                  min="0"
                  max="200"
                  step="5"
                  value={holidayRateAdjustment}
                  onChange={(e) => setHolidayRateAdjustment(e.target.value)}
                  className="w-full"
                />
                <span className="text-sm text-muted-foreground whitespace-nowrap">%</span>
              </div>
              <p className="text-sm text-muted-foreground">
                设置为 50 表示节假日课时费为平时的 1.5 倍。系统会自动识别法定节假日并应用此规则
              </p>
            </div>

            <div className="grid gap-2 pt-2 mt-2 border-t">
              <div>
                <h4 className="text-sm font-medium">应用范围</h4>
                <p className="text-xs text-muted-foreground mt-1 mb-2">选择此规则适用的课程类型或具体课程</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-between"
                  onClick={() => openRuleScopeDialog("holiday")}
                  disabled={!holidayEnabled}
                >
                  <span>设置应用范围</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowHolidayRateDialog(false)}>
              取消
            </Button>
            <Button onClick={handleSaveHolidayRate}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 临时代课调整对话框 */}
      <Dialog open={showSubstituteRateDialog} onOpenChange={setShowSubstituteRateDialog}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>临时代课课时费调整</DialogTitle>
            <DialogDescription>
              设置临时代课课时费的调整比例，需在排课系统中标记为"临时代课"才会生效
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="substitute-rate">临时代课课时费上浮比例</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="substitute-rate"
                  type="number"
                  min="0"
                  max="100"
                  step="5"
                  value={substituteRateAdjustment}
                  onChange={(e) => setSubstituteRateAdjustment(e.target.value)}
                  className="w-full"
                />
                <span className="text-sm text-muted-foreground whitespace-nowrap">%</span>
              </div>
              <p className="text-sm text-muted-foreground">
                设置为 20 表示临时代课课时费为原课时费的 1.2 倍。需在排课系统中标记为"临时代课"才会生效
              </p>
            </div>

            <div className="grid gap-2 pt-2 mt-2 border-t">
              <div>
                <h4 className="text-sm font-medium">应用范围</h4>
                <p className="text-xs text-muted-foreground mt-1 mb-2">选择此规则适用的课程类型或具体课程</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-between"
                  onClick={() => openRuleScopeDialog("substitute")}
                  disabled={!substituteEnabled}
                >
                  <span>设置应用范围</span>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSubstituteRateDialog(false)}>
              取消
            </Button>
            <Button onClick={handleSaveSubstituteRate}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 结算周期对话框 */}
      <Dialog open={showSettlementCycleDialog} onOpenChange={setShowSettlementCycleDialog}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>课时费结算周期</DialogTitle>
            <DialogDescription>
              设置课时费的结算周期
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label>选择结算周期</Label>
              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                     onClick={() => setSettlementCycle("monthly")}>
                  <div className={`h-4 w-4 rounded-full ${settlementCycle === "monthly" ? "bg-primary" : "border"}`} />
                  <div className="flex-1">每月结算</div>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                     onClick={() => setSettlementCycle("biweekly")}>
                  <div className={`h-4 w-4 rounded-full ${settlementCycle === "biweekly" ? "bg-primary" : "border"}`} />
                  <div className="flex-1">每两周结算</div>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                     onClick={() => setSettlementCycle("weekly")}>
                  <div className={`h-4 w-4 rounded-full ${settlementCycle === "weekly" ? "bg-primary" : "border"}`} />
                  <div className="flex-1">每周结算</div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSettlementCycleDialog(false)}>
              取消
            </Button>
            <Button onClick={handleSaveSettlementCycle}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 计算方式对话框 */}
      <Dialog open={showCalculationMethodDialog} onOpenChange={setShowCalculationMethodDialog}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>课时费计算方式</DialogTitle>
            <DialogDescription>
              设置课时费的计算方式
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label>选择计算方式</Label>
              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                     onClick={() => setCalculationMethod("actual")}>
                  <div className={`h-4 w-4 rounded-full ${calculationMethod === "actual" ? "bg-primary" : "border"}`} />
                  <div>
                    <div className="font-medium">按实际上课时长计算</div>
                    <div className="text-sm text-muted-foreground">根据教练实际上课的时长计算课时费</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                     onClick={() => setCalculationMethod("fixed")}>
                  <div className={`h-4 w-4 rounded-full ${calculationMethod === "fixed" ? "bg-primary" : "border"}`} />
                  <div>
                    <div className="font-medium">按固定课时计算</div>
                    <div className="text-sm text-muted-foreground">每节课按固定课时数计算，不考虑实际时长</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                     onClick={() => setCalculationMethod("scheduled")}>
                  <div className={`h-4 w-4 rounded-full ${calculationMethod === "scheduled" ? "bg-primary" : "border"}`} />
                  <div>
                    <div className="font-medium">按排课时长计算</div>
                    <div className="text-sm text-muted-foreground">根据排课系统中设置的课程时长计算</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCalculationMethodDialog(false)}>
              取消
            </Button>
            <Button onClick={handleSaveCalculationMethod}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 学员人数调整对话框 */}
      <Dialog open={showStudentCountDialog} onOpenChange={setShowStudentCountDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader className="flex flex-row items-center justify-between">
            <div>
              <DialogTitle>学员人数调整规则</DialogTitle>
              <DialogDescription>
                设置根据实际签到的学员人数调整课时费的规则，可针对不同课程类型或具体课程单独设置
              </DialogDescription>
            </div>
            <Button variant="ghost" size="icon" onClick={() => setShowStudentCountDialog(false)}>
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>启用学员人数调整</Label>
                <div
                  className={`flex h-6 w-11 items-center rounded-full p-1 cursor-pointer ${studentCountEnabled ? 'bg-primary' : 'bg-muted'}`}
                  onClick={toggleStudentCountEnabled}
                >
                  <div
                    className={`h-4 w-4 rounded-full bg-white transition-transform ${studentCountEnabled ? 'translate-x-5' : ''}`}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>调整方式</Label>
                <div className="grid grid-cols-1 gap-2">
                  <div className="flex items-center space-x-2 rounded-md border p-3">
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className="h-4 w-4 rounded-full bg-primary" />
                    </div>
                    <div>
                      <div className="font-medium">按人数区间调整</div>
                      <div className="text-sm text-muted-foreground">根据实际签到的学员人数区间设置不同的课时费调整比例</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>人数区间设置</Label>
                <div className="space-y-3">
                  {studentCountRanges.map((range, index) => (
                    <div key={index} className="grid grid-cols-3 gap-2 items-center">
                      <div className="col-span-2 flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <Input
                            type="number"
                            min="1"
                            value={range.min || ''}
                            onChange={(e) => updateStudentCountRange(index, 'min', e.target.value)}
                            className="w-16 h-8 text-xs"
                          />
                          <span className="text-sm">-</span>
                          <Input
                            type="number"
                            min={range.min || 1}
                            value={range.max || ''}
                            onChange={(e) => updateStudentCountRange(index, 'max', e.target.value)}
                            className="w-16 h-8 text-xs"
                            placeholder={range.max === null ? '以上' : ''}
                          />
                          <span className="text-sm">人</span>
                        </div>
                        <div className="h-1 flex-1 bg-gray-200 rounded-full"></div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 rounded-full"
                          onClick={() => deleteStudentCountRange(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="flex items-center gap-2 justify-end">
                        {range.isBase ? (
                          <div
                            className="flex items-center cursor-pointer"
                            onClick={() => updateStudentCountRange(index, 'isBase', false)}
                          >
                            <span className="text-sm">基准课时费</span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={range.adjustment}
                              onChange={(e) => updateStudentCountRange(index, 'adjustment', e.target.value)}
                              className="w-16 h-8"
                            />
                            <span className="text-sm text-muted-foreground">% 上浮</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="pt-2 flex items-center justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={addStudentCountRange}
                >
                  添加人数区间
                </Button>
                <p className="text-xs text-muted-foreground">
                  {studentCountRanges.length}/10 个区间
                </p>
              </div>

              <div className="pt-4 mt-2 border-t">
                <div>
                  <h4 className="text-sm font-medium">应用范围</h4>
                  <p className="text-xs text-muted-foreground mt-1 mb-2">选择此规则适用的课程类型或具体课程</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => openRuleScopeDialog("studentCount")}
                    disabled={!studentCountEnabled}
                  >
                    <span>设置应用范围</span>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setShowStudentCountDialog(false)}>
              取消
            </Button>
            <Button onClick={handleSaveStudentCountAdjustment}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 应用范围设置对话框 - 通用 */}
      <Dialog open={showRuleScopeDialog} onOpenChange={setShowRuleScopeDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader className="flex flex-row items-center justify-between">
            <div>
              <DialogTitle>设置应用范围</DialogTitle>
              <DialogDescription>
                选择调整规则适用的课程类型或具体课程
              </DialogDescription>
            </div>
            <Button variant="ghost" size="icon" onClick={() => setShowRuleScopeDialog(false)}>
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>选择应用方式</Label>
                <div className="grid grid-cols-1 gap-2">
                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("global")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${applicationMode === "global" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">全局应用</div>
                      <div className="text-sm text-muted-foreground">应用于所有课程类型和课程</div>
                    </div>
                  </div>

                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("courseType")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${applicationMode === "courseType" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">按课程类型应用</div>
                      <div className="text-sm text-muted-foreground">选择特定的课程类型应用规则</div>
                    </div>
                  </div>

                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("specificCourse")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${applicationMode === "specificCourse" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">按具体课程应用</div>
                      <div className="text-sm text-muted-foreground">选择特定的课程应用规则</div>
                    </div>
                  </div>
                </div>
              </div>

              {applicationMode === "courseType" && (
                <div className="space-y-2 pt-4 mt-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label>课程类型选择</Label>
                    {selectedCourseTypes.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={() => setSelectedCourseTypes([])}
                      >
                        清除选择
                      </Button>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {courseTypes.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center space-x-2 rounded-md border p-2 cursor-pointer hover:bg-accent"
                        onClick={() => toggleCourseType(item.id)}
                      >
                        <div className="flex items-center justify-center h-4 w-4">
                          <div className={`h-4 w-4 rounded ${selectedCourseTypes.includes(item.id) ? "bg-primary" : "border"}`} />
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                          <span>{item.type}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {applicationMode === "specificCourse" && (
                <div className="space-y-2 pt-4 mt-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label>具体课程选择</Label>
                    {selectedCourses.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={() => setSelectedCourses([])}
                      >
                        清除选择
                      </Button>
                    )}
                  </div>

                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索课程名称、类型或教练"
                      value={searchKeyword}
                      onChange={handleSearchChange}
                      className="pl-10 pr-10"
                    />
                    {searchKeyword && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
                        onClick={clearSearch}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  <div className="border rounded-md overflow-hidden">
                    <div className="max-h-60 overflow-y-auto">
                      {filteredCourses.length > 0 ? (
                        <div className="divide-y">
                          {filteredCourses.map((course) => {
                            const courseType = courseTypes.find(type => type.id === course.typeId)
                            return (
                              <div
                                key={course.id}
                                className={`flex items-center p-3 cursor-pointer hover:bg-accent ${
                                  selectedCourses.includes(course.id) ? 'bg-accent/50' : ''
                                }`}
                                onClick={() => toggleCourse(course.id)}
                              >
                                <div className="flex items-center justify-center h-4 w-4 mr-3">
                                  <div className={`h-4 w-4 rounded ${selectedCourses.includes(course.id) ? "bg-primary" : "border"}`} />
                                </div>
                                <div className="flex-1">
                                  <div className="font-medium">{course.name}</div>
                                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                      <div
                                        className="h-2 w-2 rounded-full"
                                        style={{ backgroundColor: courseType?.color || '#ccc' }}
                                      />
                                      <span>{course.type}</span>
                                    </div>
                                    <span>•</span>
                                    <span>{course.instructor}</span>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      ) : (
                        <div className="p-4 flex flex-col items-center justify-center text-center gap-2">
                          <Search className="h-8 w-8 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">
                            {searchKeyword ? '没有找到匹配的课程' : '搜索并选择具体课程'}
                          </p>
                        </div>
                      )}
                    </div>

                    {selectedCourses.length > 0 && (
                      <div className="p-3 bg-muted border-t">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">已选择 {selectedCourses.length} 个课程</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setShowRuleScopeDialog(false)}>
              取消
            </Button>
            <Button onClick={saveRuleScope}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 应用范围设置对话框 - 节假日 */}
      <Dialog open={showHolidayRuleScopeDialog} onOpenChange={setShowHolidayRuleScopeDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader className="flex flex-row items-center justify-between">
            <div>
              <DialogTitle>设置节假日调整应用范围</DialogTitle>
              <DialogDescription>
                选择节假日课时费调整规则适用的课程类型或具体课程
              </DialogDescription>
            </div>
            <Button variant="ghost" size="icon" onClick={() => setShowHolidayRuleScopeDialog(false)}>
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>选择应用方式</Label>
                <div className="grid grid-cols-1 gap-2">
                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("global")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${holidayApplicationMode === "global" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">全局应用</div>
                      <div className="text-sm text-muted-foreground">应用于所有课程类型和课程</div>
                    </div>
                  </div>

                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("courseType")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${holidayApplicationMode === "courseType" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">按课程类型应用</div>
                      <div className="text-sm text-muted-foreground">选择特定的课程类型应用规则</div>
                    </div>
                  </div>

                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("specificCourse")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${holidayApplicationMode === "specificCourse" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">按具体课程应用</div>
                      <div className="text-sm text-muted-foreground">选择特定的课程应用规则</div>
                    </div>
                  </div>
                </div>
              </div>

              {holidayApplicationMode === "courseType" && (
                <div className="space-y-2 pt-4 mt-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label>课程类型选择</Label>
                    {holidaySelectedCourseTypes.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={() => setHolidaySelectedCourseTypes([])}
                      >
                        清除选择
                      </Button>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {courseTypes.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center space-x-2 rounded-md border p-2 cursor-pointer hover:bg-accent"
                        onClick={() => toggleCourseType(item.id)}
                      >
                        <div className="flex items-center justify-center h-4 w-4">
                          <div className={`h-4 w-4 rounded ${holidaySelectedCourseTypes.includes(item.id) ? "bg-primary" : "border"}`} />
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                          <span>{item.type}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {holidayApplicationMode === "specificCourse" && (
                <div className="space-y-2 pt-4 mt-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label>具体课程选择</Label>
                    {holidaySelectedCourses.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={() => setHolidaySelectedCourses([])}
                      >
                        清除选择
                      </Button>
                    )}
                  </div>

                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索课程名称、类型或教练"
                      value={searchKeyword}
                      onChange={handleSearchChange}
                      className="pl-10 pr-10"
                    />
                    {searchKeyword && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
                        onClick={clearSearch}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  <div className="border rounded-md overflow-hidden">
                    <div className="max-h-60 overflow-y-auto">
                      {filteredCourses.length > 0 ? (
                        <div className="divide-y">
                          {filteredCourses.map((course) => {
                            const courseType = courseTypes.find(type => type.id === course.typeId)
                            return (
                              <div
                                key={course.id}
                                className={`flex items-center p-3 cursor-pointer hover:bg-accent ${
                                  holidaySelectedCourses.includes(course.id) ? 'bg-accent/50' : ''
                                }`}
                                onClick={() => toggleCourse(course.id)}
                              >
                                <div className="flex items-center justify-center h-4 w-4 mr-3">
                                  <div className={`h-4 w-4 rounded ${holidaySelectedCourses.includes(course.id) ? "bg-primary" : "border"}`} />
                                </div>
                                <div className="flex-1">
                                  <div className="font-medium">{course.name}</div>
                                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                      <div
                                        className="h-2 w-2 rounded-full"
                                        style={{ backgroundColor: courseType?.color || '#ccc' }}
                                      />
                                      <span>{course.type}</span>
                                    </div>
                                    <span>•</span>
                                    <span>{course.instructor}</span>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      ) : (
                        <div className="p-4 flex flex-col items-center justify-center text-center gap-2">
                          <Search className="h-8 w-8 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">
                            {searchKeyword ? '没有找到匹配的课程' : '搜索并选择具体课程'}
                          </p>
                        </div>
                      )}
                    </div>

                    {holidaySelectedCourses.length > 0 && (
                      <div className="p-3 bg-muted border-t">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">已选择 {holidaySelectedCourses.length} 个课程</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setShowHolidayRuleScopeDialog(false)}>
              取消
            </Button>
            <Button onClick={saveRuleScope}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 应用范围设置对话框 - 临时代课 */}
      <Dialog open={showSubstituteRuleScopeDialog} onOpenChange={setShowSubstituteRuleScopeDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader className="flex flex-row items-center justify-between">
            <div>
              <DialogTitle>设置临时代课调整应用范围</DialogTitle>
              <DialogDescription>
                选择临时代课课时费调整规则适用的课程类型或具体课程
              </DialogDescription>
            </div>
            <Button variant="ghost" size="icon" onClick={() => setShowSubstituteRuleScopeDialog(false)}>
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>选择应用方式</Label>
                <div className="grid grid-cols-1 gap-2">
                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("global")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${substituteApplicationMode === "global" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">全局应用</div>
                      <div className="text-sm text-muted-foreground">应用于所有课程类型和课程</div>
                    </div>
                  </div>

                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("courseType")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${substituteApplicationMode === "courseType" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">按课程类型应用</div>
                      <div className="text-sm text-muted-foreground">选择特定的课程类型应用规则</div>
                    </div>
                  </div>

                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("specificCourse")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${substituteApplicationMode === "specificCourse" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">按具体课程应用</div>
                      <div className="text-sm text-muted-foreground">选择特定的课程应用规则</div>
                    </div>
                  </div>
                </div>
              </div>

              {substituteApplicationMode === "courseType" && (
                <div className="space-y-2 pt-4 mt-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label>课程类型选择</Label>
                    {substituteSelectedCourseTypes.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={() => setSubstituteSelectedCourseTypes([])}
                      >
                        清除选择
                      </Button>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {courseTypes.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center space-x-2 rounded-md border p-2 cursor-pointer hover:bg-accent"
                        onClick={() => toggleCourseType(item.id)}
                      >
                        <div className="flex items-center justify-center h-4 w-4">
                          <div className={`h-4 w-4 rounded ${substituteSelectedCourseTypes.includes(item.id) ? "bg-primary" : "border"}`} />
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                          <span>{item.type}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {substituteApplicationMode === "specificCourse" && (
                <div className="space-y-2 pt-4 mt-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label>具体课程选择</Label>
                    {substituteSelectedCourses.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={() => setSubstituteSelectedCourses([])}
                      >
                        清除选择
                      </Button>
                    )}
                  </div>

                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索课程名称、类型或教练"
                      value={searchKeyword}
                      onChange={handleSearchChange}
                      className="pl-10 pr-10"
                    />
                    {searchKeyword && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
                        onClick={clearSearch}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  <div className="border rounded-md overflow-hidden">
                    <div className="max-h-60 overflow-y-auto">
                      {filteredCourses.length > 0 ? (
                        <div className="divide-y">
                          {filteredCourses.map((course) => {
                            const courseType = courseTypes.find(type => type.id === course.typeId)
                            return (
                              <div
                                key={course.id}
                                className={`flex items-center p-3 cursor-pointer hover:bg-accent ${
                                  substituteSelectedCourses.includes(course.id) ? 'bg-accent/50' : ''
                                }`}
                                onClick={() => toggleCourse(course.id)}
                              >
                                <div className="flex items-center justify-center h-4 w-4 mr-3">
                                  <div className={`h-4 w-4 rounded ${substituteSelectedCourses.includes(course.id) ? "bg-primary" : "border"}`} />
                                </div>
                                <div className="flex-1">
                                  <div className="font-medium">{course.name}</div>
                                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                      <div
                                        className="h-2 w-2 rounded-full"
                                        style={{ backgroundColor: courseType?.color || '#ccc' }}
                                      />
                                      <span>{course.type}</span>
                                    </div>
                                    <span>•</span>
                                    <span>{course.instructor}</span>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      ) : (
                        <div className="p-4 flex flex-col items-center justify-center text-center gap-2">
                          <Search className="h-8 w-8 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">
                            {searchKeyword ? '没有找到匹配的课程' : '搜索并选择具体课程'}
                          </p>
                        </div>
                      )}
                    </div>

                    {substituteSelectedCourses.length > 0 && (
                      <div className="p-3 bg-muted border-t">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">已选择 {substituteSelectedCourses.length} 个课程</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setShowSubstituteRuleScopeDialog(false)}>
              取消
            </Button>
            <Button onClick={saveRuleScope}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 应用范围设置对话框 - 学员人数调整 */}
      <Dialog open={showStudentCountRuleScopeDialog} onOpenChange={setShowStudentCountRuleScopeDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader className="flex flex-row items-center justify-between">
            <div>
              <DialogTitle>设置学员人数调整应用范围</DialogTitle>
              <DialogDescription>
                选择学员人数调整规则适用的课程类型或具体课程
              </DialogDescription>
            </div>
            <Button variant="ghost" size="icon" onClick={() => setShowStudentCountRuleScopeDialog(false)}>
              <X className="h-4 w-4" />
            </Button>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>选择应用方式</Label>
                <div className="grid grid-cols-1 gap-2">
                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("global")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${studentCountApplicationMode === "global" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">全局应用</div>
                      <div className="text-sm text-muted-foreground">应用于所有课程类型和课程</div>
                    </div>
                  </div>

                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("courseType")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${studentCountApplicationMode === "courseType" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">按课程类型应用</div>
                      <div className="text-sm text-muted-foreground">选择特定的课程类型应用规则</div>
                    </div>
                  </div>

                  <div
                    className="flex items-center space-x-2 rounded-md border p-3 cursor-pointer hover:bg-accent"
                    onClick={() => selectApplicationMode("specificCourse")}
                  >
                    <div className="flex items-center justify-center h-4 w-4">
                      <div className={`h-4 w-4 rounded-full ${studentCountApplicationMode === "specificCourse" ? "bg-primary" : "border"}`} />
                    </div>
                    <div>
                      <div className="font-medium">按具体课程应用</div>
                      <div className="text-sm text-muted-foreground">选择特定的课程应用规则</div>
                    </div>
                  </div>
                </div>
              </div>

              {studentCountApplicationMode === "courseType" && (
                <div className="space-y-2 pt-4 mt-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label>课程类型选择</Label>
                    {studentCountSelectedCourseTypes.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={() => setStudentCountSelectedCourseTypes([])}
                      >
                        清除选择
                      </Button>
                    )}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {courseTypes.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center space-x-2 rounded-md border p-2 cursor-pointer hover:bg-accent"
                        onClick={() => toggleCourseType(item.id)}
                      >
                        <div className="flex items-center justify-center h-4 w-4">
                          <div className={`h-4 w-4 rounded ${studentCountSelectedCourseTypes.includes(item.id) ? "bg-primary" : "border"}`} />
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                          <span>{item.type}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {studentCountApplicationMode === "specificCourse" && (
                <div className="space-y-2 pt-4 mt-2 border-t">
                  <div className="flex items-center justify-between">
                    <Label>具体课程选择</Label>
                    {studentCountSelectedCourses.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={() => setStudentCountSelectedCourses([])}
                      >
                        清除选择
                      </Button>
                    )}
                  </div>

                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索课程名称、类型或教练"
                      value={searchKeyword}
                      onChange={handleSearchChange}
                      className="pl-10 pr-10"
                    />
                    {searchKeyword && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
                        onClick={clearSearch}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  <div className="border rounded-md overflow-hidden">
                    <div className="max-h-60 overflow-y-auto">
                      {filteredCourses.length > 0 ? (
                        <div className="divide-y">
                          {filteredCourses.map((course) => {
                            const courseType = courseTypes.find(type => type.id === course.typeId)
                            return (
                              <div
                                key={course.id}
                                className={`flex items-center p-3 cursor-pointer hover:bg-accent ${
                                  studentCountSelectedCourses.includes(course.id) ? 'bg-accent/50' : ''
                                }`}
                                onClick={() => toggleCourse(course.id)}
                              >
                                <div className="flex items-center justify-center h-4 w-4 mr-3">
                                  <div className={`h-4 w-4 rounded ${studentCountSelectedCourses.includes(course.id) ? "bg-primary" : "border"}`} />
                                </div>
                                <div className="flex-1">
                                  <div className="font-medium">{course.name}</div>
                                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <div className="flex items-center gap-1">
                                      <div
                                        className="h-2 w-2 rounded-full"
                                        style={{ backgroundColor: courseType?.color || '#ccc' }}
                                      />
                                      <span>{course.type}</span>
                                    </div>
                                    <span>•</span>
                                    <span>{course.instructor}</span>
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      ) : (
                        <div className="p-4 flex flex-col items-center justify-center text-center gap-2">
                          <Search className="h-8 w-8 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">
                            {searchKeyword ? '没有找到匹配的课程' : '搜索并选择具体课程'}
                          </p>
                        </div>
                      )}
                    </div>

                    {studentCountSelectedCourses.length > 0 && (
                      <div className="p-3 bg-muted border-t">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">已选择 {studentCountSelectedCourses.length} 个课程</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setShowStudentCountRuleScopeDialog(false)}>
              取消
            </Button>
            <Button onClick={saveRuleScope}>
              <Save className="mr-2 h-4 w-4" />
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 课时费统计对话框 */}
      <Dialog open={showHourlyRateStatsDialog} onOpenChange={setShowHourlyRateStatsDialog}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>课时费统计</DialogTitle>
            <DialogDescription>
              查看各课程类型和教练的课时费统计信息
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">平均课时费</CardTitle>
                  <CardDescription>所有教练和课程类型</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">¥135.50</div>
                  <p className="text-xs text-muted-foreground mt-1">较上月 +5.2%</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">最高课时费</CardTitle>
                  <CardDescription>私教课</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">¥200.00</div>
                  <p className="text-xs text-muted-foreground mt-1">李教练</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">课时费设置数</CardTitle>
                  <CardDescription>已配置的课时费记录</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">6</div>
                  <p className="text-xs text-muted-foreground mt-1">覆盖 5 位教练</p>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">课程类型课时费统计</h3>
              <div className="space-y-6">
                {[
                  { type: "团课", color: "#4285F4", avg: 100, count: 8 },
                  { type: "小班课", color: "#34A853", avg: 120, count: 6 },
                  { type: "精品课", color: "#FBBC05", avg: 150, count: 5 },
                  { type: "私教课", color: "#EA4335", avg: 200, count: 4 },
                  { type: "教培课", color: "#9C27B0", avg: 180, count: 3 }
                ].map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full" style={{ backgroundColor: item.color }} />
                        <span className="font-medium">{item.type}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">{item.count} 条记录</span>
                        <span className="font-medium">¥{item.avg}</span>
                      </div>
                    </div>
                    <div className="h-2 w-full rounded-full bg-gray-100 overflow-hidden">
                      <div
                        className="h-full rounded-full"
                        style={{
                          width: `${(item.avg / 200) * 100}%`,
                          backgroundColor: item.color
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-4">教练课时费排名</h3>
                <div className="space-y-3">
                  {[
                    { name: "李教练", avg: 160, count: 5 },
                    { name: "王教练", avg: 145, count: 4 },
                    { name: "张教练", avg: 130, count: 3 },
                    { name: "赵教练", avg: 120, count: 2 },
                    { name: "刘教练", avg: 110, count: 1 }
                  ].map((coach, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary/10 text-primary">
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{coach.name}</div>
                          <div className="text-xs text-muted-foreground">{coach.count} 个课程</div>
                        </div>
                      </div>
                      <div className="font-medium">¥{coach.avg}</div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">课程课时费排名</h3>
                <div className="space-y-3">
                  {[
                    { name: "私教课", type: "私教课", avg: 200 },
                    { name: "教练培训课", type: "教培课", avg: 180 },
                    { name: "高级瑜伽进阶", type: "精品课", avg: 150 },
                    { name: "流瑜伽中级", type: "小班课", avg: 120 },
                    { name: "哈他瑜伽初级", type: "团课", avg: 100 }
                  ].map((course, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{course.name}</div>
                        <div className="text-xs text-muted-foreground">{course.type}</div>
                      </div>
                      <div className="font-medium">¥{course.avg}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">课时费调整规则统计</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="border rounded-md p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-3 w-3 rounded-full bg-blue-500" />
                    <span className="font-medium">节假日调整</span>
                  </div>
                  <div className="text-2xl font-bold">+{holidayRateAdjustment}%</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {holidayEnabled ? '已启用' : '已禁用'}
                  </p>
                </div>

                <div className="border rounded-md p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-3 w-3 rounded-full bg-green-500" />
                    <span className="font-medium">临时代课调整</span>
                  </div>
                  <div className="text-2xl font-bold">+{substituteRateAdjustment}%</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {substituteEnabled ? '已启用' : '已禁用'}
                  </p>
                </div>

                <div className="border rounded-md p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-3 w-3 rounded-full bg-orange-500" />
                    <span className="font-medium">学员人数调整</span>
                  </div>
                  <div className="text-2xl font-bold">
                    {studentCountEnabled ? '已启用' : '未启用'}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {studentCountEnabled ? `${studentCountRanges.length} 个人数区间` : '无调整'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={() => setShowHourlyRateStatsDialog(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 课程详情对话框 */}
      <Dialog open={showCourseDetailDialog} onOpenChange={setShowCourseDetailDialog}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>课程详情</DialogTitle>
            <DialogDescription>
              查看课程约课会员、会员卡、消耗及价值信息
            </DialogDescription>
          </DialogHeader>

          {(() => {
            const courseDetail = getSelectedCourseDetail()

            // 计算各种调整金额
            const holidayAdjustment = courseDetail.adjustments.find(adj => adj.type === "holiday")?.amount || 0
            const substituteAdjustment = courseDetail.adjustments.find(adj => adj.type === "substitute")?.amount || 0
            const studentCountAdjustment = courseDetail.adjustments.find(adj => adj.type === "studentCount")?.amount || 0

            // 计算总课时费
            const totalFee = courseDetail.fee + holidayAdjustment + substituteAdjustment + studentCountAdjustment

            return (
              <div className="grid gap-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium mb-2">课程信息</h3>
                    <div className="border rounded-md p-4 space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">课程名称</span>
                        <span className="font-medium">{courseDetail.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">课程类型</span>
                        <span className="font-medium">{courseDetail.type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">教练</span>
                        <span className="font-medium">{courseDetail.coach}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">上课时间</span>
                        <span className="font-medium">{courseDetail.date} {courseDetail.time}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">课时费</span>
                        <span className="font-medium">¥{courseDetail.fee}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">课时费信息</h3>
                    <div className="border rounded-md p-4 space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">基础课时费</span>
                        <span className="font-medium">¥{courseDetail.fee}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">节假日调整</span>
                        <span className="font-medium">¥{holidayAdjustment}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">临时代课调整</span>
                        <span className="font-medium">¥{substituteAdjustment}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">学员人数调整</span>
                        <span className="font-medium">¥{studentCountAdjustment}</span>
                      </div>
                      <div className="flex justify-between border-t pt-2">
                        <span className="text-muted-foreground font-medium">总课时费</span>
                        <span className="font-medium">¥{totalFee}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">约课会员信息</h3>
                  <div className="border rounded-md overflow-hidden">
                    <div className="grid grid-cols-5 bg-muted px-4 py-3 font-medium text-sm">
                      <div className="col-span-1">会员姓名</div>
                      <div className="col-span-1">会员卡</div>
                      <div className="col-span-1">消耗</div>
                      <div className="col-span-1">价值</div>
                      <div className="col-span-1">签到状态</div>
                    </div>

                    <div className="divide-y">
                      {courseDetail.attendees.map((member, index) => (
                        <div key={index} className="grid grid-cols-5 px-4 py-3">
                          <div className="col-span-1 flex items-center">
                            <div className="font-medium">{member.name}</div>
                          </div>
                          <div className="col-span-1 flex items-center">
                            <span>{member.card}</span>
                          </div>
                          <div className="col-span-1 flex items-center">
                            <span>{member.consumption}</span>
                          </div>
                          <div className="col-span-1 flex items-center">
                            <span>{member.value}</span>
                          </div>
                          <div className="col-span-1 flex items-center">
                            <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                              {member.status}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">会员卡消耗详情</h3>
                  <div className="border rounded-md overflow-hidden">
                    <div className="grid grid-cols-5 bg-muted px-4 py-3 font-medium text-sm">
                      <div className="col-span-1">会员卡</div>
                      <div className="col-span-1">类型</div>
                      <div className="col-span-1">单价</div>
                      <div className="col-span-1">消耗</div>
                      <div className="col-span-1">剩余</div>
                    </div>

                    <div className="divide-y">
                      {courseDetail.cards.map((card, index) => (
                        <div key={index} className="grid grid-cols-5 px-4 py-3">
                          <div className="col-span-1 flex items-center">
                            <div className="font-medium">{card.name}</div>
                          </div>
                          <div className="col-span-1 flex items-center">
                            <span>{card.type}</span>
                          </div>
                          <div className="col-span-1 flex items-center">
                            <span>{card.price}</span>
                          </div>
                          <div className="col-span-1 flex items-center">
                            <span>{card.consumption}</span>
                          </div>
                          <div className="col-span-1 flex items-center">
                            <span>{card.remaining}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )
          })()}

          <DialogFooter>
            <Button onClick={() => setShowCourseDetailDialog(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
