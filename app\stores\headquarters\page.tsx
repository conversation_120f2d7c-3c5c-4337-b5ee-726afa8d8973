"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Building, Plus, User, FileText, Settings, Briefcase, Users, <PERSON><PERSON>hart } from "lucide-react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "@/components/ui/use-toast"
import { useAuth } from "@/contexts/auth-context"

// 总部部门数据接口
interface Department {
  id: string | number;
  name: string;
  director: string;
  employeesCount: number;
  description: string;
  createdAt: string;
  status: 'active' | 'inactive';
}

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, { message: "部门名称至少需要2个字符" }).max(50, { message: "部门名称不能超过50个字符" }),
  director: z.string().min(2, { message: "负责人姓名至少需要2个字符" }).max(20, { message: "负责人姓名不能超过20个字符" }),
  description: z.string().max(500, { message: "描述不能超过500个字符" }).optional(),
  type: z.enum(["executive", "administration", "operation", "finance", "hr", "marketing", "it"], { message: "请选择部门类型" }),
});

type FormValues = z.infer<typeof formSchema>;

export default function HeadquartersPage() {
  const [departments, setDepartments] = useState<Department[]>([
    {
      id: 1,
      name: "执行办公室",
      director: "张总",
      employeesCount: 3,
      description: "负责公司整体战略规划和运营决策",
      createdAt: "2023-01-01",
      status: 'active'
    },
    {
      id: 2,
      name: "人力资源部",
      director: "李经理",
      employeesCount: 5,
      description: "负责人员招聘、培训和绩效管理",
      createdAt: "2023-01-05",
      status: 'active'
    },
    {
      id: 3,
      name: "财务部",
      director: "王经理",
      employeesCount: 4,
      description: "负责公司财务规划、预算控制和资金管理",
      createdAt: "2023-01-10",
      status: 'active'
    },
    {
      id: 4,
      name: "市场部",
      director: "赵经理",
      employeesCount: 6,
      description: "负责品牌推广、市场营销和渠道拓展",
      createdAt: "2023-01-15",
      status: 'active'
    },
    {
      id: 5,
      name: "门店运营管理部",
      director: "孙经理",
      employeesCount: 8,
      description: "负责所有连锁门店的标准化管理和运营支持",
      createdAt: "2023-01-20",
      status: 'active'
    }
  ]);
  const [loading, setLoading] = useState(false);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const { user } = useAuth();

  // 创建表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      director: "",
      description: "",
      type: "administration"
    }
  });

  // 处理添加部门
  const handleAddDepartment = (values: FormValues) => {
    try {
      // 创建新部门对象
      const newDepartment: Department = {
        id: Date.now(),
        name: values.name,
        director: values.director,
        employeesCount: 0,
        description: values.description || "",
        createdAt: new Date().toISOString().split('T')[0],
        status: 'active'
      };

      // 更新部门列表
      const updatedDepartments = [...departments, newDepartment];
      setDepartments(updatedDepartments);
      
      toast({
        title: "部门添加成功",
        description: `${values.name} 已成功添加`,
      });

      // 关闭对话框并重置表单
      setOpenAddDialog(false);
      form.reset();
    } catch (error) {
      console.error('添加部门失败:', error);
      toast({
        title: "添加部门失败",
        description: "请稍后再试",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="container max-w-6xl py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">总部管理</h1>
        <Button onClick={() => setOpenAddDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          添加部门
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">总部部门数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departments.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">总部员工数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departments.reduce((acc, dept) => acc + dept.employeesCount, 0)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">部门覆盖率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">100%</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">组织架构完整度</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">85%</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>总部部门列表</CardTitle>
          <CardDescription>管理公司总部各职能部门及负责人</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>部门名称</TableHead>
                <TableHead>负责人</TableHead>
                <TableHead>员工数量</TableHead>
                <TableHead>部门职责</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {departments.map((department) => (
                <TableRow key={department.id}>
                  <TableCell className="font-medium">{department.name}</TableCell>
                  <TableCell>{department.director}</TableCell>
                  <TableCell>{department.employeesCount}</TableCell>
                  <TableCell className="max-w-xs truncate">{department.description}</TableCell>
                  <TableCell>{department.createdAt}</TableCell>
                  <TableCell>
                    <Badge variant={department.status === 'active' ? "default" : "outline"}>
                      {department.status === 'active' ? '正常' : '停用'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm">查看</Button>
                    <Button variant="ghost" size="sm">编辑</Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 添加部门对话框 */}
      <Dialog open={openAddDialog} onOpenChange={setOpenAddDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>添加部门</DialogTitle>
            <DialogDescription>
              创建新的总部职能部门，填写部门基本信息。
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddDepartment)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>部门名称</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：人力资源部" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="director"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>负责人</FormLabel>
                    <FormControl>
                      <Input placeholder="部门负责人姓名" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>部门类型</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择部门类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="executive">决策层</SelectItem>
                        <SelectItem value="administration">行政部门</SelectItem>
                        <SelectItem value="operation">运营部门</SelectItem>
                        <SelectItem value="finance">财务部门</SelectItem>
                        <SelectItem value="hr">人力资源</SelectItem>
                        <SelectItem value="marketing">市场营销</SelectItem>
                        <SelectItem value="it">信息技术</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      选择部门所属的职能类型
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>部门职责描述</FormLabel>
                    <FormControl>
                      <Textarea placeholder="请输入部门主要职责和工作内容" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setOpenAddDialog(false)}>取消</Button>
                <Button type="submit">创建部门</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
} 