"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"
import {
  FileText,
  FileSignature,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Eye,
  Download,
  Plus
} from "lucide-react"

interface ContractIntegrationProps {
  shareholderId?: string
  shareholderType?: string
  shareholderName?: string
}

export function ContractIntegration({
  shareholderId,
  shareholderType,
  shareholderName
}: ContractIntegrationProps) {
  const router = useRouter()
  const [contracts, setContracts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  // 模拟获取股东合同数据
  useEffect(() => {
    const fetchContracts = async () => {
      setLoading(true)
      try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 500))
        
        // 模拟合同数据
        const mockContracts = [
          {
            id: "SC001",
            title: `${shareholderType || "消费型"}股东协议 - ${shareholderName || "股东"}`,
            templateName: `${shareholderType || "消费型"}股东协议`,
            serialNumber: "S20230101",
            status: "COMPLETED",
            parties: [
              { name: "静心瑜伽馆", type: "COMPANY" },
              { name: shareholderName || "股东", type: "PERSON" }
            ],
            createdAt: "2023-08-15 10:30",
            completedAt: "2023-08-15 14:45",
            category: "股东合同",
            subCategory: shareholderType || "消费型股东"
          },
          {
            id: "SC002",
            title: `${shareholderType || "消费型"}股东权益确认书`,
            templateName: `${shareholderType || "消费型"}股东权益确认书`,
            serialNumber: "S20230102",
            status: "DRAFT",
            parties: [
              { name: "静心瑜伽馆", type: "COMPANY" },
              { name: shareholderName || "股东", type: "PERSON" }
            ],
            createdAt: "2023-09-01 09:15",
            completedAt: null,
            category: "股东合同",
            subCategory: shareholderType || "消费型股东"
          }
        ]
        
        setContracts(mockContracts)
      } catch (error) {
        console.error("获取合同失败:", error)
        toast({
          title: "获取合同失败",
          description: "无法获取股东合同数据",
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }
    
    fetchContracts()
  }, [shareholderId, shareholderType, shareholderName])

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <Badge className="bg-green-500">已完成</Badge>
      case "SIGNING":
        return <Badge className="bg-blue-500">签署中</Badge>
      case "DRAFT":
        return <Badge variant="outline">草稿</Badge>
      case "REJECTED":
        return <Badge variant="destructive">已拒绝</Badge>
      case "EXPIRED":
        return <Badge variant="secondary">已过期</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <CheckCircle className="h-8 w-8 text-green-500" />
      case "SIGNING":
        return <Clock className="h-8 w-8 text-blue-500" />
      case "DRAFT":
        return <FileText className="h-8 w-8 text-gray-500" />
      case "REJECTED":
        return <XCircle className="h-8 w-8 text-red-500" />
      case "EXPIRED":
        return <AlertCircle className="h-8 w-8 text-yellow-500" />
      default:
        return <FileText className="h-8 w-8 text-gray-500" />
    }
  }

  // 查看合同详情
  const handleViewContract = (contractId: string) => {
    router.push(`/premium-services/e-contract?contractId=${contractId}`)
  }

  // 创建新合同
  const handleCreateContract = () => {
    router.push(`/premium-services/e-contract?createFor=${shareholderType}&name=${shareholderName}`)
  }

  // 下载合同
  const handleDownloadContract = (contractId: string) => {
    toast({
      title: "开始下载",
      description: "合同文件下载已开始",
    })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>电子合同</CardTitle>
            <CardDescription>查看和管理股东电子合同</CardDescription>
          </div>
          <Button onClick={handleCreateContract}>
            <Plus className="mr-2 h-4 w-4" />
            创建合同
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : contracts.length > 0 ? (
          <div className="space-y-4">
            {contracts.map((contract) => (
              <Card key={contract.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-base">{contract.title}</CardTitle>
                    {getStatusBadge(contract.status)}
                  </div>
                  <CardDescription>合同编号: {contract.serialNumber}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {getStatusIcon(contract.status)}
                      <div className="ml-3">
                        <p className="text-sm font-medium">
                          {contract.status === "COMPLETED" && "已完成签署"}
                          {contract.status === "SIGNING" && "等待签署"}
                          {contract.status === "DRAFT" && "草稿"}
                          {contract.status === "REJECTED" && "已拒绝"}
                          {contract.status === "EXPIRED" && "已过期"}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          创建于 {contract.createdAt}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {contract.status === "COMPLETED" && (
                        <Button variant="outline" size="sm" onClick={() => handleDownloadContract(contract.id)}>
                          <Download className="mr-2 h-4 w-4" />
                          下载
                        </Button>
                      )}
                      <Button variant="outline" size="sm" onClick={() => handleViewContract(contract.id)}>
                        <Eye className="mr-2 h-4 w-4" />
                        查看
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <FileText className="h-12 w-12 text-muted-foreground/50" />
            <h3 className="mt-4 text-lg font-semibold">没有合同</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              该股东还没有关联的电子合同
            </p>
            <Button className="mt-4" onClick={handleCreateContract}>
              <FileSignature className="mr-2 h-4 w-4" />
              创建合同
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
