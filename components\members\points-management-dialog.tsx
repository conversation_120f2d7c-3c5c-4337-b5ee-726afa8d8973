"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CalendarIcon, ChevronsUpDown, Coins, Plus, Minus } from "lucide-react"

// 积分历史记录模拟数据
const pointsHistory = [
  {
    id: "P001",
    date: "2023-05-15",
    type: "earn",
    amount: 150,
    description: "课程签到",
    operator: "系统",
  },
  {
    id: "P002",
    date: "2023-06-20",
    type: "earn",
    amount: 500,
    description: "购买年卡",
    operator: "王教练",
  },
  {
    id: "P003",
    date: "2023-07-10",
    type: "spend",
    amount: 200,
    description: "兑换水杯",
    operator: "李顾问",
  },
  {
    id: "P004",
    date: "2023-08-05",
    type: "earn",
    amount: 100,
    description: "生日奖励",
    operator: "系统",
  },
  {
    id: "P005",
    date: "2023-09-18",
    type: "spend",
    amount: 300,
    description: "兑换课程",
    operator: "张顾问",
  },
]

// 积分规则模拟数据
const pointsRules = [
  {
    id: "R001",
    name: "课程签到",
    points: 10,
    description: "每次课程签到获得10积分",
  },
  {
    id: "R002",
    name: "购买会员卡",
    points: "消费金额的10%",
    description: "购买会员卡时获得消费金额10%的积分",
  },
  {
    id: "R003",
    name: "生日奖励",
    points: 100,
    description: "会员生日当月获得100积分",
  },
  {
    id: "R004",
    name: "推荐新会员",
    points: 200,
    description: "成功推荐新会员注册并消费获得200积分",
  },
  {
    id: "R005",
    name: "积分有效期",
    points: "-",
    description: "积分有效期为1年，到期未使用将自动失效",
  },
]

interface PointsManagementDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
    points: number
    pointsExpiry: string
  }
}

export function PointsManagementDialog({
  open,
  onOpenChange,
  member,
}: PointsManagementDialogProps) {
  const [activeTab, setActiveTab] = useState("history")
  const [operationType, setOperationType] = useState("add")
  const [pointsAmount, setPointsAmount] = useState("")
  const [reason, setReason] = useState("")
  const [reasonCategory, setReasonCategory] = useState("")

  // 处理积分操作
  const handlePointsOperation = () => {
    if (!pointsAmount || parseInt(pointsAmount) <= 0) {
      toast({
        title: "积分数量无效",
        description: "请输入有效的积分数量",
        variant: "destructive",
      })
      return
    }

    if (!reason) {
      toast({
        title: "原因不能为空",
        description: "请输入积分操作的原因",
        variant: "destructive",
      })
      return
    }

    // 模拟积分操作
    const operation = operationType === "add" ? "增加" : "扣除"
    toast({
      title: `积分${operation}成功`,
      description: `已成功为会员 ${member.name} ${operation} ${pointsAmount} 积分`,
    })

    // 重置表单
    setPointsAmount("")
    setReason("")
    setReasonCategory("")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>会员积分管理</DialogTitle>
          <DialogDescription>
            管理会员 {member.name} 的积分，当前积分: {member.points}，有效期至: {member.pointsExpiry}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="history">积分历史</TabsTrigger>
            <TabsTrigger value="operation">积分操作</TabsTrigger>
            <TabsTrigger value="rules">积分规则</TabsTrigger>
          </TabsList>

          {/* 积分历史标签页 */}
          <TabsContent value="history" className="mt-4">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>日期</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead>数量</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>操作人</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pointsHistory.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>{record.date}</TableCell>
                      <TableCell>
                        {record.type === "earn" ? (
                          <Badge className="bg-green-500 hover:bg-green-600">
                            <Plus className="mr-1 h-3 w-3" />
                            获得
                          </Badge>
                        ) : (
                          <Badge className="bg-orange-500 hover:bg-orange-600">
                            <Minus className="mr-1 h-3 w-3" />
                            使用
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{record.amount}</TableCell>
                      <TableCell>{record.description}</TableCell>
                      <TableCell>{record.operator}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>

          {/* 积分操作标签页 */}
          <TabsContent value="operation" className="mt-4 space-y-4">
            <div className="space-y-4">
              <div>
                <Label>操作类型</Label>
                <RadioGroup
                  value={operationType}
                  onValueChange={setOperationType}
                  className="flex mt-2 space-x-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="add" id="add" />
                    <Label htmlFor="add" className="font-normal">
                      增加积分
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="subtract" id="subtract" />
                    <Label htmlFor="subtract" className="font-normal">
                      扣除积分
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label htmlFor="points-amount">积分数量</Label>
                <div className="flex items-center mt-2">
                  <Input
                    id="points-amount"
                    type="number"
                    min="1"
                    value={pointsAmount}
                    onChange={(e) => setPointsAmount(e.target.value)}
                    placeholder="输入积分数量"
                  />
                  <Coins className="ml-2 h-4 w-4 text-muted-foreground" />
                </div>
              </div>

              <div>
                <Label htmlFor="reason-category">原因分类</Label>
                <Select
                  value={reasonCategory}
                  onValueChange={setReasonCategory}
                >
                  <SelectTrigger id="reason-category" className="mt-2">
                    <SelectValue placeholder="选择原因分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="course">课程相关</SelectItem>
                    <SelectItem value="purchase">购买消费</SelectItem>
                    <SelectItem value="promotion">促销活动</SelectItem>
                    <SelectItem value="reward">奖励</SelectItem>
                    <SelectItem value="adjustment">调整</SelectItem>
                    <SelectItem value="other">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="reason">详细原因</Label>
                <Textarea
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="输入积分操作的详细原因"
                  className="mt-2"
                />
              </div>

              <Button onClick={handlePointsOperation} className="w-full">
                {operationType === "add" ? "增加积分" : "扣除积分"}
              </Button>
            </div>
          </TabsContent>

          {/* 积分规则标签页 */}
          <TabsContent value="rules" className="mt-4">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>规则名称</TableHead>
                    <TableHead>积分值</TableHead>
                    <TableHead>规则说明</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pointsRules.map((rule) => (
                    <TableRow key={rule.id}>
                      <TableCell className="font-medium">{rule.name}</TableCell>
                      <TableCell>{rule.points}</TableCell>
                      <TableCell>{rule.description}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
