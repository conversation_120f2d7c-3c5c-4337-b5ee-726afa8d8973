"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  Download,
  Edit,
  FileText,
  MapPin,
  Package,
  Printer,
  RefreshCw,
  Send,
  ShoppingBag,
  Truck,
  User,
  X
} from "lucide-react"
// 注意：Yoga 图标在 lucide-react 中不存在，需要使用其他图标或自定义组件

// 模拟获取订单详情的函数
const getOrderDetails = (orderId: string) => {
  // 在实际应用中，这里会从API获取订单详情
  return {
    id: orderId,
    customer: {
      name: "张三",
      phone: "13800138001",
      avatar: "/avatars/01.png",
      membershipLevel: "黄金会员",
      memberSince: "2022-05-15",
      totalOrders: 12,
      totalSpent: "5680.00"
    },
    orderDate: "2023-05-01 14:30:25",
    totalAmount: 199.00,
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    paymentDate: "2023-05-01 14:31:05",
    transactionId: "wx202305011431050001",
    deliveryMethod: "logistics", // 物流配送
    deliveryStatus: "delivered", // 已送达
    trackingNumber: "SF1234567890",
    logisticsCompany: "顺丰速运",
    estimatedDeliveryDate: "2023-05-03",
    actualDeliveryDate: "2023-05-02",
    orderType: "product", // 商品订单
    address: {
      recipient: "张三",
      phone: "13800138001",
      province: "上海市",
      city: "上海市",
      district: "浦东新区",
      detail: "张江高科技园区博云路2号",
      postalCode: "201203"
    },
    products: [
      {
        id: "PROD001",
        name: "瑜伽垫 - 专业防滑",
        sku: "YG-MAT-001",
        quantity: 1,
        price: 199.00,
        totalPrice: 199.00,
        image: "/products/yoga-mat.jpg",
        type: "physical", // 实物商品
        category: "equipment" // 设备类别
      }
    ],
    subtotal: 199.00,
    shippingFee: 0.00,
    discount: 0.00,
    couponCode: "",
    remark: "",
    timeline: [
      { time: "2023-05-02 15:30:45", status: "delivered", description: "订单已送达" },
      { time: "2023-05-01 16:20:10", status: "shipping", description: "订单已发货" },
      { time: "2023-05-01 15:10:30", status: "processing", description: "订单处理中" },
      { time: "2023-05-01 14:31:05", status: "paid", description: "订单已支付" },
      { time: "2023-05-01 14:30:25", status: "created", description: "订单已创建" }
    ]
  };
};

interface OrderDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderId: string;
}

export function ShopOrderDetailDialog({ open, onOpenChange, orderId }: OrderDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("details")
  const [loading, setLoading] = useState(false)
  const [orderDetails, setOrderDetails] = useState<any>(null)
  const [remark, setRemark] = useState("")
  const [deliveryStatus, setDeliveryStatus] = useState("")

  // 加载订单详情
  const loadOrderDetails = () => {
    if (!orderId) return;

    setLoading(true);
    // 模拟API请求
    setTimeout(() => {
      const details = getOrderDetails(orderId);
      setOrderDetails(details);
      setRemark(details.remark || "");
      setDeliveryStatus(details.deliveryStatus || "");
      setLoading(false);
    }, 500);
  };

  // 使用 useState 的副作用钩子来处理加载和重置
  // 这样可以避免在渲染过程中直接修改状态导致的无限循环
  React.useEffect(() => {
    if (open && orderId && !orderDetails) {
      loadOrderDetails();
    }

    if (!open && orderDetails) {
      setOrderDetails(null);
    }
  }, [open, orderId, orderDetails]);

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">待处理</Badge>;
      case "processing":
        return <Badge variant="outline" className="bg-indigo-50 text-indigo-700 hover:bg-indigo-50 border-indigo-200">处理中</Badge>;
      case "shipping":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-50 border-purple-200">配送中</Badge>;
      case "delivered":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已送达</Badge>;
      case "completed":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已完成</Badge>;
      case "cancelled":
        return <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200">已取消</Badge>;
      case "paid":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已支付</Badge>;
      case "unpaid":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-50 border-yellow-200">待付款</Badge>;
      case "created":
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 hover:bg-gray-50 border-gray-200">已创建</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // 获取订单类型图标和文本
  const getOrderTypeInfo = (orderType: string) => {
    switch (orderType) {
      case "product":
        return { icon: <ShoppingBag className="h-4 w-4 text-blue-500" />, text: "商品订单" };
      case "course":
        return { icon: <FileText className="h-4 w-4 text-purple-500" />, text: "课程订单" };
      case "membership":
        return { icon: <CreditCard className="h-4 w-4 text-green-500" />, text: "会员订单" };
      case "digital":
        return { icon: <FileText className="h-4 w-4 text-orange-500" />, text: "数字商品" };
      default:
        return { icon: <ShoppingBag className="h-4 w-4" />, text: "未知类型" };
    }
  };

  // 处理更新订单
  const handleUpdateOrder = () => {
    console.log("更新订单:", { orderId, remark, deliveryStatus });
    // 实际应用中，这里会调用API更新订单
    alert("订单更新成功");
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : orderDetails ? (
          <>
            <DialogHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <DialogTitle>订单详情 - {orderDetails.id}</DialogTitle>
                  {orderDetails.orderType && (
                    <div className="flex items-center gap-1">
                      {getOrderTypeInfo(orderDetails.orderType).icon}
                      <span className="text-xs text-muted-foreground">{getOrderTypeInfo(orderDetails.orderType).text}</span>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {getStatusBadge(orderDetails.status)}
                  {getStatusBadge(orderDetails.paymentStatus)}
                </div>
              </div>
              <DialogDescription>
                订单创建时间: {orderDetails.orderDate}
              </DialogDescription>
            </DialogHeader>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="details">订单详情</TabsTrigger>
                <TabsTrigger value="customer">客户信息</TabsTrigger>
                <TabsTrigger value="logistics">物流信息</TabsTrigger>
                <TabsTrigger value="timeline">订单历程</TabsTrigger>
              </TabsList>

              {/* 订单详情标签页 */}
              <TabsContent value="details" className="space-y-4 mt-4">
                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <ShoppingBag className="h-4 w-4 mr-2" />
                        订单信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">订单号:</span>
                        <span className="font-medium">{orderDetails.id}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">订单类型:</span>
                        <span className="flex items-center">
                          {getOrderTypeInfo(orderDetails.orderType).icon}
                          <span className="ml-1">{getOrderTypeInfo(orderDetails.orderType).text}</span>
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">订单状态:</span>
                        <span>{getStatusBadge(orderDetails.status)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">下单时间:</span>
                        <span>{orderDetails.orderDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">支付方式:</span>
                        <span>
                          {orderDetails.paymentMethod === "wechat" ? "微信支付" :
                           orderDetails.paymentMethod === "alipay" ? "支付宝" :
                           orderDetails.paymentMethod === "balance" ? "余额支付" :
                           orderDetails.paymentMethod}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">支付状态:</span>
                        <span>{getStatusBadge(orderDetails.paymentStatus)}</span>
                      </div>
                      {orderDetails.paymentStatus === "paid" && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">支付时间:</span>
                            <span>{orderDetails.paymentDate}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">交易号:</span>
                            <span className="text-xs">{orderDetails.transactionId}</span>
                          </div>
                        </>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <MapPin className="h-4 w-4 mr-2" />
                        配送信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">配送方式:</span>
                        <span>
                          {orderDetails.deliveryMethod === "logistics" ? "物流配送" :
                           orderDetails.deliveryMethod === "self_pickup" ? "门店自提" :
                           orderDetails.deliveryMethod === "verification" ? "核销" :
                           orderDetails.deliveryMethod}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">配送状态:</span>
                        <span>{getStatusBadge(orderDetails.deliveryStatus)}</span>
                      </div>
                      {orderDetails.deliveryMethod === "logistics" && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">物流公司:</span>
                            <span>{orderDetails.logisticsCompany}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">物流单号:</span>
                            <span>{orderDetails.trackingNumber}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">预计送达:</span>
                            <span>{orderDetails.estimatedDeliveryDate}</span>
                          </div>
                          {orderDetails.deliveryStatus === "delivered" && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">实际送达:</span>
                              <span>{orderDetails.actualDeliveryDate}</span>
                            </div>
                          )}
                        </>
                      )}
                      {orderDetails.address && orderDetails.deliveryMethod === "logistics" && (
                        <div className="mt-2 pt-2 border-t">
                          <div className="font-medium mb-1">收货地址</div>
                          <div className="text-muted-foreground">
                            {orderDetails.address.recipient} {orderDetails.address.phone}
                          </div>
                          <div className="text-muted-foreground">
                            {orderDetails.address.province} {orderDetails.address.city} {orderDetails.address.district} {orderDetails.address.detail}
                          </div>
                          {orderDetails.address.postalCode && (
                            <div className="text-muted-foreground">
                              邮编: {orderDetails.address.postalCode}
                            </div>
                          )}
                        </div>
                      )}
                      {orderDetails.deliveryMethod === "self_pickup" && (
                        <div className="mt-2 pt-2 border-t">
                          <div className="font-medium mb-1">自提信息</div>
                          <div className="text-muted-foreground">
                            自提点: {orderDetails.address || "门店自提"}
                          </div>
                          <div className="text-muted-foreground">
                            自提码: {orderDetails.pickupCode || "无"}
                          </div>
                        </div>
                      )}
                      {orderDetails.deliveryMethod === "verification" && (
                        <div className="mt-2 pt-2 border-t">
                          <div className="font-medium mb-1">核销信息</div>
                          <div className="text-muted-foreground">
                            核销码: {orderDetails.verificationCode || "无"}
                          </div>
                          <div className="text-muted-foreground">
                            核销状态: {orderDetails.deliveryStatus === "delivered" ? "已核销" : "未核销"}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center">
                      <Package className="h-4 w-4 mr-2" />
                      商品信息
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b bg-muted/50">
                            <th className="py-2 px-4 text-left font-medium">商品</th>
                            <th className="py-2 px-4 text-center font-medium">单价</th>
                            <th className="py-2 px-4 text-center font-medium">数量</th>
                            <th className="py-2 px-4 text-right font-medium">小计</th>
                          </tr>
                        </thead>
                        <tbody>
                          {orderDetails.products.map((product: any) => (
                            <tr key={product.id} className="border-b">
                              <td className="py-3 px-4">
                                <div className="flex items-center gap-3">
                                  <div className="w-10 h-10 bg-muted rounded-md flex items-center justify-center">
                                    {product.image ? (
                                      <img src={product.image} alt={product.name} className="w-full h-full object-cover rounded-md" />
                                    ) : (
                                      <Package className="h-5 w-5 text-muted-foreground" />
                                    )}
                                  </div>
                                  <div>
                                    <div className="font-medium">{product.name}</div>
                                    <div className="text-xs text-muted-foreground">SKU: {product.sku}</div>
                                    <div className="text-xs text-muted-foreground">
                                      类型: {product.type === "physical" ? "实物商品" :
                                            product.type === "virtual" ? "虚拟商品" :
                                            product.type}
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td className="py-3 px-4 text-center">¥{product.price.toFixed(2)}</td>
                              <td className="py-3 px-4 text-center">{product.quantity}</td>
                              <td className="py-3 px-4 text-right">¥{product.totalPrice.toFixed(2)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">商品小计:</span>
                        <span>¥{orderDetails.subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">运费:</span>
                        <span>¥{orderDetails.shippingFee.toFixed(2)}</span>
                      </div>
                      {orderDetails.discount > 0 && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">优惠:</span>
                          <span className="text-red-600">-¥{orderDetails.discount.toFixed(2)}</span>
                        </div>
                      )}
                      {orderDetails.couponCode && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">优惠券:</span>
                          <span>{orderDetails.couponCode}</span>
                        </div>
                      )}
                      <Separator />
                      <div className="flex justify-between font-medium">
                        <span>订单总计:</span>
                        <span className="text-lg">¥{orderDetails.totalAmount.toFixed(2)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 客户信息标签页 */}
              <TabsContent value="customer" className="space-y-4 mt-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center">
                      <User className="h-4 w-4 mr-2" />
                      客户信息
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-start gap-4">
                      <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                        {orderDetails.customer.avatar ? (
                          <img src={orderDetails.customer.avatar} alt={orderDetails.customer.name} className="w-full h-full object-cover rounded-full" />
                        ) : (
                          <User className="h-8 w-8 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-medium">{orderDetails.customer.name}</h3>
                        <p className="text-muted-foreground">{orderDetails.customer.phone}</p>
                        {orderDetails.customer.membershipLevel && (
                          <Badge className="mt-2">{orderDetails.customer.membershipLevel}</Badge>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mt-6">
                      <div className="space-y-2">
                        <div className="text-sm text-muted-foreground">会员注册时间</div>
                        <div className="font-medium">{orderDetails.customer.memberSince}</div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-sm text-muted-foreground">历史订单数</div>
                        <div className="font-medium">{orderDetails.customer.totalOrders}</div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-sm text-muted-foreground">累计消费</div>
                        <div className="font-medium">¥{orderDetails.customer.totalSpent}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 瑜伽商城特有：客户偏好信息 */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center">
                      <FileText className="h-4 w-4 mr-2" />
                      瑜伽偏好
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="text-sm text-muted-foreground">常用瑜伽类型</div>
                        <div className="font-medium">
                          {orderDetails.customer.yogaPreference || "暂无数据"}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-sm text-muted-foreground">常用门店</div>
                        <div className="font-medium">
                          {orderDetails.customer.preferredStore || "暂无数据"}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-sm text-muted-foreground">最近购买</div>
                        <div className="font-medium">
                          {orderDetails.customer.lastPurchase || "暂无数据"}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-sm text-muted-foreground">会员等级</div>
                        <div className="font-medium">
                          {orderDetails.customer.membershipLevel || "普通会员"}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 物流信息标签页 */}
              <TabsContent value="logistics" className="space-y-4 mt-4">
                {orderDetails.deliveryMethod === "logistics" ? (
                  <>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base flex items-center">
                          <Truck className="h-4 w-4 mr-2" />
                          物流信息
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>物流状态</Label>
                            <Select value={deliveryStatus} onValueChange={setDeliveryStatus}>
                              <SelectTrigger>
                                <SelectValue placeholder="选择物流状态" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="pending">待发货</SelectItem>
                                <SelectItem value="shipping">配送中</SelectItem>
                                <SelectItem value="delivered">已送达</SelectItem>
                                <SelectItem value="returned">已退回</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-2">
                            <Label>物流公司</Label>
                            <div className="p-2 border rounded-md">
                              {orderDetails.logisticsCompany}
                            </div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>物流单号</Label>
                          <div className="p-2 border rounded-md flex justify-between items-center">
                            <span>{orderDetails.trackingNumber}</span>
                            <Button variant="ghost" size="sm">
                              <Truck className="h-4 w-4 mr-2" />
                              查询物流
                            </Button>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>收货地址</Label>
                          <div className="p-2 border rounded-md">
                            <div>收件人: {orderDetails.address.recipient} {orderDetails.address.phone}</div>
                            <div>地址: {orderDetails.address.province} {orderDetails.address.city} {orderDetails.address.district} {orderDetails.address.detail}</div>
                            {orderDetails.address.postalCode && <div>邮编: {orderDetails.address.postalCode}</div>}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>备注</Label>
                          <Textarea
                            value={remark}
                            onChange={(e) => setRemark(e.target.value)}
                            placeholder="添加物流备注信息"
                            className="min-h-[100px]"
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </>
                ) : orderDetails.deliveryMethod === "self_pickup" ? (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <Package className="h-4 w-4 mr-2" />
                        自提信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>自提状态</Label>
                          <Select value={deliveryStatus} onValueChange={setDeliveryStatus}>
                            <SelectTrigger>
                              <SelectValue placeholder="选择自提状态" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="pending">待处理</SelectItem>
                              <SelectItem value="ready">待取货</SelectItem>
                              <SelectItem value="delivered">已取货</SelectItem>
                              <SelectItem value="expired">已过期</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>自提码</Label>
                          <div className="p-2 border rounded-md">
                            {orderDetails.pickupCode || "未生成"}
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>自提点</Label>
                        <div className="p-2 border rounded-md">
                          {orderDetails.address || "门店自提"}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>备注</Label>
                        <Textarea
                          value={remark}
                          onChange={(e) => setRemark(e.target.value)}
                          placeholder="添加自提备注信息"
                          className="min-h-[100px]"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ) : orderDetails.deliveryMethod === "verification" ? (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        核销信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>核销状态</Label>
                          <Select value={deliveryStatus} onValueChange={setDeliveryStatus}>
                            <SelectTrigger>
                              <SelectValue placeholder="选择核销状态" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="pending">未核销</SelectItem>
                              <SelectItem value="delivered">已核销</SelectItem>
                              <SelectItem value="expired">已过期</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>核销码</Label>
                          <div className="p-2 border rounded-md">
                            {orderDetails.verificationCode || "未生成"}
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>核销说明</Label>
                        <div className="p-2 border rounded-md">
                          {orderDetails.products.map((product: any) => (
                            <div key={product.id} className="mb-1">
                              {product.name} x {product.quantity}
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>备注</Label>
                        <Textarea
                          value={remark}
                          onChange={(e) => setRemark(e.target.value)}
                          placeholder="添加核销备注信息"
                          className="min-h-[100px]"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="flex items-center justify-center h-40 bg-muted/20 rounded-lg">
                    <div className="text-center">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-muted-foreground">未知的配送方式</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        配送方式: {orderDetails.deliveryMethod}
                      </p>
                    </div>
                  </div>
                )}
              </TabsContent>

              {/* 订单历程标签页 */}
              <TabsContent value="timeline" className="space-y-4 mt-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center">
                      <Clock className="h-4 w-4 mr-2" />
                      订单历程
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {orderDetails.timeline.map((event: any, index: number) => (
                        <div key={index} className="relative pl-6">
                          {index !== orderDetails.timeline.length - 1 && (
                            <div className="absolute left-[11px] top-[24px] bottom-0 w-[1px] bg-muted" />
                          )}
                          <div className="absolute left-0 top-1">
                            {event.status === "delivered" || event.status === "completed" ? (
                              <CheckCircle className="h-[22px] w-[22px] text-green-500" />
                            ) : event.status === "shipping" ? (
                              <Truck className="h-[22px] w-[22px] text-blue-500" />
                            ) : event.status === "paid" ? (
                              <CreditCard className="h-[22px] w-[22px] text-green-500" />
                            ) : event.status === "processing" ? (
                              <Package className="h-[22px] w-[22px] text-indigo-500" />
                            ) : event.status === "created" ? (
                              <FileText className="h-[22px] w-[22px] text-gray-500" />
                            ) : (
                              <Calendar className="h-[22px] w-[22px] text-gray-500" />
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{event.description}</div>
                            <div className="text-sm text-muted-foreground">{event.time}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* 瑜伽商城特有：订单操作记录 */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center">
                      <FileText className="h-4 w-4 mr-2" />
                      操作记录
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* 这里可以添加订单的操作记录，例如管理员的操作记录 */}
                      <div className="flex items-center justify-between py-2 border-b">
                        <div>
                          <div className="font-medium">订单创建</div>
                          <div className="text-sm text-muted-foreground">系统自动创建</div>
                        </div>
                        <div className="text-sm text-muted-foreground">{orderDetails.orderDate}</div>
                      </div>

                      {orderDetails.paymentStatus === "paid" && (
                        <div className="flex items-center justify-between py-2 border-b">
                          <div>
                            <div className="font-medium">支付确认</div>
                            <div className="text-sm text-muted-foreground">支付平台自动确认</div>
                          </div>
                          <div className="text-sm text-muted-foreground">{orderDetails.paymentDate}</div>
                        </div>
                      )}

                      {orderDetails.deliveryMethod === "logistics" && orderDetails.deliveryStatus === "shipping" && (
                        <div className="flex items-center justify-between py-2 border-b">
                          <div>
                            <div className="font-medium">订单发货</div>
                            <div className="text-sm text-muted-foreground">管理员操作</div>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {orderDetails.timeline.find((t: any) => t.status === "shipping")?.time || "-"}
                          </div>
                        </div>
                      )}

                      {orderDetails.deliveryStatus === "delivered" && (
                        <div className="flex items-center justify-between py-2 border-b">
                          <div>
                            <div className="font-medium">
                              {orderDetails.deliveryMethod === "logistics" ? "确认收货" :
                               orderDetails.deliveryMethod === "self_pickup" ? "确认取货" :
                               "确认核销"}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {orderDetails.deliveryMethod === "logistics" ? "用户确认" : "管理员操作"}
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {orderDetails.timeline.find((t: any) => t.status === "delivered")?.time || "-"}
                          </div>
                        </div>
                      )}

                      {orderDetails.status === "completed" && (
                        <div className="flex items-center justify-between py-2 border-b">
                          <div>
                            <div className="font-medium">订单完成</div>
                            <div className="text-sm text-muted-foreground">系统自动完成</div>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {orderDetails.timeline.find((t: any) => t.status === "completed")?.time || "-"}
                          </div>
                        </div>
                      )}

                      {orderDetails.status === "cancelled" && (
                        <div className="flex items-center justify-between py-2">
                          <div>
                            <div className="font-medium">订单取消</div>
                            <div className="text-sm text-muted-foreground">
                              {orderDetails.cancelReason ? `原因: ${orderDetails.cancelReason}` : "用户取消"}
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {orderDetails.timeline.find((t: any) => t.status === "cancelled")?.time || "-"}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <DialogFooter className="flex items-center justify-between">
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Printer className="h-4 w-4 mr-2" />
                  打印订单
                </Button>
                <Button variant="outline" size="sm">
                  <Send className="h-4 w-4 mr-2" />
                  发送订单
                </Button>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  取消
                </Button>
                <Button onClick={handleUpdateOrder}>
                  保存更改
                </Button>
              </div>
            </DialogFooter>
          </>
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">未找到订单信息</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
