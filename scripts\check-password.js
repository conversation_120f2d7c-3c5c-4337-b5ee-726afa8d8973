// 检查密码加密的脚本
const mysql = require('mysql2/promise');
const { createHash } = require('crypto');

// 密码加密函数（与API保持一致）
function hashPassword(password) {
  const salt = 'yoga_system_salt_2024';
  return createHash('sha256').update(password + salt).digest('hex');
}

async function checkPassword() {
  try {
    console.log('🔍 检查密码加密...');
    
    // 测试密码加密
    const testPassword = '123456';
    const hashedPassword = hashPassword(testPassword);
    console.log(`原始密码: ${testPassword}`);
    console.log(`加密后密码: ${hashedPassword}`);
    
    // 从环境变量中获取数据库连接信息
    const dbUrl = process.env.DATABASE_URL || 'mysql://root:123456@localhost:3306/yoga';
    
    // 解析数据库URL
    const matches = dbUrl.match(/mysql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
    if (!matches) {
      console.error('无效的数据库URL格式');
      return;
    }
    
    const [, user, password, host, port, database] = matches;
    
    console.log(`连接到数据库: ${host}:${port}/${database}`);
    
    // 创建连接
    const connection = await mysql.createConnection({
      host,
      user,
      password,
      database,
      port: parseInt(port)
    });
    
    console.log('数据库连接成功!');
    
    // 查询员工密码哈希
    const [employees] = await connection.execute(
      'SELECT id, real_name, phone, password_hash FROM Employee'
    );
    
    console.log('\n👥 员工密码信息:');
    employees.forEach(emp => {
      console.log(`ID: ${emp.id}, 姓名: ${emp.real_name}, 手机: ${emp.phone}`);
      console.log(`数据库密码哈希: ${emp.password_hash || '无'}`);
      console.log(`期望的密码哈希: ${hashedPassword}`);
      console.log(`密码匹配: ${emp.password_hash === hashedPassword ? '✅' : '❌'}`);
      console.log('---');
    });
    
    // 更新密码哈希
    console.log('\n🔧 更新员工密码哈希...');
    const [updateResult] = await connection.execute(
      'UPDATE Employee SET password_hash = ? WHERE password_hash IS NULL OR password_hash = ""',
      [hashedPassword]
    );
    console.log(`✅ 更新了 ${updateResult.affectedRows} 个员工的密码`);
    
    // 再次查询确认
    const [updatedEmployees] = await connection.execute(
      'SELECT id, real_name, phone, password_hash FROM Employee'
    );
    
    console.log('\n✅ 更新后的员工密码信息:');
    updatedEmployees.forEach(emp => {
      console.log(`ID: ${emp.id}, 姓名: ${emp.real_name}, 手机: ${emp.phone}`);
      console.log(`密码哈希: ${emp.password_hash}`);
      console.log(`密码匹配: ${emp.password_hash === hashedPassword ? '✅' : '❌'}`);
      console.log('---');
    });
    
    // 关闭连接
    await connection.end();
    console.log('\n🎉 密码检查完成!');
    
  } catch (error) {
    console.error('❌ 检查密码失败:', error);
  }
}

checkPassword();
