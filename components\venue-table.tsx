"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { MoreHorizontal, Pencil, Calendar, Eye, AlertTriangle } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useState } from "react"
import { VenueDetailDialog } from "@/components/venues/venue-detail-dialog"

const venues = [
  {
    id: "V001",
    name: "1号瑜伽室",
    capacity: 15,
    equipment: "瑜伽垫、瑜伽砖、瑜伽带",
    courses: 25,
    utilization: "85%",
    status: "available",
    area: 80,
    location: "一楼东侧",
    openTime: "06:00",
    closeTime: "22:00",
    maintenanceDate: "",
  },
  {
    id: "V002",
    name: "2号瑜伽室",
    capacity: 10,
    equipment: "瑜伽垫、瑜伽砖、瑜伽带、瑜伽球",
    courses: 18,
    utilization: "78%",
    status: "available",
    area: 60,
    location: "一楼西侧",
    openTime: "06:00",
    closeTime: "22:00",
    maintenanceDate: "",
  },
  {
    id: "V003",
    name: "3号瑜伽室",
    capacity: 15,
    equipment: "瑜伽垫、瑜伽砖、瑜伽带、瑜伽轮",
    courses: 22,
    utilization: "82%",
    status: "maintenance",
    area: 80,
    location: "二楼东侧",
    openTime: "06:00",
    closeTime: "22:00",
    maintenanceDate: "2025-04-15",
  },
  {
    id: "V004",
    name: "4号瑜伽室",
    capacity: 8,
    equipment: "空中瑜伽吊床、瑜伽垫",
    courses: 12,
    utilization: "75%",
    status: "available",
    area: 50,
    location: "二楼西侧",
    openTime: "08:00",
    closeTime: "20:00",
    maintenanceDate: "",
  },
  {
    id: "V005",
    name: "私教室",
    capacity: 2,
    equipment: "全套瑜伽装备",
    courses: 30,
    utilization: "90%",
    status: "booked",
    area: 25,
    location: "三楼",
    openTime: "08:00",
    closeTime: "20:00",
    maintenanceDate: "",
  },
]

interface VenueTableProps {
  searchQuery?: string
}

export function VenueTable({ searchQuery = "" }: VenueTableProps) {
  const [selectedVenue, setSelectedVenue] = useState<(typeof venues)[0] | null>(null)
  const [isDetailOpen, setIsDetailOpen] = useState(false)

  const filteredVenues = venues.filter(
    (venue) =>
      venue.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      venue.id.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleViewDetail = (venue: (typeof venues)[0]) => {
    setSelectedVenue(venue)
    setIsDetailOpen(true)
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>场地ID</TableHead>
              <TableHead>场地名称</TableHead>
              <TableHead>容纳人数</TableHead>
              <TableHead>面积(㎡)</TableHead>
              <TableHead>位置</TableHead>
              <TableHead>课程数</TableHead>
              <TableHead>使用率</TableHead>
              <TableHead>状态</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredVenues.length > 0 ? (
              filteredVenues.map((venue) => (
                <TableRow key={venue.id}>
                  <TableCell className="font-medium">{venue.id}</TableCell>
                  <TableCell>{venue.name}</TableCell>
                  <TableCell>{venue.capacity}人</TableCell>
                  <TableCell>{venue.area}㎡</TableCell>
                  <TableCell>{venue.location}</TableCell>
                  <TableCell>{venue.courses}</TableCell>
                  <TableCell>{venue.utilization}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        venue.status === "available"
                          ? "default"
                          : venue.status === "maintenance"
                            ? "destructive"
                            : "secondary"
                      }
                    >
                      {venue.status === "available" ? "可用" : venue.status === "maintenance" ? "维护中" : "已预订"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleViewDetail(venue)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Calendar className="mr-2 h-4 w-4" />
                          查看排期
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Pencil className="mr-2 h-4 w-4" />
                          编辑信息
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {venue.status === "available" ? (
                          <>
                            <DropdownMenuItem>
                              <AlertTriangle className="mr-2 h-4 w-4" />
                              标记为维护中
                            </DropdownMenuItem>
                          </>
                        ) : venue.status === "maintenance" ? (
                          <DropdownMenuItem>标记为可用</DropdownMenuItem>
                        ) : null}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  没有找到匹配的场地
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {selectedVenue && <VenueDetailDialog venue={selectedVenue} open={isDetailOpen} onOpenChange={setIsDetailOpen} />}
    </>
  )
}

