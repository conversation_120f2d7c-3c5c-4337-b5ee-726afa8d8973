"use client"

import { useState, useEffect } from "react"
import { useToast } from "@/hooks/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  MoreHorizontal,
  Pencil,
  Trash2,
  Plus,
  Download,
  Upload,
  Filter,
  LayoutGrid,
  LayoutList,
  BarChart,
  User,
  CreditCard,
  Tag,
  Calendar,
  Receipt,
  MessageSquare,
  Link,
  Settings,
  FileText,
  BarChart2,
  DollarSign,
  ShoppingBag,
  Clock,
  Eye,
  EyeOff,
  Copy,
  Share2,
  Percent,
  Gift,
  Users,
  AlertCircle,
  Check,
  Table as TableIcon,
  Search,
  RefreshCw,
  ChevronDown
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// 导入对话框组件
import { AddMemberCardDialog } from "@/components/members/cards/add-member-card-dialog"
import { MemberCardDetailDialog } from "@/components/members/cards/member-card-detail-dialog"
import { EditMemberCardDialog } from "@/components/members/cards/edit-member-card-dialog"
import { MemberCardStatsDialog } from "@/components/members/cards/member-card-stats-dialog"
import { CardMembersDialog } from "@/components/members/cards/reports/card-members-dialog"
import { SalesAnalysisDialog } from "@/components/members/cards/reports/sales-analysis-dialog"
import { ExportReportDialog } from "@/components/members/cards/reports/export-report-dialog"
import { FeatureInDevelopmentDialog } from "@/components/feature-in-development-dialog"
import { ImportExportMenu } from "@/components/members/cards/import-export-menu"
import { MemberCardGrid } from "@/components/members/cards/member-card-grid"

// 导入API和工具函数
import { memberCardApi, memberCardUtils, type MemberCard } from "@/lib/api/member-cards"
import { getUserInfo } from "@/lib/auth"

// 获取状态标签
const getStatusBadge = (status: string) => {
  switch (status) {
    case "销售中":
      return <Badge className="bg-green-500">销售中</Badge>
    case "已下架":
      return <Badge variant="outline" className="text-gray-500">已下架</Badge>
    case "active":
      return <Badge className="bg-green-500">销售中</Badge>
    case "inactive":
      return <Badge variant="outline" className="text-gray-500">已下架</Badge>
    case "expired":
      return <Badge variant="outline" className="text-gray-500">已过期</Badge>
    case "frozen":
      return <Badge className="bg-blue-500">已冻结</Badge>
    case "onLeave":
      return <Badge className="bg-purple-500">请假中</Badge>
    case "refunded":
      return <Badge className="bg-red-500">已退卡</Badge>
    default:
      return <Badge variant="outline">未知</Badge>
  }
}

// 示例数据
const memberCards = [
  {
    id: 1,
    name: "年卡",
    description: "365天不限次数",
    price: "¥3,680",
    originalPrice: "¥4,680",
    validity: "365天",
    limit: "不限次数",
    status: "active",
    members: 128,
    color: "#4f46e5",
    salesCount: 245,
    revenue: "¥901,600",
    createdAt: "2023-01-15",
    updatedAt: "2023-06-20",
    isTrialCard: false,
    category: "time", // 期限卡
  },
  {
    id: 2,
    name: "季卡",
    description: "90天不限次数",
    price: "¥1,280",
    originalPrice: "¥1,680",
    validity: "90天",
    limit: "不限次数",
    status: "active",
    members: 86,
    color: "#0ea5e9",
    salesCount: 156,
    revenue: "¥199,680",
    createdAt: "2023-02-10",
    updatedAt: "2023-05-15",
    isTrialCard: false,
    category: "time", // 期限卡
  },
  {
    id: 3,
    name: "月卡",
    description: "30天不限次数",
    price: "¥580",
    originalPrice: "¥680",
    validity: "30天",
    limit: "不限次数",
    status: "active",
    members: 112,
    color: "#10b981",
    salesCount: 324,
    revenue: "¥187,920",
    createdAt: "2023-01-05",
    updatedAt: "2023-06-10",
    isTrialCard: false,
    category: "time", // 期限卡
  },
  {
    id: 4,
    name: "体验卡",
    description: "7天3次体验",
    price: "¥99",
    originalPrice: "¥199",
    validity: "7天",
    limit: "最多3次",
    status: "active",
    members: 56,
    color: "#f59e0b",
    salesCount: 412,
    revenue: "¥40,788",
    createdAt: "2023-03-01",
    updatedAt: "2023-06-01",
    isTrialCard: true,
    category: "count", // 次数卡
  },
  {
    id: 5,
    name: "次卡10次",
    description: "10次课程",
    price: "¥880",
    originalPrice: "¥1,080",
    validity: "180天",
    limit: "10次",
    status: "active",
    members: 76,
    color: "#8b5cf6",
    salesCount: 189,
    revenue: "¥166,320",
    createdAt: "2023-02-15",
    updatedAt: "2023-05-20",
    isTrialCard: false,
    category: "count", // 次数卡
  },
  {
    id: 6,
    name: "次卡20次",
    description: "20次课程",
    price: "¥1,580",
    originalPrice: "¥1,880",
    validity: "365天",
    limit: "20次",
    status: "inactive",
    members: 32,
    color: "#ec4899",
    salesCount: 68,
    revenue: "¥107,440",
    createdAt: "2023-01-20",
    updatedAt: "2023-04-15",
    isTrialCard: false,
    category: "count", // 次数卡
  },
  {
    id: 7,
    name: "私教卡",
    description: "一对一私教",
    price: "¥4,880",
    originalPrice: "¥5,880",
    validity: "180天",
    limit: "10次私教",
    status: "inactive",
    members: 18,
    color: "#ef4444",
    salesCount: 42,
    revenue: "¥204,960",
    createdAt: "2023-03-10",
    updatedAt: "2023-04-20",
    isTrialCard: false,
    category: "count", // 次数卡
  },
  {
    id: 8,
    name: "储值卡500",
    description: "储值500元",
    price: "¥450",
    originalPrice: "¥500",
    validity: "365天",
    limit: "储值500元",
    status: "active",
    members: 95,
    color: "#06b6d4",
    salesCount: 210,
    revenue: "¥94,500",
    createdAt: "2023-02-05",
    updatedAt: "2023-06-15",
    isTrialCard: false,
    category: "value", // 储值卡
  },
  {
    id: 9,
    name: "储值卡1000",
    description: "储值1000元",
    price: "¥850",
    originalPrice: "¥1000",
    validity: "365天",
    limit: "储值1000元",
    status: "active",
    members: 78,
    color: "#0284c7",
    salesCount: 180,
    revenue: "¥153,000",
    createdAt: "2023-01-25",
    updatedAt: "2023-05-25",
    isTrialCard: false,
    category: "value", // 储值卡
  },
]

export default function MemberCardsPage() {
  const { toast } = useToast()

  // 数据状态管理
  const [memberCards, setMemberCards] = useState<MemberCard[]>([])
  const [loading, setLoading] = useState(true)
  const [searchKeyword, setSearchKeyword] = useState("")

  // 视图模式和筛选状态管理
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")
  const [filterStatus, setFilterStatus] = useState<"all" | "active" | "inactive">("all")
  const [filterCardCategory, setFilterCardCategory] = useState<"all" | "time" | "value" | "count">("all")
  const [filterCardType, setFilterCardType] = useState<"all" | "trial" | "normal">("all")

  // 对话框状态管理
  const [addCardOpen, setAddCardOpen] = useState(false)
  const [selectedCard, setSelectedCard] = useState<MemberCard | null>(null)
  const [editCardOpen, setEditCardOpen] = useState(false)
  const [cardToEdit, setCardToEdit] = useState<MemberCard | null>(null)
  const [statsOpen, setStatsOpen] = useState(false)
  const [featureDialogOpen, setFeatureDialogOpen] = useState(false)
  const [currentFeature, setCurrentFeature] = useState("")

  // 报表对话框状态
  const [cardMembersOpen, setCardMembersOpen] = useState(false)
  const [salesAnalysisOpen, setSalesAnalysisOpen] = useState(false)
  const [exportReportOpen, setExportReportOpen] = useState(false)
  const [selectedReportCard, setSelectedReportCard] = useState<MemberCard | null>(null)

  // 数据转换函数
  const convertToLegacyFormat = (card: MemberCard) => {
    return {
      id: card.id,
      name: card.name,
      description: card.description || '',
      price: memberCardUtils.formatPrice(Number(card.price)),
      originalPrice: card.original_price ? memberCardUtils.formatPrice(Number(card.original_price)) : memberCardUtils.formatPrice(Number(card.price)),
      validity: `${card.validity_days}天`,
      limit: card.usage_limit,
      status: (card.status === '销售中' ? 'active' : 'inactive') as 'active' | 'inactive',
      members: card.members_count,
      color: memberCardUtils.getCardTypeColor(card.card_type),
      salesCount: card.sales_count,
      revenue: memberCardUtils.formatPrice(Number(card.price) * card.sales_count),
      createdAt: card.created_at,
      updatedAt: card.updated_at,
      isTrialCard: memberCardUtils.isTrialCard(card.card_type),
      category: memberCardUtils.getCardCategory(card.card_type)
    }
  }

  // 加载会员卡数据
  const loadMemberCards = async () => {
    try {
      setLoading(true)
      const userInfo = getUserInfo()
      if (!userInfo?.tenantId) {
        console.error('未找到租户信息')
        return
      }

      const response = await memberCardApi.getAll({
        tenantId: userInfo.tenantId.toString(),
        keyword: searchKeyword || undefined,
        status: filterStatus === 'all' ? undefined : filterStatus,
        page: 1,
        pageSize: 100
      })

      if (response.code === 0) {
        setMemberCards(response.data.list || [])
        console.log('会员卡数据加载成功:', response.data.list?.length || 0, '条')
      } else {
        console.error('加载会员卡失败:', response.msg)
        toast({
          title: "加载失败",
          description: response.msg || "无法加载会员卡数据",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('加载会员卡数据失败:', error)
      toast({
        title: "加载失败",
        description: "无法加载会员卡数据",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  // 页面加载时获取数据
  useEffect(() => {
    loadMemberCards()
  }, [])

  // 搜索和筛选变化时重新加载数据
  useEffect(() => {
    const timer = setTimeout(() => {
      loadMemberCards()
    }, 300) // 防抖

    return () => clearTimeout(timer)
  }, [searchKeyword, filterStatus])

  // 打开会员卡详情
  const openCardDetail = (card: MemberCard) => {
    setSelectedCard(card)
  }

  // 打开编辑会员卡对话框
  const openEditCard = (card: MemberCard) => {
    setCardToEdit(card)
    setEditCardOpen(true)
  }

  // 处理功能点击
  const handleFeatureClick = (featureName: string) => {
    setCurrentFeature(featureName)
    setFeatureDialogOpen(true)
  }

  // 处理保存会员卡
  const handleSaveCard = async (updatedCard: MemberCard) => {
    try {
      const response = await memberCardApi.update(updatedCard.id, updatedCard)
      if (response.code === 0) {
        toast({
          title: "保存成功",
          description: `会员卡 ${updatedCard.name} 已更新`,
        })
        loadMemberCards() // 重新加载数据
      } else {
        toast({
          title: "保存失败",
          description: response.msg || "保存会员卡失败",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('保存会员卡失败:', error)
      toast({
        title: "保存失败",
        description: "保存会员卡时出现错误",
        variant: "destructive"
      })
    }
  }

  // 处理复制会员卡
  const handleCopyCard = async (originalCard: MemberCard) => {
    try {
      const userInfo = getUserInfo()
      if (!userInfo?.tenantId) {
        console.error('未找到租户信息')
        return
      }

      const newCardData = {
        tenant_id: userInfo.tenantId,
        name: `${originalCard.name} (复制)`,
        description: originalCard.description,
        price: originalCard.price,
        original_price: originalCard.original_price,
        validity_days: originalCard.validity_days,
        usage_limit: originalCard.usage_limit,
        card_type: originalCard.card_type,
        status: '已下架' // 复制的卡默认为下架状态
      }

      const response = await memberCardApi.create(newCardData)
      if (response.code === 0) {
        toast({
          title: "复制成功",
          description: `已创建新会员卡 ${newCardData.name}`,
        })
        loadMemberCards() // 重新加载数据
      } else {
        toast({
          title: "复制失败",
          description: response.msg || "复制会员卡失败",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('复制会员卡失败:', error)
      toast({
        title: "复制失败",
        description: "复制会员卡时出现错误",
        variant: "destructive"
      })
    }
  }

  // 处理会员卡状态变更
  const handleChangeCardStatus = async (cardId: number, newStatus: string, reason: string) => {
    try {
      const statusValue = newStatus === "active" ? "销售中" : "已下架"
      const response = await memberCardApi.changeStatus(cardId, statusValue)

      if (response.code === 0) {
        toast({
          title: newStatus === "active" ? "会员卡已上架" : "会员卡已下架",
          description: reason ? `原因: ${reason}` : undefined,
        })
        loadMemberCards() // 重新加载数据
      } else {
        toast({
          title: "状态更改失败",
          description: response.msg || "更改会员卡状态失败",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('更改会员卡状态失败:', error)
      toast({
        title: "状态更改失败",
        description: "更改会员卡状态时出现错误",
        variant: "destructive"
      })
    }
  }

  // 处理快捷操作
  const handleQuickAction = (action: string, card: MemberCard) => {
    switch (action) {
      case "edit":
        openEditCard(card)
        break
      case "copy":
        // 复制卡模板
        handleCopyCard(card)
        break
      case "members":
        // 查看持卡会员
        setSelectedReportCard(card)
        setCardMembersOpen(true)
        break
      case "sales":
        // 查看销售分析
        setSelectedReportCard(card)
        setSalesAnalysisOpen(true)
        break
      case "export":
        // 导出报表
        setSelectedReportCard(card)
        setExportReportOpen(true)
        break
      case "activate":
        // 上架卡种
        handleChangeCardStatus(card.id, "active", "")
        break
      case "deactivate":
        // 下架卡种
        handleChangeCardStatus(card.id, "inactive", "调整价格")
        break
      case "delete":
        // 删除卡种
        handleDeleteCard(card)
        break
      default:
        break
    }
  }

  // 处理删除会员卡
  const handleDeleteCard = async (card: MemberCard) => {
    if (!confirm(`确定要删除会员卡 "${card.name}" 吗？此操作不可恢复。`)) {
      return
    }

    try {
      const response = await memberCardApi.delete(card.id)
      if (response.code === 0) {
        toast({
          title: "删除成功",
          description: `会员卡 ${card.name} 已删除`,
        })
        loadMemberCards() // 重新加载数据
      } else {
        toast({
          title: "删除失败",
          description: response.msg || "删除会员卡失败",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('删除会员卡失败:', error)
      toast({
        title: "删除失败",
        description: "删除会员卡时出现错误",
        variant: "destructive"
      })
    }
  }

  // 根据筛选条件过滤会员卡
  const filteredCards = memberCards.filter(card => {
    // 状态筛选
    if (filterStatus !== "all") {
      const statusValue = filterStatus === "active" ? "销售中" : "已下架"
      if (card.status !== statusValue) {
        return false;
      }
    }

    // 卡类型筛选
    const isTrialCard = memberCardUtils.isTrialCard(card.card_type)
    if (filterCardType === "trial" && !isTrialCard) {
      return false;
    } else if (filterCardType === "normal" && isTrialCard) {
      return false;
    }

    // 卡片类别筛选
    if (filterCardCategory !== "all") {
      const cardCategory = memberCardUtils.getCardCategory(card.card_type)
      if (cardCategory !== filterCardCategory) {
        return false;
      }
    }

    return true;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">会员卡名称</h1>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={() => {
            // 点击高级筛选时的逻辑
          }}>
            <Filter className="mr-2 h-4 w-4" />
            高级筛选
            {(filterStatus !== "all" || filterCardType !== "all" || filterCardCategory !== "all") && (
              <Badge variant="secondary" className="ml-2 px-1">
                <span className="h-3 w-3 rounded-full bg-primary" />
              </Badge>
            )}
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <RefreshCw className="mr-2 h-4 w-4" />
                导入/导出
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => {
                // 导入会员卡逻辑
              }}>
                <Upload className="mr-2 h-4 w-4" />
                导入会员卡
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                // 导出会员卡逻辑
              }}>
                <Download className="mr-2 h-4 w-4" />
                导出会员卡
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => {
                // 下载导入模板逻辑
              }}>
                <Download className="mr-2 h-4 w-4" />
                下载导入模板
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <div className="flex rounded-md border">
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              className="rounded-r-none"
              onClick={() => setViewMode("list")}
            >
              <LayoutList className="h-4 w-4" />
              <span className="sr-only">列表视图</span>
            </Button>
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              className="rounded-l-none"
              onClick={() => setViewMode("grid")}
            >
              <LayoutGrid className="h-4 w-4" />
              <span className="sr-only">网格视图</span>
            </Button>
          </div>
          <Button onClick={() => setAddCardOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加会员卡名称
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <Card>
          <CardContent className="p-3">
            <div className="flex flex-col gap-4 md:flex-row">
              <div className="w-full md:w-1/3">
                <Input
                  placeholder="搜索会员卡名称、编号、卡类别"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                />
              </div>
              <div className="flex flex-1 flex-wrap gap-4">
                <Select defaultValue="all" onValueChange={(value) => setFilterStatus(value as "all" | "active" | "inactive")}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="卡状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="active">有效</SelectItem>
                    <SelectItem value="inactive">停用</SelectItem>
                  </SelectContent>
                </Select>

                <Select defaultValue="all" onValueChange={(value) => setFilterCardCategory(value as "all" | "time" | "value" | "count")}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="卡片类别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类别</SelectItem>
                    <SelectItem value="time">期限卡</SelectItem>
                    <SelectItem value="value">储值卡</SelectItem>
                    <SelectItem value="count">次数卡</SelectItem>
                  </SelectContent>
                </Select>

                <Select defaultValue="all" onValueChange={(value) => setFilterCardType(value as "all" | "trial" | "normal")}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="卡属性" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部属性</SelectItem>
                    <SelectItem value="normal">普通卡</SelectItem>
                    <SelectItem value="trial">体验卡</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-4">
        {viewMode === "list" ? (
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>会员卡名称</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>有效期</TableHead>
                    <TableHead>使用限制</TableHead>
                    <TableHead>卡属性</TableHead>
                    <TableHead>持卡会员</TableHead>
                    <TableHead>销售数量</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8">
                        正在加载会员卡数据...
                      </TableCell>
                    </TableRow>
                  ) : filteredCards.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={10} className="text-center py-8 text-muted-foreground">
                        暂无会员卡数据
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredCards.map((card) => (
                      <TableRow key={card.id} className="cursor-pointer" onClick={() => openCardDetail(card)}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div
                              className="h-4 w-4 rounded-full"
                              style={{ backgroundColor: memberCardUtils.getCardTypeColor(card.card_type) }}
                            />
                            <span className="font-medium">{card.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{card.description || '-'}</TableCell>
                        <TableCell>
                          <div>
                            <span className="font-medium">{memberCardUtils.formatPrice(Number(card.price))}</span>
                            {card.original_price && Number(card.original_price) !== Number(card.price) && (
                              <span className="ml-2 text-sm text-muted-foreground line-through">
                                {memberCardUtils.formatPrice(Number(card.original_price))}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{card.validity_days}天</TableCell>
                        <TableCell>{card.usage_limit}</TableCell>
                        <TableCell>
                          {memberCardUtils.isTrialCard(card.card_type) && (
                            <Badge variant="outline" className="bg-pink-50 text-pink-700 border-pink-200 mr-2">
                              体验卡
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{card.members_count}</TableCell>
                        <TableCell>{card.sales_count}</TableCell>
                        <TableCell>
                          {getStatusBadge(card.status)}
                        </TableCell>
                      <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-56">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />

                            <DropdownMenuItem onClick={() => openCardDetail(card)}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleQuickAction("edit", card)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              编辑信息
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleQuickAction("copy", card)}>
                              <Copy className="mr-2 h-4 w-4" />
                              复制卡模板
                            </DropdownMenuItem>

                            <DropdownMenuSeparator />

                            <DropdownMenuItem onClick={() => handleQuickAction("members", card)}>
                              <Users className="mr-2 h-4 w-4" />
                              持卡会员
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleQuickAction("sales", card)}>
                              <BarChart2 className="mr-2 h-4 w-4" />
                              销售分析
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleQuickAction("export", card)}>
                              <FileText className="mr-2 h-4 w-4" />
                              导出报表
                            </DropdownMenuItem>

                            <DropdownMenuSeparator />

                            {card.status === "销售中" ? (
                              <DropdownMenuItem onClick={() => handleQuickAction("deactivate", card)}>
                                <EyeOff className="mr-2 h-4 w-4" />
                                下架卡种
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem onClick={() => handleQuickAction("activate", card)}>
                                <Eye className="mr-2 h-4 w-4" />
                                重新上架
                              </DropdownMenuItem>
                            )}

                            <DropdownMenuItem
                              onClick={() => handleQuickAction("delete", card)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除卡种
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        ) : (
          <MemberCardGrid
            cards={filteredCards.map(convertToLegacyFormat)}
            onCardClick={(card) => {
              const originalCard = filteredCards.find(c => c.id === card.id)
              if (originalCard) openCardDetail(originalCard)
            }}
            onQuickAction={(action, card) => {
              const originalCard = filteredCards.find(c => c.id === card.id)
              if (originalCard) handleQuickAction(action, originalCard)
            }}
          />
        )}
      </div>

      <AddMemberCardDialog open={addCardOpen} onOpenChange={setAddCardOpen} />

      {selectedCard && (
        <MemberCardDetailDialog
          card={selectedCard}
          open={!!selectedCard}
          onOpenChange={(open) => !open && setSelectedCard(null)}
        />
      )}

      <MemberCardStatsDialog open={statsOpen} onOpenChange={setStatsOpen} cards={memberCards} />

      <EditMemberCardDialog
        open={editCardOpen}
        onOpenChange={setEditCardOpen}
        card={cardToEdit ? convertToLegacyFormat(cardToEdit) : null}
        onSave={(updatedCard) => {
          // 这里需要将legacy格式转换回MemberCard格式
          if (cardToEdit) {
            handleSaveCard({
              ...cardToEdit,
              name: updatedCard.name,
              description: updatedCard.description,
              // 其他字段保持原值或根据需要更新
            })
          }
        }}
      />

      <FeatureInDevelopmentDialog
        open={featureDialogOpen}
        onOpenChange={setFeatureDialogOpen}
        featureName={currentFeature}
      />

      {/* 报表对话框 */}
      <CardMembersDialog
        open={cardMembersOpen}
        onOpenChange={setCardMembersOpen}
        card={selectedReportCard ? {
          id: selectedReportCard.id,
          name: selectedReportCard.name,
          color: memberCardUtils.getCardTypeColor(selectedReportCard.card_type),
          members: selectedReportCard.members_count
        } : null}
      />

      <SalesAnalysisDialog
        open={salesAnalysisOpen}
        onOpenChange={setSalesAnalysisOpen}
        card={selectedReportCard ? {
          id: selectedReportCard.id,
          name: selectedReportCard.name,
          color: memberCardUtils.getCardTypeColor(selectedReportCard.card_type),
          price: memberCardUtils.formatPrice(Number(selectedReportCard.price)),
          salesCount: selectedReportCard.sales_count,
          revenue: memberCardUtils.formatPrice(Number(selectedReportCard.price) * selectedReportCard.sales_count)
        } : null}
      />

      <ExportReportDialog
        open={exportReportOpen}
        onOpenChange={setExportReportOpen}
        card={selectedReportCard ? {
          id: selectedReportCard.id,
          name: selectedReportCard.name,
          color: memberCardUtils.getCardTypeColor(selectedReportCard.card_type)
        } : null}
      />
    </div>
  )
}
