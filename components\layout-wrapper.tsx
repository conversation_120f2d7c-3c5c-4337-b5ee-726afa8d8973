"use client"

import { usePathname } from 'next/navigation'
import { Sidebar } from "@/components/sidebar"
import { TopNav } from "@/components/top-nav"

// 判断是否是公共页面（不需要显示侧边栏和顶部导航栏）
function isPublicPage(pathname: string): boolean {
  const publicPaths = ["/home", "/login"];
  return publicPaths.some(path => pathname === path || pathname.startsWith(`${path}/`));
}

export function LayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isPublic = isPublicPage(pathname);

  if (isPublic) {
    // 公共页面不显示侧边栏和顶部导航栏
    return <>{children}</>;
  }

  // 后台管理页面显示完整布局
  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex flex-col flex-1 overflow-hidden">
        <TopNav />
        <main className="flex-1 overflow-y-auto overflow-x-hidden p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
