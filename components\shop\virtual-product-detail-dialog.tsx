"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import {
  BookOpen,
  CreditCard,
  FileText,
  Package,
  Tag,
  Clock,
  Calendar,
  User,
  UserPlus,
  CheckCircle,
  AlertCircle,
  Edit,
  Trash2,
  Copy
} from "lucide-react"

interface VirtualProductDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productId: string;
}

export function VirtualProductDetailDialog({ open, onOpenChange, productId }: VirtualProductDetailDialogProps) {
  const [activeTab, setActiveTab] = React.useState("details")
  const [loading, setLoading] = React.useState(false)
  const [productDetails, setProductDetails] = React.useState<any>(null)

  // 加载虚拟商品详情
  const loadProductDetails = () => {
    if (!productId) return;

    setLoading(true);
    // 模拟API请求
    setTimeout(() => {
      // 这里应该是从API获取数据，现在使用模拟数据
      const details = getVirtualProductDetails(productId);
      setProductDetails(details);
      setLoading(false);
    }, 500);
  };

  // 使用 useEffect 处理加载和重置
  React.useEffect(() => {
    if (open && productId && !productDetails) {
      loadProductDetails();
    }
    
    if (!open && productDetails) {
      setProductDetails(null);
    }
  }, [open, productId, productDetails]);

  // 获取商品类型图标
  const getTypeIcon = (type: string) => {
    switch (type) {
      case "membership":
        return <CreditCard className="h-4 w-4 text-blue-500" />;
      case "course":
        return <BookOpen className="h-4 w-4 text-purple-500" />;
      case "identity":
        return <UserPlus className="h-4 w-4 text-green-500" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  // 获取商品类型名称
  const getTypeName = (type: string) => {
    switch (type) {
      case "membership":
        return "会员卡";
      case "course":
        return "在线课程";
      case "identity":
        return "身份卡";
      default:
        return type;
    }
  };

  // 模拟获取虚拟商品详情的函数
  const getVirtualProductDetails = (productId: string) => {
    // 模拟数据，实际应用中应该从API获取
    const products = [
      {
        id: "1",
        name: "高级会员卡 - 年卡",
        description: "瑜伽馆高级会员年卡，享受全年不限次数的课程和场地使用权",
        price: "3688.00",
        originalPrice: "4988.00",
        sales: 45,
        category: "会员卡",
        type: "membership", // 会员卡
        status: "active",
        validityPeriod: "365", // 有效期（天）
        autoActivate: true, // 自动激活
        benefits: [
          "不限次数上课",
          "专属更衣柜",
          "免费停车",
          "优先预约",
          "会员专属活动"
        ],
        createdAt: "2023-06-01",
        thumbnail: "/images/membership-card.jpg",
        verificationMethod: "二维码",
        usageInstructions: "购买后自动激活，凭会员码到前台激活实体卡",
        refundPolicy: "购买后7天内未使用可申请退款",
        stats: {
          views: 1245,
          conversions: 3.8,
          revenue: 165960
        }
      },
      {
        id: "2",
        name: "瑜伽入门课程 - 在线视频",
        description: "专业教练指导的瑜伽入门课程，包含10节视频课程",
        price: "99.00",
        originalPrice: "199.00",
        sales: 256,
        category: "在线课程",
        type: "course", // 课程
        status: "active",
        validityPeriod: "forever", // 永久有效
        autoActivate: true, // 自动激活
        courseDetails: {
          lessons: 10,
          duration: "5小时30分钟",
          level: "入门",
          format: "视频",
          instructor: "王教练"
        },
        createdAt: "2023-07-01",
        thumbnail: "/images/online-course.jpg",
        verificationMethod: "账号授权",
        usageInstructions: "购买后登录网站或APP即可观看所有课程",
        refundPolicy: "购买后3天内观看时长不超过30分钟可申请退款",
        stats: {
          views: 3560,
          conversions: 7.2,
          revenue: 25344
        }
      }
    ];
    
    return products.find(p => p.id === productId) || null;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : productDetails ? (
          <>
            <DialogHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <DialogTitle>{productDetails.name}</DialogTitle>
                  <Badge variant={productDetails.status === "active" ? "default" : "secondary"}>
                    {productDetails.status === "active" ? "已上架" : "已下架"}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="icon">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon">
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="icon" className="text-red-500">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <DialogDescription>
                创建时间: {productDetails.createdAt}
              </DialogDescription>
            </DialogHeader>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="details">基本信息</TabsTrigger>
                <TabsTrigger value="content">内容管理</TabsTrigger>
                <TabsTrigger value="stats">销售统计</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-4 mt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <Package className="h-4 w-4 mr-2" />
                        商品信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">商品ID:</span>
                        <span className="font-medium">{productDetails.id}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">商品类型:</span>
                        <span className="flex items-center">
                          {getTypeIcon(productDetails.type)}
                          <span className="ml-1">{getTypeName(productDetails.type)}</span>
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">商品分类:</span>
                        <span>{productDetails.category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">销售价格:</span>
                        <span className="text-primary font-medium">¥{productDetails.price}</span>
                      </div>
                      {productDetails.originalPrice && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">原价:</span>
                          <span className="line-through">¥{productDetails.originalPrice}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">销量:</span>
                        <span>{productDetails.sales} 件</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">有效期:</span>
                        <span>
                          {productDetails.validityPeriod === "forever" 
                            ? "永久有效" 
                            : `${productDetails.validityPeriod}天`}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">激活方式:</span>
                        <span>
                          {productDetails.autoActivate ? "自动激活" : "手动激活"}
                        </span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <FileText className="h-4 w-4 mr-2" />
                        商品详情
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">商品描述:</span>
                        <p className="mt-1">{productDetails.description}</p>
                      </div>
                      
                      <div className="mt-3">
                        <span className="text-muted-foreground">核销方式:</span>
                        <p className="mt-1">{productDetails.verificationMethod}</p>
                      </div>
                      
                      <div className="mt-3">
                        <span className="text-muted-foreground">使用说明:</span>
                        <p className="mt-1">{productDetails.usageInstructions}</p>
                      </div>
                      
                      <div className="mt-3">
                        <span className="text-muted-foreground">退款政策:</span>
                        <p className="mt-1">{productDetails.refundPolicy}</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* 根据商品类型显示不同的详情卡片 */}
                {productDetails.type === "membership" && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <CreditCard className="h-4 w-4 mr-2 text-blue-500" />
                        会员卡权益
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium mb-2">包含权益</h4>
                          <ul className="space-y-1">
                            {productDetails.benefits.map((benefit: string, index: number) => (
                              <li key={index} className="flex items-start">
                                <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                                <span>{benefit}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">使用限制</h4>
                          <p className="text-sm text-muted-foreground">
                            会员卡有效期为{productDetails.validityPeriod}天，自激活日起计算。
                            不可与其他优惠同时使用，不可转让，不可兑换现金。
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {productDetails.type === "course" && (
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base flex items-center">
                        <BookOpen className="h-4 w-4 mr-2 text-purple-500" />
                        课程详情
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">课程数量:</span>
                            <span>{productDetails.courseDetails.lessons} 节</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">总时长:</span>
                            <span>{productDetails.courseDetails.duration}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">难度等级:</span>
                            <span>{productDetails.courseDetails.level}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">课程形式:</span>
                            <span>{productDetails.courseDetails.format}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">讲师:</span>
                            <span>{productDetails.courseDetails.instructor}</span>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">课程亮点</h4>
                          <p className="text-sm text-muted-foreground">
                            专业教练指导的瑜伽入门课程，适合零基础学员。
                            包含完整的瑜伽基础动作讲解，呼吸技巧和冥想指导。
                            购买后可永久观看，不限次数回放。
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="content" className="space-y-4 mt-4">
                {/* 根据商品类型显示不同的内容管理界面 */}
                {productDetails.type === "course" ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">课程内容管理</CardTitle>
                      <CardDescription>管理课程章节和视频内容</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Button>
                          <FileText className="mr-2 h-4 w-4" />
                          添加课程章节
                        </Button>
                        
                        <div className="border rounded-md">
                          <div className="p-4 border-b bg-muted/50">
                            <div className="font-medium">第1章：瑜伽基础入门</div>
                            <div className="text-sm text-muted-foreground">3个视频 · 总时长: 45分钟</div>
                          </div>
                          <div className="p-4">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <div className="w-8 h-8 bg-muted rounded-md flex items-center justify-center mr-3">1</div>
                                  <div>
                                    <div className="font-medium">瑜伽呼吸法基础</div>
                                    <div className="text-xs text-muted-foreground">15分钟</div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Button variant="ghost" size="icon">
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button variant="ghost" size="icon" className="text-red-500">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                              
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <div className="w-8 h-8 bg-muted rounded-md flex items-center justify-center mr-3">2</div>
                                  <div>
                                    <div className="font-medium">基础站姿与平衡</div>
                                    <div className="text-xs text-muted-foreground">20分钟</div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Button variant="ghost" size="icon">
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button variant="ghost" size="icon" className="text-red-500">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : productDetails.type === "membership" ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">会员权益管理</CardTitle>
                      <CardDescription>管理会员卡包含的权益和使用规则</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Button>
                          <Plus className="mr-2 h-4 w-4" />
                          添加权益
                        </Button>
                        
                        <div className="border rounded-md">
                          <div className="p-4 border-b bg-muted/50">
                            <div className="font-medium">权益列表</div>
                          </div>
                          <div className="p-4">
                            <div className="space-y-3">
                              {productDetails.benefits.map((benefit: string, index: number) => (
                                <div key={index} className="flex items-center justify-between">
                                  <div className="flex items-center">
                                    <CheckCircle className="h-4 w-4 text-green-500 mr-3" />
                                    <div>{benefit}</div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Button variant="ghost" size="icon">
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button variant="ghost" size="icon" className="text-red-500">
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">身份权限管理</CardTitle>
                      <CardDescription>管理身份卡包含的权限和使用规则</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-8">
                        <UserPlus className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">身份权限管理</h3>
                        <p className="text-muted-foreground mb-4">
                          管理该身份卡所包含的权限和使用规则
                        </p>
                        <Button>
                          配置身份权限
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="stats" className="space-y-4 mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">销售统计</CardTitle>
                    <CardDescription>查看商品销售数据和转化率</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-muted/30 p-4 rounded-lg">
                        <div className="text-sm text-muted-foreground mb-1">浏览量</div>
                        <div className="text-2xl font-bold">{productDetails.stats.views}</div>
                        <div className="text-xs text-muted-foreground mt-1">过去30天</div>
                      </div>
                      <div className="bg-muted/30 p-4 rounded-lg">
                        <div className="text-sm text-muted-foreground mb-1">转化率</div>
                        <div className="text-2xl font-bold">{productDetails.stats.conversions}%</div>
                        <div className="text-xs text-muted-foreground mt-1">浏览-购买转化</div>
                      </div>
                      <div className="bg-muted/30 p-4 rounded-lg">
                        <div className="text-sm text-muted-foreground mb-1">销售额</div>
                        <div className="text-2xl font-bold">¥{productDetails.stats.revenue}</div>
                        <div className="text-xs text-muted-foreground mt-1">累计销售额</div>
                      </div>
                    </div>
                    
                    <div className="mt-6">
                      <h4 className="font-medium mb-3">销售趋势</h4>
                      <div className="h-48 bg-muted/30 rounded-lg flex items-center justify-center">
                        <p className="text-muted-foreground">销售趋势图表</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <DialogFooter className="flex items-center justify-between">
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <FileText className="h-4 w-4 mr-2" />
                  导出数据
                </Button>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  关闭
                </Button>
                <Button>
                  编辑商品
                </Button>
              </div>
            </DialogFooter>
          </>
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-muted-foreground">未找到商品信息</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

// 缺少的Plus图标定义
function Plus(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M5 12h14" />
      <path d="M12 5v14" />
    </svg>
  )
}
