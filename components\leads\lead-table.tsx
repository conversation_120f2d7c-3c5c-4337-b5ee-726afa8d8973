"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash,
  UserCheck,
  PhoneCall,
  Calendar,
  Mail
} from "lucide-react"
import { LeadDetailDialog } from "@/components/leads/lead-detail-dialog-new"
import { mockLeads } from "@/lib/mock-data/leads-data"

interface LeadTableProps {
  searchQuery?: string
  statusFilter?: string
}

export function LeadTable({ searchQuery = "", statusFilter = "all" }: LeadTableProps) {
  const [selectedLead, setSelectedLead] = useState<any>(null)
  const [isDetail<PERSON><PERSON>, setIsDetailOpen] = useState(false)

  // 过滤潜客数据
  const filteredLeads = mockLeads.filter((lead) => {
    // 状态过滤
    if (statusFilter !== "all" && lead.status !== statusFilter) {
      return false
    }

    // 搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        lead.name.toLowerCase().includes(query) ||
        lead.phone.includes(query) ||
        lead.source.toLowerCase().includes(query)
      )
    }

    return true
  })

  // 查看潜客详情
  const handleViewDetail = (lead: any) => {
    setSelectedLead(lead)
    setIsDetailOpen(true)
  }

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "new":
        return <Badge variant="outline">新获取</Badge>
      case "contacted":
        return <Badge variant="secondary">已联系</Badge>
      case "qualified":
        return <Badge variant="default">已确认</Badge>
      case "negotiating":
        return <Badge variant="warning">洽谈中</Badge>
      case "converted":
        return <Badge variant="success">已转化</Badge>
      case "lost":
        return <Badge variant="destructive">已流失</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>潜客信息</TableHead>
              <TableHead>联系方式</TableHead>
              <TableHead>来源</TableHead>
              <TableHead>意向度</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>获取时间</TableHead>
              <TableHead>负责人</TableHead>
              <TableHead>最近跟进</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredLeads.length > 0 ? (
              filteredLeads.map((lead) => (
                <TableRow key={lead.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-9 w-9">
                        <AvatarFallback>{lead.name[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{lead.name}</div>
                        <div className="text-sm text-muted-foreground">{lead.gender === "male" ? "男" : "女"}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <PhoneCall className="mr-2 h-3.5 w-3.5 text-muted-foreground" />
                        <span>{lead.phone}</span>
                      </div>
                      {lead.email && (
                        <div className="flex items-center mt-1">
                          <Mail className="mr-2 h-3.5 w-3.5 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">{lead.email}</span>
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{lead.source}</TableCell>
                  <TableCell>
                    <div className="flex">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <svg
                          key={i}
                          className={`h-4 w-4 ${i < lead.interest ? "text-yellow-400 fill-yellow-400" : "text-gray-300 fill-gray-300"}`}
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                        >
                          <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                        </svg>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(lead.status)}</TableCell>
                  <TableCell>{lead.createdAt}</TableCell>
                  <TableCell>{lead.assignedTo}</TableCell>
                  <TableCell>{lead.lastFollowUp}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">打开菜单</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetail(lead)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑信息
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Calendar className="mr-2 h-4 w-4" />
                          添加跟进
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <UserCheck className="mr-2 h-4 w-4" />
                          转为会员
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Trash className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  没有找到匹配的潜客
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {selectedLead && (
        <LeadDetailDialog
          lead={selectedLead}
          open={isDetailOpen}
          onOpenChange={setIsDetailOpen}
        />
      )}
    </>
  )
}
