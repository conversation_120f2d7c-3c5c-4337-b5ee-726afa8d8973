"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { Merge, Calculator, AlertTriangle, CheckCircle2 } from "lucide-react"
import { memberCards } from "@/services/member-card-data"

// 合并后的卡种选项
const mergeCardTypes = [
  { id: "stored_value", name: "储值卡", description: "将所有价值合并为储值余额", color: "#8B5CF6" },
  { id: "combo", name: "组合卡", description: "保留各种权益，统一管理", color: "#673AB7" },
  { id: "count", name: "次卡", description: "将价值转换为使用次数", color: "#9C27B0" },
  { id: "period", name: "期限卡", description: "将价值转换为使用期限", color: "#4CAF50" },
]

interface MergeMemberCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentCard: any
}

export function MergeMemberCardDialog({
  open,
  onOpenChange,
  currentCard
}: MergeMemberCardDialogProps) {
  const { toast } = useToast()
  const [selectedCards, setSelectedCards] = useState<string[]>([])
  const [mergeType, setMergeType] = useState("")
  const [mergeNote, setMergeNote] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  // 获取同一会员的其他卡片
  const getMemberOtherCards = () => {
    if (!currentCard) return []

    return memberCards.filter(card =>
      card.memberId === currentCard.memberId &&
      card.id !== currentCard.id &&
      card.status !== "refunded" &&
      card.status !== "expired"
    )
  }

  // 计算卡片剩余价值
  const calculateCardValue = (card: any) => {
    if (!card) return 0

    if (card.cardTypeBadge === "期限卡") {
      const totalDays = card.totalDays || 365
      const remainingDays = card.remainingDays || 0
      return Math.round((card.actualPrice * remainingDays) / totalDays)
    } else if (card.cardTypeBadge === "次卡") {
      const totalCount = card.totalCount || 20
      const remainingCount = card.remainingCount || 0
      return Math.round((card.actualPrice * remainingCount) / totalCount)
    } else if (card.cardTypeBadge === "储值卡") {
      return card.remainingValue || 0
    }
    return 0
  }

  // 计算合并后的总价值
  const calculateMergedValue = () => {
    const currentValue = calculateCardValue(currentCard)
    const selectedCardsValue = selectedCards.reduce((total, cardId) => {
      const card = memberCards.find(c => c.id === cardId)
      return total + (card ? calculateCardValue(card) : 0)
    }, 0)

    // 合并优惠：每多合并一张卡，额外赠送5%价值
    const bonusValue = Math.round(selectedCardsValue * 0.05 * selectedCards.length)

    return {
      currentValue,
      selectedCardsValue,
      bonusValue,
      totalValue: currentValue + selectedCardsValue + bonusValue
    }
  }

  // 处理卡片选择
  const handleCardSelect = (cardId: string, checked: boolean) => {
    if (checked) {
      setSelectedCards(prev => [...prev, cardId])
    } else {
      setSelectedCards(prev => prev.filter(id => id !== cardId))
    }
  }

  // 处理合并
  const handleMerge = async () => {
    if (selectedCards.length === 0) {
      toast({
        title: "请选择要合并的卡片",
        description: "至少需要选择一张卡片进行合并",
        variant: "destructive",
      })
      return
    }

    if (!mergeType) {
      toast({
        title: "请选择合并类型",
        description: "请选择合并后的卡片类型",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)

    try {
      // 模拟合并处理
      await new Promise(resolve => setTimeout(resolve, 2000))

      const { totalValue } = calculateMergedValue()
      const mergeTypeInfo = mergeCardTypes.find(type => type.id === mergeType)

      toast({
        title: "合并成功",
        description: `已成功合并为${mergeTypeInfo?.name}，总价值：¥${totalValue}`,
      })

      onOpenChange(false)

      // 重置表单
      setSelectedCards([])
      setMergeType("")
      setMergeNote("")

    } catch (error) {
      toast({
        title: "合并失败",
        description: "合并过程中出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const otherCards = getMemberOtherCards()
  const { currentValue, selectedCardsValue, bonusValue, totalValue } = calculateMergedValue()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Merge className="h-5 w-5" />
            合并会员卡
          </DialogTitle>
          <DialogDescription>
            将多张会员卡合并为一张，整合剩余价值和权益
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 当前主卡 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              主卡（保留）
            </h3>
            {currentCard && (
              <Card className="border-green-200">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">{currentCard.cardType}</CardTitle>
                    <Badge style={{ backgroundColor: currentCard.cardTypeColor }}>
                      {currentCard.cardTypeBadge}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">卡号：</span>
                      <span className="font-medium">{currentCard.id}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">剩余价值：</span>
                      <span className="font-medium text-green-600">¥{currentValue}</span>
                    </div>
                    {currentCard.remainingDays !== null && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">剩余天数：</span>
                        <span className="font-medium">{currentCard.remainingDays}天</span>
                      </div>
                    )}
                    {currentCard.remainingCount !== null && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">剩余次数：</span>
                        <span className="font-medium">{currentCard.remainingCount}次</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* 可合并的卡片 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">选择要合并的卡片</h3>
            {otherCards.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center text-muted-foreground">
                  <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                  <p>该会员没有其他可合并的卡片</p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {otherCards.map((card) => (
                  <Card
                    key={card.id}
                    className={`cursor-pointer transition-all ${
                      selectedCards.includes(card.id) ? 'ring-2 ring-primary bg-blue-50' : 'hover:shadow-md'
                    }`}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={selectedCards.includes(card.id)}
                          onCheckedChange={(checked) => handleCardSelect(card.id, checked as boolean)}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm truncate">{card.cardType}</h4>
                            <Badge
                              style={{ backgroundColor: card.cardTypeColor }}
                              className="text-xs"
                            >
                              {card.cardTypeBadge}
                            </Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                            <div>卡号：{card.id}</div>
                            <div>价值：¥{calculateCardValue(card)}</div>
                            {card.remainingDays !== null && (
                              <div>剩余：{card.remainingDays}天</div>
                            )}
                            {card.remainingCount !== null && (
                              <div>剩余：{card.remainingCount}次</div>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* 合并类型选择 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">合并后卡片类型</h3>
            <div className="space-y-3">
              {mergeCardTypes.map((type) => (
                <Card
                  key={type.id}
                  className={`cursor-pointer transition-all ${
                    mergeType === type.id ? 'ring-2 ring-primary' : 'hover:shadow-md'
                  }`}
                  onClick={() => setMergeType(type.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: type.color }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{type.name}</h4>
                        <p className="text-xs text-muted-foreground mt-1">
                          {type.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* 价值计算 */}
        {selectedCards.length > 0 && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Calculator className="h-4 w-4" />
                合并价值计算
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-muted-foreground">主卡价值</div>
                  <div className="text-lg font-semibold text-green-600">¥{currentValue}</div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-muted-foreground">合并卡价值</div>
                  <div className="text-lg font-semibold text-blue-600">¥{selectedCardsValue}</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <div className="text-muted-foreground">合并奖励</div>
                  <div className="text-lg font-semibold text-orange-600">+¥{bonusValue}</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-muted-foreground">合并后总值</div>
                  <div className="text-lg font-semibold text-purple-600">¥{totalValue}</div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-100 border border-blue-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div className="text-blue-700 text-sm">
                    <p className="font-medium">合并优惠说明：</p>
                    <p>每合并一张卡片，额外赠送该卡5%的价值作为合并奖励</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 备注 */}
        {selectedCards.length > 0 && (
          <div className="space-y-2">
            <Label htmlFor="mergeNote">合并备注</Label>
            <Input
              id="mergeNote"
              placeholder="合并原因或备注信息"
              value={mergeNote}
              onChange={(e) => setMergeNote(e.target.value)}
            />
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button
            onClick={handleMerge}
            disabled={selectedCards.length === 0 || !mergeType || isProcessing}
            className="min-w-[100px]"
          >
            {isProcessing ? "处理中..." : `确认合并 (${selectedCards.length}张卡)`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
