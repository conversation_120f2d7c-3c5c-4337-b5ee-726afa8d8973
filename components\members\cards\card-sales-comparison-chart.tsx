"use client"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface CardSalesComparisonChartProps {
  className?: string
}

export function CardSalesComparisonChart({ className }: CardSalesComparisonChartProps) {
  // 模拟会员卡销售数据
  const cardSalesData = [
    { name: "年卡", sales: 320, revenue: 576000, color: "#4F46E5" },
    { name: "季卡", sales: 210, revenue: 252000, color: "#06B6D4" },
    { name: "月卡", sales: 180, revenue: 144000, color: "#10B981" },
    { name: "次卡", sales: 120, revenue: 216000, color: "#F59E0B" },
    { name: "体验卡", sales: 65, revenue: 84890, color: "#EC4899" },
  ]
  
  // 计算总销售额和总销售量
  const totalRevenue = cardSalesData.reduce((sum, item) => sum + item.revenue, 0)
  const totalSales = cardSalesData.reduce((sum, item) => sum + item.sales, 0)
  
  // 计算每种卡的百分比
  const cardSalesWithPercentage = cardSalesData.map(item => ({
    ...item,
    salesPercentage: Math.round((item.sales / totalSales) * 100),
    revenuePercentage: Math.round((item.revenue / totalRevenue) * 100)
  }))
  
  // 排序，从高到低
  cardSalesWithPercentage.sort((a, b) => b.revenue - a.revenue)
  
  // 获取最大销售量，用于计算柱状图高度
  const maxSales = Math.max(...cardSalesWithPercentage.map(item => item.sales))
  const maxRevenue = Math.max(...cardSalesWithPercentage.map(item => item.revenue))
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-2">
        <CardTitle className="text-base">会员卡销售对比</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {/* 销售量对比 */}
          <div>
            <h4 className="mb-3 text-sm font-medium">销售数量对比</h4>
            <div className="space-y-2">
              {cardSalesWithPercentage.map((item, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div 
                        className="h-3 w-3 rounded-sm" 
                        style={{ backgroundColor: item.color }}
                      />
                      <span>{item.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{item.sales}</span>
                      <span className="text-muted-foreground">({item.salesPercentage}%)</span>
                    </div>
                  </div>
                  <div className="h-2 w-full overflow-hidden rounded-full bg-secondary">
                    <div 
                      className="h-full rounded-full" 
                      style={{ 
                        width: `${item.salesPercentage}%`, 
                        backgroundColor: item.color 
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* 销售额对比 */}
          <div>
            <h4 className="mb-3 text-sm font-medium">销售金额对比</h4>
            <div className="space-y-2">
              {cardSalesWithPercentage.map((item, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div 
                        className="h-3 w-3 rounded-sm" 
                        style={{ backgroundColor: item.color }}
                      />
                      <span>{item.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">¥{(item.revenue / 10000).toFixed(1)}万</span>
                      <span className="text-muted-foreground">({item.revenuePercentage}%)</span>
                    </div>
                  </div>
                  <div className="h-2 w-full overflow-hidden rounded-full bg-secondary">
                    <div 
                      className="h-full rounded-full" 
                      style={{ 
                        width: `${item.revenuePercentage}%`, 
                        backgroundColor: item.color 
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* 双柱状图 */}
          <div>
            <h4 className="mb-3 text-sm font-medium">销售数量与金额对比</h4>
            <div className="flex h-[180px] items-end justify-between gap-2 pt-6">
              {cardSalesWithPercentage.map((item, index) => {
                const salesHeight = (item.sales / maxSales) * 150
                const revenueHeight = (item.revenue / maxRevenue) * 150
                
                return (
                  <div key={index} className="flex w-1/5 flex-col items-center">
                    <div className="flex w-full gap-1">
                      {/* 销售量柱子 */}
                      <div className="flex w-1/2 flex-col items-center">
                        <div 
                          className="w-full rounded-t-sm transition-all duration-300 hover:opacity-80"
                          style={{ height: `${salesHeight}px`, backgroundColor: item.color }}
                        />
                      </div>
                      
                      {/* 销售额柱子 */}
                      <div className="flex w-1/2 flex-col items-center">
                        <div 
                          className="w-full rounded-t-sm opacity-70 transition-all duration-300 hover:opacity-100"
                          style={{ height: `${revenueHeight}px`, backgroundColor: item.color }}
                        />
                      </div>
                    </div>
                    
                    <div className="mt-2 text-center">
                      <div className="text-xs">{item.name}</div>
                    </div>
                  </div>
                )
              })}
            </div>
            
            <div className="mt-2 flex justify-center gap-4">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-sm bg-primary" />
                <span className="text-xs">销售数量</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-sm bg-primary opacity-70" />
                <span className="text-xs">销售金额</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
