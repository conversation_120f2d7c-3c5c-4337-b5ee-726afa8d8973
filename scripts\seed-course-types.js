const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const courseTypesData = [
  {
    name: "团课1",
    description: "适合多人参与的常规团体课程，人数通常在15-30人",
    color: "#4285F4",
    status: 1,
    display_order: 1,
    course_count: 35
  },
  {
    name: "小班课1",
    description: "小班精品课，人数控制在10人，更精准的指导与练习",
    color: "#34A853",
    status: 1,
    display_order: 2,
    course_count: 22
  },
  {
    name: "精品课",
    description: "高端精品课程，由资深老师授课，提供个性化的练习指导",
    color: "#FBBC04",
    status: 1,
    display_order: 3,
    course_count: 15
  },
  {
    name: "私教课",
    description: "一对一个性化教学，根据学员情况定制专属练习方案",
    color: "#EA4335",
    status: 1,
    display_order: 4,
    course_count: 28
  },
  {
    name: "孕妇课",
    description: "针对孕期女性设计的安全瑜伽课程，注重身心健康调理",
    color: "#9C27B0",
    status: 1,
    display_order: 5,
    course_count: 12
  },
  {
    name: "空中瑜伽",
    description: "使用吊床辅助的瑜伽练习，增强身体柔韧性和核心力量",
    color: "#FF5722",
    status: 1,
    display_order: 6,
    course_count: 12
  },
  {
    name: "热瑜伽",
    description: "在高温环境下进行的瑜伽练习，促进排毒和身体柔韧性",
    color: "#FF9800",
    status: 1,
    display_order: 7,
    course_count: 0
  },
  {
    name: "基础瑜伽",
    description: "基础瑜伽课程，注重基本体式的学习和呼吸法的练习",
    color: "#00BCD4",
    status: 1,
    display_order: 8,
    course_count: 20
  },
  {
    name: "理疗瑜伽",
    description: "结合理疗知识的瑜伽课程，帮助缓解身体不适和疼痛",
    color: "#607D8B",
    status: 0,
    display_order: 9,
    course_count: 15
  }
];

async function seedCourseTypes() {
  try {
    console.log('开始为课程类型表添加种子数据...');
    
    // 获取所有租户
    const tenants = await prisma.tenant.findMany();
    
    if (tenants.length === 0) {
      console.log('没有找到租户，请先创建租户');
      return;
    }
    
    // 为每个租户添加课程类型
    for (const tenant of tenants) {
      console.log(`为租户 ${tenant.tenant_name} (ID: ${tenant.id}) 添加课程类型...`);
      
      // 检查是否已有课程类型
      const existingTypes = await prisma.courseType.findMany({
        where: { tenant_id: tenant.id }
      });
      
      if (existingTypes.length > 0) {
        console.log(`租户 ${tenant.tenant_name} 已有 ${existingTypes.length} 个课程类型，跳过`);
        continue;
      }
      
      // 添加课程类型
      for (const typeData of courseTypesData) {
        await prisma.courseType.create({
          data: {
            ...typeData,
            tenant_id: tenant.id
          }
        });
      }
      
      console.log(`为租户 ${tenant.tenant_name} 添加了 ${courseTypesData.length} 个课程类型`);
    }
    
    console.log('课程类型种子数据添加完成！');
    
  } catch (error) {
    console.error('添加种子数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedCourseTypes();
