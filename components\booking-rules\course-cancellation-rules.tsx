"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon } from "lucide-react"

const formSchema = z.object({
  // 取消时间设置
  freeCancellationHours: z.coerce.number().min(0).max(72),
  penaltyCancellationHours: z.coerce.number().min(0).max(24),
  noCancellationHours: z.coerce.number().min(0).max(12),
  
  // 取消方式
  allowSelfCancellation: z.boolean(),
  allowStaffCancellation: z.boolean(),
  allowBatchCancellation: z.boolean(),
  
  // 取消次数限制
  maxCancellationsPerDay: z.coerce.number().min(0).max(10),
  maxCancellationsPerWeek: z.coerce.number().min(0).max(20),
  maxCancellationsPerMonth: z.coerce.number().min(0).max(50),
  
  // 覆盖全局设置
  overrideGlobal: z.boolean(),
})

interface CourseCancellationRulesProps {
  courseTypeId: string;
  onChange: () => void;
}

export function CourseCancellationRules({ courseTypeId, onChange }: CourseCancellationRulesProps) {
  // 根据课程类型获取不同的默认值
  const getDefaultValues = () => {
    // 这里可以根据courseTypeId返回不同的默认值
    if (courseTypeId === "private") {
      return {
        freeCancellationHours: 48,
        penaltyCancellationHours: 12,
        noCancellationHours: 4,
        allowSelfCancellation: true,
        allowStaffCancellation: true,
        allowBatchCancellation: false,
        maxCancellationsPerDay: 1,
        maxCancellationsPerWeek: 2,
        maxCancellationsPerMonth: 5,
        overrideGlobal: true,
      }
    }
    
    return {
      freeCancellationHours: 24,
      penaltyCancellationHours: 6,
      noCancellationHours: 2,
      allowSelfCancellation: true,
      allowStaffCancellation: true,
      allowBatchCancellation: true,
      maxCancellationsPerDay: 2,
      maxCancellationsPerWeek: 5,
      maxCancellationsPerMonth: 10,
      overrideGlobal: false,
    }
  }
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
  })
  
  const watchOverrideGlobal = form.watch("overrideGlobal")
  
  return (
    <Form {...form}>
      <form onChange={onChange} className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>取消规则设置</CardTitle>
                <CardDescription>
                  设置课程取消的时间限制、取消方式和取消次数限制等
                </CardDescription>
              </div>
              <FormField
                control={form.control}
                name="overrideGlobal"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      覆盖全局设置
                    </FormLabel>
                  </FormItem>
                )}
              />
            </div>
          </CardHeader>
          <CardContent>
            {!watchOverrideGlobal && (
              <Alert className="mb-6">
                <InfoIcon className="h-4 w-4" />
                <AlertTitle>使用全局设置</AlertTitle>
                <AlertDescription>
                  当前使用全局取消规则设置。启用"覆盖全局设置"开关可自定义此课程类型的取消规则。
                </AlertDescription>
              </Alert>
            )}
            
            {watchOverrideGlobal && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">取消时间设置</h3>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="freeCancellationHours"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>免费取消时间</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">小时</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            课程开始前多少小时可免费取消
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="penaltyCancellationHours"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>惩罚取消时间</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">小时</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            课程开始前多少小时内取消将受到惩罚
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="noCancellationHours"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>禁止取消时间</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">小时</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            课程开始前多少小时内禁止取消
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">取消方式</h3>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="allowSelfCancellation"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许自主取消
                            </FormLabel>
                            <FormDescription>
                              会员可通过小程序/APP自行取消
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="allowStaffCancellation"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许代操作取消
                            </FormLabel>
                            <FormDescription>
                              前台/教练可代会员取消
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="allowBatchCancellation"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许批量取消
                            </FormLabel>
                            <FormDescription>
                              管理员可批量取消多个预约
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">取消次数限制</h3>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="maxCancellationsPerDay"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>每日最大取消次数</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">次</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            会员每天最多可取消的预约次数
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="maxCancellationsPerWeek"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>每周最大取消次数</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">次</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            会员每周最多可取消的预约次数
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="maxCancellationsPerMonth"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>每月最大取消次数</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">次</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            会员每月最多可取消的预约次数
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </form>
    </Form>
  )
}
