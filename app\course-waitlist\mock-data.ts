// 模拟课程数据
export const coursesData = [
  // 2025-05-15 课程
  {
    id: "C001",
    name: "哈他瑜伽初级",
    time: "2025-05-15 10:00-11:30",
    teacher: "王教练",
    venue: "1号瑜伽室",
    capacity: 15,
    booked: 15,
    waitlistCount: 3,
    status: "full",
  },
  {
    id: "C002",
    name: "阴瑜伽",
    time: "2025-05-15 14:00-15:30",
    teacher: "李教练",
    venue: "2号瑜伽室",
    capacity: 12,
    booked: 12,
    waitlistCount: 2,
    status: "full",
  },
  {
    id: "C003",
    name: "流瑜伽中级",
    time: "2025-05-15 09:00-10:30",
    teacher: "张教练",
    venue: "1号瑜伽室",
    capacity: 18,
    booked: 15,
    waitlistCount: 0,
    status: "available",
  },
  {
    id: "C004",
    name: "私教课",
    time: "2025-05-15 16:00-17:00",
    teacher: "赵教练",
    venue: "私教室",
    capacity: 1,
    booked: 1,
    waitlistCount: 4,
    status: "full",
  },
  {
    id: "C005",
    name: "普拉提基础",
    time: "2025-05-15 18:30-20:00",
    teacher: "刘教练",
    venue: "3号瑜伽室",
    capacity: 20,
    booked: 20,
    waitlistCount: 6,
    status: "full",
  },
  {
    id: "C006",
    name: "瑜伽轮工作坊",
    time: "2025-05-15 15:00-17:00",
    teacher: "陈教练",
    venue: "2号瑜伽室",
    capacity: 10,
    booked: 10,
    waitlistCount: 5,
    status: "full",
  },

  // 2025-05-16 课程
  {
    id: "C007",
    name: "空中瑜伽初级",
    time: "2025-05-16 11:00-12:30",
    teacher: "林教练",
    venue: "空中瑜伽室",
    capacity: 8,
    booked: 8,
    waitlistCount: 7,
    status: "full",
  },
  {
    id: "C008",
    name: "孕妇瑜伽",
    time: "2025-05-16 10:30-12:00",
    teacher: "周教练",
    venue: "3号瑜伽室",
    capacity: 12,
    booked: 12,
    waitlistCount: 3,
    status: "full",
  },
  {
    id: "C009",
    name: "冥想课",
    time: "2025-05-16 20:00-21:00",
    teacher: "吴教练",
    venue: "冥想室",
    capacity: 15,
    booked: 15,
    waitlistCount: 2,
    status: "full",
  },

  // 2025-05-20 课程
  {
    id: "C010",
    name: "高温瑜伽",
    time: "2025-05-20 19:00-20:30",
    teacher: "郑教练",
    venue: "高温瑜伽室",
    capacity: 15,
    booked: 15,
    waitlistCount: 8,
    status: "full",
  },
  {
    id: "C011",
    name: "阿斯汤加瑜伽",
    time: "2025-05-20 06:30-08:00",
    teacher: "王教练",
    venue: "1号瑜伽室",
    capacity: 12,
    booked: 12,
    waitlistCount: 4,
    status: "full",
  },
  {
    id: "C012",
    name: "理疗瑜伽",
    time: "2025-05-20 13:00-14:30",
    teacher: "张教练",
    venue: "2号瑜伽室",
    capacity: 10,
    booked: 8,
    waitlistCount: 0,
    status: "available",
  },

  // 2025-05-25 课程
  {
    id: "C013",
    name: "哈他瑜伽进阶",
    time: "2025-05-25 10:00-11:30",
    teacher: "王教练",
    venue: "1号瑜伽室",
    capacity: 12,
    booked: 12,
    waitlistCount: 5,
    status: "full",
  },
  {
    id: "C014",
    name: "阴阳瑜伽",
    time: "2025-05-25 14:00-15:30",
    teacher: "李教练",
    venue: "2号瑜伽室",
    capacity: 15,
    booked: 13,
    waitlistCount: 0,
    status: "available",
  },

  // 2025-06-01 课程
  {
    id: "C015",
    name: "儿童瑜伽",
    time: "2025-06-01 09:30-10:30",
    teacher: "周教练",
    venue: "3号瑜伽室",
    capacity: 10,
    booked: 10,
    waitlistCount: 3,
    status: "full",
  },
  {
    id: "C016",
    name: "瑜伽疗愈工作坊",
    time: "2025-06-01 14:00-16:00",
    teacher: "陈教练",
    venue: "2号瑜伽室",
    capacity: 15,
    booked: 15,
    waitlistCount: 6,
    status: "full",
  },

  // 2025-06-10 课程
  {
    id: "C017",
    name: "高级流瑜伽",
    time: "2025-06-10 18:30-20:00",
    teacher: "张教练",
    venue: "1号瑜伽室",
    capacity: 12,
    booked: 10,
    waitlistCount: 0,
    status: "available",
  },
  {
    id: "C018",
    name: "瑜伽冥想进阶",
    time: "2025-06-10 20:15-21:15",
    teacher: "吴教练",
    venue: "冥想室",
    capacity: 15,
    booked: 15,
    waitlistCount: 4,
    status: "full",
  },
];

// 模拟排队数据
export const waitlistData = [
  // C001 哈他瑜伽初级的排队
  {
    id: "W001",
    courseId: "C001",
    memberId: "M001",
    memberName: "张三",
    memberPhone: "13812345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    memberLevel: "金卡会员",
    position: 1,
    waitlistTime: "2025-05-14 14:23:45",
    status: "waiting",
    priority: "high",
    note: "希望能安排在靠窗位置",
    notificationCount: 0,
    lastNotificationTime: null,
  },
  {
    id: "W002",
    courseId: "C001",
    memberId: "M002",
    memberName: "李四",
    memberPhone: "13987654321",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    memberLevel: "银卡会员",
    position: 2,
    waitlistTime: "2025-05-14 16:45:12",
    status: "waiting",
    priority: "medium",
    note: "",
    notificationCount: 0,
    lastNotificationTime: null,
  },
  {
    id: "W003",
    courseId: "C001",
    memberId: "M003",
    memberName: "王五",
    memberPhone: "13765432198",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    memberLevel: "普通会员",
    position: 3,
    waitlistTime: "2025-05-14 18:12:33",
    status: "waiting",
    priority: "low",
    note: "第一次尝试这个课程",
    notificationCount: 0,
    lastNotificationTime: null,
  },

  // C002 阴瑜伽的排队
  {
    id: "W004",
    courseId: "C002",
    memberId: "M004",
    memberName: "赵六",
    memberPhone: "13612345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    memberLevel: "金卡会员",
    position: 1,
    waitlistTime: "2025-05-14 09:34:21",
    status: "waiting",
    priority: "high",
    note: "",
    notificationCount: 0,
    lastNotificationTime: null,
  },
  {
    id: "W005",
    courseId: "C002",
    memberId: "M005",
    memberName: "钱七",
    memberPhone: "13512345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    memberLevel: "银卡会员",
    position: 2,
    waitlistTime: "2025-05-14 10:45:33",
    status: "waiting",
    priority: "medium",
    note: "有腰伤，需要特别关注",
    notificationCount: 0,
    lastNotificationTime: null,
  },

  // C004 私教课的排队
  {
    id: "W006",
    courseId: "C004",
    memberId: "M006",
    memberName: "孙八",
    memberPhone: "13412345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    memberLevel: "钻石会员",
    position: 1,
    waitlistTime: "2025-05-13 16:23:11",
    status: "waiting",
    priority: "highest",
    note: "需要针对肩颈问题进行训练",
    notificationCount: 0,
    lastNotificationTime: null,
  },
  {
    id: "W007",
    courseId: "C004",
    memberId: "M007",
    memberName: "周九",
    memberPhone: "13312345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    memberLevel: "金卡会员",
    position: 2,
    waitlistTime: "2025-05-13 17:45:22",
    status: "waiting",
    priority: "high",
    note: "",
    notificationCount: 0,
    lastNotificationTime: null,
  },
  {
    id: "W008",
    courseId: "C004",
    memberId: "M008",
    memberName: "吴十",
    memberPhone: "13212345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    memberLevel: "银卡会员",
    position: 3,
    waitlistTime: "2025-05-14 08:12:45",
    status: "waiting",
    priority: "medium",
    note: "希望能安排在下午时段",
    notificationCount: 0,
    lastNotificationTime: null,
  },
  {
    id: "W009",
    courseId: "C004",
    memberId: "M009",
    memberName: "郑十一",
    memberPhone: "13112345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    memberLevel: "普通会员",
    position: 4,
    waitlistTime: "2025-05-14 09:34:56",
    status: "waiting",
    priority: "low",
    note: "",
    notificationCount: 0,
    lastNotificationTime: null,
  }
];

// 模拟排队规则设置
export const waitlistSettings = {
  enableWaitlist: true,
  maxWaitlistSize: 10,
  autoNotify: true,
  notificationExpireHours: 24,
  priorityRules: [
    { level: "钻石会员", priority: "highest" },
    { level: "金卡会员", priority: "high" },
    { level: "银卡会员", priority: "medium" },
    { level: "普通会员", priority: "low" }
  ],
  waitlistVisibility: "public", // public, private, membersOnly
  allowSelfCancel: true,
  allowStaffAdjustment: true,
  notificationMethods: ["app", "sms", "wechat"],
  defaultMessage: "尊敬的会员，您在{courseName}课程的排队已经有名额了，请在24小时内确认是否预约，过期将自动取消排队资格。"
};
