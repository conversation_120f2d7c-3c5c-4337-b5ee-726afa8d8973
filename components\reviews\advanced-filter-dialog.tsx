"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ReviewFilterParams } from "@/lib/types/review"

// 模拟数据 - 教练列表
const coaches = [
  { id: 1, name: "张教练" },
  { id: 2, name: "李教练" },
  { id: 3, name: "王教练" },
  { id: 4, name: "赵教练" },
  { id: 5, name: "刘教练" },
]

// 模拟数据 - 课程类型
const courseTypes = [
  { id: 1, name: "基础瑜伽" },
  { id: 2, name: "高级瑜伽" },
  { id: 3, name: "阴瑜伽" },
  { id: 4, name: "孕产瑜伽" },
  { id: 5, name: "空中瑜伽" },
  { id: 6, name: "理疗瑜伽" },
  { id: 7, name: "热瑜伽" },
]

interface AdvancedFilterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onApplyFilter?: (filters: ReviewFilterParams) => void
  initialFilters?: ReviewFilterParams
}

export function AdvancedFilterDialog({ 
  open, 
  onOpenChange, 
  onApplyFilter,
  initialFilters 
}: AdvancedFilterDialogProps) {
  // 筛选状态
  const [statusFilters, setStatusFilters] = useState({
    published: true,
    pending: true,
    hidden: true,
    ...(initialFilters?.status ? { [initialFilters.status]: true } : {})
  })
  
  const [selectedCoach, setSelectedCoach] = useState(
    initialFilters?.coachId?.toString() || "all"
  )
  
  const [selectedCourseType, setSelectedCourseType] = useState(
    initialFilters?.courseType || "all"
  )
  
  const [ratingRange, setRatingRange] = useState<[number, number]>([1, 5])
  
  const [dateFrom, setDateFrom] = useState(
    initialFilters?.dateFrom || ""
  )
  
  const [dateTo, setDateTo] = useState(
    initialFilters?.dateTo || ""
  )
  
  const [hasReply, setHasReply] = useState<boolean | null>(
    initialFilters?.hasReply !== undefined ? initialFilters.hasReply : null
  )
  
  const [keywords, setKeywords] = useState(
    initialFilters?.keyword || ""
  )
  
  const [sentimentFilter, setSentimentFilter] = useState(
    initialFilters?.sentiment || "all"
  )

  // 重置筛选条件
  const handleReset = () => {
    setStatusFilters({
      published: true,
      pending: true,
      hidden: true,
    })
    setSelectedCoach("all")
    setSelectedCourseType("all")
    setRatingRange([1, 5])
    setDateFrom("")
    setDateTo("")
    setHasReply(null)
    setKeywords("")
    setSentimentFilter("all")
  }

  // 应用筛选条件
  const handleApplyFilter = () => {
    // 构建筛选参数
    const filters: ReviewFilterParams = {}
    
    // 状态筛选
    const statusValues = Object.entries(statusFilters)
      .filter(([_, value]) => value)
      .map(([key]) => key)
    
    if (statusValues.length === 1) {
      filters.status = statusValues[0] as any
    }
    
    // 教练筛选
    if (selectedCoach !== "all") {
      filters.coachId = selectedCoach
    }
    
    // 课程类型筛选
    if (selectedCourseType !== "all") {
      filters.courseType = selectedCourseType
    }
    
    // 评分范围筛选
    if (ratingRange[0] > 1 || ratingRange[1] < 5) {
      // 这里可以根据需要设置评分范围
      if (ratingRange[0] === ratingRange[1] && ratingRange[0] === 5) {
        filters.rating = "5"
      } else if (ratingRange[0] === ratingRange[1] && ratingRange[0] === 4) {
        filters.rating = "4"
      } else if (ratingRange[1] <= 3) {
        filters.rating = "3"
      }
    }
    
    // 日期范围筛选
    if (dateFrom) {
      filters.dateFrom = dateFrom
    }
    
    if (dateTo) {
      filters.dateTo = dateTo
    }
    
    // 回复状态筛选
    if (hasReply !== null) {
      filters.hasReply = hasReply
    }
    
    // 关键词筛选
    if (keywords.trim()) {
      filters.keyword = keywords.trim()
    }
    
    // 情感分析筛选
    if (sentimentFilter !== "all") {
      filters.sentiment = sentimentFilter as any
    }
    
    // 调用回调函数
    if (onApplyFilter) {
      onApplyFilter(filters)
    }
    
    // 关闭对话框
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
          <DialogDescription>设置详细的筛选条件以查找特定评价</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
          <div className="space-y-2">
            <Label>评价状态</Label>
            <div className="grid grid-cols-3 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="status-published" 
                  checked={statusFilters.published}
                  onCheckedChange={(checked) => 
                    setStatusFilters({...statusFilters, published: !!checked})
                  }
                />
                <Label htmlFor="status-published" className="font-normal">
                  已发布
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="status-pending" 
                  checked={statusFilters.pending}
                  onCheckedChange={(checked) => 
                    setStatusFilters({...statusFilters, pending: !!checked})
                  }
                />
                <Label htmlFor="status-pending" className="font-normal">
                  待审核
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="status-hidden" 
                  checked={statusFilters.hidden}
                  onCheckedChange={(checked) => 
                    setStatusFilters({...statusFilters, hidden: !!checked})
                  }
                />
                <Label htmlFor="status-hidden" className="font-normal">
                  已隐藏
                </Label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="coach">教练</Label>
            <Select value={selectedCoach} onValueChange={setSelectedCoach}>
              <SelectTrigger id="coach">
                <SelectValue placeholder="选择教练" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有教练</SelectItem>
                {coaches.map((coach) => (
                  <SelectItem key={coach.id} value={coach.id.toString()}>
                    {coach.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="course-type">课程类型</Label>
            <Select value={selectedCourseType} onValueChange={setSelectedCourseType}>
              <SelectTrigger id="course-type">
                <SelectValue placeholder="选择课程类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                {courseTypes.map((type) => (
                  <SelectItem key={type.id} value={type.name}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>评分范围</Label>
            <div className="px-2">
              <Slider 
                value={ratingRange} 
                max={5} 
                min={1} 
                step={1} 
                onValueChange={(value) => setRatingRange(value as [number, number])} 
              />
              <div className="mt-1 flex justify-between text-xs text-muted-foreground">
                <span>{ratingRange[0]}</span>
                <span>{ratingRange[1]}</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="date-from">评价日期</Label>
            <div className="flex items-center gap-2">
              <Input 
                id="date-from" 
                type="date" 
                className="w-1/2" 
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
              />
              <span>至</span>
              <Input 
                id="date-to" 
                type="date" 
                className="w-1/2" 
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>回复状态</Label>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="has-reply" 
                  checked={hasReply === true}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setHasReply(true)
                    } else {
                      setHasReply(null)
                    }
                  }}
                />
                <Label htmlFor="has-reply" className="font-normal">
                  已回复
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="no-reply" 
                  checked={hasReply === false}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setHasReply(false)
                    } else {
                      setHasReply(null)
                    }
                  }}
                />
                <Label htmlFor="no-reply" className="font-normal">
                  未回复
                </Label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="sentiment">情感分析</Label>
            <Select value={sentimentFilter} onValueChange={setSentimentFilter}>
              <SelectTrigger id="sentiment">
                <SelectValue placeholder="情感分析" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有情感</SelectItem>
                <SelectItem value="positive">正面评价</SelectItem>
                <SelectItem value="neutral">中性评价</SelectItem>
                <SelectItem value="negative">负面评价</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="keywords">关键词</Label>
            <Input 
              id="keywords" 
              placeholder="输入关键词，多个关键词用空格分隔" 
              value={keywords}
              onChange={(e) => setKeywords(e.target.value)}
            />
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
          >
            取消
          </Button>
          <Button 
            variant="outline"
            onClick={handleReset}
          >
            重置
          </Button>
          <Button
            onClick={handleApplyFilter}
          >
            应用筛选
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
