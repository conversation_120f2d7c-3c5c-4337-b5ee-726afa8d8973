// 测试门店API的脚本
// 使用Node.js 18+的内置fetch API

async function testStoresAPI() {
  try {
    console.log('🧪 开始测试门店API...');
    
    const tenantId = 1; // 使用测试租户ID
    
    // 测试1: 获取门店列表
    console.log('\n📤 测试获取门店列表...');
    const getResponse = await fetch(`http://localhost:3001/api/stores?tenantId=${tenantId}`);
    console.log('📥 获取门店列表响应状态:', getResponse.status);
    
    const getResult = await getResponse.json();
    console.log('📥 获取门店列表响应数据:', JSON.stringify(getResult, null, 2));
    
    if (getResult.success) {
      console.log('✅ 获取门店列表成功!');
      console.log(`当前门店数量: ${getResult.data.length}`);
    } else {
      console.log('❌ 获取门店列表失败:', getResult.error);
    }
    
    // 测试2: 创建新门店
    console.log('\n📤 测试创建新门店...');
    const timestamp = Date.now();
    const newStoreData = {
      name: `测试门店${timestamp}`,
      address: `北京市朝阳区测试路${timestamp}号`,
      phone: `138${timestamp.toString().slice(-8)}`,
      managerName: `测试店长${timestamp}`,
      description: '这是一个测试门店',
      area: '300',
      type: 'standard',
      tenantId: tenantId
    };
    
    console.log('创建门店数据:', JSON.stringify(newStoreData, null, 2));
    
    const createResponse = await fetch('http://localhost:3001/api/stores', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newStoreData)
    });
    
    console.log('📥 创建门店响应状态:', createResponse.status);
    
    const createResult = await createResponse.json();
    console.log('📥 创建门店响应数据:', JSON.stringify(createResult, null, 2));
    
    let createdStoreId = null;
    if (createResult.success) {
      console.log('✅ 创建门店成功!');
      console.log(`门店ID: ${createResult.data.id}`);
      createdStoreId = createResult.data.id;
    } else {
      console.log('❌ 创建门店失败:', createResult.error);
    }
    
    // 测试3: 再次获取门店列表，验证新门店是否存在
    if (createdStoreId) {
      console.log('\n📤 验证新门店是否在列表中...');
      const verifyResponse = await fetch(`http://localhost:3001/api/stores?tenantId=${tenantId}`);
      const verifyResult = await verifyResponse.json();
      
      if (verifyResult.success) {
        const foundStore = verifyResult.data.find(store => store.id === createdStoreId);
        if (foundStore) {
          console.log('✅ 新门店已成功添加到列表中!');
          console.log(`门店名称: ${foundStore.name}`);
        } else {
          console.log('❌ 新门店未在列表中找到');
        }
      }
    }
    
    // 测试4: 删除门店
    if (createdStoreId) {
      console.log('\n📤 测试删除门店...');
      const deleteResponse = await fetch(`http://localhost:3001/api/stores/${createdStoreId}`, {
        method: 'DELETE',
      });
      
      console.log('📥 删除门店响应状态:', deleteResponse.status);
      
      const deleteResult = await deleteResponse.json();
      console.log('📥 删除门店响应数据:', JSON.stringify(deleteResult, null, 2));
      
      if (deleteResult.success) {
        console.log('✅ 删除门店成功!');
      } else {
        console.log('❌ 删除门店失败:', deleteResult.error);
      }
    }
    
    // 测试5: 验证删除后门店不在列表中
    if (createdStoreId) {
      console.log('\n📤 验证门店是否已删除...');
      const finalResponse = await fetch(`http://localhost:3001/api/stores?tenantId=${tenantId}`);
      const finalResult = await finalResponse.json();
      
      if (finalResult.success) {
        const foundStore = finalResult.data.find(store => store.id === createdStoreId);
        if (!foundStore) {
          console.log('✅ 门店已成功删除!');
        } else {
          console.log('❌ 门店删除失败，仍在列表中');
        }
      }
    }
    
    console.log('\n🎉 门店API测试完成!');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testStoresAPI();
