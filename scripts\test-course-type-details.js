// 测试课程类型详情API功能
async function testCourseTypeDetails() {
  console.log('开始测试课程类型详情API功能...');
  
  try {
    // 1. 首先获取课程类型列表
    console.log('\n1. 获取课程类型列表:');
    const listResponse = await fetch('http://localhost:3005/api/course-types?tenantId=1&sortBy=display_order');
    const listResult = await listResponse.json();
    
    if (listResult.code !== 200) {
      throw new Error(`获取课程类型列表失败: ${listResult.msg}`);
    }
    
    const courseTypes = listResult.data.list;
    console.log(`✓ 获取到 ${courseTypes.length} 个课程类型`);
    
    if (courseTypes.length === 0) {
      console.log('没有课程类型数据，无法测试详情功能');
      return;
    }
    
    // 2. 测试前几个课程类型的详情API
    console.log('\n2. 测试课程类型详情API:');
    const testTypes = courseTypes.slice(0, 5); // 测试前5个
    
    for (const type of testTypes) {
      console.log(`\n测试课程类型: ${type.name} (ID: ${type.id})`);
      
      const detailResponse = await fetch(`http://localhost:3005/api/course-types/${type.id}/stats`);
      const detailResult = await detailResponse.json();
      
      if (detailResult.code === 200) {
        const data = detailResult.data;
        console.log(`  ✓ 基本信息: ${data.courseType.name}`);
        console.log(`  ✓ 关联课程: ${data.courses.length} 个`);
        console.log(`  ✓ 总预约次数: ${data.stats.totalBookings}`);
        console.log(`  ✓ 平均出勤率: ${data.stats.averageAttendance}%`);
        console.log(`  ✓ 热门时段: ${data.stats.popularTimeSlots.length} 个`);
        console.log(`  ✓ 月度趋势: ${data.stats.monthlyTrend.length} 个月`);
        
        // 显示关联课程信息
        if (data.courses.length > 0) {
          console.log(`  关联课程详情:`);
          data.courses.forEach((course, index) => {
            console.log(`    ${index + 1}. ${course.name} - ${course.coach} - ¥${course.price}`);
          });
        }
        
        // 显示热门时段
        if (data.stats.popularTimeSlots.length > 0) {
          console.log(`  热门时段:`);
          data.stats.popularTimeSlots.forEach((slot, index) => {
            console.log(`    ${index + 1}. ${slot.day} ${slot.time} - ${slot.count}次`);
          });
        }
      } else {
        console.log(`  ✗ 获取详情失败: ${detailResult.msg}`);
      }
    }
    
    // 3. 测试不存在的课程类型ID
    console.log('\n3. 测试不存在的课程类型ID:');
    const invalidResponse = await fetch('http://localhost:3005/api/course-types/99999/stats');
    const invalidResult = await invalidResponse.json();
    
    if (invalidResult.code === 404) {
      console.log('✓ 正确处理了不存在的课程类型ID');
    } else {
      console.log(`✗ 未正确处理不存在的ID: ${invalidResult.msg}`);
    }
    
    // 4. 测试无效的课程类型ID
    console.log('\n4. 测试无效的课程类型ID:');
    const invalidIdResponse = await fetch('http://localhost:3005/api/course-types/invalid/stats');
    const invalidIdResult = await invalidIdResponse.json();
    
    if (invalidIdResult.code === 400) {
      console.log('✓ 正确处理了无效的课程类型ID');
    } else {
      console.log(`✗ 未正确处理无效的ID: ${invalidIdResult.msg}`);
    }
    
    console.log('\n✓ 课程类型详情API功能测试完成!');
    console.log('\n新的详情功能特性:');
    console.log('  ✅ 获取课程类型基本信息');
    console.log('  ✅ 获取关联课程列表（包含教练、时间、场地、价格等）');
    console.log('  ✅ 生成使用统计数据（预约次数、出勤率等）');
    console.log('  ✅ 提供热门时段分析');
    console.log('  ✅ 提供月度趋势数据');
    console.log('  ✅ 错误处理（不存在的ID、无效的ID）');
    
    console.log('\n请在浏览器中访问课程类型页面，点击"查看详情"按钮测试新的详情对话框！');
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testCourseTypeDetails();
