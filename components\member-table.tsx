"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { toast } from "@/components/ui/use-toast"
import { MoreHorizontal, Pencil, CreditCard, User, Tag, Calendar, History, MessageSquare, Trash2, Coins, Eye, Edit, Trash, Receipt, Tags, Plus } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { MemberDetailDialog } from "@/components/members/member-detail-dialog"
import { PointsManagementDialog } from "@/components/members/points-management-dialog"
import { BatchTagsDialog } from "@/components/members/batch-tags-dialog"
import { BatchExportDialog } from "@/components/members/batch-export-dialog"
import { BookingRecordsDialog } from "@/components/members/booking-records-dialog"
import { TransactionRecordsDialog } from "@/components/members/transaction-records-dialog"
import { EditMemberDialog } from "@/components/members/edit-member-dialog"
import { ManageTagsDialog } from "@/components/members/manage-tags-dialog"
import { SendMessageDialog } from "@/components/members/send-message-dialog"
import { AddPointsRecordDialog } from "@/components/members/add-points-record-dialog"

const members = [
  {
    id: "M001",
    name: "张三",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "138****1234",
    level: "金卡会员",
    card: "次卡",
    remaining: "15次",
    expiry: "2025-12-31",
    status: "active",
    tags: ["新会员", "瑜伽爱好者"],
    lastVisit: "2023-03-25",
    totalSpent: "¥3,600",
    source: "官网注册",
    birthday: "1990-05-15",
    consultant: "王教练",
    points: 1250,
    pointsExpiry: "2025-12-31",
  },
  {
    id: "M002",
    name: "李四",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "139****5678",
    level: "银卡会员",
    card: "月卡",
    remaining: "无限次",
    expiry: "2025-04-30",
    status: "active",
    tags: ["老会员"],
    lastVisit: "2023-03-28",
    totalSpent: "¥2,400",
    source: "朋友推荐",
    birthday: "1985-08-22",
    consultant: "张顾问",
    points: 850,
    pointsExpiry: "2025-04-30",
  },
  {
    id: "M003",
    name: "王五",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "137****9012",
    level: "白金会员",
    card: "年卡",
    remaining: "无限次",
    expiry: "2026-03-15",
    status: "active",
    tags: ["VIP", "教练推荐"],
    lastVisit: "2023-03-27",
    totalSpent: "¥8,800",
    source: "微信小程序",
    birthday: "1982-11-03",
    consultant: "李顾问",
    points: 3680,
    pointsExpiry: "2026-03-15",
  },
  {
    id: "M004",
    name: "赵六",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "136****3456",
    level: "标准会员",
    card: "次卡",
    remaining: "3次",
    expiry: "2025-05-20",
    status: "active",
    tags: ["潜在流失"],
    lastVisit: "2023-02-15",
    totalSpent: "¥1,200",
    source: "线下门店",
    birthday: "1995-03-18",
    consultant: "刘顾问",
    points: 320,
    pointsExpiry: "2025-05-20",
  },
  {
    id: "M005",
    name: "钱七",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "135****7890",
    level: "银卡会员",
    card: "季卡",
    remaining: "无限次",
    expiry: "2025-06-30",
    status: "inactive",
    tags: ["已流失"],
    lastVisit: "2023-01-10",
    totalSpent: "¥3,200",
    source: "活动推广",
    birthday: "1988-12-25",
    consultant: "赵顾问",
    points: 980,
    pointsExpiry: "2025-06-30",
  },
]

export function MemberTable() {
  const [selectedMembers, setSelectedMembers] = useState<string[]>([])
  const [pointsMember, setPointsMember] = useState<(typeof members)[0] | null>(null)
  const [tagsMember, setTagsMember] = useState<(typeof members)[0] | null>(null)
  const [messageMember, setMessageMember] = useState<(typeof members)[0] | null>(null)
  const [editMember, setEditMember] = useState<(typeof members)[0] | null>(null)
  const [bookingMember, setBookingMember] = useState<(typeof members)[0] | null>(null)
  const [transactionMember, setTransactionMember] = useState<(typeof members)[0] | null>(null)
  const [showBatchTagsDialog, setShowBatchTagsDialog] = useState(false)
  const [showBatchExportDialog, setShowBatchExportDialog] = useState(false)
  const [detailMember, setDetailMember] = useState<(typeof members)[0] | null>(null)
  const [addPointsMember, setAddPointsMember] = useState<(typeof members)[0] | null>(null)

  const toggleSelectAll = () => {
    if (selectedMembers.length === members.length) {
      setSelectedMembers([])
    } else {
      setSelectedMembers(members.map((member) => member.id))
    }
  }

  const toggleSelectMember = (id: string) => {
    if (selectedMembers.includes(id)) {
      setSelectedMembers(selectedMembers.filter((memberId) => memberId !== id))
    } else {
      setSelectedMembers([...selectedMembers, id])
    }
  }

  const openMemberDetail = (member: (typeof members)[0]) => {
    setDetailMember(member)
  }

  // 计算是否处于"不确定"状态（部分选中）
  const isIndeterminate = selectedMembers.length > 0 && selectedMembers.length < members.length

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedMembers.length === members.length && members.length > 0}
                  // 修复：使用条件渲染来处理 indeterminate 属性
                  {...(isIndeterminate ? { indeterminate: true } : {})}
                  onCheckedChange={toggleSelectAll}
                  aria-label="选择所有会员"
                />
              </TableHead>
              <TableHead>会员ID</TableHead>
              <TableHead>会员姓名</TableHead>
              <TableHead>手机号</TableHead>
              <TableHead>会员等级</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>标签</TableHead>
              <TableHead>渠道来源</TableHead>
              <TableHead>生日</TableHead>
              <TableHead>会籍顾问</TableHead>
              <TableHead>积分</TableHead>
              <TableHead>积分过期</TableHead>
              <TableHead>最近到访</TableHead>
              <TableHead>累计消费</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {members.map((member) => (
              <TableRow
                key={member.id}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => openMemberDetail(member)}
              >
                <TableCell className="w-12" onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    checked={selectedMembers.includes(member.id)}
                    onCheckedChange={() => toggleSelectMember(member.id)}
                    aria-label={`选择会员 ${member.name}`}
                  />
                </TableCell>
                <TableCell className="font-medium">{member.id}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback>{member.name[0]}</AvatarFallback>
                    </Avatar>
                    <span>{member.name}</span>
                  </div>
                </TableCell>
                <TableCell>{member.phone}</TableCell>
                <TableCell>
                  <Badge
                    className={`${
                      member.level.includes("白金")
                        ? "bg-purple-500 hover:bg-purple-600"
                        : member.level.includes("金卡")
                          ? "bg-yellow-500 hover:bg-yellow-600 text-black"
                          : member.level.includes("银卡")
                            ? "bg-gray-400 hover:bg-gray-500"
                            : "bg-blue-500 hover:bg-blue-600"
                    }`}
                  >
                    {member.level}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={member.status === "active" ? "default" : "secondary"}>
                    {member.status === "active" ? "活跃" : "不活跃"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {member.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </TableCell>
                <TableCell>{member.source}</TableCell>
                <TableCell>{member.birthday}</TableCell>
                <TableCell>{member.consultant}</TableCell>
                <TableCell>
                  <Badge variant="outline" className="bg-amber-50 hover:bg-amber-100">
                    {member.points}
                  </Badge>
                </TableCell>
                <TableCell>{member.pointsExpiry}</TableCell>
                <TableCell>{member.lastVisit}</TableCell>
                <TableCell>{member.totalSpent}</TableCell>
                <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>操作</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => openMemberDetail(member)}>
                        <User className="mr-2 h-4 w-4" />
                        会员详情
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => window.location.href = `/members/cards/list?memberId=${member.id}`}>
                        <CreditCard className="mr-2 h-4 w-4" />
                        查看会员卡
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => console.log("查看详情", member.id)}>
                        <Eye className="mr-2 h-4 w-4" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setTagsMember(member)}>
                        <Tags className="mr-2 h-4 w-4" />
                        管理标签
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setBookingMember(member)}>
                        <Calendar className="mr-2 h-4 w-4" />
                        预约记录
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setTransactionMember(member)}>
                        <Receipt className="mr-2 h-4 w-4" />
                        消费记录
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setMessageMember(member)}>
                        <MessageSquare className="mr-2 h-4 w-4" />
                        发送消息
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setPointsMember(member)}>
                        <Coins className="mr-2 h-4 w-4" />
                        积分管理
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setAddPointsMember(member)}>
                        <Plus className="mr-2 h-4 w-4" />
                        增加积分记录
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => setEditMember(member)}>
                        <Edit className="mr-2 h-4 w-4" />
                        编辑会员
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => {
                          if (window.confirm(`确定要删除会员 ${member.name} 吗？`)) {
                            console.log("删除会员", member.id)
                            toast({
                              title: "会员已删除",
                              description: `${member.name} 已从系统中移除`,
                            })
                          }
                        }}
                        className="text-red-600"
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        删除会员
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {selectedMembers.length > 0 && (
        <div className="mt-2 flex items-center gap-2">
          <span className="text-sm text-muted-foreground">已选择 {selectedMembers.length} 个会员</span>
          <Button variant="outline" size="sm" onClick={() => setShowBatchExportDialog(true)}>
            批量导出
          </Button>
          <Button variant="outline" size="sm" onClick={() => setShowBatchTagsDialog(true)}>
            批量标签
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => {
              if (window.confirm(`确定要删除选中的 ${selectedMembers.length} 个会员吗？此操作无法撤销。`)) {
                toast({
                  title: "批量删除成功",
                  description: `已成功删除 ${selectedMembers.length} 个会员`,
                })
                setSelectedMembers([])
              }
            }}
          >
            批量删除
          </Button>
        </div>
      )}

      {detailMember && (
        <MemberDetailDialog
          member={detailMember}
          open={!!detailMember}
          onOpenChange={(open) => !open && setDetailMember(null)}
        />
      )}

      {/* 会员详情对话框 */}
      {detailMember && (
        <MemberDetailDialog
          member={detailMember}
          open={!!detailMember}
          onOpenChange={(open) => !open && setDetailMember(null)}
        />
      )}

      {/* 积分管理对话框 */}
      {pointsMember && (
        <PointsManagementDialog
          member={pointsMember}
          open={!!pointsMember}
          onOpenChange={(open) => !open && setPointsMember(null)}
        />
      )}

      {/* 标签管理对话框 */}
      {tagsMember && (
        <ManageTagsDialog
          member={tagsMember}
          open={!!tagsMember}
          onOpenChange={(open) => !open && setTagsMember(null)}
        />
      )}

      {/* 发送消息对话框 */}
      {messageMember && (
        <SendMessageDialog
          member={messageMember}
          open={!!messageMember}
          onOpenChange={(open) => !open && setMessageMember(null)}
        />
      )}

      {/* 编辑会员对话框 */}
      {editMember && (
        <EditMemberDialog
          member={editMember}
          open={!!editMember}
          onOpenChange={(open) => !open && setEditMember(null)}
          onSave={(updatedMember) => {
            toast({
              title: "会员信息已更新",
              description: `${updatedMember.name} 的信息已成功保存`,
            })
          }}
        />
      )}

      {/* 预约记录对话框 */}
      {bookingMember && (
        <BookingRecordsDialog
          member={bookingMember}
          open={!!bookingMember}
          onOpenChange={(open) => !open && setBookingMember(null)}
        />
      )}

      {/* 消费记录对话框 */}
      {transactionMember && (
        <TransactionRecordsDialog
          member={transactionMember}
          open={!!transactionMember}
          onOpenChange={(open) => !open && setTransactionMember(null)}
        />
      )}

      {/* 批量标签对话框 */}
      <BatchTagsDialog
        open={showBatchTagsDialog}
        onOpenChange={setShowBatchTagsDialog}
        selectedMemberIds={selectedMembers}
        memberCount={selectedMembers.length}
      />

      {/* 批量导出对话框 */}
      <BatchExportDialog
        open={showBatchExportDialog}
        onOpenChange={setShowBatchExportDialog}
        selectedMemberIds={selectedMembers}
        memberCount={selectedMembers.length}
      />

      {/* 增加积分记录对话框 */}
      {addPointsMember && (
        <AddPointsRecordDialog
          member={addPointsMember}
          open={!!addPointsMember}
          onOpenChange={(open) => !open && setAddPointsMember(null)}
          onPointsAdded={() => {
            toast({
              title: "积分记录已添加",
              description: "会员积分记录已成功添加",
            })
          }}
        />
      )}
    </>
  )
}

