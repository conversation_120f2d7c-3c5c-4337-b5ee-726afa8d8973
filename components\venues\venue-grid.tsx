"use client"

import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Eye, Pencil, Users, SquareIcon as SquareFeet, Clock } from "lucide-react"
import { useState } from "react"
import { VenueDetailDialog } from "@/components/venues/venue-detail-dialog"

const venues = [
  {
    id: "V001",
    name: "1号瑜伽室",
    capacity: 15,
    equipment: "瑜伽垫、瑜伽砖、瑜伽带",
    courses: 25,
    utilization: "85%",
    status: "available",
    area: 80,
    location: "一楼东侧",
    openTime: "06:00",
    closeTime: "22:00",
    maintenanceDate: "",
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: "V002",
    name: "2号瑜伽室",
    capacity: 10,
    equipment: "瑜伽垫、瑜伽砖、瑜伽带、瑜伽球",
    courses: 18,
    utilization: "78%",
    status: "available",
    area: 60,
    location: "一楼西侧",
    openTime: "06:00",
    closeTime: "22:00",
    maintenanceDate: "",
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: "V003",
    name: "3号瑜伽室",
    capacity: 15,
    equipment: "瑜伽垫、瑜伽砖、瑜伽带、瑜伽轮",
    courses: 22,
    utilization: "82%",
    status: "maintenance",
    area: 80,
    location: "二楼东侧",
    openTime: "06:00",
    closeTime: "22:00",
    maintenanceDate: "2025-04-15",
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: "V004",
    name: "4号瑜伽室",
    capacity: 8,
    equipment: "空中瑜伽吊床、瑜伽垫",
    courses: 12,
    utilization: "75%",
    status: "available",
    area: 50,
    location: "二楼西侧",
    openTime: "08:00",
    closeTime: "20:00",
    maintenanceDate: "",
    image: "/placeholder.svg?height=200&width=300",
  },
  {
    id: "V005",
    name: "私教室",
    capacity: 2,
    equipment: "全套瑜伽装备",
    courses: 30,
    utilization: "90%",
    status: "booked",
    area: 25,
    location: "三楼",
    openTime: "08:00",
    closeTime: "20:00",
    maintenanceDate: "",
    image: "/placeholder.svg?height=200&width=300",
  },
]

interface VenueGridProps {
  searchQuery?: string
}

export function VenueGrid({ searchQuery = "" }: VenueGridProps) {
  const [selectedVenue, setSelectedVenue] = useState<(typeof venues)[0] | null>(null)
  const [isDetailOpen, setIsDetailOpen] = useState(false)

  const filteredVenues = venues.filter(
    (venue) =>
      venue.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      venue.id.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleViewDetail = (venue: (typeof venues)[0]) => {
    setSelectedVenue(venue)
    setIsDetailOpen(true)
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredVenues.length > 0 ? (
          filteredVenues.map((venue) => (
            <Card key={venue.id} className="overflow-hidden">
              <div className="relative">
                <img src={venue.image || "/placeholder.svg"} alt={venue.name} className="w-full h-48 object-cover" />
                <Badge
                  className="absolute top-2 right-2"
                  variant={
                    venue.status === "available"
                      ? "default"
                      : venue.status === "maintenance"
                        ? "destructive"
                        : "secondary"
                  }
                >
                  {venue.status === "available" ? "可用" : venue.status === "maintenance" ? "维护中" : "已预订"}
                </Badge>
              </div>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">{venue.name}</h3>
                  <span className="text-sm text-muted-foreground">{venue.id}</span>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center text-sm">
                  <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>容纳人数: {venue.capacity}人</span>
                </div>
                <div className="flex items-center text-sm">
                  <SquareFeet className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>面积: {venue.area}㎡</span>
                </div>
                <div className="flex items-center text-sm">
                  <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>
                    开放时间: {venue.openTime}-{venue.closeTime}
                  </span>
                </div>
                <div className="flex items-center text-sm">
                  <span className="font-medium">使用率: </span>
                  <div className="w-full bg-gray-200 rounded-full h-2.5 ml-2">
                    <div className="bg-primary h-2.5 rounded-full" style={{ width: venue.utilization }}></div>
                  </div>
                  <span className="ml-2">{venue.utilization}</span>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" onClick={() => handleViewDetail(venue)}>
                  <Eye className="mr-2 h-4 w-4" />
                  详情
                </Button>
                <Button variant="outline" size="sm">
                  <Calendar className="mr-2 h-4 w-4" />
                  排期
                </Button>
                <Button variant="outline" size="sm">
                  <Pencil className="mr-2 h-4 w-4" />
                  编辑
                </Button>
              </CardFooter>
            </Card>
          ))
        ) : (
          <div className="col-span-full text-center py-10">没有找到匹配的场地</div>
        )}
      </div>

      {selectedVenue && <VenueDetailDialog venue={selectedVenue} open={isDetailOpen} onOpenChange={setIsDetailOpen} />}
    </>
  )
}

