"use client"

import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { CalendarIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { CalendarZh } from "@/components/ui/calendar-zh"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { DateRange } from "react-day-picker"

interface DatePickerZhProps {
  placeholder?: string
  selected?: Date | null | DateRange
  onSelect: (date: Date | null | DateRange) => void
  className?: string
  mode?: "single" | "range"
}

export function DatePickerZh({
  placeholder = "选择日期",
  selected,
  onSelect,
  className,
  mode = "single"
}: DatePickerZhProps) {

  // 处理日期显示
  const formatDate = () => {
    if (!selected) return <span>{placeholder}</span>

    if (mode === "single" && selected instanceof Date) {
      return format(selected, "yyyy年MM月dd日", { locale: zhCN })
    }

    if (mode === "range" && typeof selected === "object" && selected !== null && !Array.isArray(selected)) {
      const { from, to } = selected as DateRange
      if (from && to) {
        return `${format(from, "yyyy年MM月dd日", { locale: zhCN })} 至 ${format(to, "yyyy年MM月dd日", { locale: zhCN })}`
      } else if (from) {
        return `${format(from, "yyyy年MM月dd日", { locale: zhCN })} 至 `
      }
    }

    return <span>{placeholder}</span>
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn("w-full justify-start text-left font-normal",
            (!selected || (mode === "range" && typeof selected === "object" && !selected.from)) && "text-muted-foreground",
            className
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {formatDate()}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        {mode === "single" ? (
          <CalendarZh
            mode="single"
            selected={selected instanceof Date ? selected : undefined}
            onSelect={onSelect as (date: Date | undefined) => void}
          />
        ) : (
          <CalendarZh
            mode="range"
            selected={selected as DateRange}
            onSelect={onSelect as (date: DateRange) => void}
            numberOfMonths={2}
          />
        )}
      </PopoverContent>
    </Popover>
  )
}
