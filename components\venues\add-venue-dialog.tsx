"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>it<PERSON>, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useState } from "react"
import { TimePicker } from "@/components/venues/time-picker"

interface AddVenueDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddVenueDialog({ open, onOpenChange }: AddVenueDialogProps) {
  const [openTime, setOpenTime] = useState("06:00")
  const [closeTime, setCloseTime] = useState("22:00")

  return (
    <Dialog open={open} onO<PERSON>Change={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>添加场地</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="equipment">设备配置</TabsTrigger>
            <TabsTrigger value="settings">高级设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">场地名称</Label>
                <Input id="name" placeholder="输入场地名称" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">场地位置</Label>
                <Input id="location" placeholder="例如：一楼东侧" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="capacity">容纳人数</Label>
                <Input id="capacity" type="number" min="1" placeholder="输入最大容纳人数" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="area">面积（㎡）</Label>
                <Input id="area" type="number" min="1" placeholder="输入场地面积" />
              </div>
              <div className="space-y-2">
                <Label>开放时间</Label>
                <TimePicker value={openTime} onChange={setOpenTime} />
              </div>
              <div className="space-y-2">
                <Label>关闭时间</Label>
                <TimePicker value={closeTime} onChange={setCloseTime} />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">场地描述</Label>
              <Textarea id="description" placeholder="输入场地描述信息" rows={3} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="image">场地图片</Label>
              <Input id="image" type="file" accept="image/*" />
            </div>
          </TabsContent>

          <TabsContent value="equipment" className="space-y-4">
            <div className="space-y-2">
              <Label>设备列表</Label>
              <div className="border rounded-md p-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="equipment-1">瑜伽垫</Label>
                    <Input id="equipment-1" type="number" min="0" placeholder="数量" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="equipment-2">瑜伽砖</Label>
                    <Input id="equipment-2" type="number" min="0" placeholder="数量" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="equipment-3">瑜伽带</Label>
                    <Input id="equipment-3" type="number" min="0" placeholder="数量" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="equipment-4">瑜伽球</Label>
                    <Input id="equipment-4" type="number" min="0" placeholder="数量" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="equipment-5">瑜伽轮</Label>
                    <Input id="equipment-5" type="number" min="0" placeholder="数量" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="equipment-6">空中瑜伽吊床</Label>
                    <Input id="equipment-6" type="number" min="0" placeholder="数量" />
                  </div>
                </div>
                <Button variant="outline" size="sm" className="mt-2">
                  添加其他设备
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="equipment-notes">设备备注</Label>
              <Textarea id="equipment-notes" placeholder="输入设备相关备注信息" rows={3} />
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">场地状态</Label>
                <Select defaultValue="available">
                  <SelectTrigger id="status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="available">可用</SelectItem>
                    <SelectItem value="maintenance">维护中</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="maintenance-date">维护截止日期</Label>
                <Input id="maintenance-date" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="booking-lead-time">预订提前时间（小时）</Label>
                <Input id="booking-lead-time" type="number" min="0" defaultValue="24" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cancellation-policy">取消政策（小时）</Label>
                <Input id="cancellation-policy" type="number" min="0" defaultValue="12" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="venue-rules">场地使用规则</Label>
              <Textarea id="venue-rules" placeholder="输入场地使用规则" rows={3} />
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={() => onOpenChange(false)}>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

