"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon } from "lucide-react"

const formSchema = z.object({
  // 预约时间设置
  advanceBookingDays: z.coerce.number().min(0).max(365),
  bookingDeadlineMinutes: z.coerce.number().min(0).max(1440),
  allowLateBooking: z.boolean(),
  lateBookingMinutes: z.coerce.number().min(0).max(120).optional(),
  
  // 预约数量限制
  maxBookingsPerDay: z.coerce.number().min(0).max(100),
  maxBookingsPerWeek: z.coerce.number().min(0).max(500),
  maxFutureBookings: z.coerce.number().min(0).max(100),
  
  // 预约方式
  allowSelfBooking: z.boolean(),
  allowStaffBooking: z.boolean(),
  allowBatchBooking: z.boolean(),
  allowAutoBooking: z.boolean(),
  
  // 预约优先级
  priorityMethod: z.enum(["level", "card", "tag", "attendance", "none"]),
  
  // 预约条件限制
  checkCardStatus: z.boolean(),
  checkMemberStatus: z.boolean(),
  checkCourseMatch: z.boolean(),
  checkConflict: z.boolean(),
  
  // 覆盖全局设置
  overrideGlobal: z.boolean(),
})

interface CourseBookingRulesProps {
  courseTypeId: string;
  onChange: () => void;
}

export function CourseBookingRules({ courseTypeId, onChange }: CourseBookingRulesProps) {
  // 根据课程类型获取不同的默认值
  const getDefaultValues = () => {
    // 这里可以根据courseTypeId返回不同的默认值
    // 例如私教课和团体课的预约规则可能不同
    if (courseTypeId === "private") {
      return {
        advanceBookingDays: 14,
        bookingDeadlineMinutes: 120,
        allowLateBooking: false,
        lateBookingMinutes: 0,
        maxBookingsPerDay: 2,
        maxBookingsPerWeek: 5,
        maxFutureBookings: 10,
        allowSelfBooking: true,
        allowStaffBooking: true,
        allowBatchBooking: false,
        allowAutoBooking: false,
        priorityMethod: "level" as const,
        checkCardStatus: true,
        checkMemberStatus: true,
        checkCourseMatch: true,
        checkConflict: true,
        overrideGlobal: true,
      }
    }
    
    return {
      advanceBookingDays: 7,
      bookingDeadlineMinutes: 60,
      allowLateBooking: true,
      lateBookingMinutes: 10,
      maxBookingsPerDay: 3,
      maxBookingsPerWeek: 10,
      maxFutureBookings: 20,
      allowSelfBooking: true,
      allowStaffBooking: true,
      allowBatchBooking: true,
      allowAutoBooking: false,
      priorityMethod: "time" as const,
      checkCardStatus: true,
      checkMemberStatus: true,
      checkCourseMatch: true,
      checkConflict: true,
      overrideGlobal: false,
    }
  }
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
  })
  
  const watchAllowLateBooking = form.watch("allowLateBooking")
  const watchOverrideGlobal = form.watch("overrideGlobal")
  
  return (
    <Form {...form}>
      <form onChange={onChange} className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>预约规则设置</CardTitle>
                <CardDescription>
                  设置课程预约的时间限制、数量限制和预约方式等
                </CardDescription>
              </div>
              <FormField
                control={form.control}
                name="overrideGlobal"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      覆盖全局设置
                    </FormLabel>
                  </FormItem>
                )}
              />
            </div>
          </CardHeader>
          <CardContent>
            {!watchOverrideGlobal && (
              <Alert className="mb-6">
                <InfoIcon className="h-4 w-4" />
                <AlertTitle>使用全局设置</AlertTitle>
                <AlertDescription>
                  当前使用全局预约规则设置。启用"覆盖全局设置"开关可自定义此课程类型的预约规则。
                </AlertDescription>
              </Alert>
            )}
            
            {watchOverrideGlobal && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">预约时间设置</h3>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="advanceBookingDays"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>提前预约天数</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">天</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            会员最多可提前多少天预约课程
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="bookingDeadlineMinutes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>预约截止时间</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">分钟</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            课程开始前多少分钟停止预约
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="allowLateBooking"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">允许迟到预约</FormLabel>
                          <FormDescription>
                            允许会员在课程开始后的一段时间内仍可预约
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {watchAllowLateBooking && (
                    <FormField
                      control={form.control}
                      name="lateBookingMinutes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>迟到预约时间限制</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">分钟</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            课程开始后多少分钟内仍可预约
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">预约数量限制</h3>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="maxBookingsPerDay"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>每日最大预约数</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">节课</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            会员每天最多可预约的课程数量
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="maxBookingsPerWeek"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>每周最大预约数</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">节课</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            会员每周最多可预约的课程数量
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="maxFutureBookings"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>未来最大预约数</FormLabel>
                          <FormControl>
                            <div className="flex items-center">
                              <Input type="number" {...field} className="w-24" />
                              <span className="ml-2">节课</span>
                            </div>
                          </FormControl>
                          <FormDescription>
                            会员同时最多可预约的未来课程数量
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">预约方式</h3>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="allowSelfBooking"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许自主预约
                            </FormLabel>
                            <FormDescription>
                              会员可通过小程序/APP自行预约
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="allowStaffBooking"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许代操作预约
                            </FormLabel>
                            <FormDescription>
                              前台/教练可代会员预约
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="allowBatchBooking"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许批量预约
                            </FormLabel>
                            <FormDescription>
                              管理员可批量为多名会员预约
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="allowAutoBooking"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              允许自动预约
                            </FormLabel>
                            <FormDescription>
                              系统可根据规则自动为会员预约
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">预约优先级</h3>
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="priorityMethod"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>优先级方式</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex flex-col space-y-1"
                          >
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="level" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                基于会员等级的优先级
                              </FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="card" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                基于会员卡类型的优先级
                              </FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="tag" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                基于会员标签的优先级
                              </FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="attendance" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                基于历史出勤率的优先级
                              </FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="none" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                不设置优先级（先到先得）
                              </FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">预约条件限制</h3>
                  <Separator />
                  
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="checkCardStatus"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              会员卡状态检查
                            </FormLabel>
                            <FormDescription>
                              检查会员卡是否在有效期内、余额/次数是否充足
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="checkMemberStatus"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              会员状态检查
                            </FormLabel>
                            <FormDescription>
                              检查会员是否有欠费、违规记录等
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="checkCourseMatch"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              课程匹配检查
                            </FormLabel>
                            <FormDescription>
                              检查会员卡是否适用于该课程
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="checkConflict"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                          <FormControl>
                            <Checkbox
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              冲突检查
                            </FormLabel>
                            <FormDescription>
                              检查是否与已预约的课程时间冲突
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </form>
    </Form>
  )
}
