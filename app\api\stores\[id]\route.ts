import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { parseFromDatabase } from '@/lib/timezone';

// DELETE - 删除门店
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const storeId = parseInt(id);
    
    if (isNaN(storeId)) {
      return NextResponse.json({
        success: false,
        error: '无效的门店ID'
      }, { status: 400 });
    }

    console.log('删除门店，ID:', storeId);

    // 检查门店是否存在
    const existingStore = await prisma.store.findUnique({
      where: { id: storeId }
    });

    if (!existingStore) {
      return NextResponse.json({
        success: false,
        error: '门店不存在'
      }, { status: 404 });
    }

    // 删除门店
    await prisma.store.delete({
      where: { id: storeId }
    });

    console.log('门店删除成功:', storeId);

    return NextResponse.json({
      success: true,
      message: '门店删除成功'
    });

  } catch (error: any) {
    console.error('删除门店失败:', error);
    return NextResponse.json({
      success: false,
      error: `删除门店失败: ${error.message}`
    }, { status: 500 });
  }
}

// PUT - 更新门店
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const storeId = parseInt(id);
    
    if (isNaN(storeId)) {
      return NextResponse.json({
        success: false,
        error: '无效的门店ID'
      }, { status: 400 });
    }

    const body = await request.json();
    console.log('更新门店请求数据:', body);

    const {
      name,
      address,
      phone,
      managerName,
      description,
      area,
      type
    } = body;

    // 验证必填字段
    if (!name || !address || !phone || !managerName) {
      return NextResponse.json({
        success: false,
        error: '请填写所有必填字段'
      }, { status: 400 });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return NextResponse.json({
        success: false,
        error: '请输入有效的手机号码'
      }, { status: 400 });
    }

    // 检查门店是否存在
    const existingStore = await prisma.store.findUnique({
      where: { id: storeId }
    });

    if (!existingStore) {
      return NextResponse.json({
        success: false,
        error: '门店不存在'
      }, { status: 404 });
    }

    // 更新门店
    const updatedStore = await prisma.store.update({
      where: { id: storeId },
      data: {
        store_name: name,
        contact_person: managerName,
        phone,
        address,
      }
    });

    console.log('门店更新成功:', updatedStore.id);

    // 返回格式化的门店数据
    const formattedStore = {
      id: updatedStore.id,
      name: updatedStore.store_name,
      address: updatedStore.address,
      phone: updatedStore.phone,
      managerName: updatedStore.contact_person,
      employeesCount: 1,
      status: updatedStore.status === 1 ? 'active' : updatedStore.status === 0 ? 'pending' : 'inactive',
      createdAt: parseFromDatabase(updatedStore.created_at, 'date'),
      description: description || '门店描述待完善',
      area: area || '200',
      type: type || 'standard',
      courseCount: 0,
      memberCount: 0,
      revenue: 0,
      rating: 5.0
    };

    return NextResponse.json({
      success: true,
      data: formattedStore,
      message: '门店更新成功'
    });

  } catch (error: any) {
    console.error('更新门店失败:', error);
    
    // 检查是否是重复键错误
    if (error.code === 'P2002') {
      return NextResponse.json({
        success: false,
        error: '门店名称或电话号码已存在'
      }, { status: 400 });
    }
    
    return NextResponse.json({
      success: false,
      error: `更新门店失败: ${error.message}`
    }, { status: 500 });
  }
}
