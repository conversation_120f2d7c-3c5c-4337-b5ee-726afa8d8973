"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { format, addDays, subDays } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  Clock,
  Users,
  Search,
  UserPlus,
  CheckCircle2,
  XCircle,
  MoreHorizontal,
  Clipboard,
  ClipboardCheck,
  Bell,
  CreditCard
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { toast } from "sonner"

// 模拟课程数据
const courseSchedules = [
  {
    id: "c1",
    name: "空中瑜伽",
    time: "14:00 - 15:30",
    coach: "刘琦",
    room: "空中教室",
    capacity: 10,
    booked: 6,
    status: "active", // active, completed, cancelled
    color: "bg-amber-100 border-amber-300",
    attendees: [
      { id: "m1", name: "张三", avatar: "/avatars/01.png", status: "checked-in" },
      { id: "m2", name: "李四", avatar: "/avatars/02.png", status: "booked" },
      { id: "m3", name: "王五", avatar: "/avatars/03.png", status: "booked" },
      { id: "m4", name: "赵六", avatar: "/avatars/04.png", status: "booked" },
      { id: "m5", name: "孙七", avatar: "/avatars/05.png", status: "booked" },
      { id: "m6", name: "周八", avatar: "/avatars/06.png", status: "booked" },
    ]
  },
  {
    id: "c2",
    name: "哈他瑜伽",
    time: "16:00 - 17:30",
    coach: "王梅",
    room: "大教室",
    capacity: 15,
    booked: 12,
    status: "active",
    color: "bg-blue-100 border-blue-300",
    attendees: [
      { id: "m7", name: "吴九", avatar: "/avatars/07.png", status: "booked" },
      { id: "m8", name: "郑十", avatar: "/avatars/08.png", status: "booked" },
      // ... 其他学员
    ]
  },
  {
    id: "c3",
    name: "普拉提",
    time: "18:00 - 19:00",
    coach: "张伟",
    room: "小教室",
    capacity: 8,
    booked: 5,
    status: "active",
    color: "bg-green-100 border-green-300",
    attendees: [
      { id: "m9", name: "冯一", avatar: "/avatars/09.png", status: "booked" },
      { id: "m10", name: "陈二", avatar: "/avatars/10.png", status: "booked" },
      // ... 其他学员
    ]
  }
]

export default function ReceptionWorkspace() {
  const router = useRouter()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [activeTab, setActiveTab] = useState("group-classes")
  const [selectedCourse, setSelectedCourse] = useState(courseSchedules[0])
  const [searchQuery, setSearchQuery] = useState("")
  const [showCheckInDialog, setShowCheckInDialog] = useState(false)
  const [selectedAttendee, setSelectedAttendee] = useState<any>(null)

  // 日期导航
  const goToPreviousDay = () => {
    setCurrentDate(prev => subDays(prev, 1))
  }

  const goToNextDay = () => {
    setCurrentDate(prev => addDays(prev, 1))
  }

  const goToToday = () => {
    setCurrentDate(new Date())
  }

  // 处理签到
  const handleCheckIn = (attendee: any) => {
    setSelectedAttendee(attendee)
    setShowCheckInDialog(true)
  }

  const confirmCheckIn = () => {
    // 在实际应用中，这里会调用API进行签到操作
    toast.success(`${selectedAttendee.name} 签到成功！`)
    setShowCheckInDialog(false)

    // 更新本地状态（实际应用中会从API获取更新后的数据）
    const updatedCourses = courseSchedules.map(course => {
      if (course.id === selectedCourse.id) {
        return {
          ...course,
          attendees: course.attendees.map(a =>
            a.id === selectedAttendee.id ? { ...a, status: "checked-in" } : a
          )
        }
      }
      return course
    })

    // 更新选中的课程
    const updatedSelectedCourse = updatedCourses.find(c => c.id === selectedCourse.id)
    if (updatedSelectedCourse) {
      setSelectedCourse(updatedSelectedCourse)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">前台工作台</h1>
          <p className="text-muted-foreground">
            管理课程签到、会员接待和日常前台工作
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Bell className="h-4 w-4 mr-2" />
            通知
          </Button>
          <Button variant="outline" onClick={() => router.push('/reception/cashier')}>
            <CreditCard className="h-4 w-4 mr-2" />
            收银台
          </Button>
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            新会员登记
          </Button>
        </div>
      </div>

      {/* 功能导航 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 flex flex-col items-center text-center">
            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <ClipboardCheck className="h-6 w-6 text-primary" />
            </div>
            <h3 className="font-medium">课程签到</h3>
            <p className="text-sm text-muted-foreground mt-1">管理会员课程签到</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/reception/visits')}>
          <CardContent className="p-6 flex flex-col items-center text-center">
            <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="font-medium">会员到访</h3>
            <p className="text-sm text-muted-foreground mt-1">记录会员到访情况</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/reception/bookings')}>
          <CardContent className="p-6 flex flex-col items-center text-center">
            <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <Calendar className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="font-medium">课程预约</h3>
            <p className="text-sm text-muted-foreground mt-1">管理课程预约和排队</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/reception/cashier')}>
          <CardContent className="p-6 flex flex-col items-center text-center">
            <div className="h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center mb-4">
              <CreditCard className="h-6 w-6 text-amber-600" />
            </div>
            <h3 className="font-medium">收银台</h3>
            <p className="text-sm text-muted-foreground mt-1">会员卡充值和商品销售</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/reception/leaves')}>
          <CardContent className="p-6 flex flex-col items-center text-center">
            <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="font-medium">请假管理</h3>
            <p className="text-sm text-muted-foreground mt-1">处理会员请假申请</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/reception/no-shows')}>
          <CardContent className="p-6 flex flex-col items-center text-center">
            <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="font-medium">爽约记录</h3>
            <p className="text-sm text-muted-foreground mt-1">管理会员爽约情况</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/reception/handover')}>
          <CardContent className="p-6 flex flex-col items-center text-center">
            <div className="h-12 w-12 rounded-full bg-indigo-100 flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-indigo-600">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                <circle cx="9" cy="7" r="4" />
                <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                <path d="M16 3.13a4 4 0 0 1 0 7.75" />
              </svg>
            </div>
            <h3 className="font-medium">工作交接</h3>
            <p className="text-sm text-muted-foreground mt-1">前台工作交接记录</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => router.push('/reception/reports')}>
          <CardContent className="p-6 flex flex-col items-center text-center">
            <div className="h-12 w-12 rounded-full bg-cyan-100 flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6 text-cyan-600">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                <path d="M14 2v6h6" />
                <path d="M16 13H8" />
                <path d="M16 17H8" />
                <path d="M10 9H8" />
              </svg>
            </div>
            <h3 className="font-medium">日结报表</h3>
            <p className="text-sm text-muted-foreground mt-1">生成前台日结报表</p>
          </CardContent>
        </Card>
      </div>

      {/* 日期导航 */}
      <div className="flex items-center justify-between border-b pb-4">
        <h2 className="text-xl font-semibold">今日课程</h2>
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={goToPreviousDay}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-muted-foreground" />
            <span className="text-lg font-medium">
              {format(currentDate, "yyyy-MM-dd", { locale: zhCN })}
            </span>
          </div>
          <Button variant="outline" size="icon" onClick={goToNextDay}>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button variant="outline" onClick={goToToday}>
            返回今天
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 左侧课程列表 */}
        <div className="md:col-span-1 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium">今日课程</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {courseSchedules.map((course) => (
                <div
                  key={course.id}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedCourse?.id === course.id
                      ? "border-primary bg-primary/5"
                      : `border-muted ${course.color}`
                  }`}
                  onClick={() => setSelectedCourse(course)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-medium">{course.name}</h3>
                      <div className="text-sm text-muted-foreground flex items-center mt-1">
                        <Clock className="h-3 w-3 mr-1" />
                        {course.time}
                      </div>
                    </div>
                    <Badge variant={course.status === "active" ? "default" : course.status === "completed" ? "success" : "destructive"}>
                      {course.status === "active" ? "进行中" : course.status === "completed" ? "已完成" : "已取消"}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground">
                      {course.room} | {course.coach}
                    </div>
                    <div className="text-sm font-medium">
                      {course.booked}/{course.capacity}
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* 右侧详情区域 */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg font-medium">
                  {selectedCourse?.name} - 签到管理
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center">
                    <Users className="h-3 w-3 mr-1" />
                    {selectedCourse?.booked}/{selectedCourse?.capacity}
                  </Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>课程操作</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Clipboard className="h-4 w-4 mr-2" />
                        查看课程详情
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <ClipboardCheck className="h-4 w-4 mr-2" />
                        批量签到
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索会员姓名..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                {selectedCourse?.attendees.map((attendee) => (
                  <div
                    key={attendee.id}
                    className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={attendee.avatar} alt={attendee.name} />
                        <AvatarFallback>{attendee.name[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{attendee.name}</div>
                        <div className="text-sm text-muted-foreground">会员卡: YG-2023-{attendee.id}</div>
                      </div>
                    </div>
                    <div>
                      {attendee.status === "checked-in" ? (
                        <Badge variant="success" className="flex items-center">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          已签到
                        </Badge>
                      ) : (
                        <Button variant="outline" size="sm" onClick={() => handleCheckIn(attendee)}>
                          签到
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 签到确认对话框 */}
      <Dialog open={showCheckInDialog} onOpenChange={setShowCheckInDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认签到</DialogTitle>
            <DialogDescription>
              请确认以下会员的课程签到信息
            </DialogDescription>
          </DialogHeader>
          {selectedAttendee && (
            <div className="py-4">
              <div className="flex items-center gap-4 mb-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={selectedAttendee.avatar} alt={selectedAttendee.name} />
                  <AvatarFallback>{selectedAttendee.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="text-lg font-medium">{selectedAttendee.name}</div>
                  <div className="text-sm text-muted-foreground">会员卡: YG-2023-{selectedAttendee.id}</div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-muted-foreground">课程名称</span>
                  <span className="font-medium">{selectedCourse?.name}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-muted-foreground">上课时间</span>
                  <span className="font-medium">{selectedCourse?.time}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-muted-foreground">教练</span>
                  <span className="font-medium">{selectedCourse?.coach}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b">
                  <span className="text-muted-foreground">教室</span>
                  <span className="font-medium">{selectedCourse?.room}</span>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCheckInDialog(false)}>取消</Button>
            <Button onClick={confirmCheckIn}>确认签到</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
