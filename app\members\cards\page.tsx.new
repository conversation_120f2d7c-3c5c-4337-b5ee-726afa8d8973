"use client"

import { useState } from "react"
import { useToast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  MoreHorizontal,
  Pencil,
  Trash2,
  Plus,
  Download,
  Upload,
  Filter,
  LayoutGrid,
  LayoutList,
  BarChart,
  User,
  CreditCard,
  Tag,
  Calendar,
  Receipt,
  MessageSquare,
  Link,
  Settings,
  FileText,
  BarChart2,
  DollarSign,
  ShoppingBag,
  Clock,
  Eye,
  EyeOff,
  Copy,
  Share2,
  Percent,
  Gift,
  Users,
  AlertCircle,
  Check,
  Table as TableIcon,
  Search
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// 导入对话框组件
import { AddMemberCardDialog } from "@/components/members/add-member-card-dialog"
import { MemberCardDetailDialog } from "@/components/members/member-card-detail-dialog"
import { EditMemberCardDialog } from "@/components/members/edit-member-card-dialog"
import { MemberCardStatsDialog } from "@/components/members/member-card-stats-dialog"
import { CardMembersDialog } from "@/components/members/card-members-dialog"
import { SalesAnalysisDialog } from "@/components/members/sales-analysis-dialog"
import { ExportReportDialog } from "@/components/members/export-report-dialog"
import { FeatureInDevelopmentDialog } from "@/components/feature-in-development-dialog"
import { ImportExportMenu } from "@/components/import-export-menu"
import { MemberCardGrid } from "@/components/members/member-card-grid"

// 示例数据
const memberCards = [
  {
    id: 1,
    name: "年卡",
    description: "365天不限次数",
    price: "¥3,680",
    originalPrice: "¥4,680",
    validity: "365天",
    limit: "不限次数",
    status: "active",
    members: 128,
    color: "#4f46e5",
    salesCount: 245,
    revenue: "¥901,600",
    createdAt: "2023-01-15",
    updatedAt: "2023-06-20",
    isTrialCard: false,
  },
  {
    id: 2,
    name: "季卡",
    description: "90天不限次数",
    price: "¥1,280",
    originalPrice: "¥1,680",
    validity: "90天",
    limit: "不限次数",
    status: "active",
    members: 86,
    color: "#0ea5e9",
    salesCount: 156,
    revenue: "¥199,680",
    createdAt: "2023-02-10",
    updatedAt: "2023-05-15",
    isTrialCard: false,
  },
  {
    id: 3,
    name: "月卡",
    description: "30天不限次数",
    price: "¥580",
    originalPrice: "¥680",
    validity: "30天",
    limit: "不限次数",
    status: "active",
    members: 112,
    color: "#10b981",
    salesCount: 324,
    revenue: "¥187,920",
    createdAt: "2023-01-05",
    updatedAt: "2023-06-10",
    isTrialCard: false,
  },
  {
    id: 4,
    name: "体验卡",
    description: "7天3次体验",
    price: "¥99",
    originalPrice: "¥199",
    validity: "7天",
    limit: "最多3次",
    status: "active",
    members: 56,
    color: "#f59e0b",
    salesCount: 412,
    revenue: "¥40,788",
    createdAt: "2023-03-01",
    updatedAt: "2023-06-01",
    isTrialCard: true,
  },
  {
    id: 5,
    name: "次卡10次",
    description: "10次课程",
    price: "¥880",
    originalPrice: "¥1,080",
    validity: "180天",
    limit: "10次",
    status: "active",
    members: 76,
    color: "#8b5cf6",
    salesCount: 189,
    revenue: "¥166,320",
    createdAt: "2023-02-15",
    updatedAt: "2023-05-20",
    isTrialCard: false,
  },
  {
    id: 6,
    name: "次卡20次",
    description: "20次课程",
    price: "¥1,580",
    originalPrice: "¥1,880",
    validity: "365天",
    limit: "20次",
    status: "inactive",
    members: 32,
    color: "#ec4899",
    salesCount: 68,
    revenue: "¥107,440",
    createdAt: "2023-01-20",
    updatedAt: "2023-04-15",
    isTrialCard: false,
  },
  {
    id: 7,
    name: "私教卡",
    description: "一对一私教",
    price: "¥4,880",
    originalPrice: "¥5,880",
    validity: "180天",
    limit: "10次私教",
    status: "inactive",
    members: 18,
    color: "#ef4444",
    salesCount: 42,
    revenue: "¥204,960",
    createdAt: "2023-03-10",
    updatedAt: "2023-04-20",
    isTrialCard: false,
  },
]

export default function MemberCardsPage() {
  const { toast } = useToast()
  // 视图模式和筛选状态管理
  const [viewMode, setViewMode] = useState<"list" | "grid" | "table">("list")
  const [filterStatus, setFilterStatus] = useState<"all" | "active" | "inactive">("all")

  // 对话框状态管理
  const [addCardOpen, setAddCardOpen] = useState(false)
  const [selectedCard, setSelectedCard] = useState<(typeof memberCards)[0] | null>(null)
  const [editCardOpen, setEditCardOpen] = useState(false)
  const [cardToEdit, setCardToEdit] = useState<(typeof memberCards)[0] | null>(null)
  const [statsOpen, setStatsOpen] = useState(false)
  const [featureDialogOpen, setFeatureDialogOpen] = useState(false)
  const [currentFeature, setCurrentFeature] = useState("")

  // 报表对话框状态
  const [cardMembersOpen, setCardMembersOpen] = useState(false)
  const [salesAnalysisOpen, setSalesAnalysisOpen] = useState(false)
  const [exportReportOpen, setExportReportOpen] = useState(false)
  const [selectedReportCard, setSelectedReportCard] = useState<(typeof memberCards)[0] | null>(null)

  // 打开会员卡详情
  const openCardDetail = (card: (typeof memberCards)[0]) => {
    setSelectedCard(card)
  }

  // 打开编辑会员卡对话框
  const openEditCard = (card: (typeof memberCards)[0]) => {
    setCardToEdit(card)
    setEditCardOpen(true)
  }

  // 处理功能点击
  const handleFeatureClick = (featureName: string) => {
    setCurrentFeature(featureName)
    setFeatureDialogOpen(true)
  }

  // 处理保存会员卡
  const handleSaveCard = (updatedCard: (typeof memberCards)[0]) => {
    // 在实际应用中，这里会调用API保存会员卡信息
    toast({
      title: "保存成功",
      description: `会员卡 ${updatedCard.name} 已更新`,
    })
  }

  // 处理复制会员卡
  const handleCopyCard = (newCard: any) => {
    // 在实际应用中，这里会调用API创建新的会员卡
    toast({
      title: "复制成功",
      description: `已创建新会员卡 ${newCard.name}`,
    })
  }

  // 处理会员卡状态变更
  const handleChangeCardStatus = (cardId: number | string, newStatus: string, reason: string) => {
    // 在实际应用中，这里会调用API更改会员卡状态
    toast({
      title: newStatus === "active" ? "会员卡已上架" : "会员卡已下架",
      description: reason ? `原因: ${reason}` : undefined,
    })
  }

  // 处理快捷操作
  const handleQuickAction = (action: string, card: (typeof memberCards)[0]) => {
    switch (action) {
      case "edit":
        openEditCard(card)
        break
      case "copy":
        // 复制卡模板
        handleCopyCard({
          ...card,
          id: Date.now(),
          name: `${card.name} (复制)`,
        })
        break
      case "members":
        // 查看持卡会员
        setSelectedReportCard(card)
        setCardMembersOpen(true)
        break
      case "sales":
        // 查看销售分析
        setSelectedReportCard(card)
        setSalesAnalysisOpen(true)
        break
      case "export":
        // 导出报表
        setSelectedReportCard(card)
        setExportReportOpen(true)
        break
      case "activate":
        // 上架卡种
        handleChangeCardStatus(card.id, "active", "")
        break
      case "deactivate":
        // 下架卡种
        handleChangeCardStatus(card.id, "inactive", "调整价格")
        break
      case "delete":
        // 删除卡种
        handleFeatureClick("删除会员卡")
        break
      default:
        break
    }
  }

  // 根据筛选条件过滤会员卡
  const filteredCards = memberCards.filter(card => {
    if (filterStatus === "all") return true;
    return card.status === filterStatus;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">会员卡管理</h1>
        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                筛选
                {filterStatus !== "all" && (
                  <Badge variant="secondary" className="ml-2 px-1">
                    <span className="h-3 w-3 rounded-full bg-primary" />
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>筛选选项</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                <div className="flex items-center w-full justify-between">
                  <span>全部会员卡</span>
                  {filterStatus === "all" && <Check className="h-4 w-4" />}
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("active")}>
                <div className="flex items-center w-full justify-between">
                  <span>仅显示有效</span>
                  {filterStatus === "active" && <Check className="h-4 w-4" />}
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("inactive")}>
                <div className="flex items-center w-full justify-between">
                  <span>仅显示停用</span>
                  {filterStatus === "inactive" && <Check className="h-4 w-4" />}
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button variant="outline" size="icon" onClick={() => setStatsOpen(true)}>
            <BarChart className="h-4 w-4" />
          </Button>

          {/* 视图切换按钮组 */}
          <div className="border rounded-md flex overflow-hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setViewMode("list")}
              className={`rounded-none ${viewMode === "list" ? "bg-muted" : ""}`}
            >
              <LayoutList className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setViewMode("grid")}
              className={`rounded-none ${viewMode === "grid" ? "bg-muted" : ""}`}
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setViewMode("table")}
              className={`rounded-none ${viewMode === "table" ? "bg-muted" : ""}`}
            >
              <TableIcon className="h-4 w-4" />
            </Button>
          </div>
          <Button onClick={() => setAddCardOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加会员卡
          </Button>
        </div>
      </div>

      <div className="mt-4 flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/3 relative">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input placeholder="搜索会员卡" className="pl-10" />
        </div>
        <div className="flex-1"></div>
        <ImportExportMenu
          cards={filteredCards}
          onImportComplete={(importedCards) => {
            toast({
              title: "导入成功",
              description: `已成功导入 ${importedCards.length} 张会员卡`,
            })
            // 在实际应用中，这里会刷新会员卡列表
          }}
        />
      </div>

      <div className="mt-4">
        {viewMode === "list" ? (
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>会员卡名称</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>有效期</TableHead>
                    <TableHead>使用限制</TableHead>
                    <TableHead>卡类型</TableHead>
                    <TableHead>持卡会员</TableHead>
                    <TableHead>销售数量</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCards.map((card) => (
                    <TableRow key={card.id} className="cursor-pointer" onClick={() => openCardDetail(card)}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="h-4 w-4 rounded-full" style={{ backgroundColor: card.color }} />
                          <span className="font-medium">{card.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{card.description}</TableCell>
                      <TableCell>
                        <div>
                          <span className="font-medium">{card.price}</span>
                          {card.originalPrice !== card.price && (
                            <span className="ml-2 text-sm text-muted-foreground line-through">
                              {card.originalPrice}
                            </span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{card.validity}</TableCell>
                      <TableCell>{card.limit}</TableCell>
                      <TableCell>
                        {card.isTrialCard && (
                          <Badge variant="outline" className="bg-pink-50 text-pink-700 border-pink-200 mr-2">
                            体验卡
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{card.members}</TableCell>
                      <TableCell>{card.salesCount}</TableCell>
                      <TableCell>
                        <Badge variant={card.status === "active" ? "default" : "secondary"}>
                          {card.status === "active" ? "销售中" : "已下架"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-56">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />

                            <DropdownMenuItem onClick={() => openCardDetail(card)}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleQuickAction("edit", card)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              编辑信息
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleQuickAction("copy", card)}>
                              <Copy className="mr-2 h-4 w-4" />
                              复制卡模板
                            </DropdownMenuItem>

                            <DropdownMenuSeparator />

                            <DropdownMenuItem onClick={() => handleQuickAction("members", card)}>
                              <Users className="mr-2 h-4 w-4" />
                              持卡会员
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleQuickAction("sales", card)}>
                              <BarChart2 className="mr-2 h-4 w-4" />
                              销售分析
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleQuickAction("export", card)}>
                              <FileText className="mr-2 h-4 w-4" />
                              导出报表
                            </DropdownMenuItem>

                            <DropdownMenuSeparator />

                            {card.status === "active" ? (
                              <DropdownMenuItem onClick={() => handleQuickAction("deactivate", card)}>
                                <EyeOff className="mr-2 h-4 w-4" />
                                下架卡种
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem onClick={() => handleQuickAction("activate", card)}>
                                <Eye className="mr-2 h-4 w-4" />
                                重新上架
                              </DropdownMenuItem>
                            )}

                            <DropdownMenuItem
                              onClick={() => handleQuickAction("delete", card)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除卡种
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        ) : (
          <MemberCardGrid cards={filteredCards} onCardClick={openCardDetail} onQuickAction={handleQuickAction} />
        )}
      </div>

      <AddMemberCardDialog open={addCardOpen} onOpenChange={setAddCardOpen} />

      {selectedCard && (
        <MemberCardDetailDialog
          card={selectedCard}
          open={!!selectedCard}
          onOpenChange={(open) => !open && setSelectedCard(null)}
        />
      )}

      <MemberCardStatsDialog open={statsOpen} onOpenChange={setStatsOpen} cards={memberCards} />

      <EditMemberCardDialog
        open={editCardOpen}
        onOpenChange={setEditCardOpen}
        card={cardToEdit}
        onSave={handleSaveCard}
      />

      <FeatureInDevelopmentDialog
        open={featureDialogOpen}
        onOpenChange={setFeatureDialogOpen}
        featureName={currentFeature}
      />

      {/* 报表对话框 */}
      <CardMembersDialog
        open={cardMembersOpen}
        onOpenChange={setCardMembersOpen}
        card={selectedReportCard}
      />

      <SalesAnalysisDialog
        open={salesAnalysisOpen}
        onOpenChange={setSalesAnalysisOpen}
        card={selectedReportCard}
      />

      <ExportReportDialog
        open={exportReportOpen}
        onOpenChange={setExportReportOpen}
        card={selectedReportCard}
      />
    </div>
  )
}
