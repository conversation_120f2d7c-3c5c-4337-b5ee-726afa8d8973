"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus } from "lucide-react"

export default function TestCourseTypesPage() {
  const [open, setOpen] = useState(false)
  const [name, setName] = useState("")

  const handleAddClick = () => {
    console.log('添加按钮被点击')
    setOpen(true)
  }

  const handleSave = () => {
    console.log('保存:', name)
    setOpen(false)
    setName("")
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">课程类型测试页面</h1>
      
      <div className="mb-4">
        <Button onClick={handleAddClick}>
          <Plus className="mr-2 h-4 w-4" />
          添加课程类型
        </Button>
      </div>

      <div className="p-4 bg-gray-100 rounded">
        <p>对话框状态: {open ? '打开' : '关闭'}</p>
        <p>名称: {name}</p>
      </div>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>添加课程类型</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">名称</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="输入课程类型名称"
              />
            </div>
            
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                取消
              </Button>
              <Button onClick={handleSave}>
                保存
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
