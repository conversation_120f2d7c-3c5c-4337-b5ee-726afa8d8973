"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { CreditCard } from "lucide-react"

interface PaySalaryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  record: any
}

export function PaySalaryDialog({ open, onOpenChange, record }: PaySalaryDialogProps) {
  const [paymentMethod, setPaymentMethod] = useState<string>("bank")
  const [paymentDate, setPaymentDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  )
  const [remark, setRemark] = useState<string>("")
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    setIsSubmitting(true)
    
    // 构建发放数据
    const paymentData = {
      recordId: record?.id,
      paymentMethod,
      paymentDate,
      remark,
    }
    
    // 这里应该调用API提交发放结果
    console.log("提交发放结果:", paymentData)
    
    // 模拟API调用延迟
    setTimeout(() => {
      setIsSubmitting(false)
      onOpenChange(false)
      
      // 重置表单
      setPaymentMethod("bank")
      setPaymentDate(new Date().toISOString().split("T")[0])
      setRemark("")
    }, 1000)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        {record && (
          <form onSubmit={handleSubmit}>
            <DialogHeader>
              <DialogTitle>发放薪资</DialogTitle>
              <DialogDescription>
                为 {record.coachName} 发放 {record.salaryMonth} 薪资
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">教练姓名</p>
                  <p className="font-medium">{record.coachName}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">薪资月份</p>
                  <p className="font-medium">{record.salaryMonth}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">总薪资</p>
                  <p className="font-medium">¥{record.totalSalary.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">实发薪资</p>
                  <p className="font-medium">¥{record.netSalary.toLocaleString()}</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="payment-method">支付方式</Label>
                <Select
                  value={paymentMethod}
                  onValueChange={setPaymentMethod}
                  required
                >
                  <SelectTrigger id="payment-method">
                    <SelectValue placeholder="请选择支付方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bank">银行转账</SelectItem>
                    <SelectItem value="alipay">支付宝</SelectItem>
                    <SelectItem value="wechat">微信支付</SelectItem>
                    <SelectItem value="cash">现金</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="payment-date">发放日期</Label>
                <Input
                  id="payment-date"
                  type="date"
                  value={paymentDate}
                  onChange={(e) => setPaymentDate(e.target.value)}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="remark">备注</Label>
                <Textarea
                  id="remark"
                  value={remark}
                  onChange={(e) => setRemark(e.target.value)}
                  placeholder="请输入发放备注"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button type="submit" disabled={isSubmitting} className="gap-2">
                <CreditCard className="h-4 w-4" />
                {isSubmitting ? "发放中..." : "确认发放"}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
