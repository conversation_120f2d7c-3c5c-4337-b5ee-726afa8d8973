const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga',
  charset: 'utf8mb4'
};

async function createMemberCardTypesTable() {
  let connection;
  
  try {
    console.log('连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✓ 数据库连接成功');

    // 创建会员卡类型表
    console.log('\n1. 创建会员卡类型表...');
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS member_card_types (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tenant_id INT NOT NULL COMMENT '租户ID',
        name VARCHAR(100) NOT NULL COMMENT '卡类型名称',
        description TEXT COMMENT '卡类型描述',
        price DECIMAL(10,2) NOT NULL COMMENT '售价',
        original_price DECIMAL(10,2) COMMENT '原价',
        validity_days INT COMMENT '有效期天数',
        usage_limit VARCHAR(50) COMMENT '使用限制',
        card_category ENUM('time', 'count', 'value') NOT NULL COMMENT '卡片类别',
        card_type VARCHAR(50) NOT NULL COMMENT '具体卡类型标识',
        status ENUM('销售中', '已下架', '已停用') DEFAULT '销售中' COMMENT '卡类型状态',
        total_sold INT DEFAULT 0 COMMENT '总销售数量',
        active_cards INT DEFAULT 0 COMMENT '当前有效卡数量',
        display_order INT DEFAULT 0 COMMENT '显示顺序',
        color VARCHAR(7) COMMENT '显示颜色',
        is_trial_card BOOLEAN DEFAULT FALSE COMMENT '是否为体验卡',
        is_gift_card BOOLEAN DEFAULT FALSE COMMENT '是否为赠送卡',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        INDEX idx_tenant_id (tenant_id),
        INDEX idx_status (status),
        INDEX idx_card_category (card_category),
        INDEX idx_display_order (display_order)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡类型表'
    `);
    console.log('✓ 会员卡类型表创建成功');

    // 插入示例数据
    console.log('\n2. 插入示例会员卡类型数据...');
    
    const sampleData = [
      [2, '年卡', '365天不限次数瑜伽课程', 3680.00, 4680.00, 365, '不限次数', 'time', '年卡', '销售中', 0, 0, 1, '#4f46e5', false, false],
      [2, '季卡', '90天不限次数瑜伽课程', 1280.00, 1680.00, 90, '不限次数', 'time', '季卡', '销售中', 0, 0, 2, '#0ea5e9', false, false],
      [2, '月卡', '30天不限次数瑜伽课程', 580.00, 680.00, 30, '不限次数', 'time', '月卡', '销售中', 0, 0, 3, '#10b981', false, false],
      [2, '体验卡', '7天3次体验课程', 99.00, 199.00, 7, '最多3次', 'count', '体验卡', '销售中', 0, 0, 4, '#f59e0b', true, false],
      [2, '10次卡', '10次课程，180天有效', 880.00, 1080.00, 180, '10次', 'count', '次卡', '销售中', 0, 0, 5, '#8b5cf6', false, false],
      [2, '20次卡', '20次课程，365天有效', 1580.00, 1880.00, 365, '20次', 'count', '次卡', '已下架', 0, 0, 6, '#ec4899', false, false],
      [2, '储值卡500', '储值500元，365天有效', 450.00, 500.00, 365, '储值500元', 'value', '储值卡', '销售中', 0, 0, 7, '#06b6d4', false, false],
      [2, '储值卡1000', '储值1000元，365天有效', 850.00, 1000.00, 365, '储值1000元', 'value', '储值卡', '销售中', 0, 0, 8, '#0284c7', false, false]
    ];

    for (const data of sampleData) {
      try {
        await connection.execute(`
          INSERT INTO member_card_types 
          (tenant_id, name, description, price, original_price, validity_days, usage_limit, card_category, card_type, status, total_sold, active_cards, display_order, color, is_trial_card, is_gift_card)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, data);
        console.log(`✓ 插入成功: ${data[1]}`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`- 跳过重复数据: ${data[1]}`);
        } else {
          console.error(`插入失败: ${data[1]}`, error.message);
        }
      }
    }

    console.log('\n✓ 会员卡类型数据插入完成');

  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('✓ 数据库连接已关闭');
    }
  }
}

// 执行脚本
createMemberCardTypesTable();
