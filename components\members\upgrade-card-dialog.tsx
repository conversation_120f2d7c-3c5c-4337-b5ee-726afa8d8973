"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON>alog<PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { CreditCard, ArrowUpCircle, CalendarIcon } from "lucide-react"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface UpgradeCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
    card: string
    remaining: string
    expiry: string
  }
}

export function UpgradeCardDialog({ open, onOpenChange, member }: UpgradeCardDialogProps) {
  const { toast } = useToast()
  const [selectedCard, setSelectedCard] = useState("")
  const [paymentMethod, setPaymentMethod] = useState("wechat")
  const [amount, setAmount] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [expiryDate, setExpiryDate] = useState<Date>()

  // 模拟可升级的会员卡类型
  const upgradeOptions = [
    {
      id: "year-card",
      name: "年卡",
      price: "3,800",
      description: "无限次数，有效期1年",
      features: ["所有常规课程", "专属预约通道", "会员专享活动"]
    },
    {
      id: "vip-card",
      name: "VIP卡",
      price: "5,800",
      description: "无限次数，有效期1年",
      features: ["所有常规课程", "每月2次私教课", "会员专享活动", "专属更衣柜"]
    },
    {
      id: "platinum-card",
      name: "白金卡",
      price: "8,800",
      description: "无限次数，有效期1年",
      features: ["所有课程无限次", "每月4次私教课", "会员专享活动", "专属更衣柜", "免费停车"]
    }
  ]

  const handleSelectCard = (cardId: string) => {
    setSelectedCard(cardId)
    // 根据选择的卡设置默认金额
    const card = upgradeOptions.find(c => c.id === cardId)
    if (card) {
      setAmount(card.price.replace(/,/g, ""))
    }
  }

  const handleSubmit = async () => {
    if (!selectedCard) {
      toast({
        title: "请选择会员卡",
        description: "请选择要升级的会员卡类型",
        variant: "destructive",
      })
      return
    }

    if (!expiryDate) {
      toast({
        title: "请选择到期日期",
        description: "请为新会员卡设置到期日期",
        variant: "destructive",
      })
      return
    }

    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      toast({
        title: "请输入有效金额",
        description: "支付金额必须是大于0的数字",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const selectedCardInfo = upgradeOptions.find(c => c.id === selectedCard)
      
      toast({
        title: "会员卡升级成功",
        description: `${member.name}的会员卡已成功升级为${selectedCardInfo?.name}，到期日期：${format(expiryDate, 'yyyy-MM-dd')}`,
      })
      
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "操作失败",
        description: "会员卡升级失败，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>升级会员卡</DialogTitle>
          <DialogDescription>
            将会员当前卡升级为更高级别的会员卡
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <div className="flex items-center gap-4">
              <CreditCard className="h-8 w-8 text-muted-foreground" />
              <div>
                <h4 className="font-medium">{member.name}</h4>
                <p className="text-sm text-muted-foreground">
                  当前: {member.card} (剩余: {member.remaining}, 到期: {member.expiry})
                </p>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <Label>选择升级卡种</Label>
            <div className="grid gap-4">
              {upgradeOptions.map((card) => (
                <Card 
                  key={card.id}
                  className={cn(
                    "cursor-pointer hover:border-primary",
                    selectedCard === card.id ? "border-2 border-primary" : ""
                  )}
                  onClick={() => handleSelectCard(card.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-bold">{card.name}</h3>
                        <p className="text-sm text-muted-foreground">{card.description}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">¥{card.price}</p>
                      </div>
                    </div>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {card.features.map((feature, index) => (
                        <Badge key={index} variant="secondary">{feature}</Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="expiry-date">新卡到期日期</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="expiry-date"
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {expiryDate ? format(expiryDate, 'yyyy-MM-dd') : <span>选择日期</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={expiryDate}
                  onSelect={setExpiryDate}
                  initialFocus
                  locale={zhCN}
                  disabled={(date) => date < new Date()}
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="space-y-2">
            <Label>支付方式</Label>
            <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod} className="flex space-x-4">
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="wechat" id="wechat" />
                <Label htmlFor="wechat">微信支付</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="alipay" id="alipay" />
                <Label htmlFor="alipay">支付宝</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="cash" id="cash" />
                <Label htmlFor="cash">现金</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="card" id="card" />
                <Label htmlFor="card">刷卡</Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="amount">支付金额</Label>
            <div className="flex items-center">
              <span className="mr-2">¥</span>
              <Input
                id="amount"
                type="text"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
              />
            </div>
          </div>
          
          <div className="rounded-md bg-muted p-3 text-sm">
            <p className="font-medium">升级说明：</p>
            <ul className="list-disc pl-5 pt-2 text-muted-foreground">
              <li>升级后，原会员卡将被停用</li>
              <li>原会员卡的剩余价值将自动折算到新卡中</li>
              <li>升级完成后，系统将自动生成收款记录</li>
            </ul>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>取消</Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "处理中..." : "确认升级"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
