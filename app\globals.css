@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 217 89% 61%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 89% 61%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 89% 61%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    -webkit-text-size-adjust: 100%;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
  }
}

@layer utilities {
  .animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 超大屏幕适配样式 */
  .ultra-large-container {
    max-width: 2400px;
    margin: 0 auto;
  }

  .ultra-large-text {
    font-size: clamp(1rem, 2.5vw, 2rem);
  }

  .ultra-large-heading {
    font-size: clamp(2rem, 4vw, 4rem);
  }

  .ultra-large-hero-heading {
    font-size: clamp(3rem, 5vw, 6rem);
  }
}

/* 超大屏幕媒体查询 */
@media screen and (min-width: 1920px) {
  .container {
    max-width: 1800px;
  }
}

@media screen and (min-width: 2560px) {
  .container {
    max-width: 2400px;
  }

  body {
    font-size: 18px;
  }

  h1 {
    font-size: 4rem;
  }

  h2 {
    font-size: 3rem;
  }

  h3 {
    font-size: 2rem;
  }

  p {
    font-size: 1.25rem;
    line-height: 1.8;
  }
}

@media screen and (min-width: 3840px) {
  .container {
    max-width: 3600px;
  }

  body {
    font-size: 20px;
  }

  h1 {
    font-size: 5rem;
  }

  h2 {
    font-size: 4rem;
  }

  h3 {
    font-size: 2.5rem;
  }

  p {
    font-size: 1.5rem;
    line-height: 1.8;
  }

  /* 增加按钮和卡片的尺寸 */
  .btn-large {
    padding: 1rem 2rem;
    font-size: 1.25rem;
  }

  .card-large {
    padding: 2rem;
  }
}

@media screen and (min-width: 5120px) {
  .container {
    max-width: 4800px;
  }

  body {
    font-size: 24px;
  }

  h1 {
    font-size: 6rem;
  }

  h2 {
    font-size: 5rem;
  }

  h3 {
    font-size: 3rem;
  }

  p {
    font-size: 1.75rem;
    line-height: 1.8;
  }
}

