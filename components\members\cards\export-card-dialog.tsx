"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Download, FileSpreadsheet, FileText, FileJson, Info } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Progress } from "@/components/ui/progress"

interface ExportCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  cards: any[]
}

export function ExportCardDialog({
  open,
  onOpenChange,
  cards
}: ExportCardDialogProps) {
  const { toast } = useToast()
  const [exportFormat, setExportFormat] = useState("excel")
  const [selectedFields, setSelectedFields] = useState<string[]>([
    "name", "description", "price", "validity", "limit", "status", "salesCount", "members"
  ])
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)

  // 可导出的字段
  const availableFields = [
    { id: "name", label: "会员卡名称" },
    { id: "description", label: "描述" },
    { id: "price", label: "价格" },
    { id: "validity", label: "有效期" },
    { id: "limit", label: "使用限制" },
    { id: "status", label: "状态" },
    { id: "salesCount", label: "销售数量" },
    { id: "members", label: "持卡会员数" },
    { id: "createdAt", label: "创建时间" },
    { id: "updatedAt", label: "更新时间" }
  ]

  // 处理字段选择
  const handleFieldToggle = (field: string) => {
    setSelectedFields(prev =>
      prev.includes(field)
        ? prev.filter(f => f !== field)
        : [...prev, field]
    )
  }

  // 全选/取消全选
  const handleSelectAll = (select: boolean) => {
    if (select) {
      setSelectedFields(availableFields.map(field => field.id))
    } else {
      setSelectedFields([])
    }
  }

  // 处理导出
  const handleExport = async () => {
    if (selectedFields.length === 0) {
      toast({
        title: "请选择至少一个字段",
        variant: "destructive",
      })
      return
    }

    setIsExporting(true)

    // 设置导出进度
    const interval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          return 100
        }
        return prev + 10
      })
    }, 100)

    try {
      // 准备导出数据
      const exportData = cards.map(card => {
        const exportedCard: Record<string, any> = {}
        selectedFields.forEach(field => {
          exportedCard[field] = card[field]
        })
        return exportedCard
      })

      // 根据选择的格式创建并下载文件
      let fileContent = ""
      let fileName = `会员卡数据_${new Date().toISOString().split("T")[0]}`
      let fileType = ""

      if (exportFormat === "json") {
        // JSON格式
        fileContent = JSON.stringify(exportData, null, 2)
        fileName += ".json"
        fileType = "application/json"
      } else if (exportFormat === "csv") {
        // CSV格式
        const headers = selectedFields.map(field => {
          const fieldLabel = availableFields.find(f => f.id === field)?.label || field
          return fieldLabel
        })

        fileContent = headers.join(",") + "\n"

        exportData.forEach(card => {
          const row = selectedFields.map(field => {
            // 处理包含逗号的字段，用双引号包裹
            let value = card[field]?.toString() || ""
            if (value.includes(",")) {
              value = `"${value}"`
            }
            return value
          })
          fileContent += row.join(",") + "\n"
        })

        fileName += ".csv"
        fileType = "text/csv"
      } else {
        // Excel格式 (实际上我们使用CSV，因为真正的Excel需要额外库)
        // 在实际项目中，可以使用xlsx库生成真正的Excel文件
        const headers = selectedFields.map(field => {
          const fieldLabel = availableFields.find(f => f.id === field)?.label || field
          return fieldLabel
        })

        fileContent = headers.join(",") + "\n"

        exportData.forEach(card => {
          const row = selectedFields.map(field => {
            // 处理包含逗号的字段，用双引号包裹
            let value = card[field]?.toString() || ""
            if (value.includes(",")) {
              value = `"${value}"`
            }
            return value
          })
          fileContent += row.join(",") + "\n"
        })

        fileName += ".csv" // 使用CSV作为Excel的替代
        fileType = "text/csv"
      }

      // 创建Blob对象
      const blob = new Blob([fileContent], { type: fileType })

      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url
      link.download = fileName

      // 触发下载
      document.body.appendChild(link)
      link.click()

      // 清理
      URL.revokeObjectURL(url)
      document.body.removeChild(link)

      const formatName = exportFormat === "excel"
        ? "Excel"
        : exportFormat === "csv"
          ? "CSV"
          : "JSON"

      toast({
        title: "导出成功",
        description: `已成功导出 ${cards.length} 张会员卡到${formatName}文件`,
      })

      onOpenChange(false)
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出过程中出现错误，请重试",
        variant: "destructive",
      })
      console.error("导出错误:", error)
    } finally {
      clearInterval(interval)
      setIsExporting(false)
      setExportProgress(0)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-md overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5 text-primary" />
            导出会员卡
          </DialogTitle>
          <DialogDescription>
            选择导出格式和字段，将会员卡数据导出到文件
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <Label>导出格式</Label>
            <RadioGroup
              value={exportFormat}
              onValueChange={setExportFormat}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="excel" id="excel" />
                <Label htmlFor="excel" className="flex cursor-pointer items-center gap-2 font-normal">
                  <FileSpreadsheet className="h-4 w-4 text-green-600" />
                  Excel文件 (.xlsx)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="csv" id="csv" />
                <Label htmlFor="csv" className="flex cursor-pointer items-center gap-2 font-normal">
                  <FileText className="h-4 w-4 text-blue-600" />
                  CSV文件 (.csv)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="json" id="json" />
                <Label htmlFor="json" className="flex cursor-pointer items-center gap-2 font-normal">
                  <FileJson className="h-4 w-4 text-amber-600" />
                  JSON文件 (.json)
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>导出字段</Label>
              <div className="space-x-2">
                <Button
                  variant="link"
                  size="sm"
                  className="h-auto p-0 text-xs"
                  onClick={() => handleSelectAll(true)}
                  disabled={isExporting}
                >
                  全选
                </Button>
                <span className="text-xs text-muted-foreground">|</span>
                <Button
                  variant="link"
                  size="sm"
                  className="h-auto p-0 text-xs"
                  onClick={() => handleSelectAll(false)}
                  disabled={isExporting}
                >
                  取消全选
                </Button>
              </div>
            </div>

            <div className="max-h-[200px] space-y-2 overflow-y-auto rounded-md border p-2">
              {availableFields.map(field => (
                <div key={field.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`field-${field.id}`}
                    checked={selectedFields.includes(field.id)}
                    onCheckedChange={() => handleFieldToggle(field.id)}
                    disabled={isExporting}
                  />
                  <Label
                    htmlFor={`field-${field.id}`}
                    className="cursor-pointer font-normal"
                  >
                    {field.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              将导出 {cards.length} 张会员卡的数据，包含 {selectedFields.length} 个字段
            </AlertDescription>
          </Alert>

          {isExporting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>导出进度</Label>
                <span className="text-sm">{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} className="h-2 w-full" />
            </div>
          )}
        </div>

        <DialogFooter className="gap-2 sm:gap-0">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isExporting}>
            取消
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting || selectedFields.length === 0}
          >
            {isExporting ? (
              <>导出中...</>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                开始导出
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
