"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

interface AddEquipmentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddEquipmentDialog({ open, onOpenChange }: AddEquipmentDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>添加设备</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="maintenance">维护信息</TabsTrigger>
            <TabsTrigger value="financial">财务信息</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">设备名称</Label>
                <Input id="name" placeholder="输入设备名称" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="id">设备编号</Label>
                <Input id="id" placeholder="输入设备编号" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="venue">所属场地</Label>
                <Select>
                  <SelectTrigger id="venue">
                    <SelectValue placeholder="选择场地" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有场地</SelectItem>
                    <SelectItem value="1">1号瑜伽室</SelectItem>
                    <SelectItem value="2">2号瑜伽室</SelectItem>
                    <SelectItem value="3">3号瑜伽室</SelectItem>
                    <SelectItem value="4">4号瑜伽室</SelectItem>
                    <SelectItem value="5">私教室</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="quantity">数量</Label>
                <Input id="quantity" type="number" min="1" placeholder="输入设备数量" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="brand">品牌</Label>
                <Input id="brand" placeholder="输入设备品牌" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="model">型号</Label>
                <Input id="model" placeholder="输入设备型号" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">设备描述</Label>
              <Textarea id="description" placeholder="输入设备描述信息" rows={3} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="image">设备图片</Label>
              <Input id="image" type="file" accept="image/*" />
            </div>
          </TabsContent>

          <TabsContent value="maintenance" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">设备状态</Label>
                <Select defaultValue="normal">
                  <SelectTrigger id="status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="normal">正常</SelectItem>
                    <SelectItem value="maintenance">维护中</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="lifespan">预计使用寿命</Label>
                <Input id="lifespan" placeholder="例如：24个月" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="last-maintenance">上次维护日期</Label>
                <Input id="last-maintenance" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="next-maintenance">下次维护日期</Label>
                <Input id="next-maintenance" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maintenance-cycle">维护周期（月）</Label>
                <Input id="maintenance-cycle" type="number" min="1" defaultValue="3" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maintenance-staff">维护负责人</Label>
                <Input id="maintenance-staff" placeholder="输入负责人姓名" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="maintenance-notes">维护备注</Label>
              <Textarea id="maintenance-notes" placeholder="输入维护相关备注信息" rows={3} />
            </div>
          </TabsContent>

          <TabsContent value="financial" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="purchase-date">购买日期</Label>
                <Input id="purchase-date" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">购买价格（元）</Label>
                <Input id="price" type="number" min="0" placeholder="输入购买价格" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="supplier">供应商</Label>
                <Input id="supplier" placeholder="输入供应商名称" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="warranty">保修期（月）</Label>
                <Input id="warranty" type="number" min="0" placeholder="输入保修期" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="warranty-end">保修截止日期</Label>
                <Input id="warranty-end" type="date" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="depreciation">折旧率（%/年）</Label>
                <Input id="depreciation" type="number" min="0" max="100" defaultValue="20" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="financial-notes">财务备注</Label>
              <Textarea id="financial-notes" placeholder="输入财务相关备注信息" rows={3} />
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={() => onOpenChange(false)}>保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

