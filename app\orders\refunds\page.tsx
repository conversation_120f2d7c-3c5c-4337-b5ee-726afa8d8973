"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { MoreHorizontal, Eye, FileText, Filter, Search } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Checkbox } from "@/components/ui/checkbox"
import { RefundDetailDialog } from "@/components/refund-detail-dialog"
import { OrderAdvancedFilterDialog } from "@/components/order-advanced-filter-dialog"
import { DatePicker } from "@/components/date-picker"

export default function RefundsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [refundStatus, setRefundStatus] = useState("all")
  const [orderType, setOrderType] = useState("all")
  const [dateRange, setDateRange] = useState("30days")
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)

  const [showRefundDetail, setShowRefundDetail] = useState(false)
  const [selectedRefundId, setSelectedRefundId] = useState("")
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false)
  const [selectedRefunds, setSelectedRefunds] = useState<string[]>([])

  const [refunds] = useState([
    {
      id: "REF001",
      orderId: "ORD005",
      member: {
        name: "钱七",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "会员卡",
      product: "季卡",
      amount: "¥1,200",
      date: "2025-03-28 09:10",
      reason: "个人原因",
      status: "approved",
    },
    {
      id: "REF002",
      orderId: "ORD008",
      member: {
        name: "孙八",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "单次课程",
      product: "空中瑜伽体验",
      amount: "¥200",
      date: "2025-03-27 15:30",
      reason: "时间冲突",
      status: "pending",
    },
    {
      id: "REF003",
      orderId: "ORD012",
      member: {
        name: "周九",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "课程套餐",
      product: "高级瑜伽10次课",
      amount: "¥1,080",
      date: "2025-03-26 11:20",
      reason: "身体原因",
      status: "approved",
    },
    {
      id: "REF004",
      orderId: "ORD015",
      member: {
        name: "吴十",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "商品",
      product: "瑜伽垫",
      amount: "¥280",
      date: "2025-03-25 16:45",
      reason: "质量问题",
      status: "rejected",
    },
    {
      id: "REF005",
      orderId: "ORD018",
      member: {
        name: "郑十一",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "会员卡",
      product: "月卡",
      amount: "¥450",
      date: "2025-03-24 10:05",
      reason: "个人原因",
      status: "pending",
    },
  ])

  const handleViewRefundDetail = (refundId: string) => {
    setSelectedRefundId(refundId)
    setShowRefundDetail(true)
  }

  const handleApplyFilters = (filters: any) => {
    console.log("Applied filters:", filters)
    // 实际应用中，这里会根据筛选条件更新退款列表
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRefunds(refunds.map((refund) => refund.id))
    } else {
      setSelectedRefunds([])
    }
  }

  const handleSelectRefund = (refundId: string, checked: boolean) => {
    if (checked) {
      setSelectedRefunds([...selectedRefunds, refundId])
    } else {
      setSelectedRefunds(selectedRefunds.filter((id) => id !== refundId))
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">退款管理</h1>
        <Button variant="outline">
          <FileText className="mr-2 h-4 w-4" />
          导出数据
        </Button>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative w-full md:w-1/3">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索订单号、会员姓名"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-1 gap-4">
          <Select value={refundStatus} onValueChange={setRefundStatus}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="退款状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="pending">待审核</SelectItem>
              <SelectItem value="approved">已批准</SelectItem>
              <SelectItem value="rejected">已拒绝</SelectItem>
            </SelectContent>
          </Select>

          <Select value={orderType} onValueChange={setOrderType}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="订单类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="membership">会员卡</SelectItem>
              <SelectItem value="course">单次课程</SelectItem>
              <SelectItem value="package">课程套餐</SelectItem>
              <SelectItem value="product">商品</SelectItem>
            </SelectContent>
          </Select>

          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="custom">自定义</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button variant="outline" onClick={() => setShowAdvancedFilter(true)}>
          <Filter className="mr-2 h-4 w-4" />
          高级筛选
        </Button>
      </div>

      {dateRange === "custom" && (
        <div className="flex items-center gap-2">
          <DatePicker placeholder="开始日期" selected={startDate} onSelect={setStartDate} />
          <span>至</span>
          <DatePicker placeholder="结束日期" selected={endDate} onSelect={setEndDate} />
        </div>
      )}

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <Checkbox
                    checked={selectedRefunds.length === refunds.length && refunds.length > 0}
                    onCheckedChange={handleSelectAll}
                    aria-label="全选"
                  />
                </TableHead>
                <TableHead>退款编号</TableHead>
                <TableHead>订单编号</TableHead>
                <TableHead>会员</TableHead>
                <TableHead>订单类型</TableHead>
                <TableHead>商品/服务</TableHead>
                <TableHead>退款金额</TableHead>
                <TableHead>申请时间</TableHead>
                <TableHead>退款原因</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {refunds.map((refund) => (
                <TableRow key={refund.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedRefunds.includes(refund.id)}
                      onCheckedChange={(checked) => handleSelectRefund(refund.id, !!checked)}
                      aria-label={`选择退款 ${refund.id}`}
                    />
                  </TableCell>
                  <TableCell className="font-medium">{refund.id}</TableCell>
                  <TableCell>{refund.orderId}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={refund.member.avatar} alt={refund.member.name} />
                        <AvatarFallback>{refund.member.name[0]}</AvatarFallback>
                      </Avatar>
                      <span>{refund.member.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>{refund.type}</TableCell>
                  <TableCell>{refund.product}</TableCell>
                  <TableCell>{refund.amount}</TableCell>
                  <TableCell>{refund.date}</TableCell>
                  <TableCell>{refund.reason}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        refund.status === "approved"
                          ? "default"
                          : refund.status === "pending"
                            ? "outline"
                            : "destructive"
                      }
                    >
                      {refund.status === "approved" ? "已批准" : refund.status === "pending" ? "待审核" : "已拒绝"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleViewRefundDetail(refund.id)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        {refund.status === "pending" && (
                          <>
                            <DropdownMenuItem>批准退款</DropdownMenuItem>
                            <DropdownMenuItem className="text-red-600">拒绝退款</DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <RefundDetailDialog open={showRefundDetail} onOpenChange={setShowRefundDetail} refundId={selectedRefundId} />

      <OrderAdvancedFilterDialog
        open={showAdvancedFilter}
        onOpenChange={setShowAdvancedFilter}
        onApplyFilters={handleApplyFilters}
      />
    </div>
  )
}

