"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Co<PERSON>, CopyCheck } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface CopyCardTemplateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  card: {
    id: number | string
    name: string
    description: string
    price: string
    originalPrice: string
    validity: string
    limit: string
    color: string
    isTrialCard: boolean
  } | null
  onCopy: (newCard: any) => void
}

export function CopyCardTemplateDialog({
  open,
  onOpenChange,
  card,
  onCopy
}: CopyCardTemplateDialogProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // 新卡片信息
  const [newCardName, setNewCardName] = useState("")
  const [newCardDescription, setNewCardDescription] = useState("")
  const [newCardPrice, setNewCardPrice] = useState("")
  const [newCardOriginalPrice, setNewCardOriginalPrice] = useState("")
  const [newCardValidity, setNewCardValidity] = useState("")
  const [newCardLimit, setNewCardLimit] = useState("")
  const [newCardColor, setNewCardColor] = useState("#4f46e5")
  const [newCardIsTrialCard, setNewCardIsTrialCard] = useState(false)
  const [copySettings, setCopySettings] = useState(true)
  
  // 当对话框打开时，初始化表单数据
  const initFormData = () => {
    if (card) {
      setNewCardName(`${card.name} 副本`)
      setNewCardDescription(card.description)
      setNewCardPrice(card.price)
      setNewCardOriginalPrice(card.originalPrice)
      setNewCardValidity(card.validity)
      setNewCardLimit(card.limit)
      setNewCardColor(card.color)
      setNewCardIsTrialCard(card.isTrialCard)
    }
  }
  
  // 当对话框打开状态变化时，初始化表单数据
  const handleOpenChange = (open: boolean) => {
    if (open) {
      initFormData()
    }
    onOpenChange(open)
  }

  const handleCopy = async () => {
    if (!newCardName.trim()) {
      toast({
        title: "请输入会员卡名称",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    
    try {
      // 创建新卡片对象
      const newCard = {
        id: Date.now(), // 使用时间戳作为临时ID
        name: newCardName,
        description: newCardDescription,
        price: newCardPrice,
        originalPrice: newCardOriginalPrice,
        validity: newCardValidity,
        limit: newCardLimit,
        color: newCardColor,
        isTrialCard: newCardIsTrialCard,
        status: "inactive", // 默认为下架状态
        members: 0,
        salesCount: 0,
        revenue: "¥0",
      }
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 调用父组件的回调函数
      onCopy(newCard)
      
      toast({
        title: "复制成功",
        description: `已创建 ${newCardName} 会员卡`,
      })
      
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "复制失败",
        description: "创建会员卡副本时出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!card) return null

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Copy className="h-5 w-5 text-primary" />
            复制卡模板
          </DialogTitle>
          <DialogDescription>
            基于 <span className="font-medium" style={{ color: card.color }}>{card.name}</span> 创建新的会员卡
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="new-card-name">会员卡名称 <span className="text-red-500">*</span></Label>
              <Input
                id="new-card-name"
                value={newCardName}
                onChange={(e) => setNewCardName(e.target.value)}
                placeholder="输入新会员卡名称"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="new-card-color">卡片颜色</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="new-card-color"
                  type="color"
                  value={newCardColor}
                  onChange={(e) => setNewCardColor(e.target.value)}
                  className="w-12 h-10 p-1"
                />
                <Input
                  value={newCardColor}
                  onChange={(e) => setNewCardColor(e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="new-card-description">会员卡描述</Label>
            <Textarea
              id="new-card-description"
              value={newCardDescription}
              onChange={(e) => setNewCardDescription(e.target.value)}
              placeholder="输入会员卡描述"
              className="min-h-[80px]"
            />
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="new-card-price">售价</Label>
              <Input
                id="new-card-price"
                value={newCardPrice}
                onChange={(e) => setNewCardPrice(e.target.value)}
                placeholder="例如：¥1299"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="new-card-original-price">原价</Label>
              <Input
                id="new-card-original-price"
                value={newCardOriginalPrice}
                onChange={(e) => setNewCardOriginalPrice(e.target.value)}
                placeholder="例如：¥1599"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="new-card-validity">有效期</Label>
              <Input
                id="new-card-validity"
                value={newCardValidity}
                onChange={(e) => setNewCardValidity(e.target.value)}
                placeholder="例如：12个月"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="new-card-limit">使用限制</Label>
              <Input
                id="new-card-limit"
                value={newCardLimit}
                onChange={(e) => setNewCardLimit(e.target.value)}
                placeholder="例如：不限次数"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="new-card-is-trial" className="flex items-center gap-2">
              体验卡
              <span className="text-sm font-normal text-muted-foreground">
                标记为体验卡将在列表中特殊显示
              </span>
            </Label>
            <Switch
              id="new-card-is-trial"
              checked={newCardIsTrialCard}
              onCheckedChange={setNewCardIsTrialCard}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="copy-settings" className="flex items-center gap-2">
              复制高级设置
              <span className="text-sm font-normal text-muted-foreground">
                包括折扣规则、权益等设置
              </span>
            </Label>
            <Switch
              id="copy-settings"
              checked={copySettings}
              onCheckedChange={setCopySettings}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleCopy} disabled={isSubmitting} className="gap-2">
            {isSubmitting ? (
              <>复制中...</>
            ) : (
              <>
                <CopyCheck className="h-4 w-4" />
                创建副本
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
