"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { useState } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface MemberRetentionChartProps {
  className?: string
}

export function MemberRetentionChart({ className }: MemberRetentionChartProps) {
  const [cardType, setCardType] = useState("all")
  
  // 模拟会员留存率数据 - 所有卡类型
  const allCardRetentionData = [
    { month: 1, retention: 100 },
    { month: 2, retention: 92 },
    { month: 3, retention: 88 },
    { month: 4, retention: 85 },
    { month: 5, retention: 82 },
    { month: 6, retention: 80 },
    { month: 7, retention: 78 },
    { month: 8, retention: 76 },
    { month: 9, retention: 75 },
    { month: 10, retention: 74 },
    { month: 11, retention: 73 },
    { month: 12, retention: 72 },
  ]
  
  // 模拟会员留存率数据 - 年卡
  const yearCardRetentionData = [
    { month: 1, retention: 100 },
    { month: 2, retention: 95 },
    { month: 3, retention: 92 },
    { month: 4, retention: 90 },
    { month: 5, retention: 88 },
    { month: 6, retention: 87 },
    { month: 7, retention: 86 },
    { month: 8, retention: 85 },
    { month: 9, retention: 84 },
    { month: 10, retention: 83 },
    { month: 11, retention: 82 },
    { month: 12, retention: 80 },
  ]
  
  // 模拟会员留存率数据 - 季卡
  const quarterCardRetentionData = [
    { month: 1, retention: 100 },
    { month: 2, retention: 90 },
    { month: 3, retention: 82 },
    { month: 4, retention: 78 },
    { month: 5, retention: 75 },
    { month: 6, retention: 72 },
    { month: 7, retention: 70 },
    { month: 8, retention: 68 },
    { month: 9, retention: 66 },
    { month: 10, retention: 65 },
    { month: 11, retention: 64 },
    { month: 12, retention: 62 },
  ]
  
  // 模拟会员留存率数据 - 月卡
  const monthCardRetentionData = [
    { month: 1, retention: 100 },
    { month: 2, retention: 85 },
    { month: 3, retention: 75 },
    { month: 4, retention: 70 },
    { month: 5, retention: 65 },
    { month: 6, retention: 62 },
    { month: 7, retention: 60 },
    { month: 8, retention: 58 },
    { month: 9, retention: 56 },
    { month: 10, retention: 55 },
    { month: 11, retention: 54 },
    { month: 12, retention: 52 },
  ]
  
  // 根据选择的卡类型获取数据
  const getRetentionData = () => {
    switch (cardType) {
      case "year":
        return yearCardRetentionData
      case "quarter":
        return quarterCardRetentionData
      case "month":
        return monthCardRetentionData
      case "all":
      default:
        return allCardRetentionData
    }
  }
  
  const retentionData = getRetentionData()
  
  // 获取卡类型颜色
  const getCardTypeColor = () => {
    switch (cardType) {
      case "year":
        return "#4F46E5"
      case "quarter":
        return "#06B6D4"
      case "month":
        return "#10B981"
      case "all":
      default:
        return "#F59E0B"
    }
  }
  
  // 渲染折线图
  const renderLineChart = () => {
    const maxRetention = 100
    const chartHeight = 200
    const chartWidth = 100
    
    // 生成SVG路径
    const pathData = retentionData.map((point, index) => {
      const x = (index / (retentionData.length - 1)) * chartWidth
      const y = chartHeight - (point.retention / maxRetention) * chartHeight
      return `${index === 0 ? "M" : "L"} ${x} ${y}`
    }).join(" ")
    
    return (
      <div className="relative h-[220px] w-full">
        {/* 背景网格线 */}
        <div className="absolute inset-0 grid grid-cols-6 grid-rows-4">
          {Array.from({ length: 24 }).map((_, i) => (
            <div key={i} className="border-b border-r border-gray-100"></div>
          ))}
        </div>
        
        {/* Y轴标签 */}
        <div className="absolute -left-8 top-0 flex h-full flex-col justify-between py-2 text-xs text-muted-foreground">
          <div>100%</div>
          <div>75%</div>
          <div>50%</div>
          <div>25%</div>
          <div>0%</div>
        </div>
        
        {/* 折线图 */}
        <div className="absolute inset-0 h-[200px] w-full">
          <svg className="h-full w-full" viewBox="0 0 100 200" preserveAspectRatio="none">
            <path
              d={pathData}
              fill="none"
              stroke={getCardTypeColor()}
              strokeWidth="2"
              vectorEffect="non-scaling-stroke"
            />
            
            {/* 填充区域 */}
            <path
              d={`${pathData} L 100 200 L 0 200 Z`}
              fill={getCardTypeColor()}
              fillOpacity="0.1"
            />
          </svg>
          
          {/* 数据点 */}
          {retentionData.map((point, index) => {
            const x = (index / (retentionData.length - 1)) * 100
            const y = 200 - (point.retention / maxRetention) * 200
            
            return (
              <div
                key={index}
                className="absolute h-2 w-2 -translate-x-1 -translate-y-1 rounded-full"
                style={{ 
                  left: `${x}%`, 
                  top: `${y}px`,
                  backgroundColor: getCardTypeColor()
                }}
                title={`${point.month}个月后: ${point.retention}%`}
              ></div>
            )
          })}
        </div>
        
        {/* X轴标签 */}
        <div className="absolute bottom-0 left-0 flex w-full justify-between px-2 text-xs text-muted-foreground">
          {[1, 3, 6, 9, 12].map((month) => (
            <div key={month}>{month}个月</div>
          ))}
        </div>
      </div>
    )
  }
  
  // 渲染留存率指标
  const renderRetentionMetrics = () => {
    // 获取3个月、6个月和12个月的留存率
    const threeMonthRetention = retentionData.find(d => d.month === 3)?.retention || 0
    const sixMonthRetention = retentionData.find(d => d.month === 6)?.retention || 0
    const twelveMonthRetention = retentionData.find(d => d.month === 12)?.retention || 0
    
    return (
      <div className="mt-6 grid grid-cols-3 gap-4">
        <div className="rounded-lg border p-3 text-center">
          <div className="text-2xl font-bold text-primary">{threeMonthRetention}%</div>
          <div className="text-xs text-muted-foreground">3个月留存率</div>
        </div>
        <div className="rounded-lg border p-3 text-center">
          <div className="text-2xl font-bold text-primary">{sixMonthRetention}%</div>
          <div className="text-xs text-muted-foreground">6个月留存率</div>
        </div>
        <div className="rounded-lg border p-3 text-center">
          <div className="text-2xl font-bold text-primary">{twelveMonthRetention}%</div>
          <div className="text-xs text-muted-foreground">12个月留存率</div>
        </div>
      </div>
    )
  }
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle className="text-base">会员留存率</CardTitle>
          <CardDescription>会员卡续费率和留存分析</CardDescription>
        </div>
        <Select value={cardType} onValueChange={setCardType}>
          <SelectTrigger className="h-8 w-[120px]">
            <SelectValue placeholder="选择卡类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">所有卡</SelectItem>
            <SelectItem value="year">年卡</SelectItem>
            <SelectItem value="quarter">季卡</SelectItem>
            <SelectItem value="month">月卡</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        {renderLineChart()}
        {renderRetentionMetrics()}
      </CardContent>
    </Card>
  )
}
