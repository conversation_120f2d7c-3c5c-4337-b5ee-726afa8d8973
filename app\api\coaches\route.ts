import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// GET /api/coaches - 获取教练列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const keyword = searchParams.get('keyword');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');

    console.log('教练API接收参数:', { tenantId, keyword, status, page, pageSize });

    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID',
        data: null
      }, { status: 400 });
    }

    // 构建查询条件
    const where: any = {
      tenant_id: parseInt(tenantId)
    };

    // 关键词搜索
    if (keyword) {
      where.OR = [
        { name: { contains: keyword } },
        { phone: { contains: keyword } },
        { email: { contains: keyword } }
      ];
    }

    // 状态筛选
    if (status && status !== 'all') {
      where.status = status === 'active' ? 1 : 0;
    }

    // 查询总数
    const total = await prisma.coach.count({ where });

    // 查询教练列表
    const coaches = await prisma.coach.findMany({
      where,
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      },
      orderBy: { created_at: 'desc' },
      skip: (page - 1) * pageSize,
      take: pageSize
    });

    // 格式化返回数据
    const formattedCoaches = coaches.map(coach => ({
      id: coach.id,
      name: coach.name,
      phone: coach.phone,
      email: coach.email,
      avatar: coach.avatar,
      bio: coach.bio,
      specialties: coach.specialties ? JSON.parse(coach.specialties) : [],
      certifications: coach.certifications ? JSON.parse(coach.certifications) : [],
      experience: coach.experience,
      hourlyRate: coach.hourly_rate,
      status: coach.status === 1 ? 'active' : 'inactive',
      courseCount: coach._count.courses,
      createdAt: coach.created_at?.toISOString().split('T')[0],
      updatedAt: coach.updated_at?.toISOString().split('T')[0]
    }));

    console.log(`教练查询结果: ${formattedCoaches.length} 条记录，总计: ${total}`);

    return NextResponse.json({
      code: 200,
      msg: '获取教练列表成功',
      data: {
        list: formattedCoaches,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    });

  } catch (error) {
    console.error('获取教练列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取教练列表失败',
      data: null
    }, { status: 500 });
  }
}

// POST /api/coaches - 创建教练
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      tenantId,
      name,
      phone,
      email,
      avatar,
      bio,
      specialties,
      certifications,
      experience,
      hourlyRate,
      status
    } = body;

    console.log('创建教练API接收数据:', body);

    // 验证必填字段
    if (!tenantId || !name) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }

    // 检查手机号是否重复
    if (phone) {
      const existingCoach = await prisma.coach.findFirst({
        where: {
          tenant_id: parseInt(tenantId),
          phone: phone
        }
      });

      if (existingCoach) {
        return NextResponse.json({
          code: 400,
          msg: '手机号已存在',
          data: null
        }, { status: 400 });
      }
    }

    // 创建教练
    const coach = await prisma.coach.create({
      data: {
        tenant_id: parseInt(tenantId),
        name,
        phone,
        email,
        avatar,
        bio,
        specialties: specialties ? JSON.stringify(specialties) : null,
        certifications: certifications ? JSON.stringify(certifications) : null,
        experience: experience ? parseInt(experience) : null,
        hourly_rate: hourlyRate ? parseFloat(hourlyRate) : null,
        status: status === 'active' ? 1 : 0
      }
    });

    console.log('教练创建成功:', coach.id);

    return NextResponse.json({
      code: 200,
      msg: '创建教练成功',
      data: {
        id: coach.id,
        name: coach.name,
        phone: coach.phone,
        email: coach.email,
        avatar: coach.avatar,
        bio: coach.bio,
        specialties: coach.specialties ? JSON.parse(coach.specialties) : [],
        certifications: coach.certifications ? JSON.parse(coach.certifications) : [],
        experience: coach.experience,
        hourlyRate: coach.hourly_rate,
        status: coach.status === 1 ? 'active' : 'inactive',
        createdAt: coach.created_at?.toISOString().split('T')[0]
      }
    });

  } catch (error) {
    console.error('创建教练失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '创建教练失败',
      data: null
    }, { status: 500 });
  }
}
