import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// 定义教练接口
interface Coach {
  id: number
  name: string | null
  phone: string | null
  email: string | null
  avatar: string | null
  status: number | null
  _count?: {
    courses: number
  }
}

// GET /api/coaches - 获取教练列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const keyword = searchParams.get('keyword');
    const status = searchParams.get('status');

    // 验证租户ID
    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID',
        data: null
      }, { status: 400 });
    }

    // 模拟教练数据
    const mockCoaches: Coach[] = [
      {
        id: 1,
        name: '张教练',
        phone: '13800138001',
        email: '<EMAIL>',
        avatar: '/avatars/coach1.jpg',
        status: 1,
        _count: { courses: 5 }
      },
      {
        id: 2,
        name: '李教练',
        phone: '13800138002',
        email: '<EMAIL>',
        avatar: '/avatars/coach2.jpg',
        status: 1,
        _count: { courses: 3 }
      },
      {
        id: 3,
        name: '王教练',
        phone: '13800138003',
        email: '<EMAIL>',
        avatar: '/avatars/coach3.jpg',
        status: 1,
        _count: { courses: 4 }
      }
    ];

    // 根据关键词筛选
    let filteredCoaches = [...mockCoaches];
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      filteredCoaches = filteredCoaches.filter(
        coach => 
          coach.name?.toLowerCase().includes(lowerKeyword) || 
          coach.phone?.toLowerCase().includes(lowerKeyword) || 
          coach.email?.toLowerCase().includes(lowerKeyword)
      );
    }

    // 根据状态筛选
    if (status && status !== 'all') {
      const statusValue = status === 'active' ? 1 : 0;
      filteredCoaches = filteredCoaches.filter(coach => coach.status === statusValue);
    }

    // 格式化返回数据
    const formattedCoaches = filteredCoaches.map((coach: Coach) => ({
      id: coach.id.toString(),
      name: coach.name || '未命名教练',
      phone: coach.phone || '',
      email: coach.email || '',
      avatar: coach.avatar || '',
      status: coach.status === 1 ? 'active' : 'inactive',
      courseCount: coach._count?.courses || 0
    }));

    return NextResponse.json({
      code: 200,
      msg: '获取教练列表成功',
      data: {
        list: formattedCoaches,
        total: formattedCoaches.length
      }
    });

  } catch (error) {
    console.error('获取教练列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取教练列表失败',
      data: null
    }, { status: 500 });
  }
}

// POST /api/coaches - 创建教练
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      tenantId,
      name,
      phone,
      email,
      avatar,
      bio,
      specialties,
      certifications,
      experience,
      hourlyRate,
      status
    } = body;

    console.log('创建教练API接收数据:', body);

    // 验证必填字段
    if (!tenantId || !name) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段',
        data: null
      }, { status: 400 });
    }

    // 检查手机号是否重复
    if (phone) {
      const existingCoach = await prisma.coach.findFirst({
        where: {
          tenant_id: parseInt(tenantId),
          phone: phone
        }
      });

      if (existingCoach) {
        return NextResponse.json({
          code: 400,
          msg: '手机号已存在',
          data: null
        }, { status: 400 });
      }
    }

    // 创建教练
    const coach = await prisma.coach.create({
      data: {
        tenant_id: parseInt(tenantId),
        name,
        phone,
        email,
        avatar,
        bio,
        specialties: specialties ? JSON.stringify(specialties) : null,
        certifications: certifications ? JSON.stringify(certifications) : null,
        experience: experience ? parseInt(experience) : null,
        hourly_rate: hourlyRate ? parseFloat(hourlyRate) : null,
        status: status === 'active' ? 1 : 0
      }
    });

    console.log('教练创建成功:', coach.id);

    return NextResponse.json({
      code: 200,
      msg: '创建教练成功',
      data: {
        id: coach.id,
        name: coach.name,
        phone: coach.phone,
        email: coach.email,
        avatar: coach.avatar,
        bio: coach.bio,
        specialties: coach.specialties ? JSON.parse(coach.specialties) : [],
        certifications: coach.certifications ? JSON.parse(coach.certifications) : [],
        experience: coach.experience,
        hourlyRate: coach.hourly_rate,
        status: coach.status === 1 ? 'active' : 'inactive',
        createdAt: coach.created_at?.toISOString().split('T')[0]
      }
    });

  } catch (error) {
    console.error('创建教练失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '创建教练失败',
      data: null
    }, { status: 500 });
  }
}
