import { NextRequest, NextResponse } from 'next/server';
import { courseTypeService } from '@/services/course-type-service';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id, 10);
    
    // 获取课程类型
    const courseType = courseTypeService.getById(id);
    
    if (!courseType) {
      return NextResponse.json(
        { error: '课程类型不存在' },
        { status: 404 }
      );
    }
    
    // 模拟关联课程的详细信息
    const relatedCourses = Array.from({ length: courseType.courses || 0 }, (_, i) => ({
      id: i + 1,
      name: `${courseType.name}示例课程 ${i + 1}`,
      instructor: `教练 ${(i % 5) + 1}`,
      duration: 60 + (i % 4) * 15, // 60, 75, 90, 105分钟
      startDate: new Date(Date.now() + i * 86400000).toISOString().split('T')[0], // 从今天开始，每天一个
      enrollments: Math.floor(Math.random() * 20) + 5,
      status: i % 10 === 0 ? 'inactive' : 'active' // 每10个课程有1个停用
    }));
    
    // 构建扩展的类型详情
    const typeDetail = {
      ...courseType,
      relatedCourses,
      stats: {
        totalEnrollments: relatedCourses.reduce((sum, course) => sum + course.enrollments, 0),
        activeCourses: relatedCourses.filter(course => course.status === 'active').length,
        inactiveCourses: relatedCourses.filter(course => course.status === 'inactive').length,
        averageDuration: relatedCourses.length 
          ? Math.round(relatedCourses.reduce((sum, course) => sum + course.duration, 0) / relatedCourses.length) 
          : 0
      }
    };
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: typeDetail
    });
  } catch (error) {
    console.error('获取课程类型详情错误:', error);
    return NextResponse.json(
      { error: '获取课程类型详情失败', details: String(error) },
      { status: 500 }
    );
  }
} 