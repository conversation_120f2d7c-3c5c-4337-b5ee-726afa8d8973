// 通过API创建课程数据
const courses = [
  {
    tenantId: 1,
    title: '瑜伽基础入门',
    description: '适合初学者的瑜伽基础课程，学习基本体式和呼吸方法',
    price: 80,
    cover: '',
    type: 7, // 基础瑜伽
    content: '本课程将教授基础瑜伽体式，包括山式、下犬式、战士式等经典动作，同时学习正确的呼吸技巧。',
    duration: 90,
    level: '1',
    coachId: '张教练',
    time: '周一、三、五 19:00-20:30',
    venue: '1号瑜伽室',
    capacity: 15
  },
  {
    tenantId: 1,
    title: '高级流瑜伽',
    description: '连贯流畅的瑜伽序列，提升力量和灵活性',
    price: 120,
    cover: '',
    type: 8, // 进阶瑜伽
    content: '流瑜伽是一种动态的瑜伽练习，通过连贯的动作序列来提升身体的力量、柔韧性和平衡感。',
    duration: 75,
    level: '3',
    coachId: '李教练',
    time: '周二、四 18:30-19:45',
    venue: '2号瑜伽室',
    capacity: 10
  },
  {
    tenantId: 1,
    title: '阴瑜伽放松',
    description: '深度放松的阴瑜伽，舒缓身心压力',
    price: 85,
    cover: '',
    type: 9, // 阴瑜伽
    content: '阴瑜伽是一种缓慢、静态的瑜伽练习，通过长时间保持体式来深度拉伸筋膜和结缔组织。',
    duration: 75,
    level: '2',
    coachId: '王教练',
    time: '周六 14:00-15:15',
    venue: '3号瑜伽室',
    capacity: 12
  },
  {
    tenantId: 1,
    title: '孕产瑜伽特训',
    description: '专为孕妇设计的安全瑜伽练习',
    price: 100,
    cover: '',
    type: 10, // 孕产瑜伽
    content: '专门为孕期女性设计的瑜伽课程，帮助缓解孕期不适，为分娩做准备。',
    duration: 60,
    level: '1',
    coachId: '赵教练',
    time: '周日 10:00-11:00',
    venue: '4号瑜伽室',
    capacity: 8
  },
  {
    tenantId: 1,
    title: '空中瑜伽体验',
    description: '空中瑜伽入门体验课程',
    price: 150,
    cover: '',
    type: 11, // 空中瑜伽
    content: '使用空中吊床进行的瑜伽练习，结合传统瑜伽体式和空中技巧，带来全新的练习体验。',
    duration: 60,
    level: '2',
    coachId: '陈教练',
    time: '周六 16:00-17:00',
    venue: '空中瑜伽室',
    capacity: 6
  },
  {
    tenantId: 1,
    title: '私教一对一',
    description: '个性化一对一私教课程',
    price: 300,
    cover: '',
    type: 12, // 私教课
    content: '根据个人需求定制的一对一瑜伽私教课程，提供个性化指导和练习方案。',
    duration: 60,
    level: '5',
    coachId: '刘教练',
    time: '预约制',
    venue: '私教室',
    capacity: 1
  }
];

async function createCourses() {
  console.log('开始创建课程数据...');
  
  for (const courseData of courses) {
    try {
      const response = await fetch('http://localhost:3005/api/courses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(courseData)
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        console.log(`✓ 创建课程成功: ${courseData.title}`);
      } else {
        console.log(`✗ 创建课程失败: ${courseData.title} - ${result.msg}`);
      }
    } catch (error) {
      console.error(`✗ 创建课程出错: ${courseData.title}`, error.message);
    }
  }
  
  console.log('课程数据创建完成!');
}

createCourses();
