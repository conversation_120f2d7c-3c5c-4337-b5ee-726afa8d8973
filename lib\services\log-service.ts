import axiosInstance from "../axios-config"

// 操作日志类型
export interface OperationLog {
  id?: string
  userId: string
  userName: string
  userAvatar?: string
  module: string
  action: string
  details: string
  ip?: string
  timestamp?: string
  status: "success" | "failed" | "warning"
  userAgent?: string
  metadata?: Record<string, any>
}

// 日志查询参数
export interface LogQueryParams {
  page?: number
  pageSize?: number
  keyword?: string
  module?: string
  status?: string
  startDate?: string
  endDate?: string
  userId?: string
}

// 日志服务
class LogService {
  // 记录操作日志
  async logOperation(logData: Omit<OperationLog, "id" | "ip" | "timestamp" | "userAgent">): Promise<void> {
    try {
      // 在实际应用中，这里会调用API将日志发送到服务器
      // 这里我们只是模拟这个过程
      console.log("记录操作日志:", logData)
      
      // 实际API调用示例
      // await axiosInstance.post("/api/logs/operation", logData)
    } catch (error) {
      console.error("记录操作日志失败:", error)
    }
  }

  // 获取操作日志列表
  async getOperationLogs(params: LogQueryParams = {}): Promise<{
    logs: OperationLog[]
    total: number
  }> {
    try {
      // 在实际应用中，这里会调用API获取日志列表
      // 这里我们只是模拟这个过程
      console.log("获取操作日志列表:", params)
      
      // 实际API调用示例
      // const response = await axiosInstance.get("/api/logs/operation", { params })
      // return response.data
      
      // 模拟返回数据
      return {
        logs: [],
        total: 0
      }
    } catch (error) {
      console.error("获取操作日志列表失败:", error)
      return {
        logs: [],
        total: 0
      }
    }
  }

  // 获取日志详情
  async getLogDetail(logId: string): Promise<OperationLog | null> {
    try {
      // 在实际应用中，这里会调用API获取日志详情
      // 这里我们只是模拟这个过程
      console.log("获取日志详情:", logId)
      
      // 实际API调用示例
      // const response = await axiosInstance.get(`/api/logs/operation/${logId}`)
      // return response.data
      
      // 模拟返回数据
      return null
    } catch (error) {
      console.error("获取日志详情失败:", error)
      return null
    }
  }

  // 导出日志
  async exportLogs(params: LogQueryParams = {}): Promise<Blob | null> {
    try {
      // 在实际应用中，这里会调用API导出日志
      // 这里我们只是模拟这个过程
      console.log("导出日志:", params)
      
      // 实际API调用示例
      // const response = await axiosInstance.get("/api/logs/operation/export", {
      //   params,
      //   responseType: "blob"
      // })
      // return response.data
      
      // 模拟返回数据
      return null
    } catch (error) {
      console.error("导出日志失败:", error)
      return null
    }
  }

  // 清除日志
  async clearLogs(params: {
    module?: string
    before?: string
  } = {}): Promise<boolean> {
    try {
      // 在实际应用中，这里会调用API清除日志
      // 这里我们只是模拟这个过程
      console.log("清除日志:", params)
      
      // 实际API调用示例
      // const response = await axiosInstance.delete("/api/logs/operation", { params })
      // return response.data.success
      
      // 模拟返回数据
      return true
    } catch (error) {
      console.error("清除日志失败:", error)
      return false
    }
  }
}

// 创建日志服务实例
export const logService = new LogService()

// 创建一个简单的日志记录函数，方便在组件中使用
export function logOperation(
  module: string,
  action: string,
  details: string,
  status: "success" | "failed" | "warning" = "success",
  metadata?: Record<string, any>
): void {
  // 从localStorage或其他地方获取当前用户信息
  const userJson = localStorage.getItem("currentUser")
  let userId = "unknown"
  let userName = "未知用户"
  let userAvatar = undefined
  
  if (userJson) {
    try {
      const user = JSON.parse(userJson)
      userId = user.id || "unknown"
      userName = user.name || user.username || "未知用户"
      userAvatar = user.avatar
    } catch (e) {
      console.error("解析用户信息失败:", e)
    }
  }
  
  // 记录日志
  logService.logOperation({
    userId,
    userName,
    userAvatar,
    module,
    action,
    details,
    status,
    metadata
  })
}

// 创建一个React Hook，用于在组件中记录操作日志
export function useLogOperation() {
  return {
    logOperation,
    
    // 记录成功操作
    logSuccess: (module: string, action: string, details: string, metadata?: Record<string, any>) => {
      logOperation(module, action, details, "success", metadata)
    },
    
    // 记录失败操作
    logFailure: (module: string, action: string, details: string, metadata?: Record<string, any>) => {
      logOperation(module, action, details, "failed", metadata)
    },
    
    // 记录警告操作
    logWarning: (module: string, action: string, details: string, metadata?: Record<string, any>) => {
      logOperation(module, action, details, "warning", metadata)
    }
  }
}
