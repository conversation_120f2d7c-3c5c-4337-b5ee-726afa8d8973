"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { FileText, Plus, Trash2, User, Building2, Calendar, Eye } from "lucide-react"
import { ContractTemplatePreview } from "./contract-template-preview"

interface CreateContractDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  templates: any[]
}

export function CreateContractDialog({ open, onOpenChange, templates }: CreateContractDialogProps) {
  const [activeTab, setActiveTab] = useState("template")
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [contractData, setContractData] = useState({
    title: "",
    templateId: "",
    parties: [
      { type: "COMPANY", name: "静心瑜伽馆", idType: "", idNumber: "", mobile: "", email: "", companyName: "静心瑜伽馆", legalRepresentative: "" },
      { type: "PERSON", name: "", idType: "ID_CARD", idNumber: "", mobile: "", email: "", companyName: "", legalRepresentative: "" }
    ],
    variables: {},
    expireTime: ""
  })

  // 添加签署方
  const addParty = () => {
    setContractData({
      ...contractData,
      parties: [
        ...contractData.parties,
        { type: "PERSON", name: "", idType: "ID_CARD", idNumber: "", mobile: "", email: "", companyName: "", legalRepresentative: "" }
      ]
    })
  }

  // 移除签署方
  const removeParty = (index: number) => {
    if (contractData.parties.length <= 2) {
      toast({
        title: "无法删除",
        description: "合同至少需要两个签署方",
        variant: "destructive"
      })
      return
    }

    const newParties = [...contractData.parties]
    newParties.splice(index, 1)
    setContractData({
      ...contractData,
      parties: newParties
    })
  }

  // 更新签署方信息
  const updateParty = (index: number, field: string, value: string) => {
    const newParties = [...contractData.parties]
    newParties[index] = {
      ...newParties[index],
      [field]: value
    }
    setContractData({
      ...contractData,
      parties: newParties
    })
  }

  // 更新签署方类型
  const updatePartyType = (index: number, type: "COMPANY" | "PERSON") => {
    const newParties = [...contractData.parties]
    newParties[index] = {
      ...newParties[index],
      type
    }
    setContractData({
      ...contractData,
      parties: newParties
    })
  }

  // 选择模板
  const handleSelectTemplate = (templateId: string) => {
    setSelectedTemplate(templateId)
    setContractData({
      ...contractData,
      templateId
    })
  }

  // 预览模板
  const handlePreviewTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId)
    if (template) {
      // 根据模板名称构建URL
      let templateFileName = '';

      switch (template.name) {
        case "标准会员协议":
          templateFileName = "standard-member-agreement.html";
          break;
        case "会员卡标准协议":
          templateFileName = "membership-card-agreement.html";
          break;
        case "标准教练合同":
          templateFileName = "coach-employment-contract.html";
          break;
        case "场地租赁标准协议":
          templateFileName = "venue-rental-agreement.html";
          break;
        case "设备采购标准协议":
          templateFileName = "equipment-purchase-agreement.html";
          break;
        case "消费型股东协议":
          templateFileName = "consumer-shareholder-agreement.html";
          break;
        case "投资型股东协议":
          templateFileName = "investor-shareholder-agreement.html";
          break;
        case "资源型股东协议":
          templateFileName = "resource-shareholder-agreement.html";
          break;
        case "员工型股东协议":
          templateFileName = "employee-shareholder-agreement.html";
          break;
        case "联盟型股东协议":
          templateFileName = "alliance-shareholder-agreement.html";
          break;
        default:
          // 如果没有匹配的模板，尝试使用原来的方法
          templateFileName = `${template.name.toLowerCase().replace(/\s+/g, '-')}.html`;
      }

      const templateUrl = `/contract-templates/${templateFileName}`;
      window.open(templateUrl, '_blank');
    } else {
      toast({
        title: "预览不可用",
        description: "该模板暂无预览",
        variant: "destructive"
      })
    }
  }

  // 创建合同
  const handleCreateContract = async () => {
    try {
      // 验证必填字段
      if (!contractData.title || !contractData.templateId) {
        toast({
          title: "表单验证失败",
          description: "请填写所有必填字段",
          variant: "destructive"
        })
        return
      }

      // 验证签署方信息
      for (const party of contractData.parties) {
        if (!party.name) {
          toast({
            title: "表单验证失败",
            description: "请填写所有签署方的姓名/公司名称",
            variant: "destructive"
          })
          return
        }

        if (party.type === "PERSON" && !party.mobile) {
          toast({
            title: "表单验证失败",
            description: "请填写个人签署方的手机号码",
            variant: "destructive"
          })
          return
        }
      }

      // 模拟API调用
      console.log("创建合同:", contractData)

      // 显示成功提示
      toast({
        title: "创建成功",
        description: "合同已成功创建，可以在合同列表中查看",
      })

      // 关闭对话框
      onOpenChange(false)

      // 重置表单
      setContractData({
        title: "",
        templateId: "",
        parties: [
          { type: "COMPANY", name: "静心瑜伽馆", idType: "", idNumber: "", mobile: "", email: "", companyName: "静心瑜伽馆", legalRepresentative: "" },
          { type: "PERSON", name: "", idType: "ID_CARD", idNumber: "", mobile: "", email: "", companyName: "", legalRepresentative: "" }
        ],
        variables: {},
        expireTime: ""
      })
      setSelectedTemplate(null)
      setActiveTab("template")
    } catch (error) {
      console.error("创建合同失败:", error)
      toast({
        title: "创建失败",
        description: "创建合同时发生错误，请重试",
        variant: "destructive"
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>创建电子合同</DialogTitle>
          <DialogDescription>
            选择合同模板并填写必要信息，创建一个新的电子合同
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="template">选择模板</TabsTrigger>
            <TabsTrigger value="basic" disabled={!selectedTemplate}>基本信息</TabsTrigger>
            <TabsTrigger value="parties" disabled={!selectedTemplate}>签署方</TabsTrigger>
          </TabsList>

          {/* 选择模板 */}
          <TabsContent value="template" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <Card
                  key={template.id}
                  className={`cursor-pointer transition-all ${selectedTemplate === template.id ? 'border-primary ring-2 ring-primary/20' : 'hover:border-primary/50'}`}
                  onClick={() => handleSelectTemplate(template.id)}
                >
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">{template.name}</CardTitle>
                    <CardDescription className="text-xs">{template.category}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">{template.description}</p>
                  </CardContent>
                  <CardFooter className="pt-0 flex justify-between gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePreviewTemplate(template.id);
                      }}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      预览
                    </Button>
                    <Button
                      variant={selectedTemplate === template.id ? "default" : "outline"}
                      size="sm"
                      className="flex-1"
                      onClick={() => handleSelectTemplate(template.id)}
                    >
                      {selectedTemplate === template.id ? "已选择" : "选择"}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* 基本信息 */}
          <TabsContent value="basic" className="space-y-4 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">
                    合同标题 <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="title"
                    placeholder="请输入合同标题"
                    value={contractData.title}
                    onChange={(e) => setContractData({ ...contractData, title: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expireTime">签署截止时间</Label>
                  <Input
                    id="expireTime"
                    type="datetime-local"
                    value={contractData.expireTime}
                    onChange={(e) => setContractData({ ...contractData, expireTime: e.target.value })}
                  />
                  <p className="text-sm text-muted-foreground">如不设置，默认为创建后30天</p>
                </div>
              </div>

              <div className="border rounded-lg p-4">
                <h3 className="text-lg font-medium mb-4">模板预览</h3>
                {selectedTemplate && (
                  <ContractTemplatePreview
                    previewUrl={(() => {
                      const template = templates.find(t => t.id === selectedTemplate);
                      if (!template) return null;

                      // 根据模板名称构建URL
                      let templateFileName = '';

                      switch (template.name) {
                        case "标准会员协议":
                          templateFileName = "standard-member-agreement.html";
                          break;
                        case "会员卡标准协议":
                          templateFileName = "membership-card-agreement.html";
                          break;
                        case "标准教练合同":
                          templateFileName = "coach-employment-contract.html";
                          break;
                        case "场地租赁标准协议":
                          templateFileName = "venue-rental-agreement.html";
                          break;
                        case "设备采购标准协议":
                          templateFileName = "equipment-purchase-agreement.html";
                          break;
                        case "消费型股东协议":
                          templateFileName = "consumer-shareholder-agreement.html";
                          break;
                        case "投资型股东协议":
                          templateFileName = "investor-shareholder-agreement.html";
                          break;
                        case "资源型股东协议":
                          templateFileName = "resource-shareholder-agreement.html";
                          break;
                        case "员工型股东协议":
                          templateFileName = "employee-shareholder-agreement.html";
                          break;
                        case "联盟型股东协议":
                          templateFileName = "alliance-shareholder-agreement.html";
                          break;
                        default:
                          // 如果没有匹配的模板，尝试使用原来的方法
                          templateFileName = `${template.name.toLowerCase().replace(/\s+/g, '-')}.html`;
                      }

                      return `/contract-templates/${templateFileName}`;
                    })()}
                    templateName={templates.find(t => t.id === selectedTemplate)?.name || ""}
                    onPreview={() => handlePreviewTemplate(selectedTemplate)}
                  />
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-4">
              <Button variant="outline" onClick={() => setActiveTab("template")}>上一步</Button>
              <Button onClick={() => setActiveTab("parties")}>下一步</Button>
            </div>
          </TabsContent>

          {/* 签署方 */}
          <TabsContent value="parties" className="space-y-4 mt-4">
            <div className="space-y-4">
              {contractData.parties.map((party, index) => (
                <Card key={index}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-base">签署方 {index + 1}</CardTitle>
                      {index > 0 && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeParty(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <RadioGroup
                      value={party.type}
                      onValueChange={(value) => updatePartyType(index, value as "COMPANY" | "PERSON")}
                      className="flex space-x-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="COMPANY" id={`company-${index}`} />
                        <Label htmlFor={`company-${index}`}>企业</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="PERSON" id={`person-${index}`} />
                        <Label htmlFor={`person-${index}`}>个人</Label>
                      </div>
                    </RadioGroup>

                    {party.type === "COMPANY" ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>
                            公司名称 <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            placeholder="请输入公司名称"
                            value={party.companyName || party.name}
                            onChange={(e) => {
                              updateParty(index, "companyName", e.target.value)
                              updateParty(index, "name", e.target.value)
                            }}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>法定代表人</Label>
                          <Input
                            placeholder="请输入法定代表人姓名"
                            value={party.legalRepresentative}
                            onChange={(e) => updateParty(index, "legalRepresentative", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>统一社会信用代码</Label>
                          <Input
                            placeholder="请输入统一社会信用代码"
                            value={party.idNumber}
                            onChange={(e) => updateParty(index, "idNumber", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>联系电话</Label>
                          <Input
                            placeholder="请输入联系电话"
                            value={party.mobile}
                            onChange={(e) => updateParty(index, "mobile", e.target.value)}
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>
                            姓名 <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            placeholder="请输入姓名"
                            value={party.name}
                            onChange={(e) => updateParty(index, "name", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>
                            手机号码 <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            placeholder="请输入手机号码"
                            value={party.mobile}
                            onChange={(e) => updateParty(index, "mobile", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>身份证号码</Label>
                          <Input
                            placeholder="请输入身份证号码"
                            value={party.idNumber}
                            onChange={(e) => updateParty(index, "idNumber", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>电子邮箱</Label>
                          <Input
                            placeholder="请输入电子邮箱"
                            value={party.email}
                            onChange={(e) => updateParty(index, "email", e.target.value)}
                          />
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}

              <Button variant="outline" className="w-full" onClick={addParty}>
                <Plus className="mr-2 h-4 w-4" />
                添加签署方
              </Button>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setActiveTab("basic")}>上一步</Button>
                <Button onClick={handleCreateContract}>创建合同</Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
