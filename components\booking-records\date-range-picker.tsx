"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface BookingDateRangePickerProps {
  className?: string
  dateRange: {
    from: Date | undefined
    to: Date | undefined
  }
  onDateRangeChange: (range: { from: Date | undefined; to: Date | undefined }) => void
  placeholder?: string
}

export function BookingDateRangePicker({
  className,
  dateRange,
  onDateRangeChange,
  placeholder = "选择日期范围"
}: BookingDateRangePickerProps) {
  // 使用状态来存储窗口宽度
  const [numberOfMonths, setNumberOfMonths] = React.useState(2)

  // 在客户端渲染时检测窗口宽度
  React.useEffect(() => {
    const handleResize = () => {
      setNumberOfMonths(window.innerWidth > 768 ? 2 : 1)
    }

    // 初始化
    handleResize()

    // 添加事件监听器
    window.addEventListener('resize', handleResize)

    // 清理
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 处理日期选择
  const handleSelect = (selectedDateRange: DateRange | undefined) => {
    onDateRangeChange({
      from: selectedDateRange?.from,
      to: selectedDateRange?.to
    })
  }

  return (
    <div className={cn("grid w-full", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date-range"
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !dateRange.from && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {dateRange.from ? (
              dateRange.to ? (
                <>
                  {format(dateRange.from, "yyyy-MM-dd")} -{" "}
                  {format(dateRange.to, "yyyy-MM-dd")}
                </>
              ) : (
                format(dateRange.from, "yyyy-MM-dd")
              )
            ) : (
              <span>{placeholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 shadow-lg border-gray-200" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={dateRange.from}
            selected={{
              from: dateRange.from,
              to: dateRange.to
            }}
            onSelect={handleSelect}
            numberOfMonths={numberOfMonths}
            className="rounded-md border shadow-md"
          />
          <div className="p-3 border-t border-border">
            <div className="flex items-center justify-between mb-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDateRangeChange({ from: undefined, to: undefined })}
              >
                清除
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  const today = new Date()
                  onDateRangeChange({
                    from: today,
                    to: today
                  })
                }}
              >
                今天
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date()
                  const yesterday = new Date(today)
                  yesterday.setDate(yesterday.getDate() - 1)
                  onDateRangeChange({
                    from: yesterday,
                    to: yesterday
                  })
                }}
              >
                昨天
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date()
                  const sevenDaysAgo = new Date(today)
                  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6)
                  onDateRangeChange({
                    from: sevenDaysAgo,
                    to: today
                  })
                }}
              >
                最近7天
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const today = new Date()
                  const thirtyDaysAgo = new Date(today)
                  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 29)
                  onDateRangeChange({
                    from: thirtyDaysAgo,
                    to: today
                  })
                }}
              >
                最近30天
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
