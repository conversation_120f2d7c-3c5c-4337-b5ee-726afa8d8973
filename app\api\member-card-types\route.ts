import { NextRequest, NextResponse } from 'next/server';

// 获取会员卡类型列表
export async function GET(request: NextRequest) {
  try {
    // 模拟会员卡类型数据
    const cardTypes = [
      { id: 1, name: '年卡', color: '#4285F4' },
      { id: 2, name: '季卡', color: '#34A853' },
      { id: 3, name: '月卡', color: '#FBBC05' },
      { id: 4, name: '体验卡', color: '#EA4335' },
      { id: 5, name: '次卡10次', color: '#673AB7' },
      { id: 6, name: '次卡20次', color: '#3F51B5' },
      { id: 7, name: '私教卡', color: '#9C27B0' },
      { id: 8, name: '储值卡500', color: '#00BCD4' }
    ];
    
    return NextResponse.json({
      code: 0,
      data: cardTypes,
      msg: '获取会员卡类型列表成功'
    });
  } catch (error) {
    console.error('获取会员卡类型列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取会员卡类型列表失败',
      data: null
    }, { status: 500 });
  }
} 