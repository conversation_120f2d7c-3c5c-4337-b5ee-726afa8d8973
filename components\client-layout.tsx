"use client"

import { usePathname } from 'next/navigation'
import { Sidebar } from "@/components/sidebar"
import { TopNav } from "@/components/top-nav"
import { OnboardingGuide } from "@/components/onboarding-guide"
import { useAuth } from "@/contexts/auth-context"

// 判断是否是公共页面（不需要显示侧边栏和顶部导航栏）
function isPublicPage(pathname: string): boolean {
  const publicPaths = ["/home", "/login"];
  return publicPaths.some(path => pathname === path || pathname.startsWith(`${path}/`));
}

// 客户端布局组件，用于根据路径判断显示哪种布局
export function ClientLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isPublic = isPublicPage(pathname);
  const { isAuthenticated } = useAuth(); // 获取用户登录状态

  // 渲染内容部分
  const renderContent = () => {
    if (isPublic) {
      // 公共页面不显示侧边栏和顶部导航栏
      return <>{children}</>;
    }

    // 后台管理页面显示完整布局
    return (
      <div className="flex h-screen overflow-hidden">
        <Sidebar />
        <div className="flex flex-col flex-1 overflow-hidden">
          <TopNav />
          <main className="flex-1 overflow-y-auto overflow-x-hidden p-6">
            {children}
          </main>
        </div>
      </div>
    );
  };

  return (
    <>
      {renderContent()}
      
      {/* 只在用户登录后才显示新手指引 */}
      {isAuthenticated && <OnboardingGuide />}
    </>
  );
}
