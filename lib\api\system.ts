import axiosInstance from '../axios-config';

// 用户管理接口类型定义
export interface UserInfo {
  id: string;
  username: string;
  name: string;          // 显示名称
  nickname?: string;
  avatar?: string;
  email: string;
  phone?: string;
  role: string;
  status: string;
  lastLoginTime?: string;
  lastLogin?: string;    // 最后登录时间
  loginCount?: number;   // 登录次数
  department?: string;   // 部门
  createTime: string;
  updateTime: string;
}

export interface UserListParams {
  page: number;
  pageSize: number;
  keyword?: string;
  role?: string;
  status?: 'active' | 'disabled' | undefined;
  sortBy?: string;
  department?: string;
}

export interface CreateUserParams {
  username: string;
  password: string;
  nickname?: string;
  email: string;
  phone?: string;
  role: string;
  status: string;
  avatar?: string;
}

export interface UpdateUserParams {
  nickname?: string;
  email?: string;
  phone?: string;
  role?: string;
  status?: string;
  avatar?: string;
}

// 用户管理API
export const systemApi = {
  // 获取用户列表
  getUsers: async (params: UserListParams) => {
    try {
      // 确保分页参数为数字类型
      const queryParams = {
        ...params,
        page: Number(params.page),
        pageSize: Number(params.pageSize),
        keyword: params.keyword || undefined,
        status: params.status || undefined
      };
      
      console.log('发送请求参数:', queryParams);
      
      // 真实API调用
      const response = await axiosInstance.get('/api/employees', { 
        params: queryParams
      });
      
      console.log('API响应:', response.data);
      
      return response.data;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  },

  // 创建用户
  createUser: async (data: CreateUserParams) => {
    try {
      const response = await axiosInstance.post('/api/employees', data);
      return response.data;
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  },

  // 更新用户信息
  updateUser: async (id: string, data: UpdateUserParams) => {
    try {
      const response = await axiosInstance.put(`/api/employees/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('更新用户失败:', error);
      throw error;
    }
  },

  // 删除用户
  deleteUser: async (id: string | undefined) => {
    if (!id) {
      throw new Error('用户ID不能为空');
    }
    try {
      const response = await axiosInstance.delete(`/api/employees/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  },

  // 批量删除用户
  batchDeleteUsers: async (ids: string[]) => {
    try {
      const response = await axiosInstance.delete('/api/employees/batch', { data: { ids } });
      return response.data;
    } catch (error) {
      console.error('批量删除用户失败:', error);
      throw error;
    }
  },

  // 重置用户密码
  resetUserPassword: async (id: string, newPassword: string) => {
    try {
      const response = await axiosInstance.post(`/api/employees/${id}/reset-password`, { newPassword });
      return response.data;
    } catch (error) {
      console.error('重置用户密码失败:', error);
      throw error;
    }
  },

  // 修改用户状态
  updateUserStatus: async (id: string | undefined, status: string) => {
    if (!id) {
      throw new Error('用户ID不能为空');
    }
    try {
      const response = await axiosInstance.patch(`/api/employees/${id}/status`, { status });
      return response.data;
    } catch (error) {
      console.error('修改用户状态失败:', error);
      throw error;
    }
  },

  // 获取用户详情
  getUserDetail: async (id: string) => {
    try {
      const response = await axiosInstance.get(`/api/employees/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取用户详情失败:', error);
      throw error;
    }
  },

  // 导出用户数据
  exportUsers: async (params: UserListParams) => {
    try {
      const response = await axiosInstance.get('/api/employees/export', { 
        params,
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      console.error('导出用户数据失败:', error);
      throw error;
    }
  },

  // 导入用户数据
  importUsers: async (file: File, updateExisting: boolean = false) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('updateExisting', String(updateExisting));
      
      const response = await axiosInstance.post('/api/employees/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('导入用户数据失败:', error);
      throw error;
    }
  }
}; 