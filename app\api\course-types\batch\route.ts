import { NextRequest, NextResponse } from 'next/server';
import { courseTypeService } from '@/services/course-type-service';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // 验证请求数据
    if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
      return NextResponse.json(
        { error: '无效的请求数据：缺少课程类型ID列表' },
        { status: 400 }
      );
    }
    
    if (!data.action) {
      return NextResponse.json(
        { error: '无效的请求数据：缺少操作类型' },
        { status: 400 }
      );
    }
    
    const { ids, action, value } = data;
    let results: any[] = [];
    
    switch (action) {
      case 'updateStatus':
        // 验证状态值
        if (value !== 'active' && value !== 'inactive') {
          return NextResponse.json(
            { error: '无效的状态值' },
            { status: 400 }
          );
        }
        
        // 批量更新状态
        results = ids.map((id: string | number) => {
          try {
            const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
            const type = courseTypeService.getById(numericId);
            if (type) {
              const updated = courseTypeService.update(numericId, { 
                ...type, 
                status: value 
              });
              return { id, success: true, data: updated };
            }
            return { id, success: false, error: '课程类型不存在' };
          } catch (error) {
            return { id, success: false, error: String(error) };
          }
        });
        break;
        
      case 'delete':
        // 批量删除
        results = ids.map((id: string | number) => {
          try {
            const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
            const deleted = courseTypeService.delete(numericId);
            return { id, success: deleted, error: deleted ? null : '删除失败' };
          } catch (error) {
            return { id, success: false, error: String(error) };
          }
        });
        break;
        
      default:
        return NextResponse.json(
          { error: `不支持的操作类型: ${action}` },
          { status: 400 }
        );
    }
    
    // 计算成功和失败数量
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: {
        total: results.length,
        success: successCount,
        failure: failureCount,
        results
      }
    });
  } catch (error) {
    console.error('批量操作课程类型错误:', error);
    return NextResponse.json(
      { error: '批量操作失败', details: String(error) },
      { status: 500 }
    );
  }
} 