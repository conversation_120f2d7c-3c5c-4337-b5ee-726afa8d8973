'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface CardType {
  id: number;
  tenant_id: number;
  store_id: number | null;
  card_name: string;
  card_type: string;
  price: number;
  original_price: number;
  valid_period: number;
  total_count?: number;
  description: string;
  features: string[];
  restrictions: string[];
  status: number;
  is_universal: boolean;
  created_at: string;
  updated_at: string;
}

export default function CardsTestPage() {
  const [cards, setCards] = useState<CardType[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [universalOnly, setUniversalOnly] = useState(false)
  const [newCard, setNewCard] = useState({
    card_name: '静心瑜伽通用体验卡',
    card_type: '体验卡',
    price: 99,
    original_price: 199,
    valid_period: 7,
    store_id: null,
    description: '7天体验卡，适用于静心瑜伽所有门店',
    features: ['7天体验', '所有门店通用', '新会员专享'],
    restrictions: ['每人限购一次', '不可转让'],
    is_universal: true
  })

  const fetchCards = async () => {
    setLoading(true)
    setError('')
    
    try {
      let url = '/api/cards'
      if (universalOnly) {
        url += '?universal=true'
      }
      
      const response = await fetch(url)
      const data = await response.json()
      
      if (data.code === 0) {
        setCards(data.data.cards)
      } else {
        setError(data.message || '获取卡类型失败')
      }
    } catch (err: any) {
      setError('请求出错: ' + (err.message || '未知错误'))
    } finally {
      setLoading(false)
    }
  }

  const createCard = async () => {
    setLoading(true)
    setError('')
    
    try {
      const response = await fetch('/api/cards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newCard)
      })
      
      const data = await response.json()
      
      if (data.code === 0) {
        alert('卡类型创建成功!')
        fetchCards()
      } else {
        setError(data.message || '创建卡类型失败')
      }
    } catch (err: any) {
      setError('请求出错: ' + (err.message || '未知错误'))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCards()
  }, [universalOnly])

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">通用卡管理 API 测试</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>获取卡类型列表</CardTitle>
            <CardDescription>从 API 获取当前租户的卡类型</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center mb-4">
              <input
                type="checkbox"
                id="universalOnly"
                checked={universalOnly}
                onChange={(e) => setUniversalOnly(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="universalOnly">只显示通用卡</label>
              
              <Button onClick={fetchCards} disabled={loading} className="ml-4">
                {loading ? '加载中...' : '刷新列表'}
              </Button>
            </div>
            
            {error && <p className="text-red-500 mt-4">{error}</p>}
            
            <div className="mt-4">
              <h3 className="font-semibold mb-2">卡类型列表 ({cards.length})</h3>
              <div className="space-y-4">
                {cards.map((card) => (
                  <div key={card.id} className="p-4 border rounded-md">
                    <h4 className="font-bold">{card.card_name}</h4>
                    <p className="text-sm text-gray-500">
                      {card.card_type} - 
                      {card.store_id ? `门店专属卡 (ID: ${card.store_id})` : '总部通用卡'}
                    </p>
                    <p className="text-sm">价格: ¥{card.price} (原价: ¥{card.original_price})</p>
                    <p className="text-sm">有效期: {card.valid_period}天</p>
                    {card.total_count && <p className="text-sm">总次数: {card.total_count}次</p>}
                    <p className="text-sm">{card.description}</p>
                    <div className="mt-2">
                      <p className="text-xs font-semibold">特色:</p>
                      <ul className="text-xs list-disc list-inside">
                        {card.features.map((feature, index) => (
                          <li key={index}>{feature}</li>
                        ))}
                      </ul>
                    </div>
                    <div className="mt-2">
                      <p className="text-xs font-semibold">限制:</p>
                      <ul className="text-xs list-disc list-inside">
                        {card.restrictions.map((restriction, index) => (
                          <li key={index}>{restriction}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>创建新卡类型</CardTitle>
            <CardDescription>通过 API 创建新的卡类型</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">卡名称</label>
                <input
                  type="text"
                  value={newCard.card_name}
                  onChange={(e) => setNewCard({...newCard, card_name: e.target.value})}
                  className="w-full p-2 border rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">卡类型</label>
                <select
                  value={newCard.card_type}
                  onChange={(e) => setNewCard({...newCard, card_type: e.target.value})}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="年卡">年卡</option>
                  <option value="季卡">季卡</option>
                  <option value="月卡">月卡</option>
                  <option value="次卡">次卡</option>
                  <option value="体验卡">体验卡</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">价格 (元)</label>
                <input
                  type="number"
                  value={newCard.price}
                  onChange={(e) => setNewCard({...newCard, price: parseInt(e.target.value)})}
                  className="w-full p-2 border rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">原价 (元)</label>
                <input
                  type="number"
                  value={newCard.original_price}
                  onChange={(e) => setNewCard({...newCard, original_price: parseInt(e.target.value)})}
                  className="w-full p-2 border rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">有效期 (天)</label>
                <input
                  type="number"
                  value={newCard.valid_period}
                  onChange={(e) => setNewCard({...newCard, valid_period: parseInt(e.target.value)})}
                  className="w-full p-2 border rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">描述</label>
                <textarea
                  value={newCard.description}
                  onChange={(e) => setNewCard({...newCard, description: e.target.value})}
                  className="w-full p-2 border rounded-md"
                  rows={3}
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isUniversal"
                  checked={newCard.is_universal}
                  onChange={(e) => setNewCard({...newCard, is_universal: e.target.checked})}
                  className="mr-2"
                />
                <label htmlFor="isUniversal">设为通用卡（所有门店可用）</label>
              </div>
              
              <Button onClick={createCard} disabled={loading}>
                {loading ? '处理中...' : '创建新卡类型'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}