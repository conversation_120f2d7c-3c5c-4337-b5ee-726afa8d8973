"use client"

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import {
  Home,
  Calendar,
  CreditCard,
  User,
  Layers
} from "lucide-react"

// 预览组件接收设置作为props
interface PreviewComponentProps {
  settings: any
}

export function PreviewComponent({ settings }: PreviewComponentProps) {
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>预览</CardTitle>
        <CardDescription>
          会员端小程序效果预览
        </CardDescription>
      </CardHeader>
      <CardContent className="flex justify-center">
        <div
          className="relative w-[320px] h-[640px] bg-white rounded-[36px] shadow-lg overflow-hidden border-8 border-black"
          style={{
            backgroundColor: settings.darkMode ? '#121212' : 'white',
            color: settings.darkMode ? 'white' : 'black'
          }}
        >
          <div className="absolute top-0 left-0 right-0 h-6 bg-black flex justify-center items-end pb-1">
            <div className="w-20 h-4 bg-black rounded-b-xl"></div>
          </div>

          <div className="pt-6 h-full">
            {/* 手机状态栏 */}
            <div className="h-6 px-4 flex items-center justify-between text-xs">
              <span>9:41</span>
              <div className="flex items-center gap-1">
                <div className="w-4 h-2 bg-black rounded-sm"></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
              </div>
            </div>

            {/* 小程序内容 */}
            <div className="h-[calc(100%-6rem)] overflow-y-auto">
              {/* 页头 */}
              <div
                className={cn(
                  "w-full relative p-4",
                  settings.headerStyle === "gradient" ? "bg-gradient-to-r" : "bg-gray-100",
                  settings.darkMode ? "bg-gray-800" : ""
                )}
                style={{
                  backgroundImage: settings.headerStyle === "gradient"
                    ? `linear-gradient(to right, ${settings.primaryColor}, ${settings.secondaryColor})`
                    : 'none',
                  textAlign: settings.headerStyle === "centered" ? "center" : "left",
                  padding: settings.headerStyle === "compact" ? "0.5rem" : "1rem"
                }}
              >
                <h1 className={cn(
                  "text-lg font-bold",
                  settings.headerStyle === "gradient" ? "text-white" : ""
                )}>
                  静心瑜伽
                </h1>
                {settings.showWelcomeMessage && (
                  <p className={cn(
                    "text-sm mt-1",
                    settings.headerStyle === "gradient" ? "text-white/80" : "text-gray-500",
                    settings.darkMode && settings.headerStyle !== "gradient" ? "text-gray-300" : ""
                  )}>
                    {settings.welcomeMessage}
                  </p>
                )}
              </div>

              {/* 功能区 */}
              <div className={cn(
                "p-4",
                settings.darkMode ? "bg-gray-900" : ""
              )}>
                {/* 功能图标 */}
                {settings.layoutType !== "list" && (
                  <div className="grid grid-cols-4 gap-2 mb-4">
                    {settings.enableBooking && (
                      <div className="flex flex-col items-center">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center mb-1"
                          style={{
                            backgroundColor: `${settings.primaryColor}20`,
                            borderRadius: `${settings.borderRadius}px`
                          }}
                        >
                          <Calendar className="h-6 w-6" style={{ color: settings.primaryColor }} />
                        </div>
                        <span className="text-xs">预约</span>
                      </div>
                    )}

                    {settings.enableMemberCard && (
                      <div className="flex flex-col items-center">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center mb-1"
                          style={{
                            backgroundColor: `${settings.secondaryColor}20`,
                            borderRadius: `${settings.borderRadius}px`
                          }}
                        >
                          <CreditCard className="h-6 w-6" style={{ color: settings.secondaryColor }} />
                        </div>
                        <span className="text-xs">会员卡</span>
                      </div>
                    )}

                    {settings.enableCourseLibrary && (
                      <div className="flex flex-col items-center">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center mb-1"
                          style={{
                            backgroundColor: `${settings.primaryColor}15`,
                            borderRadius: `${settings.borderRadius}px`
                          }}
                        >
                          <Layers className="h-6 w-6" style={{ color: settings.primaryColor }} />
                        </div>
                        <span className="text-xs">课程</span>
                      </div>
                    )}

                    {settings.enablePersonalCenter && (
                      <div className="flex flex-col items-center">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center mb-1"
                          style={{
                            backgroundColor: `${settings.secondaryColor}15`,
                            borderRadius: `${settings.borderRadius}px`
                          }}
                        >
                          <User className="h-6 w-6" style={{ color: settings.secondaryColor }} />
                        </div>
                        <span className="text-xs">我的</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* 底部导航 */}
            <div className={cn(
              "h-14 border-t flex items-center justify-around px-2",
              settings.darkMode ? "bg-gray-900 border-gray-700" : ""
            )}>
              <div className="flex flex-col items-center">
                <Home className="h-5 w-5" style={{ color: settings.primaryColor }} />
                <span className="text-xs" style={{ color: settings.primaryColor }}>首页</span>
              </div>

              {settings.enableBooking && (
                <div className={cn(
                  "flex flex-col items-center",
                  settings.darkMode ? "text-gray-400" : "text-gray-500"
                )}>
                  <Calendar className="h-5 w-5" />
                  <span className="text-xs">预约</span>
                </div>
              )}

              {settings.enableCourseLibrary && (
                <div className={cn(
                  "flex flex-col items-center",
                  settings.darkMode ? "text-gray-400" : "text-gray-500"
                )}>
                  <Layers className="h-5 w-5" />
                  <span className="text-xs">课程</span>
                </div>
              )}

              {settings.enablePersonalCenter && (
                <div className={cn(
                  "flex flex-col items-center",
                  settings.darkMode ? "text-gray-400" : "text-gray-500"
                )}>
                  <User className="h-5 w-5" />
                  <span className="text-xs">我的</span>
                </div>
              )}
            </div>
          </div>

          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-1/3 h-1 bg-black rounded-full"></div>
        </div>
      </CardContent>
    </Card>
  )
}
