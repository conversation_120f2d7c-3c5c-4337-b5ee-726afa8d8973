"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  CreditCard, 
  QrCode, 
  Wallet, 
  ArrowLeft, 
  Plus, 
  Minus, 
  X, 
  CheckCircle2,
  Receipt,
  User,
  Tag,
  Clock,
  Calendar
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { toast } from "sonner"

// 模拟会员数据
const members = [
  { 
    id: "m1", 
    name: "张三", 
    avatar: "/avatars/01.png", 
    phone: "13800138001",
    cards: [
      { id: "c1", name: "瑜伽年卡", type: "次卡", balance: 42, expiry: "2024-12-31" },
      { id: "c2", name: "私教课程包", type: "次卡", balance: 8, expiry: "2024-10-15" }
    ]
  },
  { 
    id: "m2", 
    name: "李四", 
    avatar: "/avatars/02.png", 
    phone: "13800138002",
    cards: [
      { id: "c3", name: "瑜伽季卡", type: "次卡", balance: 15, expiry: "2024-08-31" }
    ]
  }
]

// 模拟商品数据
const products = [
  { id: "p1", name: "瑜伽垫", price: 280, category: "physical", image: "/products/yoga-mat.jpg" },
  { id: "p2", name: "瑜伽服", price: 360, category: "physical", image: "/products/yoga-clothes.jpg" },
  { id: "p3", name: "瑜伽砖", price: 80, category: "physical", image: "/products/yoga-brick.jpg" },
  { id: "p4", name: "瑜伽月卡", price: 680, category: "card", image: "/products/yoga-card.jpg" },
  { id: "p5", name: "私教课程 (单次)", price: 300, category: "course", image: "/products/private-class.jpg" },
  { id: "p6", name: "团体课程 (单次)", price: 120, category: "course", image: "/products/group-class.jpg" },
]

export default function CashierPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("cart")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMember, setSelectedMember] = useState<any>(null)
  const [cartItems, setCartItems] = useState<any[]>([])
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState<string | null>(null)

  // 计算购物车总金额
  const totalAmount = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)

  // 添加商品到购物车
  const addToCart = (product: any) => {
    const existingItemIndex = cartItems.findIndex(item => item.id === product.id)
    
    if (existingItemIndex >= 0) {
      // 如果商品已在购物车中，增加数量
      const updatedCart = [...cartItems]
      updatedCart[existingItemIndex].quantity += 1
      setCartItems(updatedCart)
    } else {
      // 否则添加新商品
      setCartItems([...cartItems, { ...product, quantity: 1 }])
    }
    
    toast.success(`已添加 ${product.name} 到购物车`)
  }

  // 从购物车移除商品
  const removeFromCart = (productId: string) => {
    setCartItems(cartItems.filter(item => item.id !== productId))
  }

  // 更新购物车商品数量
  const updateQuantity = (productId: string, change: number) => {
    const updatedCart = cartItems.map(item => {
      if (item.id === productId) {
        const newQuantity = Math.max(1, item.quantity + change)
        return { ...item, quantity: newQuantity }
      }
      return item
    })
    setCartItems(updatedCart)
  }

  // 处理支付
  const handlePayment = () => {
    if (!paymentMethod) {
      toast.error("请选择支付方式")
      return
    }
    
    // 在实际应用中，这里会调用支付API
    toast.success("支付成功！")
    setShowPaymentDialog(false)
    setCartItems([])
    setPaymentMethod(null)
  }

  // 过滤会员
  const filteredMembers = members.filter(member => 
    member.name.includes(searchQuery) || 
    member.phone.includes(searchQuery)
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/reception">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">收银台</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* 左侧会员信息和购物车 */}
        <div className="md:col-span-1 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium">会员信息</CardTitle>
              {!selectedMember && (
                <CardDescription>
                  搜索并选择会员以关联订单
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              {!selectedMember ? (
                <div className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索会员姓名或手机号..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2 max-h-[200px] overflow-y-auto">
                    {filteredMembers.map(member => (
                      <div 
                        key={member.id}
                        className="flex items-center gap-3 p-2 rounded-md hover:bg-muted cursor-pointer"
                        onClick={() => setSelectedMember(member)}
                      >
                        <Avatar>
                          <AvatarImage src={member.avatar} alt={member.name} />
                          <AvatarFallback>{member.name[0]}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-sm text-muted-foreground">{member.phone}</div>
                        </div>
                      </div>
                    ))}
                    
                    {filteredMembers.length === 0 && (
                      <div className="text-center py-4 text-muted-foreground">
                        未找到匹配的会员
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={selectedMember.avatar} alt={selectedMember.name} />
                        <AvatarFallback>{selectedMember.name[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium text-lg">{selectedMember.name}</div>
                        <div className="text-sm text-muted-foreground">{selectedMember.phone}</div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setSelectedMember(null)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h3 className="font-medium mb-2">会员卡</h3>
                    <div className="space-y-2">
                      {selectedMember.cards.map((card: any) => (
                        <div key={card.id} className="p-3 rounded-md border">
                          <div className="flex justify-between">
                            <div className="font-medium">{card.name}</div>
                            <Badge variant="outline">{card.type}</Badge>
                          </div>
                          <div className="flex justify-between mt-2 text-sm">
                            <span className="text-muted-foreground">剩余: {card.balance} 次</span>
                            <span className="text-muted-foreground">有效期至: {card.expiry}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-medium">购物车</CardTitle>
              <CardDescription>
                {cartItems.length} 件商品，总计 ¥{totalAmount.toFixed(2)}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {cartItems.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  购物车为空，请添加商品
                </div>
              ) : (
                <div className="space-y-3 max-h-[300px] overflow-y-auto">
                  {cartItems.map(item => (
                    <div key={item.id} className="flex items-center justify-between p-3 rounded-md border">
                      <div className="flex-1">
                        <div className="font-medium">{item.name}</div>
                        <div className="text-sm text-muted-foreground">¥{item.price.toFixed(2)}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="icon" className="h-7 w-7" onClick={() => updateQuantity(item.id, -1)}>
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="w-6 text-center">{item.quantity}</span>
                        <Button variant="outline" size="icon" className="h-7 w-7" onClick={() => updateQuantity(item.id, 1)}>
                          <Plus className="h-3 w-3" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-7 w-7 text-red-500" onClick={() => removeFromCart(item.id)}>
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-4">
              <div>
                <div className="text-sm text-muted-foreground">总计</div>
                <div className="text-2xl font-bold">¥{totalAmount.toFixed(2)}</div>
              </div>
              <Button 
                size="lg" 
                disabled={cartItems.length === 0}
                onClick={() => setShowPaymentDialog(true)}
              >
                结算
              </Button>
            </CardFooter>
          </Card>
        </div>

        {/* 右侧商品列表 */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="pb-3">
              <Tabs defaultValue="all" className="w-full">
                <TabsList className="grid grid-cols-4 mb-2">
                  <TabsTrigger value="all">全部商品</TabsTrigger>
                  <TabsTrigger value="physical">实体商品</TabsTrigger>
                  <TabsTrigger value="card">会员卡</TabsTrigger>
                  <TabsTrigger value="course">课程</TabsTrigger>
                </TabsList>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="搜索商品..." className="pl-8" />
                </div>
              </Tabs>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {products.map(product => (
                  <div 
                    key={product.id}
                    className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => addToCart(product)}
                  >
                    <div className="aspect-square bg-muted relative">
                      {/* 实际应用中这里会显示商品图片 */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        {product.category === 'physical' && <Tag className="h-8 w-8 text-blue-500" />}
                        {product.category === 'card' && <CreditCard className="h-8 w-8 text-green-500" />}
                        {product.category === 'course' && <Calendar className="h-8 w-8 text-amber-500" />}
                      </div>
                    </div>
                    <div className="p-3">
                      <div className="font-medium truncate">{product.name}</div>
                      <div className="flex justify-between items-center mt-1">
                        <div className="text-lg font-bold">¥{product.price.toFixed(2)}</div>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 支付对话框 */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>选择支付方式</DialogTitle>
            <DialogDescription>
              请选择支付方式完成订单
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-3 gap-4 py-4">
            <div 
              className={`flex flex-col items-center gap-2 p-4 rounded-lg border-2 cursor-pointer transition-all ${paymentMethod === 'wechat' ? 'border-primary' : 'border-muted'}`}
              onClick={() => setPaymentMethod('wechat')}
            >
              <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                <svg viewBox="0 0 24 24" height="24" width="24" fill="none" stroke="currentColor" className="text-green-600">
                  <path d="M9.5 9.5C9.5 10.3284 8.82843 11 8 11C7.17157 11 6.5 10.3284 6.5 9.5C6.5 8.67157 7.17157 8 8 8C8.82843 8 9.5 8.67157 9.5 9.5Z" fill="currentColor" />
                  <path d="M16.5 9.5C16.5 10.3284 15.8284 11 15 11C14.1716 11 13.5 10.3284 13.5 9.5C13.5 8.67157 14.1716 8 15 8C15.8284 8 16.5 8.67157 16.5 9.5Z" fill="currentColor" />
                  <path d="M13.5 16.5C13.5 17.3284 12.8284 18 12 18C11.1716 18 10.5 17.3284 10.5 16.5C10.5 15.6716 11.1716 15 12 15C12.8284 15 13.5 15.6716 13.5 16.5Z" fill="currentColor" />
                  <path d="M18.5 16.5C18.5 17.3284 17.8284 18 17 18C16.1716 18 15.5 17.3284 15.5 16.5C15.5 15.6716 16.1716 15 17 15C17.8284 15 18.5 15.6716 18.5 16.5Z" fill="currentColor" />
                  <path d="M7.34835 15.4899C5.15071 14.097 3.72591 11.7099 3.72591 8.99999C3.72591 4.9571 7.68301 1.72999 12.5 1.72999C17.317 1.72999 21.2741 4.9571 21.2741 8.99999C21.2741 13.0429 17.317 16.27 12.5 16.27C11.6493 16.27 10.8222 16.1726 10.0381 15.9911C9.91492 15.9599 9.78852 15.9566 9.66481 15.9815L6.74638 16.5899L7.34835 15.4899Z" stroke="currentColor" strokeWidth="1.5" />
                </svg>
              </div>
              <span className="text-sm font-medium">微信支付</span>
            </div>
            <div 
              className={`flex flex-col items-center gap-2 p-4 rounded-lg border-2 cursor-pointer transition-all ${paymentMethod === 'alipay' ? 'border-primary' : 'border-muted'}`}
              onClick={() => setPaymentMethod('alipay')}
            >
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                <svg viewBox="0 0 24 24" height="24" width="24" fill="none" stroke="currentColor" className="text-blue-600">
                  <path d="M21.422 15.358C21.2226 15.0059 20.9879 14.6755 20.722 14.372C19.4349 12.7779 17.9175 11.9993 16.5 11.9993H7.5C6.08252 11.9993 4.56513 12.7779 3.27803 14.372C3.01215 14.6755 2.77739 15.0059 2.57803 15.358C3.48252 16.0512 4.45577 16.6372 5.5 17.0604C7.5 17.8732 9.69922 18.2993 12 18.2993C14.3008 18.2993 16.5 17.8732 18.5 17.0604C19.5442 16.6372 20.5175 16.0512 21.422 15.358Z" stroke="currentColor" strokeWidth="1.5" />
                  <path d="M12 15.2993C13.6569 15.2993 15 13.9562 15 12.2993C15 10.6425 13.6569 9.29932 12 9.29932C10.3431 9.29932 9 10.6425 9 12.2993C9 13.9562 10.3431 15.2993 12 15.2993Z" stroke="currentColor" strokeWidth="1.5" />
                  <path d="M2 12.2993C2 7.33288 6.47715 3.29932 12 3.29932C17.5228 3.29932 22 7.33288 22 12.2993C22 17.2658 17.5228 21.2993 12 21.2993C6.47715 21.2993 2 17.2658 2 12.2993Z" stroke="currentColor" strokeWidth="1.5" />
                </svg>
              </div>
              <span className="text-sm font-medium">支付宝</span>
            </div>
            <div 
              className={`flex flex-col items-center gap-2 p-4 rounded-lg border-2 cursor-pointer transition-all ${paymentMethod === 'cash' ? 'border-primary' : 'border-muted'}`}
              onClick={() => setPaymentMethod('cash')}
            >
              <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center">
                <Wallet className="h-5 w-5 text-amber-600" />
              </div>
              <span className="text-sm font-medium">现金</span>
            </div>
          </div>
          <div className="flex justify-between items-center py-2 border-t border-b">
            <span className="font-medium">总计金额</span>
            <span className="text-xl font-bold">¥{totalAmount.toFixed(2)}</span>
          </div>
          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setShowPaymentDialog(false)}>取消</Button>
            <Button onClick={handlePayment}>确认支付</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
