"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Search, Download, FileText, Clock, Filter, MoreHorizontal } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"

export default function VerificationRecordsPage() {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })
  const [selectedRecord, setSelectedRecord] = useState<any>(null)
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)

  const [records] = useState([
    {
      id: "VR12345678",
      code: "MT12345678",
      platform: "美团",
      product: "基础瑜伽体验课（60分钟）",
      price: "¥99",
      status: "success",
      verifiedAt: "2025-03-28 14:30:25",
      operator: "管理员",
      customer: {
        name: "张三",
        phone: "138****1234",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "VR23456789",
      code: "MT23456789",
      platform: "美团",
      product: "高级瑜伽单次课（90分钟）",
      price: "¥129",
      status: "success",
      verifiedAt: "2025-03-28 11:15:42",
      operator: "前台",
      customer: {
        name: "李四",
        phone: "139****5678",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "VR34567890",
      code: "MT34567890",
      platform: "美团",
      product: "瑜伽垫+瑜伽服套装",
      price: "¥299",
      status: "failed",
      verifiedAt: "2025-03-28 09:45:18",
      operator: "管理员",
      customer: {
        name: "王五",
        phone: "137****9012",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "VR45678901",
      code: "DY12345678",
      platform: "抖音",
      product: "空中瑜伽体验课（60分钟）",
      price: "¥129",
      status: "success",
      verifiedAt: "2025-03-27 15:30:25",
      operator: "教练",
      customer: {
        name: "赵六",
        phone: "136****3456",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    {
      id: "**********",
      code: "**********",
      platform: "抖音",
      product: "阴瑜伽单次课（90分钟）",
      price: "¥119",
      status: "success",
      verifiedAt: "2025-03-27 12:15:42",
      operator: "前台",
      customer: {
        name: "钱七",
        phone: "135****7890",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
  ])

  const handleViewDetail = (record: any) => {
    setSelectedRecord(record)
    setIsDetailDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">核销记录</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setIsExportDialogOpen(true)}>
            <Download className="mr-2 h-4 w-4" />
            导出记录
          </Button>
          <Button>
            <FileText className="mr-2 h-4 w-4" />
            生成报表
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">全部记录</TabsTrigger>
          <TabsTrigger value="meituan">美团核销</TabsTrigger>
          <TabsTrigger value="douyin">抖音核销</TabsTrigger>
          <TabsTrigger value="exception">异常记录</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>核销记录查询</CardTitle>
              <CardDescription>查询所有平台的核销记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <Label htmlFor="search-query" className="mb-2 block">
                    搜索
                  </Label>
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input id="search-query" placeholder="券码/商品名称/用户名" className="pl-8" />
                  </div>
                </div>

                <div>
                  <Label className="mb-2 block">日期范围</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                        <Clock className="mr-2 h-4 w-4" />
                        {dateRange.from ? (
                          dateRange.to ? (
                            <>
                              {format(dateRange.from, "yyyy-MM-dd")} - {format(dateRange.to, "yyyy-MM-dd")}
                            </>
                          ) : (
                            format(dateRange.from, "yyyy-MM-dd")
                          )
                        ) : (
                          <span>选择日期范围</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="range"
                        defaultMonth={date}
                        selected={dateRange}
                        onSelect={setDateRange}
                        numberOfMonths={2}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <Label htmlFor="platform-filter" className="mb-2 block">
                    平台
                  </Label>
                  <Select defaultValue="all">
                    <SelectTrigger id="platform-filter" className="w-[180px]">
                      <SelectValue placeholder="选择平台" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部平台</SelectItem>
                      <SelectItem value="meituan">美团</SelectItem>
                      <SelectItem value="douyin">抖音</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="status-filter" className="mb-2 block">
                    状态
                  </Label>
                  <Select defaultValue="all">
                    <SelectTrigger id="status-filter" className="w-[180px]">
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="success">成功</SelectItem>
                      <SelectItem value="failed">失败</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button>
                    <Filter className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <Checkbox id="select-all" />
                    </TableHead>
                    <TableHead>券码</TableHead>
                    <TableHead>平台</TableHead>
                    <TableHead>商品</TableHead>
                    <TableHead>价格</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>核销时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作人</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {records.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <Checkbox id={`select-${record.id}`} />
                      </TableCell>
                      <TableCell className="font-medium">{record.code}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={record.platform === "美团" ? "bg-yellow-50" : "bg-black text-white"}
                        >
                          {record.platform}
                        </Badge>
                      </TableCell>
                      <TableCell>{record.product}</TableCell>
                      <TableCell>{record.price}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={record.customer.avatar} />
                            <AvatarFallback>{record.customer.name[0]}</AvatarFallback>
                          </Avatar>
                          <span>{record.customer.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{record.verifiedAt}</TableCell>
                      <TableCell>
                        <Badge variant={record.status === "success" ? "default" : "destructive"}>
                          {record.status === "success" ? "成功" : "失败"}
                        </Badge>
                      </TableCell>
                      <TableCell>{record.operator}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleViewDetail(record)}>查看详情</DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>打印凭证</DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">共 {records.length} 条记录，已选择 0 条</div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" disabled>
                    上一页
                  </Button>
                  <Button variant="outline" size="sm">
                    下一页
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="meituan" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>美团核销记录</CardTitle>
              <CardDescription>查询美团平台的核销记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8">
                <p className="text-muted-foreground">美团核销记录内容与全部记录类似，但仅显示美团平台的数据</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="douyin" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>抖音核销记录</CardTitle>
              <CardDescription>查询抖音平台的核销记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8">
                <p className="text-muted-foreground">抖音核销记录内容与全部记录类似，但仅显示抖音平台的数据</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="exception" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>异常核销记录</CardTitle>
              <CardDescription>查询异常的核销记录</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8">
                <p className="text-muted-foreground">异常核销记录内容与全部记录类似，但仅显示状态为失败的数据</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 记录详情对话框 */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>核销记录详情</DialogTitle>
            <DialogDescription>查看核销记录的详细信息</DialogDescription>
          </DialogHeader>

          {selectedRecord && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <Badge variant={selectedRecord.status === "success" ? "default" : "destructive"}>
                    {selectedRecord.status === "success" ? "核销成功" : "核销失败"}
                  </Badge>
                  <Badge
                    variant="outline"
                    className={selectedRecord.platform === "美团" ? "bg-yellow-50" : "bg-black text-white"}
                  >
                    {selectedRecord.platform}
                  </Badge>
                </div>
                <span className="text-sm text-muted-foreground">{selectedRecord.verifiedAt}</span>
              </div>

              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">券码：</span>
                    <span className="font-medium">{selectedRecord.code}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">价格：</span>
                    <span className="font-medium">{selectedRecord.price}</span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-muted-foreground">商品：</span>
                    <span className="font-medium">{selectedRecord.product}</span>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={selectedRecord.customer.avatar} />
                    <AvatarFallback>{selectedRecord.customer.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{selectedRecord.customer.name}</div>
                    <div className="text-sm text-muted-foreground">{selectedRecord.customer.phone}</div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">操作记录</h4>
                  <div className="text-sm">
                    <p className="flex justify-between">
                      <span>核销操作</span>
                      <span className="text-muted-foreground">{selectedRecord.verifiedAt}</span>
                    </p>
                    <p className="text-muted-foreground">操作人：{selectedRecord.operator}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline">打印凭证</Button>
            <Button onClick={() => setIsDetailDialogOpen(false)}>关闭</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导出对话框 */}
      <Dialog open={isExportDialogOpen} onOpenChange={setIsExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>导出核销记录</DialogTitle>
            <DialogDescription>选择导出格式和内容</DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>导出格式</Label>
              <Select defaultValue="excel">
                <SelectTrigger>
                  <SelectValue placeholder="选择导出格式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="csv">CSV (.csv)</SelectItem>
                  <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>时间范围</Label>
              <Select defaultValue="7days">
                <SelectTrigger>
                  <SelectValue placeholder="选择时间范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">今天</SelectItem>
                  <SelectItem value="yesterday">昨天</SelectItem>
                  <SelectItem value="7days">最近7天</SelectItem>
                  <SelectItem value="30days">最近30天</SelectItem>
                  <SelectItem value="custom">自定义范围</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>导出字段</Label>
              <ScrollArea className="h-[120px] border rounded-md p-2">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="field-code" defaultChecked />
                    <label htmlFor="field-code" className="text-sm">
                      券码
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="field-platform" defaultChecked />
                    <label htmlFor="field-platform" className="text-sm">
                      平台
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="field-product" defaultChecked />
                    <label htmlFor="field-product" className="text-sm">
                      商品
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="field-price" defaultChecked />
                    <label htmlFor="field-price" className="text-sm">
                      价格
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="field-customer" defaultChecked />
                    <label htmlFor="field-customer" className="text-sm">
                      用户
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="field-time" defaultChecked />
                    <label htmlFor="field-time" className="text-sm">
                      核销时间
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="field-status" defaultChecked />
                    <label htmlFor="field-status" className="text-sm">
                      状态
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="field-operator" defaultChecked />
                    <label htmlFor="field-operator" className="text-sm">
                      操作人
                    </label>
                  </div>
                </div>
              </ScrollArea>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsExportDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => setIsExportDialogOpen(false)}>导出</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

