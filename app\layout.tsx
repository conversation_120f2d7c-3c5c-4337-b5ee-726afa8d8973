import { Inter as FontSans } from "next/font/google"
import "./globals.css"
import { cn } from "@/lib/utils"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/contexts/auth-context"
import { OnboardingProvider } from "@/contexts/onboarding-context"
import { LogProvider } from "@/contexts/log-context"
import { Toaster } from "@/components/ui/toaster"
import { Toaster as SonnerToaster } from "sonner"
import { OnboardingGuide } from "@/components/onboarding-guide"
import { ClientLayout } from "@/components/client-layout"
import { ErrorBoundary } from "@/components/error-boundary"
import "@/lib/axios-config"
import "@/lib/init-error-handling"

// 元数据定义 - 在服务器组件中导出
export const metadata = {
  title: "瑜伽后台管理系统",
  description: "专业的瑜伽馆管理系统"
};

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
})

// 这是一个服务器组件
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <title>静心瑜伽馆管理系统</title>
        <meta name="description" content="瑜伽普拉提馆SaaS约课系统" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
      </head>
      <body className={cn(
        "min-h-screen bg-background font-sans antialiased",
        fontSans.variable
      )}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <AuthProvider>
            <LogProvider>
              <OnboardingProvider>
                <ClientLayout>
                  {children}
                </ClientLayout>
                {/* 将OnboardingGuide移到ClientLayout组件内部，由其进行登录状态判断 */}
                <Toaster />
                <SonnerToaster position="top-right" richColors />
              </OnboardingProvider>
            </LogProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}




