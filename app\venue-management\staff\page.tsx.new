"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/components/ui/use-toast"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MoreHorizontal, Plus, Search, UserPlus, Shield, Upload, Camera } from "lucide-react"

// 雇佣类型
const employmentTypes = {
  FULL_TIME: "全职",
  PART_TIME: "兼职",
  EXTERNAL: "外聘"
}

// 课程类型
const courseTypes = {
  GROUP: "团体课",
  PRIVATE: "私教课",
  SMALL: "小班课",
  WORKSHOP: "工作坊",
  ALL: "全部课程"
}

// 模拟员工数据
const staffData = [
  {
    id: 1,
    name: "张三",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=zhang",
    roles: ["前台"],
    status: "在职",
    phone: "13800138001",
    email: "<EMAIL>",
    joinDate: "2022-01-15",
    employmentType: employmentTypes.FULL_TIME,
  },
  {
    id: 2,
    name: "李四",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=li",
    roles: ["会籍顾问"],
    status: "在职",
    phone: "13800138002",
    email: "<EMAIL>",
    joinDate: "2022-03-10",
    employmentType: employmentTypes.FULL_TIME,
  }
]

// 角色数据 - 简化为基础角色
const roles = [
  // 管理角色
  {
    id: 1,
    name: "超级管理员",
    description: "拥有系统所有权限，不可编辑",
    isSystem: true,
    editable: false,
    category: "管理",
    hasAdvancedSettings: false
  },
  {
    id: 2,
    name: "店长",
    description: "管理场馆相关的所有功能",
    isSystem: true,
    editable: true,
    category: "管理",
    hasAdvancedSettings: false
  },
  // 教学角色
  {
    id: 3,
    name: "教练",
    description: "负责课程教学",
    isSystem: true,
    editable: true,
    category: "教学",
    hasAdvancedSettings: true
  },
  // 销售角色
  {
    id: 6,
    name: "会籍顾问",
    description: "负责会员销售和服务",
    isSystem: true,
    editable: true,
    category: "销售",
    hasAdvancedSettings: false
  },
  // 服务角色
  {
    id: 8,
    name: "前台",
    description: "负责前台接待工作",
    isSystem: true,
    editable: true,
    category: "服务",
    hasAdvancedSettings: false
  }
]

export default function StaffManagementPage() {
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedRole, setSelectedRole] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [isAddStaffDialogOpen, setIsAddStaffDialogOpen] = useState(false)
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false)
  const [currentRole, setCurrentRole] = useState<any>(null)

  // 添加员工表单状态
  const [newStaffForm, setNewStaffForm] = useState({
    name: "",
    phone: "",
    email: "",
    joinDate: "",
    status: "在职",
    selectedRoles: [] as number[],
    employmentType: employmentTypes.FULL_TIME,
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=default",
    avatarFile: null as File | null,
    advancedSettings: {
      isTeachingSales: false,
      selectedCourseTypes: [] as string[]
    },
    showAdvancedSettings: false
  })

  // 头像上传引用
  const avatarInputRef = useRef<HTMLInputElement>(null)

  // 处理角色选择变化
  const handleRoleChange = (roleId: number, checked: boolean) => {
    const role = roles.find(r => r.id === roleId);

    if (checked) {
      // 添加角色
      const newSelectedRoles = [...newStaffForm.selectedRoles, roleId];

      // 如果角色有高级设置，显示高级设置面板
      const showAdvanced = role?.hasAdvancedSettings ||
                          newSelectedRoles.some(id => roles.find(r => r.id === id)?.hasAdvancedSettings);

      setNewStaffForm({
        ...newStaffForm,
        selectedRoles: newSelectedRoles,
        showAdvancedSettings: showAdvanced
      });
    } else {
      // 移除角色
      const newSelectedRoles = newStaffForm.selectedRoles.filter(id => id !== roleId);

      // 检查是否还需要显示高级设置
      const showAdvanced = newSelectedRoles.some(id => roles.find(r => r.id === id)?.hasAdvancedSettings);

      setNewStaffForm({
        ...newStaffForm,
        selectedRoles: newSelectedRoles,
        showAdvancedSettings: showAdvanced
      });
    }
  }

  // 处理课程类型选择变化
  const handleCourseTypeChange = (courseType: string, checked: boolean) => {
    if (checked) {
      setNewStaffForm({
        ...newStaffForm,
        advancedSettings: {
          ...newStaffForm.advancedSettings,
          selectedCourseTypes: [...newStaffForm.advancedSettings.selectedCourseTypes, courseType]
        }
      });
    } else {
      setNewStaffForm({
        ...newStaffForm,
        advancedSettings: {
          ...newStaffForm.advancedSettings,
          selectedCourseTypes: newStaffForm.advancedSettings.selectedCourseTypes.filter(type => type !== courseType)
        }
      });
    }
  }

  // 处理教销一体选择变化
  const handleTeachingSalesChange = (checked: boolean) => {
    setNewStaffForm({
      ...newStaffForm,
      advancedSettings: {
        ...newStaffForm.advancedSettings,
        isTeachingSales: checked
      }
    });
  }

  // 处理头像上传
  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 创建临时URL以预览图片
      const imageUrl = URL.createObjectURL(file);
      setNewStaffForm({
        ...newStaffForm,
        avatar: imageUrl,
        avatarFile: file
      });
    }
  }

  // 触发头像上传点击
  const triggerAvatarUpload = () => {
    avatarInputRef.current?.click();
  }

  // 过滤员工数据
  const filteredStaff = staffData.filter((staff) => {
    const matchesSearch = staff.name.includes(searchTerm) ||
                         staff.phone.includes(searchTerm) ||
                         staff.email.includes(searchTerm)
    const matchesRole = selectedRole === "all" || staff.roles.includes(selectedRole)
    const matchesStatus = selectedStatus === "all" || staff.status === selectedStatus

    return matchesSearch && matchesRole && matchesStatus
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">员工管理</h1>
        <Button onClick={() => setIsAddStaffDialogOpen(true)}>
          <UserPlus className="mr-2 h-4 w-4" />
          添加员工
        </Button>
      </div>

      {/* 添加员工对话框 */}
      <Dialog open={isAddStaffDialogOpen} onOpenChange={setIsAddStaffDialogOpen}>
        <DialogContent className="sm:max-w-[650px] md:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>添加新员工</DialogTitle>
            <DialogDescription>
              填写新员工的基本信息，创建后可以进一步完善详细资料
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {/* 头像上传区域 */}
            <div className="flex justify-center mb-4">
              <div className="relative">
                <Avatar className="h-24 w-24 cursor-pointer" onClick={triggerAvatarUpload}>
                  <AvatarImage src={newStaffForm.avatar} alt="员工头像" />
                  <AvatarFallback className="text-2xl">
                    {newStaffForm.name ? newStaffForm.name.slice(0, 1) : "?"}
                  </AvatarFallback>
                </Avatar>
                <div
                  className="absolute bottom-0 right-0 p-1 bg-primary text-white rounded-full cursor-pointer"
                  onClick={triggerAvatarUpload}
                >
                  <Camera className="h-4 w-4" />
                </div>
                <input
                  type="file"
                  ref={avatarInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center">
                  姓名 <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="name"
                  placeholder="请输入员工姓名"
                  value={newStaffForm.name}
                  onChange={(e) => setNewStaffForm({...newStaffForm, name: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center">
                  手机号码 <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="phone"
                  placeholder="请输入手机号码"
                  value={newStaffForm.phone}
                  onChange={(e) => setNewStaffForm({...newStaffForm, phone: e.target.value})}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">电子邮箱</Label>
              <Input
                id="email"
                type="email"
                placeholder="请输入电子邮箱"
                value={newStaffForm.email}
                onChange={(e) => setNewStaffForm({...newStaffForm, email: e.target.value})}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status" className="flex items-center">
                  状态 <span className="text-red-500 ml-1">*</span>
                </Label>
                <Select
                  value={newStaffForm.status}
                  onValueChange={(value) => setNewStaffForm({...newStaffForm, status: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="在职">在职</SelectItem>
                    <SelectItem value="离职">离职</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="joinDate" className="flex items-center">
                  入职日期 <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="joinDate"
                  type="date"
                  value={newStaffForm.joinDate}
                  onChange={(e) => setNewStaffForm({...newStaffForm, joinDate: e.target.value})}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddStaffDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={() => {
              // 验证表单
              if (!newStaffForm.name) {
                toast({
                  title: "请输入员工姓名",
                  variant: "destructive",
                })
                return
              }

              if (!newStaffForm.phone) {
                toast({
                  title: "请输入手机号码",
                  variant: "destructive",
                })
                return
              }
            }}>
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
