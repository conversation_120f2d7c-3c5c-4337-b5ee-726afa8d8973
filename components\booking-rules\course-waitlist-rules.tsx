"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { InfoIcon } from "lucide-react"

const formSchema = z.object({
  // 候补基本设置
  waitlistEnabled: z.boolean(),
  autoEnableWhenFull: z.boolean(),
  maxWaitlistSize: z.coerce.number().min(0).max(100),
  maxWaitlistPerMember: z.coerce.number().min(0).max(20),
  
  // 候补排序设置
  sortingMethod: z.enum(["time", "level", "hybrid"]),
  levelPriorityEnabled: z.boolean(),
  
  // 候补转正设置
  promotionMethod: z.enum(["auto", "manual"]),
  promotionDeadlineHours: z.coerce.number().min(0).max(24),
  notificationMethods: z.array(z.string()).min(1),
  
  // 候补限制
  waitlistClosingMinutes: z.coerce.number().min(0).max(120),
  
  // 覆盖全局设置
  overrideGlobal: z.boolean(),
})

const notificationMethods = [
  { id: "wechat", label: "微信通知" },
  { id: "sms", label: "短信通知" },
  { id: "app", label: "APP推送" },
  { id: "email", label: "邮件通知" },
]

interface CourseWaitlistRulesProps {
  courseTypeId: string;
  onChange: () => void;
}

export function CourseWaitlistRules({ courseTypeId, onChange }: CourseWaitlistRulesProps) {
  // 根据课程类型获取不同的默认值
  const getDefaultValues = () => {
    // 这里可以根据courseTypeId返回不同的默认值
    if (courseTypeId === "private") {
      return {
        waitlistEnabled: false,
        autoEnableWhenFull: false,
        maxWaitlistSize: 5,
        maxWaitlistPerMember: 2,
        sortingMethod: "time" as const,
        levelPriorityEnabled: false,
        promotionMethod: "manual" as const,
        promotionDeadlineHours: 4,
        notificationMethods: ["wechat", "sms"],
        waitlistClosingMinutes: 60,
        overrideGlobal: true,
      }
    }
    
    return {
      waitlistEnabled: true,
      autoEnableWhenFull: true,
      maxWaitlistSize: 10,
      maxWaitlistPerMember: 3,
      sortingMethod: "hybrid" as const,
      levelPriorityEnabled: true,
      promotionMethod: "auto" as const,
      promotionDeadlineHours: 2,
      notificationMethods: ["wechat", "app"],
      waitlistClosingMinutes: 30,
      overrideGlobal: false,
    }
  }
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
  })
  
  const watchWaitlistEnabled = form.watch("waitlistEnabled")
  const watchSortingMethod = form.watch("sortingMethod")
  const watchPromotionMethod = form.watch("promotionMethod")
  const watchOverrideGlobal = form.watch("overrideGlobal")
  
  return (
    <Form {...form}>
      <form onChange={onChange} className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>候补规则设置</CardTitle>
                <CardDescription>
                  设置课程候补的基本规则、排序规则和转正规则等
                </CardDescription>
              </div>
              <FormField
                control={form.control}
                name="overrideGlobal"
                render={({ field }) => (
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormLabel className="font-normal">
                      覆盖全局设置
                    </FormLabel>
                  </FormItem>
                )}
              />
            </div>
          </CardHeader>
          <CardContent>
            {!watchOverrideGlobal && (
              <Alert className="mb-6">
                <InfoIcon className="h-4 w-4" />
                <AlertTitle>使用全局设置</AlertTitle>
                <AlertDescription>
                  当前使用全局候补规则设置。启用"覆盖全局设置"开关可自定义此课程类型的候补规则。
                </AlertDescription>
              </Alert>
            )}
            
            {watchOverrideGlobal && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">候补基本设置</h3>
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="waitlistEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">启用候补排队</FormLabel>
                          <FormDescription>
                            允许会员在课程满员时进行候补
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {watchWaitlistEnabled && (
                    <>
                      <FormField
                        control={form.control}
                        name="autoEnableWhenFull"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">满员自动开启候补</FormLabel>
                              <FormDescription>
                                课程预约人数达到上限时自动开启候补
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name="maxWaitlistSize"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>最大候补人数</FormLabel>
                              <FormControl>
                                <div className="flex items-center">
                                  <Input type="number" {...field} className="w-24" />
                                  <span className="ml-2">人</span>
                                </div>
                              </FormControl>
                              <FormDescription>
                                每个课程最多可候补的人数
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="maxWaitlistPerMember"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>每人最大候补数</FormLabel>
                              <FormControl>
                                <div className="flex items-center">
                                  <Input type="number" {...field} className="w-24" />
                                  <span className="ml-2">节课</span>
                                </div>
                              </FormControl>
                              <FormDescription>
                                每个会员同时最多可候补的课程数
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </>
                  )}
                </div>
                
                {watchWaitlistEnabled && (
                  <>
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">候补排序规则</h3>
                      <Separator />
                      
                      <FormField
                        control={form.control}
                        name="sortingMethod"
                        render={({ field }) => (
                          <FormItem className="space-y-3">
                            <FormLabel>排序方法</FormLabel>
                            <FormControl>
                              <RadioGroup
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                className="flex flex-col space-y-1"
                              >
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="time" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    按候补时间先后排序
                                  </FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="level" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    按会员等级/类型优先排序
                                  </FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="hybrid" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    混合排序（等级优先，同等级内按时间）
                                  </FormLabel>
                                </FormItem>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      {(watchSortingMethod === "level" || watchSortingMethod === "hybrid") && (
                        <FormField
                          control={form.control}
                          name="levelPriorityEnabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">等级优先</FormLabel>
                                <FormDescription>
                                  高等级会员在候补队列中优先转正
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">候补转正规则</h3>
                      <Separator />
                      
                      <FormField
                        control={form.control}
                        name="promotionMethod"
                        render={({ field }) => (
                          <FormItem className="space-y-3">
                            <FormLabel>转正方法</FormLabel>
                            <FormControl>
                              <RadioGroup
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                className="flex flex-col space-y-1"
                              >
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="auto" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    自动转正（有人取消时自动按序转正）
                                  </FormLabel>
                                </FormItem>
                                <FormItem className="flex items-center space-x-3 space-y-0">
                                  <FormControl>
                                    <RadioGroupItem value="manual" />
                                  </FormControl>
                                  <FormLabel className="font-normal">
                                    手动确认（有人取消时通知候补者确认）
                                  </FormLabel>
                                </FormItem>
                              </RadioGroup>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="promotionDeadlineHours"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>转正截止时间</FormLabel>
                            <FormControl>
                              <div className="flex items-center">
                                <Input type="number" {...field} className="w-24" />
                                <span className="ml-2">小时</span>
                              </div>
                            </FormControl>
                            <FormDescription>
                              课程开始前多少小时内不再转正
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      {watchPromotionMethod === "manual" && (
                        <FormField
                          control={form.control}
                          name="notificationMethods"
                          render={() => (
                            <FormItem>
                              <div className="mb-4">
                                <FormLabel className="text-base">通知方式</FormLabel>
                                <FormDescription>
                                  选择通知候补者确认的方式
                                </FormDescription>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {notificationMethods.map((method) => (
                                  <FormField
                                    key={method.id}
                                    control={form.control}
                                    name="notificationMethods"
                                    render={({ field }) => {
                                      return (
                                        <FormItem
                                          key={method.id}
                                          className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                                        >
                                          <FormControl>
                                            <Checkbox
                                              checked={field.value?.includes(method.id)}
                                              onCheckedChange={(checked) => {
                                                return checked
                                                  ? field.onChange([...field.value, method.id])
                                                  : field.onChange(
                                                      field.value?.filter(
                                                        (value) => value !== method.id
                                                      )
                                                    )
                                              }}
                                            />
                                          </FormControl>
                                          <div className="space-y-1 leading-none">
                                            <FormLabel>
                                              {method.label}
                                            </FormLabel>
                                          </div>
                                        </FormItem>
                                      )
                                    }}
                                  />
                                ))}
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">候补限制</h3>
                      <Separator />
                      
                      <FormField
                        control={form.control}
                        name="waitlistClosingMinutes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>候补关闭时间</FormLabel>
                            <FormControl>
                              <div className="flex items-center">
                                <Input type="number" {...field} className="w-24" />
                                <span className="ml-2">分钟</span>
                              </div>
                            </FormControl>
                            <FormDescription>
                              课程开始前多少分钟关闭候补
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </form>
    </Form>
  )
}
