"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { MessageSquare, Send, Image, Paperclip, Clock, Users, FileText } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface SendMessageDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
    phone: string
    avatar?: string
  }
}

export function SendMessageDialog({ open, onOpenChange, member }: SendMessageDialogProps) {
  const { toast } = useToast()
  const [messageType, setMessageType] = useState<"sms" | "wechat" | "app">("sms")
  const [messageContent, setMessageContent] = useState("")
  const [scheduledTime, setScheduledTime] = useState("")
  const [isScheduled, setIsScheduled] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState("")
  const [isSending, setIsSending] = useState(false)

  // 模拟消息模板
  const messageTemplates = [
    { id: "1", name: "课程提醒", content: "尊敬的{{name}}，提醒您明天{{time}}有{{course}}课程，请准时参加。" },
    { id: "2", name: "会员卡到期提醒", content: "尊敬的{{name}}，您的{{card}}将于{{date}}到期，请及时续费。" },
    { id: "3", name: "生日祝福", content: "尊敬的{{name}}，祝您生日快乐！特别为您准备了生日礼物，请到前台领取。" },
    { id: "4", name: "活动邀请", content: "尊敬的{{name}}，我们将于{{date}}举办{{activity}}，诚邀您参加。" },
  ]

  const handleSelectTemplate = (templateId: string) => {
    const template = messageTemplates.find(t => t.id === templateId)
    if (template) {
      let content = template.content
      // 替换模板变量
      content = content.replace("{{name}}", member.name)
      content = content.replace("{{time}}", "10:00")
      content = content.replace("{{course}}", "瑜伽基础课")
      content = content.replace("{{card}}", "年卡")
      content = content.replace("{{date}}", "2023-12-31")
      content = content.replace("{{activity}}", "瑜伽工作坊")

      setMessageContent(content)
      setSelectedTemplate(templateId)
    }
  }

  const handleSendMessage = async () => {
    if (!messageContent.trim()) {
      toast({
        title: "消息内容不能为空",
        variant: "destructive",
      })
      return
    }

    setIsSending(true)

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast({
        title: "消息发送成功",
        description: isScheduled
          ? `消息已安排在 ${scheduledTime} 发送给 ${member.name}`
          : `消息已发送给 ${member.name}`,
      })

      onOpenChange(false)
    } catch (error) {
      toast({
        title: "发送失败",
        description: "消息发送失败，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSending(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>发送消息</DialogTitle>
          <DialogDescription>
            向会员发送消息通知
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center gap-3 p-3 border rounded-md">
          <Avatar className="h-10 w-10">
            <AvatarImage src={member.avatar} alt={member.name} />
            <AvatarFallback>{member.name[0]}</AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">{member.name}</p>
            <p className="text-sm text-muted-foreground">{member.phone}</p>
          </div>
        </div>

        <Tabs defaultValue="compose" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="compose">
              <MessageSquare className="mr-2 h-4 w-4" />
              撰写消息
            </TabsTrigger>
            <TabsTrigger value="templates">
              <FileText className="mr-2 h-4 w-4" />
              消息模板
            </TabsTrigger>
          </TabsList>

          <TabsContent value="compose" className="space-y-4 py-4">
            <div className="space-y-4">
              <div>
                <Label>消息类型</Label>
                <RadioGroup
                  defaultValue="sms"
                  className="flex space-x-4 mt-2"
                  value={messageType}
                  onValueChange={(value) => setMessageType(value as "sms" | "wechat" | "app")}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="sms" id="sms" />
                    <Label htmlFor="sms">短信</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="wechat" id="wechat" />
                    <Label htmlFor="wechat">微信</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="app" id="app" />
                    <Label htmlFor="app">APP通知</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message-content">消息内容</Label>
                <Textarea
                  id="message-content"
                  placeholder="输入消息内容..."
                  className="min-h-[120px]"
                  value={messageContent}
                  onChange={(e) => setMessageContent(e.target.value)}
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>已输入 {messageContent.length} 字</span>
                  {messageType === "sms" && (
                    <span>{Math.ceil(messageContent.length / 70)} 条短信</span>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="scheduled"
                  checked={isScheduled}
                  onCheckedChange={setIsScheduled}
                />
                <Label htmlFor="scheduled">定时发送</Label>
              </div>

              {isScheduled && (
                <div className="space-y-2">
                  <Label htmlFor="scheduled-time">发送时间</Label>
                  <Input
                    id="scheduled-time"
                    type="datetime-local"
                    value={scheduledTime}
                    onChange={(e) => setScheduledTime(e.target.value)}
                  />
                </div>
              )}

              <div className="flex gap-2">
                <Button variant="outline" size="sm" type="button">
                  <Image className="mr-2 h-4 w-4" />
                  添加图片
                </Button>
                <Button variant="outline" size="sm" type="button">
                  <Paperclip className="mr-2 h-4 w-4" />
                  添加附件
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="template-select">选择模板</Label>
                <Select
                  value={selectedTemplate}
                  onValueChange={handleSelectTemplate}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择消息模板" />
                  </SelectTrigger>
                  <SelectContent>
                    {messageTemplates.map(template => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="template-content">模板内容</Label>
                <Textarea
                  id="template-content"
                  placeholder="选择模板后显示内容..."
                  className="min-h-[120px]"
                  value={messageContent}
                  onChange={(e) => setMessageContent(e.target.value)}
                />
              </div>

              <div className="text-sm text-muted-foreground">
                <p>提示：模板中的变量会自动替换为会员信息</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSendMessage} disabled={isSending}>
            {isSending ? (
              <>发送中...</>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                {isScheduled ? "安排发送" : "立即发送"}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
