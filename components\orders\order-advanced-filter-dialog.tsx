"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { DateRange } from "react-day-picker"
import { addDays } from "date-fns"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Filter, X } from "lucide-react"

interface OrderAdvancedFilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApplyFilters: (filters: any) => void;
}

export function OrderAdvancedFilterDialog({ 
  open, 
  onOpenChange, 
  onApplyFilters 
}: OrderAdvancedFilterDialogProps) {
  const [activeTab, setActiveTab] = useState("basic")
  
  // 基本筛选
  const [orderStatus, setOrderStatus] = useState<string[]>([])
  const [paymentStatus, setPaymentStatus] = useState<string[]>([])
  const [paymentMethod, setPaymentMethod] = useState<string[]>([])
  const [deliveryMethod, setDeliveryMethod] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: new Date(),
  })
  const [minAmount, setMinAmount] = useState("")
  const [maxAmount, setMaxAmount] = useState("")
  
  // 高级筛选
  const [productType, setProductType] = useState<string[]>([])
  const [productCategory, setProductCategory] = useState<string[]>([])
  const [customerType, setCustomerType] = useState<string[]>([])
  const [source, setSource] = useState<string[]>([])
  const [region, setRegion] = useState<string[]>([])
  
  // 处理复选框变化
  const handleCheckboxChange = (
    value: string, 
    currentValues: string[], 
    setValues: (values: string[]) => void
  ) => {
    if (currentValues.includes(value)) {
      setValues(currentValues.filter(v => v !== value));
    } else {
      setValues([...currentValues, value]);
    }
  };
  
  // 重置所有筛选条件
  const handleResetFilters = () => {
    setOrderStatus([]);
    setPaymentStatus([]);
    setPaymentMethod([]);
    setDeliveryMethod([]);
    setDateRange({
      from: addDays(new Date(), -30),
      to: new Date(),
    });
    setMinAmount("");
    setMaxAmount("");
    setProductType([]);
    setProductCategory([]);
    setCustomerType([]);
    setSource([]);
    setRegion([]);
  };
  
  // 应用筛选条件
  const handleApplyFilters = () => {
    const filters = {
      orderStatus,
      paymentStatus,
      paymentMethod,
      deliveryMethod,
      dateRange,
      minAmount: minAmount ? parseFloat(minAmount) : undefined,
      maxAmount: maxAmount ? parseFloat(maxAmount) : undefined,
      productType,
      productCategory,
      customerType,
      source,
      region,
    };
    
    onApplyFilters(filters);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            高级筛选
          </DialogTitle>
          <DialogDescription>
            设置详细的筛选条件，精确查找订单
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">基本筛选</TabsTrigger>
            <TabsTrigger value="advanced">高级筛选</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">订单状态</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="status-pending" 
                    checked={orderStatus.includes("pending")}
                    onCheckedChange={() => handleCheckboxChange("pending", orderStatus, setOrderStatus)}
                  />
                  <Label htmlFor="status-pending">待处理</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="status-processing" 
                    checked={orderStatus.includes("processing")}
                    onCheckedChange={() => handleCheckboxChange("processing", orderStatus, setOrderStatus)}
                  />
                  <Label htmlFor="status-processing">处理中</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="status-completed" 
                    checked={orderStatus.includes("completed")}
                    onCheckedChange={() => handleCheckboxChange("completed", orderStatus, setOrderStatus)}
                  />
                  <Label htmlFor="status-completed">已完成</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="status-cancelled" 
                    checked={orderStatus.includes("cancelled")}
                    onCheckedChange={() => handleCheckboxChange("cancelled", orderStatus, setOrderStatus)}
                  />
                  <Label htmlFor="status-cancelled">已取消</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">支付状态</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="payment-paid" 
                    checked={paymentStatus.includes("paid")}
                    onCheckedChange={() => handleCheckboxChange("paid", paymentStatus, setPaymentStatus)}
                  />
                  <Label htmlFor="payment-paid">已支付</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="payment-unpaid" 
                    checked={paymentStatus.includes("unpaid")}
                    onCheckedChange={() => handleCheckboxChange("unpaid", paymentStatus, setPaymentStatus)}
                  />
                  <Label htmlFor="payment-unpaid">待支付</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="payment-refunded" 
                    checked={paymentStatus.includes("refunded")}
                    onCheckedChange={() => handleCheckboxChange("refunded", paymentStatus, setPaymentStatus)}
                  />
                  <Label htmlFor="payment-refunded">已退款</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="payment-failed" 
                    checked={paymentStatus.includes("failed")}
                    onCheckedChange={() => handleCheckboxChange("failed", paymentStatus, setPaymentStatus)}
                  />
                  <Label htmlFor="payment-failed">支付失败</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">支付方式</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="method-wechat" 
                    checked={paymentMethod.includes("wechat")}
                    onCheckedChange={() => handleCheckboxChange("wechat", paymentMethod, setPaymentMethod)}
                  />
                  <Label htmlFor="method-wechat">微信支付</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="method-alipay" 
                    checked={paymentMethod.includes("alipay")}
                    onCheckedChange={() => handleCheckboxChange("alipay", paymentMethod, setPaymentMethod)}
                  />
                  <Label htmlFor="method-alipay">支付宝</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="method-balance" 
                    checked={paymentMethod.includes("balance")}
                    onCheckedChange={() => handleCheckboxChange("balance", paymentMethod, setPaymentMethod)}
                  />
                  <Label htmlFor="method-balance">余额支付</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="method-offline" 
                    checked={paymentMethod.includes("offline")}
                    onCheckedChange={() => handleCheckboxChange("offline", paymentMethod, setPaymentMethod)}
                  />
                  <Label htmlFor="method-offline">线下支付</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">配送方式</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="delivery-logistics" 
                    checked={deliveryMethod.includes("logistics")}
                    onCheckedChange={() => handleCheckboxChange("logistics", deliveryMethod, setDeliveryMethod)}
                  />
                  <Label htmlFor="delivery-logistics">物流配送</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="delivery-self-pickup" 
                    checked={deliveryMethod.includes("self_pickup")}
                    onCheckedChange={() => handleCheckboxChange("self_pickup", deliveryMethod, setDeliveryMethod)}
                  />
                  <Label htmlFor="delivery-self-pickup">门店自提</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="delivery-verification" 
                    checked={deliveryMethod.includes("verification")}
                    onCheckedChange={() => handleCheckboxChange("verification", deliveryMethod, setDeliveryMethod)}
                  />
                  <Label htmlFor="delivery-verification">核销</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">订单日期</CardTitle>
              </CardHeader>
              <CardContent>
                <DatePickerWithRange 
                  className="w-full"
                  selected={dateRange} 
                  onSelect={setDateRange} 
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">订单金额</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <div className="space-y-2 flex-1">
                    <Label htmlFor="min-amount">最小金额</Label>
                    <Input
                      id="min-amount"
                      type="number"
                      placeholder="0.00"
                      value={minAmount}
                      onChange={(e) => setMinAmount(e.target.value)}
                    />
                  </div>
                  <div className="pt-8">至</div>
                  <div className="space-y-2 flex-1">
                    <Label htmlFor="max-amount">最大金额</Label>
                    <Input
                      id="max-amount"
                      type="number"
                      placeholder="不限"
                      value={maxAmount}
                      onChange={(e) => setMaxAmount(e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">商品类型</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="product-physical" 
                    checked={productType.includes("physical")}
                    onCheckedChange={() => handleCheckboxChange("physical", productType, setProductType)}
                  />
                  <Label htmlFor="product-physical">实物商品</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="product-virtual" 
                    checked={productType.includes("virtual")}
                    onCheckedChange={() => handleCheckboxChange("virtual", productType, setProductType)}
                  />
                  <Label htmlFor="product-virtual">虚拟商品</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="product-course" 
                    checked={productType.includes("course")}
                    onCheckedChange={() => handleCheckboxChange("course", productType, setProductType)}
                  />
                  <Label htmlFor="product-course">课程</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="product-membership" 
                    checked={productType.includes("membership")}
                    onCheckedChange={() => handleCheckboxChange("membership", productType, setProductType)}
                  />
                  <Label htmlFor="product-membership">会员</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">商品分类</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="category-yoga" 
                    checked={productCategory.includes("yoga")}
                    onCheckedChange={() => handleCheckboxChange("yoga", productCategory, setProductCategory)}
                  />
                  <Label htmlFor="category-yoga">瑜伽用品</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="category-apparel" 
                    checked={productCategory.includes("apparel")}
                    onCheckedChange={() => handleCheckboxChange("apparel", productCategory, setProductCategory)}
                  />
                  <Label htmlFor="category-apparel">服装</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="category-equipment" 
                    checked={productCategory.includes("equipment")}
                    onCheckedChange={() => handleCheckboxChange("equipment", productCategory, setProductCategory)}
                  />
                  <Label htmlFor="category-equipment">器材</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="category-nutrition" 
                    checked={productCategory.includes("nutrition")}
                    onCheckedChange={() => handleCheckboxChange("nutrition", productCategory, setProductCategory)}
                  />
                  <Label htmlFor="category-nutrition">营养品</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">客户类型</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="customer-member" 
                    checked={customerType.includes("member")}
                    onCheckedChange={() => handleCheckboxChange("member", customerType, setCustomerType)}
                  />
                  <Label htmlFor="customer-member">会员</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="customer-guest" 
                    checked={customerType.includes("guest")}
                    onCheckedChange={() => handleCheckboxChange("guest", customerType, setCustomerType)}
                  />
                  <Label htmlFor="customer-guest">游客</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="customer-vip" 
                    checked={customerType.includes("vip")}
                    onCheckedChange={() => handleCheckboxChange("vip", customerType, setCustomerType)}
                  />
                  <Label htmlFor="customer-vip">VIP</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="customer-new" 
                    checked={customerType.includes("new")}
                    onCheckedChange={() => handleCheckboxChange("new", customerType, setCustomerType)}
                  />
                  <Label htmlFor="customer-new">新客户</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">订单来源</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="source-website" 
                    checked={source.includes("website")}
                    onCheckedChange={() => handleCheckboxChange("website", source, setSource)}
                  />
                  <Label htmlFor="source-website">官网</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="source-wechat" 
                    checked={source.includes("wechat")}
                    onCheckedChange={() => handleCheckboxChange("wechat", source, setSource)}
                  />
                  <Label htmlFor="source-wechat">微信小程序</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="source-app" 
                    checked={source.includes("app")}
                    onCheckedChange={() => handleCheckboxChange("app", source, setSource)}
                  />
                  <Label htmlFor="source-app">APP</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="source-store" 
                    checked={source.includes("store")}
                    onCheckedChange={() => handleCheckboxChange("store", source, setSource)}
                  />
                  <Label htmlFor="source-store">门店</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">地区</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="region-north" 
                    checked={region.includes("north")}
                    onCheckedChange={() => handleCheckboxChange("north", region, setRegion)}
                  />
                  <Label htmlFor="region-north">华北</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="region-east" 
                    checked={region.includes("east")}
                    onCheckedChange={() => handleCheckboxChange("east", region, setRegion)}
                  />
                  <Label htmlFor="region-east">华东</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="region-south" 
                    checked={region.includes("south")}
                    onCheckedChange={() => handleCheckboxChange("south", region, setRegion)}
                  />
                  <Label htmlFor="region-south">华南</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="region-west" 
                    checked={region.includes("west")}
                    onCheckedChange={() => handleCheckboxChange("west", region, setRegion)}
                  />
                  <Label htmlFor="region-west">西部</Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex items-center justify-between mt-4">
          <Button variant="outline" onClick={handleResetFilters}>
            <X className="h-4 w-4 mr-2" />
            重置筛选
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleApplyFilters}>
              应用筛选
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
