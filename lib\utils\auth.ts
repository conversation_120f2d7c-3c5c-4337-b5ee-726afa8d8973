// 用户信息工具函数

export interface UserInfo {
  id: number | string;
  username: string;
  nickname?: string;
  avatar?: string;
  email?: string;
  phone?: string;
  role: string;
  status: string;
  tenantId: number;
  permissions?: string[];
}

/**
 * 获取当前登录用户信息
 * @returns 用户信息对象或null
 */
export const getUserInfo = (): UserInfo | null => {
  if (typeof window !== 'undefined') {
    try {
      const userInfo = localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo');
      return userInfo ? JSON.parse(userInfo) : null;
    } catch (error) {
      console.error('解析用户信息失败:', error);
      return null;
    }
  }
  return null;
};

/**
 * 获取当前用户的租户ID
 * @returns 租户ID或null
 */
export const getTenantId = (): number | null => {
  const userInfo = getUserInfo();
  return userInfo?.tenantId || null;
};

/**
 * 获取当前用户的访问令牌
 * @returns 访问令牌或null
 */
export const getToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('token') || sessionStorage.getItem('token');
  }
  return null;
};

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export const isAuthenticated = (): boolean => {
  const token = getToken();
  const userInfo = getUserInfo();
  return !!(token && userInfo);
};

/**
 * 检查用户是否有指定权限
 * @param permission 权限名称
 * @returns 是否有权限
 */
export const hasPermission = (permission: string): boolean => {
  const userInfo = getUserInfo();
  if (!userInfo || !userInfo.permissions) {
    return false;
  }
  
  // 超级管理员拥有所有权限
  if (userInfo.permissions.includes('*')) {
    return true;
  }
  
  return userInfo.permissions.includes(permission);
};

/**
 * 检查用户是否为管理员
 * @returns 是否为管理员
 */
export const isAdmin = (): boolean => {
  const userInfo = getUserInfo();
  return userInfo?.role === 'admin' || userInfo?.role === 'super-admin';
};

/**
 * 清除用户认证信息
 */
export const clearAuthInfo = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userInfo');
  }
};

/**
 * 保存用户认证信息
 * @param token 访问令牌
 * @param userInfo 用户信息
 * @param rememberMe 是否记住登录状态
 */
export const saveAuthInfo = (token: string, userInfo: UserInfo, rememberMe: boolean = false): void => {
  if (typeof window !== 'undefined') {
    const storage = rememberMe ? localStorage : sessionStorage;
    storage.setItem('token', token);
    storage.setItem('userInfo', JSON.stringify(userInfo));
  }
};
