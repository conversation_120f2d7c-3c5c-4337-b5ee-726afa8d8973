"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>nt, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  Edit, 
  Users 
} from "lucide-react"

import { mockSeriesCourses, mockSeriesSchedules, mockSeriesEnrollments } from "@/lib/mock/series-courses"
import { SeriesCourse, SeriesCourseStatus } from "@/types/series-courses"

export default function SeriesCourseDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const courseId = params.id
  const [showAutoBookingDialog, setShowAutoBookingDialog] = useState(false)
  
  // 获取课程详情
  const course = mockSeriesCourses.find(c => c.id === courseId)
  
  // 获取课程排期
  const schedules = mockSeriesSchedules.filter(s => s.seriesCourseId === courseId)
  
  // 获取课程报名
  const enrollments = mockSeriesEnrollments.filter(e => e.seriesCourseId === courseId)
  
  // 如果课程不存在，显示错误信息
  if (!course) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold tracking-tight ml-2">课程不存在</h1>
        </div>
        <p>未找到ID为 {courseId} 的系列课程。</p>
        <Button onClick={() => router.push("/courses/series-courses")} className="mt-4">
          返回系列课程列表
        </Button>
      </div>
    )
  }
  
  // 确认自动约课
  const confirmAutoBooking = () => {
    toast({
      title: "自动约课成功",
      description: "已为所有报名学员自动约课，并发送通知。",
    })
    setShowAutoBookingDialog(false)
  }
  
  // 获取状态标签样式
  const getStatusBadge = (status: SeriesCourseStatus) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline">草稿</Badge>
      case "published":
        return <Badge variant="default">已发布</Badge>
      case "completed":
        return <Badge variant="secondary">已结束</Badge>
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" onClick={() => router.push("/courses/series-courses")}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold tracking-tight ml-2">{course.name}</h1>
        <div className="ml-4">{getStatusBadge(course.status)}</div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>课程详情</CardTitle>
              <CardDescription>系列课程基本信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">课程名称</h3>
                    <p>{course.name}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">课程类型</h3>
                    <p>{course.courseType}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">主讲教练</h3>
                    <p>{course.instructor}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">课时数</h3>
                    <p>{course.totalSessions}节</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">课程周期</h3>
                    <p>{course.startDate} 至 {course.endDate}</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">课程价格</h3>
                    <p className="font-medium">¥{course.price}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">学员人数</h3>
                    <p>{course.enrolledStudents}/{course.maxStudents}人</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">课程状态</h3>
                    <p>{getStatusBadge(course.status)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">标签</h3>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {course.tags?.map((tag, index) => (
                        <Badge key={index} variant="secondary">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6">
                <h3 className="text-sm font-medium text-muted-foreground mb-1">课程描述</h3>
                <p className="whitespace-pre-line">{course.description}</p>
              </div>
            </CardContent>
            <CardFooter className="flex flex-wrap gap-2">
              <Button 
                variant="outline" 
                onClick={() => router.push(`/courses/series-courses/${course.id}/edit`)}
              >
                <Edit className="mr-2 h-4 w-4" />
                编辑课程
              </Button>
              <Button 
                variant="outline" 
                onClick={() => router.push(`/courses/series-courses/${course.id}/schedule`)}
              >
                <Calendar className="mr-2 h-4 w-4" />
                管理排期
              </Button>
              <Button 
                variant="outline" 
                onClick={() => router.push(`/courses/series-courses/${course.id}/enrollments`)}
              >
                <Users className="mr-2 h-4 w-4" />
                管理报名
              </Button>
              {course.status === "published" && (
                <Button 
                  variant="default" 
                  onClick={() => setShowAutoBookingDialog(true)}
                >
                  <Clock className="mr-2 h-4 w-4" />
                  一键约课
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
        
        <div>
          <Card>
            <CardHeader>
              <CardTitle>课程统计</CardTitle>
              <CardDescription>课程数据概览</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">总课时数</span>
                  <span className="font-medium">{course.totalSessions}节</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">已完成课时</span>
                  <span className="font-medium">{schedules.filter(s => s.status === "completed").length}节</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">报名人数</span>
                  <span className="font-medium">{enrollments.length}人</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">报名率</span>
                  <span className="font-medium">{Math.round((enrollments.length / course.maxStudents) * 100)}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">总收入</span>
                  <span className="font-medium">¥{enrollments.reduce((sum, e) => sum + e.paymentAmount, 0)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {course.coverImage && (
            <Card className="mt-6 overflow-hidden">
              <CardHeader>
                <CardTitle>课程封面</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <img 
                  src={course.coverImage} 
                  alt={course.name} 
                  className="w-full h-auto"
                />
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      
      <Tabs defaultValue="schedule" className="mb-6">
        <TabsList>
          <TabsTrigger value="schedule">课程排期</TabsTrigger>
          <TabsTrigger value="enrollments">报名学员</TabsTrigger>
        </TabsList>
        
        <TabsContent value="schedule" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>课程排期</CardTitle>
                <CardDescription>系列课程的所有课次安排</CardDescription>
              </div>
              <Button onClick={() => router.push(`/courses/series-courses/${course.id}/schedule`)}>
                管理排期
              </Button>
            </CardHeader>
            <CardContent>
              {schedules.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  暂无课程排期，请点击"管理排期"添加。
                </div>
              ) : (
                <div className="border rounded-md overflow-hidden">
                  <div className="grid grid-cols-12 bg-muted px-4 py-3 font-medium text-sm">
                    <div className="col-span-1">课次</div>
                    <div className="col-span-3">课程标题</div>
                    <div className="col-span-2">上课日期</div>
                    <div className="col-span-2">上课时间</div>
                    <div className="col-span-2">教练</div>
                    <div className="col-span-2">状态</div>
                  </div>
                  
                  <div className="divide-y">
                    {schedules.map((schedule) => (
                      <div key={schedule.id} className="grid grid-cols-12 px-4 py-3 hover:bg-accent/50">
                        <div className="col-span-1 flex items-center">
                          <span>第{schedule.sessionNumber}节</span>
                        </div>
                        <div className="col-span-3 flex items-center">
                          <span>{schedule.title}</span>
                        </div>
                        <div className="col-span-2 flex items-center">
                          <span>{schedule.date}</span>
                        </div>
                        <div className="col-span-2 flex items-center">
                          <span>{schedule.startTime}-{schedule.endTime}</span>
                        </div>
                        <div className="col-span-2 flex items-center">
                          <span>{schedule.instructor}</span>
                        </div>
                        <div className="col-span-2 flex items-center">
                          {schedule.status === "scheduled" && <Badge variant="outline">待上课</Badge>}
                          {schedule.status === "completed" && <Badge variant="default">已完成</Badge>}
                          {schedule.status === "cancelled" && <Badge variant="destructive">已取消</Badge>}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="enrollments" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>报名学员</CardTitle>
                <CardDescription>已报名该系列课程的学员</CardDescription>
              </div>
              <Button onClick={() => router.push(`/courses/series-courses/${course.id}/enrollments`)}>
                管理报名
              </Button>
            </CardHeader>
            <CardContent>
              {enrollments.length === 0 ? (
                <div className="text-center py-6 text-muted-foreground">
                  暂无学员报名，请点击"管理报名"添加。
                </div>
              ) : (
                <div className="border rounded-md overflow-hidden">
                  <div className="grid grid-cols-12 bg-muted px-4 py-3 font-medium text-sm">
                    <div className="col-span-3">学员姓名</div>
                    <div className="col-span-2">报名日期</div>
                    <div className="col-span-3">会员卡</div>
                    <div className="col-span-2">支付金额</div>
                    <div className="col-span-2">支付状态</div>
                  </div>
                  
                  <div className="divide-y">
                    {enrollments.map((enrollment) => (
                      <div key={enrollment.id} className="grid grid-cols-12 px-4 py-3 hover:bg-accent/50">
                        <div className="col-span-3 flex items-center">
                          <span>{enrollment.memberName}</span>
                        </div>
                        <div className="col-span-2 flex items-center">
                          <span>{enrollment.enrollDate}</span>
                        </div>
                        <div className="col-span-3 flex items-center">
                          <span>{enrollment.membershipCardName || "直接支付"}</span>
                        </div>
                        <div className="col-span-2 flex items-center">
                          <span>¥{enrollment.paymentAmount}</span>
                        </div>
                        <div className="col-span-2 flex items-center">
                          {enrollment.paymentStatus === "pending" && <Badge variant="outline">待支付</Badge>}
                          {enrollment.paymentStatus === "paid" && <Badge variant="default">已支付</Badge>}
                          {enrollment.paymentStatus === "refunded" && <Badge variant="destructive">已退款</Badge>}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* 自动约课确认对话框 */}
      <Dialog open={showAutoBookingDialog} onOpenChange={setShowAutoBookingDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认自动约课</DialogTitle>
            <DialogDescription>
              系统将为所有报名学员自动约课，并发送通知。
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>
              您确定要为
              <span className="font-medium mx-1">
                {course.name}
              </span>
              的所有报名学员自动约课吗？
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              此操作将为所有报名学员自动约课，如果学员已经约过某节课，将不会重复约课。
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAutoBookingDialog(false)}>
              取消
            </Button>
            <Button onClick={confirmAutoBooking}>
              确认约课
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
