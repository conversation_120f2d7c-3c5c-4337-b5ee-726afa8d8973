"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Plus, Trash, Edit, Search, Filter, Download, Upload } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// 模拟课程类型数据
const courseTypes = [
  { id: "group", name: "团课", color: "#4285F4" },
  { id: "small", name: "小班课", color: "#34A853" },
  { id: "premium", name: "精品课", color: "#FBBC05" },
  { id: "private", name: "私教课", color: "#EA4335" },
  { id: "training", name: "教培课", color: "#9C27B0" },
]

// 模拟课程数据
const courses = [
  { id: "C001", name: "哈他瑜伽初级", typeId: "group", typeName: "团课", color: "#4285F4" },
  { id: "C002", name: "阴瑜伽", typeId: "group", typeName: "团课", color: "#4285F4" },
  { id: "C003", name: "流瑜伽中级", typeId: "small", typeName: "小班课", color: "#34A853" },
  { id: "C004", name: "私教课", typeId: "private", typeName: "私教课", color: "#EA4335" },
  { id: "C005", name: "高级瑜伽进阶", typeId: "premium", typeName: "精品课", color: "#FBBC05" },
  { id: "C006", name: "教练培训课", typeId: "training", typeName: "教培课", color: "#9C27B0" },
  { id: "C007", name: "孕产瑜伽", typeId: "small", typeName: "小班课", color: "#34A853" },
  { id: "C008", name: "空中瑜伽", typeId: "premium", typeName: "精品课", color: "#FBBC05" },
]

// 模拟教练数据
const coaches = [
  { id: "1", name: "张教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "2", name: "李教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "3", name: "王教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "4", name: "赵教练", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "5", name: "刘教练", avatar: "/placeholder.svg?height=32&width=32" },
]

// 模拟课时费数据
const initialHourlyRates = [
  // 按课程类型的课时费
  { id: "1", coachId: "1", courseTypeId: "group", courseId: null, rate: 100, isDefault: true },
  { id: "2", coachId: "1", courseTypeId: "small", courseId: null, rate: 120, isDefault: true },
  { id: "3", coachId: "1", courseTypeId: "premium", courseId: null, rate: 150, isDefault: true },
  { id: "4", coachId: "2", courseTypeId: "group", courseId: null, rate: 110, isDefault: true },
  { id: "5", coachId: "2", courseTypeId: "private", courseId: null, rate: 200, isDefault: true },
  { id: "6", coachId: "3", courseTypeId: "training", courseId: null, rate: 180, isDefault: true },

  // 按具体课程的课时费
  { id: "7", coachId: "1", courseTypeId: "group", courseId: "C001", rate: 120, isDefault: false },
  { id: "8", coachId: "2", courseTypeId: "premium", courseId: "C005", rate: 180, isDefault: false },
  { id: "9", coachId: "3", courseTypeId: "small", courseId: "C003", rate: 150, isDefault: false },
]

interface HourlyRateManagementProps {
  className?: string
}

export function HourlyRateManagement({ className }: HourlyRateManagementProps) {
  const { toast } = useToast()
  const [hourlyRates, setHourlyRates] = useState(initialHourlyRates)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCoach, setSelectedCoach] = useState<string>("all")
  const [selectedCourseType, setSelectedCourseType] = useState<string>("all")
  const [selectedCourse, setSelectedCourse] = useState<string>("all")
  const [rateType, setRateType] = useState<"all" | "default" | "specific">("all")

  // 编辑相关状态
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [editingRate, setEditingRate] = useState<any>(null)
  const [newRate, setNewRate] = useState({
    coachId: "",
    courseTypeId: "",
    courseId: "",
    rate: "",
    isDefault: true
  })

  // 重置新增表单
  const resetNewRateForm = () => {
    setNewRate({
      coachId: "",
      courseTypeId: "",
      courseId: "",
      rate: "",
      isDefault: true
    })
  }

  // 打开添加对话框
  const handleAddRate = () => {
    resetNewRateForm()
    setEditingRate(null)
    setShowAddDialog(true)
  }

  // 打开编辑对话框
  const handleEditRate = (rate: any) => {
    setEditingRate(rate)
    setNewRate({
      coachId: rate.coachId,
      courseTypeId: rate.courseTypeId,
      courseId: rate.courseId || "",
      rate: rate.rate.toString(),
      isDefault: !!rate.isDefault
    })
    setShowAddDialog(true)
  }

  // 删除课时费
  const handleDeleteRate = (id: string) => {
    setHourlyRates(hourlyRates.filter(rate => rate.id !== id))
    toast({
      title: "删除成功",
      description: "课时费设置已删除"
    })
  }

  // 保存课时费
  const handleSaveRate = () => {
    // 验证表单
    if (!newRate.coachId || !newRate.courseTypeId || !newRate.rate) {
      toast({
        title: "表单不完整",
        description: "请填写所有必填字段",
        variant: "destructive"
      })
      return
    }

    // 如果是具体课程的课时费，必须选择课程
    if (!newRate.isDefault && !newRate.courseId) {
      toast({
        title: "表单不完整",
        description: "请选择具体课程",
        variant: "destructive"
      })
      return
    }

    // 检查是否已存在相同教练和课程类型/课程的记录
    const existingIndex = hourlyRates.findIndex(
      rate => rate.coachId === newRate.coachId &&
             rate.courseTypeId === newRate.courseTypeId &&
             rate.isDefault === newRate.isDefault &&
             (newRate.isDefault || rate.courseId === newRate.courseId) &&
             (!editingRate || rate.id !== editingRate.id)
    )

    if (existingIndex !== -1) {
      toast({
        title: "记录已存在",
        description: newRate.isDefault
          ? "该教练的此课程类型默认课时费已设置，请直接编辑"
          : "该教练的此课程课时费已设置，请直接编辑",
        variant: "destructive"
      })
      return
    }

    if (editingRate) {
      // 更新现有记录
      setHourlyRates(hourlyRates.map(rate =>
        rate.id === editingRate.id
          ? {
              ...rate,
              coachId: newRate.coachId,
              courseTypeId: newRate.courseTypeId,
              courseId: newRate.isDefault ? null : newRate.courseId,
              rate: parseInt(newRate.rate),
              isDefault: newRate.isDefault
            }
          : rate
      ))
      toast({
        title: "更新成功",
        description: "课时费设置已更新"
      })
    } else {
      // 添加新记录
      const newId = (Math.max(...hourlyRates.map(r => parseInt(r.id))) + 1).toString()
      setHourlyRates([
        ...hourlyRates,
        {
          id: newId,
          coachId: newRate.coachId,
          courseTypeId: newRate.courseTypeId,
          courseId: newRate.isDefault ? null : newRate.courseId,
          rate: parseInt(newRate.rate),
          isDefault: newRate.isDefault
        }
      ])
      toast({
        title: "添加成功",
        description: "新的课时费设置已添加"
      })
    }

    setShowAddDialog(false)
    resetNewRateForm()
    setEditingRate(null)
  }

  // 过滤课时费记录
  const filteredRates = hourlyRates.filter(rate => {
    // 按教练筛选
    if (selectedCoach !== "all" && rate.coachId !== selectedCoach) {
      return false
    }

    // 按课程类型筛选
    if (selectedCourseType !== "all" && rate.courseTypeId !== selectedCourseType) {
      return false
    }

    // 按具体课程筛选
    if (selectedCourse !== "all" && rate.courseId !== selectedCourse) {
      return false
    }

    // 按课时费类型筛选（默认/具体课程）
    if (rateType !== "all") {
      if (rateType === "default" && !rate.isDefault) {
        return false
      }
      if (rateType === "specific" && rate.isDefault) {
        return false
      }
    }

    // 按搜索关键词筛选
    if (searchQuery) {
      const coach = coaches.find(c => c.id === rate.coachId)
      const courseType = courseTypes.find(t => t.id === rate.courseTypeId)
      const course = rate.courseId ? courses.find(c => c.id === rate.courseId) : null

      const coachName = coach?.name || ""
      const courseTypeName = courseType?.name || ""
      const courseName = course?.name || ""

      return coachName.toLowerCase().includes(searchQuery.toLowerCase()) ||
             courseTypeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
             courseName.toLowerCase().includes(searchQuery.toLowerCase())
    }

    return true
  })

  // 获取教练名称
  const getCoachName = (coachId: string) => {
    const coach = coaches.find(c => c.id === coachId)
    return coach ? coach.name : "未知教练"
  }

  // 获取课程类型信息
  const getCourseTypeInfo = (courseTypeId: string) => {
    const courseType = courseTypes.find(t => t.id === courseTypeId)
    return courseType ? { name: courseType.name, color: courseType.color } : { name: "未知类型", color: "#999999" }
  }

  // 获取课程信息
  const getCourseInfo = (courseId: string | null) => {
    if (!courseId) return null
    const course = courses.find(c => c.id === courseId)
    return course || null
  }

  return (
    <div className={className}>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">课时费管理</h2>
          <p className="text-muted-foreground">管理不同教练和课程类型的课时费标准</p>
        </div>
        <Button onClick={handleAddRate}>
          <Plus className="mr-2 h-4 w-4" />
          添加课时费
        </Button>
      </div>

      <div className="flex flex-col gap-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="w-full md:w-1/3 relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="搜索教练、课程类型或课程"
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="flex gap-4 w-full md:w-2/3">
            <Select value={selectedCoach} onValueChange={setSelectedCoach}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="选择教练" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有教练</SelectItem>
                {coaches.map((coach) => (
                  <SelectItem key={coach.id} value={coach.id}>
                    {coach.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedCourseType} onValueChange={setSelectedCourseType}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="选择课程类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有课程类型</SelectItem>
                {courseTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id}>
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full" style={{ backgroundColor: type.color }} />
                      <span>{type.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <Select value={selectedCourse} onValueChange={setSelectedCourse}>
            <SelectTrigger className="w-full md:w-1/2">
              <SelectValue placeholder="选择具体课程" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有课程</SelectItem>
              {courses.map((course) => (
                <SelectItem key={course.id} value={course.id}>
                  <div className="flex items-center gap-2">
                    <div className="h-3 w-3 rounded-full" style={{ backgroundColor: course.color }} />
                    <span>{course.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={rateType} onValueChange={(value: "all" | "default" | "specific") => setRateType(value)}>
            <SelectTrigger className="w-full md:w-1/2">
              <SelectValue placeholder="课时费类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              <SelectItem value="default">默认课时费</SelectItem>
              <SelectItem value="specific">具体课程课时费</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>教练</TableHead>
                <TableHead>课程类型</TableHead>
                <TableHead>课程</TableHead>
                <TableHead>类型</TableHead>
                <TableHead className="text-right">课时费(元/课时)</TableHead>
                <TableHead className="w-[100px]">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRates.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                    没有找到匹配的课时费记录
                  </TableCell>
                </TableRow>
              ) : (
                filteredRates.map((rate) => {
                  const courseTypeInfo = getCourseTypeInfo(rate.courseTypeId)
                  const courseInfo = getCourseInfo(rate.courseId)
                  return (
                    <TableRow key={rate.id}>
                      <TableCell>{getCoachName(rate.coachId)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div
                            className="h-3 w-3 rounded-full"
                            style={{ backgroundColor: courseTypeInfo.color }}
                          />
                          <span>{courseTypeInfo.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {courseInfo ? (
                          <div className="flex items-center gap-2">
                            <div
                              className="h-3 w-3 rounded-full"
                              style={{ backgroundColor: courseInfo.color }}
                            />
                            <span>{courseInfo.name}</span>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={rate.isDefault ? "outline" : "secondary"}>
                          {rate.isDefault ? "默认" : "具体课程"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right font-medium">¥{rate.rate}</TableCell>
                      <TableCell>
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditRate(rate)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteRate(rate.id)}
                          >
                            <Trash className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 添加/编辑课时费对话框 */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{editingRate ? "编辑课时费" : "添加课时费"}</DialogTitle>
            <DialogDescription>
              {editingRate
                ? (editingRate.isDefault
                    ? "修改教练的课程类型默认课时费设置"
                    : "修改教练的具体课程课时费设置")
                : "为教练设置课程类型默认课时费或具体课程课时费"}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="coach">教练</Label>
              <Select
                value={newRate.coachId}
                onValueChange={(value) => setNewRate({...newRate, coachId: value})}
                disabled={!!editingRate}
              >
                <SelectTrigger id="coach">
                  <SelectValue placeholder="选择教练" />
                </SelectTrigger>
                <SelectContent>
                  {coaches.map((coach) => (
                    <SelectItem key={coach.id} value={coach.id}>
                      {coach.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="courseType">课程类型</Label>
              <Select
                value={newRate.courseTypeId}
                onValueChange={(value) => setNewRate({...newRate, courseTypeId: value})}
                disabled={!!editingRate}
              >
                <SelectTrigger id="courseType">
                  <SelectValue placeholder="选择课程类型" />
                </SelectTrigger>
                <SelectContent>
                  {courseTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full" style={{ backgroundColor: type.color }} />
                        <span>{type.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isDefault"
                  checked={newRate.isDefault}
                  onCheckedChange={(checked) => setNewRate({...newRate, isDefault: !!checked, courseId: ""})}
                />
                <Label htmlFor="isDefault">设为默认课时费</Label>
              </div>
              <p className="text-sm text-muted-foreground">
                默认课时费适用于该教练的所有同类型课程，除非为特定课程单独设置
              </p>
            </div>

            {!newRate.isDefault && (
              <div className="grid gap-2">
                <Label htmlFor="course">具体课程</Label>
                <Select
                  value={newRate.courseId}
                  onValueChange={(value) => setNewRate({...newRate, courseId: value})}
                  disabled={!!editingRate}
                >
                  <SelectTrigger id="course">
                    <SelectValue placeholder="选择课程" />
                  </SelectTrigger>
                  <SelectContent>
                    {courses
                      .filter(course => course.typeId === newRate.courseTypeId)
                      .map((course) => (
                        <SelectItem key={course.id} value={course.id}>
                          <div className="flex items-center gap-2">
                            <div className="h-3 w-3 rounded-full" style={{ backgroundColor: course.color }} />
                            <span>{course.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="rate">课时费(元/课时)</Label>
              <Input
                id="rate"
                type="number"
                min="0"
                step="1"
                value={newRate.rate}
                onChange={(e) => setNewRate({...newRate, rate: e.target.value})}
                placeholder="输入课时费金额"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              取消
            </Button>
            <Button onClick={handleSaveRate}>
              {editingRate ? "更新" : "添加"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
