import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 更新会员卡状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const data = await request.json();
    
    console.log('更新会员卡状态，ID:', id, '状态:', data.status);
    
    if (!data.status) {
      return NextResponse.json({
        code: 400,
        msg: '缺少状态参数',
        data: null
      }, { status: 400 });
    }
    
    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);
    
    try {
      // 更新会员卡状态
      const [result] = await connection.execute(
        'UPDATE member_card_types SET status = ?, updated_at = NOW() WHERE id = ?',
        [data.status, parseInt(id)]
      );
      
      const affectedRows = (result as any).affectedRows;
      
      if (affectedRows === 0) {
        return NextResponse.json({
          code: 404,
          msg: '会员卡不存在',
          data: null
        }, { status: 404 });
      }
      
      // 查询更新后的会员卡
      const [updatedRows] = await connection.execute(
        'SELECT * FROM member_card_types WHERE id = ?',
        [parseInt(id)]
      );
      
      const updatedCard = (updatedRows as any[])[0];
      
      console.log('会员卡状态更新成功:', updatedCard.name, '状态:', updatedCard.status);
      
      return NextResponse.json({
        code: 0,
        data: { status: updatedCard.status },
        msg: '更新会员卡状态成功'
      });
      
    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('更新会员卡状态失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '更新会员卡状态失败',
      data: null
    }, { status: 500 });
  }
}
