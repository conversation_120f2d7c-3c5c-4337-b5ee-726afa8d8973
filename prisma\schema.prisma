// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}
model Tenant {
  id               Int       @id @default(autoincrement())
  tenant_name      String
  contact_person   String?
  phone            String?
  email            String?
  address          String?
  logo_url         String?
  city             String?
  province         String?
  country          String?
  business_license String?
  valid_start      DateTime?
  valid_end        DateTime?
  status           Int?
  created_at       DateTime? @default(now())
  updated_at       DateTime? @updatedAt
  stores           Store[]
  employees        Employee[]
}

model Store {
  id            Int      @id @default(autoincrement())
  tenant_id     Int
  store_name    String
  contact_person String?
  phone         String?
  address       String?
  business_hours String?
  status        Int?
  created_at    DateTime? @default(now())
  updated_at    DateTime? @updatedAt
  tenant        Tenant    @relation(fields: [tenant_id], references: [id])
}

model Employee {
  id            Int      @id @default(autoincrement())
  tenant_id     Int
  employee_no   String?
  real_name     String?
  phone         String?
  email         String?
  password_hash String?
  role_id       Int?
  avatar_url    String?
  is_super_admin Int?
  openid        String?
  unionid       String?
  status        Int?
  created_at    DateTime? @default(now())
  updated_at    DateTime? @updatedAt
  tenant        Tenant    @relation(fields: [tenant_id], references: [id])
}