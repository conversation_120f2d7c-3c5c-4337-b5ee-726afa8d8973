// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id               Int        @id @default(autoincrement())
  tenant_name      String
  contact_person   String?
  phone            String?
  email            String?
  address          String?
  logo_url         String?
  city             String?
  province         String?
  district         String?
  country          String?
  business_license String?
  valid_start      DateTime?
  valid_end        DateTime?
  status           Int?
  created_at       DateTime? @default(now())
  updated_at       DateTime? @updatedAt
  stores           Store[]
  employees        Employee[]
  courseTypes      CourseType[]
  courses          Course[]
  coaches          Coach[]
  venues           Venue[]
}

model Store {
  id            Int      @id @default(autoincrement())
  tenant_id     Int
  store_name    String
  contact_person String?
  phone         String?
  address       String?
  business_hours String?
  status        Int?
  created_at    DateTime? @default(now())
  updated_at    DateTime? @updatedAt
  tenant        Tenant    @relation(fields: [tenant_id], references: [id])
}

model Employee {
  id            Int      @id @default(autoincrement())
  tenant_id     Int
  employee_no   String?
  real_name     String?
  phone         String?
  email         String?
  password_hash String?
  role_id       Int?
  avatar_url    String?
  is_super_admin Int?
  openid        String?
  unionid       String?
  status        Int?
  created_at    DateTime? @default(now())
  updated_at    DateTime? @updatedAt
  tenant        Tenant    @relation(fields: [tenant_id], references: [id])
}

model CourseType {
  id            Int      @id @default(autoincrement())
  tenant_id     Int
  name          String
  description   String?
  color         String?
  status        Int?     @default(1) // 1=active, 0=inactive
  display_order Int?     @default(0)
  course_count  Int?     @default(0)
  created_at    DateTime? @default(now())
  updated_at    DateTime? @updatedAt
  tenant        Tenant    @relation(fields: [tenant_id], references: [id])
  courses       Course[]  // 关联的课程
}

model Coach {
  id            Int      @id @default(autoincrement())
  tenant_id     Int
  name          String   // 教练姓名
  phone         String?  // 联系电话
  email         String?  // 邮箱
  avatar        String?  // 头像URL
  bio           String?  // 个人简介
  specialties   String?  // 专业特长（JSON格式）
  certifications String? // 认证资质（JSON格式）
  experience    Int?     // 从业年限
  hourly_rate   Decimal? @db.Decimal(10, 2) // 课时费
  status        Int?     @default(1) // 1=active, 0=inactive
  created_at    DateTime? @default(now())
  updated_at    DateTime? @updatedAt

  // 关联关系
  tenant        Tenant   @relation(fields: [tenant_id], references: [id])
  courses       Course[] // 教练的课程
}

model Venue {
  id            Int      @id @default(autoincrement())
  tenant_id     Int
  name          String   // 场地名称
  location      String?  // 位置描述
  capacity      Int?     // 容量
  area          Decimal? @db.Decimal(8, 2) // 面积（平方米）
  equipment     String?  // 设备清单（JSON格式）
  description   String?  // 场地描述
  hourly_rate   Decimal? @db.Decimal(10, 2) // 场地租金
  status        Int?     @default(1) // 1=active, 0=inactive
  created_at    DateTime? @default(now())
  updated_at    DateTime? @updatedAt

  // 关联关系
  tenant        Tenant   @relation(fields: [tenant_id], references: [id])
  courses       Course[] // 场地的课程
}

model Course {
  id            Int      @id @default(autoincrement())
  tenant_id     Int
  title         String   // 课程名称
  description   String?  // 课程描述
  price         Decimal? @db.Decimal(10, 2) // 课程价格
  cover         String?  // 封面图片URL
  type_id       Int?     // 课程类型ID
  content       String?  // 课程内容
  duration      Int?     // 课程时长（分钟）
  level         String?  // 课程难度等级
  coach_id      Int?     // 教练ID
  time          String?  // 上课时间
  venue_id      Int?     // 场地ID
  capacity      Int?     // 课程容量
  status        Int?     @default(1) // 1=active, 0=inactive
  created_at    DateTime? @default(now())
  updated_at    DateTime? @updatedAt

  // 关联关系
  tenant        Tenant     @relation(fields: [tenant_id], references: [id])
  course_type   CourseType? @relation(fields: [type_id], references: [id])
  coach         Coach?     @relation(fields: [coach_id], references: [id])
  venue         Venue?     @relation(fields: [venue_id], references: [id])
}