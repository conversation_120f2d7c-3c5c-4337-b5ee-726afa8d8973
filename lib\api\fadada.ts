import axiosInstance from '../axios-config';

/**
 * 法大大电子合同API接口
 */
export interface FadadaConfig {
  appId: string;
  appSecret: string;
  serverUrl: string;
}

export interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  previewUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface ContractParty {
  type: 'COMPANY' | 'PERSON';
  name: string;
  idType?: string;
  idNumber?: string;
  mobile?: string;
  email?: string;
  companyName?: string;
  legalRepresentative?: string;
}

export interface ContractData {
  id?: string;
  title: string;
  templateId: string;
  serialNumber?: string;
  status?: 'DRAFT' | 'SIGNING' | 'COMPLETED' | 'REJECTED' | 'EXPIRED';
  parties: ContractParty[];
  variables?: Record<string, string>;
  expireTime?: string;
  createdAt?: string;
  completedAt?: string;
  contractFileUrl?: string;
}

/**
 * 法大大电子合同API服务
 */
export const fadadaApi = {
  /**
   * 获取合同模板列表
   */
  getTemplates: async (params?: { 
    page?: number; 
    pageSize?: number; 
    category?: string;
  }) => {
    try {
      const response = await axiosInstance.get('/api/fadada/templates', { params });
      return response.data;
    } catch (error) {
      console.error('获取合同模板失败:', error);
      throw error;
    }
  },

  /**
   * 获取合同模板详情
   */
  getTemplateById: async (id: string) => {
    try {
      const response = await axiosInstance.get(`/api/fadada/templates/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取合同模板详情失败:', error);
      throw error;
    }
  },

  /**
   * 创建合同
   */
  createContract: async (data: ContractData) => {
    try {
      const response = await axiosInstance.post('/api/fadada/contracts', data);
      return response.data;
    } catch (error) {
      console.error('创建合同失败:', error);
      throw error;
    }
  },

  /**
   * 获取合同列表
   */
  getContracts: async (params?: { 
    page?: number; 
    pageSize?: number; 
    status?: string;
    keyword?: string;
    startDate?: string;
    endDate?: string;
    category?: string;
  }) => {
    try {
      const response = await axiosInstance.get('/api/fadada/contracts', { params });
      return response.data;
    } catch (error) {
      console.error('获取合同列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取合同详情
   */
  getContractById: async (id: string) => {
    try {
      const response = await axiosInstance.get(`/api/fadada/contracts/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取合同详情失败:', error);
      throw error;
    }
  },

  /**
   * 发起合同签署
   */
  initiateSignProcess: async (contractId: string) => {
    try {
      const response = await axiosInstance.post(`/api/fadada/contracts/${contractId}/sign`);
      return response.data;
    } catch (error) {
      console.error('发起合同签署失败:', error);
      throw error;
    }
  },

  /**
   * 获取签署链接
   */
  getSignUrl: async (contractId: string, partyId: string) => {
    try {
      const response = await axiosInstance.get(`/api/fadada/contracts/${contractId}/sign-url`, {
        params: { partyId }
      });
      return response.data;
    } catch (error) {
      console.error('获取签署链接失败:', error);
      throw error;
    }
  },

  /**
   * 下载合同文件
   */
  downloadContract: async (contractId: string) => {
    try {
      const response = await axiosInstance.get(`/api/fadada/contracts/${contractId}/download`, {
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      console.error('下载合同文件失败:', error);
      throw error;
    }
  },

  /**
   * 作废合同
   */
  invalidateContract: async (contractId: string, reason: string) => {
    try {
      const response = await axiosInstance.post(`/api/fadada/contracts/${contractId}/invalidate`, { reason });
      return response.data;
    } catch (error) {
      console.error('作废合同失败:', error);
      throw error;
    }
  },

  /**
   * 获取合同签署状态
   */
  getContractStatus: async (contractId: string) => {
    try {
      const response = await axiosInstance.get(`/api/fadada/contracts/${contractId}/status`);
      return response.data;
    } catch (error) {
      console.error('获取合同签署状态失败:', error);
      throw error;
    }
  },
};
