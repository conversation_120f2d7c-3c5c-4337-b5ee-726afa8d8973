// 测试简洁的颜色选择器功能
async function testSimpleColorPicker() {
  console.log('开始测试简洁的颜色选择器功能...');
  
  try {
    // 1. 测试简洁样式的颜色选择器
    console.log('\n测试简洁样式的颜色选择器:');
    const simpleColors = [
      { name: '简洁蓝色', color: '#2196F3' },
      { name: '简洁绿色', color: '#4CAF50' },
      { name: '简洁橙色', color: '#FF9800' },
      { name: '简洁紫色', color: '#9C27B0' },
      { name: '简洁红色', color: '#F44336' }
    ];
    
    for (const testType of simpleColors) {
      const response = await fetch('http://localhost:3005/api/course-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId: 1,
          name: testType.name,
          description: `使用简洁颜色选择器选择的${testType.color}`,
          color: testType.color,
          displayOrder: Math.floor(Math.random() * 100),
          status: 'active'
        })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        console.log(`✓ 简洁样式创建成功: ${testType.name} (${testType.color})`);
      } else {
        console.log(`✗ 简洁样式创建失败: ${testType.name} - ${result.msg}`);
      }
    }
    
    // 2. 测试自定义颜色功能
    console.log('\n测试自定义颜色功能:');
    const customColors = [
      { name: '自定义天蓝', color: '#87CEEB' },
      { name: '自定义薄荷绿', color: '#98FB98' },
      { name: '自定义珊瑚红', color: '#FF7F50' },
      { name: '自定义淡紫', color: '#DDA0DD' }
    ];
    
    for (const customType of customColors) {
      const response = await fetch('http://localhost:3005/api/course-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId: 1,
          name: customType.name,
          description: `使用自定义颜色输入的${customType.color}`,
          color: customType.color,
          displayOrder: Math.floor(Math.random() * 100),
          status: 'active'
        })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        console.log(`✓ 自定义颜色创建成功: ${customType.name} (${customType.color})`);
      } else {
        console.log(`✗ 自定义颜色创建失败: ${customType.name} - ${result.msg}`);
      }
    }
    
    // 3. 验证所有课程类型
    console.log('\n验证所有课程类型:');
    const response = await fetch('http://localhost:3005/api/course-types?tenantId=1&sortBy=name');
    const result = await response.json();
    
    if (result.code === 200) {
      console.log(`总共有 ${result.data.list.length} 个课程类型`);
      console.log('最新创建的课程类型:');
      result.data.list.slice(-9).forEach((type, index) => {
        console.log(`  ${index + 1}. ${type.name}: ${type.color}`);
      });
    }
    
    console.log('\n✓ 简洁颜色选择器功能测试完成!');
    console.log('\n新的简洁颜色选择器特性:');
    console.log('  ✅ 简洁的下拉按钮样式');
    console.log('  ✅ 显示当前选中的颜色圆点和颜色值');
    console.log('  ✅ 点击后弹出颜色选择面板');
    console.log('  ✅ 预设颜色圆点，6列网格布局');
    console.log('  ✅ 选中状态有蓝色光环效果');
    console.log('  ✅ 自定义颜色文本输入框');
    console.log('  ✅ HTML5颜色选择器');
    console.log('  ✅ 更好的用户体验和界面美观度');
    
    console.log('\n请在浏览器中访问课程类型页面，点击"添加课程类型"按钮测试新的简洁颜色选择器界面！');
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testSimpleColorPicker();
