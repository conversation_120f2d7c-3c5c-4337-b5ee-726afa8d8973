import { Employee } from '../types/employee';

// 模拟员工数据
const mockEmployees: Employee[] = [
  {
    id: 1,
    tenant_id: 1,
    employee_no: 'EMP001',
    real_name: '张三',
    phone: '13800138001',
    email: '<PERSON><PERSON><PERSON>@example.com',
    role_id: 1,
    avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=1',
    status: 1,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    tenant_id: 1,
    employee_no: 'EMP002',
    real_name: '李四',
    phone: '13800138002',
    email: '<EMAIL>',
    role_id: 2,
    avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=2',
    status: 1,
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z'
  },
  {
    id: 3,
    tenant_id: 1,
    employee_no: 'EMP003',
    real_name: '王五',
    phone: '13800138003',
    email: '<EMAIL>',
    role_id: 2,
    avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=3',
    status: 0,
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z'
  }
];

// 获取员工列表
export const getEmployees = async (params: {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: number;
}): Promise<{ data: Employee[]; total: number }> => {
  const { page = 1, pageSize = 10, search = '', status } = params;
  
  // 过滤数据
  let filteredData = [...mockEmployees];
  
  if (search) {
    filteredData = filteredData.filter(emp => 
      emp.real_name.includes(search) || 
      emp.employee_no.includes(search) ||
      emp.phone.includes(search)
    );
  }
  
  if (status !== undefined) {
    filteredData = filteredData.filter(emp => emp.status === status);
  }
  
  // 计算总数
  const total = filteredData.length;
  
  // 分页
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const data = filteredData.slice(start, end);
  
  return { data, total };
};

// 获取单个员工
export const getEmployee = async (id: number): Promise<Employee | null> => {
  const employee = mockEmployees.find(emp => emp.id === id);
  return employee || null;
};

// 创建员工
export const createEmployee = async (employee: Omit<Employee, 'id' | 'created_at' | 'updated_at'>): Promise<Employee> => {
  const newEmployee: Employee = {
    ...employee,
    id: mockEmployees.length + 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  mockEmployees.push(newEmployee);
  return newEmployee;
};

// 更新员工
export const updateEmployee = async (id: number, employee: Partial<Employee>): Promise<Employee | null> => {
  const index = mockEmployees.findIndex(emp => emp.id === id);
  if (index === -1) return null;
  
  const updatedEmployee = {
    ...mockEmployees[index],
    ...employee,
    updated_at: new Date().toISOString()
  };
  
  mockEmployees[index] = updatedEmployee;
  return updatedEmployee;
};

// 删除员工
export const deleteEmployee = async (id: number): Promise<boolean> => {
  const index = mockEmployees.findIndex(emp => emp.id === id);
  if (index === -1) return false;
  
  mockEmployees.splice(index, 1);
  return true;
};

// 更新员工状态
export const updateEmployeeStatus = async (id: number, status: number): Promise<Employee | null> => {
  return updateEmployee(id, { status });
}; 