import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Pencil, Settings, Calendar, AlertTriangle } from "lucide-react"

interface EquipmentDetailDialogProps {
  equipment: {
    id: string
    name: string
    venue: string
    quantity: number
    status: string
    lastMaintenance: string
    nextMaintenance: string
    brand: string
    purchaseDate: string
    price: number
    lifespan: string
  }
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function EquipmentDetailDialog({ equipment, open, onOpenChange }: EquipmentDetailDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>{equipment.name} 详情</span>
            <Badge variant={equipment.status === "normal" ? "default" : "destructive"}>
              {equipment.status === "normal" ? "正常" : "维护中"}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="info">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="info">基本信息</TabsTrigger>
            <TabsTrigger value="maintenance">维护记录</TabsTrigger>
            <TabsTrigger value="usage">使用情况</TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">基本信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <p className="text-sm font-medium">设备编号</p>
                      <p className="text-sm">{equipment.id}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">所属场地</p>
                      <p className="text-sm">{equipment.venue}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">数量</p>
                      <p className="text-sm">{equipment.quantity}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">品牌</p>
                      <p className="text-sm">{equipment.brand}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">购买日期</p>
                      <p className="text-sm">{equipment.purchaseDate}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">预计使用寿命</p>
                      <p className="text-sm">{equipment.lifespan}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">财务信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <p className="text-sm font-medium">购买价格</p>
                      <p className="text-sm">¥{equipment.price}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">总价值</p>
                      <p className="text-sm">¥{equipment.price * equipment.quantity}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">折旧率</p>
                      <p className="text-sm">20%/年</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">当前价值</p>
                      <p className="text-sm">¥{Math.round(equipment.price * equipment.quantity * 0.8)}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">供应商</p>
                      <p className="text-sm">瑜伽设备专营店</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">保修期</p>
                      <p className="text-sm">12个月</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">维护信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-medium">上次维护</p>
                    <p className="text-sm">{equipment.lastMaintenance}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">下次维护</p>
                    <p className="text-sm">{equipment.nextMaintenance}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">维护周期</p>
                    <p className="text-sm">3个月</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">维护负责人</p>
                    <p className="text-sm">张维修</p>
                  </div>
                </div>

                {equipment.status === "maintenance" && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
                    <AlertTriangle className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                    <div>
                      <p className="font-medium text-red-600">当前处于维护状态</p>
                      <p className="text-sm text-red-600">预计完成时间: 2025-03-30</p>
                      <p className="text-sm text-red-600">维护原因: 部分设备磨损需要更换</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-end gap-2">
              <Button variant="outline" size="sm">
                <Calendar className="mr-2 h-4 w-4" />
                安排维护
              </Button>
              <Button variant="outline" size="sm">
                <Pencil className="mr-2 h-4 w-4" />
                编辑信息
              </Button>
              {equipment.status === "normal" ? (
                <Button variant="outline" size="sm">
                  <Settings className="mr-2 h-4 w-4" />
                  标记为维护中
                </Button>
              ) : (
                <Button variant="outline" size="sm">
                  <Settings className="mr-2 h-4 w-4" />
                  标记为正常
                </Button>
              )}
            </div>
          </TabsContent>

          <TabsContent value="maintenance">
            <Card>
              <CardHeader>
                <CardTitle>维护记录</CardTitle>
                <CardDescription>设备的历史维护记录</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-1 h-full bg-primary rounded-full"></div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="font-medium">常规维护</p>
                        <p className="text-sm text-muted-foreground">{equipment.lastMaintenance}</p>
                      </div>
                      <p className="text-sm text-muted-foreground">进行了常规清洁和检查</p>
                      <p className="text-sm">维护人员: 张维修</p>
                      <p className="text-sm">维护费用: ¥200</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-1 h-full bg-primary rounded-full"></div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="font-medium">常规维护</p>
                        <p className="text-sm text-muted-foreground">2024-11-15</p>
                      </div>
                      <p className="text-sm text-muted-foreground">进行了常规清洁和检查</p>
                      <p className="text-sm">维护人员: 李维修</p>
                      <p className="text-sm">维护费用: ¥200</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-1 h-full bg-destructive rounded-full"></div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="font-medium">紧急维修</p>
                        <p className="text-sm text-muted-foreground">2024-09-05</p>
                      </div>
                      <p className="text-sm text-muted-foreground">部分设备出现损坏，进行了更换</p>
                      <p className="text-sm">维护人员: 王维修</p>
                      <p className="text-sm">维护费用: ¥500</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-1 h-full bg-primary rounded-full"></div>
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <p className="font-medium">常规维护</p>
                        <p className="text-sm text-muted-foreground">2024-08-15</p>
                      </div>
                      <p className="text-sm text-muted-foreground">进行了常规清洁和检查</p>
                      <p className="text-sm">维护人员: 张维修</p>
                      <p className="text-sm">维护费用: ¥200</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="usage">
            <Card>
              <CardHeader>
                <CardTitle>使用情况</CardTitle>
                <CardDescription>设备的使用统计</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-muted p-4 rounded-md text-center">
                    <p className="text-sm text-muted-foreground">使用频率</p>
                    <p className="text-2xl font-bold">每周5次</p>
                  </div>
                  <div className="bg-muted p-4 rounded-md text-center">
                    <p className="text-sm text-muted-foreground">平均使用时长</p>
                    <p className="text-2xl font-bold">1.5小时/次</p>
                  </div>
                  <div className="bg-muted p-4 rounded-md text-center">
                    <p className="text-sm text-muted-foreground">使用率</p>
                    <p className="text-2xl font-bold">75%</p>
                  </div>
                </div>

                <div>
                  <p className="font-medium mb-2">使用场景分布</p>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <span className="w-24">基础瑜伽</span>
                      <div className="flex-1 h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "60%" }}></div>
                      </div>
                      <span className="ml-2 text-sm">60%</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-24">高级瑜伽</span>
                      <div className="flex-1 h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "25%" }}></div>
                      </div>
                      <span className="ml-2 text-sm">25%</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-24">阴瑜伽</span>
                      <div className="flex-1 h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "15%" }}></div>
                      </div>
                      <span className="ml-2 text-sm">15%</span>
                    </div>
                  </div>
                </div>

                <div>
                  <p className="font-medium mb-2">近期使用记录</p>
                  <div className="space-y-2">
                    <div className="p-3 border rounded-md">
                      <div className="flex justify-between">
                        <p className="font-medium">基础瑜伽入门</p>
                        <p className="text-sm">2025-03-25</p>
                      </div>
                      <p className="text-sm text-muted-foreground">使用数量: 15个 | 使用时长: 1.5小时</p>
                      <p className="text-sm text-muted-foreground">场地: 1号瑜伽室 | 教练: 张教练</p>
                    </div>
                    <div className="p-3 border rounded-md">
                      <div className="flex justify-between">
                        <p className="font-medium">高级瑜伽进阶</p>
                        <p className="text-sm">2025-03-24</p>
                      </div>
                      <p className="text-sm text-muted-foreground">使用数量: 10个 | 使用时长: 1.5小时</p>
                      <p className="text-sm text-muted-foreground">场地: 2号瑜伽室 | 教练: 李教练</p>
                    </div>
                    <div className="p-3 border rounded-md">
                      <div className="flex justify-between">
                        <p className="font-medium">阴瑜伽放松</p>
                        <p className="text-sm">2025-03-23</p>
                      </div>
                      <p className="text-sm text-muted-foreground">使用数量: 8个 | 使用时长: 1小时</p>
                      <p className="text-sm text-muted-foreground">场地: 3号瑜伽室 | 教练: 王教练</p>
                    </div>
                  </div>
                </div>

                <div>
                  <p className="font-medium mb-2">使用趋势</p>
                  <div className="h-40 flex items-end space-x-2">
                    {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, i) => {
                      const height = Math.floor(Math.random() * 60) + 20
                      return (
                        <div key={day} className="flex flex-col items-center flex-1">
                          <div className="bg-primary w-full rounded-t-md" style={{ height: `${height}%` }}></div>
                          <span className="text-xs mt-1">{day}</span>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

