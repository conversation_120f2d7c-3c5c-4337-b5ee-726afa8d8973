import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { prisma } from '@/lib/db'
import { createHash } from 'crypto'

// 密码加密函数（与注册时保持一致）
function hashPassword(password: string): string {
  const salt = 'yoga_system_salt_2024';
  return createHash('sha256').update(password + salt).digest('hex');
}

export async function POST(request: Request) {
  try {
    // 解析请求体
    const body = await request.json()
    const { username, password } = body

    console.log('登录请求:', { username });

    // 验证用户名和密码
    if (!username || !password) {
      return NextResponse.json(
        { code: 400, message: '用户名和密码不能为空' },
        { status: 400 }
      )
    }

    let user = null
    let token = null

    // 用户登录（从数据库验证）
    try {
      const hashedPassword = hashPassword(password);

      // 可以通过手机号或用户名登录
      const employee = await prisma.employee.findFirst({
        where: {
          OR: [
            { phone: username },
            { real_name: username }
          ],
          password_hash: hashedPassword,
          status: 1, // 只允许激活的用户登录
        },
        include: {
          tenant: true
        }
      });

      if (!employee) {
        console.log('登录失败: 用户名或密码不正确', { username, hashedPassword });
        return NextResponse.json(
          { code: 401, message: '用户名或密码不正确，请检查后重试' },
          { status: 401 }
        )
      }

      // 检查租户状态
      if (employee.tenant.status === 0) {
        return NextResponse.json(
          { code: 403, message: '您的账户正在审核中，请耐心等待审核通过' },
          { status: 403 }
        )
      }

      if (employee.tenant.status !== 1) {
        return NextResponse.json(
          { code: 403, message: '您的账户已被暂停，请联系客服' },
          { status: 403 }
        )
      }

      user = {
        id: employee.id.toString(),
        username: employee.real_name,
        nickname: employee.real_name,
        role: employee.is_super_admin ? 'admin' : 'employee',
        avatar: employee.avatar_url || '/avatars/user.png',
        permissions: employee.is_super_admin ? ['*'] : ['view'],
        tenantId: employee.tenant_id,
        phone: employee.phone,
        email: employee.email,
        companyName: employee.tenant.tenant_name,
      }
      token = `user-${employee.tenant_id}-${employee.id}-` + Date.now()

      console.log('登录成功:', { userId: employee.id, tenantId: employee.tenant_id });

    } catch (dbError) {
      console.error('数据库查询失败:', dbError);
      return NextResponse.json(
        { code: 500, message: '登录服务暂时不可用，请稍后再试' },
        { status: 500 }
      )
    }
    
    // 创建响应对象
    const response = NextResponse.json({
      code: 200,
      message: '登录成功',
      data: {
        token,
        user,
      }
    })
    
    // 设置Cookie (7天过期)
    response.cookies.set({
      name: 'token',
      value: token,
      httpOnly: true,
      path: '/',
      maxAge: 7 * 24 * 60 * 60, // 7天（秒）
      sameSite: 'strict',
    })
    
    return response
  } catch (error) {
    console.error('登录处理出错:', error)
    
    // 返回错误响应
    return NextResponse.json(
      { code: 500, message: '服务器内部错误' },
      { status: 500 }
    )
  }
} 