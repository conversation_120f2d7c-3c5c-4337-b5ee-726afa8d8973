"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Pencil, Trash2, MoreHorizontal } from "lucide-react"
import { PositionEditDialog } from "@/components/settings/position-edit-dialog"
import { useToast } from "@/hooks/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// 模拟职位数据
const initialPositions = [
  {
    id: "1",
    name: "格斗教练",
    department: "2",
    description: "负责格斗课程教学和训练",
    isActive: true
  },
  {
    id: "2",
    name: "瑜伽教练",
    department: "2",
    description: "负责瑜伽课程教学和训练",
    isActive: true
  },
  {
    id: "3",
    name: "会籍顾问",
    department: "3",
    description: "负责会员卡销售和会员管理",
    isActive: true
  },
  {
    id: "4",
    name: "前台接待",
    department: "1",
    description: "负责前台接待和日常咨询",
    isActive: true
  },
  {
    id: "5",
    name: "财务主管",
    department: "4",
    description: "负责财务管理和账目核算",
    isActive: true
  },
  {
    id: "6",
    name: "健身教练",
    department: "2",
    description: "负责健身指导和私教课程",
    isActive: false
  },
  {
    id: "7",
    name: "舞蹈教师",
    department: "2",
    description: "负责舞蹈课程教学",
    isActive: true
  },
  {
    id: "8",
    name: "保洁人员",
    department: "5",
    description: "负责场地清洁和卫生维护",
    isActive: true
  },
  {
    id: "9",
    name: "维修人员",
    department: "5",
    description: "负责设备维修和设施维护",
    isActive: true
  },
]

// 部门映射表
const departmentMap = {
  "1": "行政部",
  "2": "教学部",
  "3": "销售部",
  "4": "财务部",
  "5": "后勤部",
}

export default function PositionsPage() {
  const [positions, setPositions] = useState(initialPositions)
  const [searchQuery, setSearchQuery] = useState("")
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [currentPosition, setCurrentPosition] = useState<any>(null)
  const { toast } = useToast()

  // 过滤职位列表
  const filteredPositions = positions.filter(position => {
    return (
      position.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      departmentMap[position.department]?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      position.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })

  // 添加新职位
  const handleAddPosition = () => {
    setCurrentPosition(null) // 清空当前选中的职位
    setEditDialogOpen(true) // 打开编辑对话框
  }

  // 编辑职位
  const handleEditPosition = (position: any) => {
    setCurrentPosition(position)
    setEditDialogOpen(true)
  }

  // 保存职位
  const handleSavePosition = (position: any) => {
    if (position.id) {
      // 更新现有职位
      setPositions(positions.map(p => p.id === position.id ? position : p))
    } else {
      // 添加新职位
      const newPosition = {
        ...position,
        id: String(positions.length + 1), // 简单生成ID
        isActive: true
      }
      setPositions([...positions, newPosition])
    }
  }

  // 删除职位
  const handleDeletePosition = (id: string) => {
    if (window.confirm("确定要删除此职位吗？")) {
      setPositions(positions.filter(p => p.id !== id))
      toast({
        title: "职位已删除",
        description: "职位已成功从系统中移除"
      })
    }
  }

  // 切换职位状态
  const handleToggleStatus = (id: string) => {
    setPositions(positions.map(p => {
      if (p.id === id) {
        return { ...p, isActive: !p.isActive }
      }
      return p
    }))
    
    const position = positions.find(p => p.id === id)
    toast({
      title: position?.isActive ? "职位已停用" : "职位已启用",
      description: `职位 ${position?.name} 已${position?.isActive ? "停用" : "启用"}`
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">职位管理</h1>
        <Button onClick={handleAddPosition}>
          <Plus className="mr-2 h-4 w-4" />
          添加职位
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>职位列表</CardTitle>
          <CardDescription>管理场馆中的所有职位和职位设置</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索职位名称、部门..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>职位名称</TableHead>
                  <TableHead>所属部门</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPositions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      没有找到匹配的职位
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPositions.map((position) => (
                    <TableRow key={position.id}>
                      <TableCell className="font-medium">{position.name}</TableCell>
                      <TableCell>{departmentMap[position.department]}</TableCell>
                      <TableCell className="max-w-xs truncate">{position.description}</TableCell>
                      <TableCell>
                        <Badge variant={position.isActive ? "default" : "secondary"}>
                          {position.isActive ? "启用" : "停用"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditPosition(position)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              编辑
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleToggleStatus(position.id)}>
                              {position.isActive ? "停用" : "启用"}
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDeletePosition(position.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      
      <PositionEditDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        position={currentPosition}
        onSave={handleSavePosition}
      />
    </div>
  )
}
