"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2 } from "lucide-react"

interface AddStoreDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddStoreDialog({ open, onOpenChange }: AddStoreDialogProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")
  const [formData, setFormData] = useState({
    name: "",
    address: "",
    phone: "",
    manager: "",
    status: "active",
    businessHours: "",
    description: "",
    area: "",
    capacity: "",
    facilities: "",
    syncSettings: "auto"
  })

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = async () => {
    // 表单验证
    if (!formData.name) {
      toast({
        title: "请输入门店名称",
        variant: "destructive",
      })
      return
    }

    if (!formData.address) {
      toast({
        title: "请输入门店地址",
        variant: "destructive",
      })
      return
    }

    if (!formData.phone) {
      toast({
        title: "请输入联系电话",
        variant: "destructive",
      })
      return
    }

    if (!formData.manager) {
      toast({
        title: "请输入店长姓名",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1500))

      toast({
        title: "门店添加成功",
        description: `${formData.name} 已成功添加到连锁门店列表`,
      })

      // 重置表单
      setFormData({
        name: "",
        address: "",
        phone: "",
        manager: "",
        status: "active",
        businessHours: "",
        description: "",
        area: "",
        capacity: "",
        facilities: "",
        syncSettings: "auto"
      })

      // 关闭对话框
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "添加失败",
        description: "添加门店时出错，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>添加连锁门店</DialogTitle>
          <DialogDescription>
            添加新的连锁门店信息，完成后将自动创建数据同步关系
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="details">详细信息</TabsTrigger>
            <TabsTrigger value="sync">同步设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="store-name">门店名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="store-name"
                  value={formData.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  placeholder="例如：瑜伽中心朝阳店"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="store-address">门店地址 <span className="text-red-500">*</span></Label>
                <Input
                  id="store-address"
                  value={formData.address}
                  onChange={(e) => handleChange("address", e.target.value)}
                  placeholder="例如：北京市朝阳区建国路88号"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="store-phone">联系电话 <span className="text-red-500">*</span></Label>
                <Input
                  id="store-phone"
                  value={formData.phone}
                  onChange={(e) => handleChange("phone", e.target.value)}
                  placeholder="例如：010-12345678"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="store-manager">店长姓名 <span className="text-red-500">*</span></Label>
                <Input
                  id="store-manager"
                  value={formData.manager}
                  onChange={(e) => handleChange("manager", e.target.value)}
                  placeholder="例如：张经理"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="store-status">营业状态</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleChange("status", value)}
                >
                  <SelectTrigger id="store-status">
                    <SelectValue placeholder="选择营业状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">营业中</SelectItem>
                    <SelectItem value="inactive">已关闭</SelectItem>
                    <SelectItem value="preparing">筹备中</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="details" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="business-hours">营业时间</Label>
                <Input
                  id="business-hours"
                  value={formData.businessHours}
                  onChange={(e) => handleChange("businessHours", e.target.value)}
                  placeholder="例如：周一至周日 9:00-21:00"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="store-description">门店描述</Label>
                <Textarea
                  id="store-description"
                  value={formData.description}
                  onChange={(e) => handleChange("description", e.target.value)}
                  placeholder="描述门店的特点和服务..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="store-area">门店面积 (㎡)</Label>
                  <Input
                    id="store-area"
                    value={formData.area}
                    onChange={(e) => handleChange("area", e.target.value)}
                    placeholder="例如：300"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="store-capacity">容纳人数</Label>
                  <Input
                    id="store-capacity"
                    value={formData.capacity}
                    onChange={(e) => handleChange("capacity", e.target.value)}
                    placeholder="例如：50"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="store-facilities">设施与设备</Label>
                <Textarea
                  id="store-facilities"
                  value={formData.facilities}
                  onChange={(e) => handleChange("facilities", e.target.value)}
                  placeholder="描述门店的设施与设备..."
                  rows={3}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="sync" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="sync-settings">数据同步设置</Label>
                <Select
                  value={formData.syncSettings}
                  onValueChange={(value) => handleChange("syncSettings", value)}
                >
                  <SelectTrigger id="sync-settings">
                    <SelectValue placeholder="选择同步设置" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">自动同步（推荐）</SelectItem>
                    <SelectItem value="manual">手动同步</SelectItem>
                    <SelectItem value="scheduled">定时同步</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground mt-1">
                  自动同步将在数据变更时实时同步，手动同步需要手动触发，定时同步按设定的时间自动同步
                </p>
              </div>

              <div className="rounded-md bg-blue-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3 flex-1 md:flex md:justify-between">
                    <p className="text-sm text-blue-700">
                      添加门店后，系统将自动创建数据同步关系，您可以在门店详情页面修改同步设置
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                添加中...
              </>
            ) : (
              "添加门店"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
