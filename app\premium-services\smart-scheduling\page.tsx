"use client"

import { useState } from "react"
import Link from "next/link"
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  Pie,
  Cell,
  <PERSON><PERSON><PERSON> as <PERSON>chartsBar<PERSON>hart,
  Bar,
  <PERSON><PERSON>hart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as <PERSON>chartsTooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  ReferenceLine
} from "recharts"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import {
  BarChart,
  BarChart3,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Clock,
  Edit,
  Eye,
  Filter,
  Info,
  LayoutGrid,
  List,
  MoreHorizontal,
  Plus,
  RefreshCw,
  Save,
  Search,
  Settings,
  Sliders,
  Sparkles,
  Trash,
  TrendingUp,
  Users,
  X,
  AlertTriangle,
  CheckCircle2,
  ArrowUpDown,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// 模拟课程数据
const courses = [
  {
    id: "1",
    name: "瑜伽基础课",
    coach: "张教练",
    location: "1号教室",
    capacity: 20,
    enrolled: 15,
    startTime: "2023-07-15T09:00:00",
    endTime: "2023-07-15T10:30:00",
    status: "scheduled",
    recurring: "weekly",
    type: "group",
    level: "beginner",
  },
  {
    id: "2",
    name: "高级瑜伽",
    coach: "李教练",
    location: "2号教室",
    capacity: 15,
    enrolled: 12,
    startTime: "2023-07-15T11:00:00",
    endTime: "2023-07-15T12:30:00",
    status: "scheduled",
    recurring: "weekly",
    type: "group",
    level: "advanced",
  },
  {
    id: "3",
    name: "私教课",
    coach: "王教练",
    location: "私教区",
    capacity: 1,
    enrolled: 1,
    startTime: "2023-07-15T14:00:00",
    endTime: "2023-07-15T15:00:00",
    status: "scheduled",
    recurring: "once",
    type: "private",
    level: "intermediate",
  },
  {
    id: "4",
    name: "普拉提",
    coach: "赵教练",
    location: "3号教室",
    capacity: 18,
    enrolled: 10,
    startTime: "2023-07-15T16:00:00",
    endTime: "2023-07-15T17:30:00",
    status: "scheduled",
    recurring: "weekly",
    type: "group",
    level: "beginner",
  },
  {
    id: "5",
    name: "瑜伽进阶课",
    coach: "张教练",
    location: "1号教室",
    capacity: 20,
    enrolled: 8,
    startTime: "2023-07-16T09:00:00",
    endTime: "2023-07-16T10:30:00",
    status: "scheduled",
    recurring: "weekly",
    type: "group",
    level: "intermediate",
  },
]

// 模拟教练数据
const coaches = [
  {
    id: "1",
    name: "张教练",
    specialty: "瑜伽",
    workload: "85%",
    courses: 12,
    availability: [
      { day: "周一", slots: ["上午", "下午"] },
      { day: "周二", slots: ["上午"] },
      { day: "周四", slots: ["上午", "下午"] },
      { day: "周六", slots: ["上午"] },
    ],
  },
  {
    id: "2",
    name: "李教练",
    specialty: "高级瑜伽",
    workload: "70%",
    courses: 8,
    availability: [
      { day: "周二", slots: ["下午"] },
      { day: "周三", slots: ["上午", "下午"] },
      { day: "周五", slots: ["上午", "下午"] },
      { day: "周日", slots: ["上午"] },
    ],
  },
  {
    id: "3",
    name: "王教练",
    specialty: "私教",
    workload: "90%",
    courses: 15,
    availability: [
      { day: "周一", slots: ["下午"] },
      { day: "周三", slots: ["上午", "下午"] },
      { day: "周五", slots: ["上午"] },
      { day: "周六", slots: ["下午"] },
      { day: "周日", slots: ["上午", "下午"] },
    ],
  },
  {
    id: "4",
    name: "赵教练",
    specialty: "普拉提",
    workload: "60%",
    courses: 6,
    availability: [
      { day: "周一", slots: ["上午", "下午"] },
      { day: "周二", slots: ["上午", "下午"] },
      { day: "周四", slots: ["下午"] },
      { day: "周日", slots: ["下午"] },
    ],
  },
]

// 模拟场地数据
const locations = [
  {
    id: "1",
    name: "1号教室",
    capacity: 20,
    type: "group",
    utilization: "75%",
    equipment: ["瑜伽垫", "瑜伽砖", "瑜伽带"],
    availability: "高",
  },
  {
    id: "2",
    name: "2号教室",
    capacity: 15,
    type: "group",
    utilization: "80%",
    equipment: ["瑜伽垫", "瑜伽砖", "瑜伽带", "瑜伽球"],
    availability: "中",
  },
  {
    id: "3",
    name: "3号教室",
    capacity: 18,
    type: "group",
    utilization: "60%",
    equipment: ["瑜伽垫", "普拉提器械"],
    availability: "高",
  },
  {
    id: "4",
    name: "私教区",
    capacity: 3,
    type: "private",
    utilization: "90%",
    equipment: ["瑜伽垫", "瑜伽砖", "瑜伽带", "瑜伽球", "普拉提器械"],
    availability: "低",
  },
]

// 模拟冲突数据
const conflicts = [
  {
    id: "1",
    type: "coach",
    description: "张教练在7月15日上午9:00-10:30有两个课程安排",
    severity: "high",
    courses: ["瑜伽基础课", "瑜伽进阶课"],
    status: "unresolved",
  },
  {
    id: "2",
    type: "location",
    description: "1号教室在7月16日上午9:00-10:30有两个课程安排",
    severity: "high",
    courses: ["瑜伽基础课", "瑜伽进阶课"],
    status: "unresolved",
  },
  {
    id: "3",
    type: "coach",
    description: "李教练在7月17日下午工作时间超过建议上限",
    severity: "medium",
    courses: ["高级瑜伽", "瑜伽冥想课"],
    status: "resolved",
  },
]

export default function SmartSchedulingPage() {
  const [activeTab, setActiveTab] = useState("dashboard")
  const [searchQuery, setSearchQuery] = useState("")
  const [showConflictDetails, setShowConflictDetails] = useState(false)
  const [selectedConflict, setSelectedConflict] = useState<any>(null)
  const [showScheduleForm, setShowScheduleForm] = useState(false)
  const [optimizationRunning, setOptimizationRunning] = useState(false)
  const [viewMode, setViewMode] = useState<"list" | "calendar">("list")
  const [currentDate, setCurrentDate] = useState(new Date())
  const [calendarView, setCalendarView] = useState<"day" | "week" | "month">("week")

  // 处理冲突详情查看
  const handleViewConflictDetails = (conflict: any) => {
    setSelectedConflict(conflict)
    setShowConflictDetails(true)
  }

  // 处理智能优化
  const handleRunOptimization = () => {
    setOptimizationRunning(true)

    // 模拟优化过程
    setTimeout(() => {
      setOptimizationRunning(false)
      // 实际应用中应该更新课程和冲突数据
    }, 3000)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">智能排课系统</h1>
          <p className="text-muted-foreground">基于AI的智能排课系统，自动优化教练和场地资源</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleRunOptimization} disabled={optimizationRunning}>
            {optimizationRunning ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                优化中...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                智能优化
              </>
            )}
          </Button>
          <Button onClick={() => setShowScheduleForm(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加课程
          </Button>
        </div>
      </div>

      <div className="text-sm breadcrumbs">
        <ul className="flex items-center space-x-1">
          <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li>智能排课系统</li>
        </ul>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 max-w-2xl">
          <TabsTrigger value="dashboard">仪表盘</TabsTrigger>
          <TabsTrigger value="schedule">课程安排</TabsTrigger>
          <TabsTrigger value="coaches">教练管理</TabsTrigger>
          <TabsTrigger value="locations">场地管理</TabsTrigger>
          <TabsTrigger value="conflicts">冲突检测</TabsTrigger>
        </TabsList>

        {/* 仪表盘 */}
        <TabsContent value="dashboard" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">总课程数</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">24</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +8%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上周</span>
                </div>
                <p className="text-xs text-muted-foreground mt-3 pt-3 border-t">本周安排的课程总数</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">教练工作量</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">76%</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +5%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上周</span>
                </div>
                <p className="text-xs text-muted-foreground mt-3 pt-3 border-t">平均教练工作量</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">场地利用率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">68%</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +3%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上周</span>
                </div>
                <p className="text-xs text-muted-foreground mt-3 pt-3 border-t">平均场地利用率</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">未解决冲突</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-500">2</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +1
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上周</span>
                </div>
                <p className="text-xs text-muted-foreground mt-3 pt-3 border-t">需要解决的排课冲突</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>本周课程分布</CardTitle>
                <CardDescription>按课程类型统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={[
                          { name: "瑜伽基础课", value: 8, color: "#4285F4" },
                          { name: "高级瑜伽", value: 5, color: "#EA4335" },
                          { name: "阴瑜伽", value: 4, color: "#FBBC05" },
                          { name: "私教课", value: 6, color: "#34A853" },
                          { name: "普拉提", value: 1, color: "#9C27B0" },
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {[
                          { name: "瑜伽基础课", value: 8, color: "#4285F4" },
                          { name: "高级瑜伽", value: 5, color: "#EA4335" },
                          { name: "阴瑜伽", value: 4, color: "#FBBC05" },
                          { name: "私教课", value: 6, color: "#34A853" },
                          { name: "普拉提", value: 1, color: "#9C27B0" },
                        ].map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Legend layout="horizontal" verticalAlign="bottom" align="center" />
                      <RechartsTooltip
                        formatter={(value, name) => [
                          `${value}节课`,
                          name
                        ]}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>场地利用率</CardTitle>
                <CardDescription>各场地使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={[
                        { name: "1号教室", 利用率: 75, 容量: 20, 已预订: 15 },
                        { name: "2号教室", 利用率: 80, 容量: 15, 已预订: 12 },
                        { name: "3号教室", 利用率: 60, 容量: 18, 已预订: 11 },
                        { name: "私教区", 利用率: 90, 容量: 5, 已预订: 4 },
                        { name: "户外场地", 利用率: 45, 容量: 30, 已预订: 14 },
                      ]}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis
                        yAxisId="left"
                        orientation="left"
                        label={{ value: '利用率(%)', angle: -90, position: 'insideLeft' }}
                      />
                      <YAxis
                        yAxisId="right"
                        orientation="right"
                        label={{ value: '容量/已预订', angle: 90, position: 'insideRight' }}
                      />
                      <RechartsTooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="利用率" fill="#8884d8" />
                      <Bar yAxisId="right" dataKey="容量" fill="#82ca9d" />
                      <Bar yAxisId="right" dataKey="已预订" fill="#ffc658" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>教练工作量分布</CardTitle>
                <CardDescription>各教练课时分配情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      layout="vertical"
                      data={[
                        { name: "张教练", 课时: 12, 最大工作量: 15, 工作量比例: 80 },
                        { name: "李教练", 课时: 8, 最大工作量: 12, 工作量比例: 67 },
                        { name: "王教练", 课时: 15, 最大工作量: 15, 工作量比例: 100 },
                        { name: "赵教练", 课时: 6, 最大工作量: 10, 工作量比例: 60 },
                      ]}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 70,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" />
                      <RechartsTooltip
                        formatter={(value, name, props) => {
                          if (name === "课时") return [`${value}课时`, "已安排课时"];
                          if (name === "最大工作量") return [`${value}课时`, "最大工作量"];
                          return [value, name];
                        }}
                      />
                      <Legend />
                      <Bar dataKey="课时" fill="#8884d8" />
                      <Bar dataKey="最大工作量" fill="#82ca9d" stackId="a" />
                      <ReferenceLine x={10} stroke="red" label="平均工作量" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>时段热力分析</CardTitle>
                <CardDescription>各时段课程安排密度</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <div className="grid grid-cols-7 gap-1 h-full">
                    {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day) => (
                      <div key={day} className="flex flex-col">
                        <div className="text-xs text-center font-medium mb-1">{day}</div>
                        <div className="flex-1 flex flex-col gap-1">
                          {[
                            { time: "早上", density: Math.random() },
                            { time: "上午", density: Math.random() },
                            { time: "中午", density: Math.random() },
                            { time: "下午", density: Math.random() },
                            { time: "晚上", density: Math.random() },
                          ].map((slot) => (
                            <TooltipProvider key={slot.time}>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div
                                    className="flex-1 rounded-sm cursor-pointer"
                                    style={{
                                      backgroundColor: `rgba(54, 162, 235, ${slot.density})`,
                                    }}
                                  ></div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>{day} {slot.time}</p>
                                  <p>课程数: {Math.floor(slot.density * 10)}</p>
                                  <p>利用率: {Math.floor(slot.density * 100)}%</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-between items-center mt-4">
                    <div className="text-xs">低密度</div>
                    <div className="w-full mx-2 h-2 bg-gradient-to-r from-white to-blue-500 rounded-full"></div>
                    <div className="text-xs">高密度</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>智能排课建议</CardTitle>
                  <CardDescription>基于AI分析的排课优化建议</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Sparkles className="mr-2 h-4 w-4" />
                  生成建议
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <Users className="h-4 w-4 text-blue-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">优化教练工作量</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        王教练当前工作量达到100%，建议将部分课程重新分配给工作量较低的赵教练（当前工作量60%）。
                        系统推荐将周三下午的"私教课"重新分配。
                      </p>
                      <div className="flex items-center gap-2 mt-3">
                        <Button variant="outline" size="sm">忽略</Button>
                        <Button size="sm">应用建议</Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-green-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <BarChart3 className="h-4 w-4 text-green-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">提高场地利用率</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        户外场地的利用率仅为45%，远低于其他场地。建议将部分适合户外进行的课程（如晨练瑜伽）
                        调整到户外场地，提高整体场地利用效率。
                      </p>
                      <div className="flex items-center gap-2 mt-3">
                        <Button variant="outline" size="sm">忽略</Button>
                        <Button size="sm">应用建议</Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-8 h-8 bg-amber-50 rounded-full flex items-center justify-center flex-shrink-0">
                      <Clock className="h-4 w-4 text-amber-500" />
                    </div>
                    <div>
                      <h3 className="font-medium">优化高峰时段安排</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        周一至周五的晚上时段课程密度过高，而上午时段相对空闲。建议将部分非热门课程
                        调整到上午时段，缓解晚上时段的压力，提高会员体验。
                      </p>
                      <div className="flex items-center gap-2 mt-3">
                        <Button variant="outline" size="sm">忽略</Button>
                        <Button size="sm">应用建议</Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 课程安排 */}
        <TabsContent value="schedule" className="space-y-4 mt-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-1 items-center gap-2 max-w-md">
              <Input
                placeholder="搜索课程名称、教练或场地..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <div className="flex items-center gap-2">
              <Select defaultValue="all">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="课程类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有类型</SelectItem>
                  <SelectItem value="group">团体课</SelectItem>
                  <SelectItem value="private">私教课</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle>课程安排</CardTitle>
                <div className="flex items-center gap-2">
                  <Button
                    variant={viewMode === "calendar" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("calendar")}
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    日历视图
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                  >
                    <List className="mr-2 h-4 w-4" />
                    列表视图
                  </Button>
                </div>
              </div>
              <CardDescription>管理所有课程安排，查看详情和编辑</CardDescription>
            </CardHeader>
            <CardContent>
              {viewMode === "list" ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[250px]">课程名称</TableHead>
                      <TableHead>教练</TableHead>
                      <TableHead>场地</TableHead>
                      <TableHead>时间</TableHead>
                      <TableHead>容量</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {courses.map((course) => {
                      const startDate = new Date(course.startTime);
                      const endDate = new Date(course.endTime);
                      const formattedDate = `${startDate.getMonth() + 1}月${startDate.getDate()}日`;
                      const formattedTime = `${startDate.getHours()}:${startDate.getMinutes().toString().padStart(2, '0')}-${endDate.getHours()}:${endDate.getMinutes().toString().padStart(2, '0')}`;

                      return (
                        <TableRow key={course.id}>
                          <TableCell className="font-medium">{course.name}</TableCell>
                          <TableCell>{course.coach}</TableCell>
                          <TableCell>{course.location}</TableCell>
                          <TableCell>
                            <div className="flex flex-col">
                              <span>{formattedDate}</span>
                              <span className="text-muted-foreground text-xs">{formattedTime}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <span>{course.enrolled}/{course.capacity}</span>
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger>
                                    <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                                      <div
                                        className="h-full bg-primary"
                                        style={{ width: `${(course.enrolled / course.capacity) * 100}%` }}
                                      ></div>
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>已报名: {course.enrolled}人</p>
                                    <p>总容量: {course.capacity}人</p>
                                    <p>报名率: {Math.round((course.enrolled / course.capacity) * 100)}%</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={course.status === "scheduled" ? "default" : "outline"}>
                              {course.status === "scheduled" ? "已安排" : "草稿"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>操作</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  查看详情
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑课程
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Calendar className="mr-2 h-4 w-4" />
                                  调整时间
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <Trash className="mr-2 h-4 w-4" />
                                  删除课程
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              ) : (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" onClick={() => {
                        const newDate = new Date(currentDate);
                        if (calendarView === "day") {
                          newDate.setDate(newDate.getDate() - 1);
                        } else if (calendarView === "week") {
                          newDate.setDate(newDate.getDate() - 7);
                        } else {
                          newDate.setMonth(newDate.getMonth() - 1);
                        }
                        setCurrentDate(newDate);
                      }}>
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => setCurrentDate(new Date())}>
                        今天
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => {
                        const newDate = new Date(currentDate);
                        if (calendarView === "day") {
                          newDate.setDate(newDate.getDate() + 1);
                        } else if (calendarView === "week") {
                          newDate.setDate(newDate.getDate() + 7);
                        } else {
                          newDate.setMonth(newDate.getMonth() + 1);
                        }
                        setCurrentDate(newDate);
                      }}>
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                      <div className="text-lg font-medium ml-2">
                        {calendarView === "day" && `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月${currentDate.getDate()}日`}
                        {calendarView === "week" && `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月${currentDate.getDate()}日 - ${new Date(currentDate.getTime() + 6 * 24 * 60 * 60 * 1000).getDate()}日`}
                        {calendarView === "month" && `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`}
                      </div>
                    </div>
                    <div className="flex items-center border rounded-md">
                      <Button
                        variant={calendarView === "day" ? "secondary" : "ghost"}
                        size="sm"
                        className="rounded-none"
                        onClick={() => setCalendarView("day")}
                      >
                        日
                      </Button>
                      <Button
                        variant={calendarView === "week" ? "secondary" : "ghost"}
                        size="sm"
                        className="rounded-none"
                        onClick={() => setCalendarView("week")}
                      >
                        周
                      </Button>
                      <Button
                        variant={calendarView === "month" ? "secondary" : "ghost"}
                        size="sm"
                        className="rounded-none"
                        onClick={() => setCalendarView("month")}
                      >
                        月
                      </Button>
                    </div>
                  </div>

                  {calendarView === "day" && (
                    <div className="border rounded-lg overflow-hidden">
                      <div className="grid grid-cols-[100px_1fr] h-[600px]">
                        {/* 时间轴 */}
                        <div className="border-r">
                          {Array.from({ length: 12 }).map((_, index) => (
                            <div key={index} className="h-[50px] border-b flex items-center justify-center text-sm text-muted-foreground">
                              {index + 8}:00
                            </div>
                          ))}
                        </div>
                        {/* 日程 */}
                        <div className="relative">
                          {Array.from({ length: 12 }).map((_, index) => (
                            <div key={index} className="h-[50px] border-b"></div>
                          ))}
                          {courses.filter(course => {
                            const courseDate = new Date(course.startTime);
                            return courseDate.getDate() === currentDate.getDate() &&
                                  courseDate.getMonth() === currentDate.getMonth() &&
                                  courseDate.getFullYear() === currentDate.getFullYear();
                          }).map(course => {
                            const startDate = new Date(course.startTime);
                            const endDate = new Date(course.endTime);
                            const startHour = startDate.getHours();
                            const startMinute = startDate.getMinutes();
                            const endHour = endDate.getHours();
                            const endMinute = endDate.getMinutes();

                            const top = (startHour - 8) * 50 + (startMinute / 60) * 50;
                            const height = (endHour - startHour) * 50 + ((endMinute - startMinute) / 60) * 50;

                            return (
                              <div
                                key={course.id}
                                className="absolute rounded-md p-2 overflow-hidden cursor-pointer"
                                style={{
                                  top: `${top}px`,
                                  height: `${height}px`,
                                  left: '10px',
                                  right: '10px',
                                  backgroundColor: course.type === 'group' ? '#e0f2fe' : '#fef3c7',
                                  borderLeft: course.type === 'group' ? '4px solid #0ea5e9' : '4px solid #f59e0b',
                                }}
                              >
                                <div className="font-medium text-sm truncate">{course.name}</div>
                                <div className="text-xs text-muted-foreground truncate">
                                  {course.coach} • {course.location}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {startHour}:{startMinute.toString().padStart(2, '0')} - {endHour}:{endMinute.toString().padStart(2, '0')}
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  )}

                  {calendarView === "week" && (
                    <div className="border rounded-lg overflow-hidden">
                      <div className="grid grid-cols-[100px_1fr_1fr_1fr_1fr_1fr_1fr_1fr] h-[600px]">
                        {/* 时间轴 */}
                        <div className="border-r">
                          <div className="h-[40px] border-b"></div>
                          {Array.from({ length: 12 }).map((_, index) => (
                            <div key={index} className="h-[50px] border-b flex items-center justify-center text-sm text-muted-foreground">
                              {index + 8}:00
                            </div>
                          ))}
                        </div>

                        {/* 星期几 */}
                        {Array.from({ length: 7 }).map((_, dayIndex) => {
                          const day = new Date(currentDate);
                          day.setDate(day.getDate() - day.getDay() + dayIndex);
                          const dayStr = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"][dayIndex];
                          const isToday = day.toDateString() === new Date().toDateString();

                          return (
                            <div key={dayIndex} className="border-r relative">
                              <div className={`h-[40px] border-b flex items-center justify-center font-medium ${isToday ? 'bg-primary/10' : ''}`}>
                                <div className="text-sm">
                                  {dayStr} ({day.getMonth() + 1}/{day.getDate()})
                                </div>
                              </div>

                              {Array.from({ length: 12 }).map((_, hourIndex) => (
                                <div key={hourIndex} className={`h-[50px] border-b ${isToday ? 'bg-primary/5' : ''}`}></div>
                              ))}

                              {courses.filter(course => {
                                const courseDate = new Date(course.startTime);
                                return courseDate.getDate() === day.getDate() &&
                                      courseDate.getMonth() === day.getMonth() &&
                                      courseDate.getFullYear() === day.getFullYear();
                              }).map(course => {
                                const startDate = new Date(course.startTime);
                                const endDate = new Date(course.endTime);
                                const startHour = startDate.getHours();
                                const startMinute = startDate.getMinutes();
                                const endHour = endDate.getHours();
                                const endMinute = endDate.getMinutes();

                                const top = 40 + (startHour - 8) * 50 + (startMinute / 60) * 50;
                                const height = (endHour - startHour) * 50 + ((endMinute - startMinute) / 60) * 50;

                                return (
                                  <div
                                    key={course.id}
                                    className="absolute rounded-md p-1 overflow-hidden cursor-pointer"
                                    style={{
                                      top: `${top}px`,
                                      height: `${height}px`,
                                      left: '2px',
                                      right: '2px',
                                      backgroundColor: course.type === 'group' ? '#e0f2fe' : '#fef3c7',
                                      borderLeft: course.type === 'group' ? '3px solid #0ea5e9' : '3px solid #f59e0b',
                                    }}
                                  >
                                    <div className="font-medium text-xs truncate">{course.name}</div>
                                    <div className="text-[10px] text-muted-foreground truncate">
                                      {course.coach}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {calendarView === "month" && (
                    <div className="border rounded-lg overflow-hidden">
                      <div className="grid grid-cols-7 text-center">
                        {["周日", "周一", "周二", "周三", "周四", "周五", "周六"].map((day, index) => (
                          <div key={index} className="py-2 border-b font-medium">
                            {day}
                          </div>
                        ))}

                        {(() => {
                          const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                          const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
                          const daysInMonth = lastDay.getDate();
                          const startOffset = firstDay.getDay();

                          return Array.from({ length: 42 }).map((_, index) => {
                            const dayNumber = index - startOffset + 1;
                            const isCurrentMonth = dayNumber > 0 && dayNumber <= daysInMonth;
                            const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), dayNumber);
                            const isToday = isCurrentMonth && date.toDateString() === new Date().toDateString();

                            const dayCourses = courses.filter(course => {
                              if (!isCurrentMonth) return false;
                              const courseDate = new Date(course.startTime);
                              return courseDate.getDate() === dayNumber &&
                                    courseDate.getMonth() === currentDate.getMonth() &&
                                    courseDate.getFullYear() === currentDate.getFullYear();
                            });

                            return (
                              <div
                                key={index}
                                className={`border h-[100px] p-1 ${isCurrentMonth ? '' : 'bg-muted/20'} ${isToday ? 'bg-primary/10' : ''}`}
                              >
                                {isCurrentMonth && (
                                  <>
                                    <div className={`text-right mb-1 ${isToday ? 'font-bold text-primary' : ''}`}>
                                      {dayNumber}
                                    </div>
                                    <div className="space-y-1 overflow-y-auto max-h-[70px]">
                                      {dayCourses.slice(0, 3).map(course => (
                                        <div
                                          key={course.id}
                                          className="text-xs p-1 rounded truncate"
                                          style={{
                                            backgroundColor: course.type === 'group' ? '#e0f2fe' : '#fef3c7',
                                          }}
                                        >
                                          {course.name}
                                        </div>
                                      ))}
                                      {dayCourses.length > 3 && (
                                        <div className="text-xs text-muted-foreground text-center">
                                          +{dayCourses.length - 3}更多
                                        </div>
                                      )}
                                    </div>
                                  </>
                                )}
                              </div>
                            );
                          });
                        })()}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 教练管理 */}
        <TabsContent value="coaches" className="space-y-4 mt-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-1 items-center gap-2 max-w-md">
              <Input
                placeholder="搜索教练姓名或专长..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              添加教练
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {coaches.map((coach) => (
              <Card key={coach.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{coach.name}</CardTitle>
                        <CardDescription>{coach.specialty}</CardDescription>
                      </div>
                    </div>
                    <Badge variant="outline" className={
                      parseInt(coach.workload) > 80 ? "bg-red-50 text-red-700 border-red-200" :
                      parseInt(coach.workload) > 60 ? "bg-yellow-50 text-yellow-700 border-yellow-200" :
                      "bg-green-50 text-green-700 border-green-200"
                    }>
                      工作量 {coach.workload}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <p className="text-muted-foreground">课程数</p>
                      <p className="font-medium">{coach.courses}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">可用时段</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {coach.availability.slice(0, 2).map((avail, index) => (
                          <Badge key={index} variant="outline" className="bg-muted">
                            {avail.day}
                          </Badge>
                        ))}
                        {coach.availability.length > 2 && (
                          <Badge variant="outline" className="bg-muted">
                            +{coach.availability.length - 2}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button variant="outline" size="sm" className="w-full">
                    查看详情
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>教练工作量分布</CardTitle>
                <CardDescription>各教练工作量占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={coaches.map(coach => ({
                          name: coach.name,
                          value: parseInt(coach.workload),
                          courses: coach.courses
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {coaches.map((coach, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={[
                              "#4285F4", "#EA4335", "#FBBC05", "#34A853", "#9C27B0",
                              "#00ACC1", "#FF7043", "#9E9E9E", "#5C6BC0", "#26A69A"
                            ][index % 10]}
                          />
                        ))}
                      </Pie>
                      <Legend layout="horizontal" verticalAlign="bottom" align="center" />
                      <RechartsTooltip
                        formatter={(value, name, props) => {
                          if (!props || !props.payload) return [value, name];
                          const { payload } = props;
                          return [
                            `${value}% (${payload.courses}课程)`,
                            name
                          ];
                        }}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>教练专长分布</CardTitle>
                <CardDescription>各专长教练数量</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={[
                        { name: "瑜伽基础", count: 5 },
                        { name: "高级瑜伽", count: 3 },
                        { name: "阴瑜伽", count: 2 },
                        { name: "哈他瑜伽", count: 4 },
                        { name: "普拉提", count: 2 },
                        { name: "孕产瑜伽", count: 1 },
                      ]}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <RechartsTooltip formatter={(value) => [`${value}名教练`, "数量"]} />
                      <Bar dataKey="count" fill="#8884d8">
                        {[
                          { name: "瑜伽基础", count: 5 },
                          { name: "高级瑜伽", count: 3 },
                          { name: "阴瑜伽", count: 2 },
                          { name: "哈他瑜伽", count: 4 },
                          { name: "普拉提", count: 2 },
                          { name: "孕产瑜伽", count: 1 },
                        ].map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={[
                              "#4285F4", "#EA4335", "#FBBC05", "#34A853", "#9C27B0", "#00ACC1"
                            ][index % 6]}
                          />
                        ))}
                      </Bar>
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>教练工作量趋势</CardTitle>
              <CardDescription>近6周教练工作量变化趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={[
                      { week: "第1周", "张教练": 65, "李教练": 70, "王教练": 80, "赵教练": 60 },
                      { week: "第2周", "张教练": 68, "李教练": 72, "王教练": 82, "赵教练": 62 },
                      { week: "第3周", "张教练": 70, "李教练": 75, "王教练": 85, "赵教练": 65 },
                      { week: "第4周", "张教练": 72, "李教练": 78, "王教练": 88, "赵教练": 68 },
                      { week: "第5周", "张教练": 75, "李教练": 80, "王教练": 90, "赵教练": 70 },
                      { week: "第6周", "张教练": 78, "李教练": 82, "王教练": 92, "赵教练": 72 },
                    ]}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 10,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="week" />
                    <YAxis
                      label={{ value: '工作量(%)', angle: -90, position: 'insideLeft' }}
                    />
                    <RechartsTooltip />
                    <Legend />
                    <Line type="monotone" dataKey="张教练" stroke="#4285F4" strokeWidth={2} />
                    <Line type="monotone" dataKey="李教练" stroke="#EA4335" strokeWidth={2} />
                    <Line type="monotone" dataKey="王教练" stroke="#FBBC05" strokeWidth={2} />
                    <Line type="monotone" dataKey="赵教练" stroke="#34A853" strokeWidth={2} />
                    <ReferenceLine y={90} stroke="red" strokeDasharray="3 3" label="工作量上限" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 场地管理 */}
        <TabsContent value="locations" className="space-y-4 mt-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-1 items-center gap-2 max-w-md">
              <Input
                placeholder="搜索场地名称或类型..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              添加场地
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {locations.map((location) => (
              <Card key={location.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{location.name}</CardTitle>
                      <CardDescription>{location.type === "group" ? "团体教室" : "私教区域"}</CardDescription>
                    </div>
                    <Badge variant="outline" className={
                      location.utilization === "90%" ? "bg-red-50 text-red-700 border-red-200" :
                      location.utilization === "80%" ? "bg-yellow-50 text-yellow-700 border-yellow-200" :
                      "bg-green-50 text-green-700 border-green-200"
                    }>
                      利用率 {location.utilization}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <p className="text-muted-foreground">容量</p>
                      <p className="font-medium">{location.capacity}人</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">可用性</p>
                      <Badge variant={
                        location.availability === "高" ? "outline" :
                        location.availability === "中" ? "secondary" : "destructive"
                      }>
                        {location.availability}
                      </Badge>
                    </div>
                    <div className="col-span-2">
                      <p className="text-muted-foreground">设备</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {location.equipment.map((equip, index) => (
                          <Badge key={index} variant="outline" className="bg-muted">
                            {equip}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button variant="outline" size="sm" className="w-full">
                    查看详情
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">场地总数</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{locations.length}</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +1
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上月</span>
                </div>
                <p className="text-xs text-muted-foreground mt-3 pt-3 border-t">当前可用场地总数</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">平均利用率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round(locations.reduce((sum, location) => sum + parseInt(location.utilization), 0) / locations.length)}%
                </div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +5%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上月</span>
                </div>
                <p className="text-xs text-muted-foreground mt-3 pt-3 border-t">所有场地的平均利用率</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">总容量</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {locations.reduce((sum, location) => {
                    // 确保capacity是数字类型
                    const capacity = typeof location.capacity === 'number'
                      ? location.capacity
                      : parseInt(String(location.capacity).replace(/[^0-9]/g, ''));
                    return sum + capacity;
                  }, 0)}
                </div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +20
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">较上月</span>
                </div>
                <p className="text-xs text-muted-foreground mt-3 pt-3 border-t">所有场地的总容量</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>场地利用率分析</CardTitle>
                <CardDescription>各场地的使用情况和利用率</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={locations.map(location => ({
                        name: location.name,
                        利用率: parseInt(location.utilization),
                        类型: location.type === "group" ? "团体教室" : "私教区域"
                      }))}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis
                        label={{ value: '利用率(%)', angle: -90, position: 'insideLeft' }}
                      />
                      <RechartsTooltip
                        formatter={(value, name, props) => {
                          if (!props || !props.payload) return [value, name];
                          const { payload } = props;
                          return [
                            `${value}%`,
                            `${payload.name} (${payload.类型})`
                          ];
                        }}
                      />
                      <Legend />
                      <Bar dataKey="利用率" fill="#8884d8">
                        {locations.map((location, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={location.type === "group" ? "#4285F4" : "#34A853"}
                          />
                        ))}
                      </Bar>
                      <ReferenceLine y={90} stroke="red" strokeDasharray="3 3" label="高负荷警戒线" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>场地类型分布</CardTitle>
                <CardDescription>不同类型场地的数量和容量</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={[
                          {
                            name: "团体教室",
                            value: locations.filter(l => l.type === "group").length,
                            capacity: locations
                              .filter(l => l.type === "group")
                              .reduce((sum, l) => {
                                const capacity = typeof l.capacity === 'number'
                                  ? l.capacity
                                  : parseInt(String(l.capacity).replace(/[^0-9]/g, ''));
                                return sum + capacity;
                              }, 0)
                          },
                          {
                            name: "私教区域",
                            value: locations.filter(l => l.type === "private").length,
                            capacity: locations
                              .filter(l => l.type === "private")
                              .reduce((sum, l) => {
                                const capacity = typeof l.capacity === 'number'
                                  ? l.capacity
                                  : parseInt(String(l.capacity).replace(/[^0-9]/g, ''));
                                return sum + capacity;
                              }, 0)
                          }
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        <Cell fill="#4285F4" />
                        <Cell fill="#34A853" />
                      </Pie>
                      <Legend layout="horizontal" verticalAlign="bottom" align="center" />
                      <RechartsTooltip
                        formatter={(value, name, props) => {
                          if (!props || !props.payload) return [value, name];
                          const { payload } = props;
                          return [
                            `${value}个场地 (总容量: ${payload.capacity}人)`,
                            name
                          ];
                        }}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>场地使用时段热力图</CardTitle>
              <CardDescription>各场地在不同时段的使用密度</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <div className="grid grid-cols-[150px_1fr] h-full">
                  <div className="pr-4 border-r">
                    <div className="h-10"></div>
                    {locations.map((location, index) => (
                      <div key={index} className="h-10 flex items-center">
                        <span className="text-sm truncate">{location.name}</span>
                      </div>
                    ))}
                  </div>
                  <div>
                    <div className="grid grid-cols-5 h-10">
                      {["上午", "中午", "下午", "傍晚", "晚上"].map((time, index) => (
                        <div key={index} className="flex items-center justify-center text-xs font-medium">
                          {time}
                        </div>
                      ))}
                    </div>
                    {locations.map((location, locationIndex) => (
                      <div key={locationIndex} className="grid grid-cols-5 h-10 gap-1">
                        {[0.3, 0.7, 0.9, 0.8, 0.5].map((density, timeIndex) => (
                          <TooltipProvider key={timeIndex}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div
                                  className="h-8 rounded-sm cursor-pointer"
                                  style={{
                                    backgroundColor: `rgba(54, 162, 235, ${density})`,
                                  }}
                                ></div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{location.name} - {["上午", "中午", "下午", "傍晚", "晚上"][timeIndex]}</p>
                                <p>使用率: {Math.round(density * 100)}%</p>
                                <p>课程数: {Math.round(density * 10)}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
                <div className="flex justify-between items-center mt-4">
                  <div className="text-xs">低使用率</div>
                  <div className="w-full mx-2 h-2 bg-gradient-to-r from-white to-blue-500 rounded-full"></div>
                  <div className="text-xs">高使用率</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 冲突检测 */}
        <TabsContent value="conflicts" className="space-y-4 mt-6">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle>排课冲突</CardTitle>
                <Button variant="outline" size="sm">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重新检测
                </Button>
              </div>
              <CardDescription>自动检测到的排课冲突和解决建议</CardDescription>
            </CardHeader>
            <CardContent>
              {conflicts.length > 0 ? (
                <div className="space-y-4">
                  {conflicts.map((conflict) => (
                    <div key={conflict.id} className={`border rounded-lg p-4 ${
                      conflict.status === "resolved" ? "border-green-200 bg-green-50" :
                      conflict.severity === "high" ? "border-red-200 bg-red-50" : "border-yellow-200 bg-yellow-50"
                    }`}>
                      <div className="flex justify-between items-start">
                        <div className="flex items-start gap-3">
                          {conflict.status === "resolved" ? (
                            <CheckCircle2 className="h-5 w-5 text-green-500 mt-0.5" />
                          ) : (
                            <AlertTriangle className={`h-5 w-5 ${
                              conflict.severity === "high" ? "text-red-500" : "text-yellow-500"
                            } mt-0.5`} />
                          )}
                          <div>
                            <h3 className="font-medium">
                              {conflict.type === "coach" ? "教练冲突" : "场地冲突"}
                            </h3>
                            <p className="text-sm text-muted-foreground mt-1">{conflict.description}</p>
                          </div>
                        </div>
                        <Badge variant={
                          conflict.status === "resolved" ? "outline" :
                          conflict.severity === "high" ? "destructive" : "secondary"
                        }>
                          {conflict.status === "resolved" ? "已解决" :
                           conflict.severity === "high" ? "高优先级" : "中优先级"}
                        </Badge>
                      </div>

                      <div className="mt-3 flex flex-wrap gap-2">
                        {conflict.courses.map((course, index) => (
                          <Badge key={index} variant="outline" className="bg-background">
                            {course}
                          </Badge>
                        ))}
                      </div>

                      <div className="mt-3 flex justify-end gap-2">
                        {conflict.status !== "resolved" && (
                          <>
                            <Button variant="outline" size="sm">
                              自动解决
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleViewConflictDetails(conflict)}
                            >
                              手动处理
                            </Button>
                          </>
                        )}
                        {conflict.status === "resolved" && (
                          <Button variant="outline" size="sm">
                            查看详情
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium">没有检测到冲突</h3>
                  <p className="text-muted-foreground mt-1">当前排课没有任何冲突</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 冲突详情对话框 */}
      {showConflictDetails && selectedConflict && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className={`h-6 w-6 ${
                    selectedConflict.severity === "high" ? "text-red-500" : "text-yellow-500"
                  }`} />
                  <div>
                    <h2 className="text-xl font-semibold">
                      {selectedConflict.type === "coach" ? "教练冲突" : "场地冲突"}
                    </h2>
                    <p className="text-muted-foreground mt-1">{selectedConflict.description}</p>
                  </div>
                </div>
                <Button variant="ghost" size="icon" onClick={() => setShowConflictDetails(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="mb-6">
                <h3 className="font-medium mb-3">冲突课程</h3>
                <div className="space-y-3">
                  {selectedConflict.courses.map((courseName: string, index: number) => {
                    const course = courses.find(c => c.name === courseName);
                    if (!course) return null;

                    const startDate = new Date(course.startTime);
                    const endDate = new Date(course.endTime);
                    const formattedDate = `${startDate.getMonth() + 1}月${startDate.getDate()}日`;
                    const formattedTime = `${startDate.getHours()}:${startDate.getMinutes().toString().padStart(2, '0')}-${endDate.getHours()}:${endDate.getMinutes().toString().padStart(2, '0')}`;

                    return (
                      <Card key={index}>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium">{course.name}</h4>
                              <div className="flex items-center gap-3 mt-1 text-sm">
                                <span>{course.coach}</span>
                                <span>•</span>
                                <span>{course.location}</span>
                                <span>•</span>
                                <span>{formattedDate} {formattedTime}</span>
                              </div>
                            </div>
                            <Badge variant="outline">
                              {course.type === "group" ? "团体课" : "私教课"}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>

              <div className="mb-6">
                <h3 className="font-medium mb-3">解决建议</h3>
                <div className="space-y-3">
                  <div className="border rounded-lg p-4 bg-blue-50 border-blue-200">
                    <h4 className="font-medium text-blue-700">自动解决方案</h4>
                    <p className="text-sm text-blue-600 mt-1">
                      {selectedConflict.type === "coach"
                        ? "建议将第二个课程更换为其他可用教练，或调整课程时间避免冲突。"
                        : "建议将第二个课程更换为其他可用场地，或调整课程时间避免冲突。"}
                    </p>
                    <Button variant="outline" size="sm" className="mt-3">
                      应用自动解决
                    </Button>
                  </div>

                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium">手动解决</h4>
                    <div className="grid grid-cols-2 gap-3 mt-3">
                      <div>
                        <Label htmlFor="solution-type" className="block mb-2">解决方式</Label>
                        <Select defaultValue="reschedule">
                          <SelectTrigger id="solution-type">
                            <SelectValue placeholder="选择解决方式" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="reschedule">调整时间</SelectItem>
                            <SelectItem value="relocate">更换场地</SelectItem>
                            <SelectItem value="reassign">更换教练</SelectItem>
                            <SelectItem value="cancel">取消课程</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="course-select" className="block mb-2">选择课程</Label>
                        <Select defaultValue={selectedConflict.courses[1]}>
                          <SelectTrigger id="course-select">
                            <SelectValue placeholder="选择要调整的课程" />
                          </SelectTrigger>
                          <SelectContent>
                            {selectedConflict.courses.map((course: string, index: number) => (
                              <SelectItem key={index} value={course}>
                                {course}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2 mt-6">
                <Button variant="outline" onClick={() => setShowConflictDetails(false)}>
                  取消
                </Button>
                <Button>
                  应用解决方案
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 添加课程对话框 */}
      {showScheduleForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">添加新课程</h2>
                <Button variant="ghost" size="icon" onClick={() => setShowScheduleForm(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="course-name" className="block mb-2">课程名称</Label>
                    <Input id="course-name" placeholder="输入课程名称" />
                  </div>
                  <div>
                    <Label htmlFor="course-type" className="block mb-2">课程类型</Label>
                    <Select defaultValue="group">
                      <SelectTrigger id="course-type">
                        <SelectValue placeholder="选择课程类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="group">团体课</SelectItem>
                        <SelectItem value="private">私教课</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="course-coach" className="block mb-2">教练</Label>
                    <Select>
                      <SelectTrigger id="course-coach">
                        <SelectValue placeholder="选择教练" />
                      </SelectTrigger>
                      <SelectContent>
                        {coaches.map((coach) => (
                          <SelectItem key={coach.id} value={coach.id}>
                            {coach.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="course-location" className="block mb-2">场地</Label>
                    <Select>
                      <SelectTrigger id="course-location">
                        <SelectValue placeholder="选择场地" />
                      </SelectTrigger>
                      <SelectContent>
                        {locations.map((location) => (
                          <SelectItem key={location.id} value={location.id}>
                            {location.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="course-date" className="block mb-2">日期</Label>
                    <Input id="course-date" type="date" />
                  </div>
                  <div>
                    <Label htmlFor="course-time" className="block mb-2">时间</Label>
                    <div className="flex items-center gap-2">
                      <Input id="course-time" type="time" />
                      <span>至</span>
                      <Input type="time" />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="course-capacity" className="block mb-2">容量</Label>
                    <Input id="course-capacity" type="number" min="1" placeholder="输入课程容量" />
                  </div>
                  <div>
                    <Label htmlFor="course-recurring" className="block mb-2">重复</Label>
                    <Select defaultValue="once">
                      <SelectTrigger id="course-recurring">
                        <SelectValue placeholder="选择重复方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="once">不重复</SelectItem>
                        <SelectItem value="daily">每天</SelectItem>
                        <SelectItem value="weekly">每周</SelectItem>
                        <SelectItem value="monthly">每月</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="course-description" className="block mb-2">课程描述</Label>
                  <textarea
                    id="course-description"
                    className="w-full min-h-[100px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    placeholder="输入课程描述"
                  ></textarea>
                </div>

                <div className="flex items-center gap-2">
                  <Switch id="check-conflicts" />
                  <Label htmlFor="check-conflicts">添加前检查冲突</Label>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowScheduleForm(false)}>
                  取消
                </Button>
                <Button>
                  <Save className="mr-2 h-4 w-4" />
                  保存课程
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}