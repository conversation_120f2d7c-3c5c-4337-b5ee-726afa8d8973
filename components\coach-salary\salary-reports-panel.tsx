"use client"

import { useState } from "react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DatePickerWithRange as DateRangePicker } from "@/components/ui/date-range-picker"
import { Download, <PERSON><PERSON>hart, LineChart, PieChart } from "lucide-react"

// 导入图表组件
import {
  Bar,
  Bar<PERSON>hart as RechartsBarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart as RechartsLineChart,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rts<PERSON>ie<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
} from "recharts"

// 模拟数据 - 教练列表
const mockCoaches = [
  { id: "all", name: "所有教练" },
  { id: "C001", name: "张教练" },
  { id: "C002", name: "李教练" },
  { id: "C003", name: "王教练" },
  { id: "C004", name: "赵教练" },
]

// 模拟数据 - 薪资趋势
const mockSalaryTrendData = [
  { month: "2023-05", totalSalary: 12500, baseSalary: 5000, hourlySalary: 6000, commissionAmount: 1500 },
  { month: "2023-06", totalSalary: 13800, baseSalary: 5000, hourlySalary: 7200, commissionAmount: 1600 },
  { month: "2023-07", totalSalary: 15200, baseSalary: 5000, hourlySalary: 8000, commissionAmount: 2200 },
  { month: "2023-08", totalSalary: 16500, baseSalary: 5000, hourlySalary: 8500, commissionAmount: 3000 },
  { month: "2023-09", totalSalary: 17000, baseSalary: 5000, hourlySalary: 9000, commissionAmount: 3000 },
  { month: "2023-10", totalSalary: 16800, baseSalary: 5000, hourlySalary: 8800, commissionAmount: 3000 },
]

// 模拟数据 - 教练薪资对比
const mockCoachSalaryComparisonData = [
  { name: "张教练", totalSalary: 17000 },
  { name: "李教练", totalSalary: 12100 },
  { name: "王教练", totalSalary: 10100 },
  { name: "赵教练", totalSalary: 11200 },
]

// 模拟数据 - 薪资构成
const mockSalaryCompositionData = [
  { name: "底薪", value: 17500 },
  { name: "课时费", value: 33400 },
  { name: "提成", value: 9500 },
  { name: "奖金", value: 2300 },
]

// 饼图颜色
const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"];

export function SalaryReportsPanel() {
  const [selectedCoach, setSelectedCoach] = useState<string>("all")
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: new Date(new Date().setMonth(new Date().getMonth() - 5, 1)),
    to: new Date(),
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:items-end md:justify-between md:space-y-0">
        <div className="space-y-2">
          <Label htmlFor="coach">选择教练</Label>
          <Select value={selectedCoach} onValueChange={setSelectedCoach}>
            <SelectTrigger id="coach" className="w-[200px]">
              <SelectValue placeholder="请选择教练" />
            </SelectTrigger>
            <SelectContent>
              {mockCoaches.map((coach) => (
                <SelectItem key={coach.id} value={coach.id}>
                  {coach.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker
            className="w-[300px]"
            selected={dateRange}
            onSelect={setDateRange}
          />
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            <span>导出报表</span>
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">薪资概览</TabsTrigger>
          <TabsTrigger value="trend">薪资趋势</TabsTrigger>
          <TabsTrigger value="composition">薪资构成</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">总薪资支出</CardTitle>
                <CardDescription>所选时间范围内的总薪资支出</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥90,700</div>
                <p className="text-xs text-muted-foreground">
                  较上期增长 12.5%
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">平均月薪</CardTitle>
                <CardDescription>所选时间范围内的平均月薪</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥15,117</div>
                <p className="text-xs text-muted-foreground">
                  较上期增长 5.2%
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">薪资占收入比</CardTitle>
                <CardDescription>薪资支出占总收入的比例</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">32.5%</div>
                <p className="text-xs text-muted-foreground">
                  较上期下降 1.8%
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>教练薪资对比</CardTitle>
              <CardDescription>
                各教练薪资对比（最近一个月）
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart
                    data={mockCoachSalaryComparisonData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`¥${value}`, "薪资"]} />
                    <Legend />
                    <Bar dataKey="totalSalary" name="薪资" fill="#8884d8" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trend" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>薪资趋势</CardTitle>
              <CardDescription>
                {selectedCoach === "all" ? "所有教练" : mockCoaches.find(c => c.id === selectedCoach)?.name} 的薪资趋势
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={mockSalaryTrendData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`¥${value}`, ""]} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="totalSalary"
                      name="总薪资"
                      stroke="#8884d8"
                      activeDot={{ r: 8 }}
                    />
                    <Line type="monotone" dataKey="baseSalary" name="底薪" stroke="#82ca9d" />
                    <Line type="monotone" dataKey="hourlySalary" name="课时费" stroke="#ffc658" />
                    <Line type="monotone" dataKey="commissionAmount" name="提成" stroke="#ff7300" />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>课时数与薪资关系</CardTitle>
              <CardDescription>
                课时数与薪资的关系分析
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart
                    data={[
                      { month: "2023-05", classHours: 30, hourlySalary: 6000 },
                      { month: "2023-06", classHours: 36, hourlySalary: 7200 },
                      { month: "2023-07", classHours: 40, hourlySalary: 8000 },
                      { month: "2023-08", classHours: 42.5, hourlySalary: 8500 },
                      { month: "2023-09", classHours: 45, hourlySalary: 9000 },
                      { month: "2023-10", classHours: 44, hourlySalary: 8800 },
                    ]}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                    <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="classHours" name="课时数" fill="#8884d8" />
                    <Bar yAxisId="right" dataKey="hourlySalary" name="课时费" fill="#82ca9d" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="composition" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>薪资构成</CardTitle>
                <CardDescription>
                  薪资各组成部分占比
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={mockSalaryCompositionData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {mockSalaryCompositionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`¥${value}`, ""]} />
                      <Legend />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>薪资构成明细</CardTitle>
                <CardDescription>
                  薪资各组成部分金额
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={[
                        { month: "2023-05", baseSalary: 5000, hourlySalary: 6000, commissionAmount: 1500, bonusAmount: 0 },
                        { month: "2023-06", baseSalary: 5000, hourlySalary: 7200, commissionAmount: 1600, bonusAmount: 0 },
                        { month: "2023-07", baseSalary: 5000, hourlySalary: 8000, commissionAmount: 2200, bonusAmount: 0 },
                        { month: "2023-08", baseSalary: 5000, hourlySalary: 8500, commissionAmount: 3000, bonusAmount: 0 },
                        { month: "2023-09", baseSalary: 5000, hourlySalary: 9000, commissionAmount: 3000, bonusAmount: 0 },
                        { month: "2023-10", baseSalary: 5000, hourlySalary: 8800, commissionAmount: 3000, bonusAmount: 0 },
                      ]}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`¥${value}`, ""]} />
                      <Legend />
                      <Bar dataKey="baseSalary" name="底薪" stackId="a" fill="#8884d8" />
                      <Bar dataKey="hourlySalary" name="课时费" stackId="a" fill="#82ca9d" />
                      <Bar dataKey="commissionAmount" name="提成" stackId="a" fill="#ffc658" />
                      <Bar dataKey="bonusAmount" name="奖金" stackId="a" fill="#ff7300" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
