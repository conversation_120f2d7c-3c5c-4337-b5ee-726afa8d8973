"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  Search, 
  Star, 
  TrendingDown, 
  TrendingUp, 
  BarChart, 
  LineChart, 
  MessageSquare, 
  MoreHorizontal, 
  User,
  Calendar,
  Clock,
  ThumbsUp,
  ThumbsDown,
  AlertTriangle,
  ChevronDown,
  ChevronUp,
  Info,
  FileText,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"

// 模拟教练评分数据
const coachPerformanceData = [
  {
    id: "C001",
    name: "李教练",
    avatar: "/avatars/coach-01.png",
    phone: "13800138001",
    specialty: "哈他瑜伽",
    currentRating: 3.2,
    previousRating: 4.5,
    ratingChange: -1.3,
    ratingTrend: "下降",
    attendanceRate: 98,
    attendanceTrend: "稳定",
    classCount: 45,
    studentCount: 320,
    positiveComments: 12,
    negativeComments: 8,
    alertLevel: "高",
    lastEvaluation: "2023-05-15",
    status: "未处理",
    recentComments: [
      { content: "教练态度不好，对新学员不够耐心", rating: 2, date: "2023-06-05", student: "张三" },
      { content: "动作讲解不够清晰，课程节奏太快", rating: 3, date: "2023-06-03", student: "李四" },
      { content: "课程内容与描述不符，难度太高", rating: 2, date: "2023-06-01", student: "王五" }
    ]
  },
  {
    id: "C002",
    name: "王教练",
    avatar: "/avatars/coach-02.png",
    phone: "13800138002",
    specialty: "流瑜伽",
    currentRating: 4.8,
    previousRating: 4.6,
    ratingChange: 0.2,
    ratingTrend: "上升",
    attendanceRate: 100,
    attendanceTrend: "稳定",
    classCount: 38,
    studentCount: 280,
    positiveComments: 25,
    negativeComments: 1,
    alertLevel: "低",
    lastEvaluation: "2023-05-20",
    status: "正常",
    recentComments: [
      { content: "教练非常专业，动作讲解清晰", rating: 5, date: "2023-06-06", student: "赵六" },
      { content: "课程设计合理，很适合我的水平", rating: 5, date: "2023-06-04", student: "孙七" },
      { content: "教练很有耐心，对每个学员都很关注", rating: 5, date: "2023-06-02", student: "周八" }
    ]
  },
  {
    id: "C003",
    name: "张教练",
    avatar: "/avatars/coach-03.png",
    phone: "13800138003",
    specialty: "阴瑜伽",
    currentRating: 3.8,
    previousRating: 4.2,
    ratingChange: -0.4,
    ratingTrend: "下降",
    attendanceRate: 95,
    attendanceTrend: "下降",
    classCount: 32,
    studentCount: 240,
    positiveComments: 15,
    negativeComments: 5,
    alertLevel: "中",
    lastEvaluation: "2023-05-18",
    status: "已沟通",
    recentComments: [
      { content: "教练专业度不错，但课程安排有些混乱", rating: 4, date: "2023-06-05", student: "吴九" },
      { content: "有时候迟到，影响课程体验", rating: 3, date: "2023-06-03", student: "郑十" },
      { content: "讲解清晰，但对学员反馈不够重视", rating: 4, date: "2023-06-01", student: "冯十一" }
    ]
  },
  {
    id: "C004",
    name: "刘教练",
    avatar: "/avatars/coach-04.png",
    phone: "13800138004",
    specialty: "普拉提",
    currentRating: 4.0,
    previousRating: 3.5,
    ratingChange: 0.5,
    ratingTrend: "上升",
    attendanceRate: 97,
    attendanceTrend: "上升",
    classCount: 28,
    studentCount: 210,
    positiveComments: 18,
    negativeComments: 3,
    alertLevel: "低",
    lastEvaluation: "2023-05-22",
    status: "正常",
    recentComments: [
      { content: "教练最近进步很大，课程质量提升明显", rating: 4, date: "2023-06-06", student: "钱十二" },
      { content: "动作示范很标准，讲解也更清晰了", rating: 4, date: "2023-06-04", student: "孙十三" },
      { content: "课程设计更合理了，强度适中", rating: 4, date: "2023-06-02", student: "李十四" }
    ]
  },
  {
    id: "C005",
    name: "赵教练",
    avatar: "/avatars/coach-05.png",
    phone: "13800138005",
    specialty: "空中瑜伽",
    currentRating: 2.8,
    previousRating: 4.0,
    ratingChange: -1.2,
    ratingTrend: "下降",
    attendanceRate: 90,
    attendanceTrend: "下降",
    classCount: 25,
    studentCount: 180,
    positiveComments: 8,
    negativeComments: 12,
    alertLevel: "高",
    lastEvaluation: "2023-05-10",
    status: "未处理",
    recentComments: [
      { content: "教练最近状态不佳，课程质量下降", rating: 2, date: "2023-06-05", student: "周十五" },
      { content: "安全指导不够，有学员差点受伤", rating: 2, date: "2023-06-03", student: "吴十六" },
      { content: "经常迟到，课程时间缩短", rating: 3, date: "2023-06-01", student: "郑十七" }
    ]
  }
];

export default function CoachPerformancePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [alertLevelFilter, setAlertLevelFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedCoach, setSelectedCoach] = useState<any>(null)
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false)
  const [feedbackContent, setFeedbackContent] = useState("")

  // 获取警报等级标签样式
  const getAlertLevelBadge = (level: string) => {
    switch(level) {
      case "高":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">高风险</Badge>
      case "中":
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">中风险</Badge>
      case "低":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">低风险</Badge>
      default:
        return <Badge>{level}风险</Badge>
    }
  }

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "未处理":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">未处理</Badge>
      case "已沟通":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">已沟通</Badge>
      case "正常":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">正常</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取评分变化样式
  const getRatingChangeBadge = (change: number) => {
    if (change > 0) {
      return (
        <div className="flex items-center text-green-600">
          <TrendingUp className="h-4 w-4 mr-1" />
          <span>+{change.toFixed(1)}</span>
        </div>
      )
    } else if (change < 0) {
      return (
        <div className="flex items-center text-red-600">
          <TrendingDown className="h-4 w-4 mr-1" />
          <span>{change.toFixed(1)}</span>
        </div>
      )
    } else {
      return (
        <div className="flex items-center text-gray-600">
          <span>持平</span>
        </div>
      )
    }
  }

  // 获取评分样式
  const getRatingStyle = (rating: number) => {
    if (rating >= 4.0) {
      return "text-green-600"
    } else if (rating >= 3.0) {
      return "text-amber-600"
    } else {
      return "text-red-600"
    }
  }

  // 获取星级显示
  const getRatingStars = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating - fullStars >= 0.5;
    const stars = [];

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<Star key={i} className="h-4 w-4 fill-amber-400 text-amber-400" />);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(<Star key={i} className="h-4 w-4 fill-amber-400 text-amber-400 half-filled" />);
      } else {
        stars.push(<Star key={i} className="h-4 w-4 text-gray-300" />);
      }
    }

    return <div className="flex">{stars}</div>;
  };

  // 过滤教练数据
  const filteredCoaches = coachPerformanceData.filter(coach => {
    // 基本搜索过滤
    const searchFilter = 
      searchQuery === "" || 
      coach.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      coach.phone.includes(searchQuery) ||
      coach.specialty.toLowerCase().includes(searchQuery.toLowerCase());
    
    // 标签页过滤
    const tabFilter = 
      activeTab === "all" || 
      (activeTab === "declining" && coach.ratingTrend === "下降") ||
      (activeTab === "improving" && coach.ratingTrend === "上升") ||
      (activeTab === "stable" && coach.ratingTrend === "稳定");
    
    // 警报等级过滤
    const alertFilter = 
      alertLevelFilter === "all" || 
      coach.alertLevel === alertLevelFilter;
    
    // 状态过滤
    const statusFilterResult = 
      statusFilter === "all" || 
      coach.status === statusFilter;
    
    return searchFilter && tabFilter && alertFilter && statusFilterResult;
  });

  // 处理查看详情
  const handleViewDetail = (coach: any) => {
    setSelectedCoach(coach);
    router.push(`/coaches/${coach.id}`);
  };

  // 处理反馈
  const handleFeedback = (coach: any) => {
    setSelectedCoach(coach);
    setFeedbackContent("");
    setShowFeedbackDialog(true);
  };

  // 提交反馈
  const submitFeedback = () => {
    if (!feedbackContent.trim()) {
      toast({
        title: "反馈内容不能为空",
        description: "请输入反馈内容",
        variant: "destructive",
      });
      return;
    }

    // 实际应用中，这里应该调用API提交反馈
    toast({
      title: "反馈已提交",
      description: `已向 ${selectedCoach.name} 发送反馈`,
    });
    setShowFeedbackDialog(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">教练绩效管理</h1>
          <p className="text-muted-foreground">
            监控和管理教练评分、出勤率和学员反馈
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.push('/coaches/evaluation')}>
            <FileText className="h-4 w-4 mr-2" />
            绩效评估
          </Button>
          <Button onClick={() => router.push('/coaches/training')}>
            <User className="h-4 w-4 mr-2" />
            教练培训
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>绩效概览</CardTitle>
          <CardDescription>
            当前共有 {coachPerformanceData.filter(c => c.ratingTrend === "下降").length} 名教练评分下降，{coachPerformanceData.filter(c => c.alertLevel === "高").length} 名教练处于高风险状态
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">评分下降</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{coachPerformanceData.filter(c => c.ratingTrend === "下降").length}</div>
                <p className="text-xs text-muted-foreground">
                  评分下降的教练数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">评分上升</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{coachPerformanceData.filter(c => c.ratingTrend === "上升").length}</div>
                <p className="text-xs text-muted-foreground">
                  评分上升的教练数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">高风险</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{coachPerformanceData.filter(c => c.alertLevel === "高").length}</div>
                <p className="text-xs text-muted-foreground">
                  高风险教练数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">未处理</CardTitle>
                <Clock className="h-4 w-4 text-amber-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{coachPerformanceData.filter(c => c.status === "未处理").length}</div>
                <p className="text-xs text-muted-foreground">
                  未处理的异常教练数量
                </p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4 max-w-md">
          <TabsTrigger value="all">全部教练</TabsTrigger>
          <TabsTrigger value="declining">评分下降</TabsTrigger>
          <TabsTrigger value="improving">评分上升</TabsTrigger>
          <TabsTrigger value="stable">评分稳定</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative w-full md:w-1/3">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索教练姓名、手机号或专长"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-1 gap-2">
          <Select value={alertLevelFilter} onValueChange={setAlertLevelFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="风险等级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部风险等级</SelectItem>
              <SelectItem value="高">高风险</SelectItem>
              <SelectItem value="中">中风险</SelectItem>
              <SelectItem value="低">低风险</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="处理状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="未处理">未处理</SelectItem>
              <SelectItem value="已沟通">已沟通</SelectItem>
              <SelectItem value="正常">正常</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>教练信息</TableHead>
                <TableHead>当前评分</TableHead>
                <TableHead>评分变化</TableHead>
                <TableHead>出勤率</TableHead>
                <TableHead>学员反馈</TableHead>
                <TableHead>风险等级</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCoaches.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    没有找到符合条件的教练
                  </TableCell>
                </TableRow>
              ) : (
                filteredCoaches.map((coach) => (
                  <TableRow key={coach.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-9 w-9">
                          <AvatarImage src={coach.avatar} alt={coach.name} />
                          <AvatarFallback>{coach.name.slice(0, 1)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{coach.name}</div>
                          <div className="text-xs text-muted-foreground">{coach.specialty}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className={`font-medium ${getRatingStyle(coach.currentRating)}`}>
                        {coach.currentRating.toFixed(1)}
                      </div>
                      <div className="mt-1">
                        {getRatingStars(coach.currentRating)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getRatingChangeBadge(coach.ratingChange)}
                      <div className="text-xs text-muted-foreground mt-1">
                        上月: {coach.previousRating.toFixed(1)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{coach.attendanceRate}%</div>
                      <div className="text-xs flex items-center">
                        {coach.attendanceTrend === "上升" ? (
                          <span className="text-green-600 flex items-center">
                            <ArrowUpRight className="h-3 w-3 mr-1" />上升
                          </span>
                        ) : coach.attendanceTrend === "下降" ? (
                          <span className="text-red-600 flex items-center">
                            <ArrowDownRight className="h-3 w-3 mr-1" />下降
                          </span>
                        ) : (
                          <span className="text-gray-600">稳定</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center text-green-600">
                          <ThumbsUp className="h-4 w-4 mr-1" />
                          <span>{coach.positiveComments}</span>
                        </div>
                        <div className="flex items-center text-red-600">
                          <ThumbsDown className="h-4 w-4 mr-1" />
                          <span>{coach.negativeComments}</span>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        共 {coach.classCount} 节课 / {coach.studentCount} 名学员
                      </div>
                    </TableCell>
                    <TableCell>
                      {getAlertLevelBadge(coach.alertLevel)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(coach.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>教练操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleViewDetail(coach)}>
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleFeedback(coach)}>
                            发送反馈
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            安排培训
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            调整课程
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 反馈对话框 */}
      <Dialog open={showFeedbackDialog} onOpenChange={setShowFeedbackDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>发送反馈</DialogTitle>
            <DialogDescription>
              向 {selectedCoach?.name} 发送绩效反馈
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {selectedCoach && (
              <div className="flex items-center gap-3 p-3 rounded-md border">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={selectedCoach.avatar} alt={selectedCoach.name} />
                  <AvatarFallback>{selectedCoach.name.slice(0, 1)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{selectedCoach.name}</div>
                  <div className="text-sm text-muted-foreground">{selectedCoach.specialty}</div>
                  <div className="flex items-center gap-2 mt-1">
                    <div className={`text-sm ${getRatingStyle(selectedCoach.currentRating)}`}>
                      评分: {selectedCoach.currentRating.toFixed(1)}
                    </div>
                    <div className="text-sm">
                      {getRatingChangeBadge(selectedCoach.ratingChange)}
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div className="grid gap-2">
              <Label htmlFor="feedback">反馈内容</Label>
              <Textarea
                id="feedback"
                placeholder="请输入反馈内容..."
                rows={5}
                value={feedbackContent}
                onChange={(e) => setFeedbackContent(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowFeedbackDialog(false)}>
              取消
            </Button>
            <Button onClick={submitFeedback}>
              发送反馈
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
