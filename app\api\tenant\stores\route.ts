import { NextResponse } from 'next/server';

// 门店数据结构
export interface Store {
  id: number;
  tenant_id: number;
  store_name: string;
  store_type: string; // 总店/分店
  address: string;
  city: string;
  province: string;
  country: string;
  phone: string;
  email?: string;
  business_hours: string;
  manager_id?: number;
  manager_name?: string;
  logo_url?: string;
  status: number; // 1-营业中，0-休息中，-1-已关闭
  created_at: string;
  updated_at: string;
}

// 模拟静心瑜伽馆的门店数据
const MOCK_STORES: Store[] = [
  {
    id: 1,
    tenant_id: 1,
    store_name: '静心瑜伽馆 - 三里屯总店',
    store_type: '总店',
    address: '北京市朝阳区三里屯SOHO 2号楼3层301',
    city: '北京',
    province: '北京',
    country: '中国',
    phone: '010-12345678',
    email: '<EMAIL>',
    business_hours: '10:00-22:00',
    manager_id: 1,
    manager_name: '张静',
    logo_url: '/logo/jingxin.png',
    status: 1,
    created_at: '2023-01-01T08:00:00Z',
    updated_at: '2023-01-01T08:00:00Z'
  },
  {
    id: 2,
    tenant_id: 1,
    store_name: '静心瑜伽馆 - 国贸分店',
    store_type: '分店',
    address: '北京市朝阳区建国门外大街1号国贸商城3期5层501',
    city: '北京',
    province: '北京',
    country: '中国',
    phone: '010-87654321',
    email: '<EMAIL>',
    business_hours: '09:00-21:00',
    manager_id: 2,
    manager_name: '李平',
    logo_url: '/logo/jingxin.png',
    status: 1,
    created_at: '2023-03-15T09:30:00Z',
    updated_at: '2023-03-15T09:30:00Z'
  },
  {
    id: 3,
    tenant_id: 1,
    store_name: '静心瑜伽馆 - 望京SOHO店',
    store_type: '分店',
    address: '北京市朝阳区望京SOHO T1栋12层1208',
    city: '北京',
    province: '北京',
    country: '中国',
    phone: '010-87654322',
    email: '<EMAIL>',
    business_hours: '09:00-21:00',
    manager_id: 3,
    manager_name: '王芳',
    logo_url: '/logo/jingxin.png',
    status: 1,
    created_at: '2023-06-15T08:30:00Z',
    updated_at: '2023-06-15T08:30:00Z'
  },
  {
    id: 4,
    tenant_id: 1,
    store_name: '静心瑜伽馆 - 朝阳公园店',
    store_type: '分店',
    address: '北京市朝阳区朝阳公园路6号院17号楼1层101',
    city: '北京',
    province: '北京',
    country: '中国',
    phone: '010-87654323',
    email: '<EMAIL>',
    business_hours: '09:30-21:30',
    manager_id: 4,
    manager_name: '赵明',
    logo_url: '/logo/jingxin.png',
    status: 1,
    created_at: '2023-09-10T10:00:00Z',
    updated_at: '2023-09-10T10:00:00Z'
  }
];

// GET 获取租户门店状态
export async function GET(request: Request) {
  try {
    // 获取租户ID - 默认为1 (静心瑜伽馆)
    let tenantId = '1';
    
    // 尝试从请求头中获取租户ID
    const tenantIdHeader = request.headers.get('X-Tenant-ID');
    if (tenantIdHeader) {
      tenantId = tenantIdHeader;
    }
    
    // 根据租户ID过滤门店
    const tenantStores = MOCK_STORES.filter(store => 
      store.tenant_id.toString() === tenantId
    );
    
    // 返回门店信息和状态
    return NextResponse.json({
      code: 0,
      message: '获取租户门店信息成功',
      data: {
        hasStores: tenantStores.length > 0,
        stores: tenantStores
      }
    });
  } catch (error) {
    console.error('获取租户门店信息失败:', error);
    return NextResponse.json(
      { code: 500, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST 创建新门店
export async function POST(request: Request) {
  try {
    // 解析请求体
    const body = await request.json();
    
    // 获取租户ID - 默认为1 (静心瑜伽馆)
    let tenantId = 1;
    
    // 尝试从请求头中获取租户ID
    const tenantIdHeader = request.headers.get('X-Tenant-ID');
    if (tenantIdHeader) {
      tenantId = parseInt(tenantIdHeader);
    }
    
    // 创建新门店记录
    const newStore: Store = {
      ...body,
      id: MOCK_STORES.length + 1,
      tenant_id: tenantId,
      status: body.status || 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    // 将新门店添加到模拟数据中
    MOCK_STORES.push(newStore);
    
    // 返回创建成功的响应
    return NextResponse.json({
      code: 0,
      message: '创建门店成功',
      data: newStore
    });
  } catch (error) {
    console.error('创建门店失败:', error);
    return NextResponse.json(
      { code: 500, message: '服务器内部错误' },
      { status: 500 }
    );
  }
}