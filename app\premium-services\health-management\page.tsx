"use client"

import { useState } from "react"
import Link from "next/link"
import {
  <PERSON><PERSON>hart as RechartsLineChart,
  Line,
  Bar<PERSON>hart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as <PERSON>chartsTooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as RechartsPieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from "recharts"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"
import {
  BarChart,
  Calendar,
  ChevronRight,
  Clock,
  Download,
  FileText,
  Filter,
  LineChart,
  MoreHorizontal,
  PieChart,
  Plus,
  Printer,
  RefreshCw,
  Search,
  Settings,
  Share,
  TrendingDown,
  TrendingUp,
  DollarSign,
  ArrowUpDown,
  CreditCard,
  Wallet,
  Landmark,
  CircleDollarSign,
  BarChart3,
  ArrowDownRight,
  ArrowUpRight,
  AlertCircle,
  CheckCircle2,
  X,
  Info,
  Heart,
  Activity,
  Dumbbell,
  Utensils,
  Scale,
  Ruler,
  Flame,
  Droplets,
  Zap,
  Footprints,
  Leaf,
  Timer,
  User,
  Users,
  CalendarDays,
  Edit,
  Trash2,
  BookOpen,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"

// 模拟会员健康数据
const memberHealthData = [
  {
    id: "1",
    name: "张三",
    age: 28,
    gender: "男",
    joinDate: "2023-01-15",
    lastCheckDate: "2023-10-10",
    metrics: {
      weight: { current: 75.5, target: 70, unit: "kg", history: [78, 77.2, 76.5, 75.8, 75.5] },
      bodyFat: { current: 22, target: 18, unit: "%", history: [25, 24, 23.5, 22.8, 22] },
      flexibility: { current: 6, target: 10, unit: "cm", history: [3, 4, 5, 5.5, 6] },
      strength: { current: 65, target: 75, unit: "kg", history: [55, 58, 60, 63, 65] },
      endurance: { current: 25, target: 35, unit: "min", history: [18, 20, 22, 24, 25] },
      bmi: { current: 24.8, target: 23, unit: "", history: [25.6, 25.4, 25.1, 24.9, 24.8] },
      hydration: { current: 55, target: 60, unit: "%", history: [50, 52, 53, 54, 55] },
    },
    goals: [
      { id: "g1", name: "减重5公斤", progress: 50, deadline: "2023-12-31", status: "in-progress" },
      { id: "g2", name: "提高柔韧度", progress: 60, deadline: "2023-11-30", status: "in-progress" },
      { id: "g3", name: "增强核心力量", progress: 40, deadline: "2024-01-15", status: "in-progress" },
    ],
    activities: [
      { date: "2023-10-10", type: "瑜伽课", duration: 60, calories: 350, notes: "完成全部动作，柔韧性有提升" },
      { date: "2023-10-08", type: "力量训练", duration: 45, calories: 420, notes: "增加了重量，感觉良好" },
      { date: "2023-10-05", type: "有氧运动", duration: 30, calories: 280, notes: "有些疲劳，需要调整强度" },
      { date: "2023-10-03", type: "瑜伽课", duration: 60, calories: 340, notes: "专注于呼吸和平衡" },
      { date: "2023-10-01", type: "力量训练", duration: 50, calories: 450, notes: "核心训练，感觉有挑战" },
    ],
    nutrition: {
      caloriesIntake: { avg: 1850, target: 1800, unit: "kcal" },
      protein: { avg: 95, target: 100, unit: "g" },
      carbs: { avg: 220, target: 200, unit: "g" },
      fat: { avg: 65, target: 60, unit: "g" },
      water: { avg: 1.8, target: 2.5, unit: "L" },
    },
    recommendations: [
      { id: "r1", type: "nutrition", content: "增加蛋白质摄入，减少精制碳水化合物" },
      { id: "r2", type: "exercise", content: "增加每周瑜伽练习频率，专注于柔韧性训练" },
      { id: "r3", type: "lifestyle", content: "改善睡眠质量，每晚保证7-8小时睡眠" },
    ]
  },
  // 更多会员数据...
]

// 模拟健康评估报告数据
const healthAssessmentData = {
  overallScore: 78,
  categories: [
    { name: "身体成分", score: 75 },
    { name: "柔韧性", score: 65 },
    { name: "力量", score: 80 },
    { name: "心肺功能", score: 85 },
    { name: "平衡能力", score: 70 },
    { name: "姿势", score: 90 },
  ],
  recommendations: [
    "增加柔韧性训练，每周至少3次专项练习",
    "保持当前力量训练计划，可适当增加重量",
    "继续心肺训练，保持每周150分钟中等强度有氧运动",
    "增加平衡训练，建议加入瑜伽或太极课程",
    "保持良好姿势习惯，避免久坐不动",
  ],
  progress: [
    { date: "2023-05", score: 65 },
    { date: "2023-06", score: 68 },
    { date: "2023-07", score: 70 },
    { date: "2023-08", score: 73 },
    { date: "2023-09", score: 75 },
    { date: "2023-10", score: 78 },
  ]
}

export default function HealthManagementPage() {
  const [activeTab, setActiveTab] = useState("dashboard")
  const [selectedMember, setSelectedMember] = useState(memberHealthData[0])
  const [isLoading, setIsLoading] = useState(false)
  const [showAddGoalDialog, setShowAddGoalDialog] = useState(false)
  const [newGoal, setNewGoal] = useState({
    name: "",
    deadline: "",
    description: ""
  })

  // 刷新数据
  const refreshData = () => {
    setIsLoading(true)
    // 模拟API请求
    setTimeout(() => {
      setIsLoading(false)
      // 这里可以添加实际的数据刷新逻辑
    }, 1000)
  }

  // 添加新目标
  const handleAddGoal = () => {
    // 这里可以添加实际的添加目标逻辑
    setShowAddGoalDialog(false)
    setNewGoal({ name: "", deadline: "", description: "" })
  }

  return (
    <div className="space-y-6 relative">
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-50">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
            <span className="mt-2 text-sm text-muted-foreground">加载中...</span>
          </div>
        </div>
      )}

      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">会员健康管理系统</h1>
          <p className="text-muted-foreground">全面的会员健康数据追踪与管理，提供个性化健康目标设定与进度跟踪</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button variant="outline" onClick={refreshData}>
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新数据
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            添加会员
          </Button>
        </div>
      </div>

      <div className="text-sm breadcrumbs">
        <ul className="flex items-center space-x-1">
          <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li>会员健康管理系统</li>
        </ul>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex flex-wrap items-center gap-2 w-full md:w-auto">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索会员..."
              className="w-full pl-8"
            />
          </div>
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="会员状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部会员</SelectItem>
              <SelectItem value="active">活跃会员</SelectItem>
              <SelectItem value="inactive">非活跃会员</SelectItem>
              <SelectItem value="new">新会员</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5 max-w-3xl">
          <TabsTrigger value="dashboard">健康仪表盘</TabsTrigger>
          <TabsTrigger value="goals">健康目标</TabsTrigger>
          <TabsTrigger value="activities">活动记录</TabsTrigger>
          <TabsTrigger value="nutrition">营养管理</TabsTrigger>
          <TabsTrigger value="reports">健康报告</TabsTrigger>
        </TabsList>

        {/* 健康仪表盘 */}
        <TabsContent value="dashboard" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">体重</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{selectedMember.metrics.weight.current} {selectedMember.metrics.weight.unit}</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingDown className="mr-1 h-3 w-3" />
                    -2.5 {selectedMember.metrics.weight.unit}
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">近30天</span>
                </div>
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-muted-foreground mb-1">
                    <span>目标: {selectedMember.metrics.weight.target} {selectedMember.metrics.weight.unit}</span>
                    <span>进度: {Math.round(((selectedMember.metrics.weight.current - 78) / (selectedMember.metrics.weight.target - 78)) * 100)}%</span>
                  </div>
                  <Progress value={Math.round(((selectedMember.metrics.weight.current - 78) / (selectedMember.metrics.weight.target - 78)) * 100)} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">体脂率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{selectedMember.metrics.bodyFat.current}%</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingDown className="mr-1 h-3 w-3" />
                    -3%
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">近30天</span>
                </div>
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-muted-foreground mb-1">
                    <span>目标: {selectedMember.metrics.bodyFat.target}%</span>
                    <span>进度: {Math.round(((25 - selectedMember.metrics.bodyFat.current) / (25 - selectedMember.metrics.bodyFat.target)) * 100)}%</span>
                  </div>
                  <Progress value={Math.round(((25 - selectedMember.metrics.bodyFat.current) / (25 - selectedMember.metrics.bodyFat.target)) * 100)} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">柔韧度</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{selectedMember.metrics.flexibility.current} {selectedMember.metrics.flexibility.unit}</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +3 {selectedMember.metrics.flexibility.unit}
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">近30天</span>
                </div>
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-muted-foreground mb-1">
                    <span>目标: {selectedMember.metrics.flexibility.target} {selectedMember.metrics.flexibility.unit}</span>
                    <span>进度: {Math.round((selectedMember.metrics.flexibility.current / selectedMember.metrics.flexibility.target) * 100)}%</span>
                  </div>
                  <Progress value={Math.round((selectedMember.metrics.flexibility.current / selectedMember.metrics.flexibility.target) * 100)} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">整体健康评分</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{healthAssessmentData.overallScore}/100</div>
                <div className="flex items-center mt-1">
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    <TrendingUp className="mr-1 h-3 w-3" />
                    +13
                  </Badge>
                  <span className="text-xs text-muted-foreground ml-2">近6个月</span>
                </div>
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-muted-foreground mb-1">
                    <span>目标: 85/100</span>
                    <span>进度: {Math.round((healthAssessmentData.overallScore / 85) * 100)}%</span>
                  </div>
                  <Progress value={Math.round((healthAssessmentData.overallScore / 85) * 100)} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>健康指标趋势</CardTitle>
                <CardDescription>近6个月健康指标变化</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart
                      data={[
                        { month: "5月", weight: 78, bodyFat: 25, flexibility: 3 },
                        { month: "6月", weight: 77.2, bodyFat: 24, flexibility: 4 },
                        { month: "7月", weight: 76.5, bodyFat: 23.5, flexibility: 5 },
                        { month: "8月", weight: 76, bodyFat: 23, flexibility: 5.5 },
                        { month: "9月", weight: 75.8, bodyFat: 22.8, flexibility: 5.8 },
                        { month: "10月", weight: 75.5, bodyFat: 22, flexibility: 6 },
                      ]}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 10,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <RechartsTooltip />
                      <Legend />
                      <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="weight"
                        name="体重 (kg)"
                        stroke="#3b82f6"
                        activeDot={{ r: 8 }}
                      />
                      <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="bodyFat"
                        name="体脂率 (%)"
                        stroke="#ef4444"
                      />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="flexibility"
                        name="柔韧度 (cm)"
                        stroke="#22c55e"
                      />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>健康评估雷达图</CardTitle>
                <CardDescription>各项健康指标评分</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart outerRadius={90} data={healthAssessmentData.categories}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="name" />
                      <PolarRadiusAxis angle={30} domain={[0, 100]} />
                      <Radar
                        name="当前评分"
                        dataKey="score"
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.6}
                      />
                      <Legend />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 其他标签页内容 */}
        <TabsContent value="goals" className="space-y-4 mt-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">健康目标</h3>
            <Button onClick={() => setShowAddGoalDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              添加目标
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {selectedMember.goals.map((goal) => (
              <Card key={goal.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-base font-medium">{goal.name}</CardTitle>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑目标
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <CheckCircle2 className="mr-2 h-4 w-4" />
                          标记为完成
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除目标
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  <CardDescription>截止日期: {goal.deadline}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>进度</span>
                      <span>{goal.progress}%</span>
                    </div>
                    <Progress value={goal.progress} className="h-2" />
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>状态: {goal.status === "in-progress" ? "进行中" : "已完成"}</span>
                      <Badge variant={goal.status === "in-progress" ? "outline" : "default"}>
                        {goal.status === "in-progress" ? "进行中" : "已完成"}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* 添加目标对话框 */}
      <Dialog open={showAddGoalDialog} onOpenChange={setShowAddGoalDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>添加健康目标</DialogTitle>
            <DialogDescription>
              为会员设定新的健康目标和计划
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="goal-name">目标名称</Label>
              <Input
                id="goal-name"
                placeholder="例如：减重5公斤"
                value={newGoal.name}
                onChange={(e) => setNewGoal({ ...newGoal, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="goal-deadline">截止日期</Label>
              <Input
                id="goal-deadline"
                type="date"
                value={newGoal.deadline}
                onChange={(e) => setNewGoal({ ...newGoal, deadline: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="goal-description">目标描述</Label>
              <Textarea
                id="goal-description"
                placeholder="详细描述目标内容和达成方法..."
                value={newGoal.description}
                onChange={(e) => setNewGoal({ ...newGoal, description: e.target.value })}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddGoalDialog(false)}>取消</Button>
            <Button onClick={handleAddGoal}>添加目标</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
