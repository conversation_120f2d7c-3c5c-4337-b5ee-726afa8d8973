"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { InfoIcon } from "lucide-react"

interface CourseType {
  id: string | number
  name: string
  color?: string
  description?: string
}

interface SelectedCourseType {
  course_type_id: string | number
  consumption_weight?: number
  max_usage_times?: number
}

interface CourseTypeSelectorProps {
  courseTypes: CourseType[]
  selectedTypes: SelectedCourseType[]
  onChange: (selectedTypes: SelectedCourseType[]) => void
  showWeightSettings: boolean
  disabled?: boolean
}

export function CourseTypeSelector({
  courseTypes,
  selectedTypes,
  onChange,
  showWeightSettings,
  disabled = false,
}: CourseTypeSelectorProps) {
  // 本地状态，用于跟踪选中的课程类型
  const [selected, setSelected] = useState<Map<string | number, SelectedCourseType>>(new Map())

  // 初始化选中状态
  useEffect(() => {
    const selectedMap = new Map<string | number, SelectedCourseType>()
    selectedTypes.forEach((type) => {
      selectedMap.set(type.course_type_id, type)
    })
    setSelected(selectedMap)
  }, [selectedTypes])

  // 处理课程类型选择变化
  const handleTypeChange = (courseType: CourseType, isChecked: boolean) => {
    const newSelected = new Map(selected)
    
    if (isChecked) {
      // 添加到选中列表
      newSelected.set(courseType.id, {
        course_type_id: courseType.id,
        consumption_weight: 1.0,
        max_usage_times: null
      })
    } else {
      // 从选中列表移除
      newSelected.delete(courseType.id)
    }
    
    setSelected(newSelected)
    notifyChange(newSelected)
  }

  // 处理权重变化
  const handleWeightChange = (courseTypeId: string | number, weight: number) => {
    const newSelected = new Map(selected)
    const currentType = newSelected.get(courseTypeId)
    
    if (currentType) {
      newSelected.set(courseTypeId, {
        ...currentType,
        consumption_weight: weight
      })
      
      setSelected(newSelected)
      notifyChange(newSelected)
    }
  }

  // 处理最大使用次数变化
  const handleMaxUsageChange = (courseTypeId: string | number, maxUsage: number | null) => {
    const newSelected = new Map(selected)
    const currentType = newSelected.get(courseTypeId)
    
    if (currentType) {
      newSelected.set(courseTypeId, {
        ...currentType,
        max_usage_times: maxUsage
      })
      
      setSelected(newSelected)
      notifyChange(newSelected)
    }
  }

  // 通知父组件变化
  const notifyChange = (selectedMap: Map<string | number, SelectedCourseType>) => {
    const selectedArray = Array.from(selectedMap.values())
    onChange(selectedArray)
  }

  // 全选/取消全选
  const handleSelectAll = (isChecked: boolean) => {
    const newSelected = new Map<string | number, SelectedCourseType>()
    
    if (isChecked) {
      courseTypes.forEach(type => {
        newSelected.set(type.id, {
          course_type_id: type.id,
          consumption_weight: 1.0,
          max_usage_times: null
        })
      })
    }
    
    setSelected(newSelected)
    notifyChange(newSelected)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Checkbox 
            id="select-all" 
            checked={selected.size === courseTypes.length && courseTypes.length > 0}
            onCheckedChange={handleSelectAll}
            disabled={disabled || courseTypes.length === 0}
          />
          <Label htmlFor="select-all" className="cursor-pointer">全选</Label>
        </div>
        
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <InfoIcon className="h-4 w-4 text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>
              <p>选择此会员卡可用的课程类型</p>
              {showWeightSettings && (
                <p>权重值用于加权计算消耗值</p>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <Card>
        <CardContent className="p-4">
          {courseTypes.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              暂无课程类型数据
            </div>
          ) : (
            <ScrollArea className="h-[200px] pr-4">
              <div className="space-y-4">
                {courseTypes.map((courseType) => (
                  <div key={courseType.id} className="space-y-2">
                    <div className="flex items-start">
                      <div className="flex items-center space-x-2">
                        <Checkbox 
                          id={`course-type-${courseType.id}`}
                          checked={selected.has(courseType.id)}
                          onCheckedChange={(checked) => handleTypeChange(courseType, !!checked)}
                          disabled={disabled}
                        />
                        <div>
                          <Label 
                            htmlFor={`course-type-${courseType.id}`}
                            className="flex items-center cursor-pointer"
                          >
                            {courseType.color && (
                              <div 
                                className="h-3 w-3 rounded-full mr-2" 
                                style={{ backgroundColor: courseType.color }} 
                              />
                            )}
                            <span>{courseType.name}</span>
                          </Label>
                          {courseType.description && (
                            <p className="text-xs text-muted-foreground ml-5">
                              {courseType.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    {selected.has(courseType.id) && (
                      <div className="ml-7 grid grid-cols-1 md:grid-cols-2 gap-4">
                        {showWeightSettings && (
                          <div className="space-y-1">
                            <Label 
                              htmlFor={`weight-${courseType.id}`}
                              className="text-xs flex items-center"
                            >
                              消耗权重
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <InfoIcon className="h-3 w-3 ml-1 text-muted-foreground" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>权重越高，消耗的价值越多</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </Label>
                            <Input
                              id={`weight-${courseType.id}`}
                              type="number"
                              min="0.01"
                              step="0.01"
                              value={selected.get(courseType.id)?.consumption_weight || 1.0}
                              onChange={(e) => handleWeightChange(courseType.id, parseFloat(e.target.value) || 1.0)}
                              className="h-8"
                              disabled={disabled}
                            />
                          </div>
                        )}
                        
                        <div className="space-y-1">
                          <Label 
                            htmlFor={`max-usage-${courseType.id}`}
                            className="text-xs flex items-center"
                          >
                            最大使用次数
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <InfoIcon className="h-3 w-3 ml-1 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>限制此类型课程的最大使用次数，留空表示不限制</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </Label>
                          <Input
                            id={`max-usage-${courseType.id}`}
                            type="number"
                            min="0"
                            step="1"
                            value={selected.get(courseType.id)?.max_usage_times || ''}
                            onChange={(e) => {
                              const value = e.target.value === '' ? null : parseInt(e.target.value)
                              handleMaxUsageChange(courseType.id, value)
                            }}
                            placeholder="不限制"
                            className="h-8"
                            disabled={disabled}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
      
      <div className="flex flex-wrap gap-2">
        {Array.from(selected.entries()).map(([id, type]) => {
          const courseType = courseTypes.find(ct => ct.id === id)
          return courseType ? (
            <Badge 
              key={id} 
              variant="outline"
              className="flex items-center gap-1"
            >
              {courseType.color && (
                <div 
                  className="h-2 w-2 rounded-full" 
                  style={{ backgroundColor: courseType.color }} 
                />
              )}
              <span>{courseType.name}</span>
              {showWeightSettings && type.consumption_weight !== 1.0 && (
                <span className="text-xs">({type.consumption_weight}倍)</span>
              )}
              {type.max_usage_times && (
                <span className="text-xs">最多{type.max_usage_times}次</span>
              )}
            </Badge>
          ) : null
        })}
      </div>
    </div>
  )
}
