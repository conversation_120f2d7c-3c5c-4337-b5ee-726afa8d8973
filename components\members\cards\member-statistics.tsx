"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { GenderDistributionChart } from "./gender-distribution-chart"
import { AgeDistributionChart } from "./age-distribution-chart"
import { cn } from "@/lib/utils"

interface MemberStatisticsProps {
  cardId: number | string
  className?: string
}

export function MemberStatistics({ cardId, className }: MemberStatisticsProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <h2 className="text-xl font-semibold">会员统计</h2>
      
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <GenderDistributionChart cardId={cardId} />
        <AgeDistributionChart cardId={cardId} />
      </div>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base">会员活跃度</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="rounded-lg border p-3 text-center">
              <div className="text-2xl font-bold text-primary">85%</div>
              <div className="text-xs text-muted-foreground">续卡率</div>
            </div>
            <div className="rounded-lg border p-3 text-center">
              <div className="text-2xl font-bold text-primary">3.2</div>
              <div className="text-xs text-muted-foreground">周均访问次数</div>
            </div>
            <div className="rounded-lg border p-3 text-center">
              <div className="text-2xl font-bold text-primary">78%</div>
              <div className="text-xs text-muted-foreground">活跃会员比例</div>
            </div>
          </div>
          
          <div className="mt-4">
            <h4 className="mb-2 text-sm font-medium">会员来源分布</h4>
            <div className="grid grid-cols-4 gap-2">
              <div className="flex flex-col rounded-lg border p-2 text-center">
                <div className="text-sm font-medium">自然访问</div>
                <div className="text-lg font-bold text-primary">35%</div>
              </div>
              <div className="flex flex-col rounded-lg border p-2 text-center">
                <div className="text-sm font-medium">会员推荐</div>
                <div className="text-lg font-bold text-primary">28%</div>
              </div>
              <div className="flex flex-col rounded-lg border p-2 text-center">
                <div className="text-sm font-medium">营销活动</div>
                <div className="text-lg font-bold text-primary">22%</div>
              </div>
              <div className="flex flex-col rounded-lg border p-2 text-center">
                <div className="text-sm font-medium">其他渠道</div>
                <div className="text-lg font-bold text-primary">15%</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
