"use client"

import { useState, useEffect, useRef } from "react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface ColorPickerProps {
  color: string
  onChange: (color: string) => void
  className?: string
}

export function ColorPicker({ color, onChange, className }: ColorPickerProps) {
  const [value, setValue] = useState(color)
  const [isOpen, setIsOpen] = useState(false)
  
  // 预设颜色
  const presetColors = [
    "#FF5733", "#33FF57", "#3357FF", "#FF33F5", "#F5FF33",
    "#FF9966", "#66FF99", "#6699FF", "#FF66F9", "#F9FF66",
    "#4EADFF", "#FF4EAD", "#ADFF4E", "#4EFFAD", "#AD4EFF",
    "#5B8DEF", "#EF5B8D", "#8DEF5B", "#5BEFAD", "#AD5BEF",
  ]
  
  // 当外部颜色变化时更新内部状态
  useEffect(() => {
    setValue(color)
  }, [color])
  
  // 处理颜色变化
  const handleColorChange = (newColor: string) => {
    setValue(newColor)
    onChange(newColor)
  }
  
  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          className={cn("w-10 h-10 p-0 border-2", className)}
          style={{ backgroundColor: value }}
        />
      </PopoverTrigger>
      <PopoverContent className="w-64">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>选择颜色</Label>
            <div className="flex items-center gap-2">
              <div 
                className="w-8 h-8 rounded-md border"
                style={{ backgroundColor: value }}
              ></div>
              <Input 
                type="color"
                value={value}
                onChange={(e) => handleColorChange(e.target.value)}
                className="w-12 h-8 p-1"
              />
              <Input 
                type="text"
                value={value}
                onChange={(e) => handleColorChange(e.target.value)}
                className="flex-1"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label>预设颜色</Label>
            <div className="grid grid-cols-5 gap-2">
              {presetColors.map((presetColor, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="w-8 h-8 p-0 rounded-md"
                  style={{ backgroundColor: presetColor }}
                  onClick={() => handleColorChange(presetColor)}
                />
              ))}
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
