"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useRouter } from "next/navigation"
import {
  CheckCircle,
  AlertCircle,
  MessageSquare,
  Calendar,
  CreditCard,
  Bell,
  Clock,
  CheckSquare
} from "lucide-react"
import { Badge } from "@/components/ui/badge"

// 模拟数据
const tasks = [
  {
    id: 1,
    icon: <CheckCircle className="h-5 w-5 text-primary" />,
    title: "会员卡有效期",
    count: 5,
    href: "/members/renewal",
    priority: "high",
    dueTime: "今天",
  },
  {
    id: 2,
    icon: <AlertCircle className="h-5 w-5 text-amber-500" />,
    title: "会员生日",
    count: 2,
    href: "/members/birthday",
    priority: "medium",
    dueTime: "明天",
  },
  {
    id: 3,
    icon: <CreditCard className="h-5 w-5 text-blue-500" />,
    title: "会员卡余额",
    count: 3,
    href: "/members/balance",
    priority: "medium",
    dueTime: "今天",
  },
  {
    id: 4,
    icon: <Calendar className="h-5 w-5 text-green-500" />,
    title: "多天未上课",
    count: 8,
    href: "/members/inactive",
    priority: "high",
    dueTime: "7天以上",
  },
  {
    id: 5,
    icon: <Bell className="h-5 w-5 text-purple-500" />,
    title: "入会纪念日",
    count: 2,
    href: "/members/anniversary",
    priority: "medium",
    dueTime: "本周",
  },
  {
    id: 6,
    icon: <CreditCard className="h-5 w-5 text-indigo-500" />,
    title: "开卡",
    count: 3,
    href: "/members/new-cards",
    priority: "medium",
    dueTime: "今天",
  },
  {
    id: 7,
    icon: <Clock className="h-5 w-5 text-red-500" />,
    title: "课程到期",
    count: 4,
    href: "/courses/expiring",
    priority: "high",
    dueTime: "本周",
  },
]

export function TaskReminder() {
  const router = useRouter()

  // 获取优先级样式
  const getPriorityStyle = (priority: string) => {
    switch(priority) {
      case "high":
        return "border-l-4 border-red-500"
      case "medium":
        return "border-l-4 border-amber-500"
      case "low":
        return "border-l-4 border-green-500"
      default:
        return ""
    }
  }

  // 获取到期时间样式
  const getDueTimeStyle = (dueTime: string) => {
    if (dueTime.includes("小时")) {
      return "text-red-500"
    } else if (dueTime === "今天") {
      return "text-amber-500"
    } else {
      return "text-muted-foreground"
    }
  }

  return (
    <Card className="border shadow-sm">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium flex items-center">
          <Clock className="mr-2 h-5 w-5 text-primary" />
          待办事项
          <Badge variant="secondary" className="ml-2">
            {tasks.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {tasks.map((task) => (
            <div
              key={task.id}
              className={`flex items-center justify-between p-3 rounded-md ${getPriorityStyle(task.priority)} pl-3 bg-background hover:bg-muted/50 transition-colors border`}
            >
              <div className="flex items-center gap-3">
                {task.icon}
                <div>
                  <span className="font-medium text-sm">{task.title}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">({task.count}项)</span>
                    <span className={`text-xs ${getDueTimeStyle(task.dueTime)} flex items-center`}>
                      <Clock className="h-3 w-3 mr-1" />
                      {task.dueTime}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => router.push(task.href)}
                  className="text-primary hover:text-primary/80 h-8"
                >
                  处理
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                >
                  <CheckSquare className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
