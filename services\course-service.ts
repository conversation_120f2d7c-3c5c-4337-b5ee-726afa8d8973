// 课程接口定义
export interface Course {
  id: number;
  name: string;
  description: string;
  typeId: number;
  typeName: string;
  typeColor: string;
  instructor: string;
  capacity: number;
  enrolled: number;
  duration: number; // 分钟
  price: number;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  [key: string]: any;
}

// 模拟课程数据
const mockCourses: Course[] = [
  {
    id: 1,
    name: "瑜伽基础入门",
    description: "适合初学者的瑜伽基础课程，学习基本体式和呼吸方法",
    typeId: 1,
    typeName: "团课",
    typeColor: "#4285F4", 
    instructor: "张教练",
    capacity: 25,
    enrolled: 18,
    duration: 60, // 分钟
    price: 50,
    status: "active",
    createdAt: "2023-05-15",
    updatedAt: "2023-06-10"
  },
  {
    id: 2,
    name: "高级流瑜伽",
    description: "连贯流畅的瑜伽序列，提升力量和灵活性",
    typeId: 2,
    typeName: "小班课",
    typeColor: "#34A853",
    instructor: "李教练",
    capacity: 10,
    enrolled: 8,
    duration: 75,
    price: 80,
    status: "active",
    createdAt: "2023-05-20",
    updatedAt: "2023-06-15"
  },
  {
    id: 3,
    name: "瑜伽私教定制",
    description: "根据个人需求定制的一对一瑜伽私教课程",
    typeId: 4,
    typeName: "私教课",
    typeColor: "#EA4335",
    instructor: "王教练",
    capacity: 1,
    enrolled: 1,
    duration: 90,
    price: 300,
    status: "active",
    createdAt: "2023-06-01",
    updatedAt: "2023-06-20"
  },
  {
    id: 4,
    name: "高温瑜伽体验",
    description: "在38℃环境下进行的热瑜伽课程，促进排汗和新陈代谢",
    typeId: 6,
    typeName: "热瑜伽",
    typeColor: "#FF9800",
    instructor: "赵教练",
    capacity: 15,
    enrolled: 0,
    duration: 60,
    price: 120,
    status: "inactive",
    createdAt: "2023-06-05",
    updatedAt: "2023-06-25"
  },
  {
    id: 5,
    name: "RYT200瑜伽导师培训",
    description: "国际认证的瑜伽教练培训课程",
    typeId: 5,
    typeName: "教培课",
    typeColor: "#9C27B0",
    instructor: "陈教练",
    capacity: 12,
    enrolled: 10,
    duration: 120,
    price: 12000,
    status: "active",
    createdAt: "2023-06-10",
    updatedAt: "2023-06-30"
  },
  {
    id: 6,
    name: "普拉提器械课程",
    description: "使用专业器械辅助的普拉提课程",
    typeId: 9,
    typeName: "器械普拉提",
    typeColor: "#607D8B",
    instructor: "钱教练",
    capacity: 8,
    enrolled: 5,
    duration: 60,
    price: 150,
    status: "active",
    createdAt: "2023-06-15",
    updatedAt: "2023-07-05"
  }
];

let nextId = 7;

// 获取课程列表（兼容旧接口）
export const getCourses = async (params: {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
}): Promise<{ data: any[]; total: number }> => {
  const { page = 1, pageSize = 10, search = '', status } = params;
  let filtered = [...mockCourses];
  
  if (search) {
    filtered = filtered.filter(c => 
      c.name.toLowerCase().includes(search.toLowerCase()) || 
      c.typeName.toLowerCase().includes(search.toLowerCase()) || 
      c.instructor.toLowerCase().includes(search.toLowerCase())
    );
  }
  
  if (status && status !== 'all') {
    filtered = filtered.filter(c => c.status === status);
  }
  
  const total = filtered.length;
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const data = filtered.slice(start, end);
  
  return { data, total };
};

// 课程服务
export const courseService = {
  getAll: (params: { keyword?: string; status?: string; typeId?: number | string } = {}) => {
    let result = [...mockCourses];
    
    // 关键词搜索
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      result = result.filter(course => 
        course.name.toLowerCase().includes(keyword) ||
        course.description.toLowerCase().includes(keyword) ||
        course.instructor.toLowerCase().includes(keyword)
      );
    }
    
    // 状态筛选
    if (params.status && params.status !== 'all') {
      result = result.filter(course => course.status === params.status);
    }
    
    // 课程类型筛选
    if (params.typeId) {
      const typeId = typeof params.typeId === 'string' ? parseInt(params.typeId, 10) : params.typeId;
      result = result.filter(course => course.typeId === typeId);
    }
    
    return result;
  },

  getById: (id: number) => {
    return mockCourses.find(course => course.id === id);
  },

  create: (data: Partial<Course>) => {
    const newCourse: Course = {
      id: nextId++,
      name: data.name || '',
      description: data.description || '',
      typeId: data.typeId || 0,
      typeName: data.typeName || '未分类',
      typeColor: data.typeColor || '#cccccc',
      instructor: data.instructor || '未分配',
      capacity: data.capacity || 10,
      enrolled: 0,
      duration: data.duration || 60,
      price: data.price || 0,
      status: data.status as 'active' | 'inactive' || 'active',
      createdAt: new Date().toISOString().split('T')[0],
      updatedAt: new Date().toISOString().split('T')[0]
    };
    
    mockCourses.push(newCourse);
    return newCourse;
  },

  update: (id: number, data: Partial<Course>) => {
    const index = mockCourses.findIndex(course => course.id === id);
    if (index !== -1) {
      mockCourses[index] = { 
        ...mockCourses[index], 
        ...data, 
        updatedAt: new Date().toISOString().split('T')[0] 
      };
      return mockCourses[index];
    }
    return null;
  },

  delete: (id: number) => {
    const index = mockCourses.findIndex(course => course.id === id);
    if (index !== -1) {
      // 检查是否有学员已报名
      if (mockCourses[index].enrolled > 0) {
        return { success: false, reason: '该课程已有学员报名，无法删除' };
      }
      
      mockCourses.splice(index, 1);
      return { success: true };
    }
    return { success: false, reason: '课程不存在' };
  },
  
  // 获取课程时间表
  getSchedules: (courseId: number) => {
    const course = mockCourses.find(course => course.id === courseId);
    if (!course) return [];
    
    // 生成模拟时间表
    return [
      {
        id: 1,
        courseId,
        startTime: "2023-07-01T09:00:00",
        endTime: "2023-07-01T10:00:00",
        room: "1号瑜伽室",
        enrolled: Math.floor(course.enrolled * 0.7),
        status: "completed"
      },
      {
        id: 2,
        courseId,
        startTime: "2023-07-08T09:00:00",
        endTime: "2023-07-08T10:00:00",
        room: "1号瑜伽室",
        enrolled: Math.floor(course.enrolled * 0.8),
        status: "completed"
      },
      {
        id: 3,
        courseId,
        startTime: "2023-07-15T09:00:00",
        endTime: "2023-07-15T10:00:00",
        room: "1号瑜伽室",
        enrolled: Math.floor(course.enrolled * 0.9),
        status: "completed"
      },
      {
        id: 4,
        courseId,
        startTime: "2023-07-22T09:00:00",
        endTime: "2023-07-22T10:00:00",
        room: "1号瑜伽室",
        enrolled: course.enrolled,
        status: "upcoming"
      },
      {
        id: 5,
        courseId,
        startTime: "2023-07-29T09:00:00",
        endTime: "2023-07-29T10:00:00",
        room: "1号瑜伽室",
        enrolled: Math.floor(course.enrolled * 0.5),
        status: "upcoming"
      }
    ];
  },
  
  // 统计相关
  getStats: () => {
    const totalCourses = mockCourses.length;
    const activeCourses = mockCourses.filter(course => course.status === 'active').length;
    const totalEnrolled = mockCourses.reduce((sum, course) => sum + course.enrolled, 0);
    const totalCapacity = mockCourses.reduce((sum, course) => sum + course.capacity, 0);
    
    return {
      overview: {
        totalCourses,
        activeCourses,
        inactiveCourses: totalCourses - activeCourses,
        totalEnrolled,
        totalCapacity,
        enrollmentRate: totalCapacity > 0 ? Math.round((totalEnrolled / totalCapacity) * 100) : 0
      },
      // 按月统计课程数量（示例数据）
      monthlyStats: [
        { month: '1月', courses: 0 },
        { month: '2月', courses: 0 },
        { month: '3月', courses: 0 },
        { month: '4月', courses: 0 },
        { month: '5月', courses: 2 },
        { month: '6月', courses: 4 },
        { month: '7月', courses: 0 },
        { month: '8月', courses: 0 },
        { month: '9月', courses: 0 },
        { month: '10月', courses: 0 },
        { month: '11月', courses: 0 },
        { month: '12月', courses: 0 }
      ],
      topCourses: mockCourses
        .filter(course => course.status === 'active')
        .sort((a, b) => b.enrolled - a.enrolled)
        .slice(0, 5)
        .map(course => ({
          id: course.id,
          name: course.name,
          typeId: course.typeId,
          typeName: course.typeName,
          typeColor: course.typeColor,
          enrolled: course.enrolled,
          capacity: course.capacity,
          enrollmentRate: Math.round((course.enrolled / course.capacity) * 100)
        }))
    };
  },
  
  // 批量操作
  batchUpdateStatus: (ids: number[], status: 'active' | 'inactive') => {
    const results = ids.map(id => {
      const courseIndex = mockCourses.findIndex(c => c.id === id);
      if (courseIndex === -1) {
        return { id, success: false, error: '课程不存在' };
      }
      
      mockCourses[courseIndex].status = status;
      mockCourses[courseIndex].updatedAt = new Date().toISOString().split('T')[0];
      
      return { id, success: true, data: mockCourses[courseIndex] };
    });
    
    return results;
  },
  
  batchUpdateType: (ids: number[], typeId: number, typeName: string, typeColor: string) => {
    const results = ids.map(id => {
      const courseIndex = mockCourses.findIndex(c => c.id === id);
      if (courseIndex === -1) {
        return { id, success: false, error: '课程不存在' };
      }
      
      mockCourses[courseIndex].typeId = typeId;
      mockCourses[courseIndex].typeName = typeName;
      mockCourses[courseIndex].typeColor = typeColor;
      mockCourses[courseIndex].updatedAt = new Date().toISOString().split('T')[0];
      
      return { id, success: true, data: mockCourses[courseIndex] };
    });
    
    return results;
  },
  
  batchDelete: (ids: number[]) => {
    const results = ids.map(id => {
      const courseIndex = mockCourses.findIndex(c => c.id === id);
      if (courseIndex === -1) {
        return { id, success: false, error: '课程不存在' };
      }
      
      if (mockCourses[courseIndex].enrolled > 0) {
        return { id, success: false, error: '该课程已有学员报名，无法删除' };
      }
      
      mockCourses.splice(courseIndex, 1);
      return { id, success: true };
    });
    
    return results;
  }
}; 