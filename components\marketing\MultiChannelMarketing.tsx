"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  BarChart,
  Calendar,
  Check,
  ChevronRight,
  Clock,
  Edit,
  Eye,
  FileText,
  Image,
  LayoutGrid,
  List,
  MessageSquare,
  MoreHorizontal,
  Plus,
  Search,
  Settings,
  Share,
  ShoppingBag,
  Star,
  Tag,
  Trash,
  TrendingUp,
  Video,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// 模拟营销渠道数据
const marketingChannels = [
  {
    id: "1",
    name: "微信公众号",
    icon: "/icons/wechat.svg",
    status: "connected",
    followers: "2,345",
    engagement: "4.8%",
    lastPost: "2小时前",
    lastPostContent: "夏季瑜伽课程特惠活动，限时7折！",
    verificationStatus: "已认证",
    accountType: "服务号",
    connectTime: "2023-05-15",
    postFrequency: "每周3次",
    recentPerformance: "上升",
    recentStats: {
      views: "5,432",
      likes: "342",
      shares: "89",
      comments: "56"
    }
  },
  {
    id: "2",
    name: "抖音企业号",
    icon: "/icons/douyin.svg",
    status: "connected",
    followers: "12,890",
    engagement: "5.2%",
    lastPost: "昨天",
    lastPostContent: "10分钟居家瑜伽教程，塑造完美曲线",
    verificationStatus: "已认证",
    accountType: "企业号",
    connectTime: "2023-04-20",
    postFrequency: "每天1次",
    recentPerformance: "稳定",
    recentStats: {
      views: "15,678",
      likes: "1,245",
      shares: "356",
      comments: "234"
    }
  },
  {
    id: "3",
    name: "小红书",
    icon: "/icons/xiaohongshu.svg",
    status: "connected",
    followers: "5,678",
    engagement: "6.7%",
    lastPost: "3天前",
    lastPostContent: "瑜伽初学者必备的5款瑜伽垫推荐",
    verificationStatus: "已认证",
    accountType: "品牌号",
    connectTime: "2023-06-01",
    postFrequency: "每周2次",
    recentPerformance: "上升",
    recentStats: {
      views: "8,765",
      likes: "765",
      shares: "123",
      comments: "98"
    }
  },
  {
    id: "4",
    name: "美团",
    icon: "/icons/meituan.svg",
    status: "not-connected",
    followers: "-",
    engagement: "-",
    lastPost: "-",
    lastPostContent: "",
    verificationStatus: "未认证",
    accountType: "商家号",
    connectTime: "-",
    postFrequency: "-",
    recentPerformance: "-",
    recentStats: {
      views: "-",
      likes: "-",
      shares: "-",
      comments: "-"
    }
  },
]

// 可连接的渠道类型
const availableChannelTypes = [
  { id: "wechat", name: "微信公众号", icon: "/icons/wechat.svg", description: "连接微信公众号，发布图文、视频等内容" },
  { id: "douyin", name: "抖音企业号", icon: "/icons/douyin.svg", description: "连接抖音企业号，发布短视频、直播等内容" },
  { id: "xiaohongshu", name: "小红书", icon: "/icons/xiaohongshu.svg", description: "连接小红书账号，发布笔记、种草等内容" },
  { id: "meituan", name: "美团", icon: "/icons/meituan.svg", description: "连接美团商家号，发布优惠券、活动等内容" },
  { id: "weibo", name: "微博", icon: "/icons/weibo.svg", description: "连接微博账号，发布微博、话题等内容" },
  { id: "zhihu", name: "知乎", icon: "/icons/zhihu.svg", description: "连接知乎账号，发布问答、文章等内容" },
  { id: "bilibili", name: "哔哩哔哩", icon: "/icons/bilibili.svg", description: "连接B站账号，发布视频、直播等内容" },
  { id: "dianping", name: "大众点评", icon: "/icons/dianping.svg", description: "连接大众点评商家号，管理评价、活动等内容" },
]

// 模拟营销活动数据
const marketingCampaigns = [
  {
    id: "1",
    name: "夏季瑜伽课程促销",
    status: "active",
    channels: ["微信公众号", "抖音企业号", "小红书"],
    startDate: "2023-06-01",
    endDate: "2023-06-30",
    budget: "¥2,000",
    spent: "¥1,245",
    engagement: "高",
    roi: "320%",
  },
  {
    id: "2",
    name: "新店开业特惠活动",
    status: "scheduled",
    channels: ["微信公众号", "美团"],
    startDate: "2023-07-15",
    endDate: "2023-08-15",
    budget: "¥5,000",
    spent: "¥0",
    engagement: "-",
    roi: "-",
  },
  {
    id: "3",
    name: "会员专属优惠月",
    status: "ended",
    channels: ["微信公众号", "抖音企业号"],
    startDate: "2023-05-01",
    endDate: "2023-05-31",
    budget: "¥3,000",
    spent: "¥3,000",
    engagement: "中",
    roi: "180%",
  },
]

// 模拟内容库数据
const contentLibrary = [
  {
    id: "1",
    title: "10分钟居家瑜伽教程",
    type: "video",
    thumbnail: "/placeholder.svg?height=100&width=180",
    created: "2023-06-10",
    status: "published",
    channels: ["抖音企业号", "小红书"],
    views: "1,245",
    likes: "89",
    comments: "12",
  },
  {
    id: "2",
    title: "瑜伽对身心健康的5大好处",
    type: "article",
    thumbnail: "/placeholder.svg?height=100&width=180",
    created: "2023-06-05",
    status: "published",
    channels: ["微信公众号"],
    views: "567",
    likes: "45",
    comments: "8",
  },
  {
    id: "3",
    title: "夏季瑜伽装备推荐",
    type: "post",
    thumbnail: "/placeholder.svg?height=100&width=180",
    created: "2023-06-15",
    status: "draft",
    channels: [],
    views: "-",
    likes: "-",
    comments: "-",
  },
]

interface MultiChannelMarketingProps {
  // 入口来源: 'premium-service' 或 'marketing-center'
  entrySource: 'premium-service' | 'marketing-center';
  // 是否有完整访问权限
  hasFullAccess: boolean;
}

export default function MultiChannelMarketing({ entrySource, hasFullAccess }: MultiChannelMarketingProps) {
  // 支持通过URL参数设置初始标签
  const [activeTab, setActiveTab] = useState(() => {
    // 检查URL中是否有tab参数
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      const tabParam = params.get('tab');
      if (tabParam && ['dashboard', 'channels', 'campaigns', 'content', 'analytics'].includes(tabParam)) {
        return tabParam;
      }
    }
    return "dashboard";
  });

  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState("")

  // 渠道管理相关状态
  const [channelSearchQuery, setChannelSearchQuery] = useState("")
  const [selectedChannel, setSelectedChannel] = useState<any>(null)
  const [showChannelDetails, setShowChannelDetails] = useState(false)
  const [showAddChannelDialog, setShowAddChannelDialog] = useState(false)
  const [selectedChannelType, setSelectedChannelType] = useState<string | null>(null)
  const [channelConnectStep, setChannelConnectStep] = useState(1)
  const [channelConnectLoading, setChannelConnectLoading] = useState(false)
  const [channelConnectSuccess, setChannelConnectSuccess] = useState(false)

  // 过滤渠道
  const filteredChannels = marketingChannels.filter(
    (channel) =>
      channel.name.toLowerCase().includes(channelSearchQuery.toLowerCase())
  )

  // 处理标签变化时更新URL
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('tab', value);
      window.history.pushState({}, '', url);
    }
  }

  // 处理查看渠道详情
  const handleViewChannelDetails = (channel: any) => {
    setSelectedChannel(channel)
    setShowChannelDetails(true)
  }

  // 处理添加新渠道
  const handleAddChannel = () => {
    setShowAddChannelDialog(true)
    setSelectedChannelType(null)
    setChannelConnectStep(1)
    setChannelConnectSuccess(false)
  }

  // 处理选择渠道类型
  const handleSelectChannelType = (channelTypeId: string) => {
    setSelectedChannelType(channelTypeId)
    setChannelConnectStep(2)
  }

  // 处理连接渠道
  const handleConnectChannel = () => {
    setChannelConnectLoading(true)

    // 模拟API调用
    setTimeout(() => {
      setChannelConnectLoading(false)
      setChannelConnectSuccess(true)
      setChannelConnectStep(3)

      // 3秒后关闭对话框
      setTimeout(() => {
        setShowAddChannelDialog(false)
        // 实际应用中应该刷新渠道列表
      }, 3000)
    }, 2000)
  }

  // 根据入口来源和访问权限决定显示的UI元素
  const showUpgradePrompt = entrySource === 'marketing-center' && !hasFullAccess
  const showSubscriptionInfo = entrySource === 'premium-service'

  // 根据访问权限限制某些功能
  const limitedFeatures = !hasFullAccess && entrySource === 'marketing-center'

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">多渠道营销工具</h1>
          <p className="text-muted-foreground">一站式管理微信、抖音、美团等多渠道营销活动</p>
        </div>

        {showSubscriptionInfo && (
          <Badge variant="outline" className="px-3 py-1">
            <Clock className="mr-1 h-3 w-3" />
            增值服务 · 已开通
          </Badge>
        )}

        {showUpgradePrompt && (
          <Button variant="outline" className="gap-1">
            <Star className="h-4 w-4 text-yellow-500" />
            升级完整版
          </Button>
        )}
      </div>

      {/* 入口导航提示 */}
      {entrySource === 'premium-service' && (
        <div className="text-sm breadcrumbs">
          <ul className="flex items-center space-x-1">
            <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
            <li><ChevronRight className="h-4 w-4" /></li>
            <li>多渠道营销工具</li>
          </ul>
        </div>
      )}

      {entrySource === 'marketing-center' && (
        <div className="text-sm breadcrumbs">
          <ul className="flex items-center space-x-1">
            <li><Link href="/marketing" className="text-muted-foreground hover:text-foreground">营销中心</Link></li>
            <li><ChevronRight className="h-4 w-4" /></li>
            <li>多渠道营销工具</li>
          </ul>
        </div>
      )}

      {/* 功能限制提示 */}
      {limitedFeatures && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="text-blue-500 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-blue-700">您正在使用基础版多渠道营销工具</h3>
                <p className="text-sm text-blue-600 mt-1">
                  升级到完整版可解锁高级数据分析、自动化营销、ROI分析等功能。
                  <Link href="/premium-services" className="font-medium underline ml-1">
                    了解更多
                  </Link>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-5 max-w-3xl">
          <TabsTrigger value="dashboard">仪表盘</TabsTrigger>
          <TabsTrigger value="channels">渠道管理</TabsTrigger>
          <TabsTrigger value="campaigns">活动管理</TabsTrigger>
          <TabsTrigger value="content">内容库</TabsTrigger>
          <TabsTrigger value="analytics" disabled={limitedFeatures}>数据分析</TabsTrigger>
        </TabsList>

        {/* 仪表盘 */}
        <TabsContent value="dashboard" className="space-y-4 mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">已连接渠道</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3/4</div>
                <p className="text-xs text-muted-foreground">已连接3个营销渠道</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">活跃活动</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">当前有1个活跃营销活动</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">平均ROI</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">250%</div>
                <p className="text-xs text-muted-foreground">过去30天营销活动回报率</p>
              </CardContent>
            </Card>
          </div>

          <Card className={limitedFeatures ? "opacity-60" : ""}>
            <CardHeader>
              <CardTitle>渠道表现</CardTitle>
              <CardDescription>各营销渠道的表现对比</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[200px] flex items-center justify-center">
                {limitedFeatures ? (
                  <div className="text-center">
                    <Lock className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-muted-foreground">升级到完整版查看详细分析</p>
                  </div>
                ) : (
                  <div className="w-full h-full bg-muted rounded-md flex items-center justify-center">
                    <BarChart className="h-8 w-8 text-muted-foreground" />
                    <span className="ml-2 text-muted-foreground">渠道表现图表</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 渠道管理 */}
        <TabsContent value="channels" className="space-y-4 mt-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div>
              <h2 className="text-lg font-medium">渠道管理</h2>
              <p className="text-sm text-muted-foreground">管理所有营销渠道，查看数据和发布内容</p>
            </div>
            <Button onClick={handleAddChannel}>
              <Plus className="mr-2 h-4 w-4" />
              添加新渠道
            </Button>
          </div>

          <div className="flex items-center gap-2 max-w-md">
            <Input
              placeholder="搜索渠道..."
              value={channelSearchQuery}
              onChange={(e) => setChannelSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredChannels.map((channel) => (
              <Card key={channel.id} className={channel.status === "not-connected" ? "opacity-70 hover:opacity-90 transition-opacity" : ""}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      <div className="w-10 h-10 bg-muted rounded-md flex items-center justify-center">
                        <img src={channel.icon} alt={channel.name} className="w-6 h-6" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{channel.name}</CardTitle>
                        <p className="text-xs text-muted-foreground">{channel.accountType}</p>
                      </div>
                    </div>
                    <Badge variant={channel.status === "connected" ? "default" : "outline"}>
                      {channel.status === "connected" ? "已连接" : "未连接"}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div>
                      <p className="text-muted-foreground">粉丝数</p>
                      <p className="font-medium">{channel.followers}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">互动率</p>
                      <p className="font-medium">{channel.engagement}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">最近发布</p>
                      <p className="font-medium">{channel.lastPost}</p>
                    </div>
                  </div>

                  {channel.status === "connected" && (
                    <div className="mt-3 border-t pt-3">
                      <p className="text-xs text-muted-foreground mb-1">最近发布内容</p>
                      <p className="text-sm line-clamp-1">{channel.lastPostContent}</p>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="pt-2 flex gap-2">
                  {channel.status === "connected" ? (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => handleViewChannelDetails(channel)}
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        查看详情
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="sm" className="px-2">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>渠道操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            发布内容
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <MessageSquare className="mr-2 h-4 w-4" />
                            查看评论
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <BarChart className="mr-2 h-4 w-4" />
                            数据分析
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash className="mr-2 h-4 w-4" />
                            断开连接
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </>
                  ) : (
                    <Button
                      size="sm"
                      className="w-full"
                      onClick={() => {
                        setSelectedChannelType(channel.id)
                        setShowAddChannelDialog(true)
                        setChannelConnectStep(2)
                      }}
                    >
                      连接渠道
                    </Button>
                  )}
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* 渠道数据概览 */}
          {filteredChannels.some(channel => channel.status === "connected") && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>渠道数据概览</CardTitle>
                <CardDescription>各渠道近30天数据表现</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium">渠道</th>
                        <th className="text-left py-3 px-4 font-medium">粉丝数</th>
                        <th className="text-left py-3 px-4 font-medium">互动率</th>
                        <th className="text-left py-3 px-4 font-medium">发布频率</th>
                        <th className="text-left py-3 px-4 font-medium">浏览量</th>
                        <th className="text-left py-3 px-4 font-medium">点赞</th>
                        <th className="text-left py-3 px-4 font-medium">分享</th>
                        <th className="text-left py-3 px-4 font-medium">评论</th>
                        <th className="text-left py-3 px-4 font-medium">趋势</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredChannels
                        .filter(channel => channel.status === "connected")
                        .map((channel) => (
                        <tr key={channel.id} className="border-b">
                          <td className="py-3 px-4">
                            <div className="flex items-center gap-2">
                              <div className="w-6 h-6 bg-muted rounded-md flex items-center justify-center">
                                <img src={channel.icon} alt={channel.name} className="w-4 h-4" />
                              </div>
                              <span>{channel.name}</span>
                            </div>
                          </td>
                          <td className="py-3 px-4">{channel.followers}</td>
                          <td className="py-3 px-4">{channel.engagement}</td>
                          <td className="py-3 px-4">{channel.postFrequency}</td>
                          <td className="py-3 px-4">{channel.recentStats.views}</td>
                          <td className="py-3 px-4">{channel.recentStats.likes}</td>
                          <td className="py-3 px-4">{channel.recentStats.shares}</td>
                          <td className="py-3 px-4">{channel.recentStats.comments}</td>
                          <td className="py-3 px-4">
                            {channel.recentPerformance === "上升" && (
                              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                                <TrendingUp className="mr-1 h-3 w-3" />
                                上升
                              </Badge>
                            )}
                            {channel.recentPerformance === "稳定" && (
                              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                                稳定
                              </Badge>
                            )}
                            {channel.recentPerformance === "下降" && (
                              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                                下降
                              </Badge>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 活动管理 */}
        <TabsContent value="campaigns" className="space-y-4 mt-6">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">营销活动</h2>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              创建新活动
            </Button>
          </div>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>活动列表</CardTitle>
              <CardDescription>管理所有营销活动</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {marketingCampaigns.map((campaign) => (
                  <div key={campaign.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium">{campaign.name}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant={
                              campaign.status === "active" ? "default" :
                              campaign.status === "scheduled" ? "outline" : "secondary"
                            }
                          >
                            {campaign.status === "active" ? "进行中" :
                             campaign.status === "scheduled" ? "已排期" : "已结束"}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {campaign.startDate} 至 {campaign.endDate}
                          </span>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            编辑活动
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Trash className="mr-2 h-4 w-4" />
                            删除活动
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                      <div>
                        <p className="text-muted-foreground">发布渠道</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {campaign.channels.map((channel, index) => (
                            <Badge key={index} variant="outline" className="bg-muted">
                              {channel}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <p className="text-muted-foreground">预算/支出</p>
                        <p className="font-medium">{campaign.budget} / {campaign.spent}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">互动情况</p>
                        <p className="font-medium">{campaign.engagement}</p>
                      </div>
                      <div className={limitedFeatures ? "opacity-60" : ""}>
                        <p className="text-muted-foreground">ROI</p>
                        <p className="font-medium">
                          {limitedFeatures ? (
                            <Link href="/premium-services" className="text-primary underline">
                              升级查看
                            </Link>
                          ) : (
                            campaign.roi
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 内容库 */}
        <TabsContent value="content" className="space-y-4 mt-6">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">内容库</h2>
            <div className="flex items-center gap-2">
              <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "grid" | "list")}>
                <TabsList className="grid w-[120px] grid-cols-2">
                  <TabsTrigger value="list">
                    <List className="h-4 w-4" />
                  </TabsTrigger>
                  <TabsTrigger value="grid">
                    <LayoutGrid className="h-4 w-4" />
                  </TabsTrigger>
                </TabsList>
              </Tabs>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建内容
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2 max-w-md">
            <Input
              placeholder="搜索内容..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {viewMode === "list" ? (
            <Card>
              <CardContent className="p-0">
                <div className="divide-y">
                  {contentLibrary.map((content) => (
                    <div key={content.id} className="p-4 flex items-start gap-4">
                      <div className="w-[100px] h-[60px] bg-muted rounded-md flex-shrink-0 flex items-center justify-center">
                        {content.type === "video" && <Video className="h-6 w-6 text-muted-foreground" />}
                        {content.type === "article" && <FileText className="h-6 w-6 text-muted-foreground" />}
                        {content.type === "post" && <Image className="h-6 w-6 text-muted-foreground" />}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{content.title}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant={content.status === "published" ? "default" : "outline"}>
                                {content.status === "published" ? "已发布" : "草稿"}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                创建于 {content.created}
                              </span>
                            </div>
                          </div>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-3 gap-2 mt-2 text-sm">
                          <div>
                            <p className="text-muted-foreground">发布渠道</p>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {content.channels.length > 0 ? (
                                content.channels.map((channel, index) => (
                                  <Badge key={index} variant="outline" className="bg-muted">
                                    {channel}
                                  </Badge>
                                ))
                              ) : (
                                <span className="text-muted-foreground">未发布</span>
                              )}
                            </div>
                          </div>
                          <div>
                            <p className="text-muted-foreground">浏览/点赞</p>
                            <p className="font-medium">{content.views} / {content.likes}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">评论</p>
                            <p className="font-medium">{content.comments}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {contentLibrary.map((content) => (
                <Card key={content.id}>
                  <CardHeader className="pb-2">
                    <div className="aspect-video bg-muted rounded-md flex items-center justify-center mb-2">
                      {content.type === "video" && <Video className="h-8 w-8 text-muted-foreground" />}
                      {content.type === "article" && <FileText className="h-8 w-8 text-muted-foreground" />}
                      {content.type === "post" && <Image className="h-8 w-8 text-muted-foreground" />}
                    </div>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-base">{content.title}</CardTitle>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant={content.status === "published" ? "default" : "outline"}>
                        {content.status === "published" ? "已发布" : "草稿"}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        创建于 {content.created}
                      </span>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-2 text-sm">
                      <div>
                        <p className="text-muted-foreground">浏览</p>
                        <p className="font-medium">{content.views}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">点赞</p>
                        <p className="font-medium">{content.likes}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">评论</p>
                        <p className="font-medium">{content.comments}</p>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0">
                    <Button variant="outline" size="sm" className="w-full">
                      {content.status === "published" ? "管理内容" : "编辑草稿"}
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* 数据分析 */}
        <TabsContent value="analytics" className="space-y-4 mt-6">
          {limitedFeatures ? (
            <Card>
              <CardContent className="p-8 flex flex-col items-center justify-center text-center">
                <Lock className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium mb-2">高级数据分析功能</h3>
                <p className="text-muted-foreground mb-6 max-w-md">
                  数据分析是增值服务的高级功能，可以帮助您深入了解营销活动效果，优化营销策略，提高ROI。
                </p>
                <Button asChild>
                  <Link href="/premium-services">
                    升级到完整版
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <>
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium">营销数据分析</h2>
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  导出报告
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>渠道效果对比</CardTitle>
                    <CardDescription>各营销渠道的表现对比分析</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px] bg-muted rounded-md flex items-center justify-center">
                      <BarChart className="h-8 w-8 text-muted-foreground" />
                      <span className="ml-2 text-muted-foreground">渠道效果对比图表</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>ROI分析</CardTitle>
                    <CardDescription>营销活动投资回报率分析</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px] bg-muted rounded-md flex items-center justify-center">
                      <TrendingUp className="h-8 w-8 text-muted-foreground" />
                      <span className="ml-2 text-muted-foreground">ROI分析图表</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="md:col-span-2">
                  <CardHeader>
                    <CardTitle>营销漏斗分析</CardTitle>
                    <CardDescription>从曝光到转化的全流程分析</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px] bg-muted rounded-md flex items-center justify-center">
                      <svg className="h-8 w-8 text-muted-foreground" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 4H21V8H3V4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M5 8H19V12H5V8Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M7 12H17V16H7V12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M9 16H15V20H9V16Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <span className="ml-2 text-muted-foreground">营销漏斗分析图表</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>营销活动详细分析</CardTitle>
                  <CardDescription>各营销活动的详细效果数据</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4 font-medium">活动名称</th>
                          <th className="text-left py-3 px-4 font-medium">渠道</th>
                          <th className="text-left py-3 px-4 font-medium">曝光量</th>
                          <th className="text-left py-3 px-4 font-medium">点击量</th>
                          <th className="text-left py-3 px-4 font-medium">转化量</th>
                          <th className="text-left py-3 px-4 font-medium">转化率</th>
                          <th className="text-left py-3 px-4 font-medium">花费</th>
                          <th className="text-left py-3 px-4 font-medium">ROI</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-b">
                          <td className="py-3 px-4">夏季瑜伽课程促销</td>
                          <td className="py-3 px-4">微信公众号</td>
                          <td className="py-3 px-4">12,345</td>
                          <td className="py-3 px-4">1,234</td>
                          <td className="py-3 px-4">123</td>
                          <td className="py-3 px-4">9.97%</td>
                          <td className="py-3 px-4">¥500</td>
                          <td className="py-3 px-4">380%</td>
                        </tr>
                        <tr className="border-b">
                          <td className="py-3 px-4">夏季瑜伽课程促销</td>
                          <td className="py-3 px-4">抖音企业号</td>
                          <td className="py-3 px-4">45,678</td>
                          <td className="py-3 px-4">5,678</td>
                          <td className="py-3 px-4">345</td>
                          <td className="py-3 px-4">6.08%</td>
                          <td className="py-3 px-4">¥800</td>
                          <td className="py-3 px-4">290%</td>
                        </tr>
                        <tr>
                          <td className="py-3 px-4">夏季瑜伽课程促销</td>
                          <td className="py-3 px-4">小红书</td>
                          <td className="py-3 px-4">23,456</td>
                          <td className="py-3 px-4">3,456</td>
                          <td className="py-3 px-4">234</td>
                          <td className="py-3 px-4">6.77%</td>
                          <td className="py-3 px-4">¥600</td>
                          <td className="py-3 px-4">320%</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>
      </Tabs>

      {/* 渠道详情对话框 */}
      {showChannelDetails && selectedChannel && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-3xl max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
                    <img src={selectedChannel.icon} alt={selectedChannel.name} className="w-8 h-8" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold">{selectedChannel.name}</h2>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="default">已连接</Badge>
                      <span className="text-sm text-muted-foreground">{selectedChannel.accountType}</span>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="icon" onClick={() => setShowChannelDetails(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h3 className="font-medium mb-3">账号信息</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">认证状态</span>
                      <span>{selectedChannel.verificationStatus}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">连接时间</span>
                      <span>{selectedChannel.connectTime}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">粉丝数</span>
                      <span>{selectedChannel.followers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">互动率</span>
                      <span>{selectedChannel.engagement}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">发布频率</span>
                      <span>{selectedChannel.postFrequency}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium mb-3">近期数据</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <Card className="bg-muted/50">
                      <CardContent className="p-3">
                        <div className="text-xs text-muted-foreground">浏览量</div>
                        <div className="text-lg font-semibold">{selectedChannel.recentStats.views}</div>
                      </CardContent>
                    </Card>
                    <Card className="bg-muted/50">
                      <CardContent className="p-3">
                        <div className="text-xs text-muted-foreground">点赞</div>
                        <div className="text-lg font-semibold">{selectedChannel.recentStats.likes}</div>
                      </CardContent>
                    </Card>
                    <Card className="bg-muted/50">
                      <CardContent className="p-3">
                        <div className="text-xs text-muted-foreground">分享</div>
                        <div className="text-lg font-semibold">{selectedChannel.recentStats.shares}</div>
                      </CardContent>
                    </Card>
                    <Card className="bg-muted/50">
                      <CardContent className="p-3">
                        <div className="text-xs text-muted-foreground">评论</div>
                        <div className="text-lg font-semibold">{selectedChannel.recentStats.comments}</div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="font-medium mb-3">最近发布内容</h3>
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="w-16 h-16 bg-muted rounded-md flex-shrink-0 flex items-center justify-center">
                        <FileText className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <div>
                        <p className="font-medium">{selectedChannel.lastPostContent}</p>
                        <div className="flex items-center gap-3 mt-2 text-sm">
                          <span className="text-muted-foreground">发布时间: {selectedChannel.lastPost}</span>
                          <div className="flex items-center gap-1">
                            <Eye className="h-3 w-3 text-muted-foreground" />
                            <span>{selectedChannel.recentStats.views}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-3 w-3 text-muted-foreground" />
                            <span>{selectedChannel.recentStats.comments}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="mb-6">
                <h3 className="font-medium mb-3">渠道操作</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <Button variant="outline" className="h-auto py-3 flex flex-col items-center justify-center">
                    <Edit className="h-5 w-5 mb-1" />
                    <span>发布内容</span>
                  </Button>
                  <Button variant="outline" className="h-auto py-3 flex flex-col items-center justify-center">
                    <BarChart className="h-5 w-5 mb-1" />
                    <span>数据分析</span>
                  </Button>
                  <Button variant="outline" className="h-auto py-3 flex flex-col items-center justify-center">
                    <MessageSquare className="h-5 w-5 mb-1" />
                    <span>查看评论</span>
                  </Button>
                  <Button variant="outline" className="h-auto py-3 flex flex-col items-center justify-center">
                    <Settings className="h-5 w-5 mb-1" />
                    <span>渠道设置</span>
                  </Button>
                </div>
              </div>

              <div className="flex justify-end gap-2 mt-6">
                <Button variant="outline" onClick={() => setShowChannelDetails(false)}>
                  关闭
                </Button>
                <Button variant="destructive">
                  <Trash className="mr-2 h-4 w-4" />
                  断开连接
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 添加新渠道对话框 */}
      {showAddChannelDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">添加新渠道</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAddChannelDialog(false)}
                  disabled={channelConnectLoading}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* 步骤指示器 */}
              <div className="flex items-center mb-6">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  channelConnectStep >= 1 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                }`}>
                  1
                </div>
                <div className={`flex-1 h-1 mx-2 ${
                  channelConnectStep >= 2 ? "bg-primary" : "bg-muted"
                }`}></div>
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  channelConnectStep >= 2 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                }`}>
                  2
                </div>
                <div className={`flex-1 h-1 mx-2 ${
                  channelConnectStep >= 3 ? "bg-primary" : "bg-muted"
                }`}></div>
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  channelConnectStep >= 3 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                }`}>
                  3
                </div>
              </div>

              {/* 步骤1: 选择渠道类型 */}
              {channelConnectStep === 1 && (
                <div>
                  <h3 className="font-medium mb-3">选择要连接的渠道类型</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
                    {availableChannelTypes.map((channelType) => (
                      <Card
                        key={channelType.id}
                        className={`cursor-pointer hover:border-primary transition-colors ${
                          selectedChannelType === channelType.id ? "border-primary bg-primary/5" : ""
                        }`}
                        onClick={() => handleSelectChannelType(channelType.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-muted rounded-md flex items-center justify-center">
                              <img src={channelType.icon} alt={channelType.name} className="w-6 h-6" />
                            </div>
                            <div>
                              <h4 className="font-medium">{channelType.name}</h4>
                              <p className="text-sm text-muted-foreground">{channelType.description}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setShowAddChannelDialog(false)}>
                      取消
                    </Button>
                    <Button
                      onClick={() => setChannelConnectStep(2)}
                      disabled={!selectedChannelType}
                    >
                      下一步
                    </Button>
                  </div>
                </div>
              )}

              {/* 步骤2: 授权连接 */}
              {channelConnectStep === 2 && (
                <div>
                  <h3 className="font-medium mb-3">授权连接渠道</h3>

                  <div className="bg-muted p-4 rounded-md mb-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-10 h-10 bg-white rounded-md flex items-center justify-center">
                        <img
                          src={availableChannelTypes.find(c => c.id === selectedChannelType)?.icon}
                          alt={availableChannelTypes.find(c => c.id === selectedChannelType)?.name}
                          className="w-6 h-6"
                        />
                      </div>
                      <div>
                        <h4 className="font-medium">{availableChannelTypes.find(c => c.id === selectedChannelType)?.name}</h4>
                        <p className="text-sm text-muted-foreground">准备连接此渠道</p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="account-name" className="block mb-2">账号名称</Label>
                        <Input id="account-name" placeholder="输入您的账号名称" />
                      </div>

                      <div>
                        <Label htmlFor="account-type" className="block mb-2">账号类型</Label>
                        <select
                          id="account-type"
                          className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                        >
                          <option value="">选择账号类型</option>
                          <option value="service">服务号</option>
                          <option value="subscription">订阅号</option>
                          <option value="enterprise">企业号</option>
                          <option value="personal">个人号</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-md border border-blue-100 mb-6">
                    <div className="flex items-start gap-2">
                      <div className="text-blue-500 mt-0.5">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"></circle>
                          <path d="M12 16v-4"></path>
                          <path d="M12 8h.01"></path>
                        </svg>
                      </div>
                      <div className="text-sm text-blue-700">
                        <p className="font-medium">授权提示</p>
                        <p>点击"授权连接"按钮后，将跳转到对应平台的授权页面。请按照提示完成授权流程，授权成功后将自动返回。</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setChannelConnectStep(1)}
                      disabled={channelConnectLoading}
                    >
                      上一步
                    </Button>
                    <Button
                      onClick={handleConnectChannel}
                      disabled={channelConnectLoading}
                    >
                      {channelConnectLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          授权中...
                        </>
                      ) : (
                        "授权连接"
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {/* 步骤3: 连接成功 */}
              {channelConnectStep === 3 && channelConnectSuccess && (
                <div className="text-center py-6">
                  <div className="w-16 h-16 bg-green-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Check className="h-8 w-8 text-green-500" />
                  </div>
                  <h3 className="text-xl font-medium mb-2">连接成功</h3>
                  <p className="text-muted-foreground mb-6">
                    已成功连接{availableChannelTypes.find(c => c.id === selectedChannelType)?.name}，
                    现在您可以开始管理此渠道并发布内容了。
                  </p>
                  <Button onClick={() => setShowAddChannelDialog(false)}>
                    开始使用
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// 添加一个锁图标组件用于限制功能
function Lock(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
      <path d="M7 11V7a5 5 0 0 1 10 0v4" />
    </svg>
  )
}
