"use client"

import { use<PERSON>tate, use<PERSON><PERSON><PERSON>, use<PERSON>em<PERSON> } from "react"
import { use<PERSON>outer } from "next/navigation"
import { memberCards, addMemberCards } from "@/services/member-card-data"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Pagin<PERSON>, <PERSON><PERSON>ation<PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ation<PERSON><PERSON>, Pa<PERSON>ationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useToast } from "@/hooks/use-toast"
import { MemberCardInstanceDetailDialog } from "@/components/members/cards/member-card-instance-detail-dialog"
import { AddMemberCardInstanceDialog } from "@/components/members/cards/add-member-card-instance-dialog"
import { EditMemberCardInstanceDialog } from "@/components/members/cards/edit-member-card-instance-dialog"
import { UpgradeMemberCardDialog } from "@/components/members/cards/upgrade-member-card-dialog"
import { MergeMemberCardDialog } from "@/components/members/cards/merge-member-card-dialog"
import {
  ArrowLeft,
  CreditCard,
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Edit,
  Trash,
  Eye,
  Calendar,
  Clock,
  User,
  Tag,
  RefreshCw,
  Download,

  LayoutGrid,
  LayoutList,
  FileText,
  AlertCircle,
  CheckCircle2,

  ChevronDown,
  Printer,
  Share2,
  RotateCcw,
  CalendarRange,
  Wallet,
  Sparkles,
  PlayCircle,

  TrendingUp,
  Merge,
  X
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

// 使用共享数据服务中的会员卡数据
// 添加更多会员卡数据到共享服务
const additionalCards = [
  {
    id: "MC001",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "瑜伽年卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    status: "active",
    remainingDays: 120,
    remainingCount: null,
    remainingValue: null,
    price: 3600,
    actualPrice: 3200,
    paymentMethod: "微信支付",
    activationDate: "2023-01-01",
    lastUsedDate: "2023-09-15",
    usageCount: 45,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "老客户"],
    notes: "会员续卡，享受8.9折优惠",
    createdAt: "2022-12-25",
    createdBy: "管理员",
  },
  {
    id: "MC009",
    memberName: "刘一",
    memberPhone: "13800138009",
    memberId: "M009",
    cardType: "瑜伽年卡+私教次卡",
    cardTypeBadge: "组合卡",
    cardTypeColor: "#673AB7",
    startDate: "2023-07-01",
    endDate: "2024-06-30",
    status: "active",
    remainingDays: 300,
    remainingCount: 15,
    remainingValue: null,
    price: 5000,
    actualPrice: 4500,
    paymentMethod: "微信支付",
    activationDate: "2023-07-01",
    lastUsedDate: "2023-09-05",
    usageCount: 12,
    isTrialCard: false,
    isAutoRenew: true,
    tags: ["VIP", "高消费"],
    notes: "组合卡优惠套餐",
    createdAt: "2023-06-28",
    createdBy: "销售",
  },
  {
    id: "MC010",
    memberName: "陈二",
    memberPhone: "13800138010",
    memberId: "M010",
    cardType: "瑜伽体验卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#E91E63",
    startDate: "2023-09-01",
    endDate: "2023-09-15",
    status: "onLeave",
    remainingDays: 5,
    remainingCount: null,
    remainingValue: null,
    price: 199,
    actualPrice: 99,
    paymentMethod: "支付宝",
    activationDate: "2023-09-01",
    lastUsedDate: "2023-09-05",
    usageCount: 2,
    isTrialCard: true,
    isAutoRenew: false,
    tags: ["新客户", "潜在客户"],
    notes: "会员因病请假5天",
    createdAt: "2023-08-30",
    createdBy: "前台",
  },
  {
    id: "MC011",
    memberName: "张四",
    memberPhone: "13800138011",
    memberId: "M011",
    cardType: "瑜伽年卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-01-15",
    endDate: "2024-01-14",
    status: "refunded",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 3600,
    actualPrice: 3600,
    paymentMethod: "微信支付",
    activationDate: "2023-01-15",
    lastUsedDate: "2023-03-20",
    usageCount: 15,
    isTrialCard: false,
    isAutoRenew: false,
    tags: [],
    notes: "会员搬家，申请退卡",
    createdAt: "2023-01-10",
    createdBy: "销售",
  },
  {
    id: "MC012",
    memberName: "王小明",
    memberPhone: "13800138012",
    memberId: "M012",
    cardType: "私教次卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-08-01",
    endDate: "2024-01-31",
    status: "active",
    remainingDays: 150,
    remainingCount: 18,
    remainingValue: null,
    price: 2400,
    actualPrice: 2200,
    paymentMethod: "微信支付",
    activationDate: "2023-08-01",
    lastUsedDate: "2023-08-15",
    usageCount: 2,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["学生"],
    notes: "学生特惠价",
    createdAt: "2023-07-30",
    createdBy: "销售",
  },
  {
    id: "MC013",
    memberName: "李小红",
    memberPhone: "13800138013",
    memberId: "M013",
    cardType: "高级会员储值卡",
    cardTypeBadge: "储值卡",
    cardTypeColor: "#FF5722",
    startDate: "2023-05-15",
    endDate: null,
    status: "active",
    remainingDays: null,
    remainingCount: null,
    remainingValue: 8000,
    price: 10000,
    actualPrice: 10000,
    paymentMethod: "银行转账",
    activationDate: "2023-05-15",
    lastUsedDate: "2023-09-10",
    usageCount: 12,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "高消费"],
    notes: "充值10000元，赠送2000元",
    createdAt: "2023-05-15",
    createdBy: "管理员",
  },
  {
    id: "MC014",
    memberName: "赵小刚",
    memberPhone: "13800138014",
    memberId: "M014",
    cardType: "瑜伽季卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#2196F3",
    startDate: "2023-07-01",
    endDate: "2023-09-30",
    status: "expired",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 1200,
    actualPrice: 1000,
    paymentMethod: "支付宝",
    activationDate: "2023-07-01",
    lastUsedDate: "2023-09-28",
    usageCount: 25,
    isTrialCard: false,
    isAutoRenew: false,
    tags: [],
    notes: "季卡到期，待续费",
    createdAt: "2023-06-28",
    createdBy: "前台",
  },
  {
    id: "MC015",
    memberName: "孙小梅",
    memberPhone: "13800138015",
    memberId: "M015",
    cardType: "亲子瑜伽卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#8BC34A",
    startDate: "2023-08-15",
    endDate: "2024-02-14",
    status: "active",
    remainingDays: 170,
    remainingCount: 8,
    remainingValue: null,
    price: 1600,
    actualPrice: 1600,
    paymentMethod: "微信支付",
    activationDate: "2023-08-15",
    lastUsedDate: "2023-09-01",
    usageCount: 2,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["亲子"],
    notes: "一大一小亲子卡",
    createdAt: "2023-08-15",
    createdBy: "前台",
  },
  {
    id: "MC016",
    memberName: "周小华",
    memberPhone: "13800138016",
    memberId: "M016",
    cardType: "瑜伽月卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#FF9800",
    startDate: "2023-09-01",
    endDate: "2023-09-30",
    status: "active",
    remainingDays: 20,
    remainingCount: null,
    remainingValue: null,
    price: 450,
    actualPrice: 450,
    paymentMethod: "微信支付",
    activationDate: "2023-09-01",
    lastUsedDate: "2023-09-10",
    usageCount: 5,
    isTrialCard: false,
    isAutoRenew: true,
    tags: ["新客户"],
    notes: "首次购买月卡",
    createdAt: "2023-08-31",
    createdBy: "前台",
  },
  {
    id: "MC017",
    memberName: "吴小兰",
    memberPhone: "13800138017",
    memberId: "M017",
    cardType: "高温瑜伽卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#F44336",
    startDate: "2023-06-01",
    endDate: "2023-08-31",
    status: "expired",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 1500,
    actualPrice: 1200,
    paymentMethod: "支付宝",
    activationDate: "2023-06-01",
    lastUsedDate: "2023-08-30",
    usageCount: 30,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["夏季限定"],
    notes: "夏季高温瑜伽特惠卡",
    createdAt: "2023-05-28",
    createdBy: "销售",
  },
  {
    id: "MC002",
    memberName: "李四",
    memberPhone: "13800138002",
    memberId: "M002",
    cardType: "瑜伽季卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#2196F3",
    startDate: "2023-03-01",
    endDate: "2023-05-31",
    status: "expired",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 1200,
    actualPrice: 1200,
    paymentMethod: "现金",
    activationDate: "2023-03-01",
    lastUsedDate: "2023-05-30",
    usageCount: 28,
    isTrialCard: false,
    isAutoRenew: false,
    tags: [],
    notes: "",
    createdAt: "2023-02-28",
    createdBy: "前台",
  },
  {
    id: "MC003",
    memberName: "王五",
    memberPhone: "13800138003",
    memberId: "M003",
    cardType: "私教次卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-02-15",
    endDate: "2023-08-15",
    status: "active",
    remainingDays: 75,
    remainingCount: 8,
    remainingValue: null,
    price: 2000,
    actualPrice: 1800,
    paymentMethod: "支付宝",
    activationDate: "2023-02-15",
    lastUsedDate: "2023-07-10",
    usageCount: 12,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["新客户"],
    notes: "新会员首次购卡，赠送2次私教课",
    createdAt: "2023-02-15",
    createdBy: "销售",
  },
  {
    id: "MC004",
    memberName: "赵六",
    memberPhone: "13800138004",
    memberId: "M004",
    cardType: "储值卡",
    cardTypeBadge: "储值卡",
    cardTypeColor: "#607D8B",
    startDate: "2023-01-10",
    endDate: null,
    status: "active",
    remainingDays: null,
    remainingCount: null,
    remainingValue: 2000,
    price: 3000,
    actualPrice: 3000,
    paymentMethod: "微信支付",
    activationDate: "2023-01-10",
    lastUsedDate: "2023-09-01",
    usageCount: 8,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP"],
    notes: "充值3000元",
    createdAt: "2023-01-10",
    createdBy: "管理员",
  },
  {
    id: "MC005",
    memberName: "钱七",
    memberPhone: "13800138005",
    memberId: "M005",
    cardType: "体验卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#F44336",
    startDate: "2023-04-01",
    endDate: "2023-04-30",
    status: "expired",
    remainingDays: 0,
    remainingCount: 0,
    remainingValue: null,
    price: 199,
    actualPrice: 99,
    paymentMethod: "微信支付",
    activationDate: "2023-04-01",
    lastUsedDate: "2023-04-25",
    usageCount: 3,
    isTrialCard: true,
    isAutoRenew: false,
    tags: ["潜在客户"],
    notes: "体验卡活动价",
    createdAt: "2023-04-01",
    createdBy: "前台",
  },
  {
    id: "MC006",
    memberName: "孙八",
    memberPhone: "13800138006",
    memberId: "M006",
    cardType: "瑜伽月卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#FF9800",
    startDate: "2023-04-15",
    endDate: "2023-05-14",
    status: "frozen",
    remainingDays: 15,
    remainingCount: null,
    remainingValue: null,
    price: 450,
    actualPrice: 450,
    paymentMethod: "支付宝",
    activationDate: "2023-04-15",
    lastUsedDate: "2023-04-20",
    usageCount: 3,
    isTrialCard: false,
    isAutoRenew: true,
    tags: [],
    notes: "会员请假，临时冻结",
    createdAt: "2023-04-15",
    createdBy: "前台",
  },
  {
    id: "MC007",
    memberName: "周九",
    memberPhone: "13800138007",
    memberId: "M007",
    cardType: "瑜伽年卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-05-01",
    endDate: "2024-04-30",
    status: "active",
    remainingDays: 240,
    remainingCount: null,
    remainingValue: null,
    price: 3600,
    actualPrice: 3400,
    paymentMethod: "微信支付",
    activationDate: "2023-05-01",
    lastUsedDate: "2023-09-10",
    usageCount: 35,
    isTrialCard: false,
    isAutoRenew: true,
    tags: ["VIP"],
    notes: "",
    createdAt: "2023-04-28",
    createdBy: "销售",
  },
  {
    id: "MC008",
    memberName: "吴十",
    memberPhone: "13800138008",
    memberId: "M008",
    cardType: "私教次卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-06-01",
    endDate: "2023-12-01",
    status: "inactive",
    remainingDays: 90,
    remainingCount: 20,
    remainingValue: null,
    price: 2000,
    actualPrice: 2000,
    paymentMethod: "微信支付",
    activationDate: "",
    lastUsedDate: "",
    usageCount: 0,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["新客户"],
    notes: "等待会员首次使用激活",
    createdAt: "2023-06-01",
    createdBy: "销售",
  },
  {
    id: "MC018",
    memberName: "郑小芳",
    memberPhone: "13800138018",
    memberId: "M018",
    cardType: "孕妇瑜伽卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-08-01",
    endDate: "2024-01-31",
    status: "active",
    remainingDays: 150,
    remainingCount: 12,
    remainingValue: null,
    price: 1800,
    actualPrice: 1800,
    paymentMethod: "微信支付",
    activationDate: "2023-08-01",
    lastUsedDate: "2023-09-12",
    usageCount: 8,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["孕妇", "特殊人群"],
    notes: "孕期专属课程",
    createdAt: "2023-07-30",
    createdBy: "前台",
  },
  {
    id: "MC019",
    memberName: "马小云",
    memberPhone: "13800138019",
    memberId: "M019",
    cardType: "瑜伽年卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-02-01",
    endDate: "2024-01-31",
    status: "frozen",
    remainingDays: 150,
    remainingCount: null,
    remainingValue: null,
    price: 3600,
    actualPrice: 3400,
    paymentMethod: "支付宝",
    activationDate: "2023-02-01",
    lastUsedDate: "2023-07-15",
    usageCount: 40,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP"],
    notes: "会员出国，临时冻结3个月",
    createdAt: "2023-01-28",
    createdBy: "销售",
  },
  {
    id: "MC020",
    memberName: "林小燕",
    memberPhone: "13800138020",
    memberId: "M020",
    cardType: "瑜伽+普拉提联合卡",
    cardTypeBadge: "组合卡",
    cardTypeColor: "#673AB7",
    startDate: "2023-03-15",
    endDate: "2024-03-14",
    status: "active",
    remainingDays: 200,
    remainingCount: null,
    remainingValue: null,
    price: 4800,
    actualPrice: 4500,
    paymentMethod: "微信支付",
    activationDate: "2023-03-15",
    lastUsedDate: "2023-09-10",
    usageCount: 50,
    isTrialCard: false,
    isAutoRenew: true,
    tags: ["VIP", "高频用户"],
    notes: "联合卡优惠套餐",
    createdAt: "2023-03-10",
    createdBy: "销售",
  },
  {
    id: "MC021",
    memberName: "王小花",
    memberPhone: "13800138021",
    memberId: "M021",
    cardType: "瑜伽体验卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#E91E63",
    startDate: "2023-09-10",
    endDate: "2023-09-24",
    status: "active",
    remainingDays: 14,
    remainingCount: null,
    remainingValue: null,
    price: 199,
    actualPrice: 99,
    paymentMethod: "微信支付",
    activationDate: "2023-09-10",
    lastUsedDate: "",
    usageCount: 0,
    isTrialCard: true,
    isAutoRenew: false,
    tags: ["新客户", "潜在客户"],
    notes: "朋友推荐，体验价",
    createdAt: "2023-09-10",
    createdBy: "前台",
  },
  {
    id: "MC022",
    memberName: "张小丽",
    memberPhone: "13800138022",
    memberId: "M022",
    cardType: "瑜伽季卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#2196F3",
    startDate: "2023-04-01",
    endDate: "2023-06-30",
    status: "refunded",
    remainingDays: 0,
    remainingCount: null,
    remainingValue: null,
    price: 1200,
    actualPrice: 1200,
    paymentMethod: "微信支付",
    activationDate: "2023-04-01",
    lastUsedDate: "2023-05-15",
    usageCount: 10,
    isTrialCard: false,
    isAutoRenew: false,
    tags: [],
    notes: "会员健康原因申请退卡",
    createdAt: "2023-03-30",
    createdBy: "前台",
  },
  {
    id: "MC023",
    memberName: "李小军",
    memberPhone: "13800138023",
    memberId: "M023",
    cardType: "私教VIP卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-07-15",
    endDate: "2024-07-14",
    status: "active",
    remainingDays: 300,
    remainingCount: 45,
    remainingValue: null,
    price: 9000,
    actualPrice: 8500,
    paymentMethod: "银行转账",
    activationDate: "2023-07-15",
    lastUsedDate: "2023-09-05",
    usageCount: 5,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "高消费"],
    notes: "一年50次私教特惠套餐",
    createdAt: "2023-07-10",
    createdBy: "销售",
  },
  {
    id: "MC024",
    memberName: "赵小琳",
    memberPhone: "13800138024",
    memberId: "M024",
    cardType: "瑜伽无限卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#4CAF50",
    startDate: "2023-01-01",
    endDate: null,
    status: "active",
    remainingDays: null,
    remainingCount: null,
    remainingValue: null,
    price: 10000,
    actualPrice: 9800,
    paymentMethod: "微信支付",
    activationDate: "2023-01-01",
    lastUsedDate: "2023-09-15",
    usageCount: 180,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "终身会员"],
    notes: "终身无限次瑜伽课程",
    createdAt: "2022-12-28",
    createdBy: "管理员",
  },
  {
    id: "MC025",
    memberName: "钱小英",
    memberPhone: "13800138025",
    memberId: "M025",
    cardType: "瑜伽月卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#FF9800",
    startDate: "2023-09-01",
    endDate: "2023-09-30",
    status: "onLeave",
    remainingDays: 15,
    remainingCount: null,
    remainingValue: null,
    price: 450,
    actualPrice: 450,
    paymentMethod: "支付宝",
    activationDate: "2023-09-01",
    lastUsedDate: "2023-09-05",
    usageCount: 3,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["新客户"],
    notes: "会员出差，请假10天",
    createdAt: "2023-08-30",
    createdBy: "前台",
  },
  {
    id: "MC026",
    memberName: "孙小强",
    memberPhone: "13800138026",
    memberId: "M026",
    cardType: "健身+瑜伽卡",
    cardTypeBadge: "组合卡",
    cardTypeColor: "#673AB7",
    startDate: "2023-08-01",
    endDate: "2024-07-31",
    status: "active",
    remainingDays: 330,
    remainingCount: null,
    remainingValue: null,
    price: 5800,
    actualPrice: 5500,
    paymentMethod: "微信支付",
    activationDate: "2023-08-01",
    lastUsedDate: "2023-09-12",
    usageCount: 20,
    isTrialCard: false,
    isAutoRenew: true,
    tags: ["VIP", "跨店"],
    notes: "可在集团所有门店使用",
    createdAt: "2023-07-28",
    createdBy: "销售",
  },
  {
    id: "MC027",
    memberName: "周小强",
    memberPhone: "13800138027",
    memberId: "M027",
    cardType: "瑜伽体验卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#E91E63",
    startDate: "2023-09-05",
    endDate: "2023-09-19",
    status: "inactive",
    remainingDays: 14,
    remainingCount: 3,
    remainingValue: null,
    price: 199,
    actualPrice: 99,
    paymentMethod: "微信支付",
    activationDate: "",
    lastUsedDate: "",
    usageCount: 0,
    isTrialCard: true,
    isAutoRenew: false,
    tags: ["新客户", "潜在客户"],
    notes: "线上活动获取，待激活",
    createdAt: "2023-09-05",
    createdBy: "线上系统",
  },
  // 为张三添加更多卡片，方便测试合并功能
  {
    id: "MC028",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "私教次卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#9C27B0",
    startDate: "2023-08-01",
    endDate: "2024-02-01",
    status: "active",
    remainingDays: 120,
    remainingCount: 15,
    remainingValue: null,
    price: 2400,
    actualPrice: 2200,
    paymentMethod: "微信支付",
    activationDate: "2023-08-01",
    lastUsedDate: "2023-09-20",
    usageCount: 5,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "私教"],
    notes: "私教课程专用卡",
    createdAt: "2023-07-30",
    createdBy: "销售",
  },
  {
    id: "MC029",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "储值卡",
    cardTypeBadge: "储值卡",
    cardTypeColor: "#8B5CF6",
    startDate: "2023-06-15",
    endDate: "2024-06-14",
    status: "active",
    remainingDays: 280,
    remainingCount: null,
    remainingValue: 800,
    price: 1500,
    actualPrice: 1500,
    paymentMethod: "支付宝",
    activationDate: "2023-06-15",
    lastUsedDate: "2023-09-18",
    usageCount: 12,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "储值"],
    notes: "充值优惠活动获得",
    createdAt: "2023-06-15",
    createdBy: "前台",
  },
  {
    id: "MC030",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "瑜伽季卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#2196F3",
    startDate: "2023-07-01",
    endDate: "2023-09-30",
    status: "active",
    remainingDays: 15,
    remainingCount: null,
    remainingValue: null,
    price: 1200,
    actualPrice: 1000,
    paymentMethod: "现金",
    activationDate: "2023-07-01",
    lastUsedDate: "2023-09-10",
    usageCount: 25,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "季卡"],
    notes: "夏季促销活动购买",
    createdAt: "2023-06-28",
    createdBy: "销售",
  },
  {
    id: "MC031",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "团课次卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#FF9800",
    startDate: "2023-09-01",
    endDate: "2024-03-01",
    status: "active",
    remainingDays: 180,
    remainingCount: 8,
    remainingValue: null,
    price: 800,
    actualPrice: 720,
    paymentMethod: "微信支付",
    activationDate: "2023-09-01",
    lastUsedDate: "2023-09-15",
    usageCount: 2,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "团课"],
    notes: "团课专用次卡",
    createdAt: "2023-08-30",
    createdBy: "前台",
  },
  {
    id: "MC032",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "普拉提次卡",
    cardTypeBadge: "次卡",
    cardTypeColor: "#E91E63",
    startDate: "2023-05-15",
    endDate: "2023-11-15",
    status: "active",
    remainingDays: 60,
    remainingCount: 12,
    remainingValue: null,
    price: 1600,
    actualPrice: 1400,
    paymentMethod: "银行卡",
    activationDate: "2023-05-15",
    lastUsedDate: "2023-09-08",
    usageCount: 8,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "普拉提"],
    notes: "普拉提课程专用",
    createdAt: "2023-05-12",
    createdBy: "销售",
  },
  {
    id: "MC033",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "储值卡B",
    cardTypeBadge: "储值卡",
    cardTypeColor: "#673AB7",
    startDate: "2023-04-01",
    endDate: "2024-03-31",
    status: "active",
    remainingDays: 200,
    remainingCount: null,
    remainingValue: 450,
    price: 1000,
    actualPrice: 1000,
    paymentMethod: "微信支付",
    activationDate: "2023-04-01",
    lastUsedDate: "2023-09-12",
    usageCount: 18,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "储值"],
    notes: "生日优惠充值",
    createdAt: "2023-03-30",
    createdBy: "前台",
  },
  // 添加一张未激活的卡片给张三，用于测试激活功能
  {
    id: "MC034",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "瑜伽新手卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#795548",
    startDate: "2023-09-20",
    endDate: "2023-12-20",
    status: "inactive",
    remainingDays: 90,
    remainingCount: null,
    remainingValue: null,
    price: 800,
    actualPrice: 680,
    paymentMethod: "微信支付",
    activationDate: "",
    lastUsedDate: "",
    usageCount: 0,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "新手"],
    notes: "购买后等待首次使用激活",
    createdAt: "2023-09-20",
    createdBy: "销售",
  },
  // 添加一张请假状态的卡片给张三，用于测试销假功能
  {
    id: "MC035",
    memberName: "张三",
    memberPhone: "13800138001",
    memberId: "M001",
    cardType: "瑜伽半年卡",
    cardTypeBadge: "期限卡",
    cardTypeColor: "#3F51B5",
    startDate: "2023-06-01",
    endDate: "2023-12-01",
    status: "onLeave",
    remainingDays: 120,
    remainingCount: null,
    remainingValue: null,
    price: 1800,
    actualPrice: 1600,
    paymentMethod: "支付宝",
    activationDate: "2023-06-01",
    lastUsedDate: "2023-08-15",
    usageCount: 20,
    isTrialCard: false,
    isAutoRenew: false,
    tags: ["VIP", "请假中"],
    notes: "会员出差请假，暂停计时",
    createdAt: "2023-05-30",
    createdBy: "前台",
  },
]

// 获取状态标签
const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge className="bg-green-500">有效</Badge>
    case "expired":
      return <Badge variant="outline" className="text-gray-500">已过期</Badge>
    case "frozen":
      return <Badge className="bg-blue-500">已冻结</Badge>
    case "inactive":
      return <Badge variant="outline" className="text-yellow-500">未激活</Badge>
    case "onLeave":
      return <Badge className="bg-purple-500">请假中</Badge>
    case "refunded":
      return <Badge className="bg-red-500">已退卡</Badge>
    case "有效":
      return <Badge className="bg-green-500">有效</Badge>
    case "已过期":
      return <Badge variant="outline" className="text-gray-500">已过期</Badge>
    case "冻结":
    case "已冻结":
      return <Badge className="bg-blue-500">已冻结</Badge>
    case "未激活":
      return <Badge variant="outline" className="text-yellow-500">未激活</Badge>
    case "请假中":
      return <Badge className="bg-purple-500">请假中</Badge>
    case "已退卡":
      return <Badge className="bg-red-500">已退卡</Badge>
    default:
      return <Badge variant="outline">未知</Badge>
  }
}

// 定义对话框类型
type DialogType =
  | "freeze"
  | "unfreeze"
  | "delete"
  | "extend"
  | "transfer"
  | "renew"
  | "export"
  | "batchAction"
  | "advancedSearch"
  | "leave"
  | "endLeave"
  | "refund"
  | "activate"
  | "recharge"
  | "adjustCount"
  | "consumptionRecords"
  | "bookingRecords"
  | "sendMessage"
  | "print"
  | "qrcode"
  | "upgrade"
  | "merge"
  | "autoRenew"
  | "cancelAutoRenew"
  | null;

// 定义视图模式类型
type ViewMode = "list" | "grid";

// 定义排序字段类型
type SortField =
  | "id"
  | "memberName"
  | "cardType"
  | "startDate"
  | "endDate"
  | "status"
  | "remainingDays"
  | "remainingCount"
  | "remainingValue"
  | "createdAt";

// 定义排序方向类型
type SortDirection = "asc" | "desc";

export default function MemberCardListPage() {
  const router = useRouter()
  const { toast } = useToast()

  // 基本状态
  const [searchTerm, setSearchTerm] = useState("")
  const [cardTypeFilter, setCardTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [viewMode, setViewMode] = useState<ViewMode>("list")
  const [selectedCards, setSelectedCards] = useState<string[]>([])
  const [selectAll, setSelectAll] = useState(false)
  const [sortField, setSortField] = useState<SortField>("id")
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc")

  // 在组件初始化时将列表中的会员卡数据添加到共享服务中
  useEffect(() => {
    // 将列表中的会员卡数据添加到共享服务中
    addMemberCards(additionalCards);
  }, [])

  // 高级筛选状态
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [trialCardFilter, setTrialCardFilter] = useState<boolean | null>(null)
  const [autoRenewFilter, setAutoRenewFilter] = useState<boolean | null>(null)
  const [dateRangeFilter, setDateRangeFilter] = useState<"all" | "startDate" | "endDate" | "createdAt">("all")
  const [startDateFrom, setStartDateFrom] = useState("")
  const [startDateTo, setStartDateTo] = useState("")
  const [tagFilter, setTagFilter] = useState<string[]>([])

  // 对话框状态
  const [dialogType, setDialogType] = useState<DialogType>(null)
  const [selectedCard, setSelectedCard] = useState<any>(null)
  const [dialogNote, setDialogNote] = useState("")
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedCardId, setSelectedCardId] = useState("")
  const [addCardDialogOpen, setAddCardDialogOpen] = useState(false)
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false)
  const [mergeDialogOpen, setMergeDialogOpen] = useState(false)

  // 高级搜索状态
  const [advancedSearchFields, setAdvancedSearchFields] = useState({
    memberName: "",
    memberPhone: "",
    cardId: "",
    cardType: "",
    notes: "",
  })

  // 处理选择所有卡片
  useEffect(() => {
    if (selectAll) {
      setSelectedCards(filteredCards.map(card => card.id))
    } else if (selectedCards.length === filteredCards.length) {
      setSelectedCards([])
    }
  }, [selectAll])

  // 监听筛选条件变化，重置页码
  useEffect(() => {
    setCurrentPage(1)
  }, [searchTerm, cardTypeFilter, statusFilter, trialCardFilter, autoRenewFilter, dateRangeFilter, tagFilter])

  // 获取所有可用的标签
  const allTags = useMemo(() => {
    const tags = new Set<string>()
    memberCards.forEach(card => {
      card.tags.forEach(tag => tags.add(tag))
    })
    return Array.from(tags)
  }, [])

  // 过滤会员卡
  const filteredCards = useMemo(() => {
    return memberCards.filter(card => {
      // 基本搜索条件
      const matchesSearch = searchTerm ?
        card.memberName.includes(searchTerm) ||
        card.memberPhone.includes(searchTerm) ||
        card.id.includes(searchTerm) ||
        card.cardType.includes(searchTerm) : true

      // 高级搜索条件
      const matchesAdvancedSearch = !showAdvancedFilters || (
        (!advancedSearchFields.memberName || card.memberName.includes(advancedSearchFields.memberName)) &&
        (!advancedSearchFields.memberPhone || card.memberPhone.includes(advancedSearchFields.memberPhone)) &&
        (!advancedSearchFields.cardId || card.id.includes(advancedSearchFields.cardId)) &&
        (!advancedSearchFields.cardType || card.cardType.includes(advancedSearchFields.cardType)) &&
        (!advancedSearchFields.notes || card.notes.includes(advancedSearchFields.notes))
      )

      // 卡类型过滤
      const matchesCardType = cardTypeFilter === "all" || card.cardTypeBadge === cardTypeFilter

      // 状态过滤
      const matchesStatus = statusFilter === "all" || card.status === statusFilter

      // 体验卡过滤
      const matchesTrialCard = trialCardFilter === null || card.isTrialCard === trialCardFilter

      // 自动续费过滤
      const matchesAutoRenew = autoRenewFilter === null || card.isAutoRenew === autoRenewFilter

      // 日期范围过滤
      const matchesDateRange = dateRangeFilter === "all" || (
        dateRangeFilter === "startDate" ?
          (!startDateFrom || card.startDate >= startDateFrom) &&
          (!startDateTo || card.startDate <= startDateTo) :
        dateRangeFilter === "endDate" ?
          (!startDateFrom || (card.endDate && card.endDate >= startDateFrom)) &&
          (!startDateTo || (card.endDate && card.endDate <= startDateTo)) :
        dateRangeFilter === "createdAt" ?
          (!startDateFrom || card.createdAt >= startDateFrom) &&
          (!startDateTo || card.createdAt <= startDateTo) :
        true
      )

      // 标签过滤
      const matchesTags = tagFilter.length === 0 ||
        tagFilter.some(tag => card.tags.includes(tag))

      return matchesSearch && matchesAdvancedSearch && matchesCardType &&
        matchesStatus && matchesTrialCard && matchesAutoRenew &&
        matchesDateRange && matchesTags
    })
  }, [
    searchTerm,
    cardTypeFilter,
    statusFilter,
    trialCardFilter,
    autoRenewFilter,
    dateRangeFilter,
    startDateFrom,
    startDateTo,
    tagFilter,
    showAdvancedFilters,
    advancedSearchFields
  ])

  // 排序会员卡
  const sortedCards = useMemo(() => {
    return [...filteredCards].sort((a, b) => {
      let valueA: any = a[sortField]
      let valueB: any = b[sortField]

      // 处理特殊字段
      if (sortField === "remainingDays" || sortField === "remainingCount" || sortField === "remainingValue") {
        valueA = valueA === null ? -1 : valueA
        valueB = valueB === null ? -1 : valueB
      }

      // 比较
      if (valueA < valueB) return sortDirection === "asc" ? -1 : 1
      if (valueA > valueB) return sortDirection === "asc" ? 1 : -1
      return 0
    })
  }, [filteredCards, sortField, sortDirection])

  // 分页
  const totalPages = Math.ceil(sortedCards.length / itemsPerPage)
  const paginatedCards = sortedCards.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  // 处理排序
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // 处理卡片选择
  const handleCardSelect = (cardId: string, selected: boolean) => {
    if (selected) {
      setSelectedCards(prev => [...prev, cardId])
    } else {
      setSelectedCards(prev => prev.filter(id => id !== cardId))
    }
  }

  // 处理批量操作
  const handleBatchAction = (action: string) => {
    if (selectedCards.length === 0) {
      toast({
        title: "请先选择会员卡",
        description: "您需要至少选择一张会员卡才能执行批量操作",
        variant: "destructive",
      })
      return
    }

    switch (action) {
      case "freeze":
        toast({
          title: "批量冻结",
          description: `已选择 ${selectedCards.length} 张会员卡进行冻结操作`,
        })
        setDialogType("freeze")
        break
      case "unfreeze":
        toast({
          title: "批量解冻",
          description: `已选择 ${selectedCards.length} 张会员卡进行解冻操作`,
        })
        setDialogType("unfreeze")
        break
      case "extend":
        toast({
          title: "批量延期",
          description: `已选择 ${selectedCards.length} 张会员卡进行延期操作`,
        })
        setDialogType("extend")
        break
      case "export":
        toast({
          title: "导出数据",
          description: `正在导出 ${selectedCards.length} 张会员卡的数据`,
        })
        setDialogType("export")
        break
      case "delete":
        toast({
          title: "批量删除",
          description: `确定要删除选中的 ${selectedCards.length} 张会员卡吗？`,
          variant: "destructive",
        })
        setDialogType("delete")
        break
      default:
        break
    }
  }

  // 重置所有筛选条件
  const resetAllFilters = () => {
    setSearchTerm("")
    setCardTypeFilter("all")
    setStatusFilter("all")
    setTrialCardFilter(null)
    setAutoRenewFilter(null)
    setDateRangeFilter("all")
    setStartDateFrom("")
    setStartDateTo("")
    setTagFilter([])
    setAdvancedSearchFields({
      memberName: "",
      memberPhone: "",
      cardId: "",
      cardType: "",
      notes: "",
    })
    setShowAdvancedFilters(false)
  }

  // 计算统计数据
  const statsData = useMemo(() => {
    const activeCards = memberCards.filter(card => card.status === "active").length
    const expiredCards = memberCards.filter(card => card.status === "expired").length
    const frozenCards = memberCards.filter(card => card.status === "frozen").length
    const inactiveCards = memberCards.filter(card => card.status === "inactive").length
    const onLeaveCards = memberCards.filter(card => card.status === "onLeave").length
    const refundedCards = memberCards.filter(card => card.status === "refunded").length
    const trialCards = memberCards.filter(card => card.isTrialCard).length
    const autoRenewCards = memberCards.filter(card => card.isAutoRenew).length

    return {
      total: memberCards.length,
      active: activeCards,
      expired: expiredCards,
      frozen: frozenCards,
      inactive: inactiveCards,
      onLeave: onLeaveCards,
      refunded: refundedCards,
      trial: trialCards,
      autoRenew: autoRenewCards
    }
  }, [memberCards])

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.push("/members")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">会员卡列表</h1>
            <p className="text-muted-foreground">
              查看和管理所有会员的会员卡
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={() => setViewMode(viewMode === "list" ? "grid" : "list")}>
                  {viewMode === "list" ? <LayoutGrid className="h-4 w-4" /> : <LayoutList className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{viewMode === "list" ? "切换到网格视图" : "切换到列表视图"}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                导出
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem onClick={() => handleBatchAction("export")}>
                <FileText className="mr-2 h-4 w-4" />
                导出为Excel
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Printer className="mr-2 h-4 w-4" />
                打印会员卡列表
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                批量操作
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem onClick={() => handleBatchAction("freeze")}>
                <Clock className="mr-2 h-4 w-4" />
                批量冻结
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleBatchAction("unfreeze")}>
                <RefreshCw className="mr-2 h-4 w-4" />
                批量解冻
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleBatchAction("extend")}>
                <CalendarRange className="mr-2 h-4 w-4" />
                批量延期
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleBatchAction("delete")} className="text-red-600">
                <Trash className="mr-2 h-4 w-4" />
                批量删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button onClick={() => setAddCardDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加会员卡
          </Button>
        </div>
      </div>

      {/* 数据统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 flex justify-between items-center">
            <div>
              <p className="text-sm font-medium text-muted-foreground">总会员卡数</p>
              <h3 className="text-2xl font-bold">{statsData.total}</h3>
            </div>
            <CreditCard className="h-8 w-8 text-primary opacity-80" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex justify-between items-center">
            <div>
              <p className="text-sm font-medium text-muted-foreground">有效会员卡</p>
              <h3 className="text-2xl font-bold text-green-600">{statsData.active}</h3>
              <p className="text-xs text-muted-foreground mt-1">占比 {Math.round((statsData.active / statsData.total) * 100)}%</p>
            </div>
            <CheckCircle2 className="h-8 w-8 text-green-500 opacity-80" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex justify-between items-center">
            <div>
              <p className="text-sm font-medium text-muted-foreground">已过期</p>
              <h3 className="text-2xl font-bold text-gray-600">{statsData.expired}</h3>
              <p className="text-xs text-muted-foreground mt-1">占比 {Math.round((statsData.expired / statsData.total) * 100)}%</p>
            </div>
            <AlertCircle className="h-8 w-8 text-gray-500 opacity-80" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex justify-between items-center">
            <div>
              <p className="text-sm font-medium text-muted-foreground">已冻结/未激活</p>
              <h3 className="text-2xl font-bold text-blue-600">{statsData.frozen + statsData.inactive}</h3>
              <p className="text-xs text-muted-foreground mt-1">冻结: {statsData.frozen} / 未激活: {statsData.inactive}</p>
            </div>
            <Clock className="h-8 w-8 text-blue-500 opacity-80" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 flex justify-between items-center">
            <div>
              <p className="text-sm font-medium text-muted-foreground">请假/退卡</p>
              <h3 className="text-2xl font-bold text-purple-600">{statsData.onLeave + statsData.refunded}</h3>
              <p className="text-xs text-muted-foreground mt-1">请假: {statsData.onLeave} / 退卡: {statsData.refunded}</p>
            </div>
            <User className="h-8 w-8 text-purple-500 opacity-80" />
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索会员姓名、手机号、卡号或卡名"
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div className="flex gap-2 flex-wrap">
              <Select value={cardTypeFilter} onValueChange={setCardTypeFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="卡类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="期限卡">期限卡</SelectItem>
                  <SelectItem value="次卡">次卡</SelectItem>
                  <SelectItem value="储值卡">储值卡</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="卡状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">有效</SelectItem>
                  <SelectItem value="expired">已过期</SelectItem>
                  <SelectItem value="frozen">已冻结</SelectItem>
                  <SelectItem value="inactive">未激活</SelectItem>
                  <SelectItem value="onLeave">请假中</SelectItem>
                  <SelectItem value="refunded">已退卡</SelectItem>
                </SelectContent>
              </Select>

              <Popover open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
                <PopoverTrigger asChild>
                  <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    高级筛选
                    {(trialCardFilter !== null || autoRenewFilter !== null || dateRangeFilter !== "all" || tagFilter.length > 0) && (
                      <Badge className="ml-2 bg-primary" variant="secondary">
                        {(trialCardFilter !== null ? 1 : 0) +
                         (autoRenewFilter !== null ? 1 : 0) +
                         (dateRangeFilter !== "all" ? 1 : 0) +
                         (tagFilter.length > 0 ? 1 : 0)}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-4">
                    <h4 className="font-medium">高级筛选选项</h4>

                    <div className="space-y-2">
                      <Label>体验卡</Label>
                      <Select
                        value={trialCardFilter === null ? "all" : trialCardFilter ? "yes" : "no"}
                        onValueChange={(value) => setTrialCardFilter(value === "all" ? null : value === "yes")}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="yes">是</SelectItem>
                          <SelectItem value="no">否</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>自动续费</Label>
                      <Select
                        value={autoRenewFilter === null ? "all" : autoRenewFilter ? "yes" : "no"}
                        onValueChange={(value) => setAutoRenewFilter(value === "all" ? null : value === "yes")}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="yes">是</SelectItem>
                          <SelectItem value="no">否</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>日期范围</Label>
                      <Select
                        value={dateRangeFilter}
                        onValueChange={(value: "all" | "startDate" | "endDate" | "createdAt") => setDateRangeFilter(value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="选择日期类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">不限</SelectItem>
                          <SelectItem value="startDate">开始日期</SelectItem>
                          <SelectItem value="endDate">结束日期</SelectItem>
                          <SelectItem value="createdAt">创建日期</SelectItem>
                        </SelectContent>
                      </Select>

                      {dateRangeFilter !== "all" && (
                        <div className="grid grid-cols-2 gap-2 mt-2">
                          <div className="space-y-1">
                            <Label className="text-xs">从</Label>
                            <Input
                              type="date"
                              value={startDateFrom}
                              onChange={(e) => setStartDateFrom(e.target.value)}
                            />
                          </div>
                          <div className="space-y-1">
                            <Label className="text-xs">至</Label>
                            <Input
                              type="date"
                              value={startDateTo}
                              onChange={(e) => setStartDateTo(e.target.value)}
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    {allTags.length > 0 && (
                      <div className="space-y-2">
                        <Label>会员标签</Label>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {allTags.map(tag => (
                            <Badge
                              key={tag}
                              variant={tagFilter.includes(tag) ? "default" : "outline"}
                              className="cursor-pointer"
                              onClick={() => {
                                if (tagFilter.includes(tag)) {
                                  setTagFilter(tagFilter.filter(t => t !== tag))
                                } else {
                                  setTagFilter([...tagFilter, tag])
                                }
                              }}
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex justify-between pt-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setTrialCardFilter(null)
                          setAutoRenewFilter(null)
                          setDateRangeFilter("all")
                          setStartDateFrom("")
                          setStartDateTo("")
                          setTagFilter([])
                        }}
                      >
                        重置筛选
                      </Button>
                      <Button size="sm" onClick={() => setShowAdvancedFilters(false)}>
                        应用筛选
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              <Button variant="outline" size="icon" onClick={resetAllFilters}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* 高级搜索 */}
          {showAdvancedFilters && (
            <div className="mt-4 pt-4 border-t">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs">会员姓名</Label>
                  <Input
                    placeholder="输入会员姓名"
                    value={advancedSearchFields.memberName}
                    onChange={(e) => setAdvancedSearchFields({...advancedSearchFields, memberName: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs">会员手机号</Label>
                  <Input
                    placeholder="输入手机号"
                    value={advancedSearchFields.memberPhone}
                    onChange={(e) => setAdvancedSearchFields({...advancedSearchFields, memberPhone: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs">卡号</Label>
                  <Input
                    placeholder="输入卡号"
                    value={advancedSearchFields.cardId}
                    onChange={(e) => setAdvancedSearchFields({...advancedSearchFields, cardId: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs">卡名</Label>
                  <Input
                    placeholder="输入卡名/卡类型"
                    value={advancedSearchFields.cardType}
                    onChange={(e) => setAdvancedSearchFields({...advancedSearchFields, cardType: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs">备注</Label>
                  <Input
                    placeholder="搜索备注内容"
                    value={advancedSearchFields.notes}
                    onChange={(e) => setAdvancedSearchFields({...advancedSearchFields, notes: e.target.value})}
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 会员卡列表 */}
      <Card>
        <CardContent className="pt-6">
          {viewMode === "list" ? (
            <>
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="select-all"
                    checked={selectAll || (selectedCards.length > 0 && selectedCards.length === filteredCards.length)}
                    onCheckedChange={(checked) => {
                      setSelectAll(!!checked)
                    }}
                  />
                  <Label htmlFor="select-all" className="text-sm font-medium">
                    {selectedCards.length > 0 ? `已选择 ${selectedCards.length} 张会员卡` : "全选"}
                  </Label>

                  {selectedCards.length > 0 && (
                    <div className="flex gap-2 ml-4">
                      <Button variant="outline" size="sm" onClick={() => handleBatchAction("freeze")}>
                        <Clock className="mr-1 h-3.5 w-3.5" />
                        批量冻结
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleBatchAction("extend")}>
                        <CalendarRange className="mr-1 h-3.5 w-3.5" />
                        批量延期
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleBatchAction("export")}>
                        <Download className="mr-1 h-3.5 w-3.5" />
                        导出所选
                      </Button>
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  <Label htmlFor="items-per-page" className="text-sm">每页显示:</Label>
                  <Select
                    value={itemsPerPage.toString()}
                    onValueChange={(value) => setItemsPerPage(parseInt(value))}
                  >
                    <SelectTrigger id="items-per-page" className="w-[80px] h-8">
                      <SelectValue placeholder="10" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[40px]">
                        <Checkbox
                          checked={selectAll || (paginatedCards.length > 0 && selectedCards.length === paginatedCards.length)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedCards(paginatedCards.map(card => card.id))
                            } else {
                              setSelectedCards([])
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead
                        className="w-[100px] cursor-pointer"
                        onClick={() => handleSort("id")}
                      >
                        <div className="flex items-center">
                          卡号
                          {sortField === "id" && (
                            <span className="ml-1">
                              {sortDirection === "asc" ? "↑" : "↓"}
                            </span>
                          )}
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("memberName")}
                      >
                        <div className="flex items-center">
                          会员信息
                          {sortField === "memberName" && (
                            <span className="ml-1">
                              {sortDirection === "asc" ? "↑" : "↓"}
                            </span>
                          )}
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("cardType")}
                      >
                        <div className="flex items-center">
                          卡类型
                          {sortField === "cardType" && (
                            <span className="ml-1">
                              {sortDirection === "asc" ? "↑" : "↓"}
                            </span>
                          )}
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("startDate")}
                      >
                        <div className="flex items-center">
                          有效期
                          {(sortField === "startDate" || sortField === "endDate") && (
                            <span className="ml-1">
                              {sortDirection === "asc" ? "↑" : "↓"}
                            </span>
                          )}
                        </div>
                      </TableHead>
                      <TableHead>剩余</TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("status")}
                      >
                        <div className="flex items-center">
                          状态
                          {sortField === "status" && (
                            <span className="ml-1">
                              {sortDirection === "asc" ? "↑" : "↓"}
                            </span>
                          )}
                        </div>
                      </TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedCards.length > 0 ? (
                      paginatedCards.map((card) => (
                        <TableRow key={card.id} className={selectedCards.includes(card.id) ? "bg-muted/30" : ""}>
                          <TableCell>
                            <Checkbox
                              checked={selectedCards.includes(card.id)}
                              onCheckedChange={(checked) => handleCardSelect(card.id, !!checked)}
                            />
                          </TableCell>
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              {card.id}
                              {card.isTrialCard && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <Sparkles className="ml-1 h-3.5 w-3.5 text-pink-500" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>体验卡</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col">
                              <div className="flex items-center gap-1">
                                <span>{card.memberName}</span>
                                {card.tags.length > 0 && (
                                  <div className="flex gap-1">
                                    {card.tags.map(tag => (
                                      <Badge key={tag} variant="outline" className="text-xs py-0 h-5">
                                        {tag}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                              <span className="text-sm text-muted-foreground">{card.memberPhone}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: card.cardTypeColor }}
                              ></div>
                              <span>{card.cardType}</span>
                              {card.isAutoRenew && (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <RotateCcw className="h-3.5 w-3.5 text-blue-500" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>自动续费</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col">
                              <span className="text-sm">开始: {card.startDate}</span>
                              <span className="text-sm">结束: {card.endDate || '无限期'}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {card.remainingDays !== null && (
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                                <span>{card.remainingDays}天</span>
                              </div>
                            )}
                            {card.remainingCount !== null && (
                              <div className="flex items-center gap-1">
                                <Tag className="h-3.5 w-3.5 text-muted-foreground" />
                                <span>{card.remainingCount}次</span>
                              </div>
                            )}
                            {card.remainingValue !== null && (
                              <div className="flex items-center gap-1">
                                <Wallet className="h-3.5 w-3.5 text-muted-foreground" />
                                <span>¥{card.remainingValue}</span>
                              </div>
                            )}
                            {card.lastUsedDate && (
                              <div className="text-xs text-muted-foreground mt-1">
                                上次使用: {card.lastUsedDate}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>{getStatusBadge(card.status)}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-56">
                                <DropdownMenuLabel>基础操作</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => {
                                  setSelectedCardId(card.id)
                                  setDetailDialogOpen(true)
                                }}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  查看详情
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => {
                                  setSelectedCardId(card.id)
                                  setEditDialogOpen(true)
                                }}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  编辑信息
                                </DropdownMenuItem>

                                <DropdownMenuLabel className="mt-2">卡片管理</DropdownMenuLabel>
                                <DropdownMenuSeparator />

                                {card.status === "active" ? (
                                  <>
                                    <DropdownMenuItem onClick={() => {
                                      setSelectedCard(card)
                                      setDialogType("freeze")
                                    }}>
                                      <Clock className="mr-2 h-4 w-4" />
                                      冻结
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => {
                                      setSelectedCard(card)
                                      setDialogType("leave")
                                    }}>
                                      <User className="mr-2 h-4 w-4" />
                                      请假
                                    </DropdownMenuItem>
                                  </>
                                ) : card.status === "frozen" ? (
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedCard(card)
                                    setDialogType("unfreeze")
                                  }}>
                                    <RefreshCw className="mr-2 h-4 w-4" />
                                    解冻
                                  </DropdownMenuItem>
                                ) : card.status === "onLeave" ? (
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedCard(card)
                                    setDialogType("endLeave")
                                  }}>
                                    <RefreshCw className="mr-2 h-4 w-4" />
                                    结束请假
                                  </DropdownMenuItem>
                                ) : card.status === "inactive" ? (
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedCard(card)
                                    setDialogType("activate")
                                  }}>
                                    <PlayCircle className="mr-2 h-4 w-4" />
                                    手动激活
                                  </DropdownMenuItem>
                                ) : null}

                                {card.status !== "refunded" && (
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedCard(card)
                                    setDialogType("refund")
                                  }}>
                                    <Wallet className="mr-2 h-4 w-4" />
                                    退卡
                                  </DropdownMenuItem>
                                )}

                                <DropdownMenuItem onClick={() => {
                                  setSelectedCard(card)
                                  setDialogType("extend")
                                }}>
                                  <CalendarRange className="mr-2 h-4 w-4" />
                                  延长有效期
                                </DropdownMenuItem>

                                <DropdownMenuItem onClick={() => {
                                  setSelectedCard(card)
                                  setDialogType("transfer")
                                }}>
                                  <Share2 className="mr-2 h-4 w-4" />
                                  转让会员卡
                                </DropdownMenuItem>

                                {/* 充值和调整 */}
                                {(card.cardTypeBadge === "储值卡" || card.cardTypeBadge === "组合卡") && card.status !== "refunded" && (
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedCard(card)
                                    setDialogType("recharge")
                                  }}>
                                    <CreditCard className="mr-2 h-4 w-4" />
                                    充值续费
                                  </DropdownMenuItem>
                                )}

                                {card.cardTypeBadge === "次卡" && card.status !== "refunded" && (
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedCard(card)
                                    setDialogType("adjustCount")
                                  }}>
                                    <Plus className="mr-2 h-4 w-4" />
                                    调整次数
                                  </DropdownMenuItem>
                                )}

                                <DropdownMenuLabel className="mt-2">高级操作</DropdownMenuLabel>
                                <DropdownMenuSeparator />

                                {/* 升级会员卡 */}
                                <DropdownMenuItem onClick={() => {
                                  setSelectedCard(card)
                                  setUpgradeDialogOpen(true)
                                }}>
                                  <TrendingUp className="mr-2 h-4 w-4" />
                                  升级会员卡
                                </DropdownMenuItem>

                                {/* 合并会员卡 */}
                                <DropdownMenuItem onClick={() => {
                                  setSelectedCard(card)
                                  setMergeDialogOpen(true)
                                }}>
                                  <Merge className="mr-2 h-4 w-4" />
                                  合并会员卡
                                </DropdownMenuItem>

                                {/* 设置自动续费 */}
                                {!card.isAutoRenew && card.status === "active" && (
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedCard(card)
                                    setDialogType("autoRenew")
                                  }}>
                                    <RotateCcw className="mr-2 h-4 w-4" />
                                    设置自动续费
                                  </DropdownMenuItem>
                                )}

                                {/* 取消自动续费 */}
                                {card.isAutoRenew && (
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedCard(card)
                                    setDialogType("cancelAutoRenew")
                                  }}>
                                    <X className="mr-2 h-4 w-4" />
                                    取消自动续费
                                  </DropdownMenuItem>
                                )}

                                <DropdownMenuSeparator />

                                <DropdownMenuItem className="text-red-600" onClick={() => {
                                  setSelectedCard(card)
                                  setDialogType("delete")
                                }}>
                                  <Trash className="mr-2 h-4 w-4" />
                                  删除会员卡
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          没有找到符合条件的会员卡
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </>
          ) : (
            // 网格视图
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {paginatedCards.length > 0 ? (
                paginatedCards.map((card) => (
                  <Card key={card.id} className={`overflow-hidden ${selectedCards.includes(card.id) ? "ring-2 ring-primary" : ""}`}>
                    <CardHeader className="p-4 pb-2 flex flex-row items-start justify-between space-y-0">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={selectedCards.includes(card.id)}
                            onCheckedChange={(checked) => handleCardSelect(card.id, !!checked)}
                          />
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: card.cardTypeColor }}
                          ></div>
                          <CardTitle className="text-base font-medium">{card.cardType}</CardTitle>
                          {card.isTrialCard && <Sparkles className="h-4 w-4 text-pink-500" />}
                        </div>
                        <CardDescription>{card.id}</CardDescription>
                      </div>
                      {getStatusBadge(card.status)}
                    </CardHeader>
                    <CardContent className="p-4 pt-0">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div className="flex flex-col">
                            <span className="text-sm font-medium">{card.memberName}</span>
                            <span className="text-xs text-muted-foreground">{card.memberPhone}</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <div className="flex flex-col">
                            <span className="text-xs">开始: {card.startDate}</span>
                            <span className="text-xs">结束: {card.endDate || '无限期'}</span>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2">
                          {card.remainingDays !== null && (
                            <Badge variant="outline" className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>{card.remainingDays}天</span>
                            </Badge>
                          )}
                          {card.remainingCount !== null && (
                            <Badge variant="outline" className="flex items-center gap-1">
                              <Tag className="h-3 w-3" />
                              <span>{card.remainingCount}次</span>
                            </Badge>
                          )}
                          {card.remainingValue !== null && (
                            <Badge variant="outline" className="flex items-center gap-1">
                              <Wallet className="h-3 w-3" />
                              <span>¥{card.remainingValue}</span>
                            </Badge>
                          )}
                          {card.isAutoRenew && (
                            <Badge variant="outline" className="flex items-center gap-1 bg-blue-50">
                              <RotateCcw className="h-3 w-3" />
                              <span>自动续费</span>
                            </Badge>
                          )}
                        </div>

                        {card.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {card.tags.map(tag => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </CardContent>
                    <CardFooter className="p-4 pt-0 flex justify-between">
                      <Button variant="ghost" size="sm" onClick={() => {
                        setSelectedCardId(card.id)
                        setDetailDialogOpen(true)
                      }}>
                        <Eye className="mr-1 h-4 w-4" />
                        详情
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="mr-1 h-4 w-4" />
                            操作
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => {
                            setSelectedCardId(card.id)
                            setEditDialogOpen(true)
                          }}>
                            <Edit className="mr-2 h-4 w-4" />
                            编辑
                          </DropdownMenuItem>
                          {card.status === "active" ? (
                            <DropdownMenuItem onClick={() => {
                              setSelectedCard(card)
                              setDialogType("freeze")
                            }}>
                              <Clock className="mr-2 h-4 w-4" />
                              冻结
                            </DropdownMenuItem>
                          ) : card.status === "frozen" ? (
                            <DropdownMenuItem onClick={() => {
                              setSelectedCard(card)
                              setDialogType("unfreeze")
                            }}>
                              <RefreshCw className="mr-2 h-4 w-4" />
                              解冻
                            </DropdownMenuItem>
                          ) : card.status === "inactive" ? (
                            <DropdownMenuItem onClick={() => {
                              setSelectedCard(card)
                              setDialogType("activate")
                            }}>
                              <PlayCircle className="mr-2 h-4 w-4" />
                              手动激活
                            </DropdownMenuItem>
                          ) : null}
                          <DropdownMenuItem onClick={() => {
                            setSelectedCard(card)
                            setDialogType("extend")
                          }}>
                            <CalendarRange className="mr-2 h-4 w-4" />
                            延长有效期
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600" onClick={() => {
                            setSelectedCard(card)
                            setDialogType("delete")
                          }}>
                            <Trash className="mr-2 h-4 w-4" />
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </CardFooter>
                  </Card>
                ))
              ) : (
                <div className="col-span-full flex items-center justify-center h-40">
                  <p className="text-muted-foreground">没有找到符合条件的会员卡</p>
                </div>
              )}
            </div>
          )}

          {/* 分页 */}
          {filteredCards.length > 0 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                共 {filteredCards.length} 条记录，当前显示 {(currentPage - 1) * itemsPerPage + 1} - {Math.min(currentPage * itemsPerPage, filteredCards.length)}
              </div>
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={currentPage === 1 ? undefined : () => setCurrentPage(p => Math.max(1, p - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>

                  {totalPages <= 7 ? (
                    // 如果总页数小于等于7，显示所有页码
                    Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => setCurrentPage(page)}
                          isActive={currentPage === page}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))
                  ) : (
                    // 如果总页数大于7，显示部分页码和省略号
                    <>
                      {/* 始终显示第一页 */}
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(1)}
                          isActive={currentPage === 1}
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>

                      {/* 如果当前页大于3，显示前省略号 */}
                      {currentPage > 3 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* 显示当前页附近的页码 */}
                      {Array.from({ length: 5 }, (_, i) => {
                        const page = Math.max(2, currentPage - 1) + i
                        return page <= totalPages - 1 ? (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={currentPage === page}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ) : null
                      }).filter(Boolean).slice(0, 3)}

                      {/* 如果当前页小于总页数-2，显示后省略号 */}
                      {currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* 始终显示最后一页 */}
                      <PaginationItem>
                        <PaginationLink
                          onClick={() => setCurrentPage(totalPages)}
                          isActive={currentPage === totalPages}
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      onClick={currentPage === totalPages ? undefined : () => setCurrentPage(p => Math.min(totalPages, p + 1))}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 对话框组件 */}
      {/* 冻结会员卡对话框 */}
      <Dialog open={dialogType === "freeze"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>冻结会员卡</DialogTitle>
            <DialogDescription>
              冻结后，会员将无法使用此卡预约课程，直到解冻为止。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="freeze-reason">冻结原因</Label>
              <Input
                id="freeze-reason"
                placeholder="请输入冻结原因"
                value={dialogNote}
                onChange={(e) => setDialogNote(e.target.value)}
              />
            </div>
            {selectedCard && (
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: selectedCard.cardTypeColor }}
                  ></div>
                  <span className="font-medium">{selectedCard.cardType}</span>
                  <span className="text-sm text-muted-foreground">({selectedCard.id})</span>
                </div>
                <p className="text-sm">会员: {selectedCard.memberName}</p>
                <p className="text-sm">有效期: {selectedCard.startDate} 至 {selectedCard.endDate || '无限期'}</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: "会员卡已冻结",
                description: selectedCards.length > 0
                  ? `已成功冻结 ${selectedCards.length} 张会员卡`
                  : `已成功冻结会员卡 ${selectedCard?.id}`,
              })
              setDialogType(null)
              setDialogNote("")
            }}>
              确认冻结
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 解冻会员卡对话框 */}
      <Dialog open={dialogType === "unfreeze"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>解冻会员卡</DialogTitle>
            <DialogDescription>
              解冻后，会员将可以继续使用此卡预约课程。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="unfreeze-reason">解冻原因</Label>
              <Input
                id="unfreeze-reason"
                placeholder="请输入解冻原因"
                value={dialogNote}
                onChange={(e) => setDialogNote(e.target.value)}
              />
            </div>
            {selectedCard && (
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: selectedCard.cardTypeColor }}
                  ></div>
                  <span className="font-medium">{selectedCard.cardType}</span>
                  <span className="text-sm text-muted-foreground">({selectedCard.id})</span>
                </div>
                <p className="text-sm">会员: {selectedCard.memberName}</p>
                <p className="text-sm">有效期: {selectedCard.startDate} 至 {selectedCard.endDate || '无限期'}</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: "会员卡已解冻",
                description: selectedCards.length > 0
                  ? `已成功解冻 ${selectedCards.length} 张会员卡`
                  : `已成功解冻会员卡 ${selectedCard?.id}`,
              })
              setDialogType(null)
              setDialogNote("")
            }}>
              确认解冻
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 延长有效期对话框 */}
      <Dialog open={dialogType === "extend"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>延长会员卡有效期</DialogTitle>
            <DialogDescription>
              为会员卡延长有效期，延长后的有效期将从当前结束日期开始计算。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="extend-days">延长天数</Label>
                <Input
                  id="extend-days"
                  type="number"
                  placeholder="输入天数"
                  min="1"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="extend-reason">延期原因</Label>
                <Select defaultValue="holiday">
                  <SelectTrigger id="extend-reason">
                    <SelectValue placeholder="选择原因" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="holiday">会员请假</SelectItem>
                    <SelectItem value="compensation">服务补偿</SelectItem>
                    <SelectItem value="promotion">促销活动</SelectItem>
                    <SelectItem value="other">其他原因</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="extend-note">备注</Label>
              <Input
                id="extend-note"
                placeholder="请输入备注信息"
                value={dialogNote}
                onChange={(e) => setDialogNote(e.target.value)}
              />
            </div>

            {selectedCard && (
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: selectedCard.cardTypeColor }}
                  ></div>
                  <span className="font-medium">{selectedCard.cardType}</span>
                  <span className="text-sm text-muted-foreground">({selectedCard.id})</span>
                </div>
                <p className="text-sm">会员: {selectedCard.memberName}</p>
                <p className="text-sm">当前有效期: {selectedCard.startDate} 至 {selectedCard.endDate || '无限期'}</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: "有效期已延长",
                description: selectedCards.length > 0
                  ? `已成功延长 ${selectedCards.length} 张会员卡的有效期`
                  : `已成功延长会员卡 ${selectedCard?.id} 的有效期`,
              })
              setDialogType(null)
              setDialogNote("")
            }}>
              确认延长
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 转让会员卡对话框 */}
      <Dialog open={dialogType === "transfer"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>转让会员卡</DialogTitle>
            <DialogDescription>
              将会员卡转让给其他会员，转让后原会员将无法使用此卡。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="transfer-member">转让给会员</Label>
              <Select>
                <SelectTrigger id="transfer-member">
                  <SelectValue placeholder="选择会员" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="M010">张小明 (13900139000)</SelectItem>
                  <SelectItem value="M011">李小红 (13900139001)</SelectItem>
                  <SelectItem value="M012">王小刚 (13900139002)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="transfer-reason">转让原因</Label>
              <Input
                id="transfer-reason"
                placeholder="请输入转让原因"
                value={dialogNote}
                onChange={(e) => setDialogNote(e.target.value)}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox id="transfer-confirm" />
              <Label htmlFor="transfer-confirm">我确认此操作不可撤销，转让后原会员将无法使用此卡</Label>
            </div>

            {selectedCard && (
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: selectedCard.cardTypeColor }}
                  ></div>
                  <span className="font-medium">{selectedCard.cardType}</span>
                  <span className="text-sm text-muted-foreground">({selectedCard.id})</span>
                </div>
                <p className="text-sm">当前会员: {selectedCard.memberName}</p>
                <p className="text-sm">有效期: {selectedCard.startDate} 至 {selectedCard.endDate || '无限期'}</p>
                {selectedCard.remainingDays !== null && <p className="text-sm">剩余天数: {selectedCard.remainingDays}天</p>}
                {selectedCard.remainingCount !== null && <p className="text-sm">剩余次数: {selectedCard.remainingCount}次</p>}
                {selectedCard.remainingValue !== null && <p className="text-sm">剩余金额: ¥{selectedCard.remainingValue}</p>}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: "会员卡已转让",
                description: `已成功将会员卡 ${selectedCard?.id} 转让给新会员`,
              })
              setDialogType(null)
              setDialogNote("")
            }}>
              确认转让
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除会员卡对话框 */}
      <Dialog open={dialogType === "delete"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-red-600">删除会员卡</DialogTitle>
            <DialogDescription>
              此操作不可撤销，删除后会员卡将永久消失。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="delete-reason">删除原因</Label>
              <Input
                id="delete-reason"
                placeholder="请输入删除原因"
                value={dialogNote}
                onChange={(e) => setDialogNote(e.target.value)}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox id="delete-confirm" />
              <Label htmlFor="delete-confirm" className="text-red-600">我确认此操作不可撤销，删除后数据将无法恢复</Label>
            </div>

            {selectedCard && (
              <div className="rounded-md bg-red-50 p-4 border border-red-200">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: selectedCard.cardTypeColor }}
                  ></div>
                  <span className="font-medium">{selectedCard.cardType}</span>
                  <span className="text-sm text-muted-foreground">({selectedCard.id})</span>
                </div>
                <p className="text-sm">会员: {selectedCard.memberName}</p>
                <p className="text-sm">有效期: {selectedCard.startDate} 至 {selectedCard.endDate || '无限期'}</p>
                <p className="text-sm">状态: {
                  selectedCard.status === "active" ? "有效" :
                  selectedCard.status === "expired" ? "已过期" :
                  selectedCard.status === "frozen" ? "已冻结" :
                  selectedCard.status === "inactive" ? "未激活" : "未知"
                }</p>
              </div>
            )}

            {selectedCards.length > 0 && !selectedCard && (
              <div className="rounded-md bg-red-50 p-4 border border-red-200">
                <p className="font-medium text-red-600">您将删除 {selectedCards.length} 张会员卡</p>
                <p className="text-sm mt-1">此操作将批量删除所选的所有会员卡，请确认操作。</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button variant="destructive" onClick={() => {
              toast({
                title: "会员卡已删除",
                description: selectedCards.length > 0 && !selectedCard
                  ? `已成功删除 ${selectedCards.length} 张会员卡`
                  : `已成功删除会员卡 ${selectedCard?.id}`,
                variant: "destructive",
              })
              setDialogType(null)
              setDialogNote("")
              setSelectedCards([])
              setSelectedCard(null)
            }}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 请假对话框 */}
      <Dialog open={dialogType === "leave"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>会员卡请假</DialogTitle>
            <DialogDescription>
              请假期间，会员卡有效期将暂停计算，会员将无法使用此卡预约课程。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="leave-start-date">开始日期</Label>
                <Input
                  id="leave-start-date"
                  type="date"
                  defaultValue={new Date().toISOString().split('T')[0]}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="leave-end-date">结束日期</Label>
                <Input
                  id="leave-end-date"
                  type="date"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="leave-reason">请假原因</Label>
              <Select defaultValue="personal">
                <SelectTrigger id="leave-reason">
                  <SelectValue placeholder="选择原因" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="personal">个人原因</SelectItem>
                  <SelectItem value="health">健康原因</SelectItem>
                  <SelectItem value="travel">出差/旅行</SelectItem>
                  <SelectItem value="other">其他原因</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="leave-note">备注</Label>
              <Input
                id="leave-note"
                placeholder="请输入备注信息"
                value={dialogNote}
                onChange={(e) => setDialogNote(e.target.value)}
              />
            </div>

            {selectedCard && (
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: selectedCard.cardTypeColor }}
                  ></div>
                  <span className="font-medium">{selectedCard.cardType}</span>
                  <span className="text-sm text-muted-foreground">({selectedCard.id})</span>
                </div>
                <p className="text-sm">会员: {selectedCard.memberName}</p>
                <p className="text-sm">当前有效期: {selectedCard.startDate} 至 {selectedCard.endDate || '无限期'}</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: "请假成功",
                description: `会员卡 ${selectedCard?.id} 已成功设置为请假状态`,
              })
              setDialogType(null)
              setDialogNote("")
            }}>
              确认请假
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 结束请假对话框 */}
      <Dialog open={dialogType === "endLeave"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>结束请假</DialogTitle>
            <DialogDescription>
              结束请假后，会员卡将恢复正常状态，会员可以继续使用此卡。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="end-leave-date">实际结束日期</Label>
              <Input
                id="end-leave-date"
                type="date"
                defaultValue={new Date().toISOString().split('T')[0]}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end-leave-note">备注</Label>
              <Input
                id="end-leave-note"
                placeholder="请输入备注信息"
                value={dialogNote}
                onChange={(e) => setDialogNote(e.target.value)}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox id="extend-validity" defaultChecked />
              <Label htmlFor="extend-validity">自动延长有效期（根据请假天数）</Label>
            </div>

            {selectedCard && (
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: selectedCard.cardTypeColor }}
                  ></div>
                  <span className="font-medium">{selectedCard.cardType}</span>
                  <span className="text-sm text-muted-foreground">({selectedCard.id})</span>
                </div>
                <p className="text-sm">会员: {selectedCard.memberName}</p>
                <p className="text-sm">请假开始日期: 2023-09-01</p>
                <p className="text-sm">原计划结束日期: 2023-09-15</p>
                <p className="text-sm">请假天数: 15天</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: "请假已结束",
                description: `会员卡 ${selectedCard?.id} 已恢复正常状态`,
              })
              setDialogType(null)
              setDialogNote("")
            }}>
              确认结束请假
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 退卡对话框 */}
      <Dialog open={dialogType === "refund"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-red-600">会员卡退卡</DialogTitle>
            <DialogDescription>
              退卡后，会员将无法使用此卡，请谨慎操作。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="refund-amount">退款金额</Label>
                <Input
                  id="refund-amount"
                  type="number"
                  placeholder="输入退款金额"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="refund-method">退款方式</Label>
                <Select defaultValue="original">
                  <SelectTrigger id="refund-method">
                    <SelectValue placeholder="选择退款方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="original">原路退回</SelectItem>
                    <SelectItem value="cash">现金退款</SelectItem>
                    <SelectItem value="transfer">银行转账</SelectItem>
                    <SelectItem value="credit">转为储值</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="refund-reason">退卡原因</Label>
              <Select defaultValue="personal">
                <SelectTrigger id="refund-reason">
                  <SelectValue placeholder="选择原因" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="personal">个人原因</SelectItem>
                  <SelectItem value="health">健康原因</SelectItem>
                  <SelectItem value="dissatisfied">服务不满意</SelectItem>
                  <SelectItem value="moving">搬家/工作变动</SelectItem>
                  <SelectItem value="other">其他原因</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="refund-note">详细说明</Label>
              <Textarea
                id="refund-note"
                placeholder="请输入详细退卡原因"
                value={dialogNote}
                onChange={(e) => setDialogNote(e.target.value)}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox id="refund-confirm" />
              <Label htmlFor="refund-confirm" className="text-red-600">我确认此操作不可撤销，退卡后会员将无法使用此卡</Label>
            </div>

            {selectedCard && (
              <div className="rounded-md bg-red-50 p-4 border border-red-200">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: selectedCard.cardTypeColor }}
                  ></div>
                  <span className="font-medium">{selectedCard.cardType}</span>
                  <span className="text-sm text-muted-foreground">({selectedCard.id})</span>
                </div>
                <p className="text-sm">会员: {selectedCard.memberName}</p>
                <p className="text-sm">有效期: {selectedCard.startDate} 至 {selectedCard.endDate || '无限期'}</p>
                <p className="text-sm">原价: ¥{selectedCard.price}</p>
                <p className="text-sm">实付: ¥{selectedCard.actualPrice}</p>
                {selectedCard.remainingDays !== null && <p className="text-sm">剩余天数: {selectedCard.remainingDays}天</p>}
                {selectedCard.remainingCount !== null && <p className="text-sm">剩余次数: {selectedCard.remainingCount}次</p>}
                {selectedCard.remainingValue !== null && <p className="text-sm">剩余金额: ¥{selectedCard.remainingValue}</p>}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button variant="destructive" onClick={() => {
              toast({
                title: "退卡成功",
                description: `会员卡 ${selectedCard?.id} 已成功退卡`,
                variant: "destructive",
              })
              setDialogType(null)
              setDialogNote("")
            }}>
              确认退卡
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导出数据对话框 */}
      <Dialog open={dialogType === "export"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>导出会员卡数据</DialogTitle>
            <DialogDescription>
              选择要导出的数据格式和字段。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label>导出格式</Label>
              <div className="flex gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox id="export-excel" defaultChecked />
                  <Label htmlFor="export-excel">Excel</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="export-csv" />
                  <Label htmlFor="export-csv">CSV</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="export-pdf" />
                  <Label htmlFor="export-pdf">PDF</Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>选择字段</Label>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox id="field-id" defaultChecked />
                  <Label htmlFor="field-id">卡号</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="field-member" defaultChecked />
                  <Label htmlFor="field-member">会员信息</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="field-type" defaultChecked />
                  <Label htmlFor="field-type">卡类型</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="field-validity" defaultChecked />
                  <Label htmlFor="field-validity">有效期</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="field-remaining" defaultChecked />
                  <Label htmlFor="field-remaining">剩余次数/天数/金额</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="field-status" defaultChecked />
                  <Label htmlFor="field-status">状态</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="field-price" />
                  <Label htmlFor="field-price">价格信息</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="field-usage" />
                  <Label htmlFor="field-usage">使用记录</Label>
                </div>
              </div>
            </div>

            <div className="rounded-md bg-muted p-4">
              <p className="text-sm font-medium">导出范围</p>
              {selectedCards.length > 0 ? (
                <p className="text-sm mt-1">将导出 {selectedCards.length} 张已选择的会员卡数据</p>
              ) : (
                <p className="text-sm mt-1">将导出当前筛选条件下的 {filteredCards.length} 张会员卡数据</p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: "导出成功",
                description: selectedCards.length > 0
                  ? `已成功导出 ${selectedCards.length} 张会员卡的数据`
                  : `已成功导出 ${filteredCards.length} 张会员卡的数据`,
              })
              setDialogType(null)
            }}>
              开始导出
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 手动激活对话框 */}
      <Dialog open={dialogType === "activate"} onOpenChange={(open) => !open && setDialogType(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>手动激活会员卡</DialogTitle>
            <DialogDescription>
              激活后，会员将可以使用此卡预约课程。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="activate-reason">激活原因</Label>
              <Input
                id="activate-reason"
                placeholder="请输入激活原因"
                value={dialogNote}
                onChange={(e) => setDialogNote(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label>激活方式</Label>
              <RadioGroup defaultValue="immediate">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="immediate" id="immediate" />
                  <Label htmlFor="immediate">立即激活</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="specific_date" id="specific_date" />
                  <Label htmlFor="specific_date">指定日期激活</Label>
                </div>
              </RadioGroup>
            </div>

            {selectedCard && (
              <div className="rounded-md bg-muted p-4">
                <div className="flex items-center gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: selectedCard.cardTypeColor }}
                  ></div>
                  <span className="font-medium">{selectedCard.cardType}</span>
                  <span className="text-sm text-muted-foreground">({selectedCard.id})</span>
                </div>
                <p className="text-sm">会员: {selectedCard.memberName}</p>
                <p className="text-sm">有效期: {selectedCard.startDate} 至 {selectedCard.endDate || '无限期'}</p>
                <p className="text-sm">当前状态: <Badge variant="outline" className="text-yellow-500">未激活</Badge></p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogType(null)}>取消</Button>
            <Button onClick={() => {
              toast({
                title: "会员卡已激活",
                description: selectedCards.length > 0
                  ? `已成功激活 ${selectedCards.length} 张会员卡`
                  : `已成功激活会员卡 ${selectedCard?.id}`,
              })
              setDialogType(null)
              setDialogNote("")
            }}>
              确认激活
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 会员卡详情对话框 */}
      <MemberCardInstanceDetailDialog
        open={detailDialogOpen}
        onOpenChange={setDetailDialogOpen}
        cardId={selectedCardId}
      />

      {/* 添加会员卡对话框 */}
      <AddMemberCardInstanceDialog
        open={addCardDialogOpen}
        onOpenChange={setAddCardDialogOpen}
        onSuccess={() => {
          toast({
            title: "会员卡添加成功",
            description: "会员卡已成功添加到系统中",
          })
          // 在实际应用中，这里应该重新加载会员卡列表数据
        }}
      />

      {/* 编辑会员卡对话框 */}
      <EditMemberCardInstanceDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        cardId={selectedCardId}
        onSuccess={() => {
          toast({
            title: "会员卡更新成功",
            description: "会员卡信息已成功更新",
          })
          // 在实际应用中，这里应该重新加载会员卡列表数据
        }}
      />

      {/* 升级会员卡对话框 */}
      <UpgradeMemberCardDialog
        open={upgradeDialogOpen}
        onOpenChange={setUpgradeDialogOpen}
        currentCard={selectedCard}
      />

      {/* 合并会员卡对话框 */}
      <MergeMemberCardDialog
        open={mergeDialogOpen}
        onOpenChange={setMergeDialogOpen}
        currentCard={selectedCard}
      />
    </div>
  )
}
