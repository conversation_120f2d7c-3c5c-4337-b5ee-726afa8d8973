"use client"

import { useEffect, useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Textarea } from "@/components/ui/textarea"

interface EditSalaryConfigDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  config: any
}

export function EditSalaryConfigDialog({ open, onOpenChange, config }: EditSalaryConfigDialogProps) {
  const [salaryType, setSalaryType] = useState<string>("")
  const [baseSalary, setBaseSalary] = useState<string>("")
  const [hourlyRate, setHourlyRate] = useState<string>("")
  const [commissionRate, setCommissionRate] = useState<string>("")
  const [isProbation, setIsProbation] = useState<boolean>(false)
  const [probationSalaryPercentage, setProbationSalaryPercentage] = useState<string>("")
  const [probationEndDate, setProbationEndDate] = useState<string>("")
  const [paymentDay, setPaymentDay] = useState<string>("")
  const [paymentDetails, setPaymentDetails] = useState<string>("")

  // 当配置数据变化时，更新表单
  useEffect(() => {
    if (config) {
      setSalaryType(config.salaryType || "")
      setBaseSalary(config.baseSalary ? config.baseSalary.toString() : "")
      setHourlyRate(config.hourlyRate ? config.hourlyRate.toString() : "")
      setCommissionRate(config.commissionRate ? config.commissionRate.toString() : "")
      setIsProbation(config.isProbation || false)
      setProbationSalaryPercentage(config.probationSalaryPercentage ? config.probationSalaryPercentage.toString() : "")
      setProbationEndDate(config.probationEndDate || "")
      setPaymentDay(config.paymentDay ? config.paymentDay.toString() : "")
      setPaymentDetails(config.paymentDetails || "")
    }
  }, [config])

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // 构建更新后的薪资配置数据
    const updatedConfig = {
      ...config,
      salaryType,
      baseSalary: baseSalary ? parseFloat(baseSalary) : 0,
      hourlyRate: hourlyRate ? parseFloat(hourlyRate) : 0,
      commissionRate: commissionRate ? parseFloat(commissionRate) : 0,
      isProbation,
      probationSalaryPercentage: probationSalaryPercentage ? parseFloat(probationSalaryPercentage) : 0,
      probationEndDate: isProbation ? probationEndDate : null,
      paymentDay: paymentDay ? parseInt(paymentDay) : 0,
      paymentDetails,
    }
    
    // 这里应该调用API更新薪资配置
    console.log("更新薪资配置:", updatedConfig)
    
    // 关闭对话框
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        {config && (
          <form onSubmit={handleSubmit}>
            <DialogHeader>
              <DialogTitle>编辑薪资配置</DialogTitle>
              <DialogDescription>
                编辑 {config.coachName} 的薪资配置
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="salary-type" className="text-right">
                  薪资类型
                </Label>
                <Select
                  value={salaryType}
                  onValueChange={setSalaryType}
                  required
                >
                  <SelectTrigger id="salary-type" className="col-span-3">
                    <SelectValue placeholder="请选择薪资类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fixed">固定薪资</SelectItem>
                    <SelectItem value="hourly">课时费</SelectItem>
                    <SelectItem value="mixed">底薪+课时费</SelectItem>
                    <SelectItem value="commission">底薪+提成</SelectItem>
                    <SelectItem value="full">底薪+课时费+提成</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {(salaryType === "fixed" || salaryType === "mixed" || salaryType === "commission" || salaryType === "full") && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="base-salary" className="text-right">
                    底薪(元/月)
                  </Label>
                  <Input
                    id="base-salary"
                    type="number"
                    min="0"
                    step="100"
                    value={baseSalary}
                    onChange={(e) => setBaseSalary(e.target.value)}
                    placeholder="请输入底薪金额"
                    className="col-span-3"
                    required
                  />
                </div>
              )}
              {(salaryType === "hourly" || salaryType === "mixed" || salaryType === "full") && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="hourly-rate" className="text-right">
                    课时费(元/课时)
                  </Label>
                  <Input
                    id="hourly-rate"
                    type="number"
                    min="0"
                    step="10"
                    value={hourlyRate}
                    onChange={(e) => setHourlyRate(e.target.value)}
                    placeholder="请输入课时费金额"
                    className="col-span-3"
                    required
                  />
                </div>
              )}
              {(salaryType === "commission" || salaryType === "full") && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="commission-rate" className="text-right">
                    提成比例(%)
                  </Label>
                  <Input
                    id="commission-rate"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={commissionRate}
                    onChange={(e) => setCommissionRate(e.target.value)}
                    placeholder="请输入提成比例"
                    className="col-span-3"
                    required
                  />
                </div>
              )}
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="text-right">试用期</div>
                <div className="flex items-center space-x-2 col-span-3">
                  <Checkbox
                    id="is-probation"
                    checked={isProbation}
                    onCheckedChange={(checked) => setIsProbation(!!checked)}
                  />
                  <label
                    htmlFor="is-probation"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    是否在试用期
                  </label>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="probation-salary-percentage" className="text-right">
                  试用期薪资比例(%)
                </Label>
                <Input
                  id="probation-salary-percentage"
                  type="number"
                  min="0"
                  max="100"
                  step="1"
                  value={probationSalaryPercentage}
                  onChange={(e) => setProbationSalaryPercentage(e.target.value)}
                  placeholder="请输入试用期薪资比例"
                  className="col-span-3"
                  required
                />
              </div>
              {isProbation && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="probation-end-date" className="text-right">
                    试用期结束日期
                  </Label>
                  <Input
                    id="probation-end-date"
                    type="date"
                    value={probationEndDate}
                    onChange={(e) => setProbationEndDate(e.target.value)}
                    className="col-span-3"
                    required
                  />
                </div>
              )}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="payment-day" className="text-right">
                  发薪日(每月几号)
                </Label>
                <Input
                  id="payment-day"
                  type="number"
                  min="1"
                  max="31"
                  step="1"
                  value={paymentDay}
                  onChange={(e) => setPaymentDay(e.target.value)}
                  placeholder="请输入发薪日"
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="payment-details" className="text-right pt-2">
                  薪资发放详情
                </Label>
                <Textarea
                  id="payment-details"
                  value={paymentDetails}
                  onChange={(e) => setPaymentDetails(e.target.value)}
                  placeholder="请输入薪资发放的详细说明，如发放方式、计算规则等"
                  className="col-span-3"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button type="submit">保存更改</Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
