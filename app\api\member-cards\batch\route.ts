import { NextRequest, NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga',
  charset: 'utf8mb4'
};

// 批量操作会员卡类型
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { ids, action, data } = body;

    // 验证必填字段
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json({
        code: 400,
        msg: '请选择要操作的会员卡类型',
        data: null
      }, { status: 400 });
    }

    if (!action) {
      return NextResponse.json({
        code: 400,
        msg: '请指定操作类型',
        data: null
      }, { status: 400 });
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      let result;
      let message = '';

      // 根据操作类型执行不同的操作
      switch (action) {
        case 'on_sale':
          // 批量上架
          const onSalePlaceholders = ids.map(() => '?').join(',');
          await connection.execute(
            `UPDATE member_card_types SET status = '销售中', updated_at = NOW() WHERE id IN (${onSalePlaceholders})`,
            ids.map(id => parseInt(id))
          );
          result = { updatedCount: ids.length };
          message = `成功上架 ${ids.length} 个会员卡类型`;
          break;

        case 'off_sale':
          // 批量下架
          const offSalePlaceholders = ids.map(() => '?').join(',');
          await connection.execute(
            `UPDATE member_card_types SET status = '已下架', updated_at = NOW() WHERE id IN (${offSalePlaceholders})`,
            ids.map(id => parseInt(id))
          );
          result = { updatedCount: ids.length };
          message = `成功下架 ${ids.length} 个会员卡类型`;
          break;

        case 'delete':
          // 批量删除
          const deletePlaceholders = ids.map(() => '?').join(',');
          await connection.execute(
            `DELETE FROM member_card_types WHERE id IN (${deletePlaceholders})`,
            ids.map(id => parseInt(id))
          );
          result = { deletedCount: ids.length };
          message = `成功删除 ${ids.length} 个会员卡类型`;
          break;

        default:
          return NextResponse.json({
            code: 400,
            msg: '不支持的操作类型',
            data: null
          }, { status: 400 });
      }

      return NextResponse.json({
        code: 0,
        data: result,
        msg: message
      });

    } finally {
      await connection.end();
    }

  } catch (error) {
    console.error('批量操作会员卡类型失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '批量操作会员卡类型失败',
      data: null
    }, { status: 500 });
  }
}