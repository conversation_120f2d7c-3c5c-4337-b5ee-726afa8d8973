"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { InfoIcon, PlusCircleIcon, TrashIcon } from "lucide-react"
import { CourseTypeSelector } from "./course-type-selector"
import { CourseAssociation } from "./course-association"
import { useToast } from "@/hooks/use-toast"

interface AddMemberCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddMemberCardDialog({ open, onOpenChange }: AddMemberCardDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("basic")
  const [cardType, setCardType] = useState("time")
  const [color, setColor] = useState("#4f46e5")
  const [isTrialCard, setIsTrialCard] = useState(false)

  // 基本信息状态变量
  const [cardName, setCardName] = useState("")
  const [cardDescription, setCardDescription] = useState("")
  const [price, setPrice] = useState("")
  const [originalPrice, setOriginalPrice] = useState("")
  const [validityDays, setValidityDays] = useState("")
  const [classCount, setClassCount] = useState("")
  const [valueAmount, setValueAmount] = useState("")
  const [limitTimeCardCount, setLimitTimeCardCount] = useState(false)
  const [timeCardMaxCount, setTimeCardMaxCount] = useState("")
  const [consumptionRule, setConsumptionRule] = useState("AVERAGE")
  const [selectedCourseTypes, setSelectedCourseTypes] = useState<string[]>([])
  const [courseConsumption, setCourseConsumption] = useState<{[key: string]: number}>({})
  const [bonusClassTimes, setBonusClassTimes] = useState(0)
  const [bonusValueCoefficient, setBonusValueCoefficient] = useState(1.0)
  const [timeSlots, setTimeSlots] = useState([
    { id: 1, startHour: "00", startMinute: "00", endHour: "00", endMinute: "00" }
  ])
  const [timeMode, setTimeMode] = useState("all_time") // all_time 或 custom_time
  const [selectedDays, setSelectedDays] = useState({
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: true,
    sunday: true
  })
  const [monthlyLimitType, setMonthlyLimitType] = useState("natural_month") // natural_month 或 card_cycle
  const [weeklyLimitType, setWeeklyLimitType] = useState("natural_week") // natural_week 或 card_cycle
  const [leaveOption, setLeaveOption] = useState("no_limit") // no_allow, no_limit, limited

  // 高级设置状态变量
  const [leaveTimes, setLeaveTimes] = useState(0)
  const [leaveDays, setLeaveDays] = useState(0)
  const [autoActivateDays, setAutoActivateDays] = useState(120)
  const [maxPeoplePerClass, setMaxPeoplePerClass] = useState(1)
  const [dailyBookingLimit, setDailyBookingLimit] = useState(3)
  const [weeklyBookingLimit, setWeeklyBookingLimit] = useState(4)
  const [monthlyBookingLimit, setMonthlyBookingLimit] = useState(5)
  const [advanceBookingDays, setAdvanceBookingDays] = useState("no_limit")

  // 用卡人设置状态变量
  const [bookingIntervalEnabled, setBookingIntervalEnabled] = useState(false)
  const [bookingIntervalMinutes, setBookingIntervalMinutes] = useState(0)
  const [pendingBookingLimit, setPendingBookingLimit] = useState(0)
  const [cancelLimitEnabled, setCancelLimitEnabled] = useState(false)
  const [cancelLimitCount, setCancelLimitCount] = useState(0)
  const [cancelLimitPeriod, setCancelLimitPeriod] = useState("week")
  const [sameCourseDaily, setSameCourseDaily] = useState(1)
  const [peakTimeEnabled, setPeakTimeEnabled] = useState(false)
  const [peakStartTime, setPeakStartTime] = useState("18:00")
  const [peakEndTime, setPeakEndTime] = useState("21:00")
  const [peakDailyLimit, setPeakDailyLimit] = useState(1)
  const [priorityEnabled, setPriorityEnabled] = useState(false)
  const [priorityHours, setPriorityHours] = useState(24)
  const [priorityDescription, setPriorityDescription] = useState("优先预约")

  // 提交状态
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 提交处理函数
  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      // 构建会员卡基本数据
      const cardData = {
        name: cardName,
        description: cardDescription,
        color: color,
        card_type: cardType,
        is_trial_card: isTrialCard,
        price: parseFloat(price) || 0,
        original_price: parseFloat(originalPrice) || 0,
        validity_days: parseInt(validityDays) || 0,
        class_count: parseInt(classCount) || 0,
        value_amount: parseFloat(valueAmount) || 0,
        status: 'active'
      }

      // 创建会员卡
      const createResponse = await fetch('/api/member-cards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cardData),
      })

      if (!createResponse.ok) {
        throw new Error('创建会员卡失败')
      }

      const createResult = await createResponse.json()
      const cardId = createResult.data.id

      // 保存课程关联和高级设置
      if (selectedCourseTypes.length > 0 || Object.keys(courseConsumption).length > 0) {
        try {
          const advancedData = {
            course: {
              consumption_rule: consumptionRule.toUpperCase(),
              gift_class_count: bonusClassTimes,
              gift_value_coefficient: bonusValueCoefficient,
              all_courses_enabled: selectedCourseTypes.length === 0
            },
            courseAssociations: selectedCourseTypes.map(courseId => ({
              course_type_id: parseInt(courseId),
              tenant_id: 2,
              is_enabled: true,
              consumption_times: courseConsumption[courseId] || 1.0
            }))
          }

          const advancedResponse = await fetch(`/api/member-cards/${cardId}/advanced-settings`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(advancedData),
          })

          if (!advancedResponse.ok) {
            console.warn('课程关联保存失败')
          }
        } catch (error) {
          console.warn('保存课程关联时出错:', error)
        }
      }

      toast({
        title: "会员卡创建成功",
        description: isTrialCard
          ? "已成功创建体验卡并关联课程"
          : "已成功创建会员卡并关联课程",
      })

      // 关闭对话框
      onOpenChange(false)

    } catch (error: any) {
      console.error('创建会员卡失败:', error)
      toast({
        title: "创建失败",
        description: error.message || "创建会员卡时发生错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 模拟获取课程类型数据
  const [courseTypes, setCourseTypes] = useState([
    { id: "1", name: "基础瑜伽", color: "#4285F4", description: "适合初学者的基础瑜伽课程" },
    { id: "2", name: "高级瑜伽", color: "#34A853", description: "适合有一定基础的学员" },
    { id: "3", name: "阴瑜伽", color: "#FBBC05", description: "缓慢温和的瑜伽风格" },
    { id: "4", name: "孕产瑜伽", color: "#EA4335", description: "专为孕妇和产后恢复设计" },
    { id: "5", name: "空中瑜伽", color: "#FF6D91", description: "使用吊床进行的瑜伽练习" },
  ])

  const colors = [
    "#4f46e5", // Indigo
    "#0ea5e9", // Sky
    "#10b981", // Emerald
    "#f59e0b", // Amber
    "#ec4899", // Pink
    "#8b5cf6", // Violet
    "#ef4444", // Red
    "#14b8a6", // Teal
    "#f97316", // Orange
    "#6366f1", // Indigo
  ]

  // 在实际应用中，应该从API获取课程类型数据
  useEffect(() => {
    // 模拟API调用
    // courseApi.getAllCourseTypes().then(response => {
    //   if (response.code === 200) {
    //     setCourseTypes(response.data);
    //   }
    // });
  }, [])

  // 处理课程类型选择变化
  const handleCourseTypesChange = (types: string[]) => {
    setSelectedCourseTypes(types)
  }

  // 处理添加新时段
  const handleAddTimeSlot = () => {
    if (timeSlots.length < 3) {
      const newId = Math.max(0, ...timeSlots.map(slot => slot.id)) + 1
      setTimeSlots([
        ...timeSlots,
        { id: newId, startHour: "00", startMinute: "00", endHour: "00", endMinute: "00" }
      ])
    } else {
      toast({
        title: "最多添加3个时段",
        description: "已达到可添加的最大时段数量",
        variant: "destructive"
      })
    }
  }

  // 处理删除时段
  const handleDeleteTimeSlot = (id: number) => {
    if (timeSlots.length > 1) {
      setTimeSlots(timeSlots.filter(slot => slot.id !== id))
    } else {
      toast({
        title: "无法删除",
        description: "至少需要保留一个时段",
        variant: "destructive"
      })
    }
  }

  // 处理时段变化
  const handleTimeSlotChange = (id: number, field: string, value: string) => {
    setTimeSlots(timeSlots.map(slot =>
      slot.id === id ? { ...slot, [field]: value } : slot
    ))
  }

  // 处理星期选择变化
  const handleDayChange = (day: "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday", checked: boolean) => {
    setSelectedDays({
      ...selectedDays,
      [day]: checked
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isTrialCard ? "添加体验卡名称" : "添加会员卡名称"}</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="course_types">关联课程</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">
                  会员卡名称 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  placeholder="请输入会员卡名称"
                  value={cardName}
                  onChange={(e) => setCardName(e.target.value)}
                />
              </div>



              <div className="space-y-2">
                <Label>会员卡颜色</Label>
                <div className="flex flex-wrap gap-2">
                  {colors.map((c) => (
                    <div
                      key={c}
                      className={`h-8 w-8 cursor-pointer rounded-full ${color === c ? "ring-2 ring-primary ring-offset-2" : ""}`}
                      style={{ backgroundColor: c }}
                      onClick={() => setColor(c)}
                    />
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="price">
                  售价（元） <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="price"
                  type="number"
                  placeholder="请输入会员卡售价"
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="original-price">原价（元）</Label>
                <Input
                  id="original-price"
                  type="number"
                  placeholder="请输入会员卡原价"
                  value={originalPrice}
                  onChange={(e) => setOriginalPrice(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label>
                  会员卡类别 <span className="text-red-500">*</span>
                </Label>
                <RadioGroup value={cardType} onValueChange={setCardType} className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="time" id="time" />
                    <Label htmlFor="time">期限卡（按有效期计费）</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="count" id="count" />
                    <Label htmlFor="count">次数卡（按次数计费）</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="value" id="value" />
                    <Label htmlFor="value">储值卡（按金额计费）</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="trial-card-switch">会员卡属性</Label>
                  <Switch
                    id="trial-card-switch"
                    checked={isTrialCard}
                    onCheckedChange={setIsTrialCard}
                  />
                  <div className="w-[60%]">
                    <p className="text-xs text-muted-foreground">
                      选择体验卡属性，通常用于新会员体验，有特殊的使用限制；不选则为普通卡
                    </p>
                  </div>
                </div>

                {cardType === "time" && (
                  <div className="space-y-2">
                    <Label htmlFor="validity">
                      有效期（天） <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="validity"
                      type="number"
                      placeholder="请输入有效期天数"
                      value={validityDays}
                      onChange={(e) => setValidityDays(e.target.value)}
                    />

                    <div className="mt-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="limit-count-switch">限制使用次数</Label>
                        <Switch
                          id="limit-count-switch"
                          checked={limitTimeCardCount}
                          onCheckedChange={setLimitTimeCardCount}
                        />
                      </div>

                      {limitTimeCardCount && (
                        <div className="mt-2">
                          <Label htmlFor="time-card-count">
                            最大使用次数 <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="time-card-count"
                            type="number"
                            min="1"
                            placeholder="请输入最大使用次数"
                            value={timeCardMaxCount}
                            onChange={(e) => setTimeCardMaxCount(e.target.value)}
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            设置期限卡的最大使用次数限制
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {cardType === "count" && (
                  <div className="space-y-2">
                    <Label htmlFor="count">
                      使用次数 <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="count"
                      type="number"
                      placeholder="请输入使用次数"
                      value={classCount}
                      onChange={(e) => setClassCount(e.target.value)}
                    />

                    <div className="mt-2">
                      <Label htmlFor="count-validity">
                        有效期（天） <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="count-validity"
                        type="number"
                        placeholder="请输入有效期天数"
                        value={validityDays}
                        onChange={(e) => setValidityDays(e.target.value)}
                      />
                    </div>
                  </div>
                )}

                {cardType === "value" && (
                  <div className="space-y-2">
                    <Label htmlFor="value-amount">
                      储值金额（元） <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="value-amount"
                      type="number"
                      placeholder="请输入储值金额"
                      value={valueAmount}
                      onChange={(e) => setValueAmount(e.target.value)}
                    />

                    <div className="mt-2">
                      <Label htmlFor="value-validity">
                        有效期（天） <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="value-validity"
                        type="number"
                        placeholder="请输入有效期天数"
                        value={validityDays}
                        onChange={(e) => setValidityDays(e.target.value)}
                      />
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">会员卡描述</Label>
                <Textarea
                  id="description"
                  placeholder="请输入会员卡描述信息"
                  rows={3}
                  value={cardDescription}
                  onChange={(e) => setCardDescription(e.target.value)}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="course_types" className="mt-4 space-y-4">
            <CourseAssociation
              cardType={cardType as "count" | "time" | "value"}
              initialData={{
                consumptionRule: consumptionRule,
                selectedCourses: selectedCourseTypes,
                giftCount: bonusClassTimes,
                giftValue: bonusValueCoefficient
              }}
              onChange={(data) => {
                setConsumptionRule(data.consumptionRule);
                setSelectedCourseTypes(data.selectedCourses);
                setBonusClassTimes(data.giftCount);
                setBonusValueCoefficient(data.giftValue);
                // 从courseSettings中提取消耗值数据
                if (data.courseSettings) {
                  const consumptionData: {[key: string]: number} = {}
                  Object.keys(data.courseSettings).forEach(courseId => {
                    consumptionData[courseId] = data.courseSettings[courseId].count
                  })
                  setCourseConsumption(consumptionData)
                }
              }}
            />
          </TabsContent>

          {/* <TabsContent value="sales" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="status-switch">立即上架</Label>
                  <Switch id="status-switch" defaultChecked />
                </div>
                <p className="text-sm text-muted-foreground">开启后，会员卡将立即在前台显示并可购买</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="featured-switch">设为推荐</Label>
                  <Switch id="featured-switch" />
                </div>
                <p className="text-sm text-muted-foreground">开启后，会员卡将在前台推荐位置显示</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sort-order">排序优先级</Label>
                <Input id="sort-order" type="number" placeholder="数字越小排序越靠前" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-sales">销售上限</Label>
                <Input id="max-sales" type="number" placeholder="最大销售数量，留空表示不限制" />
                <p className="text-sm text-muted-foreground">设置后，会员卡销售达到上限将自动下架</p>
              </div>

              <div className="space-y-2">
                <Label>购买限制</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择购买限制" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">不限制</SelectItem>
                    <SelectItem value="new">仅限新会员</SelectItem>
                    <SelectItem value="old">仅限老会员</SelectItem>
                    <SelectItem value="once">每人限购一次</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {isTrialCard && (
                <div className="space-y-2">
                  <Label>体验卡使用限制</Label>
                  <Select defaultValue="once">
                    <SelectTrigger>
                      <SelectValue placeholder="选择体验卡限制" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="once">每人限购一次</SelectItem>
                      <SelectItem value="new_only">仅限新会员购买</SelectItem>
                      <SelectItem value="time_limit">限时体验（自动到期）</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">体验卡的特殊使用限制</p>
                </div>
              )}

              <div className="space-y-2 md:col-span-2">
                <Label>使用规则</Label>
                <Textarea placeholder="请输入会员卡使用规则，如使用限制、注意事项等" />
              </div>
            </div>
          </TabsContent> */}

          <TabsContent value="advanced" className="mt-4 space-y-4">
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">卡相关设置</h3>
                  <p className="text-sm text-muted-foreground">(提示: 高级设置可设置卡的请假规则, 开卡设置, 预约限额等, 建议设置)</p>
                </div>

                <div className="space-y-4 border rounded-md p-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[120px]">请假选项:</Label>
                      <div className="flex flex-col space-y-4 w-full">
                        <div className="flex items-center space-x-4">
                          <Select
                            value={leaveOption}
                            onValueChange={(value) => setLeaveOption(value)}
                          >
                            <SelectTrigger className="w-[120px]">
                              <SelectValue placeholder="选择请假选项" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="no_allow">不允许</SelectItem>
                              <SelectItem value="no_limit">不限制</SelectItem>
                              <SelectItem value="limited">有限制</SelectItem>
                            </SelectContent>
                          </Select>

                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="right" className="max-w-sm">
                                <p>不允许：会员卡不可请假</p>
                                <p>不限制：会员卡可无限次请假</p>
                                <p>有限制：会员卡在有效期内可请假指定次数，累计指定天数</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>

                        {leaveOption === "limited" && (
                          <div className="flex items-center space-x-2 ml-2">
                            <span>有效期内可请假</span>
                            <Input
                              className="w-16 h-8"
                              type="number"
                              min="0"
                              placeholder="次"
                              value={leaveTimes}
                              onChange={(e) => setLeaveTimes(parseInt(e.target.value) || 0)}
                            />
                            <span>次, 累计</span>
                            <Input
                              className="w-16 h-8"
                              type="number"
                              min="0"
                              placeholder="天"
                              value={leaveDays}
                              onChange={(e) => setLeaveDays(parseInt(e.target.value) || 0)}
                            />
                            <span>天</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[120px]">开卡设置:</Label>
                      <span>发卡后</span>
                      <Input
                        className="w-24 h-8"
                        type="number"
                        min="0"
                        value={autoActivateDays}
                        onChange={(e) => setAutoActivateDays(parseInt(e.target.value) || 120)}
                      />
                      <span>天内未开卡则自动开卡</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>设置会员卡在发卡后多少天内未手动开卡将自动开卡</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[120px]">单节课可约人数上限:</Label>
                      <Input
                        className="w-16 h-8"
                        type="number"
                        min="1"
                        value={maxPeoplePerClass}
                        onChange={(e) => setMaxPeoplePerClass(parseInt(e.target.value) || 1)}
                      />
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>限制会员在单节课程中最多可预约的人数</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[120px]">每日可约次数上限:</Label>
                      <Input
                        className="w-16 h-8"
                        type="number"
                        min="0"
                        value={dailyBookingLimit}
                        onChange={(e) => setDailyBookingLimit(parseInt(e.target.value) || 3)}
                      />
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>限制会员每天最多可预约的课程次数</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[120px]">每周可约次数上限:</Label>
                      <Input
                        className="w-16 h-8"
                        type="number"
                        min="0"
                        value={weeklyBookingLimit}
                        onChange={(e) => setWeeklyBookingLimit(parseInt(e.target.value) || 4)}
                      />
                      <Select
                        value={weeklyLimitType}
                        onValueChange={setWeeklyLimitType}
                      >
                        <SelectTrigger className="w-[150px] h-8">
                          <SelectValue placeholder="选择计算周期" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="natural_week">自然周计算</SelectItem>
                          <SelectItem value="card_cycle">购卡周期计算</SelectItem>
                        </SelectContent>
                      </Select>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right" className="max-w-md">
                            <p className="font-medium">计算周期说明：</p>
                            <p>自然周计算：每周一重置预约次数，不管会员何时购卡</p>
                            <p>购卡周期计算：从购卡日开始计算，每7天为一个周期，周期结束后重置预约次数</p>
                            <p className="mt-2 text-xs">例如：设置为4次/周，购卡周期模式下，会员周三购卡，则可在购卡后7天内预约4次课程</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[120px]">每月可约次数上限:</Label>
                      <Input
                        className="w-16 h-8"
                        type="number"
                        min="0"
                        value={monthlyBookingLimit}
                        onChange={(e) => setMonthlyBookingLimit(parseInt(e.target.value) || 5)}
                      />
                      <Select
                        value={monthlyLimitType}
                        onValueChange={setMonthlyLimitType}
                      >
                        <SelectTrigger className="w-[150px] h-8">
                          <SelectValue placeholder="选择计算周期" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="natural_month">自然月计算</SelectItem>
                          <SelectItem value="card_cycle">购卡周期计算</SelectItem>
                        </SelectContent>
                      </Select>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right" className="max-w-md">
                            <p className="font-medium">计算周期说明：</p>
                            <p>自然月计算：每月1号重置预约次数，不管会员何时购卡</p>
                            <p>购卡周期计算：从购卡日开始计算，每30天为一个周期，周期结束后重置预约次数</p>
                            <p className="mt-2 text-xs">例如：设置为5次/月，购卡周期模式下，会员5月18日购卡，则可在5月18日至6月17日期间预约5次课程</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[120px]">可预约天数:</Label>
                      <span>会员最多可预约</span>
                      <Select
                        value={advanceBookingDays}
                        onValueChange={setAdvanceBookingDays}
                      >
                        <SelectTrigger className="w-[120px] h-8">
                          <SelectValue placeholder="选择限制" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="no_limit">不限制</SelectItem>
                          <SelectItem value="7">7天</SelectItem>
                          <SelectItem value="14">14天</SelectItem>
                          <SelectItem value="30">30天</SelectItem>
                        </SelectContent>
                      </Select>
                      <span>天的课程, 0表示当天</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>限制会员最多可提前预约多少天内的课程</p>
                            <p>例如：设置为7天，则会员只能预约未来7天内的课程</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[120px]">可用时间:</Label>
                      <div className="flex flex-col space-y-4 w-full">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="custom_time_enabled"
                            checked={timeMode === "custom_time"}
                            onCheckedChange={(checked) => {
                              setTimeMode(checked ? "custom_time" : "all_time");
                            }}
                          />
                          <Label htmlFor="custom_time_enabled">启用自定义时段</Label>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="right" className="max-w-sm">
                                <p>关闭：会员卡可用于所有时间段的课程</p>
                                <p>开启：限制会员卡只能用于特定时间段的课程</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>

                        {timeMode === "custom_time" && (
                          <div className="ml-7 space-y-4">
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">可用时间内才可预约</Label>
                              <div className="flex flex-wrap gap-2">
                                {[
                                  { key: 'monday', label: '周一' },
                                  { key: 'tuesday', label: '周二' },
                                  { key: 'wednesday', label: '周三' },
                                  { key: 'thursday', label: '周四' },
                                  { key: 'friday', label: '周五' },
                                  { key: 'saturday', label: '周六' },
                                  { key: 'sunday', label: '周日' }
                                ].map((day) => (
                                  <div key={day.key} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`day-${day.key}`}
                                      checked={selectedDays[day.key as keyof typeof selectedDays]}
                                      onCheckedChange={(checked) => handleDayChange(day.key as "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday", !!checked)}
                                    />
                                    <Label htmlFor={`day-${day.key}`} className="text-sm">{day.label}</Label>
                                  </div>
                                ))}
                              </div>
                            </div>

                            <div className="space-y-2">
                              <Label className="text-sm font-medium">可用时段（最多3个）</Label>
                              <div className="space-y-2">
                                {timeSlots.map((slot) => (
                                  <div key={slot.id} className="flex items-center space-x-2">
                                    <Select
                                      value={slot.startHour}
                                      onValueChange={(value) => handleTimeSlotChange(slot.id, 'startHour', value)}
                                    >
                                      <SelectTrigger className="w-[70px]">
                                        <SelectValue placeholder="时" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {Array.from({ length: 24 }).map((_, i) => (
                                          <SelectItem key={i} value={i.toString().padStart(2, '0')}>
                                            {i.toString().padStart(2, '0')}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <span>:</span>
                                    <Select
                                      value={slot.startMinute}
                                      onValueChange={(value) => handleTimeSlotChange(slot.id, 'startMinute', value)}
                                    >
                                      <SelectTrigger className="w-[70px]">
                                        <SelectValue placeholder="分" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {['00', '15', '30', '45'].map((min) => (
                                          <SelectItem key={min} value={min}>{min}</SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <span>至</span>
                                    <Select
                                      value={slot.endHour}
                                      onValueChange={(value) => handleTimeSlotChange(slot.id, 'endHour', value)}
                                    >
                                      <SelectTrigger className="w-[70px]">
                                        <SelectValue placeholder="时" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {Array.from({ length: 24 }).map((_, i) => (
                                          <SelectItem key={i} value={i.toString().padStart(2, '0')}>
                                            {i.toString().padStart(2, '0')}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <span>:</span>
                                    <Select
                                      value={slot.endMinute}
                                      onValueChange={(value) => handleTimeSlotChange(slot.id, 'endMinute', value)}
                                    >
                                      <SelectTrigger className="w-[70px]">
                                        <SelectValue placeholder="分" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {['00', '15', '30', '45'].map((min) => (
                                          <SelectItem key={min} value={min}>{min}</SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-8 w-8"
                                      onClick={() => handleDeleteTimeSlot(slot.id)}
                                    >
                                      <TrashIcon className="h-4 w-4" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                              {timeSlots.length < 3 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mt-2"
                                  onClick={handleAddTimeSlot}
                                >
                                  <PlusCircleIcon className="h-4 w-4 mr-2" />
                                  新增可用时段
                                </Button>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">用卡人相关设置</h3>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="user_settings_section_enabled"
                      checked={bookingIntervalEnabled || cancelLimitEnabled || peakTimeEnabled || priorityEnabled}
                      onCheckedChange={(checked) => {
                        const userSettingsSection = document.getElementById('user-settings-section');
                        if (userSettingsSection) {
                          userSettingsSection.style.display = checked ? 'block' : 'none';
                        }
                        if (!checked) {
                          // 如果关闭，重置所有用卡人设置
                          setBookingIntervalEnabled(false)
                          setCancelLimitEnabled(false)
                          setPeakTimeEnabled(false)
                          setPriorityEnabled(false)
                        }
                      }}
                    />
                    <Label htmlFor="user_settings_section_enabled">展开设置</Label>
                  </div>
                </div>

                <div
                  id="user-settings-section"
                  className="space-y-4 border rounded-md p-4"
                  style={{
                    display: (bookingIntervalEnabled || cancelLimitEnabled || peakTimeEnabled || priorityEnabled) ? 'block' : 'none'
                  }}
                >
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[160px]">每日约课间隔时间限制:</Label>
                      <Input
                        className="w-24 h-8"
                        type="number"
                        min="0"
                        placeholder="不限制"
                        value={bookingIntervalMinutes}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 0
                          setBookingIntervalMinutes(value)
                          setBookingIntervalEnabled(value > 0)
                        }}
                      />
                      <span>分钟内不可连续约课</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>限制会员在预约课程后，需要等待多少分钟才能预约下一节课</p>
                            <p>防止会员短时间内连续预约多节课程</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[160px]">预约次数限制:</Label>
                      <span>未结束课程预约次数限制为</span>
                      <Input
                        className="w-24 h-8"
                        type="number"
                        min="0"
                        value={pendingBookingLimit}
                        onChange={(e) => setPendingBookingLimit(parseInt(e.target.value) || 0)}
                      />
                      <span>次, 0表示不限制</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>限制会员同时可预约但尚未上课的课程数量</p>
                            <p>例如：设置为3，则会员最多可同时预约3节未上的课程</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[160px]">会员取消预约次数限制:</Label>
                      <Input
                        className="w-24 h-8"
                        type="number"
                        min="0"
                        value={cancelLimitCount}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 0
                          setCancelLimitCount(value)
                          setCancelLimitEnabled(value > 0)
                        }}
                      />
                      <span>次, 每</span>
                      <Select
                        value={cancelLimitPeriod}
                        onValueChange={setCancelLimitPeriod}
                      >
                        <SelectTrigger className="w-[100px] h-8">
                          <SelectValue placeholder="选择周期" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="day">天</SelectItem>
                          <SelectItem value="week">周</SelectItem>
                          <SelectItem value="month">月</SelectItem>
                        </SelectContent>
                      </Select>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>限制会员在指定周期内可取消预约的次数</p>
                            <p>例如：设置为3次/周，则会员每周最多可取消3次预约</p>
                            <p>超出限制后将无法取消预约，需等待下一个周期</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[160px]">同类课程每日限制:</Label>
                      <Input
                        className="w-24 h-8"
                        type="number"
                        min="1"
                        value={sameCourseDaily}
                        onChange={(e) => setSameCourseDaily(parseInt(e.target.value) || 1)}
                      />
                      <span>次/天</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="right" className="max-w-md">
                            <p className="font-medium">同类课程每日限制说明：</p>
                            <p>限制会员每天最多可预约同一类型课程的次数</p>
                            <p>例如：设置为1次/天，则会员每天只能预约一节特定类型的课程，即使有多个班次</p>
                            <p className="mt-2 text-xs">适用场景：当您增加同类课程班次时，希望让更多不同的会员都能参与课程，避免少数会员占用多个班次</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[160px]">高峰时段预约限制:</Label>
                      <div className="flex flex-col space-y-4 w-full">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="peak_time_enabled"
                            checked={peakTimeEnabled}
                            onCheckedChange={(checked) => {
                              setPeakTimeEnabled(checked)
                              const peakTimeSettings = document.getElementById('peak-time-settings');
                              if (peakTimeSettings) {
                                peakTimeSettings.style.display = checked ? 'flex' : 'none';
                              }
                            }}
                          />
                          <Label htmlFor="peak_time_enabled">启用高峰时段限制</Label>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="right" className="max-w-md">
                                <p className="font-medium">高峰时段预约限制说明：</p>
                                <p>限制会员在指定的高峰时段内最多可预约的课程数量</p>
                                <p>例如：设置为18:00-21:00最多1节课，则会员在这个时间段内只能预约一节课程</p>
                                <p className="mt-2 text-xs">适用场景：避免会员在最受欢迎的时间段占用过多课程资源，让更多会员有机会参与</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>

                        <div id="peak-time-settings" className="flex items-center space-x-2 ml-7" style={{ display: 'none' }}>
                          <span>每天</span>
                          <Select
                            value={peakStartTime.split(':')[0]}
                            onValueChange={(value) => setPeakStartTime(`${value}:00`)}
                          >
                            <SelectTrigger className="w-[80px] h-8">
                              <SelectValue placeholder="开始时间" />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({ length: 24 }).map((_, i) => (
                                <SelectItem key={i} value={i.toString()}>{i.toString().padStart(2, '0')}:00</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <span>至</span>
                          <Select
                            value={peakEndTime.split(':')[0]}
                            onValueChange={(value) => setPeakEndTime(`${value}:00`)}
                          >
                            <SelectTrigger className="w-[80px] h-8">
                              <SelectValue placeholder="结束时间" />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({ length: 24 }).map((_, i) => (
                                <SelectItem key={i} value={i.toString()}>{i.toString().padStart(2, '0')}:00</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <span>最多可预约</span>
                          <Input
                            className="w-24 h-8"
                            type="number"
                            min="1"
                            value={peakDailyLimit}
                            onChange={(e) => setPeakDailyLimit(parseInt(e.target.value) || 1)}
                          />
                          <span>节课</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label className="min-w-[160px]">预约优先级设置:</Label>
                      <div className="flex flex-col space-y-4 w-full">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="priority_enabled"
                            checked={priorityEnabled}
                            onCheckedChange={(checked) => {
                              setPriorityEnabled(checked)
                              const prioritySettings = document.getElementById('priority-settings');
                              if (prioritySettings) {
                                prioritySettings.style.display = checked ? 'flex' : 'none';
                              }
                            }}
                          />
                          <Label htmlFor="priority_enabled">启用预约优先级</Label>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="right" className="max-w-md">
                                <p className="font-medium">预约优先级设置说明：</p>
                                <p>给予尚未参加过特定类型课程的会员优先预约权</p>
                                <p>例如：设置为24小时，则本周尚未参加此类课程的会员将有24小时的优先预约窗口期</p>
                                <p className="mt-2 text-xs">适用场景：确保所有会员都有公平机会参加热门课程，避免少数会员反复预约同类课程</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>

                        <div id="priority-settings" className="flex items-center space-x-2 ml-7" style={{ display: 'none' }}>
                          <span>本周尚未参加此类课程的会员优先预约</span>
                          <Input
                            className="w-24 h-8"
                            type="number"
                            min="1"
                            value={priorityHours}
                            onChange={(e) => setPriorityHours(parseInt(e.target.value) || 24)}
                          />
                          <span>小时</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          {activeTab !== "basic" && (
            <Button
              variant="outline"
              onClick={() =>
                setActiveTab((prev) => {
                  const tabs = ["basic", "course_types", "sales", "advanced"]
                  const currentIndex = tabs.indexOf(prev)
                  return tabs[currentIndex - 1]
                })
              }
            >
              上一步
            </Button>
          )}
          {activeTab !== "advanced" ? (
            <Button
              onClick={() =>
                setActiveTab((prev) => {
                  const tabs = ["basic", "course_types", "sales", "advanced"]
                  const currentIndex = tabs.indexOf(prev)
                  return tabs[currentIndex + 1]
                })
              }
            >
              下一步
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              保存
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

