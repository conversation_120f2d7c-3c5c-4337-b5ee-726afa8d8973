"use client"

import { EnhancedHomePage } from "./enhanced-home-page"

export default function MemberMiniProgramPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col items-center">
        <h1 className="text-2xl font-semibold mb-6">会员端小程序预览</h1>

        <div className="flex gap-4 mb-6">
          <a
            href="/member-mini-program/draggable-enhanced"
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            打开可拖动预览
          </a>
        </div>

        <div className="relative w-[375px] h-[812px] bg-white rounded-[40px] shadow-xl overflow-hidden border-8 border-black">
          <div className="absolute top-0 left-0 right-0 h-6 bg-black flex justify-center items-end pb-1">
            <div className="w-20 h-4 bg-black rounded-b-xl"></div>
          </div>

          <div className="h-full overflow-y-auto pt-6">
            <div className="h-6 px-4 flex items-center justify-between text-xs">
              <span>9:41</span>
              <div className="flex items-center gap-1">
                <div className="w-4 h-2 bg-black rounded-sm"></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
                <div className="w-2 h-2 bg-black rounded-full"></div>
              </div>
            </div>
            <EnhancedHomePage />
          </div>

          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-1/3 h-1 bg-black rounded-full"></div>
        </div>
      </div>
    </div>
  )
}
