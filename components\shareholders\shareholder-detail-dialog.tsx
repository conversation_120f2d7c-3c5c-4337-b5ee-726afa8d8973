"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  User,
  Phone,
  Calendar,
  Tag,
  Users,
  Wallet,
  Share2,
  MessageSquare,
  FileText,
  BarChart3,
  TrendingUp,
  Clock,
} from "lucide-react"
import { ContractIntegration } from "@/components/shareholders/contract-integration"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface ShareholderDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  shareholder: any
}

export function ShareholderDetailDialog({
  open,
  on<PERSON><PERSON><PERSON><PERSON><PERSON>,
  shareholder,
}: ShareholderDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("overview")

  if (!shareholder) return null

  // 模拟分红记录数据
  const dividendRecords = [
    {
      id: "1",
      period: "2023年5月",
      amount: 520,
      status: "已发放",
      referrals: 5,
      referralAmount: 10400,
      paidAt: "2023-06-05",
    },
    {
      id: "2",
      period: "2023年4月",
      amount: 480,
      status: "已发放",
      referrals: 4,
      referralAmount: 9600,
      paidAt: "2023-05-05",
    },
    {
      id: "3",
      period: "2023年3月",
      amount: 550,
      status: "已发放",
      referrals: 6,
      referralAmount: 11000,
      paidAt: "2023-04-05",
    },
  ]

  // 模拟引流客户数据
  const referralRecords = [
    {
      id: "1",
      customerName: "客户A",
      phone: "139****1234",
      joinDate: "2023-05-10",
      consumptionAmount: 2500,
      status: "活跃",
    },
    {
      id: "2",
      customerName: "客户B",
      phone: "138****5678",
      joinDate: "2023-05-15",
      consumptionAmount: 1800,
      status: "活跃",
    },
    {
      id: "3",
      customerName: "客户C",
      phone: "137****9012",
      joinDate: "2023-05-20",
      consumptionAmount: 2200,
      status: "活跃",
    },
    {
      id: "4",
      customerName: "客户D",
      phone: "136****3456",
      joinDate: "2023-05-25",
      consumptionAmount: 1900,
      status: "活跃",
    },
    {
      id: "5",
      customerName: "客户E",
      phone: "135****7890",
      joinDate: "2023-05-30",
      consumptionAmount: 2000,
      status: "活跃",
    },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>股东详情</DialogTitle>
          <DialogDescription>
            查看股东的详细信息、分红记录和引流客户
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center gap-4 py-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={shareholder.avatar} alt={shareholder.name} />
            <AvatarFallback>{shareholder.name[0]}</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-xl font-semibold">{shareholder.name}</h2>
            <div className="flex items-center gap-2 mt-1">
              <Badge>{shareholder.type}</Badge>
              <Badge variant={shareholder.status === "active" ? "default" : "secondary"}>
                {shareholder.status === "active" ? "活跃" : "不活跃"}
              </Badge>
            </div>
          </div>
        </div>

        <Tabs defaultValue="overview" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="overview">基本信息</TabsTrigger>
            <TabsTrigger value="dividends">分红记录</TabsTrigger>
            <TabsTrigger value="referrals">引流客户</TabsTrigger>
            <TabsTrigger value="contracts">电子合同</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-2 gap-4 mt-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">基本信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-muted-foreground mr-2">姓名:</span>
                    <span>{shareholder.name}</span>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-muted-foreground mr-2">电话:</span>
                    <span>{shareholder.phone}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-muted-foreground mr-2">加入日期:</span>
                    <span>{shareholder.joinDate}</span>
                  </div>
                  <div className="flex items-start">
                    <Tag className="h-4 w-4 mr-2 text-muted-foreground mt-1" />
                    <span className="text-muted-foreground mr-2">标签:</span>
                    <div className="flex flex-wrap gap-1">
                      {shareholder.tags.map((tag: string) => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">业绩概览</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-muted-foreground mr-2">引流客户:</span>
                    <span>{shareholder.referrals}人</span>
                  </div>
                  <div className="flex items-center">
                    <Wallet className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-muted-foreground mr-2">累计分红:</span>
                    <span>¥{shareholder.totalDividend.toFixed(2)}</span>
                  </div>
                  <div className="flex items-center">
                    <TrendingUp className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-muted-foreground mr-2">本月业绩:</span>
                    <span>¥5,200.00</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span className="text-muted-foreground mr-2">最近活动:</span>
                    <span>2023-06-15</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">业绩趋势</CardTitle>
                <CardDescription>近6个月业绩趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-60 flex items-center justify-center bg-muted/20 rounded-md">
                  <BarChart3 className="h-16 w-16 text-muted" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">备注</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  该股东是我们的长期合作伙伴，主要通过社交媒体为我们引流客户。
                  客户满意度高，推荐的客户续约率也很高。
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="dividends">
            <Card>
              <CardHeader>
                <CardTitle>分红记录</CardTitle>
                <CardDescription>股东的历史分红记录</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>分红周期</TableHead>
                      <TableHead>分红金额</TableHead>
                      <TableHead>引流客户</TableHead>
                      <TableHead>客户消费</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>发放日期</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dividendRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>{record.period}</TableCell>
                        <TableCell>¥{record.amount.toFixed(2)}</TableCell>
                        <TableCell>{record.referrals}人</TableCell>
                        <TableCell>¥{record.referralAmount.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant={record.status === "已发放" ? "default" : "secondary"}>
                            {record.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{record.paidAt}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="referrals">
            <Card>
              <CardHeader>
                <CardTitle>引流客户</CardTitle>
                <CardDescription>股东推荐的客户列表</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>客户姓名</TableHead>
                      <TableHead>联系电话</TableHead>
                      <TableHead>加入日期</TableHead>
                      <TableHead>消费金额</TableHead>
                      <TableHead>状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {referralRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>{record.customerName}</TableCell>
                        <TableCell>{record.phone}</TableCell>
                        <TableCell>{record.joinDate}</TableCell>
                        <TableCell>¥{record.consumptionAmount.toFixed(2)}</TableCell>
                        <TableCell>
                          <Badge variant={record.status === "活跃" ? "default" : "secondary"}>
                            {record.status}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contracts">
            <ContractIntegration
              shareholderId={shareholder.id}
              shareholderType={shareholder.type}
              shareholderName={shareholder.name}
            />
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Share2 className="mr-2 h-4 w-4" />
              引流记录
            </Button>
            <Button variant="outline" size="sm">
              <MessageSquare className="mr-2 h-4 w-4" />
              发送消息
            </Button>
            <Button variant="outline" size="sm">
              <FileText className="mr-2 h-4 w-4" />
              导出数据
            </Button>
          </div>
          <Button onClick={() => onOpenChange(false)}>关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
