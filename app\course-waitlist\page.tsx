"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Calendar, Search, Users, ArrowRight, X, Filter, RefreshCw, Bell, ArrowUp, ArrowDown, MessageSquare } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePicker } from "@/components/date-picker"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip"
import { format } from "date-fns"
import { coursesData, waitlistData, waitlistSettings as mockWaitlistSettings } from "./mock-data"

export default function CourseWaitlistPage() {
  // 状态管理
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  const [selectedCourse, setSelectedCourse] = useState<any>(null)
  const [showWaitlistDialog, setShowWaitlistDialog] = useState(false)
  const [showSettingsDialog, setShowSettingsDialog] = useState(false)
  const [showNotifyDialog, setShowNotifyDialog] = useState(false)
  const [selectedMember, setSelectedMember] = useState<any>(null)
  const [notificationMessage, setNotificationMessage] = useState("")
  const [waitlistSettings, setWaitlistSettings] = useState({
    maxWaitlistLength: mockWaitlistSettings.maxWaitlistSize,
    notificationPeriod: mockWaitlistSettings.notificationExpireHours,
    autoCleanupExpired: mockWaitlistSettings.autoNotify,
    priorityRules: {
      vipMembers: mockWaitlistSettings.priorityRules.some(rule => rule.level === "钻石会员" || rule.level === "金卡会员"),
      seniorMembers: true,
      frequentAttenders: true,
    }
  })

  // 过滤课程
  const filteredCourses = coursesData.filter(course => {
    // 基本搜索过滤
    const searchFilter =
      searchQuery === "" ||
      course.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.teacher.toLowerCase().includes(searchQuery.toLowerCase()) ||
      course.venue.toLowerCase().includes(searchQuery.toLowerCase());

    // 标签页过滤
    const tabFilter =
      activeTab === "all" ||
      (activeTab === "full" && course.status === "full") ||
      (activeTab === "available" && course.status === "available");

    // 日期过滤
    try {
      const courseDate = new Date(course.time.split(" ")[0]);
      const dateFilter = !selectedDate ||
        (courseDate.getFullYear() === selectedDate.getFullYear() &&
         courseDate.getMonth() === selectedDate.getMonth() &&
         courseDate.getDate() === selectedDate.getDate());
      return searchFilter && tabFilter && dateFilter;
    } catch (error) {
      console.error("日期解析错误:", course.time, error);
      return searchFilter && tabFilter;
    }
  });

  // 初始化和调试日志
  useEffect(() => {
    // 设置日期为2025-05-15
    const targetDate = new Date("2025-05-15");
    setSelectedDate(targetDate);

    console.log("课程数据加载:", coursesData.length, "条记录");
    console.log("排队数据加载:", waitlistData.length, "条记录");
    console.log("当前日期:", targetDate.toISOString().split('T')[0]);
  }, []);

  // 监听筛选结果变化
  useEffect(() => {
    console.log("筛选结果更新:", filteredCourses.length, "条记录");
    console.log("筛选条件:", {
      searchQuery,
      activeTab,
      selectedDate: selectedDate ? selectedDate.toISOString().split('T')[0] : null
    });
  }, [filteredCourses.length, searchQuery, activeTab, selectedDate]);

  // 获取课程的排队列表
  const getWaitlistForCourse = (courseId: string) => {
    return waitlistData
      .filter(item => item.courseId === courseId)
      .sort((a, b) => a.position - b.position);
  };

  // 查看排队列表
  const handleViewWaitlist = (course: any) => {
    setSelectedCourse(course);
    setShowWaitlistDialog(true);
  };

  // 通知会员
  const handleNotifyMember = (member: any) => {
    setSelectedMember(member);
    const template = mockWaitlistSettings.defaultMessage;
    const message = template.replace('{courseName}', selectedCourse?.name);
    setNotificationMessage(message);
    setShowNotifyDialog(true);
  };

  // 确认通知
  const confirmNotify = () => {
    // 实际应用中，这里应该调用API发送通知
    // 这里仅做模拟
    toast({
      title: "通知已发送",
      description: `已成功通知 ${selectedMember.memberName}`,
    });
    setShowNotifyDialog(false);
  };

  // 调整排队顺序
  const adjustPosition = (memberId: string, direction: "up" | "down") => {
    if (!selectedCourse) return;

    // 获取当前课程的排队列表
    const waitlist = [...waitlistData];

    // 找到要调整的会员在排队列表中的索引
    const memberIndex = waitlist.findIndex(item =>
      item.courseId === selectedCourse.id && item.memberId === memberId
    );

    if (memberIndex === -1) return;

    // 找到要交换位置的会员索引
    const targetIndex = direction === "up"
      ? waitlist.findIndex(item =>
          item.courseId === selectedCourse.id && item.position === waitlist[memberIndex].position - 1
        )
      : waitlist.findIndex(item =>
          item.courseId === selectedCourse.id && item.position === waitlist[memberIndex].position + 1
        );

    if (targetIndex === -1) return;

    // 交换位置
    const tempPosition = waitlist[memberIndex].position;
    waitlist[memberIndex].position = waitlist[targetIndex].position;
    waitlist[targetIndex].position = tempPosition;

    // 更新排队数据
    // 在实际应用中，这里应该调用API更新数据库
    // 这里我们直接修改 waitlistData 数组
    waitlistData.forEach((item, index) => {
      if (index === memberIndex || index === targetIndex) {
        item.position = waitlist[index].position;
      }
    });

    // 强制重新渲染
    setSelectedCourse({...selectedCourse});

    // 显示成功消息
    toast({
      title: "排队顺序已调整",
      description: direction === "up" ? "会员排队顺序提前" : "会员排队顺序推后",
    });
  };

  // 取消排队
  const cancelWaitlist = (memberId: string) => {
    if (!selectedCourse) return;

    // 找到要取消的排队记录索引
    const cancelIndex = waitlistData.findIndex(item =>
      item.courseId === selectedCourse.id && item.memberId === memberId
    );

    if (cancelIndex === -1) return;

    // 获取被取消的排队记录的位置
    const cancelledPosition = waitlistData[cancelIndex].position;

    // 从排队列表中移除该记录
    // 在实际应用中，这里应该调用API更新数据库
    // 这里我们直接修改 waitlistData 数组
    const updatedWaitlistData = waitlistData.filter((_, index) => index !== cancelIndex);

    // 更新后面排队会员的位置
    updatedWaitlistData.forEach(item => {
      if (item.courseId === selectedCourse.id && item.position > cancelledPosition) {
        item.position -= 1;
      }
    });

    // 更新全局 waitlistData
    // 注意：在实际应用中，这应该通过 API 调用来完成
    // 这里我们直接替换原数组内容
    waitlistData.length = 0;
    waitlistData.push(...updatedWaitlistData);

    // 更新课程的排队人数
    const updatedCourse = {...selectedCourse};
    updatedCourse.waitlistCount -= 1;

    // 更新状态
    setSelectedCourse(updatedCourse);

    // 更新课程数据中的排队人数
    const courseIndex = coursesData.findIndex(course => course.id === selectedCourse.id);
    if (courseIndex !== -1) {
      coursesData[courseIndex].waitlistCount -= 1;
    }

    // 显示成功消息
    toast({
      title: "已取消排队",
      description: "会员已从排队列表中移除",
    });
  };

  // 批量通知
  const notifyAll = () => {
    // 实际应用中，这里应该调用API批量通知
    // 这里仅做模拟
    toast({
      title: "批量通知已发送",
      description: `已通知 ${getWaitlistForCourse(selectedCourse.id).length} 位排队会员`,
    });
  };

  // 保存排队设置
  const saveWaitlistSettings = () => {
    // 实际应用中，这里应该调用API保存设置
    // 这里仅做模拟
    toast({
      title: "设置已保存",
      description: "排队系统设置已更新",
    });
    setShowSettingsDialog(false);
  };

  // 获取优先级标签颜色
  const getPriorityBadgeVariant = (priority: string) => {
    switch (priority) {
      case "high":
        return "default";
      case "medium":
        return "secondary";
      case "low":
        return "outline";
      default:
        return "secondary";
    }
  };

  // 获取优先级显示文本
  const getPriorityText = (priority: string) => {
    switch (priority) {
      case "high":
        return "高";
      case "medium":
        return "中";
      case "low":
        return "低";
      default:
        return "未知";
    }
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">课程排队管理</h1>
            <p className="text-muted-foreground">
              管理满班课程的排队情况，处理名额释放
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowSettingsDialog(true)}>
              <Filter className="mr-2 h-4 w-4" />
              排队规则设置
            </Button>
            <Button onClick={() => window.location.href = "/premium-services/staff-booking"}>
              <Calendar className="mr-2 h-4 w-4" />
              新建预约
            </Button>
          </div>
        </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>筛选条件</CardTitle>
          <CardDescription>设置筛选条件查询课程</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-end">
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="search">搜索</Label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="课程名称/教练/场地..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="date">日期</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="date"
                  type="date"
                  value={selectedDate ? selectedDate.toISOString().split('T')[0] : '2025-05-15'}
                  onChange={(e) => {
                    const dateStr = e.target.value;
                    if (dateStr) {
                      setSelectedDate(new Date(dateStr));
                    } else {
                      setSelectedDate(new Date("2025-05-15"));
                    }
                  }}
                />
              </div>
            </div>

            <div className="flex gap-2">
              <Button variant="default" onClick={() => {
                // 显示查询结果
                toast({
                  title: "查询成功",
                  description: `找到 ${filteredCourses.length} 条记录`,
                });
              }}>
                查询
              </Button>
              <Button variant="outline" onClick={() => {
                setSearchQuery("");
                setSelectedDate(new Date());
                setActiveTab("all");

                // 显示重置结果
                toast({
                  title: "已重置筛选条件",
                  description: `显示全部 ${coursesData.length} 条记录`,
                });
              }}>
                重置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="all">全部课程</TabsTrigger>
          <TabsTrigger value="full">已满课程</TabsTrigger>
          <TabsTrigger value="available">可约课程</TabsTrigger>
        </TabsList>
      </Tabs>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>课程信息</TableHead>
                <TableHead>时间</TableHead>
                <TableHead>教练</TableHead>
                <TableHead>场地</TableHead>
                <TableHead>预约情况</TableHead>
                <TableHead>排队人数</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCourses.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    没有找到符合条件的课程
                  </TableCell>
                </TableRow>
              ) : (
                filteredCourses.map((course) => (
                  <TableRow key={course.id}>
                    <TableCell>
                      <div className="font-medium">{course.name}</div>
                      <div className="text-xs text-muted-foreground">ID: {course.id}</div>
                    </TableCell>
                    <TableCell>{course.time}</TableCell>
                    <TableCell>{course.teacher}</TableCell>
                    <TableCell>{course.venue}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span>{course.booked}/{course.capacity}</span>
                        <Badge variant={course.status === "full" ? "destructive" : "default"}>
                          {course.status === "full" ? "已满" : "可约"}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      {course.waitlistCount > 0 ? (
                        <Badge variant="secondary">{course.waitlistCount} 人</Badge>
                      ) : (
                        <span className="text-muted-foreground">无</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewWaitlist(course)}
                        disabled={course.waitlistCount === 0}
                      >
                        查看排队
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 排队列表对话框 */}
      <Dialog open={showWaitlistDialog} onOpenChange={setShowWaitlistDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>{selectedCourse?.name} - 排队列表</DialogTitle>
            <DialogDescription>
              课程时间: {selectedCourse?.time} | 容量: {selectedCourse?.booked}/{selectedCourse?.capacity} | 排队人数: {selectedCourse?.waitlistCount}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-auto py-4">
            {selectedCourse && getWaitlistForCourse(selectedCourse.id).length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                当前课程没有排队记录
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12 text-center">位置</TableHead>
                      <TableHead className="w-48">会员信息</TableHead>
                      <TableHead className="w-40">排队时间</TableHead>
                      <TableHead className="w-20 text-center">优先级</TableHead>
                      <TableHead className="w-24">状态</TableHead>
                      <TableHead className="w-40">备注</TableHead>
                      <TableHead className="w-32 text-center">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {selectedCourse && getWaitlistForCourse(selectedCourse.id).map((waitlist) => (
                      <TableRow key={waitlist.id}>
                        <TableCell className="text-center">
                          <div className="font-medium">{waitlist.position}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={waitlist.memberAvatar} alt={waitlist.memberName} />
                              <AvatarFallback>{waitlist.memberName.slice(0, 1)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{waitlist.memberName}</div>
                              <div className="text-xs text-muted-foreground">{waitlist.memberPhone}</div>
                              <div className="text-xs text-muted-foreground">{waitlist.memberLevel}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">{waitlist.waitlistTime}</div>
                        </TableCell>
                        <TableCell className="text-center">
                          <Badge variant={getPriorityBadgeVariant(waitlist.priority)}>
                            {getPriorityText(waitlist.priority)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {waitlist.status === "waiting" ? "等待中" : "已通知"}
                          </div>
                          {waitlist.lastNotificationTime && (
                            <div className="text-xs text-muted-foreground">
                              通知时间: {waitlist.lastNotificationTime}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm max-w-[150px] truncate" title={waitlist.note || ""}>
                            {waitlist.note || "-"}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center justify-center gap-1">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                  onClick={() => adjustPosition(waitlist.memberId, "up")}
                                  disabled={waitlist.position === 1}
                                >
                                  <ArrowUp className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>上移</TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                  onClick={() => adjustPosition(waitlist.memberId, "down")}
                                  disabled={waitlist.position === getWaitlistForCourse(selectedCourse.id).length}
                                >
                                  <ArrowDown className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>下移</TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                  onClick={() => handleNotifyMember(waitlist)}
                                >
                                  <Bell className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>通知</TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8"
                                  onClick={() => cancelWaitlist(waitlist.memberId)}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>取消排队</TooltipContent>
                            </Tooltip>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>

          <DialogFooter className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={() => setShowWaitlistDialog(false)}>
              关闭
            </Button>
            {selectedCourse && getWaitlistForCourse(selectedCourse.id).length > 0 && (
              <Button onClick={notifyAll}>
                <Bell className="mr-2 h-4 w-4" />
                批量通知
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 通知对话框 */}
      <Dialog open={showNotifyDialog} onOpenChange={setShowNotifyDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>通知会员</DialogTitle>
            <DialogDescription>
              发送通知给 {selectedMember?.memberName}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="notification-message">通知内容</Label>
              <Textarea
                id="notification-message"
                value={notificationMessage}
                onChange={(e) => setNotificationMessage(e.target.value)}
                rows={5}
              />
            </div>

            <div className="space-y-2">
              <Label>通知方式</Label>
              <div className="flex flex-col gap-2">
                {mockWaitlistSettings.notificationMethods.includes("sms") && (
                  <div className="flex items-center space-x-2">
                    <Checkbox id="notify-sms" defaultChecked />
                    <Label htmlFor="notify-sms">短信通知</Label>
                  </div>
                )}
                {mockWaitlistSettings.notificationMethods.includes("wechat") && (
                  <div className="flex items-center space-x-2">
                    <Checkbox id="notify-wechat" defaultChecked />
                    <Label htmlFor="notify-wechat">微信通知</Label>
                  </div>
                )}
                {mockWaitlistSettings.notificationMethods.includes("app") && (
                  <div className="flex items-center space-x-2">
                    <Checkbox id="notify-app" defaultChecked={mockWaitlistSettings.notificationMethods.indexOf("app") === 0} />
                    <Label htmlFor="notify-app">APP推送</Label>
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNotifyDialog(false)}>
              取消
            </Button>
            <Button onClick={confirmNotify}>
              发送通知
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 排队规则设置对话框 */}
      <Dialog open={showSettingsDialog} onOpenChange={setShowSettingsDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>排队规则设置</DialogTitle>
            <DialogDescription>
              设置课程排队系统的规则和参数
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="max-waitlist">最大排队人数</Label>
              <Input
                id="max-waitlist"
                type="number"
                value={waitlistSettings.maxWaitlistLength}
                onChange={(e) => setWaitlistSettings({
                  ...waitlistSettings,
                  maxWaitlistLength: parseInt(e.target.value)
                })}
              />
              <p className="text-xs text-muted-foreground">
                每个课程最多允许多少人排队
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notification-period">通知有效期（小时）</Label>
              <Input
                id="notification-period"
                type="number"
                value={waitlistSettings.notificationPeriod}
                onChange={(e) => setWaitlistSettings({
                  ...waitlistSettings,
                  notificationPeriod: parseInt(e.target.value)
                })}
              />
              <p className="text-xs text-muted-foreground">
                会员收到通知后需要在多少小时内确认预约
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="auto-cleanup"
                checked={waitlistSettings.autoCleanupExpired}
                onCheckedChange={(checked) => setWaitlistSettings({
                  ...waitlistSettings,
                  autoCleanupExpired: checked
                })}
              />
              <Label htmlFor="auto-cleanup">自动清理过期排队</Label>
            </div>

            <div className="space-y-2">
              <Label>优先级规则</Label>
              <div className="flex flex-col gap-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="vip-priority"
                    checked={waitlistSettings.priorityRules.vipMembers}
                    onCheckedChange={(checked) => setWaitlistSettings({
                      ...waitlistSettings,
                      priorityRules: {
                        ...waitlistSettings.priorityRules,
                        vipMembers: checked
                      }
                    })}
                  />
                  <Label htmlFor="vip-priority">VIP会员优先</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="senior-priority"
                    checked={waitlistSettings.priorityRules.seniorMembers}
                    onCheckedChange={(checked) => setWaitlistSettings({
                      ...waitlistSettings,
                      priorityRules: {
                        ...waitlistSettings.priorityRules,
                        seniorMembers: checked
                      }
                    })}
                  />
                  <Label htmlFor="senior-priority">老会员优先</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="frequent-priority"
                    checked={waitlistSettings.priorityRules.frequentAttenders}
                    onCheckedChange={(checked) => setWaitlistSettings({
                      ...waitlistSettings,
                      priorityRules: {
                        ...waitlistSettings.priorityRules,
                        frequentAttenders: checked
                      }
                    })}
                  />
                  <Label htmlFor="frequent-priority">高频参与者优先</Label>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSettingsDialog(false)}>
              取消
            </Button>
            <Button onClick={saveWaitlistSettings}>
              保存设置
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
    </TooltipProvider>
  )
}
