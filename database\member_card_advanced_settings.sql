-- 会员卡高级设置相关表结构

-- 1. 会员卡促销设置表
CREATE TABLE IF NOT EXISTS member_card_promotions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 促销基本信息
  promotion_type ENUM('discount', 'new_member', 'renewal', 'group', 'holiday', 'limited_time') NOT NULL COMMENT '促销类型',
  promotion_name VARCHAR(100) NOT NULL COMMENT '促销名称',
  description TEXT COMMENT '促销描述',
  
  -- 折扣设置
  discount_type ENUM('percentage', 'fixed_amount') DEFAULT 'percentage' COMMENT '折扣类型：百分比/固定金额',
  discount_value DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '折扣值',
  
  -- 时间限制
  start_date DATETIME COMMENT '促销开始时间',
  end_date DATETIME COMMENT '促销结束时间',
  
  -- 购买限制
  max_purchases INT COMMENT '最大购买数量',
  per_user_limit INT DEFAULT 1 COMMENT '每人限购数量',
  min_purchase_amount DECIMAL(10,2) COMMENT '最低购买金额',
  
  -- 适用条件
  target_user_type ENUM('all', 'new', 'old', 'vip') DEFAULT 'all' COMMENT '适用用户类型',
  requires_coupon BOOLEAN DEFAULT FALSE COMMENT '是否需要优惠券',
  coupon_code VARCHAR(50) COMMENT '优惠券代码',
  
  -- 状态和统计
  status ENUM('active', 'inactive', 'expired') DEFAULT 'active' COMMENT '促销状态',
  used_count INT DEFAULT 0 COMMENT '已使用次数',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_promotion_type (promotion_type),
  INDEX idx_status (status),
  INDEX idx_date_range (start_date, end_date),
  
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡促销设置表';

-- 2. 会员卡课程关联表
CREATE TABLE IF NOT EXISTS member_card_course_associations (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  course_type_id INT COMMENT '课程类型ID（可为空表示适用所有课程）',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 消耗规则
  consumption_rule ENUM('AVERAGE', 'FIXED', 'PERCENTAGE', 'CUSTOM') DEFAULT 'AVERAGE' COMMENT '消耗规则',
  consumption_value DECIMAL(10,2) COMMENT '消耗值（次数/金额/百分比）',
  
  -- 课程设置
  is_unlimited BOOLEAN DEFAULT TRUE COMMENT '是否不限课程类型',
  allowed_course_types JSON COMMENT '允许的课程类型ID列表',
  restricted_course_types JSON COMMENT '限制的课程类型ID列表',
  
  -- 时间限制
  time_restrictions JSON COMMENT '时间限制设置（工作日/周末/节假日等）',
  booking_advance_days INT DEFAULT 7 COMMENT '可提前预约天数',
  
  -- 赠送设置
  bonus_class_times INT DEFAULT 0 COMMENT '赠送课时数',
  bonus_value_amount DECIMAL(10,2) DEFAULT 0 COMMENT '赠送金额',
  bonus_value_coefficient DECIMAL(5,2) DEFAULT 1.0 COMMENT '赠送价值系数',
  
  -- 状态
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '关联状态',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_course_type_id (course_type_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_status (status),
  
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡课程关联表';

-- 3. 会员卡销售限制表
CREATE TABLE IF NOT EXISTS member_card_sales_restrictions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 销售限制
  max_sales_total INT COMMENT '总销售上限',
  max_sales_daily INT COMMENT '每日销售上限',
  max_sales_monthly INT COMMENT '每月销售上限',
  
  -- 购买限制
  purchase_restriction ENUM('none', 'new_only', 'old_only', 'once_per_user', 'vip_only') DEFAULT 'none' COMMENT '购买限制类型',
  max_per_user INT DEFAULT 1 COMMENT '每人最大购买数量',
  min_age INT COMMENT '最小年龄限制',
  max_age INT COMMENT '最大年龄限制',
  
  -- 地域限制
  allowed_regions JSON COMMENT '允许购买的地区',
  restricted_regions JSON COMMENT '限制购买的地区',
  
  -- 会员等级限制
  required_member_level ENUM('bronze', 'silver', 'gold', 'platinum', 'diamond') COMMENT '要求的会员等级',
  min_member_points INT COMMENT '最低会员积分要求',
  
  -- 时间限制
  sale_start_date DATETIME COMMENT '销售开始时间',
  sale_end_date DATETIME COMMENT '销售结束时间',
  sale_time_slots JSON COMMENT '销售时间段设置',
  
  -- 其他限制
  requires_referral BOOLEAN DEFAULT FALSE COMMENT '是否需要推荐人',
  requires_deposit BOOLEAN DEFAULT FALSE COMMENT '是否需要押金',
  deposit_amount DECIMAL(10,2) COMMENT '押金金额',
  
  -- 状态
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '限制状态',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_purchase_restriction (purchase_restriction),
  INDEX idx_status (status),
  INDEX idx_sale_date_range (sale_start_date, sale_end_date),
  
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡销售限制表';

-- 4. 会员卡显示设置表
CREATE TABLE IF NOT EXISTS member_card_display_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 显示设置
  is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
  is_hot BOOLEAN DEFAULT FALSE COMMENT '是否热门',
  is_new BOOLEAN DEFAULT FALSE COMMENT '是否新品',
  display_order INT DEFAULT 0 COMMENT '显示顺序',
  
  -- 标签设置
  tags JSON COMMENT '标签列表',
  badges JSON COMMENT '徽章设置',
  
  -- 图片设置
  cover_image VARCHAR(500) COMMENT '封面图片URL',
  gallery_images JSON COMMENT '图片库',
  icon_image VARCHAR(500) COMMENT '图标图片URL',
  
  -- 描述设置
  short_description VARCHAR(200) COMMENT '简短描述',
  detailed_description TEXT COMMENT '详细描述',
  features JSON COMMENT '特色功能列表',
  benefits JSON COMMENT '权益列表',
  
  -- 显示条件
  show_on_homepage BOOLEAN DEFAULT TRUE COMMENT '是否在首页显示',
  show_on_category BOOLEAN DEFAULT TRUE COMMENT '是否在分类页显示',
  show_price BOOLEAN DEFAULT TRUE COMMENT '是否显示价格',
  show_original_price BOOLEAN DEFAULT TRUE COMMENT '是否显示原价',
  show_discount BOOLEAN DEFAULT TRUE COMMENT '是否显示折扣',
  
  -- 自定义样式
  custom_css TEXT COMMENT '自定义CSS样式',
  theme_color VARCHAR(7) COMMENT '主题颜色',
  background_color VARCHAR(7) COMMENT '背景颜色',
  text_color VARCHAR(7) COMMENT '文字颜色',
  
  -- 状态
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '显示状态',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_is_featured (is_featured),
  INDEX idx_display_order (display_order),
  INDEX idx_status (status),
  
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡显示设置表';

-- 5. 会员卡使用规则表
CREATE TABLE IF NOT EXISTS member_card_usage_rules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  card_type_id INT NOT NULL COMMENT '会员卡类型ID',
  tenant_id INT NOT NULL COMMENT '租户ID',
  
  -- 使用规则
  can_transfer BOOLEAN DEFAULT FALSE COMMENT '是否可转让',
  can_refund BOOLEAN DEFAULT FALSE COMMENT '是否可退款',
  can_pause BOOLEAN DEFAULT TRUE COMMENT '是否可暂停',
  can_extend BOOLEAN DEFAULT TRUE COMMENT '是否可延期',
  
  -- 转让规则
  transfer_fee_type ENUM('none', 'fixed', 'percentage') DEFAULT 'none' COMMENT '转让费用类型',
  transfer_fee_value DECIMAL(10,2) DEFAULT 0 COMMENT '转让费用值',
  max_transfers INT DEFAULT 1 COMMENT '最大转让次数',
  
  -- 退款规则
  refund_policy ENUM('no_refund', 'full_refund', 'partial_refund', 'time_based') DEFAULT 'no_refund' COMMENT '退款政策',
  refund_fee_percentage DECIMAL(5,2) DEFAULT 0 COMMENT '退款手续费百分比',
  refund_deadline_days INT COMMENT '退款截止天数',
  
  -- 暂停规则
  max_pause_times INT DEFAULT 3 COMMENT '最大暂停次数',
  max_pause_days INT DEFAULT 30 COMMENT '最大暂停天数',
  min_pause_days INT DEFAULT 7 COMMENT '最小暂停天数',
  
  -- 延期规则
  max_extension_times INT DEFAULT 2 COMMENT '最大延期次数',
  max_extension_days INT DEFAULT 90 COMMENT '最大延期天数',
  extension_fee_per_day DECIMAL(10,2) DEFAULT 0 COMMENT '每天延期费用',
  
  -- 其他规则
  grace_period_days INT DEFAULT 7 COMMENT '宽限期天数',
  auto_renewal BOOLEAN DEFAULT FALSE COMMENT '是否自动续费',
  reminder_days JSON COMMENT '提醒天数设置',
  
  -- 状态
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '规则状态',
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_card_type_id (card_type_id),
  INDEX idx_tenant_id (tenant_id),
  INDEX idx_status (status),
  
  FOREIGN KEY (card_type_id) REFERENCES member_card_types(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员卡使用规则表';

-- 插入一些示例数据
INSERT INTO member_card_promotions (card_type_id, tenant_id, promotion_type, promotion_name, description, discount_type, discount_value, target_user_type) VALUES
(1, 2, 'new_member', '新会员专享', '新注册会员首次购买享受8.5折优惠', 'percentage', 15.00, 'new'),
(2, 2, 'holiday', '节日促销', '春节期间购买年卡享受特价', 'fixed_amount', 200.00, 'all');

INSERT INTO member_card_course_associations (card_type_id, tenant_id, consumption_rule, is_unlimited, bonus_class_times) VALUES
(1, 2, 'AVERAGE', TRUE, 2),
(2, 2, 'FIXED', TRUE, 1);

INSERT INTO member_card_sales_restrictions (card_type_id, tenant_id, purchase_restriction, max_per_user) VALUES
(1, 2, 'once_per_user', 1),
(2, 2, 'none', 3);

INSERT INTO member_card_display_settings (card_type_id, tenant_id, is_featured, display_order, short_description) VALUES
(1, 2, TRUE, 1, '365天无限次瑜伽课程，新会员首选'),
(2, 2, FALSE, 2, '90天瑜伽体验，适合初学者');

INSERT INTO member_card_usage_rules (card_type_id, tenant_id, can_transfer, can_refund, can_pause, max_pause_times) VALUES
(1, 2, FALSE, TRUE, TRUE, 3),
(2, 2, TRUE, FALSE, TRUE, 2);
