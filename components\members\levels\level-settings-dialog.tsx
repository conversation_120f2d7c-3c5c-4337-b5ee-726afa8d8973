"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Settings, Save, RefreshCw } from "lucide-react"

interface LevelSettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function LevelSettingsDialog({ open, onOpenChange }: LevelSettingsDialogProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [settings, setSettings] = useState({
    autoUpgrade: true,
    autoDowngrade: false,
    showInMemberList: true,
    showInMemberDetail: true,
    notifyOnUpgrade: true,
    upgradeMessage: "恭喜您已升级为{level}，可享受更多会员权益！",
    defaultLevel: "standard",
    upgradeRules: "consumption", // consumption, frequency, time
    consumptionThreshold: "2000",
    frequencyThreshold: "10",
    timeThreshold: "90",
  })

  const handleChange = (field: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)

    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      toast({
        title: "设置已保存",
        description: "会员等级系统设置已成功更新",
      })

      onOpenChange(false)
    } catch (error) {
      toast({
        title: "保存失败",
        description: "保存设置时出错，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>会员等级设置</DialogTitle>
          <DialogDescription>
            配置会员等级系统的全局设置和升级规则
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="general">基本设置</TabsTrigger>
            <TabsTrigger value="rules">升级规则</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-upgrade">自动升级</Label>
                  <p className="text-sm text-muted-foreground">
                    会员满足条件时自动升级等级
                  </p>
                </div>
                <Switch
                  id="auto-upgrade"
                  checked={settings.autoUpgrade}
                  onCheckedChange={(checked) => handleChange("autoUpgrade", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="auto-downgrade">自动降级</Label>
                  <p className="text-sm text-muted-foreground">
                    会员不满足条件时自动降级
                  </p>
                </div>
                <Switch
                  id="auto-downgrade"
                  checked={settings.autoDowngrade}
                  onCheckedChange={(checked) => handleChange("autoDowngrade", checked)}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="show-in-list">会员列表显示等级</Label>
                  <p className="text-sm text-muted-foreground">
                    在会员列表中显示会员等级
                  </p>
                </div>
                <Switch
                  id="show-in-list"
                  checked={settings.showInMemberList}
                  onCheckedChange={(checked) => handleChange("showInMemberList", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="show-in-detail">会员详情显示等级</Label>
                  <p className="text-sm text-muted-foreground">
                    在会员详情页显示会员等级和权益
                  </p>
                </div>
                <Switch
                  id="show-in-detail"
                  checked={settings.showInMemberDetail}
                  onCheckedChange={(checked) => handleChange("showInMemberDetail", checked)}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="notify-on-upgrade">升级通知</Label>
                  <p className="text-sm text-muted-foreground">
                    会员升级时发送通知
                  </p>
                </div>
                <Switch
                  id="notify-on-upgrade"
                  checked={settings.notifyOnUpgrade}
                  onCheckedChange={(checked) => handleChange("notifyOnUpgrade", checked)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="upgrade-message">升级通知内容</Label>
                <Input
                  id="upgrade-message"
                  value={settings.upgradeMessage}
                  onChange={(e) => handleChange("upgradeMessage", e.target.value)}
                  placeholder="恭喜您已升级为{level}，可享受更多会员权益！"
                />
                <p className="text-sm text-muted-foreground">
                  使用 {"{level}"} 表示会员等级名称
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="default-level">默认会员等级</Label>
                <Select
                  value={settings.defaultLevel}
                  onValueChange={(value) => handleChange("defaultLevel", value)}
                >
                  <SelectTrigger id="default-level">
                    <SelectValue placeholder="选择默认会员等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">标准会员</SelectItem>
                    <SelectItem value="silver">银卡会员</SelectItem>
                    <SelectItem value="gold">金卡会员</SelectItem>
                    <SelectItem value="platinum">白金会员</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  新会员注册时的默认等级
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="rules" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="upgrade-rules">升级规则类型</Label>
                <Select
                  value={settings.upgradeRules}
                  onValueChange={(value) => handleChange("upgradeRules", value)}
                >
                  <SelectTrigger id="upgrade-rules">
                    <SelectValue placeholder="选择升级规则类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="consumption">消费金额</SelectItem>
                    <SelectItem value="frequency">消费次数</SelectItem>
                    <SelectItem value="time">会员时长</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {settings.upgradeRules === "consumption" && (
                <div className="space-y-2">
                  <Label htmlFor="consumption-threshold">消费金额阈值（元）</Label>
                  <Input
                    id="consumption-threshold"
                    type="number"
                    value={settings.consumptionThreshold}
                    onChange={(e) => handleChange("consumptionThreshold", e.target.value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    会员累计消费达到此金额时升级
                  </p>
                </div>
              )}

              {settings.upgradeRules === "frequency" && (
                <div className="space-y-2">
                  <Label htmlFor="frequency-threshold">消费次数阈值</Label>
                  <Input
                    id="frequency-threshold"
                    type="number"
                    value={settings.frequencyThreshold}
                    onChange={(e) => handleChange("frequencyThreshold", e.target.value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    会员累计消费达到此次数时升级
                  </p>
                </div>
              )}

              {settings.upgradeRules === "time" && (
                <div className="space-y-2">
                  <Label htmlFor="time-threshold">会员时长阈值（天）</Label>
                  <Input
                    id="time-threshold"
                    type="number"
                    value={settings.timeThreshold}
                    onChange={(e) => handleChange("timeThreshold", e.target.value)}
                  />
                  <p className="text-sm text-muted-foreground">
                    会员注册时长达到此天数时升级
                  </p>
                </div>
              )}

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">升级规则说明</CardTitle>
                </CardHeader>
                <CardContent className="text-sm text-muted-foreground">
                  <ul className="list-disc pl-4 space-y-1">
                    <li>系统将根据设定的规则自动为会员分配等级</li>
                    <li>升级规则可以基于消费金额、消费次数、会员时长等条件</li>
                    <li>每个等级可以设置不同的升级条件</li>
                    <li>会员降级需要手动操作，系统不会自动降级</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                保存中...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                保存设置
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
