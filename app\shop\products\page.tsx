"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Search, Plus, MoreHorizontal, Edit, Trash2, Eye, EyeOff, Copy, Tag, Package, Truck, ShoppingBag, Download, Pencil, BarChart, History } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// 模拟商品数据
const products = [
  {
    id: "1",
    name: "瑜伽垫 - 专业防滑",
    description: "高密度环保材质，专业防滑瑜伽垫，适合各种瑜伽练习",
    price: "199.00",
    originalPrice: "299.00",
    inventory: 56,
    sales: 128,
    category: "瑜伽装备",
    type: "physical", // 实物商品
    status: "active",
    images: ["/products/yoga-mat.jpg"],
    tags: ["热销", "防滑", "环保"],
    deliveryMethods: ["物流", "自提"],
    createdAt: "2023-05-15",
  },
  {
    id: "2",
    name: "高级会员卡 - 年卡",
    description: "瑜伽馆高级会员年卡，享受全年不限次数的课程和场地使用权",
    price: "3688.00",
    originalPrice: "4988.00",
    inventory: null, // 虚拟商品无库存限制
    sales: 45,
    category: "会员卡",
    type: "virtual", // 虚拟商品
    status: "active",
    images: ["/products/membership-card.jpg"],
    tags: ["会员卡", "畅享", "优惠"],
    deliveryMethods: ["核销"],
    createdAt: "2023-06-01",
  },
  {
    id: "3",
    name: "瑜伽服套装 - 女款",
    description: "透气舒适的瑜伽服套装，包含上衣和裤子，多色可选",
    price: "268.00",
    originalPrice: "328.00",
    inventory: 78,
    sales: 95,
    category: "瑜伽服饰",
    type: "physical", // 实物商品
    status: "active",
    images: ["/products/yoga-clothes.jpg"],
    tags: ["舒适", "透气", "弹性"],
    deliveryMethods: ["物流", "自提"],
    createdAt: "2023-06-15",
  },
  {
    id: "4",
    name: "瑜伽入门课程 - 在线视频",
    description: "专业教练指导的瑜伽入门课程，包含10节视频课程",
    price: "99.00",
    originalPrice: "199.00",
    inventory: null, // 虚拟商品无库存限制
    sales: 256,
    category: "在线课程",
    type: "virtual", // 虚拟商品
    status: "active",
    images: ["/products/yoga-course.jpg"],
    tags: ["入门", "视频课程", "随时学习"],
    deliveryMethods: ["核销"],
    createdAt: "2023-07-01",
  },
  {
    id: "5",
    name: "瑜伽砖 - 高密度泡沫",
    description: "高密度EVA泡沫瑜伽砖，帮助初学者稳定姿势",
    price: "49.00",
    originalPrice: "69.00",
    inventory: 120,
    sales: 78,
    category: "瑜伽装备",
    type: "physical", // 实物商品
    status: "active",
    images: ["/products/yoga-block.jpg"],
    tags: ["辅助工具", "稳定", "轻便"],
    deliveryMethods: ["物流", "自提"],
    createdAt: "2023-07-15",
  },
  {
    id: "6",
    name: "高级私教身份 - 月卡",
    description: "每月享受4次私教课程，专业教练一对一指导",
    price: "1288.00",
    originalPrice: "1688.00",
    inventory: null, // 虚拟商品无库存限制
    sales: 32,
    category: "身份卡",
    type: "virtual", // 虚拟商品
    status: "active",
    images: ["/products/private-coach.jpg"],
    tags: ["私教", "一对一", "专业指导"],
    deliveryMethods: ["核销"],
    createdAt: "2023-08-01",
  },
]

export default function ShopProductsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("newest") // newest, price-asc, price-desc, sales
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [showAddEditDialog, setShowAddEditDialog] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)
  const [showChapterDialog, setShowChapterDialog] = useState(false)
  const [showVideoDialog, setShowVideoDialog] = useState(false)
  const [selectedChapter, setSelectedChapter] = useState<any>(null)
  const [selectedVideo, setSelectedVideo] = useState<any>(null)
  const [useProductSpecs, setUseProductSpecs] = useState(false)
  const [specOptions, setSpecOptions] = useState<{name: string, values: string[]}[]>([
    { name: "颜色", values: ["红色", "蓝色", "黑色"] },
    { name: "尺寸", values: ["S", "M", "L", "XL"] }
  ])

  // 过滤商品
  const filteredProducts = products.filter(
    (product) =>
      (categoryFilter === "all" || product.category === categoryFilter) &&
      (typeFilter === "all" || product.type === typeFilter) &&
      (statusFilter === "all" || product.status === statusFilter) &&
      (product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())))
  )

  // 排序商品
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "price-asc":
        return parseFloat(a.price) - parseFloat(b.price)
      case "price-desc":
        return parseFloat(b.price) - parseFloat(a.price)
      case "sales":
        return b.sales - a.sales
      case "newest":
      default:
        // 假设 createdAt 是日期字符串，格式为 YYYY-MM-DD
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    }
  })

  // 获取所有分类
  const categories = Array.from(new Set(products.map(product => product.category)))

  // 处理添加商品
  const handleAddProduct = () => {
    setSelectedProduct(null)
    setShowAddEditDialog(true)
  }

  // 处理编辑商品
  const handleEditProduct = (product: any) => {
    setSelectedProduct(product)
    setShowAddEditDialog(true)
  }

  // 处理复制商品
  const handleDuplicateProduct = (product: any) => {
    const newProduct = {
      ...product,
      id: `new-${Date.now()}`,
      name: `${product.name} (复制)`,
    }
    // 这里应该有API调用来保存新商品
    alert(`已创建商品副本: ${newProduct.name}`)
  }

  // 处理删除商品
  const handleDeleteProduct = (product: any) => {
    if (confirm(`确定要删除商品 "${product.name}" 吗？`)) {
      // 这里应该有API调用来删除商品
      alert(`已删除商品: ${product.name}`)
    }
  }

  // 处理商品上下架
  const handleToggleStatus = (product: any) => {
    const newStatus = product.status === "active" ? "inactive" : "active"
    // 这里应该有API调用来更新商品状态
    alert(`商品 "${product.name}" 状态已更改为: ${newStatus === "active" ? "上架" : "下架"}`)
  }

  // 处理添加章节
  const handleAddChapter = () => {
    setSelectedChapter(null)
    setShowChapterDialog(true)
  }

  // 处理编辑章节
  const handleEditChapter = (chapter: any) => {
    setSelectedChapter(chapter)
    setShowChapterDialog(true)
  }

  // 处理删除章节
  const handleDeleteChapter = (chapter: any) => {
    if (confirm(`确定要删除章节 "${chapter.title}" 吗？所有视频内容将被删除。`)) {
      // 这里应该有API调用来删除章节
      alert(`已删除章节: ${chapter.title}`)
    }
  }

  // 处理添加视频
  const handleAddVideo = (chapter: any) => {
    setSelectedVideo(null)
    setSelectedChapter(chapter)
    setShowVideoDialog(true)
  }

  // 处理编辑视频
  const handleEditVideo = (video: any, chapter: any) => {
    setSelectedVideo(video)
    setSelectedChapter(chapter)
    setShowVideoDialog(true)
  }

  // 处理删除视频
  const handleDeleteVideo = (video: any) => {
    if (confirm(`确定要删除视频 "${video.title}" 吗？`)) {
      // 这里应该有API调用来删除视频
      alert(`已删除视频: ${video.title}`)
    }
  }

  // 处理视频预览设置
  const handleTogglePreview = (video: any) => {
    // 这里应该有API调用来更新视频预览状态
    alert(`视频 "${video.title}" ${video.isFreePreview ? "取消" : "设置"}为免费预览`)
  }

  // 添加规格名称
  const handleAddSpecName = (name: string) => {
    if (!name.trim()) return

    // 检查是否已存在相同名称的规格
    if (specOptions.some(spec => spec.name === name)) {
      alert(`规格 "${name}" 已存在`)
      return
    }

    setSpecOptions([...specOptions, { name, values: [] }])
  }

  // 删除规格名称
  const handleRemoveSpecName = (index: number) => {
    const newSpecOptions = [...specOptions]
    newSpecOptions.splice(index, 1)
    setSpecOptions(newSpecOptions)
  }

  // 添加规格值
  const handleAddSpecValue = (specIndex: number, value: string) => {
    if (!value.trim()) return

    const newSpecOptions = [...specOptions]

    // 检查是否已存在相同的规格值
    if (newSpecOptions[specIndex].values.includes(value)) {
      alert(`规格值 "${value}" 已存在`)
      return
    }

    newSpecOptions[specIndex].values.push(value)
    setSpecOptions(newSpecOptions)
  }

  // 删除规格值
  const handleRemoveSpecValue = (specIndex: number, valueIndex: number) => {
    const newSpecOptions = [...specOptions]
    newSpecOptions[specIndex].values.splice(valueIndex, 1)
    setSpecOptions(newSpecOptions)
  }

  // 生成规格组合
  const generateSpecCombinations = () => {
    // 只使用有规格值的规格
    const validSpecs = specOptions.filter(spec => spec.values.length > 0)

    if (validSpecs.length === 0) return []

    // 递归生成所有组合
    const generateCombos = (specs: typeof specOptions, index = 0, current: {[key: string]: string} = {}) => {
      if (index === specs.length) {
        return [current]
      }

      const currentSpec = specs[index]
      const result: {[key: string]: string}[] = []

      for (const value of currentSpec.values) {
        const newCurrent = { ...current, [currentSpec.name]: value }
        result.push(...generateCombos(specs, index + 1, newCurrent))
      }

      return result
    }

    return generateCombos(validSpecs)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">商品管理</h1>
        <div className="flex gap-2">
          <Button onClick={handleAddProduct}>
            <Plus className="mr-2 h-4 w-4" />
            添加商品
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出商品
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center gap-2 max-w-md">
          <Input
            placeholder="搜索商品名称、描述或标签..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
      </div>

      <div className="flex flex-wrap gap-3 items-center">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">分类:</span>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-[150px] h-9">
              <SelectValue placeholder="商品分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有分类</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">类型:</span>
          <Select value={typeFilter} onValueChange={setTypeFilter}>
            <SelectTrigger className="w-[120px] h-9">
              <SelectValue placeholder="商品类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              <SelectItem value="physical">实物商品</SelectItem>
              <SelectItem value="virtual">虚拟商品</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">状态:</span>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[120px] h-9">
              <SelectValue placeholder="商品状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有状态</SelectItem>
              <SelectItem value="active">已上架</SelectItem>
              <SelectItem value="inactive">已下架</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">排序:</span>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[150px] h-9">
              <SelectValue placeholder="排序方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">最新上架</SelectItem>
              <SelectItem value="price-asc">价格从低到高</SelectItem>
              <SelectItem value="price-desc">价格从高到低</SelectItem>
              <SelectItem value="sales">销量优先</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="ml-auto flex items-center border rounded-md">
          <Button
            variant="ghost"
            size="icon"
            className={`rounded-none ${viewMode === "grid" ? "bg-muted" : ""}`}
            onClick={() => setViewMode("grid")}
          >
            <ShoppingBag className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className={`rounded-none ${viewMode === "list" ? "bg-muted" : ""}`}
            onClick={() => setViewMode("list")}
          >
            <Package className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sortedProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{product.name}</CardTitle>
                  <Badge variant={product.type === "physical" ? "default" : "secondary"}>
                    {product.type === "physical" ? "实物商品" : "虚拟商品"}
                  </Badge>
                </div>
                <CardDescription className="line-clamp-2">{product.description}</CardDescription>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="text-sm space-y-2">
                  <div className="flex justify-between">
                    <div>
                      <span className="font-medium">价格：</span>
                      <span className="text-primary">¥{product.price}</span>
                      {product.originalPrice && (
                        <span className="text-muted-foreground line-through ml-2">¥{product.originalPrice}</span>
                      )}
                    </div>
                    <div>
                      <span className="font-medium">销量：</span>
                      {product.sales}
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <div>
                      <span className="font-medium">分类：</span>
                      {product.category}
                    </div>
                    {product.type === "physical" && (
                      <div>
                        <span className="font-medium">库存：</span>
                        {product.inventory}
                      </div>
                    )}
                  </div>
                  <div>
                    <span className="font-medium">配送方式：</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {product.deliveryMethods.map((method, index) => (
                        <Badge key={index} variant="outline" className="bg-blue-50">
                          {method}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">标签：</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {product.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="bg-green-50">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-2 flex justify-between">
                <Button variant="outline" size="sm">
                  <Eye className="mr-2 h-4 w-4" />
                  查看详情
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>操作</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleEditProduct(product)}>
                      <Edit className="mr-2 h-4 w-4" />
                      编辑商品
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleDuplicateProduct(product)}>
                      <Copy className="mr-2 h-4 w-4" />
                      复制商品
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleToggleStatus(product)}>
                      {product.status === "active" ? (
                        <>
                          <EyeOff className="mr-2 h-4 w-4" />
                          下架商品
                        </>
                      ) : (
                        <>
                          <Eye className="mr-2 h-4 w-4" />
                          上架商品
                        </>
                      )}
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Tag className="mr-2 h-4 w-4" />
                      管理标签
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleDeleteProduct(product)} className="text-red-600">
                      <Trash2 className="mr-2 h-4 w-4" />
                      删除商品
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>商品列表</CardTitle>
            <CardDescription>管理所有商品信息</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>商品名称</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>分类</TableHead>
                  <TableHead>价格</TableHead>
                  <TableHead>库存/销量</TableHead>
                  <TableHead>配送方式</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>
                      <Badge variant={product.type === "physical" ? "default" : "secondary"}>
                        {product.type === "physical" ? "实物商品" : "虚拟商品"}
                      </Badge>
                    </TableCell>
                    <TableCell>{product.category}</TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-primary">¥{product.price}</span>
                        {product.originalPrice && (
                          <span className="text-muted-foreground line-through text-xs">¥{product.originalPrice}</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {product.type === "physical" ? (
                        <span>库存: {product.inventory} / 销量: {product.sales}</span>
                      ) : (
                        <span>销量: {product.sales}</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {product.deliveryMethods.map((method, index) => (
                          <Badge key={index} variant="outline" className="bg-blue-50">
                            {method}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>{product.createdAt}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-1">
                        <Button variant="ghost" size="icon" onClick={() => handleEditProduct(product)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleToggleStatus(product)}>
                          {product.status === "active" ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>更多操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDuplicateProduct(product)}>
                              <Copy className="mr-2 h-4 w-4" />
                              复制商品
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <BarChart className="mr-2 h-4 w-4" />
                              查看统计
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleDeleteProduct(product)} className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              删除商品
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* 商品添加/编辑对话框 */}
      <Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedProduct ? "编辑商品" : "添加商品"}</DialogTitle>
            <DialogDescription>
              {selectedProduct ? "修改商品信息并保存更改" : "填写商品信息以创建新商品"}
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="basic" className="mt-4">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="basic">基本信息</TabsTrigger>
              <TabsTrigger value="price">价格库存</TabsTrigger>
              <TabsTrigger value="specs" disabled={selectedProduct?.type !== "physical"}>规格设置</TabsTrigger>
              <TabsTrigger value="delivery">配送方式</TabsTrigger>
              <TabsTrigger value="media">图片管理</TabsTrigger>
              <TabsTrigger value="course" disabled={selectedProduct?.type !== "virtual" || selectedProduct?.category !== "在线课程"}>
                课程内容
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">商品名称</Label>
                  <Input
                    id="name"
                    placeholder="输入商品名称"
                    defaultValue={selectedProduct?.name || ""}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">商品描述</Label>
                  <Textarea
                    id="description"
                    placeholder="输入商品详细描述"
                    rows={4}
                    defaultValue={selectedProduct?.description || ""}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">商品分类</Label>
                    <Select defaultValue={selectedProduct?.category || ""}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择商品分类" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map(category => (
                          <SelectItem key={category} value={category}>{category}</SelectItem>
                        ))}
                        <SelectItem value="new">+ 添加新分类</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type">商品类型</Label>
                    <Select defaultValue={selectedProduct?.type || "physical"}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择商品类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="physical">实物商品</SelectItem>
                        <SelectItem value="virtual">虚拟商品</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>商品标签</Label>
                  <div className="flex flex-wrap gap-2 border rounded-md p-2">
                    {(selectedProduct?.tags || []).map((tag, index) => (
                      <Badge key={index} variant="outline" className="bg-green-50 flex items-center gap-1">
                        {tag}
                        <Button variant="ghost" size="icon" className="h-4 w-4 rounded-full">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                    <div className="flex items-center">
                      <Input placeholder="添加标签" className="h-8 w-32" />
                      <Button variant="ghost" size="sm">添加</Button>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="price" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">销售价格</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-2.5">¥</span>
                    <Input
                      id="price"
                      type="number"
                      className="pl-7"
                      placeholder="0.00"
                      defaultValue={selectedProduct?.price || ""}
                      disabled={useProductSpecs && selectedProduct?.type === "physical"}
                    />
                  </div>
                  {useProductSpecs && selectedProduct?.type === "physical" && (
                    <p className="text-xs text-muted-foreground">启用多规格后，请在规格设置中设置各规格价格</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="originalPrice">原价</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-2.5">¥</span>
                    <Input
                      id="originalPrice"
                      type="number"
                      className="pl-7"
                      placeholder="0.00"
                      defaultValue={selectedProduct?.originalPrice || ""}
                      disabled={useProductSpecs && selectedProduct?.type === "physical"}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="inventory">库存数量</Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="unlimited" />
                    <label htmlFor="unlimited" className="text-sm">无限库存</label>
                  </div>
                </div>
                <Input
                  id="inventory"
                  type="number"
                  placeholder="输入库存数量"
                  defaultValue={selectedProduct?.inventory || ""}
                  disabled={selectedProduct?.type === "virtual" || (useProductSpecs && selectedProduct?.type === "physical")}
                />
                {selectedProduct?.type === "virtual" && (
                  <p className="text-sm text-muted-foreground">虚拟商品无需设置库存</p>
                )}
                {useProductSpecs && selectedProduct?.type === "physical" && (
                  <p className="text-sm text-muted-foreground">启用多规格后，请在规格设置中设置各规格库存</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="sku">商品编码 (SKU)</Label>
                <Input
                  id="sku"
                  placeholder="输入商品唯一编码"
                  defaultValue={selectedProduct?.sku || ""}
                />
              </div>

              {selectedProduct?.type === "physical" && (
                <div className="pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="useSpecs"
                      checked={useProductSpecs}
                      onCheckedChange={(checked) => setUseProductSpecs(checked as boolean)}
                    />
                    <label htmlFor="useSpecs" className="text-sm font-medium">
                      启用多规格
                    </label>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    启用后可以设置如颜色、尺寸等多种规格组合，并为每种组合单独设置价格和库存
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="specs" className="space-y-6 mt-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">商品规格设置</h3>
                  <p className="text-sm text-muted-foreground">添加商品规格，如颜色、尺寸等</p>
                </div>
              </div>

              {/* 规格名称设置 */}
              <div className="space-y-4 border-b pb-6">
                <h4 className="font-medium">规格名称</h4>
                <div className="flex items-center gap-2">
                  <Input placeholder="添加规格名称，如：颜色、尺寸" id="specName" className="max-w-xs" />
                  <Button
                    variant="outline"
                    onClick={() => {
                      const input = document.getElementById('specName') as HTMLInputElement
                      handleAddSpecName(input.value)
                      input.value = ''
                    }}
                  >
                    添加
                  </Button>
                </div>

                <div className="space-y-2">
                  {specOptions.map((spec, index) => (
                    <div key={index} className="flex items-center justify-between bg-muted/30 p-3 rounded-md">
                      <span className="font-medium">{spec.name}</span>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveSpecName(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* 规格值设置 */}
              <div className="space-y-4">
                <h4 className="font-medium">规格值设置</h4>

                {specOptions.length === 0 ? (
                  <div className="text-center p-6 border border-dashed rounded-md">
                    <p className="text-muted-foreground">请先添加规格名称</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {specOptions.map((spec, specIndex) => (
                      <div key={specIndex} className="space-y-3 border p-4 rounded-md">
                        <div className="flex items-center justify-between">
                          <h5 className="font-medium">{spec.name}</h5>
                        </div>

                        <div className="flex items-center gap-2">
                          <Input
                            placeholder={`添加${spec.name}值，如：红色、蓝色`}
                            id={`specValue_${specIndex}`}
                          />
                          <Button
                            variant="outline"
                            onClick={() => {
                              const input = document.getElementById(`specValue_${specIndex}`) as HTMLInputElement
                              handleAddSpecValue(specIndex, input.value)
                              input.value = ''
                            }}
                          >
                            添加
                          </Button>
                        </div>

                        <div className="flex flex-wrap gap-2">
                          {spec.values.map((value, valueIndex) => (
                            <Badge key={valueIndex} variant="outline" className="flex items-center gap-1 p-2">
                              {value}
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-4 w-4 rounded-full ml-1"
                                onClick={() => handleRemoveSpecValue(specIndex, valueIndex)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </Badge>
                          ))}

                          {spec.values.length === 0 && (
                            <p className="text-xs text-muted-foreground">暂无规格值</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* 规格组合表格 */}
              {specOptions.some(spec => spec.values.length > 0) && (
                <div className="space-y-4 border-t pt-6 mt-6">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">规格组合</h4>
                    <Button variant="outline" size="sm">批量设置</Button>
                  </div>

                  <div className="border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {specOptions.filter(spec => spec.values.length > 0).map((spec, index) => (
                            <TableHead key={index}>{spec.name}</TableHead>
                          ))}
                          <TableHead>图片</TableHead>
                          <TableHead>价格 (¥)</TableHead>
                          <TableHead>库存</TableHead>
                          <TableHead>SKU</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {generateSpecCombinations().map((combo, index) => (
                          <TableRow key={index}>
                            {Object.entries(combo).map(([name, value], i) => (
                              <TableCell key={i}>{value}</TableCell>
                            ))}
                            <TableCell>
                              <div className="w-8 h-8 border rounded-md flex items-center justify-center cursor-pointer">
                                <Plus className="h-4 w-4 text-muted-foreground" />
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="relative w-20">
                                <span className="absolute left-2 top-2.5 text-xs">¥</span>
                                <Input className="pl-5" defaultValue={selectedProduct?.price || ""} />
                              </div>
                            </TableCell>
                            <TableCell>
                              <Input className="w-20" type="number" defaultValue="0" />
                            </TableCell>
                            <TableCell>
                              <Input className="w-32" placeholder="规格SKU" />
                            </TableCell>
                          </TableRow>
                        ))}

                        {generateSpecCombinations().length === 0 && (
                          <TableRow>
                            <TableCell colSpan={specOptions.filter(spec => spec.values.length > 0).length + 4} className="text-center py-4">
                              请为每个规格添加至少一个规格值
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="delivery" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label>配送方式</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2 border rounded-md p-3">
                    <Checkbox id="logistics" defaultChecked={selectedProduct?.deliveryMethods?.includes("物流")} />
                    <label htmlFor="logistics" className="text-sm font-medium">物流配送</label>
                  </div>
                  <div className="flex items-center space-x-2 border rounded-md p-3">
                    <Checkbox id="selfPickup" defaultChecked={selectedProduct?.deliveryMethods?.includes("自提")} />
                    <label htmlFor="selfPickup" className="text-sm font-medium">门店自提</label>
                  </div>
                  <div className="flex items-center space-x-2 border rounded-md p-3">
                    <Checkbox id="verification" defaultChecked={selectedProduct?.deliveryMethods?.includes("核销")} />
                    <label htmlFor="verification" className="text-sm font-medium">到店核销</label>
                  </div>
                </div>
              </div>

              {selectedProduct?.type === "virtual" && (
                <div className="space-y-2">
                  <Label>虚拟商品设置</Label>
                  <div className="grid grid-cols-1 gap-2">
                    <div className="flex items-center space-x-2 border rounded-md p-3">
                      <Checkbox id="autoVerification" />
                      <div>
                        <label htmlFor="autoVerification" className="text-sm font-medium">自动核销</label>
                        <p className="text-xs text-muted-foreground">购买后自动完成核销，适用于在线课程等</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 border rounded-md p-3">
                      <Checkbox id="validityPeriod" />
                      <div>
                        <label htmlFor="validityPeriod" className="text-sm font-medium">设置有效期</label>
                        <p className="text-xs text-muted-foreground">购买后在指定时间内有效</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="media" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label>商品图片</Label>
                <div className="grid grid-cols-4 gap-4">
                  <div className="border border-dashed rounded-md flex flex-col items-center justify-center p-4 h-32 cursor-pointer hover:bg-muted/50">
                    <Plus className="h-8 w-8 text-muted-foreground mb-2" />
                    <span className="text-sm text-muted-foreground">上传图片</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">支持 JPG、PNG 格式，建议尺寸 800x800 像素</p>
              </div>
            </TabsContent>

            {/* 课程内容标签页 */}
            <TabsContent value="course" className="space-y-6 mt-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">课程章节管理</h3>
                  <p className="text-sm text-muted-foreground">添加和管理视频课程的章节内容</p>
                </div>
                <Button size="sm" onClick={handleAddChapter}>
                  <Plus className="h-4 w-4 mr-2" />
                  添加章节
                </Button>
              </div>

              {/* 课程介绍视频 */}
              <div className="space-y-2 border-b pb-6">
                <Label>课程介绍视频</Label>
                <div className="flex items-start gap-4">
                  <div className="border border-dashed rounded-md flex flex-col items-center justify-center p-4 h-32 w-48 cursor-pointer hover:bg-muted/50">
                    <Plus className="h-8 w-8 text-muted-foreground mb-2" />
                    <span className="text-sm text-muted-foreground">上传介绍视频</span>
                  </div>
                  <div className="space-y-2 flex-1">
                    <div className="space-y-1">
                      <Label htmlFor="introTitle">介绍标题</Label>
                      <Input id="introTitle" placeholder="输入介绍视频标题" />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="introDuration">视频时长 (分钟)</Label>
                      <Input id="introDuration" type="number" placeholder="例如：5" />
                    </div>
                    <div className="flex items-center space-x-2 mt-2">
                      <Checkbox id="freePreview" />
                      <label htmlFor="freePreview" className="text-sm">设为免费预览</label>
                    </div>
                  </div>
                </div>
              </div>

              {/* 章节列表 */}
              <div className="space-y-4">
                {/* 章节 1 */}
                <div className="border rounded-md">
                  <div className="p-4 bg-muted/30 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">第1章：瑜伽基础入门</span>
                      <Badge variant="outline">5 个视频</Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleEditChapter({ title: "瑜伽基础入门", id: "1" })}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteChapter({ title: "瑜伽基础入门", id: "1" })}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="p-4 space-y-3">
                    {/* 视频 1 */}
                    <div className="flex items-center justify-between border-b pb-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-primary/10 rounded-md p-2">
                          <div className="h-10 w-16 bg-primary/20 rounded flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                              <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                          </div>
                        </div>
                        <div>
                          <div className="font-medium">1.1 认识瑜伽</div>
                          <div className="text-sm text-muted-foreground">10分钟</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className="bg-green-50 cursor-pointer hover:bg-green-100"
                          onClick={() => handleTogglePreview({ title: "1.1 认识瑜伽", id: "1", isFreePreview: true })}
                        >
                          免费预览
                        </Badge>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditVideo({ title: "1.1 认识瑜伽", id: "1", duration: 10 }, { title: "瑜伽基础入门", id: "1" })}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteVideo({ title: "1.1 认识瑜伽", id: "1" })}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* 视频 2 */}
                    <div className="flex items-center justify-between border-b pb-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-primary/10 rounded-md p-2">
                          <div className="h-10 w-16 bg-primary/20 rounded flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary">
                              <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                          </div>
                        </div>
                        <div>
                          <div className="font-medium">1.2 基础呼吸法</div>
                          <div className="text-sm text-muted-foreground">15分钟</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditVideo({ title: "1.2 基础呼吸法", id: "2", duration: 15 }, { title: "瑜伽基础入门", id: "1" })}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteVideo({ title: "1.2 基础呼吸法", id: "2" })}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* 添加视频按钮 */}
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => handleAddVideo({ title: "瑜伽基础入门", id: "1" })}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      添加视频
                    </Button>
                  </div>
                </div>

                {/* 章节 2 */}
                <div className="border rounded-md">
                  <div className="p-4 bg-muted/30 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">第2章：基础体式练习</span>
                      <Badge variant="outline">3 个视频</Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleEditChapter({ title: "基础体式练习", id: "2" })}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteChapter({ title: "基础体式练习", id: "2" })}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* 课程设置 */}
              <div className="space-y-4 border-t pt-6 mt-6">
                <h3 className="text-lg font-medium">课程设置</h3>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="totalDuration">总课时 (分钟)</Label>
                    <Input id="totalDuration" type="number" placeholder="例如：120" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="difficulty">难度级别</Label>
                    <Select defaultValue="beginner">
                      <SelectTrigger>
                        <SelectValue placeholder="选择难度级别" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="beginner">入门级</SelectItem>
                        <SelectItem value="intermediate">中级</SelectItem>
                        <SelectItem value="advanced">高级</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="prerequisites">学习前提</Label>
                  <Textarea id="prerequisites" placeholder="学习本课程需要的前置知识或技能" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetAudience">适合人群</Label>
                  <Textarea id="targetAudience" placeholder="描述本课程适合哪些人群学习" />
                </div>

                <div className="space-y-2">
                  <Label>学习目标</Label>
                  <div className="border rounded-md p-3 space-y-3">
                    <div className="flex items-center gap-2">
                      <Input placeholder="添加学习目标" className="flex-1" />
                      <Button variant="outline" size="sm">添加</Button>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between bg-muted/30 p-2 rounded-md">
                        <span>掌握基础瑜伽体式</span>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="flex items-center justify-between bg-muted/30 p-2 rounded-md">
                        <span>学会正确的呼吸方法</span>
                        <Button variant="ghost" size="icon">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setShowAddEditDialog(false)}>取消</Button>
            <Button>{selectedProduct ? "保存修改" : "创建商品"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 章节编辑对话框 */}
      <Dialog open={showChapterDialog} onOpenChange={setShowChapterDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{selectedChapter ? "编辑章节" : "添加章节"}</DialogTitle>
            <DialogDescription>
              {selectedChapter ? "修改章节信息" : "创建新的课程章节"}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="chapterTitle">章节标题</Label>
              <Input
                id="chapterTitle"
                placeholder="例如：第1章：瑜伽基础入门"
                defaultValue={selectedChapter?.title || ""}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="chapterDescription">章节描述 (可选)</Label>
              <Textarea
                id="chapterDescription"
                placeholder="简要描述本章节内容"
                defaultValue={selectedChapter?.description || ""}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowChapterDialog(false)}>取消</Button>
            <Button>{selectedChapter ? "保存修改" : "创建章节"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 视频编辑对话框 */}
      <Dialog open={showVideoDialog} onOpenChange={setShowVideoDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedVideo ? "编辑视频" : "添加视频"}</DialogTitle>
            <DialogDescription>
              {selectedVideo ? "修改视频信息" : `添加视频到章节 "${selectedChapter?.title}"`}
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-2 gap-6 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="videoTitle">视频标题</Label>
                <Input
                  id="videoTitle"
                  placeholder="例如：1.1 认识瑜伽"
                  defaultValue={selectedVideo?.title || ""}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="videoDuration">视频时长 (分钟)</Label>
                <Input
                  id="videoDuration"
                  type="number"
                  placeholder="例如：15"
                  defaultValue={selectedVideo?.duration || ""}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="videoDescription">视频描述 (可选)</Label>
                <Textarea
                  id="videoDescription"
                  placeholder="简要描述视频内容"
                  defaultValue={selectedVideo?.description || ""}
                />
              </div>

              <div className="flex items-center space-x-2 mt-4">
                <Checkbox
                  id="videoFreePreview"
                  defaultChecked={selectedVideo?.isFreePreview || false}
                />
                <label htmlFor="videoFreePreview" className="text-sm font-medium">
                  设为免费预览
                </label>
              </div>
            </div>

            <div className="space-y-4">
              <Label>上传视频</Label>
              <div className="border border-dashed rounded-md flex flex-col items-center justify-center p-6 h-48 cursor-pointer hover:bg-muted/50">
                {selectedVideo?.videoUrl ? (
                  <div className="flex flex-col items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary mb-2">
                      <polygon points="5 3 19 12 5 21 5 3"></polygon>
                    </svg>
                    <span className="text-sm font-medium">视频已上传</span>
                    <span className="text-xs text-muted-foreground mt-1">点击更换视频</span>
                  </div>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground mb-2">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="17 8 12 3 7 8"></polyline>
                      <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                    <span className="text-sm font-medium">点击上传视频</span>
                    <span className="text-xs text-muted-foreground mt-1">支持 MP4、WebM 格式</span>
                  </>
                )}
              </div>

              <div className="space-y-2">
                <Label>视频缩略图 (可选)</Label>
                <div className="border border-dashed rounded-md flex flex-col items-center justify-center p-4 h-24 cursor-pointer hover:bg-muted/50">
                  <Plus className="h-6 w-6 text-muted-foreground mb-1" />
                  <span className="text-xs text-muted-foreground">上传缩略图</span>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowVideoDialog(false)}>取消</Button>
            <Button>{selectedVideo ? "保存修改" : "添加视频"}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
