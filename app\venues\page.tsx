"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { VenueTable } from "@/components/venue-table"
import { VenueCalendar } from "@/components/venue-calendar"
import { VenueGrid } from "@/components/venues/venue-grid"
import { Plus, Filter, Download, Upload } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useState } from "react"
import { AddVenueDialog } from "@/components/venues/add-venue-dialog"
import { VenueStatsDialog } from "@/components/venues/venue-stats-dialog"
import { AdvancedFilterDialog } from "@/components/venues/advanced-filter-dialog"

export default function VenuesPage() {
  const [isAddVenueOpen, setIsAddVenueOpen] = useState(false)
  const [isStatsOpen, setIsStatsOpen] = useState(false)
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [viewMode, setViewMode] = useState("list")
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">场地管理</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setIsStatsOpen(true)}>
            场地统计
          </Button>
          <Button onClick={() => setIsAddVenueOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加场地
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/3">
          <Input placeholder="搜索场地名称或ID" value={searchQuery} onChange={(e) => setSearchQuery(e.target.value)} />
        </div>
        <div className="flex gap-2">
          <Select defaultValue="all">
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="场地状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="available">可用</SelectItem>
              <SelectItem value="maintenance">维护中</SelectItem>
              <SelectItem value="booked">已预订</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" onClick={() => setIsFilterOpen(true)}>
            <Filter className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex gap-2 ml-auto">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="mr-2 h-4 w-4" />
            导入
          </Button>
        </div>
      </div>

      <Tabs defaultValue="list" value={viewMode} onValueChange={setViewMode}>
        <TabsList>
          <TabsTrigger value="list">列表视图</TabsTrigger>
          <TabsTrigger value="grid">网格视图</TabsTrigger>
          <TabsTrigger value="calendar">日历视图</TabsTrigger>
        </TabsList>
        <TabsContent value="list">
          <VenueTable searchQuery={searchQuery} />
        </TabsContent>
        <TabsContent value="grid">
          <VenueGrid searchQuery={searchQuery} />
        </TabsContent>
        <TabsContent value="calendar">
          <VenueCalendar />
        </TabsContent>
      </Tabs>

      <AddVenueDialog open={isAddVenueOpen} onOpenChange={setIsAddVenueOpen} />
      <VenueStatsDialog open={isStatsOpen} onOpenChange={setIsStatsOpen} />
      <AdvancedFilterDialog open={isFilterOpen} onOpenChange={setIsFilterOpen} />
    </div>
  )
}

