"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Copy, Download, Plus, RefreshCw } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useState } from "react"
import { AddScheduleDialog } from "@/components/coaches/schedule/add-schedule-dialog"
import { CopyScheduleDialog } from "@/components/coaches/schedule/copy-schedule-dialog"
import { DatePicker } from "@/components/coaches/schedule/date-picker"

export default function CoachSchedulePage() {
  const [showAddSchedule, setShowAddSchedule] = useState(false)
  const [showCopySchedule, setShowCopySchedule] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">教练排班</h1>
        <div className="flex gap-2">
          <DatePicker date={selectedDate} onSelect={setSelectedDate} />
          <Button variant="outline" className="gap-2" onClick={() => setShowCopySchedule(true)}>
            <Copy className="h-4 w-4" />
            <span className="hidden md:inline">复制排班</span>
          </Button>
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            <span className="hidden md:inline">导出排班</span>
          </Button>
          <Button onClick={() => setShowAddSchedule(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加排班
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/3">
          <Select defaultValue="all">
            <SelectTrigger>
              <SelectValue placeholder="选择教练" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有教练</SelectItem>
              <SelectItem value="1">张教练</SelectItem>
              <SelectItem value="2">李教练</SelectItem>
              <SelectItem value="3">王教练</SelectItem>
              <SelectItem value="4">赵教练</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="w-full md:w-1/3">
          <Select defaultValue="all">
            <SelectTrigger>
              <SelectValue placeholder="课程类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              <SelectItem value="basic">基础瑜伽</SelectItem>
              <SelectItem value="advanced">高级瑜伽</SelectItem>
              <SelectItem value="yin">阴瑜伽</SelectItem>
              <SelectItem value="prenatal">孕产瑜伽</SelectItem>
              <SelectItem value="aerial">空中瑜伽</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="w-full md:w-1/3">
          <Select defaultValue="all">
            <SelectTrigger>
              <SelectValue placeholder="场地" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有场地</SelectItem>
              <SelectItem value="1">1号瑜伽室</SelectItem>
              <SelectItem value="2">2号瑜伽室</SelectItem>
              <SelectItem value="3">3号瑜伽室</SelectItem>
              <SelectItem value="4">空中瑜伽室</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="week">
        <TabsList>
          <TabsTrigger value="day">日视图</TabsTrigger>
          <TabsTrigger value="week">周视图</TabsTrigger>
          <TabsTrigger value="month">月视图</TabsTrigger>
        </TabsList>
        <TabsContent value="week" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>2025年3月24日 - 3月30日</CardTitle>
                <CardDescription>本周教练排班</CardDescription>
              </div>
              <Button variant="outline" size="icon">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* 教练排班表 */}
                {["张教练", "李教练", "王教练", "赵教练", "刘教练"].map((coach, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="/placeholder.svg?height=32&width=32" alt={coach} />
                        <AvatarFallback>{coach[0]}</AvatarFallback>
                      </Avatar>
                      <h3 className="font-medium">{coach}</h3>
                    </div>
                    <div className="grid grid-cols-7 gap-2">
                      {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, dayIndex) => (
                        <div key={dayIndex} className="border rounded-md p-2 h-24 relative">
                          <div className="text-xs font-medium text-muted-foreground">{day}</div>
                          {index === 0 && dayIndex === 0 && (
                            <div className="mt-1 text-xs bg-blue-100 rounded p-1 border-l-2 border-blue-500 cursor-pointer hover:bg-blue-200 transition-colors">
                              <div>基础瑜伽</div>
                              <div>10:00-11:30</div>
                              <div className="text-muted-foreground">1号瑜伽室</div>
                            </div>
                          )}
                          {index === 1 && dayIndex === 1 && (
                            <div className="mt-1 text-xs bg-green-100 rounded p-1 border-l-2 border-green-500 cursor-pointer hover:bg-green-200 transition-colors">
                              <div>高级瑜伽</div>
                              <div>14:00-15:30</div>
                              <div className="text-muted-foreground">2号瑜伽室</div>
                            </div>
                          )}
                          {index === 2 && dayIndex === 2 && (
                            <div className="mt-1 text-xs bg-yellow-100 rounded p-1 border-l-2 border-yellow-500 cursor-pointer hover:bg-yellow-200 transition-colors">
                              <div>阴瑜伽</div>
                              <div>16:00-17:00</div>
                              <div className="text-muted-foreground">3号瑜伽室</div>
                            </div>
                          )}
                          {index === 3 && dayIndex === 3 && (
                            <div className="mt-1 text-xs bg-purple-100 rounded p-1 border-l-2 border-purple-500 cursor-pointer hover:bg-purple-200 transition-colors">
                              <div>孕产瑜伽</div>
                              <div>18:30-20:00</div>
                              <div className="text-muted-foreground">2号瑜伽室</div>
                            </div>
                          )}
                          {index === 4 && dayIndex === 4 && (
                            <div className="mt-1 text-xs bg-pink-100 rounded p-1 border-l-2 border-pink-500 cursor-pointer hover:bg-pink-200 transition-colors">
                              <div>空中瑜伽</div>
                              <div>09:00-10:30</div>
                              <div className="text-muted-foreground">空中瑜伽室</div>
                            </div>
                          )}
                          {index === 0 && dayIndex === 5 && (
                            <>
                              <div className="mt-1 mb-1 text-xs bg-blue-100 rounded p-1 border-l-2 border-blue-500 cursor-pointer hover:bg-blue-200 transition-colors">
                                <div>基础瑜伽</div>
                                <div>09:00-10:30</div>
                                <div className="text-muted-foreground">1号瑜伽室</div>
                              </div>
                              <div className="text-xs bg-green-100 rounded p-1 border-l-2 border-green-500 cursor-pointer hover:bg-green-200 transition-colors">
                                <div>高级瑜伽</div>
                                <div>15:00-16:30</div>
                                <div className="text-muted-foreground">2号瑜伽室</div>
                              </div>
                            </>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="day" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>2025年3月28日 星期五</CardTitle>
                <CardDescription>今日教练排班</CardDescription>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Calendar className="mr-2 h-4 w-4" />
                  前一天
                </Button>
                <Button variant="outline" size="sm">
                  后一天
                  <Calendar className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 时间轴 */}
                <div className="grid grid-cols-[80px_1fr] gap-4">
                  <div className="space-y-8">
                    <div className="text-sm font-medium text-muted-foreground">08:00</div>
                    <div className="text-sm font-medium text-muted-foreground">10:00</div>
                    <div className="text-sm font-medium text-muted-foreground">12:00</div>
                    <div className="text-sm font-medium text-muted-foreground">14:00</div>
                    <div className="text-sm font-medium text-muted-foreground">16:00</div>
                    <div className="text-sm font-medium text-muted-foreground">18:00</div>
                    <div className="text-sm font-medium text-muted-foreground">20:00</div>
                  </div>
                  <div className="relative">
                    {/* 课程卡片 */}
                    <div className="absolute top-0 left-0 w-full h-16 bg-blue-100 rounded-md p-2 border-l-4 border-blue-500 cursor-pointer hover:bg-blue-200 transition-colors">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src="/placeholder.svg?height=24&width=24" alt="张教练" />
                          <AvatarFallback>张</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm">基础瑜伽入门</div>
                          <div className="text-xs text-muted-foreground">08:00-09:30 | 1号瑜伽室</div>
                        </div>
                      </div>
                    </div>
                    <div className="absolute top-24 left-0 w-full h-16 bg-green-100 rounded-md p-2 border-l-4 border-green-500 cursor-pointer hover:bg-green-200 transition-colors">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src="/placeholder.svg?height=24&width=24" alt="李教练" />
                          <AvatarFallback>李</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm">高级瑜伽进阶</div>
                          <div className="text-xs text-muted-foreground">10:00-11:30 | 2号瑜伽室</div>
                        </div>
                      </div>
                    </div>
                    <div className="absolute top-72 left-0 w-full h-16 bg-yellow-100 rounded-md p-2 border-l-4 border-yellow-500 cursor-pointer hover:bg-yellow-200 transition-colors">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src="/placeholder.svg?height=24&width=24" alt="王教练" />
                          <AvatarFallback>王</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm">阴瑜伽放松</div>
                          <div className="text-xs text-muted-foreground">14:00-15:00 | 3号瑜伽室</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="month" className="mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>2025年3月</CardTitle>
                <CardDescription>本月教练排班</CardDescription>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Calendar className="mr-2 h-4 w-4" />
                  上个月
                </Button>
                <Button variant="outline" size="sm">
                  下个月
                  <Calendar className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-7 gap-1">
                {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, index) => (
                  <div key={index} className="text-center text-sm font-medium p-2">
                    {day}
                  </div>
                ))}
                {Array.from({ length: 31 }).map((_, index) => (
                  <div key={index} className="border rounded-md p-2 h-24 relative">
                    <div className="text-xs font-medium">{index + 1}</div>
                    {index === 4 && <div className="mt-1 text-xs bg-blue-100 rounded p-1 text-center">3 个课程</div>}
                    {index === 10 && <div className="mt-1 text-xs bg-green-100 rounded p-1 text-center">2 个课程</div>}
                    {index === 17 && <div className="mt-1 text-xs bg-yellow-100 rounded p-1 text-center">4 个课程</div>}
                    {index === 24 && <div className="mt-1 text-xs bg-purple-100 rounded p-1 text-center">5 个课程</div>}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <AddScheduleDialog open={showAddSchedule} onOpenChange={setShowAddSchedule} />
      <CopyScheduleDialog open={showCopySchedule} onOpenChange={setShowCopySchedule} />
    </div>
  )
}

