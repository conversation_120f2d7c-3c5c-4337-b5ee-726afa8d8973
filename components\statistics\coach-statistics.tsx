"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Legend,
  LineChart,
  Line,
} from "recharts"

const coachPerformanceData = [
  { name: "张教练", courses: 45, students: 180, rating: 4.8 },
  { name: "李教练", courses: 38, students: 152, rating: 4.9 },
  { name: "王教练", courses: 42, students: 168, rating: 4.7 },
  { name: "赵教练", courses: 30, students: 120, rating: 4.6 },
  { name: "刘教练", courses: 35, students: 140, rating: 4.5 },
]

const coachSpecialtyData = [
  { name: "基础瑜伽", value: 35, color: "#4285F4" },
  { name: "高级瑜伽", value: 25, color: "#34A853" },
  { name: "阴瑜伽", value: 15, color: "#FBBC05" },
  { name: "孕产瑜伽", value: 10, color: "#EA4335" },
  { name: "空中瑜伽", value: 15, color: "#FF6D91" },
]

const coachRatingTrendData = [
  { month: "1月", rating: 4.5 },
  { month: "2月", rating: 4.6 },
  { month: "3月", rating: 4.6 },
  { month: "4月", rating: 4.7 },
  { month: "5月", rating: 4.7 },
  { month: "6月", rating: 4.8 },
  { month: "7月", rating: 4.8 },
  { month: "8月", rating: 4.7 },
  { month: "9月", rating: 4.8 },
  { month: "10月", rating: 4.9 },
  { month: "11月", rating: 4.8 },
  { month: "12月", rating: 4.8 },
]

export function CoachStatistics() {
  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">教练总数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">较上月增长 20%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均评分</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.8</div>
            <p className="text-xs text-muted-foreground">较上月增长 0.1</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总课时数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">480</div>
            <p className="text-xs text-muted-foreground">较上月增长 8.5%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">人均课时</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">40</div>
            <p className="text-xs text-muted-foreground">较上月下降 5.2%</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>教练业绩排名</CardTitle>
          <CardDescription>按课程数量排序</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={coachPerformanceData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={80} />
                <Tooltip />
                <Legend />
                <Bar dataKey="courses" name="课程数" fill="#4285F4" />
                <Bar dataKey="students" name="学员数" fill="#34A853" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>教练专长分布</CardTitle>
            <CardDescription>不同专长教练占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={coachSpecialtyData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {coachSpecialtyData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value}%`, "占比"]} />
                  <Legend layout="vertical" verticalAlign="middle" align="right" />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>教练评分趋势</CardTitle>
            <CardDescription>过去12个月教练平均评分变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={coachRatingTrendData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="month" />
                  <YAxis domain={[4, 5]} />
                  <Tooltip />
                  <Line type="monotone" dataKey="rating" stroke="#4285F4" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

