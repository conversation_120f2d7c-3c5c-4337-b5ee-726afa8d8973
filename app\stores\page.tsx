"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Store, Plus, MapPin, Users, Phone, Calendar, Edit, Trash2, AlertCircle, Search, ListFilter, LayoutGrid, SearchX, Eye, User, BookOpen, UserPlus, DollarSign, Star } from "lucide-react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "@/components/ui/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { storeApi } from "@/lib/api"

// 门店数据接口
interface StoreData {
  id: string | number;
  name: string;
  address: string;
  phone: string;
  managerName: string;
  employeesCount: number;
  status: 'active' | 'inactive' | 'pending';
  createdAt: string;
  description?: string;
  area?: string;
  type?: 'flagship' | 'standard' | 'mini';
  courseCount?: number;
  memberCount?: number;
  revenue?: number;
  rating?: number;
}

// 默认模拟数据
const mockStores: StoreData[] = [
  {
    id: 1,
    name: "静心瑜伽旗舰店",
    address: "北京市朝阳区建国路88号",
    phone: "13800138001",
    managerName: "张晓华",
    employeesCount: 12,
    status: 'active',
    createdAt: "2023-01-15",
    description: "我们的旗舰店，提供全面的瑜伽课程和服务，拥有6间独立教室和高级淋浴设施",
    area: "500",
    type: "flagship",
    courseCount: 28,
    memberCount: 580,
    revenue: 126800,
    rating: 4.8
  },
  {
    id: 2,
    name: "静心瑜伽国贸店",
    address: "北京市朝阳区国贸中心12号楼",
    phone: "13900139002",
    managerName: "李明",
    employeesCount: 8,
    status: 'active',
    createdAt: "2023-04-20",
    description: "位于CBD核心区的高端店，专注于商务人士的瑜伽养生",
    area: "300",
    type: "standard",
    courseCount: 18,
    memberCount: 320,
    revenue: 87600,
    rating: 4.6
  },
  {
    id: 3,
    name: "静心瑜伽望京店",
    address: "北京市朝阳区望京SOHO T1",
    phone: "13800138003",
    managerName: "王芳",
    employeesCount: 6,
    status: 'active',
    createdAt: "2023-06-10",
    description: "服务望京地区的IT白领，提供清晨和晚间课程",
    area: "250",
    type: "standard",
    courseCount: 15,
    memberCount: 280,
    revenue: 65400,
    rating: 4.7
  },
  {
    id: 4,
    name: "静心瑜伽上地店",
    address: "北京市海淀区上地信息路甲28号",
    phone: "13800138004",
    managerName: "赵静",
    employeesCount: 5,
    status: 'active',
    createdAt: "2023-08-05",
    description: "专注于年轻科技工作者的瑜伽课程，提供体式调整和压力舒缓",
    area: "200",
    type: "standard",
    courseCount: 12,
    memberCount: 210,
    revenue: 45800,
    rating: 4.5
  },
  {
    id: 5,
    name: "静心瑜伽五道口店",
    address: "北京市海淀区五道口华清商务会馆",
    phone: "13900139005",
    managerName: "刘伟",
    employeesCount: 4,
    status: 'inactive',
    createdAt: "2023-05-22",
    description: "暂停营业，即将升级改造",
    area: "180",
    type: "mini",
    courseCount: 0,
    memberCount: 150,
    revenue: 28900,
    rating: 4.3
  }
];

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, { message: "门店名称至少需要2个字符" }).max(50, { message: "门店名称不能超过50个字符" }),
  address: z.string().min(5, { message: "请输入有效的地址" }).max(200, { message: "地址不能超过200个字符" }),
  phone: z.string().regex(/^1[3-9]\d{9}$/, { message: "请输入有效的手机号码" }),
  managerName: z.string().min(2, { message: "管理员姓名至少需要2个字符" }).max(20, { message: "管理员姓名不能超过20个字符" }),
  description: z.string().max(500, { message: "描述不能超过500个字符" }).optional(),
  area: z.string().regex(/^\d+$/, { message: "面积必须是数字" }).optional(),
  type: z.enum(["flagship", "standard", "mini"], { message: "请选择门店类型" }),
});

type FormValues = z.infer<typeof formSchema>;

export default function StoresPage() {
  const [stores, setStores] = useState<StoreData[]>([]);
  const [filteredStores, setFilteredStores] = useState<StoreData[]>([]);
  const [loading, setLoading] = useState(true);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDetailDialog, setOpenDetailDialog] = useState(false);
  const [selectedStore, setSelectedStore] = useState<StoreData | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"list" | "card">("list");
  const router = useRouter();
  const { user } = useAuth();

  // 创建表单
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      address: "",
      phone: "",
      managerName: "",
      description: "",
      area: "",
      type: "standard"
    }
  });

  // 获取门店列表
  useEffect(() => {
    const fetchStores = async () => {
      setLoading(true);
      try {
        // 获取当前用户的租户ID
        const tenantId = user?.tenant_id;

        if (!tenantId) {
          console.error('未找到租户ID');
          setStores([]);
          setFilteredStores([]);
          setLoading(false);
          return;
        }

        console.log('获取门店列表，租户ID:', tenantId);

        // 调用统一API获取门店数据
        const result: any = await storeApi.getAllStores({
          tenantId: tenantId.toString()
        });

        if (result.code === 0 || result.code === 200) {
          const storeData = result.data?.list || result.data || [];
          setStores(storeData);
          setFilteredStores(storeData);

          // 更新是否有门店的状态
          localStorage.setItem('hasStores', storeData.length > 0 ? 'true' : 'false');
        } else {
          throw new Error(result.message || result.msg || '获取门店列表失败');
        }

      } catch (error: any) {
        console.error('获取门店列表失败:', error);

        toast({
          title: "获取门店列表失败",
          description: error.message || "请稍后再试",
          variant: "destructive"
        });

        // 出错时设置为空数组
        setStores([]);
        setFilteredStores([]);
        localStorage.setItem('hasStores', 'false');
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
  }, [user]);

  // 搜索过滤门店
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredStores(stores);
      return;
    }
    
    const lowerCaseQuery = searchQuery.toLowerCase();
    const filtered = stores.filter(store => 
      store.name.toLowerCase().includes(lowerCaseQuery) ||
      store.address.toLowerCase().includes(lowerCaseQuery) ||
      store.managerName.toLowerCase().includes(lowerCaseQuery) ||
      store.phone.includes(searchQuery)
    );
    
    setFilteredStores(filtered);
  }, [searchQuery, stores]);

  // 处理添加门店
  const handleAddStore = async (values: FormValues) => {
    try {
      const tenantId = user?.tenant_id;

      if (!tenantId) {
        toast({
          title: "添加门店失败",
          description: "未找到租户信息",
          variant: "destructive"
        });
        return;
      }

      console.log('添加门店:', values);

      // 调用统一API创建门店
      const result: any = await storeApi.createStore({
        ...values,
        tenantId
      });

      if (result.code === 0 || result.code === 200) {
        // 更新门店列表
        const updatedStores = [...stores, result.data];
        setStores(updatedStores);
        setFilteredStores(updatedStores);

        // 更新本地存储状态
        localStorage.setItem('hasStores', 'true');

        toast({
          title: "门店添加成功",
          description: result.message || result.msg || `${values.name} 已成功添加`,
        });

        // 关闭对话框并重置表单
        setOpenAddDialog(false);
        form.reset();
      } else {
        throw new Error(result.message || result.msg || '添加门店失败');
      }
    } catch (error: any) {
      console.error('添加门店失败:', error);
      toast({
        title: "添加门店失败",
        description: error.message || "请稍后再试",
        variant: "destructive"
      });
    }
  };

  // 处理删除门店
  const handleDeleteStore = async () => {
    if (!selectedStore) return;

    try {
      console.log('删除门店:', selectedStore.id);

      // 调用API删除门店
      const response = await fetch(`/api/stores/${selectedStore.id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        // 更新门店列表
        const updatedStores = stores.filter(store => store.id !== selectedStore.id);
        setStores(updatedStores);
        setFilteredStores(updatedStores);

        // 更新本地存储状态
        localStorage.setItem('hasStores', updatedStores.length > 0 ? 'true' : 'false');

        toast({
          title: "门店删除成功",
          description: result.message || `${selectedStore.name} 已成功删除`,
        });

        // 关闭对话框
        setOpenDeleteDialog(false);
        setSelectedStore(null);
      } else {
        throw new Error(result.error || '删除门店失败');
      }
    } catch (error: any) {
      console.error('删除门店失败:', error);
      toast({
        title: "删除门店失败",
        description: error.message || "请稍后再试",
        variant: "destructive"
      });
    }
  };

  // 查看门店详情
  const handleViewDetails = (store: StoreData) => {
    setSelectedStore(store);
    setOpenDetailDialog(true);
  };

  // 编辑门店
  const handleEditStore = (store: StoreData) => {
    setSelectedStore(store);
    // 填充表单数据
    form.reset({
      name: store.name,
      address: store.address,
      phone: store.phone,
      managerName: store.managerName,
      description: store.description || '',
      area: store.area || '',
      type: store.type || 'standard'
    });
    setOpenEditDialog(true);
  };

  // 处理更新门店
  const handleUpdateStore = async (values: FormValues) => {
    if (!selectedStore) return;

    try {
      console.log('更新门店:', selectedStore.id, values);

      // 调用API更新门店
      const response = await fetch(`/api/stores/${selectedStore.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values)
      });

      const result = await response.json();

      if (result.success) {
        // 更新门店列表
        const updatedStores = stores.map(store =>
          store.id === selectedStore.id ? result.data : store
        );
        setStores(updatedStores);
        setFilteredStores(updatedStores);

        toast({
          title: "门店更新成功",
          description: result.message || `${values.name} 已成功更新`,
        });

        // 关闭对话框并重置表单
        setOpenEditDialog(false);
        setSelectedStore(null);
        form.reset();
      } else {
        throw new Error(result.error || '更新门店失败');
      }
    } catch (error: any) {
      console.error('更新门店失败:', error);
      toast({
        title: "更新门店失败",
        description: error.message || "请稍后再试",
        variant: "destructive"
      });
    }
  };

  // 渲染门店卡片视图
  const renderCardView = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredStores.map((store) => (
          <Card key={store.id} className="overflow-hidden hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex justify-between">
                <CardTitle>{store.name}</CardTitle>
                <Badge variant={
                  store.status === "active" ? "default" : 
                  store.status === "inactive" ? "secondary" : 
                  "outline"
                }>
                  {store.status === "active" ? "营业中" : 
                   store.status === "inactive" ? "已停业" : 
                   "待审核"}
                </Badge>
              </div>
              <CardDescription>
                <div className="flex items-center mt-1">
                  <MapPin className="mr-1 h-3 w-3 text-muted-foreground" />
                  {store.address}
                </div>
              </CardDescription>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center">
                    <Phone className="mr-1 h-3 w-3 text-muted-foreground" />
                    <span>{store.phone}</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="mr-1 h-3 w-3 text-muted-foreground" />
                    <span>{store.employeesCount} 名员工</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="mr-1 h-3 w-3 text-muted-foreground" />
                    <span>创建于 {store.createdAt}</span>
                  </div>
                  <div>
                    {store.type === "flagship" && "旗舰店"}
                    {store.type === "standard" && "标准店"}
                    {store.type === "mini" && "迷你店"}
                    {store.area && ` · ${store.area}㎡`}
                  </div>
                </div>
                {store.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2 mt-2">
                    {store.description}
                  </p>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between pt-2">
              <Button variant="outline" size="sm" onClick={() => handleViewDetails(store)}>
                查看详情
              </Button>
              <div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="mr-1"
                  onClick={() => handleEditStore(store)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="text-red-500"
                  onClick={() => {
                    setSelectedStore(store);
                    setOpenDeleteDialog(true);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  };

  // 渲染门店列表视图
  const renderListView = () => {
    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>门店名称</TableHead>
            <TableHead>地址</TableHead>
            <TableHead>联系电话</TableHead>
            <TableHead>管理员</TableHead>
            <TableHead>员工数</TableHead>
            <TableHead>状态</TableHead>
            <TableHead>创建日期</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredStores.map((store) => (
            <TableRow key={store.id}>
              <TableCell className="font-medium">{store.name}</TableCell>
              <TableCell>
                <div className="flex items-center">
                  <MapPin className="mr-1 h-3 w-3 text-muted-foreground" />
                  {store.address}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex items-center">
                  <Phone className="mr-1 h-3 w-3 text-muted-foreground" />
                  {store.phone}
                </div>
              </TableCell>
              <TableCell>{store.managerName}</TableCell>
              <TableCell>
                <div className="flex items-center">
                  <Users className="mr-1 h-3 w-3 text-muted-foreground" />
                  {store.employeesCount}
                </div>
              </TableCell>
              <TableCell>
                <Badge variant={
                  store.status === "active" ? "default" : 
                  store.status === "inactive" ? "secondary" : 
                  "outline"
                }>
                  {store.status === "active" ? "营业中" : 
                   store.status === "inactive" ? "已停业" : 
                   "待审核"}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex items-center">
                  <Calendar className="mr-1 h-3 w-3 text-muted-foreground" />
                  {store.createdAt}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <Button variant="ghost" size="icon" className="mr-1" onClick={() => handleViewDetails(store)}>
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="mr-1"
                  onClick={() => handleEditStore(store)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="text-red-500"
                  onClick={() => {
                    setSelectedStore(store);
                    setOpenDeleteDialog(true);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  // 展示空状态
  if (!loading && filteredStores.length === 0 && !searchQuery) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex justify-between">
          <h1 className="text-2xl font-bold">门店管理</h1>
        </div>
        
        <Card className="bg-white dark:bg-gray-950">
          <CardContent className="flex flex-col items-center justify-center p-10 text-center">
            <div className="rounded-full bg-primary/10 p-4 mb-4">
              <Store className="h-10 w-10 text-primary" />
            </div>
            <h2 className="text-xl font-semibold mb-2">还没有门店</h2>
            <p className="text-muted-foreground mb-6 max-w-md">
              您尚未添加任何门店。添加门店后，您将能够管理课程、会员等信息。
            </p>
            <Button onClick={() => setOpenAddDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              添加第一个门店
            </Button>
          </CardContent>
        </Card>

        {/* 添加门店对话框 */}
        <Dialog open={openAddDialog} onOpenChange={setOpenAddDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>添加新门店</DialogTitle>
              <DialogDescription>
                请填写门店基本信息，添加后可以进一步完善门店设置。
              </DialogDescription>
            </DialogHeader>
            
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleAddStore)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>门店名称</FormLabel>
                      <FormControl>
                        <Input placeholder="例如：静心瑜伽馆主店" {...field} />
                      </FormControl>
                      <FormDescription>
                        输入您的门店全称，将显示在系统各处
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>门店类型</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="选择门店类型" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="flagship">旗舰店</SelectItem>
                            <SelectItem value="standard">标准店</SelectItem>
                            <SelectItem value="mini">迷你店</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          选择您门店的类型
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="area"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>门店面积（㎡）</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="例如：200" {...field} />
                        </FormControl>
                        <FormDescription>
                          输入门店实际使用面积
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>门店地址</FormLabel>
                      <FormControl>
                        <Input placeholder="例如：北京市朝阳区三里屯路" {...field} />
                      </FormControl>
                      <FormDescription>
                        输入详细的门店地址，包括城市、区域、街道名称等
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>联系电话</FormLabel>
                        <FormControl>
                          <Input placeholder="例如：13800138000" {...field} />
                        </FormControl>
                        <FormDescription>
                          输入门店联系电话
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="managerName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>门店管理员</FormLabel>
                        <FormControl>
                          <Input placeholder="例如：张三" {...field} />
                        </FormControl>
                        <FormDescription>
                          输入门店负责人姓名
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>门店描述</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="例如：本店专注于热瑜伽和流瑜伽教学，拥有专业的教练团队和舒适的练习环境..." 
                          className="resize-none" 
                          rows={4}
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        简要描述门店特色、服务等信息（选填）
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setOpenAddDialog(false)}>
                    取消
                  </Button>
                  <Button type="submit">保存门店</Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  // 正常展示门店列表
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">门店管理</h1>
        <Button onClick={() => setOpenAddDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          添加门店
        </Button>
      </div>
      
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>门店列表</CardTitle>
              <CardDescription>
                管理您的所有门店信息，包括地址、联系方式等
              </CardDescription>
            </div>
            <div className="flex items-center gap-4">
              <div className="relative w-60">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索门店名称或地址..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex border rounded-md overflow-hidden">
                <Button 
                  variant={viewMode === "list" ? "default" : "outline"} 
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode("list")}
                >
                  <ListFilter className="h-4 w-4 mr-1" />
                  列表
                </Button>
                <Button 
                  variant={viewMode === "card" ? "default" : "outline"} 
                  size="sm"
                  className="rounded-none"
                  onClick={() => setViewMode("card")}
                >
                  <LayoutGrid className="h-4 w-4 mr-1" />
                  卡片
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">加载中...</span>
            </div>
          ) : filteredStores.length === 0 ? (
            <div className="text-center py-12">
              <div className="rounded-full bg-muted w-12 h-12 flex items-center justify-center mx-auto mb-4">
                <SearchX className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">未找到匹配的门店</h3>
              <p className="text-muted-foreground mb-4">
                尝试使用不同的搜索词或清除搜索条件
              </p>
              <Button variant="outline" onClick={() => setSearchQuery("")}>
                清除搜索
              </Button>
            </div>
          ) : (
            viewMode === "list" ? renderListView() : renderCardView()
          )}
        </CardContent>
      </Card>

      {/* 门店详情对话框 */}
      <Dialog open={openDetailDialog} onOpenChange={setOpenDetailDialog}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>{selectedStore?.name}</DialogTitle>
            <DialogDescription>
              门店详细信息
            </DialogDescription>
          </DialogHeader>
          
          {selectedStore && (
            <div className="space-y-6">
              <div className="flex gap-4">
                <Badge variant={
                  selectedStore.status === "active" ? "default" : 
                  selectedStore.status === "inactive" ? "secondary" : 
                  "outline"
                }>
                  {selectedStore.status === "active" ? "营业中" : 
                   selectedStore.status === "inactive" ? "已停业" : 
                   "待审核"}
                </Badge>
                
                <Badge variant="outline">
                  {selectedStore.type === "flagship" ? "旗舰店" : 
                   selectedStore.type === "standard" ? "标准店" : 
                   "迷你店"}
                </Badge>
                
                {selectedStore.area && (
                  <Badge variant="outline">
                    {selectedStore.area}㎡
                  </Badge>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">基本信息</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{selectedStore.address}</span>
                    </div>
                    <div className="flex items-center">
                      <Phone className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{selectedStore.phone}</span>
                    </div>
                    <div className="flex items-center">
                      <User className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>店长: {selectedStore.managerName}</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{selectedStore.employeesCount} 名员工</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>创建于 {selectedStore.createdAt}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">运营数据</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <BookOpen className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{selectedStore.courseCount || 0} 个课程</span>
                    </div>
                    <div className="flex items-center">
                      <UserPlus className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{selectedStore.memberCount || 0} 名会员</span>
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>¥{selectedStore.revenue?.toLocaleString() || 0} 营收</span>
                    </div>
                    <div className="flex items-center">
                      <Star className="mr-2 h-4 w-4 text-yellow-500" />
                      <span>{selectedStore.rating || 0} 分 (评分)</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {selectedStore.description && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">门店描述</h3>
                  <p className="text-sm">{selectedStore.description}</p>
                </div>
              )}
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setOpenDetailDialog(false)}>
                  关闭
                </Button>
                <Button>
                  <Edit className="mr-2 h-4 w-4" />
                  编辑门店
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 编辑门店对话框 */}
      <Dialog open={openEditDialog} onOpenChange={setOpenEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑门店</DialogTitle>
            <DialogDescription>
              修改门店信息，更新后将立即生效。
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleUpdateStore)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>门店名称</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：静心瑜伽馆主店" {...field} />
                    </FormControl>
                    <FormDescription>
                      输入您的门店全称，将显示在系统各处
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>门店类型</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择门店类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="flagship">旗舰店</SelectItem>
                          <SelectItem value="standard">标准店</SelectItem>
                          <SelectItem value="mini">迷你店</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        选择您门店的类型
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="area"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>门店面积（㎡）</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="例如：200" {...field} />
                      </FormControl>
                      <FormDescription>
                        输入门店实际使用面积
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>门店地址</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：北京市朝阳区三里屯路" {...field} />
                    </FormControl>
                    <FormDescription>
                      输入详细的门店地址，包括城市、区域、街道名称等
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系电话</FormLabel>
                      <FormControl>
                        <Input placeholder="例如：13800138000" {...field} />
                      </FormControl>
                      <FormDescription>
                        输入门店联系电话
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="managerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>门店管理员</FormLabel>
                      <FormControl>
                        <Input placeholder="例如：张三" {...field} />
                      </FormControl>
                      <FormDescription>
                        输入门店负责人姓名
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>门店描述</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="例如：本店专注于热瑜伽和流瑜伽教学，拥有专业的教练团队和舒适的练习环境..."
                        className="resize-none"
                        rows={4}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      简要描述门店特色、服务等信息（选填）
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setOpenEditDialog(false)}>
                  取消
                </Button>
                <Button type="submit">更新门店</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 添加门店对话框 */}
      <Dialog open={openAddDialog} onOpenChange={setOpenAddDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加新门店</DialogTitle>
            <DialogDescription>
              请填写门店基本信息，添加后可以进一步完善门店设置。
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddStore)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>门店名称</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：静心瑜伽馆主店" {...field} />
                    </FormControl>
                    <FormDescription>
                      输入您的门店全称，将显示在系统各处
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>门店类型</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择门店类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="flagship">旗舰店</SelectItem>
                          <SelectItem value="standard">标准店</SelectItem>
                          <SelectItem value="mini">迷你店</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        选择您门店的类型
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="area"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>门店面积（㎡）</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="例如：200" {...field} />
                      </FormControl>
                      <FormDescription>
                        输入门店实际使用面积
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>门店地址</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：北京市朝阳区三里屯路" {...field} />
                    </FormControl>
                    <FormDescription>
                      输入详细的门店地址，包括城市、区域、街道名称等
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系电话</FormLabel>
                      <FormControl>
                        <Input placeholder="例如：13800138000" {...field} />
                      </FormControl>
                      <FormDescription>
                        输入门店联系电话
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="managerName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>门店管理员</FormLabel>
                      <FormControl>
                        <Input placeholder="例如：张三" {...field} />
                      </FormControl>
                      <FormDescription>
                        输入门店负责人姓名
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>门店描述</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="例如：本店专注于热瑜伽和流瑜伽教学，拥有专业的教练团队和舒适的练习环境..." 
                        className="resize-none" 
                        rows={4}
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      简要描述门店特色、服务等信息（选填）
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setOpenAddDialog(false)}>
                  取消
                </Button>
                <Button type="submit">保存门店</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 删除门店确认对话框 */}
      <Dialog open={openDeleteDialog} onOpenChange={setOpenDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除门店</DialogTitle>
            <DialogDescription>
              您确定要删除门店 "{selectedStore?.name}" 吗？此操作无法撤销，删除后相关的所有数据将无法恢复。
            </DialogDescription>
          </DialogHeader>
          
          <div className="p-4 border rounded-md bg-orange-50 dark:bg-orange-950 flex items-start gap-2">
            <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-orange-700 dark:text-orange-300">警告</h4>
              <p className="text-sm text-orange-600 dark:text-orange-400">
                删除门店将同时删除与该门店关联的所有数据，包括课程、会员、订单等信息。
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setOpenDeleteDialog(false)}>
              取消
            </Button>
            <Button type="button" variant="destructive" onClick={handleDeleteStore}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 