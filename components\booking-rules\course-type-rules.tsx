"use client"

import { useState } from "react"
import { TabsContent } from "@/components/ui/tabs"
import { CourseBookingRules } from "./course-booking-rules"
import { CourseCheckinRules } from "./course-checkin-rules"
import { CourseCancellationRules } from "./course-cancellation-rules"
import { CourseWaitlistRules } from "./course-waitlist-rules"
import { CoursePenaltyRules } from "./course-penalty-rules"

interface CourseTypeRulesProps {
  courseTypeId: string;
  activeTab: string;
  onChange: () => void;
}

export function CourseTypeRules({ courseTypeId, activeTab, onChange }: CourseTypeRulesProps) {
  return (
    <>
      <TabsContent value="booking" className="mt-6">
        <CourseBookingRules courseTypeId={courseTypeId} onChange={onChange} />
      </TabsContent>
      
      <TabsContent value="checkin" className="mt-6">
        <CourseCheckinRules courseTypeId={courseTypeId} onChange={onChange} />
      </TabsContent>
      
      <TabsContent value="cancellation" className="mt-6">
        <CourseCancellationRules courseTypeId={courseTypeId} onChange={onChange} />
      </TabsContent>
      
      <TabsContent value="waitlist" className="mt-6">
        <CourseWaitlistRules courseTypeId={courseTypeId} onChange={onChange} />
      </TabsContent>
      
      <TabsContent value="penalty" className="mt-6">
        <CoursePenaltyRules courseTypeId={courseTypeId} onChange={onChange} />
      </TabsContent>
    </>
  )
}
