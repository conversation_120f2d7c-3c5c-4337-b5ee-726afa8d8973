"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePicker } from "@/components/date-picker"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Search, Filter, Download, RefreshCw, MoreHorizontal, Eye, AlertCircle, Clock, User, Calendar, FileText, Info, Activity, Shield, Settings as SettingsIcon, Database, CreditCard, Users, CalendarDays } from "lucide-react"

// 定义操作日志类型
interface OperationLog {
  id: string
  userId: string
  userName: string
  userAvatar?: string
  module: string
  action: string
  details: string
  ip: string
  timestamp: string
  status: "success" | "failed" | "warning"
  userAgent?: string
}

// 模拟操作日志数据
const mockLogs: OperationLog[] = [
  {
    id: "log-001",
    userId: "user-001",
    userName: "管理员",
    userAvatar: "/placeholder.svg?height=32&width=32",
    module: "会员管理",
    action: "创建会员",
    details: "创建了新会员 '张三'",
    ip: "*************",
    timestamp: "2024-05-10 14:30:25",
    status: "success",
    userAgent: "Chrome ********* / Windows 10",
  },
  {
    id: "log-002",
    userId: "user-002",
    userName: "前台小李",
    userAvatar: "/placeholder.svg?height=32&width=32",
    module: "课程管理",
    action: "修改课程",
    details: "修改了课程 '哈他瑜伽初级' 的信息",
    ip: "*************",
    timestamp: "2024-05-10 15:45:12",
    status: "success",
    userAgent: "Chrome ********* / macOS 12.0.0",
  },
  {
    id: "log-003",
    userId: "user-001",
    userName: "管理员",
    userAvatar: "/placeholder.svg?height=32&width=32",
    module: "系统设置",
    action: "修改安全设置",
    details: "修改了密码策略",
    ip: "*************",
    timestamp: "2024-05-10 16:20:45",
    status: "success",
    userAgent: "Chrome ********* / Windows 10",
  },
  {
    id: "log-004",
    userId: "user-003",
    userName: "财务小王",
    userAvatar: "/placeholder.svg?height=32&width=32",
    module: "订单管理",
    action: "退款操作",
    details: "为订单 'ORD-20240510-001' 执行退款操作",
    ip: "*************",
    timestamp: "2024-05-10 17:05:30",
    status: "failed",
    userAgent: "Firefox 99.0 / Windows 10",
  },
  {
    id: "log-005",
    userId: "user-002",
    userName: "前台小李",
    userAvatar: "/placeholder.svg?height=32&width=32",
    module: "会员管理",
    action: "会员签到",
    details: "会员 '李四' 签到课程 '阴瑜伽'",
    ip: "*************",
    timestamp: "2024-05-11 09:15:20",
    status: "success",
    userAgent: "Chrome ********* / macOS 12.0.0",
  },
  {
    id: "log-006",
    userId: "user-004",
    userName: "教练张教练",
    userAvatar: "/placeholder.svg?height=32&width=32",
    module: "课程管理",
    action: "课程签到",
    details: "为课程 '流瑜伽中级' 进行签到操作",
    ip: "*************",
    timestamp: "2024-05-11 10:30:15",
    status: "success",
    userAgent: "Safari 15.0 / iOS 15.0",
  },
  {
    id: "log-007",
    userId: "user-001",
    userName: "管理员",
    userAvatar: "/placeholder.svg?height=32&width=32",
    module: "权限管理",
    action: "修改角色权限",
    details: "修改了 '前台' 角色的权限",
    ip: "*************",
    timestamp: "2024-05-11 11:45:30",
    status: "warning",
    userAgent: "Chrome ********* / Windows 10",
  },
  {
    id: "log-008",
    userId: "user-005",
    userName: "系统",
    module: "系统",
    action: "系统备份",
    details: "执行了自动系统备份",
    ip: "127.0.0.1",
    timestamp: "2024-05-11 23:00:00",
    status: "success",
    userAgent: "System",
  },
  {
    id: "log-009",
    userId: "user-003",
    userName: "财务小王",
    userAvatar: "/placeholder.svg?height=32&width=32",
    module: "会员卡管理",
    action: "创建会员卡",
    details: "创建了新的会员卡类型 '季度瑜伽卡'",
    ip: "*************",
    timestamp: "2024-05-12 09:30:45",
    status: "success",
    userAgent: "Firefox 99.0 / Windows 10",
  },
  {
    id: "log-010",
    userId: "user-002",
    userName: "前台小李",
    userAvatar: "/placeholder.svg?height=32&width=32",
    module: "会员管理",
    action: "修改会员信息",
    details: "修改了会员 '王五' 的联系方式",
    ip: "*************",
    timestamp: "2024-05-12 10:15:20",
    status: "success",
    userAgent: "Chrome ********* / macOS 12.0.0",
  },
]

// 获取模块图标
const getModuleIcon = (module: string) => {
  switch (module) {
    case "会员管理":
      return <Users className="h-4 w-4" />
    case "课程管理":
      return <CalendarDays className="h-4 w-4" />
    case "订单管理":
      return <CreditCard className="h-4 w-4" />
    case "系统设置":
      return <SettingsIcon className="h-4 w-4" />
    case "权限管理":
      return <Shield className="h-4 w-4" />
    case "会员卡管理":
      return <CreditCard className="h-4 w-4" />
    case "系统":
      return <Database className="h-4 w-4" />
    default:
      return <Activity className="h-4 w-4" />
  }
}

// 获取状态标签变体
const getStatusBadgeVariant = (status: string) => {
  switch (status) {
    case "success":
      return "success"
    case "failed":
      return "destructive"
    case "warning":
      return "warning"
    default:
      return "secondary"
  }
}

export default function LogsPage() {
  const [activeTab, setActiveTab] = useState("operation")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedModule, setSelectedModule] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [selectedDateRange, setSelectedDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedLog, setSelectedLog] = useState<OperationLog | null>(null)
  const [showLogDetail, setShowLogDetail] = useState(false)

  // 筛选日志
  const filteredLogs = mockLogs.filter((log) => {
    // 搜索查询
    const matchesSearch =
      searchQuery === "" ||
      log.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.details.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.ip.includes(searchQuery)

    // 模块筛选
    const matchesModule = selectedModule === "all" || log.module === selectedModule

    // 状态筛选
    const matchesStatus = selectedStatus === "all" || log.status === selectedStatus

    // 日期范围筛选
    const logDate = new Date(log.timestamp)
    const matchesDateRange =
      (!selectedDateRange.from || logDate >= selectedDateRange.from) &&
      (!selectedDateRange.to || logDate <= selectedDateRange.to)

    return matchesSearch && matchesModule && matchesStatus && matchesDateRange
  })

  // 分页
  const itemsPerPage = 10
  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage)
  const paginatedLogs = filteredLogs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  // 查看日志详情
  const handleViewLogDetail = (log: OperationLog) => {
    setSelectedLog(log)
    setShowLogDetail(true)
  }

  // 导出日志
  const handleExportLogs = () => {
    toast({
      title: "导出成功",
      description: "日志已成功导出为CSV文件",
    })
  }

  // 清除筛选条件
  const handleClearFilters = () => {
    setSearchQuery("")
    setSelectedModule("all")
    setSelectedStatus("all")
    setSelectedDateRange({
      from: undefined,
      to: undefined,
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">系统日志</h1>
          <p className="text-muted-foreground">
            查看和管理系统操作日志
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="operation">操作日志</TabsTrigger>
          <TabsTrigger value="security">安全日志</TabsTrigger>
          <TabsTrigger value="system">系统日志</TabsTrigger>
        </TabsList>

        <TabsContent value="operation" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <CardTitle>操作日志</CardTitle>
                  <CardDescription>
                    记录用户在系统中的所有操作
                  </CardDescription>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Button variant="outline" size="sm" onClick={handleExportLogs}>
                    <Download className="mr-2 h-4 w-4" />
                    导出日志
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* 筛选条件 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索用户、操作、IP..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Select
                    value={selectedModule}
                    onValueChange={setSelectedModule}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择模块" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有模块</SelectItem>
                      <SelectItem value="会员管理">会员管理</SelectItem>
                      <SelectItem value="课程管理">课程管理</SelectItem>
                      <SelectItem value="订单管理">订单管理</SelectItem>
                      <SelectItem value="系统设置">系统设置</SelectItem>
                      <SelectItem value="权限管理">权限管理</SelectItem>
                      <SelectItem value="会员卡管理">会员卡管理</SelectItem>
                      <SelectItem value="系统">系统</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select
                    value={selectedStatus}
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有状态</SelectItem>
                      <SelectItem value="success">成功</SelectItem>
                      <SelectItem value="failed">失败</SelectItem>
                      <SelectItem value="warning">警告</SelectItem>
                    </SelectContent>
                  </Select>
                  <DatePicker
                    selected={selectedDateRange}
                    onSelect={setSelectedDateRange}
                    placeholder="选择日期范围"
                    mode="range"
                  />
                </div>

                {/* 筛选标签 */}
                {(searchQuery || selectedModule !== "all" || selectedStatus !== "all" || selectedDateRange.from || selectedDateRange.to) && (
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="text-sm text-muted-foreground">筛选条件:</span>
                    {searchQuery && (
                      <Badge variant="outline" className="flex items-center gap-1">
                        <Search className="h-3 w-3" />
                        {searchQuery}
                      </Badge>
                    )}
                    {selectedModule !== "all" && (
                      <Badge variant="outline" className="flex items-center gap-1">
                        {getModuleIcon(selectedModule)}
                        {selectedModule}
                      </Badge>
                    )}
                    {selectedStatus !== "all" && (
                      <Badge variant="outline" className="flex items-center gap-1">
                        <Activity className="h-3 w-3" />
                        {selectedStatus === "success" ? "成功" : selectedStatus === "failed" ? "失败" : "警告"}
                      </Badge>
                    )}
                    {(selectedDateRange.from || selectedDateRange.to) && (
                      <Badge variant="outline" className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {selectedDateRange.from ? selectedDateRange.from.toLocaleDateString() : "开始"} - {selectedDateRange.to ? selectedDateRange.to.toLocaleDateString() : "结束"}
                      </Badge>
                    )}
                    <Button variant="ghost" size="sm" onClick={handleClearFilters} className="h-7 px-2">
                      清除筛选
                    </Button>
                  </div>
                )}

                {/* 日志表格 */}
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>用户</TableHead>
                        <TableHead>模块</TableHead>
                        <TableHead>操作</TableHead>
                        <TableHead>IP地址</TableHead>
                        <TableHead>时间</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paginatedLogs.length > 0 ? (
                        paginatedLogs.map((log) => (
                          <TableRow key={log.id}>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={log.userAvatar} alt={log.userName} />
                                  <AvatarFallback>{log.userName.charAt(0)}</AvatarFallback>
                                </Avatar>
                                <span>{log.userName}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                {getModuleIcon(log.module)}
                                <span>{log.module}</span>
                              </div>
                            </TableCell>
                            <TableCell>{log.action}</TableCell>
                            <TableCell>{log.ip}</TableCell>
                            <TableCell>{log.timestamp}</TableCell>
                            <TableCell>
                              <Badge variant={getStatusBadgeVariant(log.status)}>
                                {log.status === "success" ? "成功" : log.status === "failed" ? "失败" : "警告"}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleViewLogDetail(log)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={7} className="h-24 text-center">
                            <div className="flex flex-col items-center justify-center text-muted-foreground">
                              <AlertCircle className="h-8 w-8 mb-2" />
                              <p>没有找到匹配的日志记录</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* 分页 */}
                {totalPages > 1 && (
                  <div className="flex justify-center">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                            disabled={currentPage === 1}
                          />
                        </PaginationItem>
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={currentPage === page}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ))}
                        <PaginationItem>
                          <PaginationNext
                            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                            disabled={currentPage === totalPages}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>安全日志</CardTitle>
              <CardDescription>
                记录系统安全相关的操作和事件
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Shield className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">安全日志功能即将上线</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>系统日志</CardTitle>
              <CardDescription>
                记录系统运行状态和错误信息
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Database className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">系统日志功能即将上线</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 日志详情对话框 */}
      <Dialog open={showLogDetail} onOpenChange={setShowLogDetail}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>日志详情</DialogTitle>
            <DialogDescription>
              查看操作日志的详细信息
            </DialogDescription>
          </DialogHeader>
          {selectedLog && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Avatar>
                    <AvatarImage src={selectedLog.userAvatar} alt={selectedLog.userName} />
                    <AvatarFallback>{selectedLog.userName.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{selectedLog.userName}</div>
                    <div className="text-sm text-muted-foreground">用户ID: {selectedLog.userId}</div>
                  </div>
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="space-y-1">
                    <div className="font-medium flex items-center gap-1">
                      <Activity className="h-4 w-4" />
                      操作
                    </div>
                    <div>{selectedLog.action}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="font-medium flex items-center gap-1">
                      <FileText className="h-4 w-4" />
                      模块
                    </div>
                    <div>{selectedLog.module}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="font-medium flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      时间
                    </div>
                    <div>{selectedLog.timestamp}</div>
                  </div>
                  <div className="space-y-1">
                    <div className="font-medium flex items-center gap-1">
                      <Info className="h-4 w-4" />
                      状态
                    </div>
                    <div>
                      <Badge variant={getStatusBadgeVariant(selectedLog.status)}>
                        {selectedLog.status === "success" ? "成功" : selectedLog.status === "failed" ? "失败" : "警告"}
                      </Badge>
                    </div>
                  </div>
                </div>
                <Separator />
                <div className="space-y-1">
                  <div className="font-medium">详细信息</div>
                  <p className="text-sm">{selectedLog.details}</p>
                </div>
                <Separator />
                <div className="space-y-1">
                  <div className="font-medium">IP地址</div>
                  <p className="text-sm">{selectedLog.ip}</p>
                </div>
                {selectedLog.userAgent && (
                  <>
                    <Separator />
                    <div className="space-y-1">
                      <div className="font-medium">用户代理</div>
                      <p className="text-sm">{selectedLog.userAgent}</p>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowLogDetail(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
