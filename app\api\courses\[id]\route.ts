import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// GET /api/courses/[id] - 获取单个课程详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const courseId = parseInt(params.id)

    console.log('获取课程详情API:', courseId);

    if (isNaN(courseId)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的课程ID',
        data: null
      })
    }

    // 查询课程
    const course = await prisma.course.findUnique({
      where: { id: courseId },
      include: {
        course_type: true
      }
    })

    if (!course) {
      return NextResponse.json({
        code: 404,
        msg: '课程不存在',
        data: null
      })
    }

    // 格式化返回数据
    const formattedCourse = {
      id: course.id.toString(),
      name: course.title,
      title: course.title,
      description: course.description || '',
      price: course.price ? `¥${course.price}/次` : '免费',
      priceValue: course.price ? parseFloat(course.price.toString()) : 0,
      cover: course.cover || '',
      type: course.type_id?.toString() || '',
      typeName: course.course_type?.name || '',
      typeColor: course.course_type?.color || '#3b82f6',
      content: course.content || '',
      duration: course.duration || 90,
      level: course.level || '1',
      coach: course.coach_id || '',
      coachId: course.coach_id || '',
      time: course.time || '',
      venue: course.venue || '',
      capacity: course.capacity || 0,
      status: course.status === 1 ? 'active' : 'inactive',
      createdAt: course.created_at?.toISOString().split('T')[0] || '',
      updatedAt: course.updated_at?.toISOString().split('T')[0] || '',
    }

    return NextResponse.json({
      code: 200,
      msg: '获取成功',
      data: formattedCourse
    })

  } catch (error) {
    console.error('获取课程详情失败:', error)
    return NextResponse.json({
      code: 500,
      msg: '服务器内部错误',
      data: null
    })
  }
}

// PUT /api/courses/[id] - 更新课程
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const courseId = parseInt(params.id)
    const body = await request.json()
    const {
      title,
      description,
      price,
      cover,
      type,
      content,
      duration,
      level,
      coachId,
      time,
      venue,
      capacity,
      status
    } = body

    console.log('更新课程API接收数据:', { courseId, ...body });

    if (isNaN(courseId)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的课程ID',
        data: null
      })
    }

    // 检查课程是否存在
    const existingCourse = await prisma.course.findUnique({
      where: { id: courseId }
    })

    if (!existingCourse) {
      return NextResponse.json({
        code: 404,
        msg: '课程不存在',
        data: null
      })
    }

    // 如果更新了标题，检查是否重复
    if (title && title !== existingCourse.title) {
      const duplicateCourse = await prisma.course.findFirst({
        where: {
          tenant_id: existingCourse.tenant_id,
          title: title,
          id: { not: courseId }
        }
      })

      if (duplicateCourse) {
        return NextResponse.json({
          code: 400,
          msg: '课程名称已存在',
          data: null
        })
      }
    }

    // 构建更新数据
    const updateData: any = {}
    if (title !== undefined) updateData.title = title
    if (description !== undefined) updateData.description = description
    if (price !== undefined) updateData.price = price ? parseFloat(price) : null
    if (cover !== undefined) updateData.cover = cover
    if (type !== undefined) updateData.type_id = type ? parseInt(type) : null
    if (content !== undefined) updateData.content = content
    if (duration !== undefined) updateData.duration = duration ? parseInt(duration) : null
    if (level !== undefined) updateData.level = level
    if (coachId !== undefined) updateData.coach_id = coachId
    if (time !== undefined) updateData.time = time
    if (venue !== undefined) updateData.venue = venue
    if (capacity !== undefined) updateData.capacity = capacity ? parseInt(capacity) : null
    if (status !== undefined) updateData.status = status === 'active' ? 1 : 0

    // 更新课程
    const course = await prisma.course.update({
      where: { id: courseId },
      data: updateData,
      include: {
        course_type: true
      }
    })

    console.log('课程更新成功:', course);

    // 格式化返回数据
    const formattedCourse = {
      id: course.id.toString(),
      name: course.title,
      title: course.title,
      description: course.description || '',
      price: course.price ? `¥${course.price}/次` : '免费',
      priceValue: course.price ? parseFloat(course.price.toString()) : 0,
      cover: course.cover || '',
      type: course.type_id?.toString() || '',
      typeName: course.course_type?.name || '',
      typeColor: course.course_type?.color || '#3b82f6',
      content: course.content || '',
      duration: course.duration || 90,
      level: course.level || '1',
      coach: course.coach_id || '',
      coachId: course.coach_id || '',
      time: course.time || '',
      venue: course.venue || '',
      capacity: course.capacity || 0,
      status: course.status === 1 ? 'active' : 'inactive',
      createdAt: course.created_at?.toISOString().split('T')[0] || '',
      updatedAt: course.updated_at?.toISOString().split('T')[0] || '',
    }

    return NextResponse.json({
      code: 200,
      msg: '更新成功',
      data: formattedCourse
    })

  } catch (error) {
    console.error('更新课程失败:', error)
    return NextResponse.json({
      code: 500,
      msg: '服务器内部错误',
      data: null
    })
  }
}

// DELETE /api/courses/[id] - 删除课程
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const courseId = parseInt(params.id)

    console.log('删除课程API:', courseId);

    if (isNaN(courseId)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的课程ID',
        data: null
      })
    }

    // 检查课程是否存在
    const existingCourse = await prisma.course.findUnique({
      where: { id: courseId }
    })

    if (!existingCourse) {
      return NextResponse.json({
        code: 404,
        msg: '课程不存在',
        data: null
      })
    }

    // 删除课程
    await prisma.course.delete({
      where: { id: courseId }
    })

    console.log('课程删除成功:', courseId);

    return NextResponse.json({
      code: 200,
      msg: '删除成功',
      data: null
    })

  } catch (error) {
    console.error('删除课程失败:', error)
    return NextResponse.json({
      code: 500,
      msg: '服务器内部错误',
      data: null
    })
  }
}