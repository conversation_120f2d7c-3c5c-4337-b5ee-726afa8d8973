"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Calendar, Clock, Download, Filter, MoreHorizontal, Plus, Search, Zap } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Eye, Edit, BarChart2, Trash, AlertTriangle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function FlashSalesPage() {
  const { toast } = useToast()
  const [selectedSale, setSelectedSale] = useState<any>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showDetailsDialog, setShowDetailsDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showAnalysisDialog, setShowAnalysisDialog] = useState(false)

  const handleViewDetails = (sale: any) => {
    setSelectedSale(sale)
    setShowDetailsDialog(true)
  }

  const handleEdit = (sale: any) => {
    setSelectedSale(sale)
    setShowEditDialog(true)
  }

  const handleDataAnalysis = (sale: any) => {
    setSelectedSale(sale)
    setShowAnalysisDialog(true)
  }

  const handleDelete = (sale: any) => {
    setSelectedSale(sale)
    setShowDeleteDialog(true)
  }

  const confirmDelete = () => {
    toast({
      title: "秒杀活动已删除",
      description: `${selectedSale?.name} 已成功删除`,
    })
    setShowDeleteDialog(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">秒杀活动</h1>
          <p className="text-muted-foreground">创建和管理限时限量特价活动</p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建秒杀
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>创建秒杀活动</DialogTitle>
                <DialogDescription>设置秒杀活动的商品、时间和库存</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="flash-name" className="text-sm font-medium">
                      活动名称
                    </label>
                    <Input id="flash-name" placeholder="例如：周年庆秒杀" />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="product-type" className="text-sm font-medium">
                      商品类型
                    </label>
                    <Select>
                      <SelectTrigger id="product-type">
                        <SelectValue placeholder="选择类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="course">课程</SelectItem>
                        <SelectItem value="membership">会员卡</SelectItem>
                        <SelectItem value="product">实物商品</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="product-select" className="text-sm font-medium">
                    选择商品
                  </label>
                  <Select>
                    <SelectTrigger id="product-select">
                      <SelectValue placeholder="选择商品" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="yoga-monthly">瑜伽月卡</SelectItem>
                      <SelectItem value="yoga-quarterly">瑜伽季卡</SelectItem>
                      <SelectItem value="yoga-annual">瑜伽年卡</SelectItem>
                      <SelectItem value="pilates-monthly">普拉提月卡</SelectItem>
                      <SelectItem value="pilates-quarterly">普拉提季卡</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="original-price" className="text-sm font-medium">
                      原价
                    </label>
                    <Input id="original-price" placeholder="例如：1200" />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="flash-price" className="text-sm font-medium">
                      秒杀价
                    </label>
                    <Input id="flash-price" placeholder="例如：599" />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">活动日期</label>
                    <div className="flex gap-2">
                      <Button variant="outline" size="icon" className="h-10 w-10">
                        <Calendar className="h-4 w-4" />
                      </Button>
                      <Input placeholder="开始日期" />
                      <Input placeholder="结束日期" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">活动时间</label>
                    <div className="flex gap-2">
                      <Button variant="outline" size="icon" className="h-10 w-10">
                        <Clock className="h-4 w-4" />
                      </Button>
                      <Input placeholder="开始时间" />
                      <Input placeholder="结束时间" />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="stock" className="text-sm font-medium">
                      秒杀库存
                    </label>
                    <Input id="stock" placeholder="例如：50" />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="limit" className="text-sm font-medium">
                      每人限购
                    </label>
                    <Input id="limit" placeholder="例如：1" />
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="description" className="text-sm font-medium">
                    活动说明
                  </label>
                  <Input id="description" placeholder="活动规则和说明" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline">取消</Button>
                <Button>创建秒杀</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出数据
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="搜索秒杀活动..." className="pl-8" />
        </div>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="状态筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="active">进行中</SelectItem>
            <SelectItem value="upcoming">未开始</SelectItem>
            <SelectItem value="ended">已结束</SelectItem>
            <SelectItem value="sold_out">已售罄</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>秒杀活动列表</CardTitle>
          <CardDescription>管理所有秒杀活动</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>活动名称</TableHead>
                <TableHead>商品</TableHead>
                <TableHead>秒杀价</TableHead>
                <TableHead>活动时间</TableHead>
                <TableHead>库存情况</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {flashSales.map((sale) => (
                <TableRow key={sale.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center">
                      <Zap className="mr-2 h-4 w-4 text-red-500" />
                      {sale.name}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span>{sale.product}</span>
                      <span className="text-xs text-muted-foreground">原价: ¥{sale.originalPrice}</span>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium text-red-500">¥{sale.flashPrice}</TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="text-xs">{sale.date}</span>
                      <span className="text-xs">{sale.time}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>
                          已售 {sale.sold}/{sale.stock}
                        </span>
                        <span>{Math.round((sale.sold / sale.stock) * 100)}%</span>
                      </div>
                      <Progress value={(sale.sold / sale.stock) * 100} className="h-2" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(sale.status)}>{getStatusName(sale.status)}</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewDetails(sale)}>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(sale)}>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDataAnalysis(sale)}>
                          <BarChart2 className="mr-2 h-4 w-4" />
                          数据分析
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive" onClick={() => handleDelete(sale)}>
                          <Trash className="mr-2 h-4 w-4" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>即将开始的秒杀</CardTitle>
            <CardDescription>未来7天内即将开始的秒杀活动</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingFlashSales.map((sale) => (
                <div key={sale.id} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
                  <div className="flex flex-col">
                    <span className="font-medium">{sale.name}</span>
                    <span className="text-sm text-muted-foreground">{sale.product}</span>
                    <div className="flex items-center mt-1">
                      <Clock className="mr-1 h-3 w-3 text-muted-foreground" />
                      <span className="text-xs">
                        {sale.date} {sale.time}
                      </span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="flex items-center">
                      <span className="text-sm line-through text-muted-foreground mr-2">¥{sale.originalPrice}</span>
                      <span className="font-medium text-red-500">¥{sale.flashPrice}</span>
                    </div>
                    <span className="text-xs text-muted-foreground">库存: {sale.stock}</span>
                    <Button size="sm" variant="outline" className="mt-2">
                      设置提醒
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>秒杀活动数据</CardTitle>
            <CardDescription>秒杀活动的关键指标</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">总活动数</span>
                <div className="text-2xl font-bold">12</div>
              </div>
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">总销售额</span>
                <div className="text-2xl font-bold">¥58,320</div>
              </div>
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">平均转化率</span>
                <div className="text-2xl font-bold">68.5%</div>
              </div>
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">平均售罄时间</span>
                <div className="text-2xl font-bold">18分钟</div>
              </div>
            </div>

            <div className="mt-6">
              <h4 className="text-sm font-medium mb-2">热门秒杀商品</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">瑜伽年卡</span>
                  <span className="text-sm font-medium">¥2,999</span>
                </div>
                <Progress value={90} className="h-2" />

                <div className="flex items-center justify-between">
                  <span className="text-sm">普拉提季卡</span>
                  <span className="text-sm font-medium">¥1,599</span>
                </div>
                <Progress value={75} className="h-2" />

                <div className="flex items-center justify-between">
                  <span className="text-sm">高级瑜伽垫</span>
                  <span className="text-sm font-medium">¥299</span>
                </div>
                <Progress value={60} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-destructive">
              <AlertTriangle className="mr-2 h-5 w-5" />
              确认删除秒杀活动
            </AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除 <span className="font-medium">{selectedSale?.name}</span>{" "}
              吗？此操作无法撤销，所有相关数据将被永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 查看详情对话框 */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>秒杀活动详情</DialogTitle>
            <DialogDescription>查看秒杀活动的详细信息</DialogDescription>
          </DialogHeader>
          {selectedSale && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">活动名称</h4>
                  <p className="font-medium">{selectedSale.name}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">活动状态</h4>
                  <Badge variant={getStatusBadgeVariant(selectedSale.status)}>
                    {getStatusName(selectedSale.status)}
                  </Badge>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-1">商品信息</h4>
                <p className="font-medium">{selectedSale.product}</p>
                <div className="flex items-center mt-1">
                  <span className="text-sm line-through text-muted-foreground mr-2">¥{selectedSale.originalPrice}</span>
                  <span className="font-medium text-red-500">¥{selectedSale.flashPrice}</span>
                  <span className="text-xs text-muted-foreground ml-2">
                    (优惠 {Math.round((1 - selectedSale.flashPrice / selectedSale.originalPrice) * 100)}%)
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">活动时间</h4>
                  <p>{selectedSale.date}</p>
                  <p>{selectedSale.time}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-1">库存情况</h4>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>
                        已售 {selectedSale.sold}/{selectedSale.stock}
                      </span>
                      <span>{Math.round((selectedSale.sold / selectedSale.stock) * 100)}%</span>
                    </div>
                    <Progress value={(selectedSale.sold / selectedSale.stock) * 100} className="h-2" />
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-1">销售数据</h4>
                <div className="grid grid-cols-3 gap-4 mt-2">
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-xs text-muted-foreground">总销售额</p>
                    <p className="text-lg font-bold">¥{selectedSale.sold * selectedSale.flashPrice}</p>
                  </div>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-xs text-muted-foreground">平均客单价</p>
                    <p className="text-lg font-bold">¥{selectedSale.flashPrice}</p>
                  </div>
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-xs text-muted-foreground">转化率</p>
                    <p className="text-lg font-bold">68%</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDetailsDialog(false)}>
              关闭
            </Button>
            <Button
              onClick={() => {
                setShowDetailsDialog(false)
                handleEdit(selectedSale)
              }}
            >
              编辑活动
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑秒杀活动</DialogTitle>
            <DialogDescription>修改秒杀活动的信息</DialogDescription>
          </DialogHeader>
          {selectedSale && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="edit-flash-name" className="text-sm font-medium">
                    活动名称
                  </label>
                  <Input id="edit-flash-name" defaultValue={selectedSale.name} />
                </div>
                <div className="space-y-2">
                  <label htmlFor="edit-product-type" className="text-sm font-medium">
                    商品类型
                  </label>
                  <Select defaultValue="course">
                    <SelectTrigger id="edit-product-type">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="course">课程</SelectItem>
                      <SelectItem value="membership">会员卡</SelectItem>
                      <SelectItem value="product">实物商品</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="edit-product-select" className="text-sm font-medium">
                  选择商品
                </label>
                <Select defaultValue="yoga-annual">
                  <SelectTrigger id="edit-product-select">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yoga-monthly">瑜伽月卡</SelectItem>
                    <SelectItem value="yoga-quarterly">瑜伽季卡</SelectItem>
                    <SelectItem value="yoga-annual">瑜伽年卡</SelectItem>
                    <SelectItem value="pilates-monthly">普拉提月卡</SelectItem>
                    <SelectItem value="pilates-quarterly">普拉提季卡</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="edit-original-price" className="text-sm font-medium">
                    原价
                  </label>
                  <Input id="edit-original-price" defaultValue={selectedSale.originalPrice} />
                </div>
                <div className="space-y-2">
                  <label htmlFor="edit-flash-price" className="text-sm font-medium">
                    秒杀价
                  </label>
                  <Input id="edit-flash-price" defaultValue={selectedSale.flashPrice} />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">活动日期</label>
                  <div className="flex gap-2">
                    <Button variant="outline" size="icon" className="h-10 w-10">
                      <Calendar className="h-4 w-4" />
                    </Button>
                    <Input placeholder="开始日期" defaultValue={selectedSale.date} />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">活动时间</label>
                  <div className="flex gap-2">
                    <Button variant="outline" size="icon" className="h-10 w-10">
                      <Clock className="h-4 w-4" />
                    </Button>
                    <Input placeholder="时间段" defaultValue={selectedSale.time} />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="edit-stock" className="text-sm font-medium">
                    秒杀库存
                  </label>
                  <Input id="edit-stock" defaultValue={selectedSale.stock} />
                </div>
                <div className="space-y-2">
                  <label htmlFor="edit-limit" className="text-sm font-medium">
                    每人限购
                  </label>
                  <Input id="edit-limit" defaultValue="1" />
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              取消
            </Button>
            <Button
              onClick={() => {
                toast({
                  title: "秒杀活动已更新",
                  description: "活动信息已成功保存",
                })
                setShowEditDialog(false)
              }}
            >
              保存更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 数据分析对话框 */}
      <Dialog open={showAnalysisDialog} onOpenChange={setShowAnalysisDialog}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>秒杀活动数据分析</DialogTitle>
            <DialogDescription>查看 {selectedSale?.name} 的详细数据分析</DialogDescription>
          </DialogHeader>
          {selectedSale && (
            <div className="space-y-6">
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-muted-foreground">总销售额</h4>
                  <p className="text-2xl font-bold mt-1">¥{selectedSale.sold * selectedSale.flashPrice}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    比上次活动 {selectedSale.id % 2 === 0 ? "↑" : "↓"} {Math.floor(Math.random() * 20) + 5}%
                  </p>
                </div>
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-muted-foreground">销售数量</h4>
                  <p className="text-2xl font-bold mt-1">{selectedSale.sold}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    总库存的 {Math.round((selectedSale.sold / selectedSale.stock) * 100)}%
                  </p>
                </div>
                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-muted-foreground">转化率</h4>
                  <p className="text-2xl font-bold mt-1">68%</p>
                  <p className="text-xs text-muted-foreground mt-1">比平均水平 ↑ 12%</p>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">销售趋势</h4>
                <div className="h-[200px] bg-muted rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">销售趋势图表</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium mb-2">用户分析</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>新用户</span>
                        <span>42%</span>
                      </div>
                      <Progress value={42} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>老用户</span>
                        <span>58%</span>
                      </div>
                      <Progress value={58} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>会员用户</span>
                        <span>76%</span>
                      </div>
                      <Progress value={76} className="h-2" />
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">流量来源</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>小程序</span>
                        <span>45%</span>
                      </div>
                      <Progress value={45} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>公众号</span>
                        <span>30%</span>
                      </div>
                      <Progress value={30} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>官网</span>
                        <span>25%</span>
                      </div>
                      <Progress value={25} className="h-2" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAnalysisDialog(false)}>
              关闭
            </Button>
            <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              导出报告
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// 模拟数据
const flashSales = [
  {
    id: 1,
    name: "周年庆秒杀",
    product: "瑜伽年卡",
    originalPrice: 3600,
    flashPrice: 1999,
    date: "2025-05-01",
    time: "10:00-12:00",
    stock: 50,
    sold: 50,
    status: "sold_out",
  },
  {
    id: 2,
    name: "五一特惠",
    product: "普拉提季卡",
    originalPrice: 1800,
    flashPrice: 999,
    date: "2025-05-01",
    time: "14:00-16:00",
    stock: 100,
    sold: 78,
    status: "active",
  },
  {
    id: 3,
    name: "教师节专享",
    product: "瑜伽月卡",
    originalPrice: 600,
    flashPrice: 399,
    date: "2025-09-10",
    time: "10:00-22:00",
    stock: 200,
    sold: 0,
    status: "upcoming",
  },
  {
    id: 4,
    name: "春季焕新",
    product: "高级瑜伽垫",
    originalPrice: 399,
    flashPrice: 199,
    date: "2025-03-15",
    time: "10:00-12:00",
    stock: 100,
    sold: 100,
    status: "ended",
  },
  {
    id: 5,
    name: "会员专享",
    product: "私教课5次",
    originalPrice: 1500,
    flashPrice: 899,
    date: "2025-04-20",
    time: "10:00-22:00",
    stock: 30,
    sold: 12,
    status: "ended",
  },
]

const upcomingFlashSales = [
  {
    id: 1,
    name: "教师节专享",
    product: "瑜伽月卡",
    originalPrice: 600,
    flashPrice: 399,
    date: "2025-09-10",
    time: "10:00-22:00",
    stock: 200,
  },
  {
    id: 2,
    name: "夏日清凉价",
    product: "普拉提月卡",
    originalPrice: 680,
    flashPrice: 459,
    date: "2025-06-01",
    time: "10:00-12:00",
    stock: 150,
  },
  {
    id: 3,
    name: "端午特惠",
    product: "瑜伽季卡",
    originalPrice: 1500,
    flashPrice: 999,
    date: "2025-06-10",
    time: "10:00-18:00",
    stock: 80,
  },
]

// 辅助函数
function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "active":
      return "success"
    case "upcoming":
      return "secondary"
    case "ended":
      return "outline"
    case "sold_out":
      return "destructive"
    default:
      return "default"
  }
}

function getStatusName(status: string) {
  switch (status) {
    case "active":
      return "进行中"
    case "upcoming":
      return "未开始"
    case "ended":
      return "已结束"
    case "sold_out":
      return "已售罄"
    default:
      return "未知"
  }
}

