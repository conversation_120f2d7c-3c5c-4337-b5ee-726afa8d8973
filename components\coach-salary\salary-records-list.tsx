"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { MoreHorizontal, Eye, CheckCircle, XCircle, CreditCard } from "lucide-react"
import { SalaryDetailDialog } from "./salary-detail-dialog"
import { ApproveSalaryDialog } from "./approve-salary-dialog"
import { PaySalaryDialog } from "./pay-salary-dialog"

// 薪资记录状态映射
const statusMap = {
  pending: { label: "待审核", color: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100/80" },
  approved: { label: "已审核", color: "bg-blue-100 text-blue-800 hover:bg-blue-100/80" },
  paid: { label: "已发放", color: "bg-green-100 text-green-800 hover:bg-green-100/80" },
  rejected: { label: "已拒绝", color: "bg-red-100 text-red-800 hover:bg-red-100/80" },
}

// 薪资类型映射
const salaryTypeMap = {
  fixed: "固定薪资",
  hourly: "课时费",
  mixed: "底薪+课时费",
  commission: "底薪+提成",
  full: "底薪+课时费+提成",
}

// 模拟数据
const mockSalaryRecords = [
  {
    id: "SR001",
    coachId: "C001",
    coachName: "张教练",
    salaryMonth: "2023-09",
    salaryType: "full",
    baseSalary: 5000,
    classHours: 45,
    hourlySalary: 9000,
    commissionAmount: 2500,
    bonusAmount: 1000,
    deductionAmount: 500,
    totalSalary: 17000,
    netSalary: 15300,
    status: "paid",
    createdAt: "2023-10-05 10:30:00",
  },
  {
    id: "SR002",
    coachId: "C002",
    coachName: "李教练",
    salaryMonth: "2023-09",
    salaryType: "mixed",
    baseSalary: 4000,
    classHours: 38,
    hourlySalary: 7600,
    commissionAmount: 0,
    bonusAmount: 500,
    deductionAmount: 0,
    totalSalary: 12100,
    netSalary: 10890,
    status: "approved",
    createdAt: "2023-10-05 11:15:00",
  },
  {
    id: "SR003",
    coachId: "C003",
    coachName: "王教练",
    salaryMonth: "2023-09",
    salaryType: "commission",
    baseSalary: 3500,
    classHours: 0,
    hourlySalary: 0,
    commissionAmount: 6800,
    bonusAmount: 0,
    deductionAmount: 200,
    totalSalary: 10100,
    netSalary: 9090,
    status: "pending",
    createdAt: "2023-10-05 14:20:00",
  },
  {
    id: "SR004",
    coachId: "C004",
    coachName: "赵教练",
    salaryMonth: "2023-09",
    salaryType: "hourly",
    baseSalary: 0,
    classHours: 52,
    hourlySalary: 10400,
    commissionAmount: 0,
    bonusAmount: 800,
    deductionAmount: 0,
    totalSalary: 11200,
    netSalary: 10080,
    status: "rejected",
    createdAt: "2023-10-05 16:45:00",
  },
]

interface SalaryRecordsListProps {
  dateRange?: {
    from: Date | undefined
    to: Date | undefined
  }
}

export function SalaryRecordsList({ dateRange }: SalaryRecordsListProps) {
  const [selectedRecords, setSelectedRecords] = useState<string[]>([])
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [showApproveDialog, setShowApproveDialog] = useState(false)
  const [showPayDialog, setShowPayDialog] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<any>(null)

  // 处理选择所有记录
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRecords(mockSalaryRecords.map(record => record.id))
    } else {
      setSelectedRecords([])
    }
  }

  // 处理选择单个记录
  const handleSelectRecord = (recordId: string, checked: boolean) => {
    if (checked) {
      setSelectedRecords([...selectedRecords, recordId])
    } else {
      setSelectedRecords(selectedRecords.filter(id => id !== recordId))
    }
  }

  // 打开详情对话框
  const openDetailDialog = (record: any) => {
    setCurrentRecord(record)
    setShowDetailDialog(true)
  }

  // 打开审核对话框
  const openApproveDialog = (record: any) => {
    setCurrentRecord(record)
    setShowApproveDialog(true)
  }

  // 打开发放对话框
  const openPayDialog = (record: any) => {
    setCurrentRecord(record)
    setShowPayDialog(true)
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedRecords.length === mockSalaryRecords.length}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>教练</TableHead>
              <TableHead>薪资月份</TableHead>
              <TableHead>薪资类型</TableHead>
              <TableHead className="text-right">底薪</TableHead>
              <TableHead className="text-right">课时费</TableHead>
              <TableHead className="text-right">提成</TableHead>
              <TableHead className="text-right">总薪资</TableHead>
              <TableHead>状态</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockSalaryRecords.map((record) => (
              <TableRow key={record.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedRecords.includes(record.id)}
                    onCheckedChange={(checked) => handleSelectRecord(record.id, !!checked)}
                  />
                </TableCell>
                <TableCell className="font-medium">{record.coachName}</TableCell>
                <TableCell>{record.salaryMonth}</TableCell>
                <TableCell>{salaryTypeMap[record.salaryType as keyof typeof salaryTypeMap]}</TableCell>
                <TableCell className="text-right">¥{record.baseSalary.toLocaleString()}</TableCell>
                <TableCell className="text-right">¥{record.hourlySalary.toLocaleString()}</TableCell>
                <TableCell className="text-right">¥{record.commissionAmount.toLocaleString()}</TableCell>
                <TableCell className="text-right font-medium">¥{record.totalSalary.toLocaleString()}</TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={statusMap[record.status as keyof typeof statusMap].color}
                  >
                    {statusMap[record.status as keyof typeof statusMap].label}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">打开菜单</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>操作</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => openDetailDialog(record)}>
                        <Eye className="mr-2 h-4 w-4" />
                        查看详情
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {record.status === "pending" && (
                        <DropdownMenuItem onClick={() => openApproveDialog(record)}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          审核
                        </DropdownMenuItem>
                      )}
                      {record.status === "approved" && (
                        <DropdownMenuItem onClick={() => openPayDialog(record)}>
                          <CreditCard className="mr-2 h-4 w-4" />
                          发放薪资
                        </DropdownMenuItem>
                      )}
                      {record.status === "pending" && (
                        <DropdownMenuItem className="text-red-600">
                          <XCircle className="mr-2 h-4 w-4" />
                          拒绝
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* 薪资详情对话框 */}
      <SalaryDetailDialog
        open={showDetailDialog}
        onOpenChange={setShowDetailDialog}
        record={currentRecord}
      />

      {/* 薪资审核对话框 */}
      <ApproveSalaryDialog
        open={showApproveDialog}
        onOpenChange={setShowApproveDialog}
        record={currentRecord}
      />

      {/* 薪资发放对话框 */}
      <PaySalaryDialog
        open={showPayDialog}
        onOpenChange={setShowPayDialog}
        record={currentRecord}
      />
    </div>
  )
}
