"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { toast } from "@/hooks/use-toast"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Download, FileSpreadsheet, Upload } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

const formSchema = z.object({
  file: z.instanceof(FileList).refine((files) => files.length > 0, {
    message: "请选择文件",
  }),
})

interface ImportShareholdersDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ImportShareholdersDialog({ open, onOpenChange }: ImportShareholdersDialogProps) {
  const [step, setStep] = useState(1)
  const [progress, setProgress] = useState(0)
  const [importResult, setImportResult] = useState<any>(null)
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  })

  // 模拟导入结果数据
  const mockImportResult = {
    total: 5,
    success: 3,
    failed: 2,
    successList: [
      {
        name: "张三",
        phone: "13800138001",
        type: "消费型股东",
        joinDate: "2023-05-15",
      },
      {
        name: "李四",
        phone: "13900139001",
        type: "投资型股东",
        joinDate: "2023-04-20",
      },
      {
        name: "王五",
        phone: "13700137001",
        type: "资源型股东",
        joinDate: "2023-06-01",
      },
    ],
    failedList: [
      {
        row: 4,
        name: "赵六",
        phone: "1360013600",
        type: "员工型股东",
        error: "手机号格式不正确",
      },
      {
        row: 5,
        name: "",
        phone: "13500135001",
        type: "联盟型股东",
        error: "姓名不能为空",
      },
    ],
  }

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values)
    
    // 模拟上传进度
    setStep(2)
    let currentProgress = 0
    const interval = setInterval(() => {
      currentProgress += 10
      setProgress(currentProgress)
      
      if (currentProgress >= 100) {
        clearInterval(interval)
        setImportResult(mockImportResult)
        setStep(3)
      }
    }, 300)
  }

  function handleDownloadTemplate() {
    toast({
      title: "模板下载中",
      description: "股东导入模板已开始下载",
    })
  }

  function handleClose() {
    setStep(1)
    setProgress(0)
    setImportResult(null)
    form.reset()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>导入股东</DialogTitle>
          <DialogDescription>
            批量导入股东信息
          </DialogDescription>
        </DialogHeader>

        {step === 1 && (
          <div className="space-y-6">
            <Tabs defaultValue="file" className="w-full">
              <TabsList className="grid grid-cols-2">
                <TabsTrigger value="file">文件导入</TabsTrigger>
                <TabsTrigger value="template">下载模板</TabsTrigger>
              </TabsList>
              <TabsContent value="file" className="space-y-4 py-4">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                      control={form.control}
                      name="file"
                      render={({ field: { onChange, value, ...rest } }) => (
                        <FormItem>
                          <FormLabel>选择文件</FormLabel>
                          <FormControl>
                            <Input
                              type="file"
                              accept=".xlsx,.xls,.csv"
                              onChange={(e) => onChange(e.target.files)}
                              {...rest}
                            />
                          </FormControl>
                          <FormDescription>
                            支持 Excel (.xlsx, .xls) 或 CSV (.csv) 格式文件
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>导入提示</AlertTitle>
                      <AlertDescription>
                        请确保导入文件格式正确，可以先下载模板查看所需字段。导入过程中如有重复数据，将根据手机号进行更新。
                      </AlertDescription>
                    </Alert>

                    <DialogFooter>
                      <Button type="button" variant="outline" onClick={handleClose}>
                        取消
                      </Button>
                      <Button type="submit">开始导入</Button>
                    </DialogFooter>
                  </form>
                </Form>
              </TabsContent>
              <TabsContent value="template" className="space-y-4 py-4">
                <div className="space-y-4">
                  <div className="border rounded-md p-4">
                    <div className="flex items-center gap-3 mb-4">
                      <FileSpreadsheet className="h-8 w-8 text-primary" />
                      <div>
                        <h3 className="font-medium">股东导入模板</h3>
                        <p className="text-sm text-muted-foreground">包含所有必填和选填字段的导入模板</p>
                      </div>
                    </div>
                    <Button onClick={handleDownloadTemplate} className="w-full">
                      <Download className="mr-2 h-4 w-4" />
                      下载模板
                    </Button>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">模板字段说明</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>字段名</TableHead>
                          <TableHead>是否必填</TableHead>
                          <TableHead>说明</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell>姓名/企业名称</TableCell>
                          <TableCell>
                            <Badge variant="default">必填</Badge>
                          </TableCell>
                          <TableCell>股东姓名或企业名称</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>联系电话</TableCell>
                          <TableCell>
                            <Badge variant="default">必填</Badge>
                          </TableCell>
                          <TableCell>11位手机号码</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>股东类型</TableCell>
                          <TableCell>
                            <Badge variant="default">必填</Badge>
                          </TableCell>
                          <TableCell>消费型/投资型/资源型/员工型/联盟型</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>加入日期</TableCell>
                          <TableCell>
                            <Badge variant="default">必填</Badge>
                          </TableCell>
                          <TableCell>格式：YYYY-MM-DD</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>标签</TableCell>
                          <TableCell>
                            <Badge variant="secondary">选填</Badge>
                          </TableCell>
                          <TableCell>多个标签用逗号分隔</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>备注</TableCell>
                          <TableCell>
                            <Badge variant="secondary">选填</Badge>
                          </TableCell>
                          <TableCell>股东相关备注信息</TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {step === 2 && (
          <div className="space-y-6 py-4">
            <div className="text-center">
              <Upload className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">正在导入</h3>
              <p className="text-sm text-muted-foreground mb-4">
                正在处理文件，请稍候...
              </p>
              <Progress value={progress} className="h-2 mb-2" />
              <p className="text-sm text-muted-foreground">
                已完成 {progress}%
              </p>
            </div>
          </div>
        )}

        {step === 3 && importResult && (
          <div className="space-y-6 py-4">
            <div className="text-center mb-6">
              <h3 className="text-lg font-medium mb-2">导入完成</h3>
              <p className="text-sm text-muted-foreground">
                共 {importResult.total} 条记录，成功 {importResult.success} 条，失败 {importResult.failed} 条
              </p>
            </div>

            <Tabs defaultValue="success" className="w-full">
              <TabsList className="grid grid-cols-2">
                <TabsTrigger value="success">成功记录 ({importResult.success})</TabsTrigger>
                <TabsTrigger value="failed">失败记录 ({importResult.failed})</TabsTrigger>
              </TabsList>
              <TabsContent value="success" className="space-y-4 py-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>姓名</TableHead>
                      <TableHead>联系电话</TableHead>
                      <TableHead>股东类型</TableHead>
                      <TableHead>加入日期</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {importResult.successList.map((item: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell>{item.name}</TableCell>
                        <TableCell>{item.phone}</TableCell>
                        <TableCell>{item.type}</TableCell>
                        <TableCell>{item.joinDate}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>
              <TabsContent value="failed" className="space-y-4 py-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>行号</TableHead>
                      <TableHead>姓名</TableHead>
                      <TableHead>联系电话</TableHead>
                      <TableHead>股东类型</TableHead>
                      <TableHead>错误原因</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {importResult.failedList.map((item: any, index: number) => (
                      <TableRow key={index}>
                        <TableCell>{item.row}</TableCell>
                        <TableCell>{item.name || "-"}</TableCell>
                        <TableCell>{item.phone}</TableCell>
                        <TableCell>{item.type}</TableCell>
                        <TableCell className="text-red-500">{item.error}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button onClick={handleClose}>完成</Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
