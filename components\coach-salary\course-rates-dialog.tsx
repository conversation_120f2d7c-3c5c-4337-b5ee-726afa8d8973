"use client"

import { useEffect, useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Plus, Trash } from "lucide-react"

// 模拟数据 - 课程类型
const mockCourseTypes = [
  { id: "CT001", name: "基础瑜伽" },
  { id: "CT002", name: "高级瑜伽" },
  { id: "CT003", name: "阴瑜伽" },
  { id: "CT004", name: "孕产瑜伽" },
  { id: "CT005", name: "理疗瑜伽" },
  { id: "CT006", name: "私教课" },
]

// 模拟数据 - 课时费率
const mockHourlyRates = [
  { courseTypeId: "CT001", courseTypeName: "基础瑜伽", hourlyRate: 180 },
  { courseTypeId: "CT002", courseTypeName: "高级瑜伽", hourlyRate: 220 },
  { courseTypeId: "CT006", courseTypeName: "私教课", hourlyRate: 300 },
]

// 模拟数据 - 提成比例
const mockCommissionRates = [
  { courseTypeId: "CT001", courseTypeName: "基础瑜伽", commissionRate: 8 },
  { courseTypeId: "CT002", courseTypeName: "高级瑜伽", commissionRate: 10 },
  { courseTypeId: "CT006", courseTypeName: "私教课", commissionRate: 15 },
]

interface CourseRatesDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  config: any
}

export function CourseRatesDialog({ open, onOpenChange, config }: CourseRatesDialogProps) {
  const [activeTab, setActiveTab] = useState("hourly")
  const [hourlyRates, setHourlyRates] = useState<any[]>([])
  const [commissionRates, setCommissionRates] = useState<any[]>([])
  const [newCourseTypeId, setNewCourseTypeId] = useState<string>("")
  const [newHourlyRate, setNewHourlyRate] = useState<string>("")
  const [newCommissionRate, setNewCommissionRate] = useState<string>("")

  // 当配置数据变化时，加载费率数据
  useEffect(() => {
    if (config) {
      // 这里应该调用API获取课时费率和提成比例
      // 暂时使用模拟数据
      setHourlyRates(mockHourlyRates)
      setCommissionRates(mockCommissionRates)
    }
  }, [config])

  // 添加课时费率
  const addHourlyRate = () => {
    if (!newCourseTypeId || !newHourlyRate) return

    const courseType = mockCourseTypes.find(ct => ct.id === newCourseTypeId)
    if (!courseType) return

    // 检查是否已存在
    const exists = hourlyRates.some(hr => hr.courseTypeId === newCourseTypeId)
    if (exists) {
      // 更新现有记录
      setHourlyRates(hourlyRates.map(hr => 
        hr.courseTypeId === newCourseTypeId 
          ? { ...hr, hourlyRate: parseFloat(newHourlyRate) } 
          : hr
      ))
    } else {
      // 添加新记录
      setHourlyRates([
        ...hourlyRates,
        {
          courseTypeId: newCourseTypeId,
          courseTypeName: courseType.name,
          hourlyRate: parseFloat(newHourlyRate)
        }
      ])
    }

    // 重置输入
    setNewCourseTypeId("")
    setNewHourlyRate("")
  }

  // 删除课时费率
  const deleteHourlyRate = (courseTypeId: string) => {
    setHourlyRates(hourlyRates.filter(hr => hr.courseTypeId !== courseTypeId))
  }

  // 添加提成比例
  const addCommissionRate = () => {
    if (!newCourseTypeId || !newCommissionRate) return

    const courseType = mockCourseTypes.find(ct => ct.id === newCourseTypeId)
    if (!courseType) return

    // 检查是否已存在
    const exists = commissionRates.some(cr => cr.courseTypeId === newCourseTypeId)
    if (exists) {
      // 更新现有记录
      setCommissionRates(commissionRates.map(cr => 
        cr.courseTypeId === newCourseTypeId 
          ? { ...cr, commissionRate: parseFloat(newCommissionRate) } 
          : cr
      ))
    } else {
      // 添加新记录
      setCommissionRates([
        ...commissionRates,
        {
          courseTypeId: newCourseTypeId,
          courseTypeName: courseType.name,
          commissionRate: parseFloat(newCommissionRate)
        }
      ])
    }

    // 重置输入
    setNewCourseTypeId("")
    setNewCommissionRate("")
  }

  // 删除提成比例
  const deleteCommissionRate = (courseTypeId: string) => {
    setCommissionRates(commissionRates.filter(cr => cr.courseTypeId !== courseTypeId))
  }

  // 保存所有费率
  const saveRates = () => {
    // 这里应该调用API保存课时费率和提成比例
    console.log("保存课时费率:", hourlyRates)
    console.log("保存提成比例:", commissionRates)
    
    // 关闭对话框
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        {config && (
          <>
            <DialogHeader>
              <DialogTitle>课程费率设置</DialogTitle>
              <DialogDescription>
                为 {config.coachName} 设置不同课程类型的课时费和提成比例
              </DialogDescription>
            </DialogHeader>
            
            <Tabs defaultValue="hourly" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="hourly">课时费设置</TabsTrigger>
                <TabsTrigger value="commission">提成比例设置</TabsTrigger>
              </TabsList>
              
              <TabsContent value="hourly" className="space-y-4">
                <div className="flex items-end gap-2">
                  <div className="space-y-2 flex-1">
                    <Label htmlFor="course-type">课程类型</Label>
                    <select
                      id="course-type"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={newCourseTypeId}
                      onChange={(e) => setNewCourseTypeId(e.target.value)}
                    >
                      <option value="">请选择课程类型</option>
                      {mockCourseTypes.map((courseType) => (
                        <option key={courseType.id} value={courseType.id}>
                          {courseType.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="space-y-2 flex-1">
                    <Label htmlFor="hourly-rate">课时费(元/课时)</Label>
                    <Input
                      id="hourly-rate"
                      type="number"
                      min="0"
                      step="10"
                      value={newHourlyRate}
                      onChange={(e) => setNewHourlyRate(e.target.value)}
                      placeholder="请输入课时费"
                    />
                  </div>
                  <Button onClick={addHourlyRate} className="mb-0.5">
                    <Plus className="h-4 w-4 mr-1" />
                    添加
                  </Button>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>课程类型</TableHead>
                        <TableHead className="text-right">课时费(元/课时)</TableHead>
                        <TableHead className="w-[80px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {hourlyRates.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center text-muted-foreground">
                            暂无课时费设置，请添加
                          </TableCell>
                        </TableRow>
                      ) : (
                        hourlyRates.map((rate) => (
                          <TableRow key={rate.courseTypeId}>
                            <TableCell>{rate.courseTypeName}</TableCell>
                            <TableCell className="text-right">¥{rate.hourlyRate}</TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => deleteHourlyRate(rate.courseTypeId)}
                              >
                                <Trash className="h-4 w-4 text-red-500" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
              
              <TabsContent value="commission" className="space-y-4">
                <div className="flex items-end gap-2">
                  <div className="space-y-2 flex-1">
                    <Label htmlFor="commission-course-type">课程类型</Label>
                    <select
                      id="commission-course-type"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      value={newCourseTypeId}
                      onChange={(e) => setNewCourseTypeId(e.target.value)}
                    >
                      <option value="">请选择课程类型</option>
                      {mockCourseTypes.map((courseType) => (
                        <option key={courseType.id} value={courseType.id}>
                          {courseType.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="space-y-2 flex-1">
                    <Label htmlFor="commission-rate">提成比例(%)</Label>
                    <Input
                      id="commission-rate"
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      value={newCommissionRate}
                      onChange={(e) => setNewCommissionRate(e.target.value)}
                      placeholder="请输入提成比例"
                    />
                  </div>
                  <Button onClick={addCommissionRate} className="mb-0.5">
                    <Plus className="h-4 w-4 mr-1" />
                    添加
                  </Button>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>课程类型</TableHead>
                        <TableHead className="text-right">提成比例(%)</TableHead>
                        <TableHead className="w-[80px]"></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {commissionRates.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center text-muted-foreground">
                            暂无提成比例设置，请添加
                          </TableCell>
                        </TableRow>
                      ) : (
                        commissionRates.map((rate) => (
                          <TableRow key={rate.courseTypeId}>
                            <TableCell>{rate.courseTypeName}</TableCell>
                            <TableCell className="text-right">{rate.commissionRate}%</TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => deleteCommissionRate(rate.courseTypeId)}
                              >
                                <Trash className="h-4 w-4 text-red-500" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button onClick={saveRates}>保存设置</Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}
