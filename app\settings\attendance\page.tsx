import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { FileText, Download, RefreshCw, Search, Filter, Plus } from "lucide-react"

export default function AttendancePage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">考勤管理</h1>
      </div>

      <Tabs defaultValue="rules" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 lg:w-[600px]">
          <TabsTrigger value="rules">考勤规则</TabsTrigger>
          <TabsTrigger value="schedule">排班管理</TabsTrigger>
          <TabsTrigger value="leave">请假管理</TabsTrigger>
          <TabsTrigger value="records">考勤记录</TabsTrigger>
        </TabsList>

        <TabsContent value="rules" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>考勤规则设置</CardTitle>
              <CardDescription>配置员工考勤规则和打卡要求</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">工作时间</h3>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="work-start">上班时间</Label>
                    <Input id="work-start" type="time" defaultValue="09:00" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="work-end">下班时间</Label>
                    <Input id="work-end" type="time" defaultValue="18:00" />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="flexible-hours">弹性工作时间</Label>
                    <p className="text-sm text-muted-foreground">允许员工在一定范围内自由选择上下班时间</p>
                  </div>
                  <Switch id="flexible-hours" defaultChecked />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="flex-start-range">上班弹性范围</Label>
                    <Select defaultValue="60">
                      <SelectTrigger>
                        <SelectValue placeholder="选择时间范围" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30">±30分钟</SelectItem>
                        <SelectItem value="60">±1小时</SelectItem>
                        <SelectItem value="90">±1.5小时</SelectItem>
                        <SelectItem value="120">±2小时</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="flex-end-range">下班弹性范围</Label>
                    <Select defaultValue="60">
                      <SelectTrigger>
                        <SelectValue placeholder="选择时间范围" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30">±30分钟</SelectItem>
                        <SelectItem value="60">±1小时</SelectItem>
                        <SelectItem value="90">±1.5小时</SelectItem>
                        <SelectItem value="120">±2小时</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="work-days">工作日</Label>
                  <div className="flex flex-wrap gap-2">
                    {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Switch id={`workday-${index}`} defaultChecked={index < 5} />
                        <Label htmlFor={`workday-${index}`}>{day}</Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">打卡设置</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="location-check">位置打卡</Label>
                    <p className="text-sm text-muted-foreground">要求员工在指定位置范围内打卡</p>
                  </div>
                  <Switch id="location-check" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location-range">位置范围（米）</Label>
                  <Input id="location-range" type="number" defaultValue="200" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="office-location">办公地点</Label>
                  <Input id="office-location" defaultValue="北京市朝阳区建国路88号" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="wifi-check">WiFi打卡</Label>
                    <p className="text-sm text-muted-foreground">要求员工连接指定WiFi网络打卡</p>
                  </div>
                  <Switch id="wifi-check" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="wifi-ssid">WiFi名称</Label>
                  <Input id="wifi-ssid" placeholder="输入WiFi SSID" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="face-check">人脸识别</Label>
                    <p className="text-sm text-muted-foreground">要求员工通过人脸识别进行打卡</p>
                  </div>
                  <Switch id="face-check" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="photo-check">拍照打卡</Label>
                    <p className="text-sm text-muted-foreground">要求员工拍照上传进行打卡</p>
                  </div>
                  <Switch id="photo-check" />
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">考勤规则</h3>

                <div className="space-y-2">
                  <Label htmlFor="late-threshold">迟到阈值（分钟）</Label>
                  <Input id="late-threshold" type="number" defaultValue="15" />
                  <p className="text-xs text-muted-foreground">超过上班时间多少分钟算作迟到</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="early-leave-threshold">早退阈值（分钟）</Label>
                  <Input id="early-leave-threshold" type="number" defaultValue="15" />
                  <p className="text-xs text-muted-foreground">提前下班多少分钟算作早退</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="absent-threshold">旷工阈值（分钟）</Label>
                  <Input id="absent-threshold" type="number" defaultValue="120" />
                  <p className="text-xs text-muted-foreground">迟到或早退超过多少分钟算作旷工</p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="overtime-auto">自动记录加班</Label>
                    <p className="text-sm text-muted-foreground">自动记录员工下班后的加班时间</p>
                  </div>
                  <Switch id="overtime-auto" defaultChecked />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="overtime-min">最小加班时长（分钟）</Label>
                  <Input id="overtime-min" type="number" defaultValue="30" />
                  <p className="text-xs text-muted-foreground">低于此时长的加班不计入统计</p>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="overtime-approval">加班需审批</Label>
                    <p className="text-sm text-muted-foreground">员工加班需要提前申请并获得审批</p>
                  </div>
                  <Switch id="overtime-approval" defaultChecked />
                </div>
              </div>

              <div className="flex justify-end">
                <Button>保存设置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>排班管理</CardTitle>
              <CardDescription>管理员工排班和班次设置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="relative flex-1 mr-4">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="搜索员工姓名、部门..." className="pl-8 max-w-sm" />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    导出
                  </Button>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    添加排班
                  </Button>
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>员工</TableHead>
                      <TableHead>部门</TableHead>
                      <TableHead>职位</TableHead>
                      <TableHead>班次</TableHead>
                      <TableHead>工作时间</TableHead>
                      <TableHead>排班周期</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>张静</TableCell>
                      <TableCell>教学部</TableCell>
                      <TableCell>高级教练</TableCell>
                      <TableCell>早班</TableCell>
                      <TableCell>08:00-16:00</TableCell>
                      <TableCell>周一至周五</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          生效中
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          编辑
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>李明</TableCell>
                      <TableCell>运营部</TableCell>
                      <TableCell>前台主管</TableCell>
                      <TableCell>中班</TableCell>
                      <TableCell>12:00-20:00</TableCell>
                      <TableCell>周一至周五</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          生效中
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          编辑
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>王芳</TableCell>
                      <TableCell>教学部</TableCell>
                      <TableCell>资深教练</TableCell>
                      <TableCell>晚班</TableCell>
                      <TableCell>16:00-22:00</TableCell>
                      <TableCell>周一至周五</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          生效中
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          编辑
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>赵健</TableCell>
                      <TableCell>销售部</TableCell>
                      <TableCell>销售顾问</TableCell>
                      <TableCell>标准班</TableCell>
                      <TableCell>09:00-18:00</TableCell>
                      <TableCell>周一至周五</TableCell>
                      <TableCell>
                        <Badge variant="secondary">已过期</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          编辑
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>钱蓉</TableCell>
                      <TableCell>财务部</TableCell>
                      <TableCell>财务主管</TableCell>
                      <TableCell>标准班</TableCell>
                      <TableCell>09:00-18:00</TableCell>
                      <TableCell>周一至周五</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          生效中
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          编辑
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">班次设置</h3>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>班次名称</TableHead>
                        <TableHead>开始时间</TableHead>
                        <TableHead>结束时间</TableHead>
                        <TableHead>工作时长</TableHead>
                        <TableHead>休息时间</TableHead>
                        <TableHead>适用部门</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>标准班</TableCell>
                        <TableCell>09:00</TableCell>
                        <TableCell>18:00</TableCell>
                        <TableCell>8小时</TableCell>
                        <TableCell>12:00-13:00</TableCell>
                        <TableCell>全部</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>早班</TableCell>
                        <TableCell>08:00</TableCell>
                        <TableCell>16:00</TableCell>
                        <TableCell>7小时</TableCell>
                        <TableCell>12:00-13:00</TableCell>
                        <TableCell>教学部</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>中班</TableCell>
                        <TableCell>12:00</TableCell>
                        <TableCell>20:00</TableCell>
                        <TableCell>7小时</TableCell>
                        <TableCell>16:00-17:00</TableCell>
                        <TableCell>运营部</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>晚班</TableCell>
                        <TableCell>16:00</TableCell>
                        <TableCell>22:00</TableCell>
                        <TableCell>5小时</TableCell>
                        <TableCell>18:00-19:00</TableCell>
                        <TableCell>教学部</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    添加班次
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="leave" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>请假管理</CardTitle>
              <CardDescription>管理员工请假申请和假期规则</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="relative flex-1 mr-4">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="搜索员工姓名、请假类型..." className="pl-8 max-w-sm" />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    导出
                  </Button>
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>员工</TableHead>
                      <TableHead>部门</TableHead>
                      <TableHead>请假类型</TableHead>
                      <TableHead>开始日期</TableHead>
                      <TableHead>结束日期</TableHead>
                      <TableHead>请假天数</TableHead>
                      <TableHead>请假原因</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>张静</TableCell>
                      <TableCell>教学部</TableCell>
                      <TableCell>年假</TableCell>
                      <TableCell>2024-04-15</TableCell>
                      <TableCell>2024-04-17</TableCell>
                      <TableCell>3天</TableCell>
                      <TableCell>个人休假</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          已批准
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>李明</TableCell>
                      <TableCell>运营部</TableCell>
                      <TableCell>病假</TableCell>
                      <TableCell>2024-04-10</TableCell>
                      <TableCell>2024-04-10</TableCell>
                      <TableCell>1天</TableCell>
                      <TableCell>感冒发烧</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          已批准
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>王芳</TableCell>
                      <TableCell>教学部</TableCell>
                      <TableCell>事假</TableCell>
                      <TableCell>2024-04-12</TableCell>
                      <TableCell>2024-04-12</TableCell>
                      <TableCell>1天</TableCell>
                      <TableCell>家中有事</TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-green-500">
                          已批准
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>赵健</TableCell>
                      <TableCell>销售部</TableCell>
                      <TableCell>婚假</TableCell>
                      <TableCell>2024-05-01</TableCell>
                      <TableCell>2024-05-07</TableCell>
                      <TableCell>7天</TableCell>
                      <TableCell>结婚</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                          待审批
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          审批
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>钱蓉</TableCell>
                      <TableCell>财务部</TableCell>
                      <TableCell>产假</TableCell>
                      <TableCell>2024-06-01</TableCell>
                      <TableCell>2024-08-30</TableCell>
                      <TableCell>90天</TableCell>
                      <TableCell>生育</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                          待审批
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          审批
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">假期规则设置</h3>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>假期类型</TableHead>
                        <TableHead>适用对象</TableHead>
                        <TableHead>年度额度</TableHead>
                        <TableHead>单次最长天数</TableHead>
                        <TableHead>是否带薪</TableHead>
                        <TableHead>是否需要证明</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>年假</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell>5-15天</TableCell>
                        <TableCell>不限</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>病假</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell>10天</TableCell>
                        <TableCell>3天</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>事假</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell>5天</TableCell>
                        <TableCell>2天</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell>否</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>婚假</TableCell>
                        <TableCell>全体员工</TableCell>
                        <TableCell>7天</TableCell>
                        <TableCell>7天</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>产假</TableCell>
                        <TableCell>女性员工</TableCell>
                        <TableCell>98天</TableCell>
                        <TableCell>98天</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell>是</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            编辑
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    添加假期类型
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="records" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>考勤记录</CardTitle>
              <CardDescription>查看和管理员工考勤记录</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <div className="relative flex-1 mr-4">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input placeholder="搜索员工姓名、部门..." className="pl-8 max-w-sm" />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    导出
                  </Button>
                  <Button variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>日期</TableHead>
                      <TableHead>员工</TableHead>
                      <TableHead>部门</TableHead>
                      <TableHead>上班打卡</TableHead>
                      <TableHead>下班打卡</TableHead>
                      <TableHead>工作时长</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>2024-04-01</TableCell>
                      <TableCell>张静</TableCell>
                      <TableCell>教学部</TableCell>
                      <TableCell>08:55</TableCell>
                      <TableCell>17:05</TableCell>
                      <TableCell>8小时10分钟</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          正常
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2024-04-01</TableCell>
                      <TableCell>李明</TableCell>
                      <TableCell>运营部</TableCell>
                      <TableCell>12:05</TableCell>
                      <TableCell>20:10</TableCell>
                      <TableCell>8小时5分钟</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          正常
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2024-04-01</TableCell>
                      <TableCell>王芳</TableCell>
                      <TableCell>教学部</TableCell>
                      <TableCell>16:10</TableCell>
                      <TableCell>22:05</TableCell>
                      <TableCell>5小时55分钟</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          正常
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2024-04-01</TableCell>
                      <TableCell>赵健</TableCell>
                      <TableCell>销售部</TableCell>
                      <TableCell>09:20</TableCell>
                      <TableCell>18:05</TableCell>
                      <TableCell>8小时45分钟</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                          迟到
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2024-04-01</TableCell>
                      <TableCell>钱蓉</TableCell>
                      <TableCell>财务部</TableCell>
                      <TableCell>09:05</TableCell>
                      <TableCell>17:50</TableCell>
                      <TableCell>8小时45分钟</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          正常
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground">显示 1-5 条，共 125 条记录</div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" disabled>
                    上一页
                  </Button>
                  <Button variant="outline" size="sm">
                    下一页
                  </Button>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">考勤统计</h3>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">出勤率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center">
                        <div className="text-3xl font-bold">98.5%</div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">迟到率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center">
                        <div className="text-3xl font-bold">1.2%</div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">早退率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center">
                        <div className="text-3xl font-bold">0.5%</div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">旷工率</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-center">
                        <div className="text-3xl font-bold">0.1%</div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="flex justify-end">
                  <Button variant="outline">
                    <FileText className="mr-2 h-4 w-4" />
                    生成月度报表
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

