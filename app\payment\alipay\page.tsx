"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { QrCode, Search, AlertCircle, RefreshCw, Smartphone } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

export default function AlipayPaymentPage() {
  const [amount, setAmount] = useState("")
  const [description, setDescription] = useState("")
  const [qrCodeGenerated, setQrCodeGenerated] = useState(false)

  const [recentPayments] = useState([
    {
      id: "ZFB20250328123456",
      amount: "¥199.00",
      description: "基础瑜伽月卡",
      time: "2025-03-28 14:30:25",
      status: "success",
      type: "扫码支付",
    },
    {
      id: "ZFB20250328234567",
      amount: "¥299.00",
      description: "高级瑜伽季卡",
      time: "2025-03-28 11:15:42",
      status: "success",
      type: "手机网站支付",
    },
    {
      id: "ZFB20250328345678",
      amount: "¥99.00",
      description: "单次体验课",
      time: "2025-03-28 09:45:18",
      status: "pending",
      type: "扫码支付",
    },
  ])

  const handleGenerateQRCode = () => {
    if (!amount || Number.parseFloat(amount) <= 0) {
      alert("请输入有效的金额")
      return
    }

    setQrCodeGenerated(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">支付宝</h1>
      </div>

      <Tabs defaultValue="qrcode" className="space-y-4">
        <TabsList>
          <TabsTrigger value="qrcode">扫码支付</TabsTrigger>
          <TabsTrigger value="h5">手机网站支付</TabsTrigger>
          <TabsTrigger value="notification">支付结果通知</TabsTrigger>
          <TabsTrigger value="query">支付状态查询</TabsTrigger>
          <TabsTrigger value="refund">退款处理</TabsTrigger>
        </TabsList>

        <TabsContent value="qrcode" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付宝扫码支付</CardTitle>
              <CardDescription>生成支付宝支付二维码供用户扫码支付</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="alipay-amount">支付金额（元）</Label>
                    <Input
                      id="alipay-amount"
                      placeholder="请输入金额"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alipay-description">商品描述</Label>
                    <Input
                      id="alipay-description"
                      placeholder="请输入商品描述"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                    />
                  </div>

                  <Button onClick={handleGenerateQRCode}>生成支付二维码</Button>
                </div>

                <div className="flex flex-col items-center justify-center">
                  {qrCodeGenerated ? (
                    <div className="text-center">
                      <div className="border-4 border-[#1677FF] p-4 inline-block mb-4">
                        <QrCode className="h-48 w-48 text-[#1677FF]" />
                      </div>
                      <div className="text-lg font-medium">¥{amount}</div>
                      <div className="text-sm text-muted-foreground">{description || "支付宝扫码支付"}</div>
                      <Button variant="outline" className="mt-4" onClick={() => setQrCodeGenerated(false)}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        重新生成
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center">
                      <QrCode className="h-48 w-48 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground">请输入金额并点击生成支付二维码</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>最近支付记录</CardTitle>
              <CardDescription>显示最近的支付宝支付记录</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>交易单号</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>商品描述</TableHead>
                    <TableHead>支付方式</TableHead>
                    <TableHead>支付时间</TableHead>
                    <TableHead>状态</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentPayments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">{payment.id}</TableCell>
                      <TableCell>{payment.amount}</TableCell>
                      <TableCell>{payment.description}</TableCell>
                      <TableCell>{payment.type}</TableCell>
                      <TableCell>{payment.time}</TableCell>
                      <TableCell>
                        <Badge variant={payment.status === "success" ? "default" : "outline"}>
                          {payment.status === "success" ? "支付成功" : "待支付"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="h5" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>手机网站支付</CardTitle>
              <CardDescription>生成支付宝手机网站支付链接</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="h5-amount">支付金额（元）</Label>
                    <Input id="h5-amount" placeholder="请输入金额" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="h5-description">商品描述</Label>
                    <Input id="h5-description" placeholder="请输入商品描述" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="h5-return-url">支付完成跳转URL</Label>
                    <Input id="h5-return-url" placeholder="请输入支付完成后跳转的URL" />
                  </div>

                  <Button>生成支付链接</Button>
                </div>

                <div className="flex flex-col items-center justify-center border rounded-md p-4">
                  <Smartphone className="h-24 w-24 text-[#1677FF] mb-4" />
                  <h3 className="font-medium text-lg mb-2">手机网站支付</h3>
                  <p className="text-sm text-muted-foreground mb-4 text-center">
                    生成支付链接后，用户可在手机浏览器中打开并完成支付
                  </p>
                  <div className="w-full p-3 bg-muted rounded-md text-xs font-mono break-all">
                    https://openapi.alipay.com/gateway.do?app_id=2021000000000000&method=alipay.trade.wap.pay&format=JSON&charset=utf-8&sign_type=RSA2&timestamp=2025-03-28+14:30:25&version=1.0&sign=MIGfMA0GCSqGSIb...
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notification" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付结果通知</CardTitle>
              <CardDescription>接收支付宝支付结果通知</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="alipay-notify-url">通知URL</Label>
                <Input id="alipay-notify-url" value="https://youryogastudio.com/api/alipay/payment/notify" readOnly />
                <p className="text-sm text-muted-foreground">请在支付宝开放平台设置此通知URL</p>
                <p className="text-sm text-muted-foreground">请在支付宝开放平台设置此通知URL</p>
              </div>

              <div className="space-y-2">
                <Label>最近通知记录</Label>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>通知时间</TableHead>
                      <TableHead>交易单号</TableHead>
                      <TableHead>金额</TableHead>
                      <TableHead>状态</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>2025-03-28 14:30:28</TableCell>
                      <TableCell>ZFB20250328123456</TableCell>
                      <TableCell>¥199.00</TableCell>
                      <TableCell>
                        <Badge>支付成功</Badge>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>2025-03-28 11:15:45</TableCell>
                      <TableCell>ZFB20250328234567</TableCell>
                      <TableCell>¥299.00</TableCell>
                      <TableCell>
                        <Badge>支付成功</Badge>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="query" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付状态查询</CardTitle>
              <CardDescription>查询支付宝交易状态</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input placeholder="输入交易单号或商户订单号" />
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <div className="flex items-center justify-center p-8 border rounded-lg">
                <div className="text-center">
                  <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="font-medium text-lg mb-2">暂无查询结果</h3>
                  <p className="text-sm text-muted-foreground">请输入交易单号并点击查询按钮</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="refund" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>退款处理</CardTitle>
              <CardDescription>处理支付宝退款</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="alipay-transaction-id">交易单号</Label>
                    <Input id="alipay-transaction-id" placeholder="请输入支付宝交易单号" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alipay-refund-amount">退款金额（元）</Label>
                    <Input id="alipay-refund-amount" placeholder="请输入退款金额" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="alipay-refund-reason">退款原因</Label>
                    <Input id="alipay-refund-reason" placeholder="请输入退款原因" />
                  </div>

                  <Button>申请退款</Button>
                </div>

                <div>
                  <h3 className="font-medium mb-2">退款记录</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>退款单号</TableHead>
                        <TableHead>金额</TableHead>
                        <TableHead>状态</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>ZFB-RF20250327001</TableCell>
                        <TableCell>¥99.00</TableCell>
                        <TableCell>
                          <Badge>退款成功</Badge>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>ZFB-RF20250326002</TableCell>
                        <TableCell>¥199.00</TableCell>
                        <TableCell>
                          <Badge variant="outline">处理中</Badge>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

