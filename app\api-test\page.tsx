"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"

export default function ApiTestPage() {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/course-types')
      const result = await response.json()
      
      setData(result)
      console.log('API Response:', result)
    } catch (err) {
      console.error('Error fetching data:', err)
      setError(err instanceof Error ? err.message : String(err))
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">API 测试页面</h1>
      
      <div className="flex gap-4 mb-6">
        <Button onClick={fetchData} disabled={loading}>
          {loading ? '加载中...' : '获取课程类型数据'}
        </Button>
      </div>
      
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <p className="text-red-800 font-medium">错误:</p>
          <p className="text-red-600">{error}</p>
        </div>
      )}
      
      {data && (
        <div className="space-y-4">
          <div className="bg-gray-50 border rounded-md p-4">
            <h2 className="font-semibold mb-2">响应状态码: {data.code}</h2>
            <p className="mb-2">消息: {data.msg}</p>
            {data.data && (
              <>
                <p className="mb-2">数据条数: {data.data.total || 0}</p>
                <div className="mt-4">
                  <h3 className="font-semibold mb-2">数据列表:</h3>
                  <pre className="bg-gray-100 p-4 rounded-md overflow-auto max-h-96">
                    {JSON.stringify(data.data.list || [], null, 2)}
                  </pre>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  )
} 