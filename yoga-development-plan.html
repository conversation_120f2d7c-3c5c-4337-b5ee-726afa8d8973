<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>u745cu4f3du9884u7ea6u7cfbu7edfu5f00u53d1u89c4u5212</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 1400px;
            margin: 0 auto;
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        h1 {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        h2 {
            margin-top: 30px;
            margin-bottom: 20px;
            color: #1a73e8;
        }
        .mermaid {
            overflow: auto;
        }
        .legend {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #4285f4;
        }
        .legend h3 {
            margin-top: 0;
            color: #4285f4;
        }
        .legend ul {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 10px;
            list-style-type: none;
            padding-left: 0;
        }
        .legend li {
            display: flex;
            align-items: center;
        }
        .color-box {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            display: inline-block;
            border-radius: 3px;
        }
        .phase-info {
            margin: 30px 0;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .phase-card {
            background-color: #fff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-top: 4px solid #4285f4;
        }
        .phase-1 { border-top-color: #4285f4; }
        .phase-2 { border-top-color: #0f9d58; }
        .phase-3 { border-top-color: #f4b400; }
        .phase-4 { border-top-color: #db4437; }
        .phase-card h3 {
            margin-top: 0;
            color: #333;
        }
        .phase-card ul {
            padding-left: 20px;
        }
        .note {
            margin-top: 30px;
            padding: 15px;
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>u745cu4f3du9884u7ea6u7cfbu7edfu5f00u53d1u89c4u5212</h1>
        
        <div class="legend">
            <h3>u56feu4f8bu8bf4u660e</h3>
            <ul>
                <li><span class="color-box" style="background-color: #4285f4;"></span> u57fau7840u7248u5f00u53d1</li>
                <li><span class="color-box" style="background-color: #0f9d58;"></span> u4e13u4e1au7248u5f00u53d1</li>
                <li><span class="color-box" style="background-color: #f4b400;"></span> u7ecfu8425u7248u5f00u53d1</li>
                <li><span class="color-box" style="background-color: #db4437;"></span> u5b9au5236u7248u5f00u53d1</li>
                <li><span class="color-box" style="background-color: #673ab7;"></span> u6301u7eedu8fedu4ee3u4e0eu7ef4u62a4</li>
            </ul>
        </div>
        
        <h2>u5f00u53d1u5468u8ba1u5212u7518u7279u56fe</h2>
        <div class="mermaid">
            gantt
                title u745cu4f3du9884u7ea6u7cfbu7edfu5f00u53d1u5468u8ba1u5212
                dateFormat YYYY-MM-DD
                axisFormat u7b2c%Uu5468
                todayMarker off
                
                %% u57fau7840u7248u5f00u53d1u9636u6bb5
                section u7b2cu4e00u9636u6bb5: u57fau7840u7248MVP
                u9700u6c42u5206u6790u4e0eu8bbeu8ba1                  :a1, 2025-06-01, 14d
                u6838u5fc3u529fu80fdu5f00u53d1                      :a2, after a1, 28d
                u5de5u4f5cu53f0u5f00u53d1                          :a3, 2025-06-15, 14d
                u8bfeu7a0bu7ba1u7406u5f00u53d1                      :a4, 2025-06-22, 21d
                u4f1au5458u7ba1u7406u5f00u53d1                      :a5, 2025-07-06, 14d
                u8ba2u5355u7ba1u7406u5f00u53d1                      :a6, 2025-07-13, 14d
                u6d88u606fu4e2du5fc3u5f00u53d1                      :a7, 2025-07-20, 10d
                u57fau7840u7edfu8ba1u5f00u53d1                      :a8, 2025-07-25, 10d
                u4f1au5458u7aefu5c0fu7a0bu5e8fu5f00u53d1                :a9, 2025-07-01, 30d
                u7ba1u7406u7aefu5c0fu7a0bu5e8fu5f00u53d1                :a10, 2025-07-15, 30d
                u6d4bu8bd5u4e0eu95eeu9898u4feeu590d                    :a11, 2025-08-01, 14d
                u57fau7840u7248u4e0au7ebfu4e0eu53d1u5e03                :milestone, m1, 2025-08-15, 0d
                
                %% u4e13u4e1au7248u5f00u53d1u9636u6bb5
                section u7b2cu4e8cu9636u6bb5: u4e13u4e1au7248u5347u7ea7
                u4e13u4e1au7248u9700u6c42u5206u6790                    :b1, 2025-08-16, 14d
                u4f1au5458u6807u7b7eu4e0eu7b49u7ea7u529fu80fd              :b2, after b1, 21d
                u6f5cu5ba2u7ba1u7406u529fu80fd                      :b3, 2025-09-06, 14d
                u6559u7ec3u7ba1u7406u529fu80fd                      :b4, 2025-09-13, 14d
                u573au5730u7ba1u7406u529fu80fd                      :b5, 2025-09-20, 14d
                u573au9986u7ba1u7406u529fu80fd                      :b6, 2025-10-04, 14d
                u589eu5f3au5206u6790u529fu80fd                      :b7, 2025-10-11, 14d
                u7cfbu7edfu8bbeu7f6eu4e0eu6743u9650u529fu80fd              :b8, 2025-10-18, 14d
                PCu7aefu7ba1u7406u540eu53f0u5f00u53d1                  :b9, 2025-09-01, 45d
                u6d4bu8bd5u4e0eu95eeu9898u4feeu590d                    :b10, 2025-10-25, 21d
                u4e13u4e1au7248u4e0au7ebfu4e0eu53d1u5e03                :milestone, m2, 2025-11-15, 0d
                
                %% u7ecfu8425u7248u5f00u53d1u9636u6bb5
                section u7b2cu4e09u9636u6bb5: u7ecfu8425u7248u6269u5c55
                u7ecfu8425u7248u9700u6c42u5206u6790                    :c1, 2025-11-16, 14d
                u8bfeu65f6u8d39u7ba1u7406u529fu80fd                    :c2, after c1, 21d
                u8bc4u4ef7u7ba1u7406u529fu80fd                      :c3, 2025-12-07, 21d
                u9000u6b3eu7ba1u7406u529fu80fd                      :c4, 2025-12-21, 14d
                u6df1u5ea6u5206u6790u529fu80fd                      :c5, 2026-01-04, 21d
                u591au529fu80fdu5546u57ceu5f00u53d1                    :c6, 2026-01-11, 30d
                u5171u4eabu80a1u4e1cu7cfbu7edfu5f00u53d1                  :c7, 2026-01-25, 30d
                u7cfbu7edfu6574u5408u4e0eu6d4bu8bd5                    :c8, 2026-02-15, 21d
                u7ecfu8425u7248u4e0au7ebfu4e0eu53d1u5e03                :milestone, m3, 2026-03-15, 0d
                
                %% u5b9au5236u7248u5f00u53d1u9636u6bb5
                section u7b2cu56dbu9636u6bb5: u5b9au5236u7248u5f00u53d1
                u5b9au5236u9700u6c42u8c03u7814                      :d1, 2026-03-16, 21d
                u529fu80fdu589eu5f3au4e0eu5b9au5236u5f00u53d1              :d2, after d1, 60d
                u5b9au5236u5206u6790u62a5u8868u5f00u53d1                  :d3, 2026-04-15, 30d
                u6d4bu8bd5u4e0eu95eeu9898u4feeu590d                    :d4, 2026-06-01, 21d
                u5b9au5236u7248u4ea4u4ed8                        :milestone, m4, 2026-06-30, 0d
                
                %% u6301u7eedu8fedu4ee3u4e0eu7ef4u62a4
                section u6301u7eedu8fedu4ee3
                u57fau7840u7248u7ef4u62a4u4e0eu8fedu4ee3                  :e1, 2025-08-16, 300d
                u4e13u4e1au7248u7ef4u62a4u4e0eu8fedu4ee3                  :e2, 2025-11-16, 200d
                u7ecfu8425u7248u7ef4u62a4u4e0eu8fedu4ee3                  :e3, 2026-03-16, 100d
        </div>
        
        <div class="phase-info">
            <div class="phase-card phase-1">
                <h3>u7b2cu4e00u9636u6bb5: u57fau7840u7248MVP</h3>
                <ul>
                    <li>u5de5u4f5cu53f0uff08u7cfbu7edfu603bu89c8/u524du53f0u5de5u4f5cu53f0uff09</li>
                    <li>u8bfeu7a0bu7ba1u7406uff08u8bfeu7a0bu7c7bu578b/u6392u671f/u9884u7ea6u8bb0u5f55uff09</li>
                    <li>u4f1au5458u7ba1u7406uff08u4f1au5458u5361/u4f1au5458u5217u8868uff09</li>
                    <li>u8ba2u5355u7ba1u7406uff08u8ba2u5355u5217u8868uff09</li>
                    <li>u6d88u606fu4e2du5fc3uff08u6d88u606fu6982u89c8/u89c4u5219u8bbeu7f6euff09</li>
                    <li>u57fau7840u7edfu8ba1uff08u7b80u6613u6570u636eu6c47u603buff09</li>
                    <li>u4f1au5458u7aefu548cu7ba1u7406u7aefu5c0fu7a0bu5e8f</li>
                </ul>
                <p>u9884u8ba1u5468u671f: 2025u5e746u67081u65e5 - 2025u5e748u670815u65e5</p>
            </div>
            
            <div class="phase-card phase-2">
                <h3>u7b2du4e8cu9636u6bb5: u4e13u4e1au7248u5347u7ea7</h3>
                <ul>
                    <li>u4f1au5458u6807u7b7eu4e0eu7b49u7ea7u529fu80fd</li>
                    <li>u6f5cu5ba2u7ba1u7406u529fu80fd</li>
                    <li>u6559u7ec3u7ba1u7406uff08u6559u7ec3u5217u8868/u6392u73eduff09</li>
                    <li>u573au5730u7ba1u7406uff08u573au5730u5217u8868/u9884u8ba2uff09</li>
                    <li>u573au9986u7ba1u7406uff08u5458u5de5/u8bbeu7f6e/u516cu544auff09</li>
                    <li>u589eu5f3au5206u6790uff08u4f1au5458/u8bfeu7a0bu5206u6790uff09</li>
                    <li>u7cfbu7edfu8bbeu7f6eu4e0eu6743u9650u529fu80fd</li>
                    <li>PCu7aefu7ba1u7406u540eu53f0</li>
                </ul>
                <p>u9884u8ba1u5468u671f: 2025u5e748u670816u65e5 - 2025u5e7411u670815u65e5</p>
            </div>
            
            <div class="phase-card phase-3">
                <h3>u7b2cu4e09u9636u6bb5: u7ecfu8425u7248u6269u5c55</h3>
                <ul>
                    <li>u8bfeu65f6u8d39u7ba1u7406u529fu80fd</li>
                    <li>u8bc4u4ef7u7ba1u7406u529fu80fd</li>
                    <li>u9000u6b3eu7ba1u7406u529fu80fd</li>
                    <li>u6df1u5ea6u5206u6790uff08u6536u5165/u6559u7ec3u7ee9u6548uff09</li>
                    <li>u591au529fu80fdu5546u57ceu5f00u53d1</li>
                    <li>u5171u4eabu80a1u4e1cu7cfbu7edfu5f00u53d1</li>
                </ul>
                <p>u9884u8ba1u5468u671f: 2025u5e7411u670816u65e5 - 2026u5e743u670815u65e5</p>
            </div>
            
            <div class="phase-card phase-4">
                <h3>u7b2cu56dbu9636u6bb5: u5b9au5236u7248u5f00u53d1</h3>
                <ul>
                    <li>u5b9au5236u9700u6c42u8c03u7814</li>
                    <li>u529fu80fdu589eu5f3au4e0eu5b9au5236u5f00u53d1</li>
                    <li>u5b9au5236u5206u6790u62a5u8868u5f00u53d1</li>
                    <li>u6309u9700u5b9au5236u7684u7ec8u7aefu652fu6301</li>
                </ul>
                <p>u9884u8ba1u5468u671f: 2026u5e743u670816u65e5 - 2026u5e746u670830u65e5</p>
            </div>
        </div>
        
        <div class="note">
            <p><strong>u6ce8u610fuff1a</strong>u672cu5f00u53d1u8ba1u5212u662fu57fau4e8eu529fu80fdu5168u666fu5bf9u6bd4u8868u5236u5b9auff0cu5305u542bu4e86u4eceu57fau7840u7248u5230u5b9au5236u7248u7684u5b8cu6574u5f00u53d1u5468u671fu3002u6bcfu4e2au7248u672cu7684u5f00u53d1u90fdu5305u542bu9700u6c42u5206u6790u3001u529fu80fdu5f00u53d1u3001u6d4bu8bd5u4e0eu53d1u5e03u7b49u73afu8282u3002u540cu65f6uff0cu6211u4eecu4e5fu5b89u6392u4e86u6301u7eedu8fedu4ee3u4e0eu7ef4u62a4u5de5u4f5cuff0cu786eu4fddu5df2u53d1u5e03u7248u672cu7684u7a33u5b9au8fd0u884cu3002</p>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            gantt: {
                titleTopMargin: 25,
                barHeight: 20,
                barGap: 4,
                topPadding: 50,
                sidePadding: 75,
                gridLineStartPadding: 35,
                fontSize: 12,
                fontFamily: '"Microsoft YaHei", Arial, sans-serif',
                numberSectionStyles: 5,
                axisFormat: 'u7b2c%Uu5468',
                tickInterval: '1week',
                sectionFontSize: 14,
                sectionFontWeight: 500,
                displayMode: 'compact'
            }
        });
    </script>
</body>
</html>
