"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { TrendingUp, CreditCard, Calculator, Gift, AlertTriangle } from "lucide-react"

// 可升级的会员卡类型
const upgradeCardTypes = [
  { id: "annual", name: "瑜伽年卡", price: 3600, originalPrice: 4200, validity: "365天", limit: "不限次数", color: "#4CAF50", discount: 0.1 },
  { id: "premium_annual", name: "高级年卡", price: 4800, originalPrice: 5500, validity: "365天", limit: "不限次数+私教5次", color: "#9C27B0", discount: 0.15 },
  { id: "vip_annual", name: "VIP年卡", price: 6800, originalPrice: 8000, validity: "365天", limit: "不限次数+私教10次+营养咨询", color: "#FF5722", discount: 0.2 },
  { id: "unlimited", name: "终身无限卡", price: 12800, originalPrice: 15000, validity: "终身", limit: "终身不限次数", color: "#795548", discount: 0.25 },
]

interface UpgradeMemberCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentCard: any
}

export function UpgradeMemberCardDialog({
  open,
  onOpenChange,
  currentCard
}: UpgradeMemberCardDialogProps) {
  const { toast } = useToast()
  const [selectedUpgrade, setSelectedUpgrade] = useState("")
  const [upgradeNote, setUpgradeNote] = useState("")
  const [paymentMethod, setPaymentMethod] = useState("")
  const [isCalculating, setIsCalculating] = useState(false)

  // 计算当前卡剩余价值
  const calculateRemainingValue = () => {
    if (!currentCard) return 0
    
    if (currentCard.cardTypeBadge === "期限卡") {
      // 期限卡按剩余天数计算
      const totalDays = currentCard.totalDays || 365
      const remainingDays = currentCard.remainingDays || 0
      return Math.round((currentCard.actualPrice * remainingDays) / totalDays)
    } else if (currentCard.cardTypeBadge === "次卡") {
      // 次卡按剩余次数计算
      const totalCount = currentCard.totalCount || 20
      const remainingCount = currentCard.remainingCount || 0
      return Math.round((currentCard.actualPrice * remainingCount) / totalCount)
    } else if (currentCard.cardTypeBadge === "储值卡") {
      // 储值卡直接返回剩余金额
      return currentCard.remainingValue || 0
    }
    return 0
  }

  // 计算升级费用
  const calculateUpgradeCost = () => {
    if (!selectedUpgrade) return { upgradeCost: 0, discount: 0, finalCost: 0 }
    
    const targetCard = upgradeCardTypes.find(card => card.id === selectedUpgrade)
    if (!targetCard) return { upgradeCost: 0, discount: 0, finalCost: 0 }
    
    const remainingValue = calculateRemainingValue()
    const upgradeCost = targetCard.price - remainingValue
    const discount = Math.round(upgradeCost * targetCard.discount)
    const finalCost = Math.max(0, upgradeCost - discount)
    
    return { upgradeCost, discount, finalCost, remainingValue, targetPrice: targetCard.price }
  }

  const handleUpgrade = async () => {
    if (!selectedUpgrade || !paymentMethod) {
      toast({
        title: "请完善信息",
        description: "请选择升级卡种和支付方式",
        variant: "destructive",
      })
      return
    }

    setIsCalculating(true)
    
    try {
      // 模拟升级处理
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const targetCard = upgradeCardTypes.find(card => card.id === selectedUpgrade)
      const { finalCost } = calculateUpgradeCost()
      
      toast({
        title: "升级成功",
        description: `已成功升级为${targetCard?.name}，支付金额：¥${finalCost}`,
      })
      
      onOpenChange(false)
      
      // 重置表单
      setSelectedUpgrade("")
      setUpgradeNote("")
      setPaymentMethod("")
      
    } catch (error) {
      toast({
        title: "升级失败",
        description: "升级过程中出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsCalculating(false)
    }
  }

  const { upgradeCost, discount, finalCost, remainingValue, targetPrice } = calculateUpgradeCost()
  const selectedCard = upgradeCardTypes.find(card => card.id === selectedUpgrade)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            升级会员卡
          </DialogTitle>
          <DialogDescription>
            将当前会员卡升级到更高级别，享受更多权益和优惠
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 当前卡片信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">当前会员卡</h3>
            {currentCard && (
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">{currentCard.cardType}</CardTitle>
                    <Badge style={{ backgroundColor: currentCard.cardTypeColor }}>
                      {currentCard.cardTypeBadge}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">购买价格：</span>
                      <span className="font-medium">¥{currentCard.actualPrice}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">剩余价值：</span>
                      <span className="font-medium text-green-600">¥{calculateRemainingValue()}</span>
                    </div>
                    {currentCard.remainingDays !== null && (
                      <div>
                        <span className="text-muted-foreground">剩余天数：</span>
                        <span className="font-medium">{currentCard.remainingDays}天</span>
                      </div>
                    )}
                    {currentCard.remainingCount !== null && (
                      <div>
                        <span className="text-muted-foreground">剩余次数：</span>
                        <span className="font-medium">{currentCard.remainingCount}次</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* 升级选项 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">选择升级卡种</h3>
            <div className="space-y-3">
              {upgradeCardTypes.map((card) => (
                <Card 
                  key={card.id} 
                  className={`cursor-pointer transition-all ${
                    selectedUpgrade === card.id ? 'ring-2 ring-primary' : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedUpgrade(card.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{card.name}</h4>
                          <Badge style={{ backgroundColor: card.color }} className="text-xs">
                            {Math.round(card.discount * 100)}%优惠
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{card.limit}</p>
                        <div className="flex items-center gap-4 text-sm">
                          <span className="text-muted-foreground line-through">¥{card.originalPrice}</span>
                          <span className="font-semibold text-lg">¥{card.price}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-muted-foreground">有效期</div>
                        <div className="text-sm font-medium">{card.validity}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* 费用计算 */}
        {selectedUpgrade && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Calculator className="h-4 w-4" />
                升级费用计算
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-muted-foreground">目标卡价格</div>
                  <div className="text-lg font-semibold text-blue-600">¥{targetPrice}</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-muted-foreground">当前卡价值</div>
                  <div className="text-lg font-semibold text-green-600">-¥{remainingValue}</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <div className="text-muted-foreground">升级优惠</div>
                  <div className="text-lg font-semibold text-orange-600">-¥{discount}</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-muted-foreground">需支付</div>
                  <div className="text-lg font-semibold text-red-600">¥{finalCost}</div>
                </div>
              </div>
              
              {finalCost === 0 && (
                <div className="mt-4 p-3 bg-green-100 border border-green-200 rounded-lg flex items-center gap-2">
                  <Gift className="h-4 w-4 text-green-600" />
                  <span className="text-green-700 text-sm">恭喜！您的当前卡价值已足够升级，无需额外支付！</span>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* 支付方式和备注 */}
        {selectedUpgrade && finalCost > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="space-y-2">
              <Label htmlFor="paymentMethod">支付方式</Label>
              <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                <SelectTrigger>
                  <SelectValue placeholder="选择支付方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="wechat">微信支付</SelectItem>
                  <SelectItem value="alipay">支付宝</SelectItem>
                  <SelectItem value="cash">现金</SelectItem>
                  <SelectItem value="card">银行卡</SelectItem>
                  <SelectItem value="transfer">银行转账</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="upgradeNote">升级备注</Label>
              <Input
                id="upgradeNote"
                placeholder="升级原因或备注信息"
                value={upgradeNote}
                onChange={(e) => setUpgradeNote(e.target.value)}
              />
            </div>
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={handleUpgrade} 
            disabled={!selectedUpgrade || (finalCost > 0 && !paymentMethod) || isCalculating}
            className="min-w-[100px]"
          >
            {isCalculating ? "处理中..." : `确认升级${finalCost > 0 ? ` (¥${finalCost})` : ""}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
