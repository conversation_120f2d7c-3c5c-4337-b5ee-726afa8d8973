"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"

// 模拟数据
const todayCourses = [
  {
    id: "YG001",
    name: "基础瑜伽入门",
    startTime: "10:00",
    duration: 90,
    coach: "张教练",
    venue: "1号瑜伽室",
    bookings: 12,
    capacity: 15,
    status: "upcoming", // upcoming, ongoing, full, completed
    type: "基础课程",
  },
  {
    id: "YG002",
    name: "高级瑜伽进阶",
    startTime: "14:00",
    duration: 90,
    coach: "李教练",
    venue: "2号瑜伽室",
    bookings: 8,
    capacity: 10,
    status: "ongoing",
    type: "进阶课程",
  },
  {
    id: "YG003",
    name: "阴瑜伽放松",
    startTime: "16:00",
    duration: 60,
    coach: "王教练",
    venue: "3号瑜伽室",
    bookings: 15,
    capacity: 15,
    status: "full",
    type: "放松课程",
  },
  {
    id: "YG004",
    name: "孕产瑜伽",
    startTime: "18:30",
    duration: 60,
    coach: "赵教练",
    venue: "1号瑜伽室",
    bookings: 6,
    capacity: 10,
    status: "upcoming",
    type: "专项课程",
  },
]

export function TodayCourses() {
  const router = useRouter()

  // 获取当前日期
  const today = new Date().toLocaleDateString('zh-CN', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  // 获取状态对应的标签样式和文本
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "upcoming":
        return { variant: "outline" as const, text: "即将开始" }
      case "ongoing":
        return { variant: "default" as const, text: "进行中" }
      case "full":
        return { variant: "secondary" as const, text: "已满员" }
      case "completed":
        return { variant: "outline" as const, text: "已结束" }
      default:
        return { variant: "outline" as const, text: "未知状态" }
    }
  }

  // 获取课程类型对应的样式
  const getCourseTypeStyle = (type: string) => {
    switch(type) {
      case "基础课程":
        return "bg-blue-50 text-blue-700"
      case "进阶课程":
        return "bg-purple-50 text-purple-700"
      case "放松课程":
        return "bg-green-50 text-green-700"
      case "专项课程":
        return "bg-amber-50 text-amber-700"
      default:
        return "bg-gray-50 text-gray-700"
    }
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">今日课程</CardTitle>
        <CardDescription>{today}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {todayCourses.map((course) => {
            const statusBadge = getStatusBadge(course.status)
            const typeStyle = getCourseTypeStyle(course.type)

            return (
              <div
                key={course.id}
                className="flex items-center p-3 rounded-md border hover:bg-muted/50 cursor-pointer transition-colors"
                onClick={() => router.push(`/courses/${course.id}`)}
              >
                <div className="w-16 text-center">
                  <div className="text-sm font-medium">{course.startTime}</div>
                  <div className="text-xs text-muted-foreground">{course.duration}分钟</div>
                </div>
                <div className="ml-4 flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">{course.name}</span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${typeStyle}`}>{course.type}</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {course.coach} · {course.venue} · {course.bookings}/{course.capacity}人
                  </div>
                </div>
                <Badge variant={statusBadge.variant} className="ml-2">
                  {statusBadge.text}
                </Badge>
              </div>
            )
          })}
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          className="w-full"
          onClick={() => router.push('/courses/schedule')}
        >
          查看全部课程
        </Button>
      </CardFooter>
    </Card>
  )
}
