import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// POST /api/tenants
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('接收到的数据:', body);
    const {
      name, // 超管姓名
      phone, // 超管手机号
      wechat, // 超管微信号
      company, // 租户名/公司名
      employeeCount, // 员工规模
      storeType, // 门店类型
      channel, // 渠道来源
      message // 需求描述
    } = body;

    // 检查必要参数
    if (!name || !phone) {
      console.error('缺少必要参数: name或phone');
      return NextResponse.json({ 
        success: false, 
        error: '缺少必要参数' 
      }, { status: 400 });
    }

    // 使用try-catch包装事务，以便捕获更具体的错误
    try {
      // 使用事务确保数据一致性
      const result = await prisma.$transaction(async (tx) => {
        console.log('开始创建租户...');
        // 1. 创建租户
        const tenant = await tx.tenant.create({
          data: {
            tenant_name: company || `${name}的工作室`,
            contact_person: name,
            phone,
            status: 0, // 0-待审核
          }
        });
        console.log('租户创建成功:', tenant.id);

        console.log('开始创建门店...');
        // 2. 创建门店（以公司名为门店名，可根据需要调整）
        const store = await tx.store.create({
          data: {
            tenant_id: tenant.id,
            store_name: company || `${name}的工作室`,
            contact_person: name,
            phone,
            status: 0, // 0-待审核/暂停
          }
        });
        console.log('门店创建成功:', store.id);

        console.log('开始创建超级管理员...');
        // 3. 创建超级管理员
        const employee = await tx.employee.create({
          data: {
            tenant_id: tenant.id,
            real_name: name,
            phone,
            openid: wechat || '',
            is_super_admin: 1,
            status: 1,
          }
        });
        console.log('超级管理员创建成功:', employee.id);

        return { tenant, store, employee };
      });

      console.log('事务完成，返回成功响应');
      return NextResponse.json({ 
        success: true, 
        tenantId: result.tenant.id, 
        storeId: result.store.id, 
        employeeId: result.employee.id, 
        message: '申请成功，等待平台审核' 
      });
    } catch (txError: any) {
      console.error('事务执行失败:', txError);
      // 返回更具体的错误信息
      return NextResponse.json({ 
        success: false, 
        error: `数据库操作失败: ${txError.message || '未知错误'}` 
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('租户注册失败:', error);
    return NextResponse.json({ 
      success: false, 
      error: `注册失败: ${error.message || '请稍后再试'}` 
    }, { status: 500 });
  }
} 