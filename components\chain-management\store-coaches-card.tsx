"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  UserCog, 
  Calendar, 
  Star, 
  Clock, 
  ChevronRight,
  BarChart4
} from "lucide-react"

interface StoreCoachesCardProps {
  store: any
}

export function StoreCoachesCard({ store }: StoreCoachesCardProps) {
  // 模拟教练数据
  const coaches = [
    {
      id: "1",
      name: "王教练",
      avatar: "/avatars/coach1.jpg",
      specialty: "哈他瑜伽",
      rating: 4.9,
      classesThisMonth: 45,
      status: "active",
      experience: "5年",
    },
    {
      id: "2",
      name: "李教练",
      avatar: "/avatars/coach2.jpg",
      specialty: "流瑜伽",
      rating: 4.8,
      classesThisMonth: 38,
      status: "active",
      experience: "7年",
    },
    {
      id: "3",
      name: "张教练",
      avatar: "/avatars/coach3.jpg",
      specialty: "阴瑜伽",
      rating: 4.7,
      classesThisMonth: 32,
      status: "active",
      experience: "4年",
    },
    {
      id: "4",
      name: "赵教练",
      avatar: "/avatars/coach4.jpg",
      specialty: "普拉提",
      rating: 4.6,
      classesThisMonth: 28,
      status: "active",
      experience: "3年",
    },
  ]

  // 获取教练姓名首字母
  const getInitials = (name: string) => {
    return name.charAt(0)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <UserCog className="h-5 w-5 text-blue-500" />
              教练团队
            </CardTitle>
            <CardDescription>门店教练团队信息</CardDescription>
          </div>
          <Badge variant="outline">{store.coachCount} 名教练</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {coaches.slice(0, 4).map((coach) => (
            <div key={coach.id} className="flex items-center justify-between border-b pb-3">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={coach.avatar} alt={coach.name} />
                  <AvatarFallback>{getInitials(coach.name)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{coach.name}</div>
                  <div className="text-sm text-muted-foreground">{coach.specialty}</div>
                </div>
              </div>
              <div className="flex flex-col items-end">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                  <span className="font-medium">{coach.rating}</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  <span>本月 {coach.classesThisMonth} 节课</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" size="sm">
          <BarChart4 className="mr-2 h-4 w-4" />
          教练绩效
        </Button>
        <Button size="sm">
          查看全部
          <ChevronRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  )
}
