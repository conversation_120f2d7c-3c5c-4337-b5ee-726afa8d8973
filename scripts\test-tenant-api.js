// 测试租户API的脚本
// 使用Node.js 18+的内置fetch API

async function testTenantAPI() {
  try {
    console.log('🧪 开始测试租户API...');
    
    const tenantId = 1; // 使用测试租户ID
    
    // 测试1: 获取租户信息
    console.log('\n📤 测试获取租户信息...');
    const getResponse = await fetch(`http://localhost:3001/api/tenant?tenantId=${tenantId}`);
    console.log('📥 获取租户信息响应状态:', getResponse.status);
    
    const getResult = await getResponse.json();
    console.log('📥 获取租户信息响应数据:', JSON.stringify(getResult, null, 2));
    
    if (getResult.success) {
      console.log('✅ 获取租户信息成功!');
      console.log(`租户名称: ${getResult.data.name}`);
      console.log(`联系人: ${getResult.data.contactPerson}`);
      console.log(`状态: ${getResult.data.status}`);
    } else {
      console.log('❌ 获取租户信息失败:', getResult.error);
      return;
    }
    
    // 测试2: 更新租户信息
    console.log('\n📤 测试更新租户信息...');
    const updateData = {
      tenantId: tenantId,
      name: getResult.data.name + ' (已更新)',
      contactPerson: getResult.data.contactPerson,
      phone: getResult.data.phone,
      email: '<EMAIL>',
      address: '更新后的地址：北京市朝阳区测试路123号',
      city: '朝阳区',
      province: '北京市',
      country: '中国',
      businessLicense: getResult.data.businessLicense,
      logoUrl: 'https://example.com/new-logo.png'
    };
    
    console.log('更新租户数据:', JSON.stringify(updateData, null, 2));
    
    const updateResponse = await fetch('http://localhost:3001/api/tenant', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    });
    
    console.log('📥 更新租户信息响应状态:', updateResponse.status);
    
    const updateResult = await updateResponse.json();
    console.log('📥 更新租户信息响应数据:', JSON.stringify(updateResult, null, 2));
    
    if (updateResult.success) {
      console.log('✅ 更新租户信息成功!');
      console.log(`更新后名称: ${updateResult.data.name}`);
      console.log(`更新后邮箱: ${updateResult.data.email}`);
      console.log(`更新后地址: ${updateResult.data.address}`);
    } else {
      console.log('❌ 更新租户信息失败:', updateResult.error);
    }
    
    // 测试3: 验证更新后的信息
    console.log('\n📤 验证更新后的租户信息...');
    const verifyResponse = await fetch(`http://localhost:3001/api/tenant?tenantId=${tenantId}`);
    const verifyResult = await verifyResponse.json();
    
    if (verifyResult.success) {
      console.log('✅ 验证成功，信息已更新!');
      console.log(`验证名称: ${verifyResult.data.name}`);
      console.log(`验证邮箱: ${verifyResult.data.email}`);
      console.log(`验证地址: ${verifyResult.data.address}`);
    } else {
      console.log('❌ 验证失败:', verifyResult.error);
    }
    
    // 测试4: 恢复原始信息
    console.log('\n📤 恢复原始租户信息...');
    const restoreData = {
      tenantId: tenantId,
      name: getResult.data.name, // 恢复原始名称
      contactPerson: getResult.data.contactPerson,
      phone: getResult.data.phone,
      email: getResult.data.email,
      address: getResult.data.address,
      city: getResult.data.city,
      province: getResult.data.province,
      country: getResult.data.country,
      businessLicense: getResult.data.businessLicense,
      logoUrl: getResult.data.logoUrl
    };
    
    const restoreResponse = await fetch('http://localhost:3001/api/tenant', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(restoreData)
    });
    
    const restoreResult = await restoreResponse.json();
    
    if (restoreResult.success) {
      console.log('✅ 恢复原始信息成功!');
    } else {
      console.log('❌ 恢复原始信息失败:', restoreResult.error);
    }
    
    console.log('\n🎉 租户API测试完成!');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testTenantAPI();
