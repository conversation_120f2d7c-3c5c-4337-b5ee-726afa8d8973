"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  ArrowLeft,
  Edit,
  Plus,
  Search,
  Trash2,
  X
} from "lucide-react"

import { mockSeriesCourses, mockSeriesEnrollments } from "@/lib/mock/series-courses"
import { SeriesEnrollment, PaymentStatus } from "@/types/series-courses"

// 表单验证模式
const enrollmentFormSchema = z.object({
  memberId: z.string().min(1, "请选择会员"),
  memberName: z.string().min(1, "请输入会员姓名"),
  enrollDate: z.string().min(1, "请选择报名日期"),
  paymentStatus: z.string().min(1, "请选择支付状态"),
  paymentAmount: z.string().min(1, "请输入支付金额"),
  membershipCardId: z.string().optional(),
  notes: z.string().optional(),
})

export default function SeriesCourseEnrollmentsPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const courseId = params.id
  const [searchKeyword, setSearchKeyword] = useState("")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedEnrollment, setSelectedEnrollment] = useState<SeriesEnrollment | null>(null)

  // 获取课程详情
  const course = mockSeriesCourses.find(c => c.id === courseId)

  // 获取课程报名
  const enrollments = mockSeriesEnrollments.filter(e => e.seriesCourseId === courseId)

  // 筛选报名
  const filteredEnrollments = enrollments.filter(enrollment => {
    return searchKeyword === "" ||
      enrollment.memberName.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      (enrollment.membershipCardName && enrollment.membershipCardName.toLowerCase().includes(searchKeyword.toLowerCase()))
  })

  // 如果课程不存在，显示错误信息
  if (!course) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold tracking-tight ml-2">课程不存在</h1>
        </div>
        <p>未找到ID为 {courseId} 的系列课程。</p>
        <Button onClick={() => router.push("/courses/series-courses")} className="mt-4">
          返回系列课程列表
        </Button>
      </div>
    )
  }

  // 添加报名表单
  const addForm = useForm<z.infer<typeof enrollmentFormSchema>>({
    resolver: zodResolver(enrollmentFormSchema),
    defaultValues: {
      memberId: "",
      memberName: "",
      enrollDate: new Date().toISOString().split('T')[0],
      paymentStatus: "pending",
      paymentAmount: course.price.toString(),
      membershipCardId: "none",
      notes: "",
    },
  })

  // 编辑报名表单
  const editForm = useForm<z.infer<typeof enrollmentFormSchema>>({
    resolver: zodResolver(enrollmentFormSchema),
    defaultValues: {
      memberId: "",
      memberName: "",
      enrollDate: "",
      paymentStatus: "",
      paymentAmount: "",
      membershipCardId: "",
      notes: "",
    },
  })

  // 处理添加报名
  const handleAddEnrollment = (values: z.infer<typeof enrollmentFormSchema>) => {
    console.log("添加报名:", values)
    toast({
      title: "添加报名成功",
      description: `已成功添加会员 ${values.memberName} 的报名记录。`,
    })
    setShowAddDialog(false)
    addForm.reset()
  }

  // 处理编辑报名
  const handleEditEnrollment = (values: z.infer<typeof enrollmentFormSchema>) => {
    console.log("编辑报名:", values)
    toast({
      title: "编辑报名成功",
      description: `已成功更新会员 ${values.memberName} 的报名记录。`,
    })
    setShowEditDialog(false)
  }

  // 处理删除报名
  const handleDeleteEnrollment = () => {
    if (!selectedEnrollment) return

    console.log("删除报名:", selectedEnrollment)
    toast({
      title: "删除报名成功",
      description: `已成功删除会员 ${selectedEnrollment.memberName} 的报名记录。`,
    })
    setShowDeleteDialog(false)
    setSelectedEnrollment(null)
  }

  // 打开编辑对话框
  const openEditDialog = (enrollment: SeriesEnrollment) => {
    setSelectedEnrollment(enrollment)
    editForm.reset({
      memberId: enrollment.memberId,
      memberName: enrollment.memberName,
      enrollDate: enrollment.enrollDate,
      paymentStatus: enrollment.paymentStatus,
      paymentAmount: enrollment.paymentAmount.toString(),
      membershipCardId: enrollment.membershipCardId || "none",
      notes: enrollment.notes || "",
    })
    setShowEditDialog(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (enrollment: SeriesEnrollment) => {
    setSelectedEnrollment(enrollment)
    setShowDeleteDialog(true)
  }

  // 获取支付状态标签样式
  const getPaymentStatusBadge = (status: PaymentStatus) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline">待支付</Badge>
      case "paid":
        return <Badge variant="default">已支付</Badge>
      case "refunded":
        return <Badge variant="destructive">已退款</Badge>
    }
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" onClick={() => router.push(`/courses/series-courses/${courseId}`)}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-3xl font-bold tracking-tight ml-2">报名管理</h1>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>{course.name}</CardTitle>
          <CardDescription>
            {course.courseType} | {course.instructor} | {course.totalSessions}节课 | {course.startDate} 至 {course.endDate}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索会员姓名或会员卡"
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  className="pl-10 pr-10"
                />
                {searchKeyword && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
                    onClick={() => setSearchKeyword("")}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
            <div>
              <p className="text-sm text-muted-foreground mb-2 md:mb-0 md:mr-4">
                已报名 <span className="font-medium">{enrollments.length}</span> 人，
                最大容量 <span className="font-medium">{course.maxStudents}</span> 人
              </p>
            </div>
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              添加报名
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="border rounded-md overflow-hidden">
        <div className="grid grid-cols-12 bg-muted px-4 py-3 font-medium text-sm">
          <div className="col-span-2">会员姓名</div>
          <div className="col-span-2">报名日期</div>
          <div className="col-span-3">会员卡</div>
          <div className="col-span-2">支付金额</div>
          <div className="col-span-1">支付状态</div>
          <div className="col-span-2 text-right">操作</div>
        </div>

        {filteredEnrollments.length === 0 ? (
          <div className="px-4 py-8 text-center text-muted-foreground">
            {searchKeyword ? "没有找到匹配的报名记录" : "暂无报名记录，请点击"添加报名"按钮添加。"}
          </div>
        ) : (
          <div className="divide-y">
            {filteredEnrollments.map((enrollment) => (
              <div key={enrollment.id} className="grid grid-cols-12 px-4 py-3 hover:bg-accent/50">
                <div className="col-span-2 flex items-center">
                  <span>{enrollment.memberName}</span>
                </div>
                <div className="col-span-2 flex items-center">
                  <span>{enrollment.enrollDate}</span>
                </div>
                <div className="col-span-3 flex items-center">
                  <span>{enrollment.membershipCardName || "直接支付"}</span>
                </div>
                <div className="col-span-2 flex items-center">
                  <span>¥{enrollment.paymentAmount}</span>
                </div>
                <div className="col-span-1 flex items-center">
                  {getPaymentStatusBadge(enrollment.paymentStatus)}
                </div>
                <div className="col-span-2 flex items-center justify-end gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => openEditDialog(enrollment)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => openDeleteDialog(enrollment)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 添加报名对话框 */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>添加报名</DialogTitle>
            <DialogDescription>
              为系列课程添加新的学员报名。
            </DialogDescription>
          </DialogHeader>

          <Form {...addForm}>
            <form onSubmit={addForm.handleSubmit(handleAddEnrollment)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={addForm.control}
                  name="memberId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>会员</FormLabel>
                      <Select onValueChange={(value) => {
                        field.onChange(value)
                        // 模拟根据会员ID获取会员姓名
                        const memberNames = {
                          "member-001": "张三",
                          "member-002": "李四",
                          "member-003": "王五",
                          "member-004": "赵六"
                        }
                        addForm.setValue("memberName", memberNames[value as keyof typeof memberNames] || "")
                      }} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择会员" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="member-001">张三</SelectItem>
                          <SelectItem value="member-002">李四</SelectItem>
                          <SelectItem value="member-003">王五</SelectItem>
                          <SelectItem value="member-004">赵六</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="memberName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>会员姓名</FormLabel>
                      <FormControl>
                        <Input placeholder="会员姓名" {...field} readOnly />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={addForm.control}
                  name="enrollDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>报名日期</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="paymentStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>支付状态</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择支付状态" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="pending">待支付</SelectItem>
                          <SelectItem value="paid">已支付</SelectItem>
                          <SelectItem value="refunded">已退款</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={addForm.control}
                  name="paymentAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>支付金额</FormLabel>
                      <FormControl>
                        <Input placeholder="输入支付金额" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={addForm.control}
                  name="membershipCardId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>使用会员卡</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择会员卡（可选）" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">不使用会员卡</SelectItem>
                          <SelectItem value="card-001">瑜伽年卡</SelectItem>
                          <SelectItem value="card-002">瑜伽季卡</SelectItem>
                          <SelectItem value="card-003">普拉提月卡</SelectItem>
                          <SelectItem value="card-004">私教次卡</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={addForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>备注</FormLabel>
                    <FormControl>
                      <Textarea placeholder="输入备注（选填）" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowAddDialog(false)}>
                  取消
                </Button>
                <Button type="submit">保存</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 编辑报名对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑报名</DialogTitle>
            <DialogDescription>
              编辑系列课程的学员报名信息。
            </DialogDescription>
          </DialogHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditEnrollment)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="memberId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>会员</FormLabel>
                      <Select onValueChange={(value) => {
                        field.onChange(value)
                        // 模拟根据会员ID获取会员姓名
                        const memberNames = {
                          "member-001": "张三",
                          "member-002": "李四",
                          "member-003": "王五",
                          "member-004": "赵六"
                        }
                        editForm.setValue("memberName", memberNames[value as keyof typeof memberNames] || "")
                      }} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择会员" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="member-001">张三</SelectItem>
                          <SelectItem value="member-002">李四</SelectItem>
                          <SelectItem value="member-003">王五</SelectItem>
                          <SelectItem value="member-004">赵六</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="memberName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>会员姓名</FormLabel>
                      <FormControl>
                        <Input placeholder="会员姓名" {...field} readOnly />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="enrollDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>报名日期</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="paymentStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>支付状态</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择支付状态" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="pending">待支付</SelectItem>
                          <SelectItem value="paid">已支付</SelectItem>
                          <SelectItem value="refunded">已退款</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="paymentAmount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>支付金额</FormLabel>
                      <FormControl>
                        <Input placeholder="输入支付金额" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="membershipCardId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>使用会员卡</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="选择会员卡（可选）" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">不使用会员卡</SelectItem>
                          <SelectItem value="card-001">瑜伽年卡</SelectItem>
                          <SelectItem value="card-002">瑜伽季卡</SelectItem>
                          <SelectItem value="card-003">普拉提月卡</SelectItem>
                          <SelectItem value="card-004">私教次卡</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>备注</FormLabel>
                    <FormControl>
                      <Textarea placeholder="输入备注（选填）" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowEditDialog(false)}>
                  取消
                </Button>
                <Button type="submit">保存</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* 删除报名确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              您确定要删除这个报名记录吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedEnrollment && (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">会员姓名:</span>
                  <span>{selectedEnrollment.memberName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">报名日期:</span>
                  <span>{selectedEnrollment.enrollDate}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">支付金额:</span>
                  <span>¥{selectedEnrollment.paymentAmount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">支付状态:</span>
                  <span>{getPaymentStatusBadge(selectedEnrollment.paymentStatus)}</span>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteEnrollment}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>