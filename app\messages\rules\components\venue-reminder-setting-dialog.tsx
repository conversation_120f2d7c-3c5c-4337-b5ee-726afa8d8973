import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Footer
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RecipientSelectionDialog } from './recipient-selection-dialog'

// 角色类型
interface Role {
  id: number
  name: string
}

// 员工类型
interface Employee {
  id: number
  name: string
  role: number // 角色ID
}

interface MessageRule {
  id: number
  name: string
  template: string
  smsEnabled: boolean
  appEnabled: boolean
  sendRule?: string
  // 规则设置
  reminderDays?: number // 提前提醒天数
  reminderRoles?: number[] // 接收提醒的角色ID列表
  reminderEmployees?: number[] // 接收提醒的员工ID列表
}

interface VenueReminderSettingDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  rule: MessageRule | null
  onSave: (rule: MessageRule) => void
}

// 示例角色数据
const roles: Role[] = [
  { id: 1, name: "场馆管理员" },
  { id: 2, name: "前台" },
  { id: 3, name: "教练" },
  { id: 4, name: "销售" },
  { id: 5, name: "财务" },
]

// 示例员工数据
const employees: Employee[] = [
  { id: 1, name: "张三", role: 1 },
  { id: 2, name: "李四", role: 2 },
  { id: 3, name: "王五", role: 2 },
  { id: 4, name: "赵六", role: 3 },
  { id: 5, name: "钱七", role: 3 },
  { id: 6, name: "孙八", role: 4 },
  { id: 7, name: "周九", role: 5 },
]

export function VenueReminderSettingDialog({
  open,
  onOpenChange,
  rule,
  onSave
}: VenueReminderSettingDialogProps) {
  // 规则设置状态
  const [reminderDays, setReminderDays] = useState<number>(rule?.reminderDays || 7)
  const [selectedRoles, setSelectedRoles] = useState<number[]>([])
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([])
  const [recipientDialogOpen, setRecipientDialogOpen] = useState(false)

  // 当对话框打开时，初始化设置
  React.useEffect(() => {
    if (open && rule) {
      // 根据规则名称设置默认天数
      let defaultDays = 7
      if (rule.name.includes("生日")) {
        defaultDays = 8
      } else if (rule.name.includes("未上课")) {
        defaultDays = 10
      } else if (rule.name.includes("连续")) {
        defaultDays = 10
      } else if (rule.name.includes("过期")) {
        defaultDays = 10
      } else if (rule.name.includes("次卡")) {
        defaultDays = 5
      } else if (rule.name.includes("开卡")) {
        defaultDays = 10
      } else if (rule.name.includes("请假")) {
        defaultDays = 2
      }

      setReminderDays(rule.reminderDays || defaultDays)

      // 默认全选所有角色
      let allRoleIds: number[] = [];
      if (rule.reminderRoles && rule.reminderRoles.length > 0) {
        // 如果有已保存的角色设置，则使用已保存的
        allRoleIds = rule.reminderRoles;
      } else {
        // 否则默认全选所有角色
        allRoleIds = roles.map(role => role.id);
      }
      setSelectedRoles(allRoleIds);

      // 根据选中的角色，自动选中对应的员工
      let employeeIds: number[] = [];

      // 如果有已保存的员工选择，则使用已保存的
      if (rule.reminderEmployees && rule.reminderEmployees.length > 0) {
        employeeIds = rule.reminderEmployees;
      } else {
        // 否则默认全选所有员工
        employeeIds = employees.map(emp => emp.id);
      }

      setSelectedEmployees(employeeIds);
    }
  }, [open, rule])

  // 处理角色选择变化
  const handleRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setSelectedRoles(prev => [...prev, roleId])

      // 当选中角色时，自动选中该角色下的所有员工
      const employeesOfRole = employees.filter(emp => emp.role === roleId).map(emp => emp.id)
      setSelectedEmployees(prev => [...prev, ...employeesOfRole.filter(id => !prev.includes(id))])
    } else {
      setSelectedRoles(prev => prev.filter(id => id !== roleId))

      // 当取消选中角色时，自动取消选中该角色下的所有员工
      const employeesOfRole = employees.filter(emp => emp.role === roleId).map(emp => emp.id)
      setSelectedEmployees(prev => prev.filter(id => !employeesOfRole.includes(id)))
    }
  }

  // 处理员工选择变化
  const handleEmployeeChange = (employeeId: number, checked: boolean) => {
    const employee = employees.find(emp => emp.id === employeeId)
    if (!employee) return

    if (checked) {
      setSelectedEmployees(prev => [...prev, employeeId])

      // 检查该角色下的所有员工是否都被选中，如果是，则自动选中该角色
      const roleId = employee.role
      const employeesOfRole = employees.filter(emp => emp.role === roleId)
      const allEmployeesSelected = employeesOfRole.every(emp =>
        selectedEmployees.includes(emp.id) || emp.id === employeeId
      )

      if (allEmployeesSelected && !selectedRoles.includes(roleId)) {
        setSelectedRoles(prev => [...prev, roleId])
      }
    } else {
      setSelectedEmployees(prev => prev.filter(id => id !== employeeId))

      // 当取消选中员工时，如果该角色被选中，则取消选中该角色
      const roleId = employee.role
      if (selectedRoles.includes(roleId)) {
        setSelectedRoles(prev => prev.filter(id => id !== roleId))
      }
    }
  }

  // 处理保存按钮点击
  const handleSave = () => {
    if (rule) {
      const updatedRule = {
        ...rule,
        reminderDays,
        reminderRoles: selectedRoles,
        reminderEmployees: selectedEmployees
      }

      onSave(updatedRule)
      onOpenChange(false)
    }
  }

  // 获取规则的标签文本
  const getRuleLabel = () => {
    if (!rule) return "提醒天数";

    if (rule.name.includes("生日")) {
      return "会员生日前";
    } else if (rule.name.includes("未上课") || rule.name.includes("连续")) {
      return "会员连续";
    } else if (rule.name.includes("过期")) {
      return "会员卡将在";
    } else if (rule.name.includes("次卡")) {
      return "次数少于";
    } else if (rule.name.includes("储值卡")) {
      return "余额少于";
    } else if (rule.name.includes("开卡")) {
      return "距离自动开卡前";
    } else if (rule.name.includes("请假")) {
      return "请假到期前";
    } else {
      return "提前";
    }
  }

  // 获取规则的单位文本
  const getRuleUnit = () => {
    if (!rule) return "天提醒";

    if (rule.name.includes("生日") ||
        rule.name.includes("过期") ||
        rule.name.includes("开卡") ||
        rule.name.includes("请假")) {
      return "天提醒";
    } else if (rule.name.includes("未上课") || rule.name.includes("连续")) {
      return "天未上课";
    } else if (rule.name.includes("次卡")) {
      return "次提醒";
    } else if (rule.name.includes("储值卡")) {
      return "元提醒";
    } else {
      return "天提醒";
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md p-6 rounded-md">
        <h2 className="text-lg font-medium mb-6 text-center border-b pb-4">
          {rule?.name || "场馆代办提醒"}设置
        </h2>

        <div className="space-y-4">
          <div className="flex items-center">
            <Label className="w-32 text-right mr-4 text-gray-700">{getRuleLabel()}</Label>
            <div className="flex items-center flex-1">
              <Input
                type="number"
                value={reminderDays}
                onChange={(e) => setReminderDays(parseInt(e.target.value) || 0)}
                className="w-20 text-center mx-2 h-8"
                min={1}
              />
              <Label>{getRuleUnit()}</Label>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center">
              <Label className="w-32 text-right mr-4 text-gray-700">接收人设置</Label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setRecipientDialogOpen(true)}
                className="text-sm"
              >
                设置接收人
              </Button>
            </div>

            {/* 显示已选择的角色 */}
            {selectedRoles.length > 0 && (
              <div className="flex items-start">
                <Label className="w-32 text-right mr-4 text-gray-700 invisible">已选角色</Label>
                <div className="flex flex-wrap gap-2">
                  {selectedRoles.map(roleId => {
                    const role = roles.find(r => r.id === roleId);
                    return role ? (
                      <div key={roleId} className="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded">
                        {role.name}
                      </div>
                    ) : null;
                  })}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="px-6"
          >
            取消
          </Button>
          <Button
            onClick={handleSave}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6"
          >
            确定
          </Button>
        </div>
      </DialogContent>

      {/* 接收人设置对话框 */}
      <RecipientSelectionDialog
        open={recipientDialogOpen}
        onOpenChange={setRecipientDialogOpen}
        title={`${rule?.name || "提醒"}接收人设置`}
        selectedRoles={selectedRoles}
        selectedEmployees={selectedEmployees}
        onSave={(roles, employees) => {
          setSelectedRoles(roles);
          setSelectedEmployees(employees);
        }}
        roles={roles}
        employees={employees}
      />
    </Dialog>
  )
}
