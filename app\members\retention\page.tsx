"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Search,
  Filter,
  Calendar,
  Clock,
  ChevronLeft,
  ChevronRight,
  Phone,
  Mail,
  MessageSquare,
  MoreHorizontal,
  CheckCircle,
  AlertTriangle,
  Bell,
  UserPlus,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  Percent,
  Tag,
  Gift,
  Bar<PERSON>hart
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Progress } from "@/components/ui/progress"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { DateRange } from "react-day-picker"
import { addDays, format, differenceInDays } from "date-fns"
import {
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from "recharts"

// 模拟会员流失风险数据
const retentionRiskMembers = [
  {
    id: "M001",
    name: "张三",
    avatar: "/avatars/01.png",
    phone: "13800138001",
    email: "<EMAIL>",
    cardType: "瑜伽年卡",
    cardNumber: "YG-2023-0001",
    expiryDate: "2023-12-15",
    daysLeft: 180,
    lastVisit: "2023-05-01",
    daysSinceLastVisit: 35,
    visitFrequency: "高",
    previousFrequency: "高",
    frequencyChange: "稳定",
    spendAmount: 3680,
    retentionScore: 45,
    riskLevel: "高",
    tags: ["高频用户", "VIP会员"],
    status: "未联系"
  },
  {
    id: "M002",
    name: "李四",
    avatar: "/avatars/02.png",
    phone: "13800138002",
    email: "<EMAIL>",
    cardType: "瑜伽季卡",
    cardNumber: "YG-2023-0056",
    expiryDate: "2023-09-10",
    daysLeft: 90,
    lastVisit: "2023-05-15",
    daysSinceLastVisit: 21,
    visitFrequency: "中",
    previousFrequency: "高",
    frequencyChange: "下降",
    spendAmount: 1580,
    retentionScore: 60,
    riskLevel: "中",
    tags: ["中频用户"],
    status: "已联系"
  },
  {
    id: "M003",
    name: "王五",
    avatar: "/avatars/03.png",
    phone: "13800138003",
    email: "<EMAIL>",
    cardType: "瑜伽月卡",
    cardNumber: "YG-2023-0128",
    expiryDate: "2023-07-08",
    daysLeft: 30,
    lastVisit: "2023-05-25",
    daysSinceLastVisit: 11,
    visitFrequency: "高",
    previousFrequency: "高",
    frequencyChange: "稳定",
    spendAmount: 680,
    retentionScore: 75,
    riskLevel: "低",
    tags: ["高频用户", "新会员"],
    status: "已联系"
  },
  {
    id: "M004",
    name: "赵六",
    avatar: "/avatars/04.png",
    phone: "13800138004",
    email: "<EMAIL>",
    cardType: "瑜伽年卡",
    cardNumber: "YG-2023-0078",
    expiryDate: "2023-11-20",
    daysLeft: 150,
    lastVisit: "2023-04-28",
    daysSinceLastVisit: 38,
    visitFrequency: "低",
    previousFrequency: "中",
    frequencyChange: "下降",
    spendAmount: 3680,
    retentionScore: 40,
    riskLevel: "高",
    tags: ["低频用户", "潜在流失"],
    status: "未联系"
  },
  {
    id: "M005",
    name: "孙七",
    avatar: "/avatars/05.png",
    phone: "13800138005",
    email: "<EMAIL>",
    cardType: "瑜伽季卡",
    cardNumber: "YG-2023-0145",
    expiryDate: "2023-08-12",
    daysLeft: 60,
    lastVisit: "2023-05-10",
    daysSinceLastVisit: 26,
    visitFrequency: "中",
    previousFrequency: "中",
    frequencyChange: "稳定",
    spendAmount: 1580,
    retentionScore: 65,
    riskLevel: "中",
    tags: ["中频用户"],
    status: "已联系"
  }
];

// 模拟流失原因数据
const churnReasonData = [
  { name: "价格因素", value: 35 },
  { name: "服务质量", value: 20 },
  { name: "课程安排", value: 15 },
  { name: "场地设施", value: 10 },
  { name: "个人原因", value: 20 }
];

// 模拟流失趋势数据
const churnTrendData = [
  { month: "1月", 流失人数: 12, 新增人数: 25 },
  { month: "2月", 流失人数: 15, 新增人数: 30 },
  { month: "3月", 流失人数: 10, 新增人数: 28 },
  { month: "4月", 流失人数: 8, 新增人数: 32 },
  { month: "5月", 流失人数: 14, 新增人数: 35 },
  { month: "6月", 流失人数: 18, 新增人数: 30 }
];

// 模拟会员生命周期数据
const memberLifecycleData = [
  { name: "0-3个月", value: 25 },
  { name: "3-6个月", value: 30 },
  { name: "6-12个月", value: 20 },
  { name: "1-2年", value: 15 },
  { name: "2年以上", value: 10 }
];

// 模拟挽留策略数据
const retentionStrategies = [
  {
    id: "S001",
    name: "会员关怀电话",
    description: "针对30天未到访的会员进行电话回访，了解原因并提供帮助。",
    targetRisk: "中",
    successRate: 65,
    effort: "高",
    type: "人工"
  },
  {
    id: "S002",
    name: "专属优惠券",
    description: "向高风险会员发送专属优惠券，鼓励重新到访。",
    targetRisk: "高",
    successRate: 45,
    effort: "中",
    type: "自动"
  },
  {
    id: "S003",
    name: "会员生日礼",
    description: "在会员生日当月赠送生日礼物和免费体验课。",
    targetRisk: "低",
    successRate: 80,
    effort: "中",
    type: "自动"
  },
  {
    id: "S004",
    name: "会员回归活动",
    description: "针对长期未到访会员的专属回归活动。",
    targetRisk: "高",
    successRate: 55,
    effort: "高",
    type: "活动"
  },
  {
    id: "S005",
    name: "课程推荐",
    description: "基于会员历史偏好推荐新课程。",
    targetRisk: "中",
    successRate: 70,
    effort: "低",
    type: "自动"
  }
];

export default function MemberRetentionPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("at-risk")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedMembers, setSelectedMembers] = useState<string[]>([])
  const [riskLevelFilter, setRiskLevelFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedStrategy, setSelectedStrategy] = useState<string | null>(null)

  // 获取风险等级标签样式
  const getRiskLevelBadge = (level: string) => {
    switch(level) {
      case "高":
        return <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200">高风险</Badge>
      case "中":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">中风险</Badge>
      case "低":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">低风险</Badge>
      default:
        return <Badge variant="outline">{level}风险</Badge>
    }
  }

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "未联系":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">未联系</Badge>
      case "已联系":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">已联系</Badge>
      case "已挽回":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已挽回</Badge>
      case "已流失":
        return <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200">已流失</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取留存分数样式
  const getRetentionScoreStyle = (score: number) => {
    if (score >= 70) {
      return "text-green-600"
    } else if (score >= 50) {
      return "text-amber-600"
    } else {
      return "text-red-600"
    }
  }

  // 获取频率变化样式
  const getFrequencyChangeStyle = (change: string) => {
    switch(change) {
      case "上升":
        return <span className="text-green-600 flex items-center"><ArrowUpRight className="h-3 w-3 mr-1" />上升</span>
      case "下降":
        return <span className="text-red-600 flex items-center"><ArrowDownRight className="h-3 w-3 mr-1" />下降</span>
      default:
        return <span className="text-gray-600">稳定</span>
    }
  }

  // 过滤会员
  const filteredMembers = retentionRiskMembers.filter(member => {
    // 基本搜索过滤
    const searchFilter =
      searchQuery === "" ||
      member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.phone.includes(searchQuery) ||
      member.cardNumber.toLowerCase().includes(searchQuery.toLowerCase());

    // 风险等级过滤
    const riskFilter =
      riskLevelFilter === "all" ||
      member.riskLevel === riskLevelFilter;

    // 状态过滤
    const statusFilterResult =
      statusFilter === "all" ||
      member.status === statusFilter;

    return searchFilter && riskFilter && statusFilterResult;
  });

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedMembers(filteredMembers.map(member => member.id));
    } else {
      setSelectedMembers([]);
    }
  };

  // 处理批量操作
  const handleBatchOperation = (operation: string) => {
    console.log(`对会员 ${selectedMembers.join(", ")} 执行操作: ${operation}`);
    // 实际应用中，这里会执行相应的批量操作
    alert(`已对 ${selectedMembers.length} 个会员执行 ${operation} 操作`);
    setSelectedMembers([]);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">会员流失风险管理</h1>
          <p className="text-muted-foreground">
            识别并管理有流失风险的会员，提高会员留存率
          </p>
        </div>
        <div className="flex items-center gap-2">
          {selectedMembers.length > 0 ? (
            <>
              <Select value={selectedStrategy || ""} onValueChange={setSelectedStrategy}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="选择挽留策略" />
                </SelectTrigger>
                <SelectContent>
                  {retentionStrategies.map(strategy => (
                    <SelectItem key={strategy.id} value={strategy.id}>{strategy.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                onClick={() => handleBatchOperation("应用挽留策略")}
                disabled={!selectedStrategy}
              >
                <Bell className="h-4 w-4 mr-2" />
                应用策略 ({selectedMembers.length})
              </Button>
              <Button variant="outline" onClick={() => handleBatchOperation("标记为已联系")}>
                <CheckCircle className="h-4 w-4 mr-2" />
                标记为已联系
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新数据
              </Button>
              <Button>
                <Gift className="h-4 w-4 mr-2" />
                创建挽留活动
              </Button>
            </>
          )}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>会员流失风险概览</CardTitle>
          <CardDescription>
            当前共有 {retentionRiskMembers.filter(m => m.riskLevel === "高").length} 个高风险会员，{retentionRiskMembers.filter(m => m.riskLevel === "中").length} 个中风险会员
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">高风险会员</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{retentionRiskMembers.filter(m => m.riskLevel === "高").length}</div>
                <p className="text-xs text-muted-foreground">
                  超过30天未到访的会员数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">中风险会员</CardTitle>
                <AlertTriangle className="h-4 w-4 text-amber-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{retentionRiskMembers.filter(m => m.riskLevel === "中").length}</div>
                <p className="text-xs text-muted-foreground">
                  到访频率下降的会员数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已联系</CardTitle>
                <MessageSquare className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{retentionRiskMembers.filter(m => m.status === "已联系").length}</div>
                <p className="text-xs text-muted-foreground">
                  已联系但未确认挽回的会员数量
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">挽回率</CardTitle>
                <Percent className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">65%</div>
                <p className="text-xs text-muted-foreground">
                  本月成功挽回的会员比例
                </p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3 max-w-md">
          <TabsTrigger value="at-risk">风险会员</TabsTrigger>
          <TabsTrigger value="strategies">挽留策略</TabsTrigger>
          <TabsTrigger value="analytics">流失分析</TabsTrigger>
        </TabsList>

        <TabsContent value="at-risk" className="space-y-4 mt-4">
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="relative w-full md:w-1/3">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索会员姓名、手机号或卡号"
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex flex-1 gap-2">
              <Select value={riskLevelFilter} onValueChange={setRiskLevelFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="风险等级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部风险等级</SelectItem>
                  <SelectItem value="高">高风险</SelectItem>
                  <SelectItem value="中">中风险</SelectItem>
                  <SelectItem value="低">低风险</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="联系状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="未联系">未联系</SelectItem>
                  <SelectItem value="已联系">已联系</SelectItem>
                  <SelectItem value="已挽回">已挽回</SelectItem>
                  <SelectItem value="已流失">已流失</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[40px]">
                      <Checkbox
                        checked={selectedMembers.length > 0 && selectedMembers.length === filteredMembers.length}
                        onCheckedChange={handleSelectAll}
                        aria-label="全选"
                      />
                    </TableHead>
                    <TableHead>会员信息</TableHead>
                    <TableHead>最近到访</TableHead>
                    <TableHead>到访频率</TableHead>
                    <TableHead>留存分数</TableHead>
                    <TableHead>风险等级</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMembers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="h-24 text-center">
                        没有找到符合条件的会员
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredMembers.map((member) => (
                      <TableRow key={member.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedMembers.includes(member.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedMembers([...selectedMembers, member.id]);
                              } else {
                                setSelectedMembers(selectedMembers.filter(id => id !== member.id));
                              }
                            }}
                            aria-label={`选择会员 ${member.name}`}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-9 w-9">
                              <AvatarImage src={member.avatar} alt={member.name} />
                              <AvatarFallback>{member.name.slice(0, 1)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{member.name}</div>
                              <div className="text-xs text-muted-foreground flex items-center gap-2">
                                <span className="flex items-center"><Phone className="h-3 w-3 mr-1" />{member.phone}</span>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{member.lastVisit}</div>
                          <div className="text-xs text-muted-foreground">
                            {member.daysSinceLastVisit} 天前
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{member.visitFrequency}频率</div>
                          <div className="text-xs">
                            {getFrequencyChangeStyle(member.frequencyChange)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className={`font-medium ${getRetentionScoreStyle(member.retentionScore)}`}>
                            {member.retentionScore}分
                          </div>
                          <Progress
                            value={member.retentionScore}
                            className="h-2 w-24"
                            indicatorClassName={
                              member.retentionScore >= 70 ? "bg-green-500" :
                              member.retentionScore >= 50 ? "bg-amber-500" :
                              "bg-red-500"
                            }
                          />
                        </TableCell>
                        <TableCell>
                          {getRiskLevelBadge(member.riskLevel)}
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(member.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>会员操作</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => router.push(`/members/${member.id}`)}>
                                查看详情
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                发送关怀消息
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                标记为已联系
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                发送专属优惠
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="strategies" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>会员挽留策略</CardTitle>
              <CardDescription>
                针对不同风险等级会员的挽留策略和成功率
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>策略名称</TableHead>
                    <TableHead>描述</TableHead>
                    <TableHead>目标风险等级</TableHead>
                    <TableHead>成功率</TableHead>
                    <TableHead>工作量</TableHead>
                    <TableHead>类型</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {retentionStrategies.map((strategy) => (
                    <TableRow key={strategy.id}>
                      <TableCell className="font-medium">{strategy.name}</TableCell>
                      <TableCell>{strategy.description}</TableCell>
                      <TableCell>
                        {strategy.targetRisk === "高" ? (
                          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">高风险</Badge>
                        ) : strategy.targetRisk === "中" ? (
                          <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">中风险</Badge>
                        ) : (
                          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">低风险</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className={`font-medium ${
                          strategy.successRate >= 70 ? "text-green-600" :
                          strategy.successRate >= 50 ? "text-amber-600" :
                          "text-red-600"
                        }`}>
                          {strategy.successRate}%
                        </div>
                        <Progress
                          value={strategy.successRate}
                          className="h-2 w-24"
                          indicatorClassName={
                            strategy.successRate >= 70 ? "bg-green-500" :
                            strategy.successRate >= 50 ? "bg-amber-500" :
                            "bg-red-500"
                          }
                        />
                      </TableCell>
                      <TableCell>
                        {strategy.effort === "高" ? (
                          <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200">高</Badge>
                        ) : strategy.effort === "中" ? (
                          <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">中</Badge>
                        ) : (
                          <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">低</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{strategy.type}</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm">
                          应用策略
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4 mt-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* 流失原因分析 */}
            <Card>
              <CardHeader>
                <CardTitle>会员流失原因分析</CardTitle>
                <CardDescription>
                  会员流失的主要原因分布
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={churnReasonData}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {churnReasonData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={
                            index === 0 ? "#ff6b6b" :
                            index === 1 ? "#ffa06b" :
                            index === 2 ? "#ffd06b" :
                            index === 3 ? "#6bff9e" :
                            "#6b9eff"
                          } />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* 会员生命周期分析 */}
            <Card>
              <CardHeader>
                <CardTitle>会员流失生命周期分析</CardTitle>
                <CardDescription>
                  不同会员生命周期阶段的流失分布
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={memberLifecycleData}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {memberLifecycleData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={
                            index === 0 ? "#845EC2" :
                            index === 1 ? "#D65DB1" :
                            index === 2 ? "#FF6F91" :
                            index === 3 ? "#FF9671" :
                            "#FFC75F"
                          } />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* 流失趋势分析 */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>会员流失趋势分析</CardTitle>
                <CardDescription>
                  近6个月会员流失与新增趋势对比
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={churnTrendData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="流失人数" fill="#ff6b6b" />
                      <Bar dataKey="新增人数" fill="#6bff9e" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
