"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, ShoppingCart } from "lucide-react"

interface InventoryAlertDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function InventoryAlertDialog({ open, onOpenChange }: InventoryAlertDialogProps) {
  const lowStockItems = [
    {
      id: "E003",
      name: "瑜伽带",
      venue: "所有场地",
      currentStock: 5,
      minStock: 10,
      status: "low",
    },
    {
      id: "E004",
      name: "瑜伽球",
      venue: "2号瑜伽室",
      currentStock: 2,
      minStock: 5,
      status: "critical",
    },
    {
      id: "E006",
      name: "空中瑜伽吊床",
      venue: "4号瑜伽室",
      currentStock: 1,
      minStock: 3,
      status: "critical",
    },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5 text-amber-500" />
            <span>库存预警</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
            <p className="text-amber-800 font-medium">以下设备库存低于预警阈值，请及时补充</p>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>设备编号</TableHead>
                <TableHead>设备名称</TableHead>
                <TableHead>所属场地</TableHead>
                <TableHead>当前库存</TableHead>
                <TableHead>最低库存</TableHead>
                <TableHead>状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {lowStockItems.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.id}</TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>{item.venue}</TableCell>
                  <TableCell>{item.currentStock}</TableCell>
                  <TableCell>{item.minStock}</TableCell>
                  <TableCell>
                    <Badge variant={item.status === "low" ? "outline" : "destructive"}>
                      {item.status === "low" ? "库存不足" : "库存紧急"}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="outline" size="sm">
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      采购
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          <div className="space-y-2">
            <h3 className="text-sm font-medium">建议采购清单</h3>
            <div className="space-y-2">
              <div className="p-3 border rounded-md flex justify-between items-center">
                <div>
                  <p className="font-medium">瑜伽带</p>
                  <p className="text-sm text-muted-foreground">建议采购数量: 10个</p>
                </div>
                <p className="font-medium">预计费用: ¥600</p>
              </div>
              <div className="p-3 border rounded-md flex justify-between items-center">
                <div>
                  <p className="font-medium">瑜伽球</p>
                  <p className="text-sm text-muted-foreground">建议采购数量: 5个</p>
                </div>
                <p className="font-medium">预计费用: ¥600</p>
              </div>
              <div className="p-3 border rounded-md flex justify-between items-center">
                <div>
                  <p className="font-medium">空中瑜伽吊床</p>
                  <p className="text-sm text-muted-foreground">建议采购数量: 3个</p>
                </div>
                <p className="font-medium">预计费用: ¥1,050</p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
          <Button onClick={() => onOpenChange(false)}>生成采购单</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

