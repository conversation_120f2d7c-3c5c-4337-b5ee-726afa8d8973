"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function PremiumShareholderTypesPage() {
  const router = useRouter()

  useEffect(() => {
    // 重定向到原始的股东类型页面
    router.push("/shareholders/types")
  }, [router])

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-semibold mb-4">正在跳转到股东类型页面...</h1>
        <p>如果页面没有自动跳转，请<a href="/shareholders/types" className="text-blue-500 hover:underline">点击这里</a></p>
      </div>
    </div>
  )
}
