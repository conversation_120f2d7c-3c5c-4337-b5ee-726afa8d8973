"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bs<PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle, ArrowRight, Clock, FileText, MessageSquare, Phone, Printer, Send, User } from "lucide-react"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface OrderDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  orderId: string
}

export function OrderDetailDialog({ open, onOpenChange, orderId }: OrderDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("details")
  const [showRefundForm, setShowRefundForm] = useState(false)
  const [showCancelForm, setShowCancelForm] = useState(false)
  const [showModifyForm, setShowModifyForm] = useState(false)

  // 模拟订单数据
  const order = {
    id: orderId,
    status: "paid",
    type: "membership",
    product: "年卡",
    amount: "¥3,600.00",
    originalAmount: "¥4,800.00",
    discount: "¥1,200.00",
    date: "2025-03-25 14:30:22",
    paymentMethod: "微信支付",
    transactionId: "4200001234202503281234567890",
    member: {
      id: "MEM001",
      name: "张三",
      phone: "138****1234",
      avatar: "/placeholder.svg?height=40&width=40",
      level: "黄金会员",
      joinDate: "2024-01-15",
    },
    items: [
      {
        name: "瑜伽年卡会员",
        description: "无限次瑜伽课程，有效期365天",
        price: "¥3,600.00",
        originalPrice: "¥4,800.00",
        quantity: 1,
      },
    ],
    coupon: {
      code: "YOGA25",
      name: "新春优惠券",
      discount: "¥1,200.00",
    },
    notes: "客户要求在会员卡上印制姓名",
    venue: "朝阳店",
    staff: "李四",
    timeline: [
      { time: "2025-03-25 14:28:10", action: "创建订单", operator: "系统" },
      { time: "2025-03-25 14:29:15", action: "应用优惠券 YOGA25", operator: "李四" },
      { time: "2025-03-25 14:30:22", action: "支付完成", operator: "张三" },
      { time: "2025-03-25 14:31:05", action: "发送订单确认短信", operator: "系统" },
    ],
  }

  const handlePrintOrder = () => {
    alert("打印订单: " + orderId)
  }

  const handleDownloadInvoice = () => {
    alert("下载发票: " + orderId)
  }

  const handleSendReceipt = () => {
    alert("发送电子收据: " + orderId)
  }

  const handleRefundSubmit = () => {
    alert("提交退款申请: " + orderId)
    setShowRefundForm(false)
  }

  const handleCancelSubmit = () => {
    alert("提交取消订单申请: " + orderId)
    setShowCancelForm(false)
  }

  const handleModifySubmit = () => {
    alert("提交修改订单申请: " + orderId)
    setShowModifyForm(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl">订单详情</DialogTitle>
              <DialogDescription>订单编号: {order.id}</DialogDescription>
            </div>
            <Badge
              variant={
                order.status === "paid"
                  ? "default"
                  : order.status === "pending"
                    ? "outline"
                    : order.status === "refunded"
                      ? "secondary"
                      : "destructive"
              }
              className="ml-2"
            >
              {order.status === "paid"
                ? "已支付"
                : order.status === "pending"
                  ? "待支付"
                  : order.status === "refunded"
                    ? "已退款"
                    : "已取消"}
            </Badge>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="details">订单详情</TabsTrigger>
            <TabsTrigger value="payment">支付信息</TabsTrigger>
            <TabsTrigger value="member">会员信息</TabsTrigger>
            <TabsTrigger value="history">操作记录</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">商品信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-start">
                      <div className="space-y-1">
                        <h4 className="font-medium">{item.name}</h4>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{item.price}</div>
                        {item.originalPrice !== item.price && (
                          <div className="text-sm text-muted-foreground line-through">{item.originalPrice}</div>
                        )}
                        <div className="text-sm">x{item.quantity}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">订单摘要</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单类型</span>
                    <span>
                      {order.type === "membership"
                        ? "会员卡"
                        : order.type === "course"
                          ? "单次课程"
                          : order.type === "package"
                            ? "课程套餐"
                            : "商品"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">下单时间</span>
                    <span>{order.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">门店</span>
                    <span>{order.venue}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">操作员</span>
                    <span>{order.staff}</span>
                  </div>
                  {order.notes && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">备注</span>
                      <span>{order.notes}</span>
                    </div>
                  )}
                </div>

                <Separator className="my-4" />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">商品原价</span>
                    <span>{order.originalAmount}</span>
                  </div>
                  {order.coupon && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">优惠券 ({order.coupon.code})</span>
                      <span className="text-red-500">-{order.coupon.discount}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-medium">
                    <span>实付金额</span>
                    <span>{order.amount}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {!showRefundForm && !showCancelForm && !showModifyForm && (
              <div className="flex flex-wrap gap-2 justify-end">
                <Button variant="outline" onClick={handlePrintOrder}>
                  <Printer className="mr-2 h-4 w-4" />
                  打印订单
                </Button>
                <Button variant="outline" onClick={handleDownloadInvoice}>
                  <FileText className="mr-2 h-4 w-4" />
                  下载发票
                </Button>
                <Button variant="outline" onClick={handleSendReceipt}>
                  <Send className="mr-2 h-4 w-4" />
                  发送电子收据
                </Button>
                {order.status === "paid" && (
                  <>
                    <Button variant="outline" onClick={() => setShowModifyForm(true)}>
                      修改订单
                    </Button>
                    <Button variant="outline" onClick={() => setShowRefundForm(true)}>
                      申请退款
                    </Button>
                    <Button variant="outline" className="text-red-500" onClick={() => setShowCancelForm(true)}>
                      取消订单
                    </Button>
                  </>
                )}
              </div>
            )}

            {showRefundForm && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">申请退款</CardTitle>
                  <CardDescription>请填写退款申请信息</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="refund-amount">退款金额</Label>
                      <Input id="refund-amount" defaultValue={order.amount.replace("¥", "")} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="refund-reason">退款原因</Label>
                      <Select defaultValue="personal">
                        <SelectTrigger id="refund-reason">
                          <SelectValue placeholder="选择退款原因" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="personal">个人原因</SelectItem>
                          <SelectItem value="schedule">时间冲突</SelectItem>
                          <SelectItem value="health">健康原因</SelectItem>
                          <SelectItem value="quality">服务质量问题</SelectItem>
                          <SelectItem value="other">其他原因</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="refund-notes">备注说明</Label>
                      <Textarea id="refund-notes" placeholder="请输入详细说明" />
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>退款说明</AlertTitle>
                      <AlertDescription>
                        根据退款政策，会员卡退款将收取5%的手续费。退款将在3-5个工作日内退回原支付账户。
                      </AlertDescription>
                    </Alert>

                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setShowRefundForm(false)}>
                        取消
                      </Button>
                      <Button onClick={handleRefundSubmit}>提交退款申请</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {showCancelForm && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">取消订单</CardTitle>
                  <CardDescription>请填写取消订单信息</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="cancel-reason">取消原因</Label>
                      <Select defaultValue="personal">
                        <SelectTrigger id="cancel-reason">
                          <SelectValue placeholder="选择取消原因" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="personal">个人原因</SelectItem>
                          <SelectItem value="schedule">时间冲突</SelectItem>
                          <SelectItem value="health">健康原因</SelectItem>
                          <SelectItem value="mistake">订单错误</SelectItem>
                          <SelectItem value="other">其他原因</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cancel-notes">备注说明</Label>
                      <Textarea id="cancel-notes" placeholder="请输入详细说明" />
                    </div>

                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>取消提醒</AlertTitle>
                      <AlertDescription>订单取消后将无法恢复，如需退款请选择"申请退款"选项。</AlertDescription>
                    </Alert>

                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setShowCancelForm(false)}>
                        返回
                      </Button>
                      <Button variant="destructive" onClick={handleCancelSubmit}>
                        确认取消订单
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {showModifyForm && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">修改订单</CardTitle>
                  <CardDescription>请填写修改信息</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="modify-notes">修改备注</Label>
                      <Input id="modify-notes" defaultValue={order.notes} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="modify-reason">修改原因</Label>
                      <Select defaultValue="info">
                        <SelectTrigger id="modify-reason">
                          <SelectValue placeholder="选择修改原因" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="info">信息错误</SelectItem>
                          <SelectItem value="request">客户要求</SelectItem>
                          <SelectItem value="other">其他原因</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="modify-description">详细说明</Label>
                      <Textarea id="modify-description" placeholder="请输入详细说明" />
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>修改说明</AlertTitle>
                      <AlertDescription>
                        订单修改仅限于备注信息，如需修改商品或金额，请取消订单后重新下单。
                      </AlertDescription>
                    </Alert>

                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setShowModifyForm(false)}>
                        取消
                      </Button>
                      <Button onClick={handleModifySubmit}>提交修改</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="payment" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">支付信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">支付状态</span>
                    <Badge
                      variant={
                        order.status === "paid"
                          ? "default"
                          : order.status === "pending"
                            ? "outline"
                            : order.status === "refunded"
                              ? "secondary"
                              : "destructive"
                      }
                    >
                      {order.status === "paid"
                        ? "已支付"
                        : order.status === "pending"
                          ? "待支付"
                          : order.status === "refunded"
                            ? "已退款"
                            : "已取消"}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">支付方式</span>
                    <span>{order.paymentMethod}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">交易单号</span>
                    <span>{order.transactionId}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">支付时间</span>
                    <span>{order.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">商品原价</span>
                    <span>{order.originalAmount}</span>
                  </div>
                  {order.coupon && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">优惠金额</span>
                      <span className="text-red-500">-{order.coupon.discount}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-medium">
                    <span>实付金额</span>
                    <span>{order.amount}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">发票信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>发票状态</span>
                    <Badge variant="outline">未开票</Badge>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="invoice-title">发票抬头</Label>
                    <Input id="invoice-title" placeholder="请输入发票抬头" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tax-number">税号</Label>
                    <Input id="tax-number" placeholder="请输入税号" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="invoice-email">接收邮箱</Label>
                    <Input id="invoice-email" type="email" placeholder="请输入接收发票的邮箱" />
                  </div>

                  <div className="flex justify-end">
                    <Button>申请开票</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="member" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">会员信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={order.member.avatar} alt={order.member.name} />
                    <AvatarFallback>{order.member.name[0]}</AvatarFallback>
                  </Avatar>
                  <div className="space-y-1">
                    <h3 className="font-medium text-lg">{order.member.name}</h3>
                    <div className="flex items-center text-muted-foreground">
                      <User className="mr-1 h-4 w-4" />
                      <span>{order.member.id}</span>
                    </div>
                    <div className="flex items-center text-muted-foreground">
                      <Phone className="mr-1 h-4 w-4" />
                      <span>{order.member.phone}</span>
                    </div>
                    <div className="flex items-center">
                      <Badge variant="secondary">{order.member.level}</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">会员订单历史</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2">
                        ORD001
                      </Badge>
                      <span>年卡</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">¥3,600.00</span>
                      <Badge>已支付</Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2">
                        ORD002
                      </Badge>
                      <span>瑜伽垫</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">¥280.00</span>
                      <Badge>已支付</Badge>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Badge variant="outline" className="mr-2">
                        ORD003
                      </Badge>
                      <span>私教课 x5</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground">¥1,500.00</span>
                      <Badge variant="secondary">已退款</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-end gap-2">
              <Button variant="outline">
                <MessageSquare className="mr-2 h-4 w-4" />
                联系会员
              </Button>
              <Button variant="outline">
                <ArrowRight className="mr-2 h-4 w-4" />
                查看会员详情
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">操作记录</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative pl-6 border-l">
                  {order.timeline.map((event, index) => (
                    <div key={index} className="mb-4 relative">
                      <div className="absolute -left-[25px] w-4 h-4 rounded-full bg-primary"></div>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="mr-1 h-3 w-3" />
                          <span>{event.time}</span>
                        </div>
                        <p className="font-medium">{event.action}</p>
                        <p className="text-sm text-muted-foreground">操作人: {event.operator}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

