"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  LayoutDashboard, Store, BookOpen, Users, UserCog, DollarSign, 
  Megaphone, BarChart, Settings, ChevronRight, Menu as MenuIcon
} from "lucide-react"

interface MenuItem {
  id: string;
  name: string;
  href?: string;
  icon?: string;
  children?: MenuItem[];
}

export default function MenuPage() {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchMenuData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/menu');
        const data = await response.json();
        setMenuItems(data);
      } catch (error) {
        console.error('获取菜单数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMenuData();
  }, []);

  // 根据图标名称返回图标组件
  const getIconComponent = (iconName?: string) => {
    switch (iconName) {
      case 'LayoutDashboard':
        return <LayoutDashboard className="h-5 w-5" />;
      case 'Store':
        return <Store className="h-5 w-5" />;
      case 'BookOpen':
        return <BookOpen className="h-5 w-5" />;
      case 'Users':
        return <Users className="h-5 w-5" />;
      case 'UserCog':
        return <UserCog className="h-5 w-5" />;
      case 'DollarSign':
        return <DollarSign className="h-5 w-5" />;
      case 'Megaphone':
        return <Megaphone className="h-5 w-5" />;
      case 'BarChart':
        return <BarChart className="h-5 w-5" />;
      case 'Settings':
        return <Settings className="h-5 w-5" />;
      default:
        return <MenuIcon className="h-5 w-5" />;
    }
  };

  // 导航到指定路径
  const navigateTo = (href: string | undefined) => {
    if (href) {
      router.push(href);
    } else {
      router.push('/');
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <h1 className="text-2xl font-bold">系统菜单</h1>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-2xl font-bold mb-4">系统菜单</h1>
      
      <Card className="shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle>所有功能模块</CardTitle>
          <CardDescription>
            以下是系统所有可用的功能模块，点击按钮可直接访问对应页面
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {menuItems.map((item) => (
              <Card key={item.id} className="overflow-hidden hover:shadow-md transition-shadow border">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <div className="rounded-md bg-primary/10 p-2">
                      {getIconComponent(item.icon)}
                    </div>
                    <CardTitle className="text-lg">{item.name}</CardTitle>
                  </div>
                  <CardDescription>
                    {item.children ? `${item.children.length} 个子菜单` : ''}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pb-4">
                  {item.href && !item.children && (
                    <Button 
                      variant="outline" 
                      className="w-full justify-between"
                      onClick={() => navigateTo(item.href)}
                    >
                      <span>访问{item.name}</span>
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {item.children && (
                    <ScrollArea className="h-40">
                      <div className="space-y-2">
                        {item.children.map((child) => (
                          <Button 
                            key={child.id}
                            variant="outline" 
                            className="w-full justify-between"
                            onClick={() => navigateTo(child.href)}
                          >
                            <span>{child.name}</span>
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 