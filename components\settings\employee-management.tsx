"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  UserPlus,
  FileText,
  Download,
  Upload,
  Search,
  RefreshCw,
  Filter,
  Calendar,
  Briefcase,
  Building,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuPortal,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { Textarea } from "@/components/ui/textarea"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"

// 员工数据
const employees = [
  {
    id: 1,
    name: "张静",
    avatar: "/placeholder.svg?height=32&width=32",
    email: "<EMAIL>",
    role: "瑜伽教练",
    status: "active",
    lastLogin: "2025-03-28 09:15",
    loginCount: 128,
    createdAt: "2023-01-15",
    department: "教学部",
    phone: "***********",
    position: "高级教练",
    employeeId: "YG-2023-001",
    entryDate: "2023-01-15",
    gender: "女",
    birthday: "1990-05-15",
    address: "北京市朝阳区建国路88号",
    education: "本科",
    specialty: ["哈他瑜伽", "阴瑜伽", "理疗瑜伽"],
    certificates: ["国际瑜伽联盟RYT-200", "理疗瑜伽认证"],
    salary: {
      basic: 8000,
      performance: "课时费+提成",
      bankAccount: "6222021234567890123",
      bankName: "中国工商银行",
    },
    emergencyContact: {
      name: "张父",
      relation: "父亲",
      phone: "***********",
    },
    attendance: {
      totalDays: 120,
      leaveDays: 3,
      lateDays: 1,
    },
    performance: {
      rating: 4.8,
      studentFeedback: 4.9,
      classCount: 320,
    },
  },
  {
    id: 2,
    name: "李明",
    avatar: "/placeholder.svg?height=32&width=32",
    email: "<EMAIL>",
    role: "前台接待",
    status: "active",
    lastLogin: "2025-03-27 14:30",
    loginCount: 86,
    createdAt: "2023-02-20",
    department: "运营部",
    phone: "***********",
    position: "前台主管",
    employeeId: "QT-2023-001",
    entryDate: "2023-02-20",
    gender: "男",
    birthday: "1992-08-20",
    address: "北京市海淀区中关村大街15号",
    education: "大专",
    specialty: ["客户服务", "会员管理"],
    certificates: ["服务礼仪培训证书"],
    salary: {
      basic: 6000,
      performance: "固定底薪+绩效",
      bankAccount: "6222021234567890124",
      bankName: "中国建设银行",
    },
    emergencyContact: {
      name: "李父",
      relation: "父亲",
      phone: "***********",
    },
    attendance: {
      totalDays: 110,
      leaveDays: 2,
      lateDays: 0,
    },
    performance: {
      rating: 4.5,
      customerFeedback: 4.7,
      salesPerformance: "良好",
    },
  },
  {
    id: 3,
    name: "王芳",
    avatar: "/placeholder.svg?height=32&width=32",
    email: "<EMAIL>",
    role: "普拉提教练",
    status: "active",
    lastLogin: "2025-03-28 08:45",
    loginCount: 64,
    createdAt: "2023-03-10",
    department: "教学部",
    phone: "***********",
    position: "资深教练",
    employeeId: "PLT-2023-001",
    entryDate: "2023-03-10",
    gender: "女",
    birthday: "1988-11-12",
    address: "北京市朝阳区东三环中路39号",
    education: "研究生",
    specialty: ["垫上普拉提", "器械普拉提", "产后修复"],
    certificates: ["BASI普拉提认证", "产后修复师认证"],
    salary: {
      basic: 9000,
      performance: "课时费+提成",
      bankAccount: "6222021234567890125",
      bankName: "中国招商银行",
    },
    emergencyContact: {
      name: "王母",
      relation: "母亲",
      phone: "***********",
    },
    attendance: {
      totalDays: 105,
      leaveDays: 4,
      lateDays: 2,
    },
    performance: {
      rating: 4.9,
      studentFeedback: 4.8,
      classCount: 280,
    },
  },
  {
    id: 4,
    name: "赵健",
    avatar: "/placeholder.svg?height=32&width=32",
    email: "<EMAIL>",
    role: "会籍顾问",
    status: "inactive",
    lastLogin: "2025-03-25 16:20",
    loginCount: 42,
    createdAt: "2023-04-05",
    department: "销售部",
    phone: "***********",
    position: "销售顾问",
    employeeId: "XS-2023-001",
    entryDate: "2023-04-05",
    gender: "男",
    birthday: "1995-03-25",
    address: "北京市西城区西单北大街120号",
    education: "本科",
    specialty: ["销售", "客户关系管理"],
    certificates: ["销售技巧培训证书"],
    salary: {
      basic: 5000,
      performance: "底薪+高提成",
      bankAccount: "6222021234567890126",
      bankName: "中国农业银行",
    },
    emergencyContact: {
      name: "赵母",
      relation: "母亲",
      phone: "***********",
    },
    attendance: {
      totalDays: 90,
      leaveDays: 5,
      lateDays: 3,
    },
    performance: {
      rating: 4.0,
      salesAmount: 150000,
      newMembers: 25,
    },
  },
  {
    id: 5,
    name: "钱蓉",
    avatar: "/placeholder.svg?height=32&width=32",
    email: "<EMAIL>",
    role: "财务",
    status: "active",
    lastLogin: "2025-03-28 10:05",
    loginCount: 56,
    createdAt: "2023-05-12",
    department: "财务部",
    phone: "***********",
    position: "财务主管",
    employeeId: "CW-2023-001",
    entryDate: "2023-05-12",
    gender: "女",
    birthday: "1985-07-08",
    address: "北京市东城区东直门外大街42号",
    education: "本科",
    specialty: ["财务管理", "成本控制"],
    certificates: ["会计师证书", "财务管理师"],
    salary: {
      basic: 8500,
      performance: "固定底薪+年终奖",
      bankAccount: "6222021234567890127",
      bankName: "中国银行",
    },
    emergencyContact: {
      name: "钱夫",
      relation: "丈夫",
      phone: "***********",
    },
    attendance: {
      totalDays: 85,
      leaveDays: 2,
      lateDays: 0,
    },
    performance: {
      rating: 4.7,
      reportAccuracy: "优秀",
      costSaving: "显著",
    },
  },
  {
    id: 6,
    name: "孙瑜",
    avatar: "/placeholder.svg?height=32&width=32",
    email: "<EMAIL>",
    role: "瑜伽教练",
    status: "active",
    lastLogin: "2025-03-28 11:30",
    loginCount: 38,
    createdAt: "2023-06-18",
    department: "教学部",
    phone: "***********",
    position: "初级教练",
    employeeId: "YG-2023-002",
    entryDate: "2023-06-18",
    gender: "女",
    birthday: "1993-12-05",
    address: "北京市丰台区丰台路28号",
    education: "大专",
    specialty: ["流瑜伽", "冥想"],
    certificates: ["国际瑜伽联盟RYT-200"],
    salary: {
      basic: 6500,
      performance: "课时费+提成",
      bankAccount: "6222021234567890128",
      bankName: "中国民生银行",
    },
    emergencyContact: {
      name: "孙母",
      relation: "母亲",
      phone: "***********",
    },
    attendance: {
      totalDays: 70,
      leaveDays: 1,
      lateDays: 2,
    },
    performance: {
      rating: 4.3,
      studentFeedback: 4.5,
      classCount: 180,
    },
  },
  {
    id: 7,
    name: "周洁",
    avatar: "/placeholder.svg?height=32&width=32",
    email: "<EMAIL>",
    role: "清洁人员",
    status: "active",
    lastLogin: "2025-03-27 16:45",
    loginCount: 29,
    createdAt: "2023-07-22",
    department: "后勤部",
    phone: "***********",
    position: "保洁主管",
    employeeId: "BJ-2023-001",
    entryDate: "2023-07-22",
    gender: "女",
    birthday: "1975-09-18",
    address: "北京市朝阳区望京西路50号",
    education: "高中",
    specialty: ["环境卫生管理", "物品消毒"],
    certificates: ["健康证"],
    salary: {
      basic: 4500,
      performance: "固定底薪",
      bankAccount: "6222021234567890129",
      bankName: "中国邮政储蓄银行",
    },
    emergencyContact: {
      name: "周夫",
      relation: "丈夫",
      phone: "***********",
    },
    attendance: {
      totalDays: 65,
      leaveDays: 0,
      lateDays: 0,
    },
    performance: {
      rating: 4.6,
      cleanlinessRating: "优秀",
      customerComplaints: 0,
    },
  },
]

// 员工操作日志
const employeeLogs = [
  { id: 1, employeeId: 1, action: "登录系统", ip: "*************", time: "2025-03-28 09:15:22", status: "success" },
  { id: 2, employeeId: 1, action: "修改课程信息", ip: "*************", time: "2025-03-28 09:20:15", status: "success" },
  { id: 3, employeeId: 1, action: "添加会员预约", ip: "*************", time: "2025-03-28 09:35:48", status: "success" },
  { id: 4, employeeId: 2, action: "登录系统", ip: "*************", time: "2025-03-27 14:30:05", status: "success" },
  { id: 5, employeeId: 2, action: "处理会员续费", ip: "*************", time: "2025-03-27 14:45:32", status: "success" },
  { id: 6, employeeId: 3, action: "登录系统", ip: "*************", time: "2025-03-28 08:45:18", status: "success" },
  { id: 7, employeeId: 3, action: "更新个人简介", ip: "*************", time: "2025-03-28 08:55:40", status: "success" },
  { id: 8, employeeId: 4, action: "登录失败", ip: "*************", time: "2025-03-25 16:15:10", status: "failed" },
  { id: 9, employeeId: 4, action: "登录系统", ip: "*************", time: "2025-03-25 16:20:22", status: "success" },
  { id: 10, employeeId: 5, action: "登录系统", ip: "*************", time: "2025-03-28 10:05:33", status: "success" },
]

// 员工培训记录
const trainingRecords = [
  {
    id: 1,
    employeeId: 1,
    title: "高级瑜伽教学技巧",
    type: "专业技能",
    date: "2024-01-15",
    duration: 16,
    provider: "国际瑜伽协会",
    certificate: true,
    score: 92,
  },
  {
    id: 2,
    employeeId: 1,
    title: "瑜伽解剖学进阶",
    type: "专业知识",
    date: "2024-02-20",
    duration: 8,
    provider: "瑜伽解剖学院",
    certificate: true,
    score: 88,
  },
  {
    id: 3,
    employeeId: 2,
    title: "客户服务与沟通",
    type: "软技能",
    date: "2024-01-10",
    duration: 4,
    provider: "内部培训",
    certificate: false,
    score: 90,
  },
  {
    id: 4,
    employeeId: 3,
    title: "普拉提器械使用高级班",
    type: "专业技能",
    date: "2024-03-05",
    duration: 24,
    provider: "BASI普拉提学院",
    certificate: true,
    score: 95,
  },
  {
    id: 5,
    employeeId: 3,
    title: "产后修复专题",
    type: "专业知识",
    date: "2024-02-15",
    duration: 16,
    provider: "妇幼健康协会",
    certificate: true,
    score: 91,
  },
  {
    id: 6,
    employeeId: 4,
    title: "销售技巧提升",
    type: "软技能",
    date: "2024-01-25",
    duration: 8,
    provider: "内部培训",
    certificate: false,
    score: 85,
  },
  {
    id: 7,
    employeeId: 5,
    title: "财务软件使用培训",
    type: "专业技能",
    date: "2024-02-10",
    duration: 4,
    provider: "内部培训",
    certificate: false,
    score: 93,
  },
  {
    id: 8,
    employeeId: 6,
    title: "初级瑜伽教学认证",
    type: "专业技能",
    date: "2024-01-05",
    duration: 200,
    provider: "国际瑜伽联盟",
    certificate: true,
    score: 89,
  },
]

// 员工绩效评估
const performanceReviews = [
  {
    id: 1,
    employeeId: 1,
    period: "2024年第一季度",
    overallRating: 4.8,
    reviewDate: "2024-04-05",
    reviewer: "张总监",
    details: [
      { category: "教学质量", rating: 4.9, comment: "教学质量优秀，学员反馈非常好" },
      { category: "出勤情况", rating: 4.7, comment: "出勤情况良好，偶有调课" },
      { category: "团队协作", rating: 4.8, comment: "与其他教练配合默契，积极参与团队活动" },
      { category: "自我提升", rating: 4.8, comment: "持续学习新技能，参加多次培训" },
    ],
  },
  {
    id: 2,
    employeeId: 2,
    period: "2024年第一季度",
    overallRating: 4.5,
    reviewDate: "2024-04-05",
    reviewer: "李经理",
    details: [
      { category: "服务质量", rating: 4.7, comment: "服务态度好，会员满意度高" },
      { category: "工作效率", rating: 4.5, comment: "处理事务高效，偶有疏漏" },
      { category: "团队协作", rating: 4.4, comment: "能够配合其他部门工作" },
      { category: "问题解决", rating: 4.4, comment: "能够妥善处理一般会员投诉" },
    ],
  },
  {
    id: 3,
    employeeId: 3,
    period: "2024年第一季度",
    overallRating: 4.9,
    reviewDate: "2024-04-05",
    reviewer: "张总监",
    details: [
      { category: "教学质量", rating: 5.0, comment: "教学质量极佳，深受会员喜爱" },
      { category: "出勤情况", rating: 4.7, comment: "出勤情况良好，按时上下课" },
      { category: "团队协作", rating: 4.9, comment: "团队合作意识强，乐于分享经验" },
      { category: "自我提升", rating: 5.0, comment: "不断学习新技能，引入创新教学方法" },
    ],
  },
]

// 部门列表
const departments = [
  { id: 1, name: "教学部", manager: "张总监", employeeCount: 15, description: "负责课程教学和教练管理" },
  { id: 2, name: "运营部", manager: "李经理", employeeCount: 8, description: "负责日常运营和前台接待" },
  { id: 3, name: "销售部", manager: "王主管", employeeCount: 6, description: "负责会员销售和市场推广" },
  { id: 4, name: "财务部", manager: "钱经理", employeeCount: 3, description: "负责财务管理和账目核算" },
  { id: 5, name: "后勤部", manager: "周主管", employeeCount: 4, description: "负责场地维护和清洁卫生" },
]

// 职位列表
const positions = [
  // 教学部 - 教练/老师职位
  {
    id: 1,
    name: "瑜伽高级教练",
    department: "教学部",
    level: "高级",
    baseSalary: 9000,
    description: "拥有5年以上瑜伽教学经验的资深教练",
    type: "coach",
    specialty: "瑜伽",
  },
  {
    id: 2,
    name: "瑜伽资深教练",
    department: "教学部",
    level: "中级",
    baseSalary: 7500,
    description: "拥有3-5年瑜伽教学经验的教练",
    type: "coach",
    specialty: "瑜伽",
  },
  {
    id: 3,
    name: "瑜伽初级教练",
    department: "教学部",
    level: "初级",
    baseSalary: 6000,
    description: "拥有1-3年瑜伽教学经验的教练",
    type: "coach",
    specialty: "瑜伽",
  },
  {
    id: 4,
    name: "普拉提高级教练",
    department: "教学部",
    level: "高级",
    baseSalary: 9000,
    description: "拥有5年以上普拉提教学经验的资深教练",
    type: "coach",
    specialty: "普拉提",
  },
  {
    id: 5,
    name: "普拉提资深教练",
    department: "教学部",
    level: "中级",
    baseSalary: 7500,
    description: "拥有3-5年普拉提教学经验的教练",
    type: "coach",
    specialty: "普拉提",
  },
  {
    id: 6,
    name: "普拉提初级教练",
    department: "教学部",
    level: "初级",
    baseSalary: 6000,
    description: "拥有1-3年普拉提教学经验的教练",
    type: "coach",
    specialty: "普拉提",
  },
  {
    id: 7,
    name: "舞蹈高级教师",
    department: "教学部",
    level: "高级",
    baseSalary: 8500,
    description: "拥有5年以上舞蹈教学经验的资深教师",
    type: "coach",
    specialty: "舞蹈",
  },
  {
    id: 8,
    name: "舞蹈资深教师",
    department: "教学部",
    level: "中级",
    baseSalary: 7000,
    description: "拥有3-5年舞蹈教学经验的教师",
    type: "coach",
    specialty: "舞蹈",
  },
  {
    id: 9,
    name: "舞蹈初级教师",
    department: "教学部",
    level: "初级",
    baseSalary: 5500,
    description: "拥有1-3年舞蹈教学经验的教师",
    type: "coach",
    specialty: "舞蹈",
  },
  {
    id: 10,
    name: "私教健身教练",
    department: "教学部",
    level: "中级",
    baseSalary: 7000,
    description: "专注于一对一私教健身指导",
    type: "coach",
    specialty: "健身",
  },
  {
    id: 11,
    name: "团课健身教练",
    department: "教学部",
    level: "中级",
    baseSalary: 6500,
    description: "专注于团体健身课程指导",
    type: "coach",
    specialty: "健身",
  },
  {
    id: 12,
    name: "康复理疗师",
    department: "教学部",
    level: "高级",
    baseSalary: 9500,
    description: "专注于运动康复和理疗指导",
    type: "coach",
    specialty: "康复",
  },
  {
    id: 13,
    name: "儿童体适能教练",
    department: "教学部",
    level: "中级",
    baseSalary: 6800,
    description: "专注于儿童体适能培训",
    type: "coach",
    specialty: "儿童",
  },
  // 运营部职位
  {
    id: 14,
    name: "前台主管",
    department: "运营部",
    level: "中级",
    baseSalary: 6000,
    description: "负责前台团队管理和日常运营",
    type: "staff",
  },
  {
    id: 15,
    name: "前台接待",
    department: "运营部",
    level: "初级",
    baseSalary: 5000,
    description: "负责会员接待和日常咨询",
    type: "staff",
  },
  // 销售部职位
  {
    id: 16,
    name: "销售主管",
    department: "销售部",
    level: "中级",
    baseSalary: 7000,
    description: "负责销售团队管理和业绩指标",
    type: "staff",
  },
  {
    id: 17,
    name: "会籍顾问",
    department: "销售部",
    level: "初级",
    baseSalary: 5000,
    description: "负责会员卡销售和续费",
    type: "staff",
  },
  // 财务部职位
  {
    id: 18,
    name: "财务主管",
    department: "财务部",
    level: "中级",
    baseSalary: 8000,
    description: "负责财务团队管理和财务规划",
    type: "staff",
  },
  {
    id: 19,
    name: "会计",
    department: "财务部",
    level: "初级",
    baseSalary: 6000,
    description: "负责日常账目核算和财务报表",
    type: "staff",
  },
  // 后勤部职位
  {
    id: 20,
    name: "后勤主管",
    department: "后勤部",
    level: "中级",
    baseSalary: 5500,
    description: "负责后勤团队管理和设施维护",
    type: "staff",
  },
  {
    id: 21,
    name: "保洁员",
    department: "后勤部",
    level: "初级",
    baseSalary: 4000,
    description: "负责场地清洁和卫生维护",
    type: "staff",
  },
  {
    id: 22,
    name: "设备维护员",
    department: "后勤部",
    level: "初级",
    baseSalary: 4500,
    description: "负责健身设备维护和保养",
    type: "staff",
  },
]

export function EmployeeManagement() {
  const [open, setOpen] = useState(false)
  const [editOpen, setEditOpen] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [viewMode, setViewMode] = useState("table")
  const [showInactive, setShowInactive] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("employees")
  const [trainingOpen, setTrainingOpen] = useState(false)
  const [performanceOpen, setPerformanceOpen] = useState(false)
  const [departmentOpen, setDepartmentOpen] = useState(false)
  const [positionOpen, setPositionOpen] = useState(false)
  const [attendanceOpen, setAttendanceOpen] = useState(false)
  const [salaryOpen, setSalaryOpen] = useState(false)
  const [importOpen, setImportOpen] = useState(false)
  const [importProgress, setImportProgress] = useState(0)
  const [importStatus, setImportStatus] = useState("idle")
  const { toast } = useToast()

  const filteredEmployees = employees.filter(employee => {
    // 根据搜索词过滤
    const matchesSearch =
      employee.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.role.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.position.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.employeeId.toLowerCase().includes(searchQuery.toLowerCase())

    // 根据状态过滤
    const matchesStatus = showInactive || employee.status === "active"

    return matchesSearch && matchesStatus
  })

  const handleRefresh = () => {
    setIsLoading(true)
    // 模拟刷新操作
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "刷新成功",
        description: "员工数据已更新",
      })
    }, 1000)
  }

  const handleEditEmployee = (employee) => {
    setSelectedEmployee(employee)
    setEditOpen(true)
  }

  const handleResetPassword = (employeeId) => {
    toast({
      title: "密码重置邮件已发送",
      description: `已向员工 ID ${employeeId} 发送密码重置邮件`,
    })
  }

  const handleDeleteEmployee = (employeeId) => {
    toast({
      title: "员工已删除",
      description: `员工 ID ${employeeId} 已成功删除`,
      variant: "destructive",
    })
  }

  const handleToggleStatus = (employeeId, currentStatus) => {
    const newStatus = currentStatus === "active" ? "inactive" : "active"
    toast({
      title: `员工状态已${newStatus === "active" ? "激活" : "停用"}`,
      description: `员工 ID ${employeeId} 已${newStatus === "active" ? "激活" : "停用"}`,
    })
  }

  const handleViewTraining = (employee) => {
    setSelectedEmployee(employee)
    setTrainingOpen(true)
  }

  const handleViewPerformance = (employee) => {
    setSelectedEmployee(employee)
    setPerformanceOpen(true)
  }

  const handleViewAttendance = (employee) => {
    setSelectedEmployee(employee)
    setAttendanceOpen(true)
  }

  const handleViewSalary = (employee) => {
    setSelectedEmployee(employee)
    setSalaryOpen(true)
  }

  const handleImportEmployees = () => {
    setImportOpen(true)
  }

  const startImport = () => {
    setImportStatus("importing")
    setImportProgress(0)

    // 模拟导入进度
    const interval = setInterval(() => {
      setImportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setImportStatus("completed")
          toast({
            title: "导入成功",
            description: "员工数据已成功导入系统",
          })
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>员工管理</CardTitle>
            <CardDescription>管理场馆员工信息、绩效和培训</CardDescription>
          </div>
          <div className="flex gap-2">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-[400px]">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="employees">员工</TabsTrigger>
                <TabsTrigger value="departments">部门</TabsTrigger>
                <TabsTrigger value="positions">职位</TabsTrigger>
              </TabsList>
            </Tabs>
            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              {isLoading ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardHeader>

        {activeTab === "employees" && (
          <>
            <CardContent>
              <div className="flex items-center justify-between mb-4">
                <div className="relative flex-1 mr-4">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索员工姓名、ID、职位、部门..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className={viewMode === "table" ? "bg-muted" : ""} onClick={() => setViewMode("table")}>
                    <FileText className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" className={viewMode === "grid" ? "bg-muted" : ""} onClick={() => setViewMode("grid")}>
                    <div className="grid grid-cols-2 gap-0.5">
                      <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
                      <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
                      <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
                      <div className="h-1.5 w-1.5 rounded-sm bg-current"></div>
                    </div>
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <Filter className="h-4 w-4 mr-2" />
                        筛选
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <DropdownMenuLabel>筛选选项</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <div className="p-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="show-inactive" checked={showInactive} onCheckedChange={setShowInactive} />
                          <Label htmlFor="show-inactive">显示非活跃员工</Label>
                        </div>
                      </div>
                      <DropdownMenuSeparator />
                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger>
                          <Building className="mr-2 h-4 w-4" />
                          <span>按部门筛选</span>
                        </DropdownMenuSubTrigger>
                        <DropdownMenuPortal>
                          <DropdownMenuSubContent>
                            <DropdownMenuItem>
                              <Checkbox id="dept-all" className="mr-2" />
                              <Label htmlFor="dept-all">全部部门</Label>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {departments.map(dept => (
                              <DropdownMenuItem key={dept.id}>
                                <Checkbox id={`dept-${dept.id}`} className="mr-2" />
                                <Label htmlFor={`dept-${dept.id}`}>{dept.name}</Label>
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuSubContent>
                        </DropdownMenuPortal>
                      </DropdownMenuSub>
                      <DropdownMenuSub>
                        <DropdownMenuSubTrigger>
                          <Briefcase className="mr-2 h-4 w-4" />
                          <span>按职位筛选</span>
                        </DropdownMenuSubTrigger>
                        <DropdownMenuPortal>
                          <DropdownMenuSubContent>
                            <DropdownMenuItem>
                              <Checkbox id="pos-all" className="mr-2" />
                              <Label htmlFor="pos-all">全部职位</Label>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {positions.map(pos => (
                              <DropdownMenuItem key={pos.id}>
                                <Checkbox id={`pos-${pos.id}`} className="mr-2" />
                                <Label htmlFor={`pos-${pos.id}`}>{pos.name}</Label>
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuSubContent>
                        </DropdownMenuPortal>
                      </DropdownMenuSub>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleImportEmployees}>
                        <Upload className="mr-2 h-4 w-4" />
                        导入员工
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="mr-2 h-4 w-4" />
                        导出员工列表
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <Dialog open={open} onOpenChange={setOpen}>
                    <DialogTrigger asChild>
                      <Button>
                        <UserPlus className="mr-2 h-4 w-4" />
                        添加员工
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>添加新员工</DialogTitle>
                        <DialogDescription>创建新员工档案并设置基本信息</DialogDescription>
                      </DialogHeader>
                      <Tabs defaultValue="basic" className="w-full">
                        <TabsList className="grid w-full grid-cols-4">
                          <TabsTrigger value="basic">基本信息</TabsTrigger>
                          <TabsTrigger value="work">工作信息</TabsTrigger>
                          <TabsTrigger value="salary">薪资设置</TabsTrigger>
                          <TabsTrigger value="other">其他信息</TabsTrigger>
                        </TabsList>
                        <TabsContent value="basic" className="space-y-4 py-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="name">姓名 <span className="text-red-500">*</span></Label>
                              <Input id="name" placeholder="输入员工姓名" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="employee-id">员工编号 <span className="text-red-500">*</span></Label>
                              <Input id="employee-id" placeholder="输入员工编号" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="gender">性别</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择性别" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="male">男</SelectItem>
                                  <SelectItem value="female">女</SelectItem>
                                  <SelectItem value="other">其他</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="birthday">出生日期</Label>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="outline"
                                    className="w-full justify-start text-left font-normal"
                                  >
                                    <Calendar className="mr-2 h-4 w-4" />
                                    <span>选择日期</span>
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <CalendarComponent
                                    mode="single"
                                  />
                                </PopoverContent>
                              </Popover>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="email">电子邮箱 <span className="text-red-500">*</span></Label>
                              <Input id="email" type="email" placeholder="输入电子邮箱" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="phone">手机号码 <span className="text-red-500">*</span></Label>
                              <Input id="phone" placeholder="输入手机号码" />
                            </div>
                            <div className="space-y-2 col-span-2">
                              <Label htmlFor="address">住址</Label>
                              <Input id="address" placeholder="输入详细住址" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="education">学历</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择学历" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="high-school">高中</SelectItem>
                                  <SelectItem value="college">大专</SelectItem>
                                  <SelectItem value="bachelor">本科</SelectItem>
                                  <SelectItem value="master">硕士</SelectItem>
                                  <SelectItem value="doctor">博士</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="id-card">身份证号</Label>
                              <Input id="id-card" placeholder="输入身份证号" />
                            </div>
                          </div>
                        </TabsContent>
                        <TabsContent value="work" className="space-y-4 py-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="department">所属部门 <span className="text-red-500">*</span></Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择部门" />
                                </SelectTrigger>
                                <SelectContent>
                                  {departments.map(dept => (
                                    <SelectItem key={dept.id} value={dept.id.toString()}>{dept.name}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="position">职位 <span className="text-red-500">*</span></Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择职位" />
                                </SelectTrigger>
                                <SelectContent>
                                  {positions.map(pos => (
                                    <SelectItem key={pos.id} value={pos.id.toString()}>{pos.name}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="role">系统角色</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择系统角色" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="admin">系统管理员</SelectItem>
                                  <SelectItem value="manager">部门经理</SelectItem>
                                  <SelectItem value="coach">教练</SelectItem>
                                  <SelectItem value="reception">前台</SelectItem>
                                  <SelectItem value="sales">销售</SelectItem>
                                  <SelectItem value="finance">财务</SelectItem>
                                  <SelectItem value="staff">普通员工</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="entry-date">入职日期 <span className="text-red-500">*</span></Label>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="outline"
                                    className="w-full justify-start text-left font-normal"
                                  >
                                    <Calendar className="mr-2 h-4 w-4" />
                                    <span>选择日期</span>
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                  <CalendarComponent
                                    mode="single"
                                  />
                                </PopoverContent>
                              </Popover>
                            </div>
                            <div className="space-y-2 col-span-2">
                              <Label htmlFor="specialty">专长/技能</Label>
                              <Textarea id="specialty" placeholder="输入员工专长或技能，多个专长用逗号分隔" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="work-status">工作状态</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择工作状态" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="active">在职</SelectItem>
                                  <SelectItem value="probation">试用期</SelectItem>
                                  <SelectItem value="leave">休假中</SelectItem>
                                  <SelectItem value="resigned">已离职</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="certificates">证书/资质</Label>
                              <Textarea id="certificates" placeholder="输入员工持有的证书或资质，多个证书用逗号分隔" />
                            </div>
                          </div>
                        </TabsContent>
                        <TabsContent value="salary" className="space-y-4 py-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="basic-salary">基本工资 <span className="text-red-500">*</span></Label>
                              <Input id="basic-salary" type="number" placeholder="输入基本工资金额" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="performance-type">绩效方式</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择绩效计算方式" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="fixed">固定绩效</SelectItem>
                                  <SelectItem value="commission">提成</SelectItem>
                                  <SelectItem value="class-hour">课时费</SelectItem>
                                  <SelectItem value="mixed">混合方式</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="bank-name">开户银行</Label>
                              <Input id="bank-name" placeholder="输入开户银行名称" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="bank-account">银行账号</Label>
                              <Input id="bank-account" placeholder="输入银行账号" />
                            </div>
                            <div className="space-y-2 col-span-2">
                              <Label htmlFor="salary-notes">薪资备注</Label>
                              <Textarea id="salary-notes" placeholder="输入关于薪资的其他说明" />
                            </div>
                          </div>
                        </TabsContent>
                        <TabsContent value="other" className="space-y-4 py-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="emergency-contact">紧急联系人</Label>
                              <Input id="emergency-contact" placeholder="输入紧急联系人姓名" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="emergency-relation">与员工关系</Label>
                              <Input id="emergency-relation" placeholder="输入与员工的关系" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="emergency-phone">紧急联系电话</Label>
                              <Input id="emergency-phone" placeholder="输入紧急联系人电话" />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="contract-type">合同类型</Label>
                              <Select>
                                <SelectTrigger>
                                  <SelectValue placeholder="选择合同类型" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="full-time">全职</SelectItem>
                                  <SelectItem value="part-time">兼职</SelectItem>
                                  <SelectItem value="intern">实习</SelectItem>
                                  <SelectItem value="contractor">外包</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2 col-span-2">
                              <Label htmlFor="notes">备注</Label>
                              <Textarea id="notes" placeholder="输入其他需要说明的信息" />
                            </div>
                          </div>
                        </TabsContent>
                      </Tabs>
                      <div className="flex justify-end gap-2 mt-4">
                        <Button variant="outline" onClick={() => setOpen(false)}>取消</Button>
                        <Button onClick={() => {
                          setOpen(false)
                          toast({
                            title: "员工已添加",
                            description: "新员工信息已成功添加到系统",
                          })
                        }}>保存</Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </CardContent>
          </>
        )}
      </Card>
    </>
  )
}