"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "@/components/ui/use-toast"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Stepper, Step } from "@/components/ui/stepper"

// 表单验证模式
const registerSchema = z.object({
  companyName: z.string().min(2, { message: "公司名称至少需要2个字符" }).max(50, { message: "公司名称不能超过50个字符" }),
  businessLicense: z.string().min(15, { message: "请输入有效的营业执照号" }).max(18, { message: "营业执照号不能超过18个字符" }),
  username: z.string().min(2, { message: "用户名至少需要2个字符" }).max(20, { message: "用户名不能超过20个字符" }),
  email: z.string().email({ message: "请输入有效的电子邮箱地址" }),
  phone: z.string().regex(/^1[3-9]\d{9}$/, { message: "请输入有效的手机号码" }),
  password: z.string().min(8, { message: "密码至少需要8个字符" })
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, { message: "密码必须包含大小写字母和数字" }),
  confirmPassword: z.string(),
  agreeTerms: z.boolean().refine((val) => val === true, { message: "您必须同意服务条款才能继续" })
}).refine((data) => data.password === data.confirmPassword, {
  message: "两次输入的密码不匹配",
  path: ["confirmPassword"],
});

type RegisterValues = z.infer<typeof registerSchema>;

export default function RegisterPage() {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  // 创建表单
  const form = useForm<RegisterValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      companyName: "",
      businessLicense: "",
      username: "",
      email: "",
      phone: "",
      password: "",
      confirmPassword: "",
      agreeTerms: false
    }
  });

  // 下一步
  const handleNext = async () => {
    const fieldsToValidate = currentStep === 0 
      ? ["companyName", "businessLicense", "username"] 
      : ["email", "phone", "password", "confirmPassword", "agreeTerms"];
    
    const isValid = await form.trigger(fieldsToValidate as any);
    
    if (isValid) {
      if (currentStep < 1) {
        setCurrentStep(currentStep + 1);
      } else {
        form.handleSubmit(handleRegister)();
      }
    }
  };

  // 上一步
  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // 处理注册
  const handleRegister = async (values: RegisterValues) => {
    setIsSubmitting(true);
    try {
      // 调用注册API
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values)
      });

      const result = await response.json();

      if (result.success) {
        // 存储注册信息到本地存储
        localStorage.setItem('registered_tenant', JSON.stringify({
          companyName: values.companyName,
          username: values.username,
          email: values.email,
          phone: values.phone,
          status: 'pending', // 等待审核
          createdAt: new Date().toISOString(),
          tenantId: result.tenantId
        }));

        toast({
          title: "注册成功",
          description: result.message || "您的账户已提交，正在等待审核。审核通过后，您将收到通知。",
        });

        // 注册成功后跳转到登录页
        setTimeout(() => {
          router.push('/login?register=success');
        }, 2000);
      } else {
        throw new Error(result.error || '注册失败');
      }
    } catch (error: any) {
      console.error('注册失败:', error);
      toast({
        title: "注册失败",
        description: error.message || "请稍后再试",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">创建租户账户</CardTitle>
          <CardDescription>
            填写以下信息，注册成为瑜伽管理系统的租户
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Stepper currentStep={currentStep} className="mb-8">
            <Step title="基础信息" description="公司和管理员信息" />
            <Step title="账户设置" description="联系方式和密码" />
          </Stepper>
          
          <Form {...form}>
            <form className="space-y-4">
              {currentStep === 0 && (
                <>
                  <FormField
                    control={form.control}
                    name="companyName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>公司/品牌名称</FormLabel>
                        <FormControl>
                          <Input placeholder="例如：静心瑜伽" {...field} />
                        </FormControl>
                        <FormDescription>
                          输入您的公司或品牌名称
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="businessLicense"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>营业执照号</FormLabel>
                        <FormControl>
                          <Input placeholder="统一社会信用代码" {...field} />
                        </FormControl>
                        <FormDescription>
                          输入公司的统一社会信用代码
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>管理员用户名</FormLabel>
                        <FormControl>
                          <Input placeholder="例如：admin" {...field} />
                        </FormControl>
                        <FormDescription>
                          设置租户管理员的用户名
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}
              
              {currentStep === 1 && (
                <>
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>电子邮箱</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="例如：<EMAIL>" {...field} />
                        </FormControl>
                        <FormDescription>
                          用于接收系统通知和密码重置
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>手机号码</FormLabel>
                        <FormControl>
                          <Input placeholder="例如：13800138000" {...field} />
                        </FormControl>
                        <FormDescription>
                          用于账户验证和接收通知
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>密码</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="设置账户密码" {...field} />
                        </FormControl>
                        <FormDescription>
                          密码需包含大小写字母和数字，至少8位
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>确认密码</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="再次输入密码" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="agreeTerms"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-4">
                        <FormControl>
                          <Checkbox 
                            checked={field.value} 
                            onCheckedChange={field.onChange} 
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            我已阅读并同意<Link href="/terms" className="text-primary underline">服务条款</Link>和<Link href="/privacy" className="text-primary underline">隐私政策</Link>
                          </FormLabel>
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />
                </>
              )}
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-between">
          {currentStep > 0 ? (
            <Button variant="outline" onClick={handlePrevious} disabled={isSubmitting}>
              上一步
            </Button>
          ) : (
            <Button variant="outline" asChild>
              <Link href="/login">返回登录</Link>
            </Button>
          )}
          
          <Button onClick={handleNext} disabled={isSubmitting}>
            {isSubmitting ? "处理中..." : currentStep === 1 ? "提交注册" : "下一步"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
} 