"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DashboardStats } from "@/components/dashboard-stats"
import { DashboardHeader } from "@/components/dashboard-header"
import { RecentCourses } from "@/components/recent-courses"
import { CourseDistribution } from "@/components/course-distribution"
import { BookingTrends } from "@/components/booking-trends"

export default function HomePage() {
  const { isAuthenticated, user } = useAuth()
  const router = useRouter()

  // 当页面加载时检查认证状态
  useEffect(() => {
    // 获取当前路径
    const pathname = window.location.pathname;
    
    // 排除特定路径，这些路径不进行自动重定向
    const excludePaths = ['/login', '/register', '/forgot-password'];
    if (excludePaths.includes(pathname)) {
      console.log(`当前在${pathname}页面，不进行重定向`);
      return;
    }
    
    // 如果用户已登录且在根路径，重定向到工作台
    if (isAuthenticated && pathname === '/') {
      console.log('首页检测到已登录状态，准备跳转到工作台')
      setTimeout(() => {
        router.push('/dashboard')
      }, 100)
    } 
    // 如果用户未登录且在根路径，重定向到新的首页
    else if (!isAuthenticated && pathname === '/') {
      console.log('用户未登录，重定向到官网首页')
      router.push('/home')
    }
  }, [isAuthenticated, router])

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col items-center justify-center min-h-[50vh] text-center">
        <Card className="max-w-md w-full">
          <CardContent className="pt-6">
            <CardTitle className="text-2xl mb-4">瑜伽管理系统</CardTitle>
            <CardDescription className="mb-6">
              {isAuthenticated ?
                `欢迎回来，${user?.nickname || user?.username}` :
                '请登录系统继续使用'}
            </CardDescription>

            {!isAuthenticated && (
              <Button
                onClick={() => router.push('/login')}
                className="w-full"
              >
                前往登录
              </Button>
            )}

            {isAuthenticated && (
              <Button
                onClick={() => router.push('/dashboard')}
                className="w-full"
              >
                进入工作台
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

