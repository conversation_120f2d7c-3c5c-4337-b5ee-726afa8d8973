"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { AlertCircle, Eye, Search, ShieldAlert, ShieldCheck } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

export default function PaymentSecurityPage() {
  const [dateRange, setDateRange] = useState("7days")
  const [riskLevel, setRiskLevel] = useState("all")
  const [selectedTransaction, setSelectedTransaction] = useState(null)
  const [showTransactionDetails, setShowTransactionDetails] = useState(false)

  const [riskTransactions] = useState([
    {
      id: "****************",
      amount: "¥5,999.00",
      channel: "微信支付",
      time: "2025-03-28 14:30:25",
      customer: "张三",
      riskLevel: "high",
      riskReason: "异常IP地址，交易金额异常",
      status: "blocked",
      details: {
        ip: "************",
        location: "海外地区",
        device: "iPhone 15 Pro",
        browser: "Safari 17.0",
        previousTransactions: 2,
        accountAge: "3天",
      },
    },
    {
      id: "****************",
      amount: "¥1,299.00",
      channel: "支付宝",
      time: "2025-03-28 11:15:42",
      customer: "李四",
      riskLevel: "medium",
      riskReason: "短时间内多次交易",
      status: "flagged",
      details: {
        ip: "************",
        location: "北京市",
        device: "Xiaomi 13",
        browser: "Chrome 120.0",
        previousTransactions: 5,
        accountAge: "30天",
      },
    },
    {
      id: "****************",
      amount: "¥2,499.00",
      channel: "银联支付",
      time: "2025-03-28 09:45:18",
      customer: "王五",
      riskLevel: "low",
      riskReason: "新设备登录",
      status: "approved",
      details: {
        ip: "************",
        location: "上海市",
        device: "Huawei P50",
        browser: "Firefox 115.0",
        previousTransactions: 12,
        accountAge: "180天",
      },
    },
    {
      id: "****************",
      amount: "¥3,999.00",
      channel: "微信支付",
      time: "2025-03-27 16:30:25",
      customer: "赵六",
      riskLevel: "high",
      riskReason: "账户信息不匹配",
      status: "blocked",
      details: {
        ip: "************",
        location: "广州市",
        device: "OPPO Find X5",
        browser: "Chrome 119.0",
        previousTransactions: 0,
        accountAge: "1天",
      },
    },
    {
      id: "****************",
      amount: "¥899.00",
      channel: "支付宝",
      time: "2025-03-27 10:15:42",
      customer: "钱七",
      riskLevel: "medium",
      riskReason: "交易地区异常",
      status: "approved",
      details: {
        ip: "************",
        location: "深圳市",
        device: "vivo X90",
        browser: "Edge 120.0",
        previousTransactions: 8,
        accountAge: "90天",
      },
    },
  ])

  const handleViewDetails = (transaction) => {
    setSelectedTransaction(transaction)
    setShowTransactionDetails(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">支付安全管理</h1>
      </div>

      <Tabs defaultValue="monitor" className="space-y-4">
        <TabsList>
          <TabsTrigger value="monitor">风险交易监控</TabsTrigger>
          <TabsTrigger value="rules">安全规则配置</TabsTrigger>
          <TabsTrigger value="blacklist">黑名单管理</TabsTrigger>
          <TabsTrigger value="logs">安全日志</TabsTrigger>
        </TabsList>

        <TabsContent value="monitor" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>风险交易监控</CardTitle>
              <CardDescription>监控和处理存在风险的交易</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="w-full md:w-1/3">
                  <Input placeholder="输入交易号、客户名称或IP地址" />
                </div>
                <div className="flex flex-1 gap-4">
                  <Select value={riskLevel} onValueChange={setRiskLevel}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="风险等级" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部等级</SelectItem>
                      <SelectItem value="high">高风险</SelectItem>
                      <SelectItem value="medium">中风险</SelectItem>
                      <SelectItem value="low">低风险</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">今天</SelectItem>
                      <SelectItem value="yesterday">昨天</SelectItem>
                      <SelectItem value="7days">最近7天</SelectItem>
                      <SelectItem value="30days">最近30天</SelectItem>
                      <SelectItem value="custom">自定义</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>交易单号</TableHead>
                    <TableHead>金额</TableHead>
                    <TableHead>支付渠道</TableHead>
                    <TableHead>客户</TableHead>
                    <TableHead>交易时间</TableHead>
                    <TableHead>风险等级</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {riskTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">{transaction.id}</TableCell>
                      <TableCell>{transaction.amount}</TableCell>
                      <TableCell>{transaction.channel}</TableCell>
                      <TableCell>{transaction.customer}</TableCell>
                      <TableCell>{transaction.time}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            transaction.riskLevel === "high"
                              ? "destructive"
                              : transaction.riskLevel === "medium"
                                ? "default"
                                : "outline"
                          }
                        >
                          {transaction.riskLevel === "high"
                            ? "高风险"
                            : transaction.riskLevel === "medium"
                              ? "中风险"
                              : "低风险"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            transaction.status === "blocked"
                              ? "destructive"
                              : transaction.status === "flagged"
                                ? "default"
                                : "outline"
                          }
                        >
                          {transaction.status === "blocked"
                            ? "已拦截"
                            : transaction.status === "flagged"
                              ? "已标记"
                              : "已通过"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" onClick={() => handleViewDetails(transaction)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">今日风险交易</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">较昨日增加 3 笔</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">拦截金额</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥12,998.00</div>
                <p className="text-xs text-muted-foreground">较昨日增加 ¥5,999.00</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">拦截率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2.5%</div>
                <p className="text-xs text-muted-foreground">较昨日增加 0.8%</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rules" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>安全规则配置</CardTitle>
              <CardDescription>配置支付安全规则</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">基础安全设置</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="rule-ip">IP地址风险检测</Label>
                      <p className="text-sm text-muted-foreground">检测异常IP地址和地区</p>
                    </div>
                    <Switch id="rule-ip" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="rule-device">设备风险检测</Label>
                      <p className="text-sm text-muted-foreground">检测新设备和异常设备</p>
                    </div>
                    <Switch id="rule-device" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="rule-frequency">交易频率检测</Label>
                      <p className="text-sm text-muted-foreground">检测短时间内的多次交易</p>
                    </div>
                    <Switch id="rule-frequency" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="rule-amount">交易金额检测</Label>
                      <p className="text-sm text-muted-foreground">检测异常交易金额</p>
                    </div>
                    <Switch id="rule-amount" defaultChecked />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">高级安全设置</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="rule-ai">AI风险评估</Label>
                      <p className="text-sm text-muted-foreground">使用AI模型评估交易风险</p>
                    </div>
                    <Switch id="rule-ai" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="rule-behavior">行为分析</Label>
                      <p className="text-sm text-muted-foreground">分析用户行为模式</p>
                    </div>
                    <Switch id="rule-behavior" defaultChecked />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="rule-3ds">3DS验证</Label>
                      <p className="text-sm text-muted-foreground">启用3D Secure验证</p>
                    </div>
                    <Switch id="rule-3ds" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="rule-sms">短信验证</Label>
                      <p className="text-sm text-muted-foreground">大额交易启用短信验证</p>
                    </div>
                    <Switch id="rule-sms" defaultChecked />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">风险阈值设置</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="threshold-amount">单笔交易金额阈值（元）</Label>
                    <Input id="threshold-amount" type="number" defaultValue="5000" />
                    <p className="text-xs text-muted-foreground">超过此金额将触发额外验证</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="threshold-frequency">短时交易频率阈值（次/小时）</Label>
                    <Input id="threshold-frequency" type="number" defaultValue="5" />
                    <p className="text-xs text-muted-foreground">超过此频率将触发风险检测</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="threshold-daily">日交易金额阈值（元）</Label>
                    <Input id="threshold-daily" type="number" defaultValue="10000" />
                    <p className="text-xs text-muted-foreground">超过此金额将触发额外验证</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="threshold-risk">风险评分阈值</Label>
                    <Input id="threshold-risk" type="number" defaultValue="75" />
                    <p className="text-xs text-muted-foreground">超过此评分将拦截交易</p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button>保存配置</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="blacklist" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>黑名单管理</CardTitle>
              <CardDescription>管理支付黑名单</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <div className="flex gap-2">
                  <Input placeholder="输入IP地址、设备ID或用户ID" className="w-80" />
                  <Button>
                    <Search className="mr-2 h-4 w-4" />
                    查询
                  </Button>
                </div>
                <Button>添加黑名单</Button>
              </div>

              <Tabs defaultValue="ip">
                <TabsList>
                  <TabsTrigger value="ip">IP黑名单</TabsTrigger>
                  <TabsTrigger value="device">设备黑名单</TabsTrigger>
                  <TabsTrigger value="user">用户黑名单</TabsTrigger>
                </TabsList>
                <TabsContent value="ip" className="mt-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>IP地址</TableHead>
                        <TableHead>添加时间</TableHead>
                        <TableHead>添加原因</TableHead>
                        <TableHead>操作人</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell className="font-medium">************</TableCell>
                        <TableCell>2025-03-28 14:30:25</TableCell>
                        <TableCell>多次尝试欺诈交易</TableCell>
                        <TableCell>系统</TableCell>
                        <TableCell>
                          <Badge>生效中</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            解除
                          </Button>
                        </TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell className="font-medium">************</TableCell>
                        <TableCell>2025-03-27 10:15:42</TableCell>
                        <TableCell>异常登录尝试</TableCell>
                        <TableCell>张三</TableCell>
                        <TableCell>
                          <Badge>生效中</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            解除
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TabsContent>
                <TabsContent value="device" className="mt-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>设备ID</TableHead>
                        <TableHead>设备信息</TableHead>
                        <TableHead>添加时间</TableHead>
                        <TableHead>添加原因</TableHead>
                        <TableHead>操作人</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell className="font-medium">DEV20250328001</TableCell>
                        <TableCell>iPhone 15 Pro (iOS 17.0)</TableCell>
                        <TableCell>2025-03-28 14:30:25</TableCell>
                        <TableCell>多次尝试欺诈交易</TableCell>
                        <TableCell>系统</TableCell>
                        <TableCell>
                          <Badge>生效中</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            解除
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TabsContent>
                <TabsContent value="user" className="mt-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>用户ID</TableHead>
                        <TableHead>用户信息</TableHead>
                        <TableHead>添加时间</TableHead>
                        <TableHead>添加原因</TableHead>
                        <TableHead>操作人</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell className="font-medium">USR20250328001</TableCell>
                        <TableCell>张三 (138****1234)</TableCell>
                        <TableCell>2025-03-28 14:30:25</TableCell>
                        <TableCell>多次尝试欺诈交易</TableCell>
                        <TableCell>系统</TableCell>
                        <TableCell>
                          <Badge>生效中</Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            解除
                          </Button>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>安全日志</CardTitle>
              <CardDescription>查看支付安全相关日志</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4 md:flex-row">
                <div className="w-full md:w-1/3">
                  <Input placeholder="输入关键词搜索" />
                </div>
                <div className="flex flex-1 gap-4">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="日志类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value="risk">风险检测</SelectItem>
                      <SelectItem value="block">交易拦截</SelectItem>
                      <SelectItem value="verify">身份验证</SelectItem>
                      <SelectItem value="config">配置变更</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select defaultValue="7days">
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="时间范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">今天</SelectItem>
                      <SelectItem value="yesterday">昨天</SelectItem>
                      <SelectItem value="7days">最近7天</SelectItem>
                      <SelectItem value="30days">最近30天</SelectItem>
                      <SelectItem value="custom">自定义</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button>
                  <Search className="mr-2 h-4 w-4" />
                  查询
                </Button>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>时间</TableHead>
                    <TableHead>日志类型</TableHead>
                    <TableHead>操作</TableHead>
                    <TableHead>详情</TableHead>
                    <TableHead>IP地址</TableHead>
                    <TableHead>操作人</TableHead>
                    <TableHead>结果</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell>2025-03-28 14:30:25</TableCell>
                    <TableCell>
                      <Badge variant="outline">风险检测</Badge>
                    </TableCell>
                    <TableCell>交易风险评估</TableCell>
                    <TableCell>交易ID: ****************</TableCell>
                    <TableCell>************</TableCell>
                    <TableCell>系统</TableCell>
                    <TableCell>
                      <Badge variant="destructive">高风险</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>2025-03-28 14:30:28</TableCell>
                    <TableCell>
                      <Badge variant="outline">交易拦截</Badge>
                    </TableCell>
                    <TableCell>拦截可疑交易</TableCell>
                    <TableCell>交易ID: ****************</TableCell>
                    <TableCell>************</TableCell>
                    <TableCell>系统</TableCell>
                    <TableCell>
                      <Badge variant="default">成功</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>2025-03-28 11:15:42</TableCell>
                    <TableCell>
                      <Badge variant="outline">身份验证</Badge>
                    </TableCell>
                    <TableCell>短信验证</TableCell>
                    <TableCell>交易ID: ****************</TableCell>
                    <TableCell>************</TableCell>
                    <TableCell>李四</TableCell>
                    <TableCell>
                      <Badge variant="default">成功</Badge>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>2025-03-27 16:30:25</TableCell>
                    <TableCell>
                      <Badge variant="outline">配置变更</Badge>
                    </TableCell>
                    <TableCell>修改风险阈值</TableCell>
                    <TableCell>单笔交易金额阈值: 5000元</TableCell>
                    <TableCell>192.168.1.100</TableCell>
                    <TableCell>管理员</TableCell>
                    <TableCell>
                      <Badge variant="default">成功</Badge>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 交易详情对话框 */}
      {selectedTransaction && (
        <Dialog open={showTransactionDetails} onOpenChange={setShowTransactionDetails}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>风险交易详情</DialogTitle>
              <DialogDescription>交易单号: {selectedTransaction.id}</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <Alert variant={selectedTransaction.riskLevel === "high" ? "destructive" : "default"}>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>
                  {selectedTransaction.riskLevel === "high"
                    ? "高风险交易"
                    : selectedTransaction.riskLevel === "medium"
                      ? "中风险交易"
                      : "低风险交易"}
                </AlertTitle>
                <AlertDescription>{selectedTransaction.riskReason}</AlertDescription>
              </Alert>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">交易金额</h3>
                  <p className="font-medium">{selectedTransaction.amount}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">支付渠道</h3>
                  <p>{selectedTransaction.channel}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">交易时间</h3>
                  <p>{selectedTransaction.time}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">交易状态</h3>
                  <Badge
                    variant={
                      selectedTransaction.status === "blocked"
                        ? "destructive"
                        : selectedTransaction.status === "flagged"
                          ? "default"
                          : "outline"
                    }
                  >
                    {selectedTransaction.status === "blocked"
                      ? "已拦截"
                      : selectedTransaction.status === "flagged"
                        ? "已标记"
                        : "已通过"}
                  </Badge>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">客户信息</h3>
                <p>{selectedTransaction.customer}</p>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-medium mb-2">风险详情</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">IP地址</span>
                    <span>{selectedTransaction.details.ip}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">地理位置</span>
                    <span>{selectedTransaction.details.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">设备</span>
                    <span>{selectedTransaction.details.device}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">浏览器</span>
                    <span>{selectedTransaction.details.browser}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">历史交易次数</span>
                    <span>{selectedTransaction.details.previousTransactions}次</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">账户年龄</span>
                    <span>{selectedTransaction.details.accountAge}</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                {selectedTransaction.status === "blocked" && (
                  <Button variant="outline">
                    <ShieldCheck className="mr-2 h-4 w-4" />
                    允许交易
                  </Button>
                )}
                {selectedTransaction.status === "flagged" && (
                  <>
                    <Button variant="outline">
                      <ShieldCheck className="mr-2 h-4 w-4" />
                      允许交易
                    </Button>
                    <Button variant="destructive">
                      <ShieldAlert className="mr-2 h-4 w-4" />
                      拦截交易
                    </Button>
                  </>
                )}
                {selectedTransaction.status === "approved" && (
                  <Button variant="destructive">
                    <ShieldAlert className="mr-2 h-4 w-4" />
                    拦截交易
                  </Button>
                )}
                <Button variant="outline" onClick={() => setShowTransactionDetails(false)}>
                  关闭
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

