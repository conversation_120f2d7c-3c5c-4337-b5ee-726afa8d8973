"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { DateRange } from "react-day-picker"
import { addDays } from "date-fns"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Filter, X, ShoppingBag, CreditCard, FileText } from "lucide-react"
// 注意：Yoga 图标在 lucide-react 中不存在，需要使用其他图标或自定义组件

interface OrderAdvancedFilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApplyFilters: (filters: any) => void;
}

export function ShopOrderAdvancedFilterDialog({
  open,
  onOpenChange,
  onApplyFilters
}: OrderAdvancedFilterDialogProps) {
  const [activeTab, setActiveTab] = useState("basic")

  // 基本筛选
  const [orderStatus, setOrderStatus] = useState<string[]>([])
  const [paymentStatus, setPaymentStatus] = useState<string[]>([])
  const [paymentMethod, setPaymentMethod] = useState<string[]>([])
  const [deliveryMethod, setDeliveryMethod] = useState<string[]>([])
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: new Date(),
  })
  const [minAmount, setMinAmount] = useState("")
  const [maxAmount, setMaxAmount] = useState("")

  // 商品筛选
  const [productType, setProductType] = useState<string[]>([])
  const [productCategory, setProductCategory] = useState<string[]>([])

  // 客户筛选
  const [customerType, setCustomerType] = useState<string[]>([])
  const [source, setSource] = useState<string[]>([])
  const [region, setRegion] = useState<string[]>([])

  // 瑜伽商城特有筛选
  const [yogaStudio, setYogaStudio] = useState<string[]>([])
  const [membershipLevel, setMembershipLevel] = useState<string[]>([])
  const [courseType, setCourseType] = useState<string[]>([])

  // 处理复选框变化
  const handleCheckboxChange = (
    value: string,
    currentValues: string[],
    setValues: (values: string[]) => void
  ) => {
    if (currentValues.includes(value)) {
      setValues(currentValues.filter(v => v !== value));
    } else {
      setValues([...currentValues, value]);
    }
  };

  // 重置所有筛选条件
  const handleResetFilters = () => {
    setOrderStatus([]);
    setPaymentStatus([]);
    setPaymentMethod([]);
    setDeliveryMethod([]);
    setDateRange({
      from: addDays(new Date(), -30),
      to: new Date(),
    });
    setMinAmount("");
    setMaxAmount("");
    setProductType([]);
    setProductCategory([]);
    setCustomerType([]);
    setSource([]);
    setRegion([]);
    setYogaStudio([]);
    setMembershipLevel([]);
    setCourseType([]);
  };

  // 应用筛选条件
  const handleApplyFilters = () => {
    const filters = {
      orderStatus,
      paymentStatus,
      paymentMethod,
      deliveryMethod,
      dateRange,
      minAmount: minAmount ? parseFloat(minAmount) : undefined,
      maxAmount: maxAmount ? parseFloat(maxAmount) : undefined,
      productType,
      productCategory,
      customerType,
      source,
      region,
      yogaStudio,
      membershipLevel,
      courseType
    };

    onApplyFilters(filters);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            瑜伽商城订单高级筛选
          </DialogTitle>
          <DialogDescription>
            设置详细的筛选条件，精确查找瑜伽商城订单
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">基本筛选</TabsTrigger>
            <TabsTrigger value="product">商品筛选</TabsTrigger>
            <TabsTrigger value="customer">客户筛选</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">订单状态</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-pending"
                    checked={orderStatus.includes("pending")}
                    onCheckedChange={() => handleCheckboxChange("pending", orderStatus, setOrderStatus)}
                  />
                  <Label htmlFor="status-pending">待处理</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-processing"
                    checked={orderStatus.includes("processing")}
                    onCheckedChange={() => handleCheckboxChange("processing", orderStatus, setOrderStatus)}
                  />
                  <Label htmlFor="status-processing">处理中</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-completed"
                    checked={orderStatus.includes("completed")}
                    onCheckedChange={() => handleCheckboxChange("completed", orderStatus, setOrderStatus)}
                  />
                  <Label htmlFor="status-completed">已完成</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="status-cancelled"
                    checked={orderStatus.includes("cancelled")}
                    onCheckedChange={() => handleCheckboxChange("cancelled", orderStatus, setOrderStatus)}
                  />
                  <Label htmlFor="status-cancelled">已取消</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">支付状态</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="payment-paid"
                    checked={paymentStatus.includes("paid")}
                    onCheckedChange={() => handleCheckboxChange("paid", paymentStatus, setPaymentStatus)}
                  />
                  <Label htmlFor="payment-paid">已支付</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="payment-unpaid"
                    checked={paymentStatus.includes("unpaid")}
                    onCheckedChange={() => handleCheckboxChange("unpaid", paymentStatus, setPaymentStatus)}
                  />
                  <Label htmlFor="payment-unpaid">待支付</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="payment-refunded"
                    checked={paymentStatus.includes("refunded")}
                    onCheckedChange={() => handleCheckboxChange("refunded", paymentStatus, setPaymentStatus)}
                  />
                  <Label htmlFor="payment-refunded">已退款</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="payment-failed"
                    checked={paymentStatus.includes("failed")}
                    onCheckedChange={() => handleCheckboxChange("failed", paymentStatus, setPaymentStatus)}
                  />
                  <Label htmlFor="payment-failed">支付失败</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">支付方式</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="method-wechat"
                    checked={paymentMethod.includes("wechat")}
                    onCheckedChange={() => handleCheckboxChange("wechat", paymentMethod, setPaymentMethod)}
                  />
                  <Label htmlFor="method-wechat">微信支付</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="method-alipay"
                    checked={paymentMethod.includes("alipay")}
                    onCheckedChange={() => handleCheckboxChange("alipay", paymentMethod, setPaymentMethod)}
                  />
                  <Label htmlFor="method-alipay">支付宝</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="method-balance"
                    checked={paymentMethod.includes("balance")}
                    onCheckedChange={() => handleCheckboxChange("balance", paymentMethod, setPaymentMethod)}
                  />
                  <Label htmlFor="method-balance">余额支付</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="method-offline"
                    checked={paymentMethod.includes("offline")}
                    onCheckedChange={() => handleCheckboxChange("offline", paymentMethod, setPaymentMethod)}
                  />
                  <Label htmlFor="method-offline">线下支付</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">配送方式</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="delivery-logistics"
                    checked={deliveryMethod.includes("logistics")}
                    onCheckedChange={() => handleCheckboxChange("logistics", deliveryMethod, setDeliveryMethod)}
                  />
                  <Label htmlFor="delivery-logistics">物流配送</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="delivery-self-pickup"
                    checked={deliveryMethod.includes("self_pickup")}
                    onCheckedChange={() => handleCheckboxChange("self_pickup", deliveryMethod, setDeliveryMethod)}
                  />
                  <Label htmlFor="delivery-self-pickup">门店自提</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="delivery-verification"
                    checked={deliveryMethod.includes("verification")}
                    onCheckedChange={() => handleCheckboxChange("verification", deliveryMethod, setDeliveryMethod)}
                  />
                  <Label htmlFor="delivery-verification">核销</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">订单日期</CardTitle>
              </CardHeader>
              <CardContent>
                {/* 暂时注释掉日期范围选择器，等组件创建好后再启用 */}
                {/* <DatePickerWithRange
                  className="w-full"
                  selected={dateRange}
                  onSelect={setDateRange}
                /> */}
                <div className="p-2 border rounded-md text-center text-muted-foreground">
                  日期范围选择器暂不可用
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">订单金额</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <div className="space-y-2 flex-1">
                    <Label htmlFor="min-amount">最小金额</Label>
                    <Input
                      id="min-amount"
                      type="number"
                      placeholder="0.00"
                      value={minAmount}
                      onChange={(e) => setMinAmount(e.target.value)}
                    />
                  </div>
                  <div className="pt-8">至</div>
                  <div className="space-y-2 flex-1">
                    <Label htmlFor="max-amount">最大金额</Label>
                    <Input
                      id="max-amount"
                      type="number"
                      placeholder="不限"
                      value={maxAmount}
                      onChange={(e) => setMaxAmount(e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="product" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center">
                  <ShoppingBag className="h-4 w-4 mr-2 text-blue-500" />
                  商品类型
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="product-physical"
                    checked={productType.includes("physical")}
                    onCheckedChange={() => handleCheckboxChange("physical", productType, setProductType)}
                  />
                  <Label htmlFor="product-physical">实物商品</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="product-virtual"
                    checked={productType.includes("virtual")}
                    onCheckedChange={() => handleCheckboxChange("virtual", productType, setProductType)}
                  />
                  <Label htmlFor="product-virtual">虚拟商品</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-purple-500" />
                  瑜伽课程类型
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="course-beginner"
                    checked={courseType.includes("beginner")}
                    onCheckedChange={() => handleCheckboxChange("beginner", courseType, setCourseType)}
                  />
                  <Label htmlFor="course-beginner">初级课程</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="course-intermediate"
                    checked={courseType.includes("intermediate")}
                    onCheckedChange={() => handleCheckboxChange("intermediate", courseType, setCourseType)}
                  />
                  <Label htmlFor="course-intermediate">中级课程</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="course-advanced"
                    checked={courseType.includes("advanced")}
                    onCheckedChange={() => handleCheckboxChange("advanced", courseType, setCourseType)}
                  />
                  <Label htmlFor="course-advanced">高级课程</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="course-private"
                    checked={courseType.includes("private")}
                    onCheckedChange={() => handleCheckboxChange("private", courseType, setCourseType)}
                  />
                  <Label htmlFor="course-private">私教课程</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="course-online"
                    checked={courseType.includes("online")}
                    onCheckedChange={() => handleCheckboxChange("online", courseType, setCourseType)}
                  />
                  <Label htmlFor="course-online">线上课程</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="course-workshop"
                    checked={courseType.includes("workshop")}
                    onCheckedChange={() => handleCheckboxChange("workshop", courseType, setCourseType)}
                  />
                  <Label htmlFor="course-workshop">工作坊</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center">
                  <CreditCard className="h-4 w-4 mr-2 text-green-500" />
                  会员卡类型
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="membership-monthly"
                    checked={membershipLevel.includes("monthly")}
                    onCheckedChange={() => handleCheckboxChange("monthly", membershipLevel, setMembershipLevel)}
                  />
                  <Label htmlFor="membership-monthly">月卡</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="membership-quarterly"
                    checked={membershipLevel.includes("quarterly")}
                    onCheckedChange={() => handleCheckboxChange("quarterly", membershipLevel, setMembershipLevel)}
                  />
                  <Label htmlFor="membership-quarterly">季卡</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="membership-annual"
                    checked={membershipLevel.includes("annual")}
                    onCheckedChange={() => handleCheckboxChange("annual", membershipLevel, setMembershipLevel)}
                  />
                  <Label htmlFor="membership-annual">年卡</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="membership-times"
                    checked={membershipLevel.includes("times")}
                    onCheckedChange={() => handleCheckboxChange("times", membershipLevel, setMembershipLevel)}
                  />
                  <Label htmlFor="membership-times">次卡</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">商品分类</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-equipment"
                    checked={productCategory.includes("equipment")}
                    onCheckedChange={() => handleCheckboxChange("equipment", productCategory, setProductCategory)}
                  />
                  <Label htmlFor="category-equipment">瑜伽装备</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-apparel"
                    checked={productCategory.includes("apparel")}
                    onCheckedChange={() => handleCheckboxChange("apparel", productCategory, setProductCategory)}
                  />
                  <Label htmlFor="category-apparel">瑜伽服饰</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-accessories"
                    checked={productCategory.includes("accessories")}
                    onCheckedChange={() => handleCheckboxChange("accessories", productCategory, setProductCategory)}
                  />
                  <Label htmlFor="category-accessories">瑜伽配件</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="category-nutrition"
                    checked={productCategory.includes("nutrition")}
                    onCheckedChange={() => handleCheckboxChange("nutrition", productCategory, setProductCategory)}
                  />
                  <Label htmlFor="category-nutrition">营养补剂</Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="customer" className="space-y-4 mt-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">客户类型</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="customer-member"
                    checked={customerType.includes("member")}
                    onCheckedChange={() => handleCheckboxChange("member", customerType, setCustomerType)}
                  />
                  <Label htmlFor="customer-member">会员</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="customer-guest"
                    checked={customerType.includes("guest")}
                    onCheckedChange={() => handleCheckboxChange("guest", customerType, setCustomerType)}
                  />
                  <Label htmlFor="customer-guest">游客</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="customer-vip"
                    checked={customerType.includes("vip")}
                    onCheckedChange={() => handleCheckboxChange("vip", customerType, setCustomerType)}
                  />
                  <Label htmlFor="customer-vip">VIP</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="customer-new"
                    checked={customerType.includes("new")}
                    onCheckedChange={() => handleCheckboxChange("new", customerType, setCustomerType)}
                  />
                  <Label htmlFor="customer-new">新客户</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">订单来源</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="source-website"
                    checked={source.includes("website")}
                    onCheckedChange={() => handleCheckboxChange("website", source, setSource)}
                  />
                  <Label htmlFor="source-website">官网</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="source-wechat"
                    checked={source.includes("wechat")}
                    onCheckedChange={() => handleCheckboxChange("wechat", source, setSource)}
                  />
                  <Label htmlFor="source-wechat">微信小程序</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="source-app"
                    checked={source.includes("app")}
                    onCheckedChange={() => handleCheckboxChange("app", source, setSource)}
                  />
                  <Label htmlFor="source-app">APP</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="source-store"
                    checked={source.includes("store")}
                    onCheckedChange={() => handleCheckboxChange("store", source, setSource)}
                  />
                  <Label htmlFor="source-store">门店</Label>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">瑜伽馆</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="studio-chaoyang"
                    checked={yogaStudio.includes("chaoyang")}
                    onCheckedChange={() => handleCheckboxChange("chaoyang", yogaStudio, setYogaStudio)}
                  />
                  <Label htmlFor="studio-chaoyang">朝阳店</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="studio-haidian"
                    checked={yogaStudio.includes("haidian")}
                    onCheckedChange={() => handleCheckboxChange("haidian", yogaStudio, setYogaStudio)}
                  />
                  <Label htmlFor="studio-haidian">海淀店</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="studio-dongcheng"
                    checked={yogaStudio.includes("dongcheng")}
                    onCheckedChange={() => handleCheckboxChange("dongcheng", yogaStudio, setYogaStudio)}
                  />
                  <Label htmlFor="studio-dongcheng">东城店</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="studio-xicheng"
                    checked={yogaStudio.includes("xicheng")}
                    onCheckedChange={() => handleCheckboxChange("xicheng", yogaStudio, setYogaStudio)}
                  />
                  <Label htmlFor="studio-xicheng">西城店</Label>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex items-center justify-between mt-4">
          <Button variant="outline" onClick={handleResetFilters}>
            <X className="h-4 w-4 mr-2" />
            重置筛选
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleApplyFilters}>
              应用筛选
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
