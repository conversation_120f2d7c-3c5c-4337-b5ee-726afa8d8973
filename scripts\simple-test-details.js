// 简单测试课程类型详情API
const fetch = require('node-fetch');

async function testDetails() {
  try {
    console.log('测试课程类型详情API...');
    
    // 测试一个已知的课程类型ID
    const response = await fetch('http://localhost:3005/api/course-types/1/stats');
    const result = await response.json();
    
    console.log('API响应:', JSON.stringify(result, null, 2));
    
    if (result.code === 200) {
      console.log('✓ 详情API工作正常');
      console.log(`课程类型: ${result.data.courseType.name}`);
      console.log(`关联课程: ${result.data.courses.length} 个`);
      console.log(`总预约: ${result.data.stats.totalBookings} 次`);
    } else {
      console.log('✗ API返回错误:', result.msg);
    }
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testDetails();
