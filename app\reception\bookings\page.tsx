"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { format, addDays, subDays } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Search,
  ArrowLeft,
  Plus,
  Calendar,
  Clock,
  Users,
  CheckCircle2,
  XCircle,
  Filter,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"

// 模拟课程数据
const courseData = [
  {
    id: "c1",
    name: "空中瑜伽",
    time: "09:00-10:30",
    coach: "刘教练",
    room: "空中教室",
    capacity: 10,
    booked: 8,
    status: "upcoming", // upcoming, ongoing, completed
  },
  {
    id: "c2",
    name: "哈他瑜伽",
    time: "11:00-12:30",
    coach: "王教练",
    room: "大教室",
    capacity: 15,
    booked: 12,
    status: "upcoming",
  },
  {
    id: "c3",
    name: "普拉提",
    time: "14:00-15:00",
    coach: "张教练",
    room: "小教室",
    capacity: 8,
    booked: 8,
    status: "upcoming",
  },
  {
    id: "c4",
    name: "流瑜伽",
    time: "16:00-17:30",
    coach: "李教练",
    room: "大教室",
    capacity: 15,
    booked: 10,
    status: "upcoming",
  },
  {
    id: "c5",
    name: "阴瑜伽",
    time: "18:30-20:00",
    coach: "赵教练",
    room: "大教室",
    capacity: 15,
    booked: 7,
    status: "upcoming",
  }
]

// 模拟预约数据
const bookingData = [
  {
    id: "b1",
    courseId: "c1",
    memberId: "m1",
    memberName: "张三",
    memberAvatar: "/avatars/01.png",
    phone: "13800138001",
    bookingTime: "2023-05-15 14:30:22",
    status: "confirmed", // confirmed, checked-in, cancelled, no-show
    notes: ""
  },
  {
    id: "b2",
    courseId: "c1",
    memberId: "m2",
    memberName: "李四",
    memberAvatar: "/avatars/02.png",
    phone: "13800138002",
    bookingTime: "2023-05-15 15:10:45",
    status: "confirmed",
    notes: ""
  },
  {
    id: "b3",
    courseId: "c2",
    memberId: "m3",
    memberName: "王五",
    memberAvatar: "/avatars/03.png",
    phone: "13800138003",
    bookingTime: "2023-05-15 16:20:18",
    status: "confirmed",
    notes: "需要准备额外的瑜伽垫"
  },
  {
    id: "b4",
    courseId: "c3",
    memberId: "m4",
    memberName: "赵六",
    memberAvatar: "/avatars/04.png",
    phone: "13800138004",
    bookingTime: "2023-05-15 17:05:33",
    status: "confirmed",
    notes: ""
  },
  {
    id: "b5",
    courseId: "c3",
    memberId: "m5",
    memberName: "孙七",
    memberAvatar: "/avatars/05.png",
    phone: "13800138005",
    bookingTime: "2023-05-15 18:15:10",
    status: "confirmed",
    notes: ""
  }
]

// 模拟排队数据
const queueData = [
  {
    id: "q1",
    courseId: "c3",
    memberId: "m6",
    memberName: "周八",
    memberAvatar: "/avatars/06.png",
    phone: "13800138006",
    queueTime: "2023-05-15 19:30:22",
    position: 1,
    status: "waiting", // waiting, notified, cancelled
    notes: ""
  },
  {
    id: "q2",
    courseId: "c3",
    memberId: "m7",
    memberName: "吴九",
    memberAvatar: "/avatars/07.png",
    phone: "13800138007",
    queueTime: "2023-05-15 20:15:45",
    position: 2,
    status: "waiting",
    notes: ""
  },
  {
    id: "q3",
    courseId: "c1",
    memberId: "m8",
    memberName: "郑十",
    memberAvatar: "/avatars/08.png",
    phone: "13800138008",
    queueTime: "2023-05-15 21:10:18",
    position: 1,
    status: "notified",
    notes: "已通知，等待确认"
  }
]

export default function BookingsPage() {
  const router = useRouter()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [showAddBookingDialog, setShowAddBookingDialog] = useState(false)
  const [showBookingDetailDialog, setShowBookingDetailDialog] = useState(false)
  const [selectedBooking, setSelectedBooking] = useState<any>(null)
  const [selectedCourse, setSelectedCourse] = useState<any>(null)

  // 日期导航
  const goToPreviousDay = () => {
    setCurrentDate(prev => subDays(prev, 1))
  }

  const goToNextDay = () => {
    setCurrentDate(prev => addDays(prev, 1))
  }

  const goToToday = () => {
    setCurrentDate(new Date())
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/reception">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">课程预约管理</h1>
      </div>

      {/* 日期导航 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={goToPreviousDay}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-muted-foreground" />
            <span className="text-lg font-medium">
              {format(currentDate, "yyyy-MM-dd", { locale: zhCN })}
            </span>
          </div>
          <Button variant="outline" size="icon" onClick={goToNextDay}>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button variant="outline" onClick={goToToday}>
            返回今天
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => setShowAddBookingDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            添加预约
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList className="grid w-full max-w-md grid-cols-4">
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="upcoming">即将开始</TabsTrigger>
            <TabsTrigger value="completed">已完成</TabsTrigger>
            <TabsTrigger value="queue">排队列表</TabsTrigger>
          </TabsList>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索会员姓名或课程..."
                className="pl-8 w-[250px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <TabsContent value="all" className="space-y-4">
          {courseData.map((course) => (
            <Card key={course.id} className="overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{course.name}</CardTitle>
                    <CardDescription>
                      {course.time} | {course.room} | 教练: {course.coach}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={course.booked >= course.capacity ? "destructive" : "default"}>
                      {course.booked}/{course.capacity}
                    </Badge>
                    <Button variant="outline" size="sm" onClick={() => setSelectedCourse(course)}>
                      查看预约
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="border-t">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>会员信息</TableHead>
                        <TableHead>预约时间</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>备注</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {bookingData
                        .filter(booking => booking.courseId === course.id)
                        .map((booking) => (
                          <TableRow key={booking.id}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <Avatar>
                                  <AvatarImage src={booking.memberAvatar} alt={booking.memberName} />
                                  <AvatarFallback>{booking.memberName[0]}</AvatarFallback>
                                </Avatar>
                                <div>
                                  <div className="font-medium">{booking.memberName}</div>
                                  <div className="text-xs text-muted-foreground">{booking.phone}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">{booking.bookingTime}</div>
                            </TableCell>
                            <TableCell>
                              <Badge variant={
                                booking.status === "confirmed" ? "outline" :
                                booking.status === "checked-in" ? "success" :
                                booking.status === "cancelled" ? "destructive" :
                                "secondary"
                              }>
                                {booking.status === "confirmed" ? "已确认" :
                                 booking.status === "checked-in" ? "已签到" :
                                 booking.status === "cancelled" ? "已取消" :
                                 booking.status === "no-show" ? "爽约" : "未知"}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="max-w-[200px] truncate text-sm">
                                {booking.notes || "-"}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>操作</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => {
                                    setSelectedBooking(booking);
                                    setShowBookingDetailDialog(true);
                                  }}>
                                    查看详情
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    签到
                                  </DropdownMenuItem>
                                  <DropdownMenuItem>
                                    取消预约
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                      {bookingData.filter(booking => booking.courseId === course.id).length === 0 && (
                        <TableRow>
                          <TableCell colSpan={5} className="h-24 text-center">
                            暂无预约记录
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>即将开始的课程</CardTitle>
              <CardDescription>
                查看即将开始的课程预约
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <Clock className="h-12 w-12 mx-auto mb-4 text-primary/40" />
                <p className="text-lg">即将开始的课程功能正在开发中</p>
                <p className="text-sm">即将完成预约提醒和签到功能</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>已完成的课程</CardTitle>
              <CardDescription>
                查看已完成的课程记录
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <CheckCircle2 className="h-12 w-12 mx-auto mb-4 text-primary/40" />
                <p className="text-lg">已完成课程功能正在开发中</p>
                <p className="text-sm">即将完成课程记录和统计功能</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="queue" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>课程排队列表</CardTitle>
              <CardDescription>
                管理课程排队和候补情况
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>会员信息</TableHead>
                    <TableHead>课程信息</TableHead>
                    <TableHead>排队时间</TableHead>
                    <TableHead>排队位置</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {queueData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        暂无排队记录
                      </TableCell>
                    </TableRow>
                  ) : (
                    queueData.map((queue) => {
                      const course = courseData.find(c => c.id === queue.courseId);
                      return (
                        <TableRow key={queue.id}>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Avatar>
                                <AvatarImage src={queue.memberAvatar} alt={queue.memberName} />
                                <AvatarFallback>{queue.memberName[0]}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">{queue.memberName}</div>
                                <div className="text-xs text-muted-foreground">{queue.phone}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{course?.name}</div>
                            <div className="text-xs text-muted-foreground">{course?.time} | {course?.room}</div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">{queue.queueTime}</div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              第 {queue.position} 位
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              queue.status === "waiting" ? "outline" :
                              queue.status === "notified" ? "success" :
                              "destructive"
                            }>
                              {queue.status === "waiting" ? "等待中" :
                               queue.status === "notified" ? "已通知" :
                               queue.status === "cancelled" ? "已取消" : "未知"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="outline" size="sm">
                                通知
                              </Button>
                              <Button variant="outline" size="sm">
                                取消排队
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 预约详情对话框 */}
      {selectedBooking && (
        <Dialog open={showBookingDetailDialog} onOpenChange={setShowBookingDetailDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>预约详情</DialogTitle>
              <DialogDescription>
                查看会员课程预约详情
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="flex items-center gap-4 pb-4 border-b">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={selectedBooking.memberAvatar} alt={selectedBooking.memberName} />
                  <AvatarFallback>{selectedBooking.memberName[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium text-lg">{selectedBooking.memberName}</div>
                  <div className="text-sm text-muted-foreground">{selectedBooking.phone}</div>
                </div>
                <div className="ml-auto">
                  <Badge variant={
                    selectedBooking.status === "confirmed" ? "outline" :
                    selectedBooking.status === "checked-in" ? "success" :
                    selectedBooking.status === "cancelled" ? "destructive" :
                    "secondary"
                  }>
                    {selectedBooking.status === "confirmed" ? "已确认" :
                     selectedBooking.status === "checked-in" ? "已签到" :
                     selectedBooking.status === "cancelled" ? "已取消" :
                     selectedBooking.status === "no-show" ? "爽约" : "未知"}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium">课程信息</div>
                <div className="p-3 rounded-md bg-muted/50">
                  {(() => {
                    const course = courseData.find(c => c.id === selectedBooking.courseId);
                    return (
                      <>
                        <div className="font-medium">{course?.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {course?.time} | {course?.room} | 教练: {course?.coach}
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-muted-foreground">预约时间</div>
                  <div className="font-medium">{selectedBooking.bookingTime}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">预约状态</div>
                  <div className="font-medium">
                    {selectedBooking.status === "confirmed" ? "已确认" :
                     selectedBooking.status === "checked-in" ? "已签到" :
                     selectedBooking.status === "cancelled" ? "已取消" :
                     selectedBooking.status === "no-show" ? "爽约" : "未知"}
                  </div>
                </div>
              </div>

              {selectedBooking.notes && (
                <div>
                  <div className="text-sm text-muted-foreground mb-2">备注</div>
                  <div className="p-3 rounded-md bg-muted/50">
                    {selectedBooking.notes}
                  </div>
                </div>
              )}
            </div>
            <DialogFooter className="flex justify-between">
              <Button variant="outline" className="text-red-600" onClick={() => {
                toast.success("预约已取消");
                setShowBookingDetailDialog(false);
              }}>
                取消预约
              </Button>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setShowBookingDetailDialog(false)}>关闭</Button>
                <Button onClick={() => {
                  toast.success("会员已签到");
                  setShowBookingDetailDialog(false);
                }}>
                  签到
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 添加预约对话框 */}
      <Dialog open={showAddBookingDialog} onOpenChange={setShowAddBookingDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>添加课程预约</DialogTitle>
            <DialogDescription>
              为会员添加新的课程预约
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="member-name">会员姓名</Label>
                <Input id="member-name" placeholder="输入会员姓名" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="member-phone">手机号码</Label>
                <Input id="member-phone" placeholder="输入手机号码" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="course-select">选择课程</Label>
              <select
                id="course-select"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="" disabled selected>请选择课程</option>
                {courseData.map(course => (
                  <option key={course.id} value={course.id} disabled={course.booked >= course.capacity}>
                    {course.name} ({course.time}) - {course.booked}/{course.capacity}
                  </option>
                ))}
              </select>
              <div className="text-xs text-muted-foreground">
                已满的课程无法选择，可以选择加入排队列表
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="booking-notes">备注</Label>
                <span className="text-xs text-muted-foreground">选填</span>
              </div>
              <textarea
                id="booking-notes"
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="添加预约备注信息..."
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox id="add-to-queue" />
              <label
                htmlFor="add-to-queue"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                如果课程已满，加入排队列表
              </label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddBookingDialog(false)}>取消</Button>
            <Button onClick={() => {
              toast.success("预约添加成功");
              setShowAddBookingDialog(false);
            }}>
              确认预约
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
