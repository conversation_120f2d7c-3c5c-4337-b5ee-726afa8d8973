"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  BarChart2,
  Calendar,
  Download,
  Edit,
  Eye,
  Filter,
  MoreHorizontal,
  Plus,
  Search,
  StopCircle,
  Trash,
  Users,
  XCircle,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/components/ui/use-toast"
import { useState } from "react"

export default function GroupBuyingPage() {
  const { toast } = useToast()

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [endConfirmOpen, setEndConfirmOpen] = useState(false)
  const [cancelConfirmOpen, setCancelConfirmOpen] = useState(false)
  const [selectedGroup, setSelectedGroup] = useState<any>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)
  const [editOpen, setEditOpen] = useState(false)
  const [recordsOpen, setRecordsOpen] = useState(false)
  const [analysisOpen, setAnalysisOpen] = useState(false)

  const handleViewDetails = (group: any) => {
    setSelectedGroup(group)
    setDetailsOpen(true)
  }

  const handleEditGroup = (group: any) => {
    setSelectedGroup(group)
    setEditOpen(true)
  }

  const handleViewGroupRecords = (group: any) => {
    setSelectedGroup(group)
    setRecordsOpen(true)
  }

  const handleDataAnalysis = (group: any) => {
    setSelectedGroup(group)
    setAnalysisOpen(true)
  }

  const handleDeleteGroup = (group: any) => {
    setSelectedGroup(group)
    setDeleteConfirmOpen(true)
  }

  const handleEndGroup = (group: any) => {
    setSelectedGroup(group)
    setEndConfirmOpen(true)
  }

  const handleCancelGroup = (group: any) => {
    setSelectedGroup(group)
    setCancelConfirmOpen(true)
  }

  const confirmDelete = () => {
    // 实际删除操作会调用API
    console.log("删除拼团活动:", selectedGroup?.id)
    // 模拟删除成功
    toast({
      title: "删除成功",
      description: `已删除拼团活动"${selectedGroup?.name}"`,
    })
    setDeleteConfirmOpen(false)
  }

  const confirmEndGroup = () => {
    // 实际结束活动操作会调用API
    console.log("结束拼团活动:", selectedGroup?.id)
    // 模拟操作成功
    toast({
      title: "操作成功",
      description: `已结束拼团活动"${selectedGroup?.name}"`,
    })
    setEndConfirmOpen(false)
  }

  const confirmCancelGroup = () => {
    // 实际取消活动操作会调用API
    console.log("取消拼团活动:", selectedGroup?.id)
    // 模拟操作成功
    toast({
      title: "操作成功",
      description: `已取消拼团活动"${selectedGroup?.name}"`,
    })
    setCancelConfirmOpen(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">拼团活动</h1>
          <p className="text-muted-foreground">创建和管理拼团活动，提高课程销量和会员互动</p>
        </div>
        <div className="flex items-center gap-2">
          <Dialog>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建拼团
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>创建拼团活动</DialogTitle>
                <DialogDescription>设置拼团活动的商品、规则和时间</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="group-name" className="text-sm font-medium">
                      活动名称
                    </label>
                    <Input id="group-name" placeholder="例如：三人成团·高级瑜伽课" />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="product-type" className="text-sm font-medium">
                      商品类型
                    </label>
                    <Select>
                      <SelectTrigger id="product-type">
                        <SelectValue placeholder="选择类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="course">课程</SelectItem>
                        <SelectItem value="membership">会员卡</SelectItem>
                        <SelectItem value="product">实物商品</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="product-select" className="text-sm font-medium">
                    选择商品
                  </label>
                  <Select>
                    <SelectTrigger id="product-select">
                      <SelectValue placeholder="选择商品" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="yoga-advanced">高级瑜伽课</SelectItem>
                      <SelectItem value="yoga-beginner">初级瑜伽课</SelectItem>
                      <SelectItem value="pilates-advanced">高级普拉提课</SelectItem>
                      <SelectItem value="pilates-beginner">初级普拉提课</SelectItem>
                      <SelectItem value="fitness">健身课程</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="original-price" className="text-sm font-medium">
                      原价
                    </label>
                    <Input id="original-price" placeholder="例如：300" />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="group-price" className="text-sm font-medium">
                      拼团价
                    </label>
                    <Input id="group-price" placeholder="例如：199" />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="group-size" className="text-sm font-medium">
                      成团人数
                    </label>
                    <Input id="group-size" placeholder="例如：3" />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="group-limit" className="text-sm font-medium">
                      团数限制
                    </label>
                    <Input id="group-limit" placeholder="例如：50或不限" />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">活动时间</label>
                    <div className="flex gap-2">
                      <Button variant="outline" size="icon" className="h-10 w-10">
                        <Calendar className="h-4 w-4" />
                      </Button>
                      <Input placeholder="开始日期" />
                      <Input placeholder="结束日期" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="duration" className="text-sm font-medium">
                      开团时限
                    </label>
                    <div className="flex gap-2 items-center">
                      <Input id="duration" placeholder="例如：24" />
                      <span>小时</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="description" className="text-sm font-medium">
                    活动说明
                  </label>
                  <Input id="description" placeholder="活动规则和说明" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline">取消</Button>
                <Button>创建拼团</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出数据
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="搜索拼团活动..." className="pl-8" />
        </div>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="状态筛选" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="active">进行中</SelectItem>
            <SelectItem value="upcoming">未开始</SelectItem>
            <SelectItem value="ended">已结束</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">全部拼团</TabsTrigger>
          <TabsTrigger value="active">进行中</TabsTrigger>
          <TabsTrigger value="groups">拼团记录</TabsTrigger>
          <TabsTrigger value="analysis">数据分析</TabsTrigger>
        </TabsList>
        <TabsContent value="all" className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>活动名称</TableHead>
                    <TableHead>商品</TableHead>
                    <TableHead>拼团价</TableHead>
                    <TableHead>成团规则</TableHead>
                    <TableHead>活动时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {groupBuyings.map((group) => (
                    <TableRow key={group.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <Users className="mr-2 h-4 w-4 text-blue-500" />
                          {group.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{group.product}</span>
                          <span className="text-xs text-muted-foreground">原价: ¥{group.originalPrice}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium text-blue-500">¥{group.groupPrice}</TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-xs">{group.groupSize}人成团</span>
                          <span className="text-xs">{group.duration}小时内</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="text-xs">{group.startDate}</span>
                          <span className="text-xs">至</span>
                          <span className="text-xs">{group.endDate}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(group.status)}>{getStatusName(group.status)}</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetails(group)}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditGroup(group)}>
                              <Edit className="mr-2 h-4 w-4" />
                              编辑
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleViewGroupRecords(group)}>
                              <Users className="mr-2 h-4 w-4" />
                              查看拼团记录
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDataAnalysis(group)}>
                              <BarChart2 className="mr-2 h-4 w-4" />
                              数据分析
                            </DropdownMenuItem>
                            {group.status === "upcoming" && (
                              <DropdownMenuItem onClick={() => handleCancelGroup(group)}>
                                <XCircle className="mr-2 h-4 w-4" />
                                取消活动
                              </DropdownMenuItem>
                            )}
                            {group.status === "active" && (
                              <DropdownMenuItem onClick={() => handleEndGroup(group)}>
                                <StopCircle className="mr-2 h-4 w-4" />
                                结束活动
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              className="text-destructive focus:bg-destructive/10"
                              onClick={() => handleDeleteGroup(group)}
                            >
                              <Trash className="mr-2 h-4 w-4" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="active" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>进行中的拼团</CardTitle>
              <CardDescription>当前正在进行的拼团活动</CardDescription>
            </CardHeader>
            <CardContent>{/* 进行中的拼团内容 */}</CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="groups" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>拼团记录</CardTitle>
              <CardDescription>查看所有拼团记录和状态</CardDescription>
            </CardHeader>
            <CardContent>{/* 拼团记录内容 */}</CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="analysis" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>拼团数据分析</CardTitle>
              <CardDescription>查看拼团活动的数据分析和转化率</CardDescription>
            </CardHeader>
            <CardContent>{/* 数据分析内容 */}</CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>热门拼团活动</CardTitle>
            <CardDescription>参与人数最多的拼团活动</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {hotGroupBuyings.map((group, index) => (
                <div key={group.id} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
                  <div className="flex items-center">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600 mr-3">
                      {index + 1}
                    </div>
                    <div className="flex flex-col">
                      <span className="font-medium">{group.name}</span>
                      <span className="text-sm text-muted-foreground">{group.product}</span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <div className="flex items-center">
                      <span className="text-sm line-through text-muted-foreground mr-2">¥{group.originalPrice}</span>
                      <span className="font-medium text-blue-500">¥{group.groupPrice}</span>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground mt-1">
                      <Users className="mr-1 h-3 w-3" />
                      <span>
                        {group.groupCount}个团 / {group.successRate}成功率
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>拼团数据概览</CardTitle>
            <CardDescription>拼团活动的关键指标</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">总活动数</span>
                <div className="text-2xl font-bold">8</div>
              </div>
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">总销售额</span>
                <div className="text-2xl font-bold">¥42,680</div>
              </div>
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">成团率</span>
                <div className="text-2xl font-bold">76.2%</div>
              </div>
              <div className="space-y-1">
                <span className="text-sm text-muted-foreground">平均拼团人数</span>
                <div className="text-2xl font-bold">3.4人</div>
              </div>
            </div>

            <div className="mt-6">
              <h4 className="text-sm font-medium mb-2">拼团转化率</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">浏览-参团转化率</span>
                  <span className="text-sm font-medium">32.5%</span>
                </div>
                <Progress value={32.5} className="h-2" />

                <div className="flex items-center justify-between">
                  <span className="text-sm">开团-成团转化率</span>
                  <span className="text-sm font-medium">76.2%</span>
                </div>
                <Progress value={76.2} className="h-2" />

                <div className="flex items-center justify-between">
                  <span className="text-sm">新客获取率</span>
                  <span className="text-sm font-medium">45.8%</span>
                </div>
                <Progress value={45.8} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      {/* 删除确认对话框 */}
      <Dialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>您确定要删除拼团活动 "{selectedGroup?.name}" 吗？此操作无法撤销。</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteConfirmOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 结束活动确认对话框 */}
      <Dialog open={endConfirmOpen} onOpenChange={setEndConfirmOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>确认结束活动</DialogTitle>
            <DialogDescription>
              您确定要结束拼团活动 "{selectedGroup?.name}" 吗？结束后，用户将无法继续参与此拼团。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEndConfirmOpen(false)}>
              取消
            </Button>
            <Button onClick={confirmEndGroup}>确认结束</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 取消活动确认对话框 */}
      <Dialog open={cancelConfirmOpen} onOpenChange={setCancelConfirmOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>确认取消活动</DialogTitle>
            <DialogDescription>
              您确定要取消拼团活动 "{selectedGroup?.name}" 吗？取消后，此活动将不会开始。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCancelConfirmOpen(false)}>
              取消
            </Button>
            <Button onClick={confirmCancelGroup}>确认取消</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 查看详情对话框 */}
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>拼团活动详情</DialogTitle>
            <DialogDescription>查看拼团活动的详细信息</DialogDescription>
          </DialogHeader>
          {selectedGroup && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">活动名称</h3>
                  <p className="text-base">{selectedGroup.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">商品</h3>
                  <p className="text-base">{selectedGroup.product}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">原价</h3>
                  <p className="text-base">¥{selectedGroup.originalPrice}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">拼团价</h3>
                  <p className="text-base font-medium text-blue-500">¥{selectedGroup.groupPrice}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">成团规则</h3>
                  <p className="text-base">
                    {selectedGroup.groupSize}人成团 / {selectedGroup.duration}小时内
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">状态</h3>
                  <Badge variant={getStatusBadgeVariant(selectedGroup.status)}>
                    {getStatusName(selectedGroup.status)}
                  </Badge>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">活动时间</h3>
                <p className="text-base">
                  {selectedGroup.startDate} 至 {selectedGroup.endDate}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">活动数据</h3>
                <div className="grid grid-cols-3 gap-4 mt-2">
                  <div className="bg-muted rounded-md p-3">
                    <p className="text-sm text-muted-foreground">已开团数</p>
                    <p className="text-lg font-medium">24</p>
                  </div>
                  <div className="bg-muted rounded-md p-3">
                    <p className="text-sm text-muted-foreground">成团数</p>
                    <p className="text-lg font-medium">18</p>
                  </div>
                  <div className="bg-muted rounded-md p-3">
                    <p className="text-sm text-muted-foreground">成团率</p>
                    <p className="text-lg font-medium">75%</p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setDetailsOpen(false)}>
              关闭
            </Button>
            <Button
              onClick={() => {
                setDetailsOpen(false)
                handleEditGroup(selectedGroup)
              }}
            >
              编辑活动
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑拼团对话框 */}
      <Dialog open={editOpen} onOpenChange={setEditOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>编辑拼团活动</DialogTitle>
            <DialogDescription>修改拼团活动的信息</DialogDescription>
          </DialogHeader>
          {selectedGroup && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="edit-group-name" className="text-sm font-medium">
                    活动名称
                  </label>
                  <Input id="edit-group-name" defaultValue={selectedGroup.name} />
                </div>
                <div className="space-y-2">
                  <label htmlFor="edit-product-type" className="text-sm font-medium">
                    商品类型
                  </label>
                  <Select defaultValue="course">
                    <SelectTrigger id="edit-product-type">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="course">课程</SelectItem>
                      <SelectItem value="membership">会员卡</SelectItem>
                      <SelectItem value="product">实物商品</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="edit-product-select" className="text-sm font-medium">
                  选择商品
                </label>
                <Select defaultValue="yoga-advanced">
                  <SelectTrigger id="edit-product-select">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yoga-advanced">高级瑜伽课</SelectItem>
                    <SelectItem value="yoga-beginner">初级瑜伽课</SelectItem>
                    <SelectItem value="pilates-advanced">高级普拉提课</SelectItem>
                    <SelectItem value="pilates-beginner">初级普拉提课</SelectItem>
                    <SelectItem value="fitness">健身课程</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="edit-original-price" className="text-sm font-medium">
                    原价
                  </label>
                  <Input id="edit-original-price" defaultValue={selectedGroup.originalPrice} />
                </div>
                <div className="space-y-2">
                  <label htmlFor="edit-group-price" className="text-sm font-medium">
                    拼团价
                  </label>
                  <Input id="edit-group-price" defaultValue={selectedGroup.groupPrice} />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="edit-group-size" className="text-sm font-medium">
                    成团人数
                  </label>
                  <Input id="edit-group-size" defaultValue={selectedGroup.groupSize} />
                </div>
                <div className="space-y-2">
                  <label htmlFor="edit-duration" className="text-sm font-medium">
                    开团时限
                  </label>
                  <div className="flex gap-2 items-center">
                    <Input id="edit-duration" defaultValue={selectedGroup.duration} />
                    <span>小时</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">活动时间</label>
                  <div className="flex gap-2">
                    <Button variant="outline" size="icon" className="h-10 w-10">
                      <Calendar className="h-4 w-4" />
                    </Button>
                    <Input placeholder="开始日期" defaultValue={selectedGroup.startDate} />
                    <Input placeholder="结束日期" defaultValue={selectedGroup.endDate} />
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditOpen(false)}>
              取消
            </Button>
            <Button
              onClick={() => {
                toast({
                  title: "保存成功",
                  description: "拼团活动信息已更新",
                })
                setEditOpen(false)
              }}
            >
              保存修改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 查看拼团记录对话框 */}
      <Dialog open={recordsOpen} onOpenChange={setRecordsOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>拼团记录</DialogTitle>
            <DialogDescription>"{selectedGroup?.name}" 的拼团记录</DialogDescription>
          </DialogHeader>
          {selectedGroup && (
            <div className="py-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>团ID</TableHead>
                    <TableHead>团长</TableHead>
                    <TableHead>参团人数</TableHead>
                    <TableHead>开团时间</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[
                    { id: "G001", leader: "张三", members: 3, time: "2025-04-10 14:30", status: "success" },
                    { id: "G002", leader: "李四", members: 2, time: "2025-04-10 16:45", status: "pending" },
                    { id: "G003", leader: "王五", members: 3, time: "2025-04-11 09:15", status: "success" },
                    { id: "G004", leader: "赵六", members: 1, time: "2025-04-11 11:30", status: "failed" },
                  ].map((group) => (
                    <TableRow key={group.id}>
                      <TableCell className="font-medium">{group.id}</TableCell>
                      <TableCell>{group.leader}</TableCell>
                      <TableCell>
                        {group.members}/{selectedGroup.groupSize}
                      </TableCell>
                      <TableCell>{group.time}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            group.status === "success"
                              ? "success"
                              : group.status === "pending"
                                ? "secondary"
                                : "destructive"
                          }
                        >
                          {group.status === "success" ? "已成团" : group.status === "pending" ? "拼团中" : "未成团"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          详情
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setRecordsOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 数据分析对话框 */}
      <Dialog open={analysisOpen} onOpenChange={setAnalysisOpen}>
        <DialogContent className="sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>拼团数据分析</DialogTitle>
            <DialogDescription>"{selectedGroup?.name}" 的数据分析</DialogDescription>
          </DialogHeader>
          {selectedGroup && (
            <div className="py-4 space-y-6">
              <div className="grid grid-cols-4 gap-4">
                <div className="bg-muted rounded-md p-3">
                  <p className="text-sm text-muted-foreground">浏览量</p>
                  <p className="text-lg font-medium">1,245</p>
                </div>
                <div className="bg-muted rounded-md p-3">
                  <p className="text-sm text-muted-foreground">参团人数</p>
                  <p className="text-lg font-medium">86</p>
                </div>
                <div className="bg-muted rounded-md p-3">
                  <p className="text-sm text-muted-foreground">成团数</p>
                  <p className="text-lg font-medium">18</p>
                </div>
                <div className="bg-muted rounded-md p-3">
                  <p className="text-sm text-muted-foreground">销售额</p>
                  <p className="text-lg font-medium">¥{18 * selectedGroup.groupSize * selectedGroup.groupPrice}</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">转化率</h3>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>浏览-参团转化率</span>
                      <span>6.9%</span>
                    </div>
                    <Progress value={6.9} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>开团-成团转化率</span>
                      <span>75%</span>
                    </div>
                    <Progress value={75} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>新客获取率</span>
                      <span>42.3%</span>
                    </div>
                    <Progress value={42.3} className="h-2" />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-2">参团用户分析</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="border rounded-md p-4">
                    <h4 className="text-sm font-medium mb-2">用户来源</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>微信小程序</span>
                        <span>45%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>公众号</span>
                        <span>30%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>APP</span>
                        <span>15%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>其他</span>
                        <span>10%</span>
                      </div>
                    </div>
                  </div>
                  <div className="border rounded-md p-4">
                    <h4 className="text-sm font-medium mb-2">用户类型</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>新用户</span>
                        <span>42%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>老用户</span>
                        <span>58%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setAnalysisOpen(false)}>
              关闭
            </Button>
            <Button
              onClick={() => {
                // 导出数据分析报告
                toast({
                  title: "导出成功",
                  description: "数据分析报告已导出",
                })
                setAnalysisOpen(false)
              }}
            >
              <Download className="mr-2 h-4 w-4" />
              导出报告
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// 模拟数据
const groupBuyings = [
  {
    id: 1,
    name: "三人成团·高级瑜伽课",
    product: "高级瑜伽课",
    originalPrice: 300,
    groupPrice: 199,
    groupSize: 3,
    duration: 24,
    startDate: "2025-04-10",
    endDate: "2025-05-10",
    status: "active",
  },
  {
    id: 2,
    name: "双人特惠·普拉提课",
    product: "普拉提课",
    originalPrice: 280,
    groupPrice: 199,
    groupSize: 2,
    duration: 48,
    startDate: "2025-04-15",
    endDate: "2025-05-15",
    status: "active",
  },
  {
    id: 3,
    name: "五人团·年卡特惠",
    product: "瑜伽年卡",
    originalPrice: 3600,
    groupPrice: 2799,
    groupSize: 5,
    duration: 72,
    startDate: "2025-05-01",
    endDate: "2025-05-31",
    status: "upcoming",
  },
  {
    id: 4,
    name: "亲子瑜伽·双人团",
    product: "亲子瑜伽课",
    originalPrice: 400,
    groupPrice: 299,
    groupSize: 2,
    duration: 24,
    startDate: "2025-03-01",
    endDate: "2025-03-31",
    status: "ended",
  },
  {
    id: 5,
    name: "四人团·瑜伽垫套装",
    product: "瑜伽垫套装",
    originalPrice: 500,
    groupPrice: 359,
    groupSize: 4,
    duration: 48,
    startDate: "2025-02-15",
    endDate: "2025-03-15",
    status: "ended",
  },
]

const hotGroupBuyings = [
  {
    id: 1,
    name: "三人成团·高级瑜伽课",
    product: "高级瑜伽课",
    originalPrice: 300,
    groupPrice: 199,
    groupCount: 28,
    successRate: "85%",
  },
  {
    id: 2,
    name: "双人特惠·普拉提课",
    product: "普拉提课",
    originalPrice: 280,
    groupPrice: 199,
    groupCount: 22,
    successRate: "78%",
  },
  {
    id: 3,
    name: "亲子瑜伽·双人团",
    product: "亲子瑜伽课",
    originalPrice: 400,
    groupPrice: 299,
    groupCount: 15,
    successRate: "93%",
  },
]

// 辅助函数
function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "active":
      return "success"
    case "upcoming":
      return "secondary"
    case "ended":
      return "outline"
    default:
      return "default"
  }
}

function getStatusName(status: string) {
  switch (status) {
    case "active":
      return "进行中"
    case "upcoming":
      return "未开始"
    case "ended":
      return "已结束"
    default:
      return "未知"
  }
}

