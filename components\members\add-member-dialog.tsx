"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

interface AddMemberDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddMemberDialog({ open, onOpenChange }: AddMemberDialogProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [date, setDate] = useState<Date>()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>添加会员</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="membership">会员卡信息</TabsTrigger>
            <TabsTrigger value="health">健康信息</TabsTrigger>
            <TabsTrigger value="settings">其他设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">
                  姓名 <span className="text-red-500">*</span>
                </Label>
                <Input id="name" placeholder="请输入会员姓名" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">
                  手机号 <span className="text-red-500">*</span>
                </Label>
                <Input id="phone" placeholder="请输入手机号" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender">性别</Label>
                <RadioGroup defaultValue="female" className="flex">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="male" id="male" />
                    <Label htmlFor="male">男</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="female" id="female" />
                    <Label htmlFor="female">女</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="space-y-2">
                <Label htmlFor="birthday">生日</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn("w-full justify-start text-left font-normal", !date && "text-muted-foreground")}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "yyyy-MM-dd") : "选择日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={date} onSelect={setDate} />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">邮箱</Label>
                <Input id="email" type="email" placeholder="请输入邮箱地址" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="id-card">身份证号</Label>
                <Input id="id-card" placeholder="请输入身份证号" />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">地址</Label>
                <Input id="address" placeholder="请输入详细地址" />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="note">备注</Label>
                <Textarea id="note" placeholder="请输入备注信息" />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="membership" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="card-type">
                  会员卡名称 <span className="text-red-500">*</span>
                </Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择会员卡名称" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="year">年卡</SelectItem>
                    <SelectItem value="quarter">季卡</SelectItem>
                    <SelectItem value="month">月卡</SelectItem>
                    <SelectItem value="times20">次卡20次</SelectItem>
                    <SelectItem value="times10">次卡10次</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="level">会员等级</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择会员等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">标准会员</SelectItem>
                    <SelectItem value="silver">银卡会员</SelectItem>
                    <SelectItem value="gold">金卡会员</SelectItem>
                    <SelectItem value="platinum">白金会员</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="start-date">
                  开始日期 <span className="text-red-500">*</span>
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      选择日期
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="expiry-date">到期日期</Label>
                <Input id="expiry-date" placeholder="自动计算" disabled />
              </div>

              <div className="space-y-2">
                <Label htmlFor="payment-method">支付方式</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择支付方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wechat">微信支付</SelectItem>
                    <SelectItem value="alipay">支付宝</SelectItem>
                    <SelectItem value="cash">现金</SelectItem>
                    <SelectItem value="card">刷卡</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="payment-amount">
                  支付金额 <span className="text-red-500">*</span>
                </Label>
                <Input id="payment-amount" type="number" placeholder="请输入支付金额" />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="payment-note">支付备注</Label>
                <Textarea id="payment-note" placeholder="请输入支付相关备注" />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="health" className="mt-4 space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="health-status">健康状况</Label>
                <Textarea id="health-status" placeholder="请描述会员的健康状况，如有无慢性疾病、过敏史等" />
              </div>

              <div className="space-y-2">
                <Label>运动偏好</Label>
                <div className="grid grid-cols-2 gap-2 md:grid-cols-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="yoga" />
                    <Label htmlFor="yoga">瑜伽</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="pilates" />
                    <Label htmlFor="pilates">普拉提</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="fitness" />
                    <Label htmlFor="fitness">健身</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="dance" />
                    <Label htmlFor="dance">舞蹈</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="meditation" />
                    <Label htmlFor="meditation">冥想</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="spinning" />
                    <Label htmlFor="spinning">动感单车</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="special-needs">特殊需求</Label>
                <Textarea id="special-needs" placeholder="请描述会员的特殊需求，如需要特别关注的事项" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="emergency-contact">紧急联系人</Label>
                <Input id="emergency-contact" placeholder="紧急联系人姓名" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="emergency-phone">紧急联系电话</Label>
                <Input id="emergency-phone" placeholder="紧急联系人电话" />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="mt-4 space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>会员标签</Label>
                <div className="grid grid-cols-2 gap-2 md:grid-cols-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="tag-new" />
                    <Label htmlFor="tag-new">新会员</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="tag-vip" />
                    <Label htmlFor="tag-vip">VIP</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="tag-yoga" />
                    <Label htmlFor="tag-yoga">瑜伽爱好者</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="tag-coach" />
                    <Label htmlFor="tag-coach">教练推荐</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="tag-old" />
                    <Label htmlFor="tag-old">老会员</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>通知设置</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="notify-sms" defaultChecked />
                    <Label htmlFor="notify-sms">短信通知</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="notify-wechat" defaultChecked />
                    <Label htmlFor="notify-wechat">微信通知</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="notify-email" />
                    <Label htmlFor="notify-email">邮件通知</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>隐私设置</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="privacy-marketing" defaultChecked />
                    <Label htmlFor="privacy-marketing">接收营销信息</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="privacy-data" defaultChecked />
                    <Label htmlFor="privacy-data">同意数据收集</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="source">会员来源</Label>
                <Select>
                  <SelectTrigger id="source">
                    <SelectValue placeholder="选择会员来源" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="walk-in">自然到店</SelectItem>
                    <SelectItem value="referral">会员推荐</SelectItem>
                    <SelectItem value="online">线上获客</SelectItem>
                    <SelectItem value="promotion">促销活动</SelectItem>
                    <SelectItem value="cooperation">合作伙伴</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="referrer">推荐人</Label>
                <Input id="referrer" placeholder="如有推荐人，请填写" />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          {activeTab !== "basic" && (
            <Button
              variant="outline"
              onClick={() =>
                setActiveTab((prev) => {
                  const tabs = ["basic", "membership", "health", "settings"]
                  const currentIndex = tabs.indexOf(prev)
                  return tabs[currentIndex - 1]
                })
              }
            >
              上一步
            </Button>
          )}
          {activeTab !== "settings" ? (
            <Button
              onClick={() =>
                setActiveTab((prev) => {
                  const tabs = ["basic", "membership", "health", "settings"]
                  const currentIndex = tabs.indexOf(prev)
                  return tabs[currentIndex + 1]
                })
              }
            >
              下一步
            </Button>
          ) : (
            <Button>保存</Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

