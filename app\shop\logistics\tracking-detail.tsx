import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { RefreshCcw, Package, MapPin, Truck, CheckCircle, AlertCircle, Clock } from "lucide-react"

interface TrackingDetailProps {
  trackingNumber: string
  logisticsCompany: string
  shipDate: string
  status: string
}

// 模拟物流跟踪数据
const getLogisticsUpdates = (trackingNumber: string) => {
  // 这里可以替换为实际的API调用
  return [
    {
      time: "2023-06-25 18:30:45",
      status: "delivered",
      location: "上海市浦东新区",
      description: "已签收，签收人：本人",
      operator: "张师傅 (配送员)",
    },
    {
      time: "2023-06-25 10:15:22",
      status: "shipping",
      location: "上海市浦东新区",
      description: "快件已到达【上海浦东新区营业点】，正在派送中",
      operator: "李师傅 (配送员)",
    },
    {
      time: "2023-06-24 20:45:10",
      status: "shipping",
      location: "上海市",
      description: "快件已到达【上海转运中心】",
      operator: "系统",
    },
    {
      time: "2023-06-23 18:20:35",
      status: "shipping",
      location: "广州市",
      description: "快件已从【广州转运中心】发出，下一站【上海转运中心】",
      operator: "系统",
    },
    {
      time: "2023-06-23 15:10:42",
      status: "shipping",
      location: "广州市",
      description: "快件已到达【广州转运中心】",
      operator: "系统",
    },
    {
      time: "2023-06-22 19:30:15",
      status: "shipping",
      location: "广州市天河区",
      description: "快件已从【广州天河区营业点】发出，下一站【广州转运中心】",
      operator: "系统",
    },
    {
      time: "2023-06-22 16:45:30",
      status: "pending",
      location: "广州市天河区",
      description: "【广州天河区营业点】已收件，揽收员：王师傅",
      operator: "王师傅 (揽收员)",
    },
    {
      time: "2023-06-22 14:30:20",
      status: "pending",
      location: "广州市天河区",
      description: "商家已发货，等待快递揽收",
      operator: "系统",
    },
  ]
}

// 获取状态图标
const getStatusIcon = (status: string) => {
  switch (status) {
    case "delivered":
      return <CheckCircle className="h-5 w-5 text-green-500" />
    case "shipping":
      return <Truck className="h-5 w-5 text-blue-500" />
    case "pending":
      return <Clock className="h-5 w-5 text-yellow-500" />
    case "exception":
      return <AlertCircle className="h-5 w-5 text-red-500" />
    default:
      return <Package className="h-5 w-5 text-gray-500" />
  }
}

export function TrackingDetail({ trackingNumber, logisticsCompany, shipDate, status }: TrackingDetailProps) {
  const [logisticsUpdates, setLogisticsUpdates] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [lastRefreshed, setLastRefreshed] = useState<string>("")

  useEffect(() => {
    // 模拟API加载
    setLoading(true)
    setTimeout(() => {
      setLogisticsUpdates(getLogisticsUpdates(trackingNumber))
      setLoading(false)
      setLastRefreshed(new Date().toLocaleString())
    }, 800)
  }, [trackingNumber])

  const refreshTracking = () => {
    setLoading(true)
    setTimeout(() => {
      setLogisticsUpdates(getLogisticsUpdates(trackingNumber))
      setLoading(false)
      setLastRefreshed(new Date().toLocaleString())
    }, 800)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">物流追踪</h3>
          <p className="text-sm text-muted-foreground">
            快递单号: {trackingNumber} ({logisticsCompany})
          </p>
          {lastRefreshed && (
            <p className="text-xs text-muted-foreground mt-1">
              最后更新: {lastRefreshed}
            </p>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={
            status === "delivered" ? "default" :
            status === "shipping" ? "secondary" :
            status === "pending" ? "outline" :
            "destructive"
          }>
            {status === "delivered" ? "已送达" :
             status === "shipping" ? "配送中" :
             status === "pending" ? "待发货" :
             status === "returned" ? "已退回" : "异常"}
          </Badge>
          <Button variant="outline" size="sm" onClick={refreshTracking} disabled={loading}>
            <RefreshCcw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新物流
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-0">
          <CardTitle className="text-base">物流轨迹</CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          <ScrollArea className="h-[400px] pr-4">
            {loading ? (
              <div className="flex items-center justify-center h-[300px]">
                <RefreshCcw className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            ) : (
              <div className="space-y-4">
                {logisticsUpdates.map((update, index) => (
                  <div key={index} className="relative pl-8 pb-6">
                    {index !== logisticsUpdates.length - 1 && (
                      <div className="absolute left-[15px] top-[24px] bottom-0 w-[1px] bg-muted" />
                    )}
                    <div className="absolute left-0 top-1">
                      {getStatusIcon(update.status)}
                    </div>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{update.description}</span>
                        <span className="text-sm text-muted-foreground">{update.time}</span>
                      </div>
                      <div className="flex items-center text-sm text-muted-foreground gap-2">
                        <MapPin className="h-3 w-3" />
                        <span>{update.location}</span>
                      </div>
                      {update.operator && (
                        <div className="text-sm text-muted-foreground">
                          操作人: {update.operator}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  )
}
