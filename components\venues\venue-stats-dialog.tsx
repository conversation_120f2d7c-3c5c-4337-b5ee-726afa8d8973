import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface VenueStatsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function VenueStatsDialog({ open, onOpenChange }: VenueStatsDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>场地使用统计</DialogTitle>
        </DialogHeader>

        <div className="flex justify-end mb-4">
          <Select defaultValue="30">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">过去7天</SelectItem>
              <SelectItem value="30">过去30天</SelectItem>
              <SelectItem value="90">过去90天</SelectItem>
              <SelectItem value="365">过去一年</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">总体概览</TabsTrigger>
            <TabsTrigger value="utilization">使用率分析</TabsTrigger>
            <TabsTrigger value="courses">课程分布</TabsTrigger>
            <TabsTrigger value="revenue">收入分析</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">总课程数</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">245</div>
                  <p className="text-xs text-muted-foreground">较上月 +12%</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">平均使用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">78.5%</div>
                  <p className="text-xs text-muted-foreground">较上月 +3.2%</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">总收入</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">¥58,320</div>
                  <p className="text-xs text-muted-foreground">较上月 +8.7%</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>场地使用情况对比</CardTitle>
                <CardDescription>各场地使用率和课程数对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>1号瑜伽室</span>
                      <span className="text-sm">85%</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full">
                      <div className="bg-primary h-2 rounded-full" style={{ width: "85%" }}></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>2号瑜伽室</span>
                      <span className="text-sm">78%</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full">
                      <div className="bg-primary h-2 rounded-full" style={{ width: "78%" }}></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>3号瑜伽室</span>
                      <span className="text-sm">82%</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full">
                      <div className="bg-primary h-2 rounded-full" style={{ width: "82%" }}></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>4号瑜伽室</span>
                      <span className="text-sm">75%</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full">
                      <div className="bg-primary h-2 rounded-full" style={{ width: "75%" }}></div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>私教室</span>
                      <span className="text-sm">90%</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full">
                      <div className="bg-primary h-2 rounded-full" style={{ width: "90%" }}></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="utilization">
            <Card>
              <CardHeader>
                <CardTitle>使用率分析</CardTitle>
                <CardDescription>各场地使用率趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-end space-x-2">
                  {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day) => (
                    <div key={day} className="flex-1 flex flex-col items-center">
                      <div className="w-full flex space-x-1">
                        <div className="flex-1 bg-blue-500 h-[120px] rounded-t-sm"></div>
                        <div className="flex-1 bg-green-500 h-[180px] rounded-t-sm"></div>
                        <div className="flex-1 bg-yellow-500 h-[150px] rounded-t-sm"></div>
                        <div className="flex-1 bg-red-500 h-[100px] rounded-t-sm"></div>
                        <div className="flex-1 bg-purple-500 h-[200px] rounded-t-sm"></div>
                      </div>
                      <span className="text-xs mt-2">{day}</span>
                    </div>
                  ))}
                </div>
                <div className="flex justify-center mt-4 space-x-4">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
                    <span className="text-xs">1号瑜伽室</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
                    <span className="text-xs">2号瑜伽室</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full mr-1"></div>
                    <span className="text-xs">3号瑜伽室</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
                    <span className="text-xs">4号瑜伽室</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-purple-500 rounded-full mr-1"></div>
                    <span className="text-xs">私教室</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>高峰时段分析</CardTitle>
                  <CardDescription>各时段场地使用率</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>早上 (6:00-10:00)</span>
                        <span className="text-sm">65%</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "65%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>中午 (10:00-14:00)</span>
                        <span className="text-sm">45%</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "45%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>下午 (14:00-18:00)</span>
                        <span className="text-sm">70%</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "70%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>晚上 (18:00-22:00)</span>
                        <span className="text-sm">95%</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "95%" }}></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>空闲时段分析</CardTitle>
                  <CardDescription>各场地未使用时段</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 border rounded-md">
                      <p className="font-medium">1号瑜伽室</p>
                      <p className="text-sm text-muted-foreground mt-1">周一至周五 12:00-14:00</p>
                      <p className="text-sm text-muted-foreground">周六 08:00-10:00</p>
                    </div>
                    <div className="p-3 border rounded-md">
                      <p className="font-medium">2号瑜伽室</p>
                      <p className="text-sm text-muted-foreground mt-1">周一至周五 10:00-12:00</p>
                      <p className="text-sm text-muted-foreground">周日 全天</p>
                    </div>
                    <div className="p-3 border rounded-md">
                      <p className="font-medium">3号瑜伽室</p>
                      <p className="text-sm text-muted-foreground mt-1">周二、周四 14:00-16:00</p>
                      <p className="text-sm text-muted-foreground">周六 16:00-18:00</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="courses">
            <Card>
              <CardHeader>
                <CardTitle>课程类型分布</CardTitle>
                <CardDescription>各场地课程类型占比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium mb-2">1号瑜伽室</h4>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <span className="w-24">基础瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-blue-500 h-2 rounded-full" style={{ width: "65%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">65%</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-24">高级瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-blue-500 h-2 rounded-full" style={{ width: "25%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">25%</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-24">阴瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-blue-500 h-2 rounded-full" style={{ width: "10%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">10%</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">2号瑜伽室</h4>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <span className="w-24">基础瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-green-500 h-2 rounded-full" style={{ width: "40%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">40%</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-24">高级瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-green-500 h-2 rounded-full" style={{ width: "45%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">45%</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-24">阴瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-green-500 h-2 rounded-full" style={{ width: "15%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">15%</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">3号瑜伽室</h4>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <span className="w-24">基础瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-yellow-500 h-2 rounded-full" style={{ width: "30%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">30%</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-24">高级瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-yellow-500 h-2 rounded-full" style={{ width: "50%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">50%</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-24">阴瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-yellow-500 h-2 rounded-full" style={{ width: "20%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">20%</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-2">4号瑜伽室</h4>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <span className="w-24">空中瑜伽</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-red-500 h-2 rounded-full" style={{ width: "85%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">85%</span>
                      </div>
                      <div className="flex items-center">
                        <span className="w-24">其他课程</span>
                        <div className="flex-1 h-2 bg-gray-200 rounded-full">
                          <div className="bg-red-500 h-2 rounded-full" style={{ width: "15%" }}></div>
                        </div>
                        <span className="ml-2 text-sm">15%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-4">
              <CardHeader>
                <CardTitle>热门课程排名</CardTitle>
                <CardDescription>各场地最受欢迎的课程</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 border rounded-md">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">基础瑜伽入门</p>
                        <p className="text-sm text-muted-foreground">1号瑜伽室 | 张教练</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">预订率: 95%</p>
                        <p className="text-sm text-muted-foreground">每周一、三、五 10:00-11:30</p>
                      </div>
                    </div>
                  </div>
                  <div className="p-3 border rounded-md">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">高级瑜伽进阶</p>
                        <p className="text-sm text-muted-foreground">2号瑜伽室 | 李教练</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">预订率: 90%</p>
                        <p className="text-sm text-muted-foreground">每周二、四、六 14:00-15:30</p>
                      </div>
                    </div>
                  </div>
                  <div className="p-3 border rounded-md">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">空中瑜伽体验</p>
                        <p className="text-sm text-muted-foreground">4号瑜伽室 | 刘教练</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">预订率: 88%</p>
                        <p className="text-sm text-muted-foreground">每周六、日 09:00-10:30</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="revenue">
            <Card>
              <CardHeader>
                <CardTitle>场地收入分析</CardTitle>
                <CardDescription>各场地收入贡献</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-end space-x-2">
                  {["1号瑜伽室", "2号瑜伽室", "3号瑜伽室", "4号瑜伽室", "私教室"].map((venue, i) => {
                    const heights = [70, 60, 65, 55, 90]
                    const colors = ["bg-blue-500", "bg-green-500", "bg-yellow-500", "bg-red-500", "bg-purple-500"]
                    return (
                      <div key={venue} className="flex-1 flex flex-col items-center">
                        <div className={`w-full ${colors[i]} rounded-t-sm`} style={{ height: `${heights[i]}%` }}></div>
                        <span className="text-xs mt-2">{venue}</span>
                        <span className="text-xs font-medium">¥{Math.round(heights[i] * 200)}</span>
                      </div>
                    )
                  })}
                </div>

                <div className="mt-8">
                  <h4 className="text-sm font-medium mb-4">收入来源分析</h4>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <span className="w-24">团体课</span>
                      <div className="flex-1 h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "65%" }}></div>
                      </div>
                      <span className="ml-2 text-sm">65%</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-24">私教课</span>
                      <div className="flex-1 h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "25%" }}></div>
                      </div>
                      <span className="ml-2 text-sm">25%</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-24">场地租赁</span>
                      <div className="flex-1 h-2 bg-gray-200 rounded-full">
                        <div className="bg-primary h-2 rounded-full" style={{ width: "10%" }}></div>
                      </div>
                      <span className="ml-2 text-sm">10%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>收入趋势</CardTitle>
                  <CardDescription>近6个月收入变化</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[200px] flex items-end space-x-2">
                    {["10月", "11月", "12月", "1月", "2月", "3月"].map((month, i) => {
                      const heights = [60, 65, 70, 55, 75, 80]
                      return (
                        <div key={month} className="flex-1 flex flex-col items-center">
                          <div className="w-full bg-primary rounded-t-sm" style={{ height: `${heights[i]}%` }}></div>
                          <span className="text-xs mt-2">{month}</span>
                          <span className="text-xs font-medium">¥{Math.round(heights[i] * 150)}</span>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>场地投资回报率</CardTitle>
                  <CardDescription>各场地ROI分析</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>1号瑜伽室</span>
                        <span className="text-sm">185%</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: "85%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>2号瑜伽室</span>
                        <span className="text-sm">165%</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: "75%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>3号瑜伽室</span>
                        <span className="text-sm">170%</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: "80%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>4号瑜伽室</span>
                        <span className="text-sm">210%</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: "95%" }}></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>私教室</span>
                        <span className="text-sm">250%</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: "100%" }}></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

