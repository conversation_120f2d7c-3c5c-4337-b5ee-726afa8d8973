export interface CourseType {
  id: number;
  name: string;
  description: string;
  courses: number;
  status: 'active' | 'inactive';
  color: string;
  createdAt: string;
  updatedAt: string;
  displayOrder?: number;
}

export interface CourseTypeFilter {
  keyword?: string;
  status?: string;
  showWithCourses?: boolean;
  showWithoutCourses?: boolean;
  createdAfter?: string;
  createdBefore?: string;
} 