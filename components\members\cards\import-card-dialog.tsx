"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Upload, FileText, AlertCircle, Check, Download, FileSpreadsheet } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Progress } from "@/components/ui/progress"

interface ImportCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImportComplete: (cards: any[]) => void
}

export function ImportCardDialog({
  open,
  onOpenChange,
  onImportComplete
}: ImportCardDialogProps) {
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("file")
  const [file, setFile] = useState<File | null>(null)
  const [jsonData, setJsonData] = useState("")
  const [importFormat, setImportFormat] = useState("excel")
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [previewData, setPreviewData] = useState<any[]>([])

  // 处理文件上传
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      setValidationErrors([])
      setPreviewData([])

      // 检查文件类型
      const fileExt = selectedFile.name.split('.').pop()?.toLowerCase()
      if (importFormat === "excel" && !["xlsx", "xls"].includes(fileExt || "")) {
        setValidationErrors(["请上传Excel文件（.xlsx或.xls格式）"])
        return
      } else if (importFormat === "csv" && fileExt !== "csv") {
        setValidationErrors(["请上传CSV文件（.csv格式）"])
        return
      } else if (importFormat === "json" && fileExt !== "json") {
        setValidationErrors(["请上传JSON文件（.json格式）"])
        return
      }

      // 读取文件内容
      const reader = new FileReader()

      reader.onload = (event) => {
        try {
          const content = event.target?.result as string

          if (fileExt === "json") {
            // 解析JSON文件
            const jsonData = JSON.parse(content)

            if (!Array.isArray(jsonData)) {
              setValidationErrors(["JSON数据必须是数组格式"])
              return
            }

            if (jsonData.length === 0) {
              setValidationErrors(["JSON数据不能为空数组"])
              return
            }

            // 显示预览数据
            setPreviewData(jsonData.slice(0, 3).map(item => ({
              name: item.name || "未命名",
              description: item.description || "",
              price: item.price || "¥0",
              validity: item.validity || "",
              limit: item.limit || ""
            })))
          } else if (fileExt === "csv") {
            // 解析CSV文件
            const lines = content.split('\n')
            if (lines.length < 2) {
              setValidationErrors(["CSV文件格式不正确或为空"])
              return
            }

            const headers = lines[0].split(',').map(h => h.trim())
            const nameIndex = headers.findIndex(h => h.includes('名称') || h.toLowerCase().includes('name'))
            const descIndex = headers.findIndex(h => h.includes('描述') || h.toLowerCase().includes('description'))
            const priceIndex = headers.findIndex(h => h.includes('价格') || h.toLowerCase().includes('price'))
            const validityIndex = headers.findIndex(h => h.includes('有效期') || h.toLowerCase().includes('validity'))
            const limitIndex = headers.findIndex(h => h.includes('限制') || h.toLowerCase().includes('limit'))

            if (nameIndex === -1) {
              setValidationErrors(["CSV文件缺少名称列"])
              return
            }

            // 解析数据行
            const dataRows = lines.slice(1).filter(line => line.trim() !== '')
            if (dataRows.length === 0) {
              setValidationErrors(["CSV文件没有数据行"])
              return
            }

            // 显示预览数据
            const previewRows = dataRows.slice(0, 3).map(row => {
              const columns = row.split(',').map(col => col.trim().replace(/^"|"$/g, ''))
              return {
                name: nameIndex >= 0 && nameIndex < columns.length ? columns[nameIndex] : "未命名",
                description: descIndex >= 0 && descIndex < columns.length ? columns[descIndex] : "",
                price: priceIndex >= 0 && priceIndex < columns.length ? columns[priceIndex] : "¥0",
                validity: validityIndex >= 0 && validityIndex < columns.length ? columns[validityIndex] : "",
                limit: limitIndex >= 0 && limitIndex < columns.length ? columns[limitIndex] : ""
              }
            })

            setPreviewData(previewRows)
          } else if (["xlsx", "xls"].includes(fileExt || "")) {
            // 对于Excel文件，我们在这里只提供一个模拟预览
            // 实际项目中应使用xlsx库解析Excel文件
            setPreviewData([
              { name: "年卡", description: "365天不限次数", price: "¥3,600", validity: "365天", limit: "不限次数" },
              { name: "季卡", description: "90天不限次数", price: "¥1,200", validity: "90天", limit: "不限次数" },
              { name: "月卡", description: "30天不限次数", price: "¥500", validity: "30天", limit: "不限次数" }
            ])

            // 添加提示信息
            toast({
              title: "Excel文件预览",
              description: "这是模拟预览。在实际项目中，需要使用xlsx库解析Excel文件。",
            })
          }
        } catch (error) {
          console.error("文件解析错误:", error)
          setValidationErrors([`文件解析错误: ${(error as Error).message}`])
        }
      }

      reader.onerror = () => {
        setValidationErrors(["文件读取失败"])
      }

      if (fileExt === "json" || fileExt === "csv") {
        reader.readAsText(selectedFile)
      } else if (["xlsx", "xls"].includes(fileExt || "")) {
        // 对于Excel文件，我们在这里只提供一个模拟预览
        // 实际项目中应使用xlsx库解析Excel文件
        reader.readAsText(selectedFile)
      }
    }
  }

  // 处理JSON数据输入
  const handleJsonDataChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setJsonData(e.target.value)
    setValidationErrors([])

    // 验证JSON格式
    try {
      if (e.target.value) {
        const parsed = JSON.parse(e.target.value)
        if (!Array.isArray(parsed)) {
          setValidationErrors(["JSON数据必须是数组格式"])
        } else if (parsed.length === 0) {
          setValidationErrors(["JSON数据不能为空数组"])
        } else {
          // 模拟预览
          setPreviewData(parsed.slice(0, 3).map(item => ({
            name: item.name || "未命名",
            description: item.description || "",
            price: item.price || "¥0",
            validity: item.validity || "",
            limit: item.limit || ""
          })))
        }
      } else {
        setPreviewData([])
      }
    } catch (error) {
      setValidationErrors(["JSON格式无效，请检查语法"])
      setPreviewData([])
    }
  }

  // 处理导入
  const handleImport = async () => {
    if (activeTab === "file" && !file) {
      toast({
        title: "请选择文件",
        variant: "destructive",
      })
      return
    }

    if (activeTab === "json" && !jsonData) {
      toast({
        title: "请输入JSON数据",
        variant: "destructive",
      })
      return
    }

    if (validationErrors.length > 0) {
      toast({
        title: "存在验证错误",
        description: "请修复所有错误后再导入",
        variant: "destructive",
      })
      return
    }

    setIsUploading(true)

    // 设置上传进度
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          return 100
        }
        return prev + 10
      })
    }, 200)

    try {
      let importedCards: any[] = []

      if (activeTab === "file" && file) {
        const fileExt = file.name.split('.').pop()?.toLowerCase()

        if (fileExt === "json") {
          // 读取并解析JSON文件
          const content = await readFileAsText(file)
          const jsonData = JSON.parse(content)
          importedCards = processImportedData(jsonData)
        } else if (fileExt === "csv") {
          // 读取并解析CSV文件
          const content = await readFileAsText(file)
          const lines = content.split('\n')
          const headers = lines[0].split(',').map(h => h.trim())

          const nameIndex = headers.findIndex(h => h.includes('名称') || h.toLowerCase().includes('name'))
          const descIndex = headers.findIndex(h => h.includes('描述') || h.toLowerCase().includes('description'))
          const priceIndex = headers.findIndex(h => h.includes('价格') || h.toLowerCase().includes('price'))
          const validityIndex = headers.findIndex(h => h.includes('有效期') || h.toLowerCase().includes('validity'))
          const limitIndex = headers.findIndex(h => h.includes('限制') || h.toLowerCase().includes('limit'))

          const dataRows = lines.slice(1).filter(line => line.trim() !== '')

          const parsedData = dataRows.map((row, index) => {
            const columns = row.split(',').map(col => col.trim().replace(/^"|"$/g, ''))
            return {
              id: Date.now() + index, // 生成临时ID
              name: nameIndex >= 0 && nameIndex < columns.length ? columns[nameIndex] : "未命名卡片",
              description: descIndex >= 0 && descIndex < columns.length ? columns[descIndex] : "",
              price: priceIndex >= 0 && priceIndex < columns.length ? columns[priceIndex] : "¥0",
              validity: validityIndex >= 0 && validityIndex < columns.length ? columns[validityIndex] : "",
              limit: limitIndex >= 0 && limitIndex < columns.length ? columns[limitIndex] : "",
              status: "active",
              members: 0,
              salesCount: 0,
              revenue: "¥0",
              color: getRandomColor(),
              createdAt: new Date().toISOString().split('T')[0],
              updatedAt: new Date().toISOString().split('T')[0],
              isTrialCard: false
            }
          })

          importedCards = processImportedData(parsedData)
        } else if (["xlsx", "xls"].includes(fileExt || "")) {
          // 对于Excel文件，这里只是模拟
          // 实际项目中应使用xlsx库解析Excel文件
          toast({
            title: "Excel导入",
            description: "这是模拟导入。在实际项目中，需要使用xlsx库解析Excel文件。",
          })

          // 模拟导入结果
          importedCards = [
            { id: 101, name: "年卡", description: "365天不限次数", price: "¥3,600", validity: "365天", limit: "不限次数", status: "active", members: 0, salesCount: 0, revenue: "¥0", color: "#4F46E5" },
            { id: 102, name: "季卡", description: "90天不限次数", price: "¥1,200", validity: "90天", limit: "不限次数", status: "active", members: 0, salesCount: 0, revenue: "¥0", color: "#06B6D4" },
            { id: 103, name: "月卡", description: "30天不限次数", price: "¥500", validity: "30天", limit: "不限次数", status: "active", members: 0, salesCount: 0, revenue: "¥0", color: "#10B981" }
          ]
        }
      } else if (activeTab === "json" && jsonData) {
        // 解析JSON文本输入
        const parsedData = JSON.parse(jsonData)
        importedCards = processImportedData(parsedData)
      }

      // 调用导入完成回调
      onImportComplete(importedCards)

      toast({
        title: "导入成功",
        description: `已成功导入 ${importedCards.length} 张会员卡`,
      })

      onOpenChange(false)
    } catch (error) {
      console.error("导入错误:", error)
      toast({
        title: "导入失败",
        description: `导入过程中出现错误: ${(error as Error).message}`,
        variant: "destructive",
      })
    } finally {
      clearInterval(interval)
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  // 辅助函数：读取文件为文本
  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (event) => resolve(event.target?.result as string)
      reader.onerror = (error) => reject(error)
      reader.readAsText(file)
    })
  }

  // 辅助函数：处理导入的数据
  const processImportedData = (data: any[]): any[] => {
    return data.map((item, index) => {
      // 确保每个项目都有必要的字段
      return {
        id: item.id || Date.now() + index,
        name: item.name || "未命名卡片",
        description: item.description || "",
        price: item.price || "¥0",
        validity: item.validity || "",
        limit: item.limit || "",
        status: item.status || "active",
        members: item.members || 0,
        salesCount: item.salesCount || 0,
        revenue: item.revenue || "¥0",
        color: item.color || getRandomColor(),
        createdAt: item.createdAt || new Date().toISOString().split('T')[0],
        updatedAt: item.updatedAt || new Date().toISOString().split('T')[0],
        isTrialCard: item.isTrialCard || false
      }
    })
  }

  // 辅助函数：生成随机颜色
  const getRandomColor = (): string => {
    const colors = ["#4F46E5", "#06B6D4", "#10B981", "#F59E0B", "#EC4899", "#8B5CF6", "#EF4444"]
    return colors[Math.floor(Math.random() * colors.length)]
  }

  // 下载模板
  const handleDownloadTemplate = () => {
    // 创建模板数据
    let templateContent = ""
    let fileName = `会员卡导入模板_${new Date().toISOString().split("T")[0]}`
    let fileType = ""

    if (importFormat === "json") {
      // JSON模板
      const templateData = [
        {
          name: "年卡",
          description: "365天不限次数",
          price: "¥3,600",
          validity: "365天",
          limit: "不限次数",
          status: "active"
        },
        {
          name: "季卡",
          description: "90天不限次数",
          price: "¥1,200",
          validity: "90天",
          limit: "不限次数",
          status: "active"
        },
        {
          name: "月卡",
          description: "30天不限次数",
          price: "¥500",
          validity: "30天",
          limit: "不限次数",
          status: "active"
        }
      ]

      templateContent = JSON.stringify(templateData, null, 2)
      fileName += ".json"
      fileType = "application/json"
    } else {
      // CSV模板 (也用于Excel)
      const headers = ["名称", "描述", "价格", "有效期", "使用限制", "状态"]
      const rows = [
        ["年卡", "365天不限次数", "¥3,600", "365天", "不限次数", "active"],
        ["季卡", "90天不限次数", "¥1,200", "90天", "不限次数", "active"],
        ["月卡", "30天不限次数", "¥500", "30天", "不限次数", "active"]
      ]

      templateContent = headers.join(",") + "\n"
      rows.forEach(row => {
        templateContent += row.join(",") + "\n"
      })

      if (importFormat === "excel") {
        fileName += ".csv" // 使用CSV作为Excel的替代
        fileType = "text/csv"
      } else {
        fileName += ".csv"
        fileType = "text/csv"
      }
    }

    // 创建Blob对象
    const blob = new Blob([templateContent], { type: fileType })

    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = fileName

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    URL.revokeObjectURL(url)
    document.body.removeChild(link)

    toast({
      title: "模板下载成功",
      description: `会员卡导入${importFormat === "excel" ? "Excel" : importFormat === "csv" ? "CSV" : "JSON"}模板已下载`
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5 text-primary" />
            导入会员卡
          </DialogTitle>
          <DialogDescription>
            通过文件或JSON数据导入会员卡信息
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="file">文件导入</TabsTrigger>
            <TabsTrigger value="json">JSON导入</TabsTrigger>
          </TabsList>

          <TabsContent value="file" className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>选择导入格式</Label>
              <Select value={importFormat} onValueChange={setImportFormat}>
                <SelectTrigger>
                  <SelectValue placeholder="选择文件格式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel文件 (.xlsx, .xls)</SelectItem>
                  <SelectItem value="csv">CSV文件 (.csv)</SelectItem>
                  <SelectItem value="json">JSON文件 (.json)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="file-upload">上传文件</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="file-upload"
                  type="file"
                  onChange={handleFileChange}
                  accept={
                    importFormat === "excel"
                      ? ".xlsx,.xls"
                      : importFormat === "csv"
                        ? ".csv"
                        : ".json"
                  }
                  disabled={isUploading}
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleDownloadTemplate}
                  title="下载导入模板"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                {importFormat === "excel"
                  ? "支持.xlsx和.xls格式的Excel文件"
                  : importFormat === "csv"
                    ? "支持.csv格式的文件，UTF-8编码"
                    : "支持.json格式的文件，必须是数组格式"}
              </p>
            </div>

            {file && (
              <div className="rounded-md border p-3">
                <div className="flex items-center gap-2">
                  {importFormat === "excel" ? (
                    <FileSpreadsheet className="h-5 w-5 text-primary" />
                  ) : (
                    <FileText className="h-5 w-5 text-primary" />
                  )}
                  <div className="flex-1">
                    <p className="font-medium">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {(file.size / 1024).toFixed(2)} KB
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setFile(null)}
                    disabled={isUploading}
                  >
                    移除
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="json" className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="json-data">JSON数据</Label>
              <Textarea
                id="json-data"
                placeholder='[{"name": "年卡", "description": "365天不限次数", "price": "¥3,600", "validity": "365天", "limit": "不限次数"}]'
                className="min-h-[200px] font-mono text-sm"
                value={jsonData}
                onChange={handleJsonDataChange}
                disabled={isUploading}
              />
              <p className="text-xs text-muted-foreground">
                输入符合格式的JSON数组数据，每个对象代表一张会员卡
              </p>
            </div>
          </TabsContent>
        </Tabs>

        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>验证错误</AlertTitle>
            <AlertDescription>
              <ul className="ml-4 list-disc">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {previewData.length > 0 && (
          <div className="space-y-2">
            <Label>数据预览</Label>
            <div className="max-h-[200px] overflow-y-auto rounded-md border">
              <table className="w-full">
                <thead>
                  <tr className="border-b bg-muted/50">
                    <th className="p-2 text-left text-sm font-medium">会员卡名称</th>
                    <th className="p-2 text-left text-sm font-medium">描述</th>
                    <th className="p-2 text-left text-sm font-medium">价格</th>
                    <th className="p-2 text-left text-sm font-medium">有效期</th>
                    <th className="p-2 text-left text-sm font-medium">使用限制</th>
                  </tr>
                </thead>
                <tbody>
                  {previewData.map((item, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 text-sm">{item.name}</td>
                      <td className="p-2 text-sm">{item.description}</td>
                      <td className="p-2 text-sm">{item.price}</td>
                      <td className="p-2 text-sm">{item.validity}</td>
                      <td className="p-2 text-sm">{item.limit}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <p className="text-xs text-muted-foreground">
              仅显示前3条数据，导入时将处理所有数据
            </p>
          </div>
        )}

        {isUploading && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>上传进度</Label>
              <span className="text-sm">{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} className="h-2 w-full" />
          </div>
        )}

        <DialogFooter className="gap-2 sm:gap-0">
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isUploading}>
            取消
          </Button>
          <Button onClick={handleImport} disabled={isUploading || validationErrors.length > 0}>
            {isUploading ? (
              <>导入中...</>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                开始导入
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
