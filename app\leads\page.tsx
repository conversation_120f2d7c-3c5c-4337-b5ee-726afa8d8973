"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  LayoutList,
  LayoutGrid,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  RefreshCw,
  ChevronDown,
  SlidersHorizontal,
  Sparkles,
  ChevronRight,
  ChevronLeft
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { LeadTable } from "@/components/leads/lead-table"
import { LeadGrid } from "@/components/leads/lead-grid"
import { AddLeadDialog } from "@/components/leads/add-lead-dialog"
import { ImportLeadsDialog } from "@/components/leads/import-leads-dialog"
import { AdvancedFilterDialog } from "@/components/leads/advanced-filter-dialog"
import Link from "next/link"

export default function LeadsPage() {
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showFilterDialog, setShowFilterDialog] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isPremium, setIsPremium] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(5) // 实际应用中应该从API获取

  // 检查用户是否已开通高级版
  useEffect(() => {
    // 这里应该是实际的API调用，检查用户是否已开通高级版
    // 为了演示，我们使用localStorage来模拟状态
    const premiumStatus = localStorage.getItem("premiumLeadsEnabled")
    setIsPremium(premiumStatus === "true")
  }, [])

  // 仅用于演示，实际应用中不需要此功能
  const handleTogglePremium = () => {
    const newStatus = !isPremium
    localStorage.setItem("premiumLeadsEnabled", newStatus.toString())
    setIsPremium(newStatus)
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return
    setCurrentPage(page)
    // 实际应用中应该调用API获取对应页的数据
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">潜客管理</h1>
          <div className="flex items-center mt-1">
            {/* 仅用于演示，实际应用中不需要此按钮 */}
            <Button variant="ghost" size="sm" className="h-6 px-2 text-xs" onClick={handleTogglePremium}>
              (演示用：{isPremium ? "取消" : "开通"}高级版)
            </Button>
            {isPremium && (
              <Badge className="ml-2 bg-gradient-to-r from-amber-500 to-amber-300 hover:from-amber-500 hover:to-amber-300 text-white">
                <Sparkles className="h-3 w-3 mr-1" />
                高级版已开通
              </Badge>
            )}
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={() => setShowFilterDialog(true)}>
            <Filter className="mr-2 h-4 w-4" />
            高级筛选
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <RefreshCw className="mr-2 h-4 w-4" />
                导入/导出
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowImportDialog(true)}>
                <Upload className="mr-2 h-4 w-4" />
                导入潜客
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="mr-2 h-4 w-4" />
                导出潜客
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={() => setShowAddDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            添加潜客
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/3 relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索潜客姓名、电话或来源..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="潜客状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="new">新获取</SelectItem>
              <SelectItem value="contacted">已联系</SelectItem>
              <SelectItem value="qualified">已确认</SelectItem>
              <SelectItem value="negotiating">洽谈中</SelectItem>
              <SelectItem value="converted">已转化</SelectItem>
              <SelectItem value="lost">已流失</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <SlidersHorizontal className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex gap-2 ml-auto">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setViewMode("list")}
            className={viewMode === "list" ? "bg-muted" : ""}
          >
            <LayoutList className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setViewMode("grid")}
            className={viewMode === "grid" ? "bg-muted" : ""}
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="bg-background">
        <Tabs defaultValue="all">
          <TabsList>
            <TabsTrigger value="all">全部潜客</TabsTrigger>
            <TabsTrigger value="new">新获取</TabsTrigger>
            <TabsTrigger value="followup">待跟进</TabsTrigger>
            <TabsTrigger value="hot">高意向</TabsTrigger>
            <TabsTrigger value="converted">已转化</TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="mt-4">
            {viewMode === "list" ? <LeadTable searchQuery={searchQuery} statusFilter={statusFilter} /> : <LeadGrid searchQuery={searchQuery} statusFilter={statusFilter} />}
          </TabsContent>
          <TabsContent value="new" className="mt-4">
            {viewMode === "list" ? <LeadTable searchQuery={searchQuery} statusFilter="new" /> : <LeadGrid searchQuery={searchQuery} statusFilter="new" />}
          </TabsContent>
          <TabsContent value="followup" className="mt-4">
            {viewMode === "list" ? <LeadTable searchQuery={searchQuery} statusFilter="contacted" /> : <LeadGrid searchQuery={searchQuery} statusFilter="contacted" />}
          </TabsContent>
          <TabsContent value="hot" className="mt-4">
            {viewMode === "list" ? <LeadTable searchQuery={searchQuery} statusFilter="qualified" /> : <LeadGrid searchQuery={searchQuery} statusFilter="qualified" />}
          </TabsContent>
          <TabsContent value="converted" className="mt-4">
            {viewMode === "list" ? <LeadTable searchQuery={searchQuery} statusFilter="converted" /> : <LeadGrid searchQuery={searchQuery} statusFilter="converted" />}
          </TabsContent>
        </Tabs>

        {/* 分页控件 */}
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-muted-foreground">
            显示第 <span className="font-medium">{(currentPage - 1) * 10 + 1}</span> 至 <span className="font-medium">{Math.min(currentPage * 10, 47)}</span> 条，共 <span className="font-medium">47</span> 条
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              上一页
            </Button>
            <div className="flex items-center">
              {[...Array(totalPages)].map((_, i) => (
                <Button
                  key={i}
                  variant={currentPage === i + 1 ? "default" : "outline"}
                  size="sm"
                  className="w-9 h-9 p-0 mx-1"
                  onClick={() => handlePageChange(i + 1)}
                >
                  {i + 1}
                </Button>
              ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              下一页
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      </div>

      {/* 高级功能入口 */}
      <div className="mt-8 flex justify-between items-center border-t pt-6">
        <div className="flex items-center">
          {isPremium ? (
            <>
              <Badge className="mr-2 bg-gradient-to-r from-amber-500 to-amber-300 hover:from-amber-500 hover:to-amber-300 text-white">
                <Sparkles className="h-3 w-3 mr-1" />
                高级版
              </Badge>
              <span className="text-sm text-muted-foreground">您已开通高级潜客管理功能</span>
            </>
          ) : (
            <span className="text-sm text-muted-foreground">升级到高级版，解锁更多潜客管理功能</span>
          )}
        </div>
        <Link href={isPremium ? "/leads/premium" : "/premium-services/advanced-leads"}>
          <Button variant={isPremium ? "default" : "outline"} size="sm">
            {isPremium ? (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                进入高级版
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                了解高级功能
              </>
            )}
          </Button>
        </Link>
      </div>



      <AddLeadDialog open={showAddDialog} onOpenChange={setShowAddDialog} />
      <ImportLeadsDialog open={showImportDialog} onOpenChange={setShowImportDialog} />
      <AdvancedFilterDialog open={showFilterDialog} onOpenChange={setShowFilterDialog} />
    </div>
  )
}
