"use client"

import React, { useState, useMemo, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { OnboardingIndicator } from "@/components/onboarding-indicator"
import {
  BarChart3,
  CalendarDays,
  CreditCard,
  LayoutDashboard,
  MapPin,
  Menu,
  Settings,
  Users,
  UserCircle,
  CreditCardIcon as PaymentIcon,
  Building,
  ClipboardCheck,
  Bell,
  Star,
  Gift,
  Store,
  LinkIcon,
} from "lucide-react"
import { useAuth } from "@/contexts/auth-context"

// 菜单项接口
interface MenuItem {
  name: string;
  href?: string;
  icon: any; // Lucide图标组件类型
  children: Array<{ name: string; href: string }>;
  requiredStore?: boolean; // 是否需要门店才能显示
  adminOnly?: boolean; // 是否只有租户管理员可见
}

// 将现有的 sidebarItems 数组替换为以下嵌套结构的菜单数据
const sidebarItems: MenuItem[] = [
  {
    name: "工作台",
    href: "/dashboard",
    icon: LayoutDashboard,
    children: [],
    requiredStore: false,
  },
  {
    name: "连锁管理",
    href: "/stores",
    icon: Store,
    children: [
      { name: "门店列表", href: "/stores" },
      { name: "连锁管理", href: "/stores/chain" },
      { name: "连锁同步", href: "/stores/chain/sync" },
      { name: "门店信息设置", href: "/stores/settings" },
      { name: "通用卡管理", href: "/stores/cards" },
      // { name: "总部管理", href: "/stores/headquarters" },
      // { name: "品牌管理", href: "/stores/brand" },
      // { name: "资源调配", href: "/stores/resources" },
    ],
    requiredStore: false,
  },
  {
    name: "前台工作台",
    href: "/reception",
    icon: ClipboardCheck,
    children: [],
    requiredStore: true,
  },
  {
    name: "课程管理",
    icon: CalendarDays,
    children: [
      { name: "课程类型", href: "/courses/types" },
      { name: "课程列表", href: "/courses" },
      { name: "系列课程", href: "/courses/series-courses" },
      { name: "课程排期", href: "/courses/schedule" },
      { name: "课时费管理", href: "/courses/hourly-rates" },
      { name: "预约记录", href: "/booking-records" },
      // { name: "排队管理", href: "/course-waitlist" },
      { name: "预约规则", href: "/booking-rules" },
      // { name: "签到核销", href: "/checkin" },
    ],
    requiredStore: true,
  },
  {
    name: "评价管理",
    icon: Star,
    children: [
      { name: "课程与教练评价", href: "/reviews" },
      { name: "评价设置", href: "/reviews/settings" },
    ],
    requiredStore: true,
  },
  {
    name: "会员管理",
    icon: Users,
    children: [
      { name: "会员卡名称", href: "/members/cards" },
      { name: "会员卡列表", href: "/members/cards/list" },
      { name: "会员列表", href: "/members" },
      { name: "会员标签", href: "/members/tags" },
      { name: "会员等级", href: "/members/levels" },
      { name: "潜客管理", href: "/leads" },
    ],
    requiredStore: true,
  },
  // {
  //   name: "教练管理",
  //   icon: UserCircle,
  //   children: [
  //     { name: "教练列表", href: "/coaches" },
  //     { name: "教练排班", href: "/coaches/schedule" },
  //   ],
  // },
  // {
  //   name: "场地管理",
  //   icon: MapPin,
  //   children: [
  //     { name: "场地列表", href: "/venues" },
  //     { name: "场地预订", href: "/venues/bookings" },
  //     // { name: "设备管理", href: "/venues/equipment" },
  //   ],
  // },
  {
    name: "场馆管理",
    icon: Building,
    children: [
      { name: "员工管理", href: "/venue-management/staff" },
      { name: "场馆设置", href: "/venue-management/settings" },
      { name: "场地管理", href: "/venues" },
      { name: "公告管理", href: "/venue-management/announcements" },

    ],
    requiredStore: true,
  },
  {
    name: "订单管理",
    icon: CreditCard,
    children: [
      { name: "订单列表", href: "/orders" },
      { name: "退款管理", href: "/orders/refunds" },
      // { name: "支付设置", href: "/orders/payment-settings" },
    ],
    requiredStore: true,
  },
  // 核销管理已整合到收银台
  // 添加支付渠道管理菜单
  // {
  //   name: "支付渠道",
  //   icon: PaymentIcon,
  //   children: [
  //     { name: "微信支付", href: "/payment/wechat" },
  //     { name: "支付宝", href: "/payment/alipay" },
  //     { name: "支付配置", href: "/payment/config" },
  //     { name: "支付记录管理", href: "/payment/records" },
  //   ],
  // },

  // {
  //   name: "增值服务",
  //   icon: Gift,
  //   children: [
  //     { name: "服务列表", href: "/premium-services" },
  //     { name: "订购记录", href: "/premium-services/orders" },
  //     { name: "服务配置", href: "/premium-services/settings" },
  //     { name: "订阅支付", href: "/premium-services/subscription-payment" },
  //     // { name: "教练薪资", href: "/premium-services/coach-salary" },
  //     // { name: "高级潜客管理", href: "/premium-services/advanced-leads" },
  //   ],
  // },
  {
    name: "瑜伽商城",
    icon: CreditCard,
    children: [
      { name: "商品管理", href: "/shop/products" },
      { name: "商品分类", href: "/shop/categories" },
      { name: "订单管理", href: "/shop/orders" },
      { name: "虚拟商品", href: "/shop/virtual-products" },
      { name: "物流管理", href: "/shop/logistics" },
      { name: "商城设置", href: "/shop/settings" },
    ],
    requiredStore: true,
  },
  {
    name: "营销中心",
    icon: Gift,
    children: [
      { name: "优惠券管理", href: "/marketing/coupons" },
      { name: "促销活动", href: "/marketing/promotions" },
      { name: "会员积分", href: "/marketing/points" },
      { name: "拼团活动", href: "/marketing/group-buying" },
      { name: "秒杀活动", href: "/marketing/flash-sales" },
      { name: "营销数据", href: "/marketing/analytics" },
    ],
    requiredStore: true,
  },
  {
    name: "数据统计",
    icon: BarChart3,
    children: [
      { name: "会员分析", href: "/statistics" },
      { name: "课程分析", href: "/statistics/courses" },
      { name: "收入分析", href: "/statistics/revenue" },
      { name: "教练分析", href: "/statistics/coaches" },
    ],
    requiredStore: true,
  },
  {
    name: "消息中心",
    icon: Bell,
    children: [
      // { name: "消息概览", href: "/messages/dashboard" },
      { name: "消息规则", href: "/messages/rules" },
      // { name: "消息模板", href: "/messages/templates" },
      { name: "发送记录", href: "/messages/history" },
      // { name: "消息设置", href: "/messages/settings" },
    ],
    requiredStore: true,
  },
  {
    name: "系统设置",
    icon: Settings,
    children: [
      { name: "基础设置", href: "/settings" },
      { name: "用户管理", href: "/settings/users" },
      { name: "角色权限", href: "/settings/roles" },
      // { name: "通知设置", href: "/settings/notifications" },
      // { name: "小程序设置", href: "/settings/mini-program" },
      { name: "会员端小程序", href: "/settings/member-mini-program" },
      { name: "操作日志", href: "/settings/logs" },
    ],
    requiredStore: false,
  },
]

// 修改 Sidebar 组件的实现
export function Sidebar() {
  const pathname = usePathname()
  const [collapsed, setCollapsed] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const { user } = useAuth()
  const [hasStores, setHasStores] = useState(false)

  // 模拟从API检查租户是否有门店
  useEffect(() => {
    const checkStores = async () => {
      try {
        // 总是将 hasStores 设置为 true，确保显示所有菜单
        setHasStores(true);
        localStorage.setItem('hasStores', 'true');
        
        /* 原代码
        // 使用本地API检查是否有门店
        const response = await fetch('/api/stores');
        const result = await response.json();
        
        if (result.success) {
          // 如果API返回数据成功
          setHasStores((result.data && result.data.length > 0) || false);
          // 更新localStorage状态
          localStorage.setItem('hasStores', (result.data && result.data.length > 0) ? 'true' : 'false');
        } else {
          // API调用失败，尝试从localStorage获取
          const hasMockStores = localStorage.getItem('hasStores') === 'true';
          setHasStores(hasMockStores);
        }
        */
      } catch (error) {
        console.error('检查门店失败:', error);
        // 即使出错也设置为 true
        setHasStores(true);
        localStorage.setItem('hasStores', 'true');
      }
    };

    if (user) {
      checkStores();
    }
  }, [user]);

  const toggleExpand = (name: string) => {
    setExpandedItems((prev) => {
      // 如果已经展开，则关闭它
      if (prev.includes(name)) {
        return prev.filter((item) => item !== name);
      }
      // 否则展开它
      return [...prev, name];
    });
  }

  // 过滤菜单项 - 基于用户角色和门店状态
  const filteredSidebarItems = useMemo(() => {
    // 临时返回所有菜单项，不进行过滤
    return sidebarItems;
    
    /* 原过滤逻辑
    return sidebarItems.filter(item => {
      // 如果需要门店且没有门店，则不显示该菜单
      if (item.requiredStore && !hasStores) {
        return false;
      }
      
      // 如果需要管理员权限且用户不是管理员，则不显示
      if (item.adminOnly && user?.role !== 'admin') {
        return false;
      }
      
      return true;
    });
    */
  }, [hasStores, user?.role]);

  // 检查当前路径是否匹配某个菜单项或其子项
  const isActive = (item: MenuItem) => {
    if (item.href === pathname) return true
    return item.children.some((child) => child.href === pathname)
  }

  // 使用useMemo优化路径匹配计算
  const activeItems = useMemo(() => {
    return filteredSidebarItems.filter(item =>
      item.href === pathname ||
      item.children.some(child => child.href === pathname)
    ).map(item => item.name);
  }, [pathname, filteredSidebarItems]);

  // 初始化展开状态
  useEffect(() => {
    // 当路径变化时，自动展开包含当前路径的菜单项
    setExpandedItems(prev => {
      const newExpanded = [...prev];
      activeItems.forEach(item => {
        if (!newExpanded.includes(item)) {
          newExpanded.push(item);
        }
      });
      return newExpanded;
    });
  }, [activeItems]);

  // 检查菜单项是否应该展开
  const isExpanded = (name: string) => {
    // 只检查是否在展开数组中，不再自动展开活动项
    return expandedItems.includes(name);
  }

  return (
    <div
      className={cn(
        "flex h-screen flex-col border-r bg-[#1E2A3B] transition-all duration-300",
        collapsed ? "w-16" : "w-64",
      )}
    >
      <div className="flex h-16 items-center border-b border-white/10 px-4">
        {!collapsed ? (
          <>
            <div className="flex items-center">
              <div className="flex items-center justify-center h-8 w-8 rounded-md bg-white mr-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#1E2A3B"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <path d="M12 2L5 12l7 10 7-10z" />
                  <circle cx="12" cy="9" r="3" />
                </svg>
              </div>
              <h1 className="text-xl font-bold text-white">静心瑜伽馆</h1>
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center h-8 w-8 rounded-md bg-white mx-auto">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#1E2A3B"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5"
            >
              <path d="M12 2L5 12l7 10 7-10z" />
              <circle cx="12" cy="9" r="3" />
            </svg>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          className={cn("ml-auto text-white hover:bg-white/10", collapsed && "ml-0 w-full")}
          onClick={() => setCollapsed(!collapsed)}
        >
          <Menu className="h-5 w-5" />
        </Button>
      </div>
      <div className="flex-1 overflow-auto py-2">
        {!collapsed && <OnboardingIndicator />}
        <nav className="grid items-start px-2 text-sm font-medium">
          {filteredSidebarItems.map((item) => (
            <div key={item.name} className="py-1">
              {/* 如果没有子菜单或处于折叠状态，直接渲染为链接 */}
              {item.children.length === 0 || collapsed ? (
                <Link
                  href={item.href || item.children[0]?.href || "#"}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 transition-all",
                    isActive(item) ? "bg-white/10 text-white" : "text-gray-400 hover:bg-white/5 hover:text-white",
                  )}
                >
                  <item.icon className={cn("h-5 w-5", collapsed && "mx-auto")} />
                  {!collapsed && <span>{item.name}</span>}
                </Link>
              ) : (
                <>
                  {/* 父菜单项 */}
                  <button
                    onClick={() => toggleExpand(item.name)}
                    className={cn(
                      "flex w-full items-center justify-between gap-3 rounded-lg px-3 py-2 transition-all",
                      isActive(item) ? "bg-white/10 text-white" : "text-gray-400 hover:bg-white/5 hover:text-white",
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <item.icon className="h-5 w-5" />
                      <span>{item.name}</span>
                    </div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className={cn("transition-transform", isExpanded(item.name) ? "rotate-180" : "rotate-0")}
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </button>

                  {/* 子菜单项 */}
                  {isExpanded(item.name) && (
                    <div className="mt-1 ml-4 pl-4 border-l border-white/10">
                      {item.children.map((child) => (
                        <Link
                          key={child.href}
                          href={child.href}
                          className={cn(
                            "flex items-center py-2 px-2 rounded-lg text-sm transition-all",
                            pathname === child.href
                              ? "text-white bg-white/5"
                              : "text-gray-400 hover:text-white hover:bg-white/5",
                          )}
                        >
                          {child.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </nav>
      </div>
    </div>
  )
}

