// 测试新注册用户登录
// 使用Node.js 18+的内置fetch API

async function testNewUserLogin() {
  try {
    console.log('🧪 测试新注册用户登录...');
    
    // 测试新注册的用户
    const loginData = {
      username: "13886132451", // 新注册的手机号
      password: "123456" // 默认密码
    };
    
    console.log('登录数据:', JSON.stringify(loginData, null, 2));
    
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData)
    });
    
    console.log('📥 登录响应状态:', loginResponse.status);
    
    const loginResult = await loginResponse.json();
    console.log('📥 登录响应数据:', JSON.stringify(loginResult, null, 2));
    
    if (loginResult.code === 200) {
      console.log('✅ 新用户登录成功!');
      console.log(`用户: ${loginResult.data.user.nickname}`);
      console.log(`公司: ${loginResult.data.user.companyName}`);
      console.log(`角色: ${loginResult.data.user.role}`);
      console.log(`手机: ${loginResult.data.user.phone}`);
    } else {
      console.log('❌ 新用户登录失败:', loginResult.message);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testNewUserLogin();
