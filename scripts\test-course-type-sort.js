// 测试课程类型排序功能
async function testCourseTypeSort() {
  console.log('开始测试课程类型排序功能...');
  
  try {
    // 1. 获取当前课程类型列表
    console.log('\n1. 获取当前课程类型列表:');
    const response = await fetch('http://localhost:3005/api/course-types?tenantId=1&sortBy=display_order');
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('当前课程类型排序:');
      result.data.list.forEach(type => {
        console.log(`  ${type.displayOrder}: ${type.name} (课程数: ${type.courseCount})`);
      });
    }
    
    // 2. 添加一个新的课程类型
    console.log('\n2. 添加新课程类型:');
    const newType = {
      tenantId: 1,
      name: '热瑜伽',
      description: '高温环境下的瑜伽练习',
      color: '#FF5722',
      displayOrder: 25, // 插入到空中瑜伽(20)和孕产瑜伽(30)之间
      status: 'active'
    };
    
    const createResponse = await fetch('http://localhost:3005/api/course-types', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newType)
    });
    
    const createResult = await createResponse.json();
    
    if (createResult.code === 200) {
      console.log(`✓ 创建成功: ${createResult.data.name} (排序: ${createResult.data.displayOrder})`);
    } else {
      console.log(`✗ 创建失败: ${createResult.msg}`);
    }
    
    // 3. 再次获取课程类型列表，验证排序
    console.log('\n3. 验证排序结果:');
    const verifyResponse = await fetch('http://localhost:3005/api/course-types?tenantId=1&sortBy=display_order');
    const verifyResult = await verifyResponse.json();
    
    if (verifyResult.code === 200) {
      console.log('更新后的课程类型排序:');
      verifyResult.data.list.forEach(type => {
        console.log(`  ${type.displayOrder}: ${type.name} (课程数: ${type.courseCount})`);
      });
    }
    
    // 4. 测试其他排序方式
    console.log('\n4. 测试按名称排序:');
    const nameResponse = await fetch('http://localhost:3005/api/course-types?tenantId=1&sortBy=name');
    const nameResult = await nameResponse.json();
    
    if (nameResult.code === 200) {
      console.log('按名称排序:');
      nameResult.data.list.forEach(type => {
        console.log(`  ${type.name} (排序: ${type.displayOrder})`);
      });
    }
    
    // 5. 测试按课程数量排序
    console.log('\n5. 测试按课程数量排序:');
    const countResponse = await fetch('http://localhost:3005/api/course-types?tenantId=1&sortBy=course_count');
    const countResult = await countResponse.json();
    
    if (countResult.code === 200) {
      console.log('按课程数量排序:');
      countResult.data.list.forEach(type => {
        console.log(`  ${type.name}: ${type.courseCount} 个课程`);
      });
    }
    
    console.log('\n✓ 课程类型排序功能测试完成!');
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testCourseTypeSort();
