"use client"

import React from "react"
import { <PERSON><PERSON>ircle, Circle } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Progress } from "@/components/ui/progress"
import { useOnboarding } from "@/contexts/onboarding-context"

export function OnboardingIndicator() {
  const { steps, isOnboardingComplete, setShowOnboarding, resetOnboarding } = useOnboarding()
  
  const completedSteps = steps.filter(step => step.completed).length
  const progress = (completedSteps / steps.length) * 100

  if (isOnboardingComplete) {
    return null
  }

  return (
    <div className="px-3 py-2 mt-2">
      <div className="bg-primary/10 rounded-lg p-3">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-primary">系统初始化进度</h3>
          <span className="text-xs text-muted-foreground">
            {completedSteps}/{steps.length}
          </span>
        </div>
        
        <Progress value={progress} className="h-1.5 mb-3" />
        
        <div className="grid grid-cols-3 gap-1">
          {steps.map((step, index) => (
            <TooltipProvider key={step.id} delayDuration={300}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    className="flex items-center justify-center h-6 w-6 rounded-full"
                    onClick={() => setShowOnboarding(true)}
                  >
                    {step.completed ? (
                      <CheckCircle className="h-5 w-5 text-primary" />
                    ) : (
                      <Circle className="h-5 w-5 text-muted-foreground" />
                    )}
                  </button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p className="font-medium">{step.title}</p>
                  <p className="text-xs text-muted-foreground">{step.description}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
        </div>
        
        <button
          className="w-full mt-2 text-xs text-center text-primary hover:underline"
          onClick={() => setShowOnboarding(true)}
        >
          继续设置
        </button>
      </div>
    </div>
  )
}
