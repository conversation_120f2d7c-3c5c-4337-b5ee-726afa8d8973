"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  BarChart, 
  BellRing, 
  UserCheck, 
  Clock, 
  Activity, 
  Smartphone, 
  Globe,
  ChevronRight,
  Calendar,
  Target,
  TrendingUp,
  Users,
  MessageSquare,
  Tag,
  Zap
} from "lucide-react"
import Link from "next/link"

interface PremiumFeaturesProps {
  className?: string
}

export function PremiumFeatures({ className }: PremiumFeaturesProps) {
  return (
    <div className={`space-y-8 ${className}`}>
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold tracking-tight">高级功能</h2>
        <Link href="/premium-services/advanced-leads">
          <Button variant="link" className="text-primary">
            查看更多高级功能
            <ChevronRight className="ml-1 h-4 w-4" />
          </Button>
        </Link>
      </div>

      {/* 主要高级功能 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* 智能跟进提醒 */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <BellRing className="mr-2 h-5 w-5 text-primary" />
              智能跟进提醒
            </CardTitle>
            <CardDescription>自动提醒跟进时间，不错过任何潜在客户</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span>今日待跟进</span>
                <Badge>5</Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>明日待跟进</span>
                <Badge variant="outline">3</Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>已逾期未跟进</span>
                <Badge variant="destructive">2</Badge>
              </div>
              <Button size="sm" className="w-full">
                <BellRing className="mr-2 h-4 w-4" />
                查看全部提醒
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 潜客画像分析 */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <UserCheck className="mr-2 h-5 w-5 text-primary" />
              潜客画像分析
            </CardTitle>
            <CardDescription>深入了解潜客特征，精准营销</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span>潜客兴趣分布</span>
                <Button variant="ghost" size="sm">查看</Button>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>年龄段分布</span>
                <Button variant="ghost" size="sm">查看</Button>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>消费能力分析</span>
                <Button variant="ghost" size="sm">查看</Button>
              </div>
              <Button size="sm" className="w-full">
                <UserCheck className="mr-2 h-4 w-4" />
                查看完整画像
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 转化漏斗分析 */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <BarChart className="mr-2 h-5 w-5 text-primary" />
              转化漏斗分析
            </CardTitle>
            <CardDescription>分析潜客转化各环节数据，优化流程</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span>本月转化率</span>
                <Badge className="bg-green-500">32%</Badge>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>环比上月</span>
                <span className="text-green-500">↑ 5%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>流失率</span>
                <Badge variant="outline" className="text-red-500">15%</Badge>
              </div>
              <Button size="sm" className="w-full">
                <BarChart className="mr-2 h-4 w-4" />
                查看详细报告
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* 访客行为分析 */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="mr-2 h-5 w-5 text-primary" />
              访客行为分析
            </CardTitle>
            <CardDescription>分析潜客访问路径、停留时间和交互行为</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>平均停留时长</span>
                </div>
                <span className="font-medium">3分42秒</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Activity className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>页面浏览量</span>
                </div>
                <span className="font-medium">5.2页/访客</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Smartphone className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>移动端占比</span>
                </div>
                <span className="font-medium">78%</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Globe className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>主要来源渠道</span>
                </div>
                <span className="font-medium">微信小程序</span>
              </div>
              <Button size="sm" className="w-full mt-2">查看详细数据</Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="mr-2 h-5 w-5 text-primary" />
              热门访问路径
            </CardTitle>
            <CardDescription>了解潜客最常访问的页面和路径</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <Badge variant="outline" className="mr-2">1</Badge>
                    <span>首页 → 课程介绍 → 预约体验</span>
                  </div>
                  <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                    <div className="bg-primary h-2 rounded-full" style={{ width: "42%" }}></div>
                  </div>
                </div>
                <span className="ml-4 text-sm font-medium">42%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <Badge variant="outline" className="mr-2">2</Badge>
                    <span>首页 → 教练团队 → 课程介绍</span>
                  </div>
                  <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                    <div className="bg-primary h-2 rounded-full" style={{ width: "28%" }}></div>
                  </div>
                </div>
                <span className="ml-4 text-sm font-medium">28%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center">
                    <Badge variant="outline" className="mr-2">3</Badge>
                    <span>活动页 → 会员卡介绍 → 联系我们</span>
                  </div>
                  <div className="w-full bg-secondary h-2 mt-1 rounded-full">
                    <div className="bg-primary h-2 rounded-full" style={{ width: "17%" }}></div>
                  </div>
                </div>
                <span className="ml-4 text-sm font-medium">17%</span>
              </div>
              
              <Button size="sm" className="w-full mt-2">查看更多路径</Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 更多高级功能入口 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
          <CardContent className="p-4 flex flex-col items-center justify-center text-center">
            <Calendar className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-medium">智能排期</h3>
            <p className="text-xs text-muted-foreground mt-1">自动推荐最佳跟进时间</p>
          </CardContent>
        </Card>
        
        <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
          <CardContent className="p-4 flex flex-col items-center justify-center text-center">
            <TrendingUp className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-medium">预测分析</h3>
            <p className="text-xs text-muted-foreground mt-1">预测潜客转化概率</p>
          </CardContent>
        </Card>
        
        <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
          <CardContent className="p-4 flex flex-col items-center justify-center text-center">
            <Users className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-medium">团队协作</h3>
            <p className="text-xs text-muted-foreground mt-1">多人协同跟进潜客</p>
          </CardContent>
        </Card>
        
        <Card className="hover:bg-muted/50 transition-colors cursor-pointer">
          <CardContent className="p-4 flex flex-col items-center justify-center text-center">
            <Zap className="h-8 w-8 text-primary mb-2" />
            <h3 className="font-medium">自动化营销</h3>
            <p className="text-xs text-muted-foreground mt-1">智能触发营销活动</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
