import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';

// 获取路径参数
interface Params {
  params: {
    id: string;
  };
}

// GET /api/venues/[id] - 获取场地详情
export async function GET(request: NextRequest, { params }: Params) {
  try {
    const { id: paramId } = await params;
    const id = parseInt(paramId);
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的场地ID',
        data: null
      }, { status: 400 });
    }
    
    // 查询场地详情，包含课程数量统计
    const venue = await prisma.venue.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            courses: true
          }
        },
        courses: {
          select: {
            id: true,
            title: true,
            status: true,
            created_at: true
          },
          orderBy: { created_at: 'desc' },
          take: 10 // 最近10个课程
        }
      }
    });

    // 检查是否存在
    if (!venue) {
      return NextResponse.json({
        code: 404,
        msg: '未找到场地',
        data: null
      }, { status: 404 });
    }

    // 格式化返回数据
    const formattedVenue = {
      id: venue.id,
      name: venue.name,
      location: venue.location,
      capacity: venue.capacity,
      area: venue.area,
      equipment: venue.equipment ? JSON.parse(venue.equipment) : [],
      description: venue.description,
      hourlyRate: venue.hourly_rate,
      status: venue.status === 1 ? 'active' : 'inactive',
      courseCount: venue._count.courses,
      recentCourses: venue.courses.map(course => ({
        id: course.id,
        title: course.title,
        status: course.status === 1 ? 'active' : 'inactive',
        createdAt: course.created_at?.toISOString().split('T')[0]
      })),
      createdAt: venue.created_at?.toISOString().split('T')[0],
      updatedAt: venue.updated_at?.toISOString().split('T')[0]
    };

    return NextResponse.json({
      code: 200,
      msg: '获取场地详情成功',
      data: formattedVenue
    });

  } catch (error) {
    console.error('获取场地详情失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取场地详情失败',
      data: null
    }, { status: 500 });
  }
}

// PUT /api/venues/[id] - 更新场地
export async function PUT(request: NextRequest, { params }: Params) {
  try {
    const { id: paramId } = await params;
    const id = parseInt(paramId);
    const body = await request.json();
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的场地ID',
        data: null
      }, { status: 400 });
    }

    const {
      name,
      location,
      capacity,
      area,
      equipment,
      description,
      hourlyRate,
      status
    } = body;

    console.log('更新场地API:', id, body);

    // 检查场地是否存在
    const existingVenue = await prisma.venue.findUnique({
      where: { id }
    });

    if (!existingVenue) {
      return NextResponse.json({
        code: 404,
        msg: '未找到场地',
        data: null
      }, { status: 404 });
    }

    // 如果更新场地名称，检查是否重复
    if (name && name !== existingVenue.name) {
      const duplicateVenue = await prisma.venue.findFirst({
        where: {
          tenant_id: existingVenue.tenant_id,
          name: name,
          id: { not: id }
        }
      });

      if (duplicateVenue) {
        return NextResponse.json({
          code: 400,
          msg: '场地名称已存在',
          data: null
        }, { status: 400 });
      }
    }

    // 更新场地
    const updatedVenue = await prisma.venue.update({
      where: { id },
      data: {
        name,
        location,
        capacity: capacity ? parseInt(capacity) : null,
        area: area ? parseFloat(area) : null,
        equipment: equipment ? JSON.stringify(equipment) : null,
        description,
        hourly_rate: hourlyRate ? parseFloat(hourlyRate) : null,
        status: status === 'active' ? 1 : 0
      }
    });

    console.log('场地更新成功:', updatedVenue.id);

    return NextResponse.json({
      code: 200,
      msg: '更新场地成功',
      data: {
        id: updatedVenue.id,
        name: updatedVenue.name,
        location: updatedVenue.location,
        capacity: updatedVenue.capacity,
        area: updatedVenue.area,
        equipment: updatedVenue.equipment ? JSON.parse(updatedVenue.equipment) : [],
        description: updatedVenue.description,
        hourlyRate: updatedVenue.hourly_rate,
        status: updatedVenue.status === 1 ? 'active' : 'inactive',
        updatedAt: updatedVenue.updated_at?.toISOString().split('T')[0]
      }
    });

  } catch (error) {
    console.error('更新场地失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '更新场地失败',
      data: null
    }, { status: 500 });
  }
}

// DELETE /api/venues/[id] - 删除场地
export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    const { id: paramId } = await params;
    const id = parseInt(paramId);
    
    // 验证ID
    if (isNaN(id)) {
      return NextResponse.json({
        code: 400,
        msg: '无效的场地ID',
        data: null
      }, { status: 400 });
    }

    console.log('删除场地API:', id);

    // 检查场地是否存在
    const venue = await prisma.venue.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            courses: true
          }
        }
      }
    });

    if (!venue) {
      return NextResponse.json({
        code: 404,
        msg: '未找到场地',
        data: null
      }, { status: 404 });
    }

    // 检查是否有关联课程
    if (venue._count.courses > 0) {
      return NextResponse.json({
        code: 400,
        msg: `该场地下有 ${venue._count.courses} 个关联课程，无法删除`,
        data: null
      }, { status: 400 });
    }

    // 删除场地
    await prisma.venue.delete({
      where: { id }
    });

    console.log('场地删除成功:', id);

    return NextResponse.json({
      code: 200,
      msg: '删除场地成功',
      data: null
    });

  } catch (error) {
    console.error('删除场地失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '删除场地失败',
      data: null
    }, { status: 500 });
  }
}
