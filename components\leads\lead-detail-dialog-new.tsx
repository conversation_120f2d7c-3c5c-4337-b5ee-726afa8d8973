"use client"

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogTitle,
  DialogHeader
} from "@/components/ui/dialog"
import { LeadDetailPage } from "@/components/leads/lead-detail-page"

interface LeadDetailDialogProps {
  lead: any
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function LeadDetailDialog({ lead, open, onOpenChange }: LeadDetailDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto p-6">
        <DialogHeader className="sr-only">
          <DialogTitle>潜客详情</DialogTitle>
        </DialogHeader>
        <LeadDetailPage
          lead={lead}
          onClose={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  )
}
