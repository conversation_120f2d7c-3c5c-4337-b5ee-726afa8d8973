"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Download, Upload, AlertCircle, CheckCircle2 } from "lucide-react"

interface ImportMembersDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ImportMembersDialog({ open, onOpenChange }: ImportMembersDialogProps) {
  const [activeTab, setActiveTab] = useState("upload")
  const [file, setFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [importStatus, setImportStatus] = useState<
    "idle" | "uploading" | "validating" | "importing" | "success" | "error"
  >("idle")
  const [importResult, setImportResult] = useState<{
    total: number
    success: number
    failed: number
    errors: Array<{ row: number; message: string }>
  } | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0])
    }
  }

  const handleUpload = () => {
    if (!file) return

    setImportStatus("uploading")
    setUploadProgress(0)

    // 模拟上传进度
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setImportStatus("validating")

          // 模拟验证和导入过程
          setTimeout(() => {
            setImportStatus("importing")

            setTimeout(() => {
              setImportStatus("success")
              setImportResult({
                total: 50,
                success: 47,
                failed: 3,
                errors: [
                  { row: 5, message: "手机号格式不正确" },
                  { row: 12, message: "会员卡类型不存在" },
                  { row: 28, message: "必填字段缺失" },
                ],
              })
              setActiveTab("result")
            }, 2000)
          }, 1500)
          return 100
        }
        return prev + 10
      })
    }, 300)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-3xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>导入会员</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload">上传文件</TabsTrigger>
            <TabsTrigger value="template">模板说明</TabsTrigger>
            <TabsTrigger value="result" disabled={importStatus !== "success" && importStatus !== "error"}>
              导入结果
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="mt-4 space-y-4">
            <div className="rounded-lg border border-dashed p-8">
              <div className="flex flex-col items-center justify-center space-y-4 text-center">
                <Upload className="h-12 w-12 text-muted-foreground" />
                <div>
                  <h3 className="text-lg font-medium">上传会员数据文件</h3>
                  <p className="text-sm text-muted-foreground">支持 Excel (.xlsx) 或 CSV 格式文件</p>
                </div>

                <div className="flex w-full max-w-sm flex-col gap-2">
                  <input
                    type="file"
                    id="file-upload"
                    className="hidden"
                    accept=".xlsx,.csv"
                    onChange={handleFileChange}
                  />
                  <label htmlFor="file-upload">
                    <Button className="w-full" variant="outline" asChild>
                      <span>选择文件</span>
                    </Button>
                  </label>

                  <Button variant="outline" onClick={() => setActiveTab("template")}>
                    <Download className="mr-2 h-4 w-4" />
                    下载导入模板
                  </Button>
                </div>

                {file && (
                  <div className="w-full max-w-sm rounded-lg border p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{file.name.endsWith(".csv") ? "CSV" : "Excel"}</Badge>
                        <span className="font-medium">{file.name}</span>
                      </div>
                      <span className="text-sm text-muted-foreground">{(file.size / 1024).toFixed(1)} KB</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {importStatus !== "idle" && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">
                      {importStatus === "uploading"
                        ? "上传中..."
                        : importStatus === "validating"
                          ? "验证中..."
                          : importStatus === "importing"
                            ? "导入中..."
                            : importStatus === "success"
                              ? "导入完成"
                              : "导入失败"}
                    </span>
                    <span className="text-sm text-muted-foreground">{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="h-2 w-full" />
                </div>

                {importStatus === "error" && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>导入失败</AlertTitle>
                    <AlertDescription>文件格式不正确或服务器错误，请检查文件格式后重试。</AlertDescription>
                  </Alert>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="template" className="mt-4 space-y-4">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium">导入模板说明</h3>
                <p className="text-sm text-muted-foreground">请按照以下格式准备会员数据，确保必填字段不为空</p>
              </div>

              <div className="rounded-lg border">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="p-2 text-left text-sm font-medium">字段名</th>
                        <th className="p-2 text-left text-sm font-medium">是否必填</th>
                        <th className="p-2 text-left text-sm font-medium">说明</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="p-2 text-sm">姓名</td>
                        <td className="p-2 text-sm">是</td>
                        <td className="p-2 text-sm">会员姓名</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2 text-sm">手机号</td>
                        <td className="p-2 text-sm">是</td>
                        <td className="p-2 text-sm">11位手机号码</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2 text-sm">性别</td>
                        <td className="p-2 text-sm">否</td>
                        <td className="p-2 text-sm">男/女</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2 text-sm">生日</td>
                        <td className="p-2 text-sm">否</td>
                        <td className="p-2 text-sm">格式：YYYY-MM-DD</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2 text-sm">会员卡类型</td>
                        <td className="p-2 text-sm">是</td>
                        <td className="p-2 text-sm">年卡/季卡/月卡/次卡20次/次卡10次</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2 text-sm">开始日期</td>
                        <td className="p-2 text-sm">是</td>
                        <td className="p-2 text-sm">格式：YYYY-MM-DD</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2 text-sm">会员等级</td>
                        <td className="p-2 text-sm">否</td>
                        <td className="p-2 text-sm">标准会员/银卡会员/金卡会员/白金会员</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2 text-sm">标签</td>
                        <td className="p-2 text-sm">否</td>
                        <td className="p-2 text-sm">多个标签用逗号分隔</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2 text-sm">邮箱</td>
                        <td className="p-2 text-sm">否</td>
                        <td className="p-2 text-sm">电子邮箱地址</td>
                      </tr>
                      <tr>
                        <td className="p-2 text-sm">备注</td>
                        <td className="p-2 text-sm">否</td>
                        <td className="p-2 text-sm">会员备注信息</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">注意事项：</h4>
                <ul className="list-inside list-disc space-y-1 text-sm">
                  <li>请确保Excel文件的第一行为字段名</li>
                  <li>手机号必须为11位有效手机号码</li>
                  <li>会员卡类型必须与系统中已有的卡类型匹配</li>
                  <li>日期格式必须为YYYY-MM-DD，如2023-01-01</li>
                  <li>导入前请确保数据格式正确，避免导入失败</li>
                </ul>
              </div>

              <div className="flex justify-center">
                <Button>
                  <Download className="mr-2 h-4 w-4" />
                  下载导入模板
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="result" className="mt-4 space-y-4">
            {importResult && (
              <div className="space-y-4">
                <div className="rounded-lg border p-4">
                  <div className="flex flex-col items-center justify-center space-y-2 text-center">
                    {importResult.failed === 0 ? (
                      <CheckCircle2 className="h-12 w-12 text-green-500" />
                    ) : (
                      <AlertCircle className="h-12 w-12 text-yellow-500" />
                    )}

                    <h3 className="text-lg font-medium">{importResult.failed === 0 ? "导入成功" : "部分导入成功"}</h3>

                    <div className="flex gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold">{importResult.total}</p>
                        <p className="text-sm text-muted-foreground">总记录数</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-500">{importResult.success}</p>
                        <p className="text-sm text-muted-foreground">成功</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-red-500">{importResult.failed}</p>
                        <p className="text-sm text-muted-foreground">失败</p>
                      </div>
                    </div>
                  </div>
                </div>

                {importResult.failed > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">错误详情：</h4>
                    <div className="max-h-60 overflow-y-auto rounded-lg border">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b bg-muted/50">
                            <th className="p-2 text-left text-sm font-medium">行号</th>
                            <th className="p-2 text-left text-sm font-medium">错误信息</th>
                          </tr>
                        </thead>
                        <tbody>
                          {importResult.errors.map((error, index) => (
                            <tr key={index} className="border-b">
                              <td className="p-2 text-sm">{error.row}</td>
                              <td className="p-2 text-sm">{error.message}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => onOpenChange(false)}>
                    关闭
                  </Button>
                  {importResult.failed > 0 && (
                    <Button>
                      <Download className="mr-2 h-4 w-4" />
                      下载错误报告
                    </Button>
                  )}
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {activeTab === "upload" && (
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleUpload} disabled={!file || importStatus !== "idle"}>
              开始导入
            </Button>
          </div>
        )}

        {activeTab === "template" && (
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={() => setActiveTab("upload")}>返回上传</Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

