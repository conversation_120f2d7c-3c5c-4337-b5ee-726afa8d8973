"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { useForm } from "react-hook-form"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { Loader2, QrCode, Mail, Smartphone } from "lucide-react"
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { authApi } from "@/lib/api/auth"
import { useAuth } from "@/contexts/auth-context"

// 定义登录表单数据类型
interface LoginFormData {
  username: string;
  password: string;
  rememberMe: boolean;
  tenant_id: number;
  verifyCode?: string; // 验证码
  phone?: string; // 手机号
  email?: string; // 邮箱
}

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectUrl = searchParams.get('redirect') || '/';
  const { toast } = useToast();
  const { isAuthenticated } = useAuth();

  // 登录方式状态
  const [loginType, setLoginType] = useState<'username' | 'phone' | 'email' | 'qrcode'>('username');

  // 表单状态管理
  const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm<LoginFormData>({
    defaultValues: {
      rememberMe: false,
      tenant_id: 1
    }
  })

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)
  // 租户列表
  const [tenants, setTenants] = useState([
    { id: 1, name: '默认租户' },
    { id: 2, name: '北京分馆' },
    { id: 3, name: '上海分馆' }
  ]);
  // 验证码相关状态
  const [verifyCodeImage, setVerifyCodeImage] = useState<string>('y 9 7 7');
  const [countdown, setCountdown] = useState(0); // 短信验证码倒计时

  // 监听租户选择变化
  const selectedTenantId = watch("tenant_id");
  
  // 获取验证码函数
  const getVerifyCode = () => {
    // 模拟获取图形验证码
    // 实际项目中应该调用后端API获取验证码图片
    setVerifyCodeImage('y 9 7 7');
  }
  
  // 发送短信验证码
  const sendSmsCode = () => {
    if (countdown > 0) return;
    
    // 模拟发送短信验证码
    // 实际项目中应该调用后端API发送短信
    setCountdown(60);
    
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    toast({
      title: "验证码已发送",
      description: "请查看手机短信",
    });
  }
  
  // 获取租户列表
  useEffect(() => {
    // 这里可以添加获取租户列表的API调用
    // 示例: const fetchTenants = async () => { ... }
    // fetchTenants()
    
    // 初始化获取验证码
    getVerifyCode();
  }, [])

  // 登录处理函数
  const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true)
      
      // 调用登录API
      const response = await authApi.login({
        username: data.username,
        password: data.password,
        tenant_id: data.tenant_id,
      })
      
      console.log('-----登录响应详情-----');
      console.log(JSON.stringify(response, null, 2));
      console.log('---------------------');
      
      // 强制类型转换并向下解析
      const loginResponse = response as any;
      
      // 确定登录是否成功 (code === 0 且返回了数据)
      const isLoginSuccess = 
        loginResponse && 
        loginResponse.code === 0 && 
        loginResponse.data;
      
      if (isLoginSuccess) {
        // 获取data对象
        const responseData = loginResponse.data;
        
        // 提取token和用户信息 (不管嵌套结构如何)
        let accessToken = '';
        let tokenType = 'bearer';
        let userInfo = null;
        
        // 尝试直接读取
        if (responseData.access_token && responseData.user_info) {
          accessToken = responseData.access_token;
          tokenType = responseData.token_type || 'bearer';
          userInfo = responseData.user_info;
          console.log('成功读取 - 一级结构');
        }
        
        // 如果没有找到，则尝试读取嵌套数据
        if (!accessToken && responseData.data) {
          const nestedData = responseData.data;
          if (nestedData.access_token && nestedData.user_info) {
            accessToken = nestedData.access_token;
            tokenType = nestedData.token_type || 'bearer';
            userInfo = nestedData.user_info;
            console.log('成功读取 - 二级结构');
          }
        }
        
        // 检查是否成功提取到数据
        if (accessToken && userInfo) {
          console.log('登录成功，提取到数据:', {
            token首部: accessToken.substring(0, 10) + '...',
            token类型: tokenType,
            用户ID: userInfo.id,
            用户名: userInfo.username
          });
          
          try {
            // 清除旧数据
            localStorage.removeItem('token');
            localStorage.removeItem('token_type');
            localStorage.removeItem('userInfo');
            localStorage.removeItem('tenant_id');
            sessionStorage.removeItem('token');
            sessionStorage.removeItem('token_type');
            sessionStorage.removeItem('userInfo');
            sessionStorage.removeItem('tenant_id');
            
            // 确定存储位置
            const storage = data.rememberMe ? localStorage : sessionStorage;
            
            // 存储新数据
            storage.setItem('token', accessToken);
            storage.setItem('userInfo', JSON.stringify(userInfo));
            storage.setItem('tenant_id', String(userInfo.tenant_id || data.tenant_id || 1));
            
            // 检查存储状态
            const savedToken = storage.getItem('token');
            console.log('存储状态:', {
              已存储: !!savedToken,
              token长度: savedToken?.length
            });
            
            // 显示成功消息
            toast({
              title: "登录成功",
              description: `欢迎回来，${userInfo.nickname || userInfo.username}`
            });
            
            // 使用window.location直接跳转到工作台，避免路由冲突
            setTimeout(() => {
              window.location.href = '/dashboard';
            }, 500);
            
            return; // 成功登录，提前返回
          } catch (error) {
            console.error('存储登录信息失败:', error);
          }
        } else {
          console.error('无法从响应中提取token和用户信息:', responseData);
        }
      }
      
      // 如果执行到这里，说明登录失败或提取数据失败
      console.error('登录处理失败:', loginResponse);
      
      // 尝试获取错误消息
      let errorMessage = "登录失败，请检查用户名和密码";
      
      if (loginResponse && loginResponse.message) {
        errorMessage = loginResponse.message;
      }
      
      toast({
        title: "登录失败",
        description: errorMessage,
        variant: "destructive"
      });
      
    } catch (error: any) {
      console.error('登录过程发生异常:', error);
      toast({
        title: "登录失败",
        description: error?.message || "发生未知错误，请稍后再试",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }

  // 检查是否已登录，如果已登录则直接跳转到后台
  useEffect(() => {
    // 获取当前路径
    const pathname = window.location.pathname;
    
    // 只有当前在登录页面时才执行检查
    if (pathname === '/login') {
      // 获取token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      
      // 如果有token，说明已经登录，直接跳转到后台
      if (token) {
        console.log('检测到有效登录信息，直接进入后台');
        router.push('/dashboard');
      }
    }
  }, [router])

  return (
    <div className="min-h-screen bg-white">
      {/* 导航栏 */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link href="/home">
                <Image 
                  src="https://ai-public.mastergo.com/ai/img_res/8440626a989270c41c4abf3cd0025b9d.jpg" 
                  alt="静心瑜伽生活馆" 
                  width={120}
                  height={32}
                  className="h-8 w-auto"
                />
              </Link>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#" className="text-gray-600 hover:text-indigo-600">产品功能</a>
              <a href="#" className="text-gray-600 hover:text-indigo-600">解决方案</a>
              <a href="#" className="text-gray-600 hover:text-indigo-600">客户案例</a>
              <a href="#" className="text-gray-600 hover:text-indigo-600">定价方案</a>
              <a href="#" className="text-gray-600 hover:text-indigo-600">帮助中心</a>
              <Link href="/home" className="text-gray-600 hover:text-indigo-600 font-medium ml-4">
                返回首页
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 登录页面内容 */}
      <div className="pt-20 flex min-h-screen flex-col md:flex-row">
        {/* 左侧品牌区域 - 在移动设备上隐藏 */}
        <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-blue-50 via-indigo-100 to-blue-200 items-center justify-center relative overflow-hidden">
          <div className="absolute inset-0 opacity-20">
            <svg className="h-full w-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="url(#grid)" />
              <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                  <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#6366f1" strokeWidth="0.3" />
                </pattern>
              </defs>
            </svg>
          </div>
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-indigo-200/30 to-transparent opacity-40"></div>
          <div className="text-center text-indigo-900 px-12 relative z-10">
            <div className="inline-block p-3 bg-white/80 shadow-md rounded-lg backdrop-blur-sm mb-6">
              <Image 
                src="https://ai-public.mastergo.com/ai/img_res/8440626a989270c41c4abf3cd0025b9d.jpg" 
                alt="静心瑜伽生活馆" 
                width={60}
                height={60}
                className="h-12 w-auto"
              />
            </div>
            <h1 className="text-4xl font-bold mb-4 text-indigo-800">瑜伽生活会馆管理系统</h1>
            <div className="w-16 h-1 bg-indigo-400 mx-auto mb-6 rounded-full"></div>
            <p className="text-lg text-indigo-700 mb-8 leading-relaxed">
              专业的瑜伽馆管理解决方案<br/>轻松管理会员、课程、排课和财务
            </p>
            <div className="relative mx-auto w-64 h-64 mb-4">
              <div className="absolute -inset-4 bg-white/50 rounded-full blur-xl"></div>
              <div className="relative z-10">
                <Image 
                  src="https://ai-public.mastergo.com/ai/img_res/3e2e8a4f9d8b3d3c6e7d8b3d3c6e7d8b.png" 
                  alt="瑜伽插图" 
                  width={300} 
                  height={250} 
                  className="object-contain"
                />
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-blue-100/50 to-transparent opacity-50 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* 右侧登录表单 */}
        <div className="flex-1 flex items-center justify-center p-8 bg-white">
          <div className="w-full max-w-md relative">
            {/* 装饰元素 */}
            <div className="absolute -top-6 -left-6 w-12 h-12 rounded-full bg-indigo-100 opacity-70"></div>
            <div className="absolute -bottom-6 -right-6 w-12 h-12 rounded-full bg-indigo-100 opacity-70"></div>
            <div className="absolute top-1/4 -right-3 w-6 h-6 rounded-full bg-purple-100 opacity-70"></div>
            <div className="absolute bottom-1/4 -left-3 w-6 h-6 rounded-full bg-purple-100 opacity-70"></div>
            
            {/* 移动设备上显示的标题 */}
            <div className="md:hidden mb-8 text-center">
              <h1 className="text-2xl font-bold text-gray-900">登录瑜伽生活管理系统</h1>
              <p className="mt-2 text-gray-600">请输入您的账号和密码</p>
            </div>

            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900">欢迎回来</h2>
              <p className="text-gray-500 mt-2">请登录您的账号继续使用</p>
            </div>
            
            {/* 登录方式切换 */}
            <div className="bg-blue-50 rounded-lg p-1 flex border border-blue-100 mb-6 shadow-sm">
              <button
                type="button"
                className={`flex-1 py-2 text-center font-medium rounded-md transition-all ${loginType === 'username' ? 'bg-white text-indigo-700 shadow-sm' : 'text-indigo-500 hover:text-indigo-600'}`}
                onClick={() => setLoginType('username')}
              >
                <span className="inline-flex items-center justify-center">用户名</span>
              </button>
              <button
                type="button"
                className={`flex-1 py-2 text-center font-medium rounded-md transition-all ${loginType === 'phone' ? 'bg-white text-indigo-700 shadow-sm' : 'text-indigo-500 hover:text-indigo-600'}`}
                onClick={() => setLoginType('phone')}
              >
                <span className="inline-flex items-center justify-center"><Smartphone className="w-4 h-4 mr-1" />手机</span>
              </button>
              <button
                type="button"
                className={`flex-1 py-2 text-center font-medium rounded-md transition-all ${loginType === 'email' ? 'bg-white text-indigo-700 shadow-sm' : 'text-indigo-500 hover:text-indigo-600'}`}
                onClick={() => setLoginType('email')}
              >
                <span className="inline-flex items-center justify-center"><Mail className="w-4 h-4 mr-1" />邮箱</span>
              </button>
              <button
                type="button"
                className={`flex-1 py-2 text-center font-medium rounded-md transition-all ${loginType === 'qrcode' ? 'bg-white text-indigo-700 shadow-sm' : 'text-indigo-500 hover:text-indigo-600'}`}
                onClick={() => setLoginType('qrcode')}
              >
                <span className="inline-flex items-center justify-center"><QrCode className="w-4 h-4 mr-1" />扫码</span>
              </button>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {loginType === 'username' && (
                <div className="space-y-2">
                  <Label htmlFor="username" className="text-gray-700 font-medium">用户名</Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <Input 
                      id="username"
                      placeholder="请输入用户名" 
                      className="pl-10 bg-blue-50/50 border-blue-100 focus:bg-white focus:ring-2 focus:ring-indigo-200 rounded-md"
                      disabled={isLoading}
                      {...register("username", { 
                        required: "用户名是必填项",
                        minLength: { value: 2, message: "用户名至少需要2个字符" } 
                      })}
                    />
                  </div>
                  {errors.username && (
                    <p className="text-sm text-red-500">{errors.username.message}</p>
                  )}
                </div>
              )}
              
              {loginType === 'phone' && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-gray-700 font-medium">手机号</Label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Smartphone className="h-5 w-5 text-gray-400" />
                      </div>
                      <Input 
                        id="phone"
                        placeholder="请输入手机号" 
                        className="pl-10 bg-blue-50/50 border-blue-100 focus:bg-white focus:ring-2 focus:ring-indigo-200 rounded-md"
                        disabled={isLoading}
                        {...register("phone", { 
                          required: "手机号是必填项",
                          pattern: {
                            value: /^1[3-9]\d{9}$/,
                            message: "请输入有效的手机号"
                          }
                        })}
                      />
                    </div>
                    {errors.phone && (
                      <p className="text-sm text-red-500">{errors.phone.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="smsCode" className="text-gray-700 font-medium">短信验证码</Label>
                    <div className="flex space-x-2">
                      <div className="relative flex-1">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M2 3a1 1 0 011-1h14a1 1 0 011 1v14a1 1 0 01-1 1H3a1 1 0 01-1-1V3z" />
                            <path d="M6 6h8v2H6V6zM6 10h4v2H6v-2z" />
                          </svg>
                        </div>
                        <Input 
                          id="smsCode"
                          placeholder="请输入验证码" 
                          className="pl-10 bg-blue-50/50 border-blue-100 focus:bg-white focus:ring-2 focus:ring-indigo-200 rounded-md"
                          disabled={isLoading}
                          {...register("verifyCode", { 
                            required: "验证码是必填项"
                          })}
                        />
                      </div>
                      <Button 
                        type="button" 
                        variant="outline" 
                        className="w-32 border-blue-200 hover:bg-blue-50 hover:text-indigo-600 transition-colors"
                        disabled={countdown > 0 || isLoading}
                        onClick={sendSmsCode}
                      >
                        {countdown > 0 ? `${countdown}秒后重发` : '获取验证码'}
                      </Button>
                    </div>
                    {errors.verifyCode && (
                      <p className="text-sm text-red-500">{errors.verifyCode.message}</p>
                    )}
                  </div>
                </>
              )}
              
              {loginType === 'email' && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-gray-700 font-medium">邮箱地址</Label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="h-5 w-5 text-gray-400" />
                      </div>
                      <Input 
                        id="email"
                        placeholder="请输入邮箱地址" 
                        className="pl-10 bg-blue-50/50 border-blue-100 focus:bg-white focus:ring-2 focus:ring-indigo-200 rounded-md"
                        disabled={isLoading}
                        {...register("email", { 
                          required: "邮箱地址是必填项",
                          pattern: {
                            value: /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/,
                            message: "请输入有效的邮箱地址"
                          }
                        })}
                      />
                    </div>
                    {errors.email && (
                      <p className="text-sm text-red-500">{errors.email.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="emailCode" className="text-gray-700 font-medium">邮箱验证码</Label>
                    <div className="flex space-x-2">
                      <div className="relative flex-1">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M2 3a1 1 0 011-1h14a1 1 0 011 1v14a1 1 0 01-1 1H3a1 1 0 01-1-1V3z" />
                            <path d="M6 6h8v2H6V6zM6 10h4v2H6v-2z" />
                          </svg>
                        </div>
                        <Input 
                          id="emailCode"
                          placeholder="请输入验证码" 
                          className="pl-10 bg-blue-50/50 border-blue-100 focus:bg-white focus:ring-2 focus:ring-indigo-200 rounded-md"
                          disabled={isLoading}
                          {...register("verifyCode", { 
                            required: "验证码是必填项"
                          })}
                        />
                      </div>
                      <Button 
                        type="button" 
                        variant="outline" 
                        className="w-32 border-blue-200 hover:bg-blue-50 hover:text-indigo-600 transition-colors"
                        disabled={countdown > 0 || isLoading}
                        onClick={sendSmsCode}
                      >
                        {countdown > 0 ? `${countdown}秒后重发` : '获取验证码'}
                      </Button>
                    </div>
                    {errors.verifyCode && (
                      <p className="text-sm text-red-500">{errors.verifyCode.message}</p>
                    )}
                  </div>
                </>
              )}
              
              {loginType === 'qrcode' && (
                <div className="py-8 flex flex-col items-center">
                  <div className="w-56 h-56 bg-blue-50 border border-blue-100 rounded-lg flex items-center justify-center mb-6 shadow-sm relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 to-transparent opacity-50"></div>
                    <div className="absolute top-0 left-0 w-full h-6 bg-gradient-to-r from-indigo-100 to-blue-100 opacity-30"></div>
                    <div className="absolute bottom-0 right-0 w-full h-6 bg-gradient-to-l from-indigo-100 to-blue-100 opacity-30"></div>
                    <div className="relative z-10 p-2 bg-white rounded-md shadow-sm">
                      <QrCode className="w-40 h-40 text-indigo-800" />
                    </div>
                  </div>
                  <p className="text-gray-700 text-center font-medium">请使用手机扫描二维码登录</p>
                  <p className="text-gray-500 text-sm text-center mt-2">扫码登录更快捷更安全</p>
                </div>
              )}

              {(loginType === 'username') && (
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-gray-700 font-medium">密码</Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" />
                      </svg>
                    </div>
                    <Input 
                      id="password"
                      type="password" 
                      placeholder="请输入密码" 
                      className="pl-10 bg-blue-50/50 border-blue-100 focus:bg-white focus:ring-2 focus:ring-indigo-200 rounded-md"
                      disabled={isLoading}
                      {...register("password", { 
                        required: "密码是必填项",
                        minLength: { value: 6, message: "密码至少需要6个字符" } 
                      })}
                    />
                  </div>
                  {errors.password && (
                    <p className="text-sm text-red-500">{errors.password.message}</p>
                  )}
                </div>
              )}

              {loginType === 'username' && (
                <div className="space-y-2">
                  <Label htmlFor="verifyCode" className="text-gray-700 font-medium">验证码</Label>
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                          <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <Input 
                        id="verifyCode"
                        placeholder="请输入验证码" 
                        className="pl-10 bg-blue-50/50 border-blue-100 focus:bg-white focus:ring-2 focus:ring-indigo-200 rounded-md"
                        disabled={isLoading}
                        {...register("verifyCode", { 
                          required: "验证码是必填项"
                        })}
                      />
                    </div>
                    <div 
                      className="w-28 h-10 bg-blue-50 border border-blue-100 rounded-md flex items-center justify-center text-lg font-mono cursor-pointer hover:bg-blue-100 transition-colors" 
                      onClick={getVerifyCode}
                    >
                      <span className="select-none tracking-wider text-indigo-700">{verifyCodeImage}</span>
                    </div>
                  </div>
                  {errors.verifyCode && (
                    <p className="text-sm text-red-500">{errors.verifyCode.message}</p>
                  )}
                </div>
              )}

              {loginType !== 'qrcode' && (
                <div className="space-y-2">
                  <Label htmlFor="tenant" className="text-gray-700 font-medium">租户选择</Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10.496 2.132a1 1 0 00-.992 0l-7 4A1 1 0 003 8v7a1 1 0 100 2h14a1 1 0 100-2V8a1 1 0 00.496-1.868l-7-4zM6 9a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1zm3 1a1 1 0 012 0v3a1 1 0 11-2 0v-3zm5-1a1 1 0 00-1 1v3a1 1 0 102 0v-3a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <Select
                      disabled={isLoading}
                      onValueChange={(value) => setValue("tenant_id", parseInt(value))}
                      defaultValue={selectedTenantId?.toString()}
                    >
                      <SelectTrigger className="pl-10 bg-blue-50/50 border-blue-100 focus:bg-white focus:ring-2 focus:ring-indigo-200 rounded-md">
                        <SelectValue placeholder="请选择租户" />
                      </SelectTrigger>
                      <SelectContent>
                        {tenants.map((tenant) => (
                          <SelectItem key={tenant.id} value={tenant.id.toString()}>
                            {tenant.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {loginType !== 'qrcode' && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 group cursor-pointer">
                    <Checkbox 
                      id="rememberMe" 
                      {...register("rememberMe")}
                      disabled={isLoading}
                      className="text-indigo-600 border-gray-300 focus:ring-indigo-300"
                    />
                    <Label 
                      htmlFor="rememberMe" 
                      className="text-sm font-medium text-gray-700 cursor-pointer group-hover:text-indigo-600 transition-colors"
                    >
                      记住我
                    </Label>
                  </div>
                  {loginType === 'username' && (
                    <Link 
                      href="/forgot-password" 
                      className="text-sm font-medium text-indigo-600 hover:text-indigo-700 transition-colors"
                    >
                      忘记密码?
                    </Link>
                  )}
                </div>
              )}

              {loginType !== 'qrcode' ? (
                <Button 
                  type="submit" 
                  className="w-full bg-indigo-500 hover:bg-indigo-600 shadow-md hover:shadow-lg transition-all duration-200 text-white font-medium py-2.5" 
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      登录中...
                    </>
                  ) : "登录"}
                </Button>
              ) : (
                <Button 
                  type="button" 
                  variant="outline" 
                  className="w-full mt-4 border-blue-200 text-indigo-600 hover:bg-blue-50 hover:border-blue-300 transition-colors" 
                  onClick={getVerifyCode}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  刷新二维码
                </Button>
              )}
            </form>

            <div className="mt-6 text-center text-sm">
              <span className="text-gray-500">没有账号? </span>
              <Link href="/register" className="font-medium text-indigo-600 hover:text-indigo-700">
                联系管理员
              </Link>
            </div>
            
            <div className="mt-10 pt-6 border-t border-gray-200 text-center text-xs text-gray-400">
              © 2025 瑜伽生活会馆管理系统 版权所有
            </div>
