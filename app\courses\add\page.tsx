"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { ArrowLeft, Clock, Users, Calendar, MapPin, DollarSign, BarChart } from "lucide-react"
import Link from "next/link"

// 课程表单接口
interface CourseForm {
  title: string
  description: string
  price: string
  type_id: string
  coach_id: string
  venue: string
  time: string
  duration: string
  capacity: string
  level: string
  cover: string
}

// 课程类型接口
interface CourseType {
  id: string
  name: string
  color: string
}

// 教练接口
interface Coach {
  id: string
  name: string
}

// 场地接口
interface Venue {
  id: string
  name: string
}

export default function AddCoursePage() {
  const router = useRouter()
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [courseTypes, setCourseTypes] = useState<CourseType[]>([])
  const [coaches, setCoaches] = useState<Coach[]>([])
  const [venues, setVenues] = useState<Venue[]>([])
  const [formData, setFormData] = useState<CourseForm>({
    title: "",
    description: "",
    price: "",
    type_id: "",
    coach_id: "",
    venue: "",
    time: "",
    duration: "60",
    capacity: "10",
    level: "初级",
    cover: ""
  })

  // 加载课程类型
  useEffect(() => {
    const fetchCourseTypes = async () => {
      try {
        if (!user?.tenant_id) return
        
        const response = await fetch(`/api/course-types?tenantId=${user.tenant_id}`)
        const result = await response.json()
        
        if (result.code === 200) {
          setCourseTypes(result.data.list || [])
        }
      } catch (error) {
        console.error("获取课程类型失败:", error)
        toast({
          title: "错误",
          description: "获取课程类型失败",
          variant: "destructive"
        })
      }
    }
    
    fetchCourseTypes()
  }, [user])

  // 加载教练列表
  useEffect(() => {
    const fetchCoaches = async () => {
      try {
        if (!user?.tenant_id) return
        
        const response = await fetch(`/api/coaches?tenantId=${user.tenant_id}`)
        const result = await response.json()
        
        if (result.code === 200) {
          setCoaches(result.data.list || [])
        }
      } catch (error) {
        console.error("获取教练列表失败:", error)
        toast({
          title: "错误",
          description: "获取教练列表失败",
          variant: "destructive"
        })
      }
    }
    
    fetchCoaches()
  }, [user])

  // 加载场地列表
  useEffect(() => {
    const fetchVenues = async () => {
      try {
        if (!user?.tenant_id) return
        
        const response = await fetch(`/api/venues?tenantId=${user.tenant_id}`)
        const result = await response.json()
        
        if (result.code === 200) {
          setVenues(result.data.list || [])
        }
      } catch (error) {
        console.error("获取场地列表失败:", error)
        toast({
          title: "错误",
          description: "获取场地列表失败",
          variant: "destructive"
        })
      }
    }
    
    fetchVenues()
  }, [user])

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // 处理选择变化
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user?.tenant_id) {
      toast({
        title: "错误",
        description: "未找到租户信息，请重新登录",
        variant: "destructive"
      })
      return
    }
    
    // 表单验证
    if (!formData.title || !formData.type_id || !formData.venue) {
      toast({
        title: "表单不完整",
        description: "请填写所有必填字段",
        variant: "destructive"
      })
      return
    }
    
    setLoading(true)
    
    try {
      const response = await fetch("/api/courses", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          ...formData,
          tenant_id: user.tenant_id,
          price: parseFloat(formData.price) || 0,
          duration: parseInt(formData.duration) || 60,
          capacity: parseInt(formData.capacity) || 10,
          type_id: parseInt(formData.type_id)
        })
      })
      
      const result = await response.json()
      
      if (result.code === 200) {
        toast({
          title: "创建成功",
          description: "课程已成功创建"
        })
        router.push("/courses")
      } else {
        throw new Error(result.msg || "创建失败")
      }
    } catch (error) {
      console.error("创建课程失败:", error)
      toast({
        title: "创建失败",
        description: error instanceof Error ? error.message : "创建课程时出现错误",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Link href="/courses" className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background hover:bg-accent hover:text-accent-foreground h-9 px-3">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回课程列表
          </Link>
          <h1 className="text-2xl font-semibold tracking-tight">添加课程</h1>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
            <CardDescription>设置课程的基本信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title">课程名称 <span className="text-red-500">*</span></Label>
                <Input
                  id="title"
                  name="title"
                  placeholder="输入课程名称"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="type_id">课程类型 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.type_id}
                  onValueChange={(value) => handleSelectChange("type_id", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择课程类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {courseTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: type.color }}
                          />
                          {type.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="coach_id">教练</Label>
                <Select
                  value={formData.coach_id}
                  onValueChange={(value) => handleSelectChange("coach_id", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择教练" />
                  </SelectTrigger>
                  <SelectContent>
                    {coaches.map((coach) => (
                      <SelectItem key={coach.id} value={coach.id}>
                        {coach.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="venue">场地 <span className="text-red-500">*</span></Label>
                <Select
                  value={formData.venue}
                  onValueChange={(value) => handleSelectChange("venue", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择场地" />
                  </SelectTrigger>
                  <SelectContent>
                    {venues.map((venue) => (
                      <SelectItem key={venue.id} value={venue.name}>
                        {venue.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="time">上课时间</Label>
                <Input
                  id="time"
                  name="time"
                  placeholder="例如: 周一至周五 10:00-12:00"
                  value={formData.time}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="price">价格 (元)</Label>
                <Input
                  id="price"
                  name="price"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="0.00"
                  value={formData.price}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">课程描述</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="输入课程描述"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>详细设置</CardTitle>
            <CardDescription>设置课程的详细参数</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="duration">
                  <Clock className="h-4 w-4 inline mr-1" />
                  课程时长 (分钟)
                </Label>
                <Input
                  id="duration"
                  name="duration"
                  type="number"
                  min="5"
                  step="5"
                  placeholder="60"
                  value={formData.duration}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="capacity">
                  <Users className="h-4 w-4 inline mr-1" />
                  课程容量
                </Label>
                <Input
                  id="capacity"
                  name="capacity"
                  type="number"
                  min="1"
                  placeholder="10"
                  value={formData.capacity}
                  onChange={handleInputChange}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="level">
                  <BarChart className="h-4 w-4 inline mr-1" />
                  难度等级
                </Label>
                <Select
                  value={formData.level}
                  onValueChange={(value) => handleSelectChange("level", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择难度等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="初级">初级</SelectItem>
                    <SelectItem value="中级">中级</SelectItem>
                    <SelectItem value="高级">高级</SelectItem>
                    <SelectItem value="全部">全部级别</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="cover">封面图片URL</Label>
              <Input
                id="cover"
                name="cover"
                placeholder="输入图片URL"
                value={formData.cover}
                onChange={handleInputChange}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.back()}>
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "保存中..." : "保存课程"}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  )
} 