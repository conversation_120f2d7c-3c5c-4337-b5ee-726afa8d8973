"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Search, 
  ArrowLeft, 
  Plus, 
  Calendar,
  Clock,
  CheckCircle2,
  XCircle,
  Filter,
  Download,
  MoreHorizontal,
  FileText,
  Phone,
  MessageSquare
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"

// 模拟爽约记录数据
const noShowRecords = [
  { 
    id: "ns1", 
    memberId: "m1",
    memberName: "张三", 
    memberAvatar: "/avatars/01.png", 
    phone: "13800138001",
    cardType: "瑜伽年卡",
    courseDate: "2023-05-15",
    courseTime: "14:00-15:30",
    courseName: "空中瑜伽",
    coach: "刘教练",
    status: "未处理",
    reason: null,
    followUp: null,
    followUpBy: null,
    followUpAt: null,
    noShowCount: 3
  },
  { 
    id: "ns2", 
    memberId: "m2",
    memberName: "李四", 
    memberAvatar: "/avatars/02.png", 
    phone: "13800138002",
    cardType: "瑜伽季卡",
    courseDate: "2023-05-16",
    courseTime: "10:00-11:30",
    courseName: "哈他瑜伽",
    coach: "王教练",
    status: "已联系",
    reason: "临时有事，忘记取消预约",
    followUp: "已提醒会员下次及时请假",
    followUpBy: "前台小李",
    followUpAt: "2023-05-16 14:30:22",
    noShowCount: 1
  },
  { 
    id: "ns3", 
    memberId: "m3",
    memberName: "王五", 
    memberAvatar: "/avatars/03.png", 
    phone: "13800138003",
    cardType: "瑜伽月卡",
    courseDate: "2023-05-16",
    courseTime: "16:00-17:00",
    courseName: "普拉提",
    coach: "张教练",
    status: "未处理",
    reason: null,
    followUp: null,
    followUpBy: null,
    followUpAt: null,
    noShowCount: 2
  },
  { 
    id: "ns4", 
    memberId: "m4",
    memberName: "赵六", 
    memberAvatar: "/avatars/04.png", 
    phone: "13800138004",
    cardType: "瑜伽年卡",
    courseDate: "2023-05-17",
    courseTime: "09:00-10:30",
    courseName: "空中瑜伽",
    coach: "刘教练",
    status: "已联系",
    reason: "生病了，忘记取消",
    followUp: "已告知会员请假流程",
    followUpBy: "前台小王",
    followUpAt: "2023-05-17 11:15:40",
    noShowCount: 1
  },
  { 
    id: "ns5", 
    memberId: "m5",
    memberName: "孙七", 
    memberAvatar: "/avatars/05.png", 
    phone: "13800138005",
    cardType: "瑜伽季卡",
    courseDate: "2023-05-17",
    courseTime: "18:00-19:30",
    courseName: "哈他瑜伽",
    coach: "王教练",
    status: "未处理",
    reason: null,
    followUp: null,
    followUpBy: null,
    followUpAt: null,
    noShowCount: 4
  }
]

export default function NoShowsPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [showFollowUpDialog, setShowFollowUpDialog] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<any>(null)
  const [followUpData, setFollowUpData] = useState({
    reason: "",
    followUp: ""
  })

  // 过滤爽约记录
  const filteredRecords = noShowRecords.filter(record => {
    // 基本搜索过滤
    const searchFilter = 
      searchQuery === "" || 
      record.memberName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.phone.includes(searchQuery) ||
      record.courseName.toLowerCase().includes(searchQuery.toLowerCase());
    
    // 标签过滤
    const tabFilter = 
      activeTab === "all" || 
      (activeTab === "pending" && record.status === "未处理") ||
      (activeTab === "contacted" && record.status === "已联系") ||
      (activeTab === "frequent" && record.noShowCount >= 3);
    
    return searchFilter && tabFilter;
  });

  // 处理跟进
  const handleFollowUp = () => {
    // 在实际应用中，这里会调用API进行跟进记录
    toast.success("跟进记录已保存！");
    setShowFollowUpDialog(false);
    
    // 重置表单
    setFollowUpData({
      reason: "",
      followUp: ""
    });
  }

  // 打开跟进对话框
  const openFollowUpDialog = (record: any) => {
    setSelectedRecord(record);
    setFollowUpData({
      reason: record.reason || "",
      followUp: record.followUp || ""
    });
    setShowFollowUpDialog(true);
  }

  // 获取状态标签样式
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "未处理":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">未处理</Badge>
      case "已联系":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已联系</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // 获取爽约次数标签样式
  const getNoShowCountBadge = (count: number) => {
    if (count >= 3) {
      return <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200">{count}次</Badge>
    } else if (count >= 2) {
      return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50 border-amber-200">{count}次</Badge>
    } else {
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">{count}次</Badge>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/reception">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-semibold tracking-tight">爽约记录管理</h1>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>爽约记录管理</CardTitle>
              <CardDescription>
                管理会员课程爽约记录和跟进
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col md:flex-row md:items-center gap-4 justify-between">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full max-w-md">
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="pending">未处理</TabsTrigger>
                <TabsTrigger value="contacted">已联系</TabsTrigger>
                <TabsTrigger value="frequent">频繁爽约</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索会员姓名或课程..."
                  className="pl-8 w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>会员信息</TableHead>
                <TableHead>课程信息</TableHead>
                <TableHead>爽约次数</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>跟进记录</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    没有找到符合条件的爽约记录
                  </TableCell>
                </TableRow>
              ) : (
                filteredRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={record.memberAvatar} alt={record.memberName} />
                          <AvatarFallback>{record.memberName[0]}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{record.memberName}</div>
                          <div className="text-xs text-muted-foreground">{record.phone}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{record.courseName}</div>
                      <div className="text-xs text-muted-foreground">
                        {record.courseDate} {record.courseTime}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        教练: {record.coach}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getNoShowCountBadge(record.noShowCount)}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(record.status)}
                    </TableCell>
                    <TableCell>
                      <div className="max-w-[200px] truncate text-sm">
                        {record.followUp || "-"}
                      </div>
                      {record.followUpBy && (
                        <div className="text-xs text-muted-foreground">
                          {record.followUpBy} 于 {record.followUpAt?.split(' ')[0]}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                          <Phone className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => openFollowUpDialog(record)}
                        >
                          记录跟进
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-4">
          <div className="text-sm text-muted-foreground">
            共 {filteredRecords.length} 条记录
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled>
              上一页
            </Button>
            <Button variant="outline" size="sm" disabled>
              下一页
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* 跟进记录对话框 */}
      {selectedRecord && (
        <Dialog open={showFollowUpDialog} onOpenChange={setShowFollowUpDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>记录跟进情况</DialogTitle>
              <DialogDescription>
                记录会员爽约原因和跟进情况
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="flex items-center gap-4 pb-4 border-b">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={selectedRecord.memberAvatar} alt={selectedRecord.memberName} />
                  <AvatarFallback>{selectedRecord.memberName[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium text-lg">{selectedRecord.memberName}</div>
                  <div className="text-sm text-muted-foreground">{selectedRecord.phone}</div>
                </div>
                <div className="ml-auto">
                  {getNoShowCountBadge(selectedRecord.noShowCount)}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">课程信息</div>
                <div className="p-3 rounded-md bg-muted/50">
                  <div className="font-medium">{selectedRecord.courseName}</div>
                  <div className="text-sm text-muted-foreground">
                    {selectedRecord.courseDate} {selectedRecord.courseTime}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    教练: {selectedRecord.coach}
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="no-show-reason">爽约原因</Label>
                <textarea
                  id="no-show-reason"
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={followUpData.reason}
                  onChange={(e) => setFollowUpData({...followUpData, reason: e.target.value})}
                  placeholder="记录会员爽约原因..."
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="follow-up-notes">跟进记录</Label>
                <textarea
                  id="follow-up-notes"
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={followUpData.followUp}
                  onChange={(e) => setFollowUpData({...followUpData, followUp: e.target.value})}
                  placeholder="记录跟进情况..."
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowFollowUpDialog(false)}>取消</Button>
              <Button onClick={handleFollowUp}>保存记录</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
