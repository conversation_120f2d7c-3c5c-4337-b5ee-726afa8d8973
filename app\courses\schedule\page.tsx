"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Plus, ChevronLeft, ChevronRight, MoreHorizontal, Copy, Users, FileImage, PauseCircle, PlayCircle } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Check, ChevronsUpDown, Search, Plus as PlusIcon, X } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// 模拟数据 - 课程类型
const courseTypes = [
  { id: "basic", name: "基础瑜伽", color: "#4285F4" },
  { id: "advanced", name: "高级瑜伽", color: "#34A853" },
  { id: "yin", name: "阴瑜伽", color: "#FBBC05" },
  { id: "prenatal", name: "孕产瑜伽", color: "#EA4335" },
  { id: "aerial", name: "空中瑜伽", color: "#FF6D91" },
]

// 模拟数据 - 教练
const coaches = [
  {
    id: "1",
    name: "张教练",
    avatar: "/placeholder.svg?height=32&width=32",
    defaultDuration: 90,
    defaultPrice: 80,
    defaultVenue: "1",
    defaultCapacity: 15
  },
  {
    id: "2",
    name: "李教练",
    avatar: "/placeholder.svg?height=32&width=32",
    defaultDuration: 75,
    defaultPrice: 85,
    defaultVenue: "2",
    defaultCapacity: 12
  },
  {
    id: "3",
    name: "王教练",
    avatar: "/placeholder.svg?height=32&width=32",
    defaultDuration: 60,
    defaultPrice: 75,
    defaultVenue: "3",
    defaultCapacity: 15
  },
  {
    id: "4",
    name: "赵教练",
    avatar: "/placeholder.svg?height=32&width=32",
    defaultDuration: 90,
    defaultPrice: 120,
    defaultVenue: "4",
    defaultCapacity: 8
  },
  {
    id: "5",
    name: "刘教练",
    avatar: "/placeholder.svg?height=32&width=32",
    defaultDuration: 75,
    defaultPrice: 110,
    defaultVenue: "1",
    defaultCapacity: 15
  },
]

// 模拟数据 - 场地
const venues = [
  { id: "1", name: "1号瑜伽室", capacity: 15 },
  { id: "2", name: "2号瑜伽室", capacity: 10 },
  { id: "3", name: "3号瑜伽室", capacity: 15 },
  { id: "4", name: "4号瑜伽室", capacity: 8 },
  { id: "5", name: "私教室", capacity: 2 },
]

// 模拟数据 - 课程列表（按课程类型分组）
const coursesByType = {
  basic: [
    {
      id: "c1",
      name: "基础瑜伽入门",
      description: "适合初学者的基础瑜伽课程",
      defaultCoach: "1",
      defaultDuration: 90,
      defaultPrice: 80,
      defaultVenue: "1",
      defaultCapacity: 15
    },
    {
      id: "c2",
      name: "基础体式练习",
      description: "学习基本瑜伽体式",
      defaultCoach: "1",
      defaultDuration: 75,
      defaultPrice: 75,
      defaultVenue: "1",
      defaultCapacity: 15
    },
    {
      id: "c3",
      name: "基础呼吸法",
      description: "掌握基础呼吸技巧",
      defaultCoach: "2",
      defaultDuration: 60,
      defaultPrice: 60,
      defaultVenue: "2",
      defaultCapacity: 12
    },
    {
      id: "c4",
      name: "基础冥想",
      description: "入门级冥想练习",
      defaultCoach: "2",
      defaultDuration: 45,
      defaultPrice: 50,
      defaultVenue: "2",
      defaultCapacity: 10
    },
    {
      id: "c5",
      name: "基础拉伸",
      description: "基础身体拉伸训练",
      defaultCoach: "3",
      defaultDuration: 60,
      defaultPrice: 65,
      defaultVenue: "3",
      defaultCapacity: 15
    },
  ],
  advanced: [
    {
      id: "c6",
      name: "高级瑜伽进阶",
      description: "适合有基础的学员",
      defaultCoach: "4",
      defaultDuration: 90,
      defaultPrice: 120,
      defaultVenue: "4",
      defaultCapacity: 8
    },
    {
      id: "c7",
      name: "高级体式挑战",
      description: "挑战性瑜伽体式",
      defaultCoach: "4",
      defaultDuration: 75,
      defaultPrice: 110,
      defaultVenue: "4",
      defaultCapacity: 8
    },
    {
      id: "c8",
      name: "高级流瑜伽",
      description: "连贯性瑜伽练习",
      defaultCoach: "5",
      defaultDuration: 90,
      defaultPrice: 130,
      defaultVenue: "1",
      defaultCapacity: 15
    },
    {
      id: "c9",
      name: "高级力量瑜伽",
      description: "增强力量的瑜伽练习",
      defaultCoach: "5",
      defaultDuration: 75,
      defaultPrice: 115,
      defaultVenue: "1",
      defaultCapacity: 12
    },
  ],
  yin: [
    {
      id: "c10",
      name: "阴瑜伽放松",
      description: "深度放松的阴瑜伽",
      defaultCoach: "2",
      defaultDuration: 75,
      defaultPrice: 85,
      defaultVenue: "3",
      defaultCapacity: 15
    },
    {
      id: "c11",
      name: "阴瑜伽修复",
      description: "身心修复瑜伽",
      defaultCoach: "2",
      defaultDuration: 90,
      defaultPrice: 95,
      defaultVenue: "3",
      defaultCapacity: 12
    },
    {
      id: "c12",
      name: "阴瑜伽冥想",
      description: "结合冥想的阴瑜伽",
      defaultCoach: "3",
      defaultDuration: 60,
      defaultPrice: 75,
      defaultVenue: "3",
      defaultCapacity: 10
    },
    {
      id: "c13",
      name: "阴瑜伽理疗",
      description: "理疗性阴瑜伽练习",
      defaultCoach: "3",
      defaultDuration: 75,
      defaultPrice: 90,
      defaultVenue: "3",
      defaultCapacity: 12
    },
  ],
  prenatal: [
    {
      id: "c14",
      name: "孕产瑜伽特训",
      description: "专为孕妇设计的瑜伽",
      defaultCoach: "2",
      defaultDuration: 60,
      defaultPrice: 100,
      defaultVenue: "2",
      defaultCapacity: 8
    },
    {
      id: "c15",
      name: "产前瑜伽",
      description: "产前准备瑜伽练习",
      defaultCoach: "2",
      defaultDuration: 45,
      defaultPrice: 80,
      defaultVenue: "2",
      defaultCapacity: 6
    },
    {
      id: "c16",
      name: "产后修复瑜伽",
      description: "产后身体恢复瑜伽",
      defaultCoach: "3",
      defaultDuration: 60,
      defaultPrice: 90,
      defaultVenue: "2",
      defaultCapacity: 8
    },
  ],
  aerial: [
    {
      id: "c17",
      name: "空中瑜伽体验",
      description: "空中瑜伽入门体验",
      defaultCoach: "4",
      defaultDuration: 60,
      defaultPrice: 150,
      defaultVenue: "4",
      defaultCapacity: 6
    },
    {
      id: "c18",
      name: "空中瑜伽进阶",
      description: "进阶空中瑜伽练习",
      defaultCoach: "4",
      defaultDuration: 75,
      defaultPrice: 180,
      defaultVenue: "4",
      defaultCapacity: 6
    },
    {
      id: "c19",
      name: "空中瑜伽流",
      description: "流畅的空中瑜伽序列",
      defaultCoach: "5",
      defaultDuration: 90,
      defaultPrice: 200,
      defaultVenue: "4",
      defaultCapacity: 8
    },
  ],
}

// 在模拟数据部分添加会员数据
const members = [
  { id: "m1", name: "张三", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "m2", name: "李四", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "m3", name: "王五", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "m4", name: "赵六", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "m5", name: "钱七", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "m6", name: "孙八", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "m7", name: "周九", avatar: "/placeholder.svg?height=32&width=32" },
  { id: "m8", name: "吴十", avatar: "/placeholder.svg?height=32&width=32" },
]

// 模拟数据 - 课程安排
const initialSchedules = [
  {
    id: "s1",
    courseType: "basic",
    courseName: "基础瑜伽入门",
    coach: "1",
    venue: "1",
    date: "2025-03-28",
    startTime: "10:00",
    endTime: "11:30",
    bookings: 8,
    capacity: 15,
    status: "upcoming",
    bookedMembers: ["m1", "m2", "m3", "m4", "m5", "m6", "m7", "m8"],
  },
  {
    id: "s2",
    courseType: "advanced",
    courseName: "高级瑜伽进阶",
    coach: "2",
    venue: "2",
    date: "2025-03-28",
    startTime: "14:00",
    endTime: "15:30",
    bookings: 7,
    capacity: 10,
    status: "upcoming",
    bookedMembers: ["m2", "m3", "m5", "m6", "m7", "m1", "m4"],
  },
  {
    id: "s3",
    courseType: "yin",
    courseName: "阴瑜伽放松",
    coach: "3",
    venue: "3",
    date: "2025-03-28",
    startTime: "16:00",
    endTime: "17:00",
    bookings: 12,
    capacity: 15,
    status: "upcoming",
    bookedMembers: ["m1", "m3", "m5", "m7", "m2", "m4", "m6", "m8", "m1", "m2", "m3", "m4"],
  },
  {
    id: "s4",
    courseType: "prenatal",
    courseName: "孕产瑜伽特训",
    coach: "4",
    venue: "1",
    date: "2025-03-28",
    startTime: "18:30",
    endTime: "20:00",
    bookings: 5,
    capacity: 8,
    status: "upcoming",
    bookedMembers: ["m2", "m4", "m6", "m8", "m1"],
  },
  {
    id: "s5",
    courseType: "aerial",
    courseName: "空中瑜伽体验",
    coach: "5",
    venue: "4",
    date: "2025-03-29",
    startTime: "09:00",
    endTime: "10:30",
    bookings: 6,
    capacity: 8,
    status: "upcoming",
    bookedMembers: ["m3", "m5", "m7", "m1", "m2", "m4"],
  },
  {
    id: "s6",
    courseType: "basic",
    courseName: "基础瑜伽入门",
    coach: "1",
    venue: "1",
    date: "2025-03-29",
    startTime: "14:00",
    endTime: "15:30",
    bookings: 10,
    capacity: 15,
    status: "upcoming",
    bookedMembers: ["m1", "m2", "m3", "m4", "m5", "m6", "m7", "m8", "m1", "m2"],
  },
  {
    id: "s7",
    courseType: "advanced",
    courseName: "高级瑜伽进阶",
    coach: "2",
    venue: "2",
    date: "2025-03-30",
    startTime: "10:00",
    endTime: "11:30",
    bookings: 8,
    capacity: 10,
    status: "upcoming",
    bookedMembers: ["m2", "m4", "m6", "m8", "m1", "m3", "m5", "m7"],
  },
  {
    id: "s8",
    courseType: "yin",
    courseName: "阴瑜伽放松",
    coach: "3",
    venue: "3",
    date: "2025-03-30",
    startTime: "16:00",
    endTime: "17:00",
    bookings: 9,
    capacity: 15,
    status: "upcoming",
    bookedMembers: ["m1", "m3", "m5", "m7", "m2", "m4", "m6", "m8", "m1"],
  },
  {
    id: "s9",
    courseType: "basic",
    courseName: "基础瑜伽入门",
    coach: "1",
    venue: "1",
    date: "2025-03-25",
    startTime: "10:00",
    endTime: "11:30",
    bookings: 12,
    capacity: 15,
    status: "completed",
    bookedMembers: ["m1", "m2", "m3", "m4", "m5", "m6", "m7", "m8", "m1", "m2", "m3", "m4"],
  },
  {
    id: "s10",
    courseType: "advanced",
    courseName: "高级瑜伽进阶",
    coach: "2",
    venue: "2",
    date: "2025-03-26",
    startTime: "14:00",
    endTime: "15:30",
    bookings: 9,
    capacity: 10,
    status: "completed",
    bookedMembers: ["m2", "m4", "m6", "m8", "m1", "m3", "m5", "m7", "m2"],
  },
  {
    id: "s11",
    courseType: "yin",
    courseName: "阴瑜伽放松",
    coach: "3",
    venue: "3",
    date: "2025-03-27",
    startTime: "16:00",
    endTime: "17:00",
    bookings: 11,
    capacity: 15,
    status: "completed",
    bookedMembers: ["m1", "m3", "m5", "m7", "m2", "m4", "m6", "m8", "m1", "m2", "m3"],
  },
]

// 获取课程类型信息
const getCourseTypeInfo = (typeId) => {
  return courseTypes.find((type) => type.id === typeId) || { name: "未知", color: "#999999" }
}

// 获取教练信息
const getCoachInfo = (coachId) => {
  return (
    coaches.find((coach) => coach.id === coachId) || { name: "未知", avatar: "/placeholder.svg?height=32&width=32" }
  )
}

// 获取场地信息
const getVenueInfo = (venueId) => {
  return venues.find((venue) => venue.id === venueId) || { name: "未知", capacity: 0 }
}

// 生成时间选项（5分钟间隔）
const generateTimeOptions = () => {
  const options = []
  for (let hour = 6; hour <= 22; hour++) {
    for (let minute = 0; minute < 60; minute += 5) {
      const formattedHour = hour.toString().padStart(2, "0")
      const formattedMinute = minute.toString().padStart(2, "0")
      options.push(`${formattedHour}:${formattedMinute}`)
    }
  }
  return options
}

// 获取当前周的日期范围
const getWeekDates = (date) => {
  const currentDate = new Date(date)
  const day = currentDate.getDay() // 0 是周日，1-6 是周一到周六
  const diff = currentDate.getDate() - day + (day === 0 ? -6 : 1) // 调整到周一

  const monday = new Date(currentDate)
  monday.setDate(diff)

  const weekDates = []
  for (let i = 0; i < 7; i++) {
    const date = new Date(monday)
    date.setDate(monday.getDate() + i)
    weekDates.push(date)
  }

  return weekDates
}

// 格式化日期为 YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, "0")
  const day = date.getDate().toString().padStart(2, "0")
  return `${year}-${month}-${day}`
}

// 在 CourseSchedulePage 组件中添加新的状态和引用
export default function CourseSchedulePage() {
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [selectedVenue, setSelectedVenue] = useState("all")
  const [selectedCoach, setSelectedCoach] = useState("all")
  const [selectedCourseType, setSelectedCourseType] = useState("all")
  const [schedules, setSchedules] = useState(initialSchedules)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isCopyDialogOpen, setIsCopyDialogOpen] = useState(false) // 新增
  const [isSuspendDialogOpen, setIsSuspendDialogOpen] = useState(false) // 停课对话框
  const [isResumeDialogOpen, setIsResumeDialogOpen] = useState(false) // 恢复上课对话框
  const [currentSchedule, setCurrentSchedule] = useState(null)
  const [suspendSchedule, setSuspendSchedule] = useState(null) // 要停课的课程
  const [resumeSchedule, setResumeSchedule] = useState(null) // 要恢复的课程
  const [suspendReason, setSuspendReason] = useState("") // 停课原因
  const [suspendNote, setSuspendNote] = useState("") // 停课备注
  const [resumeNote, setResumeNote] = useState("") // 恢复备注
  const [newSchedule, setNewSchedule] = useState({
    courseType: "basic",
    courseName: "",
    coach: "1",
    venue: "1",
    date: formatDate(new Date()),
    startTime: "10:00",
    endTime: "11:30",
    capacity: 15,
    duration: 90, // 课程时长（分钟）
    price: 80, // 课时费
  })

  // 周期性课程设置状态
  const [isRecurring, setIsRecurring] = useState(false)
  const [endDate, setEndDate] = useState("")
  const [selectedDays, setSelectedDays] = useState<string[]>([])

  // 课程选择相关状态
  const [courseSearchOpen, setCourseSearchOpen] = useState(false)
  const [courseSearchValue, setCourseSearchValue] = useState("")
  const [selectedTimeSlots, setSelectedTimeSlots] = useState<Array<{start: string, end: string}>>([
    { start: "10:00", end: "11:30" }
  ])
  const [copySource, setCopySource] = useState({
    type: "day", // 'day' 或 'week'
    date: formatDate(new Date()),
  })
  const [copyTarget, setCopyTarget] = useState({
    date: formatDate(new Date(new Date().setDate(new Date().getDate() + 7))), // 默认为一周后
  })

  // 添加引用，用于导出图片
  const dayViewRef = useRef(null)
  const weekViewRef = useRef(null)
  const monthViewRef = useRef(null)

  // 获取会员信息
  const getMemberInfo = (memberId) => {
    return (
      members.find((member) => member.id === memberId) || {
        name: "未知",
        avatar: "/placeholder.svg?height=32&width=32",
      }
    )
  }

  // 添加复制课表功能
  const handleCopySchedule = () => {
    // 获取源日期的课程
    let sourceSchedules = []

    if (copySource.type === "day") {
      // 复制单日课程
      sourceSchedules = schedules.filter((schedule) => schedule.date === copySource.date)
    } else {
      // 复制整周课程
      const sourceWeekDates = getWeekDates(new Date(copySource.date)).map((date) => formatDate(date))
      sourceSchedules = schedules.filter((schedule) => sourceWeekDates.includes(schedule.date))
    }

    if (sourceSchedules.length === 0) {
      alert("所选日期没有课程可复制")
      return
    }

    // 计算日期差
    const sourceDateObj = new Date(copySource.date)
    const targetDateObj = new Date(copyTarget.date)
    const daysDiff = Math.round((targetDateObj - sourceDateObj) / (1000 * 60 * 60 * 24))

    // 创建新的课程安排
    const newSchedules = sourceSchedules.map((schedule) => {
      const scheduleDate = new Date(schedule.date)
      scheduleDate.setDate(scheduleDate.getDate() + daysDiff)

      return {
        ...schedule,
        id: `s${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        date: formatDate(scheduleDate),
        bookings: 0,
        bookedMembers: [],
        status: "upcoming",
      }
    })

    // 添加到现有课程
    setSchedules([...schedules, ...newSchedules])
    setIsCopyDialogOpen(false)
  }

  // 添加导出图片功能
  const handleExportImage = async (viewType) => {
    try {
      // 动态导入 html2canvas
      const html2canvasModule = await import("html2canvas")
      const html2canvas = html2canvasModule.default

      let element

      switch (viewType) {
        case "day":
          element = dayViewRef.current
          break
        case "week":
          element = weekViewRef.current
          break
        case "month":
          element = monthViewRef.current
          break
        default:
          return
      }

      if (!element) return

      // 创建画布
      const canvas = await html2canvas(element, {
        backgroundColor: "#ffffff",
        scale: 2, // 提高清晰度
      })

      // 转换为图片并下载
      const image = canvas.toDataURL("image/png")
      const link = document.createElement("a")
      link.href = image
      link.download = `课程表_${viewType}_${formatDate(selectedDate)}.png`
      link.click()
    } catch (error) {
      console.error("导出图片失败:", error)
      alert("导出图片失败，请重试")
    }
  }

  // 获取当前周的日期
  const weekDates = getWeekDates(selectedDate)
  const formattedWeekDates = weekDates.map((date) => formatDate(date))

  // 过滤课程安排
  const filteredSchedules = schedules.filter((schedule) => {
    let match = true

    if (selectedVenue !== "all") {
      match = match && schedule.venue === selectedVenue
    }

    if (selectedCoach !== "all") {
      match = match && schedule.coach === selectedCoach
    }

    if (selectedCourseType !== "all") {
      match = match && schedule.courseType === selectedCourseType
    }

    return match
  })

  // 按日期分组的课程安排 (用于周视图)
  const schedulesByDate = {}
  formattedWeekDates.forEach((date) => {
    schedulesByDate[date] = filteredSchedules.filter((schedule) => schedule.date === date)
  })

  // 处理上一周/下一周
  const handlePreviousWeek = () => {
    const newDate = new Date(selectedDate)
    newDate.setDate(newDate.getDate() - 7)
    setSelectedDate(newDate)
  }

  const handleNextWeek = () => {
    const newDate = new Date(selectedDate)
    newDate.setDate(newDate.getDate() + 7)
    setSelectedDate(newDate)
  }

  // 处理添加课程排期
  const handleAddSchedule = () => {
    if (!newSchedule.courseName) {
      alert("请选择课程名称")
      return
    }

    // 验证所有时间段
    for (let i = 0; i < selectedTimeSlots.length; i++) {
      const slot = selectedTimeSlots[i]
      if (!isValidTimeSlot(slot.start, slot.end)) {
        alert(`第${i + 1}个时段的结束时间必须大于开始时间`)
        return
      }
    }

    const newSchedules = []

    // 为每个时间段创建排期
    selectedTimeSlots.forEach((slot, index) => {
      const id = `s${schedules.length + newSchedules.length + 1}`
      const scheduleWithId = {
        ...newSchedule,
        id,
        startTime: slot.start,
        endTime: slot.end,
        bookings: 0,
        status: "upcoming",
        bookedMembers: [],
      }
      newSchedules.push(scheduleWithId)
    })

    // 如果是周期性课程，为每个选中的日期创建排期
    if (isRecurring && selectedDays.length > 0 && endDate) {
      const startDateObj = new Date(newSchedule.date)
      const endDateObj = new Date(endDate)
      const allSchedules = []

      // 遍历日期范围
      for (let d = new Date(startDateObj); d <= endDateObj; d.setDate(d.getDate() + 1)) {
        const dayName = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"][d.getDay()]

        if (selectedDays.includes(dayName)) {
          selectedTimeSlots.forEach((slot) => {
            const id = `s${schedules.length + allSchedules.length + 1}`
            const scheduleWithId = {
              ...newSchedule,
              id,
              date: formatDate(new Date(d)),
              startTime: slot.start,
              endTime: slot.end,
              bookings: 0,
              status: "upcoming",
              bookedMembers: [],
            }
            allSchedules.push(scheduleWithId)
          })
        }
      }

      setSchedules([...schedules, ...allSchedules])
    } else {
      setSchedules([...schedules, ...newSchedules])
    }

    setIsAddDialogOpen(false)
    setNewSchedule({
      courseType: "basic",
      courseName: "",
      coach: "1",
      venue: "1",
      date: formatDate(new Date()),
      startTime: "10:00",
      endTime: "11:30",
      capacity: 15,
      duration: 90,
      price: 80,
    })
    setSelectedTimeSlots([{ start: "10:00", end: "11:30" }])
    setIsRecurring(false)
    setSelectedDays([])
    setEndDate("")
    setCourseSearchValue("")
  }

  // 处理编辑课程排期
  const handleEditSchedule = () => {
    const updatedSchedules = schedules.map((schedule) =>
      schedule.id === currentSchedule.id ? currentSchedule : schedule,
    )

    setSchedules(updatedSchedules)
    setIsEditDialogOpen(false)
    setCurrentSchedule(null)
  }

  // 处理删除课程排期
  const handleDeleteSchedule = (id) => {
    const updatedSchedules = schedules.filter((schedule) => schedule.id !== id)
    setSchedules(updatedSchedules)
  }

  // 打开停课对话框
  const openSuspendDialog = (schedule) => {
    setSuspendSchedule(schedule)
    setSuspendReason("")
    setSuspendNote("")
    setIsSuspendDialogOpen(true)
  }

  // 处理停课
  const handleSuspendSchedule = () => {
    if (!suspendReason) {
      alert("请选择停课原因")
      return
    }

    const updatedSchedules = schedules.map((schedule) =>
      schedule.id === suspendSchedule.id
        ? {
            ...schedule,
            status: "suspended",
            suspendReason: suspendReason,
            suspendNote: suspendNote,
            suspendDate: new Date().toISOString().split('T')[0]
          }
        : schedule
    )

    setSchedules(updatedSchedules)
    setIsSuspendDialogOpen(false)
    setSuspendSchedule(null)
    setSuspendReason("")
    setSuspendNote("")
  }

  // 打开恢复上课对话框
  const openResumeDialog = (schedule) => {
    setResumeSchedule(schedule)
    setResumeNote("")
    setIsResumeDialogOpen(true)
  }

  // 处理恢复上课
  const handleResumeSchedule = () => {
    const updatedSchedules = schedules.map((schedule) =>
      schedule.id === resumeSchedule.id
        ? {
            ...schedule,
            status: "upcoming", // 恢复为即将开始状态
            resumeNote: resumeNote,
            resumeDate: new Date().toISOString().split('T')[0],
            // 保留停课历史记录
            suspendHistory: {
              suspendReason: schedule.suspendReason,
              suspendNote: schedule.suspendNote,
              suspendDate: schedule.suspendDate
            }
          }
        : schedule
    )

    setSchedules(updatedSchedules)
    setIsResumeDialogOpen(false)
    setResumeSchedule(null)
    setResumeNote("")
  }

  // 处理星期选择
  const handleDayToggle = (day: string) => {
    setSelectedDays(prev =>
      prev.includes(day)
        ? prev.filter(d => d !== day)
        : [...prev, day]
    )
  }

  // 获取当前课程类型下的课程列表
  const getCoursesForType = (courseType: string) => {
    return coursesByType[courseType] || []
  }

  // 根据课程名称获取课程信息
  const getCourseInfo = (courseName: string, courseType: string) => {
    const courses = getCoursesForType(courseType)
    return courses.find(course => course.name === courseName)
  }

  // 获取场地信息
  const getVenueInfo = (venueId: string) => {
    return venues.find(venue => venue.id === venueId) || { name: "未知场地", capacity: 0 }
  }

  // 过滤课程（搜索功能）
  const getFilteredCourses = (courseType: string) => {
    const courses = getCoursesForType(courseType)
    if (!courseSearchValue) return courses

    return courses.filter(course =>
      course.name.toLowerCase().includes(courseSearchValue.toLowerCase()) ||
      course.description.toLowerCase().includes(courseSearchValue.toLowerCase())
    )
  }

  // 处理课程类型变化
  const handleCourseTypeChange = (courseType: string) => {
    setNewSchedule({
      ...newSchedule,
      courseType,
      courseName: "", // 重置课程名称
      coach: "1", // 重置为默认教练
      duration: 90, // 重置为默认时长
      price: 80 // 重置为默认价格
    })
    setCourseSearchValue("")
  }

  // 处理课程选择
  const handleCourseSelect = (course: any) => {
    const endTime = calculateEndTime(newSchedule.startTime, course.defaultDuration)

    setNewSchedule({
      ...newSchedule,
      courseName: course.name,
      coach: course.defaultCoach,
      duration: course.defaultDuration,
      price: course.defaultPrice,
      venue: course.defaultVenue,
      capacity: course.defaultCapacity,
      endTime: endTime
    })

    // 更新所有时段的结束时间
    const updatedSlots = selectedTimeSlots.map(slot => ({
      ...slot,
      end: calculateEndTime(slot.start, course.defaultDuration)
    }))
    setSelectedTimeSlots(updatedSlots)

    setCourseSearchOpen(false)
    setCourseSearchValue("")
  }

  // 处理教练选择（只关联默认课时费）
  const handleCoachSelect = (coachId: string) => {
    const coach = getCoachInfo(coachId)

    setNewSchedule({
      ...newSchedule,
      coach: coachId,
      price: coach.defaultPrice  // 只关联默认课时费
    })
  }

  // 处理课程时长变化
  const handleDurationChange = (duration: number) => {
    const endTime = calculateEndTime(newSchedule.startTime, duration)

    setNewSchedule({
      ...newSchedule,
      duration: duration,
      endTime: endTime
    })

    // 更新所有时段的结束时间
    const updatedSlots = selectedTimeSlots.map(slot => ({
      ...slot,
      end: calculateEndTime(slot.start, duration)
    }))
    setSelectedTimeSlots(updatedSlots)
  }

  // 时间计算函数
  const calculateEndTime = (startTime: string, duration: number) => {
    if (!startTime || !duration) return ""

    const [hours, minutes] = startTime.split(':').map(Number)
    const startDate = new Date(2000, 0, 1, hours, minutes)
    const endDate = new Date(startDate.getTime() + duration * 60000) // duration in minutes

    const endHours = endDate.getHours().toString().padStart(2, '0')
    const endMinutes = endDate.getMinutes().toString().padStart(2, '0')

    return `${endHours}:${endMinutes}`
  }

  // 时间验证函数
  const isValidTimeSlot = (start: string, end: string) => {
    if (!start || !end) return false

    const startTime = new Date(`2000-01-01 ${start}:00`)
    const endTime = new Date(`2000-01-01 ${end}:00`)

    return endTime > startTime
  }

  // 获取有效的结束时间选项（必须大于开始时间）
  const getValidEndTimeOptions = (startTime: string) => {
    if (!startTime) return generateTimeOptions()

    const allOptions = generateTimeOptions()
    const startIndex = allOptions.indexOf(startTime)

    if (startIndex === -1) return allOptions

    // 返回开始时间之后的所有选项
    return allOptions.slice(startIndex + 1)
  }

  // 处理时间段添加
  const addTimeSlot = () => {
    if (selectedTimeSlots.length >= 5) {
      alert("最多只能添加5个时段")
      return
    }
    setSelectedTimeSlots([...selectedTimeSlots, { start: "10:00", end: "11:30" }])
  }

  // 处理时间段删除
  const removeTimeSlot = (index: number) => {
    if (selectedTimeSlots.length > 1) {
      setSelectedTimeSlots(selectedTimeSlots.filter((_, i) => i !== index))
    }
  }

  // 处理时间段更新
  const updateTimeSlot = (index: number, field: 'start' | 'end', value: string) => {
    const updatedSlots = [...selectedTimeSlots]

    // 如果更新的是开始时间，自动计算结束时间
    if (field === 'start') {
      updatedSlots[index].start = value
      updatedSlots[index].end = calculateEndTime(value, newSchedule.duration)
    } else {
      // 结束时间不允许手动修改，这里不做处理
      return
    }

    setSelectedTimeSlots(updatedSlots)
  }

  // 打开编辑对话框
  const openEditDialog = (schedule) => {
    setCurrentSchedule({ ...schedule })
    setIsEditDialogOpen(true)
  }

  // 获取周一到周日的标题
  const weekDayTitles = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

  // 获取日期标题 (例如: 3/28)
  const getDateTitle = (date) => {
    const d = new Date(date)
    return `${d.getMonth() + 1}/${d.getDate()}`
  }

  // 获取时间段标题
  const getTimeSlotTitle = (schedule) => {
    return `${schedule.startTime}-${schedule.endTime}`
  }

  // 获取课程卡片样式
  const getCourseCardStyle = (courseType, status = "upcoming") => {
    const typeInfo = getCourseTypeInfo(courseType)

    if (status === "suspended") {
      return {
        backgroundColor: "#f3f4f6", // 灰色背景
        borderLeftColor: "#9ca3af",
        borderLeftWidth: "4px",
        borderLeftStyle: "solid",
        opacity: "0.7",
      }
    }

    return {
      backgroundColor: `${typeInfo.color}15`, // 15% 透明度
      borderLeftColor: typeInfo.color,
      borderLeftWidth: "4px",
      borderLeftStyle: "solid",
    }
  }

  // 修改 renderDayView 函数，添加 ref
  const renderDayView = () => {
    const date = formatDate(selectedDate)
    const daySchedules = filteredSchedules.filter((schedule) => schedule.date === date)

    // 按时间排序
    daySchedules.sort((a, b) => {
      if (a.startTime < b.startTime) return -1
      if (a.startTime > b.startTime) return 1
      return 0
    })

    // 生成时间轴
    const timeSlots = []
    for (let hour = 6; hour <= 22; hour++) {
      timeSlots.push(`${hour.toString().padStart(2, "0")}:00`)
    }

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>
                {selectedDate.toLocaleDateString("zh-CN", { year: "numeric", month: "long", day: "numeric" })}{" "}
                {selectedDate.toLocaleDateString("zh-CN", { weekday: "long" })}
              </CardTitle>
              <CardDescription>今日课程安排</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const newDate = new Date(selectedDate)
                  newDate.setDate(newDate.getDate() - 1)
                  setSelectedDate(newDate)
                }}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const newDate = new Date(selectedDate)
                  newDate.setDate(newDate.getDate() + 1)
                  setSelectedDate(newDate)
                }}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4" ref={dayViewRef}>
            <div className="grid grid-cols-[80px_1fr] gap-4">
              <div className="space-y-16">
                {timeSlots.map((time, index) => (
                  <div key={index} className="text-sm font-medium text-muted-foreground">
                    {time}
                  </div>
                ))}
              </div>
              <div className="relative min-h-[800px]">
                {/* 时间轴背景线 */}
                {timeSlots.map((time, index) => (
                  <div
                    key={index}
                    className="absolute w-full border-t border-dashed border-gray-200"
                    style={{ top: `${index * 64}px` }}
                  />
                ))}

                {/* 课程卡片 */}
                {daySchedules.map((schedule) => {
                  const startHour = Number.parseInt(schedule.startTime.split(":")[0])
                  const startMinute = Number.parseInt(schedule.startTime.split(":")[1])
                  const endHour = Number.parseInt(schedule.endTime.split(":")[0])
                  const endMinute = Number.parseInt(schedule.endTime.split(":")[1])

                  // 计算位置和高度
                  const startPosition = (startHour - 6) * 64 + (startMinute / 60) * 64
                  const duration = endHour - startHour + (endMinute - startMinute) / 60
                  const height = duration * 64

                  const courseType = getCourseTypeInfo(schedule.courseType)
                  const coach = getCoachInfo(schedule.coach)
                  const venue = getVenueInfo(schedule.venue)

                  return (
                    <div
                      key={schedule.id}
                      className="absolute left-0 w-full rounded-md p-2 shadow-sm transition-all hover:shadow-md"
                      style={{
                        top: `${startPosition}px`,
                        height: `${height}px`,
                        ...getCourseCardStyle(schedule.courseType, schedule.status),
                      }}
                    >
                      <div className="flex h-full flex-col justify-between">
                        <div>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className="font-medium">{schedule.courseName}</div>
                              {schedule.status === "suspended" && (
                                <Badge variant="secondary" className="bg-orange-100 text-orange-800 text-xs">
                                  已停课
                                </Badge>
                              )}
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon" className="h-6 w-6">
                                  <MoreHorizontal className="h-3 w-3" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => openEditDialog(schedule)}>编辑课程</DropdownMenuItem>
                                {schedule.status === "suspended" ? (
                                  <DropdownMenuItem
                                    onClick={() => openResumeDialog(schedule)}
                                    className="text-green-600"
                                  >
                                    <PlayCircle className="mr-2 h-4 w-4" />
                                    恢复上课
                                  </DropdownMenuItem>
                                ) : (
                                  <DropdownMenuItem
                                    onClick={() => openSuspendDialog(schedule)}
                                    className="text-orange-600"
                                  >
                                    <PauseCircle className="mr-2 h-4 w-4" />
                                    停课
                                  </DropdownMenuItem>
                                )}
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => handleDeleteSchedule(schedule.id)}
                                >
                                  删除课程
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {schedule.startTime}-{schedule.endTime}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1">
                            <Avatar className="h-5 w-5">
                              <AvatarImage src={coach.avatar} alt={coach.name} />
                              <AvatarFallback>{coach.name[0]}</AvatarFallback>
                            </Avatar>
                            <span className="text-xs">{coach.name}</span>
                          </div>
                          <div className="text-xs">{venue.name}</div>
                        </div>
                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs">
                            {courseType.name}
                          </Badge>
                          <div className="text-xs">
                            {schedule.bookings}/{schedule.capacity}
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // 修改 renderWeekView 函数，添加 ref 和更新 PopoverContent
  const renderWeekView = () => {
    const startDate = weekDates[0]
    const endDate = weekDates[6]
    const startDateStr = startDate.toLocaleDateString("zh-CN", { month: "numeric", day: "numeric" })
    const endDateStr = endDate.toLocaleDateString("zh-CN", { month: "numeric", day: "numeric" })

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>
                {startDateStr} - {endDateStr}
              </CardTitle>
              <CardDescription>本周课程安排</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="icon" onClick={handlePreviousWeek}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" onClick={handleNextWeek}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-4" ref={weekViewRef}>
            {weekDayTitles.map((day, index) => (
              <div key={index} className="text-center font-medium">
                {day}
              </div>
            ))}
            {formattedWeekDates.map((date, index) => (
              <div key={index} className="text-center text-sm text-muted-foreground">
                {getDateTitle(date)}
              </div>
            ))}
            {formattedWeekDates.map((date, index) => (
              <div
                key={index}
                className={cn(
                  "h-64 border rounded-md p-2 relative overflow-auto",
                  date === formatDate(new Date()) && "bg-blue-50",
                )}
              >
                {schedulesByDate[date]?.map((schedule) => {
                  const courseType = getCourseTypeInfo(schedule.courseType)
                  return (
                    <Popover key={schedule.id}>
                      <PopoverTrigger asChild>
                        <div
                          className="mb-1 cursor-pointer rounded-md p-1 text-xs shadow-sm transition-all hover:shadow-md"
                          style={getCourseCardStyle(schedule.courseType, schedule.status)}
                        >
                          <div className="font-medium truncate">
                            {schedule.courseName}
                            {schedule.status === "suspended" && (
                              <span className="ml-1 text-orange-600 text-xs">[已停课]</span>
                            )}
                          </div>
                          <div className="text-muted-foreground">{getTimeSlotTitle(schedule)}</div>
                        </div>
                      </PopoverTrigger>
                      <PopoverContent className="w-80">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{schedule.courseName}</h4>
                            <Badge style={{ backgroundColor: courseType.color }}>{courseType.name}</Badge>
                          </div>
                          <div className="text-sm">
                            <div className="flex items-center gap-2 mb-1">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span>
                                {date} {getTimeSlotTitle(schedule)}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 mb-1">
                              <Avatar className="h-5 w-5">
                                <AvatarImage
                                  src={getCoachInfo(schedule.coach).avatar}
                                  alt={getCoachInfo(schedule.coach).name}
                                />
                                <AvatarFallback>{getCoachInfo(schedule.coach).name[0]}</AvatarFallback>
                              </Avatar>
                              <span>{getCoachInfo(schedule.coach).name}</span>
                            </div>
                            <div className="flex items-center justify-between mb-1">
                              <span>{getVenueInfo(schedule.venue).name}</span>
                              <span>
                                预约: {schedule.bookings}/{schedule.capacity}
                              </span>
                            </div>
                          </div>

                          {/* 添加会员头像列表 */}
                          <div>
                            <h5 className="text-sm font-medium mb-2 flex items-center gap-1">
                              <Users className="h-4 w-4" /> 已预约会员
                            </h5>
                            <div className="flex flex-wrap gap-1">
                              {schedule.bookedMembers.length > 0 ? (
                                schedule.bookedMembers.map((memberId, idx) => {
                                  const member = getMemberInfo(memberId)
                                  return (
                                    <TooltipProvider key={idx}>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Avatar className="h-8 w-8 cursor-pointer">
                                            <AvatarImage src={member.avatar} alt={member.name} />
                                            <AvatarFallback>{member.name[0]}</AvatarFallback>
                                          </Avatar>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          <p>{member.name}</p>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  )
                                })
                              ) : (
                                <p className="text-sm text-muted-foreground">暂无会员预约</p>
                              )}
                            </div>
                          </div>

                          <div className="flex justify-end gap-2 mt-2">
                            <Button variant="outline" size="sm" onClick={() => openEditDialog(schedule)}>
                              编辑
                            </Button>
                            {schedule.status === "suspended" ? (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openResumeDialog(schedule)}
                                className="text-green-600 hover:text-green-700"
                              >
                                <PlayCircle className="mr-1 h-3 w-3" />
                                恢复上课
                              </Button>
                            ) : (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openSuspendDialog(schedule)}
                                className="text-orange-600 hover:text-orange-700"
                              >
                                <PauseCircle className="mr-1 h-3 w-3" />
                                停课
                              </Button>
                            )}
                            <Button variant="destructive" size="sm" onClick={() => handleDeleteSchedule(schedule.id)}>
                              删除
                            </Button>
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  )
                })}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  // 修改 renderMonthView 函数，添加 ref
  const renderMonthView = () => {
    const year = selectedDate.getFullYear()
    const month = selectedDate.getMonth()
    const monthName = selectedDate.toLocaleDateString("zh-CN", { year: "numeric", month: "long" })

    // 获取当月第一天是星期几
    const firstDay = new Date(year, month, 1).getDay() || 7 // 转换为周一为1，周日为7

    // 获取当月天数
    const daysInMonth = new Date(year, month + 1, 0).getDate()

    // 生成日历数据
    const calendarDays = []

    // 上个月的最后几天
    const daysInPrevMonth = new Date(year, month, 0).getDate()
    for (let i = firstDay - 1; i > 0; i--) {
      calendarDays.push({
        date: new Date(year, month - 1, daysInPrevMonth - i + 1),
        isCurrentMonth: false,
      })
    }

    // 当月的天数
    for (let i = 1; i <= daysInMonth; i++) {
      calendarDays.push({
        date: new Date(year, month, i),
        isCurrentMonth: true,
      })
    }

    // 下个月的前几天
    const remainingDays = 42 - calendarDays.length // 6行7列 = 42
    for (let i = 1; i <= remainingDays; i++) {
      calendarDays.push({
        date: new Date(year, month + 1, i),
        isCurrentMonth: false,
      })
    }

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{monthName}</CardTitle>
              <CardDescription>本月课程安排</CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const newDate = new Date(selectedDate)
                  newDate.setMonth(newDate.getMonth() - 1)
                  setSelectedDate(newDate)
                }}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  const newDate = new Date(selectedDate)
                  newDate.setMonth(newDate.getMonth() + 1)
                  setSelectedDate(newDate)
                }}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-1" ref={monthViewRef}>
            {["一", "二", "三", "四", "五", "六", "日"].map((day, index) => (
              <div key={index} className="text-center font-medium p-2">
                周{day}
              </div>
            ))}
            {calendarDays.map((day, index) => {
              const dateStr = formatDate(day.date)
              const daySchedules = filteredSchedules.filter((schedule) => schedule.date === dateStr)

              return (
                <div
                  key={index}
                  className={cn(
                    "h-24 border p-1 relative overflow-hidden",
                    day.isCurrentMonth ? "bg-white" : "bg-gray-50 text-gray-400",
                    dateStr === formatDate(new Date()) && "bg-blue-50",
                  )}
                >
                  <div className="text-xs font-medium">{day.date.getDate()}</div>
                  <div className="space-y-1 mt-1 overflow-hidden">
                    {daySchedules.slice(0, 3).map((schedule) => (
                      <Popover key={schedule.id}>
                        <PopoverTrigger asChild>
                          <div
                            className="text-xs truncate rounded-sm p-0.5"
                            style={getCourseCardStyle(schedule.courseType, schedule.status)}
                          >
                            {schedule.startTime} {schedule.courseName}
                            {schedule.status === "suspended" && (
                              <span className="text-orange-600">[停]</span>
                            )}
                          </div>
                        </PopoverTrigger>
                        <PopoverContent className="w-80">
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium">{schedule.courseName}</h4>
                              <Badge style={{ backgroundColor: getCourseTypeInfo(schedule.courseType).color }}>
                                {getCourseTypeInfo(schedule.courseType).name}
                              </Badge>
                            </div>
                            <div className="text-sm">
                              <div className="flex items-center gap-2 mb-1">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <span>
                                  {dateStr} {getTimeSlotTitle(schedule)}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 mb-1">
                                <Avatar className="h-5 w-5">
                                  <AvatarImage
                                    src={getCoachInfo(schedule.coach).avatar}
                                    alt={getCoachInfo(schedule.coach).name}
                                  />
                                  <AvatarFallback>{getCoachInfo(schedule.coach).name[0]}</AvatarFallback>
                                </Avatar>
                                <span>{getCoachInfo(schedule.coach).name}</span>
                              </div>
                              <div className="flex items-center justify-between mb-1">
                                <span>{getVenueInfo(schedule.venue).name}</span>
                                <span>
                                  预约: {schedule.bookings}/{schedule.capacity}
                                </span>
                              </div>
                            </div>

                            {/* 添加会员头像列表 */}
                            <div>
                              <h5 className="text-sm font-medium mb-2 flex items-center gap-1">
                                <Users className="h-4 w-4" /> 已预约会员
                              </h5>
                              <div className="flex flex-wrap gap-1">
                                {schedule.bookedMembers.length > 0 ? (
                                  schedule.bookedMembers.map((memberId, idx) => {
                                    const member = getMemberInfo(memberId)
                                    return (
                                      <TooltipProvider key={idx}>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Avatar className="h-8 w-8 cursor-pointer">
                                              <AvatarImage src={member.avatar} alt={member.name} />
                                              <AvatarFallback>{member.name[0]}</AvatarFallback>
                                            </Avatar>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>{member.name}</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    )
                                  })
                                ) : (
                                  <p className="text-sm text-muted-foreground">暂无会员预约</p>
                                )}
                              </div>
                            </div>

                            <div className="flex justify-end gap-2 mt-2">
                              <Button variant="outline" size="sm" onClick={() => openEditDialog(schedule)}>
                                编辑
                              </Button>
                              {schedule.status === "suspended" ? (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openResumeDialog(schedule)}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <PlayCircle className="mr-1 h-3 w-3" />
                                  恢复上课
                                </Button>
                              ) : (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openSuspendDialog(schedule)}
                                  className="text-orange-600 hover:text-orange-700"
                                >
                                  <PauseCircle className="mr-1 h-3 w-3" />
                                  停课
                                </Button>
                              )}
                              <Button variant="destructive" size="sm" onClick={() => handleDeleteSchedule(schedule.id)}>
                                删除
                              </Button>
                            </div>
                          </div>
                        </PopoverContent>
                      </Popover>
                    ))}
                    {daySchedules.length > 3 && (
                      <div className="text-xs text-muted-foreground text-center">+{daySchedules.length - 3} 更多</div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    )
  }

  // 在 return 语句中的操作按钮部分添加复制课表和导出图片按钮
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">课程排期</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2" onClick={() => setSelectedDate(new Date())}>
            <Calendar className="h-4 w-4" />
            <span>今天</span>
          </Button>

          {/* 添加复制课表按钮 */}
          <Dialog open={isCopyDialogOpen} onOpenChange={setIsCopyDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Copy className="h-4 w-4" />
                <span>复制课表</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>复制课表</DialogTitle>
                <DialogDescription>将现有课表复制到其他日期，快速创建重复课程。</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label>复制类型</Label>
                  <div className="flex gap-4">
                    <div className="flex items-center gap-2">
                      <input
                        type="radio"
                        id="copy-day"
                        name="copy-type"
                        checked={copySource.type === "day"}
                        onChange={() => setCopySource({ ...copySource, type: "day" })}
                      />
                      <Label htmlFor="copy-day">单日课程</Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="radio"
                        id="copy-week"
                        name="copy-type"
                        checked={copySource.type === "week"}
                        onChange={() => setCopySource({ ...copySource, type: "week" })}
                      />
                      <Label htmlFor="copy-week">整周课程</Label>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="source-date">源日期</Label>
                    <Input
                      id="source-date"
                      type="date"
                      value={copySource.date}
                      onChange={(e) => setCopySource({ ...copySource, date: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="target-date">目标日期</Label>
                    <Input
                      id="target-date"
                      type="date"
                      value={copyTarget.date}
                      onChange={(e) => setCopyTarget({ ...copyTarget, date: e.target.value })}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCopyDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCopySchedule}>复制</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* 添加导出图片按钮 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                <FileImage className="h-4 w-4" />
                <span>导出图片</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleExportImage("day")}>导出日视图</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExportImage("week")}>导出周视图</DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExportImage("month")}>导出月视图</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                添加排期
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>添加课程排期</DialogTitle>
                <DialogDescription>创建新的课程排期，填写以下信息。</DialogDescription>
              </DialogHeader>
              <div className="grid gap-6 py-4">
                {/* 周期性课程设置 */}
                <div className="flex items-center space-x-2 p-4 bg-gray-50 rounded-lg">
                  <Switch
                    id="isRecurring"
                    checked={isRecurring}
                    onCheckedChange={setIsRecurring}
                  />
                  <Label htmlFor="isRecurring" className="text-sm font-medium">设置为周期性课程</Label>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="courseType">课程类型</Label>
                    <Select
                      value={newSchedule.courseType}
                      onValueChange={handleCourseTypeChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择课程类型" />
                      </SelectTrigger>
                      <SelectContent>
                        {courseTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            <div className="flex items-center gap-2">
                              <div className="h-3 w-3 rounded-full" style={{ backgroundColor: type.color }} />
                              <span>{type.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="courseName">课程名称</Label>
                    <Popover open={courseSearchOpen} onOpenChange={setCourseSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={courseSearchOpen}
                          className="w-full justify-between"
                        >
                          {newSchedule.courseName || "选择课程..."}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-full p-0">
                        <Command>
                          <CommandInput
                            placeholder="搜索课程..."
                            value={courseSearchValue}
                            onValueChange={setCourseSearchValue}
                          />
                          <CommandEmpty>未找到课程</CommandEmpty>
                          <CommandGroup className="max-h-64 overflow-auto">
                            {getFilteredCourses(newSchedule.courseType).map((course) => (
                              <CommandItem
                                key={course.id}
                                value={course.name}
                                onSelect={() => handleCourseSelect(course)}
                              >
                                <Check
                                  className={`mr-2 h-4 w-4 ${
                                    newSchedule.courseName === course.name ? "opacity-100" : "opacity-0"
                                  }`}
                                />
                                <div className="flex-1">
                                  <div className="font-medium">{course.name}</div>
                                  <div className="text-sm text-muted-foreground">{course.description}</div>
                                  <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
                                    <span>教练: {getCoachInfo(course.defaultCoach).name}</span>
                                    <span>时长: {course.defaultDuration}分钟</span>
                                    <span>教室: {getVenueInfo(course.defaultVenue).name}</span>
                                    <span>容量: {course.defaultCapacity}人</span>
                                    <span className="text-green-600 font-medium">¥{course.defaultPrice}</span>
                                  </div>
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="coach">教练</Label>
                    <Select
                      value={newSchedule.coach}
                      onValueChange={handleCoachSelect}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择教练" />
                      </SelectTrigger>
                      <SelectContent>
                        {coaches.map((coach) => (
                          <SelectItem key={coach.id} value={coach.id}>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-5 w-5">
                                <AvatarImage src={coach.avatar} alt={coach.name} />
                                <AvatarFallback>{coach.name[0]}</AvatarFallback>
                              </Avatar>
                              <span>{coach.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="venue">场地</Label>
                    <Select
                      value={newSchedule.venue}
                      onValueChange={(value) => setNewSchedule({ ...newSchedule, venue: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择场地" />
                      </SelectTrigger>
                      <SelectContent>
                        {venues.map((venue) => (
                          <SelectItem key={venue.id} value={venue.id}>
                            {venue.name} (容量: {venue.capacity})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* 课程时长和课时费 */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="duration">课程时长 (分钟)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={newSchedule.duration}
                      onChange={(e) => handleDurationChange(Number(e.target.value))}
                      placeholder="课程时长"
                      min="15"
                      max="180"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="price">课时费 (元)</Label>
                    <Input
                      id="price"
                      type="number"
                      value={newSchedule.price}
                      onChange={(e) => setNewSchedule({ ...newSchedule, price: Number(e.target.value) })}
                      placeholder="课时费"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">开始日期</Label>
                    <Input
                      id="date"
                      type="date"
                      value={newSchedule.date}
                      onChange={(e) => setNewSchedule({ ...newSchedule, date: e.target.value })}
                    />
                  </div>

                  {isRecurring && (
                    <div className="space-y-2">
                      <Label htmlFor="endDate">结束日期</Label>
                      <Input
                        id="endDate"
                        type="date"
                        value={endDate}
                        onChange={(e) => setEndDate(e.target.value)}
                        placeholder="选择结束日期"
                      />
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>课程时段 ({selectedTimeSlots.length}/5)</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addTimeSlot}
                      disabled={selectedTimeSlots.length >= 5}
                      className="h-8"
                    >
                      <PlusIcon className="h-4 w-4 mr-1" />
                      添加时段
                    </Button>
                  </div>

                  <div className="space-y-3">
                    {selectedTimeSlots.map((slot, index) => {
                      const isValid = isValidTimeSlot(slot.start, slot.end)
                      return (
                      <div key={index} className={`flex items-center gap-2 p-3 border rounded-lg ${
                        isValid ? 'bg-gray-50 border-gray-200' : 'bg-red-50 border-red-200'
                      }`}>
                        <div className="flex-1 grid grid-cols-2 gap-2">
                          <div className="space-y-1">
                            <Label className="text-xs">开始时间</Label>
                            <Select
                              value={slot.start}
                              onValueChange={(value) => updateTimeSlot(index, 'start', value)}
                            >
                              <SelectTrigger className="h-8">
                                <SelectValue placeholder="开始时间" />
                              </SelectTrigger>
                              <SelectContent>
                                {generateTimeOptions().map((time) => (
                                  <SelectItem key={time} value={time}>
                                    {time}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="space-y-1">
                            <Label className="text-xs">结束时间</Label>
                            <Input
                              value={slot.end}
                              readOnly
                              className="h-8 bg-gray-100 cursor-not-allowed"
                              placeholder="自动计算"
                            />
                          </div>
                        </div>
                        {selectedTimeSlots.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeTimeSlot(index)}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                        {!isValid && (
                          <div className="col-span-2 text-xs text-red-500 mt-1">
                            ⚠️ 结束时间必须大于开始时间
                          </div>
                        )}
                      </div>
                      )
                    })}
                  </div>

                  {/* 时段限制提示 */}
                  <div className="text-xs text-muted-foreground">
                    💡 提示：最多可添加5个时段，时间精确到5分钟，结束时间根据开始时间和课程时长自动计算
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="capacity">容量</Label>
                  <Input
                    id="capacity"
                    type="number"
                    value={newSchedule.capacity}
                    onChange={(e) => setNewSchedule({ ...newSchedule, capacity: Number.parseInt(e.target.value) })}
                  />
                </div>

                {isRecurring && (
                  <div className="space-y-2">
                    <Label>重复日期</Label>
                    <div className="flex flex-wrap gap-2">
                      {["周一", "周二", "周三", "周四", "周五", "周六", "周日"].map((day) => (
                        <div key={day} className="flex items-center space-x-2">
                          <Checkbox
                            id={`day-${day}`}
                            checked={selectedDays.includes(day)}
                            onCheckedChange={() => handleDayToggle(day)}
                          />
                          <Label htmlFor={`day-${day}`} className="text-sm">{day}</Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAddSchedule}>添加</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="w-full md:w-1/3">
          <Select value={selectedVenue} onValueChange={setSelectedVenue}>
            <SelectTrigger>
              <SelectValue placeholder="选择场地" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有场地</SelectItem>
              {venues.map((venue) => (
                <SelectItem key={venue.id} value={venue.id}>
                  {venue.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="w-full md:w-1/3">
          <Select value={selectedCoach} onValueChange={setSelectedCoach}>
            <SelectTrigger>
              <SelectValue placeholder="选择教练" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有教练</SelectItem>
              {coaches.map((coach) => (
                <SelectItem key={coach.id} value={coach.id}>
                  {coach.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="w-full md:w-1/3">
          <Select value={selectedCourseType} onValueChange={setSelectedCourseType}>
            <SelectTrigger>
              <SelectValue placeholder="课程类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              {courseTypes.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="week">
        <TabsList>
          <TabsTrigger value="day">日视图</TabsTrigger>
          <TabsTrigger value="week">周视图</TabsTrigger>
          <TabsTrigger value="month">月视图</TabsTrigger>
        </TabsList>
        <TabsContent value="day" className="mt-4">
          {renderDayView()}
        </TabsContent>
        <TabsContent value="week" className="mt-4">
          {renderWeekView()}
        </TabsContent>
        <TabsContent value="month" className="mt-4">
          {renderMonthView()}
        </TabsContent>
      </Tabs>

      {/* 编辑课程对话框 */}
      {currentSchedule && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>编辑课程排期</DialogTitle>
              <DialogDescription>修改课程排期信息。</DialogDescription>
            </DialogHeader>
            <div className="grid gap-6 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-courseType">课程类型</Label>
                  <Select
                    value={currentSchedule.courseType}
                    onValueChange={(value) => setCurrentSchedule({ ...currentSchedule, courseType: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="基础瑜伽" />
                    </SelectTrigger>
                    <SelectContent>
                      {courseTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          <div className="flex items-center gap-2">
                            <div className="h-3 w-3 rounded-full" style={{ backgroundColor: type.color }} />
                            <span>{type.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-courseName">课程名称</Label>
                  <Input
                    id="edit-courseName"
                    value={currentSchedule.courseName}
                    onChange={(e) => setCurrentSchedule({ ...currentSchedule, courseName: e.target.value })}
                    placeholder="基础瑜伽入门"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-coach">教练</Label>
                  <Select
                    value={currentSchedule.coach}
                    onValueChange={(value) => setCurrentSchedule({ ...currentSchedule, coach: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择教练" />
                    </SelectTrigger>
                    <SelectContent>
                      {coaches.map((coach) => (
                        <SelectItem key={coach.id} value={coach.id}>
                          <div className="flex items-center gap-2">
                            <Avatar className="h-5 w-5">
                              <AvatarImage src={coach.avatar} alt={coach.name} />
                              <AvatarFallback>{coach.name[0]}</AvatarFallback>
                            </Avatar>
                            <span>{coach.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-venue">场地</Label>
                  <Select
                    value={currentSchedule.venue}
                    onValueChange={(value) => setCurrentSchedule({ ...currentSchedule, venue: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择场地" />
                    </SelectTrigger>
                    <SelectContent>
                      {venues.map((venue) => (
                        <SelectItem key={venue.id} value={venue.id}>
                          {venue.name} (容量: {venue.capacity})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 课程时长和课时费 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-duration">课程时长 (分钟)</Label>
                  <Input
                    id="edit-duration"
                    type="number"
                    value={currentSchedule.duration || 90}
                    onChange={(e) => setCurrentSchedule({ ...currentSchedule, duration: Number(e.target.value) })}
                    placeholder="课程时长"
                    min="15"
                    max="180"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-price">课时费 (元)</Label>
                  <Input
                    id="edit-price"
                    type="number"
                    value={currentSchedule.price || 80}
                    onChange={(e) => setCurrentSchedule({ ...currentSchedule, price: Number(e.target.value) })}
                    placeholder="课时费"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-date">开始日期</Label>
                <Input
                  id="edit-date"
                  type="date"
                  value={currentSchedule.date}
                  onChange={(e) => setCurrentSchedule({ ...currentSchedule, date: e.target.value })}
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>课程时段 (1/1)</Label>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2 p-3 border rounded-lg bg-gray-50 border-gray-200">
                    <div className="flex-1 grid grid-cols-2 gap-2">
                      <div className="space-y-1">
                        <Label className="text-xs">开始时间</Label>
                        <Select
                          value={currentSchedule.startTime}
                          onValueChange={(value) => setCurrentSchedule({ ...currentSchedule, startTime: value })}
                        >
                          <SelectTrigger className="h-8">
                            <SelectValue placeholder="开始时间" />
                          </SelectTrigger>
                          <SelectContent>
                            {generateTimeOptions().map((time) => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">结束时间</Label>
                        <Input
                          value={currentSchedule.endTime}
                          readOnly
                          className="h-8 bg-gray-100 cursor-not-allowed"
                          placeholder="自动计算"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* 时段限制提示 */}
                <div className="text-xs text-muted-foreground">
                  💡 提示：结束时间根据开始时间和课程时长自动计算
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-capacity">容量</Label>
                <Input
                  id="edit-capacity"
                  type="number"
                  value={currentSchedule.capacity}
                  onChange={(e) =>
                    setCurrentSchedule({ ...currentSchedule, capacity: Number.parseInt(e.target.value) })
                  }
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleEditSchedule}>保存</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 停课对话框 */}
      {suspendSchedule && (
        <Dialog open={isSuspendDialogOpen} onOpenChange={setIsSuspendDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>停课确认</DialogTitle>
              <DialogDescription>
                确认停课：{suspendSchedule.courseName} ({suspendSchedule.date} {suspendSchedule.startTime}-{suspendSchedule.endTime})
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="suspend-reason">停课原因 *</Label>
                <Select value={suspendReason} onValueChange={setSuspendReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="请选择停课原因" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="teacher-sick">教练生病</SelectItem>
                    <SelectItem value="teacher-leave">教练请假</SelectItem>
                    <SelectItem value="venue-maintenance">场地维护</SelectItem>
                    <SelectItem value="equipment-failure">设备故障</SelectItem>
                    <SelectItem value="weather">天气原因</SelectItem>
                    <SelectItem value="emergency">紧急情况</SelectItem>
                    <SelectItem value="insufficient-enrollment">报名人数不足</SelectItem>
                    <SelectItem value="other">其他原因</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="suspend-note">停课备注</Label>
                <textarea
                  id="suspend-note"
                  value={suspendNote}
                  onChange={(e) => setSuspendNote(e.target.value)}
                  placeholder="请输入停课的详细说明..."
                  className="w-full min-h-[80px] px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none rounded-md"
                />
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <div className="text-yellow-600 mt-0.5">⚠️</div>
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium">停课提醒：</p>
                    <ul className="mt-1 space-y-1 text-xs">
                      <li>• 停课后将自动通知已预约的会员</li>
                      <li>• 会员的课时将自动退还</li>
                      <li>• 停课记录将保存在系统中</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsSuspendDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleSuspendSchedule}
                className="bg-orange-600 hover:bg-orange-700"
              >
                确认停课
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* 恢复上课对话框 */}
      {resumeSchedule && (
        <Dialog open={isResumeDialogOpen} onOpenChange={setIsResumeDialogOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>恢复上课确认</DialogTitle>
              <DialogDescription>
                确认恢复上课：{resumeSchedule.courseName} ({resumeSchedule.date} {resumeSchedule.startTime}-{resumeSchedule.endTime})
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              {/* 显示停课历史信息 */}
              {resumeSchedule.suspendReason && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                  <div className="text-sm text-gray-800">
                    <p className="font-medium mb-2">停课历史：</p>
                    <div className="space-y-1 text-xs">
                      <p><span className="font-medium">停课原因：</span>{
                        resumeSchedule.suspendReason === "teacher-sick" ? "教练生病" :
                        resumeSchedule.suspendReason === "teacher-leave" ? "教练请假" :
                        resumeSchedule.suspendReason === "venue-maintenance" ? "场地维护" :
                        resumeSchedule.suspendReason === "equipment-failure" ? "设备故障" :
                        resumeSchedule.suspendReason === "weather" ? "天气原因" :
                        resumeSchedule.suspendReason === "emergency" ? "紧急情况" :
                        resumeSchedule.suspendReason === "insufficient-enrollment" ? "报名人数不足" :
                        "其他原因"
                      }</p>
                      <p><span className="font-medium">停课日期：</span>{resumeSchedule.suspendDate}</p>
                      {resumeSchedule.suspendNote && (
                        <p><span className="font-medium">停课备注：</span>{resumeSchedule.suspendNote}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="resume-note">恢复备注</Label>
                <textarea
                  id="resume-note"
                  value={resumeNote}
                  onChange={(e) => setResumeNote(e.target.value)}
                  placeholder="请输入恢复上课的说明（可选）..."
                  className="w-full min-h-[80px] px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none rounded-md"
                />
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <div className="text-green-600 mt-0.5">✅</div>
                  <div className="text-sm text-green-800">
                    <p className="font-medium">恢复提醒：</p>
                    <ul className="mt-1 space-y-1 text-xs">
                      <li>• 课程将恢复正常状态，可以接受预约</li>
                      <li>• 系统将自动通知相关会员</li>
                      <li>• 停课历史记录将被保留</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsResumeDialogOpen(false)}>
                取消
              </Button>
              <Button
                onClick={handleResumeSchedule}
                className="bg-green-600 hover:bg-green-700"
              >
                确认恢复
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

