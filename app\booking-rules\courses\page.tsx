"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Calendar, Save, Plus, Trash, Clock, ArrowLeft, Settings, FileText, BookOpen, Layers, Copy, AlertTriangle } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { useRouter } from "next/navigation"
import { BookingRulesSettings } from "@/components/courses/booking-rules-settings"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CourseTypeRules } from "@/components/booking-rules/course-type-rules"

// 模拟课程类型数据
const courseTypes = [
  { id: "CT001", name: "哈他瑜伽", color: "#4CAF50" },
  { id: "CT002", name: "阴瑜伽", color: "#2196F3" },
  { id: "CT003", name: "流瑜伽", color: "#FF9800" },
  { id: "CT004", name: "私教课", color: "#9C27B0" },
  { id: "CT005", name: "普拉提", color: "#F44336" },
]

// 模拟课程数据
const courses = [
  { id: "C001", name: "哈他瑜伽基础", typeId: "CT001", color: "#4CAF50" },
  { id: "C002", name: "阴瑜伽入门", typeId: "CT002", color: "#2196F3" },
  { id: "C003", name: "流瑜伽进阶", typeId: "CT003", color: "#FF9800" },
  { id: "C004", name: "私教一对一", typeId: "CT004", color: "#9C27B0" },
  { id: "C005", name: "普拉提塑形", typeId: "CT005", color: "#F44336" },
]

export default function CourseBookingRulesPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("global")
  const [selectedCourseType, setSelectedCourseType] = useState("")
  const [selectedCourse, setSelectedCourse] = useState("")
  const [ruleTab, setRuleTab] = useState("booking")
  const [hasChanges, setHasChanges] = useState(false)
  const [showConflictAlert, setShowConflictAlert] = useState(false)

  // 根据选择的课程类型筛选课程
  const filteredCourses = selectedCourseType && selectedCourseType !== "all"
    ? courses.filter(course => course.typeId === selectedCourseType)
    : courses

  // 保存设置
  const handleSave = (settings: any) => {
    console.log("保存课程预约规则设置:", settings)
    toast({
      title: "规则已保存",
      description: "课程预约规则已成功保存",
    })
    setHasChanges(false)
    setShowConflictAlert(false)
  }

  // 处理规则变更
  const handleRuleChange = () => {
    setHasChanges(true)
    // 模拟规则冲突检测
    if (Math.random() > 0.7 && !showConflictAlert) {
      setShowConflictAlert(true)
    }
  }

  // 重置设置
  const handleReset = () => {
    toast({
      title: "设置已重置",
      description: "课程规则设置已重置为上次保存的状态",
    })
    setHasChanges(false)
    setShowConflictAlert(false)
  }

  // 从其他类型复制规则
  const handleCopyFromType = (sourceTypeId: string) => {
    const sourceType = courseTypes.find(type => type.id === sourceTypeId)
    if (sourceType) {
      toast({
        title: "已复制规则",
        description: `已将${sourceType.name}的规则复制到当前课程类型`,
      })
      setHasChanges(true)
    }
  }

  // 从全局规则复制
  const handleCopyFromGlobal = () => {
    toast({
      title: "已复制全局规则",
      description: "已将全局规则复制到当前课程类型",
    })
    setHasChanges(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.push("/booking-rules")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">课程预约规则</h1>
            <p className="text-muted-foreground">
              设置课程相关的预约、签到、取消、候补和惩罚规则
            </p>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={!hasChanges}
          >
            <Clock className="h-4 w-4 mr-2" />
            重置
          </Button>
          <Button
            onClick={handleSave}
            disabled={!hasChanges}
          >
            <Save className="h-4 w-4 mr-2" />
            保存设置
          </Button>
        </div>
      </div>

      {showConflictAlert && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>检测到规则冲突</AlertTitle>
          <AlertDescription>
            当前设置与全局规则或其他课程类型规则存在冲突。请检查并调整设置，或确认覆盖现有规则。
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Layers className="h-5 w-5 mr-2 text-primary" />
            课程预约规则层级
          </CardTitle>
          <CardDescription>
            课程预约规则分为三个层级：全局规则、课程类型规则和具体课程规则
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm">
              规则按照从具体到一般的顺序应用，更具体的规则会覆盖更一般的规则：
            </p>
            <ol className="list-decimal list-inside space-y-1 text-sm ml-4">
              <li>具体课程规则（最高优先级）</li>
              <li>课程类型规则</li>
              <li>全局课程规则（最低优先级）</li>
            </ol>
            <p className="text-sm">
              例如，如果一个课程有自己的预约规则，那么这个规则会覆盖课程类型规则和全局规则。
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>选择规则层级</CardTitle>
            <CardDescription>
              选择要设置的预约规则层级
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="global">全局</TabsTrigger>
                  <TabsTrigger value="type">课程类型</TabsTrigger>
                  <TabsTrigger value="course">具体课程</TabsTrigger>
                </TabsList>
              </Tabs>

              {activeTab === "type" && (
                <div className="space-y-2">
                  <Label htmlFor="course-type-select">课程类型</Label>
                  <Select value={selectedCourseType} onValueChange={setSelectedCourseType}>
                    <SelectTrigger id="course-type-select">
                      <SelectValue placeholder="选择课程类型" />
                    </SelectTrigger>
                    <SelectContent>
                      {courseTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          <div className="flex items-center">
                            <div
                              className="w-3 h-3 rounded-full mr-2"
                              style={{ backgroundColor: type.color }}
                            ></div>
                            {type.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {activeTab === "course" && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="course-type-filter">课程类型筛选</Label>
                    <Select value={selectedCourseType} onValueChange={(value) => {
                      setSelectedCourseType(value)
                      setSelectedCourse("")
                    }}>
                      <SelectTrigger id="course-type-filter">
                        <SelectValue placeholder="选择课程类型（可选）" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部类型</SelectItem>
                        {courseTypes.map((type) => (
                          <SelectItem key={type.id} value={type.id}>
                            <div className="flex items-center">
                              <div
                                className="w-3 h-3 rounded-full mr-2"
                                style={{ backgroundColor: type.color }}
                              ></div>
                              {type.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="course-select">课程</Label>
                    <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                      <SelectTrigger id="course-select">
                        <SelectValue placeholder="选择课程" />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredCourses.map((course) => (
                          <SelectItem key={course.id} value={course.id}>
                            <div className="flex items-center">
                              <div
                                className="w-3 h-3 rounded-full mr-2"
                                style={{ backgroundColor: course.color }}
                              ></div>
                              {course.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {activeTab === "type" && selectedCourseType && (
                <div className="mt-4 space-y-2">
                  <Label>规则复制</Label>
                  <div className="flex flex-col gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="justify-start"
                      onClick={handleCopyFromGlobal}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      复制全局规则
                    </Button>

                    <Select onValueChange={handleCopyFromType}>
                      <SelectTrigger>
                        <SelectValue placeholder="复制其他类型规则" />
                      </SelectTrigger>
                      <SelectContent>
                        {courseTypes
                          .filter(type => type.id !== selectedCourseType)
                          .map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              <div className="flex items-center">
                                <div
                                  className="w-3 h-3 rounded-full mr-2"
                                  style={{ backgroundColor: type.color }}
                                ></div>
                                {type.name}
                              </div>
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="md:col-span-3">
          {activeTab === "global" ? (
            <BookingRulesSettings
              level="global"
              onSave={handleSave}
            />
          ) : activeTab === "type" && !selectedCourseType ? (
            <Card>
              <CardContent className="flex items-center justify-center min-h-[400px]">
                <div className="text-center text-muted-foreground">
                  <p>请先选择一个课程类型</p>
                </div>
              </CardContent>
            </Card>
          ) : activeTab === "type" && selectedCourseType ? (
            <div className="space-y-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle>
                    {courseTypes.find(type => type.id === selectedCourseType)?.name || "课程类型"} 规则设置
                  </CardTitle>
                  <CardDescription>
                    设置此类型课程的预约、签到、取消、候补和惩罚规则
                  </CardDescription>
                </CardHeader>
              </Card>

              <Tabs defaultValue="booking" className="w-full">
                <TabsList className="grid grid-cols-5 w-full">
                  <TabsTrigger value="booking" onClick={() => setRuleTab("booking")}>预约规则</TabsTrigger>
                  <TabsTrigger value="checkin" onClick={() => setRuleTab("checkin")}>签到规则</TabsTrigger>
                  <TabsTrigger value="cancellation" onClick={() => setRuleTab("cancellation")}>取消规则</TabsTrigger>
                  <TabsTrigger value="waitlist" onClick={() => setRuleTab("waitlist")}>候补规则</TabsTrigger>
                  <TabsTrigger value="penalty" onClick={() => setRuleTab("penalty")}>惩罚规则</TabsTrigger>
                </TabsList>

                <CourseTypeRules
                  courseTypeId={selectedCourseType === "CT004" ? "private" : "group"}
                  activeTab={ruleTab}
                  onChange={handleRuleChange}
                />
              </Tabs>
            </div>
          ) : activeTab === "course" && !selectedCourse ? (
            <Card>
              <CardContent className="flex items-center justify-center min-h-[400px]">
                <div className="text-center text-muted-foreground">
                  <p>请先选择一个课程</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <BookingRulesSettings
              level="course"
              courseId={selectedCourse}
              onSave={handleSave}
            />
          )}
        </div>
      </div>

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => router.push("/booking-rules")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回规则管理
        </Button>

        <Button variant="outline" onClick={() => router.push("/booking-rules/courses/schedule")}>
          <Calendar className="mr-2 h-4 w-4" />
          课程表规则设置
        </Button>
      </div>
    </div>
  )
}
