"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Calendar, Search, Download, FileText, Filter, RefreshCw, CheckCircle2, X, MoreHorizontal, Eye, Pencil, Trash2, CalendarIcon } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BookingDateRangePickerZh } from "@/components/booking-records/date-range-picker-zh"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

// 模拟预约记录数据
const bookingRecordsData = [
  {
    id: "B001",
    memberId: "M001",
    memberName: "张三",
    memberPhone: "13812345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    courseId: "C001",
    courseName: "哈他瑜伽初级",
    courseTime: "2024-04-10 10:00-11:30",
    teacher: "王教练",
    venue: "1号瑜伽室",
    bookingTime: "2024-04-05 14:23",
    status: "confirmed",
    operatorId: "S001",
    operatorName: "客服小王",
    note: "",
    membershipCard: "瑜伽年卡",
    checkinTime: null,
    checkinStatus: "pending",
  },
  {
    id: "B002",
    memberId: "M002",
    memberName: "李四",
    memberPhone: "13987654321",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    courseId: "C003",
    courseName: "流瑜伽中级",
    courseTime: "2024-04-11 09:00-10:30",
    teacher: "张教练",
    venue: "1号瑜伽室",
    bookingTime: "2024-04-06 09:15",
    status: "confirmed",
    operatorId: "S002",
    operatorName: "客服小李",
    note: "会员要求靠窗位置",
    membershipCard: "瑜伽季卡",
    checkinTime: null,
    checkinStatus: "pending",
  },
  {
    id: "B003",
    memberId: "M003",
    memberName: "王五",
    memberPhone: "13765432198",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    courseId: "C002",
    courseName: "阴瑜伽",
    courseTime: "2024-04-10 14:00-15:30",
    teacher: "李教练",
    venue: "2号瑜伽室",
    bookingTime: "2024-04-06 16:42",
    status: "cancelled",
    operatorId: "S001",
    operatorName: "客服小王",
    cancelReason: "会员临时有事",
    note: "",
    membershipCard: "瑜伽月卡",
    checkinTime: null,
    checkinStatus: "cancelled",
  },
  {
    id: "B004",
    memberId: "M001",
    memberName: "张三",
    memberPhone: "13812345678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    courseId: "C005",
    courseName: "普拉提",
    courseTime: "2024-04-09 11:00-12:00",
    teacher: "刘教练",
    venue: "3号瑜伽室",
    bookingTime: "2024-04-04 10:30",
    status: "completed",
    operatorId: "S003",
    operatorName: "客服小张",
    note: "",
    membershipCard: "瑜伽年卡",
    checkinTime: "2024-04-09 10:50",
    checkinStatus: "checked_in",
  },
  {
    id: "B005",
    memberId: "M004",
    memberName: "赵六",
    memberPhone: "13654321987",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    courseId: "C004",
    courseName: "私教课",
    courseTime: "2024-04-08 16:00-17:00",
    teacher: "赵教练",
    venue: "私教室",
    bookingTime: "2024-04-03 15:20",
    status: "completed",
    operatorId: "S002",
    operatorName: "客服小李",
    note: "会员要求准备瑜伽砖",
    membershipCard: "私教次卡",
    checkinTime: "2024-04-08 15:55",
    checkinStatus: "checked_in",
  },
  {
    id: "B006",
    memberId: "M005",
    memberName: "钱七",
    memberPhone: "13543219876",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    courseId: "C001",
    courseName: "哈他瑜伽初级",
    courseTime: "2024-04-10 10:00-11:30",
    teacher: "王教练",
    venue: "1号瑜伽室",
    bookingTime: "2024-04-07 11:10",
    status: "confirmed",
    operatorId: "S001",
    operatorName: "客服小王",
    note: "",
    membershipCard: "瑜伽年卡",
    checkinTime: null,
    checkinStatus: "pending",
  },
  {
    id: "B007",
    memberId: "M002",
    memberName: "李四",
    memberPhone: "13987654321",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    courseId: "C005",
    courseName: "普拉提",
    courseTime: "2024-04-09 11:00-12:00",
    teacher: "刘教练",
    venue: "3号瑜伽室",
    bookingTime: "2024-04-05 16:30",
    status: "no_show",
    operatorId: "S003",
    operatorName: "客服小张",
    note: "",
    membershipCard: "瑜伽季卡",
    checkinTime: null,
    checkinStatus: "no_show",
  },
]

export default function BookingRecordsPage() {
  // 状态管理
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedTeacher, setSelectedTeacher] = useState("all")
  const [selectedVenue, setSelectedVenue] = useState("all")
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined
    to: Date | undefined
  }>({
    from: undefined,
    to: undefined,
  })
  const [selectedRecords, setSelectedRecords] = useState<string[]>([])
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [selectedRecord, setSelectedRecord] = useState<any>(null)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showCheckinDialog, setShowCheckinDialog] = useState(false)
  const [showCancelDialog, setShowCancelDialog] = useState(false)
  const [cancelReason, setCancelReason] = useState("")

  // 过滤预约记录
  const filteredRecords = bookingRecordsData.filter(record => {
    // 基本搜索过滤
    const searchFilter =
      searchQuery === "" ||
      record.memberName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.memberPhone.includes(searchQuery) ||
      record.courseName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      record.id.toLowerCase().includes(searchQuery.toLowerCase());

    // 标签页过滤
    const tabFilter =
      activeTab === "all" ||
      (activeTab === "upcoming" && ["confirmed"].includes(record.status)) ||
      (activeTab === "completed" && ["completed"].includes(record.status)) ||
      (activeTab === "cancelled" && ["cancelled"].includes(record.status)) ||
      (activeTab === "no_show" && ["no_show"].includes(record.status));

    // 状态过滤
    const statusFilter =
      selectedStatus === "all" ||
      record.status === selectedStatus;

    // 教练过滤
    const teacherFilter =
      selectedTeacher === "all" ||
      record.teacher === selectedTeacher;

    // 场地过滤
    const venueFilter =
      selectedVenue === "all" ||
      record.venue === selectedVenue;

    // 日期范围过滤
    const courseDate = new Date(record.courseTime.split(" ")[0]);
    const dateFilter =
      !dateRange.from ||
      (courseDate >= dateRange.from &&
       (!dateRange.to || courseDate <= dateRange.to));

    return searchFilter && tabFilter && statusFilter && teacherFilter && venueFilter && dateFilter;
  });

  // 处理查看详情
  const handleViewDetail = (record: any) => {
    setSelectedRecord(record);
    setShowDetailDialog(true);
  };

  // 处理签到
  const handleCheckin = (record: any) => {
    setSelectedRecord(record);
    setShowCheckinDialog(true);
  };

  // 确认签到
  const confirmCheckin = () => {
    // 实际应用中，这里应该调用API进行签到
    // 这里仅做模拟
    toast({
      title: "签到成功",
      description: `已为 ${selectedRecord.memberName} 完成签到`,
    });
    setShowCheckinDialog(false);
  };

  // 处理取消预约
  const handleCancelBooking = (record: any) => {
    setSelectedRecord(record);
    setCancelReason("");
    setShowCancelDialog(true);
  };

  // 确认取消预约
  const confirmCancelBooking = () => {
    // 实际应用中，这里应该调用API取消预约
    // 这里仅做模拟
    toast({
      title: "预约已取消",
      description: `已取消 ${selectedRecord.memberName} 的预约`,
    });
    setShowCancelDialog(false);
  };

  // 处理批量操作
  const handleBulkAction = (action: string) => {
    if (selectedRecords.length === 0) {
      toast({
        title: "请先选择记录",
        description: "请至少选择一条记录进行操作",
        variant: "destructive",
      });
      return;
    }

    if (action === "checkin") {
      toast({
        title: "批量签到成功",
        description: `已为 ${selectedRecords.length} 条记录完成签到`,
      });
    } else if (action === "cancel") {
      toast({
        title: "批量取消成功",
        description: `已取消 ${selectedRecords.length} 条预约`,
      });
    } else if (action === "export") {
      setShowExportDialog(true);
    }

    // 重置选择
    setSelectedRecords([]);
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRecords(filteredRecords.map(record => record.id));
    } else {
      setSelectedRecords([]);
    }
  };

  // 获取状态标签颜色
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "confirmed":
        return "default";
      case "completed":
        return "success";
      case "cancelled":
        return "destructive";
      case "no_show":
        return "warning";
      default:
        return "secondary";
    }
  };

  // 获取状态显示文本
  const getStatusText = (status: string) => {
    switch (status) {
      case "confirmed":
        return "已确认";
      case "completed":
        return "已完成";
      case "cancelled":
        return "已取消";
      case "no_show":
        return "爽约";
      default:
        return "未知";
    }
  };

  // 获取签到状态显示文本
  const getCheckinStatusText = (status: string) => {
    switch (status) {
      case "checked_in":
        return "已签到";
      case "pending":
        return "待签到";
      case "no_show":
        return "爽约";
      case "cancelled":
        return "已取消";
      default:
        return "未知";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">预约记录查询</h1>
          <p className="text-muted-foreground">
            查询和管理所有预约记录
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => handleBulkAction("export")}>
            <Download className="mr-2 h-4 w-4" />
            导出记录
          </Button>
          <Button onClick={() => window.location.href = "/premium-services/staff-booking"}>
            <Calendar className="mr-2 h-4 w-4" />
            新建预约
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>筛选条件</CardTitle>
          <CardDescription>设置筛选条件查询预约记录</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-end">
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="search">搜索</Label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="会员姓名/手机号/课程名称..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="grid w-full md:min-w-[240px] items-center gap-1.5">
              <Label htmlFor="date-range">日期范围</Label>
              <BookingDateRangePickerZh
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
                placeholder="选择日期范围"
                className="w-full"
              />
            </div>

            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="status">状态</Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="confirmed">已确认</SelectItem>
                  <SelectItem value="completed">已完成</SelectItem>
                  <SelectItem value="cancelled">已取消</SelectItem>
                  <SelectItem value="no_show">爽约</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Button variant="default" onClick={() => {
                // 实际应用中，这里应该触发查询
                toast({
                  title: "查询成功",
                  description: `找到 ${filteredRecords.length} 条记录`,
                });
              }}>
                查询
              </Button>
              <Button variant="outline" onClick={() => {
                setSearchQuery("");
                setSelectedStatus("all");
                setSelectedTeacher("all");
                setSelectedVenue("all");
                setDateRange({ from: undefined, to: undefined });
              }}>
                重置
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-5">
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="upcoming">待上课</TabsTrigger>
          <TabsTrigger value="completed">已完成</TabsTrigger>
          <TabsTrigger value="cancelled">已取消</TabsTrigger>
          <TabsTrigger value="no_show">爽约</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Checkbox
            id="select-all"
            checked={selectedRecords.length > 0 && selectedRecords.length === filteredRecords.length}
            onCheckedChange={handleSelectAll}
          />
          <Label htmlFor="select-all">全选</Label>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleBulkAction("checkin")}
            disabled={selectedRecords.length === 0}
          >
            <CheckCircle2 className="mr-2 h-4 w-4" />
            批量签到
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleBulkAction("cancel")}
            disabled={selectedRecords.length === 0}
          >
            <X className="mr-2 h-4 w-4" />
            批量取消
          </Button>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]"></TableHead>
                <TableHead>会员信息</TableHead>
                <TableHead>课程信息</TableHead>
                <TableHead>预约时间</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>签到状态</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    没有找到符合条件的记录
                  </TableCell>
                </TableRow>
              ) : (
                filteredRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedRecords.includes(record.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedRecords([...selectedRecords, record.id]);
                          } else {
                            setSelectedRecords(selectedRecords.filter(id => id !== record.id));
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={record.memberAvatar} alt={record.memberName} />
                          <AvatarFallback>{record.memberName.slice(0, 1)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{record.memberName}</div>
                          <div className="text-sm text-muted-foreground">{record.memberPhone}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{record.courseName}</div>
                        <div className="text-sm text-muted-foreground">{record.courseTime}</div>
                        <div className="text-xs text-muted-foreground">{record.teacher} | {record.venue}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{record.bookingTime}</div>
                      <div className="text-xs text-muted-foreground">操作员: {record.operatorName}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadgeVariant(record.status)}>
                        {getStatusText(record.status)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {getCheckinStatusText(record.checkinStatus)}
                      </div>
                      {record.checkinTime && (
                        <div className="text-xs text-muted-foreground">
                          {record.checkinTime}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleViewDetail(record)}>
                            <Eye className="mr-2 h-4 w-4" />
                            查看详情
                          </DropdownMenuItem>
                          {record.status === "confirmed" && (
                            <>
                              <DropdownMenuItem onClick={() => handleCheckin(record)}>
                                <CheckCircle2 className="mr-2 h-4 w-4" />
                                签到
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleCancelBooking(record)}>
                                <X className="mr-2 h-4 w-4" />
                                取消预约
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 预约详情对话框 */}
      <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>预约详情</DialogTitle>
            <DialogDescription>
              预约编号: {selectedRecord?.id}
            </DialogDescription>
          </DialogHeader>

          {selectedRecord && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">会员信息</h3>
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={selectedRecord.memberAvatar} alt={selectedRecord.memberName} />
                    <AvatarFallback>{selectedRecord.memberName.slice(0, 1)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{selectedRecord.memberName}</div>
                    <div className="text-sm text-muted-foreground">{selectedRecord.memberPhone}</div>
                    <div className="text-xs text-muted-foreground">会员卡: {selectedRecord.membershipCard}</div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">课程信息</h3>
                <div className="rounded-md border p-3">
                  <div className="font-medium">{selectedRecord.courseName}</div>
                  <div className="text-sm text-muted-foreground">{selectedRecord.courseTime}</div>
                  <div className="text-sm text-muted-foreground">教练: {selectedRecord.teacher}</div>
                  <div className="text-sm text-muted-foreground">场地: {selectedRecord.venue}</div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">预约状态</h3>
                <div className="flex items-center gap-2">
                  <Badge variant={getStatusBadgeVariant(selectedRecord.status)}>
                    {getStatusText(selectedRecord.status)}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    预约时间: {selectedRecord.bookingTime}
                  </span>
                </div>
                <div className="text-sm text-muted-foreground">
                  操作员: {selectedRecord.operatorName}
                </div>
                {selectedRecord.cancelReason && (
                  <div className="text-sm text-muted-foreground">
                    取消原因: {selectedRecord.cancelReason}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">签到状态</h3>
                <div className="text-sm">
                  {getCheckinStatusText(selectedRecord.checkinStatus)}
                  {selectedRecord.checkinTime && (
                    <span className="ml-2 text-muted-foreground">
                      ({selectedRecord.checkinTime})
                    </span>
                  )}
                </div>
              </div>

              {selectedRecord.note && (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">备注</h3>
                  <div className="text-sm rounded-md border p-3">
                    {selectedRecord.note}
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDetailDialog(false)}>
              关闭
            </Button>
            {selectedRecord?.status === "confirmed" && (
              <>
                <Button variant="default" onClick={() => {
                  setShowDetailDialog(false);
                  handleCheckin(selectedRecord);
                }}>
                  签到
                </Button>
                <Button variant="destructive" onClick={() => {
                  setShowDetailDialog(false);
                  handleCancelBooking(selectedRecord);
                }}>
                  取消预约
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 签到对话框 */}
      <Dialog open={showCheckinDialog} onOpenChange={setShowCheckinDialog}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle>确认签到</DialogTitle>
            <DialogDescription>
              确认为 {selectedRecord?.memberName} 的 {selectedRecord?.courseName} 课程签到
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="rounded-md border p-3 bg-muted/50">
              <div className="font-medium">{selectedRecord?.courseName}</div>
              <div className="text-sm text-muted-foreground">{selectedRecord?.courseTime}</div>
              <div className="text-sm text-muted-foreground">教练: {selectedRecord?.teacher}</div>
              <div className="text-sm text-muted-foreground">场地: {selectedRecord?.venue}</div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCheckinDialog(false)}>
              取消
            </Button>
            <Button onClick={confirmCheckin}>
              确认签到
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 取消预约对话框 */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle>取消预约</DialogTitle>
            <DialogDescription>
              确认取消 {selectedRecord?.memberName} 的 {selectedRecord?.courseName} 课程预约
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="rounded-md border p-3 bg-muted/50">
              <div className="font-medium">{selectedRecord?.courseName}</div>
              <div className="text-sm text-muted-foreground">{selectedRecord?.courseTime}</div>
              <div className="text-sm text-muted-foreground">教练: {selectedRecord?.teacher}</div>
              <div className="text-sm text-muted-foreground">场地: {selectedRecord?.venue}</div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="cancel-reason">取消原因</Label>
              <Input
                id="cancel-reason"
                placeholder="请输入取消原因..."
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCancelDialog(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmCancelBooking}>
              确认取消预约
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 导出对话框 */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle>导出预约记录</DialogTitle>
            <DialogDescription>
              选择导出格式和范围
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>导出格式</Label>
              <Select defaultValue="excel">
                <SelectTrigger>
                  <SelectValue placeholder="选择导出格式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excel">Excel (.xlsx)</SelectItem>
                  <SelectItem value="csv">CSV (.csv)</SelectItem>
                  <SelectItem value="pdf">PDF (.pdf)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>导出范围</Label>
              <Select defaultValue="filtered">
                <SelectTrigger>
                  <SelectValue placeholder="选择导出范围" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="filtered">当前筛选结果 ({filteredRecords.length}条)</SelectItem>
                  <SelectItem value="selected">已选记录 ({selectedRecords.length}条)</SelectItem>
                  <SelectItem value="all">全部记录 ({bookingRecordsData.length}条)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowExportDialog(false)}>
              取消
            </Button>
            <Button onClick={() => {
              toast({
                title: "导出成功",
                description: "预约记录已成功导出",
              });
              setShowExportDialog(false);
            }}>
              确认导出
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
