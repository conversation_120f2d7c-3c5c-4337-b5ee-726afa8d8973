// 测试数据库连接的脚本
const mysql = require('mysql2/promise');

async function testConnection() {
  try {
    // 从环境变量中获取数据库连接信息
    const dbUrl = process.env.DATABASE_URL || 'mysql://root:123456@localhost:3306/yoga';
    
    // 解析数据库URL
    const matches = dbUrl.match(/mysql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)/);
    if (!matches) {
      console.error('无效的数据库URL格式');
      return;
    }
    
    const [, user, password, host, port, database] = matches;
    
    console.log(`尝试连接到数据库: ${host}:${port}/${database}`);
    
    // 创建连接
    const connection = await mysql.createConnection({
      host,
      user,
      password,
      database,
      port: parseInt(port)
    });
    
    console.log('数据库连接成功!');
    
    // 测试查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('测试查询结果:', rows);
    
    // 关闭连接
    await connection.end();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('数据库连接测试失败:', error);
  }
}

testConnection(); 