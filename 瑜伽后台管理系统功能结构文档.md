# 瑜伽后台管理系统功能结构文档

## 系统概述

瑜伽后台管理系统是一套专为瑜伽馆、健身房等场所设计的综合管理平台，提供从会员管理、课程排期到营销推广的全方位解决方案。本系统基于 Next.js 15.2.4、React 19、Shadcn UI、Tailwind CSS 等现代技术栈开发，支持多终端访问（PC端管理后台、会员端小程序等）。

## 系统版本功能对比

| 版本 | 价格范围 | 核心功能模块 | 终端支持 |
| ---- | -------- | ------------ | -------- |
| 基础版 | 598元/年 | 预约闭环功能 + 基础数据统计 | 会员端小程序 + 管理端小程序 |
| 专业版 | 1280~1980元/年 | 基础版功能 + 会员等级 + 自定义标签 | 增加PC端管理后台 |
| 经营版 | 8000~39800元/年 | 专业版功能 + 多功能商城 + 共享股东系统 | 同专业版 |
| 定制版 | 100000元起 | 经营版基础 + 定制开发模块 | 按需扩展 |

## 系统功能模块详解

本文档将系统功能划分为**核心业务功能**（服务预约系统的闭环功能）和**增值扩展功能**（增值服务、瑜伽商城、营销中心等）两大类，以便清晰理解系统架构。

### 一、核心业务功能（服务预约闭环）

#### 1. 工作台

- **工作台**: 系统总览，展示关键业务指标、待办事项
- **前台工作台**: 针对前台接待人员的专用工作界面，提供快捷操作

#### 2. 课程管理

课程管理是系统的核心模块，实现完整的课程预约闭环：

- **课程类型**: 管理不同种类的瑜伽课程（如基础瑜伽、高级瑜伽等）
- **课程列表**: 所有可预约课程的管理
- **系列课程**: 管理连续性课程包
- **课程排期**: 安排课程的具体时间、地点、教练
- **课时费管理**: 设置和管理教练课时费
- **预约记录**: 查看和管理会员的课程预约情况
- **预约规则**: 设置课程预约的规则（如提前预约时间、取消规则等）

#### 3. 评价管理

- **课程与教练评价**: 管理会员对课程和教练的评价
- **评价设置**: 配置评价相关的规则和选项

#### 4. 会员管理

- **会员卡名称**: 设置不同类型的会员卡
- **会员卡列表**: 管理已发放的会员卡
- **会员列表**: 管理所有会员信息
- **会员标签**: 自定义会员标签，便于精准营销
- **会员等级**: 设置会员等级体系
- **潜客管理**: 管理潜在客户信息

#### 5. 教练管理

- **教练列表**: 管理教练基本信息
- **教练排班**: 安排教练的工作时间

#### 6. 场地管理

- **场地列表**: 管理瑜伽室等场地信息
- **场地预订**: 管理场地的预订情况

#### 7. 场馆管理

- **员工管理**: 管理场馆员工信息
- **场馆设置**: 配置场馆基本信息
- **公告管理**: 发布和管理场馆公告

#### 8. 订单管理

- **订单列表**: 管理所有交易订单
- **退款管理**: 处理退款申请

#### 9. 数据统计

- **会员分析**: 会员数据统计和分析
- **课程分析**: 课程数据统计和分析
- **收入分析**: 收入数据统计和分析
- **教练分析**: 教练绩效统计和分析

#### 10. 消息中心

- **消息概览**: 查看系统消息总览
- **消息规则**: 设置消息推送规则
- **发送记录**: 查看消息发送历史

#### 11. 系统设置

- **基础设置**: 系统基本配置
- **用户管理**: 管理后台用户账号
- **角色权限**: 设置不同角色的权限
- **通知设置**: 配置系统通知方式
- **会员端小程序**: 配置会员端小程序
- **操作日志**: 查看系统操作日志

### 二、增值扩展功能

#### 1. 增值服务

- **服务列表**: 管理增值服务项目
- **订购记录**: 查看增值服务订购情况
- **服务配置**: 配置增值服务参数
- **订阅支付**: 管理订阅式付费服务

#### 2. 瑜伽商城

- **商品管理**: 管理商城商品
- **商品分类**: 设置商品分类
- **订单管理**: 管理商城订单
- **虚拟商品**: 管理虚拟商品（如线上课程）
- **物流管理**: 管理商品物流
- **商城设置**: 配置商城基本参数

#### 3. 营销中心

- **优惠券管理**: 创建和管理优惠券
- **促销活动**: 设置各类促销活动
- **会员积分**: 管理会员积分系统
- **拼团活动**: 设置拼团营销活动
- **秒杀活动**: 设置限时秒杀活动
- **营销数据**: 分析营销活动效果

## 系统流程闭环

瑜伽后台管理系统实现了完整的服务预约闭环流程：

1. **课程设置**: 创建课程类型 → 设置课程内容 → 安排课程排期
2. **会员管理**: 会员注册 → 会员卡购买 → 会员等级提升
3. **预约流程**: 会员预约课程 → 签到上课 → 课后评价
4. **运营分析**: 数据收集 → 统计分析 → 运营优化

## 终端支持

系统支持多种终端访问方式：

1. **PC端管理后台**: 提供全面的管理功能，适合管理员和工作人员使用
2. **会员端小程序**: 提供给会员使用，支持课程预约、查看课表等功能
3. **管理端小程序**: 简化版管理功能，方便管理人员移动办公

## 系统优势

1. **全面的业务覆盖**: 从会员管理、课程预约到营销推广，覆盖瑜伽馆经营的各个环节
2. **灵活的配置选项**: 提供丰富的自定义设置，适应不同规模场馆的需求
3. **数据驱动决策**: 强大的数据分析功能，帮助管理者做出更明智的经营决策
4. **多端协同工作**: 支持PC端和移动端协同工作，提高工作效率
5. **扩展性强**: 模块化设计，可根据需求增加功能模块

## 部署与性能优化

系统在部署时需注意以下几点：

1. 选择合适的服务器配置，推荐至少4核8G内存
2. 针对Next.js应用，禁用部分实验性功能以提高性能
3. 优化API请求，减少不必要的数据传输
4. 合理设置缓存策略，提高页面加载速度
5. 定期维护数据库，确保查询效率

## 结语

瑜伽后台管理系统通过整合课程管理、会员管理、场地管理等核心功能，为瑜伽馆提供了一站式管理解决方案。系统的模块化设计使得用户可以根据实际需求选择适合的版本，并在使用过程中逐步扩展功能。通过本系统，瑜伽馆可以提高运营效率，改善会员体验，最终实现业务增长和品牌提升。
