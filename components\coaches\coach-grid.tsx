"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { MoreHorizontal, Calendar, BarChart, Star, Phone, Mail } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useState } from "react"
import { CoachDetailDialog } from "@/components/coaches/coach-detail-dialog"

const coaches = [
  {
    id: "C001",
    name: "张教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "138****1234",
    specialty: ["基础瑜伽", "高级瑜伽"],
    courses: 12,
    students: 45,
    rating: 4.8,
    status: "active",
    joinDate: "2023-05-15",
    email: "<EMAIL>",
  },
  {
    id: "C002",
    name: "李教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "139****5678",
    specialty: ["高级瑜伽", "阴瑜伽"],
    courses: 8,
    students: 32,
    rating: 4.9,
    status: "active",
    joinDate: "2023-07-22",
    email: "<EMAIL>",
  },
  {
    id: "C003",
    name: "王教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "137****9012",
    specialty: ["阴瑜伽", "孕产瑜伽"],
    courses: 10,
    students: 38,
    rating: 4.7,
    status: "leave",
    joinDate: "2023-03-10",
    email: "<EMAIL>",
  },
  {
    id: "C004",
    name: "赵教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "136****3456",
    specialty: ["孕产瑜伽"],
    courses: 6,
    students: 24,
    rating: 4.6,
    status: "active",
    joinDate: "2023-09-05",
    email: "<EMAIL>",
  },
  {
    id: "C005",
    name: "刘教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "135****7890",
    specialty: ["空中瑜伽", "基础瑜伽"],
    courses: 9,
    students: 36,
    rating: 4.5,
    status: "active",
    joinDate: "2023-11-18",
    email: "<EMAIL>",
  },
  {
    id: "C006",
    name: "陈教练",
    avatar: "/placeholder.svg?height=32&width=32",
    phone: "134****2345",
    specialty: ["基础瑜伽", "阴瑜伽"],
    courses: 7,
    students: 28,
    rating: 4.7,
    status: "resigned",
    joinDate: "2023-01-20",
    email: "<EMAIL>",
  },
]

interface CoachGridProps {
  statusFilter?: string
}

export function CoachGrid({ statusFilter }: CoachGridProps) {
  const [selectedCoach, setSelectedCoach] = useState<any>(null)
  const [showDetail, setShowDetail] = useState(false)

  const filteredCoaches = statusFilter ? coaches.filter((coach) => coach.status === statusFilter) : coaches

  const handleCardClick = (coach: any) => {
    // 使用路由导航到教练详情页面
    window.location.href = `/coaches/${coach.id}`
  }

  return (
    <>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {filteredCoaches.map((coach) => (
          <Card key={coach.id} className="cursor-pointer overflow-hidden" onClick={() => handleCardClick(coach)}>
            <CardHeader className="p-4 pb-0">
              <div className="flex items-center justify-between">
                <Badge
                  variant={coach.status === "active" ? "default" : coach.status === "leave" ? "secondary" : "outline"}
                >
                  {coach.status === "active" ? "在职" : coach.status === "leave" ? "请假中" : "已离职"}
                </Badge>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>操作</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation()
                        window.location.href = `/coaches/${coach.id}`
                      }}
                    >
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                      <Calendar className="mr-2 h-4 w-4" />
                      排课管理
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                      <BarChart className="mr-2 h-4 w-4" />
                      业绩统计
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                      <Star className="mr-2 h-4 w-4" />
                      查看评价
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-6">
              <div className="flex flex-col items-center text-center">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={coach.avatar} alt={coach.name} />
                  <AvatarFallback className="text-xl">{coach.name[0]}</AvatarFallback>
                </Avatar>
                <h3 className="mt-3 text-lg font-semibold">{coach.name}</h3>
                <p className="text-sm text-muted-foreground">ID: {coach.id}</p>
                <div className="mt-2 flex items-center">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="ml-1 font-medium">{coach.rating}</span>
                </div>
                <div className="mt-3 flex flex-wrap justify-center gap-1">
                  {coach.specialty.map((spec) => (
                    <Badge key={spec} variant="outline" className="text-xs">
                      {spec}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between p-4 pt-0">
              <Button variant="ghost" size="sm" className="w-1/2" onClick={(e) => e.stopPropagation()}>
                <Phone className="mr-2 h-4 w-4" />
                电话
              </Button>
              <Button variant="ghost" size="sm" className="w-1/2" onClick={(e) => e.stopPropagation()}>
                <Mail className="mr-2 h-4 w-4" />
                邮件
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      <CoachDetailDialog open={showDetail} onOpenChange={setShowDetail} coach={selectedCoach} />
    </>
  )
}

