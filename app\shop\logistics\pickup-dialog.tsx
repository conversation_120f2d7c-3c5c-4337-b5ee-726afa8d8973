import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { MapPin } from "lucide-react"

interface PickupLocation {
  id: string
  name: string
  address: string
  contactPerson: string
  contactPhone: string
  businessHours: string
  status: string
  description?: string
  latitude?: string
  longitude?: string
  area?: string
}

interface PickupDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  location: PickupLocation | null
  mode: "add" | "edit"
}

export function PickupDialog({ open, onOpenChange, location, mode }: PickupDialogProps) {
  const [activeTab, setActiveTab] = useState("basic")
  const [formData, setFormData] = useState<PickupLocation>(
    location || {
      id: "",
      name: "",
      address: "",
      contactPerson: "",
      contactPhone: "",
      businessHours: "",
      status: "active",
      description: "",
      latitude: "",
      longitude: "",
      area: "",
    }
  )

  const handleInputChange = (field: keyof PickupLocation, value: string | boolean) => {
    setFormData({
      ...formData,
      [field]: value,
    })
  }

  const handleSubmit = () => {
    console.log("保存自提点信息:", formData)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{mode === "add" ? "添加自提点" : "编辑自提点"}</DialogTitle>
          <DialogDescription>
            {mode === "add" ? "添加新的自提点到系统" : `编辑 ${location?.name} 的信息`}
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="location">位置信息</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">自提点名称</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="例如：中心店"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactPerson">联系人</Label>
                  <Input
                    id="contactPerson"
                    value={formData.contactPerson}
                    onChange={(e) => handleInputChange("contactPerson", e.target.value)}
                    placeholder="输入联系人姓名"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contactPhone">联系电话</Label>
                  <Input
                    id="contactPhone"
                    value={formData.contactPhone}
                    onChange={(e) => handleInputChange("contactPhone", e.target.value)}
                    placeholder="输入联系电话"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="businessHours">营业时间</Label>
                  <Input
                    id="businessHours"
                    value={formData.businessHours}
                    onChange={(e) => handleInputChange("businessHours", e.target.value)}
                    placeholder="例如：周一至周日 9:00-21:00"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">自提点描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description || ""}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="添加自提点描述信息"
                    className="min-h-[120px]"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="status" className="cursor-pointer">启用状态</Label>
                  <Switch
                    id="status"
                    checked={formData.status === "active"}
                    onCheckedChange={(checked) => handleInputChange("status", checked ? "active" : "inactive")}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="location" className="space-y-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">地址信息</CardTitle>
                <CardDescription>设置自提点的详细地址和位置信息</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="address">详细地址</Label>
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange("address", e.target.value)}
                    placeholder="输入详细地址"
                    className="min-h-[80px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="area">所属区域</Label>
                  <Input
                    id="area"
                    value={formData.area || ""}
                    onChange={(e) => handleInputChange("area", e.target.value)}
                    placeholder="例如：浦东新区"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="latitude">纬度</Label>
                    <Input
                      id="latitude"
                      value={formData.latitude || ""}
                      onChange={(e) => handleInputChange("latitude", e.target.value)}
                      placeholder="例如：31.2304"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="longitude">经度</Label>
                    <Input
                      id="longitude"
                      value={formData.longitude || ""}
                      onChange={(e) => handleInputChange("longitude", e.target.value)}
                      placeholder="例如：121.4737"
                    />
                  </div>
                </div>

                <Button variant="outline" className="w-full">
                  <MapPin className="mr-2 h-4 w-4" />
                  在地图上选择位置
                </Button>

                {/* 地图预览区域 - 实际项目中可以集成地图API */}
                {(formData.latitude && formData.longitude) && (
                  <div className="w-full h-[200px] bg-muted rounded-md flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <MapPin className="h-6 w-6 mx-auto mb-2" />
                      <p>地图预览区域</p>
                      <p className="text-xs mt-1">
                        经度: {formData.longitude}, 纬度: {formData.latitude}
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSubmit}>
            {mode === "add" ? "添加自提点" : "保存修改"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
