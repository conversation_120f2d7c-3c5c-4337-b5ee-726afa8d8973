"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, CreditCard, DollarSign, Download } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
// 临时移除日期范围选择器，使用简化版本

interface TransactionRecordsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
  }
}

// 模拟消费记录数据
const transactionRecords = [
  {
    id: "T001",
    title: "会员卡续费 - 次卡20次",
    date: "2023-03-01",
    time: "14:25",
    amount: 1600,
    paymentMethod: "微信支付",
    status: "completed", // completed, refunded, pending
    type: "membership", // membership, course, product, other
    staff: "李顾问",
  },
  {
    id: "T002",
    title: "私教课程 - 5次",
    date: "2023-02-15",
    time: "10:10",
    amount: 2000,
    paymentMethod: "支付宝",
    status: "completed",
    type: "course",
    staff: "王教练",
  },
  {
    id: "T003",
    title: "瑜伽垫",
    date: "2023-01-20",
    time: "16:45",
    amount: 280,
    paymentMethod: "微信支付",
    status: "completed",
    type: "product",
    staff: "张店员",
  },
  {
    id: "T004",
    title: "团课预约 - 瑜伽基础班",
    date: "2023-01-10",
    time: "09:30",
    amount: 120,
    paymentMethod: "会员卡扣费",
    status: "completed",
    type: "course",
    staff: "系统",
  },
  {
    id: "T005",
    title: "私教课退款",
    date: "2022-12-25",
    time: "11:20",
    amount: -500,
    paymentMethod: "原路退回",
    status: "refunded",
    type: "course",
    staff: "李顾问",
    refundReason: "教练时间冲突",
  },
]

export function TransactionRecordsDialog({ open, onOpenChange, member }: TransactionRecordsDialogProps) {
  const [activeTab, setActiveTab] = useState<"all" | "membership" | "course" | "product" | "other">("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [startDate, setStartDate] = useState<string>("")
  const [endDate, setEndDate] = useState<string>("")
  
  // 根据标签、搜索和日期范围过滤消费记录
  const filteredRecords = transactionRecords.filter(record => {
    // 根据标签过滤
    if (activeTab !== "all" && record.type !== activeTab) return false
    
    // 根据搜索过滤
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        record.title.toLowerCase().includes(query) ||
        record.paymentMethod.toLowerCase().includes(query) ||
        record.staff.toLowerCase().includes(query)
      )
    }
    
    // 根据日期范围过滤
    if (startDate) {
      const recordDate = new Date(record.date)
      const fromDate = new Date(startDate)
      if (recordDate < fromDate) return false
    }
    
    if (endDate) {
      const recordDate = new Date(record.date)
      const toDate = new Date(endDate)
      toDate.setHours(23, 59, 59, 999) // 设置为当天结束时间
      if (recordDate > toDate) return false
    }
    
    return true
  })
  
  // 计算总消费金额
  const totalAmount = filteredRecords.reduce((sum, record) => sum + record.amount, 0)
  
  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge variant="outline" className="bg-green-50 text-green-700">已完成</Badge>
      case "refunded":
        return <Badge variant="outline" className="bg-red-50 text-red-700">已退款</Badge>
      case "pending":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700">处理中</Badge>
      default:
        return null
    }
  }
  
  // 格式化金额显示
  const formatAmount = (amount: number) => {
    return `¥${Math.abs(amount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
  }
  
  // 导出消费记录
  const handleExport = () => {
    alert(`已导出${filteredRecords.length}条消费记录到Excel文件`)
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>消费记录</DialogTitle>
          <DialogDescription>
            会员 {member.name} 的消费和支付历史
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Tabs defaultValue="all" value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all">全部</TabsTrigger>
              <TabsTrigger value="membership">会员卡</TabsTrigger>
              <TabsTrigger value="course">课程</TabsTrigger>
              <TabsTrigger value="product">商品</TabsTrigger>
              <TabsTrigger value="other">其他</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-between">
            <Input 
              placeholder="搜索交易..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-[300px]"
            />
            
            <div className="flex gap-2">
              <div className="flex items-center gap-2">
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-[140px]"
                  placeholder="开始日期"
                />
                <span>-</span>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-[140px]"
                  placeholder="结束日期"
                />
              </div>
              
              <Button variant="outline" size="icon" onClick={handleExport}>
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="flex justify-between items-center py-2 px-4 bg-muted rounded-md">
            <span>共 {filteredRecords.length} 条记录</span>
            <div className="flex items-center">
              <span className="mr-2">总金额:</span>
              <span className={`font-medium ${totalAmount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatAmount(totalAmount)}
              </span>
            </div>
          </div>
          
          {filteredRecords.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchQuery || startDate || endDate ? (
                <p>没有找到匹配的消费记录</p>
              ) : (
                <p>没有{activeTab === "all" ? "" : activeTab === "membership" ? "会员卡" : activeTab === "course" ? "课程" : activeTab === "product" ? "商品" : "其他"}消费记录</p>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredRecords.map((record) => (
                <Card key={record.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{record.title}</CardTitle>
                        <CardDescription className="flex items-center mt-1">
                          <Calendar className="h-4 w-4 mr-1" />
                          {record.date} {record.time}
                        </CardDescription>
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        <span className={`font-medium ${record.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {record.amount >= 0 ? '' : '-'}{formatAmount(record.amount)}
                        </span>
                        {getStatusBadge(record.status)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pb-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center">
                        <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-muted-foreground mr-1">支付方式:</span>
                        {record.paymentMethod}
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-muted-foreground mr-1">操作人员:</span>
                        {record.staff}
                      </div>
                      {record.refundReason && (
                        <div className="sm:col-span-2 text-red-500">
                          <span className="font-medium">退款原因:</span> {record.refundReason}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
