"use client"

import { Calendar, CreditCard, Layers, User, ChevronRight, MapPin, Clock, Star } from "lucide-react"

export function EnhancedHomePage({ settings = {} }) {
  // 默认设置
  const defaultSettings = {
    title: "静心瑜伽",
    subtitle: "欢迎来到静心瑜伽，开启您的瑜伽之旅",
    primaryColor: "#FF9800",
    secondaryColor: "#F44336",
    borderRadius: 8,
    darkMode: false,
    theme: "warm"
  }

  // 合并设置
  const mergedSettings = { ...defaultSettings, ...settings }

  // 轮播图数据
  const carouselItems = [
    {
      id: 1,
      image: "https://images.unsplash.com/photo-1545205597-3d9d02c29597?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      title: "瑜伽训练营开始啦"
    },
    {
      id: 2,
      image: "https://images.unsplash.com/photo-1599447292180-45fd84092ef4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      title: "新店开业优惠"
    }
  ]

  // 公告数据
  const announcements = [
    "场馆公告：2025年国庆放假通知请点击！！！",
    "新店开业，全场8折优惠"
  ]

  // 课程推荐数据
  const recommendedCourses = [
    {
      id: 1,
      title: "初级瑜伽训练",
      time: "周一 10:00-11:30",
      location: "一号教室",
      trainer: "Sarah",
      image: "https://images.unsplash.com/photo-1599447292180-45fd84092ef4?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
    },
    {
      id: 2,
      title: "高级瑜伽训练",
      time: "周三 14:00-15:30",
      location: "二号教室",
      trainer: "Mike",
      image: "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
    }
  ]

  // 场馆简介数据
  const venueInfo = {
    name: "静心瑜伽美学生活馆",
    address: "北京市朝阳区三里屯SOHO 5号楼3层301",
    phone: "010-12345678",
    hours: "周一至周日 09:00-22:00",
    description: "静心瑜伽美学生活馆是一家专注于提供高品质瑜伽课程的场馆，我们拥有专业的教练团队和舒适的环境，致力于帮助每一位会员找到身心平衡。"
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* 轮播图 */}
      {mergedSettings.showCarousel !== false && (
        <div className="relative w-full h-40 bg-gray-200 overflow-hidden">
          <div className="absolute inset-0">
            <img
              src={carouselItems[0].image}
              alt={carouselItems[0].title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
              <h2 className="text-white text-xl font-bold">{carouselItems[0].title}</h2>
            </div>
          </div>
          <div className="absolute bottom-2 left-0 right-0 flex justify-center">
            <div className="flex gap-1">
              <div className="w-2 h-2 rounded-full bg-white"></div>
              <div className="w-2 h-2 rounded-full bg-white/50"></div>
            </div>
          </div>
        </div>
      )}

      {/* 头部 */}
      <div className="bg-gray-100 p-4 text-center">
        <h1 className="text-xl font-bold">{mergedSettings.title}</h1>
        <p className="text-sm text-gray-600 mt-1">
          {mergedSettings.subtitle}
        </p>
      </div>

      {/* 功能图标 */}
      <div className="grid grid-cols-4 gap-4 p-4">
        <div className="flex flex-col items-center">
          <div
            className="w-14 h-14 rounded-lg flex items-center justify-center mb-1"
            style={{
              borderRadius: `${mergedSettings.borderRadius}px`,
              backgroundColor: "#F3E8FF"
            }}
          >
            <Calendar
              className="h-6 w-6"
              style={{ color: "#9333EA" }}
            />
          </div>
          <span className="text-xs">预约</span>
        </div>

        <div className="flex flex-col items-center">
          <div
            className="w-14 h-14 rounded-lg flex items-center justify-center mb-1"
            style={{
              borderRadius: `${mergedSettings.borderRadius}px`,
              backgroundColor: "#FFE4E4"
            }}
          >
            <CreditCard
              className="h-6 w-6"
              style={{ color: "#F43F5E" }}
            />
          </div>
          <span className="text-xs">会员卡</span>
        </div>

        <div className="flex flex-col items-center">
          <div
            className="w-14 h-14 rounded-lg flex items-center justify-center mb-1"
            style={{
              borderRadius: `${mergedSettings.borderRadius}px`,
              backgroundColor: "#E0E7FF"
            }}
          >
            <Layers
              className="h-6 w-6"
              style={{ color: "#6366F1" }}
            />
          </div>
          <span className="text-xs">课程</span>
        </div>

        <div className="flex flex-col items-center">
          <div
            className="w-14 h-14 rounded-lg flex items-center justify-center mb-1"
            style={{
              borderRadius: `${mergedSettings.borderRadius}px`,
              backgroundColor: "#FCE7F3"
            }}
          >
            <User
              className="h-6 w-6"
              style={{ color: "#EC4899" }}
            />
          </div>
          <span className="text-xs">我的</span>
        </div>
      </div>

      {/* 公告栏 */}
      {mergedSettings.showAnnouncement !== false && (
        <div className="mx-4 my-3 flex items-center bg-white border-l-4 border-orange-500 rounded-md px-3 py-2 text-sm shadow-sm">
          <span className="text-orange-500 mr-2">📢</span>
          <div className="flex-1 truncate">{mergedSettings.announcement || announcements[0]}</div>
          <span className="text-blue-500">查看 &gt;</span>
        </div>
      )}

      {/* 课程推荐 */}
      {mergedSettings.showCourses !== false && (
        <div className="mx-4 my-3">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-base font-bold">课程推荐</h2>
            <span className="text-sm text-gray-500 flex items-center">
              更多 <ChevronRight size={16} />
            </span>
          </div>

          <div className="space-y-3">
            {recommendedCourses.map(course => (
              <div key={course.id} className="bg-white rounded-lg shadow-sm border border-gray-100 flex">
                <div className="w-20 h-20 overflow-hidden">
                  <img
                    src={course.image}
                    alt={course.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-1 p-2">
                  <div className="font-medium">{course.title}</div>
                  <div className="text-xs text-gray-500 flex items-center mt-1">
                    <Clock size={12} className="mr-1" /> {course.time}
                  </div>
                  <div className="text-xs text-gray-500 flex items-center mt-1">
                    <MapPin size={12} className="mr-1" /> {course.location}
                  </div>
                </div>
                <div className="p-2 flex flex-col justify-between items-end">
                  <div className="text-xs text-gray-500">教练: {course.trainer}</div>
                  <button
                    className="px-3 py-1 text-xs rounded-full text-white"
                    style={{ backgroundColor: mergedSettings.primaryColor || "#9333EA" }}
                  >
                    预约
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 场馆简介 */}
      {mergedSettings.showVenueInfo !== false && (
        <div className="mx-4 my-3">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-base font-bold">场馆简介</h2>
            <span className="text-sm text-gray-500 flex items-center">
              详情 <ChevronRight size={16} />
            </span>
          </div>

          <div className="bg-white rounded-lg shadow p-3">
            <h3 className="font-medium">{mergedSettings.venueName || venueInfo.name}</h3>
            <div className="text-sm text-gray-500 mt-2 line-clamp-2">
              {mergedSettings.venueDescription || venueInfo.description}
            </div>
            <div className="mt-2 text-xs text-gray-500 flex items-center">
              <MapPin size={12} className="mr-1" /> {mergedSettings.venueAddress || venueInfo.address}
            </div>
            <div className="mt-1 text-xs text-gray-500 flex items-center">
              <Clock size={12} className="mr-1" /> {mergedSettings.venueHours || venueInfo.hours}
            </div>
            <div className="mt-2 flex items-center">
              <div className="flex">
                {[1, 2, 3, 4, 5].map(star => (
                  <Star key={star} size={12} className="text-yellow-400" fill="currentColor" />
                ))}
              </div>
              <span className="text-xs text-gray-500 ml-1">5.0 (128条评价)</span>
            </div>
          </div>
        </div>
      )}

      {/* 底部导航 */}
      <div className="mt-auto border-t border-gray-200 flex justify-around py-2">
        <div className="flex flex-col items-center" style={{ color: mergedSettings.primaryColor }}>
          <svg viewBox="0 0 24 24" className="h-6 w-6" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
            <polyline points="9 22 9 12 15 12 15 22" />
          </svg>
          <span className="text-xs mt-1">首页</span>
        </div>

        <div className="flex flex-col items-center text-gray-400">
          <Calendar className="h-6 w-6" />
          <span className="text-xs mt-1">预约</span>
        </div>

        <div className="flex flex-col items-center text-gray-400">
          <Layers className="h-6 w-6" />
          <span className="text-xs mt-1">课程</span>
        </div>

        <div className="flex flex-col items-center text-gray-400">
          <User className="h-6 w-6" />
          <span className="text-xs mt-1">我的</span>
        </div>
      </div>
    </div>
  )
}
