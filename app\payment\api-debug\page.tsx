"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Copy, Play, Save, Trash } from "lucide-react"
import { Separator } from "@/components/ui/separator"

export default function PaymentApiDebugPage() {
  const [apiMethod, setApiMethod] = useState("POST")
  const [apiEndpoint, setApiEndpoint] = useState("")
  const [requestBody, setRequestBody] = useState(`{
  "out_trade_no": "YG20250330001",
  "total_fee": 10000,
  "body": "瑜伽课程",
  "notify_url": "https://youryogastudio.com/api/wechat/payment/notify"
}`)
  const [responseBody, setResponseBody] = useState("")
  const [responseStatus, setResponseStatus] = useState("")
  const [responseTime, setResponseTime] = useState("")

  const handleSendRequest = () => {
    // 模拟API请求
    setResponseStatus("200 OK")
    setResponseTime("256ms")
    setResponseBody(`{
  "return_code": "SUCCESS",
  "return_msg": "OK",
  "appid": "wx123456789abcdef",
  "mch_id": "**********",
  "nonce_str": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
  "sign": "C380BEC2BFD727A4B6845133519F3AD6",
  "result_code": "SUCCESS",
  "prepay_id": "wx201410272009395522657a690389285100",
  "trade_type": "NATIVE",
  "code_url": "weixin://wxpay/bizpayurl?pr=NwY5Mz9"
}`)
  }

  const handleClearResponse = () => {
    setResponseBody("")
    setResponseStatus("")
    setResponseTime("")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">支付API调试</h1>
      </div>

      <Tabs defaultValue="wechat" className="space-y-4">
        <TabsList>
          <TabsTrigger value="wechat">微信支付API</TabsTrigger>
          <TabsTrigger value="alipay">支付宝API</TabsTrigger>
          <TabsTrigger value="callback">回调测试</TabsTrigger>
          <TabsTrigger value="saved">已保存请求</TabsTrigger>
        </TabsList>

        <TabsContent value="wechat" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>微信支付API调试</CardTitle>
              <CardDescription>测试微信支付API接口</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <div className="w-32">
                  <Select value={apiMethod} onValueChange={setApiMethod}>
                    <SelectTrigger>
                      <SelectValue placeholder="请求方法" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GET">GET</SelectItem>
                      <SelectItem value="POST">POST</SelectItem>
                      <SelectItem value="PUT">PUT</SelectItem>
                      <SelectItem value="DELETE">DELETE</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <Input
                    placeholder="API端点"
                    value={apiEndpoint || "https://api.mch.weixin.qq.com/pay/unifiedorder"}
                    onChange={(e) => setApiEndpoint(e.target.value)}
                  />
                </div>
                <Button onClick={handleSendRequest}>
                  <Play className="mr-2 h-4 w-4" />
                  发送请求
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="request-body">请求体 (JSON)</Label>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm" onClick={() => navigator.clipboard.writeText(requestBody)}>
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => setRequestBody("")}>
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <Textarea
                    id="request-body"
                    className="font-mono h-[400px]"
                    value={requestBody}
                    onChange={(e) => setRequestBody(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="response-body">响应</Label>
                    <div className="flex items-center gap-2">
                      {responseBody && (
                        <>
                          <Badge variant="outline">{responseStatus}</Badge>
                          <Badge variant="outline">{responseTime}</Badge>
                          <Button variant="ghost" size="sm" onClick={() => navigator.clipboard.writeText(responseBody)}>
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" onClick={handleClearResponse}>
                            <Trash className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                  <Textarea
                    id="response-body"
                    className="font-mono h-[400px]"
                    value={responseBody}
                    readOnly
                    placeholder="响应将显示在这里..."
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button variant="outline">
                  <Save className="mr-2 h-4 w-4" />
                  保存请求
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>常用API示例</CardTitle>
              <CardDescription>微信支付常用API示例</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-md">
                  <h3 className="font-medium mb-2">统一下单接口</h3>
                  <p className="text-sm text-muted-foreground mb-2">创建微信支付订单，获取支付参数</p>
                  <div className="flex gap-2">
                    <Badge variant="outline">POST</Badge>
                    <span className="text-sm">https://api.mch.weixin.qq.com/pay/unifiedorder</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mt-2"
                    onClick={() => {
                      setApiMethod("POST")
                      setApiEndpoint("https://api.mch.weixin.qq.com/pay/unifiedorder")
                      setRequestBody(`{
  "appid": "wx123456789abcdef",
  "mch_id": "**********",
  "nonce_str": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
  "sign_type": "MD5",
  "body": "瑜伽课程",
  "out_trade_no": "YG20250330001",
  "total_fee": 10000,
  "spbill_create_ip": "*************",
  "notify_url": "https://youryogastudio.com/api/wechat/payment/notify",
  "trade_type": "NATIVE"
}`)
                    }}
                  >
                    使用此示例
                  </Button>
                </div>

                <div className="p-4 border rounded-md">
                  <h3 className="font-medium mb-2">查询订单接口</h3>
                  <p className="text-sm text-muted-foreground mb-2">查询微信支付订单状态</p>
                  <div className="flex gap-2">
                    <Badge variant="outline">POST</Badge>
                    <span className="text-sm">https://api.mch.weixin.qq.com/pay/orderquery</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mt-2"
                    onClick={() => {
                      setApiMethod("POST")
                      setApiEndpoint("https://api.mch.weixin.qq.com/pay/orderquery")
                      setRequestBody(`{
  "appid": "wx123456789abcdef",
  "mch_id": "**********",
  "out_trade_no": "YG20250330001",
  "nonce_str": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
  "sign_type": "MD5"
}`)
                    }}
                  >
                    使用此示例
                  </Button>
                </div>

                <div className="p-4 border rounded-md">
                  <h3 className="font-medium mb-2">申请退款接口</h3>
                  <p className="text-sm text-muted-foreground mb-2">申请微信支付退款</p>
                  <div className="flex gap-2">
                    <Badge variant="outline">POST</Badge>
                    <span className="text-sm">https://api.mch.weixin.qq.com/secapi/pay/refund</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mt-2"
                    onClick={() => {
                      setApiMethod("POST")
                      setApiEndpoint("https://api.mch.weixin.qq.com/secapi/pay/refund")
                      setRequestBody(`{
  "appid": "wx123456789abcdef",
  "mch_id": "**********",
  "nonce_str": "5K8264ILTKCH16CQ2502SI8ZNMTM67VS",
  "sign_type": "MD5",
  "out_trade_no": "YG20250330001",
  "out_refund_no": "YG20250330001R",
  "total_fee": 10000,
  "refund_fee": 10000,
  "refund_desc": "客户申请退款"
}`)
                    }}
                  >
                    使用此示例
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alipay" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付宝API调试</CardTitle>
              <CardDescription>测试支付宝API接口</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <div className="w-32">
                  <Select defaultValue="POST">
                    <SelectTrigger>
                      <SelectValue placeholder="请求方法" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GET">GET</SelectItem>
                      <SelectItem value="POST">POST</SelectItem>
                      <SelectItem value="PUT">PUT</SelectItem>
                      <SelectItem value="DELETE">DELETE</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <Input placeholder="API端点" defaultValue="https://openapi.alipay.com/gateway.do" />
                </div>
                <Button>
                  <Play className="mr-2 h-4 w-4" />
                  发送请求
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="alipay-request-body">请求体 (JSON)</Label>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <Textarea
                    id="alipay-request-body"
                    className="font-mono h-[400px]"
                    defaultValue={`{
  "app_id": "2021000000000000",
  "method": "alipay.trade.precreate",
  "charset": "utf-8",
  "sign_type": "RSA2",
  "timestamp": "2025-03-30 12:00:00",
  "version": "1.0",
  "biz_content": {
    "out_trade_no": "YG20250330001",
    "total_amount": "100.00",
    "subject": "瑜伽课程",
    "body": "瑜伽基础课程月卡"
  }
}`}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="alipay-response-body">响应</Label>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <Textarea
                    id="alipay-response-body"
                    className="font-mono h-[400px]"
                    placeholder="响应将显示在这里..."
                    readOnly
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button variant="outline">
                  <Save className="mr-2 h-4 w-4" />
                  保存请求
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>常用API示例</CardTitle>
              <CardDescription>支付宝常用API示例</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-md">
                  <h3 className="font-medium mb-2">当面付接口</h3>
                  <p className="text-sm text-muted-foreground mb-2">创建支付宝当面付订单，获取支付二维码</p>
                  <div className="flex gap-2">
                    <Badge variant="outline">POST</Badge>
                    <span className="text-sm">https://openapi.alipay.com/gateway.do</span>
                  </div>
                  <Button variant="ghost" size="sm" className="mt-2">
                    使用此示例
                  </Button>
                </div>

                <div className="p-4 border rounded-md">
                  <h3 className="font-medium mb-2">手机网站支付接口</h3>
                  <p className="text-sm text-muted-foreground mb-2">创建支付宝手机网站支付订单</p>
                  <div className="flex gap-2">
                    <Badge variant="outline">POST</Badge>
                    <span className="text-sm">https://openapi.alipay.com/gateway.do</span>
                  </div>
                  <Button variant="ghost" size="sm" className="mt-2">
                    使用此示例
                  </Button>
                </div>

                <div className="p-4 border rounded-md">
                  <h3 className="font-medium mb-2">交易查询接口</h3>
                  <p className="text-sm text-muted-foreground mb-2">查询支付宝交易状态</p>
                  <div className="flex gap-2">
                    <Badge variant="outline">POST</Badge>
                    <span className="text-sm">https://openapi.alipay.com/gateway.do</span>
                  </div>
                  <Button variant="ghost" size="sm" className="mt-2">
                    使用此示例
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="callback" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付回调测试</CardTitle>
              <CardDescription>测试支付回调处理</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="callback-url">回调URL</Label>
                  <Input id="callback-url" defaultValue="https://youryogastudio.com/api/payment/notify" />
                </div>

                <div className="space-y-2">
                  <Label>支付渠道</Label>
                  <Select defaultValue="wechat">
                    <SelectTrigger>
                      <SelectValue placeholder="选择支付渠道" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="wechat">微信支付</SelectItem>
                      <SelectItem value="alipay">支付宝</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>回调类型</Label>
                  <Select defaultValue="success">
                    <SelectTrigger>
                      <SelectValue placeholder="选择回调类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="success">支付成功</SelectItem>
                      <SelectItem value="fail">支付失败</SelectItem>
                      <SelectItem value="refund">退款成功</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="callback-body">回调内容</Label>
                  <Textarea
                    id="callback-body"
                    className="font-mono h-[200px]"
                    defaultValue={`<xml>
  <appid><![CDATA[wx123456789abcdef]]></appid>
  <bank_type><![CDATA[CMB_DEBIT]]></bank_type>
  <cash_fee><![CDATA[10000]]></cash_fee>
  <fee_type><![CDATA[CNY]]></fee_type>
  <is_subscribe><![CDATA[Y]]></is_subscribe>
  <mch_id><![CDATA[**********]]></mch_id>
  <nonce_str><![CDATA[5K8264ILTKCH16CQ2502SI8ZNMTM67VS]]></nonce_str>
  <openid><![CDATA[oUpF8uMuAJO_M2pxb1Q9zNjWeS6o]]></openid>
  <out_trade_no><![CDATA[YG20250330001]]></out_trade_no>
  <result_code><![CDATA[SUCCESS]]></result_code>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <sign><![CDATA[C380BEC2BFD727A4B6845133519F3AD6]]></sign>
  <time_end><![CDATA[**************]]></time_end>
  <total_fee>10000</total_fee>
  <trade_type><![CDATA[NATIVE]]></trade_type>
  <transaction_id><![CDATA[4200000123202503301234567890]]></transaction_id>
</xml>`}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="sign-callback">自动签名</Label>
                    <p className="text-sm text-muted-foreground">自动计算并添加签名</p>
                  </div>
                  <Switch id="sign-callback" defaultChecked />
                </div>

                <Button className="w-full">
                  <Play className="mr-2 h-4 w-4" />
                  发送回调请求
                </Button>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="callback-response">回调响应</Label>
                  <Badge variant="outline">200 OK</Badge>
                </div>
                <Textarea
                  id="callback-response"
                  className="font-mono h-[100px]"
                  defaultValue={`<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
</xml>`}
                  readOnly
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="saved" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>已保存请求</CardTitle>
              <CardDescription>查看和管理已保存的API请求</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-md">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">微信支付统一下单</h3>
                    <Badge variant="outline">微信支付</Badge>
                  </div>
                  <div className="flex gap-2 mb-2">
                    <Badge>POST</Badge>
                    <span className="text-sm text-muted-foreground">
                      https://api.mch.weixin.qq.com/pay/unifiedorder
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">创建微信支付订单，获取支付参数</p>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Play className="mr-2 h-4 w-4" />
                      运行
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Copy className="mr-2 h-4 w-4" />
                      复制
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Trash className="mr-2 h-4 w-4" />
                      删除
                    </Button>
                  </div>
                </div>

                <div className="p-4 border rounded-md">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">支付宝当面付</h3>
                    <Badge variant="outline">支付宝</Badge>
                  </div>
                  <div className="flex gap-2 mb-2">
                    <Badge>POST</Badge>
                    <span className="text-sm text-muted-foreground">https://openapi.alipay.com/gateway.do</span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">创建支付宝当面付订单，获取支付二维码</p>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Play className="mr-2 h-4 w-4" />
                      运行
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Copy className="mr-2 h-4 w-4" />
                      复制
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Trash className="mr-2 h-4 w-4" />
                      删除
                    </Button>
                  </div>
                </div>

                <div className="p-4 border rounded-md">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">微信支付退款</h3>
                    <Badge variant="outline">微信支付</Badge>
                  </div>
                  <div className="flex gap-2 mb-2">
                    <Badge>POST</Badge>
                    <span className="text-sm text-muted-foreground">
                      https://api.mch.weixin.qq.com/secapi/pay/refund
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">申请微信支付退款</p>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Play className="mr-2 h-4 w-4" />
                      运行
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Copy className="mr-2 h-4 w-4" />
                      复制
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Trash className="mr-2 h-4 w-4" />
                      删除
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

