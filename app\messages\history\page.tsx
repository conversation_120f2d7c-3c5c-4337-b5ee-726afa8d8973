"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import {
  History,
  Search,
  Filter,
  Eye,
  RotateCcw,
  Calendar,
  Download,
  CheckCircle,
  XCircle,
  Clock,
  Send,
  MessageSquare,
  Mail,
  Smartphone,
  AlertCircle,
  Bell
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { format } from "date-fns"

// 示例数据
const messageHistory = [
  {
    id: "MSG001",
    type: "预约成功",
    recipient: "张三",
    recipientId: "M10001",
    phone: "138****1234",
    channel: "微信",
    sendTime: "2023-05-01 10:00:15",
    status: "已送达",
    readStatus: "已读",
    readTime: "2023-05-01 10:05:32",
    content: "尊敬的张三，您已成功预约高级瑜伽课程，上课时间：2023-05-02 10:00，地点：A区瑜伽室，教练：李教练。期待您的到来！"
  },
  {
    id: "MSG002",
    type: "课程提醒",
    recipient: "李四",
    recipientId: "M10002",
    phone: "139****5678",
    channel: "短信",
    sendTime: "2023-05-01 14:30:22",
    status: "已送达",
    readStatus: "未知",
    readTime: "-",
    content: "尊敬的李四，温馨提醒您，您预约的初级瑜伽课程将于明天(2023-05-02) 15:00开始，地点：B区瑜伽室，教练：王教练。请提前到达，谢谢！"
  },
  {
    id: "MSG003",
    type: "卡到期提醒",
    recipient: "王五",
    recipientId: "M10003",
    phone: "137****9012",
    channel: "APP",
    sendTime: "2023-05-02 09:15:48",
    status: "已送达",
    readStatus: "未读",
    readTime: "-",
    content: "尊敬的王五，您的年卡会员将于2023-05-15到期，为了不影响您的使用，请及时续费。"
  },
  {
    id: "MSG004",
    type: "营销通知",
    recipient: "赵六",
    recipientId: "M10004",
    phone: "136****3456",
    channel: "微信",
    sendTime: "2023-05-02 16:20:05",
    status: "未送达",
    readStatus: "未知",
    readTime: "-",
    content: "尊敬的赵六，我们将于本周六(2023-05-06)举办瑜伽体验活动，活动期间办卡享受8折优惠，欢迎参加！"
  },
  {
    id: "MSG005",
    type: "课程取消",
    recipient: "钱七",
    recipientId: "M10005",
    phone: "135****7890",
    channel: "短信",
    sendTime: "2023-05-03 08:45:33",
    status: "已送达",
    readStatus: "未知",
    readTime: "-",
    content: "尊敬的钱七，很抱歉通知您，您预约的2023-05-03 10:00的高级瑜伽课程因教练临时有事已取消，已自动为您退还课时，给您带来不便敬请谅解。"
  },
  {
    id: "MSG006",
    type: "生日祝福",
    recipient: "孙八",
    recipientId: "M10006",
    phone: "134****2345",
    channel: "短信",
    sendTime: "2023-05-03 09:00:00",
    status: "已送达",
    readStatus: "未知",
    readTime: "-",
    content: "亲爱的孙八，静心瑜伽馆全体员工祝您生日快乐！作为我们尊贵的会员，我们为您准备了生日专属优惠，详情请咨询前台。"
  },
  {
    id: "MSG007",
    type: "系统通知",
    recipient: "管理员",
    recipientId: "A10001",
    phone: "133****6789",
    channel: "系统内通知",
    sendTime: "2023-05-03 10:30:15",
    status: "已送达",
    readStatus: "已读",
    readTime: "2023-05-03 10:35:20",
    content: "系统将于今晚22:00-23:00进行维护更新，请提前做好准备。"
  },
  {
    id: "MSG008",
    type: "会员卡余额",
    recipient: "周九",
    recipientId: "M10007",
    phone: "132****0123",
    channel: "APP",
    sendTime: "2023-05-03 14:15:42",
    status: "已送达",
    readStatus: "已读",
    readTime: "2023-05-03 15:20:18",
    content: "尊敬的周九，您的储值卡余额不足100元，为了不影响您的正常使用，请及时充值。"
  },
]

export default function MessageHistoryPage() {
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [selectedMessage, setSelectedMessage] = useState(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [dateRange, setDateRange] = useState({ start: "", end: "" })

  const handleViewDetails = (message) => {
    setSelectedMessage(message)
    setDetailsDialogOpen(true)
  }

  const handleResendMessage = (messageId) => {
    toast({
      title: "消息已重新发送",
      description: `消息ID: ${messageId} 已成功重新发送`,
    })
    setDetailsDialogOpen(false)
  }

  const handleExportData = () => {
    toast({
      title: "数据导出中",
      description: "消息发送记录数据正在导出，请稍候...",
    })

    // 模拟导出完成
    setTimeout(() => {
      toast({
        title: "数据导出完成",
        description: "消息发送记录数据已成功导出",
      })
    }, 2000)
  }

  const filteredMessages = messageHistory.filter(message => {
    // 搜索条件过滤
    const matchesSearch = message.recipient.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          message.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          message.type.toLowerCase().includes(searchQuery.toLowerCase());

    // 日期范围过滤 (简化实现，实际应该使用日期对象比较)
    const matchesDateRange = true; // 这里简化处理，实际应该检查日期范围

    return matchesSearch && matchesDateRange;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">发送记录</h1>
          <p className="text-sm text-muted-foreground mt-1">查看和管理消息发送历史记录</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExportData}>
            <Download className="mr-2 h-4 w-4" />
            导出数据
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 md:flex-row md:items-center">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索接收人、消息ID或类型..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
          <div className="flex items-center gap-2">
            <Label htmlFor="start-date" className="w-20">开始日期:</Label>
            <Input
              id="start-date"
              type="date"
              className="w-40"
              value={dateRange.start}
              onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
            />
          </div>
          <div className="flex items-center gap-2">
            <Label htmlFor="end-date" className="w-20">结束日期:</Label>
            <Input
              id="end-date"
              type="date"
              className="w-40"
              value={dateRange.end}
              onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select defaultValue="all">
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="消息类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="booking">预约通知</SelectItem>
              <SelectItem value="reminder">课程提醒</SelectItem>
              <SelectItem value="expiry">到期提醒</SelectItem>
              <SelectItem value="marketing">营销通知</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all">
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="发送渠道" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部渠道</SelectItem>
              <SelectItem value="sms">短信</SelectItem>
              <SelectItem value="wechat">微信</SelectItem>
              <SelectItem value="app">APP</SelectItem>
              <SelectItem value="system">系统内通知</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all">
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="发送状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="delivered">已送达</SelectItem>
              <SelectItem value="failed">未送达</SelectItem>
              <SelectItem value="read">已读</SelectItem>
              <SelectItem value="unread">未读</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="secondary">
            <Filter className="mr-2 h-4 w-4" />
            筛选
          </Button>
        </div>
      </div>

      <div className="rounded-md border">
        <div className="relative w-full overflow-auto">
          <table className="w-full caption-bottom text-sm">
            <thead className="[&_tr]:border-b">
              <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                <th className="h-12 px-4 text-left align-middle font-medium">消息ID</th>
                <th className="h-12 px-4 text-left align-middle font-medium">消息类型</th>
                <th className="h-12 px-4 text-left align-middle font-medium">接收人</th>
                <th className="h-12 px-4 text-left align-middle font-medium">发送渠道</th>
                <th className="h-12 px-4 text-left align-middle font-medium">发送时间</th>
                <th className="h-12 px-4 text-left align-middle font-medium">状态</th>
                <th className="h-12 px-4 text-left align-middle font-medium">操作</th>
              </tr>
            </thead>
            <tbody className="[&_tr:last-child]:border-0">
              {filteredMessages.map((message) => (
                <tr key={message.id} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                  <td className="p-4 align-middle">{message.id}</td>
                  <td className="p-4 align-middle">{message.type}</td>
                  <td className="p-4 align-middle">{message.recipient}</td>
                  <td className="p-4 align-middle">
                    <Badge variant="outline">
                      {message.channel === "短信" && <Mail className="h-3 w-3 mr-1" />}
                      {message.channel === "微信" && <MessageSquare className="h-3 w-3 mr-1" />}
                      {message.channel === "APP" && <Smartphone className="h-3 w-3 mr-1" />}
                      {message.channel === "系统内通知" && <Bell className="h-3 w-3 mr-1" />}
                      {message.channel}
                    </Badge>
                  </td>
                  <td className="p-4 align-middle">{message.sendTime}</td>
                  <td className="p-4 align-middle">
                    <Badge variant={message.status === "已送达" ? "success" : "destructive"}>
                      {message.status === "已送达" ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1" />
                      )}
                      {message.status}
                    </Badge>
                    {message.status === "已送达" && message.readStatus !== "未知" && (
                      <Badge variant={message.readStatus === "已读" ? "outline" : "secondary"} className="ml-1">
                        {message.readStatus}
                      </Badge>
                    )}
                  </td>
                  <td className="p-4 align-middle">
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm" onClick={() => handleViewDetails(message)}>
                        详情
                      </Button>
                      {message.status !== "已送达" && (
                        <Button variant="ghost" size="sm" onClick={() => handleResendMessage(message.id)}>
                          重发
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          共 {filteredMessages.length} 条记录
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button variant="outline" size="sm" className="px-4">
            1
          </Button>
          <Button variant="outline" size="sm" disabled>
            下一页
          </Button>
        </div>
      </div>

      {/* 消息详情对话框 */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>消息详情</DialogTitle>
            <DialogDescription>
              查看消息的详细信息和发送结果
            </DialogDescription>
          </DialogHeader>
          {selectedMessage && (
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">基本信息</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">消息ID:</div>
                  <div>{selectedMessage.id}</div>
                  <div className="text-muted-foreground">消息类型:</div>
                  <div>{selectedMessage.type}</div>
                  <div className="text-muted-foreground">发送渠道:</div>
                  <div>{selectedMessage.channel}</div>
                  <div className="text-muted-foreground">发送时间:</div>
                  <div>{selectedMessage.sendTime}</div>
                  <div className="text-muted-foreground">状态:</div>
                  <div>{selectedMessage.status}</div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="text-sm font-medium">接收人信息</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">接收人:</div>
                  <div>{selectedMessage.recipient}</div>
                  <div className="text-muted-foreground">手机号:</div>
                  <div>{selectedMessage.phone}</div>
                  <div className="text-muted-foreground">会员ID:</div>
                  <div>{selectedMessage.recipientId}</div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="text-sm font-medium">消息内容</h3>
                <div className="rounded-md border p-3 text-sm">
                  {selectedMessage.content}
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="text-sm font-medium">发送结果</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="text-muted-foreground">状态码:</div>
                  <div>{selectedMessage.status === "已送达" ? "200" : "500"}</div>
                  <div className="text-muted-foreground">结果描述:</div>
                  <div>{selectedMessage.status === "已送达" ? "发送成功" : "发送失败"}</div>
                  {selectedMessage.status === "已送达" && (
                    <>
                      <div className="text-muted-foreground">送达时间:</div>
                      <div>{selectedMessage.sendTime}</div>
                      <div className="text-muted-foreground">阅读状态:</div>
                      <div>{selectedMessage.readStatus}</div>
                      {selectedMessage.readStatus === "已读" && (
                        <>
                          <div className="text-muted-foreground">阅读时间:</div>
                          <div>{selectedMessage.readTime}</div>
                        </>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setDetailsDialogOpen(false)}>
              关闭
            </Button>
            {selectedMessage && selectedMessage.status !== "已送达" && (
              <Button onClick={() => handleResendMessage(selectedMessage.id)}>
                重新发送
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
