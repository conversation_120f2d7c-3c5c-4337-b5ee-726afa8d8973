"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Calendar, Save, Plus, Trash, Clock, ArrowLeft, Settings, FileText, Users, Layers, Tag, Award } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { useRouter } from "next/navigation"
import { BookingRulesSettings } from "@/components/courses/booking-rules-settings"

// 模拟会员等级数据
const memberLevels = [
  { id: "L001", name: "普通会员", color: "#607D8B" },
  { id: "L002", name: "银卡会员", color: "#9E9E9E" },
  { id: "L003", name: "金卡会员", color: "#FFC107" },
  { id: "L004", name: "钻石会员", color: "#00BCD4" },
  { id: "L005", name: "黑卡会员", color: "#000000" },
]

// 模拟会员标签数据
const memberTags = [
  { id: "T001", name: "新客户", color: "#4CAF50" },
  { id: "T002", name: "老客户", color: "#2196F3" },
  { id: "T003", name: "VIP", color: "#FF9800" },
  { id: "T004", name: "教练", color: "#9C27B0" },
  { id: "T005", name: "员工", color: "#F44336" },
  { id: "T006", name: "股东", color: "#607D8B" },
]

export default function MemberBookingRulesPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("identity")
  const [selectedLevel, setSelectedLevel] = useState("")
  const [selectedTag, setSelectedTag] = useState("")
  
  // 保存设置
  const handleSave = (settings: any) => {
    console.log("保存会员预约规则设置:", settings)
    toast({
      title: "规则已保存",
      description: "会员预约规则已成功保存",
    })
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.push("/booking-rules")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">会员预约规则</h1>
            <p className="text-muted-foreground">
              设置会员相关的预约特权和限制
            </p>
          </div>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Layers className="h-5 w-5 mr-2 text-primary" />
            会员预约规则层级
          </CardTitle>
          <CardDescription>
            会员预约规则分为三个层级：身份规则、等级规则和标签规则
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm">
              规则按照从具体到一般的顺序应用，更具体的规则会覆盖更一般的规则：
            </p>
            <ol className="list-decimal list-inside space-y-1 text-sm ml-4">
              <li>会员标签规则（最高优先级）</li>
              <li>会员等级规则</li>
              <li>会员身份规则（最低优先级）</li>
            </ol>
            <p className="text-sm">
              例如，如果一个会员同时拥有标签规则和等级规则，那么标签规则会优先应用。
            </p>
            <p className="text-sm mt-4">
              <strong>注意：</strong> 会员规则的优先级高于会员卡规则，但低于课程规则。当规则冲突时，课程规则优先，其次是会员规则，最后是会员卡规则。
            </p>
          </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>选择规则层级</CardTitle>
            <CardDescription>
              选择要设置的预约规则层级
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="identity">身份</TabsTrigger>
                  <TabsTrigger value="level">等级</TabsTrigger>
                  <TabsTrigger value="tag">标签</TabsTrigger>
                </TabsList>
              </Tabs>
              
              {activeTab === "level" && (
                <div className="space-y-2">
                  <Label htmlFor="level-select">会员等级</Label>
                  <Select value={selectedLevel} onValueChange={setSelectedLevel}>
                    <SelectTrigger id="level-select">
                      <SelectValue placeholder="选择会员等级" />
                    </SelectTrigger>
                    <SelectContent>
                      {memberLevels.map((level) => (
                        <SelectItem key={level.id} value={level.id}>
                          <div className="flex items-center">
                            <div
                              className="w-3 h-3 rounded-full mr-2"
                              style={{ backgroundColor: level.color }}
                            ></div>
                            {level.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              {activeTab === "tag" && (
                <div className="space-y-2">
                  <Label htmlFor="tag-select">会员标签</Label>
                  <Select value={selectedTag} onValueChange={setSelectedTag}>
                    <SelectTrigger id="tag-select">
                      <SelectValue placeholder="选择会员标签" />
                    </SelectTrigger>
                    <SelectContent>
                      {memberTags.map((tag) => (
                        <SelectItem key={tag.id} value={tag.id}>
                          <div className="flex items-center">
                            <div
                              className="w-3 h-3 rounded-full mr-2"
                              style={{ backgroundColor: tag.color }}
                            ></div>
                            {tag.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        <div className="md:col-span-3">
          {activeTab === "identity" ? (
            <BookingRulesSettings 
              level="global"
              onSave={handleSave}
            />
          ) : activeTab === "level" && !selectedLevel ? (
            <Card>
              <CardContent className="flex items-center justify-center min-h-[400px]">
                <div className="text-center text-muted-foreground">
                  <p>请先选择一个会员等级</p>
                </div>
              </CardContent>
            </Card>
          ) : activeTab === "level" && selectedLevel ? (
            <BookingRulesSettings 
              level="global"
              onSave={handleSave}
            />
          ) : activeTab === "tag" && !selectedTag ? (
            <Card>
              <CardContent className="flex items-center justify-center min-h-[400px]">
                <div className="text-center text-muted-foreground">
                  <p>请先选择一个会员标签</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <BookingRulesSettings 
              level="global"
              onSave={handleSave}
            />
          )}
        </div>
      </div>
      
      <div className="flex justify-between">
        <Button variant="outline" onClick={() => router.push("/booking-rules")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回规则管理
        </Button>
        
        <div className="space-x-2">
          <Button variant="outline" onClick={() => router.push("/members/levels")}>
            <Award className="mr-2 h-4 w-4" />
            会员等级管理
          </Button>
          <Button variant="outline" onClick={() => router.push("/members/tags")}>
            <Tag className="mr-2 h-4 w-4" />
            会员标签管理
          </Button>
        </div>
      </div>
    </div>
  )
}
