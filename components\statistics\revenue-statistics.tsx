"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  Legend,
  LineChart,
  Line,
} from "recharts"

const monthlyRevenueData = [
  { month: "1月", value: 32000 },
  { month: "2月", value: 35000 },
  { month: "3月", value: 38000 },
  { month: "4月", value: 42000 },
  { month: "5月", value: 48000 },
  { month: "6月", value: 52000 },
  { month: "7月", value: 55000 },
  { month: "8月", value: 59000 },
  { month: "9月", value: 62000 },
  { month: "10月", value: 65000 },
  { month: "11月", value: 68000 },
  { month: "12月", value: 72000 },
]

const revenueSourceData = [
  { name: "会员卡", value: 60, color: "#4285F4" },
  { name: "单次课程", value: 15, color: "#34A853" },
  { name: "课程套餐", value: 20, color: "#FBBC05" },
  { name: "商品销售", value: 5, color: "#EA4335" },
]

const memberCardTypeData = [
  { name: "年卡", value: 40, color: "#4285F4" },
  { name: "季卡", value: 25, color: "#34A853" },
  { name: "月卡", value: 20, color: "#FBBC05" },
  { name: "次卡", value: 15, color: "#EA4335" },
]

const courseRevenueData = [
  { name: "基础瑜伽", value: 25000 },
  { name: "高级瑜伽", value: 18000 },
  { name: "阴瑜伽", value: 12000 },
  { name: "孕产瑜伽", value: 8000 },
  { name: "空中瑜伽", value: 9000 },
]

export function RevenueStatistics() {
  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥72,000</div>
            <p className="text-xs text-muted-foreground">较上月增长 5.9%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">会员卡收入</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥43,200</div>
            <p className="text-xs text-muted-foreground">较上月增长 4.2%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">课程收入</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥25,200</div>
            <p className="text-xs text-muted-foreground">较上月增长 7.5%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">人均消费</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥580</div>
            <p className="text-xs text-muted-foreground">较上月增长 2.5%</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-1">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>月度收入趋势</CardTitle>
            <CardDescription>过去12个月收入变化</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={monthlyRevenueData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`¥${value}`, "收入"]} />
                  <Line type="monotone" dataKey="value" stroke="#4285F4" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>收入来源分布</CardTitle>
            <CardDescription>不同收入来源占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={revenueSourceData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {revenueSourceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value}%`, "占比"]} />
                  <Legend layout="vertical" verticalAlign="middle" align="right" />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>会员卡类型收入</CardTitle>
            <CardDescription>不同会员卡类型收入占比</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={memberCardTypeData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {memberCardTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value}%`, "占比"]} />
                  <Legend layout="vertical" verticalAlign="middle" align="right" />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>课程收入分布</CardTitle>
          <CardDescription>不同类型课程收入情况</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={courseRevenueData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`¥${value}`, "收入"]} />
                <Bar dataKey="value" fill="#4285F4" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

