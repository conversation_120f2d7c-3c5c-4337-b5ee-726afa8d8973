"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { format } from "date-fns"
import { Calendar as CalendarIcon, Check, ChevronsUpDown, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/hooks/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

const formSchema = z.object({
  shareholderId: z.string({
    required_error: "请选择股东",
  }),
  period: z.string({
    required_error: "请选择分红周期",
  }),
  amount: z.string().min(1, {
    message: "请输入分红金额",
  }),
  referrals: z.string().min(1, {
    message: "请输入引流客户数",
  }),
  referralAmount: z.string().optional(),
  dividendRate: z.string().min(1, {
    message: "请输入分红比例",
  }),
  notes: z.string().optional(),
})

// 模拟股东数据
const shareholders = [
  {
    id: "101",
    name: "张三",
    type: "消费型股东",
    avatar: "/avatars/01.png",
  },
  {
    id: "102",
    name: "李四",
    type: "投资型股东",
    avatar: "/avatars/02.png",
  },
  {
    id: "103",
    name: "王五",
    type: "资源型股东",
    avatar: "/avatars/03.png",
  },
  {
    id: "104",
    name: "赵六",
    type: "员工型股东",
    avatar: "/avatars/04.png",
  },
  {
    id: "105",
    name: "美丽美容院",
    type: "联盟型股东",
    avatar: "/avatars/05.png",
  },
]

interface AddDividendDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddDividendDialog({ open, onOpenChange }: AddDividendDialogProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedShareholder, setSelectedShareholder] = useState<any>(null)
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      period: format(new Date(), "yyyy年M月"),
      referrals: "0",
      referralAmount: "0",
    },
  })

  // 过滤股东
  const filteredShareholders = shareholders.filter((shareholder) => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        shareholder.name.toLowerCase().includes(query) ||
        shareholder.type.toLowerCase().includes(query)
      )
    }
    return true
  })

  // 选择股东
  const handleSelectShareholder = (shareholder: any) => {
    setSelectedShareholder(shareholder)
    form.setValue("shareholderId", shareholder.id)
  }

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values)
    toast({
      title: "添加成功",
      description: `分红记录已成功添加`,
    })
    form.reset()
    setSelectedShareholder(null)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>添加分红记录</DialogTitle>
          <DialogDescription>
            为股东添加新的分红记录
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="shareholderId"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>选择股东</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          className={cn(
                            "w-full justify-between",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {selectedShareholder ? (
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={selectedShareholder.avatar} alt={selectedShareholder.name} />
                                <AvatarFallback>{selectedShareholder.name[0]}</AvatarFallback>
                              </Avatar>
                              <span>{selectedShareholder.name}</span>
                              <Badge className="ml-2">{selectedShareholder.type}</Badge>
                            </div>
                          ) : (
                            "选择股东"
                          )}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0">
                      <Command>
                        <CommandInput 
                          placeholder="搜索股东..." 
                          onValueChange={setSearchQuery}
                          className="h-9"
                        />
                        <CommandEmpty>未找到股东</CommandEmpty>
                        <CommandGroup className="max-h-[300px] overflow-auto">
                          {filteredShareholders.map((shareholder) => (
                            <CommandItem
                              key={shareholder.id}
                              value={shareholder.id}
                              onSelect={() => handleSelectShareholder(shareholder)}
                            >
                              <div className="flex items-center gap-2 w-full">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={shareholder.avatar} alt={shareholder.name} />
                                  <AvatarFallback>{shareholder.name[0]}</AvatarFallback>
                                </Avatar>
                                <span>{shareholder.name}</span>
                                <Badge className="ml-auto">{shareholder.type}</Badge>
                              </div>
                              <Check
                                className={cn(
                                  "ml-auto h-4 w-4",
                                  selectedShareholder?.id === shareholder.id
                                    ? "opacity-100"
                                    : "opacity-0"
                                )}
                              />
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="period"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>分红周期</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="选择分红周期" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="2023年5月">2023年5月</SelectItem>
                        <SelectItem value="2023年4月">2023年4月</SelectItem>
                        <SelectItem value="2023年3月">2023年3月</SelectItem>
                        <SelectItem value="2023年2月">2023年2月</SelectItem>
                        <SelectItem value="2023年1月">2023年1月</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>分红金额</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入分红金额" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="referrals"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>引流客户数</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入引流客户数" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="referralAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>客户消费金额</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入客户消费金额" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="dividendRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>分红比例</FormLabel>
                  <FormControl>
                    <Input placeholder="例如：5%或50元/人" {...field} />
                  </FormControl>
                  <FormDescription>
                    根据股东类型的分红规则填写，例如：消费型股东为5%，资源型股东为50元/人
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请输入备注信息"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button type="submit">保存</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
