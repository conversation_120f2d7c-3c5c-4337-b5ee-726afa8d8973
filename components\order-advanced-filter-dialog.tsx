"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@/components/date-picker"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"

interface OrderAdvancedFilterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onApplyFilters: (filters: any) => void
}

export function OrderAdvancedFilterDialog({ open, onOpenChange, onApplyFilters }: OrderAdvancedFilterDialogProps) {
  const [filters, setFilters] = useState({
    dateRange: {
      from: null,
      to: null,
    },
    orderStatus: [],
    paymentMethods: [],
    orderTypes: [],
    priceRange: {
      min: "",
      max: "",
    },
    venues: [],
    staff: [],
  })

  const [activeFilters, setActiveFilters] = useState<string[]>([])

  const handleAddFilter = (filterName: string, value: any) => {
    if (!activeFilters.includes(filterName)) {
      setActiveFilters([...activeFilters, filterName])
    }
  }

  const handleRemoveFilter = (filterName: string) => {
    setActiveFilters(activeFilters.filter((f) => f !== filterName))
  }

  const handleApply = () => {
    onApplyFilters(filters)
    onOpenChange(false)
  }

  const handleReset = () => {
    setFilters({
      dateRange: {
        from: null,
        to: null,
      },
      orderStatus: [],
      paymentMethods: [],
      orderTypes: [],
      priceRange: {
        min: "",
        max: "",
      },
      venues: [],
      staff: [],
    })
    setActiveFilters([])
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
          <DialogDescription>设置多个条件筛选订单</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {activeFilters.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {activeFilters.map((filter) => (
                <Badge key={filter} variant="secondary" className="px-2 py-1">
                  {filter === "dateRange" && "日期范围"}
                  {filter === "orderStatus" && "订单状态"}
                  {filter === "paymentMethods" && "支付方式"}
                  {filter === "orderTypes" && "订单类型"}
                  {filter === "priceRange" && "价格范围"}
                  {filter === "venues" && "门店"}
                  {filter === "staff" && "操作员"}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 ml-1 p-0"
                    onClick={() => handleRemoveFilter(filter)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
              <Button variant="ghost" size="sm" onClick={() => setActiveFilters([])}>
                清除全部
              </Button>
            </div>
          )}

          <div className="grid grid-cols-1 gap-4">
            {!activeFilters.includes("dateRange") && (
              <Button variant="outline" className="justify-start" onClick={() => handleAddFilter("dateRange", {})}>
                + 添加日期范围
              </Button>
            )}

            {activeFilters.includes("dateRange") && (
              <div className="space-y-2">
                <Label>日期范围</Label>
                <div className="flex items-center gap-2">
                  <DatePicker
                    placeholder="开始日期"
                    selected={filters.dateRange.from}
                    onSelect={(date) => setFilters({ ...filters, dateRange: { ...filters.dateRange, from: date } })}
                  />
                  <span>至</span>
                  <DatePicker
                    placeholder="结束日期"
                    selected={filters.dateRange.to}
                    onSelect={(date) => setFilters({ ...filters, dateRange: { ...filters.dateRange, to: date } })}
                  />
                </div>
              </div>
            )}

            {!activeFilters.includes("orderStatus") && (
              <Button variant="outline" className="justify-start" onClick={() => handleAddFilter("orderStatus", [])}>
                + 添加订单状态
              </Button>
            )}

            {activeFilters.includes("orderStatus") && (
              <div className="space-y-2">
                <Label>订单状态</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-paid"
                      checked={filters.orderStatus.includes("paid")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, orderStatus: [...filters.orderStatus, "paid"] })
                        } else {
                          setFilters({ ...filters, orderStatus: filters.orderStatus.filter((s) => s !== "paid") })
                        }
                      }}
                    />
                    <label
                      htmlFor="status-paid"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      已支付
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-pending"
                      checked={filters.orderStatus.includes("pending")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, orderStatus: [...filters.orderStatus, "pending"] })
                        } else {
                          setFilters({ ...filters, orderStatus: filters.orderStatus.filter((s) => s !== "pending") })
                        }
                      }}
                    />
                    <label
                      htmlFor="status-pending"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      待支付
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-refunded"
                      checked={filters.orderStatus.includes("refunded")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, orderStatus: [...filters.orderStatus, "refunded"] })
                        } else {
                          setFilters({ ...filters, orderStatus: filters.orderStatus.filter((s) => s !== "refunded") })
                        }
                      }}
                    />
                    <label
                      htmlFor="status-refunded"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      已退款
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="status-cancelled"
                      checked={filters.orderStatus.includes("cancelled")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, orderStatus: [...filters.orderStatus, "cancelled"] })
                        } else {
                          setFilters({ ...filters, orderStatus: filters.orderStatus.filter((s) => s !== "cancelled") })
                        }
                      }}
                    />
                    <label
                      htmlFor="status-cancelled"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      已取消
                    </label>
                  </div>
                </div>
              </div>
            )}

            {!activeFilters.includes("paymentMethods") && (
              <Button variant="outline" className="justify-start" onClick={() => handleAddFilter("paymentMethods", [])}>
                + 添加支付方式
              </Button>
            )}

            {activeFilters.includes("paymentMethods") && (
              <div className="space-y-2">
                <Label>支付方式</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="payment-wechat"
                      checked={filters.paymentMethods.includes("wechat")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, paymentMethods: [...filters.paymentMethods, "wechat"] })
                        } else {
                          setFilters({
                            ...filters,
                            paymentMethods: filters.paymentMethods.filter((p) => p !== "wechat"),
                          })
                        }
                      }}
                    />
                    <label
                      htmlFor="payment-wechat"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      微信支付
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="payment-alipay"
                      checked={filters.paymentMethods.includes("alipay")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, paymentMethods: [...filters.paymentMethods, "alipay"] })
                        } else {
                          setFilters({
                            ...filters,
                            paymentMethods: filters.paymentMethods.filter((p) => p !== "alipay"),
                          })
                        }
                      }}
                    />
                    <label
                      htmlFor="payment-alipay"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      支付宝
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="payment-bank"
                      checked={filters.paymentMethods.includes("bank")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, paymentMethods: [...filters.paymentMethods, "bank"] })
                        } else {
                          setFilters({ ...filters, paymentMethods: filters.paymentMethods.filter((p) => p !== "bank") })
                        }
                      }}
                    />
                    <label
                      htmlFor="payment-bank"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      银行卡
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="payment-cash"
                      checked={filters.paymentMethods.includes("cash")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, paymentMethods: [...filters.paymentMethods, "cash"] })
                        } else {
                          setFilters({ ...filters, paymentMethods: filters.paymentMethods.filter((p) => p !== "cash") })
                        }
                      }}
                    />
                    <label
                      htmlFor="payment-cash"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      现金
                    </label>
                  </div>
                </div>
              </div>
            )}

            {!activeFilters.includes("orderTypes") && (
              <Button variant="outline" className="justify-start" onClick={() => handleAddFilter("orderTypes", [])}>
                + 添加订单类型
              </Button>
            )}

            {activeFilters.includes("orderTypes") && (
              <div className="space-y-2">
                <Label>订单类型</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="type-membership"
                      checked={filters.orderTypes.includes("membership")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, orderTypes: [...filters.orderTypes, "membership"] })
                        } else {
                          setFilters({ ...filters, orderTypes: filters.orderTypes.filter((t) => t !== "membership") })
                        }
                      }}
                    />
                    <label
                      htmlFor="type-membership"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      会员卡
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="type-course"
                      checked={filters.orderTypes.includes("course")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, orderTypes: [...filters.orderTypes, "course"] })
                        } else {
                          setFilters({ ...filters, orderTypes: filters.orderTypes.filter((t) => t !== "course") })
                        }
                      }}
                    />
                    <label
                      htmlFor="type-course"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      单次课程
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="type-package"
                      checked={filters.orderTypes.includes("package")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, orderTypes: [...filters.orderTypes, "package"] })
                        } else {
                          setFilters({ ...filters, orderTypes: filters.orderTypes.filter((t) => t !== "package") })
                        }
                      }}
                    />
                    <label
                      htmlFor="type-package"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      课程套餐
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="type-product"
                      checked={filters.orderTypes.includes("product")}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setFilters({ ...filters, orderTypes: [...filters.orderTypes, "product"] })
                        } else {
                          setFilters({ ...filters, orderTypes: filters.orderTypes.filter((t) => t !== "product") })
                        }
                      }}
                    />
                    <label
                      htmlFor="type-product"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      商品
                    </label>
                  </div>
                </div>
              </div>
            )}

            {!activeFilters.includes("priceRange") && (
              <Button variant="outline" className="justify-start" onClick={() => handleAddFilter("priceRange", {})}>
                + 添加价格范围
              </Button>
            )}

            {activeFilters.includes("priceRange") && (
              <div className="space-y-2">
                <Label>价格范围</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    placeholder="最低价格"
                    value={filters.priceRange.min}
                    onChange={(e) =>
                      setFilters({ ...filters, priceRange: { ...filters.priceRange, min: e.target.value } })
                    }
                  />
                  <span>至</span>
                  <Input
                    type="number"
                    placeholder="最高价格"
                    value={filters.priceRange.max}
                    onChange={(e) =>
                      setFilters({ ...filters, priceRange: { ...filters.priceRange, max: e.target.value } })
                    }
                  />
                </div>
              </div>
            )}

            {!activeFilters.includes("venues") && (
              <Button variant="outline" className="justify-start" onClick={() => handleAddFilter("venues", [])}>
                + 添加门店
              </Button>
            )}

            {activeFilters.includes("venues") && (
              <div className="space-y-2">
                <Label>门店</Label>
                <Select
                  value={filters.venues.length > 0 ? filters.venues[0] : ""}
                  onValueChange={(value) => setFilters({ ...filters, venues: [value] })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择门店" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chaoyang">朝阳店</SelectItem>
                    <SelectItem value="haidian">海淀店</SelectItem>
                    <SelectItem value="dongcheng">东城店</SelectItem>
                    <SelectItem value="xicheng">西城店</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {!activeFilters.includes("staff") && (
              <Button variant="outline" className="justify-start" onClick={() => handleAddFilter("staff", [])}>
                + 添加操作员
              </Button>
            )}

            {activeFilters.includes("staff") && (
              <div className="space-y-2">
                <Label>操作员</Label>
                <Select
                  value={filters.staff.length > 0 ? filters.staff[0] : ""}
                  onValueChange={(value) => setFilters({ ...filters, staff: [value] })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择操作员" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="lisi">李四</SelectItem>
                    <SelectItem value="wangwu">王五</SelectItem>
                    <SelectItem value="zhaoliu">赵六</SelectItem>
                    <SelectItem value="admin">管理员</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>

        <Separator />

        <DialogFooter>
          <Button variant="outline" onClick={handleReset}>
            重置
          </Button>
          <Button onClick={handleApply}>应用筛选</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

