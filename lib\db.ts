import { PrismaClient } from '@/lib/generated/prisma';

// 防止开发环境下热重载创建多个Prisma实例
declare global {
  var prisma: PrismaClient | undefined;
}

// 使用全局变量来缓存Prisma实例
export const prisma = global.prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

// 在开发环境下将prisma实例保存到全局变量中
if (process.env.NODE_ENV !== 'production') {
  global.prisma = prisma;
} 