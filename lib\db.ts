import { PrismaClient } from '@prisma/client'

// PrismaClient是一个可以被实例化的类
// 由于Next.js的开发服务器会在开发过程中多次调用这个文件，
// 我们需要确保不会创建太多连接
// 因此，我们将prisma客户端附加到global对象上

const globalForPrisma = global as unknown as { prisma: PrismaClient }

// 检查global对象上是否已有prisma实例，如果有则重用，否则创建新实例
export const prisma = globalForPrisma.prisma || new PrismaClient()

// 只有在非生产环境下才将prisma客户端附加到global对象上
if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma 