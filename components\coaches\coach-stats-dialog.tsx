import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface CoachStatsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CoachStatsDialog({ open, onOpenChange }: CoachStatsDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>教练统计分析</DialogTitle>
          <DialogDescription>查看教练团队的统计数据和分析报告</DialogDescription>
        </DialogHeader>

        <div className="flex justify-end">
          <Select defaultValue="last30days">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last7days">最近7天</SelectItem>
              <SelectItem value="last30days">最近30天</SelectItem>
              <SelectItem value="last90days">最近90天</SelectItem>
              <SelectItem value="lastYear">最近一年</SelectItem>
              <SelectItem value="custom">自定义范围</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Tabs defaultValue="overview">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">总体概览</TabsTrigger>
            <TabsTrigger value="performance">业绩分析</TabsTrigger>
            <TabsTrigger value="attendance">出勤分析</TabsTrigger>
            <TabsTrigger value="reviews">评价分析</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>教练总数</CardDescription>
                  <CardTitle className="text-2xl">12</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+2</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>在职教练</CardDescription>
                  <CardTitle className="text-2xl">10</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+1</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>平均评分</CardDescription>
                  <CardTitle className="text-2xl">4.7</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+0.2</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>总课程数</CardDescription>
                  <CardTitle className="text-2xl">245</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+28</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>总学员数</CardDescription>
                  <CardTitle className="text-2xl">876</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+45</span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardDescription>平均出勤率</CardDescription>
                  <CardTitle className="text-2xl">96%</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    较上月 <span className="text-green-500">+2%</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>教练状态分布</CardTitle>
                <CardDescription>各状态教练数量分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">教练状态分布图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>教练专长分布</CardTitle>
                <CardDescription>各专长教练数量分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">教练专长分布图表</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>教练业绩排名</CardTitle>
                <CardDescription>按课程数量和学员数量排名</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">教练业绩排名图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>课程类型分布</CardTitle>
                <CardDescription>各类型课程数��分布</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">课程类型分布图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>教练收入分析</CardTitle>
                <CardDescription>教练收入来源和趋势分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">教练收入分析图表</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="attendance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>教练出勤率</CardTitle>
                <CardDescription>各教练出勤率统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">教练出勤率图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>请假分析</CardTitle>
                <CardDescription>教练请假原因和时长分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">请假分析图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>工作时长分析</CardTitle>
                <CardDescription>教练工作时长统计和分析</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">工作时长分析图表</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reviews" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>评分分布</CardTitle>
                <CardDescription>各评分等级分布情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">评分分布图表</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>评价关键词</CardTitle>
                <CardDescription>学员评价中的高频关键词</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">评价关键词云图</div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>评分趋势</CardTitle>
                <CardDescription>教练评分随时间变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] w-full rounded-md bg-muted p-4 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">评分趋势图表</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

