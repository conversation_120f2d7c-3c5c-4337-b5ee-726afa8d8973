export interface Employee {
  id: number;
  tenant_id: number;
  employee_no: string;
  real_name: string;
  phone: string;
  email?: string;
  role_id?: number;
  avatar_url?: string;
  status: number; // 1-在职，0-休假，-1-离职
  created_at: string;
  updated_at: string;
}

export interface EmployeeQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: number;
}

export interface EmployeeResponse {
  data: Employee[];
  total: number;
} 