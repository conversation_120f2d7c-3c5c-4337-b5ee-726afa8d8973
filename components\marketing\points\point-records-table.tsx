"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar, ChevronLeft, ChevronRight, Download, Filter, Search } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

export function PointRecordsTable() {
  const [date, setDate] = useState<Date>()

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="relative w-[300px]">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="搜索会员姓名或手机号..." className="pl-8" />
          </div>

          <Select defaultValue="all">
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="记录类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部记录</SelectItem>
              <SelectItem value="earn">获取积分</SelectItem>
              <SelectItem value="spend">使用积分</SelectItem>
              <SelectItem value="adjust">调整积分</SelectItem>
              <SelectItem value="expire">积分过期</SelectItem>
            </SelectContent>
          </Select>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn("w-[180px] justify-start text-left font-normal", !date && "text-muted-foreground")}
              >
                <Calendar className="mr-2 h-4 w-4" />
                {date ? format(date, "yyyy年MM月dd日") : "选择日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <CalendarComponent mode="single" selected={date} onSelect={setDate} />
            </PopoverContent>
          </Popover>

          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          导出记录
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>会员信息</TableHead>
              <TableHead>变动积分</TableHead>
              <TableHead>变动类型</TableHead>
              <TableHead>关联规则</TableHead>
              <TableHead>变动时间</TableHead>
              <TableHead>操作人</TableHead>
              <TableHead>备注</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {pointRecords.map((record) => (
              <TableRow key={record.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={record.memberAvatar} alt={record.memberName} />
                      <AvatarFallback>{record.memberName.substring(0, 1)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{record.memberName}</div>
                      <div className="text-xs text-muted-foreground">{record.memberPhone}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className={record.type === "earn" ? "text-green-600" : "text-red-600"}>
                  {record.type === "earn" ? "+" : "-"}
                  {record.points}
                </TableCell>
                <TableCell>
                  <Badge variant={getTypeBadgeVariant(record.type)}>{getTypeName(record.type)}</Badge>
                </TableCell>
                <TableCell>{record.rule}</TableCell>
                <TableCell>{record.date}</TableCell>
                <TableCell>{record.operator}</TableCell>
                <TableCell className="max-w-[200px] truncate">{record.note}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">显示 1 至 10 条，共 42 条记录</div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" disabled>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="sm" className="h-8 w-8">
            1
          </Button>
          <Button variant="outline" size="sm" className="h-8 w-8">
            2
          </Button>
          <Button variant="outline" size="sm" className="h-8 w-8">
            3
          </Button>
          <Button variant="outline" size="sm" className="h-8 w-8">
            4
          </Button>
          <Button variant="outline" size="sm" className="h-8 w-8">
            5
          </Button>
          <Button variant="outline" size="icon">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// 模拟数据
const pointRecords = [
  {
    id: 1,
    memberName: "张小明",
    memberPhone: "138****1234",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 100,
    type: "earn",
    rule: "课程购买",
    date: "2023-04-22 14:30",
    operator: "系统",
    note: "购买瑜伽月卡获得积分",
  },
  {
    id: 2,
    memberName: "李华",
    memberPhone: "139****5678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 10,
    type: "earn",
    rule: "课程签到",
    date: "2023-04-22 09:15",
    operator: "系统",
    note: "瑜伽课程签到获得积分",
  },
  {
    id: 3,
    memberName: "王芳",
    memberPhone: "137****9012",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 500,
    type: "spend",
    rule: "积分兑换",
    date: "2023-04-21 16:45",
    operator: "系统",
    note: "兑换瑜伽单次体验课",
  },
  {
    id: 4,
    memberName: "赵敏",
    memberPhone: "136****3456",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 200,
    type: "earn",
    rule: "生日特权",
    date: "2023-04-21 10:00",
    operator: "系统",
    note: "生日当月获得积分奖励",
  },
  {
    id: 5,
    memberName: "刘伟",
    memberPhone: "135****7890",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 1500,
    type: "spend",
    rule: "积分兑换",
    date: "2023-04-20 15:30",
    operator: "系统",
    note: "兑换会员月卡8折券",
  },
  {
    id: 6,
    memberName: "陈静",
    memberPhone: "134****2345",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 100,
    type: "earn",
    rule: "会员推荐",
    date: "2023-04-20 11:20",
    operator: "系统",
    note: "推荐新会员获得积分",
  },
  {
    id: 7,
    memberName: "张小明",
    memberPhone: "138****1234",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 50,
    type: "adjust",
    rule: "积分调整",
    date: "2023-04-19 16:00",
    operator: "管理员",
    note: "客服补偿积分",
  },
  {
    id: 8,
    memberName: "王芳",
    memberPhone: "137****9012",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 30,
    type: "expire",
    rule: "积分过期",
    date: "2023-04-19 00:00",
    operator: "系统",
    note: "积分到期自动扣除",
  },
  {
    id: 9,
    memberName: "李华",
    memberPhone: "139****5678",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 2000,
    type: "spend",
    rule: "积分兑换",
    date: "2023-04-18 14:15",
    operator: "系统",
    note: "兑换高级瑜伽垫",
  },
  {
    id: 10,
    memberName: "赵敏",
    memberPhone: "136****3456",
    memberAvatar: "/placeholder.svg?height=40&width=40",
    points: 10,
    type: "earn",
    rule: "课程签到",
    date: "2023-04-18 10:30",
    operator: "系统",
    note: "普拉提课程签到获得积分",
  },
]

// 辅助函数
function getTypeBadgeVariant(type: string) {
  switch (type) {
    case "earn":
      return "success"
    case "spend":
      return "default"
    case "adjust":
      return "outline"
    case "expire":
      return "destructive"
    default:
      return "default"
  }
}

function getTypeName(type: string) {
  switch (type) {
    case "earn":
      return "获取"
    case "spend":
      return "使用"
    case "adjust":
      return "调整"
    case "expire":
      return "过期"
    default:
      return "其他"
  }
}

