"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Calendar, Save, Plus, Trash, Clock, ArrowLeft, Settings, FileText, BookOpen, Layers } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { useRouter } from "next/navigation"
import { BookingRulesSettings } from "@/components/courses/booking-rules-settings"

// 模拟课程表数据
const schedules = [
  { id: "S001", name: "周一哈他瑜伽 10:00-11:30", courseId: "C001", instructor: "张教练", color: "#4CAF50" },
  { id: "S002", name: "周二阴瑜伽 14:00-15:30", courseId: "C002", instructor: "李教练", color: "#2196F3" },
  { id: "S003", name: "周三流瑜伽 19:00-20:30", courseId: "C003", instructor: "王教练", color: "#FF9800" },
  { id: "S004", name: "周四私教一对一 (可选时段)", courseId: "C004", instructor: "赵教练", color: "#9C27B0" },
  { id: "S005", name: "周六普拉提 16:00-17:30", courseId: "C005", instructor: "刘教练", color: "#F44336" },
]

export default function ScheduleBookingRulesPage() {
  const router = useRouter()
  const [selectedSchedule, setSelectedSchedule] = useState("")
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date())
  
  // 保存设置
  const handleSave = (settings: any) => {
    console.log("保存课程表预约规则设置:", settings)
    toast({
      title: "规则已保存",
      description: "课程表预约规则已成功保存",
    })
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.push("/booking-rules/courses")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">课程表预约规则</h1>
            <p className="text-muted-foreground">
              设置特定课程表的预约规则
            </p>
          </div>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Layers className="h-5 w-5 mr-2 text-primary" />
            课程表预约规则说明
          </CardTitle>
          <CardDescription>
            课程表预约规则是最具体的规则，会覆盖课程规则、会员规则和会员卡规则
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm">
              课程表预约规则适用于特定的课程排期，例如"周一哈他瑜伽 10:00-11:30"。这些规则会覆盖所有其他规则，包括：
            </p>
            <ol className="list-decimal list-inside space-y-1 text-sm ml-4">
              <li>课程规则</li>
              <li>会员规则</li>
              <li>会员卡规则</li>
            </ol>
            <p className="text-sm">
              您可以为特定的课程排期设置特殊的预约规则，例如限制预约人数、设置特殊的预约时间等。
            </p>
          </div>
        </CardContent>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>选择课程表</CardTitle>
            <CardDescription>
              选择要设置预约规则的课程表
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="schedule-select">课程表</Label>
                <Select value={selectedSchedule} onValueChange={setSelectedSchedule}>
                  <SelectTrigger id="schedule-select">
                    <SelectValue placeholder="选择课程表" />
                  </SelectTrigger>
                  <SelectContent>
                    {schedules.map((schedule) => (
                      <SelectItem key={schedule.id} value={schedule.id}>
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: schedule.color }}
                          ></div>
                          {schedule.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>日期选择（可选）</Label>
                <div className="border rounded-md p-3">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    className="mx-auto"
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  您可以选择特定日期设置一次性规则，不选择则应用于所有日期
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="md:col-span-3">
          {!selectedSchedule ? (
            <Card>
              <CardContent className="flex items-center justify-center min-h-[400px]">
                <div className="text-center text-muted-foreground">
                  <p>请先选择一个课程表</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <BookingRulesSettings 
              level="course"
              courseId={schedules.find(s => s.id === selectedSchedule)?.courseId}
              onSave={handleSave}
            />
          )}
        </div>
      </div>
      
      <div className="flex justify-between">
        <Button variant="outline" onClick={() => router.push("/booking-rules/courses")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回课程规则
        </Button>
        
        <Button variant="outline" onClick={() => router.push("/courses/schedule")}>
          <Calendar className="mr-2 h-4 w-4" />
          查看课程表
        </Button>
      </div>
    </div>
  )
}
