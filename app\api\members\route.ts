import { NextRequest, NextResponse } from "next/server";
import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

// 获取会员列表
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get('tenantId');
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const keyword = searchParams.get('keyword');
    const status = searchParams.get('status');
    const level = searchParams.get('level');
    const sortBy = searchParams.get('sortBy') || 'newest';

    console.log('获取会员列表，参数:', { tenantId, page, pageSize, keyword, status, level, sortBy });

    if (!tenantId) {
      return NextResponse.json({
        code: 400,
        msg: '缺少租户ID',
        data: null
      }, { status: 400 });
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 构建查询条件
      let whereConditions = ['m.tenant_id = ?'];
      let queryParams: any[] = [parseInt(tenantId)];

      if (keyword) {
        whereConditions.push('(m.name LIKE ? OR m.phone LIKE ? OR m.member_no LIKE ?)');
        const keywordPattern = `%${keyword}%`;
        queryParams.push(keywordPattern, keywordPattern, keywordPattern);
      }

      if (status && status !== 'all') {
        whereConditions.push('m.status = ?');
        queryParams.push(status);
      }

      if (level && level !== 'all') {
        whereConditions.push('m.level = ?');
        queryParams.push(level);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 构建排序条件
      let orderBy = 'ORDER BY m.created_at DESC';
      switch (sortBy) {
        case 'newest':
          orderBy = 'ORDER BY m.created_at DESC';
          break;
        case 'oldest':
          orderBy = 'ORDER BY m.created_at ASC';
          break;
        case 'name':
          orderBy = 'ORDER BY m.name ASC';
          break;
        case 'level':
          orderBy = 'ORDER BY m.level DESC, m.created_at DESC';
          break;
        case 'points':
          orderBy = 'ORDER BY m.points DESC';
          break;
        case 'spent':
          orderBy = 'ORDER BY m.total_spent DESC';
          break;
      }

      // 查询总数
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM members m 
        ${whereClause}
      `;
      const [countResult] = await connection.execute(countQuery, queryParams);
      const total = (countResult as any[])[0].total;

      // 查询会员列表
      const offset = (page - 1) * pageSize;
      const listQuery = `
        SELECT 
          m.*,
          COUNT(mc.id) as card_count,
          COUNT(CASE WHEN mc.status = 'active' THEN 1 END) as active_card_count
        FROM members m
        LEFT JOIN member_cards mc ON m.id = mc.member_id
        ${whereClause}
        GROUP BY m.id
        ${orderBy}
        LIMIT ? OFFSET ?
      `;
      
      const listParams = [...queryParams, pageSize, offset];
      const [members] = await connection.execute(listQuery, listParams);

      console.log('查询会员成功，总数:', total, '当前页:', members);

      return NextResponse.json({
        code: 200,
        data: {
          list: members,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        },
        msg: '获取会员列表成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('获取会员列表失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '获取会员列表失败',
      data: null
    }, { status: 500 });
  }
}

// 创建会员
export async function POST(req: NextRequest) {
  try {
    const data = await req.json();
    console.log('创建会员，数据:', data);

    const {
      tenant_id,
      name,
      phone,
      email,
      gender,
      birthday,
      emergency_contact,
      emergency_phone,
      address,
      notes,
      level = 'bronze',
      source = 'offline',
      referrer_id
    } = data;

    // 验证必填字段
    if (!tenant_id || !name || !phone) {
      return NextResponse.json({
        code: 400,
        msg: '缺少必填字段：租户ID、姓名、手机号',
        data: null
      }, { status: 400 });
    }

    // 创建数据库连接
    const connection = await mysql.createConnection(dbConfig);

    try {
      // 检查手机号是否已存在
      const [existingMembers] = await connection.execute(
        'SELECT id FROM members WHERE phone = ? AND tenant_id = ?',
        [phone, tenant_id]
      );

      if ((existingMembers as any[]).length > 0) {
        return NextResponse.json({
          code: 400,
          msg: '该手机号已被注册',
          data: null
        }, { status: 400 });
      }

      // 生成会员编号
      const [maxMemberNo] = await connection.execute(
        'SELECT member_no FROM members WHERE tenant_id = ? ORDER BY id DESC LIMIT 1',
        [tenant_id]
      );

      let memberNo;
      if ((maxMemberNo as any[]).length > 0) {
        const lastNo = (maxMemberNo as any[])[0].member_no;
        const lastNumber = parseInt(lastNo.slice(-3));
        memberNo = `M${tenant_id.toString().padStart(3, '0')}${(lastNumber + 1).toString().padStart(3, '0')}`;
      } else {
        memberNo = `M${tenant_id.toString().padStart(3, '0')}001`;
      }

      // 插入会员数据
      const insertQuery = `
        INSERT INTO members (
          tenant_id, member_no, name, phone, email, gender, birthday,
          emergency_contact, emergency_phone, address, notes, level, source, referrer_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const [result] = await connection.execute(insertQuery, [
        tenant_id, memberNo, name, phone, email, gender, birthday,
        emergency_contact, emergency_phone, address, notes, level, source, referrer_id
      ]);

      const memberId = (result as any).insertId;

      console.log('创建会员成功，ID:', memberId);

      return NextResponse.json({
        code: 200,
        data: {
          id: memberId,
          member_no: memberNo,
          name,
          phone,
          email,
          level,
          status: 'active'
        },
        msg: '创建会员成功'
      });

    } finally {
      await connection.end();
    }
  } catch (error) {
    console.error('创建会员失败:', error);
    return NextResponse.json({
      code: 500,
      msg: '创建会员失败',
      data: null
    }, { status: 500 });
  }
}
