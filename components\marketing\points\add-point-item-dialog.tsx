"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { CalendarIcon, ImageIcon, Upload } from "lucide-react"

interface AddPointItemDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AddPointItemDialog({ open, onOpenChange }: AddPointItemDialogProps) {
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle>添加积分商品</DialogTitle>
          <DialogDescription>创建新的积分兑换商品，设置详细信息和兑换规则</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">基本信息</TabsTrigger>
            <TabsTrigger value="rules">兑换规则</TabsTrigger>
            <TabsTrigger value="display">展示设置</TabsTrigger>
            <TabsTrigger value="advanced">高级设置</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">商品名称</Label>
                <Input id="name" placeholder="输入商品名称" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">商品类型</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="选择商品类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="course">课程</SelectItem>
                    <SelectItem value="product">实物商品</SelectItem>
                    <SelectItem value="coupon">优惠券</SelectItem>
                    <SelectItem value="service">增值服务</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 col-span-2">
                <Label htmlFor="description">商品描述</Label>
                <Textarea id="description" placeholder="输入商品详细描述" rows={3} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="points">所需积分</Label>
                <Input id="points" type="number" min="1" placeholder="输入兑换所需积分" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="stock">库存数量</Label>
                <Input id="stock" type="number" min="0" placeholder="输入库存数量" />
              </div>

              <div className="space-y-2">
                <Label>有效期开始</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "PPP") : "选择开始日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={startDate} onSelect={setStartDate} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>有效期结束</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn("w-full justify-start text-left font-normal", !endDate && "text-muted-foreground")}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "PPP") : "选择结束日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar mode="single" selected={endDate} onSelect={setEndDate} initialFocus />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="rules" className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="limit-per-user">每人限兑</Label>
                <Input id="limit-per-user" type="number" min="0" placeholder="0表示不限制" />
                <p className="text-xs text-muted-foreground mt-1">每位会员最多可兑换的数量，0表示不限制</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="daily-limit">每日限量</Label>
                <Input id="daily-limit" type="number" min="0" placeholder="0表示不限制" />
                <p className="text-xs text-muted-foreground mt-1">每天最多可兑换的总数量，0表示不限制</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="member-level">会员等级限制</Label>
                <Select>
                  <SelectTrigger id="member-level">
                    <SelectValue placeholder="选择会员等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有会员</SelectItem>
                    <SelectItem value="silver">银卡会员及以上</SelectItem>
                    <SelectItem value="gold">金卡会员及以上</SelectItem>
                    <SelectItem value="platinum">铂金会员及以上</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="exchange-period">兑换周期</Label>
                <Select>
                  <SelectTrigger id="exchange-period">
                    <SelectValue placeholder="选择兑换周期" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">不限制</SelectItem>
                    <SelectItem value="daily">每日一次</SelectItem>
                    <SelectItem value="weekly">每周一次</SelectItem>
                    <SelectItem value="monthly">每月一次</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="col-span-2 space-y-2">
                <Label>兑换审核</Label>
                <div className="flex items-center space-x-2">
                  <Switch id="require-approval" />
                  <Label htmlFor="require-approval">需要管理员审核</Label>
                </div>
                <p className="text-xs text-muted-foreground mt-1">开启后，会员兑换需要管理员审核通过</p>
              </div>

              <div className="col-span-2 space-y-2">
                <Label>兑换通知</Label>
                <div className="flex items-center space-x-2">
                  <Switch id="notify-admin" defaultChecked />
                  <Label htmlFor="notify-admin">通知管理员</Label>
                </div>
                <div className="flex items-center space-x-2 mt-2">
                  <Switch id="notify-member" defaultChecked />
                  <Label htmlFor="notify-member">通知会员</Label>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="display" className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-2">
                <Label>商品图片</Label>
                <div className="border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center">
                  <ImageIcon className="h-10 w-10 text-muted-foreground mb-2" />
                  <div className="flex flex-col items-center">
                    <p className="text-sm text-muted-foreground mb-1">拖放图片或点击上传</p>
                    <p className="text-xs text-muted-foreground">支持 JPG, PNG, GIF (最大 2MB)</p>
                  </div>
                  <Button variant="outline" size="sm" className="mt-4">
                    <Upload className="h-4 w-4 mr-2" />
                    选择文件
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="display-order">显示顺序</Label>
                <Input id="display-order" type="number" min="0" placeholder="数字越小越靠前" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="highlight">高亮显示</Label>
                <div className="flex items-center space-x-2">
                  <Switch id="highlight" />
                  <Label htmlFor="highlight">在首页推荐</Label>
                </div>
              </div>

              <div className="col-span-2 space-y-2">
                <Label htmlFor="tags">标签</Label>
                <Input id="tags" placeholder="输入标签，用逗号分隔" />
                <p className="text-xs text-muted-foreground mt-1">例如：热门,限时,新品</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 space-y-2">
                <Label>兑换后操作</Label>
                <div className="flex items-center space-x-2">
                  <Switch id="auto-send" />
                  <Label htmlFor="auto-send">自动发放</Label>
                </div>
                <p className="text-xs text-muted-foreground mt-1">开启后，系统将自动完成发放流程</p>
              </div>

              <div className="col-span-2 space-y-2">
                <Label>库存预警</Label>
                <div className="flex items-center space-x-2">
                  <Switch id="stock-alert" />
                  <Label htmlFor="stock-alert">启用库存预警</Label>
                </div>
                <Input id="stock-alert-threshold" type="number" min="1" placeholder="预警阈值" className="mt-2 w-1/3" />
                <p className="text-xs text-muted-foreground mt-1">当库存低于阈值时，系统将发送通知</p>
              </div>

              <div className="col-span-2 space-y-2">
                <Label htmlFor="exchange-instructions">兑换说明</Label>
                <Textarea id="exchange-instructions" placeholder="输入兑换说明，将显示给会员" rows={3} />
              </div>

              <div className="col-span-2 space-y-2">
                <Label htmlFor="admin-notes">管理员备注</Label>
                <Textarea id="admin-notes" placeholder="输入管理员备注，仅管理员可见" rows={2} />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button type="submit">保存</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

