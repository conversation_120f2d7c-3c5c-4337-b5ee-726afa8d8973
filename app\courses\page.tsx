"use client"

// 在 import 部分添加新的导入
import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CourseTable } from "@/components/course-table"
import { Plus, Filter, SlidersHorizontal, Download, Trash2, FileUp, FileDown } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
  SheetClose,
} from "@/components/ui/sheet"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { AddEditCourseDialog } from "@/components/courses/add-edit-course-dialog"
import { ImportCoursesDialog } from "@/components/courses/import-courses-dialog"
import { ExportCoursesDialog } from "@/components/courses/export-courses-dialog"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CourseGrid } from "@/components/courses/course-grid"
import { courseApi, coachApi, venueApi } from "@/lib/api"
import { toast } from "@/components/ui/use-toast"
import { useToast } from "@/hooks/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useDebounce } from "../../hooks/use-debounce"
import { courseService } from "@/services/course-service"

// 添加课程种类定义
interface CourseType {
  id: string;
  name: string; // 课程名称
  description: string;
  price: number; // 标准价格
  coverImage?: string; // 课程封面图片
  type: string; // 课程类型ID
  typeName?: string; // 课程类型名称
  typeColor?: string; // 课程类型颜色
  duration: number; // 标准课程时长（分钟）
  level: number; // 课程难度（1-5星）
  courseColor?: string; // 课程颜色
  defaultCoach: string; // 默认教练ID
  defaultCoachName?: string; // 默认教练名称
  defaultVenue: string; // 默认场地ID
  defaultVenueName?: string; // 默认场地名称
  capacity: number; // 标准容量
  status: string; // 状态：active, inactive
  statusName?: string; // 状态名称
  createTime?: string;
  updateTime?: string;
  createdAt?: string;
  // 课程属性
  tags?: string[]; // 课程标签
  requiresEquipment?: boolean; // 是否需要器材
  equipmentList?: string; // 器材清单
  notes?: string; // 备注
  isPrivate?: boolean; // 是否私教课
  allowWaitlist?: boolean; // 是否允许候补
  maxWaitlist?: number; // 最大候补人数
  cancellationPolicy?: string; // 取消政策
  prerequisites?: string; // 前置要求
  membershipCards?: string[]; // 关联会员卡
  isPaidCourse?: boolean; // 是否付费课程
  paymentType?: string; // 付费类型
  // 统计信息
  totalSchedules?: number; // 总排期数
  activeSchedules?: number; // 活跃排期数
  totalBookings?: number; // 总预约数
  averageRating?: number; // 平均评分
}

// 添加 currentFilters 的类型定义
interface CurrentFilters {
  keyword?: string
  type?: string
  status?: string
  coachId?: string
  priceMin?: number
  priceMax?: number
  capacityMin?: number
  capacityMax?: number
  dayOfWeek?: string[]
  timeOfDay?: string[]
  venueId?: string
  page?: number
  pageSize?: number
  sortBy?: string
  // 添加额外的字段以匹配advancedFilters的类型
  advancedFilters?: any // 简化类型匹配
}

// 修改 CoursesPage 组件，添加新的状态和处理函数
export default function CoursesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  // 使用防抖钩子处理搜索查询
  const debouncedSearchQuery = useDebounce(searchQuery, 500);
  const [selectedType, setSelectedType] = useState("all")
  const [selectedStatus, setSelectedStatus] = useState("all")
  const [selectedCoach, setSelectedCoach] = useState("all")
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [sortBy, setSortBy] = useState("newest")
  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<"list" | "grid">("list")
  const [showAddEditDialog, setShowAddEditDialog] = useState(false)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [selectedCourseIds, setSelectedCourseIds] = useState<string[]>([])
  const [courseToEdit, setCourseToEdit] = useState<any>(null)
  const [visibleColumns, setVisibleColumns] = useState({
    id: true,
    name: true,
    type: true,
    defaultCoach: true, // 默认教练
    price: true,
    duration: true, // 课程时长
    level: true, // 课程难度
    capacity: true,
    defaultVenue: true, // 默认场地
    status: true,
    schedules: true, // 排期统计
    bookings: false, // 预约统计
    rating: false, // 平均评分
    tags: false, // 课程标签
    equipment: false, // 器材要求
    notes: false, // 备注
  })

  // 分页状态
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)

  // 课程种类数据状态
  const [courseTypes, setCourseTypes] = useState<CourseType[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 教练和场地数据状态
  const [coaches, setCoaches] = useState<any[]>([])
  const [venues, setVenues] = useState<any[]>([])
  const [courseTypeOptions, setCourseTypeOptions] = useState<any[]>([]) // 课程类型选项
  const [loadingOptions, setLoadingOptions] = useState({
    coaches: false,
    venues: false,
    types: false
  })

  // 高级筛选选项
  const [advancedFilters, setAdvancedFilters] = useState({
    priceMin: "",
    priceMax: "",
    capacityMin: "",
    capacityMax: "",
    durationMin: "", // 最短时长
    durationMax: "", // 最长时长
    level: [] as string[], // 课程难度
    venue: "all" as string,
    requiresEquipment: "all" as string, // 器材要求
    isPaidCourse: "all" as string, // 是否付费课程
    isPrivate: "all" as string, // 是否私教课
    allowWaitlist: "all" as string, // 是否允许候补
    tags: [] as string[], // 课程标签
    bookingsMin: "", // 最少预约数
    bookingsMax: "", // 最多预约数
    ratingMin: "all", // 最低评分
  })

  // 添加删除对话框状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);

  // 添加状态来跟踪当前筛选条件
  const [currentFilters, setCurrentFilters] = useState<CurrentFilters>({});
  
  // 获取教练列表
  const fetchCoaches = async () => {
    try {
      setLoadingOptions(prev => ({ ...prev, coaches: true }))
      console.log('使用默认教练数据...')

      // 直接使用默认教练数据，不调用API
      const defaultCoaches = [
        { id: "1", name: "张教练", avatar: "/placeholder.svg?height=32&width=32", specialty: ["基础瑜伽", "哈他瑜伽"] },
        { id: "2", name: "李教练", avatar: "/placeholder.svg?height=32&width=32", specialty: ["阴瑜伽", "孕产瑜伽"] },
        { id: "3", name: "王教练", avatar: "/placeholder.svg?height=32&width=32", specialty: ["流瑜伽", "力量瑜伽"] },
        { id: "4", name: "赵教练", avatar: "/placeholder.svg?height=32&width=32", specialty: ["空中瑜伽", "进阶瑜伽"] },
        { id: "5", name: "刘教练", avatar: "/placeholder.svg?height=32&width=32", specialty: ["私教", "康复瑜伽"] },
      ];

      console.log('教练数据:', defaultCoaches);
      setCoaches(defaultCoaches);

    } catch (err) {
      console.error('获取教练数据失败:', err)
      toast({
        title: '错误',
        description: '获取教练数据失败',
        variant: 'destructive',
      })
    } finally {
      setLoadingOptions(prev => ({ ...prev, coaches: false }))
    }
  }

  // 获取场馆列表
  const fetchVenues = async () => {
    try {
      setLoadingOptions(prev => ({ ...prev, venues: true }))
      console.log('使用默认场地数据...')

      // 直接使用默认场地数据，不调用API
      const defaultVenues = [
        { id: "1", name: "1号瑜伽室", capacity: 15, location: "一楼东侧" },
        { id: "2", name: "2号瑜伽室", capacity: 10, location: "一楼西侧" },
        { id: "3", name: "3号瑜伽室", capacity: 15, location: "二楼东侧" },
        { id: "4", name: "4号瑜伽室", capacity: 8, location: "二楼西侧" },
        { id: "5", name: "私教室", capacity: 2, location: "三楼" },
      ];

      console.log('场地数据:', defaultVenues);
      setVenues(defaultVenues);

    } catch (err) {
      console.error('获取场馆数据失败:', err)
      toast({
        title: '错误',
        description: '获取场馆数据失败',
        variant: 'destructive',
      })
    } finally {
      setLoadingOptions(prev => ({ ...prev, venues: false }))
    }
  }

  // 获取课程类型选项列表
  const fetchCourseTypeOptions = async () => {
    try {
      setLoadingOptions(prev => ({ ...prev, types: true }))

      // 使用默认的课程类型选项
      const defaultTypes = [
        { id: "basic", name: "基础瑜伽", color: "#4285F4", description: "适合初学者的基础瑜伽课程" },
        { id: "advanced", name: "进阶瑜伽", color: "#34A853", description: "适合有基础的学员" },
        { id: "yin", name: "阴瑜伽", color: "#FBBC05", description: "深度放松的阴瑜伽" },
        { id: "prenatal", name: "孕产瑜伽", color: "#EA4335", description: "专为孕妇设计的瑜伽" },
        { id: "aerial", name: "空中瑜伽", color: "#FF6D91", description: "空中瑜伽练习" },
        { id: "private", name: "私教课", color: "#9C27B0", description: "一对一私教课程" },
      ];

      console.log('使用默认课程类型选项:', defaultTypes);
      setCourseTypeOptions(defaultTypes);

    } catch (err) {
      console.error('获取课程类型选项失败:', err)
      toast({
        title: '错误',
        description: '获取课程类型选项失败',
        variant: 'destructive',
      })
    } finally {
      setLoadingOptions(prev => ({ ...prev, types: false }))
    }
  }
  
  // 在组件挂载时获取选项数据
  useEffect(() => {
    const loadInitialData = async () => {
      // 并行加载所有选项数据
      await Promise.all([
        fetchCoaches(),
        fetchVenues(),
        fetchCourseTypeOptions()
      ]);

      // 等所有选项数据加载完成后再加载课程种类数据
      fetchCourseTypes();
    };

    loadInitialData();
  }, []);

  // 处理筛选条件变化和数据获取
  useEffect(() => {
    // 只有在组件已挂载完成后才响应筛选条件变化
    if (venues.length > 0 || coaches.length > 0) {
      // 更新筛选条件状态，但不直接触发状态更新循环
      const filters: CurrentFilters = {
        keyword: debouncedSearchQuery,
        type: selectedType !== 'all' ? selectedType : undefined,
        status: selectedStatus !== 'all' ? selectedStatus : undefined,
        coachId: selectedCoach !== 'all' ? selectedCoach : undefined,
        page,
        pageSize,
        sortBy
      };
      
      // 添加高级筛选条件
      if (advancedFilters.priceMin) filters.priceMin = Number(advancedFilters.priceMin);
      if (advancedFilters.priceMax) filters.priceMax = Number(advancedFilters.priceMax);
      if (advancedFilters.capacityMin) filters.capacityMin = Number(advancedFilters.capacityMin);
      if (advancedFilters.capacityMax) filters.capacityMax = Number(advancedFilters.capacityMax);
      if (advancedFilters.venue !== 'all') filters.venueId = advancedFilters.venue;
      
      // 一次性更新筛选条件
      setCurrentFilters(filters);
      
      // 直接调用fetchCourseTypes，但不在这里依赖currentFilters
      fetchCourseTypes();
    }
  }, [debouncedSearchQuery, selectedType, selectedStatus, selectedCoach, sortBy, advancedFilters, venues.length, coaches.length, page, pageSize]);

  // 获取课程种类数据
  const fetchCourseTypes = async () => {
    try {
      setLoading(true)
      console.log('开始获取课程种类数据...')
      
      // 使用内部服务调用而非API请求
      try {
        // 构建过滤参数 - 直接使用当前状态而非currentFilters
        const filterParams: any = {};
        
        if (debouncedSearchQuery) filterParams.keyword = debouncedSearchQuery;
        if (selectedType !== 'all') filterParams.typeId = selectedType;
        if (selectedStatus !== 'all') filterParams.status = selectedStatus;
        if (selectedCoach !== 'all') filterParams.instructor = selectedCoach;
        
        // 添加高级筛选条件
        if (advancedFilters.priceMin) filterParams.priceMin = Number(advancedFilters.priceMin);
        if (advancedFilters.priceMax) filterParams.priceMax = Number(advancedFilters.priceMax);
        if (advancedFilters.capacityMin) filterParams.capacityMin = Number(advancedFilters.capacityMin);
        if (advancedFilters.capacityMax) filterParams.capacityMax = Number(advancedFilters.capacityMax);
        if (advancedFilters.venue !== 'all') filterParams.venueId = advancedFilters.venue;
        
        console.log('使用过滤参数:', filterParams);
        
        // 直接调用课程服务获取数据
        const coursesData = courseService.getAll(filterParams);
        console.log('从服务获取的课程数据:', coursesData);
        
        // 转换数据类型适配前端需要的格式
        const transformedCourses: CourseType[] = coursesData.map(course => ({
          id: String(course.id),
          name: course.name,
          description: course.description,
          price: course.price,
          type: String(course.typeId),
          typeName: course.typeName,
          typeColor: course.typeColor,
          duration: course.duration,
          level: course.level || 1,
          defaultCoach: String(course.instructor) || "1",
          defaultCoachName: course.instructor,
          defaultVenue: "1",
          defaultVenueName: "默认场地",
          capacity: course.capacity,
          status: course.status,
          statusName: course.status === 'active' ? '进行中' : '未开始',
          createdAt: course.createdAt,
          isPaidCourse: course.price > 0,
          totalBookings: course.enrolled || 0
        }));
        
        // 设置课程数据
        setCourseTypes(transformedCourses);
        setTotal(transformedCourses.length);
        return;
      } catch (serviceError) {
        console.error('服务调用出错:', serviceError);
      }
      
      // 如果服务调用失败，使用备用模拟数据
      console.log('服务调用失败，使用备用模拟数据');
      console.log('当前筛选条件:', currentFilters);
      const mockCourseTypes: CourseType[] = [
        {
          id: "CT001",
          name: "基础瑜伽入门",
          description: "适合初学者的基础瑜伽课程，学习基本体式和呼吸法",
          price: 80,
          coverImage: "/placeholder.svg?height=225&width=400",
          type: "basic",
          typeName: "基础瑜伽",
          typeColor: "#4285F4",
          duration: 90,
          level: 1,
          courseColor: "#4285F4",
          defaultCoach: "1",
          defaultCoachName: "张教练",
          defaultVenue: "1",
          defaultVenueName: "1号瑜伽室",
          capacity: 15,
          status: "active",
          statusName: "进行中",
          tags: ["初学者", "基础", "体式"],
          requiresEquipment: false,
          allowWaitlist: true,
          maxWaitlist: 5,
          cancellationPolicy: "24小时前可取消",
          membershipCards: ["basic", "premium"],
          isPaidCourse: false,
          totalSchedules: 12,
          activeSchedules: 8,
          totalBookings: 156,
          averageRating: 4.5,
          createdAt: new Date().toISOString(),
        },
        {
          id: "CT002",
          name: "进阶流瑜伽",
          description: "适合有基础的学员，连贯性瑜伽练习",
          price: 120,
          coverImage: "https://images.unsplash.com/photo-1506629905607-d9c297d3d75f?w=400&h=225&fit=crop&crop=center",
          type: "advanced",
          typeName: "进阶瑜伽",
          typeColor: "#34A853",
          duration: 75,
          level: 3,
          courseColor: "#34A853",
          defaultCoach: "4",
          defaultCoachName: "赵教练",
          defaultVenue: "4",
          defaultVenueName: "4号瑜伽室",
          capacity: 8,
          status: "active",
          statusName: "进行中",
          tags: ["进阶", "流瑜伽", "力量"],
          requiresEquipment: true,
          equipmentList: "瑜伽砖、瑜伽带",
          allowWaitlist: true,
          maxWaitlist: 3,
          cancellationPolicy: "24小时前可取消",
          membershipCards: ["premium", "vip"],
          isPaidCourse: false,
          totalSchedules: 8,
          activeSchedules: 6,
          totalBookings: 89,
          averageRating: 4.8,
          createdAt: new Date().toISOString(),
        },
        {
          id: "CT003",
          name: "阴瑜伽放松",
          description: "深度放松的阴瑜伽，舒缓身心压力",
          price: 85,
          coverImage: "/placeholder.svg?height=225&width=400",
          type: "yin",
          typeName: "阴瑜伽",
          typeColor: "#FBBC05",
          duration: 75,
          level: 2,
          courseColor: "#FBBC05",
          defaultCoach: "2",
          defaultCoachName: "李教练",
          defaultVenue: "3",
          defaultVenueName: "3号瑜伽室",
          capacity: 15,
          status: "active",
          statusName: "进行中",
          tags: ["放松", "阴瑜伽", "冥想"],
          requiresEquipment: true,
          equipmentList: "瑜伽枕、毛毯",
          allowWaitlist: true,
          maxWaitlist: 5,
          cancellationPolicy: "2小时前可取消",
          membershipCards: ["basic", "premium"],
          isPaidCourse: false,
          totalSchedules: 6,
          activeSchedules: 4,
          totalBookings: 72,
          averageRating: 4.6,
          createdAt: new Date().toISOString(),
        },
        {
          id: "CT004",
          name: "孕产瑜伽特训",
          description: "专为孕妇设计的安全瑜伽练习",
          price: 100,
          coverImage: "/placeholder.svg?height=225&width=400",
          type: "prenatal",
          typeName: "孕产瑜伽",
          typeColor: "#EA4335",
          duration: 60,
          level: 1,
          courseColor: "#EA4335",
          defaultCoach: "2",
          defaultCoachName: "李教练",
          defaultVenue: "2",
          defaultVenueName: "2号瑜伽室",
          capacity: 8,
          status: "active",
          statusName: "进行中",
          tags: ["孕妇", "安全", "特殊"],
          requiresEquipment: true,
          equipmentList: "瑜伽球、瑜伽枕",
          allowWaitlist: true,
          maxWaitlist: 3,
          cancellationPolicy: "4小时前可取消",
          prerequisites: "怀孕12周以上，医生同意",
          membershipCards: ["premium", "vip"],
          isPaidCourse: false,
          totalSchedules: 4,
          activeSchedules: 3,
          totalBookings: 28,
          averageRating: 4.9,
          createdAt: new Date().toISOString(),
        },
        {
          id: "CT005",
          name: "空中瑜伽体验",
          description: "空中瑜伽入门体验课程",
          price: 150,
          coverImage: "/placeholder.svg?height=225&width=400",
          type: "aerial",
          typeName: "空中瑜伽",
          typeColor: "#FF6D91",
          duration: 60,
          level: 2,
          courseColor: "#FF6D91",
          defaultCoach: "4",
          defaultCoachName: "赵教练",
          defaultVenue: "4",
          defaultVenueName: "4号瑜伽室",
          capacity: 6,
          status: "active",
          statusName: "进行中",
          tags: ["空中", "体验", "新颖"],
          requiresEquipment: true,
          equipmentList: "空中瑜伽吊床",
          allowWaitlist: true,
          maxWaitlist: 2,
          cancellationPolicy: "24小时前可取消",
          prerequisites: "无心脏病、高血压等疾病",
          membershipCards: ["vip"],
          isPaidCourse: true,
          paymentType: "单次付费",
          totalSchedules: 3,
          activeSchedules: 2,
          totalBookings: 18,
          averageRating: 4.7,
          createdAt: new Date().toISOString(),
        },
        {
          id: "CT006",
          name: "私教一对一",
          description: "个性化一对一私教课程",
          price: 200,
          coverImage: "https://images.unsplash.com/photo-1506629905607-d9c297d3d75f?w=400&h=225&fit=crop&crop=center",
          type: "private",
          typeName: "私教课",
          typeColor: "#9C27B0",
          duration: 60,
          level: 5,
          courseColor: "#9C27B0",
          defaultCoach: "5",
          defaultCoachName: "刘教练",
          defaultVenue: "5",
          defaultVenueName: "私教室",
          capacity: 1,
          status: "active",
          statusName: "进行中",
          tags: ["私教", "个性化", "高端"],
          requiresEquipment: false,
          isPrivate: true,
          allowWaitlist: false,
          cancellationPolicy: "12小时前可取消",
          membershipCards: ["vip"],
          isPaidCourse: true,
          paymentType: "按次付费",
          totalSchedules: 15,
          activeSchedules: 12,
          totalBookings: 45,
          averageRating: 5.0,
          createdAt: new Date().toISOString(),
        }
      ];

      // 应用筛选条件
      let filteredTypes = mockCourseTypes;

      // 关键词搜索
      if (debouncedSearchQuery) {
        filteredTypes = filteredTypes.filter(courseType =>
          courseType.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          courseType.description.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          courseType.tags?.some(tag => tag.toLowerCase().includes(debouncedSearchQuery.toLowerCase()))
        );
      }

      // 课程类型筛选
      if (selectedType !== 'all') {
        filteredTypes = filteredTypes.filter(courseType => courseType.type === selectedType);
      }

      // 状态筛选
      if (selectedStatus !== 'all') {
        filteredTypes = filteredTypes.filter(courseType => courseType.status === selectedStatus);
      }

      // 教练筛选
      if (selectedCoach !== 'all') {
        filteredTypes = filteredTypes.filter(courseType => courseType.defaultCoach === selectedCoach);
      }

      // 价格筛选
      if (advancedFilters.priceMin) {
        filteredTypes = filteredTypes.filter(courseType => courseType.price >= Number(advancedFilters.priceMin));
      }
      if (advancedFilters.priceMax) {
        filteredTypes = filteredTypes.filter(courseType => courseType.price <= Number(advancedFilters.priceMax));
      }

      // 容量筛选
      if (advancedFilters.capacityMin) {
        filteredTypes = filteredTypes.filter(courseType => courseType.capacity >= Number(advancedFilters.capacityMin));
      }
      if (advancedFilters.capacityMax) {
        filteredTypes = filteredTypes.filter(courseType => courseType.capacity <= Number(advancedFilters.capacityMax));
      }

      // 场地筛选
      if (advancedFilters.venue !== 'all') {
        filteredTypes = filteredTypes.filter(courseType => courseType.defaultVenue === advancedFilters.venue);
      }

      // 时长筛选
      if (advancedFilters.durationMin) {
        filteredTypes = filteredTypes.filter(courseType => courseType.duration >= Number(advancedFilters.durationMin));
      }
      if (advancedFilters.durationMax) {
        filteredTypes = filteredTypes.filter(courseType => courseType.duration <= Number(advancedFilters.durationMax));
      }

      // 难度筛选
      if (advancedFilters.level.length > 0) {
        filteredTypes = filteredTypes.filter(courseType =>
          advancedFilters.level.includes(courseType.level?.toString() || "1")
        );
      }

      // 器材要求筛选
      if (advancedFilters.requiresEquipment !== 'all') {
        const requiresEquipment = advancedFilters.requiresEquipment === 'true';
        filteredTypes = filteredTypes.filter(courseType => courseType.requiresEquipment === requiresEquipment);
      }

      // 付费课程筛选
      if (advancedFilters.isPaidCourse !== 'all') {
        const isPaidCourse = advancedFilters.isPaidCourse === 'true';
        filteredTypes = filteredTypes.filter(courseType => courseType.isPaidCourse === isPaidCourse);
      }

      // 私教课筛选
      if (advancedFilters.isPrivate !== 'all') {
        const isPrivate = advancedFilters.isPrivate === 'true';
        filteredTypes = filteredTypes.filter(courseType => courseType.isPrivate === isPrivate);
      }

      // 候补筛选
      if (advancedFilters.allowWaitlist !== 'all') {
        const allowWaitlist = advancedFilters.allowWaitlist === 'true';
        filteredTypes = filteredTypes.filter(courseType => courseType.allowWaitlist === allowWaitlist);
      }

      // 标签筛选
      if (advancedFilters.tags.length > 0) {
        filteredTypes = filteredTypes.filter(courseType =>
          courseType.tags?.some(tag => advancedFilters.tags.includes(tag))
        );
      }

      // 预约数筛选
      if (advancedFilters.bookingsMin) {
        filteredTypes = filteredTypes.filter(courseType =>
          (courseType.totalBookings || 0) >= Number(advancedFilters.bookingsMin)
        );
      }
      if (advancedFilters.bookingsMax) {
        filteredTypes = filteredTypes.filter(courseType =>
          (courseType.totalBookings || 0) <= Number(advancedFilters.bookingsMax)
        );
      }

      // 评分筛选
      if (advancedFilters.ratingMin) {
        filteredTypes = filteredTypes.filter(courseType =>
          (courseType.averageRating || 0) >= Number(advancedFilters.ratingMin)
        );
      }

      // 排序
      switch (sortBy) {
        case 'newest':
          filteredTypes.sort((a, b) => new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime());
          break;
        case 'oldest':
          filteredTypes.sort((a, b) => new Date(a.createdAt || '').getTime() - new Date(b.createdAt || '').getTime());
          break;
        case 'price-high':
          filteredTypes.sort((a, b) => b.price - a.price);
          break;
        case 'price-low':
          filteredTypes.sort((a, b) => a.price - b.price);
          break;
        case 'popular':
          filteredTypes.sort((a, b) => (b.totalBookings || 0) - (a.totalBookings || 0));
          break;
      }

      // 分页
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedTypes = filteredTypes.slice(startIndex, endIndex);

      setTotal(filteredTypes.length);
      setCourseTypes(paginatedTypes);

      console.log('处理后的课程种类数据:', paginatedTypes);

    } catch (err) {
      console.error('获取课程种类数据失败:', err)
      setError('获取课程种类数据失败')
      toast({
        title: '错误',
        description: '获取课程种类数据失败',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // 获取课程列表数据（保留原函数名以兼容）
  const fetchCourses = async () => {
    // 现在调用课程种类数据获取函数
    await fetchCourseTypes()
  }

  // 获取状态名称
  const getStatusName = (status: string): string => {
    const statusNames: Record<string, string> = {
      active: "进行中",
      inactive: "未开始",
      upcoming: "即将开始",
      ended: "已结束",
      cancelled: "已取消",
    };
    return statusNames[status] || '未知状态';
  }

  // 处理高级筛选变更
  const handleAdvancedFilterChange = (field: string, value: any) => {
    setAdvancedFilters((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // 处理难度选择
  const handleLevelChange = (level: string) => {
    setAdvancedFilters((prev) => {
      const current = [...prev.level]
      if (current.includes(level)) {
        const updated = current.filter((l) => l !== level);
        console.log(`移除课程难度: ${level}，当前选择:`, updated);
        return { ...prev, level: updated }
      } else {
        const updated = [...current, level];
        console.log(`添加课程难度: ${level}，当前选择:`, updated);
        return { ...prev, level: updated }
      }
    })
  }

  // 处理标签选择
  const handleTagChange = (tag: string) => {
    setAdvancedFilters((prev) => {
      const current = [...prev.tags]
      if (current.includes(tag)) {
        const updated = current.filter((t) => t !== tag);
        console.log(`移除课程标签: ${tag}，当前选择:`, updated);
        return { ...prev, tags: updated }
      } else {
        const updated = [...current, tag];
        console.log(`添加课程标签: ${tag}，当前选择:`, updated);
        return { ...prev, tags: updated }
      }
    })
  }

  // 处理高级筛选应用
  const applyAdvancedFilters = () => {
    setShowFilters(false)
    // 重置页码
    setPage(1)
    // 调用API应用高级筛选
    console.log('应用高级筛选:', advancedFilters);
    fetchCourses()
  }

  // 重置高级筛选
  const resetAdvancedFilters = () => {
    setAdvancedFilters({
      priceMin: "",
      priceMax: "",
      capacityMin: "",
      capacityMax: "",
      durationMin: "",
      durationMax: "",
      level: [],
      venue: "all",
      requiresEquipment: "all",
      isPaidCourse: "all",
      isPrivate: "all",
      allowWaitlist: "all",
      tags: [],
      bookingsMin: "",
      bookingsMax: "",
      ratingMin: "all",
    })
  }

  // 处理列可见性变更
  const handleColumnVisibilityChange = (column: string) => {
    setVisibleColumns((prev) => ({
      ...prev,
      [column]: !prev[column as keyof typeof prev],
    }))
  }

  // 处理批量操作
  const handleBulkAction = useCallback(
    (action: string) => {
      if (action === "delete") {
        setIsBulkDeleting(true);
        console.log("删除选中课程", selectedItems);

        // 显示确认对话框
        setDeleteDialogOpen(true);
      } else if (action === "export") {
        // 打开导出对话框
        setShowExportDialog(true);
      }
    },
    [selectedItems]
  );

  // 处理搜索
  const handleSearch = () => {
    setPage(1) // 重置页码
    fetchCourses()
  }

  // 处理重置筛选
  const handleResetFilters = () => {
    setSearchQuery("")
    setSelectedType("all")
    setSelectedStatus("all")
    setSelectedCoach("all")
    setSortBy("newest")
    resetAdvancedFilters()
    setPage(1)

    // 添加延迟以确保状态已更新
    setTimeout(() => {
      fetchCourses()
    }, 0)
  }

  // 处理选中项变更
  const handleSelectedItemsChange = (items: string[]) => {
    setSelectedItems(items)
    // 同时更新导出组件使用的ID列表
    setSelectedCourseIds(items)
  }

  // 处理添加/编辑课程
  const handleAddEditCourse = (course: any) => {
    setCourseToEdit(course)
    setShowAddEditDialog(true)
  }

  // 处理课程保存
  const handleCourseSaved = () => {
    setShowAddEditDialog(false)
    setCourseToEdit(null)
    // 刷新课程列表
    fetchCourses()
  }

  // 处理复制课程
  const handleCopyCourse = (copiedCourse: any) => {
    try {
      // 设置要编辑的课程为复制的课程数据
      setCourseToEdit(copiedCourse)
      // 打开编辑对话框，让用户可以修改复制的课程信息
      setShowAddEditDialog(true)

      console.log('准备编辑复制的课程:', copiedCourse)
    } catch (error) {
      console.error('处理复制课程失败:', error)
      toast({
        title: "复制失败",
        description: "处理复制课程时发生错误",
        variant: "destructive"
      })
    }
  }

  // 处理删除课程
  const handleDeleteCourse = (courseId: string) => {
    try {
      // 从课程列表中移除指定课程
      setCourseTypes(prevCourses => prevCourses.filter(course => course.id !== courseId))

      // 更新总数
      setTotal(prevTotal => Math.max(0, prevTotal - 1))

      // 如果删除后当前页没有数据且不是第一页，则跳转到上一页
      const remainingCourses = courseTypes.filter(course => course.id !== courseId)
      if (remainingCourses.length === 0 && page > 1) {
        setPage(page - 1)
      }

      console.log('课程删除成功:', courseId)
    } catch (error) {
      console.error('处理删除课程失败:', error)
      toast({
        title: "删除失败",
        description: "处理删除课程时发生错误",
        variant: "destructive"
      })
    }
  }

  // 处理批量删除
  const handleDeleteSelected = async () => {
    if (selectedItems.length === 0) {
      toast({
        title: "请选择课程",
        description: "请先选择要删除的课程",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsBulkDeleting(true);
      // 执行批量删除
      await courseApi.deleteCourses(selectedItems);

      toast({
        title: "删除成功",
        description: `已成功删除 ${selectedItems.length} 个课程`,
      });

      // 清空选中项
      setSelectedItems([]);
      // 刷新课程列表
      fetchCourses();
    } catch (error) {
      console.error("批量删除失败:", error);
      toast({
        title: "删除失败",
        description: "批量删除课程时发生错误",
        variant: "destructive",
      });
    } finally {
      setIsBulkDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  // 添加导入模板下载函数
  const handleDownloadTemplate = () => {
    try {
      // 动态导入downloadHelper以避免SSR问题
      import("@/lib/download-helper").then(({ downloadHelper }) => {
        downloadHelper.createCourseImportTemplate();
        toast({
          title: "下载成功",
          description: "课程导入模板已下载",
        });
      });
    } catch (error) {
      console.error('模板下载失败:', error);
      toast({
        title: "下载失败",
        description: "模板下载失败，请重试",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl font-semibold tracking-tight">课程管理</h1>
        <div className="flex items-center gap-2">
          {selectedItems.length > 0 && (
            <div className="flex items-center gap-2 mr-2">
              <Badge variant="outline" className="px-2 py-1">
                已选择 {selectedItems.length} 项
              </Badge>
              <Button variant="outline" size="icon" onClick={() => handleBulkAction("delete")} title="批量删除">
                <Trash2 className="h-4 w-4 text-destructive" />
              </Button>
              <Button variant="outline" size="icon" onClick={() => handleBulkAction("export")} title="批量导出">
                <Download className="h-4 w-4" />
              </Button>
              <Separator orientation="vertical" className="h-6" />
            </div>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <SlidersHorizontal className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">显示列</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuCheckboxItem
                checked={visibleColumns.id}
                onCheckedChange={() => handleColumnVisibilityChange("id")}
              >
                课程编号
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.name}
                onCheckedChange={() => handleColumnVisibilityChange("name")}
                disabled
              >
                课程名称
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.type}
                onCheckedChange={() => handleColumnVisibilityChange("type")}
              >
                课程类型
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.defaultCoach}
                onCheckedChange={() => handleColumnVisibilityChange("defaultCoach")}
              >
                默认教练
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.defaultVenue}
                onCheckedChange={() => handleColumnVisibilityChange("defaultVenue")}
              >
                默认场地
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.price}
                onCheckedChange={() => handleColumnVisibilityChange("price")}
              >
                价格
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.duration}
                onCheckedChange={() => handleColumnVisibilityChange("duration")}
              >
                课程时长
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.level}
                onCheckedChange={() => handleColumnVisibilityChange("level")}
              >
                课程难度
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.capacity}
                onCheckedChange={() => handleColumnVisibilityChange("capacity")}
              >
                标准容量
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.schedules}
                onCheckedChange={() => handleColumnVisibilityChange("schedules")}
              >
                排期统计
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.bookings}
                onCheckedChange={() => handleColumnVisibilityChange("bookings")}
              >
                总预约
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.rating}
                onCheckedChange={() => handleColumnVisibilityChange("rating")}
              >
                平均评分
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.status}
                onCheckedChange={() => handleColumnVisibilityChange("status")}
              >
                状态
              </DropdownMenuCheckboxItem>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={visibleColumns.tags}
                onCheckedChange={() => handleColumnVisibilityChange("tags")}
              >
                课程标签
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.equipment}
                onCheckedChange={() => handleColumnVisibilityChange("equipment")}
              >
                器材要求
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={visibleColumns.notes}
                onCheckedChange={() => handleColumnVisibilityChange("notes")}
              >
                备注
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Sheet open={showFilters} onOpenChange={setShowFilters}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <Filter className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">高级筛选</span>
              </Button>
            </SheetTrigger>
            <SheetContent className="sm:max-w-md">
              <SheetHeader>
                <SheetTitle>高级筛选</SheetTitle>
                <SheetDescription>设置更精确的筛选条件来查找课程</SheetDescription>
              </SheetHeader>
              <div className="grid gap-4 py-4 max-h-[70vh] overflow-y-auto">
                {/* 价格范围 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">价格范围</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label htmlFor="price-min">最低价格</Label>
                      <Input
                        id="price-min"
                        placeholder="¥"
                        value={advancedFilters.priceMin}
                        onChange={(e) => handleAdvancedFilterChange("priceMin", e.target.value)}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="price-max">最高价格</Label>
                      <Input
                        id="price-max"
                        placeholder="¥"
                        value={advancedFilters.priceMax}
                        onChange={(e) => handleAdvancedFilterChange("priceMax", e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* 时长范围 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">时长范围（分钟）</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label htmlFor="duration-min">最短时长</Label>
                      <Input
                        id="duration-min"
                        placeholder="分钟"
                        value={advancedFilters.durationMin}
                        onChange={(e) => handleAdvancedFilterChange("durationMin", e.target.value)}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="duration-max">最长时长</Label>
                      <Input
                        id="duration-max"
                        placeholder="分钟"
                        value={advancedFilters.durationMax}
                        onChange={(e) => handleAdvancedFilterChange("durationMax", e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* 容量范围 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">容量范围</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label htmlFor="capacity-min">最小容量</Label>
                      <Input
                        id="capacity-min"
                        placeholder="人数"
                        value={advancedFilters.capacityMin}
                        onChange={(e) => handleAdvancedFilterChange("capacityMin", e.target.value)}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="capacity-max">最大容量</Label>
                      <Input
                        id="capacity-max"
                        placeholder="人数"
                        value={advancedFilters.capacityMax}
                        onChange={(e) => handleAdvancedFilterChange("capacityMax", e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* 课程难度 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">课程难度</h3>
                  <div className="grid grid-cols-5 gap-2">
                    {["1", "2", "3", "4", "5"].map((level) => (
                      <div key={level} className="flex items-center space-x-2">
                        <Checkbox
                          id={`level-${level}`}
                          checked={advancedFilters.level.includes(level)}
                          onCheckedChange={() => handleLevelChange(level)}
                        />
                        <Label htmlFor={`level-${level}`}>{level}星</Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 场馆选择 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">场馆选择</h3>
                  <Select
                    value={advancedFilters.venue || "all"}
                    onValueChange={(value) => handleAdvancedFilterChange("venue", value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="选择场馆" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部场馆</SelectItem>
                      {venues.length > 0 ? (
                        venues.map((venue) => (
                          <SelectItem key={venue.id} value={venue.id}>
                            {venue.name} {venue.capacity > 0 ? `(容量: ${venue.capacity}人)` : ''}
                          </SelectItem>
                        ))
                      ) : (
                        <>
                          <SelectItem value="1">1号瑜伽室</SelectItem>
                          <SelectItem value="2">2号瑜伽室</SelectItem>
                          <SelectItem value="3">3号瑜伽室</SelectItem>
                          <SelectItem value="4">4号瑜伽室</SelectItem>
                          <SelectItem value="5">私教室</SelectItem>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* 课程特性 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">课程特性</h3>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="requires-equipment">器材要求</Label>
                      <Select
                        value={advancedFilters.requiresEquipment}
                        onValueChange={(value) => handleAdvancedFilterChange("requiresEquipment", value)}
                      >
                        <SelectTrigger className="w-[120px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="true">需要器材</SelectItem>
                          <SelectItem value="false">无需器材</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="is-paid">付费课程</Label>
                      <Select
                        value={advancedFilters.isPaidCourse}
                        onValueChange={(value) => handleAdvancedFilterChange("isPaidCourse", value)}
                      >
                        <SelectTrigger className="w-[120px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="true">付费课程</SelectItem>
                          <SelectItem value="false">免费课程</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="is-private">私教课程</Label>
                      <Select
                        value={advancedFilters.isPrivate}
                        onValueChange={(value) => handleAdvancedFilterChange("isPrivate", value)}
                      >
                        <SelectTrigger className="w-[120px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="true">私教课</SelectItem>
                          <SelectItem value="false">团体课</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="allow-waitlist">候补功能</Label>
                      <Select
                        value={advancedFilters.allowWaitlist}
                        onValueChange={(value) => handleAdvancedFilterChange("allowWaitlist", value)}
                      >
                        <SelectTrigger className="w-[120px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部</SelectItem>
                          <SelectItem value="true">支持候补</SelectItem>
                          <SelectItem value="false">不支持候补</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* 课程标签 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">课程标签</h3>
                  <div className="grid grid-cols-3 gap-2">
                    {["初学者", "基础", "进阶", "放松", "力量", "冥想", "孕妇", "安全", "空中", "体验", "私教", "个性化"].map((tag) => (
                      <div key={tag} className="flex items-center space-x-2">
                        <Checkbox
                          id={`tag-${tag}`}
                          checked={advancedFilters.tags.includes(tag)}
                          onCheckedChange={() => handleTagChange(tag)}
                        />
                        <Label htmlFor={`tag-${tag}`} className="text-xs">{tag}</Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 预约数范围 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">预约数范围</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label htmlFor="bookings-min">最少预约</Label>
                      <Input
                        id="bookings-min"
                        placeholder="人数"
                        value={advancedFilters.bookingsMin}
                        onChange={(e) => handleAdvancedFilterChange("bookingsMin", e.target.value)}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label htmlFor="bookings-max">最多预约</Label>
                      <Input
                        id="bookings-max"
                        placeholder="人数"
                        value={advancedFilters.bookingsMax}
                        onChange={(e) => handleAdvancedFilterChange("bookingsMax", e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                {/* 评分筛选 */}
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">最低评分</h3>
                  <Select
                    value={advancedFilters.ratingMin}
                    onValueChange={(value) => handleAdvancedFilterChange("ratingMin", value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="选择最低评分" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">不限</SelectItem>
                      <SelectItem value="4.5">4.5分以上</SelectItem>
                      <SelectItem value="4.0">4.0分以上</SelectItem>
                      <SelectItem value="3.5">3.5分以上</SelectItem>
                      <SelectItem value="3.0">3.0分以上</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <SheetFooter>
                <Button variant="outline" onClick={resetAdvancedFilters}>
                  重置
                </Button>
                <SheetClose asChild>
                  <Button onClick={applyAdvancedFilters}>应用筛选</Button>
                </SheetClose>
              </SheetFooter>
            </SheetContent>
          </Sheet>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9">
                <FileDown className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">导入/导出</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setShowImportDialog(true)}>
                <FileUp className="mr-2 h-4 w-4" />
                导入课程
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setShowExportDialog(true)}>
                <FileDown className="mr-2 h-4 w-4" />
                导出所有课程
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleDownloadTemplate}>
                <Download className="mr-2 h-4 w-4" />
                下载导入模板
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button onClick={() => handleAddEditCourse(null)}>
            <Plus className="mr-2 h-4 w-4" />
            添加课程
          </Button>
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="w-full md:w-1/3">
              <div className="flex gap-2">
                <Input
                  placeholder="搜索课程名称、教练"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button type="button" variant="default" onClick={handleSearch} className="shrink-0">
                  搜索
                </Button>
              </div>
            </div>
            <div className="flex flex-1 gap-4">
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="课程类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  {courseTypeOptions.length > 0 ? (
                    courseTypeOptions.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))
                  ) : (
                    <>
                      <SelectItem value="basic">基础瑜伽</SelectItem>
                      <SelectItem value="advanced">进阶瑜伽</SelectItem>
                      <SelectItem value="yin">阴瑜伽</SelectItem>
                      <SelectItem value="prenatal">孕产瑜伽</SelectItem>
                      <SelectItem value="aerial">空中瑜伽</SelectItem>
                      <SelectItem value="private">私教课</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>

              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="课程状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">进行中</SelectItem>
                  <SelectItem value="upcoming">即将开始</SelectItem>
                  <SelectItem value="ended">已结束</SelectItem>
                  <SelectItem value="cancelled">已取消</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedCoach} onValueChange={setSelectedCoach}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="教练" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部教练</SelectItem>
                  {coaches.length > 0 ? (
                    coaches.map((coach) => (
                      <SelectItem key={coach.id} value={coach.id}>
                        {coach.name}
                      </SelectItem>
                    ))
                  ) : (
                    <>
                      <SelectItem value="1">张教练</SelectItem>
                      <SelectItem value="2">李教练</SelectItem>
                      <SelectItem value="3">王教练</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="mt-4 flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              共找到 <span className="font-medium">{total}</span> 个课程
              {(searchQuery || selectedType !== "all" || selectedStatus !== "all" || selectedCoach !== "all" ||
                advancedFilters.level.length > 0 || advancedFilters.tags.length > 0 ||
                advancedFilters.priceMin || advancedFilters.priceMax ||
                advancedFilters.capacityMin || advancedFilters.capacityMax ||
                advancedFilters.durationMin || advancedFilters.durationMax ||
                advancedFilters.bookingsMin || advancedFilters.bookingsMax ||
                advancedFilters.ratingMin !== "all" ||
                advancedFilters.venue !== "all" ||
                advancedFilters.requiresEquipment !== "all" ||
                advancedFilters.isPaidCourse !== "all" ||
                advancedFilters.isPrivate !== "all" ||
                advancedFilters.allowWaitlist !== "all") && (
                <Button variant="link" size="sm" className="ml-2 h-auto p-0" onClick={handleResetFilters}>
                  重置筛选
                </Button>
              )}
            </div>
            <div className="flex items-center gap-4">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="排序方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">最新添加</SelectItem>
                  <SelectItem value="name-asc">名称 (A-Z)</SelectItem>
                  <SelectItem value="name-desc">名称 (Z-A)</SelectItem>
                  <SelectItem value="price-asc">价格 (低-高)</SelectItem>
                  <SelectItem value="price-desc">价格 (高-低)</SelectItem>
                </SelectContent>
              </Select>

              <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "list" | "grid")}>
                <TabsList className="grid w-[120px] grid-cols-2">
                  <TabsTrigger value="list">列表</TabsTrigger>
                  <TabsTrigger value="grid">网格</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </CardContent>
      </Card>

      {viewMode === "list" ? (
        <CourseTable
          courses={courseTypes}
          loading={loading}
          total={total}
          page={page}
          pageSize={pageSize}
          visibleColumns={visibleColumns}
          onSelectedItemsChange={handleSelectedItemsChange}
          onEditCourse={handleAddEditCourse}
          onCopyCourse={handleCopyCourse}
          onDeleteCourse={handleDeleteCourse}
          onPageChange={setPage}
          onPageSizeChange={setPageSize}
        />
      ) : (
        <CourseGrid
          courses={courseTypes}
          loading={loading}
          onEditCourse={handleAddEditCourse}
          onCopyCourse={handleCopyCourse}
          onDeleteCourse={handleDeleteCourse}
        />
      )}

      {/* 添加/编辑课程对话框 */}
      <AddEditCourseDialog
        open={showAddEditDialog}
        onOpenChange={setShowAddEditDialog}
        course={courseToEdit}
        onSave={handleCourseSaved}
        existingCourses={courseTypes}
      />

      {/* 导入课程对话框 */}
      <ImportCoursesDialog
        open={showImportDialog}
        onOpenChange={setShowImportDialog}
        onImportSuccess={fetchCourses}
      />

      {/* 导出课程对话框 */}
      <ExportCoursesDialog
        open={showExportDialog}
        onOpenChange={setShowExportDialog}
        selectedCourseIds={selectedCourseIds}
        currentFilters={{
          keyword: debouncedSearchQuery,
          type: selectedType !== 'all' ? selectedType : undefined,
          status: selectedStatus !== 'all' ? selectedStatus : undefined,
          coachId: selectedCoach !== 'all' ? selectedCoach : undefined,
          page,
          pageSize,
          sortBy
        }}
      />

      {/* 添加删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>
              确定要删除选中的 {selectedItems.length} 个课程吗？此操作无法撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteSelected} disabled={isBulkDeleting}>
              {isBulkDeleting ? "删除中..." : "确认删除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

