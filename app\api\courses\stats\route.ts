import { NextRequest, NextResponse } from 'next/server';
import { courseService } from '@/services/course-service';

export async function GET(request: NextRequest) {
  try {
    const stats = courseService.getStats();
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: stats
    });
  } catch (error) {
    console.error('获取课程统计数据错误:', error);
    return NextResponse.json(
      { code: 500, msg: '获取数据失败', error: String(error) },
      { status: 500 }
    );
  }
} 