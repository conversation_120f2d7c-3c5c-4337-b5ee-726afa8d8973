// 为租户创建会员卡数据
const mysql = require('mysql2/promise');

async function createMemberCardData() {
  console.log('开始创建会员卡数据...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    // 租户1的会员卡数据
    const tenant1Cards = [
      {
        tenant_id: 1,
        name: '年卡',
        description: '365天不限次数',
        price: 3680.00,
        original_price: 4680.00,
        validity_days: 365,
        usage_limit: '不限次数',
        card_type: '年卡',
        status: '销售中',
        members_count: 128,
        sales_count: 245
      },
      {
        tenant_id: 1,
        name: '季卡',
        description: '90天不限次数',
        price: 1280.00,
        original_price: 1680.00,
        validity_days: 90,
        usage_limit: '不限次数',
        card_type: '季卡',
        status: '销售中',
        members_count: 86,
        sales_count: 156
      },
      {
        tenant_id: 1,
        name: '月卡',
        description: '30天不限次数',
        price: 580.00,
        original_price: 680.00,
        validity_days: 30,
        usage_limit: '不限次数',
        card_type: '月卡',
        status: '销售中',
        members_count: 112,
        sales_count: 324
      },
      {
        tenant_id: 1,
        name: '体验卡',
        description: '7天3次体验',
        price: 99.00,
        original_price: 199.00,
        validity_days: 7,
        usage_limit: '最多3次',
        card_type: '体验卡',
        status: '销售中',
        members_count: 56,
        sales_count: 412
      },
      {
        tenant_id: 1,
        name: '次卡10次',
        description: '10次课程',
        price: 880.00,
        original_price: 1080.00,
        validity_days: 180,
        usage_limit: '10次',
        card_type: '次卡',
        status: '销售中',
        members_count: 76,
        sales_count: 189
      },
      {
        tenant_id: 1,
        name: '次卡20次',
        description: '20次课程',
        price: 1580.00,
        original_price: 1880.00,
        validity_days: 365,
        usage_limit: '20次',
        card_type: '次卡',
        status: '已下架',
        members_count: 32,
        sales_count: 68
      },
      {
        tenant_id: 1,
        name: '私教卡',
        description: '一对一私教',
        price: 4880.00,
        original_price: 5880.00,
        validity_days: 180,
        usage_limit: '10次私教',
        card_type: '私教卡',
        status: '已下架',
        members_count: 18,
        sales_count: 42
      },
      {
        tenant_id: 1,
        name: '储值卡500',
        description: '储值500元',
        price: 450.00,
        original_price: 500.00,
        validity_days: 365,
        usage_limit: '储值500元',
        card_type: '储值卡',
        status: '销售中',
        members_count: 95,
        sales_count: 210
      },
      {
        tenant_id: 1,
        name: '储值卡1000',
        description: '储值1000元',
        price: 850.00,
        original_price: 1000.00,
        validity_days: 365,
        usage_limit: '储值1000元',
        card_type: '储值卡',
        status: '销售中',
        members_count: 78,
        sales_count: 180
      }
    ];

    // 租户2的会员卡数据
    const tenant2Cards = [
      {
        tenant_id: 2,
        name: '哈他年卡',
        description: '365天哈他瑜伽不限次数',
        price: 3200.00,
        original_price: 4200.00,
        validity_days: 365,
        usage_limit: '不限次数',
        card_type: '年卡',
        status: '销售中',
        members_count: 95,
        sales_count: 180
      },
      {
        tenant_id: 2,
        name: '流瑜伽季卡',
        description: '90天流瑜伽不限次数',
        price: 1180.00,
        original_price: 1480.00,
        validity_days: 90,
        usage_limit: '不限次数',
        card_type: '季卡',
        status: '销售中',
        members_count: 68,
        sales_count: 120
      },
      {
        tenant_id: 2,
        name: '阴瑜伽月卡',
        description: '30天阴瑜伽不限次数',
        price: 520.00,
        original_price: 620.00,
        validity_days: 30,
        usage_limit: '不限次数',
        card_type: '月卡',
        status: '销售中',
        members_count: 89,
        sales_count: 256
      },
      {
        tenant_id: 2,
        name: '空中瑜伽体验卡',
        description: '7天空中瑜伽3次体验',
        price: 128.00,
        original_price: 228.00,
        validity_days: 7,
        usage_limit: '最多3次',
        card_type: '体验卡',
        status: '销售中',
        members_count: 42,
        sales_count: 298
      },
      {
        tenant_id: 2,
        name: '孕产瑜伽次卡',
        description: '12次孕产瑜伽课程',
        price: 1200.00,
        original_price: 1500.00,
        validity_days: 180,
        usage_limit: '12次',
        card_type: '次卡',
        status: '销售中',
        members_count: 35,
        sales_count: 78
      }
    ];

    // 插入租户1的会员卡数据
    console.log('\n1. 创建租户1的会员卡数据:');
    for (const card of tenant1Cards) {
      const [result] = await connection.execute(
        `INSERT INTO membercard (tenant_id, name, description, price, original_price, validity_days, usage_limit, card_type, status, members_count, sales_count, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [card.tenant_id, card.name, card.description, card.price, card.original_price, card.validity_days, card.usage_limit, card.card_type, card.status, card.members_count, card.sales_count]
      );
      console.log(`✓ 创建会员卡成功: ${card.name} (ID: ${result.insertId})`);
    }

    // 插入租户2的会员卡数据
    console.log('\n2. 创建租户2的会员卡数据:');
    for (const card of tenant2Cards) {
      const [result] = await connection.execute(
        `INSERT INTO membercard (tenant_id, name, description, price, original_price, validity_days, usage_limit, card_type, status, members_count, sales_count, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [card.tenant_id, card.name, card.description, card.price, card.original_price, card.validity_days, card.usage_limit, card.card_type, card.status, card.members_count, card.sales_count]
      );
      console.log(`✓ 创建会员卡成功: ${card.name} (ID: ${result.insertId})`);
    }

    // 验证创建结果
    console.log('\n3. 验证创建结果:');
    
    const [tenant1Count] = await connection.execute(`SELECT COUNT(*) as count FROM membercard WHERE tenant_id = 1`);
    const [tenant2Count] = await connection.execute(`SELECT COUNT(*) as count FROM membercard WHERE tenant_id = 2`);
    
    console.log(`✓ 租户1会员卡数量: ${tenant1Count[0].count} 个`);
    console.log(`✓ 租户2会员卡数量: ${tenant2Count[0].count} 个`);

    await connection.end();
    console.log('\n✓ 会员卡数据创建完成!');
    
  } catch (error) {
    console.error('创建失败:', error.message);
  }
}

createMemberCardData();
