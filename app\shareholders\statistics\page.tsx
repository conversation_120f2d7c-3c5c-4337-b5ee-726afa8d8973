"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import {
  Download,
  TrendingUp,
  TrendingDown,
  Share2,
  Users,
  Wallet,
  CreditCard,
  BarChart3,
  PieChart,
  LineChart,
  Calendar,
  Filter,
  MoreHorizontal,
  Maximize2,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { ShareholderBreadcrumb } from "@/components/shareholders/shareholder-breadcrumb"
import { ShareholderNav } from "@/components/shareholders/shareholder-nav"

// 导入Recharts库
import {
  ResponsiveContainer,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  Tooltip,
  Legend,
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LineChart as RechartsLineChart,
  Line
} from 'recharts';

// 模拟数据 - 股东类型分布
const shareholderTypeData = [
  { name: '消费型股东', value: 28 },
  { name: '投资型股东', value: 12 },
  { name: '资源型股东', value: 15 },
  { name: '员工型股东', value: 8 },
  { name: '联盟型股东', value: 6 },
];

// 模拟数据 - 分红金额分布
const dividendData = [
  { name: '消费型股东', value: 42000 },
  { name: '投资型股东', value: 36000 },
  { name: '资源型股东', value: 28000 },
  { name: '员工型股东', value: 15000 },
  { name: '联盟型股东', value: 7500 },
];

// 模拟数据 - 引流客户趋势
const customerTrendData = [
  { month: '1月', customers: 120 },
  { month: '2月', customers: 150 },
  { month: '3月', customers: 180 },
  { month: '4月', customers: 220 },
  { month: '5月', customers: 280 },
  { month: '6月', customers: 350 },
];

// 模拟数据 - 类型分析
const typeAnalysisData = [
  { name: '消费型股东', 引流客户数: 280, 分红金额: 42000, 客户消费额: 840000 },
  { name: '投资型股东', 引流客户数: 120, 分红金额: 36000, 客户消费额: 360000 },
  { name: '资源型股东', 引流客户数: 300, 分红金额: 28000, 客户消费额: 560000 },
  { name: '员工型股东', 引流客户数: 160, 分红金额: 15000, 客户消费额: 320000 },
  { name: '联盟型股东', 引流客户数: 240, 分红金额: 7500, 客户消费额: 250000 },
];

// 模拟数据 - 个人业绩排行
const individualPerformanceData = [
  { name: '张三', type: '消费型股东', 引流客户数: 35, 分红金额: 5200 },
  { name: '李四', type: '投资型股东', 引流客户数: 0, 分红金额: 4800 },
  { name: '王五', type: '资源型股东', 引流客户数: 42, 分红金额: 4200 },
  { name: '赵六', type: '员工型股东', 引流客户数: 28, 分红金额: 3600 },
  { name: '美丽美容院', type: '联盟型股东', 引流客户数: 32, 分红金额: 3200 },
  { name: '刘七', type: '消费型股东', 引流客户数: 25, 分红金额: 2800 },
  { name: '孙八', type: '资源型股东', 引流客户数: 30, 分红金额: 2500 },
  { name: '周九', type: '消费型股东', 引流客户数: 22, 分红金额: 2200 },
  { name: '吴十', type: '员工型股东', 引流客户数: 18, 分红金额: 1800 },
  { name: '健身工作室', type: '联盟型股东', 引流客户数: 15, 分红金额: 1500 },
];

// 模拟数据 - 业绩趋势
const performanceTrendData = [
  { month: '1月', 引流客户数: 120, 分红金额: 12000, 客户消费额: 240000 },
  { month: '2月', 引流客户数: 150, 分红金额: 15000, 客户消费额: 300000 },
  { month: '3月', 引流客户数: 180, 分红金额: 18000, 客户消费额: 360000 },
  { month: '4月', 引流客户数: 220, 分红金额: 22000, 客户消费额: 440000 },
  { month: '5月', 引流客户数: 280, 分红金额: 28000, 客户消费额: 560000 },
  { month: '6月', 引流客户数: 350, 分红金额: 35000, 客户消费额: 700000 },
];

// 图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

export default function ShareholderStatisticsPage() {
  const [dateRange, setDateRange] = useState("30days")
  const [shareholderType, setShareholderType] = useState("all")
  const [date, setDate] = useState<Date>()
  const [viewMode, setViewMode] = useState("charts")

  return (
    <div className="space-y-6">
      <ShareholderBreadcrumb />
      <ShareholderNav />

      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">股东业绩统计</h1>
        <div className="flex gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                <Calendar className="mr-2 h-4 w-4" />
                {date ? format(date, "yyyy年MM月") : "选择月份"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <CalendarComponent
                mode="single"
                selected={date}
                onSelect={setDate}
              />
            </PopoverContent>
          </Popover>

          <Select value={shareholderType} onValueChange={setShareholderType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="股东类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="consumer">消费型股东</SelectItem>
              <SelectItem value="investor">投资型股东</SelectItem>
              <SelectItem value="resource">资源型股东</SelectItem>
              <SelectItem value="employee">员工型股东</SelectItem>
              <SelectItem value="alliance">联盟型股东</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报表
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">股东总数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">69</div>
            <div className="flex items-center justify-between">
              <p className="text-xs text-muted-foreground">
                较上月 <span className="text-green-500">+8.5%</span>
              </p>
              <Badge variant="outline">目标: 100</Badge>
            </div>
            <Progress value={69} className="mt-2 h-1.5" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">累计分红金额</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥128,430.00</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                较上月增长 12.5%
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">引流客户数</CardTitle>
            <Share2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,284</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                较上月增长 8.3%
              </span>
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">引流消费额</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥543,000.00</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-500 flex items-center">
                <TrendingUp className="mr-1 h-3 w-3" />
                较上月增长 15.2%
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">总体概览</TabsTrigger>
          <TabsTrigger value="type">类型分析</TabsTrigger>
          <TabsTrigger value="individual">个人业绩</TabsTrigger>
          <TabsTrigger value="trend">趋势分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader className="flex items-center justify-between">
                <div>
                  <CardTitle>股东分布</CardTitle>
                  <CardDescription>各类型股东数量分布</CardDescription>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Download className="mr-2 h-4 w-4" />
                      <span>导出数据</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Share2 className="mr-2 h-4 w-4" />
                      <span>分享图表</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Maximize2 className="mr-2 h-4 w-4" />
                      <span>全屏查看</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent>
                {/* 使用Recharts库实现实际的饼图 */}
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={shareholderTypeData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {shareholderTypeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex items-center justify-between">
                <div>
                  <CardTitle>分红金额分布</CardTitle>
                  <CardDescription>各类型股东分红金额分布</CardDescription>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>
                      <Download className="mr-2 h-4 w-4" />
                      <span>导出数据</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Share2 className="mr-2 h-4 w-4" />
                      <span>分享图表</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Maximize2 className="mr-2 h-4 w-4" />
                      <span>全屏查看</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardHeader>
              <CardContent>
                {/* 使用Recharts库实现实际的柱状图 */}
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsBarChart
                      data={dividendData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="value" fill="#8884d8" name="分红金额" />
                    </RechartsBarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader className="flex items-center justify-between">
              <div>
                <CardTitle>引流客户趋势</CardTitle>
                <CardDescription>各月份引流客户数量变化</CardDescription>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Download className="mr-2 h-4 w-4" />
                    <span>导出数据</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Share2 className="mr-2 h-4 w-4" />
                    <span>分享图表</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Maximize2 className="mr-2 h-4 w-4" />
                    <span>全屏查看</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardHeader>
            <CardContent>
              {/* 使用Recharts库实现实际的折线图 */}
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={customerTrendData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="customers"
                      stroke="#8884d8"
                      activeDot={{ r: 8 }}
                      name="引流客户数"
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="type" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>股东类型分析</CardTitle>
              <CardDescription>各类型股东的业绩对比分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart
                    data={typeAnalysisData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                    <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                    <Tooltip />
                    <Legend />
                    <Bar yAxisId="left" dataKey="引流客户数" fill="#8884d8" />
                    <Bar yAxisId="left" dataKey="分红金额" fill="#82ca9d" />
                    <Bar yAxisId="right" dataKey="客户消费额" fill="#ffc658" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="individual" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>个人业绩排行</CardTitle>
              <CardDescription>股东个人业绩排名</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsBarChart
                    data={individualPerformanceData}
                    layout="vertical"
                    margin={{
                      top: 20,
                      right: 30,
                      left: 100,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="引流客户数" fill="#8884d8" />
                    <Bar dataKey="分红金额" fill="#82ca9d" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trend" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>业绩趋势分析</CardTitle>
              <CardDescription>股东业绩随时间变化趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[500px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={performanceTrendData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" orientation="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="引流客户数"
                      stroke="#8884d8"
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="分红金额"
                      stroke="#82ca9d"
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="客户消费额"
                      stroke="#ffc658"
                      activeDot={{ r: 8 }}
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
