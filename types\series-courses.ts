// 系列课程类型定义

// 系列课程状态
export type SeriesCourseStatus = 'draft' | 'published' | 'completed';

// 系列课程排期状态
export type SeriesScheduleStatus = 'scheduled' | 'completed' | 'cancelled';

// 支付状态
export type PaymentStatus = 'pending' | 'paid' | 'refunded';

// 出勤状态
export type AttendanceStatus = 'booked' | 'attended' | 'absent' | 'cancelled';

// 系列课程
export interface SeriesCourse {
  id: string;
  name: string;                // 系列课程名称
  description: string;         // 课程描述
  courseType: string;          // 课程类型（瑜伽/普拉提等）
  totalSessions: number;       // 总课时数
  startDate: string;           // 开始日期
  endDate: string;             // 结束日期
  price: number;               // 价格
  maxStudents: number;         // 最大学员数
  instructor: string;          // 主讲教练
  instructorId: string;        // 主讲教练ID
  status: SeriesCourseStatus;  // 状态
  coverImage?: string;         // 封面图片
  tags?: string[];             // 标签
  enrolledStudents: number;    // 已报名学员数
}

// 系列课程排期
export interface SeriesSchedule {
  id: string;
  seriesCourseId: string;      // 所属系列课程ID
  sessionNumber: number;       // 第几节课
  title: string;               // 本节课标题
  date: string;                // 上课日期
  startTime: string;           // 开始时间
  endTime: string;             // 结束时间
  venue: string;               // 场地
  venueId: string;             // 场地ID
  instructor: string;          // 教练（可能与系列课程主讲不同）
  instructorId: string;        // 教练ID
  description?: string;        // 本节课描述
  status: SeriesScheduleStatus; // 状态
}

// 系列课程报名
export interface SeriesEnrollment {
  id: string;
  seriesCourseId: string;      // 系列课程ID
  memberId: string;            // 会员ID
  memberName: string;          // 会员姓名
  enrollDate: string;          // 报名日期
  paymentStatus: PaymentStatus; // 支付状态
  paymentAmount: number;       // 支付金额
  membershipCardId?: string;   // 使用的会员卡ID（如适用）
  membershipCardName?: string; // 使用的会员卡名称
  notes?: string;              // 备注
}

// 系列课程出勤记录
export interface SeriesAttendance {
  id: string;
  seriesScheduleId: string;    // 系列课程排期ID
  seriesCourseId: string;      // 系列课程ID
  memberId: string;            // 会员ID
  memberName: string;          // 会员姓名
  status: AttendanceStatus;    // 出勤状态
  checkInTime?: string;        // 签到时间
  notes?: string;              // 备注
}

// 系列课程自动约课结果
export interface AutoBookingResult {
  success: {
    memberId: string;
    memberName: string;
    scheduleId: string;
    sessionNumber: number;
  }[];
  failed: {
    memberId: string;
    memberName: string;
    scheduleId: string;
    sessionNumber: number;
    error: string;
  }[];
}

// 模拟API响应类型
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}
