"use client"

import { Di<PERSON>Footer } from "@/components/ui/dialog"

import { Checkbox } from "@/components/ui/checkbox"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Save,
  RefreshCw,
  MessageSquare,
  Mail,
  Bell,
  User,
  AlertCircle,
  History,
  Smartphone,
  Settings,
  Sliders,
  TestTube,
  Zap,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>ertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "@/hooks/use-toast"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Copy, FileText, Code, Wand2, Info, Search, Download, Filter, Eye, RotateCcw } from "lucide-react"
import { useState } from "react"
import React from "react"
import { CollapsibleSwitchSection } from "@/components/settings/collapsible-switch-section"

export default function NotificationsPage() {
  const handleSaveSettings = () => {
    toast({
      title: "设置已保存",
      description: "通知设置已成功更新",
    })
  }

  const handleTestNotification = () => {
    toast({
      title: "测试通知已发送",
      description: "请检查相应渠道是否收到测试通知",
    })
  }

  // 添加状态管理
  const [activeTemplates, setActiveTemplates] = React.useState({
    booking: true,
    reminder: true,
    expiry: true,
    birthday: true,
    feedback: true,
    marketing: true,
  })

  const [previewContent, setPreviewContent] = React.useState(
    `尊敬的张三，您已成功预约高级瑜伽课程，上课时间：2023-04-01 10:00，地点：A区瑜伽室，教练：李教练。期待您的到来！`,
  )

  const [templateContent, setTemplateContent] = React.useState(
    `尊敬的${"{{会员名称}}"}，您已成功预约${"{{课程名称}}"}，上课时间：${"{{上课时间}}"}，地点：${"{{场地}}"}，教练：${"{{教练}}"}。期待您的到来！`,
  )

  const updatePreview = (content) => {
    // 替换模板变量为示例值
    const preview = content
      .replace(/\{\{会员名称\}\}/g, "张三")
      .replace(/\{\{课程名称\}\}/g, "高级瑜伽课程")
      .replace(/\{\{上课时间\}\}/g, "2023-04-01 10:00")
      .replace(/\{\{场地\}\}/g, "A区瑜伽室")
      .replace(/\{\{教练\}\}/g, "李教练")
      .replace(/\{\{会员卡类型\}\}/g, "年卡")
      .replace(/\{\{到期日期\}\}/g, "2023-12-31")
      .replace(/\{\{反馈链接\}\}/g, "https://feedback.example.com/xyz")

    setPreviewContent(preview)
  }

  const handleTemplateChange = (e) => {
    const newContent = e.target.value
    setTemplateContent(newContent)
    updatePreview(newContent)
  }

  const [notificationSettings, setNotificationSettings] = React.useState({
    sms: true,
    email: true,
    wechat: true,
    app: true,
  })

  const toggleNotificationChannel = (channel) => {
    setNotificationSettings((prev) => ({
      ...prev,
      [channel]: !prev[channel],
    }))

    toast({
      title: `${channel.toUpperCase()} 通知已${notificationSettings[channel] ? "禁用" : "启用"}`,
      description: `您已成功${notificationSettings[channel] ? "禁用" : "启用"} ${channel.toUpperCase()} 通知渠道`,
    })
  }

  const [historyData] = React.useState([
    { id: 1, type: "预约通知", recipient: "张三", channel: "短信", status: "已送达", time: "2023-03-30 14:30" },
    { id: 2, type: "课程提醒", recipient: "李四", channel: "微信", status: "已送达", time: "2023-03-30 15:20" },
    { id: 3, type: "到期提醒", recipient: "王五", channel: "邮件", status: "已送达", time: "2023-03-30 16:45" },
    { id: 4, type: "生日祝福", recipient: "赵六", channel: "APP", status: "未送达", time: "2023-03-31 18:00" },
    { id: 5, type: "营销通知", recipient: "钱七", channel: "短信", status: "已送达", time: "2023-03-31 09:15" },
    { id: 6, type: "系统通知", recipient: "管理员", channel: "邮件", status: "已送达", time: "2023-03-31 10:30" },
    { id: 7, type: "预约通知", recipient: "孙八", channel: "微信", status: "已送达", time: "2023-03-31 11:45" },
    { id: 8, type: "课程提醒", recipient: "周九", channel: "APP", status: "未送达", time: "2023-03-31 13:00" },
  ])

  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [currentRole, setCurrentRole] = useState(null)
  const [addRoleDialogOpen, setAddRoleDialogOpen] = useState(false)
  const [roles] = useState([
    {
      id: "venue-admin",
      name: "场馆管理员",
      description: "管理场馆相关的所有功能，包括课程、会员、教练等",
      userCount: 3,
      permissions: {
        member: true,
        course: true,
        coach: true,
        revenue: true,
        report: true,
        system: true,
      },
    },
    {
      id: "course-admin",
      name: "课程管理员",
      description: "管理课程相关的功能，包括课程安排、类型设置等",
      userCount: 2,
      permissions: {
        member: false,
        course: true,
        coach: true,
        revenue: false,
        report: true,
        system: true,
      },
    },
    {
      id: "finance",
      name: "财务人员",
      description: "管理财务相关功能，包括订单、支付、对账等",
      userCount: 2,
      permissions: {
        member: false,
        course: false,
        coach: false,
        revenue: true,
        report: true,
        system: true,
      },
    },
  ])

  // 添加处理函数
  const handleOpenDetails = (role) => {
    setCurrentRole(role)
    setDetailsDialogOpen(true)
  }

  const handleAddRole = () => {
    setAddRoleDialogOpen(true)
  }

  const handleSaveRoleDetails = () => {
    toast({
      title: "角色通知设置已更新",
      description: `${currentRole.name}的通知设置已成功保存`,
    })
    setDetailsDialogOpen(false)
  }

  const handleCreateRole = () => {
    toast({
      title: "新角色已创建",
      description: "新角色通知设置已成功添加到系统",
    })
    setAddRoleDialogOpen(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">通知设置</h1>
          <p className="text-sm text-muted-foreground mt-1">配置系统通知的发送方式、内容和接收对象</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            重置设置
          </Button>
          <Button onClick={handleSaveSettings}>
            <Save className="mr-2 h-4 w-4" />
            保存设置
          </Button>
        </div>
      </div>

      <Tabs defaultValue="member" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="member" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            会员通知
          </TabsTrigger>
          <TabsTrigger value="admin" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            管理员通知
          </TabsTrigger>
          <TabsTrigger value="channel" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            通知渠道
          </TabsTrigger>
          <TabsTrigger value="template" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            模板管理
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            通知历史
          </TabsTrigger>
        </TabsList>

        <TabsContent value="member" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>会员通知设置</CardTitle>
              <CardDescription>配置发送给会员的通知</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <CollapsibleSwitchSection
                  id="booking-notification"
                  title="预约通知"
                  description="会员预约课程成功后发送通知"
                  defaultChecked={true}
                >
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="booking-template">预约通知模板</Label>
                      <Select defaultValue="default">
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="选择模板" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="default">默认模板</SelectItem>
                          <SelectItem value="simple">简洁模板</SelectItem>
                          <SelectItem value="detailed">详细模板</SelectItem>
                          <SelectItem value="custom">自定义模板</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Textarea
                      id="booking-template"
                      value={templateContent}
                      onChange={handleTemplateChange}
                      className="min-h-[100px]"
                    />
                    <p className="text-sm text-muted-foreground">
                      可用变量：{"{{会员名称}}"}、{"{{课程名称}}"}、{"{{上课时间}}"}、{"{{场地}}"}、{"{{教练}}"}
                    </p>
                  </div>

                  <div className="flex items-center justify-between mt-4">
                    <Label htmlFor="booking-channels">发送渠道</Label>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="booking-sms" defaultChecked />
                        <Label htmlFor="booking-sms">短信</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="booking-wechat" defaultChecked />
                        <Label htmlFor="booking-wechat">微信</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="booking-app" defaultChecked />
                        <Label htmlFor="booking-app">APP</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="booking-email" />
                        <Label htmlFor="booking-email">邮件</Label>
                      </div>
                    </div>
                  </div>
                </CollapsibleSwitchSection>

                <Separator className="my-4" />

                <CollapsibleSwitchSection
                  id="reminder-notification"
                  title="课程提醒"
                  description="课程开始前提醒会员"
                  defaultChecked={true}
                >
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="reminder-time">提前提醒时间（小时）</Label>
                      <div className="flex items-center space-x-2">
                        <Input id="reminder-time" type="number" defaultValue="2" />
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              多时间点
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>设置多个提醒时间点</DialogTitle>
                              <DialogDescription>可以设置多个时间点发送提醒，确保会员不会错过课程</DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                              <div className="flex items-center space-x-2">
                                <Checkbox id="reminder-24h" defaultChecked />
                                <Label htmlFor="reminder-24h">提前24小时</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Checkbox id="reminder-12h" />
                                <Label htmlFor="reminder-12h">提前12小时</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Checkbox id="reminder-2h" defaultChecked />
                                <Label htmlFor="reminder-2h">提前2小时</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Checkbox id="reminder-30m" defaultChecked />
                                <Label htmlFor="reminder-30m">提前30分钟</Label>
                              </div>
                              <div className="flex items-center space-x-4 mt-4">
                                <Label htmlFor="custom-reminder">自定义时间</Label>
                                <div className="flex items-center space-x-2">
                                  <Input id="custom-reminder" type="number" placeholder="小时" className="w-20" />
                                  <Button variant="secondary" size="sm">
                                    添加
                                  </Button>
                                </div>
                              </div>
                            </div>
                            <DialogFooter>
                              <Button type="submit">保存设置</Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="reminder-channel">提醒渠道</Label>
                      <Select defaultValue="all">
                        <SelectTrigger>
                          <SelectValue placeholder="选择提醒渠道" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">全部渠道</SelectItem>
                          <SelectItem value="sms">仅短信</SelectItem>
                          <SelectItem value="wechat">仅微信</SelectItem>
                          <SelectItem value="app">仅APP推送</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2 mt-4">
                    <Label htmlFor="reminder-template">课程提醒模板</Label>
                    <Textarea
                      id="reminder-template"
                      defaultValue={`尊敬的${"{{会员名称}}"}，温馨提醒您，您预约的${"{{课程名称}}"}将于${"{{上课时间}}"}开始，地点：${"{{场地}}"}，教练：${"{{教练}}"}。请提前到达，谢谢！`}
                      className="min-h-[100px]"
                    />
                  </div>
                </CollapsibleSwitchSection>

                <Separator className="my-4" />

                <CollapsibleSwitchSection
                  id="expiry-notification"
                  title="会员卡到期提醒"
                  description="会员卡到期前提醒会员"
                  defaultChecked={true}
                >
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="expiry-days">提前提醒天数</Label>
                      <Input id="expiry-days" type="number" defaultValue="7" />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="expiry-repeat">重复提醒</Label>
                      <Select defaultValue="once">
                        <SelectTrigger>
                          <SelectValue placeholder="选择重复提醒方式" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="once">仅提醒一次</SelectItem>
                          <SelectItem value="daily">每天提醒</SelectItem>
                          <SelectItem value="3days">每3天提醒</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2 mt-4">
                    <Label htmlFor="expiry-template">到期提醒模板</Label>
                    <Textarea
                      id="expiry-template"
                      defaultValue={`尊敬的${"{{会员名称}}"}，您的${"{{会员卡类型}}"}将于${"{{到期日期}}"}到期，为了不影响您的使用，请及时续费。`}
                      className="min-h-[100px]"
                    />
                  </div>
                </CollapsibleSwitchSection>

                <CollapsibleSwitchSection
                  id="birthday-notification"
                  title="生日祝福"
                  description="会员生日当天发送祝福"
                  defaultChecked={true}
                >
                  <div className="space-y-2">
                    <Label htmlFor="birthday-template">生日祝福模板</Label>
                    <Textarea
                      id="birthday-template"
                      defaultValue={`亲爱的${"{{会员名称}}"}，静心瑜伽馆全体员工祝您生日快乐！作为我们尊贵的会员，我们为您准备了生日专属优惠，详情请咨询前台。`}
                      className="min-h-[100px]"
                    />
                  </div>
                </CollapsibleSwitchSection>

                <Separator className="my-4" />

                <CollapsibleSwitchSection
                  id="feedback-notification"
                  title="课后反馈"
                  description="课程结束后请会员提供反馈"
                  defaultChecked={true}
                >
                  <div className="space-y-2">
                    <Label htmlFor="feedback-delay">课后发送延迟（小时）</Label>
                    <Input id="feedback-delay" type="number" defaultValue="2" />
                  </div>

                  <div className="space-y-2 mt-4">
                    <Label htmlFor="feedback-template">反馈请求模板</Label>
                    <Textarea
                      id="feedback-template"
                      defaultValue={`尊敬的${"{{会员名称}}"}，感谢您参加${"{{课程名称}}"}课程。我们非常重视您的体验和反馈，请点击链接完成课程评价：${"{{反馈链接}}"}。`}
                      className="min-h-[100px]"
                    />
                  </div>
                </CollapsibleSwitchSection>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <TestTube className="mr-2 h-4 w-4" />
                    测试通知
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>测试会员通知</DialogTitle>
                    <DialogDescription>选择通知类型和接收人，发送测试通知</DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="test-notification-type">通知类型</Label>
                      <Select defaultValue="booking">
                        <SelectTrigger>
                          <SelectValue placeholder="选择通知类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="booking">预约通知</SelectItem>
                          <SelectItem value="reminder">课程提醒</SelectItem>
                          <SelectItem value="expiry">到期提醒</SelectItem>
                          <SelectItem value="birthday">生日祝福</SelectItem>
                          <SelectItem value="feedback">课后反馈</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="test-recipient">接收人</Label>
                      <Select defaultValue="self">
                        <SelectTrigger>
                          <SelectValue placeholder="选择接收人" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="self">自己</SelectItem>
                          <SelectItem value="test">测试账号</SelectItem>
                          <SelectItem value="custom">自定义...</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="test-channel">发送渠道</Label>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="test-sms" defaultChecked />
                          <Label htmlFor="test-sms">短信</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="test-wechat" defaultChecked />
                          <Label htmlFor="test-wechat">微信</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="test-app" />
                          <Label htmlFor="test-app">APP</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="test-email" />
                          <Label htmlFor="test-email">邮件</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleTestNotification}>发送测试通知</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              <Button>
                <Save className="mr-2 h-4 w-4" />
                保存设置
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>会员营销通知</CardTitle>
              <CardDescription>配置发送给会员的营销和活动通知</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>营销通知提示</AlertTitle>
                <AlertDescription>
                  发送营销通知前，请确保已获得会员的营销通知授权，以符合相关法规要求。
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <CollapsibleSwitchSection
                  id="promotion-notification"
                  title="促销活动"
                  description="向会员发送促销活动信息"
                  defaultChecked={true}
                >
                  {/* 促销活动内容区域可以根据需要添加 */}
                </CollapsibleSwitchSection>

                <div className="mt-4"></div>
                
                <CollapsibleSwitchSection
                  id="new-course-notification"
                  title="新课程通知"
                  description="有新课程上线时通知会员"
                  defaultChecked={true}
                >
                  {/* 新课程通知内容区域可以根据需要添加 */}
                </CollapsibleSwitchSection>

                <div className="mt-4"></div>
                
                <CollapsibleSwitchSection
                  id="inactive-notification"
                  title="唤醒通知"
                  description="向长期未活跃会员发送唤醒通知"
                  defaultChecked={true}
                >
                  <div className="space-y-2">
                    <Label htmlFor="inactive-days">未活跃天数阈值</Label>
                    <Input id="inactive-days" type="number" defaultValue="30" />
                  </div>
                </CollapsibleSwitchSection>

                <div className="space-y-2">
                  <Label htmlFor="marketing-frequency">营销通知频率限制</Label>
                  <Select defaultValue="weekly">
                    <SelectTrigger>
                      <SelectValue placeholder="选择频率限制" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="daily">每日最多1条</SelectItem>
                      <SelectItem value="weekly">每周最多2条</SelectItem>
                      <SelectItem value="biweekly">每两周最多3条</SelectItem>
                      <SelectItem value="monthly">每月最多4条</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">限制向同一会员发送营销通知的频率，避免打扰会员</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="marketing-segment">会员细分</Label>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="segment-all" />
                      <Label htmlFor="segment-all">所有会员</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="segment-active" defaultChecked />
                      <Label htmlFor="segment-active">活跃会员</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="segment-new" defaultChecked />
                      <Label htmlFor="segment-new">新会员</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="segment-vip" defaultChecked />
                      <Label htmlFor="segment-vip">VIP会员</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="segment-expiring" defaultChecked />
                      <Label htmlFor="segment-expiring">即将到期会员</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="segment-custom" />
                      <Label htmlFor="segment-custom">自定义标签</Label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="marketing-schedule">定时发送</Label>
                    <Switch id="marketing-schedule" defaultChecked />
                  </div>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div className="space-y-2">
                      <Label htmlFor="marketing-time">发送时间</Label>
                      <Input id="marketing-time" type="time" defaultValue="10:00" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="marketing-days">发送日</Label>
                      <Select defaultValue="weekdays">
                        <SelectTrigger>
                          <SelectValue placeholder="选择发送日" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="everyday">每天</SelectItem>
                          <SelectItem value="weekdays">工作日</SelectItem>
                          <SelectItem value="weekends">周末</SelectItem>
                          <SelectItem value="custom">自定义</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button>
                <Save className="mr-2 h-4 w-4" />
                保存设置
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>智能通知策略</CardTitle>
              <CardDescription>根据会员行为和偏好自动调整通知策略</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="smart-notification">启用智能通知</Label>
                  <p className="text-sm text-muted-foreground">根据会员行为自动调整通知频率和内容</p>
                </div>
                <Switch id="smart-notification" defaultChecked />
              </div>

              <div className="space-y-2">
                <Label>智能策略选项</Label>
                <div className="grid grid-cols-1 gap-4 mt-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="smart-frequency" defaultChecked />
                    <Label htmlFor="smart-frequency">根据会员打开率调整通知频率</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="smart-channel" defaultChecked />
                    <Label htmlFor="smart-channel">根据会员响应率选择最佳通知渠道</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="smart-content" defaultChecked />
                    <Label htmlFor="smart-content">根据会员兴趣个性化通知内容</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="smart-time" defaultChecked />
                    <Label htmlFor="smart-time">根据会员活跃时间选择最佳发送时间</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="learning-period">学习周期（天）</Label>
                <Input id="learning-period" type="number" defaultValue="14" />
                <p className="text-sm text-muted-foreground">系统需要多少天的数据来学习会员偏好</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="optimization-frequency">优化频率</Label>
                <Select defaultValue="weekly">
                  <SelectTrigger>
                    <SelectValue placeholder="选择优化频率" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">每日</SelectItem>
                    <SelectItem value="weekly">每周</SelectItem>
                    <SelectItem value="biweekly">每两周</SelectItem>
                    <SelectItem value="monthly">每月</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">系统多久重新评估一次通知策略</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="admin" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>管理员通知设置</CardTitle>
              <CardDescription>配置发送给管理员的通知</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="new-member-notification">新会员通知</Label>
                    <p className="text-sm text-muted-foreground">有新会员注册时通知管理员</p>
                  </div>
                  <Switch id="new-member-notification" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="full-class-notification">满员通知</Label>
                    <p className="text-sm text-muted-foreground">课程预约满员时通知管理员</p>
                  </div>
                  <Switch id="full-class-notification" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="daily-report-notification">日报通知</Label>
                    <p className="text-sm text-muted-foreground">每日发送运营数据报告</p>
                  </div>
                  <Switch id="daily-report-notification" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="abnormal-notification">异常情况通知</Label>
                    <p className="text-sm text-muted-foreground">系统检测到异常情况时通知管理员</p>
                  </div>
                  <Switch id="abnormal-notification" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="system-notification">系统通知</Label>
                    <p className="text-sm text-muted-foreground">系统更新、维护等信息通知</p>
                  </div>
                  <Switch id="system-notification" defaultChecked />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="security-notification">安全通知</Label>
                    <p className="text-sm text-muted-foreground">安全相关事件通知</p>
                  </div>
                  <Switch id="security-notification" defaultChecked />
                </div>

                <Separator className="my-4" />

                <div className="space-y-2">
                  <Label htmlFor="admin-emails">管理员邮箱</Label>
                  <Input id="admin-emails" defaultValue="<EMAIL>, <EMAIL>" />
                  <p className="text-sm text-muted-foreground">多个邮箱请用逗号分隔</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="admin-phones">管理员手机</Label>
                  <Input id="admin-phones" defaultValue="13800138000, 13900139000" />
                  <p className="text-sm text-muted-foreground">多个手机号请用逗号分隔</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notification-level">通知级别</Label>
                  <Select defaultValue="all">
                    <SelectTrigger>
                      <SelectValue placeholder="选择通知级别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">所有通知</SelectItem>
                      <SelectItem value="important">仅重要通知</SelectItem>
                      <SelectItem value="critical">仅紧急通知</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">设置接收通知的级别阈值</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="report-time">日报发送时间</Label>
                  <Input id="report-time" type="time" defaultValue="09:00" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="report-content">日报内容设置</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="report-member" defaultChecked />
                      <Label htmlFor="report-member">会员数据</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="report-course" defaultChecked />
                      <Label htmlFor="report-course">课程数据</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="report-revenue" defaultChecked />
                      <Label htmlFor="report-revenue">收入数据</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="report-coach" defaultChecked />
                      <Label htmlFor="report-coach">教练数据</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="report-abnormal" defaultChecked />
                      <Label htmlFor="report-abnormal">异常情况</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="report-task" defaultChecked />
                      <Label htmlFor="report-task">待办任务</Label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="alert-thresholds">预警阈值设置</Label>
                  <div className="grid grid-cols-2 gap-4 mt-2">
                    <div className="space-y-2">
                      <Label htmlFor="revenue-threshold">日收入低于（元）</Label>
                      <Input id="revenue-threshold" type="number" defaultValue="1000" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="booking-threshold">日预约低于（人次）</Label>
                      <Input id="booking-threshold" type="number" defaultValue="10" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cancel-threshold">取消率高于（%）</Label>
                      <Input id="cancel-threshold" type="number" defaultValue="20" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="complaint-threshold">投诉数高于</Label>
                      <Input id="complaint-threshold" type="number" defaultValue="3" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <TestTube className="mr-2 h-4 w-4" />
                    测试管理员通知
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>测试管理员通知</DialogTitle>
                    <DialogDescription>选择通知类型和接收人，发送测试通知</DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="test-admin-notification-type">通知类型</Label>
                      <Select defaultValue="daily-report">
                        <SelectTrigger>
                          <SelectValue placeholder="选择通知类型" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="new-member">新会员通知</SelectItem>
                          <SelectItem value="full-class">满员通知</SelectItem>
                          <SelectItem value="daily-report">日报通知</SelectItem>
                          <SelectItem value="abnormal">异常情况通知</SelectItem>
                          <SelectItem value="security">安全通知</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="test-admin-recipient">接收人</Label>
                      <Select defaultValue="all">
                        <SelectTrigger>
                          <SelectValue placeholder="选择接收人" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">所有管理员</SelectItem>
                          <SelectItem value="self">仅自己</SelectItem>
                          <SelectItem value="custom">自定义...</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleTestNotification}>发送测试通知</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
              <Button>
                <Save className="mr-2 h-4 w-4" />
                保存设置
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>角色通知设置</CardTitle>
              <CardDescription>根据不同角色配置通知</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="rounded-md border p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium">场馆管理员</h3>
                    <Badge>3个用户</Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="venue-member" defaultChecked />
                      <Label htmlFor="venue-member">会员相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="venue-course" defaultChecked />
                      <Label htmlFor="venue-course">课程相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="venue-coach" defaultChecked />
                      <Label htmlFor="venue-coach">教练相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="venue-revenue" defaultChecked />
                      <Label htmlFor="venue-revenue">收入相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="venue-report" defaultChecked />
                      <Label htmlFor="venue-report">日报通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="venue-system" defaultChecked />
                      <Label htmlFor="venue-system">系统通知</Label>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button variant="outline" size="sm" onClick={() => handleOpenDetails(roles[0])}>
                      <Settings className="mr-2 h-4 w-4" />
                      详细设置
                    </Button>
                  </div>
                </div>

                <div className="rounded-md border p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium">课程管理员</h3>
                    <Badge>2个用户</Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="course-member" />
                      <Label htmlFor="course-member">会员相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="course-course" defaultChecked />
                      <Label htmlFor="course-course">课程相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="course-coach" defaultChecked />
                      <Label htmlFor="course-coach">教练相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="course-revenue" />
                      <Label htmlFor="course-revenue">收入相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="course-report" defaultChecked />
                      <Label htmlFor="course-report">日报通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="course-system" defaultChecked />
                      <Label htmlFor="course-system">系统通知</Label>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button variant="outline" size="sm" onClick={() => handleOpenDetails(roles[1])}>
                      <Settings className="mr-2 h-4 w-4" />
                      详细设置
                    </Button>
                  </div>
                </div>

                <div className="rounded-md border p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium">财务人员</h3>
                    <Badge>2个用户</Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="finance-member" />
                      <Label htmlFor="finance-member">会员相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="finance-course" />
                      <Label htmlFor="finance-course">课程相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="finance-coach" />
                      <Label htmlFor="finance-coach">教练相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="finance-revenue" defaultChecked />
                      <Label htmlFor="finance-revenue">收入相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="finance-report" defaultChecked />
                      <Label htmlFor="finance-report">日报通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="finance-system" defaultChecked />
                      <Label htmlFor="finance-system">系统通知</Label>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button variant="outline" size="sm" onClick={() => handleOpenDetails(roles[2])}>
                      <Settings className="mr-2 h-4 w-4" />
                      详细设置
                    </Button>
                  </div>
                </div>

                <div className="flex justify-center mt-4">
                  <Button variant="outline" onClick={handleAddRole}>
                    <Sliders className="mr-2 h-4 w-4" />
                    添加新角色通知设置
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 角色详细设置对话框 */}
          <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
            <DialogContent className="max-w-4xl">
              <DialogHeader>
                <DialogTitle>角色通知详细设置</DialogTitle>
                <DialogDescription>
                  {currentRole ? `配置 ${currentRole.name} 的详细通知设置` : "配置角色的详细通知设置"}
                </DialogDescription>
              </DialogHeader>
              {currentRole && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium">{currentRole.name}</h3>
                      <p className="text-sm text-muted-foreground">{currentRole.description}</p>
                    </div>
                    <Badge>{currentRole.userCount}个用户</Badge>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">通知类型设置</h4>
                    <div className="grid grid-cols-2 gap-x-12 gap-y-4">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label className="text-base">会员相关通知</Label>
                          <div className="ml-4 space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-member-new`}>新会员注册</Label>
                              <Switch
                                id={`${currentRole.id}-member-new`}
                                defaultChecked={currentRole.permissions.member}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-member-card`}>会员卡到期</Label>
                              <Switch
                                id={`${currentRole.id}-member-card`}
                                defaultChecked={currentRole.permissions.member}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-member-birthday`}>会员生日</Label>
                              <Switch
                                id={`${currentRole.id}-member-birthday`}
                                defaultChecked={currentRole.permissions.member}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-base">课程相关通知</Label>
                          <div className="ml-4 space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-course-new`}>新课程创建</Label>
                              <Switch
                                id={`${currentRole.id}-course-new`}
                                defaultChecked={currentRole.permissions.course}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-course-full`}>课程满员</Label>
                              <Switch
                                id={`${currentRole.id}-course-full`}
                                defaultChecked={currentRole.permissions.course}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-course-cancel`}>课程取消</Label>
                              <Switch
                                id={`${currentRole.id}-course-cancel`}
                                defaultChecked={currentRole.permissions.course}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-base">教练相关通知</Label>
                          <div className="ml-4 space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-coach-new`}>新教练加入</Label>
                              <Switch
                                id={`${currentRole.id}-coach-new`}
                                defaultChecked={currentRole.permissions.coach}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-coach-schedule`}>教练排班变更</Label>
                              <Switch
                                id={`${currentRole.id}-coach-schedule`}
                                defaultChecked={currentRole.permissions.coach}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-coach-review`}>教练收到评价</Label>
                              <Switch
                                id={`${currentRole.id}-coach-review`}
                                defaultChecked={currentRole.permissions.coach}
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label className="text-base">收入相关通知</Label>
                          <div className="ml-4 space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-revenue-new`}>新订单</Label>
                              <Switch
                                id={`${currentRole.id}-revenue-new`}
                                defaultChecked={currentRole.permissions.revenue}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-revenue-refund`}>退款申请</Label>
                              <Switch
                                id={`${currentRole.id}-revenue-refund`}
                                defaultChecked={currentRole.permissions.revenue}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-revenue-alert`}>收入异常预警</Label>
                              <Switch
                                id={`${currentRole.id}-revenue-alert`}
                                defaultChecked={currentRole.permissions.revenue}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-base">报表相关通知</Label>
                          <div className="ml-4 space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-report-daily`}>日报</Label>
                              <Switch
                                id={`${currentRole.id}-report-daily`}
                                defaultChecked={currentRole.permissions.report}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-report-weekly`}>周报</Label>
                              <Switch
                                id={`${currentRole.id}-report-weekly`}
                                defaultChecked={currentRole.permissions.report}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-report-monthly`}>月报</Label>
                              <Switch
                                id={`${currentRole.id}-report-monthly`}
                                defaultChecked={currentRole.permissions.report}
                              />
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-base">系统相关通知</Label>
                          <div className="ml-4 space-y-2">
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-system-update`}>系统更新</Label>
                              <Switch
                                id={`${currentRole.id}-system-update`}
                                defaultChecked={currentRole.permissions.system}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-system-security`}>安全事件</Label>
                              <Switch
                                id={`${currentRole.id}-system-security`}
                                defaultChecked={currentRole.permissions.system}
                              />
                            </div>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`${currentRole.id}-system-maintenance`}>系统维护</Label>
                              <Switch
                                id={`${currentRole.id}-system-maintenance`}
                                defaultChecked={currentRole.permissions.system}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">通知渠道设置</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`${currentRole.id}-channel-priority`}>优先通知渠道</Label>
                        <Select defaultValue="all">
                          <SelectTrigger>
                            <SelectValue placeholder="选择优先通知渠道" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">全部渠道</SelectItem>
                            <SelectItem value="sms">短信</SelectItem>
                            <SelectItem value="email">邮件</SelectItem>
                            <SelectItem value="wechat">微信</SelectItem>
                            <SelectItem value="app">APP推送</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`${currentRole.id}-notification-level`}>通知级别</Label>
                        <Select defaultValue="all">
                          <SelectTrigger>
                            <SelectValue placeholder="选择通知级别" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">所有通知</SelectItem>
                            <SelectItem value="important">仅重要通知</SelectItem>
                            <SelectItem value="critical">仅紧急通知</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>可用通知渠道</Label>
                      <div className="grid grid-cols-2 gap-4 mt-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id={`${currentRole.id}-channel-sms`} defaultChecked />
                          <Label htmlFor={`${currentRole.id}-channel-sms`}>短信</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id={`${currentRole.id}-channel-email`} defaultChecked />
                          <Label htmlFor={`${currentRole.id}-channel-email`}>邮件</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id={`${currentRole.id}-channel-wechat`} defaultChecked />
                          <Label htmlFor={`${currentRole.id}-channel-wechat`}>微信</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id={`${currentRole.id}-channel-app`} defaultChecked />
                          <Label htmlFor={`${currentRole.id}-channel-app`}>APP推送</Label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="font-medium">通知时间设置</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`${currentRole.id}-time-start`}>通知时间段开始</Label>
                        <Input id={`${currentRole.id}-time-start`} type="time" defaultValue="08:00" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor={`${currentRole.id}-time-end`}>通知时间段结束</Label>
                        <Input id={`${currentRole.id}-time-end`} type="time" defaultValue="20:00" />
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id={`${currentRole.id}-time-urgent`} defaultChecked />
                      <Label htmlFor={`${currentRole.id}-time-urgent`}>紧急通知不受时间限制</Label>
                    </div>
                  </div>
                </div>
              )}
              <DialogFooter>
                <Button variant="outline" onClick={() => setDetailsDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleSaveRoleDetails}>保存设置</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* 添加新角色通知设置对话框 */}
          <Dialog open={addRoleDialogOpen} onOpenChange={setAddRoleDialogOpen}>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>添加新角色通知设置</DialogTitle>
                <DialogDescription>创建新的角色通知设置，配置该角色可接收的通知类型和方式</DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-role-name">角色名称</Label>
                    <Input id="new-role-name" placeholder="输入角色名称" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-role-users">关联用户数</Label>
                    <Input id="new-role-users" type="number" defaultValue="0" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="new-role-description">角色描述</Label>
                  <Textarea id="new-role-description" placeholder="描述该角色的职责和权限范围" />
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">通知权限模板</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="border rounded-md p-3 flex items-center space-x-3">
                      <Checkbox id="template-venue-admin" />
                      <div>
                        <Label htmlFor="template-venue-admin">场馆管理员模板</Label>
                        <p className="text-xs text-muted-foreground">包含场馆管理相关的所有通知权限</p>
                      </div>
                    </div>
                    <div className="border rounded-md p-3 flex items-center space-x-3">
                      <Checkbox id="template-course-admin" />
                      <div>
                        <Label htmlFor="template-course-admin">课程管理员模板</Label>
                        <p className="text-xs text-muted-foreground">包含课程管理相关的所有通知权限</p>
                      </div>
                    </div>
                    <div className="border rounded-md p-3 flex items-center space-x-3">
                      <Checkbox id="template-finance" />
                      <div>
                        <Label htmlFor="template-finance">财务人员模板</Label>
                        <p className="text-xs text-muted-foreground">包含财务相关的所有通知权限</p>
                      </div>
                    </div>
                    <div className="border rounded-md p-3 flex items-center space-x-3">
                      <Checkbox id="template-reception" />
                      <div>
                        <Label htmlFor="template-reception">前台接待模板</Label>
                        <p className="text-xs text-muted-foreground">包含前台日常工作所需的通知权限</p>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">通知类型设置</h4>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="select-all-notifications" />
                      <Label htmlFor="select-all-notifications">全选</Label>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="new-role-member" />
                      <Label htmlFor="new-role-member">会员相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="new-role-course" />
                      <Label htmlFor="new-role-course">课程相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="new-role-coach" />
                      <Label htmlFor="new-role-coach">教练相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="new-role-revenue" />
                      <Label htmlFor="new-role-revenue">收入相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="new-role-report" />
                      <Label htmlFor="new-role-report">报表相关通知</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="new-role-system" />
                      <Label htmlFor="new-role-system">系统相关通知</Label>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox id="customize-later" defaultChecked />
                  <Label htmlFor="customize-later">创建后进一步自定义详细设置</Label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setAddRoleDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleCreateRole}>创建角色</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </TabsContent>

        <TabsContent value="channel" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>通知渠道设置</CardTitle>
              <CardDescription>配置系统通知的发送渠道</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-3 mb-4">
                    <MessageSquare className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">短信通知</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="sms-channel">启用短信通知</Label>
                        <p className="text-sm text-muted-foreground">通过短信发送通知</p>
                      </div>
                      <Switch
                        id="sms-channel"
                        checked={notificationSettings.sms}
                        onClick={() => toggleNotificationChannel("sms")}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="sms-provider">短信服务提供商</Label>
                      <Select defaultValue="aliyun">
                        <SelectTrigger>
                          <SelectValue placeholder="选择短信服务提供商" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="aliyun">阿里云短信</SelectItem>
                          <SelectItem value="tencent">腾讯云短信</SelectItem>
                          <SelectItem value="netease">网易云信</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="sms-key">AccessKey</Label>
                        <Input id="sms-key" defaultValue="LTAIxxxxxxxxxxxxxxxx" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="sms-secret">AccessSecret</Label>
                        <Input id="sms-secret" type="password" defaultValue="********************************" />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="sms-sign">短信签名</Label>
                      <Input id="sms-sign" defaultValue="静心瑜伽馆" />
                    </div>

                    <div className="space-y-2">
                      <Label>适用通知类型</Label>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="sms-booking" defaultChecked />
                          <Label htmlFor="sms-booking">预约通知</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="sms-reminder" defaultChecked />
                          <Label htmlFor="sms-reminder">课程提醒</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="sms-expiry" defaultChecked />
                          <Label htmlFor="sms-expiry">到期提醒</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="sms-birthday" defaultChecked />
                          <Label htmlFor="sms-birthday">生日祝福</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="sms-marketing" />
                          <Label htmlFor="sms-marketing">营销通知</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="sms-admin" defaultChecked />
                          <Label htmlFor="sms-admin">管理员通知</Label>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="sms-quota">短信配额</Label>
                      <div className="flex items-center space-x-4">
                        <Input id="sms-quota" type="number" defaultValue="5000" />
                        <Button variant="outline" size="sm">
                          充值
                        </Button>
                      </div>
                      <p className="text-sm text-muted-foreground">当月剩余短信配额：3254条</p>
                    </div>

                    <div className="flex justify-end">
                      <Button variant="outline" size="sm" onClick={handleTestNotification}>
                        <TestTube className="mr-2 h-4 w-4" />
                        测试短信
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-3 mb-4">
                    <Mail className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">邮件通知</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="email-channel">启用邮件通知</Label>
                        <p className="text-sm text-muted-foreground">通过邮件发送通知</p>
                      </div>
                      <Switch
                        id="email-channel"
                        checked={notificationSettings.email}
                        onClick={() => toggleNotificationChannel("email")}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="smtp-server">SMTP服务器</Label>
                        <Input id="smtp-server" defaultValue="smtp.example.com" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="smtp-port">SMTP端口</Label>
                        <Input id="smtp-port" defaultValue="587" />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="smtp-user">SMTP用户名</Label>
                        <Input id="smtp-user" defaultValue="<EMAIL>" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="smtp-password">SMTP密码</Label>
                        <Input id="smtp-password" type="password" defaultValue="********************************" />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email-from">发件人</Label>
                      <Input id="email-from" defaultValue="静心瑜伽馆 <<EMAIL>>" />
                    </div>

                    <div className="space-y-2">
                      <Label>适用通知类型</Label>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="email-booking" defaultChecked />
                          <Label htmlFor="email-booking">预约通知</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="email-reminder" defaultChecked />
                          <Label htmlFor="email-reminder">课程提醒</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="email-expiry" defaultChecked />
                          <Label htmlFor="email-expiry">到期提醒</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="email-birthday" defaultChecked />
                          <Label htmlFor="email-birthday">生日祝福</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="email-marketing" defaultChecked />
                          <Label htmlFor="email-marketing">营销通知</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="email-admin" defaultChecked />
                          <Label htmlFor="email-admin">管理员通知</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="email-report" defaultChecked />
                          <Label htmlFor="email-report">报表通知</Label>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button variant="outline" size="sm" onClick={handleTestNotification}>
                        <TestTube className="mr-2 h-4 w-4" />
                        测试邮件
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-3 mb-4">
                    <Smartphone className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">微信通知</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="wechat-channel">启用微信通知</Label>
                        <p className="text-sm text-muted-foreground">通过微信公众号/小程序发送通知</p>
                      </div>
                      <Switch
                        id="wechat-channel"
                        checked={notificationSettings.wechat}
                        onClick={() => toggleNotificationChannel("wechat")}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="wechat-appid">AppID</Label>
                        <Input id="wechat-appid" defaultValue="wx1234567890abcdef" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="wechat-secret">AppSecret</Label>
                        <Input id="wechat-secret" type="password" defaultValue="********************************" />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="wechat-type">微信账号类型</Label>
                      <RadioGroup defaultValue="mp" className="flex space-x-4">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="mp" id="mp" />
                          <Label htmlFor="mp">公众号</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="miniapp" id="miniapp" />
                          <Label htmlFor="miniapp">小程序</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="both" id="both" />
                          <Label htmlFor="both">两者都有</Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="wechat-templates">消息模板ID</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Label htmlFor="wechat-booking-template" className="w-24">
                            预约通知:
                          </Label>
                          <Input id="wechat-booking-template" defaultValue="TM00001" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Label htmlFor="wechat-reminder-template" className="w-24">
                            课程提醒:
                          </Label>
                          <Input id="wechat-reminder-template" defaultValue="TM00002" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Label htmlFor="wechat-expiry-template" className="w-24">
                            到期提醒:
                          </Label>
                          <Input id="wechat-expiry-template" defaultValue="TM00003" />
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button variant="outline" size="sm" onClick={handleTestNotification}>
                        <TestTube className="mr-2 h-4 w-4" />
                        测试微信通知
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="rounded-md border p-4">
                  <div className="flex items-center gap-3 mb-4">
                    <Zap className="h-5 w-5 text-primary" />
                    <h3 className="text-lg font-medium">APP推送</h3>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="app-channel">启用APP推送</Label>
                        <p className="text-sm text-muted-foreground">通过移动应用推送通知</p>
                      </div>
                      <Switch
                        id="app-channel"
                        checked={notificationSettings.app}
                        onClick={() => toggleNotificationChannel("app")}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="push-provider">推送服务提供商</Label>
                      <Select defaultValue="jpush">
                        <SelectTrigger>
                          <SelectValue placeholder="选择推送服务提供商" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="jpush">极光推送</SelectItem>
                          <SelectItem value="getui">个推</SelectItem>
                          <SelectItem value="umeng">友盟推送</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="push-key">AppKey</Label>
                        <Input id="push-key" defaultValue="1234567890abcdef" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="push-secret">Master Secret</Label>
                        <Input id="push-secret" type="password" defaultValue="********************************" />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>适用通知类型</Label>
                      <div className="grid grid-cols-2 gap-2 mt-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="app-booking" defaultChecked />
                          <Label htmlFor="app-booking">预约通知</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="app-reminder" defaultChecked />
                          <Label htmlFor="app-reminder">课程提醒</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="app-expiry" defaultChecked />
                          <Label htmlFor="app-expiry">到期提醒</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="app-birthday" defaultChecked />
                          <Label htmlFor="app-birthday">生日祝福</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="app-marketing" defaultChecked />
                          <Label htmlFor="app-marketing">营销通知</Label>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="push-sound">推送声音</Label>
                      <Select defaultValue="default">
                        <SelectTrigger>
                          <SelectValue placeholder="选择推送声音" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="default">默认声音</SelectItem>
                          <SelectItem value="gentle">温和提示音</SelectItem>
                          <SelectItem value="bell">铃声</SelectItem>
                          <SelectItem value="none">无声音</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex justify-end">
                      <Button variant="outline" size="sm" onClick={handleTestNotification}>
                        <TestTube className="mr-2 h-4 w-4" />
                        测试APP推送
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="flex justify-center mt-4">
                  <Button variant="outline">
                    <Sliders className="mr-2 h-4 w-4" />
                    添加新通知渠道
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="template" className="space-y-4">
          <Card>
            <CardHeader className="space-y-1">
              <div className="flex items-center justify-between">
                <CardTitle>通知模板管理</CardTitle>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>
                      <FileText className="mr-2 h-4 w-4" />
                      新建模板
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl">
                    <DialogHeader>
                      <DialogTitle>创建新模板</DialogTitle>
                      <DialogDescription>创建一个新的通知模板，设置模板内容和适用场景</DialogDescription>
                    </DialogHeader>
                    <div className="grid grid-cols-5 gap-6">
                      <div className="col-span-2 space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="template-name">模板名称</Label>
                          <Input id="template-name" placeholder="输入模板名称" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="template-category">模板分类</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="选择模板分类" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="booking">预约通知</SelectItem>
                              <SelectItem value="reminder">课程提醒</SelectItem>
                              <SelectItem value="expiry">到期提醒</SelectItem>
                              <SelectItem value="birthday">生日祝福</SelectItem>
                              <SelectItem value="marketing">营销通知</SelectItem>
                              <SelectItem value="admin">管理员通知</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>支持渠道</Label>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex items-center space-x-2">
                              <Checkbox id="channel-sms" defaultChecked />
                              <Label htmlFor="channel-sms">短信</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox id="channel-wechat" defaultChecked />
                              <Label htmlFor="channel-wechat">微信</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox id="channel-app" defaultChecked />
                              <Label htmlFor="channel-app">APP</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox id="channel-email" defaultChecked />
                              <Label htmlFor="channel-email">邮件</Label>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>可用变量</Label>
                          <div className="rounded-md border p-3 h-[300px] overflow-y-auto">
                            <Accordion type="multiple" className="w-full">
                              <AccordionItem value="common">
                                <AccordionTrigger>通用变量</AccordionTrigger>
                                <AccordionContent>
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7"
                                          onClick={() => toast({ description: "变量已复制到剪贴板" })}
                                        >
                                          <Copy className="h-3.5 w-3.5 mr-1" />
                                          复制
                                        </Button>
                                        <span className="text-sm font-medium">{"{{会员名称}}"}</span>
                                      </div>
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button variant="ghost" size="sm" className="h-7">
                                              <Info className="h-3.5 w-3.5" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>会员的姓名，例如：张三</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7"
                                          onClick={() => toast({ description: "变量已复制到剪贴板" })}
                                        >
                                          <Copy className="h-3.5 w-3.5 mr-1" />
                                          复制
                                        </Button>
                                        <span className="text-sm font-medium">{"{{当前日期}}"}</span>
                                      </div>
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button variant="ghost" size="sm" className="h-7">
                                              <Info className="h-3.5 w-3.5" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>当前日期，例如：2023-04-01</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                  </div>
                                </AccordionContent>
                              </AccordionItem>
                              <AccordionItem value="course">
                                <AccordionTrigger>课程相关变量</AccordionTrigger>
                                <AccordionContent>
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7"
                                          onClick={() => toast({ description: "变量已复制到剪贴板" })}
                                        >
                                          <Copy className="h-3.5 w-3.5 mr-1" />
                                          复制
                                        </Button>
                                        <span className="text-sm font-medium">{"{{课程名称}}"}</span>
                                      </div>
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button variant="ghost" size="sm" className="h-7">
                                              <Info className="h-3.5 w-3.5" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>课程的名称，例如：高级瑜伽</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7"
                                          onClick={() => toast({ description: "变量已复制到剪贴板" })}
                                        >
                                          <Copy className="h-3.5 w-3.5 mr-1" />
                                          复制
                                        </Button>
                                        <span className="text-sm font-medium">{"{{上课时间}}"}</span>
                                      </div>
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button variant="ghost" size="sm" className="h-7">
                                              <Info className="h-3.5 w-3.5" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>课程的开始时间，例如：2023-04-01 10:00</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                  </div>
                                </AccordionContent>
                              </AccordionItem>
                              <AccordionItem value="member">
                                <AccordionTrigger>会员相关变量</AccordionTrigger>
                                <AccordionContent>
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7"
                                          onClick={() => toast({ description: "变量已复制到剪贴板" })}
                                        >
                                          <Copy className="h-3.5 w-3.5 mr-1" />
                                          复制
                                        </Button>
                                        <span className="text-sm font-medium">{"{{会员卡类型}}"}</span>
                                      </div>
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button variant="ghost" size="sm" className="h-7">
                                              <Info className="h-3.5 w-3.5" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>会员卡的类型，例如：年卡</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center space-x-2">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-7"
                                          onClick={() => toast({ description: "变量已复制到剪贴板" })}
                                        >
                                          <Copy className="h-3.5 w-3.5 mr-1" />
                                          复制
                                        </Button>
                                        <span className="text-sm font-medium">{"{{到期日期}}"}</span>
                                      </div>
                                      <TooltipProvider>
                                        <Tooltip>
                                          <TooltipTrigger asChild>
                                            <Button variant="ghost" size="sm" className="h-7">
                                              <Info className="h-3.5 w-3.5" />
                                            </Button>
                                          </TooltipTrigger>
                                          <TooltipContent>
                                            <p>会员卡的到期日期，例如：2023-12-31</p>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    </div>
                                  </div>
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          </div>
                        </div>
                      </div>
                      <div className="col-span-3 space-y-4">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="template-content">模板内容</Label>
                            <div className="flex items-center space-x-2">
                              <Button variant="outline" size="sm">
                                <Wand2 className="mr-2 h-4 w-4" />
                                AI 辅助编写
                              </Button>
                              <Button variant="outline" size="sm">
                                <Code className="mr-2 h-4 w-4" />
                                高级编辑
                              </Button>
                            </div>
                          </div>
                          <Textarea
                            id="template-content"
                            placeholder="输入模板内容，可以使用左侧的变量"
                            className="min-h-[200px]"
                            value={templateContent}
                            onChange={handleTemplateChange}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>模板预览</Label>
                          <div className="rounded-md border p-4 bg-gray-50">
                            <p>{previewContent}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button type="submit">保存模板</Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <CardDescription>管理和自定义通知模板</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="rounded-md border p-4">
                  <h3 className="text-lg font-medium">预约成功通知</h3>
                  <p className="text-sm text-muted-foreground mt-1">用于会员预约课程成功后发送的通知</p>
                  <div className="mt-4 space-x-2">
                    <Badge variant="secondary">短信</Badge>
                    <Badge variant="secondary">微信</Badge>
                    <Badge variant="secondary">APP</Badge>
                  </div>
                  <div className="flex justify-end mt-4">
                    <Button variant="outline" size="sm">
                      编辑
                    </Button>
                  </div>
                </div>

                <div className="rounded-md border p-4">
                  <h3 className="text-lg font-medium">课程开始提醒</h3>
                  <p className="text-sm text-muted-foreground mt-1">在课程开始前一段时间提醒会员</p>
                  <div className="mt-4 space-x-2">
                    <Badge variant="secondary">短信</Badge>
                    <Badge variant="secondary">微信</Badge>
                    <Badge variant="secondary">APP</Badge>
                  </div>
                  <div className="flex justify-end mt-4">
                    <Button variant="outline" size="sm">
                      编辑
                    </Button>
                  </div>
                </div>

                <div className="rounded-md border p-4">
                  <h3 className="text-lg font-medium">会员卡到期提醒</h3>
                  <p className="text-sm text-muted-foreground mt-1">在会员卡到期前提醒会员续费</p>
                  <div className="mt-4 space-x-2">
                    <Badge variant="secondary">短信</Badge>
                    <Badge variant="secondary">微信</Badge>
                    <Badge variant="secondary">邮件</Badge>
                  </div>
                  <div className="flex justify-end mt-4">
                    <Button variant="outline" size="sm">
                      编辑
                    </Button>
                  </div>
                </div>
              </div>
              <div className="flex justify-center mt-4">
                <Button variant="outline">
                  <Sliders className="mr-2 h-4 w-4" />
                  添加新模板
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>通知历史记录</CardTitle>
                  <CardDescription>查看已发送的通知记录和状态</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="relative w-[300px]">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input type="search" placeholder="搜索通知记录..." className="pl-8" />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    筛选
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    导出
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="grid grid-cols-6 border-b bg-muted/50 px-4 py-2.5 text-sm font-medium">
                  <div>通知类型</div>
                  <div>接收人</div>
                  <div>发送渠道</div>
                  <div>状态</div>
                  <div>发送时间</div>
                  <div className="text-right">操作</div>
                </div>
                {historyData.map((record) => (
                  <div key={record.id} className="grid grid-cols-6 items-center px-4 py-3 text-sm">
                    <div>{record.type}</div>
                    <div>{record.recipient}</div>
                    <div>{record.channel}</div>
                    <div>
                      <div className="flex items-center gap-2">
                        <div
                          className={`h-2 w-2 rounded-full ${record.status === "已送达" ? "bg-green-500" : "bg-red-500"}`}
                        ></div>
                        {record.status}
                      </div>
                    </div>
                    <div>{record.time}</div>
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <RotateCcw className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  显示 <strong>1-8</strong> 条，共 <strong>24</strong> 条
                </div>
                <div className="flex items-center gap-1">
                  <Button variant="outline" size="sm" disabled>
                    上一页
                  </Button>
                  <Button variant="outline" size="sm" className="bg-primary text-primary-foreground">
                    1
                  </Button>
                  <Button variant="outline" size="sm">
                    2
                  </Button>
                  <Button variant="outline" size="sm">
                    3
                  </Button>
                  <Button variant="outline" size="sm">
                    下一页
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

