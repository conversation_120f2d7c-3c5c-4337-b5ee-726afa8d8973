// 测试颜色选择器功能
async function testColorPicker() {
  console.log('开始测试颜色选择器功能...');
  
  try {
    // 1. 创建一个新的课程类型，测试不同颜色
    const testColors = [
      { name: '测试蓝色', color: '#4285F4' },
      { name: '测试绿色', color: '#34A853' },
      { name: '测试黄色', color: '#FBBC05' },
      { name: '测试红色', color: '#EA4335' },
      { name: '测试紫色', color: '#9C27B0' },
      { name: '测试粉色', color: '#FF6D91' },
      { name: '测试青色', color: '#00BCD4' },
      { name: '测试橙色', color: '#FF5722' }
    ];
    
    console.log('\n创建测试课程类型:');
    
    for (const testType of testColors) {
      const response = await fetch('http://localhost:3005/api/course-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId: 1,
          name: testType.name,
          description: `测试${testType.color}颜色的课程类型`,
          color: testType.color,
          displayOrder: Math.floor(Math.random() * 100),
          status: 'active'
        })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        console.log(`✓ 创建成功: ${testType.name} (${testType.color})`);
      } else {
        console.log(`✗ 创建失败: ${testType.name} - ${result.msg}`);
      }
    }
    
    // 2. 获取所有课程类型，验证颜色
    console.log('\n验证颜色设置:');
    const response = await fetch('http://localhost:3005/api/course-types?tenantId=1&sortBy=name');
    const result = await response.json();
    
    if (result.code === 200) {
      console.log('所有课程类型的颜色:');
      result.data.list.forEach(type => {
        console.log(`  ${type.name}: ${type.color}`);
      });
    }
    
    // 3. 测试自定义颜色
    console.log('\n测试自定义颜色:');
    const customColors = [
      { name: '自定义深蓝', color: '#1a237e' },
      { name: '自定义深绿', color: '#1b5e20' },
      { name: '自定义深红', color: '#b71c1c' }
    ];
    
    for (const customType of customColors) {
      const response = await fetch('http://localhost:3005/api/course-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId: 1,
          name: customType.name,
          description: `测试自定义颜色${customType.color}`,
          color: customType.color,
          displayOrder: Math.floor(Math.random() * 100),
          status: 'active'
        })
      });
      
      const result = await response.json();
      
      if (result.code === 200) {
        console.log(`✓ 自定义颜色创建成功: ${customType.name} (${customType.color})`);
      } else {
        console.log(`✗ 自定义颜色创建失败: ${customType.name} - ${result.msg}`);
      }
    }
    
    console.log('\n✓ 颜色选择器功能测试完成!');
    console.log('请在浏览器中访问课程类型页面查看颜色效果。');
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

testColorPicker();
