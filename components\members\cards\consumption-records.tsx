"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePickerWithRange as DateRangePicker } from "@/components/ui/date-range-picker"
import { Button } from "@/components/ui/button"
import { Download, Filter } from "lucide-react"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"

interface ConsumptionRecord {
  id: string | number
  course_name: string
  course_type_name: string
  course_type_color?: string
  attendance_date: string
  consumed_value: number
  is_bonus: boolean
  calculation_method: string
}

interface ConsumptionRecordsProps {
  userId?: string | number
  membershipId?: string | number
}

export function ConsumptionRecords({ userId, membershipId }: ConsumptionRecordsProps) {
  // 分页状态
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 10,
    total: 0
  })

  // 筛选状态
  const [filters, setFilters] = useState({
    courseType: "all",
    dateRange: {
      from: undefined,
      to: undefined
    }
  })

  // 消费记录数据
  const [records, setRecords] = useState<ConsumptionRecord[]>([])

  // 课程类型数据
  const [courseTypes, setCourseTypes] = useState([
    { id: "all", name: "全部类型" },
    { id: "1", name: "基础瑜伽", color: "#4285F4" },
    { id: "2", name: "高级瑜伽", color: "#34A853" },
    { id: "3", name: "阴瑜伽", color: "#FBBC05" },
    { id: "4", name: "孕产瑜伽", color: "#EA4335" },
    { id: "5", name: "空中瑜伽", color: "#FF6D91" },
  ])

  // 模拟数据
  const mockRecords: ConsumptionRecord[] = [
    {
      id: 1,
      course_name: "基础瑜伽入门",
      course_type_name: "基础瑜伽",
      course_type_color: "#4285F4",
      attendance_date: "2023-11-15 10:00",
      consumed_value: 120,
      is_bonus: false,
      calculation_method: "平均消耗法"
    },
    {
      id: 2,
      course_name: "高级体式练习",
      course_type_name: "高级瑜伽",
      course_type_color: "#34A853",
      attendance_date: "2023-11-18 15:30",
      consumed_value: 180,
      is_bonus: false,
      calculation_method: "加权计算法(1.5倍)"
    },
    {
      id: 3,
      course_name: "阴瑜伽放松",
      course_type_name: "阴瑜伽",
      course_type_color: "#FBBC05",
      attendance_date: "2023-11-20 19:00",
      consumed_value: 100,
      is_bonus: false,
      calculation_method: "平均消耗法"
    },
    {
      id: 4,
      course_name: "孕期瑜伽",
      course_type_name: "孕产瑜伽",
      course_type_color: "#EA4335",
      attendance_date: "2023-11-25 09:30",
      consumed_value: 150,
      is_bonus: true,
      calculation_method: "平均消耗法(赠送课程)"
    },
    {
      id: 5,
      course_name: "空中瑜伽体验",
      course_type_name: "空中瑜伽",
      course_type_color: "#FF6D91",
      attendance_date: "2023-11-28 16:00",
      consumed_value: 200,
      is_bonus: false,
      calculation_method: "实际价格法"
    }
  ]

  // 图表数据
  const chartData = [
    { name: "基础瑜伽", 消费次数: 8, 消费价值: 960 },
    { name: "高级瑜伽", 消费次数: 5, 消费价值: 900 },
    { name: "阴瑜伽", 消费次数: 3, 消费价值: 300 },
    { name: "孕产瑜伽", 消费次数: 2, 消费价值: 300 },
    { name: "空中瑜伽", 消费次数: 4, 消费价值: 800 }
  ]

  // 模拟获取数据
  useEffect(() => {
    // 在实际应用中，这里应该调用API获取数据
    // consumptionApi.getConsumptionRecords({
    //   user_id: userId,
    //   membership_id: membershipId,
    //   course_type_id: filters.courseType !== "all" ? filters.courseType : undefined,
    //   start_date: filters.dateRange.from?.toISOString(),
    //   end_date: filters.dateRange.to?.toISOString(),
    //   page: pagination.page,
    //   page_size: pagination.pageSize
    // }).then(response => {
    //   if (response.code === 200) {
    //     setRecords(response.data.list);
    //     setPagination({
    //       ...pagination,
    //       total: response.data.total
    //     });
    //   }
    // });

    // 使用模拟数据
    setRecords(mockRecords)
    setPagination({
      ...pagination,
      total: mockRecords.length
    })
  }, [userId, membershipId, filters, pagination.page, pagination.pageSize])

  // 处理筛选变化
  const handleFilterChange = (type: string, value: any) => {
    setFilters({
      ...filters,
      [type]: value
    })
    // 重置页码
    setPagination({
      ...pagination,
      page: 1
    })
  }

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setPagination({
      ...pagination,
      page
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <h2 className="text-xl font-semibold">消费记录</h2>

        <div className="flex flex-wrap gap-2">
          <Select
            value={filters.courseType}
            onValueChange={(value) => handleFilterChange("courseType", value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="课程类型" />
            </SelectTrigger>
            <SelectContent>
              {courseTypes.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  <div className="flex items-center gap-2">
                    {type.color && type.id !== "all" && (
                      <div className="h-3 w-3 rounded-full" style={{ backgroundColor: type.color }} />
                    )}
                    <span>{type.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <DateRangePicker
            selected={filters.dateRange}
            onSelect={(value) => handleFilterChange("dateRange", value)}
          />

          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>

          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>消费趋势</CardTitle>
            <CardDescription>按课程类型统计的消费次数和价值</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                <Tooltip />
                <Legend />
                <Bar yAxisId="left" dataKey="消费次数" fill="#8884d8" />
                <Bar yAxisId="right" dataKey="消费价值" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>消费统计</CardTitle>
            <CardDescription>会员卡消费情况概览</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">总消费次数</p>
                <p className="text-2xl font-bold">22次</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">总消费价值</p>
                <p className="text-2xl font-bold">¥3,260</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">平均单次价值</p>
                <p className="text-2xl font-bold">¥148.2</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">剩余价值</p>
                <p className="text-2xl font-bold">¥1,340</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>课程名称</TableHead>
                <TableHead>课程类型</TableHead>
                <TableHead>上课日期</TableHead>
                <TableHead>消耗价值</TableHead>
                <TableHead>计算方法</TableHead>
                <TableHead>是否赠送</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {records.map((record) => (
                <TableRow key={record.id}>
                  <TableCell className="font-medium">{record.course_name}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {record.course_type_color && (
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: record.course_type_color }}
                        />
                      )}
                      <span>{record.course_type_name}</span>
                    </div>
                  </TableCell>
                  <TableCell>{record.attendance_date}</TableCell>
                  <TableCell>¥{record.consumed_value}</TableCell>
                  <TableCell>{record.calculation_method}</TableCell>
                  <TableCell>
                    {record.is_bonus ? (
                      <Badge variant="secondary">赠送</Badge>
                    ) : (
                      <Badge variant="outline">常规</Badge>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              href="#"
              onClick={(e) => {
                e.preventDefault()
                if (pagination.page > 1) {
                  handlePageChange(pagination.page - 1)
                }
              }}
            />
          </PaginationItem>
          {[...Array(Math.min(5, Math.ceil(pagination.total / pagination.pageSize)))].map((_, i) => (
            <PaginationItem key={i}>
              <PaginationLink
                href="#"
                isActive={pagination.page === i + 1}
                onClick={(e) => {
                  e.preventDefault()
                  handlePageChange(i + 1)
                }}
              >
                {i + 1}
              </PaginationLink>
            </PaginationItem>
          ))}
          <PaginationItem>
            <PaginationNext
              href="#"
              onClick={(e) => {
                e.preventDefault()
                if (pagination.page < Math.ceil(pagination.total / pagination.pageSize)) {
                  handlePageChange(pagination.page + 1)
                }
              }}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  )
}
