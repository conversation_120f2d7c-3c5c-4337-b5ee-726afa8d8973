"use client"

import { useState } from "react"
import { HelpCircle } from "lucide-react"
import { ConsumptionRuleDialog } from "./consumption-rule-dialog"
import { cn } from "@/lib/utils"

interface RuleInfoIconProps {
  ruleType?: "custom" | "average" | "actual" | "weighted"
  className?: string
}

export function RuleInfoIcon({ 
  ruleType = "custom", 
  className
}: RuleInfoIconProps) {
  const [dialogOpen, setDialogOpen] = useState(false)
  
  return (
    <>
      <HelpCircle 
        className={cn("h-4 w-4 text-muted-foreground cursor-pointer hover:text-primary transition-colors", className)}
        onClick={() => setDialogOpen(true)}
      />
      
      <ConsumptionRuleDialog 
        open={dialogOpen} 
        onOpenChange={setDialogOpen} 
        ruleType={ruleType}
      />
    </>
  )
}
