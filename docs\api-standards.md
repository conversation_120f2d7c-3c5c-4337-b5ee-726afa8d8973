# API 调用规范文档

## 概述

本文档定义了项目中统一的API调用规范，确保所有接口调用方式一致，便于维护和扩展。

## 统一API配置

### 基础配置 (`lib/api.ts`)

```typescript
// 获取正确的API基础URL
const getApiBaseUrl = () => {
  // 在客户端，使用当前域名
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  // 在服务端，使用环境变量或默认值
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3004';
};

const API_BASE_URL = getApiBaseUrl();

const instance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});
```

### 响应拦截器

```typescript
instance.interceptors.response.use(
  (response) => {
    // 统一处理响应数据，返回 response.data
    console.log('请求成功:', response.config.url, response.data);
    return response.data;
  },
  (error) => {
    // 统一错误处理
    const message = error.response?.data?.message || error.message || '请求失败';
    toast({
      title: '请求错误',
      description: message,
      variant: 'destructive',
    });
    return Promise.reject(error);
  }
);
```

## API 模块规范

### 1. 会员卡类型 API (`memberCardApi`)

```typescript
export const memberCardApi = {
  // 获取所有会员卡类型
  getAllMemberCards: (params?: {
    tenantId?: string;
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
    cardType?: string;
  }) => instance.get('/api/member-cards', { params }),

  // 获取会员卡类型详情
  getMemberCardById: (id: string) => instance.get(`/api/member-cards/${id}`),

  // 创建会员卡类型
  createMemberCard: (data: any) => instance.post('/api/member-cards', data),

  // 更新会员卡类型
  updateMemberCard: (id: string, data: any) => instance.put(`/api/member-cards/${id}`, data),

  // 删除会员卡类型
  deleteMemberCard: (id: string) => instance.delete(`/api/member-cards/${id}`),

  // 更新会员卡类型状态
  updateMemberCardStatus: (id: string, status: string) => 
    instance.put(`/api/member-cards/${id}/status`, { status }),

  // 批量删除会员卡类型
  deleteMemberCards: (ids: string[]) => 
    instance.post('/api/member-cards/batch', { action: 'delete', ids }),

  // 复制会员卡类型
  copyMemberCard: (id: string) => instance.post(`/api/member-cards/copy/${id}`),
};
```

### 2. 课程类型 API (`courseTypeApi`)

```typescript
export const courseTypeApi = {
  // 获取所有课程类型
  getAll: (filter?: CourseTypeFilter) => instance.get('/api/course-types', { params: filter }),

  // 获取单个课程类型
  getById: (id: number) => instance.get(`/api/course-types/${id}`),

  // 创建课程类型
  create: (data: Partial<CourseType>) => instance.post('/api/course-types', data),

  // 更新课程类型
  update: (id: number, data: Partial<CourseType>) => 
    instance.put(`/api/course-types/${id}`, data),

  // 删除课程类型
  delete: (id: number) => instance.delete(`/api/course-types/${id}`),

  // 搜索课程类型
  search: (query: string) => instance.get('/api/course-types/search', { params: { q: query } }),

  // 获取统计信息
  getStats: () => instance.get('/api/course-types/stats'),
};
```

### 3. 门店 API (`storeApi`)

```typescript
export const storeApi = {
  // 获取所有门店
  getAllStores: (params?: {
    tenantId?: string;
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
  }) => instance.get('/api/stores', { params }),

  // 获取门店详情
  getStoreById: (id: string) => instance.get(`/api/stores/${id}`),

  // 创建门店
  createStore: (data: any) => instance.post('/api/stores', data),

  // 更新门店
  updateStore: (id: string, data: any) => instance.put(`/api/stores/${id}`, data),

  // 删除门店
  deleteStore: (id: string) => instance.delete(`/api/stores/${id}`),

  // 更新门店状态
  updateStoreStatus: (id: string, status: string) => 
    instance.put(`/api/stores/${id}/status`, { status }),
};
```

### 4. 教练 API (`coachApi`)

```typescript
export const coachApi = {
  // 获取所有教练
  getAllCoaches: (params?: {
    tenantId?: string;
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
  }) => instance.get('/api/coaches', { params }),

  // 获取教练详情
  getCoachById: (id: string) => instance.get(`/api/coaches/${id}`),

  // 创建教练
  createCoach: (data: any) => instance.post('/api/coaches', data),

  // 更新教练
  updateCoach: (id: string, data: any) => instance.put(`/api/coaches/${id}`, data),

  // 删除教练
  deleteCoach: (id: string) => instance.delete(`/api/coaches/${id}`),
};
```

### 5. 场地 API (`venueApi`)

```typescript
export const venueApi = {
  // 获取所有场地
  getAllVenues: (params?: {
    tenantId?: string;
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
  }) => instance.get('/api/venues', { params }),

  // 获取场地详情
  getVenueById: (id: string) => instance.get(`/api/venues/${id}`),

  // 创建场地
  createVenue: (data: any) => instance.post('/api/venues', data),

  // 更新场地
  updateVenue: (id: string, data: any) => instance.put(`/api/venues/${id}`, data),

  // 删除场地
  deleteVenue: (id: string) => instance.delete(`/api/venues/${id}`),
};
```

## 使用规范

### 1. 导入方式

```typescript
import { memberCardApi, courseTypeApi, storeApi, coachApi, venueApi } from '@/lib/api';
```

### 2. 调用方式

```typescript
// 获取数据
const result: any = await memberCardApi.getAllMemberCards({
  tenantId: '2',
  page: 1,
  pageSize: 10
});

// 处理响应（响应拦截器已经返回了 response.data）
if (result.code === 0 || result.code === 200) {
  const data = result.data?.list || result.data || [];
  // 处理数据
} else {
  // 处理错误
  throw new Error(result.msg || '操作失败');
}
```

### 3. 错误处理

由于响应拦截器已经统一处理了错误，页面组件只需要：

```typescript
try {
  const result = await api.someMethod();
  // 处理成功响应
} catch (error) {
  // 错误已经通过 toast 显示给用户
  console.error('操作失败:', error);
}
```

## 响应格式规范

### 成功响应

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "list": [...],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

### 错误响应

```json
{
  "code": 500,
  "msg": "操作失败的具体原因",
  "data": null
}
```

## 注意事项

1. **类型安全**: 在调用API时使用 `any` 类型标注以避免TypeScript类型错误
2. **错误处理**: 响应拦截器已经处理了错误显示，组件中只需要处理业务逻辑
3. **参数传递**: 所有查询参数通过 `params` 对象传递
4. **租户隔离**: 大部分API都需要传递 `tenantId` 参数
5. **分页**: 使用统一的分页参数 `page` 和 `pageSize`

## 迁移指南

### 从直接 fetch 迁移

**之前:**
```typescript
const response = await fetch('/api/member-cards?tenantId=2');
const result = await response.json();
```

**现在:**
```typescript
const result: any = await memberCardApi.getAllMemberCards({ tenantId: '2' });
```

### 从自定义 axios 实例迁移

**之前:**
```typescript
import axiosInstance from '@/lib/api';
const response = await axiosInstance.get('/api/member-cards');
```

**现在:**
```typescript
import { memberCardApi } from '@/lib/api';
const result: any = await memberCardApi.getAllMemberCards();
```

这样的统一规范确保了：
- 代码一致性和可维护性
- 错误处理的统一性
- 类型安全（在可能的情况下）
- 更好的开发体验
