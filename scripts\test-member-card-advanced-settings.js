const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'yoga'
};

async function testMemberCardAdvancedSettings() {
  console.log('开始测试会员卡高级设置功能...\n');

  const connection = await mysql.createConnection(dbConfig);

  try {
    // 1. 测试数据表是否存在
    console.log('1. 检查数据表结构:');
    const tables = [
      'member_card_advanced_settings',
      'member_card_user_settings', 
      'member_card_course_settings',
      'member_card_course_associations',
      'member_card_sales_settings'
    ];

    for (const table of tables) {
      try {
        const [result] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
        const count = result[0].count;
        console.log(`  ✓ ${table}: ${count} 条记录`);
      } catch (error) {
        console.log(`  ✗ ${table}: 表不存在或查询失败`);
      }
    }

    // 2. 测试会员卡类型数据
    console.log('\n2. 检查会员卡类型数据:');
    const [cardTypes] = await connection.execute(
      'SELECT id, name, card_category FROM member_card_types ORDER BY id'
    );
    
    console.log(`  找到 ${cardTypes.length} 个会员卡类型:`);
    cardTypes.forEach(card => {
      console.log(`    ID: ${card.id}, 名称: ${card.name}, 类别: ${card.card_category}`);
    });

    // 3. 测试高级设置数据
    console.log('\n3. 检查高级设置数据:');
    const [advancedSettings] = await connection.execute(`
      SELECT 
        mas.card_type_id,
        mct.name as card_name,
        mas.leave_option,
        mas.auto_activate_days,
        mas.daily_booking_limit,
        mas.weekly_booking_limit,
        mas.monthly_booking_limit
      FROM member_card_advanced_settings mas
      LEFT JOIN member_card_types mct ON mas.card_type_id = mct.id
      ORDER BY mas.card_type_id
    `);

    console.log(`  找到 ${advancedSettings.length} 个高级设置:`);
    advancedSettings.forEach(setting => {
      console.log(`    卡片: ${setting.card_name} (ID: ${setting.card_type_id})`);
      console.log(`      请假选项: ${setting.leave_option}`);
      console.log(`      自动开卡: ${setting.auto_activate_days}天`);
      console.log(`      预约限制: 每日${setting.daily_booking_limit}次, 每周${setting.weekly_booking_limit}次, 每月${setting.monthly_booking_limit}次`);
    });

    // 4. 测试课程关联数据
    console.log('\n4. 检查课程关联数据:');
    const [courseAssociations] = await connection.execute(`
      SELECT 
        mca.card_type_id,
        mct.name as card_name,
        mca.course_type_id,
        ct.name as course_name,
        mca.consumption_times,
        mca.is_enabled
      FROM member_card_course_associations mca
      LEFT JOIN member_card_types mct ON mca.card_type_id = mct.id
      LEFT JOIN coursetype ct ON mca.course_type_id = ct.id
      ORDER BY mca.card_type_id, mca.course_type_id
    `);

    console.log(`  找到 ${courseAssociations.length} 个课程关联:`);
    const groupedAssociations = {};
    courseAssociations.forEach(assoc => {
      if (!groupedAssociations[assoc.card_type_id]) {
        groupedAssociations[assoc.card_type_id] = {
          cardName: assoc.card_name,
          courses: []
        };
      }
      groupedAssociations[assoc.card_type_id].courses.push({
        courseName: assoc.course_name,
        consumption: assoc.consumption_times,
        enabled: assoc.is_enabled
      });
    });

    Object.keys(groupedAssociations).forEach(cardId => {
      const card = groupedAssociations[cardId];
      console.log(`    ${card.cardName} (ID: ${cardId}):`);
      card.courses.forEach(course => {
        const status = course.enabled ? '启用' : '禁用';
        console.log(`      - ${course.courseName}: 消耗${course.consumption}次 (${status})`);
      });
    });

    // 5. 测试API接口
    console.log('\n5. 测试API接口:');
    
    // 测试获取高级设置API
    try {
      const response = await fetch('http://localhost:3004/api/member-cards/1/advanced-settings');
      if (response.ok) {
        const data = await response.json();
        console.log('  ✓ 获取高级设置API正常工作');
        console.log(`    返回数据包含: ${Object.keys(data.data).join(', ')}`);
      } else {
        console.log('  ✗ 获取高级设置API返回错误:', response.status);
      }
    } catch (error) {
      console.log('  ✗ 获取高级设置API请求失败:', error.message);
    }

    // 测试获取课程关联API
    try {
      const response = await fetch('http://localhost:3004/api/member-cards/1/course-associations');
      if (response.ok) {
        const data = await response.json();
        console.log('  ✓ 获取课程关联API正常工作');
        console.log(`    关联课程数量: ${data.data.associations.length}`);
        console.log(`    可用课程类型数量: ${data.data.availableCourseTypes.length}`);
      } else {
        console.log('  ✗ 获取课程关联API返回错误:', response.status);
      }
    } catch (error) {
      console.log('  ✗ 获取课程关联API请求失败:', error.message);
    }

    // 6. 数据完整性检查
    console.log('\n6. 数据完整性检查:');
    
    // 检查每个会员卡类型是否都有对应的高级设置
    const [missingAdvanced] = await connection.execute(`
      SELECT mct.id, mct.name 
      FROM member_card_types mct
      LEFT JOIN member_card_advanced_settings mas ON mct.id = mas.card_type_id
      WHERE mas.card_type_id IS NULL
    `);

    if (missingAdvanced.length === 0) {
      console.log('  ✓ 所有会员卡类型都有对应的高级设置');
    } else {
      console.log(`  ✗ 发现 ${missingAdvanced.length} 个会员卡类型缺少高级设置:`);
      missingAdvanced.forEach(card => {
        console.log(`    - ${card.name} (ID: ${card.id})`);
      });
    }

    // 检查外键约束
    const [orphanedAssociations] = await connection.execute(`
      SELECT mca.id, mca.card_type_id, mca.course_type_id
      FROM member_card_course_associations mca
      LEFT JOIN member_card_types mct ON mca.card_type_id = mct.id
      LEFT JOIN coursetype ct ON mca.course_type_id = ct.id
      WHERE mct.id IS NULL OR ct.id IS NULL
    `);

    if (orphanedAssociations.length === 0) {
      console.log('  ✓ 所有课程关联的外键约束正常');
    } else {
      console.log(`  ✗ 发现 ${orphanedAssociations.length} 个无效的课程关联`);
    }

    console.log('\n✓ 会员卡高级设置功能测试完成!');

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    await connection.end();
  }
}

// 运行测试
testMemberCardAdvancedSettings().catch(console.error);
