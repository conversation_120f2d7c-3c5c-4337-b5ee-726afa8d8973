import { NextRequest, NextResponse } from 'next/server';
import { courseTypeService } from '@/services/course-type-service';

export async function GET(request: NextRequest) {
  try {
    const stats = courseTypeService.getStats();
    
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: stats
    });
  } catch (error) {
    console.error('获取课程类型统计错误:', error);
    return NextResponse.json(
      { code: 500, msg: '获取统计数据失败', error: String(error) },
      { status: 500 }
    );
  }
} 