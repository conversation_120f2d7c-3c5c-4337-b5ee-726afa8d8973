import { NextRequest, NextResponse } from 'next/server';
import { courseTypeService } from '@/services/course-type-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const keyword = searchParams.get('keyword') || '';
    const status = searchParams.get('status') || 'all';
    
    // 获取课程类型数据
    const types = courseTypeService.getAll({ keyword, status });
    
    // 将数据转换为导出格式
    const exportData = types.map(type => ({
      id: type.id,
      name: type.name,
      description: type.description || '',
      status: type.status,
      courses: type.courses,
      color: type.color,
      createdAt: type.createdAt,
      updatedAt: type.updatedAt
    }));
    
    // 在实际项目中，这里可能需要生成CSV/Excel文件
    // 为了简化，我们直接返回JSON数据
    return NextResponse.json({
      code: 200,
      msg: 'Success',
      data: {
        total: exportData.length,
        types: exportData,
        // 模拟文件下载链接
        downloadUrl: `/api/course-types/download?timestamp=${Date.now()}`
      }
    });
  } catch (error) {
    console.error('导出课程类型错误:', error);
    return NextResponse.json(
      { error: '导出失败', details: String(error) },
      { status: 500 }
    );
  }
} 