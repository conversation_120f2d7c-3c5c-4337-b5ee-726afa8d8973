"use client"

import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Shuffle, Calendar, Users, ArrowRight, Clock } from "lucide-react"

export function ResourceAllocationCard() {
  // 资源调配数据
  const resourceAllocations = [
    {
      id: "1",
      type: "coach",
      name: "张教练",
      fromStore: "朝阳店",
      toStore: "西城店",
      startDate: "2023-06-15",
      endDate: "2023-06-20",
      status: "upcoming",
    },
    {
      id: "2",
      type: "coach",
      name: "李教练",
      fromStore: "海淀店",
      toStore: "朝阳店",
      startDate: "2023-06-10",
      endDate: "2023-06-12",
      status: "active",
    },
    {
      id: "3",
      type: "equipment",
      name: "瑜伽垫 (20个)",
      fromStore: "朝阳店",
      toStore: "通州店",
      startDate: "2023-06-18",
      endDate: "2023-06-25",
      status: "upcoming",
    },
  ]

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500">进行中</Badge>
      case "upcoming":
        return <Badge variant="outline" className="text-blue-500 border-blue-500">即将开始</Badge>
      case "completed":
        return <Badge variant="outline" className="text-gray-500">已完成</Badge>
      default:
        return null
    }
  }

  // 获取资源类型图标
  const getResourceTypeIcon = (type: string) => {
    switch (type) {
      case "coach":
        return <Users className="h-4 w-4 text-blue-500" />
      case "equipment":
        return <Shuffle className="h-4 w-4 text-green-500" />
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shuffle className="h-5 w-5 text-blue-500" />
          资源调配
        </CardTitle>
        <CardDescription>连锁门店间的资源共享与调配</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {resourceAllocations.map((allocation) => (
            <div key={allocation.id} className="flex items-start justify-between border-b pb-3">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  {getResourceTypeIcon(allocation.type)}
                  <span className="font-medium">{allocation.name}</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <span>{allocation.fromStore}</span>
                  <ArrowRight className="h-3 w-3" />
                  <span>{allocation.toStore}</span>
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  <span>{allocation.startDate} 至 {allocation.endDate}</span>
                </div>
              </div>
              <div>
                {getStatusBadge(allocation.status)}
              </div>
            </div>
          ))}

          <div className="pt-2">
            <h4 className="text-sm font-medium mb-3">即将到期的调配</h4>
            <div className="space-y-3">
              <div className="flex items-start gap-3 border rounded-md p-3">
                <div className="bg-blue-100 p-2 rounded-full">
                  <Clock className="h-4 w-4 text-blue-600" />
                </div>
                <div className="space-y-1">
                  <div className="font-medium">李教练 (海淀店 → 朝阳店)</div>
                  <div className="text-sm text-muted-foreground">还有 1 天结束</div>
                  <div className="flex gap-2 mt-1">
                    <Button variant="outline" size="sm">延长</Button>
                    <Button size="sm">确认归还</Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full">管理资源调配</Button>
      </CardFooter>
    </Card>
  )
}
