"use client"

import { useState } from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/hooks/use-toast"
import { Loader2 } from "lucide-react"

interface DeleteStoreDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  store: {
    id: string
    name: string
  }
  onDeleteSuccess?: () => void
}

export function DeleteStoreDialog({ open, onOpenChange, store, onDeleteSuccess }: DeleteStoreDialogProps) {
  const { toast } = useToast()
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDelete = async () => {
    setIsDeleting(true)

    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1500))

      toast({
        title: "门店已删除",
        description: `${store.name} 已成功从连锁门店列表中删除`,
      })

      // 关闭对话框
      onOpenChange(false)

      // 调用删除成功回调
      if (onDeleteSuccess) {
        onDeleteSuccess()
      }
    } catch (error) {
      toast({
        title: "删除失败",
        description: "删除门店时出错，请重试",
        variant: "destructive",
      })
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除门店</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除 <strong>{store.name}</strong> 吗？此操作无法撤销，门店的所有数据将被永久删除。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault()
              handleDelete()
            }}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                删除中...
              </>
            ) : (
              "确认删除"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
