// 检查会员卡表结构和数据
const mysql = require('mysql2/promise');

async function checkMemberCardTable() {
  console.log('检查会员卡表结构和数据...');
  
  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'yoga'
    });

    console.log('✓ 数据库连接成功');

    // 1. 检查会员卡表是否存在
    console.log('\n1. 检查会员卡表:');
    try {
      const [tables] = await connection.execute("SHOW TABLES LIKE 'membercard'");
      if (tables.length > 0) {
        console.log('✓ membercard表已存在');
        
        // 查看表结构
        const [columns] = await connection.execute('DESCRIBE membercard');
        console.log('\n表结构:');
        columns.forEach(col => {
          console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
        });
        
        // 查看现有数据
        const [rows] = await connection.execute('SELECT COUNT(*) as count FROM membercard');
        console.log(`\n现有数据: ${rows[0].count} 条记录`);
        
        if (rows[0].count > 0) {
          const [data] = await connection.execute('SELECT * FROM membercard LIMIT 5');
          console.log('\n前5条数据:');
          data.forEach((row, index) => {
            console.log(`  ${index + 1}. ID: ${row.id}, 名称: ${row.name}, 价格: ${row.price}, 租户: ${row.tenant_id}`);
          });
        }
      } else {
        console.log('❌ membercard表不存在，需要创建');
      }
    } catch (error) {
      console.log('❌ membercard表不存在，需要创建');
    }

    // 2. 如果表不存在，创建表
    try {
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS membercard (
          id INT PRIMARY KEY AUTO_INCREMENT,
          tenant_id INT NOT NULL,
          name VARCHAR(100) NOT NULL COMMENT '会员卡名称',
          description TEXT COMMENT '描述',
          price DECIMAL(10, 2) NOT NULL COMMENT '价格',
          original_price DECIMAL(10, 2) COMMENT '原价',
          validity_days INT NOT NULL COMMENT '有效期天数',
          usage_limit VARCHAR(100) NOT NULL COMMENT '使用限制',
          card_type VARCHAR(50) NOT NULL COMMENT '卡属性',
          status VARCHAR(20) DEFAULT '销售中' COMMENT '状态',
          members_count INT DEFAULT 0 COMMENT '持卡会员数',
          sales_count INT DEFAULT 0 COMMENT '销售数量',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (tenant_id) REFERENCES tenant(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员卡表'
      `);
      console.log('\n✓ 会员卡表创建成功');
    } catch (error) {
      console.log('\n表创建失败:', error.message);
    }

    await connection.end();
    console.log('\n✓ 检查完成');

  } catch (error) {
    console.error('检查失败:', error.message);
  }
}

checkMemberCardTable();
