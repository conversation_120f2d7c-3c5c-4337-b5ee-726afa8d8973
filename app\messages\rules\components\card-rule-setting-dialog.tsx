import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Footer
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

// 会员卡类型
interface CardType {
  id: number
  name: string
  balanceThreshold?: number // 余额提醒阈值
}

interface MessageRule {
  id: number
  name: string
  template: string
  smsEnabled: boolean
  appEnabled: boolean
  sendRule?: string
  showRuleButton?: boolean // 是否显示设置规则按钮
  // 规则设置
  reminderDays?: number // 提前提醒天数
  reminderTimes?: number // 提醒次数
  reminderTime?: string // 提醒时间
  balanceThresholds?: {[key: number]: number} // 会员卡类型ID -> 余额提醒阈值
}

interface CardRuleSettingDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  rule: MessageRule | null
  onSave: (rule: MessageRule) => void
}

// 示例会员卡类型数据
const cardTypes: CardType[] = [
  { id: 1, name: "储值卡", balanceThreshold: 100 },
  { id: 2, name: "次卡", balanceThreshold: 5 },
  { id: 3, name: "时间卡" },
  { id: 4, name: "组合卡" },
  { id: 5, name: "体验卡" },
]

export function CardRuleSettingDialog({
  open,
  onOpenChange,
  rule,
  onSave
}: CardRuleSettingDialogProps) {
  // 规则设置状态
  const [reminderDays, setReminderDays] = useState<number>(rule?.reminderDays || 3)
  const [reminderTimes, setReminderTimes] = useState<number>(rule?.reminderTimes || 2)
  const [reminderTime, setReminderTime] = useState<string>(rule?.reminderTime || "07:00")
  const [cardBalanceThresholds, setCardBalanceThresholds] = useState<{[key: number]: number}>({})
  const [showBalanceSettings, setShowBalanceSettings] = useState(false)
  const [showExpirySettings, setShowExpirySettings] = useState(false)

  // 当对话框打开时，初始化设置
  React.useEffect(() => {
    if (open && rule) {
      // 检查是否是余额提醒规则
      const isBalanceRule = rule.name.includes("余额") || rule.name.includes("储值")
      setShowBalanceSettings(isBalanceRule)

      // 检查是否是到期提醒规则
      const isExpiryRule = rule.name.includes("到期") || rule.name.includes("续费")
      setShowExpirySettings(isExpiryRule)

      // 初始化提醒设置
      setReminderDays(rule.reminderDays || 3)
      setReminderTimes(rule.reminderTimes || 2)
      setReminderTime(rule.reminderTime || "07:00")

      // 初始化余额阈值设置
      if (isBalanceRule) {
        const initialThresholds: {[key: number]: number} = {}
        cardTypes.forEach(card => {
          if (card.balanceThreshold) {
            initialThresholds[card.id] = rule.balanceThresholds && rule.balanceThresholds[card.id]
              ? rule.balanceThresholds[card.id]
              : card.balanceThreshold
          }
        })
        setCardBalanceThresholds(initialThresholds)
      }
    }
  }, [open, rule])

  // 处理余额阈值变化
  const handleBalanceThresholdChange = (cardId: number, threshold: number) => {
    setCardBalanceThresholds(prev => ({
      ...prev,
      [cardId]: threshold
    }))
  }

  // 处理保存按钮点击
  const handleSave = () => {
    if (rule) {
      const updatedRule = {
        ...rule,
        reminderDays,
        reminderTimes,
        reminderTime
      }

      // 如果是余额提醒规则，添加余额阈值设置
      if (showBalanceSettings) {
        updatedRule.balanceThresholds = cardBalanceThresholds
      }

      onSave(updatedRule)
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md p-6 rounded-md">
        <h2 className="text-lg font-medium mb-6 text-center border-b pb-4">
          {rule?.name || "消息规则"}设置
        </h2>

        <div className="space-y-4">
          {showExpirySettings && (
            <div className="flex items-center">
              <Label className="w-24 text-right mr-4 text-gray-700">提醒条件</Label>
              <div className="flex items-center flex-1">
                <Label className="mr-2">会员卡到期前</Label>
                <Input
                  type="number"
                  value={reminderDays}
                  onChange={(e) => setReminderDays(parseInt(e.target.value) || 0)}
                  className="w-20 text-center mx-2 h-8"
                  min={1}
                />
                <Label>天提醒</Label>
              </div>
            </div>
          )}

          {showBalanceSettings && (
            <div className="space-y-3">
              <div className="flex items-center">
                <Label className="w-24 text-right mr-4 text-gray-700">余额阈值</Label>
                <div className="flex-1">
                  <p className="text-xs text-gray-500 mb-2">设置不同卡类型的余额提醒阈值</p>
                  <div className="space-y-2">
                    {cardTypes.map(card => (
                      card.balanceThreshold ? (
                        <div key={card.id} className="flex items-center">
                          <Label className="w-16">{card.name}</Label>
                          <div className="flex items-center">
                            <Label className="mr-2">低于</Label>
                            <Input
                              type="number"
                              value={cardBalanceThresholds[card.id] || card.balanceThreshold}
                              onChange={(e) => handleBalanceThresholdChange(
                                card.id,
                                parseInt(e.target.value) || card.balanceThreshold
                              )}
                              className="w-20 text-center mx-2 h-8"
                              min={1}
                            />
                            <Label>{card.id === 1 ? "元" : "次"}提醒</Label>
                          </div>
                        </div>
                      ) : null
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="flex items-center">
            <Label className="w-24 text-right mr-4 text-gray-700">提醒频次</Label>
            <Input
              type="number"
              value={reminderTimes}
              onChange={(e) => setReminderTimes(parseInt(e.target.value) || 0)}
              className="w-20 text-center h-8"
              min={1}
            />
          </div>

          <div className="flex items-center">
            <Label className="w-24 text-right mr-4 text-gray-700">提醒时间</Label>
            <Input
              type="time"
              value={reminderTime}
              onChange={(e) => setReminderTime(e.target.value)}
              className="w-32 h-8"
            />
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="px-6"
          >
            取消
          </Button>
          <Button
            onClick={handleSave}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6"
          >
            确定
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
