"use client"

import React, { createContext, useContext, useState, useEffect } from "react"

// 定义引导步骤
export const onboardingSteps = [
  {
    id: "system-settings",
    title: "系统基础设置",
    description: "设置工作室基本信息、上传logo、配置系统默认设置",
    path: "/settings",
    completed: false,
  },
  {
    id: "venue-setup",
    title: "场馆与教室设置",
    description: "添加场馆信息、设置教室容量和设施信息",
    path: "/venues",
    completed: false,
  },
  {
    id: "staff-setup",
    title: "员工账号设置",
    description: "添加管理员、前台、财务和教练账号，分配权限",
    path: "/settings/employees",
    completed: false,
  },
  {
    id: "course-types",
    title: "课程类型设置",
    description: "添加课程大类和具体课程类型，设置课程基本信息",
    path: "/courses/types",
    completed: false,
  },
  {
    id: "membership-cards",
    title: "会员卡类型设置",
    description: "添加期限卡、次卡和私教卡类型，设置价格和有效期",
    path: "/members/cards",
    completed: false,
  },
  {
    id: "card-course-relation",
    title: "会员卡与课程类型关联",
    description: "设置不同会员卡可用的课程类型和消耗权重",
    path: "/members/cards/course-types",
    completed: false,
  },
  {
    id: "coach-setup",
    title: "教练信息设置",
    description: "添加教练基本信息、专长领域和可授课的课程类型",
    path: "/coaches",
    completed: false,
  },
  {
    id: "course-schedule",
    title: "排课设置",
    description: "设置排课周期、添加固定课程和临时课程",
    path: "/courses/schedule",
    completed: false,
  },
  {
    id: "booking-rules",
    title: "预约规则设置",
    description: "设置预约开放天数、截止时间、取消规则和签到规则",
    path: "/booking-rules",
    completed: false,
  },
  {
    id: "member-import",
    title: "会员信息导入",
    description: "导入或手动添加会员信息和会员卡数据",
    path: "/members",
    completed: false,
  },
  {
    id: "system-test",
    title: "系统测试与验证",
    description: "测试会员注册、课程预约、签到和会员卡消费流程",
    path: "/",
    completed: false,
  },
]

type OnboardingContextType = {
  steps: typeof onboardingSteps
  currentStepIndex: number
  isOnboardingComplete: boolean
  isOnboardingEnabled: boolean
  showOnboarding: boolean
  setShowOnboarding: (show: boolean) => void
  markStepAsCompleted: (stepId: string) => void
  resetOnboarding: () => void
  nextStep: () => void
  previousStep: () => void
  goToStep: (index: number) => void
  dismissOnboarding: () => void
  enableOnboarding: () => void
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined)

export const useOnboarding = () => {
  const context = useContext(OnboardingContext)
  if (!context) {
    throw new Error("useOnboarding must be used within an OnboardingProvider")
  }
  return context
}

export const OnboardingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 从localStorage加载状态
  const [steps, setSteps] = useState(onboardingSteps)
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [showOnboarding, setShowOnboarding] = useState(false)
  const [isOnboardingEnabled, setIsOnboardingEnabled] = useState(true)

  // 初始化时从localStorage加载状态
  useEffect(() => {
    if (typeof window !== "undefined") {
      // 加载步骤完成状态
      const savedSteps = localStorage.getItem("onboardingSteps")
      if (savedSteps) {
        setSteps(JSON.parse(savedSteps))
      }

      // 加载当前步骤索引
      const savedStepIndex = localStorage.getItem("onboardingCurrentStep")
      if (savedStepIndex) {
        setCurrentStepIndex(parseInt(savedStepIndex, 10))
      }

      // 加载是否启用引导
      const onboardingEnabled = localStorage.getItem("onboardingEnabled")
      if (onboardingEnabled !== null) {
        setIsOnboardingEnabled(onboardingEnabled === "true")
      }

      // 如果是首次访问且引导已启用，显示引导
      const isFirstVisit = localStorage.getItem("firstVisit") === null
      if (isFirstVisit && isOnboardingEnabled) {
        setShowOnboarding(true)
        localStorage.setItem("firstVisit", "false")
      }
    }
  }, [])

  // 保存状态到localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("onboardingSteps", JSON.stringify(steps))
      localStorage.setItem("onboardingCurrentStep", currentStepIndex.toString())
      localStorage.setItem("onboardingEnabled", isOnboardingEnabled.toString())
    }
  }, [steps, currentStepIndex, isOnboardingEnabled])

  // 检查是否所有步骤都已完成
  const isOnboardingComplete = steps.every((step) => step.completed)

  // 标记步骤为已完成
  const markStepAsCompleted = (stepId: string) => {
    setSteps((prevSteps) =>
      prevSteps.map((step) =>
        step.id === stepId ? { ...step, completed: true } : step
      )
    )
  }

  // 重置引导
  const resetOnboarding = () => {
    setSteps(onboardingSteps)
    setCurrentStepIndex(0)
    setShowOnboarding(true)
    setIsOnboardingEnabled(true)
    if (typeof window !== "undefined") {
      localStorage.removeItem("firstVisit")
    }
  }

  // 下一步
  const nextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1)
    }
  }

  // 上一步
  const previousStep = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1)
    }
  }

  // 跳转到指定步骤
  const goToStep = (index: number) => {
    if (index >= 0 && index < steps.length) {
      setCurrentStepIndex(index)
    }
  }

  // 关闭引导
  const dismissOnboarding = () => {
    setShowOnboarding(false)
    setIsOnboardingEnabled(false)
  }

  // 启用引导
  const enableOnboarding = () => {
    setIsOnboardingEnabled(true)
    setShowOnboarding(true)
  }

  return (
    <OnboardingContext.Provider
      value={{
        steps,
        currentStepIndex,
        isOnboardingComplete,
        isOnboardingEnabled,
        showOnboarding,
        setShowOnboarding,
        markStepAsCompleted,
        resetOnboarding,
        nextStep,
        previousStep,
        goToStep,
        dismissOnboarding,
        enableOnboarding,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  )
}
