"use client"

import { useState, useEffect } from "react"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"

interface CollapsibleSwitchSectionProps {
  id: string
  title: string
  description?: string
  defaultChecked?: boolean
  children: React.ReactNode
  className?: string
}

export function CollapsibleSwitchSection({
  id,
  title,
  description,
  defaultChecked = true,
  children,
  className,
}: CollapsibleSwitchSectionProps) {
  const [isEnabled, setIsEnabled] = useState(defaultChecked)
  
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <div className="space-y-0.5">
          <Label htmlFor={id}>{title}</Label>
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </div>
        <Switch 
          id={id} 
          checked={isEnabled}
          onCheckedChange={setIsEnabled}
        />
      </div>
      
      {isEnabled && (
        <div className="transition-all duration-300 ease-in-out">
          {children}
        </div>
      )}
    </div>
  )
}
