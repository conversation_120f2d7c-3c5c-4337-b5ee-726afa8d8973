"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import {
  Search,
  Plus,
  MoreHorizontal,
  Check,
  X,
  Lock,
  Unlock,
  Share2,
  ShoppingBag,
  CreditCard,
  Users,
  BarChart3,
  BarChart,
  Package,
  Truck,
  Settings,
  BookOpen,
  Tag,
  DollarSign,
  FileText,
  Repeat,
  QrCode,
  Calendar
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

// 增值服务类型
const serviceTypes = [
  { id: "all", name: "全部服务" },
  { id: "operational", name: "运营工具" },
  { id: "analytics", name: "数据分析" },
  { id: "marketing", name: "营销工具" },
  { id: "financial", name: "财务工具" },
  { id: "hr", name: "人力资源" },
  { id: "business", name: "业务拓展" },
  { id: "member", name: "会员服务" },
]

// 模拟增值服务数据
const premiumServices = [
  {
    id: "18",
    name: "工作人员代约课",
    description: "提供工作人员帮助会员预约课程的功能，支持快速会员查询、课程筛选和预约管理",
    price: "199/月",
    category: "运营工具",
    type: "operational",
    status: "active",
    features: [
      "会员快速查询",
      "课程日期筛选",
      "一键代约课",
      "预约记录管理",
    ],
    icon: <Calendar className="h-8 w-8 text-green-500" />,
    route: "/premium-services/staff-booking",
  },
  {
    id: "17",
    name: "渠道核销系统",
    description: "支持美团、抖音等第三方渠道的订单核销管理，提供扫码核销、批量核销、核销记录查询等功能",
    price: "299/月",
    category: "运营工具",
    type: "operational",
    status: "active",
    features: [
      "美团券码核销",
      "抖音订单核销",
      "扫码快速核销",
      "核销数据统计",
    ],
    icon: <QrCode className="h-8 w-8 text-blue-500" />,
    route: "/premium-services/verification",
    subModules: [
      { name: "美团核销", route: "/verification/meituan" },
      { name: "抖音核销", route: "/verification/douyin" },
      { name: "核销记录", route: "/verification/records" },
      { name: "核销设置", route: "/verification/settings" },
    ],
  },
  {
    id: "16",
    name: "教练薪资管理",
    description: "全面的教练薪资计算与管理系统，支持多种薪资模式，自动化薪资计算、审核和发放流程",
    price: "399/月",
    category: "人力资源",
    type: "hr",
    status: "active",
    features: [
      "多种薪资模式",
      "自动薪资计算",
      "审核与发放流程",
      "薪资报表分析",
    ],
    icon: <DollarSign className="h-8 w-8 text-emerald-500" />,
    route: "/premium-services/coach-salary",
  },
  {
    id: "15",
    name: "订阅支付功能",
    description: "支持微信支付和支付宝的周期性扣款功能，适用于会员服务、订阅内容等场景",
    price: "399/月",
    category: "运营工具",
    type: "operational",
    status: "active",
    features: [
      "支付宝周期扣款",
      "微信自动扣费",
      "订阅计划管理",
      "扣款记录追踪",
    ],
    icon: <Repeat className="h-8 w-8 text-purple-500" />,
    route: "/premium-services/subscription-payment",
  },
  // 核心业务增值服务
  {
    id: "14",
    name: "法大大电子合同",
    description: "安全、合规的电子合同签署和管理解决方案，支持会员合同、教练合同等多种合同类型",
    price: "499/月",
    category: "运营工具",
    type: "operational",
    status: "active",
    features: [
      "电子合同签署",
      "合同模板管理",
      "签署流程追踪",
      "合同存档管理",
    ],
    icon: <FileText className="h-8 w-8 text-blue-500" />,
    route: "/premium-services/e-contract",
  },
  {
    id: "13",
    name: "智能收银系统",
    description: "一站式收银解决方案，整合商城虚拟商品核销、美团/抖音渠道核销、会员课程预约等功能",
    price: "399/月",
    category: "运营工具",
    type: "operational",
    status: "inactive",
    features: [
      "商城商品核销",
      "渠道订单核销",
      "会员课程预约",
      "收银数据统计",
    ],
    icon: <CreditCard className="h-8 w-8 text-teal-500" />,
    route: "/premium-services/cashier",
  },
  {
    id: "1",
    name: "智能排课系统",
    description: "基于AI的智能排课系统，自动优化教练和场地资源，提高场馆利用率",
    price: "299/月",
    category: "运营工具",
    type: "operational",
    status: "active",
    features: [
      "智能排课算法",
      "冲突自动检测",
      "教练工作量均衡",
      "场地利用率分析",
    ],
    icon: <BarChart3 className="h-8 w-8 text-blue-500" />,
    route: "/premium-services/smart-scheduling",
  },
  {
    id: "2",
    name: "会员数据分析",
    description: "深度分析会员行为和消费习惯，提供精准营销建议和会员流失预警",
    price: "399/月",
    category: "数据分析",
    type: "analytics",
    status: "inactive",
    features: [
      "会员画像分析",
      "消费习惯洞察",
      "流失风险预警",
      "精准营销建议",
    ],
    icon: <Users className="h-8 w-8 text-indigo-500" />,
    route: "/premium-services/member-analytics",
  },
  {
    id: "3",
    name: "多渠道营销工具",
    description: "一站式管理微信、抖音、美团等多渠道营销活动，提高获客效率",
    price: "499/月",
    category: "营销工具",
    type: "marketing",
    status: "active",
    features: [
      "多平台内容发布",
      "营销活动管理",
      "效果数据追踪",
      "ROI分析",
    ],
    icon: <Tag className="h-8 w-8 text-pink-500" />,
    route: "/premium-services/multi-channel-marketing",
    subModules: [
      { name: "渠道管理", route: "/premium-services/multi-channel-marketing?tab=channels" },
      { name: "活动管理", route: "/premium-services/multi-channel-marketing?tab=campaigns" },
      { name: "内容库", route: "/premium-services/multi-channel-marketing?tab=content" },
      { name: "数据分析", route: "/premium-services/multi-channel-marketing?tab=analytics" },
    ],
    // 标记此服务可在营销中心访问
    availableIn: ["premium-service", "marketing-center"]
  },
  {
    id: "4",
    name: "高级财务报表",
    description: "提供详细的财务分析报表，包括现金流、盈亏分析、成本控制等",
    price: "599/月",
    category: "财务工具",
    type: "financial",
    status: "inactive",
    features: [
      "多维度财务报表",
      "现金流分析",
      "成本控制建议",
      "税务优化方案",
    ],
    icon: <DollarSign className="h-8 w-8 text-green-500" />,
    route: "/premium-services/financial-reports",
  },
  {
    id: "5",
    name: "私教绩效系统",
    description: "全面的私教绩效评估和激励系统，提高教练积极性和客户满意度",
    price: "349/月",
    category: "人力资源",
    type: "hr",
    status: "active",
    features: [
      "绩效指标设置",
      "自动数据收集",
      "激励方案管理",
      "客户满意度反馈",
    ],
    icon: <Users className="h-8 w-8 text-yellow-500" />,
    route: "/premium-services/coach-performance",
  },

  // 共享股东服务
  {
    id: "6",
    name: "共享股东",
    description: "全面的股东管理解决方案，支持多种股东类型、分红管理和业绩统计",
    price: "699/月",
    category: "业务拓展",
    type: "business",
    status: "active",
    features: [
      "多类型股东管理",
      "自动分红计算",
      "业绩追踪统计",
      "股东权益设置",
    ],
    icon: <Share2 className="h-8 w-8 text-purple-500" />,
    route: "/premium-services/shareholders",
    subModules: [
      { name: "股东列表", route: "/shareholders" },
      { name: "股东类型", route: "/shareholders/types" },
      { name: "分红记录", route: "/shareholders/dividends" },
      { name: "业绩统计", route: "/shareholders/statistics" },
    ]
  },

  // 瑜伽商城服务
  {
    id: "7",
    name: "瑜伽商城系统",
    description: "一站式瑜伽商城解决方案，支持实体商品和虚拟商品销售，多种配送方式",
    price: "899/月",
    category: "业务拓展",
    type: "business",
    status: "active",
    features: [
      "实体商品管理",
      "虚拟商品销售",
      "多种配送方式",
      "订单全流程管理",
    ],
    icon: <ShoppingBag className="h-8 w-8 text-orange-500" />,
    route: "/premium-services/shop/products",
    subModules: [
      { name: "商品管理", route: "/premium-services/shop/products" },
      { name: "商品分类", route: "/premium-services/shop/categories" },
      { name: "订单管理", route: "/premium-services/shop/orders" },
      { name: "虚拟商品", route: "/premium-services/shop/virtual-products" },
      { name: "物流管理", route: "/premium-services/shop/logistics" },
      { name: "商城设置", route: "/premium-services/shop/settings" },
    ]
  },

  // 新增服务
  {
    id: "8",
    name: "会员健康管理系统",
    description: "全面的会员健康数据追踪与管理，提供个性化健康目标设定与进度跟踪",
    price: "499/月",
    category: "会员服务",
    type: "member",
    status: "inactive",
    features: [
      "健康数据追踪",
      "个性化目标设定",
      "健康评估报告",
      "营养建议计划",
    ],
    icon: <BookOpen className="h-8 w-8 text-emerald-500" />,
    route: "/premium-services/health-management",
  },
  {
    id: "9",
    name: "智能课程推荐系统",
    description: "基于AI的个性化课程推荐，根据会员历史记录、健康目标和偏好智能匹配课程",
    price: "399/月",
    category: "会员服务",
    type: "member",
    status: "inactive",
    features: [
      "AI课程推荐",
      "个性化课程路径",
      "智能课程匹配",
      "热门时段提醒",
    ],
    icon: <BarChart className="h-8 w-8 text-cyan-500" />,
    route: "/premium-services/course-recommendation",
  },
  {
    id: "10",
    name: "教练专业发展平台",
    description: "为教练提供专业成长路径，包括在线学习、认证系统和教学资源共享",
    price: "349/月",
    category: "人力资源",
    type: "hr",
    status: "inactive",
    features: [
      "在线学习认证",
      "教学技能评估",
      "教案资源共享",
      "专业成长规划",
    ],
    icon: <Users className="h-8 w-8 text-amber-500" />,
    route: "/premium-services/coach-development",
  },
  {
    id: "11",
    name: "社区互动平台",
    description: "打造瑜伽爱好者社区，增强会员归属感与互动，提高会员留存率",
    price: "299/月",
    category: "会员服务",
    type: "member",
    status: "inactive",
    features: [
      "会员社交网络",
      "瑜伽挑战活动",
      "作品分享展示",
      "社区互助问答",
    ],
    icon: <Share2 className="h-8 w-8 text-rose-500" />,
    route: "/premium-services/community",
  },
  {
    id: "12",
    name: "瑜伽工作室连锁管理",
    description: "多门店统一管理解决方案，支持跨店会员服务与数据同步，提高连锁运营效率",
    price: "999/月",
    category: "业务拓展",
    type: "business",
    status: "active",
    features: [
      "多门店管理",
      "跨店数据同步",
      "资源统一调配",
      "连锁标准化",
    ],
    icon: <Settings className="h-8 w-8 text-violet-500" />,
    route: "/premium-services/chain-management",
    subModules: [
      { name: "门店管理", route: "/premium-services/chain-management?tab=stores" },
      { name: "跨店服务", route: "/premium-services/chain-management?tab=cross-store" },
      { name: "标准化管理", route: "/premium-services/chain-management?tab=standards" },
      { name: "资源调配", route: "/premium-services/chain-management" },
    ]
  }
]

export default function PremiumServicesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [showServiceDetails, setShowServiceDetails] = useState(false)
  const [selectedService, setSelectedService] = useState<any>(null)

  // 过滤服务
  const filteredServices = premiumServices.filter(
    (service) =>
      (activeTab === "all" || service.type === activeTab) &&
      (statusFilter === "all" ||
        (statusFilter === "active" && service.status === "active") ||
        (statusFilter === "inactive" && service.status === "inactive")) &&
      (service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.category.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  // 处理服务详情查看
  const handleViewServiceDetails = (service: any) => {
    setSelectedService(service)
    setShowServiceDetails(true)
  }

  // 开通服务对话框状态
  const [showActivateDialog, setShowActivateDialog] = useState(false)
  const [activationPeriod, setActivationPeriod] = useState("12")
  const [activationLoading, setActivationLoading] = useState(false)
  const [activationSuccess, setActivationSuccess] = useState(false)

  // 处理服务开通/取消
  const handleToggleService = (service: any) => {
    if (service.status === 'active') {
      // 取消服务逻辑
      console.log(`取消服务: ${service.name}`)
    } else {
      // 开通服务逻辑 - 显示开通对话框
      setSelectedService(service)
      setShowActivateDialog(true)
    }
  }

  // 处理服务开通确认
  const handleActivateService = () => {
    setActivationLoading(true)

    // 模拟API调用
    setTimeout(() => {
      setActivationLoading(false)
      setActivationSuccess(true)

      // 3秒后关闭成功提示并关闭对话框
      setTimeout(() => {
        setActivationSuccess(false)
        setShowActivateDialog(false)

        // 更新服务状态（实际应用中应该通过API获取最新状态）
        console.log(`成功开通服务: ${selectedService?.name}, 期限: ${activationPeriod}个月`)
      }, 3000)
    }, 2000)
  }

  // 处理进入服务模块
  const handleEnterService = (service: any) => {
    // 对于共享股东服务，直接跳转到股东列表页
    if (service.id === "6" && service.name === "共享股东") {
      window.location.href = "/shareholders"
      return
    }

    // 如果有子模块，则显示详情对话框
    if (service.subModules && service.subModules.length > 0) {
      handleViewServiceDetails(service)
    } else {
      // 否则直接导航到服务路由
      window.location.href = service.route
    }
  }

  // 订购新服务对话框状态
  const [showOrderDialog, setShowOrderDialog] = useState(false)

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">增值服务</h1>
        <Button onClick={() => setShowOrderDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          订购新服务
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 items-center gap-2 max-w-md">
          <Input
            placeholder="搜索服务名称或描述..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <div className="flex items-center gap-2">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          >
            <option value="all">全部状态</option>
            <option value="active">已开通</option>
            <option value="inactive">未开通</option>
          </select>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-7 max-w-4xl">
          {serviceTypes.map((type) => (
            <TabsTrigger key={type.id} value={type.id}>
              {type.name}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredServices.map((service) => (
              <Card key={service.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      <div>{service.icon}</div>
                      <CardTitle className="text-lg">{service.name}</CardTitle>
                    </div>
                    <Badge variant={service.status === "active" ? "default" : "outline"}>
                      {service.status === "active" ? "已开通" : "未开通"}
                    </Badge>
                  </div>
                  <CardDescription className="line-clamp-2 mt-1">{service.description}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="text-sm space-y-2">
                    <div>
                      <span className="font-medium">价格：</span>
                      <span className="text-primary">{service.price}</span>
                    </div>
                    <div>
                      <span className="font-medium">类别：</span>
                      {service.category}
                    </div>
                    <div>
                      <span className="font-medium">主要功能：</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {service.features.map((feature: string, index: number) => (
                          <Badge key={index} variant="outline" className="bg-muted">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-2 flex gap-2">
                  {service.status === "active" && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => handleEnterService(service)}
                    >
                      进入服务
                    </Button>
                  )}
                  <Button
                    variant={service.status === "active" ? "destructive" : "default"}
                    size="sm"
                    className={service.status === "active" ? "flex-1" : "w-full"}
                    onClick={() => handleToggleService(service)}
                  >
                    {service.status === "active" ? (
                      <>
                        <Lock className="mr-2 h-4 w-4" />
                        取消服务
                      </>
                    ) : (
                      <>
                        <Unlock className="mr-2 h-4 w-4" />
                        开通服务
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* 服务详情对话框 */}
      {showServiceDetails && selectedService && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-2">
                  {selectedService.icon}
                  <h2 className="text-xl font-semibold">{selectedService.name}</h2>
                </div>
                <Button variant="ghost" size="icon" onClick={() => setShowServiceDetails(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <p className="text-muted-foreground mb-4">{selectedService.description}</p>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <h3 className="font-medium mb-2">价格</h3>
                  <p className="text-primary font-semibold">{selectedService.price}</p>
                </div>
                <div>
                  <h3 className="font-medium mb-2">状态</h3>
                  <Badge variant={selectedService.status === "active" ? "default" : "outline"}>
                    {selectedService.status === "active" ? "已开通" : "未开通"}
                  </Badge>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="font-medium mb-2">功能特点</h3>
                <ul className="space-y-1">
                  {selectedService.features.map((feature: string, index: number) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-4 w-4 text-green-500 mr-2 mt-1" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {selectedService.subModules && selectedService.subModules.length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">功能模块</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {selectedService.subModules.map((module: any, index: number) => (
                      <Link
                        key={index}
                        href={module.route}
                        className="flex items-center p-3 border rounded-md hover:bg-muted transition-colors"
                      >
                        <span>{module.name}</span>
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-2 mt-6">
                <Button variant="outline" onClick={() => setShowServiceDetails(false)}>
                  关闭
                </Button>
                {selectedService.status === "active" ? (
                  <Button variant="destructive" onClick={() => handleToggleService(selectedService)}>
                    <Lock className="mr-2 h-4 w-4" />
                    取消服务
                  </Button>
                ) : (
                  <Button onClick={() => {
                    setShowServiceDetails(false)
                    setTimeout(() => setShowActivateDialog(true), 100)
                  }}>
                    <Unlock className="mr-2 h-4 w-4" />
                    开通服务
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 开通服务对话框 */}
      {showActivateDialog && selectedService && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-md">
            <div className="p-6">
              {!activationSuccess ? (
                <>
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold">开通服务</h2>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowActivateDialog(false)}
                      disabled={activationLoading}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center gap-3 mb-4">
                      {selectedService.icon}
                      <div>
                        <h3 className="font-medium">{selectedService.name}</h3>
                        <p className="text-sm text-muted-foreground">{selectedService.category}</p>
                      </div>
                    </div>

                    <div className="bg-muted p-4 rounded-md mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm">服务价格</span>
                        <span className="font-medium text-primary">{selectedService.price}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">服务状态</span>
                        <Badge variant="outline">未开通</Badge>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="activation-period" className="block mb-2">选择开通时长</Label>
                        <select
                          id="activation-period"
                          value={activationPeriod}
                          onChange={(e) => setActivationPeriod(e.target.value)}
                          className="w-full h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                          disabled={activationLoading}
                        >
                          <option value="1">1个月</option>
                          <option value="3">3个月</option>
                          <option value="6">6个月</option>
                          <option value="12">12个月</option>
                          <option value="24">24个月</option>
                        </select>
                      </div>

                      <div className="bg-blue-50 p-3 rounded-md border border-blue-100">
                        <div className="flex items-start gap-2">
                          <div className="text-blue-500 mt-0.5">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <circle cx="12" cy="12" r="10"></circle>
                              <path d="M12 16v-4"></path>
                              <path d="M12 8h.01"></path>
                            </svg>
                          </div>
                          <div className="text-sm text-blue-700">
                            <p className="font-medium">开通提示</p>
                            <p>选择更长的开通时长可享受更多优惠。开通后，您可以立即使用该服务的所有功能。</p>
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-between items-center font-medium">
                        <span>总计费用</span>
                        <span className="text-primary text-lg">
                          ¥{parseInt(selectedService.price) * parseInt(activationPeriod)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowActivateDialog(false)}
                      disabled={activationLoading}
                    >
                      取消
                    </Button>
                    <Button
                      onClick={handleActivateService}
                      disabled={activationLoading}
                    >
                      {activationLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          处理中...
                        </>
                      ) : (
                        "确认开通"
                      )}
                    </Button>
                  </div>
                </>
              ) : (
                <div className="text-center py-6">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Check className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-xl font-semibold mb-2">开通成功！</h2>
                  <p className="text-muted-foreground mb-6">
                    您已成功开通 {selectedService.name} 服务，有效期 {activationPeriod} 个月
                  </p>
                  <Button className="w-full" onClick={() => {
                    // 对于共享股东服务，直接跳转到股东列表页
                    if (selectedService.id === "6" && selectedService.name === "共享股东") {
                      window.location.href = "/shareholders"
                    } else {
                      window.location.href = selectedService.route
                    }
                  }}>
                    立即使用
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 订购新服务对话框 */}
      {showOrderDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-3xl max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">订购新服务</h2>
                <Button variant="ghost" size="icon" onClick={() => setShowOrderDialog(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">选择服务类型</h3>
                <div className="grid grid-cols-3 gap-2">
                  {serviceTypes.slice(1).map((type) => (
                    <Button
                      key={type.id}
                      variant="outline"
                      className="justify-start h-auto py-3"
                    >
                      {type.name}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">推荐服务</h3>
                <div className="grid grid-cols-1 gap-4">
                  {premiumServices
                    .filter(service => service.status === "inactive")
                    .slice(0, 3)
                    .map(service => (
                      <div key={service.id} className="border rounded-lg p-4 flex justify-between items-center">
                        <div className="flex items-center gap-3">
                          {service.icon}
                          <div>
                            <h4 className="font-medium">{service.name}</h4>
                            <p className="text-sm text-muted-foreground line-clamp-1">{service.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="text-primary font-medium">{service.price}</span>
                          <Button size="sm" onClick={() => {
                            setShowOrderDialog(false)
                            setSelectedService(service)
                            setShowServiceDetails(true)
                          }}>
                            查看详情
                          </Button>
                        </div>
                      </div>
                    ))
                  }
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">服务套餐</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="border rounded-lg p-4 flex flex-col">
                    <h4 className="font-medium mb-2">基础套餐</h4>
                    <p className="text-sm text-muted-foreground mb-2">适合小型瑜伽工作室的基础服务包</p>
                    <ul className="text-sm space-y-1 mb-4 flex-1">
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>智能排课系统</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>私教绩效系统</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>基础数据分析</span>
                      </li>
                    </ul>
                    <div className="mt-auto">
                      <div className="text-primary font-medium mb-2">¥599/月</div>
                      <Button size="sm" className="w-full">选择套餐</Button>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4 flex flex-col relative overflow-hidden">
                    <div className="absolute top-0 right-0 bg-primary text-primary-foreground px-2 py-0.5 text-xs">
                      推荐
                    </div>
                    <h4 className="font-medium mb-2">专业套餐</h4>
                    <p className="text-sm text-muted-foreground mb-2">适合中型瑜伽工作室的全面服务包</p>
                    <ul className="text-sm space-y-1 mb-4 flex-1">
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>智能排课系统</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>私教绩效系统</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>会员数据分析</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>多渠道营销工具</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>股东管理系统</span>
                      </li>
                    </ul>
                    <div className="mt-auto">
                      <div className="text-primary font-medium mb-2">¥1299/月</div>
                      <Button size="sm" className="w-full">选择套餐</Button>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4 flex flex-col">
                    <h4 className="font-medium mb-2">全能套餐</h4>
                    <p className="text-sm text-muted-foreground mb-2">适合大型瑜伽连锁的全套解决方案</p>
                    <ul className="text-sm space-y-1 mb-4 flex-1">
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>所有专业套餐功能</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>高级财务报表</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>瑜伽商城系统</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>多门店管理</span>
                      </li>
                      <li className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                        <span>专属客户经理</span>
                      </li>
                    </ul>
                    <div className="mt-auto">
                      <div className="text-primary font-medium mb-2">¥2499/月</div>
                      <Button size="sm" className="w-full">选择套餐</Button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2 mt-6">
                <Button variant="outline" onClick={() => setShowOrderDialog(false)}>
                  取消
                </Button>
                <Button onClick={() => setShowOrderDialog(false)}>
                  联系客服咨询
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
