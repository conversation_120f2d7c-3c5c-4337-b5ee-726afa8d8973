"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Tag, Search, Plus, X, Check, Users } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

interface BatchTagsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedMemberIds: string[]
  memberCount: number
}

export function BatchTagsDialog({ open, onOpenChange, selectedMemberIds, memberCount }: BatchTagsDialogProps) {
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [operation, setOperation] = useState<"add" | "remove">("add")
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // 模拟标签数据
  const allTags = [
    { id: "1", name: "新会员", color: "#4CAF50", count: 45 },
    { id: "2", name: "高频用户", color: "#2196F3", count: 28 },
    { id: "3", name: "VIP", color: "#FFC107", count: 15 },
    { id: "4", name: "潜在流失", color: "#F44336", count: 12 },
    { id: "5", name: "已推荐", color: "#9C27B0", count: 8 },
    { id: "6", name: "生日月", color: "#E91E63", count: 22 },
    { id: "7", name: "瑜伽爱好者", color: "#3F51B5", count: 34 },
    { id: "8", name: "普拉提爱好者", color: "#009688", count: 19 },
    { id: "9", name: "周末班", color: "#FF5722", count: 27 },
    { id: "10", name: "工作日班", color: "#795548", count: 31 },
    { id: "11", name: "早班", color: "#607D8B", count: 16 },
    { id: "12", name: "晚班", color: "#9E9E9E", count: 23 },
  ]
  
  // 根据搜索过滤标签
  const filteredTags = allTags.filter(tag => 
    tag.name.toLowerCase().includes(searchQuery.toLowerCase())
  )
  
  // 处理标签选择
  const toggleTag = (tagId: string) => {
    if (selectedTags.includes(tagId)) {
      setSelectedTags(selectedTags.filter(id => id !== tagId))
    } else {
      setSelectedTags([...selectedTags, tagId])
    }
  }
  
  // 处理批量操作
  const handleBatchOperation = async () => {
    if (selectedTags.length === 0) {
      toast({
        title: "请选择标签",
        description: "请至少选择一个标签进行操作",
        variant: "destructive",
      })
      return
    }
    
    setIsSubmitting(true)
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const operationText = operation === "add" ? "添加" : "移除"
      const tagNames = selectedTags.map(id => {
        const tag = allTags.find(t => t.id === id)
        return tag ? tag.name : ""
      }).filter(Boolean).join("、")
      
      toast({
        title: `批量${operationText}标签成功`,
        description: `已为 ${memberCount} 名会员${operationText}标签: ${tagNames}`,
      })
      
      onOpenChange(false)
    } catch (error) {
      toast({
        title: "操作失败",
        description: "批量操作标签时出现错误，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>批量管理标签</DialogTitle>
          <DialogDescription>
            为 {memberCount} 名选中的会员批量添加或移除标签
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div>
            <Label>操作类型</Label>
            <RadioGroup
              value={operation}
              onValueChange={(value) => setOperation(value as "add" | "remove")}
              className="flex space-x-4 mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="add" id="add" />
                <Label htmlFor="add">添加标签</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="remove" id="remove" />
                <Label htmlFor="remove">移除标签</Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>选择标签</Label>
              <span className="text-sm text-muted-foreground">
                已选择 {selectedTags.length} 个标签
              </span>
            </div>
            
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索标签..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div>
              <Label className="text-sm text-muted-foreground">已选择的标签</Label>
              <div className="flex flex-wrap gap-2 mt-2 min-h-10 p-2 border rounded-md">
                {selectedTags.length === 0 ? (
                  <span className="text-sm text-muted-foreground">未选择任何标签</span>
                ) : (
                  selectedTags.map(tagId => {
                    const tag = allTags.find(t => t.id === tagId)
                    if (!tag) return null
                    return (
                      <Badge 
                        key={tagId} 
                        style={{ backgroundColor: tag.color }}
                        className="text-white flex items-center gap-1"
                      >
                        {tag.name}
                        <X 
                          className="h-3 w-3 cursor-pointer" 
                          onClick={() => toggleTag(tagId)}
                        />
                      </Badge>
                    )
                  })
                )}
              </div>
            </div>
            
            <ScrollArea className="h-[300px] border rounded-md p-4">
              <div className="grid grid-cols-2 gap-2">
                {filteredTags.map(tag => (
                  <div 
                    key={tag.id}
                    className={`flex items-center justify-between p-2 rounded-md border cursor-pointer hover:bg-muted transition-colors ${
                      selectedTags.includes(tag.id) ? 'bg-muted' : ''
                    }`}
                    onClick={() => toggleTag(tag.id)}
                  >
                    <div className="flex items-center gap-2">
                      <div 
                        className="h-4 w-4 rounded-full" 
                        style={{ backgroundColor: tag.color }}
                      />
                      <span>{tag.name}</span>
                      <span className="text-xs text-muted-foreground">({tag.count})</span>
                    </div>
                    {selectedTags.includes(tag.id) && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
        
        <DialogFooter className="flex justify-between">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">将{operation === "add" ? "添加" : "移除"}标签至 {memberCount} 名会员</span>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleBatchOperation} disabled={isSubmitting}>
              {isSubmitting ? "处理中..." : `批量${operation === "add" ? "添加" : "移除"}标签`}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
