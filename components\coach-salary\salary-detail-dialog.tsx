"use client"

import { useEffect, useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { Download, Printer } from "lucide-react"

// 薪资记录状态映射
const statusMap = {
  pending: { label: "待审核", color: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100/80" },
  approved: { label: "已审核", color: "bg-blue-100 text-blue-800 hover:bg-blue-100/80" },
  paid: { label: "已发放", color: "bg-green-100 text-green-800 hover:bg-green-100/80" },
  rejected: { label: "已拒绝", color: "bg-red-100 text-red-800 hover:bg-red-100/80" },
}

// 薪资类型映射
const salaryTypeMap = {
  fixed: "固定薪资",
  hourly: "课时费",
  mixed: "底薪+课时费",
  commission: "底薪+提成",
  full: "底薪+课时费+提成",
}

interface SalaryDetailDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  record: any
}

export function SalaryDetailDialog({ open, onOpenChange, record }: SalaryDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const [detailData, setDetailData] = useState<any>(null)

  // 当记录数据变化时，获取详细数据
  useEffect(() => {
    if (record) {
      // 这里应该调用API获取薪资记录详情
      // 暂时使用模拟数据
      setDetailData({
        ...record,
        classRecords: [
          { date: "2023-09-02", courseName: "基础瑜伽", duration: 1.5, hourlyRate: 200, amount: 300 },
          { date: "2023-09-05", courseName: "高级瑜伽", duration: 2, hourlyRate: 250, amount: 500 },
          { date: "2023-09-08", courseName: "阴瑜伽", duration: 1.5, hourlyRate: 200, amount: 300 },
          { date: "2023-09-12", courseName: "基础瑜伽", duration: 1.5, hourlyRate: 200, amount: 300 },
          { date: "2023-09-15", courseName: "高级瑜伽", duration: 2, hourlyRate: 250, amount: 500 },
          { date: "2023-09-19", courseName: "阴瑜伽", duration: 1.5, hourlyRate: 200, amount: 300 },
          { date: "2023-09-22", courseName: "基础瑜伽", duration: 1.5, hourlyRate: 200, amount: 300 },
          { date: "2023-09-25", courseName: "高级瑜伽", duration: 2, hourlyRate: 250, amount: 500 },
          { date: "2023-09-29", courseName: "阴瑜伽", duration: 1.5, hourlyRate: 200, amount: 300 },
        ],
        commissionRecords: [
          { date: "2023-09-02", courseName: "私教课", income: 5000, rate: 10, amount: 500 },
          { date: "2023-09-10", courseName: "私教课", income: 8000, rate: 10, amount: 800 },
          { date: "2023-09-18", courseName: "私教课", income: 12000, rate: 10, amount: 1200 },
        ],
        bonusRecords: [
          { date: "2023-09-15", reason: "绩效奖金", amount: 500 },
          { date: "2023-09-25", reason: "满勤奖", amount: 500 },
        ],
        deductionRecords: [
          { date: "2023-09-12", reason: "迟到", amount: 200 },
          { date: "2023-09-20", reason: "请假", amount: 300 },
        ],
        taxDetails: {
          taxableIncome: 17000,
          taxRate: 10,
          quickDeduction: 210,
          taxAmount: 1490,
        },
        socialInsurance: {
          pensionInsurance: 800,
          medicalInsurance: 300,
          unemploymentInsurance: 50,
          employmentInjuryInsurance: 20,
          maternityInsurance: 30,
          total: 1200,
        },
        housingFund: {
          personalPart: 500,
          companyPart: 500,
        },
        approvalHistory: [
          { date: "2023-10-05 14:30:00", operator: "张经理", action: "提交审核", remark: "薪资计算完成，提交审核" },
          { date: "2023-10-06 10:15:00", operator: "王总监", action: "审核通过", remark: "薪资核算无误，审核通过" },
          { date: "2023-10-10 09:30:00", operator: "李财务", action: "发放薪资", remark: "已通过银行转账发放薪资" },
        ],
      })
    }
  }, [record])

  // 打印薪资单
  const printSalarySlip = () => {
    window.print()
  }

  // 导出薪资单
  const exportSalarySlip = () => {
    // 这里应该调用API导出薪资单
    alert("薪资单导出功能待实现")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px]">
        {record && detailData && (
          <>
            <DialogHeader>
              <DialogTitle>薪资详情</DialogTitle>
              <DialogDescription>
                {record.coachName} - {record.salaryMonth} 薪资详情
              </DialogDescription>
            </DialogHeader>
            
            <div className="flex justify-between items-center">
              <Badge
                variant="outline"
                className={statusMap[record.status as keyof typeof statusMap].color}
              >
                {statusMap[record.status as keyof typeof statusMap].label}
              </Badge>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={printSalarySlip} className="gap-1">
                  <Printer className="h-4 w-4" />
                  打印
                </Button>
                <Button variant="outline" size="sm" onClick={exportSalarySlip} className="gap-1">
                  <Download className="h-4 w-4" />
                  导出
                </Button>
              </div>
            </div>
            
            <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">薪资概览</TabsTrigger>
                <TabsTrigger value="details">明细详情</TabsTrigger>
                <TabsTrigger value="tax">税费明细</TabsTrigger>
                <TabsTrigger value="approval">审批记录</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview" className="space-y-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">教练姓名</p>
                          <p className="font-medium">{detailData.coachName}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">薪资月份</p>
                          <p className="font-medium">{detailData.salaryMonth}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">薪资类型</p>
                          <p className="font-medium">{salaryTypeMap[detailData.salaryType as keyof typeof salaryTypeMap]}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">创建时间</p>
                          <p className="font-medium">{detailData.createdAt}</p>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">底薪:</span>
                          <span>¥{detailData.baseSalary.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">课时费 ({detailData.classHours}课时):</span>
                          <span>¥{detailData.hourlySalary.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">提成:</span>
                          <span>¥{detailData.commissionAmount.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">奖金:</span>
                          <span>¥{detailData.bonusAmount.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">扣款:</span>
                          <span className="text-red-500">-¥{detailData.deductionAmount.toLocaleString()}</span>
                        </div>
                        <Separator />
                        <div className="flex justify-between font-medium">
                          <span>总薪资:</span>
                          <span>¥{detailData.totalSalary.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">个人所得税:</span>
                          <span className="text-red-500">-¥{detailData.taxDetails?.taxAmount.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">社保:</span>
                          <span className="text-red-500">-¥{detailData.socialInsurance?.total.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">公积金:</span>
                          <span className="text-red-500">-¥{detailData.housingFund?.personalPart.toLocaleString()}</span>
                        </div>
                        <Separator />
                        <div className="flex justify-between text-lg font-bold">
                          <span>实发薪资:</span>
                          <span>¥{detailData.netSalary.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="details" className="space-y-4">
                {detailData.classRecords.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">课时费明细</h4>
                    <div className="max-h-40 overflow-y-auto rounded-md border p-2">
                      {detailData.classRecords.map((record: any, index: number) => (
                        <div key={index} className="flex justify-between py-1 text-sm">
                          <span>{record.date} {record.courseName} ({record.duration}课时)</span>
                          <span>¥{record.amount}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {detailData.commissionRecords.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">提成明细</h4>
                    <div className="max-h-40 overflow-y-auto rounded-md border p-2">
                      {detailData.commissionRecords.map((record: any, index: number) => (
                        <div key={index} className="flex justify-between py-1 text-sm">
                          <span>{record.date} {record.courseName} (¥{record.income} × {record.rate}%)</span>
                          <span>¥{record.amount}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {detailData.bonusRecords.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">奖金明细</h4>
                    <div className="max-h-40 overflow-y-auto rounded-md border p-2">
                      {detailData.bonusRecords.map((record: any, index: number) => (
                        <div key={index} className="flex justify-between py-1 text-sm">
                          <span>{record.date} {record.reason}</span>
                          <span>¥{record.amount}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {detailData.deductionRecords.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">扣款明细</h4>
                    <div className="max-h-40 overflow-y-auto rounded-md border p-2">
                      {detailData.deductionRecords.map((record: any, index: number) => (
                        <div key={index} className="flex justify-between py-1 text-sm">
                          <span>{record.date} {record.reason}</span>
                          <span className="text-red-500">-¥{record.amount}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="tax" className="space-y-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">个人所得税计算</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">应纳税所得额:</span>
                          <span>¥{detailData.taxDetails?.taxableIncome.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">适用税率:</span>
                          <span>{detailData.taxDetails?.taxRate}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">速算扣除数:</span>
                          <span>¥{detailData.taxDetails?.quickDeduction.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between font-medium">
                          <span>个人所得税:</span>
                          <span>¥{detailData.taxDetails?.taxAmount.toLocaleString()}</span>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <h4 className="font-medium">社会保险</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">养老保险:</span>
                          <span>¥{detailData.socialInsurance?.pensionInsurance.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">医疗保险:</span>
                          <span>¥{detailData.socialInsurance?.medicalInsurance.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">失业保险:</span>
                          <span>¥{detailData.socialInsurance?.unemploymentInsurance.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">工伤保险:</span>
                          <span>¥{detailData.socialInsurance?.employmentInjuryInsurance.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">生育保险:</span>
                          <span>¥{detailData.socialInsurance?.maternityInsurance.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between font-medium">
                          <span>社保合计:</span>
                          <span>¥{detailData.socialInsurance?.total.toLocaleString()}</span>
                        </div>
                      </div>
                      
                      <Separator />
                      
                      <h4 className="font-medium">住房公积金</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">个人部分:</span>
                          <span>¥{detailData.housingFund?.personalPart.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">公司部分:</span>
                          <span>¥{detailData.housingFund?.companyPart.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="approval" className="space-y-4">
                <div className="space-y-4">
                  {detailData.approvalHistory.map((record: any, index: number) => (
                    <div key={index} className="flex items-start gap-4 pb-4 border-b last:border-0">
                      <div className="w-32 flex-shrink-0">
                        <p className="text-sm font-medium">{record.date.split(' ')[0]}</p>
                        <p className="text-xs text-muted-foreground">{record.date.split(' ')[1]}</p>
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{record.action}</p>
                        <p className="text-sm text-muted-foreground">操作人: {record.operator}</p>
                        {record.remark && (
                          <p className="text-sm mt-1">备注: {record.remark}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
            
            <DialogFooter>
              <Button onClick={() => onOpenChange(false)}>关闭</Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}
