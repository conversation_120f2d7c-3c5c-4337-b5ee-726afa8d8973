"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Search, Download, RefreshCw, Filter, SlidersHorizontal, MoreHorizontal,
  Eye, Truck, Printer, Send, CheckCircle, X, Clock, AlertCircle,
  ShoppingBag, Calendar, CreditCard, BarChart3, FileText
} from "lucide-react"
import { addDays, format } from "date-fns"
import { DateRange } from "react-day-picker"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { cn } from "@/lib/utils"

// 模拟订单数据
const orders = [
  {
    id: "ORD-20230501-001",
    customer: {
      name: "张三",
      phone: "13800138001",
      avatar: "/avatars/01.png",
      memberType: "黄金会员"
    },
    orderDate: "2023-05-01 14:30:25",
    totalAmount: 199.00,
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "logistics", // 物流配送
    deliveryStatus: "delivered", // 已送达
    products: [
      { name: "瑜伽垫 - 专业防滑", quantity: 1, price: 199.00, type: "physical", category: "equipment" }
    ],
    orderType: "product", // 商品订单
    source: "website", // 订单来源
    address: "上海市浦东新区XX路XX号",
    trackingNumber: "SF1234567890",
    remark: "",
  },
  {
    id: "ORD-20230515-002",
    customer: {
      name: "李四",
      phone: "13800138002",
      avatar: "/avatars/02.png",
      memberType: "普通会员"
    },
    orderDate: "2023-05-15 09:20:15",
    totalAmount: 2580.00,
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "alipay", // 支付宝
    deliveryMethod: "self_pickup", // 自提
    deliveryStatus: "delivered", // 已送达
    products: [
      { name: "瑜伽年卡", quantity: 1, price: 2580.00, type: "membership", category: "membership" }
    ],
    orderType: "membership", // 会员订单
    source: "app", // 订单来源
    address: "",
    trackingNumber: "",
    remark: "会员卡已激活",
  },
  {
    id: "ORD-20230515-003",
    customer: {
      name: "王五",
      phone: "13800138003",
      avatar: "/avatars/03.png",
      memberType: "白金会员"
    },
    orderDate: "2023-05-15 16:45:30",
    totalAmount: 1588.00,
    status: "processing", // 处理中
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "logistics", // 物流配送
    deliveryStatus: "shipping", // 配送中
    products: [
      { name: "瑜伽服套装 - 女款", quantity: 1, price: 1588.00, type: "physical", category: "apparel" }
    ],
    orderType: "product", // 商品订单
    source: "wechat", // 订单来源
    address: "北京市朝阳区XX街XX号",
    trackingNumber: "YT9876543210",
    remark: "尺码: M",
  },
  {
    id: "ORD-20230520-004",
    customer: {
      name: "赵六",
      phone: "13800138004",
      avatar: "/avatars/04.png",
      memberType: "普通会员"
    },
    orderDate: "2023-05-20 09:30:45",
    totalAmount: 99.00,
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "balance", // 余额支付
    deliveryMethod: "verification", // 核销
    deliveryStatus: "delivered", // 已送达
    products: [
      { name: "瑜伽单次体验课", quantity: 1, price: 99.00, type: "course", category: "course" }
    ],
    orderType: "course", // 课程订单
    source: "store", // 订单来源
    address: "",
    trackingNumber: "",
    remark: "已核销",
  },
  {
    id: "ORD-20230525-005",
    customer: {
      name: "孙七",
      phone: "13800138005",
      avatar: "/avatars/05.png",
      memberType: "普通会员"
    },
    orderDate: "2023-05-25 13:20:18",
    totalAmount: 49.00,
    status: "completed", // 已完成
    paymentStatus: "paid", // 已支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "verification", // 核销
    deliveryStatus: "delivered", // 已送达
    products: [
      { name: "瑜伽视频教程 - 初级", quantity: 1, price: 49.00, type: "virtual", category: "course" }
    ],
    orderType: "digital", // 数字商品订单
    source: "website", // 订单来源
    address: "",
    trackingNumber: "",
    remark: "",
  },
  {
    id: "ORD-20230601-006",
    customer: {
      name: "周八",
      phone: "13800138006",
      avatar: "/avatars/06.png",
      memberType: "普通会员"
    },
    orderDate: "2023-06-01 11:05:33",
    totalAmount: 1288.00,
    status: "pending", // 待处理
    paymentStatus: "unpaid", // 未支付
    paymentMethod: "wechat", // 微信支付
    deliveryMethod: "verification", // 核销
    deliveryStatus: "pending", // 待处理
    products: [
      { name: "高级私教身份 - 月卡", quantity: 1, price: 1288.00, type: "virtual", category: "membership" }
    ],
    orderType: "membership", // 会员订单
    source: "app", // 订单来源
    address: "",
    trackingNumber: "",
    remark: "等待支付",
  },
]

// 订单统计数据
const orderStatistics = {
  total: 256,
  completed: 198,
  processing: 42,
  pending: 16,
  totalAmount: 128560.00,
  todayOrders: 24,
  todayAmount: 12480.00,
  productOrders: 145,
  courseOrders: 68,
  membershipOrders: 43
}

export default function YogaShopOrderManagementPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [paymentStatus, setPaymentStatus] = useState("all")
  const [orderStatus, setOrderStatus] = useState("all")
  const [deliveryMethod, setDeliveryMethod] = useState("all")
  const [orderType, setOrderType] = useState("all")
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: new Date(),
  })
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [showOrderDetail, setShowOrderDetail] = useState(false)
  const [selectedOrderId, setSelectedOrderId] = useState("")
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false)
  const [showBatchOperations, setShowBatchOperations] = useState(false)

  // 过滤订单
  const filteredOrders = orders.filter((order) => {
    // 基本搜索过滤
    const searchFilter =
      searchQuery === "" ||
      order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.customer.phone.includes(searchQuery) ||
      order.products.some(p => p.name.toLowerCase().includes(searchQuery.toLowerCase()));

    // 标签页过滤
    const tabFilter =
      activeTab === "all" ||
      (activeTab === "product" && order.orderType === "product") ||
      (activeTab === "course" && order.orderType === "course") ||
      (activeTab === "membership" && order.orderType === "membership") ||
      (activeTab === "digital" && order.orderType === "digital") ||
      (activeTab === "pending-payment" && order.paymentStatus === "unpaid");

    // 支付状态过滤
    const paymentFilter =
      paymentStatus === "all" ||
      order.paymentStatus === paymentStatus;

    // 订单状态过滤
    const statusFilter =
      orderStatus === "all" ||
      order.status === orderStatus;

    // 配送方式过滤
    const deliveryFilter =
      deliveryMethod === "all" ||
      order.deliveryMethod === deliveryMethod;

    // 订单类型过滤
    const typeFilter =
      orderType === "all" ||
      order.orderType === orderType;

    // 日期范围过滤
    const orderDate = new Date(order.orderDate);
    const dateFilter =
      !dateRange ||
      !dateRange.from ||
      (orderDate >= dateRange.from &&
       (!dateRange.to || orderDate <= dateRange.to));

    return searchFilter && tabFilter && paymentFilter && statusFilter && deliveryFilter && typeFilter && dateFilter;
  });

  // 处理查看订单详情
  const handleViewOrderDetail = (orderId: string) => {
    setSelectedOrderId(orderId);
    setShowOrderDetail(true);
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedOrders(filteredOrders.map(order => order.id));
    } else {
      setSelectedOrders([]);
    }
  };

  // 处理批量操作
  const handleBatchOperation = (operation: string) => {
    console.log(`对订单 ${selectedOrders.join(", ")} 执行操作: ${operation}`);
    // 实际应用中，这里会执行相应的批量操作
    alert(`已对 ${selectedOrders.length} 个订单执行 ${operation} 操作`);
    setSelectedOrders([]);
  };

  // 处理应用高级筛选
  const handleApplyFilters = (filters: any) => {
    console.log("应用高级筛选:", filters);
    // 实际应用中，这里会更新筛选状态
    alert("已应用高级筛选条件");
  };

  // 获取订单状态标签
  const getStatusBadge = (status: string, paymentStatus: string) => {
    if (paymentStatus === "unpaid") {
      return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 hover:bg-yellow-50 border-yellow-200">待付款</Badge>;
    }

    switch (status) {
      case "pending":
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50 border-blue-200">待处理</Badge>;
      case "processing":
        return <Badge variant="outline" className="bg-indigo-50 text-indigo-700 hover:bg-indigo-50 border-indigo-200">处理中</Badge>;
      case "completed":
        return <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50 border-green-200">已完成</Badge>;
      case "cancelled":
        return <Badge variant="outline" className="bg-red-50 text-red-700 hover:bg-red-50 border-red-200">已取消</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // 获取订单类型图标
  const getOrderTypeIcon = (orderType: string) => {
    switch (orderType) {
      case "product":
        return <ShoppingBag className="h-4 w-4 text-blue-500" />;
      case "course":
        return <Calendar className="h-4 w-4 text-purple-500" />;
      case "membership":
        return <CreditCard className="h-4 w-4 text-green-500" />;
      case "digital":
        return <FileText className="h-4 w-4 text-orange-500" />;
      default:
        return <ShoppingBag className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">瑜伽商城订单管理</h1>
        <div className="flex items-center gap-2">
          {selectedOrders.length > 0 ? (
            <>
              <Button variant="outline" onClick={() => setShowBatchOperations(true)}>
                批量操作 ({selectedOrders.length})
              </Button>
              <Button variant="outline" onClick={() => setSelectedOrders([])}>
                <X className="h-4 w-4 mr-2" />
                取消选择
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={() => setShowAdvancedFilter(true)}>
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                高级筛选
              </Button>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                导出订单
              </Button>
              <Button variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 订单统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">订单总数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderStatistics.total}</div>
            <p className="text-xs text-muted-foreground">
              已完成: {orderStatistics.completed} | 处理中: {orderStatistics.processing}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待处理订单</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderStatistics.pending}</div>
            <p className="text-xs text-muted-foreground">
              待付款: {orderStatistics.pending} | 今日新增: {orderStatistics.todayOrders}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">订单金额</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">¥{orderStatistics.totalAmount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              今日: ¥{orderStatistics.todayAmount.toLocaleString()}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">订单分布</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-md font-medium">
              <span className="inline-flex items-center mr-2">
                <ShoppingBag className="h-3 w-3 mr-1 text-blue-500" />
                {orderStatistics.productOrders}
              </span>
              <span className="inline-flex items-center mr-2">
                <Calendar className="h-3 w-3 mr-1 text-purple-500" />
                {orderStatistics.courseOrders}
              </span>
              <span className="inline-flex items-center">
                <CreditCard className="h-3 w-3 mr-1 text-green-500" />
                {orderStatistics.membershipOrders}
              </span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              商品 | 课程 | 会员
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6 max-w-3xl">
          <TabsTrigger value="all">全部订单</TabsTrigger>
          <TabsTrigger value="product">商品订单</TabsTrigger>
          <TabsTrigger value="course">课程订单</TabsTrigger>
          <TabsTrigger value="membership">会员订单</TabsTrigger>
          <TabsTrigger value="digital">数字商品</TabsTrigger>
          <TabsTrigger value="pending-payment">待付款</TabsTrigger>
        </TabsList>
      </Tabs>

      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative w-full md:w-1/3">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索订单号、客户姓名或手机号"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-1 gap-2 flex-wrap md:flex-nowrap">
          <Select value={orderType} onValueChange={setOrderType}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="订单类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              <SelectItem value="product">商品订单</SelectItem>
              <SelectItem value="course">课程订单</SelectItem>
              <SelectItem value="membership">会员订单</SelectItem>
              <SelectItem value="digital">数字商品</SelectItem>
            </SelectContent>
          </Select>

          <Select value={paymentStatus} onValueChange={setPaymentStatus}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="支付状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部支付状态</SelectItem>
              <SelectItem value="paid">已支付</SelectItem>
              <SelectItem value="unpaid">待支付</SelectItem>
              <SelectItem value="refunded">已退款</SelectItem>
              <SelectItem value="failed">支付失败</SelectItem>
            </SelectContent>
          </Select>

          <Select value={deliveryMethod} onValueChange={setDeliveryMethod}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="配送方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部配送方式</SelectItem>
              <SelectItem value="logistics">物流配送</SelectItem>
              <SelectItem value="self_pickup">门店自提</SelectItem>
              <SelectItem value="verification">核销</SelectItem>
            </SelectContent>
          </Select>

          <DatePickerWithRange
            className="w-full"
            selected={dateRange}
            onSelect={setDateRange}
          />
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[40px]">
                  <Checkbox
                    checked={selectedOrders.length > 0 && selectedOrders.length === filteredOrders.length}
                    onCheckedChange={handleSelectAll}
                    aria-label="全选"
                  />
                </TableHead>
                <TableHead>订单号</TableHead>
                <TableHead>订单类型</TableHead>
                <TableHead>客户信息</TableHead>
                <TableHead>订单日期</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>订单状态</TableHead>
                <TableHead>支付方式</TableHead>
                <TableHead>配送方式</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={10} className="h-24 text-center">
                    没有找到符合条件的订单
                  </TableCell>
                </TableRow>
              ) : (
                filteredOrders.map((order) => (
                  <TableRow key={order.id} className={cn(selectedOrders.includes(order.id) && "bg-muted/50")}>
                    <TableCell>
                      <Checkbox
                        checked={selectedOrders.includes(order.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedOrders([...selectedOrders, order.id]);
                          } else {
                            setSelectedOrders(selectedOrders.filter(id => id !== order.id));
                          }
                        }}
                        aria-label={`选择订单 ${order.id}`}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{order.id}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {getOrderTypeIcon(order.orderType)}
                        <span className="text-xs">
                          {order.orderType === "product" ? "商品" :
                           order.orderType === "course" ? "课程" :
                           order.orderType === "membership" ? "会员" :
                           order.orderType === "digital" ? "数字商品" :
                           order.orderType}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-1">
                          <span>{order.customer.name}</span>
                          {order.customer.memberType && (
                            <Badge variant="outline" className="text-[10px] px-1 py-0 h-4">
                              {order.customer.memberType}
                            </Badge>
                          )}
                        </div>
                        <span className="text-muted-foreground text-xs">{order.customer.phone}</span>
                      </div>
                    </TableCell>
                    <TableCell>{order.orderDate}</TableCell>
                    <TableCell>¥{order.totalAmount.toFixed(2)}</TableCell>
                    <TableCell>{getStatusBadge(order.status, order.paymentStatus)}</TableCell>
                    <TableCell>
                      {order.paymentMethod === "wechat" ? "微信支付" :
                       order.paymentMethod === "alipay" ? "支付宝" :
                       order.paymentMethod === "balance" ? "余额支付" :
                       order.paymentMethod}
                    </TableCell>
                    <TableCell>
                      {order.deliveryMethod === "logistics" ? "物流配送" :
                       order.deliveryMethod === "self_pickup" ? "门店自提" :
                       order.deliveryMethod === "verification" ? "核销" :
                       order.deliveryMethod}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>订单操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleViewOrderDetail(order.id)}>
                            <Eye className="h-4 w-4 mr-2" />
                            查看详情
                          </DropdownMenuItem>

                          {order.paymentStatus === "unpaid" && (
                            <DropdownMenuItem>
                              <Clock className="h-4 w-4 mr-2" />
                              催付提醒
                            </DropdownMenuItem>
                          )}

                          {order.deliveryMethod === "logistics" && order.deliveryStatus === "shipping" && (
                            <DropdownMenuItem>
                              <Truck className="h-4 w-4 mr-2" />
                              查看物流
                            </DropdownMenuItem>
                          )}

                          <DropdownMenuItem>
                            <Printer className="h-4 w-4 mr-2" />
                            打印订单
                          </DropdownMenuItem>

                          <DropdownMenuItem>
                            <Send className="h-4 w-4 mr-2" />
                            发送订单
                          </DropdownMenuItem>

                          <DropdownMenuSeparator />

                          {order.status !== "completed" && order.status !== "cancelled" && (
                            <DropdownMenuItem>
                              <CheckCircle className="h-4 w-4 mr-2" />
                              标记为已完成
                            </DropdownMenuItem>
                          )}

                          {order.status !== "cancelled" && (
                            <DropdownMenuItem className="text-red-600">
                              <X className="h-4 w-4 mr-2" />
                              取消订单
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
