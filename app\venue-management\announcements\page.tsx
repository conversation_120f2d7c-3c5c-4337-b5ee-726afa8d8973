"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { CalendarIcon, BellRing, MoreHorizontal, Plus, Search, Eye, Edit, Trash2, Upload, Image as ImageIcon } from "lucide-react"

// 模拟公告数据
const announcements = [
  {
    id: 1,
    title: "国庆节营业时间调整通知",
    content: "尊敬的会员，国庆节期间（10月1日-10月7日）本馆营业时间调整为10:00-18:00，请知悉。",
    type: "营业通知",
    status: "已发布",
    publishDate: "2023-09-25",
    expireDate: "2023-10-08",
    isSticky: true,
    isPopup: true,
    viewCount: 245,
    author: "管理员",
  },
  {
    id: 2,
    title: "新增高温瑜伽课程",
    content: "本馆将于10月15日起新增高温瑜伽课程，每周二、四、六开课，欢迎会员预约体验。",
    type: "课程通知",
    status: "已发布",
    publishDate: "2023-09-30",
    expireDate: "2023-10-30",
    isSticky: false,
    isPopup: false,
    viewCount: 189,
    author: "课程部",
  },
  {
    id: 3,
    title: "会员积分兑换活动",
    content: "即日起至10月31日，会员可使用积分兑换瑜伽垫、瑜伽服等商品，详情请咨询前台。",
    type: "活动通知",
    status: "已发布",
    publishDate: "2023-10-01",
    expireDate: "2023-10-31",
    isSticky: true,
    isPopup: false,
    viewCount: 156,
    author: "市场部",
  },
  {
    id: 4,
    title: "场馆设施维护通知",
    content: "本馆将于10月10日进行设施维护，当日瑜伽教室2暂停使用，已预约的课程将调整至瑜伽教室1，给您带来不便，敬请谅解。",
    type: "场馆通知",
    status: "草稿",
    publishDate: "",
    expireDate: "",
    isSticky: false,
    isPopup: false,
    viewCount: 0,
    author: "管理员",
  },
  {
    id: 5,
    title: "教练团队调整通知",
    content: "即日起，Sarah教练将加入我们的团队，主要负责阴瑜伽和流瑜伽课程，欢迎会员预约体验。",
    type: "人员通知",
    status: "已过期",
    publishDate: "2023-09-01",
    expireDate: "2023-09-30",
    isSticky: false,
    isPopup: false,
    viewCount: 210,
    author: "人事部",
  },
]

// 公告类型
const announcementTypes = [
  { id: 1, name: "营业通知", color: "bg-blue-100 text-blue-800" },
  { id: 2, name: "课程通知", color: "bg-green-100 text-green-800" },
  { id: 3, name: "活动通知", color: "bg-purple-100 text-purple-800" },
  { id: 4, name: "场馆通知", color: "bg-orange-100 text-orange-800" },
  { id: 5, name: "人员通知", color: "bg-pink-100 text-pink-800" },
]

export default function AnnouncementsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [isAddAnnouncementDialogOpen, setIsAddAnnouncementDialogOpen] = useState(false)
  const [publishDate, setPublishDate] = useState<Date>()
  const [expireDate, setExpireDate] = useState<Date>()

  // 过滤公告数据
  const filteredAnnouncements = announcements.filter((announcement) => {
    const matchesSearch = announcement.title.includes(searchTerm) || 
                         announcement.content.includes(searchTerm)
    const matchesType = selectedType === "all" || announcement.type === selectedType
    const matchesStatus = selectedStatus === "all" || announcement.status === selectedStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  // 获取公告类型的颜色
  const getTypeColor = (type: string) => {
    const foundType = announcementTypes.find(t => t.name === type)
    return foundType ? foundType.color : "bg-gray-100 text-gray-800"
  }

  // 获取状态的颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case "已发布":
        return "bg-green-100 text-green-800"
      case "草稿":
        return "bg-gray-100 text-gray-800"
      case "已过期":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">公告管理</h1>
        <Button onClick={() => setIsAddAnnouncementDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          发布公告
        </Button>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>公告列表</CardTitle>
          <CardDescription>
            管理场馆的公告信息，包括营业通知、课程通知、活动通知等
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="搜索公告标题或内容..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select
              value={selectedType}
              onValueChange={setSelectedType}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择公告类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                {announcementTypes.map((type) => (
                  <SelectItem key={type.id} value={type.name}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={selectedStatus}
              onValueChange={setSelectedStatus}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                <SelectItem value="已发布">已发布</SelectItem>
                <SelectItem value="草稿">草稿</SelectItem>
                <SelectItem value="已过期">已过期</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>公告标题</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>发布日期</TableHead>
                <TableHead>过期日期</TableHead>
                <TableHead>置顶</TableHead>
                <TableHead>弹窗</TableHead>
                <TableHead>浏览量</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAnnouncements.map((announcement) => (
                <TableRow key={announcement.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <BellRing className="h-4 w-4 text-muted-foreground" />
                      <span>{announcement.title}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={cn("font-normal", getTypeColor(announcement.type))}>
                      {announcement.type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={cn("font-normal", getStatusColor(announcement.status))}>
                      {announcement.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{announcement.publishDate || "-"}</TableCell>
                  <TableCell>{announcement.expireDate || "-"}</TableCell>
                  <TableCell>
                    <Badge variant={announcement.isSticky ? "default" : "outline"} className="font-normal">
                      {announcement.isSticky ? "是" : "否"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={announcement.isPopup ? "default" : "outline"} className="font-normal">
                      {announcement.isPopup ? "是" : "否"}
                    </Badge>
                  </TableCell>
                  <TableCell>{announcement.viewCount}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">打开菜单</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>操作</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" />
                          编辑公告
                        </DropdownMenuItem>
                        {announcement.status === "草稿" && (
                          <DropdownMenuItem>
                            <BellRing className="mr-2 h-4 w-4" />
                            发布公告
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          删除公告
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* 添加公告对话框 */}
      <Dialog open={isAddAnnouncementDialogOpen} onOpenChange={setIsAddAnnouncementDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>发布新公告</DialogTitle>
            <DialogDescription>
              创建新的公告信息，设置公告的类型、发布时间和展示方式
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">公告标题</Label>
              <Input id="title" placeholder="请输入公告标题" />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="type">公告类型</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="选择公告类型" />
                </SelectTrigger>
                <SelectContent>
                  {announcementTypes.map((type) => (
                    <SelectItem key={type.id} value={type.name}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="content">公告内容</Label>
              <Textarea id="content" placeholder="请输入公告内容" rows={5} />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>发布日期</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !publishDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {publishDate ? format(publishDate, "yyyy-MM-dd", { locale: zhCN }) : "选择发布日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={publishDate}
                      onSelect={setPublishDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label>过期日期</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !expireDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {expireDate ? format(expireDate, "yyyy-MM-dd", { locale: zhCN }) : "选择过期日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={expireDate}
                      onSelect={setExpireDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>公告图片（可选）</Label>
              <div className="border rounded-md p-4 flex flex-col items-center justify-center">
                <div className="w-full h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
                  <ImageIcon className="h-8 w-8 text-gray-400" />
                </div>
                <Button variant="outline" size="sm">
                  <Upload className="mr-2 h-4 w-4" />
                  上传图片
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch id="sticky" />
                <Label htmlFor="sticky">置顶公告</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch id="popup" />
                <Label htmlFor="popup">弹窗显示</Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddAnnouncementDialogOpen(false)}>
              保存为草稿
            </Button>
            <Button onClick={() => setIsAddAnnouncementDialogOpen(false)}>
              立即发布
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
