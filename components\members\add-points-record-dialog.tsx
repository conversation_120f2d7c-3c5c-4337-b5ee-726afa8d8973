"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Coins, Plus, Minus } from "lucide-react"

// 积分类别选项
const pointsCategories = [
  { value: "purchase", label: "购买消费" },
  { value: "attendance", label: "课程签到" },
  { value: "referral", label: "推荐新会员" },
  { value: "birthday", label: "生日奖励" },
  { value: "activity", label: "活动参与" },
  { value: "exchange", label: "积分兑换" },
  { value: "expired", label: "积分过期" },
  { value: "adjustment", label: "手动调整" },
  { value: "other", label: "其他" },
]

interface AddPointsRecordDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: {
    id: string
    name: string
    avatar?: string
    points?: number
  } | null
  onPointsAdded?: () => void
}

export function AddPointsRecordDialog({
  open,
  onOpenChange,
  member,
  onPointsAdded,
}: AddPointsRecordDialogProps) {
  const [operationType, setOperationType] = useState("add")
  const [pointsAmount, setPointsAmount] = useState("")
  const [category, setCategory] = useState("")
  const [reason, setReason] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  // 重置表单
  const resetForm = () => {
    setOperationType("add")
    setPointsAmount("")
    setCategory("")
    setReason("")
  }

  // 处理对话框关闭
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetForm()
    }
    onOpenChange(open)
  }

  // 处理积分操作
  const handleSubmit = async () => {
    if (!member) return

    if (!pointsAmount || parseInt(pointsAmount) <= 0) {
      toast({
        title: "积分数量无效",
        description: "请输入有效的积分数量",
        variant: "destructive",
      })
      return
    }

    if (!category) {
      toast({
        title: "请选择积分类别",
        description: "请选择积分变动的类别",
        variant: "destructive",
      })
      return
    }

    if (!reason) {
      toast({
        title: "原因不能为空",
        description: "请输入积分操作的原因",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const operation = operationType === "add" ? "增加" : "扣除"
      const categoryLabel = pointsCategories.find(c => c.value === category)?.label || category
      
      toast({
        title: `积分${operation}成功`,
        description: `已成功为会员 ${member.name} ${operation} ${pointsAmount} 积分（${categoryLabel}）`,
      })
      
      if (onPointsAdded) {
        onPointsAdded()
      }
      
      handleOpenChange(false)
    } catch (error) {
      toast({
        title: "操作失败",
        description: "积分操作失败，请重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!member) return null

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>增加积分记录</DialogTitle>
          <DialogDescription>
            为会员添加或扣除积分，并记录相关信息
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="flex items-center gap-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={member.avatar} alt={member.name} />
              <AvatarFallback>{member.name?.[0]}</AvatarFallback>
            </Avatar>
            <div>
              <h4 className="font-medium">{member.name}</h4>
              <p className="text-sm text-muted-foreground">
                <Coins className="mr-1 h-4 w-4 inline-block" />
                当前积分: {member.points || 0}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label>操作类型</Label>
            <RadioGroup
              value={operationType}
              onValueChange={setOperationType}
              className="flex space-x-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="add" id="add" />
                <Label htmlFor="add" className="flex items-center">
                  <Plus className="mr-1 h-4 w-4 text-green-500" />
                  增加积分
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="subtract" id="subtract" />
                <Label htmlFor="subtract" className="flex items-center">
                  <Minus className="mr-1 h-4 w-4 text-red-500" />
                  扣除积分
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label htmlFor="points-amount">积分数量</Label>
            <Input
              id="points-amount"
              type="number"
              min="1"
              placeholder="请输入积分数量"
              value={pointsAmount}
              onChange={(e) => setPointsAmount(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="points-category">积分类别</Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger id="points-category">
                <SelectValue placeholder="选择积分类别" />
              </SelectTrigger>
              <SelectContent>
                {pointsCategories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">积分说明</Label>
            <Textarea
              id="reason"
              placeholder="请输入积分变动的详细说明..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => handleOpenChange(false)}>取消</Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "提交中..." : "确认提交"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
