"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>L<PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Building2, 
  Users, 
  Phone, 
  MapPin, 
  User, 
  Calendar, 
  Clock, 
  Edit, 
  ArrowUpDown,
  ChevronRight,
  ChevronLeft,
  BarChart4,
  FileText,
  Settings,
  UserCog,
  Dumbbell,
  DollarSign,
  Share2,
  Shuffle,
  RefreshCw,
  AlertTriangle
} from "lucide-react"
import { EditStoreDialog } from "@/components/chain-management/edit-store-dialog"
import { DeleteStoreDialog } from "@/components/chain-management/delete-store-dialog"
import { StoreStatsCard } from "@/components/chain-management/store-stats-card"
import { StoreCoachesCard } from "@/components/chain-management/store-coaches-card"
import { StoreClassesCard } from "@/components/chain-management/store-classes-card"
import { StoreMembersCard } from "@/components/chain-management/store-members-card"
import { useToast } from "@/hooks/use-toast"

// 模拟门店数据
const stores = [
  {
    id: "1",
    name: "瑜伽中心旗舰店",
    address: "北京市朝阳区建国路88号",
    phone: "010-12345678",
    manager: "张经理",
    status: "active",
    memberCount: 450,
    coachCount: 12,
    monthlyRevenue: 120000,
    openDate: "2020-05-15",
    lastSync: "2023-06-10T08:30:00",
    businessHours: "周一至周日 9:00-21:00",
    description: "旗舰店位于朝阳区中心地带，环境优雅，设施齐全，提供全系列瑜伽课程。",
    area: "500",
    capacity: "80",
    facilities: "高级瑜伽垫、专业音响系统、淋浴间、更衣室、休息区",
    syncSettings: "auto",
    region: "朝阳区",
    city: "北京市"
  },
  {
    id: "2",
    name: "瑜伽中心西城店",
    address: "北京市西城区西单北大街120号",
    phone: "010-87654321",
    manager: "李经理",
    status: "active",
    memberCount: 320,
    coachCount: 8,
    monthlyRevenue: 85000,
    openDate: "2021-03-20",
    lastSync: "2023-06-09T17:45:00",
    businessHours: "周一至周日 9:00-21:00",
    description: "西城店位于商业中心，交通便利，专注小班教学。",
    area: "350",
    capacity: "50",
    facilities: "瑜伽垫、瑜伽砖、瑜伽带、更衣室、休息区",
    syncSettings: "auto",
    region: "西城区",
    city: "北京市"
  },
  {
    id: "3",
    name: "瑜伽中心海淀店",
    address: "北京市海淀区中关村大街28号",
    phone: "010-56781234",
    manager: "王经理",
    status: "active",
    memberCount: 380,
    coachCount: 10,
    monthlyRevenue: 95000,
    openDate: "2021-08-10",
    lastSync: "2023-06-10T09:15:00",
    businessHours: "周一至周日 9:00-21:00",
    description: "海淀店位于大学区，主要面向年轻客户群体，提供多样化课程。",
    area: "400",
    capacity: "60",
    facilities: "瑜伽垫、瑜伽砖、瑜伽带、更衣室、休息区",
    syncSettings: "auto",
    region: "海淀区",
    city: "北京市"
  },
  {
    id: "4",
    name: "瑜伽中心通州店",
    address: "北京市通州区新华大街56号",
    phone: "010-43215678",
    manager: "赵经理",
    status: "inactive",
    memberCount: 180,
    coachCount: 5,
    monthlyRevenue: 45000,
    openDate: "2022-04-05",
    lastSync: "2023-06-08T14:20:00",
    businessHours: "周一至周日 9:00-21:00",
    description: "通州店是新开分店，正在积极发展中。",
    area: "300",
    capacity: "40",
    facilities: "瑜伽垫、瑜伽砖、瑜伽带、更衣室",
    syncSettings: "manual",
    region: "通州区",
    city: "北京市"
  },
]

export default function StoreDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("overview")
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [store, setStore] = useState<any>(null)

  // 获取门店数据
  useEffect(() => {
    const storeId = params.id as string
    const foundStore = stores.find(s => s.id === storeId)
    
    if (foundStore) {
      setStore(foundStore)
    } else {
      toast({
        title: "门店不存在",
        description: "未找到该门店信息，请返回门店列表",
        variant: "destructive",
      })
      router.push("/premium-services/chain-management?tab=stores")
    }
  }, [params.id, router, toast])

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN')
  }

  // 格式化时间
  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString)
    return date.toLocaleString('zh-CN')
  }

  // 同步数据
  const syncData = () => {
    setIsLoading(true)
    // 模拟API请求
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "同步成功",
        description: `${store.name} 的数据已成功同步`,
      })
    }, 1500)
  }

  // 删除门店后的回调
  const handleDeleteSuccess = () => {
    router.push("/premium-services/chain-management?tab=stores")
  }

  if (!store) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => router.push("/premium-services/chain-management?tab=stores")}
            >
              <ChevronLeft className="h-4 w-4" />
              返回列表
            </Button>
            <h1 className="text-2xl font-semibold tracking-tight">{store.name}</h1>
            <Badge variant={store.status === "active" ? "default" : "secondary"}>
              {store.status === "active" ? "营业中" : "已关闭"}
            </Badge>
          </div>
          <p className="text-muted-foreground mt-1">{store.address}</p>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button variant="outline" onClick={syncData} disabled={isLoading}>
            {isLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                同步中...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                同步数据
              </>
            )}
          </Button>
          <Button variant="outline" onClick={() => setShowEditDialog(true)}>
            <Edit className="mr-2 h-4 w-4" />
            编辑门店
          </Button>
          <Button variant="destructive" onClick={() => setShowDeleteDialog(true)}>
            删除门店
          </Button>
        </div>
      </div>

      <div className="text-sm breadcrumbs">
        <ul className="flex items-center space-x-1">
          <li><Link href="/premium-services" className="text-muted-foreground hover:text-foreground">增值服务</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li><Link href="/premium-services/chain-management" className="text-muted-foreground hover:text-foreground">瑜伽工作室连锁管理</Link></li>
          <li><ChevronRight className="h-4 w-4" /></li>
          <li>{store.name}</li>
        </ul>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">会员数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{store.memberCount}</div>
              <Users className="h-4 w-4 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              较上月增长 5.2%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">教练数量</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{store.coachCount}</div>
              <UserCog className="h-4 w-4 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              较上月增长 0%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">月营收</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">¥{store.monthlyRevenue.toLocaleString()}</div>
              <BarChart4 className="h-4 w-4 text-muted-foreground" />
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              较上月增长 3.8%
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="members">会员</TabsTrigger>
          <TabsTrigger value="coaches">教练</TabsTrigger>
          <TabsTrigger value="classes">课程</TabsTrigger>
          <TabsTrigger value="settings">设置</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>门店基本信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">门店名称</p>
                      <p className="font-medium">{store.name}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">营业状态</p>
                      <Badge variant={store.status === "active" ? "default" : "secondary"}>
                        {store.status === "active" ? "营业中" : "已关闭"}
                      </Badge>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">联系电话</p>
                      <p className="font-medium">{store.phone}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">店长</p>
                      <p className="font-medium">{store.manager}</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">门店地址</p>
                    <p className="font-medium">{store.address}</p>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">开业日期</p>
                      <p className="font-medium">{formatDate(store.openDate)}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">营业时间</p>
                      <p className="font-medium">{store.businessHours}</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">门店面积</p>
                      <p className="font-medium">{store.area} ㎡</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">容纳人数</p>
                      <p className="font-medium">{store.capacity} 人</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">门店描述</p>
                    <p className="font-medium">{store.description}</p>
                  </div>

                  <Separator />

                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">设施与设备</p>
                    <p className="font-medium">{store.facilities}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <StoreStatsCard store={store} />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <StoreCoachesCard store={store} />
            <StoreClassesCard store={store} />
          </div>
        </TabsContent>

        <TabsContent value="members">
          <StoreMembersCard store={store} />
        </TabsContent>

        <TabsContent value="coaches">
          <div className="p-4 text-center">
            <p>教练管理功能正在开发中...</p>
          </div>
        </TabsContent>

        <TabsContent value="classes">
          <div className="p-4 text-center">
            <p>课程管理功能正在开发中...</p>
          </div>
        </TabsContent>

        <TabsContent value="settings">
          <div className="p-4 text-center">
            <p>门店设置功能正在开发中...</p>
          </div>
        </TabsContent>
      </Tabs>

      <EditStoreDialog 
        open={showEditDialog} 
        onOpenChange={setShowEditDialog} 
        store={store} 
      />

      <DeleteStoreDialog 
        open={showDeleteDialog} 
        onOpenChange={setShowDeleteDialog} 
        store={store}
        onDeleteSuccess={handleDeleteSuccess}
      />
    </div>
  )
}
