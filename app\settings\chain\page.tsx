"use client";

import { useState, useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/auth-context";
import { Building2, Phone, Mail, MapPin, Calendar, FileText, Upload, Save, Loader2, Image, Camera, Edit2 } from "lucide-react";

// 中国省市区数据
import { provinces, cities, districts } from "./location-data";

// 表单验证模式
const chainInfoSchema = z.object({
  name: z.string().min(2, "连锁名称至少需要2个字符"),
  contactPerson: z.string().min(2, "联系人姓名至少需要2个字符"),
  phone: z.string().regex(/^1[3-9]\d{9}$/, "请输入有效的手机号码"),
  email: z.string().email("请输入有效的邮箱地址").optional().or(z.literal("")),
  address: z.string().optional(),
  city: z.string().optional(),
  province: z.string().optional(),
  district: z.string().optional(),
  businessLicense: z.string().optional(),
});

type ChainInfoValues = z.infer<typeof chainInfoSchema>;

interface TenantData {
  id: number;
  name: string;
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  city: string;
  province: string;
  district: string;
  businessLicense: string;
  logoUrl: string;
  validStart: string;
  validEnd: string;
  status: 'active' | 'pending' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export default function ChainSettingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [tenantData, setTenantData] = useState<TenantData | null>(null);
  const [uploading, setUploading] = useState(false);
  
  // 省市区联动状态
  const [selectedProvince, setSelectedProvince] = useState<string>("");
  const [selectedCity, setSelectedCity] = useState<string>("");
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [availableDistricts, setAvailableDistricts] = useState<string[]>([]);
  
  // 文件上传引用
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 表单状态管理
  const form = useForm<ChainInfoValues>({
    resolver: zodResolver(chainInfoSchema),
    defaultValues: {
      name: "",
      contactPerson: "",
      phone: "",
      email: "",
      address: "",
      city: "",
      province: "",
      district: "",
      businessLicense: "",
    }
  });

  // 监听省份变化，更新城市列表
  useEffect(() => {
    const province = form.watch("province");
    if (province) {
      setSelectedProvince(province);
      // 获取该省份下的城市列表
      const provinceCities = cities[province] || [];
      setAvailableCities(provinceCities);
      // 清空城市和区县选择
      form.setValue("city", "");
      form.setValue("district", "");
      setSelectedCity("");
      setAvailableDistricts([]);
    }
  }, [form.watch("province")]);

  // 监听城市变化，更新区列表
  useEffect(() => {
    const city = form.watch("city");
    if (city && selectedProvince) {
      setSelectedCity(city);
      // 获取该城市下的区县列表
      const cityDistricts = districts[selectedProvince]?.[city] || [];
      setAvailableDistricts(cityDistricts);
      // 清空区县选择
      form.setValue("district", "");
    }
  }, [form.watch("city"), selectedProvince]);

  // 获取租户信息
  useEffect(() => {
    const fetchTenantInfo = async () => {
      setLoading(true);
      try {
        const tenantId = user?.tenant_id;
        
        if (!tenantId) {
          console.error('未找到租户ID');
          setLoading(false);
          return;
        }

        console.log('获取租户信息，租户ID:', tenantId);

        const response = await fetch(`/api/tenant?tenantId=${tenantId}`);
        const result = await response.json();

        if (result.success) {
          setTenantData(result.data);
          
          // 填充表单数据
          form.reset({
            name: result.data.name,
            contactPerson: result.data.contactPerson,
            phone: result.data.phone,
            email: result.data.email,
            address: result.data.address,
            city: result.data.city,
            province: result.data.province,
            district: result.data.district,
            businessLicense: result.data.businessLicense,
          });
          
          // 如果有省份数据，触发省市区联动
          if (result.data.province) {
            setSelectedProvince(result.data.province);
            // 获取该省份下的城市列表
            const provinceCities = cities[result.data.province] || [];
            setAvailableCities(provinceCities);
            
            if (result.data.city) {
              setSelectedCity(result.data.city);
              // 获取该城市下的区县列表
              const cityDistricts = districts[result.data.province]?.[result.data.city] || [];
              setAvailableDistricts(cityDistricts);
            }
          }
        } else {
          throw new Error(result.error || '获取连锁信息失败');
        }

      } catch (error: any) {
        console.error('获取连锁信息失败:', error);
        
        toast({
          title: "获取连锁信息失败",
          description: error.message || "请稍后再试",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTenantInfo();
  }, [user, form, toast]);

  // 处理保存
  const handleSave = async (values: ChainInfoValues) => {
    setSaving(true);
    try {
      const tenantId = user?.tenant_id;
      
      if (!tenantId) {
        toast({
          title: "保存失败",
          description: "未找到租户信息",
          variant: "destructive"
        });
        return;
      }

      console.log('保存连锁信息:', values);

      // 调用API更新租户信息
      const response = await fetch('/api/tenant', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          tenantId,
          // 确保省市区信息正确传递
          province: values.province,
          city: values.city,
          district: values.district
        })
      });

      const result = await response.json();

      if (result.success) {
        setTenantData(result.data);
        
        toast({
          title: "保存成功",
          description: result.message || "连锁信息已成功更新",
        });
      } else {
        throw new Error(result.error || '保存连锁信息失败');
      }
    } catch (error: any) {
      console.error('保存连锁信息失败:', error);
      toast({
        title: "保存失败",
        description: error.message || "请稍后再试",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  // 获取状态显示
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-500">正常营业</Badge>;
      case 'pending':
        return <Badge variant="secondary">待审核</Badge>;
      case 'inactive':
        return <Badge variant="destructive">已暂停</Badge>;
      default:
        return <Badge variant="outline">未知状态</Badge>;
    }
  };

  // 触发文件选择
  const handleLogoUploadClick = () => {
    fileInputRef.current?.click();
  };

  // 处理Logo上传
  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      toast({
        title: "上传失败",
        description: "请选择图片文件",
        variant: "destructive"
      });
      return;
    }

    // 检查文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "上传失败",
        description: "图片大小不能超过2MB",
        variant: "destructive"
      });
      return;
    }

    setUploading(true);
    try {
      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', file);
      formData.append('tenantId', user?.tenant_id?.toString() || '');

      // 发送上传请求
      const response = await fetch('/api/upload/logo', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        // 更新租户数据中的logoUrl
        if (tenantData) {
          setTenantData({
            ...tenantData,
            logoUrl: result.url
          });
        }
        
        toast({
          title: "上传成功",
          description: "Logo已成功上传",
        });
      } else {
        throw new Error(result.error || '上传Logo失败');
      }
    } catch (error: any) {
      console.error('上传Logo失败:', error);
      toast({
        title: "上传失败",
        description: error.message || "请稍后再试",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      // 清空文件输入，以便再次选择同一个文件
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">连锁信息设置</h1>
          <p className="text-muted-foreground">
            管理您的连锁品牌基本信息和设置
          </p>
        </div>
      </div>

      <div className="grid gap-6">
        {/* 基本信息卡片 */}
        {tenantData && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                连锁概览
              </CardTitle>
              <CardDescription>
                当前连锁品牌的基本信息和状态
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-4">
                <div className="relative">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={tenantData.logoUrl} alt={tenantData.name} />
                    <AvatarFallback className="text-lg">
                      {tenantData.name.slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  <div 
                    className="absolute bottom-0 right-0 bg-primary text-white rounded-full p-1 cursor-pointer hover:bg-primary/80"
                    onClick={handleLogoUploadClick}
                  >
                    {uploading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Camera className="h-4 w-4" />
                    )}
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleLogoUpload}
                    accept="image/*"
                    className="hidden"
                  />
                </div>
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2">
                    <h3 className="text-xl font-semibold">{tenantData.name}</h3>
                    {getStatusBadge(tenantData.status)}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      {tenantData.phone}
                    </div>
                    {tenantData.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {tenantData.email}
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      {tenantData.province} {tenantData.city} {tenantData.district} {tenantData.address}
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      创建时间：{tenantData.createdAt}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 编辑表单 */}
        <Card>
          <CardHeader>
            <CardTitle>编辑连锁信息</CardTitle>
            <CardDescription>
              修改连锁品牌的基本信息，更新后将在系统中生效
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSave)} className="space-y-6">
                {/* 基本信息 */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">基本信息</h4>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>连锁名称 *</FormLabel>
                          <FormControl>
                            <Input placeholder="例如：静心瑜伽连锁" {...field} />
                          </FormControl>
                          <FormDescription>
                            您的连锁品牌名称
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="contactPerson"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>联系人 *</FormLabel>
                          <FormControl>
                            <Input placeholder="例如：张三" {...field} />
                          </FormControl>
                          <FormDescription>
                            主要联系人姓名
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>联系电话 *</FormLabel>
                          <FormControl>
                            <Input placeholder="例如：13800138000" {...field} />
                          </FormControl>
                          <FormDescription>
                            主要联系电话
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>邮箱地址</FormLabel>
                          <FormControl>
                            <Input placeholder="例如：<EMAIL>" {...field} />
                          </FormControl>
                          <FormDescription>
                            用于接收重要通知
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* 地址信息 */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">地址信息</h4>
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="province"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>省份</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择省份" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {provinces.map((province) => (
                                <SelectItem key={province} value={province}>
                                  {province}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>城市</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            value={field.value}
                            disabled={!selectedProvince || availableCities.length === 0}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择城市" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {availableCities.map((city) => (
                                <SelectItem key={city} value={city}>
                                  {city}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="district"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>区/县</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            value={field.value}
                            disabled={!selectedCity || availableDistricts.length === 0}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择区/县" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {availableDistricts.map((district) => (
                                <SelectItem key={district} value={district}>
                                  {district}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>详细地址</FormLabel>
                        <FormControl>
                          <Textarea 
                            placeholder="例如：三里屯路19号院1号楼" 
                            className="resize-none" 
                            rows={3}
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          总部或主要办公地址
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* 其他信息 */}
                <div className="space-y-4">
                  <h4 className="text-lg font-medium">其他信息</h4>
                  <Separator />
                  
                  <FormField
                    control={form.control}
                    name="businessLicense"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>营业执照号</FormLabel>
                        <FormControl>
                          <Input placeholder="例如：91110000000000000X" {...field} />
                        </FormControl>
                        <FormDescription>
                          企业营业执照统一社会信用代码
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={saving}>
                    {saving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        保存中...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        保存更改
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
