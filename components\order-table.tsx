"use client"

import { useState } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { MoreHorizontal, Eye, FileText, RefreshCw } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"

interface OrderTableProps {
  onViewDetail: (orderId: string) => void
  selectedOrders: string[]
  onSelectOrders: (orderIds: string[]) => void
}

export function OrderTable({ onViewDetail, selectedOrders, onSelectOrders }: OrderTableProps) {
  const [orders] = useState([
    {
      id: "ORD001",
      member: {
        name: "张三",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "会员卡",
      product: "年卡",
      amount: "¥3,600",
      date: "2025-03-25 14:30",
      payment: "微信支付",
      status: "paid",
      venue: "朝阳店",
    },
    {
      id: "ORD002",
      member: {
        name: "李四",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "课程套餐",
      product: "高级瑜伽10次课",
      amount: "¥1,080",
      date: "2025-03-26 10:15",
      payment: "支付宝",
      status: "paid",
      venue: "海淀店",
    },
    {
      id: "ORD003",
      member: {
        name: "王五",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "单次课程",
      product: "空中瑜伽体验",
      amount: "¥200",
      date: "2025-03-27 16:45",
      payment: "银行卡",
      status: "pending",
      venue: "朝阳店",
    },
    {
      id: "ORD004",
      member: {
        name: "赵六",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "商品",
      product: "瑜伽垫",
      amount: "¥280",
      date: "2025-03-27 18:20",
      payment: "微信支付",
      status: "paid",
      venue: "东城店",
    },
    {
      id: "ORD005",
      member: {
        name: "钱七",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      type: "会员卡",
      product: "季卡",
      amount: "¥1,200",
      date: "2025-03-28 09:10",
      payment: "支付宝",
      status: "refunded",
      venue: "西城店",
    },
  ])

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectOrders(orders.map((order) => order.id))
    } else {
      onSelectOrders([])
    }
  }

  const handleSelectOrder = (orderId: string, checked: boolean) => {
    if (checked) {
      onSelectOrders([...selectedOrders, orderId])
    } else {
      onSelectOrders(selectedOrders.filter((id) => id !== orderId))
    }
  }

  const handleRefreshStatus = (orderId: string) => {
    alert(`刷新订单状态: ${orderId}`)
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[40px]">
              <Checkbox
                checked={selectedOrders.length === orders.length && orders.length > 0}
                onCheckedChange={handleSelectAll}
                aria-label="全选"
              />
            </TableHead>
            <TableHead>订单编号</TableHead>
            <TableHead>会员</TableHead>
            <TableHead>订单类型</TableHead>
            <TableHead>商品/服务</TableHead>
            <TableHead>金额</TableHead>
            <TableHead>下单时间</TableHead>
            <TableHead>支付方式</TableHead>
            <TableHead>门店</TableHead>
            <TableHead>状态</TableHead>
            <TableHead className="text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.map((order) => (
            <TableRow key={order.id}>
              <TableCell>
                <Checkbox
                  checked={selectedOrders.includes(order.id)}
                  onCheckedChange={(checked) => handleSelectOrder(order.id, !!checked)}
                  aria-label={`选择订单 ${order.id}`}
                />
              </TableCell>
              <TableCell className="font-medium">{order.id}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={order.member.avatar} alt={order.member.name} />
                    <AvatarFallback>{order.member.name[0]}</AvatarFallback>
                  </Avatar>
                  <span>{order.member.name}</span>
                </div>
              </TableCell>
              <TableCell>{order.type}</TableCell>
              <TableCell>{order.product}</TableCell>
              <TableCell>{order.amount}</TableCell>
              <TableCell>{order.date}</TableCell>
              <TableCell>{order.payment}</TableCell>
              <TableCell>{order.venue}</TableCell>
              <TableCell>
                <Badge
                  variant={order.status === "paid" ? "default" : order.status === "pending" ? "outline" : "destructive"}
                >
                  {order.status === "paid" ? "已支付" : order.status === "pending" ? "待支付" : "已退款"}
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>操作</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onViewDetail(order.id)}>
                      <Eye className="mr-2 h-4 w-4" />
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <FileText className="mr-2 h-4 w-4" />
                      导出订单
                    </DropdownMenuItem>
                    {order.status === "pending" && (
                      <DropdownMenuItem onClick={() => handleRefreshStatus(order.id)}>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        刷新状态
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

