"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { useState } from "react"

interface AdvancedFilterDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function AdvancedFilterDialog({ open, onOpenChange }: AdvancedFilterDialogProps) {
  const [capacityRange, setCapacityRange] = useState([0, 20])
  const [utilizationRange, setUtilizationRange] = useState([0, 100])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>高级筛选</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="status">场地状态</Label>
            <Select defaultValue="all">
              <SelectTrigger id="status">
                <SelectValue placeholder="选择状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="available">可用</SelectItem>
                <SelectItem value="maintenance">维护中</SelectItem>
                <SelectItem value="booked">已预订</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="location">场地位置</Label>
            <Select defaultValue="all">
              <SelectTrigger id="location">
                <SelectValue placeholder="选择位置" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部位置</SelectItem>
                <SelectItem value="floor1">一楼</SelectItem>
                <SelectItem value="floor2">二楼</SelectItem>
                <SelectItem value="floor3">三楼</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>容纳人数范围</Label>
            <div className="pt-4 px-1">
              <Slider defaultValue={[0, 20]} max={30} step={1} value={capacityRange} onValueChange={setCapacityRange} />
              <div className="flex justify-between mt-2 text-sm">
                <span>{capacityRange[0]}人</span>
                <span>{capacityRange[1]}人</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>使用率范围</Label>
            <div className="pt-4 px-1">
              <Slider
                defaultValue={[0, 100]}
                max={100}
                step={5}
                value={utilizationRange}
                onValueChange={setUtilizationRange}
              />
              <div className="flex justify-between mt-2 text-sm">
                <span>{utilizationRange[0]}%</span>
                <span>{utilizationRange[1]}%</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>设备筛选</Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              <div className="flex items-center space-x-2">
                <Checkbox id="equipment-1" />
                <label htmlFor="equipment-1" className="text-sm">
                  瑜伽垫
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="equipment-2" />
                <label htmlFor="equipment-2" className="text-sm">
                  瑜伽砖
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="equipment-3" />
                <label htmlFor="equipment-3" className="text-sm">
                  瑜伽带
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="equipment-4" />
                <label htmlFor="equipment-4" className="text-sm">
                  瑜伽球
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="equipment-5" />
                <label htmlFor="equipment-5" className="text-sm">
                  瑜伽轮
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox id="equipment-6" />
                <label htmlFor="equipment-6" className="text-sm">
                  空中瑜伽吊床
                </label>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label>可用时间</Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              <div className="space-y-2">
                <Label htmlFor="start-time" className="text-xs">
                  开始时间
                </Label>
                <Select defaultValue="any">
                  <SelectTrigger id="start-time">
                    <SelectValue placeholder="选择时间" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">任意时间</SelectItem>
                    <SelectItem value="06:00">06:00</SelectItem>
                    <SelectItem value="08:00">08:00</SelectItem>
                    <SelectItem value="10:00">10:00</SelectItem>
                    <SelectItem value="12:00">12:00</SelectItem>
                    <SelectItem value="14:00">14:00</SelectItem>
                    <SelectItem value="16:00">16:00</SelectItem>
                    <SelectItem value="18:00">18:00</SelectItem>
                    <SelectItem value="20:00">20:00</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="end-time" className="text-xs">
                  结束时间
                </Label>
                <Select defaultValue="any">
                  <SelectTrigger id="end-time">
                    <SelectValue placeholder="选择时间" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">任意时间</SelectItem>
                    <SelectItem value="08:00">08:00</SelectItem>
                    <SelectItem value="10:00">10:00</SelectItem>
                    <SelectItem value="12:00">12:00</SelectItem>
                    <SelectItem value="14:00">14:00</SelectItem>
                    <SelectItem value="16:00">16:00</SelectItem>
                    <SelectItem value="18:00">18:00</SelectItem>
                    <SelectItem value="20:00">20:00</SelectItem>
                    <SelectItem value="22:00">22:00</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            重置
          </Button>
          <Button onClick={() => onOpenChange(false)}>应用筛选</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

